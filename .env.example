# Application
NODE_ENV=development
PORT=3000
API_PREFIX=api
APP_NAME=Retreat And Be API

# Database - Development
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=YOUR_DB_PASSWORD
DB_DATABASE=retreat_and_be

# Database - Test
TEST_DB_TYPE=postgres
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USERNAME=YOUR_TEST_DB_USERNAME
TEST_DB_PASSWORD=YOUR_TEST_DB_PASSWORD
TEST_DB_DATABASE=retreat_and_be_test
TEST_DB_SYNCHRONIZE=true

# JWT
JWT_SECRET=YOUR_JWT_SECRET
JWT_EXPIRES_IN=1d

# External APIs - Add your API keys here
RECOMMENDATION_API_KEY=YOUR_RECOMMENDATION_API_KEY
WEATHER_API_KEY=YOUR_WEATHER_API_KEY
MAP_API_KEY=YOUR_MAP_API_KEY
ANALYTICS_API_KEY=YOUR_ANALYTICS_API_KEY
