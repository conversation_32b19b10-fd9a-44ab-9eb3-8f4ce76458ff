# Context Prime: Project Overview

## Instructions for AI Assistant

Before starting any development task, please read and process the following project context to ensure you have a comprehensive understanding of the project architecture, conventions, and business logic. **Pay particular attention to the design principles and UX/UI quality standards defined in the conventions, as achieving a world-class ("Silicon Valley / Y Combinator" standard) design is a primary objective of this project.**
 
## Essential Files to Read
 
Please read and analyze the following files in order:

1. **Project README**
   - Location: `/README.md`
   - Purpose: Understand the project overview, setup instructions, and high-level goals.
 
2. **Project PRD**
   - Location: `/project_prd.md` (**Generated** PRD)
   - Purpose: Understand the complete product requirements, features, and context. **Primary source of truth.**
 
3. **Project-Specific Architecture**
   - Location: `/02_AI-DOCS/Architecture/architecture.md` (**Generated** document)
   - Purpose: Understand the specific system architecture for **this project**.
 
4. **Project-Specific API Integrations**
   - Location: `/02_AI-DOCS/Integrations/api_integration.md` (**Generated** document)
   - Purpose: Understand the specific external services and APIs used in **this project**.
 
5. **Project-Specific Business Logic**
   - Location: `/02_AI-DOCS/BusinessLogic/business_logic.md` (**Generated** document)
   - Purpose: Understand the core business rules specific to **this project**.
 
6. **Project-Specific Coding Conventions**
   - Location: `/02_AI-DOCS/Conventions/coding_conventions.md` (**Generated** document)
   - Purpose: Understand **this project's** specific coding standards and patterns.
 
7. **Project-Specific Design Conventions**
   - Location: `/02_AI-DOCS/Conventions/design_conventions.md` (**Generated** document)
   - Purpose: Understand **this project's** specific design standards and critical design/UX guidelines.
 
8. **Project-Specific Deployment Guide**
   - Location: `/02_AI-DOCS/Deployment/deployment_guide.md` (**Generated** document)
   - Purpose: Understand the deployment strategy for **this project**.
 
9. **Core AI Agent Guidance (Reference)**
   - Location: `/02_AI-DOCS/Documentation/AI_Coding_Agent_Optimization.md`
   - Purpose: Reference general best practices for AI agent operation.
 
10. **Core AI Design Guidance (Reference)**
    - Location: `/02_AI-DOCS/Documentation/AI_Design_Agent_Optimization.md`
    - Purpose: Reference general best practices for achieving high-quality design/UX.
 
## Current Task Context

After reading the above files, please review the specific task details:

- **Task ID:** [Task ID]
- **Task Type:** [Feature/Bugfix/Refactor]
- **Task Description:** [Brief description of the task]
- **Specification:** `/03_SPECS/[type]/[type]_spec_[ID].md` (**Generated** specification file for this specific feature/bug)
 
## Key Project Information
 
- **Project Name:** [Project Name]
- **Technology Stack:** [List of key technologies]
- **Main Components:** [List of main system components]
- **Development Approach:** [Agile/TDD/etc.]
- **Core Design Goal:** Achieve exceptional UI/UX quality, aiming for "Silicon Valley / Y Combinator" standards in terms of aesthetics, intuitiveness, and polish.
 
## Working Conventions
 
### Code Organization

- [Brief description of code organization]

### Testing Approach

- [Brief description of testing approach]

### Git Workflow

- [Brief description of git workflow]

## Response Format

After processing this context, please respond with:

1. A brief summary of your understanding of the project
2. Your understanding of the current task
3. Your proposed approach to implementing the task
4. Any clarifying questions you have before proceeding

---

*Note: This context prime should be customized for each project and updated as the project evolves.*
