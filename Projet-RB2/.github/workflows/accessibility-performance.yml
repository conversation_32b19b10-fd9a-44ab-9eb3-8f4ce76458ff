name: Accessibility and Performance Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  # Allow manual triggering
  workflow_dispatch:

jobs:
  accessibility-testing:
    name: Accessibility Testing
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Run accessibility tests
        run: npm run test:accessibility

      - name: Upload accessibility report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: accessibility-report
          path: reports/accessibility
          retention-days: 30

  performance-testing:
    name: Performance Testing
    runs-on: ubuntu-latest
    strategy:
      matrix:
        device: ['desktop', 'mobile']

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Install Lighthouse
        run: npm install -g lighthouse lighthouse-ci

      - name: Start server
        run: npm run serve &
        env:
          PORT: 3000

      - name: Wait for server
        run: npx wait-on http://localhost:3000

      - name: Generate auth cookie (if needed)
        run: |
          # Script pour générer un cookie d'authentification
          # Ceci est un exemple, à adapter selon votre système d'authentification
          AUTH_COOKIE=$(node scripts/generate-auth-cookie.js)
          echo "AUTH_COOKIE=$AUTH_COOKIE" >> $GITHUB_ENV

      - name: Run Lighthouse CI for ${{ matrix.device }}
        run: |
          lighthouse-ci http://localhost:3000 \
            --output=json \
            --output-path=./lighthouse-report-${{ matrix.device }}.json \
            --config=./lighthouse-config.js
        env:
          DEVICE_TYPE: ${{ matrix.device }}
          AUTH_COOKIE: ${{ env.AUTH_COOKIE }}

      - name: Upload performance report for ${{ matrix.device }}
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: lighthouse-report-${{ matrix.device }}
          path: ./lighthouse-report-${{ matrix.device }}.json
          retention-days: 30

      - name: Check performance thresholds for ${{ matrix.device }}
        run: |
          node scripts/check-lighthouse-thresholds.js ./lighthouse-report-${{ matrix.device }}.json
        env:
          DEVICE_TYPE: ${{ matrix.device }}

  visual-regression-testing:
    name: Visual Regression Testing
    runs-on: ubuntu-latest
    strategy:
      matrix:
        viewport: ['desktop', 'tablet', 'mobile']

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Start server
        run: npm run serve &
        env:
          PORT: 3000

      - name: Wait for server
        run: npx wait-on http://localhost:3000

      - name: Run visual regression tests for ${{ matrix.viewport }}
        run: npm run test:visual -- --viewport=${{ matrix.viewport }}
        env:
          VIEWPORT: ${{ matrix.viewport }}
          BASE_URL: http://localhost:3000

      - name: Upload visual regression report for ${{ matrix.viewport }}
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: visual-regression-report-${{ matrix.viewport }}
          path: reports/visual-regression
          retention-days: 30

      - name: Upload baseline screenshots
        uses: actions/upload-artifact@v3
        if: success()
        with:
          name: baseline-screenshots
          path: reports/visual-regression/screenshots/baseline
          retention-days: 30
