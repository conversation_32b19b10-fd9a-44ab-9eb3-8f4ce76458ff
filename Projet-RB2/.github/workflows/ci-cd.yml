name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - staging
          - production

jobs:
  lint:
    name: Lint Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm test

      - name: Run integration tests
        run: npm run test:integration

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: test
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-artifacts
          path: |
            dist/
            package.json
            package-lock.json

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --production

      - name: Run OWASP Dependency-Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'RB2'
          path: '.'
          format: 'HTML'
          out: 'reports'
          args: >
            --failOnCVSS 7
            --enableRetired
            
      - name: Run ESLint Security
        run: |
          npm install -g eslint eslint-plugin-security
          eslint --plugin security --ext .js,.ts,.tsx . -c .eslintrc.js

      - name: Run Snyk Security Scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN || '' }}
        with:
          args: --severity-threshold=high
          
      - name: Run Secret Scanner
        uses: zricethezav/gitleaks-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE || '' }}
          
      - name: Run SAST
        run: |
          npm install -g sonarqube-scanner
          sonar-scanner \
            -Dsonar.projectKey=rb2-project \
            -Dsonar.sources=. \
            -Dsonar.host.url=${{ secrets.SONAR_HOST_URL || 'http://localhost:9000' }} \
            -Dsonar.login=${{ secrets.SONAR_TOKEN || '' }}

      - name: Run Trivy Scanner for Container Images
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rb2-app/backend-api:latest'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload vulnerability reports
        uses: actions/upload-artifact@v3
        with:
          name: vulnerability-reports
          path: reports/
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  docker-build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME || '' }}
          password: ${{ secrets.DOCKERHUB_TOKEN || '' }}

      - name: Generate image tags
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: rb2-app/backend-api
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=sha,format=long
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}

      - name: Build and push backend API
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./backend/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=registry,ref=rb2-app/backend-api:buildcache
          cache-to: type=registry,ref=rb2-app/backend-api:buildcache,mode=max

      - name: Generate frontend image tags
        id: frontend-meta
        uses: docker/metadata-action@v4
        with:
          images: rb2-app/frontend-app
          tags: |
            type=ref,event=branch
            type=semver,pattern={{version}}
            type=sha,format=long
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}

      - name: Build and push frontend
        uses: docker/build-push-action@v3
        with:
          context: ./frontend
          push: true
          tags: ${{ steps.frontend-meta.outputs.tags }}
          cache-from: type=registry,ref=rb2-app/frontend-app:buildcache
          cache-to: type=registry,ref=rb2-app/frontend-app:buildcache,mode=max

  deploy-test:
    name: Deploy to Test Environment
    runs-on: ubuntu-latest
    needs: docker-build
    if: (github.event_name == 'push' && github.ref == 'refs/heads/develop') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'test')
    environment: test
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Configure Kubernetes
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_TEST || '' }}

      - name: Deploy to test environment
        run: |
          cd infrastructure/environments/test
          kubectl apply -f config.yaml
          kubectl apply -f deployment.yaml
          
          # Restart deployments to pick up new images
          kubectl rollout restart deployment/backend-api -n rb2-test
          kubectl rollout restart deployment/frontend-app -n rb2-test
          
          # Wait for deployments to complete
          kubectl rollout status deployment/backend-api -n rb2-test
          kubectl rollout status deployment/frontend-app -n rb2-test

      - name: Run smoke tests
        run: |
          # Install k6 for performance testing
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
          # Run smoke test
          k6 run --env BASE_URL=https://api.test.rb2-app.com --env SCENARIO=smoke infrastructure/testing/performance-tests.js

  deploy-staging:
    name: Deploy to Staging Environment
    runs-on: ubuntu-latest
    needs: deploy-test
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Configure Kubernetes
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING || '' }}
          
      - name: Deploy to staging environment
        run: |
          cd infrastructure/environments/staging
          kubectl apply -f config.yaml
          kubectl apply -f deployment.yaml
          
          # Restart deployments to pick up new images
          kubectl rollout restart deployment/backend-api -n rb2-staging
          kubectl rollout restart deployment/frontend-app -n rb2-staging
          
          # Wait for deployments to complete
          kubectl rollout status deployment/backend-api -n rb2-staging
          kubectl rollout status deployment/frontend-app -n rb2-staging

      - name: Run validation tests
        run: |
          # Install k6 for performance testing
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
          # Run smoke test
          k6 run --env BASE_URL=https://api.staging.rb2-app.com --env SCENARIO=smoke infrastructure/testing/performance-tests.js

  deploy-production:
    name: Deploy to Production Environment
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Configure Kubernetes
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION || '' }}
          
      - name: Validate deployment readiness
        run: |
          # Check production deployment readiness
          bash scripts/pre-deploy-validation.sh production
          
      - name: Create deployment backup
        run: |
          # Create backup of current production state before deployment
          mkdir -p backups
          kubectl get all -n rb2-production -o yaml > backups/pre-deploy-$(date +%Y%m%d-%H%M%S).yaml
          
      - name: Deploy to production environment
        run: |
          # Create kubernetes namespace if it doesn't exist
          kubectl create namespace rb2-production --dry-run=client -o yaml | kubectl apply -f -
          
          # Apply configurations
          kubectl apply -f infrastructure/environments/production/config.yaml
          kubectl apply -f infrastructure/environments/production/deployment.yaml
          
          # Perform a canary deployment
          bash scripts/canary-deployment.sh production

      - name: Validate production deployment
        run: |
          # Run a comprehensive health check
          kubectl exec -n rb2-production deployment/backend-api -- curl -s http://localhost:8081/health
          
          # Run smoke test
          k6 run --env BASE_URL=https://api.rb2-app.com --env SCENARIO=smoke infrastructure/testing/performance-tests.js
          
      - name: Test disaster recovery procedures
        run: |
          # Test the disaster recovery procedure in a controlled environment
          echo "Running disaster recovery simulation tests..."
          bash scripts/disaster-recovery/simulate-db-failure.sh --dry-run
          bash scripts/disaster-recovery/simulate-pod-failure.sh --dry-run
          bash scripts/disaster-recovery/validate-backup-restore.sh --dry-run
          echo "Disaster recovery simulation completed successfully."
        continue-on-error: true  # Don't fail the deployment, but log if DR tests fail

  security-compliance:
    name: Security Compliance Scan
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.event.inputs.environment == 'production' && github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run PCI DSS Compliance Check
        run: |
          # Run PCI DSS compliance checks
          bash scripts/compliance/run-pci-scan.sh
        continue-on-error: true

      - name: Run GDPR Compliance Check
        run: |
          # Run GDPR compliance check script
          bash scripts/compliance/run-gdpr-scan.sh
        continue-on-error: true
        
      - name: Run SOC2 Compliance Check
        run: |
          # Run SOC2 compliance check script
          bash scripts/compliance/run-soc2-scan.sh
        continue-on-error: true
        
      - name: Run HIPAA Compliance Check (if applicable)
        if: ${{ vars.ENABLE_HIPAA_CHECKS == 'true' || false }}
        run: |
          # Run HIPAA compliance check script
          bash scripts/compliance/run-hipaa-scan.sh
        continue-on-error: true

      - name: Generate Compliance Report
        run: |
          # Generate compliance report
          bash scripts/compliance/generate-report.sh > compliance-report.md
          
      - name: Upload Compliance Report
        uses: actions/upload-artifact@v3
        with:
          name: compliance-report
          path: compliance-report.md

  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [deploy-test, deploy-staging, deploy-production, security-compliance]
    if: always()
    steps:
      - name: Send Slack notification
        uses: slackapi/slack-github-action@v1.23.0
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": "GitHub Action CI/CD Result: ${{ job.status }}",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "CI/CD Pipeline Result: ${{ job.status }}"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Repository:*\n${{ github.repository }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Workflow:*\n${{ github.workflow }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Branch:*\n${{ github.ref_name }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Commit:*\n${{ github.sha }}"
                    }
                  ]
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Workflow Run"
                      },
                      "url": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL || '' }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
