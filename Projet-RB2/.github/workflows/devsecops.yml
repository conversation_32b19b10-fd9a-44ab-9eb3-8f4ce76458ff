name: DevSecOps Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 2 * * 1' # Run weekly on Mondays at 2 AM

jobs:
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch all history for all branches and tags

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd Backend && npm ci
          cd ../frontend && npm ci

      # Static Application Security Testing (SAST)
      - name: Run ESLint with security plugins
        run: |
          npm install -g eslint eslint-plugin-security eslint-plugin-node eslint-plugin-sonarjs
          eslint --plugin security --plugin sonarjs --ext .js,.ts,.tsx . -c .eslintrc.js || true
          
      - name: Run SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          args: >
            -Dsonar.projectKey=retreat-be
            -Dsonar.organization=retreat-be
            -Dsonar.sources=.
            -Dsonar.exclusions=**/node_modules/**,**/tests/**,**/*.test.js,**/*.spec.js
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
        continue-on-error: true

      # Secrets Scanning
      - name: Check for hardcoded secrets
        uses: zricethezav/gitleaks-action@master
        continue-on-error: true

      # Software Composition Analysis (SCA)
      - name: Run npm audit
        run: |
          mkdir -p security-reports
          npm audit --json > security-reports/npm-audit-report.json || true
          cd Backend && npm audit --json > ../security-reports/backend-audit-report.json || true
          cd ../frontend && npm audit --json > ../security-reports/frontend-audit-report.json || true

      - name: Run OWASP Dependency-Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'RB2'
          path: '.'
          format: 'HTML'
          out: 'security-reports'
          args: >
            --failOnCVSS 7
            --enableRetired
        continue-on-error: true

      # Container Security
      - name: Build Docker images
        run: |
          docker build -t rb2-backend:${{ github.sha }} ./Backend || true
          docker build -t rb2-frontend:${{ github.sha }} ./frontend || true

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rb2-backend:${{ github.sha }}'
          format: 'sarif'
          output: 'security-reports/trivy-backend-results.sarif'
          severity: 'CRITICAL,HIGH'
        continue-on-error: true

      - name: Run Trivy vulnerability scanner for frontend
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rb2-frontend:${{ github.sha }}'
          format: 'sarif'
          output: 'security-reports/trivy-frontend-results.sarif'
          severity: 'CRITICAL,HIGH'
        continue-on-error: true

      # Infrastructure as Code (IaC) Security
      - name: Run TFSec
        uses: aquasecurity/tfsec-action@master
        with:
          soft_fail: true
        continue-on-error: true

      # Upload security reports
      - name: Upload security reports
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: security-reports/

      # Security report summary
      - name: Generate security report summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "## Dependency Vulnerabilities" >> security-summary.md
          echo "### Backend" >> security-summary.md
          if [ -f security-reports/backend-audit-report.json ]; then
            echo "\`\`\`" >> security-summary.md
            cat security-reports/backend-audit-report.json | grep -o '"vulnerabilities": {[^}]*}' | grep -o '"low": [0-9]*\|"moderate": [0-9]*\|"high": [0-9]*\|"critical": [0-9]*' >> security-summary.md || echo "No vulnerabilities found" >> security-summary.md
            echo "\`\`\`" >> security-summary.md
          else
            echo "No report generated" >> security-summary.md
          fi
          
          echo "### Frontend" >> security-summary.md
          if [ -f security-reports/frontend-audit-report.json ]; then
            echo "\`\`\`" >> security-summary.md
            cat security-reports/frontend-audit-report.json | grep -o '"vulnerabilities": {[^}]*}' | grep -o '"low": [0-9]*\|"moderate": [0-9]*\|"high": [0-9]*\|"critical": [0-9]*' >> security-summary.md || echo "No vulnerabilities found" >> security-summary.md
            echo "\`\`\`" >> security-summary.md
          else
            echo "No report generated" >> security-summary.md
          fi
          
          echo "## Container Security" >> security-summary.md
          if [ -f security-reports/trivy-backend-results.sarif ] || [ -f security-reports/trivy-frontend-results.sarif ]; then
            echo "Container security reports generated. See attached artifacts for details." >> security-summary.md
          else
            echo "No container security reports generated" >> security-summary.md
          fi
          
          echo "## Secret Scanning" >> security-summary.md
          echo "Check Gitleaks results in the job output" >> security-summary.md
          
          echo "## Next Steps" >> security-summary.md
          echo "1. Review the detailed reports in the artifacts" >> security-summary.md
          echo "2. Address critical and high severity issues" >> security-summary.md
          echo "3. Update dependencies with known vulnerabilities" >> security-summary.md

      - name: Create security report issue
        uses: peter-evans/create-issue-from-file@v4
        with:
          title: Security Scan Report - ${{ github.sha }}
          content-filepath: ./security-summary.md
          labels: security, automated-report
          token: ${{ secrets.GITHUB_TOKEN }}
        continue-on-error: true

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: security-scan
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          npm ci
          cd Backend && npm ci
          cd ../frontend && npm ci

      # Run security-specific tests
      - name: Run security tests
        run: |
          cd Backend && npm run test:security || true
          cd ../frontend && npm run test:security || true

      # Dynamic Application Security Testing (DAST)
      - name: Start application for DAST
        run: |
          cd Backend && npm run start:test &
          sleep 10
        continue-on-error: true

      - name: Run OWASP ZAP scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
        continue-on-error: true

  security-compliance:
    name: Security Compliance
    runs-on: ubuntu-latest
    needs: security-tests
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      # Check security configurations
      - name: Verify security configurations
        run: |
          mkdir -p compliance-reports
          
          # Check Content Security Policy
          echo "## Content Security Policy Check" > compliance-reports/csp-check.md
          grep -r "Content-Security-Policy" --include="*.js" --include="*.ts" . || echo "No CSP found" >> compliance-reports/csp-check.md
          
          # Check HTTPS enforcement
          echo "## HTTPS Enforcement Check" > compliance-reports/https-check.md
          grep -r "HSTS\|Strict-Transport-Security" --include="*.js" --include="*.ts" . || echo "No HSTS found" >> compliance-reports/https-check.md
          
          # Check authentication mechanisms
          echo "## Authentication Check" > compliance-reports/auth-check.md
          grep -r "authenticate\|passport\|jwt\|token" --include="*.js" --include="*.ts" . | wc -l >> compliance-reports/auth-check.md
          
          # Check for proper error handling
          echo "## Error Handling Check" > compliance-reports/error-check.md
          grep -r "try\s*{" --include="*.js" --include="*.ts" . | wc -l >> compliance-reports/error-check.md
          grep -r "catch\s*(" --include="*.js" --include="*.ts" . | wc -l >> compliance-reports/error-check.md
        continue-on-error: true

      - name: Upload compliance reports
        uses: actions/upload-artifact@v3
        with:
          name: compliance-reports
          path: compliance-reports/

      # Generate compliance summary
      - name: Generate compliance summary
        run: |
          echo "# Security Compliance Summary" > compliance-summary.md
          echo "## Content Security Policy" >> compliance-summary.md
          cat compliance-reports/csp-check.md >> compliance-summary.md
          echo "## HTTPS Enforcement" >> compliance-summary.md
          cat compliance-reports/https-check.md >> compliance-summary.md
          echo "## Authentication Mechanisms" >> compliance-summary.md
          cat compliance-reports/auth-check.md >> compliance-summary.md
          echo "## Error Handling" >> compliance-summary.md
          cat compliance-reports/error-check.md >> compliance-summary.md
          
          echo "## Recommendations" >> compliance-summary.md
          echo "1. Ensure Content Security Policy is properly configured" >> compliance-summary.md
          echo "2. Enforce HTTPS with HSTS headers" >> compliance-summary.md
          echo "3. Implement proper authentication with JWT and refresh tokens" >> compliance-summary.md
          echo "4. Use try-catch blocks for all async operations" >> compliance-summary.md

      - name: Create compliance report issue
        uses: peter-evans/create-issue-from-file@v4
        with:
          title: Security Compliance Report - ${{ github.sha }}
          content-filepath: ./compliance-summary.md
          labels: security, compliance, automated-report
          token: ${{ secrets.GITHUB_TOKEN }}
        continue-on-error: true

  security-notification:
    name: Security Notification
    runs-on: ubuntu-latest
    needs: [security-scan, security-tests, security-compliance]
    if: always()
    steps:
      - name: Send notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: security-alerts
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_MESSAGE: 'Security pipeline completed for ${{ github.repository }}@${{ github.sha }}'
          SLACK_TITLE: Security Pipeline Results
          SLACK_USERNAME: DevSecOps Bot
        continue-on-error: true
