name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    services:
      # Local blockchain for testing
      hardhat:
        image: node:16
        ports:
          - 8545:8545
        options: --entrypoint "npx hardhat node"
    
    strategy:
      matrix:
        node-version: [16.x]
        # Set this to 2 to run tests in parallel
        containers: [1, 2]
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci
          npx cypress install
      
      - name: Start backend services
        run: |
          npm run start:api &
          npm run start:web3 &
          wait-on http://localhost:4000 http://localhost:8545
      
      - name: Start frontend
        run: |
          npm run dev &
          wait-on http://localhost:3000
      
      - name: Run E2E Tests
        run: npx cypress run --record --parallel --group "e2e-tests"
        env:
          # Pass GitHub token to allow parallelization
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # Split tests between containers
          SPLIT: ${{ strategy.job-total }}
          SPLIT_INDEX: ${{ strategy.job-index }}
      
      - name: Upload test artifacts
        uses: actions/upload-artifact@v2
        if: always()
        with:
          name: cypress-artifacts
          path: |
            cypress/videos
            cypress/screenshots
            cypress/reports
      
      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v3
        if: always()
        with:
          report_paths: 'cypress/reports/junit/*.xml'
          
      - name: Notify on failure
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_COLOR: '#FF0000'
          SLACK_MESSAGE: 'E2E Tests failed! :x:'
          SLACK_TITLE: E2E Test Results