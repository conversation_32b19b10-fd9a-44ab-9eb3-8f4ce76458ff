name: Tests isolés

on:
  push:
    branches: [ main, master, dev ]
  pull_request:
    branches: [ main, master, dev ]

jobs:
  isolated-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Utilisation de Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Installation des dépendances
      run: npm ci
      
    - name: Installation des types
      run: npm install --save-dev @types/jest @types/node
      
    - name: Vérification des types
      run: npx tsc --noEmit
      
    - name: Exécution des tests isolés
      run: node scripts/run-isolated-tests.js
      
    - name: Notification du résultat
      if: always()
      uses: actions/github-script@v6
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const { owner, repo } = context.repo;
          const run_id = context.runId;
          const run_url = `https://github.com/${owner}/${repo}/actions/runs/${run_id}`;
          
          if (context.job.status === 'success') {
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner,
              repo,
              body: `✅ Les tests isolés ont réussi ! [Voir les détails](${run_url})`
            });
          } else {
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner,
              repo,
              body: `❌ Les tests isolés ont échoué ! [Voir les détails](${run_url})`
            });
          } 