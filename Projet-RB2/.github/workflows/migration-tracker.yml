name: Design System Migration Tracker

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'frontend/src/components/**'
      - 'frontend/src/atomic/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'frontend/src/components/**'
      - 'frontend/src/atomic/**'
  schedule:
    - cron: '0 8 * * 1' # Tous les lundis à 8h

jobs:
  track-migration:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci
    
    - name: Generate migration report
      run: node scripts/migration-tracker.js
    
    - name: Upload migration report
      uses: actions/upload-artifact@v3
      with:
        name: migration-report
        path: migration-report.json
    
    - name: Create or update migration status issue
      if: github.event_name != 'pull_request' && github.ref == 'refs/heads/main'
      uses: peter-evans/create-or-update-comment@v2
      with:
        issue-number: ${{ env.MIGRATION_ISSUE_NUMBER || 1 }}
        body: |
          ## Rapport de migration vers l'architecture atomique
          
          Mise à jour automatique du ${{ github.event.repository.updated_at }}
          
          Vous pouvez télécharger le rapport complet dans les artifacts de la dernière exécution du workflow.
          
          ### Résumé
          
          ```
          Progression: $(cat migration-report.json | jq .statistics.completionPercentage)%
          ```
          
          - Composants migrés: $(cat migration-report.json | jq .statistics.migrated)
          - Composants en attente: $(cat migration-report.json | jq .statistics.pending)
          
          ### Détails par type
          
          - Atomes: $(cat migration-report.json | jq '.atomicComponents.atoms | length')
          - Molécules: $(cat migration-report.json | jq '.atomicComponents.molecules | length')
          - Organismes: $(cat migration-report.json | jq '.atomicComponents.organisms | length')
          - Templates: $(cat migration-report.json | jq '.atomicComponents.templates | length')
          
          ### Qualité
          
          - Tests: $(cat migration-report.json | jq '.atomicComponents.atoms + .atomicComponents.molecules + .atomicComponents.organisms + .atomicComponents.templates | map(select(.hasTests)) | length') composants testés
          - Stories: $(cat migration-report.json | jq '.atomicComponents.atoms + .atomicComponents.molecules + .atomicComponents.organisms + .atomicComponents.templates | map(select(.hasStory)) | length') composants documentés
          
          [Voir le rapport complet]({{ github.server_url }}/{{ github.repository }}/actions/runs/{{ github.run_id }})
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        MIGRATION_ISSUE_NUMBER: 1 # À remplacer par le numéro de l'issue dédiée au suivi 