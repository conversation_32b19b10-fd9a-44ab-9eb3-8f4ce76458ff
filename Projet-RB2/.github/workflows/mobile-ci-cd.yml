name: Mobile CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'mobile/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'mobile/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - staging
          - production
      platform:
        description: 'Platform to build'
        required: true
        default: 'both'
        type: choice
        options:
          - ios
          - android
          - both

jobs:
  lint:
    name: Lint Mobile Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mobile/package.json'

      - name: Install dependencies
        run: cd mobile && npm ci

      - name: Run linting
        run: cd mobile && npm run lint

  test:
    name: Run Mobile Tests
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mobile/package.json'

      - name: Install dependencies
        run: cd mobile && npm ci

      - name: Run tests
        run: cd mobile && npm test

  security-scan:
    name: Mobile Security Scan
    runs-on: ubuntu-latest
    needs: test
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mobile/package.json'

      - name: Install dependencies
        run: cd mobile && npm ci

      - name: Run npm audit
        run: cd mobile && npm audit --production

      - name: Run OWASP Dependency-Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'RB2-Mobile'
          path: './mobile'
          format: 'HTML'
          out: 'reports'
          args: >
            --failOnCVSS 7
            --enableRetired

      - name: Run ESLint Security
        run: |
          npm install -g eslint eslint-plugin-security
          cd mobile && eslint --plugin security --ext .js,.ts,.tsx . -c .eslintrc.js

      - name: Run Secret Scanner
        uses: zricethezav/gitleaks-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE || '' }}

  build-android:
    name: Build Android App
    runs-on: ubuntu-latest
    needs: security-scan
    if: github.event.inputs.platform == 'android' || github.event.inputs.platform == 'both' || github.event.inputs.platform == null
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mobile/package.json'

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Install dependencies
        run: cd mobile && npm ci

      - name: Cache Gradle packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Make Gradlew Executable
        run: cd mobile/android && chmod +x ./gradlew

      - name: Build Android App
        run: |
          cd mobile/android
          ./gradlew assembleRelease

      - name: Sign Android App
        uses: r0adkll/sign-android-release@v1
        id: sign_app
        with:
          releaseDirectory: mobile/android/app/build/outputs/apk/release
          signingKeyBase64: ${{ secrets.ANDROID_SIGNING_KEY || '' }}
          alias: ${{ secrets.ANDROID_ALIAS || '' }}
          keyStorePassword: ${{ secrets.ANDROID_KEY_STORE_PASSWORD || '' }}
          keyPassword: ${{ secrets.ANDROID_KEY_PASSWORD || '' }}

      - name: Upload Android Artifact
        uses: actions/upload-artifact@v3
        with:
          name: app-release-signed
          path: ${{ steps.sign_app.outputs.signedReleaseFile }}

  build-ios:
    name: Build iOS App
    runs-on: macos-latest
    needs: security-scan
    if: github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'both' || github.event.inputs.platform == null
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mobile/package.json'

      - name: Install dependencies
        run: cd mobile && npm ci

      - name: Install CocoaPods
        run: |
          cd mobile/ios
          pod install

      - name: Build iOS App
        env:
          DEVELOPER_APP_IDENTIFIER: ${{ secrets.IOS_APP_IDENTIFIER || '' }}
          PROVISIONING_PROFILE_SPECIFIER: ${{ secrets.IOS_PROVISIONING_PROFILE_SPECIFIER || '' }}
          TEAM_ID: ${{ secrets.IOS_TEAM_ID || '' }}
          APPLE_CERTIFICATE: ${{ secrets.IOS_CERTIFICATE || '' }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.IOS_CERTIFICATE_PASSWORD || '' }}
          APPLE_P12_PASSWORD: ${{ secrets.IOS_P12_PASSWORD || '' }}
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD || '' }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD || '' }}
        run: |
          cd mobile/ios
          fastlane build

      - name: Upload iOS Artifact
        uses: actions/upload-artifact@v3
        with:
          name: ios-app
          path: mobile/ios/build/RetreatAndBeMobile.ipa

  deploy-testflight:
    name: Deploy to TestFlight
    runs-on: macos-latest
    needs: build-ios
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'staging' || github.event.inputs.environment == 'production'))
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Download iOS Artifact
        uses: actions/download-artifact@v3
        with:
          name: ios-app
          path: mobile/ios/build

      - name: Deploy to TestFlight
        env:
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD || '' }}
          APPLE_API_KEY_ID: ${{ secrets.APPLE_API_KEY_ID || '' }}
          APPLE_API_ISSUER_ID: ${{ secrets.APPLE_API_ISSUER_ID || '' }}
          APPLE_API_KEY_CONTENT: ${{ secrets.APPLE_API_KEY_CONTENT || '' }}
        run: |
          cd mobile/ios
          fastlane upload_to_testflight

  deploy-play-store:
    name: Deploy to Play Store
    runs-on: ubuntu-latest
    needs: build-android
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'staging' || github.event.inputs.environment == 'production'))
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Download Android Artifact
        uses: actions/download-artifact@v3
        with:
          name: app-release-signed
          path: mobile/android/app/build/outputs/apk/release

      - name: Setup Google Play
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.PLAY_STORE_SERVICE_ACCOUNT_JSON || '' }}
          packageName: com.retreatandbemobile
          releaseFiles: mobile/android/app/build/outputs/apk/release/app-release-signed.apk
          track: ${{ github.event.inputs.environment == 'production' && 'production' || 'internal' }}
          status: completed
          inAppUpdatePriority: 3

  post-deploy-verification:
    name: Mobile Post-Deploy Verification
    runs-on: ubuntu-latest
    needs: [deploy-testflight, deploy-play-store]
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && (github.event.inputs.environment == 'staging' || github.event.inputs.environment == 'production'))
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run Mobile Verification
        run: |
          chmod +x ./mobile/scripts/post-deploy-verification.sh
          ./mobile/scripts/post-deploy-verification.sh

  build-docker-image:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    needs: [security-scan]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME || '' }}
          password: ${{ secrets.DOCKERHUB_TOKEN || '' }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: rb2-app/mobile-service
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,format=short
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}
            type=raw,value=${{ github.event.inputs.environment || 'dev' }},enable=${{ github.event_name == 'workflow_dispatch' }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: ./mobile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=rb2-app/mobile-service:buildcache
          cache-to: type=registry,ref=rb2-app/mobile-service:buildcache,mode=max

  deploy-to-kubernetes:
    name: Deploy to Kubernetes
    runs-on: ubuntu-latest
    needs: [build-docker-image, post-deploy-verification]
    if: github.event.inputs.environment != ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: 'v3.10.0'

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.25.0'

      - name: Set Kubernetes Context
        uses: azure/k8s-set-context@v3
        with:
          kubeconfig: ${{ secrets[format('KUBE_CONFIG_{0}', github.event.inputs.environment)] || '' }}

      - name: Deploy to Kubernetes
        run: |
          cd infrastructure/scripts
          ./deploy-mobile-service.sh ${{ github.event.inputs.environment }}

      - name: Verify Deployment
        run: |
          kubectl rollout status deployment/mobile-service -n rb2-mobile --timeout=300s

  verify-web-mobile-sync:
    name: Verify Web-Mobile Synchronization
    runs-on: ubuntu-latest
    needs: deploy-to-kubernetes
    if: github.event.inputs.environment != ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: |
          cd mobile
          npm install axios colors

      - name: Run Sync Verification
        env:
          WEB_API_URL: ${{ github.event.inputs.environment == 'production' && 'https://api.rb2.com' || format('https://api-{0}.rb2.com', github.event.inputs.environment) }}
          MOBILE_API_URL: ${{ github.event.inputs.environment == 'production' && 'https://api.rb2.com/mobile' || format('https://api-{0}.rb2.com/mobile', github.event.inputs.environment) }}
          AUTH_TOKEN: ${{ secrets.TEST_AUTH_TOKEN || '' }}
        run: |
          cd mobile/scripts
          ./verify-web-mobile-sync.js
