name: Unified Frontend CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    paths:
      - 'packages/**'
      - '.github/workflows/unified-frontend-ci-cd.yml'
  pull_request:
    branches: [main, develop]
    paths:
      - 'packages/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'test'
        type: choice
        options:
          - test
          - staging
          - production
      platform:
        description: 'Platform to build'
        required: true
        default: 'all'
        type: choice
        options:
          - web
          - ios
          - android
          - all

# Environment variables used across jobs
env:
  NODE_VERSION: '18'
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

jobs:
  lint:
    name: Lint Code
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test

      - name: Upload test coverage
        uses: actions/upload-artifact@v3
        with:
          name: test-coverage
          path: packages/*/coverage
          retention-days: 14

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --production

      - name: Run OWASP Dependency-Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'RB2-Unified-Frontend'
          path: './packages'
          format: 'HTML'
          out: 'reports'
          args: >-
            --failOnCVSS 7
            --enableRetired

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        with:
          name: security-reports
          path: reports
          retention-days: 14

  build-web:
    name: Build Web Application
    runs-on: ubuntu-latest
    needs: security-scan
    if: ${{ github.event.inputs.platform == 'web' || github.event.inputs.platform == 'all' || github.event.inputs.platform == null }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build web application
        run: npm run build -- --filter=web

      - name: Upload web build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: web-build
          path: packages/web/dist
          retention-days: 7

  build-ios:
    name: Build iOS Application
    runs-on: macos-latest
    needs: security-scan
    if: ${{ github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all' || github.event.inputs.platform == null }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Install CocoaPods
        run: gem install cocoapods

      - name: Install iOS dependencies
        run: cd packages/mobile/ios && pod install

      - name: Build iOS application
        run: |
          cd packages/mobile/ios
          xcodebuild -workspace RB2.xcworkspace -scheme RB2 -configuration Release -sdk iphoneos -archivePath ./build/RB2.xcarchive archive

      - name: Upload iOS build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ios-build
          path: packages/mobile/ios/build
          retention-days: 7

  build-android:
    name: Build Android Application
    runs-on: ubuntu-latest
    needs: security-scan
    if: ${{ github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all' || github.event.inputs.platform == null }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup JDK
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Build Android application
        run: |
          cd packages/mobile/android
          ./gradlew assembleRelease

      - name: Upload Android build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: android-build
          path: packages/mobile/android/app/build/outputs/apk/release
          retention-days: 7

  deploy-web:
    name: Deploy Web Application
    runs-on: ubuntu-latest
    needs: build-web
    if: ${{ (github.event.inputs.platform == 'web' || github.event.inputs.platform == 'all' || github.event.inputs.platform == null) && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch') }}
    environment:
      name: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production') || 'test' }}
    steps:
      - name: Download web build artifacts
        uses: actions/download-artifact@v3
        with:
          name: web-build
          path: web-build

      - name: Deploy to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          projectId: rb2-unified-frontend
          channelId: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'live') || 'preview' }}

  deploy-ios:
    name: Deploy iOS Application
    runs-on: macos-latest
    needs: build-ios
    if: ${{ (github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all' || github.event.inputs.platform == null) && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production') }}
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Download iOS build artifacts
        uses: actions/download-artifact@v3
        with:
          name: ios-build
          path: ios-build

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Install fastlane
        run: gem install fastlane

      - name: Deploy to App Store
        run: |
          cd packages/mobile/ios
          fastlane release
        env:
          APP_STORE_CONNECT_API_KEY_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_KEY_ID }}
          APP_STORE_CONNECT_API_KEY_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ISSUER_ID }}
          APP_STORE_CONNECT_API_KEY_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY_KEY }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}

  deploy-android:
    name: Deploy Android Application
    runs-on: ubuntu-latest
    needs: build-android
    if: ${{ (github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all' || github.event.inputs.platform == null) && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production') }}
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Download Android build artifacts
        uses: actions/download-artifact@v3
        with:
          name: android-build
          path: android-build

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Install fastlane
        run: gem install fastlane

      - name: Deploy to Play Store
        run: |
          cd packages/mobile/android
          fastlane release
        env:
          SUPPLY_JSON_KEY: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}

  version-management:
    name: Version Management
    runs-on: ubuntu-latest
    needs: [deploy-web, deploy-ios, deploy-android]
    if: ${{ always() && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production') }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Bump version
        run: npx lerna version patch --yes --no-push

      - name: Push version changes
        run: git push --follow-tags