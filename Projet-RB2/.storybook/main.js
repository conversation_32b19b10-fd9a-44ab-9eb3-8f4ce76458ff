/** @type { import('@storybook/react-webpack5').StorybookConfig } */
const config = {
  stories: [
    '../frontend/src/atomic/**/*.stories.@(js|jsx|ts|tsx|mdx)',
    '../frontend/src/atomic/**/stories/*.@(js|jsx|ts|tsx|mdx)',
  ],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-a11y',
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  staticDirs: ['../public'],
};

export default config; 