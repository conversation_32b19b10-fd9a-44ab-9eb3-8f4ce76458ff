# Guide des composants UI - Retreat And Be

Ce document décrit les composants UI réutilisables disponibles dans l'application Retreat And Be. Ces composants sont conçus pour améliorer l'expérience utilisateur avec des animations, des transitions et des états de chargement élégants.

## Table des matières

1. [Composants d'animation](#composants-danimation)
   - [FadeIn](#fadein)
   - [AnimatedCounter](#animatedcounter)
2. [Composants de chargement](#composants-de-chargement)
   - [Skeleton](#skeleton)
   - [SkeletonText](#skeletontext)
   - [SkeletonCard](#skeletoncard)
   - [SkeletonAvatar](#skeletonavatar)
   - [SkeletonChart](#skeletonchart)
3. [Bonnes pratiques](#bonnes-pratiques)
   - [Quand utiliser les animations](#quand-utiliser-les-animations)
   - [Accessibilité](#accessibilité)
   - [Performance](#performance)

## Composants d'animation

### FadeIn

Le composant `FadeIn` permet d'animer l'apparition d'un élément avec un effet de fondu et de mouvement.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | ReactNode | - | Le contenu à animer |
| `duration` | number | 500 | Durée de l'animation en millisecondes |
| `delay` | number | 0 | Délai avant le début de l'animation en millisecondes |
| `direction` | 'up' \| 'down' \| 'left' \| 'right' \| 'none' | 'up' | Direction de l'animation |
| `distance` | number | 20 | Distance de déplacement en pixels |
| `className` | string | '' | Classes CSS supplémentaires |

#### Exemple d'utilisation

```jsx
import { FadeIn } from '../components/ui/FadeIn';

const MyComponent = () => {
  return (
    <div>
      <FadeIn>
        <h1>Titre avec animation</h1>
      </FadeIn>
      
      <FadeIn delay={200} direction="left">
        <p>Paragraphe avec animation depuis la gauche</p>
      </FadeIn>
      
      <FadeIn delay={400} direction="right" duration={800}>
        <button>Bouton avec animation depuis la droite</button>
      </FadeIn>
    </div>
  );
};
```

### AnimatedCounter

Le composant `AnimatedCounter` permet d'animer l'incrémentation ou la décrémentation d'un nombre.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | number | - | La valeur finale à afficher |
| `duration` | number | 1000 | Durée de l'animation en millisecondes |
| `formatValue` | (value: number) => string | val => val.toLocaleString() | Fonction pour formater la valeur |
| `className` | string | '' | Classes CSS supplémentaires |

#### Exemple d'utilisation

```jsx
import { AnimatedCounter } from '../components/ui/AnimatedCounter';

const StatCard = ({ value, label }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-sm font-medium text-gray-500">{label}</h3>
      <p className="text-2xl font-bold mt-2">
        <AnimatedCounter value={value} />
      </p>
    </div>
  );
};

// Avec formatage personnalisé
const RevenueCard = ({ value }) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-sm font-medium text-gray-500">Revenus</h3>
      <p className="text-2xl font-bold mt-2">
        <AnimatedCounter 
          value={value} 
          formatValue={(val) => `${val.toLocaleString()} €`}
        />
      </p>
    </div>
  );
};
```

## Composants de chargement

### Skeleton

Le composant `Skeleton` affiche un placeholder animé pendant le chargement des données.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `width` | string \| number | '100%' | Largeur du skeleton |
| `height` | string \| number | '1rem' | Hauteur du skeleton |
| `borderRadius` | string \| number | '0.25rem' | Rayon de bordure du skeleton |
| `className` | string | '' | Classes CSS supplémentaires |
| `animate` | boolean | true | Activer/désactiver l'animation |

#### Exemple d'utilisation

```jsx
import { Skeleton } from '../components/ui/Skeleton';

const LoadingState = () => {
  return (
    <div>
      <Skeleton width={200} height={24} className="mb-4" />
      <Skeleton width="100%" height={16} className="mb-2" />
      <Skeleton width="100%" height={16} className="mb-2" />
      <Skeleton width="75%" height={16} />
    </div>
  );
};
```

### SkeletonText

Le composant `SkeletonText` affiche plusieurs lignes de texte en chargement.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `lines` | number | 3 | Nombre de lignes à afficher |
| `className` | string | '' | Classes CSS supplémentaires |
| `lineHeight` | string \| number | '1rem' | Hauteur de chaque ligne |
| `lineSpacing` | string \| number | '0.5rem' | Espacement entre les lignes |

#### Exemple d'utilisation

```jsx
import { SkeletonText } from '../components/ui/Skeleton';

const LoadingArticle = () => {
  return (
    <div>
      <SkeletonText lines={1} lineHeight="2rem" className="mb-4" />
      <SkeletonText lines={5} className="mb-6" />
      <SkeletonText lines={3} lineHeight="0.875rem" />
    </div>
  );
};
```

### SkeletonCard

Le composant `SkeletonCard` affiche une carte en chargement.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | string | '' | Classes CSS supplémentaires |
| `height` | string \| number | '12rem' | Hauteur de la carte |

#### Exemple d'utilisation

```jsx
import { SkeletonCard } from '../components/ui/Skeleton';

const LoadingCards = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <SkeletonCard />
      <SkeletonCard />
      <SkeletonCard />
    </div>
  );
};
```

### SkeletonChart

Le composant `SkeletonChart` affiche un graphique en chargement.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `width` | string \| number | '100%' | Largeur du graphique |
| `height` | string \| number | '15rem' | Hauteur du graphique |
| `className` | string | '' | Classes CSS supplémentaires |

#### Exemple d'utilisation

```jsx
import { SkeletonChart } from '../components/ui/Skeleton';

const LoadingDashboard = () => {
  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Tableau de bord</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <SkeletonChart height="300px" />
        <SkeletonChart height="300px" />
      </div>
    </div>
  );
};
```

## Bonnes pratiques

### Quand utiliser les animations

- **Utilisez les animations avec parcimonie** : Trop d'animations peuvent distraire l'utilisateur et nuire à l'expérience.
- **Animez les éléments importants** : Utilisez les animations pour attirer l'attention sur les éléments importants.
- **Coordonnez les animations** : Utilisez les délais (`delay`) pour créer des séquences d'animations cohérentes.
- **Adaptez la durée** : Utilisez des durées plus courtes (300-500ms) pour les petits éléments et des durées plus longues (500-800ms) pour les grands éléments.

### Accessibilité

- **Respectez les préférences de réduction de mouvement** : Les utilisateurs peuvent avoir activé l'option "Réduire les animations" dans leur système d'exploitation. Respectez cette préférence en utilisant la media query `prefers-reduced-motion`.
- **Évitez les animations clignotantes** : Les animations clignotantes peuvent déclencher des crises d'épilepsie chez certaines personnes.
- **Assurez-vous que le contenu est accessible sans animations** : Les animations doivent être un enrichissement, pas une nécessité.

### Performance

- **Limitez le nombre d'animations simultanées** : Trop d'animations simultanées peuvent affecter les performances.
- **Utilisez les propriétés CSS optimisées** : Préférez animer les propriétés `transform` et `opacity` qui sont optimisées pour les performances.
- **Utilisez `will-change`** : Pour les animations complexes, utilisez la propriété CSS `will-change` pour indiquer au navigateur qu'un élément va être animé.
- **Testez sur des appareils de faible puissance** : Assurez-vous que les animations fonctionnent correctement sur des appareils moins puissants.
