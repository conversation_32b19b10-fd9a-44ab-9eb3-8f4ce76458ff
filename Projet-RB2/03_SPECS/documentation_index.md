# Index de la Documentation Technique - Retreat And Be

## Vue d'Ensemble

Ce document sert d'index central pour toute la documentation technique du projet Retreat And Be. Il est organisé par catégories pour faciliter la navigation et la recherche d'informations spécifiques.

## Architecture

- [Architecture Technique Globale](../02_AI-DOCS/Architecture/architecture_technique.md) - Vue d'ensemble de l'architecture du système
- [Architecture des Microservices](../Projet-RB2/MICROSERVICES-ARCHITECTURE.md) - Description détaillée de l'architecture des microservices
- [Intégration des Microservices](../Projet-RB2/MICROSERVICES-INTEGRATION.md) - Guide d'intégration entre les différents microservices
- [Architecture de Scalabilité](../Projet-RB2/docs/architecture/scalability.md) - Stratégies de mise à l'échelle du système
- [Architecture du Système de Recommandations](../Projet-RB2/docs/architecture/recommendation-system.md) - Détails sur le système de recommandations IA

## Spécifications Fonctionnelles

### Fonctionnalités Principales

- [Retreat-Pro-Matcher](features/retreat_pro_matcher.md) - Système de mise en relation entre utilisateurs et professionnels
- [Système de Monétisation](../Projet-RB2/Financial-Management/API.md) - API et fonctionnalités du système de monétisation
- [Programme de Fidélité](../Projet-RB2/docs/tokens/token-architecture.md) - Architecture du système de tokens RandB
- [Système de Notifications](../Projet-RB2/docs/NOTIFICATIONS-TECHNICAL.md) - Documentation technique du système de notifications

### Services Complémentaires

- [Location de Voitures](../Projet-RB2/Car-Rental/README.md) - Service de location de voitures
- [Comparaison d'Assurances](../Projet-RB2/Compare-Insurance/README.md) - Service de comparaison d'assurances
- [Recherche de Vols](../Projet-RB2/Flight-Finder/README.md) - Service de recherche de vols
- [Réservation d'Hôtels](../Projet-RB2/Hotel-Booking/README.md) - Service de réservation d'hôtels

## Spécifications Techniques

### Backend

- [API Reference](../Projet-RB2/docs/api-reference.md) - Documentation complète de l'API
- [Modèles de Données](../Projet-RB2/Backend-NestJS/prisma/schema.prisma) - Schéma de la base de données
- [Authentification](../Projet-RB2/Security/README-SECURITY-MONITORING.md) - Documentation sur le système d'authentification
- [Sécurité](../Projet-RB2/SECURITY.md) - Mesures de sécurité implémentées

### Frontend

- [Architecture Frontend](../Projet-RB2/frontend-refonte/README.md) - Structure et organisation du frontend
- [Composants UI](../Projet-RB2/packages/ui/README.md) - Bibliothèque de composants UI réutilisables
- [Internationalisation](../Projet-RB2/Front-Audrey-V1-Main-main/INTERNATIONALIZATION-GUIDE.md) - Guide d'internationalisation
- [Animations](../Projet-RB2/docs/ANIMATIONS.md) - Documentation sur les animations

### Intelligence Artificielle

- [Agents IA](../Projet-RB2/superagent/README.md) - Documentation sur les agents d'intelligence artificielle
- [Système de Recommandation](../Projet-RB2/docs/RECOMMENDATION-SYSTEM-ROADMAP.md) - Feuille de route du système de recommandation
- [Analyse de Sentiment](../Projet-RB2/Analyzer/README.md) - Documentation sur l'analyse de sentiment

## Guides d'Implémentation

- [Guide de Déploiement](../Projet-RB2/docs/deployment.md) - Instructions pour le déploiement
- [Guide d'Intégration](../Projet-RB2/README-INTEGRATION.md) - Guide pour l'intégration des différents composants
- [Guide de Monitoring](../Projet-RB2/docs/monitoring.md) - Configuration et utilisation du monitoring
- [Guide de Sécurité](../Projet-RB2/Security/SECURITY.md) - Bonnes pratiques de sécurité

## Feuilles de Route

- [Roadmap Globale](../Projet-RB2/ROADMAP-GLOBAL.md) - Vue d'ensemble de la roadmap du projet
- [Roadmap Backend NestJS](../Projet-RB2/Backend-NestJS/ROADMAP-BACKEND-NESTJS.md) - Roadmap spécifique au backend NestJS
- [Roadmap Frontend](../Projet-RB2/ROADMAP-FRONTEND-PAGES.md) - Roadmap spécifique au frontend
- [Roadmap Sécurité](../Projet-RB2/ROADMAP-SECURITY.md) - Roadmap des améliorations de sécurité
- [Roadmap Mobile](../Projet-RB2/ROADMAP-MOBILE.md) - Roadmap pour le développement mobile

## Rapports et Analyses

- [Audit et Recommandations](../Projet-RB2/Audit_et_Recommandations.md) - Résultats d'audit et recommandations
- [Optimisation des Performances](../Projet-RB2/PERFORMANCE-OPTIMIZATION.md) - Stratégies d'optimisation des performances
- [Optimisation SEO](../Projet-RB2/SEO-OPTIMIZATION.md) - Stratégies d'optimisation pour les moteurs de recherche
- [Optimisation de l'Accessibilité](../Projet-RB2/ACCESSIBILITY-OPTIMIZATION.md) - Améliorations pour l'accessibilité

## Tests

- [Plan de Test](../Projet-RB2/TEST-PLAN.md) - Plan global de test
- [Tests Backend](../Projet-RB2/ROADMAP-BACKEND-TESTING.md) - Stratégie de test pour le backend
- [Tests Frontend](../Projet-RB2/frontend/README.md#tests) - Stratégie de test pour le frontend
- [Tests de Performance](../Projet-RB2/test-performance-report.md) - Résultats des tests de performance

## Guides de Contribution

- [Guide de Contribution](../Projet-RB2/Analyzer/CONTRIBUTING.md) - Comment contribuer au projet
- [Conventions de Codage](../02_AI-DOCS/Conventions/coding_conventions_template.md) - Standards de codage
- [Conventions de Design](../02_AI-DOCS/Conventions/design_conventions_template.md) - Standards de design
- [Workflow Git](../Projet-RB2/docs/developer-wiki.md) - Processus de gestion des branches et des pull requests

## Ressources Additionnelles

- [Glossaire](../Projet-RB2/docs/glossary.md) - Définitions des termes techniques
- [FAQ Développeurs](../Projet-RB2/docs/developer-faq.md) - Questions fréquemment posées
- [Troubleshooting](../Projet-RB2/docs/troubleshooting.md) - Guide de résolution des problèmes courants
- [Références Externes](../Projet-RB2/docs/external-references.md) - Liens vers des ressources externes utiles

## Historique des Modifications

- [Changelog](../Projet-RB2/CHANGELOG.md) - Historique des versions et des modifications
- [Journal de Migration](../Projet-RB2/MIGRATION-JOURNAL.md) - Détails sur les migrations majeures
- [Rapport de Projet](../Projet-RB2/PROJECT-REPORT.md) - Rapport global sur l'état du projet
