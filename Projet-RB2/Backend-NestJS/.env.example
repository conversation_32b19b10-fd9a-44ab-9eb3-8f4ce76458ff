# Application
NODE_ENV=development
PORT=3000
API_PREFIX=/api/v1
APP_NAME=Retreat And Be
APP_DESCRIPTION=Retreat And Be API
APP_VERSION=1.0.0
FRONTEND_URL=http://localhost:3001

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/retreat_and_be?schema=public

# JWT
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# CORS
CORS_ORIGIN=http://localhost:3000

# Upload
UPLOAD_DIR=uploads
FILE_STORAGE_TYPE=local
FILE_STORAGE_LOCAL_PATH=./uploads
FILE_STORAGE_S3_BUCKET=retreatandbe
FILE_STORAGE_S3_REGION=eu-west-3
FILE_STORAGE_S3_ACCESS_KEY=your-s3-access-key
FILE_STORAGE_S3_SECRET_KEY=your-s3-secret-key

# Matching
MATCHING_NOTIFICATION_THRESHOLD=80
MATCHING_DEFAULT_LIMIT=10
MATCHING_MAX_LIMIT=100
MATCHING_WEIGHT_SKILL=0.35
MATCHING_WEIGHT_LOCATION=0.2
MATCHING_WEIGHT_RATING=0.2
MATCHING_WEIGHT_AVAILABILITY=0.15
MATCHING_WEIGHT_BUDGET=0.1
MATCHING_ENABLE_NOTIFICATIONS=true
MATCHING_ENABLE_CACHING=true
MATCHING_CACHE_TTL=3600
MATCHING_ENABLE_DETAILED_LOGGING=false
MATCHING_ENABLE_REMINDERS=true
MATCHING_REMINDER_INTERVAL=24

# Notifications
NOTIFICATIONS_ENABLE_EMAIL=true
NOTIFICATIONS_ENABLE_PUSH=true
NOTIFICATIONS_ENABLE_IN_APP=true
NOTIFICATIONS_THROTTLE_INTERVAL=15
NOTIFICATIONS_EMAIL_FROM=<EMAIL>
NOTIFICATIONS_EMAIL_REPLY_TO=<EMAIL>
NOTIFICATIONS_EMAIL_TEMPLATE_MATCHING=matching-notification
NOTIFICATIONS_EMAIL_TEMPLATE_REMINDER=matching-reminder
NOTIFICATIONS_PUSH_VAPID_PUBLIC_KEY=your-vapid-public-key
NOTIFICATIONS_PUSH_VAPID_PRIVATE_KEY=your-vapid-private-key
NOTIFICATIONS_PUSH_SUBJECT=mailto:<EMAIL>

# Email
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Payments
PAYMENT_STRIPE_SECRET_KEY=your-stripe-secret-key
PAYMENT_STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
PAYMENT_CURRENCY=EUR

# External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
SENDGRID_API_KEY=your-sendgrid-api-key

# Chiffrement général
ENCRYPTION_ENABLED=true

# Gestion des clés
KEY_ROTATION_INTERVAL=604800000  # 7 jours
KEY_EXPIRY=2592000000  # 30 jours

# HashiCorp Vault
VAULT_ENABLED=false
VAULT_ENDPOINT=http://localhost:8200
VAULT_TOKEN=
VAULT_NAMESPACE=
VAULT_API_VERSION=v1
VAULT_KEY_PREFIX=retreat-and-be
VAULT_SSL_VERIFY=true
VAULT_TIMEOUT=5000
VAULT_RETRY_COUNT=3
VAULT_RETRY_FACTOR=2
VAULT_RETRY_MIN_TIMEOUT=1000
VAULT_RETRY_MAX_TIMEOUT=10000

# Chiffrement financier
FINANCIAL_ENCRYPTION_ENABLED=true
FINANCIAL_ENCRYPTION_ALGORITHM=aes-256-gcm

# Chiffrement de bout en bout
E2E_ENCRYPTION_ENABLED=true
E2E_ENCRYPTION_CURVE=prime256v1
E2E_ENCRYPTION_ALGORITHM=aes-256-gcm

# Chiffrement homomorphique
HOMOMORPHIC_ENCRYPTION_ENABLED=false
HOMOMORPHIC_ENCRYPTION_SCHEME=BFV
HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL=128
HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE=4096
HOMOMORPHIC_ENCRYPTION_PLAINTEXT_MODULUS=1024
HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS=60,40,40,60

# Chiffrement résistant aux ordinateurs quantiques
QUANTUM_RESISTANT_ENABLED=false
QUANTUM_RESISTANT_ALGORITHM=hybrid
QUANTUM_RESISTANT_KEY_SIZE=3072
QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM=rsa
QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE=4096

# Chiffrement pour les services financiers
FINANCIAL_ENCRYPTION_ENABLED=true
FINANCIAL_ENCRYPTION_ALGORITHM=aes-256-gcm

# Chiffrement pour le stockage décentralisé
STORAGE_ENCRYPTION_ENABLED=true
STORAGE_ENCRYPTION_ALGORITHM=aes-256-gcm
STORAGE_ENCRYPTION_RSA_BITS=4096

# Chiffrement des données sensibles au repos
SENSITIVE_DATA_ENCRYPTION_ENABLED=true
SENSITIVE_DATA_ENCRYPTION_ALGORITHM=aes-256-gcm
SENSITIVE_DATA_FIELDS=password,creditCard,cardNumber,cvv,ssn,socialSecurityNumber,passport
SENSITIVE_DATA_TYPES=financial,personal,health,authentication,location

# Zero Trust et mTLS
ZERO_TRUST_MTLS_ENABLED=false
MTLS_CERT_PATH=./certs/server.crt
MTLS_KEY_PATH=./certs/server.key
MTLS_CA_PATH=./certs/ca.crt
MTLS_REJECT_UNAUTHORIZED=true
MTLS_TRUSTED_CNS=client.retreatandbe.com
MTLS_TRUSTED_OUS=Clients
MTLS_CRL_PATH=./certs/ca.crl
MTLS_OCSP_ENABLED=false
CERTS_DIR=./certs

# Rotation des certificats
ENABLE_CERT_AUTO_ROTATION=true
CERT_ROTATION_THRESHOLD_DAYS=30

# Révocation et surveillance des certificats
MTLS_OCSP_ENABLED=false
OCSP_PORT=9080
OCSP_URL=http://localhost:9080
CRL_UPDATE_INTERVAL_HOURS=24
CERT_ALERT_THRESHOLD_DAYS=14
CERT_SCAN_INTERVAL_HOURS=6

# Journalisation cryptographique
ENCRYPTION_LOGGING_ENABLED=true
ENCRYPTION_LOGGING_LEVEL=internal
ENCRYPTION_LOGGING_LOG_TO_FILE=true
ENCRYPTION_LOGGING_FILE_PATH=./logs/crypto-operations.log
ENCRYPTION_LOGGING_ROTATION_SIZE_BYTES=10485760
ENCRYPTION_LOGGING_SIGN_LOGS=true
ENCRYPTION_LOGGING_ENCRYPT_SENSITIVE_LOGS=true
ENCRYPTION_LOGGING_RETENTION_DAYS=90
ENCRYPTION_LOGGING_ANONYMIZE_USER_IDS=true

# Performance cryptographique
ENCRYPTION_PERFORMANCE_USE_WORKER_THREADS=false
ENCRYPTION_PERFORMANCE_MAX_WORKER_THREADS=3
ENCRYPTION_PERFORMANCE_BATCH_SIZE=10
ENCRYPTION_PERFORMANCE_CACHE_ENABLED=true
ENCRYPTION_PERFORMANCE_CACHE_MAX_SIZE=1000
ENCRYPTION_PERFORMANCE_CACHE_TTL_MS=60000
ENCRYPTION_PERFORMANCE_ADAPTIVE_BATCHING=true
ENCRYPTION_PERFORMANCE_ADAPTIVE_CACHING=true
ENCRYPTION_PERFORMANCE_MONITORING_ENABLED=true
ENCRYPTION_PERFORMANCE_MONITORING_INTERVAL_MS=60000

# Tests automatisés
TESTING_ENABLED=true
CRYPTO_TESTING_ENABLED=true
CRYPTO_AUTO_TEST_INTERVAL_MS=3600000
CRYPTO_TEST_DATA_SIZE=100
CRYPTO_TEST_TIMEOUT=30000
PERFORMANCE_TESTING_ENABLED=true
PERFORMANCE_TEST_ITERATIONS=100
PERFORMANCE_TEST_DATA_SIZE=1024
SECURITY_TESTING_ENABLED=true
SECURITY_SCAN_INTERVAL=86400000
VULNERABILITY_SCAN_ENABLED=true
PENETRATION_TEST_ENABLED=false
