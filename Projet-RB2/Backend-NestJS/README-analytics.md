# Module d'Analyse Avancée pour Créateurs - Retreat And Be

Ce module est responsable de l'analyse avancée des données pour les créateurs sur la plateforme Retreat And Be. Il fournit des API pour l'accès aux métriques, la visualisation des données, les prévisions et l'analyse comparative.

## Fonctionnalités

- Collecte et stockage des données d'engagement
- Métriques de base (vues, likes, commentaires)
- Tableaux de bord analytiques personnalisables
- Visualisations interactives
- Prévisions d'engagement
- Analyse des tendances temporelles
- Analyse comparative avec benchmarks
- Recommandations personnalisées

## Architecture

Le module est intégré au backend NestJS et s'intègre avec les autres modules de la plateforme.

```
                  ┌─────────────────────┐
                  │                     │
                  │  Frontend           │
                  │                     │
                  └─────────┬───────────┘
                            │
                            ▼
┌─────────────────────────────────────────────┐
│                                             │
│  API Gateway                                │
│                                             │
└─────────────────┬───────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Module d'Analyse Avancée pour Créateurs    │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Collecte    │    │ Métriques           │ │
│  │ de Données  │    │ de Base             │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Prévisions  │    │ Analyse             │ │
│  │ et Tendances│    │ Comparative         │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
└─────────────────────┬───────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Base de Données                            │
│                                             │
└─────────────────────────────────────────────┘
```

## Technologies

- **Backend**: NestJS, TypeScript
- **Base de données**: PostgreSQL avec Prisma ORM, TimescaleDB pour les séries temporelles
- **Analyse de données**: Pandas, NumPy, scikit-learn (via API Python)
- **Visualisation**: D3.js, Chart.js
- **Cache**: Redis
- **Monitoring**: Prometheus, Grafana

## API

### Métriques de Base

- `GET /api/analytics/metrics/:creatorId` - Obtenir les métriques de base pour un créateur
- `GET /api/analytics/metrics/:creatorId/content/:contentId` - Obtenir les métriques pour un contenu spécifique
- `GET /api/analytics/metrics/:creatorId/summary` - Obtenir un résumé des métriques pour un créateur

### Tableaux de Bord

- `GET /api/analytics/dashboards/:creatorId` - Obtenir les tableaux de bord d'un créateur
- `GET /api/analytics/dashboards/detail/:dashboardId` - Obtenir un tableau de bord spécifique
- `POST /api/analytics/dashboards/:creatorId` - Créer un nouveau tableau de bord
- `PUT /api/analytics/dashboards/:dashboardId` - Mettre à jour un tableau de bord
- `DELETE /api/analytics/dashboards/:dashboardId` - Supprimer un tableau de bord

### Prévisions et Tendances

- `GET /api/analytics/forecasting/:creatorId` - Obtenir les prévisions pour un créateur

### Analyse Comparative

- `GET /api/analytics/benchmarks/:creatorId` - Obtenir les benchmarks pour un créateur

## Intégration avec les Autres Modules

- **Module Utilisateur** : Récupération des informations utilisateur
- **Module Contenu** : Récupération des données de contenu
- **Module Événement** : Collecte des événements d'engagement
- **Module Recommandation** : Partage des insights pour les recommandations

## Modèles de Données

### EngagementMetric

```prisma
model EngagementMetric {
  id            String         @id @default(uuid())
  creatorId     String
  contentId     String
  contentType   ContentType
  views         Int            @default(0)
  likes         Int            @default(0)
  comments      Int            @default(0)
  shares        Int            @default(0)
  bookmarks     Int            @default(0)
  clickThroughs Int            @default(0)
  date          DateTime       @default(now())
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}
```

### AudienceMetric

```prisma
model AudienceMetric {
  id                String         @id @default(uuid())
  creatorId         String
  totalFollowers    Int            @default(0)
  newFollowers      Int            @default(0)
  lostFollowers     Int            @default(0)
  activeFollowers   Int            @default(0)
  demographics      Json?          // Données démographiques
  date              DateTime       @default(now())
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
}
```

### Dashboard

```prisma
model Dashboard {
  id            String         @id @default(uuid())
  creatorId     String
  name          String
  description   String?
  layout        Json           // Configuration du layout
  widgets       Widget[]       // Widgets du tableau de bord
  isDefault     Boolean        @default(false)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}
```

## Sécurité

- Authentification JWT pour toutes les API
- Autorisation basée sur les rôles (créateur, admin)
- Validation des entrées utilisateur
- Journalisation des accès aux données
- Chiffrement des données sensibles
