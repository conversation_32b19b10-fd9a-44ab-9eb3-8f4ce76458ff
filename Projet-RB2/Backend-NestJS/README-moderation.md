# Module de Modération de Contenu - Retreat And Be

Ce module est responsable de la modération automatique et manuelle du contenu sur la plateforme Retreat And Be. Il fournit des API pour la détection de contenu inapproprié, la gestion des signalements et l'application des règles de modération.

## Fonctionnalités

- Modération automatique de texte basée sur des règles et l'IA
- Modération automatique d'images basée sur des règles et l'IA
- Système de signalement de contenu inapproprié
- Gestion des actions de modération
- Tableau de bord pour les modérateurs
- Système de réputation des utilisateurs
- Métriques et statistiques de modération

## Architecture

Le module est intégré au backend NestJS et s'intègre avec les autres modules de la plateforme.

```
                  ┌─────────────────────┐
                  │                     │
                  │  Frontend           │
                  │                     │
                  └─────────┬───────────┘
                            │
                            ▼
┌─────────────────────────────────────────────┐
│                                             │
│  API Gateway                                │
│                                             │
└─────────────────┬───────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Module de Modération de Contenu            │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Modération  │    │ Modération          │ │
│  │ de Texte    │    │ d'Images            │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Gestion des │    │ Tableau de Bord     │ │
│  │ Signalements│    │ de Modération       │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
└─────────────────────┬───────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Base de Données                            │
│                                             │
└─────────────────────────────────────────────┘
```

## Technologies

- **Backend**: NestJS, TypeScript
- **Base de données**: PostgreSQL avec Prisma ORM
- **IA pour la modération**: TensorFlow.js
- **Cache**: Redis
- **Monitoring**: Prometheus, Grafana

## API

### Modération de Contenu

- `POST /api/moderation/content` - Modérer un contenu (texte ou image)
- `GET /api/moderation/text/rules` - Obtenir les règles de modération de texte
- `POST /api/moderation/text/rules` - Créer une règle de modération de texte
- `PUT /api/moderation/text/rules/:id` - Mettre à jour une règle de modération de texte
- `DELETE /api/moderation/text/rules/:id` - Supprimer une règle de modération de texte
- `GET /api/moderation/image/rules` - Obtenir les règles de modération d'images
- `POST /api/moderation/image/rules` - Créer une règle de modération d'images
- `PUT /api/moderation/image/rules/:id` - Mettre à jour une règle de modération d'images
- `DELETE /api/moderation/image/rules/:id` - Supprimer une règle de modération d'images

### Signalements

- `GET /api/moderation/reports` - Obtenir la liste des signalements
- `GET /api/moderation/reports/:id` - Obtenir un signalement par son ID
- `POST /api/moderation/reports` - Créer un nouveau signalement
- `PUT /api/moderation/reports/:id` - Mettre à jour un signalement
- `POST /api/moderation/reports/:id/actions` - Ajouter une action de modération à un signalement
- `GET /api/moderation/reports/:id/actions` - Obtenir les actions de modération d'un signalement
- `GET /api/moderation/stats` - Obtenir les statistiques des signalements

## Intégration avec les Autres Modules

- **Module Utilisateur** : Vérification des permissions, gestion de la réputation
- **Module Contenu** : Modération du contenu, mise à jour du statut du contenu
- **Module Notification** : Envoi de notifications aux utilisateurs concernés
- **Module Analytique** : Collecte de métriques pour l'analyse

## Modèles de Données

### TextModerationRule

```prisma
model TextModerationRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  pattern     String   // Regex ou mot-clé
  severity    Severity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

### ImageModerationRule

```prisma
model ImageModerationRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  category    String   // Type de contenu à détecter
  threshold   Float    // Seuil de confiance
  severity    Severity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

### Report

```prisma
model Report {
  id                String           @id @default(uuid())
  contentType       ContentType
  contentId         String
  reporterId        String
  reason            String
  description       String?
  status            ReportStatus     @default(PENDING)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  moderationActions ModerationAction[]
}
```

## Sécurité

- Authentification JWT pour toutes les API
- Autorisation basée sur les rôles (admin, modérateur)
- Validation des entrées utilisateur
- Journalisation des actions de modération
- Chiffrement des données sensibles
