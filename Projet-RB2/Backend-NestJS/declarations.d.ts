declare module 'speakeasy';
declare module 'qrcode';
declare module 'handlebars';

// Déclaration temporaire pour @prisma/client
declare module '@prisma/client' {
  export class PrismaClient {
    constructor(options?: any);
    $connect(): Promise<void>;
    $disconnect(): Promise<void>;
    $on(event: string, callback: (...args: any[]) => Promise<any>): void;
    $transaction<P extends Promise<any>[]>(arg: [...P]): Promise<any[]>;
    $transaction<R>(fn: (prisma: PrismaClient) => Promise<R>): Promise<R>;

    // Modèles de base de données
    course: any;
    lesson: any;
    enrollment: any;
    user: any;
    securityEvent: any;
    securityAlert: any;
    webhook: any;
    file: any;
    userLoginHistory: any;
    apiRequestLog: any;
    [key: string]: any; // Pour permettre l'accès dynamique aux modèles
  }

  export interface Course {
    id: string;
    title: string;
    description: string;
    level?: string;
    coverImage?: string;
    category?: string;
    createdAt: Date;
    updatedAt: Date;
  }

  export interface Lesson {
    id: string;
    title: string;
    content?: string;
    videoUrl?: string;
    order: number;
    courseId: string;
    createdAt: Date;
    updatedAt: Date;
  }

  export interface Enrollment {
    id: string;
    userId: string;
    courseId: string;
    progress: number;
    status: string;
    startedAt: Date;
    completedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
  }

  export enum WebhookStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    ERROR = 'ERROR'
  }
}
