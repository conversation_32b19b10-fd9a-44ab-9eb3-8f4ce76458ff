# Système Avancé de Gestion des Certificats mTLS

Ce document décrit le système avancé de gestion des certificats mTLS (mutual TLS) implémenté dans le projet Retreat And Be.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Services](#services)
4. [Configuration](#configuration)
5. [API REST](#api-rest)
6. [Rotation automatique](#rotation-automatique)
7. [Révocation de certificats](#révocation-de-certificats)
8. [Surveillance et métriques](#surveillance-et-métriques)
9. [OCSP et CRL](#ocsp-et-crl)
10. [Bonnes pratiques](#bonnes-pratiques)
11. [Dépannage](#dépannage)

## Vue d'ensemble

Le système avancé de gestion des certificats mTLS permet de sécuriser les communications entre les microservices de la plateforme Retreat And Be. Il fournit des fonctionnalités complètes pour :

- Générer et gérer des certificats X.509
- Effectuer la rotation automatique des certificats
- Révoquer des certificats et gérer les listes de révocation (CRL)
- Fournir un service OCSP (Online Certificate Status Protocol)
- Surveiller l'état des certificats et collecter des métriques
- Alerter sur les certificats expirant bientôt

## Architecture

Le système de gestion des certificats est composé de plusieurs services interconnectés :

```
┌─────────────────────────┐     ┌─────────────────────────┐
│ CertificateManagement   │     │ CertificateRotation     │
│ Service                 │     │ Service                 │
│                         │     │                         │
│ - Génération de CA      │     │ - Rotation automatique  │
│ - Génération de certif. │     │ - Validation            │
│ - Validation            │     │ - Sauvegarde/Restauration│
└───────────┬─────────────┘     └───────────┬─────────────┘
            │                               │
            │                               │
┌───────────▼───────────┐     ┌─────────────▼───────────┐
│ CertificateRevocation │     │ CertificateMonitoring   │
│ Service               │     │ Service                 │
│                       │     │                         │
│ - Révocation          │     │ - Surveillance          │
│ - Gestion des CRL     │     │ - Métriques             │
│ - Service OCSP        │     │ - Alertes               │
└───────────────────────┘     └─────────────────────────┘
```

## Services

### CertificateManagementService

Ce service gère la création et la validation des certificats X.509 pour l'authentification mTLS. Il fournit des méthodes pour :

- Générer une autorité de certification (CA)
- Générer des certificats clients et serveurs
- Révoquer des certificats
- Vérifier la validité des certificats

### CertificateRotationService

Ce service gère la rotation automatique des certificats mTLS. Il fournit des fonctionnalités pour :

- Vérifier l'état de santé des certificats
- Effectuer la rotation des certificats avant leur expiration
- Sauvegarder les certificats avant rotation
- Restaurer des certificats à partir de sauvegardes
- Planifier la rotation automatique des certificats

### CertificateRevocationService

Ce service gère la révocation des certificats et les listes de révocation (CRL). Il fournit des fonctionnalités pour :

- Révoquer des certificats
- Générer et mettre à jour les listes de révocation (CRL)
- Fournir un service OCSP (Online Certificate Status Protocol)
- Vérifier si un certificat est révoqué

### CertificateMonitoringService

Ce service surveille l'état des certificats et collecte des métriques. Il fournit des fonctionnalités pour :

- Scanner les certificats et collecter des métriques
- Identifier les certificats expirant bientôt
- Fournir des tableaux de bord pour la surveillance
- Enregistrer l'historique des rotations de certificats

## Configuration

La configuration des services de gestion des certificats se fait via les variables d'environnement suivantes :

```
# Zero Trust et mTLS
ZERO_TRUST_MTLS_ENABLED=true
MTLS_CERT_PATH=./certs/server.crt
MTLS_KEY_PATH=./certs/server.key
MTLS_CA_PATH=./certs/ca.crt
MTLS_REJECT_UNAUTHORIZED=true
MTLS_TRUSTED_CNS=client.retreatandbe.com
MTLS_TRUSTED_OUS=Clients
MTLS_CRL_PATH=./certs/ca.crl
MTLS_OCSP_ENABLED=false
CERTS_DIR=./certs

# Rotation des certificats
ENABLE_CERT_AUTO_ROTATION=true
CERT_ROTATION_THRESHOLD_DAYS=30

# Révocation et surveillance des certificats
OCSP_PORT=9080
OCSP_URL=http://localhost:9080
CRL_UPDATE_INTERVAL_HOURS=24
CERT_ALERT_THRESHOLD_DAYS=14
CERT_SCAN_INTERVAL_HOURS=6
```

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `ZERO_TRUST_MTLS_ENABLED` | Active ou désactive l'authentification mTLS | `false` |
| `MTLS_CERT_PATH` | Chemin vers le certificat du service | `./certs/server.crt` |
| `MTLS_KEY_PATH` | Chemin vers la clé privée du service | `./certs/server.key` |
| `MTLS_CA_PATH` | Chemin vers le certificat de l'autorité de certification | `./certs/ca.crt` |
| `MTLS_REJECT_UNAUTHORIZED` | Rejette les connexions non autorisées | `true` |
| `MTLS_TRUSTED_CNS` | Liste des noms communs (CN) de confiance | `client.retreatandbe.com` |
| `MTLS_TRUSTED_OUS` | Liste des unités organisationnelles (OU) de confiance | `Clients` |
| `MTLS_CRL_PATH` | Chemin vers la liste de révocation de certificats (CRL) | `./certs/ca.crl` |
| `MTLS_OCSP_ENABLED` | Active ou désactive la vérification OCSP | `false` |
| `CERTS_DIR` | Répertoire de stockage des certificats | `./certs` |
| `ENABLE_CERT_AUTO_ROTATION` | Active ou désactive la rotation automatique des certificats | `true` |
| `CERT_ROTATION_THRESHOLD_DAYS` | Nombre de jours avant expiration pour déclencher la rotation | `30` |
| `OCSP_PORT` | Port du service OCSP | `9080` |
| `OCSP_URL` | URL du service OCSP | `http://localhost:9080` |
| `CRL_UPDATE_INTERVAL_HOURS` | Intervalle de mise à jour de la CRL en heures | `24` |
| `CERT_ALERT_THRESHOLD_DAYS` | Nombre de jours avant expiration pour déclencher une alerte | `14` |
| `CERT_SCAN_INTERVAL_HOURS` | Intervalle de scan des certificats en heures | `6` |

## API REST

Le système expose plusieurs endpoints REST pour gérer les certificats :

### Gestion des certificats

| Méthode | Endpoint | Description | Rôles |
|---------|----------|-------------|-------|
| `POST` | `/security/certificates/generate/:serviceName` | Génère un certificat pour un service | `admin` |
| `GET` | `/security/certificates/health` | Vérifie l'état de santé des certificats | `admin` |
| `POST` | `/security/certificates/rotate` | Effectue la rotation des certificats | `admin` |
| `GET` | `/security/certificates/backups` | Liste les sauvegardes de certificats disponibles | `admin` |
| `POST` | `/security/certificates/restore/:backupId` | Restaure des certificats à partir d'une sauvegarde | `admin` |

### Surveillance et révocation

| Méthode | Endpoint | Description | Rôles |
|---------|----------|-------------|-------|
| `GET` | `/security/certificates/monitoring/metrics` | Obtient les métriques des certificats | `admin`, `security` |
| `GET` | `/security/certificates/monitoring/certificates` | Obtient les informations détaillées de tous les certificats | `admin`, `security` |
| `GET` | `/security/certificates/monitoring/certificates/:service` | Obtient les informations détaillées d'un certificat | `admin`, `security` |
| `GET` | `/security/certificates/monitoring/expiring` | Obtient la liste des certificats expirant bientôt | `admin`, `security` |
| `GET` | `/security/certificates/monitoring/expired` | Obtient la liste des certificats expirés | `admin`, `security` |
| `GET` | `/security/certificates/monitoring/revoked` | Obtient la liste des certificats révoqués | `admin`, `security` |
| `POST` | `/security/certificates/monitoring/revoke/:service` | Révoque un certificat | `admin` |
| `GET` | `/security/certificates/monitoring/crl` | Obtient la liste de révocation de certificats (CRL) | `admin`, `security` |
| `POST` | `/security/certificates/monitoring/crl/generate` | Génère une nouvelle liste de révocation de certificats (CRL) | `admin` |
| `POST` | `/security/certificates/monitoring/scan` | Déclenche un scan des certificats | `admin`, `security` |

## Rotation automatique

Le service `CertificateRotationService` effectue automatiquement la rotation des certificats avant leur expiration. Par défaut, la rotation est déclenchée 30 jours avant l'expiration du certificat.

La rotation automatique est planifiée pour s'exécuter tous les jours à minuit grâce à la tâche cron suivante :

```typescript
@Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
async scheduledCertificateRotation(): Promise<void> {
  // ...
}
```

Lors de la rotation, les certificats existants sont sauvegardés dans le répertoire `certs/backup/{timestamp}` avant d'être remplacés par de nouveaux certificats.

## Révocation de certificats

Le service `CertificateRevocationService` permet de révoquer des certificats et de gérer les listes de révocation (CRL).

### Processus de révocation

1. Le certificat est marqué comme révoqué dans le fichier `index.txt`
2. Une nouvelle liste de révocation (CRL) est générée
3. Le service OCSP est mis à jour pour refléter le nouveau statut du certificat

### Mise à jour de la CRL

La CRL est mise à jour automatiquement toutes les 24 heures (configurable) grâce à la tâche cron suivante :

```typescript
@Cron(CronExpression.EVERY_HOUR)
async scheduledCRLUpdate(): Promise<void> {
  // ...
}
```

## Surveillance et métriques

Le service `CertificateMonitoringService` surveille l'état des certificats et collecte des métriques.

### Métriques collectées

- Nombre total de certificats
- Nombre de certificats valides
- Nombre de certificats expirant bientôt
- Nombre de certificats expirés
- Nombre de certificats révoqués
- Moyenne des jours avant expiration
- Nombre de certificats par service
- Historique des rotations de certificats

### Scan des certificats

Le scan des certificats est effectué automatiquement toutes les 6 heures (configurable) grâce à la tâche cron suivante :

```typescript
@Cron(CronExpression.EVERY_HOUR)
async scheduledCertificateScan(): Promise<void> {
  // ...
}
```

## OCSP et CRL

Le système prend en charge deux méthodes pour vérifier le statut de révocation des certificats :

### CRL (Certificate Revocation List)

La CRL est une liste signée par l'autorité de certification (CA) qui contient les numéros de série des certificats révoqués. Elle est générée et mise à jour périodiquement.

### OCSP (Online Certificate Status Protocol)

Le service OCSP permet de vérifier le statut de révocation d'un certificat en temps réel. Il est plus efficace que la CRL car il ne nécessite pas de télécharger la liste complète des certificats révoqués.

Pour activer le service OCSP, définissez la variable d'environnement `MTLS_OCSP_ENABLED` à `true`.

## Bonnes pratiques

1. **Rotation régulière** : Effectuez la rotation des certificats régulièrement, même s'ils ne sont pas près d'expirer, pour limiter l'impact d'une compromission potentielle.

2. **Surveillance** : Surveillez l'état des certificats et configurez des alertes pour être informé des problèmes potentiels.

3. **Sauvegarde** : Sauvegardez régulièrement les certificats et les clés privées dans un emplacement sécurisé.

4. **Révocation** : Révoquez immédiatement les certificats compromis ou qui ne sont plus nécessaires.

5. **Validation** : Validez toujours les certificats clients lors des connexions mTLS et vérifiez leur statut de révocation.

6. **Automatisation** : Automatisez autant que possible les processus de gestion des certificats pour réduire les erreurs humaines.

7. **Journalisation** : Enregistrez toutes les opérations liées aux certificats pour faciliter l'audit et le dépannage.

## Dépannage

### Problèmes courants

1. **Certificat expiré** : Si un certificat a expiré, effectuez une rotation manuelle des certificats.

2. **Erreur de validation** : Vérifiez que le certificat a été signé par la bonne autorité de certification et que la chaîne de confiance est complète.

3. **Erreur de génération** : Assurez-vous que OpenSSL est installé et accessible dans le chemin système.

4. **Erreur de rotation** : Vérifiez les permissions des répertoires de certificats et assurez-vous que le service a les droits d'écriture nécessaires.

5. **Erreur OCSP** : Vérifiez que le service OCSP est en cours d'exécution et accessible à l'URL configurée.

### Restauration

En cas de problème après une rotation de certificats, vous pouvez restaurer les certificats précédents à partir des sauvegardes :

```typescript
const backups = await this.certificateRotationService.listCertificateBackups();
const latestBackup = backups[0]; // Les sauvegardes sont triées par ordre chronologique inversé
await this.certificateRotationService.restoreCertificatesFromBackup(`certs/backup/${latestBackup}`);
```

### Journalisation

Le système de gestion des certificats utilise le système de journalisation de NestJS pour enregistrer les événements importants. Consultez les journaux pour obtenir des informations détaillées sur les erreurs et les opérations effectuées.

```typescript
this.logger.log('Certificate rotation completed successfully');
this.logger.error('Failed to rotate certificates', error);
```
