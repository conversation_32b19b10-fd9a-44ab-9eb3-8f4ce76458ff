# Guide de migration des données - Sprint 4 à Sprint 5

Ce document détaille les étapes nécessaires pour migrer les données du système de recommandation du Sprint 4 au Sprint 5, en mettant l'accent sur la préservation et la transformation des données collectées pendant le Sprint 4 pour leur utilisation dans le système d'apprentissage continu du Sprint 5.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Prérequis](#prérequis)
3. [Types de données à migrer](#types-de-données-à-migrer)
4. [Processus de migration](#processus-de-migration)
5. [Validation post-migration](#validation-post-migration)
6. [Rollback en cas de problème](#rollback-en-cas-de-problème)
7. [Maintenance post-migration](#maintenance-post-migration)
8. [Annexes](#annexes)

## Vue d'ensemble

La migration des données du Sprint 4 au Sprint 5 implique plusieurs étapes importantes :

1. **Extraction** des données existantes (feedback, interactions, profils utilisateur)
2. **Transformation** des données pour les adapter au nouveau schéma et aux besoins d'apprentissage automatique
3. **Chargement** des données transformées dans les nouvelles structures
4. **Validation** pour s'assurer que les données sont correctement migrées et utilisables

Cette migration est cruciale car les données collectées pendant le Sprint 4 serviront de base d'apprentissage pour les modèles d'IA du Sprint 5.

## Prérequis

Avant de commencer la migration, assurez-vous que les conditions suivantes sont remplies :

1. **Environnement de développement**
   - Node.js 14+ et npm 6+
   - Python 3.8+ (pour les scripts de transformation de données)
   - PostgreSQL 12+
   - Accès à l'infrastructure d'apprentissage automatique

2. **Outils et scripts**
   - Script `prepare-data-for-sprint5.js` installé et configuré
   - Utilitaires de base de données (pg_dump, psql)
   - Outils ETL (si nécessaire)

3. **Sauvegardes**
   - Sauvegarde complète de la base de données du Sprint 4
   - Sauvegarde des fichiers de configuration

4. **Accès et permissions**
   - Accès administrateur à la base de données
   - Permissions pour créer/modifier des tables et des index
   - Accès aux systèmes de stockage de fichiers

## Types de données à migrer

### 1. Données de feedback utilisateur

Les données de feedback collectées via le système de feedback du Sprint 4 :

- Likes/dislikes
- Sauvegardes
- Masquages
- Signalements
- Commentaires
- Notes

### 2. Données d'interaction utilisateur

Les données d'interaction collectées via le système d'analytics du Sprint 4 :

- Vues de recommandations
- Clics sur les recommandations
- Temps passé sur les recommandations
- Vues des explications
- Interactions avec les explications

### 3. Profils utilisateur

Les données de profil utilisateur, y compris :

- Préférences explicites
- Intérêts
- Historique des interactions
- Segments d'utilisateurs

### 4. Métadonnées de contenu

Les métadonnées des contenus recommandés :

- Catégories
- Tags
- Attributs spécifiques (prix, durée, localisation, etc.)
- Popularité
- Évaluations moyennes

## Processus de migration

La migration se déroulera en plusieurs phases pour minimiser les risques et assurer la qualité des données.

### Phase 1 : Préparation et analyse

1. **Analyse des données existantes**

   ```bash
   # Exécuter le script d'analyse des données
   cd Backend-NestJS
   node scripts/analyze-sprint4-data.js --output=analysis-report.json
   ```

   Ce script analysera les données existantes et générera un rapport sur :
   - Le volume de données par type
   - La qualité des données (valeurs manquantes, incohérences)
   - Les statistiques de base (distribution, moyennes, etc.)

2. **Planification de la transformation**

   Basé sur l'analyse, définissez les règles de transformation pour chaque type de données :
   
   ```javascript
   // Exemple de règles de transformation
   const transformationRules = {
     feedback: {
       // Mapper les anciens types de feedback vers les nouveaux
       typeMapping: {
         'LIKE': 'POSITIVE',
         'DISLIKE': 'NEGATIVE',
         'SAVE': 'BOOKMARK',
         'HIDE': 'IGNORE',
         'REPORT': 'FLAG'
       },
       // Calculer un score de sentiment basé sur le commentaire
       derivedFields: [
         {
           name: 'sentimentScore',
           source: 'comment',
           transformation: 'sentiment_analysis'
         }
       ]
     },
     // Autres règles pour d'autres types de données
   };
   ```

3. **Configuration de l'environnement de migration**

   ```bash
   # Créer un environnement de migration isolé
   ./scripts/setup-migration-environment.sh
   ```

   Ce script configurera :
   - Une base de données temporaire pour la migration
   - Les connexions aux systèmes source et cible
   - Les journaux de migration

### Phase 2 : Extraction des données

1. **Extraction des données de feedback**

   ```bash
   # Extraire les données de feedback
   node scripts/prepare-data-for-sprint5.js --type=feedback --output=data/sprint5/feedback
   ```

2. **Extraction des données d'interaction**

   ```bash
   # Extraire les données d'interaction
   node scripts/prepare-data-for-sprint5.js --type=interactions --output=data/sprint5/interactions
   ```

3. **Extraction des profils utilisateur**

   ```bash
   # Extraire les profils utilisateur
   node scripts/prepare-data-for-sprint5.js --type=users --output=data/sprint5/users
   ```

4. **Extraction des métadonnées de contenu**

   ```bash
   # Extraire les métadonnées de contenu
   node scripts/prepare-data-for-sprint5.js --type=content --output=data/sprint5/content
   ```

### Phase 3 : Transformation des données

1. **Transformation des données de feedback**

   ```bash
   # Transformer les données de feedback
   node scripts/transform-data.js --input=data/sprint5/feedback --output=data/sprint5/transformed/feedback --rules=config/transformation-rules.json
   ```

2. **Transformation des données d'interaction**

   ```bash
   # Transformer les données d'interaction
   node scripts/transform-data.js --input=data/sprint5/interactions --output=data/sprint5/transformed/interactions --rules=config/transformation-rules.json
   ```

3. **Enrichissement des profils utilisateur**

   ```bash
   # Enrichir les profils utilisateur
   node scripts/enrich-user-profiles.js --input=data/sprint5/users --output=data/sprint5/transformed/users --interactions=data/sprint5/interactions --feedback=data/sprint5/feedback
   ```

4. **Préparation des données pour l'apprentissage automatique**

   ```bash
   # Préparer les données pour l'apprentissage automatique
   python scripts/prepare_ml_data.py --input=data/sprint5/transformed --output=data/sprint5/ml
   ```

   Ce script Python préparera les données pour l'apprentissage automatique :
   - Création de jeux de données d'entraînement et de test
   - Normalisation des caractéristiques
   - Encodage des variables catégorielles
   - Gestion des valeurs manquantes

### Phase 4 : Chargement des données

1. **Chargement des données dans la base de données**

   ```bash
   # Charger les données transformées dans la base de données
   node scripts/load-data.js --input=data/sprint5/transformed --target=database
   ```

2. **Chargement des données dans l'infrastructure ML**

   ```bash
   # Charger les données préparées dans l'infrastructure ML
   ./scripts/load-ml-data.sh --input=data/sprint5/ml --target=ml-infrastructure
   ```

3. **Mise à jour des métadonnées et des index**

   ```bash
   # Mettre à jour les métadonnées et les index
   node scripts/update-metadata.js
   ```

## Validation post-migration

Après la migration, effectuez les vérifications suivantes :

### 1. Vérification de l'intégrité des données

```bash
# Vérifier l'intégrité des données
node scripts/validate-data-integrity.js
```

Ce script vérifiera :
- Le nombre d'enregistrements avant et après la migration
- La cohérence des relations entre les entités
- L'intégrité référentielle

### 2. Vérification de la qualité des données

```bash
# Vérifier la qualité des données
node scripts/validate-data-quality.js
```

Ce script vérifiera :
- La présence de valeurs aberrantes
- La distribution des données
- La qualité des données dérivées

### 3. Tests fonctionnels

```bash
# Exécuter les tests fonctionnels
npm run test:migration
```

Ces tests vérifieront que les fonctionnalités du système fonctionnent correctement avec les données migrées.

### 4. Validation des modèles ML

```bash
# Valider les modèles ML avec les données migrées
python scripts/validate_ml_models.py
```

Ce script validera que les modèles d'apprentissage automatique peuvent être entraînés correctement avec les données migrées.

## Rollback en cas de problème

Si des problèmes surviennent pendant ou après la migration, suivez ces étapes pour effectuer un rollback :

### 1. Arrêt des services

```bash
# Arrêter les services
./scripts/stop-services.sh
```

### 2. Restauration de la base de données

```bash
# Restaurer la base de données à partir de la sauvegarde
./scripts/restore-database.sh --backup=backups/pre-migration-backup.sql
```

### 3. Restauration des fichiers de configuration

```bash
# Restaurer les fichiers de configuration
./scripts/restore-config.sh --backup=backups/pre-migration-config.tar.gz
```

### 4. Redémarrage des services

```bash
# Redémarrer les services
./scripts/start-services.sh
```

## Maintenance post-migration

Après une migration réussie, effectuez les tâches de maintenance suivantes :

### 1. Nettoyage des données temporaires

```bash
# Nettoyer les données temporaires
./scripts/cleanup-migration-data.sh
```

### 2. Optimisation de la base de données

```bash
# Optimiser la base de données
./scripts/optimize-database.sh
```

### 3. Documentation des changements

Mettez à jour la documentation pour refléter les changements dans la structure des données et les processus.

### 4. Surveillance continue

Configurez une surveillance continue pour détecter les problèmes potentiels liés à la migration :

```bash
# Configurer la surveillance post-migration
./scripts/setup-migration-monitoring.sh
```

## Annexes

### A. Schéma de données du Sprint 4

```
RecommendationFeedback
- id: UUID
- userId: UUID
- recommendationId: UUID
- recommendationType: Enum
- feedbackType: Enum
- rating: Number
- comment: String
- createdAt: DateTime

UserInteraction
- id: UUID
- userId: UUID
- contentId: UUID
- contentType: Enum
- type: Enum
- duration: Number
- timestamp: DateTime

User
- id: UUID
- preferences: JSON
- interests: String[]
- demographics: JSON
```

### B. Schéma de données du Sprint 5

```
RecommendationFeedback
- id: UUID
- userId: UUID
- recommendationId: UUID
- recommendationType: Enum
- feedbackType: Enum
- rating: Number
- comment: String
- sentimentScore: Number
- contextData: JSON
- modelId: UUID
- createdAt: DateTime

UserInteraction
- id: UUID
- userId: UUID
- contentId: UUID
- contentType: Enum
- type: Enum
- duration: Number
- contextData: JSON
- sessionId: String
- deviceInfo: JSON
- timestamp: DateTime

User
- id: UUID
- preferences: JSON
- interests: String[]
- demographics: JSON
- segments: UserSegment[]
- contextualFactors: ContextualFactor[]

UserSegment
- id: UUID
- name: String
- description: String
- criteria: JSON
- users: User[]

ContextualFactor
- id: UUID
- name: String
- type: String
- value: String
- weight: Number
- userId: UUID

MLModel
- id: UUID
- name: String
- version: String
- type: String
- parameters: JSON
- metrics: JSON
- isActive: Boolean
- modelPath: String
```

### C. Mappings de transformation

```json
{
  "feedbackType": {
    "LIKE": "POSITIVE",
    "DISLIKE": "NEGATIVE",
    "SAVE": "BOOKMARK",
    "HIDE": "IGNORE",
    "REPORT": "FLAG"
  },
  "interactionType": {
    "VIEW": "IMPRESSION",
    "CLICK": "ENGAGEMENT",
    "BOOKMARK": "SAVE",
    "SHARE": "SOCIAL_SHARE",
    "PURCHASE": "CONVERSION"
  }
}
```

### D. Scripts de migration

- `analyze-sprint4-data.js` : Analyse les données du Sprint 4
- `prepare-data-for-sprint5.js` : Extrait et prépare les données pour le Sprint 5
- `transform-data.js` : Transforme les données selon les règles définies
- `enrich-user-profiles.js` : Enrichit les profils utilisateur avec des données dérivées
- `prepare_ml_data.py` : Prépare les données pour l'apprentissage automatique
- `load-data.js` : Charge les données transformées dans la base de données
- `load-ml-data.sh` : Charge les données préparées dans l'infrastructure ML
- `update-metadata.js` : Met à jour les métadonnées et les index
- `validate-data-integrity.js` : Vérifie l'intégrité des données
- `validate-data-quality.js` : Vérifie la qualité des données
- `validate_ml_models.py` : Valide les modèles ML avec les données migrées
