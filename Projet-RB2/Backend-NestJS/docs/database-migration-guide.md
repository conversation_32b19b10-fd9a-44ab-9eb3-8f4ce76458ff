# Guide de migration de la base de données - Sprint 4 à Sprint 5

Ce document détaille les étapes nécessaires pour migrer la base de données du système de recommandation du Sprint 4 au Sprint 5, en mettant l'accent sur la préservation des données collectées pendant le Sprint 4.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Prérequis](#prérequis)
3. [Sauvegarde des données](#sauvegarde-des-données)
4. [Schéma de base de données du Sprint 5](#schéma-de-base-de-données-du-sprint-5)
5. [Procédure de migration](#procédure-de-migration)
6. [Vérification post-migration](#vérification-post-migration)
7. [Rollback en cas de problème](#rollback-en-cas-de-problème)
8. [Maintenance post-migration](#maintenance-post-migration)

## Vue d'ensemble

La migration de la base de données du Sprint 4 au Sprint 5 implique plusieurs changements importants :

1. Ajout de nouvelles tables pour le système d'apprentissage continu
2. Extension des tables existantes pour prendre en charge la personnalisation avancée
3. Ajout de nouvelles relations entre les tables
4. Optimisation des index pour améliorer les performances

Cette migration doit être effectuée avec soin pour préserver les données de feedback et d'interactions collectées pendant le Sprint 4, qui sont essentielles pour l'apprentissage automatique dans le Sprint 5.

## Prérequis

Avant de commencer la migration, assurez-vous que les conditions suivantes sont remplies :

1. **Environnement de développement**
   - Node.js 14+ et npm 6+
   - PostgreSQL 12+
   - Prisma CLI installé (`npm install -g prisma`)

2. **Accès à la base de données**
   - Accès administrateur à la base de données PostgreSQL
   - Permissions pour créer/modifier des tables et des index

3. **Outils de sauvegarde**
   - `pg_dump` et `pg_restore` installés
   - Espace disque suffisant pour les sauvegardes

4. **Fichiers de configuration**
   - Fichier `.env` avec les variables d'environnement correctes
   - Fichier `schema.prisma` du Sprint 5

## Sauvegarde des données

Avant toute modification, effectuez une sauvegarde complète de la base de données :

```bash
# Sauvegarde complète de la base de données
pg_dump -U postgres -d retreat_and_be -f backup_sprint4_full.sql

# Sauvegarde des tables spécifiques au système de recommandation
pg_dump -U postgres -d retreat_and_be -t "Recommendation*" -t "UserInteraction" -t "UserFeedback" -t "UserPreference" -f backup_sprint4_recommendation.sql
```

Vérifiez que les sauvegardes sont complètes et utilisables :

```bash
# Créer une base de données de test
createdb -U postgres retreat_and_be_test

# Restaurer la sauvegarde dans la base de données de test
pg_restore -U postgres -d retreat_and_be_test backup_sprint4_full.sql

# Vérifier que la restauration a fonctionné
psql -U postgres -d retreat_and_be_test -c "SELECT COUNT(*) FROM \"RecommendationFeedback\";"
```

## Schéma de base de données du Sprint 5

Le Sprint 5 introduit plusieurs nouvelles tables et modifications :

### Nouvelles tables

1. **`MLModel`** : Stocke les informations sur les modèles d'apprentissage automatique
   ```prisma
   model MLModel {
     id          String   @id @default(uuid())
     name        String
     version     String
     type        String
     parameters  Json
     metrics     Json
     createdAt   DateTime @default(now())
     updatedAt   DateTime @updatedAt
     isActive    Boolean  @default(false)
     modelPath   String
   }
   ```

2. **`UserSegment`** : Stocke les segments d'utilisateurs pour la personnalisation
   ```prisma
   model UserSegment {
     id          String   @id @default(uuid())
     name        String
     description String?
     criteria    Json
     createdAt   DateTime @default(now())
     updatedAt   DateTime @updatedAt
     users       User[]   @relation("UserToSegment")
   }
   ```

3. **`ContextualFactor`** : Stocke les facteurs contextuels pour les recommandations
   ```prisma
   model ContextualFactor {
     id          String   @id @default(uuid())
     name        String
     type        String
     value       String
     weight      Float
     createdAt   DateTime @default(now())
     updatedAt   DateTime @updatedAt
     userId      String
     user        User     @relation(fields: [userId], references: [id])
   }
   ```

4. **`RecommendationExplanationFeedback`** : Stocke le feedback sur les explications
   ```prisma
   model RecommendationExplanationFeedback {
     id             String   @id @default(uuid())
     explanationId  String
     userId         String
     feedbackType   String
     comment        String?
     createdAt      DateTime @default(now())
     user           User     @relation(fields: [userId], references: [id])
   }
   ```

### Modifications des tables existantes

1. **`User`** : Ajout de relations avec les nouvelles tables
   ```prisma
   model User {
     // Champs existants...
     segments                      UserSegment[]               @relation("UserToSegment")
     contextualFactors            ContextualFactor[]
     explanationFeedback          RecommendationExplanationFeedback[]
   }
   ```

2. **`RecommendationFeedback`** : Ajout de nouveaux champs
   ```prisma
   model RecommendationFeedback {
     // Champs existants...
     contextData    Json?
     modelId        String?
     model          MLModel?       @relation(fields: [modelId], references: [id])
   }
   ```

3. **`UserInteraction`** : Ajout de nouveaux champs
   ```prisma
   model UserInteraction {
     // Champs existants...
     contextData    Json?
     sessionId      String?
     deviceInfo     Json?
   }
   ```

### Nouveaux index

1. Index sur `UserInteraction.sessionId` pour améliorer les requêtes par session
2. Index sur `RecommendationFeedback.modelId` pour améliorer les requêtes par modèle
3. Index composé sur `UserInteraction.userId` et `UserInteraction.timestamp` pour améliorer les requêtes chronologiques

## Procédure de migration

La migration sera effectuée en plusieurs étapes pour minimiser les temps d'arrêt et les risques :

### 1. Préparation

```bash
# Cloner le schéma Prisma du Sprint 5
cp schema.prisma.sprint5 prisma/schema.prisma

# Générer la migration sans l'appliquer
npx prisma migrate dev --name sprint5 --create-only
```

Examinez le fichier de migration généré dans `prisma/migrations/YYYYMMDDHHMMSS_sprint5/migration.sql` et assurez-vous qu'il est correct.

### 2. Migration en environnement de développement

```bash
# Appliquer la migration en environnement de développement
npx prisma migrate dev --name sprint5
```

Vérifiez que la migration s'est bien déroulée et que les données sont préservées.

### 3. Migration en environnement de test

```bash
# Définir la variable d'environnement pour l'environnement de test
export DATABASE_URL="postgresql://user:password@localhost:5432/retreat_and_be_test"

# Appliquer la migration en environnement de test
npx prisma migrate deploy
```

Exécutez les tests pour vérifier que tout fonctionne correctement.

### 4. Migration en environnement de production

La migration en production doit être effectuée pendant une période de faible activité et après avoir informé les utilisateurs d'une maintenance planifiée.

```bash
# Définir la variable d'environnement pour l'environnement de production
export DATABASE_URL="*************************************************/retreat_and_be"

# Appliquer la migration en environnement de production
npx prisma migrate deploy
```

## Vérification post-migration

Après la migration, effectuez les vérifications suivantes :

### 1. Vérification de l'intégrité des données

```bash
# Vérifier le nombre d'enregistrements dans les tables principales
psql -U postgres -d retreat_and_be -c "SELECT COUNT(*) FROM \"RecommendationFeedback\";"
psql -U postgres -d retreat_and_be -c "SELECT COUNT(*) FROM \"UserInteraction\";"
psql -U postgres -d retreat_and_be -c "SELECT COUNT(*) FROM \"User\";"

# Vérifier que les nouvelles tables sont créées
psql -U postgres -d retreat_and_be -c "SELECT COUNT(*) FROM \"MLModel\";"
psql -U postgres -d retreat_and_be -c "SELECT COUNT(*) FROM \"UserSegment\";"
psql -U postgres -d retreat_and_be -c "SELECT COUNT(*) FROM \"ContextualFactor\";"
```

### 2. Vérification des fonctionnalités

Exécutez les tests d'intégration pour vérifier que toutes les fonctionnalités fonctionnent correctement avec le nouveau schéma :

```bash
npm run test:integration
```

### 3. Vérification des performances

Exécutez les tests de performance pour vérifier que les performances sont acceptables :

```bash
npm run test:performance
```

## Rollback en cas de problème

Si des problèmes surviennent après la migration, suivez ces étapes pour effectuer un rollback :

### 1. Rollback de la migration Prisma

```bash
# Revenir à la migration précédente
npx prisma migrate resolve --rolled-back YYYYMMDDHHMMSS_sprint5
```

### 2. Restauration de la base de données

Si le rollback de la migration ne fonctionne pas, restaurez la sauvegarde complète :

```bash
# Supprimer la base de données actuelle
dropdb -U postgres retreat_and_be

# Créer une nouvelle base de données
createdb -U postgres retreat_and_be

# Restaurer la sauvegarde
pg_restore -U postgres -d retreat_and_be backup_sprint4_full.sql
```

## Maintenance post-migration

Après une migration réussie, effectuez les tâches de maintenance suivantes :

### 1. Optimisation de la base de données

```bash
# Analyser les tables pour mettre à jour les statistiques
psql -U postgres -d retreat_and_be -c "ANALYZE;"

# Réindexer les tables
psql -U postgres -d retreat_and_be -c "REINDEX DATABASE retreat_and_be;"
```

### 2. Nettoyage des sauvegardes

Conservez les sauvegardes pendant au moins 30 jours, puis supprimez-les si tout fonctionne correctement :

```bash
# Archiver les sauvegardes
tar -czf backup_sprint4_$(date +%Y%m%d).tar.gz backup_sprint4_*.sql

# Déplacer l'archive vers un stockage à long terme
mv backup_sprint4_$(date +%Y%m%d).tar.gz /backup/
```

### 3. Documentation

Mettez à jour la documentation pour refléter les changements dans le schéma de la base de données :

```bash
# Générer la documentation du schéma Prisma
npx prisma-docs-generator
```
