# Guide de déploiement - Sprint 4

Ce guide détaille les étapes nécessaires pour déployer le Sprint 4 du système de recommandation de Retreat And Be.

## Table des matières

1. [Prérequis](#prérequis)
2. [Préparation du déploiement](#préparation-du-déploiement)
3. [Déploiement du backend](#déploiement-du-backend)
4. [Déploiement du frontend](#déploiement-du-frontend)
5. [Configuration des services externes](#configuration-des-services-externes)
6. [Vérification du déploiement](#vérification-du-déploiement)
7. [Rollback en cas de problème](#rollback-en-cas-de-problème)
8. [Maintenance post-déploiement](#maintenance-post-déploiement)

## Prérequis

### Environnement de production

- Serveur Linux (Ubuntu 20.04 LTS recommandé)
- Node.js 14+ et npm 6+
- PostgreSQL 12+
- Nginx ou Apache pour servir le frontend
- PM2 pour gérer les processus Node.js
- Git

### Accès et permissions

- Accès SSH au serveur de production
- Permissions pour créer/modifier des bases de données PostgreSQL
- Permissions pour configurer Nginx/Apache
- Accès aux services externes (modération, analytics)

## Préparation du déploiement

### 1. Exécuter le script de préparation

```bash
cd Projet-RB2/Backend-NestJS
./scripts/prepare-deployment.sh
```

Ce script effectue les opérations suivantes :
- Crée une branche de déploiement
- Exécute les tests unitaires et d'intégration
- Construit le backend et le frontend
- Prépare une archive de déploiement

### 2. Vérifier l'archive de déploiement

L'archive `deploy/sprint4.tar.gz` contient :
- Les fichiers compilés du backend
- Les fichiers compilés du frontend
- La documentation
- Les fichiers de configuration

## Déploiement du backend

### 1. Transférer les fichiers sur le serveur

```bash
scp deploy/sprint4.tar.gz user@production-server:/tmp/
ssh user@production-server
cd /tmp/
tar -xzf sprint4.tar.gz
```

### 2. Sauvegarder la version actuelle

```bash
# Sauvegarder la base de données
pg_dump -U postgres -d retreat_and_be > /backup/retreat_and_be_$(date +%Y%m%d).sql

# Sauvegarder les fichiers actuels
cp -r /var/www/retreat-and-be/backend /backup/backend_$(date +%Y%m%d)
```

### 3. Configurer les variables d'environnement

Créer ou mettre à jour le fichier `.env` dans le dossier du backend :

```bash
cd /tmp/sprint4/backend
cp .env.example .env
nano .env
```

Contenu minimal du fichier `.env` :

```
# Configuration de base
PORT=3000
NODE_ENV=production

# Configuration de la base de données
DATABASE_URL="postgresql://user:password@localhost:5432/retreat_and_be"

# Configuration des services externes
MODERATION_SERVICE_URL="https://moderation.retreat-and-be.com"
MODERATION_SERVICE_API_KEY="your-api-key"
ANALYTICS_SERVICE_URL="https://analytics.retreat-and-be.com"
ANALYTICS_SERVICE_API_KEY="your-api-key"

# Configuration JWT
JWT_SECRET="your-jwt-secret"
JWT_EXPIRATION="1d"
```

### 4. Installer les dépendances et appliquer les migrations

```bash
cd /tmp/sprint4/backend
npm install --production
npx prisma migrate deploy
```

### 5. Déployer les fichiers

```bash
# Arrêter le service actuel
pm2 stop retreat-backend

# Déployer les nouveaux fichiers
cp -r /tmp/sprint4/backend/* /var/www/retreat-and-be/backend/

# Démarrer le service
cd /var/www/retreat-and-be/backend
pm2 start npm --name "retreat-backend" -- run start:prod
```

## Déploiement du frontend

### 1. Déployer les fichiers

```bash
# Sauvegarder les fichiers actuels
cp -r /var/www/retreat-and-be/frontend /backup/frontend_$(date +%Y%m%d)

# Déployer les nouveaux fichiers
cp -r /tmp/sprint4/frontend/* /var/www/retreat-and-be/frontend/
```

### 2. Configurer le serveur web (Nginx)

```bash
nano /etc/nginx/sites-available/retreat-and-be.conf
```

Configuration Nginx :

```nginx
server {
    listen 80;
    server_name app.retreat-and-be.com;

    root /var/www/retreat-and-be/frontend;
    index index.html;

    # API Proxy
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Frontend
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

Activer la configuration et redémarrer Nginx :

```bash
sudo ln -s /etc/nginx/sites-available/retreat-and-be.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Configuration des services externes

### 1. Service de modération

Assurez-vous que le service de modération est correctement configuré pour accepter les requêtes du nouveau système de recommandation :

- Vérifiez que l'API key est valide
- Vérifiez que les règles de modération sont à jour
- Testez la connexion entre le backend et le service de modération

### 2. Service d'analytics

Assurez-vous que le service d'analytics est correctement configuré :

- Vérifiez que l'API key est valide
- Vérifiez que les événements sont correctement enregistrés
- Testez la connexion entre le backend et le service d'analytics

## Vérification du déploiement

### 1. Vérifier le backend

```bash
# Vérifier que le service est en cours d'exécution
pm2 status retreat-backend

# Vérifier les logs
pm2 logs retreat-backend

# Tester l'API
curl http://localhost:3000/api/health
```

### 2. Vérifier le frontend

- Ouvrez l'application dans un navigateur : https://app.retreat-and-be.com
- Vérifiez que les nouvelles fonctionnalités sont disponibles
- Testez le système de recommandation, les explications et le feedback

### 3. Vérifier les intégrations

- Testez l'intégration avec le service de modération
- Testez l'intégration avec le service d'analytics
- Vérifiez que les événements sont correctement enregistrés

## Rollback en cas de problème

Si des problèmes surviennent après le déploiement, suivez ces étapes pour revenir à la version précédente :

### 1. Rollback du backend

```bash
# Arrêter le service
pm2 stop retreat-backend

# Restaurer les fichiers
cp -r /backup/backend_$(date +%Y%m%d)/* /var/www/retreat-and-be/backend/

# Restaurer la base de données
psql -U postgres -d retreat_and_be < /backup/retreat_and_be_$(date +%Y%m%d).sql

# Redémarrer le service
cd /var/www/retreat-and-be/backend
pm2 start npm --name "retreat-backend" -- run start:prod
```

### 2. Rollback du frontend

```bash
# Restaurer les fichiers
cp -r /backup/frontend_$(date +%Y%m%d)/* /var/www/retreat-and-be/frontend/
```

## Maintenance post-déploiement

### 1. Surveillance

- Configurez des alertes pour surveiller les performances du système
- Surveillez les logs pour détecter d'éventuels problèmes
- Surveillez l'utilisation des ressources (CPU, mémoire, disque)

### 2. Sauvegarde

- Configurez des sauvegardes régulières de la base de données
- Configurez des sauvegardes régulières des fichiers

### 3. Mise à jour

- Planifiez des mises à jour régulières des dépendances
- Planifiez des mises à jour régulières du système d'exploitation
- Planifiez des mises à jour régulières des services externes
