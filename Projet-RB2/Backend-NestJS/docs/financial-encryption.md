# Service de Chiffrement Financier

Ce document décrit le service de chiffrement financier implémenté dans le projet Retreat And Be.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Configuration](#configuration)
4. [Utilisation](#utilisation)
5. [Chiffrement d'objets](#chiffrement-dobjets)
6. [Champs sensibles](#champs-sensibles)
7. [Bonnes pratiques](#bonnes-pratiques)
8. [Dépannage](#dépannage)

## Vue d'ensemble

Le service de chiffrement financier est responsable de la protection des données financières sensibles dans l'application. Il utilise l'algorithme AES-256-GCM pour chiffrer les données financières telles que les numéros de carte de crédit, les informations bancaires et autres données sensibles.

## Architecture

Le service de chiffrement financier s'appuie sur le service de gestion des clés pour obtenir et gérer les clés cryptographiques utilisées pour le chiffrement et le déchiffrement des données financières.

```
┌─────────────────────────┐     ┌─────────────────────────┐
│ FinancialEncryption     │     │ KeyManagement           │
│ Service                 │     │ Service                 │
│                         │     │                         │
│ - Chiffrement AES-GCM   │     │ - Gestion des clés      │
│ - Validation de cartes  │     │ - Rotation des clés     │
│ - Chiffrement d'objets  │     │ - Stockage sécurisé     │
└───────────┬─────────────┘     └───────────┬─────────────┘
            │                               │
            └───────────────────────────────┘
                           │
                           ▼
                  ┌─────────────────┐
                  │ Données         │
                  │ financières     │
                  │ chiffrées       │
                  └─────────────────┘
```

## Configuration

La configuration du service de chiffrement financier se fait via les variables d'environnement suivantes :

```
# Chiffrement financier
FINANCIAL_ENCRYPTION_ENABLED=true
FINANCIAL_ENCRYPTION_ALGORITHM=aes-256-gcm
```

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `FINANCIAL_ENCRYPTION_ENABLED` | Active ou désactive le chiffrement financier | `true` |
| `FINANCIAL_ENCRYPTION_ALGORITHM` | Algorithme de chiffrement à utiliser | `aes-256-gcm` |

## Utilisation

### Chiffrement de données

```typescript
import { FinancialEncryptionService } from './services/financial-encryption.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly financialEncryptionService: FinancialEncryptionService,
  ) {}

  async processPayment(cardNumber: string, cvv: string) {
    // Chiffrer le numéro de carte
    const encryptedCard = await this.financialEncryptionService.encryptCreditCard(cardNumber);
    
    // Chiffrer le CVV
    const encryptedCvv = await this.financialEncryptionService.encrypt(cvv);
    
    // Stocker les données chiffrées
    // ...
    
    // Traiter le paiement
    // ...
  }
}
```

### Déchiffrement de données

```typescript
import { FinancialEncryptionService, EncryptedFinancialData } from './services/financial-encryption.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly financialEncryptionService: FinancialEncryptionService,
  ) {}

  async getCardDetails(encryptedCard: EncryptedFinancialData) {
    // Déchiffrer le numéro de carte
    const cardNumber = await this.financialEncryptionService.decryptCreditCard(encryptedCard);
    
    // Masquer le numéro de carte pour l'affichage
    const maskedCard = this.maskCardNumber(cardNumber);
    
    return { maskedCard };
  }
  
  private maskCardNumber(cardNumber: string): string {
    // Supprimer les espaces et tirets
    const sanitized = cardNumber.replace(/[\s-]/g, '');
    
    // Masquer tous les chiffres sauf les 4 derniers
    return sanitized.slice(0, -4).replace(/./g, '*') + sanitized.slice(-4);
  }
}
```

## Chiffrement d'objets

Le service permet de chiffrer automatiquement les champs sensibles d'un objet financier.

### Chiffrement d'un objet

```typescript
import { FinancialEncryptionService } from './services/financial-encryption.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly financialEncryptionService: FinancialEncryptionService,
  ) {}

  async savePaymentMethod(paymentMethod: {
    cardNumber: string;
    cardholderName: string;
    expirationDate: string;
    cvv: string;
  }) {
    // Chiffrer automatiquement les champs sensibles
    const encryptedPaymentMethod = await this.financialEncryptionService.encryptObject(paymentMethod);
    
    // Stocker l'objet chiffré
    // ...
    
    return { success: true, id: '...' };
  }
}
```

### Déchiffrement d'un objet

```typescript
import { FinancialEncryptionService } from './services/financial-encryption.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly financialEncryptionService: FinancialEncryptionService,
  ) {}

  async getPaymentMethod(id: string) {
    // Récupérer l'objet chiffré
    const encryptedPaymentMethod = await this.getEncryptedPaymentMethod(id);
    
    // Déchiffrer automatiquement les champs chiffrés
    const paymentMethod = await this.financialEncryptionService.decryptObject(encryptedPaymentMethod);
    
    return paymentMethod;
  }
  
  private async getEncryptedPaymentMethod(id: string) {
    // Récupérer l'objet chiffré depuis la base de données
    // ...
    return { /* objet chiffré */ };
  }
}
```

## Champs sensibles

Le service identifie automatiquement les champs sensibles dans un objet financier en se basant sur une liste de noms de champs sensibles.

### Liste des champs sensibles

```typescript
export const SENSITIVE_FINANCIAL_FIELDS = [
  'cardNumber',
  'cvv',
  'cvc',
  'securityCode',
  'expirationDate',
  'accountNumber',
  'routingNumber',
  'iban',
  'bic',
  'swift',
  'taxId',
  'ssn',
  'paymentDetails',
  'transactionDetails',
];
```

### Spécification manuelle des champs sensibles

```typescript
import { FinancialEncryptionService } from './services/financial-encryption.service';

@Injectable()
export class PaymentService {
  constructor(
    private readonly financialEncryptionService: FinancialEncryptionService,
  ) {}

  async saveCustomData(data: Record<string, any>) {
    // Spécifier manuellement les champs à chiffrer
    const sensitiveFields = ['customField1', 'customField2'];
    
    // Chiffrer uniquement les champs spécifiés
    const encryptedData = await this.financialEncryptionService.encryptObject(data, sensitiveFields);
    
    // Stocker l'objet chiffré
    // ...
    
    return { success: true };
  }
}
```

## Bonnes pratiques

1. **Minimiser les données sensibles** : Ne stockez que les données financières absolument nécessaires.

2. **Chiffrer au plus tôt, déchiffrer au plus tard** : Chiffrez les données sensibles dès leur réception et ne les déchiffrez qu'au moment où elles sont nécessaires.

3. **Validation des données** : Validez toujours les données financières avant de les chiffrer pour éviter de stocker des données invalides.

4. **Rotation des clés** : Utilisez le service de gestion des clés pour effectuer régulièrement la rotation des clés de chiffrement.

5. **Journalisation sécurisée** : Ne journalisez jamais les données financières en clair, même en cas d'erreur.

6. **Conformité PCI DSS** : Si vous traitez des données de cartes de paiement, assurez-vous de respecter les exigences PCI DSS.

## Dépannage

### Problèmes courants

1. **Erreur de chiffrement** : Vérifiez que le service de gestion des clés est correctement configuré et qu'une clé active est disponible pour le chiffrement financier.

2. **Erreur de déchiffrement** : Assurez-vous que la clé utilisée pour le chiffrement est toujours disponible. Si la clé a été révoquée, il ne sera plus possible de déchiffrer les données.

3. **Validation de carte échouée** : Vérifiez que le numéro de carte est au format correct et qu'il passe la validation de l'algorithme de Luhn.

### Journalisation

Le service de chiffrement financier utilise le système de journalisation de NestJS pour enregistrer les événements importants. Consultez les journaux pour obtenir des informations détaillées sur les erreurs et les opérations effectuées.

```typescript
this.logger.log('Financial encryption service initialized');
this.logger.error('Failed to encrypt financial data', error);
```
