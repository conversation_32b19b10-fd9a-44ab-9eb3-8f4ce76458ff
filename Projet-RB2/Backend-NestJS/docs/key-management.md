# Service de Gestion des Clés

Ce document décrit le service de gestion des clés implémenté dans le projet Retreat And Be, y compris l'intégration avec HashiCorp Vault.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Services](#services)
4. [Configuration](#configuration)
5. [API REST](#api-rest)
6. [Intégration avec HashiCorp Vault](#intégration-avec-hashicorp-vault)
7. [Rotation des clés](#rotation-des-clés)
8. [Bonnes pratiques](#bonnes-pratiques)
9. [Dépannage](#dépannage)

## Vue d'ensemble

Le service de gestion des clés est responsable de la création, du stockage, de la rotation et de la révocation des clés cryptographiques utilisées dans l'application. Il fournit une interface unifiée pour la gestion des clés, indépendamment du backend de stockage utilisé (mémoire ou HashiCorp Vault).

## Architecture

Le système de gestion des clés est composé de deux services principaux :

```
┌─────────────────────────┐     ┌─────────────────────────┐
│ KeyManagementService    │     │ VaultService            │
│                         │     │                         │
│ - Création de clés      │     │ - Intégration Vault     │
│ - Rotation de clés      │     │ - Stockage sécurisé     │
│ - Révocation de clés    │     │ - Génération de clés    │
│ - Gestion du cycle de   │     │ - Gestion des secrets   │
│   vie des clés          │     │                         │
└───────────┬─────────────┘     └───────────┬─────────────┘
            │                               │
            └───────────────────────────────┘
                           │
                           ▼
                  ┌─────────────────┐
                  │ Stockage des    │
                  │ clés            │
                  └─────────────────┘
```

## Services

### KeyManagementService

Ce service gère le cycle de vie des clés cryptographiques. Il fournit des méthodes pour :

- Créer de nouvelles clés
- Récupérer des clés existantes
- Effectuer la rotation des clés
- Révoquer des clés
- Lister les clés actives

### VaultService

Ce service gère l'intégration avec HashiCorp Vault. Il fournit des méthodes pour :

- Stocker des secrets dans Vault
- Récupérer des secrets depuis Vault
- Générer des clés cryptographiques via Vault
- Lister les secrets disponibles dans Vault

## Configuration

La configuration des services de gestion des clés se fait via les variables d'environnement suivantes :

```
# Gestion des clés
KEY_ROTATION_INTERVAL=604800000  # 7 jours
KEY_EXPIRY=2592000000  # 30 jours

# HashiCorp Vault
VAULT_ENABLED=false
VAULT_ENDPOINT=http://localhost:8200
VAULT_TOKEN=
VAULT_NAMESPACE=
VAULT_API_VERSION=v1
VAULT_KEY_PREFIX=retreat-and-be
VAULT_SSL_VERIFY=true
VAULT_TIMEOUT=5000
VAULT_RETRY_COUNT=3
VAULT_RETRY_FACTOR=2
VAULT_RETRY_MIN_TIMEOUT=1000
VAULT_RETRY_MAX_TIMEOUT=10000
```

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `KEY_ROTATION_INTERVAL` | Intervalle de rotation des clés en millisecondes | `604800000` (7 jours) |
| `KEY_EXPIRY` | Durée de vie des clés en millisecondes | `2592000000` (30 jours) |
| `VAULT_ENABLED` | Active ou désactive l'intégration avec Vault | `false` |
| `VAULT_ENDPOINT` | URL de l'API Vault | `http://localhost:8200` |
| `VAULT_TOKEN` | Token d'authentification pour Vault | `` |
| `VAULT_NAMESPACE` | Namespace Vault (pour Vault Enterprise) | `` |
| `VAULT_API_VERSION` | Version de l'API Vault | `v1` |
| `VAULT_KEY_PREFIX` | Préfixe pour les clés stockées dans Vault | `retreat-and-be` |
| `VAULT_SSL_VERIFY` | Vérifie les certificats SSL lors des connexions à Vault | `true` |
| `VAULT_TIMEOUT` | Timeout des requêtes à Vault en millisecondes | `5000` |
| `VAULT_RETRY_COUNT` | Nombre de tentatives en cas d'échec | `3` |
| `VAULT_RETRY_FACTOR` | Facteur de multiplication pour le délai entre les tentatives | `2` |
| `VAULT_RETRY_MIN_TIMEOUT` | Délai minimum entre les tentatives en millisecondes | `1000` |
| `VAULT_RETRY_MAX_TIMEOUT` | Délai maximum entre les tentatives en millisecondes | `10000` |

## API REST

Le service expose plusieurs endpoints REST pour la gestion des clés :

| Méthode | Endpoint | Description | Rôles |
|---------|----------|-------------|-------|
| `GET` | `/encryption/keys` | Liste les clés actives | `admin` |
| `POST` | `/encryption/keys/create` | Crée une nouvelle clé | `admin` |
| `POST` | `/encryption/keys/rotate/:keyId` | Effectue la rotation d'une clé | `admin` |
| `POST` | `/encryption/keys/revoke/:keyId` | Révoque une clé | `admin` |
| `GET` | `/encryption/vault/status` | Vérifie le statut de l'intégration avec Vault | `admin` |

## Intégration avec HashiCorp Vault

Le service de gestion des clés peut être configuré pour utiliser HashiCorp Vault comme backend de stockage sécurisé pour les clés cryptographiques. Lorsque l'intégration avec Vault est activée, le service utilise les fonctionnalités suivantes de Vault :

### Moteur KV v2

Le moteur KV (Key/Value) v2 est utilisé pour stocker les clés cryptographiques et leurs métadonnées. Les clés sont stockées dans le chemin `secret/data/{prefix}/keys/values/{keyId}` et les métadonnées dans le chemin `secret/data/{prefix}/keys/metadata`.

### Moteur Transit

Le moteur Transit est utilisé pour générer des clés cryptographiques de haute qualité. Le service crée temporairement une clé dans Transit, l'exporte, puis la supprime.

### Fallback vers le stockage en mémoire

Si l'intégration avec Vault est désactivée ou si une erreur se produit lors de l'interaction avec Vault, le service utilise automatiquement un stockage en mémoire comme fallback. Cela garantit que l'application peut continuer à fonctionner même en cas de problème avec Vault.

## Rotation des clés

La rotation des clés est un processus important pour maintenir la sécurité des données chiffrées. Le service de gestion des clés effectue automatiquement la rotation des clés selon l'intervalle configuré.

### Processus de rotation

1. Une nouvelle clé est créée avec les mêmes paramètres que l'ancienne clé
2. L'ancienne clé est marquée comme "en rotation"
3. La nouvelle clé est utilisée pour le chiffrement des nouvelles données
4. L'ancienne clé est conservée pour le déchiffrement des données existantes

### Rotation automatique

La rotation automatique des clés est planifiée pour s'exécuter périodiquement. Le service vérifie toutes les heures si des clés doivent être rotées et effectue la rotation si nécessaire.

### Rotation manuelle

La rotation manuelle des clés peut être déclenchée via l'API REST en appelant l'endpoint `/encryption/keys/rotate/:keyId`.

## Bonnes pratiques

1. **Activation de Vault en production** : En environnement de production, il est fortement recommandé d'activer l'intégration avec HashiCorp Vault pour un stockage sécurisé des clés.

2. **Rotation régulière** : Configurez un intervalle de rotation approprié pour vos clés en fonction de la sensibilité des données.

3. **Sauvegarde** : Assurez-vous de sauvegarder régulièrement les données de Vault pour éviter la perte de clés.

4. **Surveillance** : Surveillez les opérations de rotation des clés et configurez des alertes en cas d'échec.

5. **Séparation des clés** : Utilisez des clés différentes pour différents usages (chiffrement, signature, HMAC) et différents services.

## Dépannage

### Problèmes courants

1. **Erreur de connexion à Vault** : Vérifiez que Vault est en cours d'exécution et accessible à l'URL configurée. Vérifiez également que le token d'authentification est valide.

2. **Erreur de génération de clé** : Si la génération de clé échoue avec Vault, le service utilise automatiquement la génération locale comme fallback.

3. **Erreur de rotation de clé** : Vérifiez les journaux pour plus de détails sur l'erreur. Assurez-vous que la clé existe et est active.

### Journalisation

Le service de gestion des clés utilise le système de journalisation de NestJS pour enregistrer les événements importants. Consultez les journaux pour obtenir des informations détaillées sur les erreurs et les opérations effectuées.

```typescript
this.logger.log('Key Management Service initialized successfully');
this.logger.error('Failed to initialize Key Management Service', error);
```
