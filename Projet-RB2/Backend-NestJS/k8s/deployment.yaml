apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-nestjs
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-nestjs
  template:
    metadata:
      labels:
        app: backend-nestjs
    spec:
      containers:
        - name: backend-nestjs
          image: <your-backend-nestjs-image>
          envFrom:
            - configMapRef:
                name: backend-nestjs-config
            - secretRef:
                name: backend-nestjs-secrets
          ports:
            - containerPort: 3000
          livenessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 10
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 10
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
