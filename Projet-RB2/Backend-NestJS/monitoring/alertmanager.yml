global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/TXXXXXXXX/BXXXXXXXX/XXXXXXXXXXXXXXXXXX'
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'
  smtp_require_tls: true

route:
  group_by: ['alertname', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'team-recommendation'
  routes:
  - match:
      severity: critical
    receiver: 'team-recommendation-critical'
    continue: true
  - match_re:
      service: recommendation-system
    receiver: 'team-recommendation'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service']

receivers:
  - name: 'team-recommendation'
    slack_configs:
    - channel: '#recommendation-alerts'
      send_resolved: true
      title: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
      title_link: 'https://grafana.retreat-and-be.com/d/recommendation-system'
      text: >-
        {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
        {{ end }}
    email_configs:
    - to: '<EMAIL>'
      send_resolved: true
      headers:
        subject: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
      html: >-
        <h3>{{ .Status | toUpper }}</h3>
        <p><strong>Alert:</strong> {{ .GroupLabels.alertname }}</p>
        <p><strong>Service:</strong> {{ .GroupLabels.service }}</p>
        <ul>
        {{ range .Alerts }}
          <li>
            <p><strong>Alert:</strong> {{ .Annotations.summary }}</p>
            <p><strong>Description:</strong> {{ .Annotations.description }}</p>
            <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
          </li>
        {{ end }}
        </ul>
        <p><a href="{{ .Annotations.dashboard }}">View Dashboard</a></p>

  - name: 'team-recommendation-critical'
    slack_configs:
    - channel: '#recommendation-critical'
      send_resolved: true
      title: '[CRITICAL] {{ .GroupLabels.alertname }}'
      title_link: 'https://grafana.retreat-and-be.com/d/recommendation-system'
      text: >-
        {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
        {{ end }}
    pagerduty_configs:
    - service_key: 'your_pagerduty_service_key'
      send_resolved: true
      description: >-
        [CRITICAL] {{ .GroupLabels.alertname }} - {{ .Annotations.summary }}
      client: 'AlertManager'
      client_url: 'https://alertmanager.retreat-and-be.com'
      details:
        description: '{{ .Annotations.description }}'
        service: '{{ .Labels.service }}'
        severity: '{{ .Labels.severity }}'
