groups:
  - name: recommendation_alerts
    rules:
      - alert: HighErrorRate
        expr: sum(rate(recommendation_requests_total{status="error"}[5m])) / sum(rate(recommendation_requests_total[5m])) > 0.05
        for: 5m
        labels:
          severity: critical
          service: recommendation-system
        annotations:
          summary: "High error rate in recommendation system"
          description: "Recommendation system has a high error rate (> 5%) for the last 5 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: SlowResponseTime
        expr: avg(recommendation_response_time_ms) > 500
        for: 5m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "Slow response time in recommendation system"
          description: "Recommendation system has a slow average response time (> 500ms) for the last 5 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: HighModerationFilterRate
        expr: sum(recommendation_moderation_filtered_total) / sum(recommendation_requests_total) > 0.2
        for: 15m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "High moderation filter rate in recommendation system"
          description: "Recommendation system has a high moderation filter rate (> 20%) for the last 15 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: LowClickThroughRate
        expr: sum(recommendation_clicks_total) / sum(recommendation_views_total) < 0.02
        for: 30m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "Low click-through rate in recommendation system"
          description: "Recommendation system has a low click-through rate (< 2%) for the last 30 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: HighNegativeFeedbackRate
        expr: sum(recommendation_feedback_total{type=~"dislike|report"}) / sum(recommendation_feedback_total) > 0.5
        for: 30m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "High negative feedback rate in recommendation system"
          description: "Recommendation system has a high negative feedback rate (> 50%) for the last 30 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: RecommendationServiceDown
        expr: up{job="recommendation-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: recommendation-system
        annotations:
          summary: "Recommendation service is down"
          description: "Recommendation service has been down for more than 1 minute."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: HighCPUUsage
        expr: avg(rate(process_cpu_seconds_total{job="recommendation-service"}[5m])) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "High CPU usage in recommendation service"
          description: "Recommendation service has high CPU usage (> 80%) for the last 5 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="recommendation-service"} / 1024 / 1024 / 1024 > 1.5
        for: 5m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "High memory usage in recommendation service"
          description: "Recommendation service is using more than 1.5GB of memory for the last 5 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count{datname="recommendation_db"} > 80
        for: 5m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "High number of database connections"
          description: "Recommendation database has a high number of connections (> 80) for the last 5 minutes."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"

      - alert: SlowDatabaseQueries
        expr: pg_stat_activity_max_tx_duration{datname="recommendation_db"} > 10
        for: 5m
        labels:
          severity: warning
          service: recommendation-system
        annotations:
          summary: "Slow database queries detected"
          description: "Recommendation database has queries running for more than 10 seconds."
          dashboard: "https://grafana.retreat-and-be.com/d/recommendation-system"
