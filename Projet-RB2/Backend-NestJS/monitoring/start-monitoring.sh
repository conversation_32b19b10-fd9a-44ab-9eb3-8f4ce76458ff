#!/bin/bash

# Script pour démarrer l'infrastructure de monitoring du système de recommandation

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Démarrage de l'infrastructure de monitoring du système de recommandation ===${NC}"

# Vérifier si Docker est installé
if ! [ -x "$(command -v docker)" ]; then
  echo -e "${RED}Erreur: Docker n'est pas installé.${NC}" >&2
  exit 1
fi

# Vérifier si Docker Compose est installé
if ! [ -x "$(command -v docker-compose)" ]; then
  echo -e "${RED}Erreur: Docker Compose n'est pas installé.${NC}" >&2
  exit 1
fi

# Créer les dossiers nécessaires
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards

# Créer le fichier de configuration des sources de données Grafana
cat > grafana/provisioning/datasources/datasource.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: false
EOF

# Créer le fichier de configuration des tableaux de bord Grafana
cat > grafana/provisioning/dashboards/dashboard.yml << EOF
apiVersion: 1

providers:
  - name: 'Recommendation System'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /var/lib/grafana/dashboards
EOF

# Démarrer l'infrastructure de monitoring
echo -e "${YELLOW}Démarrage des conteneurs...${NC}"
docker-compose up -d

# Vérifier que les conteneurs sont démarrés
echo -e "${YELLOW}Vérification des conteneurs...${NC}"
sleep 5
CONTAINERS=("prometheus" "alertmanager" "grafana" "node-exporter" "cadvisor" "postgres-exporter")
ALL_RUNNING=true

for CONTAINER in "${CONTAINERS[@]}"; do
  STATUS=$(docker ps --filter "name=$CONTAINER" --format "{{.Status}}" | grep -c "Up")
  if [ "$STATUS" -eq 0 ]; then
    echo -e "${RED}Le conteneur $CONTAINER n'est pas démarré.${NC}"
    ALL_RUNNING=false
  else
    echo -e "${GREEN}Le conteneur $CONTAINER est démarré.${NC}"
  fi
done

if [ "$ALL_RUNNING" = false ]; then
  echo -e "${RED}Certains conteneurs ne sont pas démarrés. Vérifiez les logs avec 'docker-compose logs'.${NC}"
  exit 1
fi

# Afficher les URLs des services
echo -e "${GREEN}=== Infrastructure de monitoring démarrée avec succès ===${NC}"
echo -e "${YELLOW}Prometheus: http://localhost:9090${NC}"
echo -e "${YELLOW}AlertManager: http://localhost:9093${NC}"
echo -e "${YELLOW}Grafana: http://localhost:3001${NC}"
echo -e "${YELLOW}  Utilisateur: admin${NC}"
echo -e "${YELLOW}  Mot de passe: admin${NC}"
echo -e "${YELLOW}Node Exporter: http://localhost:9100${NC}"
echo -e "${YELLOW}cAdvisor: http://localhost:8080${NC}"
echo -e "${YELLOW}Postgres Exporter: http://localhost:9187${NC}"

exit 0
