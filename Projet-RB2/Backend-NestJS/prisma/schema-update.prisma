// Ajout des énumérations pour la modération et l'analyse

// Énumérations pour la modération
enum Severity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ReportStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  ESCALATED
}

enum ActionType {
  APPROVE
  REJECT
  ESCALATE
  WARN_USER
  BAN_USER
  DELETE_CONTENT
  HIDE_CONTENT
}

// Énumérations pour l'analyse
enum RevenueSource {
  SUBSCRIPTION
  DONATION
  PRODUCT_SALE
  AFFILIATE
  ADVERTISING
  SPONSORSHIP
}

enum WidgetType {
  LINE_CHART
  BAR_CHART
  PIE_CHART
  COUNTER
  TABLE
  HEATMAP
  MAP
  GAUGE
  CUSTOM
}

enum DifficultyLevel {
  EASY
  MEDIUM
  HARD
}

// Mise à jour de ContentType pour inclure plus de types
enum ContentType {
  ARTICLE
  VIDEO
  AUDIO
  EXERCISE
  MEDITATION
  WORKSHOP
  TEXT
  IMAGE
  COMMENT
  POST
  RETREAT
}

// Mise à jour de MetricType pour inclure plus de métriques
enum MetricType {
  STRESS_LEVEL
  SLEEP_QUALITY
  ENERGY_LEVEL
  MINDFULNESS
  PHYSICAL_ACTIVITY
  NUTRITION
  VIEWS
  LIKES
  COMMENTS
  SHARES
  BOOKMARKS
  CLICK_THROUGH_RATE
  FOLLOWERS
  ENGAGEMENT_RATE
  REVENUE
  CONVERSION_RATE
}

// Modèles pour la modération de contenu

// Modèle pour les règles de modération de texte
model TextModerationRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  pattern     String   // Regex ou mot-clé
  severity    Severity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("text_moderation_rules")
}

// Modèle pour les règles de modération d'images
model ImageModerationRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  category    String   // Type de contenu à détecter
  threshold   Float    // Seuil de confiance
  severity    Severity
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("image_moderation_rules")
}

// Modèle pour les signalements
model Report {
  id                String           @id @default(uuid())
  contentType       ContentType
  contentId         String
  reporterId        String
  reason            String
  description       String?
  status            ReportStatus     @default(PENDING)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  moderationActions ModerationAction[]

  @@index([contentType, contentId])
  @@index([reporterId])
  @@index([status])
  @@map("reports")
}

// Modèle pour les actions de modération
model ModerationAction {
  id          String       @id @default(uuid())
  report      Report       @relation(fields: [reportId], references: [id])
  reportId    String
  moderatorId String
  action      ActionType
  comment     String?
  createdAt   DateTime     @default(now())

  @@index([reportId])
  @@index([moderatorId])
  @@map("moderation_actions")
}

// Modèle pour la réputation des utilisateurs
model UserReputation {
  id              String   @id @default(uuid())
  userId          String   @unique
  reputationScore Int      @default(0)
  isTrusted       Boolean  @default(false)
  privileges      String[] // Liste des privilèges basés sur la réputation
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
  @@map("user_reputations")
}

// Modèle pour les métriques de modération
model ModerationMetric {
  id                String   @id @default(uuid())
  date              DateTime @default(now())
  totalReports      Int      @default(0)
  processedReports  Int      @default(0)
  approvedReports   Int      @default(0)
  rejectedReports   Int      @default(0)
  averageProcessTime Float?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([date])
  @@map("moderation_metrics")
}

// Modèle pour les logs de modération
model ModerationLog {
  id          String   @id @default(uuid())
  userId      String
  action      String
  details     Json?
  createdAt   DateTime @default(now())
  
  user User @relation("UserModerationLogs", fields: [userId], references: [id])

  @@index([userId])
  @@map("moderation_logs")
}

// Modèles pour l'analyse avancée des créateurs

// Modèle pour les métriques d'engagement
model EngagementMetric {
  id            String         @id @default(uuid())
  creatorId     String
  contentId     String
  contentType   ContentType
  views         Int            @default(0)
  likes         Int            @default(0)
  comments      Int            @default(0)
  shares        Int            @default(0)
  bookmarks     Int            @default(0)
  clickThroughs Int            @default(0)
  date          DateTime       @default(now())
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([creatorId])
  @@index([contentId])
  @@index([date])
  @@map("engagement_metrics")
}

// Modèle pour les métriques d'audience
model AudienceMetric {
  id                String         @id @default(uuid())
  creatorId         String
  totalFollowers    Int            @default(0)
  newFollowers      Int            @default(0)
  lostFollowers     Int            @default(0)
  activeFollowers   Int            @default(0)
  demographics      Json?          // Données démographiques (âge, genre, localisation)
  date              DateTime       @default(now())
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  @@index([creatorId])
  @@index([date])
  @@map("audience_metrics")
}

// Modèle pour les métriques de revenus
model RevenueMetric {
  id                String         @id @default(uuid())
  creatorId         String
  contentId         String?
  amount            Float          @default(0)
  currency          String         @default("EUR")
  source            RevenueSource
  date              DateTime       @default(now())
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  @@index([creatorId])
  @@index([contentId])
  @@index([date])
  @@map("revenue_metrics")
}

// Modèle pour les tableaux de bord personnalisés
model Dashboard {
  id            String         @id @default(uuid())
  creatorId     String
  name          String
  description   String?
  layout        Json           // Configuration du layout
  widgets       Widget[]       // Widgets du tableau de bord
  isDefault     Boolean        @default(false)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([creatorId])
  @@map("dashboards")
}

// Modèle pour les widgets des tableaux de bord
model Widget {
  id            String         @id @default(uuid())
  dashboard     Dashboard      @relation(fields: [dashboardId], references: [id], onDelete: Cascade)
  dashboardId   String
  type          WidgetType
  title         String
  config        Json           // Configuration du widget
  position      Json           // Position dans le tableau de bord
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([dashboardId])
  @@map("widgets")
}

// Modèle pour les prévisions
model Forecast {
  id            String         @id @default(uuid())
  creatorId     String
  contentId     String?
  metricType    MetricType
  predictions   Json           // Valeurs prédites
  confidence    Float          // Niveau de confiance
  startDate     DateTime       // Date de début de la prévision
  endDate       DateTime       // Date de fin de la prévision
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([creatorId])
  @@index([contentId])
  @@map("forecasts")
}

// Modèle pour les benchmarks
model Benchmark {
  id            String         @id @default(uuid())
  category      String         // Catégorie du benchmark
  metricType    MetricType
  value         Float          // Valeur du benchmark
  percentile    Float?         // Percentile (optionnel)
  date          DateTime       @default(now())
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@index([category])
  @@index([metricType])
  @@index([date])
  @@map("benchmarks")
}

// Modèle pour les recommandations
model Recommendation {
  id            String              @id @default(uuid())
  creatorId     String
  type          RecommendationType
  title         String
  description   String
  impact        Float               // Impact estimé
  difficulty    DifficultyLevel
  isImplemented Boolean             @default(false)
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt

  @@index([creatorId])
  @@index([type])
  @@map("recommendations")
}

// Modèle pour le feedback sur les recommandations
model RecommendationFeedback {
  id                String   @id @default(uuid())
  userId            String
  recommendationId  String
  isHelpful         Boolean
  comment           String?
  createdAt         DateTime @default(now())
  
  user User @relation("UserRecommendationFeedback", fields: [userId], references: [id])

  @@index([userId])
  @@index([recommendationId])
  @@map("recommendation_feedback")
}
