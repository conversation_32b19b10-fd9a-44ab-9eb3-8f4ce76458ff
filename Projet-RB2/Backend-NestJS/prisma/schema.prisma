// This is your Prisma Schema file
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "./generated/app"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Profile {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// ==================================
// ENUMS
// ==================================

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum UserRole {
  USER
  ADMIN
  MODERATOR
  PARTNER
  HOST
  ORGANIZER
  PARTICIPANT
}

enum Status {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum Level {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  REFUNDED
}

enum ProjectStatus {
  ACTIVE
  COMPLETED
  CANCELLED
  SUSPENDED
}

enum Season {
  SPRING
  SUMMER
  FALL
  WINTER
}

enum NotificationType {
  BOOKING_CONFIRMATION
  PAYMENT_CONFIRMATION
  RETREAT_REMINDER
  WELCOME
  SYSTEM_MESSAGE
}

enum NotificationStatus {
  SENT
  DELIVERED
  READ
}

enum RetreatStatus {
  DRAFT
  PUBLISHED
  CANCELLED
  COMPLETED
}

enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  WEBHOOK
  IN_APP
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum ScheduleStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}

enum FriendshipStatus {
  PENDING
  ACCEPTED
  REJECTED
  BLOCKED
}

enum CollaborationStatus {
  PENDING
  ACCEPTED
  REJECTED
  BLOCKED
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum DietType {
  VEGETARIAN
  VEGAN
  GLUTEN_FREE
  DAIRY_FREE
  KETO
  PALEO
  STANDARD
}

enum ResourceType {
  DOCUMENT
  VIDEO
  AUDIO
  IMAGE
  PRESENTATION
  GUIDE
  TUTORIAL
  TEMPLATE
  WORKSHEET
  ASSESSMENT
  TOOLKIT
}

enum SessionStatus {
  ACTIVE
  PAUSED
  COMPLETED
}

enum MoodType {
  VERY_SATISFIED
  SATISFIED
  NEUTRAL
  DISSATISFIED
  VERY_DISSATISFIED
}

enum ActivityType {
  WORKSHOP
  EXCURSION
  MEDITATION
  YOGA
  THERAPY
  COACHING
}

enum RegistrationStatus {
  PENDING
  CONFIRMED
  CANCELLED
  WAITLISTED
}

enum SkillLevel {
  NOVICE
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum AchievementCategory {
  PARTICIPATION
  SOCIAL
  WELLNESS
  LEARNING
  CONTRIBUTION
  MILESTONE
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  PENDING_REVIEW
}

enum Visibility {
  PUBLIC
  PRIVATE
  GROUP_ONLY
  FRIENDS_ONLY
}

enum MentorshipStatus {
  PENDING
  ACTIVE
  COMPLETED
  CANCELLED
}

enum NoteVisibility {
  PRIVATE
  SHARED
  PUBLIC
}

enum ParticipationStatus {
  ACTIVE
  COMPLETED
  ABANDONED
}

enum RewardType {
  DISCOUNT
  CREDIT
  FEATURE_ACCESS
  PREMIUM_CONTENT
  SPECIAL_STATUS
}

enum RewardStatus {
  ACTIVE
  USED
  EXPIRED
  REVOKED
}

enum AccessLevel {
  PUBLIC
  PREMIUM
  EXCLUSIVE
  RESTRICTED
}

enum ContentType {
  ARTICLE
  VIDEO
  AUDIO
  EXERCISE
  MEDITATION
  WORKSHOP
}

enum MetricType {
  STRESS_LEVEL
  SLEEP_QUALITY
  ENERGY_LEVEL
  MINDFULNESS
  PHYSICAL_ACTIVITY
  NUTRITION
}

enum GoalStatus {
  IN_PROGRESS
  ACHIEVED
  MISSED
  ABANDONED
}

enum CommunityType {
  INTEREST_GROUP
  PRACTICE_GROUP
  STUDY_GROUP
  SUPPORT_GROUP
  EVENT_BASED
}

enum CommunityStatus {
  ACTIVE
  ARCHIVED
  SUSPENDED
}

enum MemberRole {
  ADMIN
  MODERATOR
  MEMBER
  GUEST
}

enum MemberStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum EventType {
  MEETUP
  WORKSHOP
  DISCUSSION
  PRACTICE
  CELEBRATION
}

enum AttendeeStatus {
  REGISTERED
  WAITLISTED
  ATTENDED
  CANCELLED
}

enum RecommendationType {
  RETREAT
  CONTENT
  EVENT
  COMMUNITY
  MENTOR
}

enum RecommendationStatus {
  ACTIVE
  VIEWED
  DISMISSED
  EXPIRED
}

enum ModuleType {
  THEORY
  PRACTICE
  QUIZ
  REFLECTION
  ASSIGNMENT
}

enum EnrollmentStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  PAUSED
}

enum MeditationType {
  MINDFULNESS
  BREATHING
  BODY_SCAN
  LOVING_KINDNESS
  VISUALIZATION
  TRANSCENDENTAL
}

enum PrivacyLevel {
  PRIVATE
  SHARED_MENTOR
  SHARED_GROUP
  PUBLIC
}

enum CertificateCategory {
  MEDITATION
  YOGA
  WELLNESS
  NUTRITION
  PERSONAL_DEVELOPMENT
}

enum CertificationStatus {
  ACTIVE
  EXPIRED
  REVOKED
  SUSPENDED
}

enum ConsultationType {
  WELLNESS
  NUTRITION
  MEDITATION
  LIFE_COACHING
  SPIRITUAL_GUIDANCE
}

enum SlotStatus {
  AVAILABLE
  BOOKED
  CANCELLED
  COMPLETED
}

enum GoalCategory {
  HEALTH
  MINDFULNESS
  PERSONAL_GROWTH
  RELATIONSHIPS
  CAREER
  SPIRITUAL
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum QuestStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  FAILED
  EXPIRED
}

enum PartnerType {
  PREMIUM_CERTIFIED
  CERTIFIED
  STANDARD
}

enum PartnerCategory {
  ORGANIZER
  TRAVEL_AGENCY
  CATERING
  GUIDE
  TRANSPORT
  WELLNESS
  INSURANCE
  ACCOMMODATION
  EQUIPMENT
  OTHER
}

enum PartnerStatus {
  PENDING
  ACTIVE
  SUSPENDED
  TERMINATED
}

enum ReviewStatus {
  PENDING
  PUBLISHED
  HIDDEN
  FLAGGED
}

enum ChallengeType {
  DAILY
  WEEKLY
  MONTHLY
  SPECIAL
}

enum QuestType {
  STORY
  DAILY
  WEEKLY
  ACHIEVEMENT
  SPECIAL_EVENT
  CHALLENGE
}

enum QuestDifficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
  MASTER
}

enum BadgeType {
  PREMIUM_CERTIFIED
  VERIFIED_PARTNER
  TOP_RATED
  SUSTAINABLE
  EXPERT
}

enum BadgeStatus {
  ACTIVE
  EXPIRED
  REVOKED
  SUSPENDED
}

enum VerificationType {
  IDENTITY
  PROFESSIONAL_LICENSE
  INSURANCE
  CERTIFICATION
  BACKGROUND_CHECK
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum ServiceStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  SEASONAL
}

enum KYCDocumentType {
  IDENTITY
  LICENSE
  INSURANCE
  CERTIFICATION
  TAX
  LEGAL
}

enum DocumentStatus {
  PENDING
  VERIFIED
  REJECTED
  EXPIRED
}

enum TrainingType {
  ONBOARDING
  CERTIFICATION
  COMPLIANCE
  PRODUCT
  SERVICE
  SAFETY
  QUALITY
}

enum TrainingStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  MANDATORY
}

enum SupportType {
  TECHNICAL
  BILLING
  CERTIFICATION
  MARKETING
  GENERAL
  URGENT
}

enum SupportPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum MessageStatus {
  SENT
  DELIVERED
  READ
  FAILED
}

enum SupportStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
  ESCALATED
}

enum CertificationLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum CriteriaStatus {
  PENDING
  COMPLETED
  FAILED
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum ActivityStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum InviteStatus {
  PENDING
  ACCEPTED
  REJECTED
  CANCELLED
}

enum AIAgentType {
  ASSISTANT
  SYSTEM
  USER
}

//==================================
// CORE MODELS
//==================================

model Account {
  id                String  @id @default(uuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model EventParticipant {
  id        String   @id @default(uuid())
  eventId   String
  userId    String
  status    String
  createdAt DateTime @default(now())

  event Event @relation("EventParticipants", fields: [eventId], references: [id])
  user  User  @relation("EventParticipants", fields: [userId], references: [id])
}

model RetreatParticipant {
  id        String   @id @default(uuid())
  retreatId String
  userId    String
  status    String   @default("pending")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  retreat Retreat @relation("RetreatParticipants", fields: [retreatId], references: [id], onDelete: Cascade)
  user    User    @relation("UserRetreatParticipations", fields: [userId], references: [id], onDelete: Cascade)

  metadata   Json?   @default("{}")
  role       String  @default("participant")
  notes      String?
  attendance Json?   @default("{}")

  @@unique([retreatId, userId])
  @@index([retreatId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@map("retreat_participants")
}

//==================================
// PRODUCT, ORDER, REVIEW
//==================================

model Product {
  id          String   @id @default(uuid())
  name        String
  description String?
  price       Float
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  status      String   @default("active")
  sku         String?  @unique
  stock       Int      @default(0)
  featured    Boolean  @default(false)
  metadata    Json?    @default("{}")
  images      String[] @default([])

  // Relations avec User (Vendeur)
  sellerId String @map("seller_id")
  seller   User   @relation("UserProducts", fields: [sellerId], references: [id], onDelete: Cascade)

  // Relations avec Category
  categoryId String?   @map("category_id")
  category   Category? @relation("ProductCategories", fields: [categoryId], references: [id])

  // Relations avec Order et Review
  orders  Order[]
  reviews Review[]

  // Indexes pour optimiser les performances
  @@index([sellerId])
  @@index([categoryId])
  @@index([status])
  @@index([featured])
  @@index([sku])
  // Configuration de la table
  @@map("products")
}

model Order {
  id          String    @id @default(uuid())
  userId      String
  productId   String
  quantity    Int       @default(1)
  totalPrice  Float
  status      String    @default("pending")
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now())
  completedAt DateTime?

  user    User    @relation("UserOrders", fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Restrict)

  payment Payment?
  review  Review?

  metadata     Json? @default("{}")
  shippingInfo Json? @default("{}")
  billingInfo  Json? @default("{}")
  trackingInfo Json? @default("{}")

  @@index([userId])
  @@index([productId])
  @@index([status])
  @@map("orders")
}

model Review {
  id        String   @id @default(uuid())
  rating    Int
  comment   String?  @db.Text
  userId    String
  productId String?
  orderId   String?  @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user    User     @relation("UserReviews", fields: [userId], references: [id], onDelete: Cascade)
  product Product? @relation(fields: [productId], references: [id], onDelete: SetNull)
  order   Order?   @relation(fields: [orderId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([productId])
  @@map("reviews")
}

//==================================
// CATEGORY, BLOG
//==================================

model Category {
  id          String   @id @default(uuid())
  name        String   @unique
  slug        String   @unique
  description String?
  parentId    String?  @map("parent_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  metadata    Json?    @default("{}")
  icon        String?
  color       String?
  featured    Boolean  @default(false)
  order       Int      @default(0)

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: SetNull)
  children Category[] @relation("CategoryHierarchy")
  retreats Retreat[]  @relation("RetreatCategories")
  products Product[]  @relation("ProductCategories")
  blogs    Blog[]     @relation("BlogCategories")

  // Indexes pour optimiser les performances
  @@index([parentId])
  @@index([name])
  @@index([slug])
  @@index([featured])
  @@index([order])
  // Configuration de la table
  @@map("categories")
}

model Blog {
  id              String    @id @default(uuid())
  title           String
  content         String
  authorId        String    @map("author_id")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  publishedAt     DateTime? @map("published_at")
  status          String    @default("draft")
  slug            String    @unique
  excerpt         String?
  metaTitle       String?   @map("meta_title")
  metaDescription String?   @map("meta_description")
  canonicalUrl    String?   @map("canonical_url")
  readingTime     Int?      @default(0) @map("reading_time")
  featured        Boolean   @default(false)
  coverImage      String?   @map("cover_image")
  thumbnailImage  String?   @map("thumbnail_image")
  contentType     String    @default("article") @map("content_type")
  locale          String    @default("en")
  viewCount       Int       @default(0) @map("view_count")
  likeCount       Int       @default(0) @map("like_count")
  commentCount    Int       @default(0) @map("comment_count")
  shareCount      Int       @default(0) @map("share_count")
  metadata        Json?     @default("{}")
  settings        Json?     @default("{}")

  // Relations
  author     User          @relation("UserBlogs", fields: [authorId], references: [id], onDelete: Cascade)
  blogTags   BlogTag[]     @relation("BlogTags")
  categories Category[]    @relation("BlogCategories")
  comments   BlogComment[]
  likes      BlogLike[]

  // Indexes pour optimiser les performances
  @@index([authorId])
  @@index([status])
  @@index([publishedAt])
  @@index([featured])
  @@index([contentType])
  @@index([locale])
  // Configuration de la table
  @@map("blogs")
}

model BlogTag {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relation avec Blog
  blogId String @map("blog_id")
  blog   Blog   @relation("BlogTags", fields: [blogId], references: [id], onDelete: Cascade)

  // Relation avec Tag
  tagId String @map("tag_id")
  tag   Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)

  // Métadonnées optionnelles
  order    Int   @default(0)
  metadata Json? @default("{}")

  // Contraintes et indexes
  @@unique([blogId, tagId], name: "blog_tag_unique")
  @@index([blogId])
  @@index([tagId])
  @@index([order])
  // Configuration de la table
  @@map("blog_tags")
}

model BlogComment {
  id        String   @id @default(uuid())
  content   String
  blogId    String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  blog Blog @relation(fields: [blogId], references: [id])
  user User @relation("UserBlogComments", fields: [userId], references: [id])

  @@index([blogId])
  @@index([userId])
  @@map("blog_comments")
}

model BlogLike {
  id        String   @id @default(uuid())
  blogId    String
  userId    String
  createdAt DateTime @default(now())

  blog Blog @relation(fields: [blogId], references: [id])
  user User @relation("UserBlogLikes", fields: [userId], references: [id])

  @@unique([blogId, userId])
  @@index([blogId])
  @@index([userId])
  @@map("blog_likes")
}

//==================================
// DOCUMENT
//==================================

model Document {
  id        String   @id @default(uuid())
  name      String
  type      String
  size      Int
  metadata  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation("UserDocuments", fields: [userId], references: [id])

  @@map("documents")
}

//==================================
// GROUP & GROUP MEMBER
//==================================

model Group {
  id          String   @id @default(uuid())
  name        String
  description String?  @db.Text
  type        String
  status      String   @default("active")
  rules       String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  ownerId String
  owner   User   @relation("GroupOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  members GroupMember[] @relation("Group_Members")
  users   User[]        @relation("UserGroupsViaPivot")

  posts  Post[]  @relation("GroupPosts")
  events Event[] @relation("GroupEvents")

  metadata Json?    @default("{}")
  location String?
  category String?
  tags     String[] @default([])

  memberCount Int @default(0)
  postCount   Int @default(0)
  eventCount  Int @default(0)

  isPrivate  Boolean @default(false)
  joinPolicy String  @default("open")

  settings Json? @default("{}")

  invites GroupInvite[]

  adminId String?
  admin   User?   @relation("GroupAdmin", fields: [adminId], references: [id])

  @@index([ownerId])
  @@index([type])
  @@index([status])
  @@index([category])
  @@index([createdAt])
  @@map("groups")
}

model GroupMember {
  id        String   @id @default(uuid())
  groupId   String
  userId    String
  role      String   @default("member")
  status    String   @default("active")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  group Group @relation("Group_Members", fields: [groupId], references: [id], onDelete: Cascade)
  user  User  @relation("UserGroupMembers", fields: [userId], references: [id], onDelete: Cascade)

  metadata   Json?     @default("{}")
  notes      String?
  joinedAt   DateTime  @default(now())
  lastActive DateTime?

  @@unique([groupId, userId])
  @@index([groupId])
  @@index([userId])
  @@index([role])
  @@index([status])
  @@map("group_members")
}

//==================================
// USER
//==================================

model User {
  id            String     @id @default(uuid())
  firstName     String?
  lastName      String?
  email         String?    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole   @default(USER)
  isAdmin       Boolean    @default(false)
  provider      String?
  address       String?
  phone         String?
  bio           String?
  birthDate     DateTime?
  gender        String?
  language      String?
  timezone      String?
  currency      String?
  isVerified    Boolean    @default(false)
  isActive      Boolean    @default(true)
  lastLoginAt   DateTime?
  status        UserStatus @default(ACTIVE)

  // 2FA fields
  twoFactorSecret  String?
  twoFactorEnabled Boolean @default(false)

  // Accounts & sessions
  accounts Account[]
  sessions Session[]

  // Post, Comment, Like, Reaction
  posts     Post[]     @relation("UserPosts")
  comments  Comment[]  @relation("UserComments")
  likes     Like[]     @relation("UserLikes")
  reactions Reaction[] @relation("UserReactions")

  // Blogs
  blogs        Blog[]        @relation("UserBlogs")
  blogComments BlogComment[] @relation("UserBlogComments")
  blogLikes    BlogLike[]    @relation("UserBlogLikes")
  documents    Document[]    @relation("UserDocuments")

  // Reviews
  reviews Review[] @relation("UserReviews")

  // Produits & commandes
  products Product[] @relation("UserProducts")
  orders   Order[]   @relation("UserOrders")

  // Groupes
  ownedGroups  Group[]       @relation("GroupOwner")
  adminGroups  Group[]       @relation("GroupAdmin")
  groupMembers GroupMember[] @relation("UserGroupMembers")
  groups       Group[]       @relation("UserGroupsViaPivot")

  groupInvites     GroupInvite[] @relation("GroupInviteReceiver")
  groupInvitesSent GroupInvite[] @relation("GroupInviteSender")

  // Retraites
  organizedRetreats     Retreat[]            @relation("UserOrganizedRetreats")
  retreatParticipations RetreatParticipant[] @relation("UserRetreatParticipations")

  // Event
  eventParticipants    EventParticipant[] @relation("EventParticipants")
  auditEvents          AuditEvent[]
  // Payment & Transaction
  payments             Payment[]          @relation("UserPayments")
  paymentsSent         Payment[]          @relation("PaymentSender")
  paymentsReceived     Payment[]          @relation("PaymentReceiver")
  transactions         Transaction[]      @relation("UserTransactions")
  transactionsSent     Transaction[]      @relation("TransactionSender")
  transactionsReceived Transaction[]      @relation("TransactionReceiver")

  // Mentorship
  mentorships Mentorship[] @relation("MentorRelation")
  menteeship  Mentorship[] @relation("MenteeRelation")

  // Bookings
  bookings Booking[] @relation("UserBookings")

  // Resource
  resourceDownloads ResourceDownload[] @relation("ResourceDownloads")
  resourceRatings   ResourceRating[]   @relation("ResourceRatings")

  // Activity
  activities            Activity[]             @relation("ActivityParticipant")
  activityCreator       Activity[]             @relation("ActivityCreator")
  activityRegistrations ActivityRegistration[]
  activitiesCreated     Activity[]             @relation("ActivitiesCreatedByUser")
  userActivities        Activity[]             @relation("UserActivities")
  userActivity          Activity[]             @relation("ActivityUser")

  // Notification
  notifications     Notification[] @relation("NotificationReceiver")
  notificationsSent Notification[] @relation("NotificationSender")

  // Vector documents
  authoredVectorDocuments VectorDocument[] @relation("UserVectorDocumentsAuthor")
  ownedVectorDocuments    VectorDocument[] @relation("UserVectorDocumentsOwner")

  // Additional items: Referrals, Enrollments, etc.
  userReferrals Referral[] @relation("UserReferrals")
  referrals     Referral[] @relation("ReferralCreator")
  referredBy    Referral[] @relation("ReferredUser")

  // Achievements
  userAchievements      UserAchievement[]      @relation("UserAchievements")
  userBadges            UserBadge[]            @relation("UserBadges")
  challengeParticipants ChallengeParticipant[] @relation("UserChallenges")
  playerProgress        PlayerProgress[]       @relation("UserPlayerProgress")
  questProgress         QuestProgress[]        @relation("UserQuestProgress")

  // Community
  communityMembers    CommunityMember[] @relation("UserCommunityMembers")
  discussionsAuthored Discussion[]      @relation("UserDiscussions")
  repliesAuthored     Reply[]           @relation("UserReplies")
  eventAttendees      EventAttendee[]   @relation("UserEventAttendees")

  // Learning
  userEnrollments       Enrollment[]       @relation("UserEnrollments")
  userPathEnrollments   PathEnrollment[]   @relation("UserPathEnrollments")
  userModuleCompletions ModuleCompletion[] @relation("UserModuleCompletions")

  // Certification
  certificationIssuances CertificationIssuance[] @relation("UserCertificationIssuances")

  // Consultation
  expertSlots    ConsultationSlot[]    @relation("ExpertSlots")
  clientBookings ConsultationBooking[] @relation("ClientBookings")

  // Meditation
  meditationPractices MeditationPractice[] @relation("UserMeditationPractices")

  // Wellness
  wellnessTracker WellnessTracker? @relation("UserWellnessTracker")
  journals        Journal[]        @relation("UserJournals")

  // Partner
  partner        Partner?        @relation("UserPartner")
  partnerReviews PartnerReview[] @relation("UserPartnerReviews")

  // RewardPoints
  rewardPoints RewardPoint[] @relation("UserRewardPoints")

  // ReferralCode
  referralCodes ReferralCode[] @relation("UserReferralCodes")

  // NotificationPreference
  notificationPreference NotificationPreference? @relation("UserNotificationPreferences")

  // Security
  securityAlerts        SecurityAlert[]
  securityLogs          SecurityLog[]
  securitySessions      SecuritySession[]
  securitySettings      SecuritySettings?      @relation("UserSecuritySettings")
  twoFactorAuthSettings TwoFactorAuthSettings? @relation("UserTwoFactorAuthSettings")
  assignedIncidents     SecurityIncident[]     @relation("AssignedIncidents")
  reportedIncidents     SecurityIncident[]     @relation("ReportedIncidents")
  incidentActivities    IncidentActivity[]

  // Additional
  preferenceData    UserPreferenceData?
  preferences       UserPreferences?
  location          Location?
  wishlist          Wishlist[]              @relation("UserWishlist")
  subscription      Subscription[]          @relation("UserSubscriptions")
  activityLogs      ActivityLog[]
  downloads         Download[]
  personalGoals     PersonalGoal[]
  recommendations   ContentRecommendation[]
  supportResponses  SupportResponse[]
  feedbackResponses FeedbackResponse[]      @relation("UserFeedbackResponses")

  // RBAC
  roles UserRolePivot[] @relation("UserRolesPivot_User")

  // KYC Verification
  kycVerifications KYCVerification[]

  // Videos
  videos Video[] @relation("UserVideos")

  // Wellness Bookings
  wellnessBookings WellnessBooking[] @relation("UserWellnessBookings")

  // Social Platform
  hostedLivestreams      Livestream[]           @relation("UserHostedLivestreams")
  livestreamMessages     LivestreamMessage[]    @relation("UserLivestreamMessages")
  livestreamParticipations LivestreamParticipant[] @relation("UserLivestreamParticipations")
  blogPosts              BlogPost[]             @relation("UserBlogPosts")
  socialEvents           SocialEvent[]          @relation("UserSocialEvents")

  // Moderation
  moderationLogs ModerationLog[] @relation("UserModerationLogs")

  // Recommendation Feedback
  recommendationFeedback RecommendationFeedback[] @relation("UserRecommendationFeedback")

  // Image relations
  images        Image[] @relation("UserImages")
  createdImages Image[] @relation("ImageCreatedBy")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Extras (Rewards, RetreatReviews, Tags, Schedules, Events, etc.)
  rewards                   Reward[]           @relation("UserRewards")
  retreatReviews            RetreatReview[]    @relation("UserRetreatReviews")
  createdTags               Tag[]              @relation("UserTags")
  ownedSchedules            Schedule[]         @relation("ScheduleOwner")
  scheduleParticipations    Schedule[]         @relation("ScheduleParticipants")
  organizedEvents           Event[]            @relation("EventOrganizer")
  // Messaging
  createdConversations    Conversation[]     @relation("UserCreatedConversations")
  participatedConversations Conversation[]   @relation("ConversationParticipants")
  adminConversations      Conversation[]     @relation("ConversationAdmin")
  sentMessages            Message[]          @relation("sender")
  receivedMessages        Message[]          @relation("receiver")
  readMessages            Message[]          @relation("MessageReadUsers")
  userConversations       UserConversation[]
  followers                 Follow[]           @relation("Followers")
  following                 Follow[]           @relation("Following")
  friendshipsRequested      Friendship[]       @relation("FriendshipRequester")
  friendshipsReceived       Friendship[]       @relation("FriendshipAddressee")
  sentCollaborations        Collaboration[]    @relation("SentCollaborations")
  receivedCollaborations    Collaboration[]    @relation("ReceivedCollaborations")
  AIConversation            AIConversation[]

  // Continuous Learning
  userModel                 UserModel?
  userPreferences           UserPreference[]
  categoryInterests         CategoryInterest[]
  tagInterests              TagInterest[]
  learningEvents            LearningEvent[]

  // Explanation System
  explanationHistory        ExplanationHistory[]
  explanationEvents         ExplanationEvent[]
  explanationABAssignments  ExplanationABTestAssignment[]
  explanationABInteractions ExplanationABTestInteraction[]
  explanationPreferences    ExplanationPreferences?

  // Matching Analytics
  matchingAnalytics MatchingAnalytics[] @relation("UserMatchingAnalytics")

  // Export History
  exportHistory ExportHistory[] @relation("UserExportHistory")

  @@index([email])
  @@index([status])
  @@index([role])
  @@index([createdAt])
  @@map("users")
}

//==================================
// TRANSACTION
//==================================

model Transaction {
  id          String            @id @default(uuid())
  amount      Float
  currency    String
  status      TransactionStatus @default(PENDING)
  type        String
  description String?
  senderId    String
  receiverId  String
  userId      String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @default(now())

  sender   User @relation("TransactionSender", fields: [senderId], references: [id])
  receiver User @relation("TransactionReceiver", fields: [receiverId], references: [id])
  user     User @relation("UserTransactions", fields: [userId], references: [id])

  @@map("transactions")
}

//==================================
// GROUP INVITE
//==================================

model GroupInvite {
  id         String       @id @default(uuid())
  groupId    String
  senderId   String
  receiverId String
  status     InviteStatus @default(PENDING)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @default(now())

  group    Group @relation(fields: [groupId], references: [id])
  sender   User  @relation("GroupInviteSender", fields: [senderId], references: [id])
  receiver User  @relation("GroupInviteReceiver", fields: [receiverId], references: [id])

  @@map("group_invites")
}

//==================================
// ACTIVITY
//==================================

model Activity {
  id                  String         @id @default(uuid())
  title               String
  description         String?
  type                String
  startDate           DateTime
  endDate             DateTime?
  maxParticipants     Int
  currentParticipants Int
  location            String?
  capacity            Int
  price               Float
  status              ActivityStatus @default(PLANNED)
  userId              String
  user                User           @relation("ActivityUser", fields: [userId], references: [id])
  creatorId           String
  createdAt           DateTime       @default(now())
  updatedAt           DateTime       @default(now())
  bookings            Booking[]
  createdById         String         @map("createdById")

  creator        User   @relation("ActivityCreator", fields: [creatorId], references: [id])
  participants   User[] @relation("ActivityParticipant")
  createdBy      User[] @relation("ActivitiesCreatedByUser")
  userActivities User[] @relation("UserActivities")

  @@index([userId])
  @@map("activities")
}

//==================================
// USER RELATED MODELS
//==================================

model Wishlist {
  id        String   @id @default(uuid())
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  retreatId String?

  user    User     @relation("UserWishlist", fields: [userId], references: [id])
  retreat Retreat? @relation("RetreatWishlist", fields: [retreatId], references: [id])

  @@map("wishlists")
}

model Subscription {
  id        String   @id @default(uuid())
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user User @relation("UserSubscriptions", fields: [userId], references: [id])

  @@map("subscriptions")
}

model ActivityLog {
  id        String   @id @default(uuid())
  userId    String
  action    String
  details   Json?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model Download {
  id        String   @id @default(uuid())
  userId    String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("downloads")
}

model ActivityRegistration {
  id        String   @id @default(uuid())
  userId    String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("activity_registrations")
}

model UserPreferenceData {
  id     String @id @default(uuid())
  userId String @unique

  user User @relation(fields: [userId], references: [id])

  @@map("user_preference_data")
}

model Mentorship {
  id        String   @id @default(uuid())
  mentorId  String
  menteeId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  mentor User @relation("MentorRelation", fields: [mentorId], references: [id])
  mentee User @relation("MenteeRelation", fields: [menteeId], references: [id])

  @@map("mentorships")
}

//==================================
// RESOURCE DOWNLOAD / RATING
//==================================

model ResourceDownload {
  id         String   @id @default(uuid())
  resourceId String
  userId     String
  createdAt  DateTime @default(now())

  resource Resource @relation("ResourceDownloads", fields: [resourceId], references: [id])
  user     User     @relation("ResourceDownloads", fields: [userId], references: [id])

  @@map("resource_downloads")
}

model ResourceRating {
  id         String   @id @default(uuid())
  resourceId String
  userId     String
  rating     Int
  createdAt  DateTime @default(now())

  resource Resource @relation("ResourceRatings", fields: [resourceId], references: [id])
  user     User     @relation("ResourceRatings", fields: [userId], references: [id])

  @@map("resource_ratings")
}

//==================================
// REWARD
//==================================

model Reward {
  id          String   @id @default(uuid())
  userId      String
  type        String
  description String
  createdAt   DateTime @default(now())

  user User @relation("UserRewards", fields: [userId], references: [id])

  @@map("rewards")
}

//==================================
// CONTENT RECOMMENDATIONS
//==================================

model ContentRecommendation {
  id        String   @id @default(uuid())
  userId    String
  content   String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("content_recommendations")
}

model PersonalGoal {
  id        String   @id @default(uuid())
  userId    String
  title     String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("personal_goals")
}

model SupportResponse {
  id        String   @id @default(uuid())
  userId    String
  content   String
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("support_responses")
}

//==================================
// LOCATION AND PREFERENCES
//==================================

model Location {
  id        String @id @default(uuid())
  latitude  Float
  longitude Float
  address   String
  city      String
  country   String
  userId    String @unique

  user User @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  equipment         Equipment[]
  weatherRecords    WeatherRecord[]
  dietaryPlans      DietaryPlan[]
  specialActivities SpecialActivity[]

  @@map("locations")
}

model UserPreferences {
  id     String @id @default(uuid())
  userId String @unique

  user User @relation(fields: [userId], references: [id])

  categories       String[]
  activities       String[]
  minPrice         Float?
  maxPrice         Float?
  maxDistance      Float?
  preferredSeasons Season[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("user_preferences")
}

model Equipment {
  id         String   @id @default(uuid())
  locationId String
  name       String
  status     String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())
  retreatId  String?

  location Location @relation(fields: [locationId], references: [id])
  retreat  Retreat? @relation("RetreatEquipment", fields: [retreatId], references: [id])

  @@map("equipment")
}

model WeatherRecord {
  id          String   @id @default(uuid())
  locationId  String
  temperature Float
  conditions  String
  recordedAt  DateTime @default(now())
  retreatId   String?

  location Location @relation(fields: [locationId], references: [id])
  retreat  Retreat? @relation("RetreatWeatherRecords", fields: [retreatId], references: [id])

  @@map("weather_records")
}

model DietaryPlan {
  id          String   @id @default(uuid())
  locationId  String
  name        String
  description String
  createdAt   DateTime @default(now())
  retreatId   String?

  location Location @relation(fields: [locationId], references: [id])
  retreat  Retreat? @relation("RetreatDietaryPlans", fields: [retreatId], references: [id])

  @@map("dietary_plans")
}

model SpecialActivity {
  id          String   @id @default(uuid())
  locationId  String
  name        String
  description String
  createdAt   DateTime @default(now())
  retreatId   String?

  location Location @relation(fields: [locationId], references: [id])
  retreat  Retreat? @relation("RetreatSpecialActivities", fields: [retreatId], references: [id])

  @@map("special_activities")
}

//==================================
// PERMISSION SYSTEM (RBAC)
//==================================

model Role {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users           UserRolePivot[]
  rolePermissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]

  @@map("permissions")
}

model RolePermission {
  id           String   @id @default(uuid())
  roleId       String
  permissionId String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  role       Role       @relation(fields: [roleId], references: [id])
  permission Permission @relation(fields: [permissionId], references: [id])

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRolePivot {
  id        String   @id @default(uuid())
  userId    String
  roleId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation("UserRolesPivot_User", fields: [userId], references: [id])
  role Role @relation(fields: [roleId], references: [id])

  @@unique([userId, roleId])
  @@map("user_roles_pivot")
}

//==================================
// RETREAT SYSTEM
//==================================

model Retreat {
  id                 String        @id @default(uuid())
  title              String
  description        String        @db.Text
  startDate          DateTime      @map("start_date")
  endDate            DateTime      @map("end_date")
  location           String
  capacity           Int
  price              Float
  status             RetreatStatus @default(DRAFT)
  type               String
  level              String
  language           String
  prerequisites      String?       @db.Text
  includedItems      String[]      @map("included_items")
  excludedItems      String[]      @map("excluded_items")
  cancellationPolicy String        @map("cancellation_policy") @db.Text
  images             String[]
  featured           Boolean       @default(false)
  averageRating      Float?        @map("average_rating")
  totalRatings       Int           @default(0) @map("total_ratings")
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")

  // Relations avec User (Organisateur)
  organizerId String @map("organizer_id")
  organizer   User   @relation("UserOrganizedRetreats", fields: [organizerId], references: [id])

  // Relations avec Category
  categories Category[] @relation("RetreatCategories")

  // Relations principales
  participants RetreatParticipant[] @relation("RetreatParticipants")
  bookings     Booking[]            @relation("RetreatBookings")
  reviews      RetreatReview[]      @relation("RetreatReviews")

  // Relations de contenu
  schedules         Schedule[]        @relation("ScheduleRetreat")
  events            Event[]           @relation("RetreatEvents")
  specialActivities SpecialActivity[] @relation("RetreatSpecialActivities")
  equipment         Equipment[]       @relation("RetreatEquipment")
  resources         Resource[]        @relation("RetreatResources")
  dietaryPlans      DietaryPlan[]     @relation("RetreatDietaryPlans")

  // Relations de support
  weatherRecords   WeatherRecord[]      @relation("RetreatWeatherRecords")
  translations     RetreatTranslation[] @relation("RetreatTranslations")
  feedbackSessions FeedbackSession[]    @relation("RetreatFeedbackSession")
  certificates     Certificate[]        @relation("CertificateRetreat")

  // Relations commerciales
  partners Partner[]    @relation("PartnerRetreats")
  payments Payment[]    @relation("PaymentRetreat")
  wishlist Wishlist[]   @relation("RetreatWishlist")
  tags     RetreatTag[] @relation("RetreatTags")

  // Analytics
  matchingAnalytics MatchingAnalytics[] @relation("RetreatMatchingAnalytics")

  // Indexes pour optimiser les performances
  @@index([organizerId])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
  @@index([featured])
  @@index([price])
  @@index([averageRating])
  @@index([type])
  @@index([level])
  @@index([language])
  @@map("retreats")
}

model RetreatTranslation {
  id          String   @id @default(uuid())
  retreatId   String
  language    String
  title       String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  retreat Retreat @relation("RetreatTranslations", fields: [retreatId], references: [id])

  @@unique([retreatId, language])
  @@map("retreat_translations")
}

model RetreatReview {
  id        String   @id @default(uuid())
  rating    Int
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  userId    String
  retreatId String

  user    User    @relation("UserRetreatReviews", fields: [userId], references: [id])
  retreat Retreat @relation("RetreatReviews", fields: [retreatId], references: [id])

  @@index([userId])
  @@index([retreatId])
  @@map("retreat_reviews")
}

model RetreatTag {
  id        String   @id @default(uuid())
  retreatId String
  tagId     String
  createdAt DateTime @default(now())

  // Relations
  retreat Retreat @relation("RetreatTags", fields: [retreatId], references: [id], onDelete: Cascade)
  tag     Tag     @relation("RetreatTagsPivot", fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([retreatId, tagId])
  @@index([retreatId])
  @@index([tagId])
  @@map("retreat_tags")
}

//==================================
// TAGS
//==================================

model Tag {
  id          String   @id @default(uuid())
  name        String   @unique
  slug        String   @unique
  description String?  @db.Text
  type        String?
  color       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())
  metadata    Json?    @default("{}")
  isActive    Boolean  @default(true)

  blogTags    BlogTag[]
  retreatTags RetreatTag[] @relation("RetreatTagsPivot")

  creatorId String?
  creator   User?     @relation("UserTags", fields: [creatorId], references: [id])
  postTags  PostTag[] @relation("PostTagsPivot")

  @@index([creatorId])
  @@index([type])
  @@index([name])
  @@index([slug])
  @@map("tags")
}

//==================================
// BOOKING & PAYMENT
//==================================

model Booking {
  id         String        @id @default(uuid())
  activityId String
  status     BookingStatus @default(PENDING) // Utilisation d'une enum pour le status
  date       DateTime
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt // Utiliser @updatedAt au lieu de @default(now())
  activity   Activity      @relation(fields: [activityId], references: [id])

  userId String
  user   User   @relation("UserBookings", fields: [userId], references: [id])

  retreatId String?
  retreat   Retreat? @relation("RetreatBookings", fields: [retreatId], references: [id])

  payment Payment?

  couponId String?
  coupon   Coupon? @relation("BookingCouponPivot", fields: [couponId], references: [id])

  @@index([userId])
  @@index([retreatId]) // Ajout d'un index sur retreatId pour de meilleures performances
  @@map("bookings")
}

model Payment {
  id          String        @id @default(uuid())
  amount      Float
  currency    String        @default("USD")
  status      PaymentStatus @default(PENDING)
  type        PaymentType   @default(DEPOSIT)
  description String?       @db.Text
  provider    String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @default(now())

  userId String
  user   User   @relation("UserPayments", fields: [userId], references: [id], onDelete: Cascade)

  senderId String?
  sender   User?   @relation("PaymentSender", fields: [senderId], references: [id], onDelete: SetNull)

  receiverId String?
  receiver   User?   @relation("PaymentReceiver", fields: [receiverId], references: [id], onDelete: SetNull)

  orderId String? @unique
  order   Order?  @relation(fields: [orderId], references: [id], onDelete: SetNull)

  bookingId String?  @unique
  booking   Booking? @relation(fields: [bookingId], references: [id], onDelete: SetNull)

  retreatId String?
  retreat   Retreat? @relation("PaymentRetreat", fields: [retreatId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([senderId])
  @@index([receiverId])
  @@index([retreatId])
  @@map("payments")
}

enum PaymentType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  ORDER
  BOOKING
  RETREAT
}

model Coupon {
  id         String    @id @default(uuid())
  code       String    @unique
  discount   Float
  isActive   Boolean   @default(true)
  usageLimit Int?
  usedCount  Int       @default(0)
  validUntil DateTime?

  bookings Booking[] @relation("BookingCouponPivot")

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("coupons")
}

//==================================
// SCHEDULE & EVENT
//==================================

model Schedule {
  id          String   @id @default(uuid())
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime
  status      String
  location    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  ownerId String
  owner   User   @relation("ScheduleOwner", fields: [ownerId], references: [id])

  participants User[] @relation("ScheduleParticipants")

  eventId String?
  event   Event?  @relation("ScheduleEvent", fields: [eventId], references: [id])

  retreatId String?
  retreat   Retreat? @relation("ScheduleRetreat", fields: [retreatId], references: [id])

  @@index([ownerId])
  @@index([eventId])
  @@index([retreatId])
  @@map("schedules")
}

model Event {
  id          String   @id @default(uuid())
  title       String
  description String?  @db.Text
  startDate   DateTime
  endDate     DateTime
  location    String?
  status      String   @default("draft")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  organizerId String
  organizer   User   @relation("EventOrganizer", fields: [organizerId], references: [id])

  participants EventParticipant[] @relation("EventParticipants")

  groupId String?
  group   Group?  @relation("GroupEvents", fields: [groupId], references: [id])

  retreatId String?
  retreat   Retreat? @relation("RetreatEvents", fields: [retreatId], references: [id])

  schedules Schedule[] @relation("ScheduleEvent")

  metadata Json? @default("{}")

  @@index([organizerId])
  @@index([groupId])
  @@map("events")
}

//==================================
// NOTIFICATION
//==================================

model Notification {
  id        String   @id @default(uuid())
  type      String
  title     String
  message   String
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  receiverId String
  senderId   String

  receiver User @relation("NotificationReceiver", fields: [receiverId], references: [id])
  sender   User @relation("NotificationSender", fields: [senderId], references: [id])

  @@index([receiverId])
  @@index([senderId])
  @@map("notifications")
}

model NotificationPreference {
  id           String  @id @default(uuid())
  userId       String  @unique
  emailEnabled Boolean @default(true)
  smsEnabled   Boolean @default(true)
  pushEnabled  Boolean @default(true)
  inAppEnabled Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user User @relation("UserNotificationPreferences", fields: [userId], references: [id])

  @@map("notification_preferences")
}

//==================================
// MESSAGING
//==================================

enum MessageType {
  TEXT
  IMAGE
  FILE
  AUDIO
  VIDEO
  LOCATION
  SYSTEM
}

enum ConversationType {
  DIRECT
  GROUP
  SUPPORT
  SYSTEM
}

model Conversation {
  id          String           @id @default(uuid())
  title       String?
  type        ConversationType @default(DIRECT)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  lastMessage DateTime?
  metadata    Json?            @default("{}")
  isEncrypted Boolean          @default(false)

  messages          Message[]
  participants      User[]             @relation("ConversationParticipants")
  admins            User[]             @relation("ConversationAdmin")
  userConversations UserConversation[]
  createdBy         User               @relation("UserCreatedConversations", fields: [createdById], references: [id])
  createdById       String

  @@index([createdById])
  @@index([type])
  @@index([lastMessage])
  @@map("conversations")
}

model Message {
  id             String        @id @default(uuid())
  content        String        @db.Text
  conversationId String
  senderId       String
  receiverId     String?
  type           MessageType   @default(TEXT)
  status         MessageStatus @default(SENT)
  sentAt         DateTime      @default(now())
  deliveredAt    DateTime?
  readAt         DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  metadata       Json?         @default("{}")
  replyToId      String?
  isEncrypted    Boolean       @default(false)

  conversation Conversation @relation(fields: [conversationId], references: [id])
  sender       User         @relation("sender", fields: [senderId], references: [id])
  receiver     User?        @relation("receiver", fields: [receiverId], references: [id])
  replyTo      Message?     @relation("MessageReplies", fields: [replyToId], references: [id])
  replies      Message[]    @relation("MessageReplies")

  // AI-related fields
  role     String?  @default("USER")
  agentId  String?
  agent    AIAgent? @relation("AIAgentMessages", fields: [agentId], references: [id])

  // Message tracking
  readBy      User[]             @relation("MessageReadUsers")
  reactions   Reaction[]         @relation("MessageReactions")
  attachments MessageAttachment[] @relation("MessageAttachments")

  @@index([conversationId])
  @@index([senderId])
  @@index([receiverId])
  @@index([replyToId])
  @@index([status])
  @@index([sentAt])
  @@map("messages")
}

model UserConversation {
  id             String    @id @default(uuid())
  userId         String
  conversationId String
  joinedAt       DateTime  @default(now())
  leftAt         DateTime?
  role           String    @default("member")
  status         String    @default("active")
  lastRead       DateTime?
  nickname       String?

  user         User         @relation(fields: [userId], references: [id])
  conversation Conversation @relation(fields: [conversationId], references: [id])

  @@unique([userId, conversationId])
  @@index([userId])
  @@index([conversationId])
  @@map("user_conversations")
}

model MessageAttachment {
  id        String   @id @default(uuid())
  messageId String
  type      String
  url       String
  name      String
  size      Int
  mimeType  String
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now())

  message Message @relation("MessageAttachments", fields: [messageId], references: [id])

  @@index([messageId])
  @@map("message_attachments")
}

model Reaction {
  id        String   @id @default(uuid())
  type      String
  postId    String?
  messageId String?
  userId    String
  commentId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  // Principales relations
  user    User     @relation("UserReactions", fields: [userId], references: [id], onDelete: Cascade)
  post    Post?    @relation("PostReactions", fields: [postId], references: [id], onDelete: Cascade)
  message Message? @relation("MessageReactions", fields: [messageId], references: [id], onDelete: Cascade)
  comment Comment? @relation("CommentReactions", fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, postId, commentId, type])
  @@index([userId])
  @@index([postId])
  @@index([commentId])
  @@index([type])
  @@map("reactions")
}

//==================================
// SOCIAL
//==================================

model Follow {
  id          String   @id @default(uuid())
  followerId  String
  followingId String
  status      String   @default("pending")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  follower  User @relation("Followers", fields: [followerId], references: [id], onDelete: Cascade)
  following User @relation("Following", fields: [followingId], references: [id], onDelete: Cascade)

  metadata     Json?   @default("{}")
  notification Boolean @default(true)

  @@unique([followerId, followingId])
  @@index([followerId])
  @@index([followingId])
  @@map("follows")
}

model Friendship {
  id        String   @id @default(uuid())
  status    String   @default("pending")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  requesterId String
  addresseeId String

  requester User @relation("FriendshipRequester", fields: [requesterId], references: [id], onDelete: Cascade)
  addressee User @relation("FriendshipAddressee", fields: [addresseeId], references: [id], onDelete: Cascade)

  metadata          Json?     @default("{}")
  lastInteractionAt DateTime?

  @@unique([requesterId, addresseeId])
  @@index([requesterId])
  @@index([addresseeId])
  @@index([status])
  @@map("friendships")
}

model Like {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  userId String
  user   User   @relation("UserLikes", fields: [userId], references: [id], onDelete: Cascade)

  postId String?
  post   Post?   @relation("PostLikes", fields: [postId], references: [id], onDelete: Cascade)

  commentId String?
  comment   Comment? @relation("CommentLikes", fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, postId, commentId])
  @@index([userId])
  @@index([postId])
  @@index([commentId])
  @@map("likes")
}

model Comment {
  id        String   @id @default(uuid())
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  userId String
  user   User   @relation("UserComments", fields: [userId], references: [id], onDelete: Cascade)

  postId String
  post   Post   @relation("PostComments", fields: [postId], references: [id], onDelete: Cascade)

  parentId String?
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")

  likes     Like[]     @relation("CommentLikes")
  reactions Reaction[] @relation("CommentReactions")

  @@index([userId])
  @@index([postId])
  @@index([parentId])
  @@map("comments")
}

//==================================
// GAMIFICATION
//==================================

model Achievement {
  id          String              @id @default(uuid())
  name        String
  description String
  icon        String?
  points      Int
  category    AchievementCategory
  criteria    Json

  users UserAchievement[] @relation("AchievementUsers")

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("achievements")
}

model UserAchievement {
  id            String   @id @default(uuid())
  userId        String
  achievementId String
  unlockedAt    DateTime @default(now())

  user        User        @relation("UserAchievements", fields: [userId], references: [id])
  achievement Achievement @relation("AchievementUsers", fields: [achievementId], references: [id])

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model Badge {
  id          String  @id @default(uuid())
  name        String  @unique
  description String?
  iconUrl     String?

  userBadges UserBadge[] @relation("BadgeUsers")

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("badges")
}

model UserBadge {
  userId     String
  badgeId    String
  dateEarned DateTime @default(now())

  user  User  @relation("UserBadges", fields: [userId], references: [id])
  badge Badge @relation("BadgeUsers", fields: [badgeId], references: [id])

  @@id([userId, badgeId])
  @@map("user_badges")
}

model Challenge {
  id          String        @id @default(uuid())
  title       String
  description String
  startDate   DateTime
  endDate     DateTime
  type        ChallengeType
  rewards     Json
  criteria    Json

  participants ChallengeParticipant[] @relation("ChallengeUsers")

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("challenges")
}

model ChallengeParticipant {
  id          String              @id @default(uuid())
  challengeId String
  userId      String
  progress    Int                 @default(0)
  status      ParticipationStatus @default(ACTIVE)

  challenge Challenge @relation("ChallengeUsers", fields: [challengeId], references: [id])
  user      User      @relation("UserChallenges", fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@unique([challengeId, userId])
  @@map("challenge_participants")
}

model GameSystem {
  id          String @id @default(uuid())
  name        String
  description String
  rules       Json

  levels GameLevel[]
  quests Quest[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("game_systems")
}

model GameLevel {
  id          String @id @default(uuid())
  systemId    String
  level       Int
  name        String
  description String
  xpRequired  Int
  rewards     Json

  system  GameSystem       @relation(fields: [systemId], references: [id])
  players PlayerProgress[] @relation("LevelPlayers")

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("game_levels")
}

model PlayerProgress {
  id           String @id @default(uuid())
  userId       String
  levelId      String
  currentXP    Int    @default(0)
  achievements Json

  user  User      @relation("UserPlayerProgress", fields: [userId], references: [id])
  level GameLevel @relation("LevelPlayers", fields: [levelId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("player_progress")
}

model Quest {
  id          String          @id @default(uuid())
  systemId    String
  title       String
  description String
  type        QuestType
  difficulty  QuestDifficulty
  rewards     Json
  conditions  Json

  system   GameSystem      @relation(fields: [systemId], references: [id])
  progress QuestProgress[] @relation("QuestUsers")

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("quests")
}

model QuestProgress {
  id             String      @id @default(uuid())
  questId        String
  userId         String
  progress       Float       @default(0)
  status         QuestStatus @default(IN_PROGRESS)
  completedSteps Json

  startedAt   DateTime  @default(now())
  completedAt DateTime?

  quest Quest @relation("QuestUsers", fields: [questId], references: [id])
  user  User  @relation("UserQuestProgress", fields: [userId], references: [id])

  @@map("quest_progress")
}

//==================================
// COMMUNITY
//==================================

model Community {
  id          String          @id @default(uuid())
  name        String
  description String
  type        CommunityType
  status      CommunityStatus @default(ACTIVE)
  rules       String[]

  members     CommunityMember[]
  discussions Discussion[]
  events      CommunityEvent[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("communities")
}

model CommunityMember {
  id          String       @id @default(uuid())
  communityId String
  userId      String
  role        MemberRole   @default(MEMBER)
  status      MemberStatus @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  community Community @relation(fields: [communityId], references: [id])
  user      User      @relation("UserCommunityMembers", fields: [userId], references: [id])

  @@unique([communityId, userId])
  @@map("community_members")
}

model Discussion {
  id          String   @id @default(uuid())
  communityId String
  title       String
  content     String
  authorId    String
  tags        String[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  community Community @relation(fields: [communityId], references: [id])
  author    User      @relation("UserDiscussions", fields: [authorId], references: [id])

  replies Reply[]

  @@map("discussions")
}

model Reply {
  id            String  @id @default(uuid())
  discussionId  String
  content       String
  authorId      String
  parentReplyId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  discussion   Discussion @relation(fields: [discussionId], references: [id])
  author       User       @relation("UserReplies", fields: [authorId], references: [id])
  parentReply  Reply?     @relation("ReplyToReply", fields: [parentReplyId], references: [id])
  childReplies Reply[]    @relation("ReplyToReply")

  @@map("replies")
}

model CommunityEvent {
  id          String    @id @default(uuid())
  communityId String
  title       String
  description String
  startDate   DateTime
  endDate     DateTime
  location    String?
  type        EventType
  capacity    Int?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  community Community       @relation(fields: [communityId], references: [id])
  attendees EventAttendee[]

  @@map("community_events")
}

model EventAttendee {
  id      String         @id @default(uuid())
  eventId String
  userId  String
  status  AttendeeStatus @default(REGISTERED)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  event CommunityEvent @relation(fields: [eventId], references: [id])
  user  User           @relation("UserEventAttendees", fields: [userId], references: [id])

  @@unique([eventId, userId])
  @@map("event_attendees")
}

//==================================
// LEARNING SYSTEM
//==================================

model Course {
  id          String  @id @default(uuid())
  title       String
  description String
  level       String?
  coverImage  String?
  category    String?

  lessons     Lesson[]
  enrollments Enrollment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("courses")
}

model Lesson {
  id       String  @id @default(uuid())
  title    String
  content  String?
  videoUrl String?
  order    Int

  courseId String
  course   Course @relation(fields: [courseId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([courseId])
  @@map("lessons")
}

model Enrollment {
  id       String @id @default(uuid())
  progress Int    @default(0)

  userId   String
  user     User   @relation("UserEnrollments", fields: [userId], references: [id])
  courseId String
  course   Course @relation(fields: [courseId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@unique([userId, courseId])
  @@map("enrollments")
}

model LearningPath {
  id          String     @id @default(uuid())
  title       String
  description String
  level       SkillLevel
  duration    Int
  objectives  String[]

  modules     LearningModule[]
  enrollments PathEnrollment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("learning_paths")
}

model LearningModule {
  id       String     @id @default(uuid())
  pathId   String
  title    String
  content  Json
  order    Int
  duration Int
  type     ModuleType

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  path        LearningPath       @relation(fields: [pathId], references: [id])
  completions ModuleCompletion[]

  @@map("learning_modules")
}

model PathEnrollment {
  id       String           @id @default(uuid())
  pathId   String
  userId   String
  progress Float            @default(0)
  status   EnrollmentStatus @default(IN_PROGRESS)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  path LearningPath @relation(fields: [pathId], references: [id])
  user User         @relation("UserPathEnrollments", fields: [userId], references: [id])

  @@unique([pathId, userId])
  @@map("path_enrollments")
}

model ModuleCompletion {
  id       String  @id @default(uuid())
  moduleId String
  userId   String
  score    Float?
  feedback String?

  completedAt DateTime @default(now())

  module LearningModule @relation(fields: [moduleId], references: [id])
  user   User           @relation("UserModuleCompletions", fields: [userId], references: [id])

  @@unique([moduleId, userId])
  @@map("module_completions")
}

//==================================
// CERTIFICATION SYSTEM
//==================================

model Certificate {
  id          String    @id @default(uuid())
  name        String
  description String
  issuer      String
  validUntil  DateTime?
  imageUrl    String?

  retreatId String
  retreat   Retreat @relation("CertificateRetreat", fields: [retreatId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("certificates")
}

model Certification {
  id           String              @id @default(uuid())
  name         String
  description  String
  category     CertificateCategory
  validityDays Int?
  requirements Json

  issuances CertificationIssuance[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("certifications")
}

model CertificationIssuance {
  id              String              @id @default(uuid())
  certificationId String
  userId          String
  issueDate       DateTime            @default(now())
  expiryDate      DateTime?
  credential      String              @unique
  status          CertificationStatus @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  certification Certification @relation(fields: [certificationId], references: [id])
  user          User          @relation("UserCertificationIssuances", fields: [userId], references: [id])

  @@map("certification_issuances")
}

//==================================
// CONSULTATION SYSTEM
//==================================

model ConsultationService {
  id          String           @id @default(uuid())
  name        String
  description String
  duration    Int
  type        ConsultationType
  price       Float?

  availability ConsultationSlot[]
  bookings     ConsultationBooking[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("consultation_services")
}

model ConsultationSlot {
  id        String     @id @default(uuid())
  startTime DateTime
  endTime   DateTime
  status    SlotStatus @default(AVAILABLE)

  serviceId String
  service   ConsultationService @relation(fields: [serviceId], references: [id])

  expertId String
  expert   User   @relation("ExpertSlots", fields: [expertId], references: [id])

  booking ConsultationBooking?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("consultation_slots")
}

model ConsultationBooking {
  id     String        @id @default(uuid())
  status BookingStatus @default(PENDING)
  notes  String?

  serviceId String
  slotId    String @unique
  userId    String

  service ConsultationService @relation(fields: [serviceId], references: [id])
  slot    ConsultationSlot    @relation(fields: [slotId], references: [id])
  user    User                @relation("ClientBookings", fields: [userId], references: [id])

  feedback ConsultationFeedback?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("consultation_bookings")
}

model ConsultationFeedback {
  id        String  @id @default(uuid())
  rating    Int
  comment   String?
  anonymous Boolean @default(false)

  bookingId String              @unique
  booking   ConsultationBooking @relation(fields: [bookingId], references: [id])

  createdAt DateTime @default(now())

  @@map("consultation_feedback")
}

//==================================
// WELLNESS & MEDITATION
//==================================

model MeditationSession {
  id          String         @id @default(uuid())
  title       String
  description String
  duration    Int
  type        MeditationType
  audioUrl    String
  level       SkillLevel
  tags        String[]

  practices MeditationPractice[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("meditation_sessions")
}

model MeditationPractice {
  id       String  @id @default(uuid())
  duration Int
  rating   Int?
  notes    String?

  sessionId String
  session   MeditationSession @relation(fields: [sessionId], references: [id])

  userId String
  user   User   @relation("UserMeditationPractices", fields: [userId], references: [id])

  startedAt   DateTime  @default(now())
  completedAt DateTime?

  @@map("meditation_practices")
}

model WellnessTracker {
  id     String @id @default(uuid())
  userId String @unique

  user User @relation("UserWellnessTracker", fields: [userId], references: [id])

  metrics WellnessMetrics[]
  goals   WellnessGoal[]

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("wellness_trackers")
}

model WellnessMetrics {
  id    String     @id @default(uuid())
  type  MetricType
  value Float
  notes String?
  date  DateTime

  trackerId String
  tracker   WellnessTracker @relation(fields: [trackerId], references: [id])

  createdAt DateTime @default(now())

  @@map("wellness_metrics")
}

model WellnessGoal {
  id       String     @id @default(uuid())
  type     MetricType
  target   Float
  deadline DateTime
  status   GoalStatus @default(IN_PROGRESS)

  trackerId String
  tracker   WellnessTracker @relation(fields: [trackerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("wellness_goals")
}

model Journal {
  id        String   @id @default(uuid())
  userId    String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user    User           @relation("UserJournals", fields: [userId], references: [id])
  entries JournalEntry[]

  @@map("journals")
}

model JournalEntry {
  id         String       @id @default(uuid())
  journalId  String
  title      String?
  content    String
  mood       MoodType
  tags       String[]
  visibility PrivacyLevel @default(PRIVATE)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  journal Journal @relation(fields: [journalId], references: [id])

  @@map("journal_entries")
}

//==================================
// PARTNER SYSTEM
//==================================

model Partner {
  id                 String              @id @default(uuid())
  userId             String              @unique
  companyName        String
  type               PartnerType
  category           PartnerCategory
  description        String
  status             PartnerStatus       @default(PENDING)
  logo               String?
  website            String?
  certificationLevel CertificationLevel?
  certificationDate  DateTime?
  lastAuditDate      DateTime?
  qualityScore       Float?
  responseRate       Float?
  completedServices  Int                 @default(0)
  specializations    String[]
  languages          String[]
  coverageAreas      Json?
  insurance          Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user User @relation("UserPartner", fields: [userId], references: [id])

  retreats              Retreat[]                      @relation("PartnerRetreats")
  certificationCriteria PartnerCertificationCriteria[]
  badges                PartnerBadge[]
  verifications         PartnerVerification[]
  services              PartnerService[]
  reviews               PartnerReview[]                @relation("PartnerReviews")
  documents             PartnerDocument[]
  trainings             PartnerTraining[]
  supports              PartnerSupport[]
  matchingAnalytics     MatchingAnalytics[]           @relation("PartnerMatchingAnalytics")

  @@map("partners")
}

model PartnerBadge {
  id        String      @id @default(uuid())
  type      BadgeType
  issuedAt  DateTime    @default(now())
  expiresAt DateTime?
  status    BadgeStatus @default(ACTIVE)

  partnerId String
  partner   Partner @relation(fields: [partnerId], references: [id])

  @@map("partner_badges")
}

model PartnerVerification {
  id         String             @id @default(uuid())
  type       VerificationType
  status     VerificationStatus @default(PENDING)
  details    Json
  verifiedBy String?
  verifiedAt DateTime?

  partnerId String
  partner   Partner @relation(fields: [partnerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("partner_verifications")
}

model PartnerService {
  id           String        @id @default(uuid())
  name         String
  description  String
  price        Float?
  duration     Int?
  capacity     Int?
  availability Json?
  status       ServiceStatus @default(ACTIVE)

  partnerId String
  partner   Partner @relation(fields: [partnerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("partner_services")
}

model PartnerReview {
  id       String       @id @default(uuid())
  rating   Int
  comment  String?
  response String?
  status   ReviewStatus @default(PENDING)

  partnerId String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  partner Partner @relation("PartnerReviews", fields: [partnerId], references: [id])
  user    User    @relation("UserPartnerReviews", fields: [userId], references: [id])

  @@map("partner_reviews")
}

model PartnerDocument {
  id         String          @id @default(uuid())
  type       KYCDocumentType
  url        String
  status     DocumentStatus  @default(PENDING)
  validUntil DateTime?

  partnerId String
  partner   Partner @relation(fields: [partnerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("partner_documents")
}

model PartnerTraining {
  id       String         @id @default(uuid())
  title    String
  type     TrainingType
  status   TrainingStatus
  required Boolean        @default(false)

  partnerId String
  partner   Partner @relation(fields: [partnerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("partner_trainings")
}

model PartnerSupport {
  id          String          @id @default(uuid())
  subject     String
  description String
  type        SupportType
  priority    SupportPriority
  status      SupportStatus

  partnerId String
  partner   Partner @relation(fields: [partnerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("partner_supports")
}

model PartnerCertificationCriteria {
  id         String         @id @default(uuid())
  partnerId  String
  type       PartnerType
  criteria   Json
  status     CriteriaStatus
  validUntil DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  partner Partner @relation(fields: [partnerId], references: [id])

  @@map("partner_certification_criteria")
}

//==================================
// REWARD POINTS
//==================================

model RewardPoint {
  id          String    @id @default(uuid())
  amount      Int
  type        String
  description String
  expiresAt   DateTime?

  userId String
  user   User   @relation("UserRewardPoints", fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("reward_points")
}

model RewardTier {
  id        String   @id @default(uuid())
  name      String
  threshold Int
  benefits  String[]
  icon      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("reward_tiers")
}

//==================================
// REFERRAL SYSTEM
//==================================

model ReferralProgram {
  id            String    @id @default(uuid())
  name          String
  description   String
  rewardAmount  Float
  referrerBonus Float
  referredBonus Float
  isActive      Boolean   @default(true)
  maxReferrals  Int?
  validUntil    DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("referral_programs")
}

model ReferralCode {
  id         String    @id @default(uuid())
  code       String    @unique
  usageCount Int       @default(0)
  maxUses    Int?
  expiresAt  DateTime?

  userId String
  user   User   @relation("UserReferralCodes", fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("referral_codes")
}

model Referral {
  id        String   @id @default(uuid())
  code      String   @unique
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  creatorId  String?
  userId     String?
  referredId String?

  creator  User? @relation("ReferralCreator", fields: [creatorId], references: [id])
  user     User? @relation("UserReferrals", fields: [userId], references: [id])
  referred User? @relation("ReferredUser", fields: [referredId], references: [id])

  @@index([creatorId])
  @@index([userId])
  @@index([referredId])
  @@map("referrals")
}

//==================================
// MEDIA, ATTACHMENTS, POSTS
//==================================

model Media {
  id        String   @id @default(uuid())
  url       String
  type      String
  filename  String
  mimeType  String
  size      Int
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@map("media")
}

model Attachment {
  id        String   @id @default(uuid())
  url       String
  filename  String
  mimeType  String
  size      Int
  postId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  post Post @relation("PostAttachments", fields: [postId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@map("attachments")
}

model Post {
  id         String   @id @default(uuid())
  content    String   @db.Text
  title      String?
  type       String   @default("regular")
  status     String   @default("published")
  visibility String   @default("public")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())

  userId String
  user   User   @relation("UserPosts", fields: [userId], references: [id], onDelete: Cascade)

  groupId String?
  group   Group?  @relation("GroupPosts", fields: [groupId], references: [id], onDelete: SetNull)

  comments    Comment[]    @relation("PostComments")
  likes       Like[]       @relation("PostLikes")
  reactions   Reaction[]   @relation("PostReactions")
  tags        PostTag[]    @relation("PostTags")
  attachments Attachment[] @relation("PostAttachments")

  metadata Json? @default("{}")

  @@index([userId])
  @@index([groupId])
  @@index([createdAt])
  @@index([status])
  @@map("posts")
}

model PostTag {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  postId String
  post   Post   @relation("PostTags", fields: [postId], references: [id], onDelete: Cascade)

  tagId String
  tag   Tag    @relation("PostTagsPivot", fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([postId, tagId])
  @@index([postId])
  @@index([tagId])
  @@map("post_tags")
}

//==================================
// FEEDBACK
//==================================

model FeedbackSession {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  retreatId String?

  retreat   Retreat?           @relation("RetreatFeedbackSession", fields: [retreatId], references: [id])
  responses FeedbackResponse[]

  @@map("feedback_sessions")
}

model FeedbackResponse {
  id        String   @id @default(uuid())
  sessionId String
  userId    String
  content   String
  createdAt DateTime @default(now())

  session FeedbackSession @relation(fields: [sessionId], references: [id])
  user    User            @relation("UserFeedbackResponses", fields: [userId], references: [id])

  @@map("feedback_responses")
}

//==================================
// VECTOR DOCUMENT
//==================================

model VectorDocument {
  id        String   @id @default(uuid())
  authorId  String
  userId    String
  title     String
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  author User @relation("UserVectorDocumentsAuthor", fields: [authorId], references: [id])
  user   User @relation("UserVectorDocumentsOwner", fields: [userId], references: [id])

  metadata Json? @default("{}")
}

//==================================
// RESOURCE
//==================================

model Resource {
  id          String   @id @default(uuid())
  title       String
  description String?  @db.Text
  type        String
  url         String?
  fileUrl     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  downloads ResourceDownload[] @relation("ResourceDownloads")
  ratings   ResourceRating[]   @relation("ResourceRatings")
  retreats  Retreat[]          @relation("RetreatResources")

  metadata Json?  @default("{}")
  status   String @default("active")

  @@map("resources")
}

// Security Models

model SecurityAlert {
  id         String    @id @default(uuid())
  type       String
  severity   String    @default("LOW")
  message    String
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  resolved   Boolean   @default(false)
  resolvedAt DateTime?
  metadata   Json?     @default("{}")
  userId     String?

  // Relation avec User (optionnelle)
  user User? @relation(fields: [userId], references: [id])

  // Relation avec les incidents
  incidents IncidentAlertRelation[]

  @@index([type])
  @@index([severity])
  @@index([resolved])
  @@index([userId])
  @@map("security_alerts")
}

model AuditEvent {
  id        String   @id @default(uuid())
  type      String
  action    String // New action field added
  message   String
  metadata  Json?    @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String?

  user User? @relation(fields: [userId], references: [id])

  @@map("audit_events")
}

model SecurityLog {
  id         String   @id @default(uuid())
  timestamp  DateTime @default(now())
  userId     String
  action     String
  eventType  String
  status     String   @default("SUCCESS")
  resourceId String?
  ipAddress  String?
  userAgent  String?
  details    Json?
  metadata   Json?
  severity   String   @default("INFO")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id])

  @@index([eventType])
  @@index([userId])
  @@index([timestamp])
  @@index([status])
  @@index([severity])
  @@map("security_logs")
}

model SecuritySession {
  id           String   @id @default(uuid())
  userId       String
  token        String   @unique
  ipAddress    String?
  userAgent    String?
  lastActivity DateTime @default(now())
  expiresAt    DateTime
  isRevoked    Boolean  @default(false)
  user         User     @relation(fields: [userId], references: [id])

  @@map("security_sessions")
}

model SecuritySettings {
  id                  String    @id @default(uuid())
  userId              String    @unique
  twoFactorEnabled    Boolean   @default(false)
  loginAlerts         Boolean   @default(true)
  suspiciousActivity  Boolean   @default(true)
  passwordChangedAt   DateTime?
  lastLoginAt         DateTime?
  failedLoginAttempts Int       @default(0)
  accountLocked       Boolean   @default(false)
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  user User @relation(fields: [userId], references: [id], map: "security_settings_user_fk", name: "UserSecuritySettings")

  @@map("security_settings")
}

model TwoFactorAuthSettings {
  id          String   @id @default(uuid())
  userId      String   @unique
  secret      String
  backupCodes String[]
  enabled     Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], map: "two_factor_auth_settings_user_fk", name: "UserTwoFactorAuthSettings")

  @@map("two_factor_auth_settings")
}

model SecurityPolicy {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  rules       Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("security_policies")
}

model SecurityAudit {
  id        String   @id @default(uuid())
  type      String
  severity  String
  message   String
  metadata  Json?
  timestamp DateTime @default(now())

  @@map("security_audits")
}

model SecurityIncident {
  id                String               @id @default(uuid())
  title             String
  description       String
  status            IncidentStatus       @default(OPEN)
  severity          IncidentSeverity     @default(MEDIUM)
  type              String
  source            String
  detectedAt        DateTime             @default(now())
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  resolvedAt        DateTime?
  assignedToId      String?
  reportedById      String?
  affectedAssets    String[]
  tags              String[]
  metadata          Json?                @default("{}")

  // Relations
  assignedTo        User?                @relation("AssignedIncidents", fields: [assignedToId], references: [id])
  reportedBy        User?                @relation("ReportedIncidents", fields: [reportedById], references: [id])
  activities        IncidentActivity[]
  relatedAlerts     IncidentAlertRelation[]
  relatedEvents     IncidentEventRelation[]

  @@index([status])
  @@index([severity])
  @@index([type])
  @@index([source])
  @@index([detectedAt])
  @@index([assignedToId])
  @@map("security_incidents")
}

model IncidentActivity {
  id          String   @id @default(uuid())
  incidentId  String
  userId      String?
  action      String
  details     String?
  timestamp   DateTime @default(now())
  metadata    Json?    @default("{}")

  // Relations
  incident    SecurityIncident @relation(fields: [incidentId], references: [id], onDelete: Cascade)
  user        User?            @relation(fields: [userId], references: [id])

  @@index([incidentId])
  @@index([userId])
  @@index([timestamp])
  @@map("incident_activities")
}

model IncidentAlertRelation {
  id          String   @id @default(uuid())
  incidentId  String
  alertId     String
  createdAt   DateTime @default(now())

  // Relations
  incident    SecurityIncident @relation(fields: [incidentId], references: [id], onDelete: Cascade)
  alert       SecurityAlert    @relation(fields: [alertId], references: [id], onDelete: Cascade)

  @@unique([incidentId, alertId])
  @@map("incident_alert_relations")
}

model IncidentEventRelation {
  id          String   @id @default(uuid())
  incidentId  String
  eventId     String
  createdAt   DateTime @default(now())

  // Relations
  incident    SecurityIncident @relation(fields: [incidentId], references: [id], onDelete: Cascade)
  // Note: Assuming SecurityEvent model exists
  // event       SecurityEvent    @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@unique([incidentId, eventId])
  @@map("incident_event_relations")
}

enum IncidentStatus {
  OPEN
  INVESTIGATING
  CONTAINED
  REMEDIATED
  RESOLVED
  CLOSED
}

enum IncidentSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

model Project {
  id             String          @id @default(uuid())
  name           String
  description    String?
  startDate      DateTime
  endDate        DateTime?
  status         ProjectStatus   @default(ACTIVE)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  collaborations Collaboration[]

  @@map("projects")
}

model Collaboration {
  id         String              @id @default(uuid())
  senderId   String
  receiverId String
  projectId  String
  status     CollaborationStatus @default(PENDING)
  createdAt  DateTime            @default(now())
  updatedAt  DateTime            @updatedAt

  sender   User    @relation("SentCollaborations", fields: [senderId], references: [id])
  receiver User    @relation("ReceivedCollaborations", fields: [receiverId], references: [id])
  project  Project @relation(fields: [projectId], references: [id])

  @@index([senderId])
  @@index([receiverId])
  @@index([projectId])
  @@map("collaborations") // Ajoutez cette ligne
}

model AIAgent {
  id            String           @id @default(uuid())
  name          String?
  type          AIAgentType
  capabilities  String[]
  configuration Json
  messages      AIMessage[]      @relation("AIAgentMessages")
  conversations AIConversation[]
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  chatMessages  Message[]        @relation("AIAgentMessages")

  @@index([type])
  @@index([createdAt])
}

model AIMessage {
  id             String         @id @default(uuid())
  role           String         @default("USER")
  content        String         @db.Text
  metadata       Json?
  conversationId String
  conversation   AIConversation @relation(fields: [conversationId], references: [id])
  createdAt      DateTime       @default(now())
  agent          AIAgent        @relation("AIAgentMessages", fields: [agentId], references: [id])
  agentId        String

  @@index([conversationId])
  @@index([createdAt])
}

model AIConversation {
  id        String      @id @default(uuid())
  userId    String
  user      User        @relation(fields: [userId], references: [id])
  agentId   String
  agent     AIAgent     @relation(fields: [agentId], references: [id])
  context   Json?
  messages  AIMessage[]
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  @@index([agentId])
  @@index([userId])
  @@index([createdAt])
}

//==================================
// CONTINUOUS LEARNING SYSTEM
//==================================

model UserModel {
  id        String   @id @default(uuid())
  userId    String   @unique
  context   Json     @default("{}")
  metrics   Json     @default("{}")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  user              User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  preferences       UserPreference[]
  categoryInterests CategoryInterest[]
  tagInterests      TagInterest[]

  @@index([userId])
  @@index([updatedAt])
  @@map("user_models")
}

model UserPreference {
  id          String   @id @default(uuid())
  userId      String
  userModelId String
  key         String
  value       Float
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userModel UserModel @relation(fields: [userModelId], references: [id], onDelete: Cascade)

  @@unique([userId, key])
  @@index([userId])
  @@index([userModelId])
  @@map("user_preferences")
}

model CategoryInterest {
  id            String   @id @default(uuid())
  userId        String
  userModelId   String
  category      String
  interestLevel Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userModel UserModel @relation(fields: [userModelId], references: [id], onDelete: Cascade)

  @@unique([userId, category])
  @@index([userId])
  @@index([userModelId])
  @@index([category])
  @@map("category_interests")
}

model TagInterest {
  id            String   @id @default(uuid())
  userId        String
  userModelId   String
  tag           String
  interestLevel Float
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  userModel UserModel @relation(fields: [userModelId], references: [id], onDelete: Cascade)

  @@unique([userId, tag])
  @@index([userId])
  @@index([userModelId])
  @@index([tag])
  @@map("tag_interests")
}

model LearningEvent {
  id              String   @id @default(uuid())
  userId          String
  eventType       String
  data            Json
  estimatedImpact Float    @default(0.5)
  timestamp       DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([eventType])
  @@index([timestamp])
  @@map("learning_events")
}

//==================================
// EXPLANATION SYSTEM
//==================================

model ExplanationHistory {
  id              String   @id @default(uuid())
  recommendationId String
  userId          String
  itemId          String
  itemType        String
  strategy        String
  score           Float
  factors         Json
  summary         String
  metadata        Json     @default("{}")
  createdAt       DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([recommendationId])
  @@index([itemType])
  @@index([strategy])
  @@index([createdAt])
  @@map("explanation_history")
}

model ExplanationEvent {
  id              String   @id @default(uuid())
  userId          String
  recommendationId String
  eventType       String
  data            Json     @default("{}")
  createdAt       DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([recommendationId])
  @@index([eventType])
  @@index([createdAt])
  @@map("explanation_events")
}

model ExplanationTemplate {
  id              String   @id @default(uuid())
  name            String
  description     String
  factorType      String
  template        String
  variables       String[] @default([])
  language        String   @default("fr")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now())

  @@unique([name, language])
  @@index([factorType])
  @@index([language])
  @@map("explanation_templates")
}

model ExplanationPreferences {
  id                     String   @id @default(uuid())
  userId                 String   @unique
  preferredStyle         String   @default("CONVERSATIONAL")
  detailLevel            String   @default("MODERATE")
  preferredFormat        String[] @default(["MIXED"])
  highlightedFactors     String[] @default([])
  hiddenFactors          String[] @default([])
  language               String   @default("fr")
  culturalPreferences    Json     @default("{}")
  accessibilityPreferences Json   @default("{}")
  metadata               Json?
  createdAt              DateTime @default(now())
  updatedAt              DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([language])
  @@map("explanation_preferences")
}

// Modèles pour l'apprentissage par renforcement

model RLAgent {
  id          String        @id @default(uuid())
  name        String
  description String
  agentType   String
  state       String
  config      RLAgentConfig @relation(fields: [id], references: [agentId], onDelete: Cascade)
  stats       RLAgentStats  @relation(fields: [id], references: [agentId], onDelete: Cascade)
  episodes    RLEpisode[]
  metadata    Json?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@index([agentType])
  @@index([state])
  @@map("rl_agents")
}

model RLAgentConfig {
  agentId                     String   @id
  learningRate                Float
  discountFactor              Float
  explorationRate             Float
  explorationStrategy         String
  experienceMemorySize        Int
  batchSize                   Int
  targetNetworkUpdateFrequency Int?
  agentSpecificParams         String?
  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt

  agent RLAgent @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("rl_agent_configs")
}

model RLAgentStats {
  agentId                 String   @id
  totalEpisodes           Int      @default(0)
  totalSteps              Int      @default(0)
  cumulativeReward        Float    @default(0)
  averageRewardPerEpisode Float    @default(0)
  convergenceRate         Float    @default(0)
  currentExplorationRate  Float
  rewardHistory           String   @default("[]")
  errorHistory            String   @default("[]")
  agentSpecificMetrics    String?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  agent RLAgent @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("rl_agent_stats")
}

model RLEpisode {
  id          String      @id @default(uuid())
  agentId     String
  userId      String
  totalReward Float
  steps       Int
  startTime   DateTime
  endTime     DateTime
  metadata    Json?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  experiences RLExperience[]

  agent RLAgent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  user  User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([agentId])
  @@index([userId])
  @@map("rl_episodes")
}

model RLExperience {
  id          String   @id @default(uuid())
  episodeId   String
  state       Json
  action      Json
  reward      Float
  nextState   Json
  done        Boolean
  timestamp   DateTime
  createdAt   DateTime @default(now())

  episode RLEpisode @relation(fields: [episodeId], references: [id], onDelete: Cascade)

  @@index([episodeId])
  @@map("rl_experiences")
}

//==================================
// EXPLANATION A/B TESTING SYSTEM
//==================================

enum ExplanationVariantType {
  DETAILED
  SIMPLE
  VISUAL
  TECHNICAL
  PERSONALIZED
}

model ExplanationABTest {
  id          String   @id @default(uuid())
  name        String
  description String
  startDate   DateTime
  endDate     DateTime
  status      String   @default("ACTIVE")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())

  variants     ExplanationVariant[]
  assignments  ExplanationABTestAssignment[]
  interactions ExplanationABTestInteraction[]
  metrics      ExplanationABTestMetrics[]

  @@index([status])
  @@index([startDate, endDate])
  @@map("explanation_ab_tests")
}

model ExplanationVariant {
  id            String                 @id @default(uuid())
  testId        String
  name          String
  description   String
  type          ExplanationVariantType
  configuration Json
  allocation    Float                  @default(0.5)
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @default(now())

  test         ExplanationABTest              @relation(fields: [testId], references: [id], onDelete: Cascade)
  assignments  ExplanationABTestAssignment[]
  interactions ExplanationABTestInteraction[]

  @@index([testId])
  @@index([type])
  @@map("explanation_variants")
}

model ExplanationABTestAssignment {
  id        String   @id @default(uuid())
  userId    String
  testId    String
  variantId String
  assignedAt DateTime @default(now())

  user    User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  test    ExplanationABTest  @relation(fields: [testId], references: [id], onDelete: Cascade)
  variant ExplanationVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([userId, testId])
  @@index([userId])
  @@index([testId])
  @@index([variantId])
  @@map("explanation_ab_test_assignments")
}

model ExplanationABTestInteraction {
  id              String   @id @default(uuid())
  userId          String
  testId          String
  variantId       String
  recommendationId String
  interactionType String
  data            Json     @default("{}")
  timestamp       DateTime @default(now())

  user    User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  test    ExplanationABTest  @relation(fields: [testId], references: [id], onDelete: Cascade)
  variant ExplanationVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([testId])
  @@index([variantId])
  @@index([recommendationId])
  @@index([interactionType])
  @@index([timestamp])
  @@map("explanation_ab_test_interactions")
}

model ExplanationABTestMetrics {
  id        String   @id @default(uuid())
  testId    String
  timestamp DateTime @default(now())
  metrics   Json

  test ExplanationABTest @relation(fields: [testId], references: [id], onDelete: Cascade)

  @@index([testId])
  @@index([timestamp])
  @@map("explanation_ab_test_metrics")
}

model GasPrice {
  id        String   @id @default(uuid())
  slow      Float
  standard  Float
  fast      Float
  timestamp DateTime @default(now())

  @@index([timestamp])
}

// KYC Verification

enum KYCStatus {
  PENDING
  APPROVED
  REJECTED
}

model KYCDocument {
  id             String          @id @default(uuid())
  type           KYCDocumentType
  number         String
  expiryDate     DateTime
  issuingCountry String
  imageUrl       String
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  verification   KYCVerification @relation(fields: [verificationId], references: [id])
  verificationId String
}

model KYCVerification {
  id                String    @id @default(uuid())
  userId            String
  status            KYCStatus @default(PENDING)
  verificationLevel Int       @default(1)
  submittedAt       DateTime  @default(now())
  processedAt       DateTime?
  metadata          Json?

  documents KYCDocument[]
  user      User          @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

//==================================
// VIDEO
//==================================

enum VideoStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum VideoQuality {
  SD
  HD
  FHD
  UHD
}

model Video {
  id          String       @id @default(uuid())
  title       String
  description String?
  url         String // URL du fichier vidéo
  duration    Float // Durée en secondes
  size        BigInt // Taille en octets
  format      String // Format du fichier (mp4, etc.)
  quality     VideoQuality
  status      VideoStatus  @default(PENDING)
  metadata    Json? // Métadonnées additionnelles
  userId      String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  thumbnail   String?

  user         User               @relation("UserVideos", fields: [userId], references: [id])
  thumbnails   VideoThumbnail[]
  transcodings VideoTranscoding[]

  @@index([userId])
}

model VideoThumbnail {
  id        String   @id @default(uuid())
  url       String
  timestamp Int // Position dans la vidéo en secondes
  width     Int
  height    Int
  videoId   String
  createdAt DateTime @default(now())

  video Video @relation(fields: [videoId], references: [id])

  @@index([videoId])
}

model VideoTranscoding {
  id        String       @id @default(uuid())
  quality   VideoQuality
  status    VideoStatus  @default(PENDING)
  url       String?
  progress  Float        @default(0)
  metadata  Json? // Ajout du champ metadata
  videoId   String
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  video Video @relation(fields: [videoId], references: [id])

  @@index([videoId])
}

//==================================
// WELLNESS PACKAGE
//==================================

model WellnessPackage {
  id              String   @id @default(uuid())
  name            String
  description     String?
  duration        Int // en minutes
  maxParticipants Int
  basePrice       Float
  isCustom        Boolean  @default(false)
  preferences     Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  sessions WellnessSession[]
  bookings WellnessBooking[] @relation("PackageBookings")
}

model WellnessSession {
  id                  String   @id @default(uuid())
  type                String
  duration            Int // en minutes
  preferredTime       String?
  specialRequirements String[]
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())

  // Relations
  packageId String
  package   WellnessPackage @relation(fields: [packageId], references: [id])

  @@index([packageId])
}

model WellnessBooking {
  id        String        @id @default(uuid())
  userId    String
  packageId String
  status    BookingStatus @default(PENDING)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  user    User            @relation("UserWellnessBookings", fields: [userId], references: [id])
  package WellnessPackage @relation("PackageBookings", fields: [packageId], references: [id])

  @@index([userId])
  @@index([packageId])
}

//==================================
// MODERATION
//==================================

model ModerationLog {
  id          String   @id @default(uuid())
  userId      String
  content     String?
  contentType String
  isApproved  Boolean
  violations  String[]
  score       Json
  timestamp   DateTime @default(now())

  // Relations
  user User @relation("UserModerationLogs", fields: [userId], references: [id])

  @@index([userId])
  @@index([contentType])
  @@index([timestamp])
  @@map("moderation_logs")
}

//==================================
// RECOMMENDATION FEEDBACK
//==================================

model RecommendationFeedback {
  id                 String   @id @default(uuid())
  userId             String
  recommendationId   String
  recommendationType String
  feedbackType       String
  comment            String?
  rating             Int?
  metadata           Json?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  user User @relation("UserRecommendationFeedback", fields: [userId], references: [id])

  @@index([userId])
  @@index([recommendationId])
  @@index([recommendationType])
  @@index([feedbackType])
  @@index([createdAt])
  @@map("recommendation_feedback")
}

model Image {
  id          String   @id @default(uuid())
  url         String
  title       String?
  description String?
  width       Int?
  height      Int?
  format      String?
  size        Int?
  altText     String?
  metadata    Json?
  userId      String
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User @relation("UserImages", fields: [userId], references: [id])
  createdBy User @relation("ImageCreatedBy", fields: [createdById], references: [id])

  @@index([userId])
  @@index([createdById])
  @@map("images")
}

//==================================
// MATCHING ANALYTICS
//==================================

model MatchingAnalytics {
  id             String   @id @default(uuid())
  eventType      String
  partnerId      String?
  retreatId      String?
  userId         String?
  score          Float?
  criteriaUsed   String?
  executionTimeMs Int?
  resultCount    Int?
  timestamp      DateTime @default(now())
  metadata       String?

  // Relations
  partner Partner? @relation("PartnerMatchingAnalytics", fields: [partnerId], references: [id], map: "matching_analytics_partner_fkey")
  retreat Retreat? @relation("RetreatMatchingAnalytics", fields: [retreatId], references: [id], map: "matching_analytics_retreat_fkey")
  user    User?    @relation("UserMatchingAnalytics", fields: [userId], references: [id])

  @@index([partnerId])
  @@index([retreatId])
  @@index([userId])
  @@index([eventType])
  @@index([timestamp])
  @@map("matching_analytics")
}

model ExportHistory {
  id          String   @id @default(uuid())
  userId      String
  entityType  String   // PARTNER, RETREAT, etc.
  entityId    String
  format      String   // csv, excel, json
  filePath    String
  recordCount Int
  createdAt   DateTime @default(now())

  // Relations
  user User @relation("UserExportHistory", fields: [userId], references: [id])

  @@index([userId])
  @@index([entityType])
  @@index([entityId])
  @@index([createdAt])
  @@map("export_history")
}

//==================================
// SOCIAL PLATFORM MODELS
//==================================

// Enums for Social Platform
enum LivestreamStatus {
  SCHEDULED
  LIVE
  ENDED
  CANCELLED
}

enum BlogPostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum CommentType {
  TEXT
  QUESTION
  REACTION
}

enum SocialContentType {
  LIVESTREAM
  BLOG
  VIDEO
}

// Livestream Model
model Livestream {
  id                String           @id @default(uuid())
  title             String
  description       String           @db.Text
  hostId            String
  status            LivestreamStatus @default(SCHEDULED)
  startTime         DateTime
  endTime           DateTime?
  thumbnailUrl      String?
  streamUrl         String?
  recordingUrl      String?
  isPrivate         Boolean          @default(false)
  viewerCount       Int              @default(0)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  // Relations
  host              User             @relation("UserHostedLivestreams", fields: [hostId], references: [id])
  messages          LivestreamMessage[] @relation("LivestreamMessages")
  participants      LivestreamParticipant[] @relation("LivestreamParticipants")
  analytics         SocialAnalytics? @relation("LivestreamAnalytics")

  // Metadata
  metadata          Json?            @default("{}")

  @@index([hostId])
  @@index([status])
  @@index([startTime])
  @@map("livestreams")
}

// Livestream Message Model
model LivestreamMessage {
  id                String      @id @default(uuid())
  content           String
  type              CommentType @default(TEXT)
  userId            String
  livestreamId      String
  createdAt         DateTime    @default(now())

  // Relations
  user              User        @relation("UserLivestreamMessages", fields: [userId], references: [id])
  livestream        Livestream  @relation("LivestreamMessages", fields: [livestreamId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([livestreamId])
  @@index([createdAt])
  @@map("livestream_messages")
}

// Livestream Participant Model
model LivestreamParticipant {
  id                String      @id @default(uuid())
  userId            String
  livestreamId      String
  joinedAt          DateTime    @default(now())
  leftAt            DateTime?
  isActive          Boolean     @default(true)

  // Relations
  user              User        @relation("UserLivestreamParticipations", fields: [userId], references: [id])
  livestream        Livestream  @relation("LivestreamParticipants", fields: [livestreamId], references: [id], onDelete: Cascade)

  @@unique([userId, livestreamId])
  @@index([userId])
  @@index([livestreamId])
  @@map("livestream_participants")
}

// Blog Post Model
model BlogPost {
  id                String          @id @default(uuid())
  title             String
  content           String          @db.Text
  authorId          String
  authorName        String
  status            BlogPostStatus  @default(DRAFT)
  publishDate       DateTime?
  imageUrl          String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  author            User            @relation("UserBlogPosts", fields: [authorId], references: [id])
  tags              BlogPostTag[]   @relation("BlogPostTags")
  analytics         SocialAnalytics? @relation("BlogPostAnalytics")

  // Metadata
  metadata          Json?           @default("{}")

  @@index([authorId])
  @@index([status])
  @@index([publishDate])
  @@map("blog_posts")
}



// Blog Post Tag Model
model BlogPostTag {
  id                String      @id @default(uuid())
  name              String
  blogPostId        String
  createdAt         DateTime    @default(now())

  // Relations
  blogPost          BlogPost    @relation("BlogPostTags", fields: [blogPostId], references: [id], onDelete: Cascade)

  @@index([blogPostId])
  @@index([name])
  @@map("blog_post_tags")
}

// Social Analytics Model
model SocialAnalytics {
  id                String            @id @default(uuid())
  entityId          String            @unique
  entityType        SocialContentType
  views             Int               @default(0)
  likes             Int               @default(0)
  comments          Int               @default(0)
  shares            Int               @default(0)
  engagementRate    Float             @default(0)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  livestream        Livestream?       @relation("LivestreamAnalytics", fields: [entityId], references: [id], onDelete: Cascade, map: "social_analytics_livestream_fkey")
  blogPost          BlogPost?         @relation("BlogPostAnalytics", fields: [entityId], references: [id], onDelete: Cascade, map: "social_analytics_blogpost_fkey")

  // Metadata
  metadata          Json?             @default("{}")

  @@index([entityType])
  @@index([views])
  @@index([engagementRate])
  @@map("social_analytics")
}

// Social Event Model (for tracking analytics events)
model SocialEvent {
  id                String            @id @default(uuid())
  eventType         String
  entityId          String
  entityType        SocialContentType
  userId            String?
  timestamp         DateTime          @default(now())

  // Relations
  user              User?             @relation("UserSocialEvents", fields: [userId], references: [id])

  // Metadata
  metadata          Json?             @default("{}")

  @@index([eventType])
  @@index([entityId])
  @@index([entityType])
  @@index([userId])
  @@index([timestamp])
  @@map("social_events")
}

// Modèle pour les alertes du système de monitoring
model Alert {
  id        String   @id @default(uuid())
  level     String   // info, warning, error, critical
  source    String   // recommendation, auth, etc.
  title     String
  message   String
  metadata  Json?    // Métadonnées additionnelles
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([level])
  @@index([source])
  @@index([createdAt])
  @@map("alerts")
}
