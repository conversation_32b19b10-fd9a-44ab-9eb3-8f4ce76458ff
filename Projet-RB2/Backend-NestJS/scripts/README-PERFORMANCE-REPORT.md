# Script de Rapport de Performance du Système de Recommandation

Ce script génère des rapports détaillés sur les performances du système de recommandation de Retreat And Be. Il collecte des métriques de performance, les analyse et génère un rapport complet avec des visualisations et des recommandations d'optimisation.

## Fonctionnalités

- Collecte de métriques système (CPU, mémoire, etc.)
- Collecte de métriques API (temps de réponse, taux d'erreur, etc.)
- Collecte de métriques de base de données (temps de requête, utilisation du pool de connexions, etc.)
- Collecte de métriques de cache (taux de succès, taille, etc.)
- Collecte de métriques spécifiques au système de recommandation (précision, diversité, etc.)
- Tests de performance sur les endpoints de recommandation
- Analyse des métriques et génération de recommandations
- Génération de rapports dans différents formats (JSON, PDF, HTML)
- Affichage d'un résumé dans la console

## Prérequis

- Node.js 14+
- npm ou yarn
- Accès à l'API du système de recommandation

## Installation

Les dépendances nécessaires sont déjà installées dans le projet. Si vous avez besoin de les réinstaller, exécutez :

```bash
npm install commander chalk ora cli-table3 canvas pdfkit
```

## Utilisation

```bash
node scripts/recommendation-performance-report.js [options]
```

### Options

- `--format=json|pdf|html` : Format de sortie (défaut: json)
- `--output=<path>` : Chemin du fichier de sortie (défaut: ./reports/recommendation-performance-report-<timestamp>.<format>)
- `--days=<number>` : Nombre de jours d'historique à analyser (défaut: 7)
- `--detailed` : Inclure des informations détaillées
- `--api-url=<url>` : URL de l'API (défaut: http://localhost:3000)
- `--token=<jwt>` : Token JWT pour l'authentification
- `--endpoints=<list>` : Liste d'endpoints à tester, séparés par des virgules
- `--strategies=<list>` : Liste de stratégies à tester, séparées par des virgules
- `--help` : Afficher l'aide

### Exemples

Générer un rapport JSON avec les paramètres par défaut :

```bash
node scripts/recommendation-performance-report.js
```

Générer un rapport PDF détaillé pour les 30 derniers jours :

```bash
node scripts/recommendation-performance-report.js --format=pdf --days=30 --detailed
```

Générer un rapport HTML avec un token d'authentification :

```bash
node scripts/recommendation-performance-report.js --format=html --token=votre_jwt_token
```

Tester des endpoints spécifiques avec des stratégies spécifiques :

```bash
node scripts/recommendation-performance-report.js --endpoints=/api/v1/recommendations,/api/v1/recommendations/trending --strategies=HYBRID,CONTENT_BASED
```

## Structure du Rapport

Le rapport généré contient les sections suivantes :

1. **Informations générales** : Date, période d'analyse, etc.
2. **Statut global** : Évaluation globale des performances (EXCELLENT, GOOD, AVERAGE, POOR, CRITICAL)
3. **Recommandations** : Liste des recommandations pour améliorer les performances
4. **Métriques système** : CPU, mémoire, etc.
5. **Métriques API** : Temps de réponse, taux d'erreur, etc.
6. **Métriques de base de données** : Temps de requête, utilisation du pool de connexions, etc.
7. **Métriques de cache** : Taux de succès, taille, etc.
8. **Métriques de recommandation** : Précision, diversité, etc.
9. **Résultats des tests de performance** : Temps de réponse, taux de succès, etc.
10. **Statistiques** : Statistiques détaillées sur les tests de performance

## Intégration avec le Système de Monitoring

Ce script peut être exécuté périodiquement (par exemple, via un cron job) pour générer des rapports réguliers sur les performances du système de recommandation. Les rapports peuvent être stockés dans un répertoire dédié et consultés ultérieurement.

## Contribution

Pour contribuer à ce script, veuillez suivre les conventions de code du projet et ajouter des tests pour les nouvelles fonctionnalités.
