#!/bin/bash

# Script pour appliquer les modifications du schéma Prisma

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Application des modifications du schéma Prisma ===${NC}"

# Vérifier si Prisma est installé
if ! command -v npx &> /dev/null; then
  echo -e "${RED}Erreur: npx n'est pas installé. Veuillez installer Node.js et npm.${NC}"
  exit 1
fi

# Générer la migration
echo -e "${YELLOW}Génération de la migration...${NC}"
npx prisma migrate dev --name add_monitoring_alerts

if [ $? -eq 0 ]; then
  echo -e "${GREEN}Migration générée avec succès.${NC}"
else
  echo -e "${RED}Erreur lors de la génération de la migration.${NC}"
  
  # Proposer de forcer la génération du client Prisma
  echo -e "${YELLOW}Voulez-vous forcer la génération du client Prisma sans migration ? (y/n)${NC}"
  read -r response
  
  if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo -e "${YELLOW}Génération du client Prisma...${NC}"
    npx prisma generate
    
    if [ $? -eq 0 ]; then
      echo -e "${GREEN}Client Prisma généré avec succès.${NC}"
    else
      echo -e "${RED}Erreur lors de la génération du client Prisma.${NC}"
      exit 1
    fi
  else
    echo -e "${YELLOW}Opération annulée.${NC}"
    exit 1
  fi
fi

echo -e "${GREEN}=== Modifications du schéma Prisma appliquées avec succès ===${NC}"
