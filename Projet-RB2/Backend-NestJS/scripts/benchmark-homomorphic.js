/**
 * Script de benchmark pour le service HomomorphicEncryptionService
 * 
 * Ce script teste les performances des différentes opérations homomorphiques
 * et compare les résultats avec des opérations normales.
 * 
 * Usage: node benchmark-homomorphic.js
 */

const { performance } = require('perf_hooks');

// Simuler l'environnement NestJS
const configService = {
  get: (key, defaultValue) => {
    const config = {
      'HOMOMORPHIC_ENCRYPTION_ENABLED': true,
      'HOMOMORPHIC_ENCRYPTION_SCHEME': 'CKKS',
      'HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE': 8192,
      'HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL': 128,
    };
    return config[key] !== undefined ? config[key] : defaultValue;
  }
};

// Importer dynamiquement le service
async function importService() {
  try {
    // Importer le service
    const { HomomorphicEncryptionService } = require('../dist/modules/security/services/homomorphic-encryption.service');
    const service = new HomomorphicEncryptionService(configService);
    await service.initialize();
    return service;
  } catch (error) {
    console.error('Erreur lors de l\'importation du service:', error);
    throw error;
  }
}

// Fonction de benchmark
async function runBenchmark(service) {
  console.log('=== Benchmark du service HomomorphicEncryptionService ===');
  
  // Données de test
  const testValues = [
    { name: 'Petits nombres', values: [1, 2, 3, 4, 5] },
    { name: 'Nombres moyens', values: [100, 200, 300, 400, 500] },
    { name: 'Grands nombres', values: [10000, 20000, 30000, 40000, 50000] },
    { name: 'Nombres décimaux', values: [1.5, 2.25, 3.75, 4.5, 5.125] },
  ];
  
  // Tester le chiffrement/déchiffrement
  console.log('\n--- Test de chiffrement/déchiffrement ---');
  for (const { name, values } of testValues) {
    console.log(`\nTest avec ${name}:`);
    
    // Chiffrement individuel
    console.log('Chiffrement individuel:');
    const encryptStart = performance.now();
    const encryptedValues = await Promise.all(values.map(v => service.encrypt(v)));
    const encryptEnd = performance.now();
    const encryptTime = encryptEnd - encryptStart;
    console.log(`  Temps total: ${encryptTime.toFixed(2)} ms`);
    console.log(`  Temps moyen par valeur: ${(encryptTime / values.length).toFixed(2)} ms`);
    
    // Chiffrement par lot
    console.log('Chiffrement par lot:');
    const batchEncryptStart = performance.now();
    const batchEncrypted = await service.encryptBatch(values);
    const batchEncryptEnd = performance.now();
    const batchEncryptTime = batchEncryptEnd - batchEncryptStart;
    console.log(`  Temps total: ${batchEncryptTime.toFixed(2)} ms`);
    console.log(`  Gain de performance: ${(encryptTime / batchEncryptTime).toFixed(2)}x`);
    
    // Déchiffrement individuel
    console.log('Déchiffrement individuel:');
    const decryptStart = performance.now();
    const decryptedValues = await Promise.all(encryptedValues.map(v => service.decrypt(v)));
    const decryptEnd = performance.now();
    const decryptTime = decryptEnd - decryptStart;
    console.log(`  Temps total: ${decryptTime.toFixed(2)} ms`);
    console.log(`  Temps moyen par valeur: ${(decryptTime / values.length).toFixed(2)} ms`);
    
    // Déchiffrement par lot
    console.log('Déchiffrement par lot:');
    const batchDecryptStart = performance.now();
    const batchDecrypted = await service.decryptBatch(batchEncrypted);
    const batchDecryptEnd = performance.now();
    const batchDecryptTime = batchDecryptEnd - batchDecryptStart;
    console.log(`  Temps total: ${batchDecryptTime.toFixed(2)} ms`);
    console.log(`  Gain de performance: ${(decryptTime / batchDecryptTime).toFixed(2)}x`);
    
    // Vérifier les résultats
    console.log('Validation:');
    const individualOk = values.every((v, i) => Math.abs(v - decryptedValues[i]) < 0.001);
    const batchOk = values.every((v, i) => Math.abs(v - batchDecrypted[i]) < 0.001);
    console.log(`  Individuel: ${individualOk ? 'Réussi ✅' : 'Échec ❌'}`);
    console.log(`  Lot: ${batchOk ? 'Réussi ✅' : 'Échec ❌'}`);
  }
  
  // Tester les opérations homomorphiques
  console.log('\n--- Test des opérations homomorphiques ---');
  
  // Préparer les données
  const a = 10, b = 5;
  console.log(`Valeurs de test: a = ${a}, b = ${b}`);
  
  const encryptedA = await service.encrypt(a);
  const encryptedB = await service.encrypt(b);
  
  // Addition
  console.log('\nAddition:');
  const normalAddStart = performance.now();
  const normalAddResult = a + b;
  const normalAddEnd = performance.now();
  console.log(`  Normal: ${normalAddResult} (${(normalAddEnd - normalAddStart).toFixed(2)} ms)`);
  
  const homoAddStart = performance.now();
  const homoAddEncrypted = await service.add(encryptedA, encryptedB);
  const homoAddResult = await service.decrypt(homoAddEncrypted);
  const homoAddEnd = performance.now();
  console.log(`  Homomorphique: ${homoAddResult} (${(homoAddEnd - homoAddStart).toFixed(2)} ms)`);
  console.log(`  Précision: ${Math.abs(normalAddResult - homoAddResult) < 0.001 ? 'Bonne ✅' : 'Mauvaise ❌'}`);
  
  // Soustraction
  console.log('\nSoustraction:');
  const normalSubStart = performance.now();
  const normalSubResult = a - b;
  const normalSubEnd = performance.now();
  console.log(`  Normal: ${normalSubResult} (${(normalSubEnd - normalSubStart).toFixed(2)} ms)`);
  
  const homoSubStart = performance.now();
  const homoSubEncrypted = await service.subtract(encryptedA, encryptedB);
  const homoSubResult = await service.decrypt(homoSubEncrypted);
  const homoSubEnd = performance.now();
  console.log(`  Homomorphique: ${homoSubResult} (${(homoSubEnd - homoSubStart).toFixed(2)} ms)`);
  console.log(`  Précision: ${Math.abs(normalSubResult - homoSubResult) < 0.001 ? 'Bonne ✅' : 'Mauvaise ❌'}`);
  
  // Multiplication
  console.log('\nMultiplication:');
  const normalMulStart = performance.now();
  const normalMulResult = a * b;
  const normalMulEnd = performance.now();
  console.log(`  Normal: ${normalMulResult} (${(normalMulEnd - normalMulStart).toFixed(2)} ms)`);
  
  const homoMulStart = performance.now();
  const homoMulEncrypted = await service.multiply(encryptedA, encryptedB);
  const homoMulResult = await service.decrypt(homoMulEncrypted);
  const homoMulEnd = performance.now();
  console.log(`  Homomorphique: ${homoMulResult} (${(homoMulEnd - homoMulStart).toFixed(2)} ms)`);
  console.log(`  Précision: ${Math.abs(normalMulResult - homoMulResult) < 0.001 ? 'Bonne ✅' : 'Mauvaise ❌'}`);
  
  // Division
  console.log('\nDivision:');
  const normalDivStart = performance.now();
  const normalDivResult = a / b;
  const normalDivEnd = performance.now();
  console.log(`  Normal: ${normalDivResult} (${(normalDivEnd - normalDivStart).toFixed(2)} ms)`);
  
  const homoDivStart = performance.now();
  const homoDivEncrypted = await service.divide(encryptedA, b); // Division par un scalaire
  const homoDivResult = await service.decrypt(homoDivEncrypted);
  const homoDivEnd = performance.now();
  console.log(`  Homomorphique: ${homoDivResult} (${(homoDivEnd - homoDivStart).toFixed(2)} ms)`);
  console.log(`  Précision: ${Math.abs(normalDivResult - homoDivResult) < 0.001 ? 'Bonne ✅' : 'Mauvaise ❌'}`);
  
  // Moyenne
  const testArray = [10, 20, 30, 40, 50];
  console.log(`\nMoyenne de [${testArray.join(', ')}]:`);
  
  const normalAvgStart = performance.now();
  const normalAvgResult = testArray.reduce((a, b) => a + b, 0) / testArray.length;
  const normalAvgEnd = performance.now();
  console.log(`  Normal: ${normalAvgResult} (${(normalAvgEnd - normalAvgStart).toFixed(2)} ms)`);
  
  const encryptedArray = await Promise.all(testArray.map(v => service.encrypt(v)));
  const homoAvgStart = performance.now();
  const homoAvgEncrypted = await service.average(encryptedArray);
  const homoAvgResult = await service.decrypt(homoAvgEncrypted);
  const homoAvgEnd = performance.now();
  console.log(`  Homomorphique: ${homoAvgResult} (${(homoAvgEnd - homoAvgStart).toFixed(2)} ms)`);
  console.log(`  Précision: ${Math.abs(normalAvgResult - homoAvgResult) < 0.001 ? 'Bonne ✅' : 'Mauvaise ❌'}`);
  
  // Variance
  console.log(`\nVariance de [${testArray.join(', ')}]:`);
  
  const normalVarStart = performance.now();
  const mean = testArray.reduce((a, b) => a + b, 0) / testArray.length;
  const normalVarResult = testArray.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / testArray.length;
  const normalVarEnd = performance.now();
  console.log(`  Normal: ${normalVarResult} (${(normalVarEnd - normalVarStart).toFixed(2)} ms)`);
  
  const homoVarStart = performance.now();
  const homoVarEncrypted = await service.variance(encryptedArray);
  const homoVarResult = await service.decrypt(homoVarEncrypted);
  const homoVarEnd = performance.now();
  console.log(`  Homomorphique: ${homoVarResult} (${(homoVarEnd - homoVarStart).toFixed(2)} ms)`);
  console.log(`  Précision: ${Math.abs(normalVarResult - homoVarResult) < 0.1 ? 'Bonne ✅' : 'Mauvaise ❌'}`);
  
  // Test de rotation des clés
  console.log('\n--- Test de rotation des clés ---');
  const rotateStart = performance.now();
  const newKeys = await service.rotateKeys();
  const rotateEnd = performance.now();
  console.log(`Rotation des clés: ${(rotateEnd - rotateStart).toFixed(2)} ms`);
  
  // Vérifier que les nouvelles clés fonctionnent
  const testValue = 42;
  const encryptedWithNewKeys = await service.encrypt(testValue);
  const decryptedWithNewKeys = await service.decrypt(encryptedWithNewKeys);
  console.log(`Validation avec nouvelles clés: ${Math.abs(testValue - decryptedWithNewKeys) < 0.001 ? 'Réussi ✅' : 'Échec ❌'}`);
}

// Exécuter le benchmark
async function main() {
  try {
    const service = await importService();
    await runBenchmark(service);
  } catch (error) {
    console.error('Erreur lors de l\'exécution du benchmark:', error);
  }
}

main();
