/**
 * Script pour mesurer les performances du système de recommandation
 * 
 * Usage:
 * node benchmark-recommendation-system.js --url=http://localhost:3000 --token=your_jwt_token --iterations=100
 */

const axios = require('axios');
const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');
const { program } = require('commander');

// Configurer les options de ligne de commande
program
  .option('--url <url>', 'URL de base de l\'API', 'http://localhost:3000')
  .option('--token <token>', 'Token JWT pour l\'authentification')
  .option('--iterations <iterations>', 'Nombre d\'itérations pour chaque test', parseInt, 50)
  .option('--output <output>', 'Fichier de sortie pour les résultats', 'benchmark-results.json')
  .option('--verbose', 'Afficher les résultats détaillés', false)
  .option('--cache <cache>', 'Activer ou désactiver le cache', 'true')
  .parse(process.argv);

const options = program.opts();

// Vérifier les options
if (!options.token) {
  console.error('Erreur: Token JWT requis. Utilisez --token=your_jwt_token');
  process.exit(1);
}

// Configurer le client HTTP
const api = axios.create({
  baseURL: options.url,
  headers: {
    'Authorization': `Bearer ${options.token}`,
    'Content-Type': 'application/json',
  },
});

// Types d'éléments à tester
const itemTypes = ['RETREAT', 'PARTNER', 'COURSE'];

// Stratégies de recommandation à tester
const strategies = ['CONTENT_BASED', 'COLLABORATIVE', 'HYBRID'];

// Endpoints à tester
const endpoints = [
  {
    name: 'Recommandations personnalisées',
    path: '/api/v1/recommendations',
    method: 'get',
    params: (type, strategy) => ({
      type,
      strategy,
      limit: 10,
      page: 1,
    }),
  },
  {
    name: 'Recommandations tendance',
    path: '/api/v1/recommendations/trending',
    method: 'get',
    params: (type) => ({
      type,
      limit: 10,
      page: 1,
    }),
  },
  {
    name: 'Éléments similaires',
    path: (type) => `/api/v1/recommendations/similar/${type}/item-1`,
    method: 'get',
    params: () => ({
      limit: 10,
      page: 1,
    }),
  },
];

/**
 * Exécute un test de performance pour un endpoint
 * @param {Object} endpoint Configuration de l'endpoint
 * @param {string} type Type d'élément
 * @param {string} strategy Stratégie de recommandation
 * @returns {Object} Résultats du test
 */
async function runTest(endpoint, type, strategy = null) {
  const results = {
    endpoint: typeof endpoint.path === 'function' ? endpoint.path(type) : endpoint.path,
    type,
    strategy,
    iterations: options.iterations,
    times: [],
    errors: 0,
    avgTime: 0,
    minTime: Infinity,
    maxTime: 0,
    medianTime: 0,
    p95Time: 0,
    p99Time: 0,
    successRate: 0,
  };

  // Paramètres de la requête
  const params = strategy 
    ? endpoint.params(type, strategy)
    : endpoint.params(type);
  
  // Ajouter le paramètre de cache si spécifié
  if (options.cache !== 'true') {
    params.cache = options.cache === 'true';
  }

  // Exécuter les itérations
  for (let i = 0; i < options.iterations; i++) {
    try {
      const start = performance.now();
      
      if (endpoint.method === 'get') {
        await api.get(typeof endpoint.path === 'function' ? endpoint.path(type) : endpoint.path, { params });
      } else if (endpoint.method === 'post') {
        await api.post(typeof endpoint.path === 'function' ? endpoint.path(type) : endpoint.path, params);
      }
      
      const end = performance.now();
      const time = end - start;
      
      results.times.push(time);
      
      if (time < results.minTime) results.minTime = time;
      if (time > results.maxTime) results.maxTime = time;
      
      if (options.verbose) {
        console.log(`Itération ${i + 1}/${options.iterations}: ${time.toFixed(2)}ms`);
      }
    } catch (error) {
      results.errors++;
      console.error(`Erreur lors de l'itération ${i + 1}:`, error.message);
    }
  }

  // Calculer les statistiques
  if (results.times.length > 0) {
    results.avgTime = results.times.reduce((sum, time) => sum + time, 0) / results.times.length;
    
    // Trier les temps pour calculer la médiane et les percentiles
    results.times.sort((a, b) => a - b);
    
    const medianIndex = Math.floor(results.times.length / 2);
    results.medianTime = results.times[medianIndex];
    
    const p95Index = Math.floor(results.times.length * 0.95);
    results.p95Time = results.times[p95Index];
    
    const p99Index = Math.floor(results.times.length * 0.99);
    results.p99Time = results.times[p99Index];
    
    results.successRate = ((options.iterations - results.errors) / options.iterations) * 100;
  }

  return results;
}

/**
 * Exécute tous les tests de performance
 */
async function runAllTests() {
  console.log(`Démarrage des tests de performance avec ${options.iterations} itérations par test...`);
  console.log(`URL de base: ${options.url}`);
  console.log(`Cache: ${options.cache}`);
  
  const allResults = {
    timestamp: new Date().toISOString(),
    config: {
      url: options.url,
      iterations: options.iterations,
      cache: options.cache === 'true',
    },
    results: [],
  };

  // Tester les recommandations personnalisées avec différentes stratégies
  for (const type of itemTypes) {
    for (const strategy of strategies) {
      console.log(`\nTest: Recommandations personnalisées - Type: ${type}, Stratégie: ${strategy}`);
      const result = await runTest(endpoints[0], type, strategy);
      console.log(`Temps moyen: ${result.avgTime.toFixed(2)}ms, Médiane: ${result.medianTime.toFixed(2)}ms, P95: ${result.p95Time.toFixed(2)}ms, Taux de succès: ${result.successRate.toFixed(2)}%`);
      allResults.results.push(result);
    }
  }

  // Tester les recommandations tendance
  for (const type of itemTypes) {
    console.log(`\nTest: Recommandations tendance - Type: ${type}`);
    const result = await runTest(endpoints[1], type);
    console.log(`Temps moyen: ${result.avgTime.toFixed(2)}ms, Médiane: ${result.medianTime.toFixed(2)}ms, P95: ${result.p95Time.toFixed(2)}ms, Taux de succès: ${result.successRate.toFixed(2)}%`);
    allResults.results.push(result);
  }

  // Tester les éléments similaires
  for (const type of itemTypes) {
    console.log(`\nTest: Éléments similaires - Type: ${type}`);
    const result = await runTest(endpoints[2], type);
    console.log(`Temps moyen: ${result.avgTime.toFixed(2)}ms, Médiane: ${result.medianTime.toFixed(2)}ms, P95: ${result.p95Time.toFixed(2)}ms, Taux de succès: ${result.successRate.toFixed(2)}%`);
    allResults.results.push(result);
  }

  // Enregistrer les résultats
  fs.writeFileSync(
    path.join(process.cwd(), options.output),
    JSON.stringify(allResults, null, 2)
  );
  
  console.log(`\nTests terminés. Résultats enregistrés dans ${options.output}`);
}

// Exécuter les tests
runAllTests().catch(error => {
  console.error('Erreur lors de l\'exécution des tests:', error);
  process.exit(1);
});
