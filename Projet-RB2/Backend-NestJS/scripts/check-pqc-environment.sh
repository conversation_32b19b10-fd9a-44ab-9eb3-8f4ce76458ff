#!/usr/bin/env bash
# Vérifie la présence de liboqs-node et des outils natifs requis
set -e

echo "=== Vérification de l'environnement pour la cryptographie post-quantique ==="

# Vérification de Node.js
if ! command -v node > /dev/null; then
  echo "[ERREUR] Node.js n'est pas installé." >&2
  exit 1
fi

# Vérification de la version Node.js (LTS recommandé)
NODE_VERSION=$(node -v)
echo "Node.js version: $NODE_VERSION"

# Vérification de CMake
if ! command -v cmake > /dev/null; then
  echo "[AVERTISSEMENT] CMake n'est pas installé. La compilation native de liboqs-node ne sera pas possible." >&2
  HAS_CMAKE=0
else
  CMAKE_VERSION=$(cmake --version | head -n 1)
  echo "CMake version: $CMAKE_VERSION"
  HAS_CMAKE=1
fi

# Vérification de Ninja
if ! command -v ninja > /dev/null; then
  echo "[AVERTISSEMENT] Ninja n'est pas installé. La compilation native de liboqs-node ne sera pas possible." >&2
  HAS_NINJA=0
else
  NINJA_VERSION=$(ninja --version)
  echo "Ninja version: $NINJA_VERSION"
  HAS_NINJA=1
fi

# Vérification du compilateur C/C++
if ! command -v gcc > /dev/null && ! command -v clang > /dev/null; then
  echo "[AVERTISSEMENT] Aucun compilateur C/C++ (gcc/clang) n'est installé. La compilation native de liboqs-node ne sera pas possible." >&2
  HAS_COMPILER=0
else
  if command -v gcc > /dev/null; then
    GCC_VERSION=$(gcc --version | head -n 1)
    echo "GCC version: $GCC_VERSION"
    HAS_COMPILER=1
  elif command -v clang > /dev/null; then
    CLANG_VERSION=$(clang --version | head -n 1)
    echo "Clang version: $CLANG_VERSION"
    HAS_COMPILER=1
  fi
fi

# Vérification de la présence de liboqs-node dans node_modules
if [ ! -d "node_modules/liboqs-node" ]; then
  echo "[AVERTISSEMENT] liboqs-node n'est pas installé dans node_modules." >&2
  echo "Pour l'installer, exécutez: npm install liboqs-node" >&2
  HAS_LIBOQS=0
else
  echo "liboqs-node est installé dans node_modules."
  HAS_LIBOQS=1
  
  # Test rapide d'import JS
  if node -e "try { require('liboqs-node'); console.log('Import réussi!'); } catch(e) { console.error('Erreur:', e.message); process.exit(1); }"; then
    echo "Import de liboqs-node réussi."
    IMPORT_OK=1
  else
    echo "[ERREUR] Impossible de charger liboqs-node." >&2
    IMPORT_OK=0
  fi
fi

# Vérification du mode disponible
if [ "$HAS_CMAKE" -eq 1 ] && [ "$HAS_NINJA" -eq 1 ] && [ "$HAS_COMPILER" -eq 1 ] && [ "$HAS_LIBOQS" -eq 1 ] && [ "$IMPORT_OK" -eq 1 ]; then
  echo "[OK] Environnement prêt pour le mode post-quantique natif."
  echo "Mode disponible: NATIF (utilisation réelle des algorithmes post-quantiques)"
  exit 0
elif [ "$HAS_LIBOQS" -eq 1 ] && [ "$IMPORT_OK" -eq 1 ]; then
  echo "[OK] liboqs-node est installé mais l'environnement de compilation n'est pas complet."
  echo "Mode disponible: SIMULATION (fallback vers des algorithmes classiques)"
  exit 0
else
  echo "[AVERTISSEMENT] L'environnement n'est pas prêt pour la cryptographie post-quantique."
  echo "Mode disponible: SIMULATION (fallback vers des algorithmes classiques)"
  exit 1
fi
