#!/bin/bash
# Archive automatiquement les rapports de conformité (Markdown et PDF) dans un bucket S3 sécurisé
# Prérequis : awscli configuré avec les droits adéquats

BUCKET="s3://my-compliance-archive-bucket"
DATE=$(date +'%Y-%m-%d_%H-%M-%S')

if [ ! -f compliance-report.md ]; then
  echo "Le rapport Markdown n'existe pas. Générez-le d'abord."
  exit 1
fi

if [ ! -f compliance-report.pdf ]; then
  echo "Le rapport PDF n'existe pas. Générez-le d'abord."
  exit 1
fi

aws s3 cp compliance-report.md "$BUCKET/compliance-report_$DATE.md"
aws s3 cp compliance-report.pdf "$BUCKET/compliance-report_$DATE.pdf"

if [ $? -eq 0 ]; then
  echo "✅ Rapports archivés avec succès dans $BUCKET."
else
  echo "❌ Erreur lors de l'archivage S3."
  exit 1
fi
