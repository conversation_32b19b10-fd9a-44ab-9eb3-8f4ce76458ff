// Script Node.js : Convertit le rapport Markdown de conformité en PDF
const fs = require('fs');
const markdownpdf = require('markdown-pdf');

const mdFile = 'compliance-report.md';
const pdfFile = 'compliance-report.pdf';

if (!fs.existsSync(mdFile)) {
  console.error(`Le fichier ${mdFile} n'existe pas. Générez d'abord le rapport Markdown.`);
  process.exit(1);
}

markdownpdf()
  .from(mdFile)
  .to(pdfFile, function () {
    console.log(`✅ Rapport PDF généré : ${pdfFile}`);
  });
