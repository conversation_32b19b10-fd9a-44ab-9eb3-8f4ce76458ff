/**
 * Script de test de charge pour les services de cryptage
 * 
 * Ce script teste les performances des services de cryptage sous différentes charges
 * et génère un rapport détaillé.
 * 
 * Usage: node crypto-load-test.js [options]
 * Options:
 *   --service=<service>    Service à tester (standard, homomorphic, quantum, all)
 *   --operations=<ops>     Nombre d'opérations à exécuter
 *   --concurrency=<conc>   Niveau de concurrence
 *   --data-size=<size>     Taille des données en octets
 *   --report=<file>        Fichier de rapport
 */

const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// Analyser les arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  const [key, value] = arg.split('=');
  acc[key.replace(/^--/, '')] = value;
  return acc;
}, {});

const SERVICE = args.service || 'all';
const OPERATIONS = parseInt(args.operations || '1000', 10);
const CONCURRENCY = parseInt(args.concurrency || '10', 10);
const DATA_SIZE = parseInt(args.datasize || '1024', 10);
const REPORT_FILE = args.report || 'crypto-load-test-report.json';

// Configuration des tests
const TEST_CONFIG = {
  standard: {
    encrypt: true,
    decrypt: true,
    batch: true,
  },
  homomorphic: {
    encrypt: true,
    decrypt: true,
    add: true,
    multiply: true,
    divide: true,
    average: true,
    batch: true,
  },
  quantum: {
    encrypt: true,
    decrypt: true,
    hybrid: true,
  },
};

// Fonction pour générer des données aléatoires
function generateRandomData(size) {
  return crypto.randomBytes(size);
}

// Fonction pour générer des nombres aléatoires
function generateRandomNumbers(count) {
  return Array.from({ length: count }, () => Math.random() * 1000);
}

// Fonction pour mesurer le temps d'exécution
async function measureTime(fn) {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  return {
    result,
    duration: end - start,
  };
}

// Fonction pour exécuter un test
async function runTest(service, operation, data, count) {
  // Simuler l'environnement NestJS
  const configService = {
    get: (key, defaultValue) => {
      const config = {
        'HOMOMORPHIC_ENCRYPTION_ENABLED': true,
        'HOMOMORPHIC_ENCRYPTION_SCHEME': 'CKKS',
        'HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE': 8192,
        'HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL': 128,
        'QUANTUM_RESISTANT_ENABLED': true,
        'QUANTUM_RESISTANT_ALGORITHM': 'hybrid',
        'QUANTUM_RESISTANT_KEY_SIZE': 3072,
        'QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM': 'rsa',
        'QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE': 2048,
        'CRYPTO_CACHE_ENABLED': true,
        'CRYPTO_CACHE_TTL': 60 * 60 * 1000,
        'CRYPTO_OPTIMIZATION_BATCH_SIZE': 100,
        'CRYPTO_OPTIMIZATION_PARALLELISM': 4,
      };
      return config[key] !== undefined ? config[key] : defaultValue;
    },
  };

  // Importer le service
  let serviceInstance;
  try {
    if (service === 'standard') {
      const { SensitiveDataEncryptionService } = require('../dist/modules/security/services/sensitive-data-encryption.service');
      serviceInstance = new SensitiveDataEncryptionService(configService);
    } else if (service === 'homomorphic') {
      const { HomomorphicEncryptionService } = require('../dist/modules/security/services/homomorphic-encryption.service');
      serviceInstance = new HomomorphicEncryptionService(configService);
      await serviceInstance.initialize();
    } else if (service === 'quantum') {
      const { QuantumResistantService } = require('../dist/modules/security/services/quantum-resistant.service');
      serviceInstance = new QuantumResistantService(configService);
      await serviceInstance.generateKeys();
    } else {
      throw new Error(`Service inconnu: ${service}`);
    }
  } catch (error) {
    console.error(`Erreur lors de l'initialisation du service ${service}:`, error);
    return {
      service,
      operation,
      success: false,
      error: error.message,
    };
  }

  // Exécuter l'opération
  try {
    let results = [];
    
    for (let i = 0; i < count; i++) {
      let result;
      
      if (service === 'standard') {
        if (operation === 'encrypt') {
          result = await measureTime(() => serviceInstance.encrypt(data));
        } else if (operation === 'decrypt') {
          const encrypted = await serviceInstance.encrypt(data);
          result = await measureTime(() => serviceInstance.decrypt(encrypted));
        } else if (operation === 'batch') {
          const objects = Array.from({ length: 10 }, (_, i) => ({
            id: i,
            name: `User ${i}`,
            email: `user${i}@example.com`,
            password: `password${i}`,
            address: `Address ${i}`,
          }));
          const fieldsToEncrypt = ['name', 'email', 'password'];
          result = await measureTime(() => serviceInstance.encryptObject(objects[0], fieldsToEncrypt));
        }
      } else if (service === 'homomorphic') {
        if (operation === 'encrypt') {
          result = await measureTime(() => serviceInstance.encrypt(Math.random() * 1000));
        } else if (operation === 'decrypt') {
          const encrypted = await serviceInstance.encrypt(Math.random() * 1000);
          result = await measureTime(() => serviceInstance.decrypt(encrypted));
        } else if (operation === 'add') {
          const a = await serviceInstance.encrypt(Math.random() * 1000);
          const b = await serviceInstance.encrypt(Math.random() * 1000);
          result = await measureTime(() => serviceInstance.add(a, b));
        } else if (operation === 'multiply') {
          const a = await serviceInstance.encrypt(Math.random() * 1000);
          const b = await serviceInstance.encrypt(Math.random() * 1000);
          result = await measureTime(() => serviceInstance.multiply(a, b));
        } else if (operation === 'divide') {
          const a = await serviceInstance.encrypt(Math.random() * 1000);
          result = await measureTime(() => serviceInstance.divide(a, Math.random() * 100 + 1));
        } else if (operation === 'average') {
          const values = await Promise.all(
            generateRandomNumbers(5).map(v => serviceInstance.encrypt(v))
          );
          result = await measureTime(() => serviceInstance.average(values));
        } else if (operation === 'batch') {
          const values = generateRandomNumbers(10);
          result = await measureTime(() => serviceInstance.encryptBatch(values));
        }
      } else if (service === 'quantum') {
        if (operation === 'encrypt') {
          result = await measureTime(() => serviceInstance.encrypt(data));
        } else if (operation === 'decrypt') {
          const encrypted = await serviceInstance.encrypt(data);
          result = await measureTime(() => serviceInstance.decrypt(encrypted));
        } else if (operation === 'hybrid') {
          result = await measureTime(() => serviceInstance.hybridEncrypt(data));
        }
      }
      
      results.push(result.duration);
    }
    
    // Calculer les statistiques
    results.sort((a, b) => a - b);
    const min = results[0];
    const max = results[results.length - 1];
    const avg = results.reduce((a, b) => a + b, 0) / results.length;
    const median = results[Math.floor(results.length / 2)];
    const p95 = results[Math.floor(results.length * 0.95)];
    const p99 = results[Math.floor(results.length * 0.99)];
    
    return {
      service,
      operation,
      count,
      dataSize: data.length,
      success: true,
      stats: {
        min,
        max,
        avg,
        median,
        p95,
        p99,
      },
    };
  } catch (error) {
    console.error(`Erreur lors de l'exécution de l'opération ${operation} sur le service ${service}:`, error);
    return {
      service,
      operation,
      success: false,
      error: error.message,
    };
  }
}

// Fonction principale pour exécuter les tests
async function runTests() {
  console.log('=== Test de charge des services de cryptage ===');
  console.log(`Service: ${SERVICE}`);
  console.log(`Opérations: ${OPERATIONS}`);
  console.log(`Concurrence: ${CONCURRENCY}`);
  console.log(`Taille des données: ${DATA_SIZE} octets`);
  console.log('');
  
  const startTime = performance.now();
  const results = [];
  
  // Générer les données de test
  const testData = generateRandomData(DATA_SIZE);
  
  // Déterminer les services à tester
  const servicesToTest = SERVICE === 'all'
    ? Object.keys(TEST_CONFIG)
    : [SERVICE];
  
  // Exécuter les tests pour chaque service
  for (const service of servicesToTest) {
    console.log(`\n--- Test du service ${service} ---`);
    
    // Déterminer les opérations à tester
    const operations = Object.entries(TEST_CONFIG[service])
      .filter(([_, enabled]) => enabled)
      .map(([operation]) => operation);
    
    // Exécuter les tests pour chaque opération
    for (const operation of operations) {
      console.log(`\nOpération: ${operation}`);
      
      // Créer des workers pour exécuter les tests en parallèle
      const operationsPerWorker = Math.ceil(OPERATIONS / CONCURRENCY);
      const workers = [];
      
      for (let i = 0; i < CONCURRENCY; i++) {
        const worker = new Worker(__filename, {
          workerData: {
            service,
            operation,
            data: testData,
            count: operationsPerWorker,
          },
        });
        
        workers.push(new Promise((resolve, reject) => {
          worker.on('message', resolve);
          worker.on('error', reject);
          worker.on('exit', code => {
            if (code !== 0) {
              reject(new Error(`Worker stopped with exit code ${code}`));
            }
          });
        }));
      }
      
      // Attendre que tous les workers aient terminé
      console.log(`Exécution de ${OPERATIONS} opérations avec ${CONCURRENCY} workers...`);
      const workerResults = await Promise.all(workers);
      
      // Agréger les résultats
      const successResults = workerResults.filter(r => r.success);
      if (successResults.length === 0) {
        console.error(`Toutes les opérations ${operation} ont échoué`);
        results.push({
          service,
          operation,
          success: false,
          error: workerResults[0].error,
        });
        continue;
      }
      
      // Calculer les statistiques globales
      const allDurations = successResults.flatMap(r => 
        Array.from({ length: r.count }, () => r.stats.avg)
      );
      
      const min = Math.min(...successResults.map(r => r.stats.min));
      const max = Math.max(...successResults.map(r => r.stats.max));
      const avg = allDurations.reduce((a, b) => a + b, 0) / allDurations.length;
      const median = successResults.reduce((a, b) => a + b.stats.median, 0) / successResults.length;
      const p95 = successResults.reduce((a, b) => a + b.stats.p95, 0) / successResults.length;
      const p99 = successResults.reduce((a, b) => a + b.stats.p99, 0) / successResults.length;
      
      const result = {
        service,
        operation,
        count: OPERATIONS,
        dataSize: DATA_SIZE,
        concurrency: CONCURRENCY,
        success: true,
        stats: {
          min,
          max,
          avg,
          median,
          p95,
          p99,
          throughput: OPERATIONS / ((performance.now() - startTime) / 1000),
        },
      };
      
      results.push(result);
      
      console.log(`Min: ${min.toFixed(2)} ms`);
      console.log(`Max: ${max.toFixed(2)} ms`);
      console.log(`Avg: ${avg.toFixed(2)} ms`);
      console.log(`Median: ${median.toFixed(2)} ms`);
      console.log(`P95: ${p95.toFixed(2)} ms`);
      console.log(`P99: ${p99.toFixed(2)} ms`);
      console.log(`Throughput: ${result.stats.throughput.toFixed(2)} ops/s`);
    }
  }
  
  const endTime = performance.now();
  const totalDuration = (endTime - startTime) / 1000;
  
  console.log(`\n=== Test terminé en ${totalDuration.toFixed(2)} secondes ===`);
  
  // Générer le rapport
  const report = {
    timestamp: new Date().toISOString(),
    config: {
      service: SERVICE,
      operations: OPERATIONS,
      concurrency: CONCURRENCY,
      dataSize: DATA_SIZE,
    },
    duration: totalDuration,
    results,
  };
  
  // Écrire le rapport dans un fichier
  fs.writeFileSync(REPORT_FILE, JSON.stringify(report, null, 2));
  console.log(`Rapport écrit dans ${REPORT_FILE}`);
  
  return report;
}

// Code principal
if (isMainThread) {
  // Thread principal
  if (require.main === module) {
    runTests().catch(console.error);
  }
} else {
  // Worker thread
  const { service, operation, data, count } = workerData;
  runTest(service, operation, data, count)
    .then(result => parentPort.postMessage(result))
    .catch(error => {
      console.error(`Erreur dans le worker:`, error);
      parentPort.postMessage({
        service,
        operation,
        success: false,
        error: error.message,
      });
    });
}
