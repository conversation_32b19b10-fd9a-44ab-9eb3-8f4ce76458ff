import { createConnection, getRepository } from 'typeorm';
import { SensitiveEntity } from '../../src/entities/SensitiveEntity';
import * as fs from 'fs';

async function restoreData(backupPath: string) {
  const connection = await createConnection();
  const repo = getRepository(SensitiveEntity);
  const data: SensitiveEntity[] = JSON.parse(fs.readFileSync(backupPath, 'utf-8'));

  for (const entity of data) {
    await repo.save(entity);
  }

  await connection.close();
  console.log('Restore complete.');
}

// Aucun champ sensitiveField ici
// Si besoin de déchiffrer/restaurer, utiliser encryptedValue
restoreData('./backup/sensitive-entities-backup.json').catch(e => {
  console.error('<PERSON><PERSON> failed:', e);
  process.exit(1);
});
