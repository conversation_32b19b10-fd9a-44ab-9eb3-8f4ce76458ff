#!/bin/bash

# Script pour résoudre les problèmes de migration Prisma

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Script de résolution des problèmes de migration Prisma ===${NC}"

# Vérifier si Prisma CLI est installé
if ! [ -x "$(command -v npx prisma)" ]; then
  echo -e "${RED}Erreur: Prisma CLI n'est pas installé.${NC}" >&2
  echo -e "${YELLOW}Installation de Prisma CLI...${NC}"
  npm install --save-dev prisma
fi

# Créer un backup du schéma Prisma
echo -e "${YELLOW}Création d'un backup du schéma Prisma...${NC}"
cp prisma/schema.prisma prisma/schema.prisma.backup
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Backup créé avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création du backup${NC}"
  exit 1
fi

# Vérifier si le dossier migrations existe
if [ ! -d "prisma/migrations" ]; then
  echo -e "${YELLOW}Le dossier migrations n'existe pas. Création du dossier...${NC}"
  mkdir -p prisma/migrations
fi

# Créer un backup du dossier migrations
echo -e "${YELLOW}Création d'un backup du dossier migrations...${NC}"
cp -r prisma/migrations prisma/migrations.backup
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Backup des migrations créé avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création du backup des migrations${NC}"
  exit 1
fi

# Réinitialiser le dossier migrations
echo -e "${YELLOW}Réinitialisation du dossier migrations...${NC}"
rm -rf prisma/migrations/*
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Dossier migrations réinitialisé avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la réinitialisation du dossier migrations${NC}"
  exit 1
fi

# Créer une migration initiale
echo -e "${YELLOW}Création d'une migration initiale...${NC}"
npx prisma migrate dev --name init --create-only
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Migration initiale créée avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création de la migration initiale${NC}"
  echo -e "${YELLOW}Restauration du backup...${NC}"
  cp prisma/schema.prisma.backup prisma/schema.prisma
  cp -r prisma/migrations.backup/* prisma/migrations/
  exit 1
fi

# Appliquer la migration
echo -e "${YELLOW}Application de la migration...${NC}"
npx prisma migrate dev
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Migration appliquée avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de l'application de la migration${NC}"
  echo -e "${YELLOW}Restauration du backup...${NC}"
  cp prisma/schema.prisma.backup prisma/schema.prisma
  cp -r prisma/migrations.backup/* prisma/migrations/
  exit 1
fi

# Générer le client Prisma
echo -e "${YELLOW}Génération du client Prisma...${NC}"
npx prisma generate
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Client Prisma généré avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la génération du client Prisma${NC}"
  exit 1
fi

# Supprimer les backups
echo -e "${YELLOW}Suppression des backups...${NC}"
rm prisma/schema.prisma.backup
rm -rf prisma/migrations.backup
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Backups supprimés avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la suppression des backups${NC}"
  exit 1
fi

echo -e "${GREEN}=== Problèmes de migration Prisma résolus avec succès ===${NC}"
echo -e "${YELLOW}Note: Si vous avez des données importantes dans la base de données, assurez-vous de les sauvegarder avant d'exécuter ce script.${NC}"
echo -e "${YELLOW}Ce script réinitialise complètement les migrations Prisma, ce qui peut entraîner la perte de données.${NC}"
exit 0
