#!/usr/bin/env node

/**
 * This script generates TypeScript interfaces from the Prisma schema
 * It creates a types.ts file that can be used in both frontend and backend
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths
const PRISMA_SCHEMA_PATH = path.join(__dirname, '../prisma/schema.prisma');
const TYPES_OUTPUT_PATH = path.join(__dirname, '../src/types/prisma-types.ts');
const FRONTEND_TYPES_PATH = path.join(__dirname, '../../Front-Audrey-V1-Main-main/src/types/prisma-types.ts');

// Read the Prisma schema
const schemaContent = fs.readFileSync(PRISMA_SCHEMA_PATH, 'utf8');

// Extract models and enums
const modelRegex = /model\s+(\w+)\s+{([^}]*)}/g;
const enumRegex = /enum\s+(\w+)\s+{([^}]*)}/g;

let models = [];
let enums = [];
let match;

// Extract models
while ((match = modelRegex.exec(schemaContent)) !== null) {
  const modelName = match[1];
  const modelContent = match[2];
  
  // Extract fields
  const fields = modelContent
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.startsWith('@@') && !line.startsWith('//'));
  
  models.push({ name: modelName, fields });
}

// Extract enums
while ((match = enumRegex.exec(schemaContent)) !== null) {
  const enumName = match[1];
  const enumContent = match[2];
  
  // Extract values
  const values = enumContent
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.startsWith('//'));
  
  enums.push({ name: enumName, values });
}

// Generate TypeScript interfaces
let typesContent = `/**
 * This file is auto-generated from the Prisma schema
 * Do not edit this file directly
 */

`;

// Generate enums
enums.forEach(enumDef => {
  typesContent += `export enum ${enumDef.name} {\n`;
  enumDef.values.forEach(value => {
    typesContent += `  ${value} = "${value}",\n`;
  });
  typesContent += `}\n\n`;
});

// Generate interfaces
models.forEach(model => {
  typesContent += `export interface ${model.name} {\n`;
  
  model.fields.forEach(field => {
    // Parse field
    const fieldParts = field.split(/\s+/);
    const fieldName = fieldParts[0];
    let fieldType = fieldParts[1];
    const isOptional = field.includes('?');
    const isArray = field.includes('[]');
    
    // Map Prisma types to TypeScript types
    let tsType;
    switch (fieldType) {
      case 'String':
        tsType = 'string';
        break;
      case 'Int':
      case 'Float':
        tsType = 'number';
        break;
      case 'Boolean':
        tsType = 'boolean';
        break;
      case 'DateTime':
        tsType = 'Date';
        break;
      case 'Json':
        tsType = 'Record<string, any>';
        break;
      default:
        // Check if it's an enum or another model
        if (enums.some(e => e.name === fieldType)) {
          tsType = fieldType;
        } else if (models.some(m => m.name === fieldType)) {
          tsType = fieldType;
        } else {
          tsType = 'any';
        }
    }
    
    // Handle arrays
    if (isArray) {
      tsType = `${tsType}[]`;
    }
    
    // Add field to interface
    typesContent += `  ${fieldName}${isOptional ? '?' : ''}: ${tsType};\n`;
  });
  
  typesContent += `}\n\n`;
});

// Write the types file
fs.writeFileSync(TYPES_OUTPUT_PATH, typesContent);
console.log(`Generated types at ${TYPES_OUTPUT_PATH}`);

// Copy to frontend if the directory exists
if (fs.existsSync(path.dirname(FRONTEND_TYPES_PATH))) {
  fs.mkdirSync(path.dirname(FRONTEND_TYPES_PATH), { recursive: true });
  fs.writeFileSync(FRONTEND_TYPES_PATH, typesContent);
  console.log(`Copied types to frontend at ${FRONTEND_TYPES_PATH}`);
}

// Run prettier on the generated files
try {
  execSync(`npx prettier --write ${TYPES_OUTPUT_PATH}`);
  if (fs.existsSync(FRONTEND_TYPES_PATH)) {
    execSync(`npx prettier --write ${FRONTEND_TYPES_PATH}`);
  }
  console.log('Formatted generated files with prettier');
} catch (error) {
  console.warn('Warning: Could not format files with prettier', error.message);
}
