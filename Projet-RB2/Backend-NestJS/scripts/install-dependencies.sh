#!/bin/bash

# Script pour installer les dépendances nécessaires pour les nouveaux modules

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Installation des dépendances pour les nouveaux modules...${NC}"

# Installer les dépendances pour le module de modération
echo -e "${YELLOW}Installation des dépendances pour le module de modération...${NC}"
npm install @tensorflow/tfjs-node --save
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'installation de @tensorflow/tfjs-node.${NC}"
    exit 1
fi
echo -e "${GREEN}@tensorflow/tfjs-node installé avec succès.${NC}"

# Installer les dépendances pour le module d'analyse
echo -e "${YELLOW}Installation des dépendances pour le module d'analyse...${NC}"
npm install @nestjs/schedule @nestjs/axios rxjs --save
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'installation des dépendances pour le module d'analyse.${NC}"
    exit 1
fi
echo -e "${GREEN}Dépendances pour le module d'analyse installées avec succès.${NC}"

# Installer les dépendances de développement
echo -e "${YELLOW}Installation des dépendances de développement...${NC}"
npm install jest-mock-extended --save-dev
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'installation des dépendances de développement.${NC}"
    exit 1
fi
echo -e "${GREEN}Dépendances de développement installées avec succès.${NC}"

# Créer les dossiers pour les modèles d'IA
echo -e "${YELLOW}Création des dossiers pour les modèles d'IA...${NC}"
mkdir -p models/text-moderation
mkdir -p models/image-moderation
echo -e "${GREEN}Dossiers pour les modèles d'IA créés avec succès.${NC}"

echo -e "${GREEN}Installation des dépendances terminée avec succès.${NC}"
