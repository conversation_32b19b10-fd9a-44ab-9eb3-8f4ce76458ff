#!/bin/bash

# Script pour migrer vers la nouvelle API de recommandation
# Ce script effectue les étapes suivantes :
# 1. Sauvegarde les fichiers existants
# 2. Renomme les nouveaux fichiers refactorisés
# 3. Met à jour les imports dans les fichiers dépendants

set -e

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Répertoire de base
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
RECOMMENDATION_DIR="${BASE_DIR}/src/modules/recommendation"
BACKUP_DIR="${BASE_DIR}/backup/recommendation_$(date +%Y%m%d%H%M%S)"

echo -e "${YELLOW}Migration de l'API de recommandation${NC}"
echo -e "${YELLOW}=================================${NC}"

# Étape 1: Créer le répertoire de sauvegarde
echo -e "${GREEN}Création du répertoire de sauvegarde...${NC}"
mkdir -p "${BACKUP_DIR}"

# Étape 2: Sauvegarder les fichiers existants
echo -e "${GREEN}Sauvegarde des fichiers existants...${NC}"
cp -r "${RECOMMENDATION_DIR}" "${BACKUP_DIR}"
echo -e "${GREEN}Sauvegarde terminée dans ${BACKUP_DIR}${NC}"

# Étape 3: Renommer les fichiers refactorisés
echo -e "${GREEN}Renommage des fichiers refactorisés...${NC}"

# Renommer le contrôleur
if [ -f "${RECOMMENDATION_DIR}/controllers/recommendation.controller.refactored.ts" ]; then
  mv "${RECOMMENDATION_DIR}/controllers/recommendation.controller.ts" "${RECOMMENDATION_DIR}/controllers/recommendation.controller.old.ts"
  mv "${RECOMMENDATION_DIR}/controllers/recommendation.controller.refactored.ts" "${RECOMMENDATION_DIR}/controllers/recommendation.controller.ts"
  mv "${RECOMMENDATION_DIR}/controllers/recommendation.controller.refactored.spec.ts" "${RECOMMENDATION_DIR}/controllers/recommendation.controller.spec.ts"
  echo -e "${GREEN}Contrôleur renommé avec succès${NC}"
else
  echo -e "${RED}Fichier du contrôleur refactorisé non trouvé${NC}"
  exit 1
fi

# Renommer le module
if [ -f "${RECOMMENDATION_DIR}/recommendation.module.refactored.ts" ]; then
  mv "${RECOMMENDATION_DIR}/recommendation.module.ts" "${RECOMMENDATION_DIR}/recommendation.module.old.ts"
  mv "${RECOMMENDATION_DIR}/recommendation.module.refactored.ts" "${RECOMMENDATION_DIR}/recommendation.module.ts"
  echo -e "${GREEN}Module renommé avec succès${NC}"
else
  echo -e "${RED}Fichier du module refactorisé non trouvé${NC}"
  exit 1
fi

# Étape 4: Mettre à jour les imports dans les fichiers dépendants
echo -e "${GREEN}Mise à jour des imports dans les fichiers dépendants...${NC}"

# Trouver tous les fichiers qui importent l'ancien contrôleur
FILES_TO_UPDATE=$(grep -r --include="*.ts" "from '.*recommendation.controller'" "${BASE_DIR}" | grep -v "recommendation.controller.refactored" | cut -d':' -f1)

# Mettre à jour les imports
for FILE in $FILES_TO_UPDATE; do
  echo -e "${GREEN}Mise à jour des imports dans ${FILE}${NC}"
  # Remplacer les imports
  sed -i 's/from '\''.*\/recommendation.controller'\''/from '\''..\/modules\/recommendation\/controllers\/recommendation.controller'\''/g' "${FILE}"
done

echo -e "${GREEN}Migration terminée avec succès!${NC}"
echo -e "${YELLOW}Note: Les anciens fichiers ont été renommés avec l'extension .old.ts et peuvent être supprimés une fois la migration vérifiée.${NC}"

# Étape 5: Exécuter les tests
echo -e "${GREEN}Exécution des tests...${NC}"
cd "${BASE_DIR}" && npm test -- --testPathPattern=recommendation.controller

if [ $? -eq 0 ]; then
  echo -e "${GREEN}Tests réussis!${NC}"
else
  echo -e "${RED}Certains tests ont échoué. Veuillez vérifier les erreurs et corriger les problèmes.${NC}"
  echo -e "${YELLOW}Vous pouvez restaurer les fichiers depuis ${BACKUP_DIR} si nécessaire.${NC}"
  exit 1
fi

echo -e "${GREEN}Migration complète et validée!${NC}"
echo -e "${YELLOW}N'oubliez pas de mettre à jour la documentation Swagger et d'informer les équipes concernées.${NC}"
