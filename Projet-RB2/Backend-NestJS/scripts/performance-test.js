/**
 * Script de test de performance pour le système de recommandation
 * 
 * Ce script teste les performances des principales API du système de recommandation
 * en simulant plusieurs utilisateurs simultanés.
 * 
 * Utilisation:
 * node performance-test.js --users=100 --duration=60 --target=http://localhost:3000
 */

const axios = require('axios');
const { performance } = require('perf_hooks');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const fs = require('fs');
const path = require('path');

// Analyser les arguments de la ligne de commande
const args = process.argv.slice(2).reduce((acc, arg) => {
  const [key, value] = arg.replace('--', '').split('=');
  acc[key] = value;
  return acc;
}, {});

const NUM_USERS = parseInt(args.users || 10);
const DURATION_SECONDS = parseInt(args.duration || 30);
const TARGET_URL = args.target || 'http://localhost:3000';
const REPORT_DIR = args.report || 'performance-reports';

// Créer le dossier de rapports s'il n'existe pas
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true });
}

// Endpoints à tester
const ENDPOINTS = [
  {
    name: 'getRecommendations',
    method: 'GET',
    url: '/api/recommendations',
    params: { type: 'RETREAT', limit: 10 },
  },
  {
    name: 'getEnhancedExplanation',
    method: 'GET',
    url: '/api/recommendations/explanations/RETREAT/test-retreat-id',
    params: { includeVisualizations: true, detailLevel: 'STANDARD' },
  },
  {
    name: 'recordFeedback',
    method: 'POST',
    url: '/api/recommendations/feedback',
    data: {
      recommendationId: 'test-retreat-id',
      recommendationType: 'RETREAT',
      feedbackType: 'LIKE',
      comment: 'Great recommendation!',
      rating: 5,
    },
  },
  {
    name: 'checkModeration',
    method: 'POST',
    url: '/api/recommendations/moderation/check/RETREAT/test-retreat-id',
    data: {},
  },
  {
    name: 'trackAnalytics',
    method: 'POST',
    url: '/api/recommendations/analytics-integration/view/RETREAT/test-retreat-id',
    data: {
      metadata: {
        source: 'performance-test',
        position: 1,
      },
    },
  },
];

// Fonction pour générer un token JWT de test
function generateTestToken() {
  // Ceci est un token JWT de test, à remplacer par un vrai token en production
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.tJ8wM1U9C9ErJgKh5nGQ5QZ9Z7QJC3jXGpyxCQ0jZzs';
}

// Fonction pour exécuter un test sur un endpoint
async function testEndpoint(endpoint) {
  const token = generateTestToken();
  const startTime = performance.now();
  let success = false;
  let statusCode = 0;
  let errorMessage = '';

  try {
    const config = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      params: endpoint.params,
    };

    let response;
    if (endpoint.method === 'GET') {
      response = await axios.get(`${TARGET_URL}${endpoint.url}`, config);
    } else if (endpoint.method === 'POST') {
      response = await axios.post(`${TARGET_URL}${endpoint.url}`, endpoint.data, config);
    }

    success = true;
    statusCode = response.status;
  } catch (error) {
    success = false;
    statusCode = error.response ? error.response.status : 0;
    errorMessage = error.message;
  }

  const endTime = performance.now();
  const duration = endTime - startTime;

  return {
    endpoint: endpoint.name,
    success,
    statusCode,
    duration,
    errorMessage,
    timestamp: new Date().toISOString(),
  };
}

// Fonction pour exécuter tous les tests
async function runTests() {
  const results = [];

  for (const endpoint of ENDPOINTS) {
    const result = await testEndpoint(endpoint);
    results.push(result);
  }

  return results;
}

// Fonction pour simuler un utilisateur
async function simulateUser(userId) {
  const startTime = Date.now();
  const endTime = startTime + (DURATION_SECONDS * 1000);
  const results = [];

  while (Date.now() < endTime) {
    const testResults = await runTests();
    results.push(...testResults);

    // Pause aléatoire entre 100ms et 1000ms
    await new Promise(resolve => setTimeout(resolve, Math.random() * 900 + 100));
  }

  return {
    userId,
    results,
  };
}

// Fonction pour générer un rapport
function generateReport(allResults) {
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const reportFile = path.join(REPORT_DIR, `performance-report-${timestamp}.json`);
  const summaryFile = path.join(REPORT_DIR, `performance-summary-${timestamp}.json`);

  // Enregistrer les résultats bruts
  fs.writeFileSync(reportFile, JSON.stringify(allResults, null, 2));

  // Calculer les statistiques
  const stats = {};
  
  ENDPOINTS.forEach(endpoint => {
    const endpointResults = allResults.flatMap(user => 
      user.results.filter(result => result.endpoint === endpoint.name)
    );
    
    const durations = endpointResults.map(result => result.duration);
    const successCount = endpointResults.filter(result => result.success).length;
    const totalCount = endpointResults.length;
    
    stats[endpoint.name] = {
      totalRequests: totalCount,
      successRate: (successCount / totalCount) * 100,
      avgResponseTime: durations.reduce((sum, duration) => sum + duration, 0) / totalCount,
      minResponseTime: Math.min(...durations),
      maxResponseTime: Math.max(...durations),
      p95ResponseTime: calculatePercentile(durations, 95),
      p99ResponseTime: calculatePercentile(durations, 99),
    };
  });

  // Statistiques globales
  const allDurations = allResults.flatMap(user => 
    user.results.map(result => result.duration)
  );
  
  const allSuccessCount = allResults.flatMap(user => 
    user.results.filter(result => result.success)
  ).length;
  
  const allTotalCount = allResults.flatMap(user => user.results).length;

  stats.global = {
    totalUsers: NUM_USERS,
    totalRequests: allTotalCount,
    successRate: (allSuccessCount / allTotalCount) * 100,
    avgResponseTime: allDurations.reduce((sum, duration) => sum + duration, 0) / allTotalCount,
    minResponseTime: Math.min(...allDurations),
    maxResponseTime: Math.max(...allDurations),
    p95ResponseTime: calculatePercentile(allDurations, 95),
    p99ResponseTime: calculatePercentile(allDurations, 99),
    durationSeconds: DURATION_SECONDS,
    requestsPerSecond: allTotalCount / DURATION_SECONDS,
  };

  // Enregistrer le résumé
  fs.writeFileSync(summaryFile, JSON.stringify(stats, null, 2));

  return {
    reportFile,
    summaryFile,
    stats,
  };
}

// Fonction pour calculer un percentile
function calculatePercentile(values, percentile) {
  if (values.length === 0) return 0;
  
  const sorted = [...values].sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index];
}

// Fonction principale
async function main() {
  console.log(`Starting performance test with ${NUM_USERS} users for ${DURATION_SECONDS} seconds`);
  console.log(`Target URL: ${TARGET_URL}`);
  console.log('Endpoints to test:');
  ENDPOINTS.forEach(endpoint => {
    console.log(`- ${endpoint.method} ${endpoint.url}`);
  });

  const startTime = Date.now();

  if (NUM_USERS <= 5) {
    // Exécution séquentielle pour un petit nombre d'utilisateurs
    const allResults = [];
    for (let i = 0; i < NUM_USERS; i++) {
      console.log(`Simulating user ${i + 1}/${NUM_USERS}...`);
      const userResults = await simulateUser(i + 1);
      allResults.push(userResults);
    }

    const report = generateReport(allResults);
    console.log(`Test completed in ${(Date.now() - startTime) / 1000} seconds`);
    console.log(`Report saved to ${report.reportFile}`);
    console.log(`Summary saved to ${report.summaryFile}`);
    console.log('Summary:');
    console.log(JSON.stringify(report.stats.global, null, 2));
  } else {
    // Exécution parallèle avec des worker threads pour un grand nombre d'utilisateurs
    const workers = [];
    const allResults = [];

    for (let i = 0; i < NUM_USERS; i++) {
      const worker = new Worker(__filename, {
        workerData: { userId: i + 1, isWorker: true },
      });

      worker.on('message', (userResults) => {
        allResults.push(userResults);
        console.log(`User ${userResults.userId}/${NUM_USERS} completed`);

        if (allResults.length === NUM_USERS) {
          const report = generateReport(allResults);
          console.log(`Test completed in ${(Date.now() - startTime) / 1000} seconds`);
          console.log(`Report saved to ${report.reportFile}`);
          console.log(`Summary saved to ${report.summaryFile}`);
          console.log('Summary:');
          console.log(JSON.stringify(report.stats.global, null, 2));
        }
      });

      worker.on('error', (error) => {
        console.error(`Worker error: ${error}`);
      });

      workers.push(worker);
    }
  }
}

// Code pour les worker threads
if (!isMainThread && workerData && workerData.isWorker) {
  (async () => {
    const userResults = await simulateUser(workerData.userId);
    parentPort.postMessage(userResults);
  })();
} else if (isMainThread) {
  main().catch(error => {
    console.error(`Error in main thread: ${error}`);
  });
}
