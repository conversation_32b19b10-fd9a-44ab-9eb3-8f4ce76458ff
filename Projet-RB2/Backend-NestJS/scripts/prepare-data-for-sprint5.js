/**
 * Script pour préparer les données collectées pendant le Sprint 4
 * pour leur utilisation dans le Sprint 5 (Apprentissage Continu et Personnalisation Avancée)
 * 
 * Ce script :
 * 1. Extrait les données de feedback et d'interactions
 * 2. Nettoie et transforme les données
 * 3. Génère des ensembles de données pour l'entraînement des modèles
 * 4. Exporte les données dans des formats adaptés à l'apprentissage automatique
 * 
 * Utilisation :
 * node prepare-data-for-sprint5.js --output=data/sprint5 --days=30
 */

const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma/client');
const { program } = require('commander');
const { parse } = require('json2csv');

// Configuration du script
program
  .option('--output <path>', 'Chemin de sortie pour les données préparées', 'data/sprint5')
  .option('--days <number>', 'Nombre de jours de données à extraire', '30')
  .option('--format <format>', 'Format de sortie (csv, json)', 'csv')
  .option('--split <ratio>', 'Ratio de division train/test (0.8 = 80% train, 20% test)', '0.8')
  .parse(process.argv);

const options = program.opts();

// Initialisation du client Prisma
const prisma = new PrismaClient();

// Fonction principale
async function main() {
  console.log('Préparation des données pour le Sprint 5...');
  
  // Créer le dossier de sortie s'il n'existe pas
  const outputDir = path.resolve(options.output);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Calculer la date de début pour l'extraction des données
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(options.days));
  
  console.log(`Extraction des données depuis ${startDate.toISOString()}...`);
  
  try {
    // 1. Extraire les données de feedback
    const feedbackData = await extractFeedbackData(startDate);
    console.log(`${feedbackData.length} enregistrements de feedback extraits.`);
    
    // 2. Extraire les données d'interactions
    const interactionData = await extractInteractionData(startDate);
    console.log(`${interactionData.length} enregistrements d'interactions extraits.`);
    
    // 3. Extraire les données utilisateur
    const userData = await extractUserData();
    console.log(`${userData.length} enregistrements utilisateur extraits.`);
    
    // 4. Extraire les données de recommandation
    const recommendationData = await extractRecommendationData();
    console.log(`${recommendationData.length} enregistrements de recommandation extraits.`);
    
    // 5. Nettoyer et transformer les données
    const cleanedData = cleanAndTransformData(feedbackData, interactionData, userData, recommendationData);
    console.log('Données nettoyées et transformées.');
    
    // 6. Générer des ensembles de données pour l'entraînement
    const { trainingData, testingData } = generateDatasets(cleanedData, parseFloat(options.split));
    console.log(`Ensembles de données générés : ${trainingData.length} entraînement, ${testingData.length} test.`);
    
    // 7. Exporter les données
    await exportData(trainingData, testingData, outputDir, options.format);
    console.log(`Données exportées dans ${outputDir}.`);
    
    console.log('Préparation des données terminée avec succès.');
  } catch (error) {
    console.error('Erreur lors de la préparation des données :', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Extrait les données de feedback depuis la base de données
 */
async function extractFeedbackData(startDate) {
  try {
    const feedbacks = await prisma.recommendationFeedback.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            preferences: true,
          },
        },
      },
    });
    
    return feedbacks;
  } catch (error) {
    console.error('Erreur lors de l\'extraction des données de feedback :', error);
    
    // Si la base de données n'est pas disponible, utiliser des données de test
    console.log('Utilisation de données de feedback de test...');
    return generateMockFeedbackData();
  }
}

/**
 * Extrait les données d'interactions depuis la base de données
 */
async function extractInteractionData(startDate) {
  try {
    const interactions = await prisma.userInteraction.findMany({
      where: {
        timestamp: {
          gte: startDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
          },
        },
      },
    });
    
    return interactions;
  } catch (error) {
    console.error('Erreur lors de l\'extraction des données d\'interactions :', error);
    
    // Si la base de données n'est pas disponible, utiliser des données de test
    console.log('Utilisation de données d\'interactions de test...');
    return generateMockInteractionData();
  }
}

/**
 * Extrait les données utilisateur depuis la base de données
 */
async function extractUserData() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        preferences: true,
        interests: true,
        demographics: true,
      },
    });
    
    return users;
  } catch (error) {
    console.error('Erreur lors de l\'extraction des données utilisateur :', error);
    
    // Si la base de données n'est pas disponible, utiliser des données de test
    console.log('Utilisation de données utilisateur de test...');
    return generateMockUserData();
  }
}

/**
 * Extrait les données de recommandation depuis la base de données
 */
async function extractRecommendationData() {
  try {
    // Extraire les retraites
    const retreats = await prisma.retreat.findMany({
      select: {
        id: true,
        title: true,
        description: true,
        categories: true,
        tags: true,
        location: true,
        price: true,
      },
    });
    
    // Extraire les cours
    const courses = await prisma.course.findMany({
      select: {
        id: true,
        title: true,
        description: true,
        categories: true,
        tags: true,
        instructor: true,
        price: true,
      },
    });
    
    // Combiner les données
    const recommendations = [
      ...retreats.map(retreat => ({ ...retreat, type: 'RETREAT' })),
      ...courses.map(course => ({ ...course, type: 'COURSE' })),
    ];
    
    return recommendations;
  } catch (error) {
    console.error('Erreur lors de l\'extraction des données de recommandation :', error);
    
    // Si la base de données n'est pas disponible, utiliser des données de test
    console.log('Utilisation de données de recommandation de test...');
    return generateMockRecommendationData();
  }
}

/**
 * Nettoie et transforme les données pour l'apprentissage automatique
 */
function cleanAndTransformData(feedbackData, interactionData, userData, recommendationData) {
  // Créer un dictionnaire des utilisateurs pour un accès rapide
  const userDict = userData.reduce((dict, user) => {
    dict[user.id] = user;
    return dict;
  }, {});
  
  // Créer un dictionnaire des recommandations pour un accès rapide
  const recommendationDict = recommendationData.reduce((dict, rec) => {
    dict[`${rec.type}:${rec.id}`] = rec;
    return dict;
  }, {});
  
  // Transformer les données de feedback
  const transformedFeedback = feedbackData.map(feedback => {
    const user = userDict[feedback.userId] || {};
    const recommendation = recommendationDict[`${feedback.recommendationType}:${feedback.recommendationId}`] || {};
    
    return {
      userId: feedback.userId,
      recommendationId: feedback.recommendationId,
      recommendationType: feedback.recommendationType,
      feedbackType: feedback.feedbackType,
      rating: feedback.rating || 0,
      timestamp: feedback.createdAt,
      userPreferences: user.preferences || {},
      userInterests: user.interests || [],
      userDemographics: user.demographics || {},
      recommendationCategories: recommendation.categories || [],
      recommendationTags: recommendation.tags || [],
      recommendationPrice: recommendation.price || 0,
      recommendationLocation: recommendation.location || '',
    };
  });
  
  // Transformer les données d'interactions
  const transformedInteractions = interactionData.map(interaction => {
    const user = userDict[interaction.userId] || {};
    const recommendation = recommendationDict[`${interaction.contentType}:${interaction.contentId}`] || {};
    
    return {
      userId: interaction.userId,
      recommendationId: interaction.contentId,
      recommendationType: interaction.contentType,
      interactionType: interaction.type,
      duration: interaction.duration || 0,
      timestamp: interaction.timestamp,
      userPreferences: user.preferences || {},
      userInterests: user.interests || [],
      userDemographics: user.demographics || {},
      recommendationCategories: recommendation.categories || [],
      recommendationTags: recommendation.tags || [],
      recommendationPrice: recommendation.price || 0,
      recommendationLocation: recommendation.location || '',
    };
  });
  
  // Combiner les données transformées
  return {
    feedback: transformedFeedback,
    interactions: transformedInteractions,
    users: userData,
    recommendations: recommendationData,
  };
}

/**
 * Génère des ensembles de données pour l'entraînement et le test
 */
function generateDatasets(cleanedData, splitRatio) {
  // Combiner les données de feedback et d'interactions
  const allData = [
    ...cleanedData.feedback,
    ...cleanedData.interactions,
  ];
  
  // Mélanger les données
  const shuffledData = allData.sort(() => Math.random() - 0.5);
  
  // Diviser les données en ensembles d'entraînement et de test
  const splitIndex = Math.floor(shuffledData.length * splitRatio);
  const trainingData = shuffledData.slice(0, splitIndex);
  const testingData = shuffledData.slice(splitIndex);
  
  return {
    trainingData,
    testingData,
  };
}

/**
 * Exporte les données dans le format spécifié
 */
async function exportData(trainingData, testingData, outputDir, format) {
  // Créer les sous-dossiers
  const trainingDir = path.join(outputDir, 'training');
  const testingDir = path.join(outputDir, 'testing');
  
  if (!fs.existsSync(trainingDir)) {
    fs.mkdirSync(trainingDir, { recursive: true });
  }
  
  if (!fs.existsSync(testingDir)) {
    fs.mkdirSync(testingDir, { recursive: true });
  }
  
  // Exporter les données d'entraînement
  if (format === 'csv') {
    const trainingCsv = parse(trainingData);
    fs.writeFileSync(path.join(trainingDir, 'data.csv'), trainingCsv);
  } else {
    fs.writeFileSync(path.join(trainingDir, 'data.json'), JSON.stringify(trainingData, null, 2));
  }
  
  // Exporter les données de test
  if (format === 'csv') {
    const testingCsv = parse(testingData);
    fs.writeFileSync(path.join(testingDir, 'data.csv'), testingCsv);
  } else {
    fs.writeFileSync(path.join(testingDir, 'data.json'), JSON.stringify(testingData, null, 2));
  }
  
  // Exporter les métadonnées
  const metadata = {
    trainingSize: trainingData.length,
    testingSize: testingData.length,
    format,
    createdAt: new Date().toISOString(),
    description: 'Données préparées pour le Sprint 5 (Apprentissage Continu et Personnalisation Avancée)',
  };
  
  fs.writeFileSync(path.join(outputDir, 'metadata.json'), JSON.stringify(metadata, null, 2));
}

/**
 * Génère des données de feedback de test
 */
function generateMockFeedbackData() {
  const feedbackTypes = ['LIKE', 'DISLIKE', 'SAVE', 'HIDE', 'REPORT'];
  const recommendationTypes = ['RETREAT', 'COURSE', 'VIDEO', 'ARTICLE', 'EVENT'];
  const mockData = [];
  
  for (let i = 0; i < 1000; i++) {
    mockData.push({
      id: `feedback-${i}`,
      userId: `user-${Math.floor(Math.random() * 100)}`,
      recommendationId: `rec-${Math.floor(Math.random() * 200)}`,
      recommendationType: recommendationTypes[Math.floor(Math.random() * recommendationTypes.length)],
      feedbackType: feedbackTypes[Math.floor(Math.random() * feedbackTypes.length)],
      rating: Math.floor(Math.random() * 5) + 1,
      comment: Math.random() > 0.7 ? `Comment ${i}` : null,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      user: {
        id: `user-${Math.floor(Math.random() * 100)}`,
        preferences: {
          categories: ['yoga', 'meditation', 'wellness'].filter(() => Math.random() > 0.5),
        },
      },
    });
  }
  
  return mockData;
}

/**
 * Génère des données d'interactions de test
 */
function generateMockInteractionData() {
  const interactionTypes = ['VIEW', 'CLICK', 'BOOKMARK', 'SHARE', 'PURCHASE'];
  const contentTypes = ['RETREAT', 'COURSE', 'VIDEO', 'ARTICLE', 'EVENT'];
  const mockData = [];
  
  for (let i = 0; i < 2000; i++) {
    mockData.push({
      id: `interaction-${i}`,
      userId: `user-${Math.floor(Math.random() * 100)}`,
      contentId: `rec-${Math.floor(Math.random() * 200)}`,
      contentType: contentTypes[Math.floor(Math.random() * contentTypes.length)],
      type: interactionTypes[Math.floor(Math.random() * interactionTypes.length)],
      duration: Math.random() * 300,
      timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      user: {
        id: `user-${Math.floor(Math.random() * 100)}`,
      },
    });
  }
  
  return mockData;
}

/**
 * Génère des données utilisateur de test
 */
function generateMockUserData() {
  const mockData = [];
  
  for (let i = 0; i < 100; i++) {
    mockData.push({
      id: `user-${i}`,
      preferences: {
        categories: ['yoga', 'meditation', 'wellness', 'fitness', 'nutrition'].filter(() => Math.random() > 0.5),
        priceRange: {
          min: Math.floor(Math.random() * 50) * 10,
          max: Math.floor(Math.random() * 20) * 100 + 500,
        },
        location: Math.random() > 0.5 ? 'remote' : ['Paris', 'Lyon', 'Marseille', 'Bordeaux'][Math.floor(Math.random() * 4)],
      },
      interests: ['yoga', 'meditation', 'hiking', 'cooking', 'reading', 'travel'].filter(() => Math.random() > 0.5),
      demographics: {
        age: Math.floor(Math.random() * 50) + 18,
        gender: ['male', 'female', 'non-binary'][Math.floor(Math.random() * 3)],
      },
    });
  }
  
  return mockData;
}

/**
 * Génère des données de recommandation de test
 */
function generateMockRecommendationData() {
  const categories = ['yoga', 'meditation', 'wellness', 'fitness', 'nutrition', 'mindfulness'];
  const tags = ['beginner', 'intermediate', 'advanced', 'intensive', 'relaxing', 'outdoor', 'indoor'];
  const locations = ['Paris', 'Lyon', 'Marseille', 'Bordeaux', 'Remote'];
  const mockData = [];
  
  // Générer des retraites
  for (let i = 0; i < 100; i++) {
    mockData.push({
      id: `rec-${i}`,
      type: 'RETREAT',
      title: `Retreat ${i}`,
      description: `Description for retreat ${i}`,
      categories: categories.filter(() => Math.random() > 0.5),
      tags: tags.filter(() => Math.random() > 0.5),
      location: locations[Math.floor(Math.random() * locations.length)],
      price: Math.floor(Math.random() * 20) * 100 + 500,
    });
  }
  
  // Générer des cours
  for (let i = 100; i < 200; i++) {
    mockData.push({
      id: `rec-${i}`,
      type: 'COURSE',
      title: `Course ${i}`,
      description: `Description for course ${i}`,
      categories: categories.filter(() => Math.random() > 0.5),
      tags: tags.filter(() => Math.random() > 0.5),
      instructor: `Instructor ${Math.floor(Math.random() * 10)}`,
      price: Math.floor(Math.random() * 10) * 50 + 50,
    });
  }
  
  return mockData;
}

// Exécuter le script
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
