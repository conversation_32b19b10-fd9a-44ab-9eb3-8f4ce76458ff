#!/bin/bash

# Script pour préparer le déploiement du Sprint 4

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Préparation du déploiement du Sprint 4 ===${NC}"

# Vérifier si Git est installé
if ! [ -x "$(command -v git)" ]; then
  echo -e "${RED}Erreur: Git n'est pas installé.${NC}" >&2
  exit 1
fi

# Vérifier si npm est installé
if ! [ -x "$(command -v npm)" ]; then
  echo -e "${RED}Erreur: npm n'est pas installé.${NC}" >&2
  exit 1
fi

# Créer une branche pour le déploiement
echo -e "${YELLOW}Création d'une branche pour le déploiement...${NC}"
git checkout -b deploy/sprint4
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Branche deploy/sprint4 créée avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création de la branche${NC}"
  exit 1
fi

# Installer les dépendances du backend
echo -e "${YELLOW}Installation des dépendances du backend...${NC}"
npm install
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Dépendances du backend installées avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de l'installation des dépendances du backend${NC}"
  exit 1
fi

# Exécuter les tests unitaires
echo -e "${YELLOW}Exécution des tests unitaires...${NC}"
npm run test
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests unitaires exécutés avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de l'exécution des tests unitaires${NC}"
  exit 1
fi

# Exécuter les tests d'intégration
echo -e "${YELLOW}Exécution des tests d'intégration...${NC}"
./scripts/run-integration-tests.sh
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests d'intégration exécutés avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de l'exécution des tests d'intégration${NC}"
  exit 1
fi

# Construire le backend
echo -e "${YELLOW}Construction du backend...${NC}"
npm run build
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Backend construit avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la construction du backend${NC}"
  exit 1
fi

# Aller dans le dossier du frontend
echo -e "${YELLOW}Passage au dossier du frontend...${NC}"
cd ../Front-Audrey-V1-Main-main
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Passage au dossier du frontend réussi${NC}"
else
  echo -e "${RED}✗ Erreur lors du passage au dossier du frontend${NC}"
  exit 1
fi

# Installer les dépendances du frontend
echo -e "${YELLOW}Installation des dépendances du frontend...${NC}"
npm install
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Dépendances du frontend installées avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de l'installation des dépendances du frontend${NC}"
  exit 1
fi

# Exécuter les tests du frontend
echo -e "${YELLOW}Exécution des tests du frontend...${NC}"
npm test
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests du frontend exécutés avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de l'exécution des tests du frontend${NC}"
  exit 1
fi

# Construire le frontend
echo -e "${YELLOW}Construction du frontend...${NC}"
npm run build
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Frontend construit avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la construction du frontend${NC}"
  exit 1
fi

# Revenir au dossier racine
echo -e "${YELLOW}Retour au dossier racine...${NC}"
cd ..
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Retour au dossier racine réussi${NC}"
else
  echo -e "${RED}✗ Erreur lors du retour au dossier racine${NC}"
  exit 1
fi

# Créer un dossier pour le déploiement
echo -e "${YELLOW}Création d'un dossier pour le déploiement...${NC}"
mkdir -p deploy/sprint4
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Dossier de déploiement créé avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création du dossier de déploiement${NC}"
  exit 1
fi

# Copier les fichiers du backend
echo -e "${YELLOW}Copie des fichiers du backend...${NC}"
cp -r Backend-NestJS/dist deploy/sprint4/backend
cp Backend-NestJS/package.json deploy/sprint4/backend
cp Backend-NestJS/package-lock.json deploy/sprint4/backend
cp -r Backend-NestJS/prisma deploy/sprint4/backend
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Fichiers du backend copiés avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la copie des fichiers du backend${NC}"
  exit 1
fi

# Copier les fichiers du frontend
echo -e "${YELLOW}Copie des fichiers du frontend...${NC}"
cp -r Front-Audrey-V1-Main-main/build deploy/sprint4/frontend
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Fichiers du frontend copiés avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la copie des fichiers du frontend${NC}"
  exit 1
fi

# Copier la documentation
echo -e "${YELLOW}Copie de la documentation...${NC}"
mkdir -p deploy/sprint4/docs
cp Backend-NestJS/docs/sprint4-features.md deploy/sprint4/docs
cp Front-Audrey-V1-Main-main/docs/user-guide-sprint4.md deploy/sprint4/docs
cp README-SPRINT4.md deploy/sprint4/docs
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Documentation copiée avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la copie de la documentation${NC}"
  exit 1
fi

# Créer un fichier README pour le déploiement
echo -e "${YELLOW}Création d'un fichier README pour le déploiement...${NC}"
cat > deploy/sprint4/README.md << EOF
# Déploiement du Sprint 4

Ce dossier contient les fichiers nécessaires pour déployer le Sprint 4 du système de recommandation de Retreat And Be.

## Structure du dossier

- \`backend/\` : Fichiers du backend (NestJS)
- \`frontend/\` : Fichiers du frontend (React)
- \`docs/\` : Documentation

## Instructions de déploiement

### Backend

1. Accédez au dossier \`backend\`
2. Installez les dépendances : \`npm install\`
3. Configurez les variables d'environnement dans un fichier \`.env\`
4. Appliquez les migrations Prisma : \`npx prisma migrate deploy\`
5. Démarrez le serveur : \`npm run start:prod\`

### Frontend

1. Déployez les fichiers du dossier \`frontend\` sur un serveur web statique
2. Configurez le serveur web pour rediriger toutes les requêtes non-API vers \`index.html\`

## Documentation

- \`sprint4-features.md\` : Documentation des fonctionnalités pour les développeurs
- \`user-guide-sprint4.md\` : Guide utilisateur des nouvelles fonctionnalités
- \`README-SPRINT4.md\` : Présentation générale du Sprint 4
EOF
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Fichier README créé avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création du fichier README${NC}"
  exit 1
fi

# Créer une archive pour le déploiement
echo -e "${YELLOW}Création d'une archive pour le déploiement...${NC}"
cd deploy
tar -czf sprint4.tar.gz sprint4
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Archive créée avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la création de l'archive${NC}"
  exit 1
fi

# Revenir au dossier racine
cd ..

echo -e "${GREEN}=== Préparation du déploiement terminée avec succès ===${NC}"
echo -e "${YELLOW}L'archive de déploiement est disponible dans le dossier deploy/sprint4.tar.gz${NC}"
echo -e "${YELLOW}Pour déployer, suivez les instructions dans le fichier deploy/sprint4/README.md${NC}"
exit 0
