#!/bin/bash

# Script pour exécuter les tests d'intégration du Sprint 4

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Exécution des tests d'intégration du Sprint 4 ===${NC}"

# Vérifier si Jest est installé
if ! [ -x "$(command -v jest)" ]; then
  echo -e "${RED}Erreur: Jest n'est pas installé.${NC}" >&2
  echo -e "${YELLOW}Installation de Jest...${NC}"
  npm install --save-dev jest
fi

# Créer le dossier de rapports s'il n'existe pas
mkdir -p reports/integration

# Exécuter les tests d'intégration
echo -e "${YELLOW}Exécution des tests d'intégration...${NC}"

# Test du service de feedback
echo -e "${YELLOW}Test du service de feedback...${NC}"
npx jest --config=jest.config.js test/integration/feedback.integration.spec.ts --verbose
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests du service de feedback réussis${NC}"
else
  echo -e "${RED}✗ Tests du service de feedback échoués${NC}"
  FAILED=1
fi

# Test du service d'explications améliorées
echo -e "${YELLOW}Test du service d'explications améliorées...${NC}"
npx jest --config=jest.config.js test/integration/enhanced-explanation.integration.spec.ts --verbose
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests du service d'explications améliorées réussis${NC}"
else
  echo -e "${RED}✗ Tests du service d'explications améliorées échoués${NC}"
  FAILED=1
fi

# Test du service de modération
echo -e "${YELLOW}Test du service de modération...${NC}"
npx jest --config=jest.config.js test/integration/moderation-integration.integration.spec.ts --verbose
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests du service de modération réussis${NC}"
else
  echo -e "${RED}✗ Tests du service de modération échoués${NC}"
  FAILED=1
fi

# Test du service d'analytics
echo -e "${YELLOW}Test du service d'analytics...${NC}"
npx jest --config=jest.config.js test/integration/analytics-integration.integration.spec.ts --verbose
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Tests du service d'analytics réussis${NC}"
else
  echo -e "${RED}✗ Tests du service d'analytics échoués${NC}"
  FAILED=1
fi

# Générer un rapport de couverture
echo -e "${YELLOW}Génération du rapport de couverture...${NC}"
npx jest --config=jest.config.js test/integration --coverage --coverageDirectory=reports/integration/coverage
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Rapport de couverture généré avec succès${NC}"
else
  echo -e "${RED}✗ Erreur lors de la génération du rapport de couverture${NC}"
  FAILED=1
fi

# Vérifier si tous les tests ont réussi
if [ "$FAILED" == "1" ]; then
  echo -e "${RED}✗ Certains tests ont échoué${NC}"
  exit 1
else
  echo -e "${GREEN}✓ Tous les tests ont réussi${NC}"
  exit 0
fi
