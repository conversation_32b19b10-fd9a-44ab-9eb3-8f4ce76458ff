#!/bin/bash

# Script pour exécuter les tests de performance du système de recommandation

# Couleurs pour les messages
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Tests de performance du système de recommandation ===${NC}"

# Vérifier si Node.js est installé
if ! [ -x "$(command -v node)" ]; then
  echo -e "${RED}Erreur: Node.js n'est pas installé.${NC}" >&2
  exit 1
fi

# Vérifier si axios est installé
if ! [ -d "node_modules/axios" ]; then
  echo -e "${YELLOW}Installation d'axios...${NC}"
  npm install axios
fi

# Créer le dossier de rapports s'il n'existe pas
mkdir -p performance-reports

# Fonction pour exécuter un test de performance
run_test() {
  local users=$1
  local duration=$2
  local target=$3
  local name=$4

  echo -e "${YELLOW}Exécution du test de performance : ${name}${NC}"
  echo -e "${YELLOW}Utilisateurs : ${users}, Durée : ${duration}s, Cible : ${target}${NC}"

  node scripts/performance-test.js --users=${users} --duration=${duration} --target=${target} --report=performance-reports/${name}

  if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Test de performance terminé avec succès${NC}"
  else
    echo -e "${RED}✗ Erreur lors de l'exécution du test de performance${NC}"
    exit 1
  fi
}

# Fonction pour générer un rapport comparatif
generate_comparative_report() {
  echo -e "${YELLOW}Génération du rapport comparatif...${NC}"

  # Trouver les derniers fichiers de résumé pour chaque test
  local baseline_summary=$(ls -t performance-reports/baseline/performance-summary-*.json | head -n 1)
  local load_summary=$(ls -t performance-reports/load/performance-summary-*.json | head -n 1)
  local stress_summary=$(ls -t performance-reports/stress/performance-summary-*.json | head -n 1)

  if [ -z "$baseline_summary" ] || [ -z "$load_summary" ] || [ -z "$stress_summary" ]; then
    echo -e "${RED}Erreur: Impossible de trouver tous les fichiers de résumé${NC}"
    exit 1
  fi

  # Créer un rapport comparatif en HTML
  cat > performance-reports/comparative-report.html << EOF
<!DOCTYPE html>
<html>
<head>
  <title>Rapport de performance comparatif</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1, h2 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .chart-container { width: 100%; height: 400px; margin-bottom: 20px; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <h1>Rapport de performance comparatif</h1>
  <p>Date de génération : $(date)</p>

  <h2>Résumé global</h2>
  <table id="globalTable">
    <tr>
      <th>Métrique</th>
      <th>Baseline (${users_baseline} utilisateurs)</th>
      <th>Charge (${users_load} utilisateurs)</th>
      <th>Stress (${users_stress} utilisateurs)</th>
    </tr>
  </table>

  <h2>Temps de réponse moyen par endpoint</h2>
  <div class="chart-container">
    <canvas id="avgResponseTimeChart"></canvas>
  </div>

  <h2>Taux de succès par endpoint</h2>
  <div class="chart-container">
    <canvas id="successRateChart"></canvas>
  </div>

  <script>
    // Charger les données des fichiers JSON
    const baselineData = JSON.parse('$(cat $baseline_summary)');
    const loadData = JSON.parse('$(cat $load_summary)');
    const stressData = JSON.parse('$(cat $stress_summary)');

    // Remplir le tableau global
    const globalTable = document.getElementById('globalTable');
    const metrics = [
      { name: 'Requêtes totales', key: 'totalRequests' },
      { name: 'Taux de succès (%)', key: 'successRate' },
      { name: 'Temps de réponse moyen (ms)', key: 'avgResponseTime' },
      { name: 'Temps de réponse min (ms)', key: 'minResponseTime' },
      { name: 'Temps de réponse max (ms)', key: 'maxResponseTime' },
      { name: 'Temps de réponse P95 (ms)', key: 'p95ResponseTime' },
      { name: 'Temps de réponse P99 (ms)', key: 'p99ResponseTime' },
      { name: 'Requêtes par seconde', key: 'requestsPerSecond' },
    ];

    metrics.forEach(metric => {
      const row = globalTable.insertRow();
      const cell1 = row.insertCell(0);
      const cell2 = row.insertCell(1);
      const cell3 = row.insertCell(2);
      const cell4 = row.insertCell(3);

      cell1.textContent = metric.name;
      cell2.textContent = baselineData.global[metric.key].toFixed(2);
      cell3.textContent = loadData.global[metric.key].toFixed(2);
      cell4.textContent = stressData.global[metric.key].toFixed(2);
    });

    // Créer le graphique des temps de réponse moyens
    const endpoints = Object.keys(baselineData).filter(key => key !== 'global');
    const avgResponseTimeCtx = document.getElementById('avgResponseTimeChart').getContext('2d');
    new Chart(avgResponseTimeCtx, {
      type: 'bar',
      data: {
        labels: endpoints,
        datasets: [
          {
            label: 'Baseline',
            data: endpoints.map(endpoint => baselineData[endpoint].avgResponseTime),
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
          },
          {
            label: 'Charge',
            data: endpoints.map(endpoint => loadData[endpoint].avgResponseTime),
            backgroundColor: 'rgba(255, 206, 86, 0.5)',
          },
          {
            label: 'Stress',
            data: endpoints.map(endpoint => stressData[endpoint].avgResponseTime),
            backgroundColor: 'rgba(255, 99, 132, 0.5)',
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Temps de réponse moyen (ms)'
            }
          }
        }
      }
    });

    // Créer le graphique des taux de succès
    const successRateCtx = document.getElementById('successRateChart').getContext('2d');
    new Chart(successRateCtx, {
      type: 'bar',
      data: {
        labels: endpoints,
        datasets: [
          {
            label: 'Baseline',
            data: endpoints.map(endpoint => baselineData[endpoint].successRate),
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
          },
          {
            label: 'Charge',
            data: endpoints.map(endpoint => loadData[endpoint].successRate),
            backgroundColor: 'rgba(255, 206, 86, 0.5)',
          },
          {
            label: 'Stress',
            data: endpoints.map(endpoint => stressData[endpoint].successRate),
            backgroundColor: 'rgba(255, 99, 132, 0.5)',
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            title: {
              display: true,
              text: 'Taux de succès (%)'
            }
          }
        }
      }
    });
  </script>
</body>
</html>
EOF

  echo -e "${GREEN}✓ Rapport comparatif généré avec succès : performance-reports/comparative-report.html${NC}"
}

# Paramètres des tests
TARGET_URL=${1:-"http://localhost:3000"}
users_baseline=5
users_load=20
users_stress=50
duration_baseline=30
duration_load=60
duration_stress=60

# Exécuter les tests
run_test $users_baseline $duration_baseline $TARGET_URL "baseline"
run_test $users_load $duration_load $TARGET_URL "load"
run_test $users_stress $duration_stress $TARGET_URL "stress"

# Générer le rapport comparatif
generate_comparative_report

echo -e "${GREEN}=== Tests de performance terminés avec succès ===${NC}"
echo -e "${YELLOW}Les rapports sont disponibles dans le dossier performance-reports${NC}"
echo -e "${YELLOW}Rapport comparatif : performance-reports/comparative-report.html${NC}"
exit 0
