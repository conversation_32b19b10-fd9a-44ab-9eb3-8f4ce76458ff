#!/usr/bin/env node

/**
 * Script pour planifier et envoyer automatiquement les rapports de performance du système de recommandation
 * 
 * Ce script configure une tâche cron pour exécuter le script de rapport de performance
 * à intervalles réguliers et envoyer les résultats par email aux parties prenantes.
 * 
 * Usage: node schedule-recommendation-reports.js [options]
 * 
 * Options:
 *   --schedule=<cron>      Expression cron pour la planification [default: "0 0 * * 0"] (chaque dimanche à minuit)
 *   --recipients=<emails>  Liste d'emails séparés par des virgules
 *   --format=<format>      Format du rapport (json, pdf, html) [default: html]
 *   --days=<number>        Nombre de jours d'historique à analyser [default: 7]
 *   --detailed             Inclure des informations détaillées
 *   --help                 Afficher l'aide
 */

const fs = require('fs');
const path = require('path');
const { program } = require('commander');
const nodemailer = require('nodemailer');
const cron = require('node-cron');
const { spawn } = require('child_process');
const chalk = require('chalk');

// Configuration par défaut
const DEFAULT_SCHEDULE = '0 0 * * 0'; // Chaque dimanche à minuit
const DEFAULT_FORMAT = 'html';
const DEFAULT_DAYS = 7;
const REPORTS_DIR = path.join(process.cwd(), 'reports');

// Définir les options de ligne de commande
program
  .option('--schedule <cron>', 'Expression cron pour la planification', DEFAULT_SCHEDULE)
  .option('--recipients <emails>', 'Liste d\'emails séparés par des virgules')
  .option('--format <format>', 'Format du rapport (json, pdf, html)', DEFAULT_FORMAT)
  .option('--days <number>', 'Nombre de jours d\'historique à analyser', DEFAULT_DAYS)
  .option('--detailed', 'Inclure des informations détaillées')
  .option('--help', 'Afficher l\'aide');

program.parse(process.argv);

const options = program.opts();

// Afficher l'aide si demandé
if (options.help) {
  console.log(`
Usage: node schedule-recommendation-reports.js [options]

Options:
  --schedule=<cron>      Expression cron pour la planification [default: "0 0 * * 0"] (chaque dimanche à minuit)
  --recipients=<emails>  Liste d'emails séparés par des virgules
  --format=<format>      Format du rapport (json, pdf, html) [default: html]
  --days=<number>        Nombre de jours d'historique à analyser [default: 7]
  --detailed             Inclure des informations détaillées
  --help                 Afficher l'aide
  `);
  process.exit(0);
}

// Vérifier que les destinataires sont spécifiés
if (!options.recipients) {
  console.error(chalk.red('Erreur: Vous devez spécifier au moins un destinataire avec --recipients'));
  process.exit(1);
}

// Créer le répertoire de rapports s'il n'existe pas
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

// Configurer le transporteur d'email
// Note: Dans un environnement de production, utilisez des variables d'environnement pour les informations sensibles
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.example.com',
  port: process.env.SMTP_PORT || 587,
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASS || 'password',
  },
});

// Fonction pour générer le rapport
async function generateReport() {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue('Génération du rapport de performance...'));
    
    const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0];
    const outputPath = path.join(REPORTS_DIR, `recommendation-performance-report-${timestamp}.${options.format}`);
    
    // Construire les arguments pour le script de rapport
    const args = [
      path.join(__dirname, 'recommendation-performance-report.js'),
      `--format=${options.format}`,
      `--output=${outputPath}`,
      `--days=${options.days}`,
    ];
    
    if (options.detailed) {
      args.push('--detailed');
    }
    
    // Exécuter le script de rapport
    const reportProcess = spawn('node', args);
    
    let stdout = '';
    let stderr = '';
    
    reportProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    reportProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    reportProcess.on('close', (code) => {
      if (code === 0) {
        console.log(chalk.green('Rapport généré avec succès'));
        resolve({
          path: outputPath,
          stdout,
          stderr,
        });
      } else {
        console.error(chalk.red(`Erreur lors de la génération du rapport (code ${code})`));
        console.error(stderr);
        reject(new Error(`Erreur lors de la génération du rapport (code ${code})`));
      }
    });
  });
}

// Fonction pour envoyer le rapport par email
async function sendReportByEmail(reportPath) {
  console.log(chalk.blue('Envoi du rapport par email...'));
  
  // Lire le contenu du rapport
  const reportContent = fs.readFileSync(reportPath);
  
  // Préparer les options de l'email
  const mailOptions = {
    from: process.env.EMAIL_FROM || '<EMAIL>',
    to: options.recipients,
    subject: `Rapport de performance du système de recommandation - ${new Date().toLocaleDateString()}`,
    text: `Veuillez trouver ci-joint le rapport de performance du système de recommandation généré le ${new Date().toLocaleString()}.`,
    attachments: [
      {
        filename: path.basename(reportPath),
        content: reportContent,
      },
    ],
  };
  
  // Si le format est HTML, ajouter le contenu HTML dans le corps de l'email
  if (options.format === 'html') {
    mailOptions.html = reportContent.toString();
  }
  
  // Envoyer l'email
  try {
    const info = await transporter.sendMail(mailOptions);
    console.log(chalk.green(`Email envoyé: ${info.messageId}`));
    return info;
  } catch (error) {
    console.error(chalk.red(`Erreur lors de l'envoi de l'email: ${error.message}`));
    throw error;
  }
}

// Fonction principale pour générer et envoyer le rapport
async function generateAndSendReport() {
  try {
    console.log(chalk.blue.bold('=== Génération et envoi du rapport de performance ==='));
    console.log(chalk.blue(`Date: ${new Date().toLocaleString()}`));
    
    // Générer le rapport
    const report = await generateReport();
    
    // Envoyer le rapport par email
    await sendReportByEmail(report.path);
    
    console.log(chalk.green.bold('Rapport généré et envoyé avec succès!'));
  } catch (error) {
    console.error(chalk.red(`Erreur: ${error.message}`));
  }
}

// Fonction pour valider l'expression cron
function isValidCron(cronExpression) {
  return cron.validate(cronExpression);
}

// Vérifier que l'expression cron est valide
if (!isValidCron(options.schedule)) {
  console.error(chalk.red(`Erreur: Expression cron invalide: ${options.schedule}`));
  process.exit(1);
}

// Planifier la tâche
console.log(chalk.blue.bold('=== Planification des rapports de performance ==='));
console.log(chalk.blue(`Planification: ${options.schedule}`));
console.log(chalk.blue(`Destinataires: ${options.recipients}`));
console.log(chalk.blue(`Format: ${options.format}`));
console.log(chalk.blue(`Jours d'historique: ${options.days}`));
console.log(chalk.blue(`Détaillé: ${options.detailed ? 'Oui' : 'Non'}`));

// Exécuter immédiatement si demandé
if (process.argv.includes('--run-now')) {
  console.log(chalk.blue('Exécution immédiate demandée...'));
  generateAndSendReport();
}

// Planifier la tâche cron
cron.schedule(options.schedule, () => {
  console.log(chalk.blue(`Exécution planifiée déclenchée: ${new Date().toLocaleString()}`));
  generateAndSendReport();
});

console.log(chalk.green('Tâche planifiée avec succès. Le script reste en cours d\'exécution.'));
console.log(chalk.yellow('Appuyez sur Ctrl+C pour arrêter.'));

// Gérer les signaux pour une sortie propre
process.on('SIGINT', () => {
  console.log(chalk.yellow('\nArrêt du planificateur...'));
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\nArrêt du planificateur...'));
  process.exit(0);
});

// Pour exécuter ce script comme un service, utilisez PM2 ou un gestionnaire de processus similaire
