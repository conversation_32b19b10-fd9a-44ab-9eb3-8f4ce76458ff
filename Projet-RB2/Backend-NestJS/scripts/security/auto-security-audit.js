#!/usr/bin/env node

/**
 * Script d'audit de sécurité automatique
 *
 * Ce script effectue une analyse statique du code pour détecter les vulnérabilités
 * potentielles et génère un rapport détaillé.
 *
 * Usage:
 *   node auto-security-audit.js [options]
 *
 * Options:
 *   --path=<path>            Chemin du répertoire à analyser (défaut: répertoire courant)
 *   --output=<path>          Chemin du fichier de sortie (défaut: ./security-audit-report-<timestamp>.json)
 *   --format=json|html|pdf   Format de sortie (défaut: json)
 *   --severity=low|medium|high|critical  Niveau de sévérité minimum à signaler (défaut: low)
 *   --fix                    Tenter de corriger automatiquement les problèmes détectés
 *   --ignore-file=<path>     Fichier contenant les patterns à ignorer
 *   --scan-deps              Analyser également les dépendances
 *   --help                   Afficher l'aide
 */

const fs = require('fs');
const path = require('path');
const { execSync, exec } = require('child_process');
const { program } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const Table = require('cli-table3');
const PDFDocument = require('pdfkit');
const { promisify } = require('util');
const glob = promisify(require('glob'));
const { ESLint } = require('eslint');

// Configuration par défaut
const DEFAULT_OUTPUT_FORMAT = 'json';
const DEFAULT_SEVERITY = 'low';
const TIMESTAMP = new Date().toISOString().replace(/:/g, '-').split('.')[0];
const REPORTS_DIR = path.join(process.cwd(), 'security-reports');

// Définir les options de ligne de commande
program
  .option('--path <path>', 'Chemin du répertoire à analyser', process.cwd())
  .option('--output <path>', 'Chemin du fichier de sortie')
  .option('--format <format>', 'Format de sortie (json, html, pdf)', DEFAULT_OUTPUT_FORMAT)
  .option('--severity <level>', 'Niveau de sévérité minimum à signaler (low, medium, high, critical)', DEFAULT_SEVERITY)
  .option('--fix', 'Tenter de corriger automatiquement les problèmes détectés')
  .option('--ignore-file <path>', 'Fichier contenant les patterns à ignorer')
  .option('--scan-deps', 'Analyser également les dépendances')
  .option('--help', 'Afficher l\'aide');

program.parse(process.argv);

const options = program.opts();

// Afficher l'aide si demandé
if (options.help) {
  console.log(`
Usage: node auto-security-audit.js [options]

Options:
  --path=<path>            Chemin du répertoire à analyser (défaut: répertoire courant)
  --output=<path>          Chemin du fichier de sortie (défaut: ./security-audit-report-<timestamp>.json)
  --format=json|html|pdf   Format de sortie (défaut: json)
  --severity=low|medium|high|critical  Niveau de sévérité minimum à signaler (défaut: low)
  --fix                    Tenter de corriger automatiquement les problèmes détectés
  --ignore-file=<path>     Fichier contenant les patterns à ignorer
  --scan-deps              Analyser également les dépendances
  --help                   Afficher l'aide
  `);
  process.exit(0);
}

// Créer le répertoire de rapports s'il n'existe pas
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

// Déterminer le chemin de sortie
const format = options.format.toLowerCase();
const defaultFilename = `security-audit-report-${TIMESTAMP}.${format}`;
const outputPath = options.output || path.join(REPORTS_DIR, defaultFilename);

// Niveaux de sévérité
const SEVERITY_LEVELS = {
  low: 0,
  medium: 1,
  high: 2,
  critical: 3
};

// Vérifier que le niveau de sévérité est valide
if (!SEVERITY_LEVELS.hasOwnProperty(options.severity.toLowerCase())) {
  console.error(chalk.red(`Niveau de sévérité invalide: ${options.severity}`));
  console.error(chalk.red('Les niveaux valides sont: low, medium, high, critical'));
  process.exit(1);
}

// Convertir le niveau de sévérité en valeur numérique
const minSeverityLevel = SEVERITY_LEVELS[options.severity.toLowerCase()];

/**
 * Fonction principale
 */
async function main() {
  try {
    console.log(chalk.blue.bold('=== Audit de Sécurité Automatique ==='));
    console.log(chalk.blue(`Date: ${new Date().toLocaleString()}`));
    console.log(chalk.blue(`Répertoire analysé: ${options.path}`));
    console.log(chalk.blue(`Format de sortie: ${format}`));
    console.log(chalk.blue(`Niveau de sévérité minimum: ${options.severity}`));
    console.log(chalk.blue(`Fichier de sortie: ${outputPath}`));
    console.log('');

    // Vérifier les dépendances
    const spinner = ora('Vérification des dépendances...').start();
    const dependencyIssues = await checkDependencies();
    spinner.succeed(`${dependencyIssues.length} problèmes de dépendances détectés`);

    // Analyser le code avec ESLint
    spinner.text = 'Analyse statique du code avec ESLint...';
    spinner.start();
    const eslintIssues = await runEslintAnalysis();
    spinner.succeed(`${eslintIssues.length} problèmes ESLint détectés`);

    // Rechercher les secrets exposés
    spinner.text = 'Recherche de secrets exposés...';
    spinner.start();
    const secretsIssues = await findExposedSecrets();
    spinner.succeed(`${secretsIssues.length} secrets potentiellement exposés détectés`);

    // Vérifier les problèmes de configuration
    spinner.text = 'Vérification des problèmes de configuration...';
    spinner.start();
    const configIssues = await checkConfigurationIssues();
    spinner.succeed(`${configIssues.length} problèmes de configuration détectés`);

    // Combiner tous les problèmes
    const allIssues = [
      ...dependencyIssues,
      ...eslintIssues,
      ...secretsIssues,
      ...configIssues
    ];

    // Filtrer par niveau de sévérité
    const filteredIssues = allIssues.filter(issue =>
      SEVERITY_LEVELS[issue.severity.toLowerCase()] >= minSeverityLevel
    );

    // Générer le rapport
    spinner.text = 'Génération du rapport...';
    spinner.start();
    await generateReport(filteredIssues);
    spinner.succeed(`Rapport généré avec succès: ${outputPath}`);

    // Afficher un résumé
    displaySummary(filteredIssues);

    // Corriger automatiquement si demandé
    if (options.fix) {
      spinner.text = 'Correction automatique des problèmes...';
      spinner.start();
      const fixedCount = await autoFix(filteredIssues);
      spinner.succeed(`${fixedCount} problèmes corrigés automatiquement`);
    }

  } catch (error) {
    console.error(chalk.red(`Erreur: ${error.message}`));
    process.exit(1);
  }
}

/**
 * Vérifier les dépendances pour les vulnérabilités
 */
async function checkDependencies() {
  try {
    // Utiliser npm audit pour vérifier les dépendances
    const auditOutput = execSync('npm audit --json', {
      cwd: options.path,
      stdio: ['ignore', 'pipe', 'ignore']
    }).toString();

    const auditResult = JSON.parse(auditOutput);

    // Convertir les résultats en format standard
    const issues = [];

    if (auditResult.vulnerabilities) {
      for (const [name, vuln] of Object.entries(auditResult.vulnerabilities)) {
        const severity = vuln.severity;

        issues.push({
          type: 'dependency',
          severity,
          name,
          description: vuln.overview || `Vulnerability in ${name}`,
          file: 'package.json',
          line: 0,
          column: 0,
          recommendation: vuln.recommendation || `Run 'npm audit fix' to fix this issue`,
          fixable: true
        });
      }
    }

    return issues;
  } catch (error) {
    console.warn(chalk.yellow(`Impossible de vérifier les dépendances: ${error.message}`));
    return [];
  }
}

/**
 * Exécuter l'analyse ESLint
 */
async function runEslintAnalysis() {
  try {
    // Initialiser ESLint avec la configuration de sécurité
    const eslint = new ESLint({
      useEslintrc: true,
      cwd: options.path,
      fix: false,
      overrideConfig: {
        plugins: ['security', 'node', 'sonarjs'],
        extends: [
          'plugin:security/recommended',
          'plugin:node/recommended',
          'plugin:sonarjs/recommended'
        ]
      }
    });

    // Trouver tous les fichiers JavaScript et TypeScript
    const patterns = [
      '**/*.js',
      '**/*.ts',
      '!node_modules/**',
      '!dist/**',
      '!coverage/**',
      '!**/node_modules/**'
    ];

    // Ajouter les patterns à ignorer depuis le fichier d'ignore
    if (options.ignoreFile && fs.existsSync(options.ignoreFile)) {
      const ignorePatterns = fs.readFileSync(options.ignoreFile, 'utf8')
        .split('\n')
        .filter(line => line.trim() && !line.startsWith('#'))
        .map(line => `!${line.trim()}`);

      patterns.push(...ignorePatterns);
    }

    const files = await glob(patterns, { cwd: options.path });

    // Exécuter ESLint
    const results = await eslint.lintFiles(files.map(file => path.join(options.path, file)));

    // Convertir les résultats en format standard
    const issues = [];

    for (const result of results) {
      for (const message of result.messages) {
        // Déterminer la sévérité
        let severity;
        switch (message.severity) {
          case 2: severity = 'high'; break;
          case 1: severity = 'medium'; break;
          default: severity = 'low';
        }

        // Convertir les règles de sécurité spécifiques en sévérité plus élevée
        if (message.ruleId && (
          message.ruleId.startsWith('security/') ||
          message.ruleId.includes('injection') ||
          message.ruleId.includes('xss') ||
          message.ruleId.includes('csrf')
        )) {
          severity = 'high';
        }

        issues.push({
          type: 'code',
          severity,
          name: message.ruleId || 'unknown-rule',
          description: message.message,
          file: path.relative(options.path, result.filePath),
          line: message.line || 0,
          column: message.column || 0,
          recommendation: `Fix the issue according to ESLint rule: ${message.ruleId}`,
          fixable: message.fix !== undefined
        });
      }
    }

    return issues;
  } catch (error) {
    console.warn(chalk.yellow(`Impossible d'exécuter ESLint: ${error.message}`));
    return [];
  }
}

/**
 * Rechercher les secrets exposés dans le code
 */
async function findExposedSecrets() {
  try {
    // Patterns de secrets courants
    const secretPatterns = [
      { pattern: /(['"])(?:api|jwt|token|key|secret|password|pass|pwd)(?:_?key)?(['"])\s*(?::|=>|=)\s*(['"])(?!process\.env)[^\3]+\3/gi, severity: 'critical' },
      { pattern: /(['"])(?:mongodb|postgres|mysql|redis):\/\/[^\1]+\1/gi, severity: 'critical' },
      { pattern: /(['"])(?:[a-zA-Z0-9+/]{40,}|[a-zA-Z0-9_-]{40,})(['"])/g, severity: 'high' }, // Clés API potentielles
      { pattern: /(['"])(?:sk|pk)_(?:test|live)_[a-zA-Z0-9]{24,}(['"])/g, severity: 'critical' }, // Clés Stripe
      { pattern: /(['"])(?:ACCESS|SECRET|PRIVATE)_KEY(['"])\s*(?::|=>|=)\s*(['"])[^\3]+\3/gi, severity: 'critical' },
    ];

    // Trouver tous les fichiers à analyser
    const patterns = [
      '**/*.js',
      '**/*.ts',
      '**/*.json',
      '**/*.env',
      '!node_modules/**',
      '!dist/**',
      '!coverage/**',
      '!**/node_modules/**'
    ];

    const files = await glob(patterns, { cwd: options.path });

    const issues = [];

    // Analyser chaque fichier
    for (const file of files) {
      const filePath = path.join(options.path, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');

      for (const { pattern, severity } of secretPatterns) {
        pattern.lastIndex = 0; // Réinitialiser le regex

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          pattern.lastIndex = 0;

          let match;
          while ((match = pattern.exec(line)) !== null) {
            issues.push({
              type: 'secret',
              severity,
              name: 'exposed-secret',
              description: `Potential secret or credential found in code`,
              file: path.relative(options.path, filePath),
              line: i + 1,
              column: match.index,
              recommendation: 'Move this secret to environment variables or a secure vault',
              fixable: false
            });
          }
        }
      }
    }

    return issues;
  } catch (error) {
    console.warn(chalk.yellow(`Impossible de rechercher les secrets exposés: ${error.message}`));
    return [];
  }
}

/**
 * Vérifier les problèmes de configuration de sécurité
 */
async function checkConfigurationIssues() {
  try {
    const issues = [];

    // Vérifier les fichiers de configuration courants
    const configFiles = [
      { path: 'package.json', checks: checkPackageJson },
      { path: 'tsconfig.json', checks: checkTsConfig },
      { path: '.env', checks: checkEnvFile },
      { path: '.env.example', checks: checkEnvExample },
      { path: 'nest-cli.json', checks: checkNestConfig },
      { path: 'src/main.ts', checks: checkMainTs }
    ];

    for (const { path: configPath, checks } of configFiles) {
      const fullPath = path.join(options.path, configPath);

      if (fs.existsSync(fullPath)) {
        const configIssues = await checks(fullPath);
        issues.push(...configIssues);
      }
    }

    return issues;
  } catch (error) {
    console.warn(chalk.yellow(`Impossible de vérifier les problèmes de configuration: ${error.message}`));
    return [];
  }
}

/**
 * Vérifier le fichier package.json
 */
async function checkPackageJson(filePath) {
  const issues = [];
  const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));

  // Vérifier les scripts potentiellement dangereux
  if (content.scripts) {
    for (const [name, script] of Object.entries(content.scripts)) {
      if (script.includes('eval') || script.includes('Function(') || script.includes('child_process')) {
        issues.push({
          type: 'config',
          severity: 'high',
          name: 'unsafe-script',
          description: `Potentially unsafe script "${name}" in package.json`,
          file: path.relative(options.path, filePath),
          line: 0,
          column: 0,
          recommendation: 'Review and remove potentially unsafe code in npm scripts',
          fixable: false
        });
      }
    }
  }

  // Vérifier les dépendances obsolètes ou connues pour être vulnérables
  const knownVulnerableDeps = [
    'node-serialize', 'serialize-javascript@<3.1.0', 'lodash@<4.17.19'
  ];

  for (const section of ['dependencies', 'devDependencies']) {
    if (content[section]) {
      for (const [dep, version] of Object.entries(content[section])) {
        for (const vulnDep of knownVulnerableDeps) {
          if (vulnDep.startsWith(dep + '@')) {
            const vulnVersion = vulnDep.split('@')[1];
            if (compareVersions(version, vulnVersion)) {
              issues.push({
                type: 'config',
                severity: 'high',
                name: 'vulnerable-dependency',
                description: `Known vulnerable dependency ${dep}@${version}`,
                file: path.relative(options.path, filePath),
                line: 0,
                column: 0,
                recommendation: `Update ${dep} to a newer version`,
                fixable: true
              });
            }
          }
        }
      }
    }
  }

  return issues;
}

/**
 * Vérifier le fichier tsconfig.json
 */
async function checkTsConfig(filePath) {
  const issues = [];
  const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));

  // Vérifier les options de compilation potentiellement dangereuses
  if (content.compilerOptions) {
    // Vérifier si strictNullChecks est désactivé
    if (content.compilerOptions.strictNullChecks === false) {
      issues.push({
        type: 'config',
        severity: 'medium',
        name: 'tsconfig-strict-null-checks-disabled',
        description: 'strictNullChecks is disabled in tsconfig.json',
        file: path.relative(options.path, filePath),
        line: 0,
        column: 0,
        recommendation: 'Enable strictNullChecks to prevent null/undefined errors',
        fixable: false
      });
    }

    // Vérifier si noImplicitAny est désactivé
    if (content.compilerOptions.noImplicitAny === false) {
      issues.push({
        type: 'config',
        severity: 'medium',
        name: 'tsconfig-no-implicit-any-disabled',
        description: 'noImplicitAny is disabled in tsconfig.json',
        file: path.relative(options.path, filePath),
        line: 0,
        column: 0,
        recommendation: 'Enable noImplicitAny to ensure type safety',
        fixable: false
      });
    }
  }

  return issues;
}

/**
 * Vérifier le fichier .env
 */
async function checkEnvFile(filePath) {
  const issues = [];
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');

  // Vérifier les variables d'environnement sensibles
  const sensitiveVars = [
    'PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'CREDENTIAL', 'AUTH'
  ];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Ignorer les commentaires et les lignes vides
    if (line.startsWith('#') || !line) continue;

    // Vérifier si la ligne contient une variable sensible
    for (const sensitiveVar of sensitiveVars) {
      if (line.toUpperCase().includes(sensitiveVar) && !line.includes('${') && !line.includes('${}')) {
        issues.push({
          type: 'secret',
          severity: 'high',
          name: 'sensitive-env-var',
          description: `Sensitive environment variable found in .env file`,
          file: path.relative(options.path, filePath),
          line: i + 1,
          column: 0,
          recommendation: 'Do not commit .env files with sensitive data. Use .env.example with placeholders instead.',
          fixable: false
        });
        break;
      }
    }
  }

  return issues;
}

/**
 * Vérifier le fichier .env.example
 */
async function checkEnvExample(filePath) {
  const issues = [];
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');

  // Vérifier les variables d'environnement sensibles avec des valeurs réelles
  const sensitiveVars = [
    'PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'CREDENTIAL', 'AUTH'
  ];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Ignorer les commentaires et les lignes vides
    if (line.startsWith('#') || !line) continue;

    // Vérifier si la ligne contient une variable sensible avec une valeur qui ne semble pas être un placeholder
    for (const sensitiveVar of sensitiveVars) {
      if (line.toUpperCase().includes(sensitiveVar)) {
        const parts = line.split('=');
        if (parts.length > 1) {
          const value = parts[1].trim();

          // Vérifier si la valeur semble être un secret réel et non un placeholder
          if (value.length > 8 &&
              !value.includes('${') &&
              !value.includes('<') &&
              !value.includes('PLACEHOLDER') &&
              !value.includes('YOUR_') &&
              !value.includes('CHANGE_ME')) {

            issues.push({
              type: 'secret',
              severity: 'critical',
              name: 'sensitive-env-example',
              description: `Sensitive environment variable with real value in .env.example file`,
              file: path.relative(options.path, filePath),
              line: i + 1,
              column: 0,
              recommendation: 'Use placeholders like YOUR_SECRET_KEY in .env.example files, not real values',
              fixable: false
            });
            break;
          }
        }
      }
    }
  }

  return issues;
}

/**
 * Vérifier le fichier nest-cli.json
 */
async function checkNestConfig(filePath) {
  // Pas de problèmes de sécurité courants dans nest-cli.json
  return [];
}

/**
 * Vérifier le fichier main.ts
 */
async function checkMainTs(filePath) {
  const issues = [];
  const content = fs.readFileSync(filePath, 'utf8');

  // Vérifier si CORS est configuré de manière trop permissive
  if (content.includes('cors: true') || content.includes('cors()')) {
    issues.push({
      type: 'config',
      severity: 'medium',
      name: 'permissive-cors',
      description: 'CORS is configured too permissively',
      file: path.relative(options.path, filePath),
      line: 0,
      column: 0,
      recommendation: 'Configure CORS with specific origins, methods, and headers',
      fixable: false
    });
  }

  // Vérifier si Helmet n'est pas utilisé
  if (!content.includes('helmet') && !content.includes('Helmet')) {
    issues.push({
      type: 'config',
      severity: 'high',
      name: 'missing-helmet',
      description: 'Helmet middleware is not used',
      file: path.relative(options.path, filePath),
      line: 0,
      column: 0,
      recommendation: 'Use Helmet middleware to set security-related HTTP headers',
      fixable: false
    });
  }

  return issues;
}

/**
 * Générer le rapport dans le format demandé
 */
async function generateReport(issues) {
  switch (format) {
    case 'json':
      return generateJsonReport(issues);
    case 'html':
      return generateHtmlReport(issues);
    case 'pdf':
      return generatePdfReport(issues);
    default:
      return generateJsonReport(issues);
  }
}

/**
 * Générer un rapport au format JSON
 */
async function generateJsonReport(issues) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: issues.length,
      bySeverity: {
        critical: issues.filter(issue => issue.severity === 'critical').length,
        high: issues.filter(issue => issue.severity === 'high').length,
        medium: issues.filter(issue => issue.severity === 'medium').length,
        low: issues.filter(issue => issue.severity === 'low').length
      },
      byType: {}
    },
    issues
  };

  // Compter les problèmes par type
  for (const issue of issues) {
    if (!report.summary.byType[issue.type]) {
      report.summary.byType[issue.type] = 0;
    }
    report.summary.byType[issue.type]++;
  }

  // Écrire le rapport JSON
  fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
  return outputPath;
}

/**
 * Générer un rapport au format HTML
 */
async function generateHtmlReport(issues) {
  // Trier les problèmes par sévérité (du plus critique au moins critique)
  const sortedIssues = [...issues].sort((a, b) => {
    const severityOrder = {
      critical: 0,
      high: 1,
      medium: 2,
      low: 3
    };
    return severityOrder[a.severity] - severityOrder[b.severity];
  });

  // Créer le contenu HTML
  let html = `
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Rapport d'Audit de Sécurité</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .summary {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 5px;
    }
    .severity-count {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .severity-item {
      text-align: center;
      padding: 10px;
      border-radius: 5px;
      flex: 1;
      margin: 0 5px;
      color: white;
      font-weight: bold;
    }
    .severity-critical {
      background-color: #c0392b;
    }
    .severity-high {
      background-color: #e74c3c;
    }
    .severity-medium {
      background-color: #f39c12;
    }
    .severity-low {
      background-color: #2ecc71;
    }
    .issue {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 5px;
      border-left: 5px solid #ddd;
    }
    .issue-critical {
      border-left-color: #c0392b;
      background-color: rgba(192, 57, 43, 0.1);
    }
    .issue-high {
      border-left-color: #e74c3c;
      background-color: rgba(231, 76, 60, 0.1);
    }
    .issue-medium {
      border-left-color: #f39c12;
      background-color: rgba(243, 156, 18, 0.1);
    }
    .issue-low {
      border-left-color: #2ecc71;
      background-color: rgba(46, 204, 113, 0.1);
    }
    .issue-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .issue-title {
      font-weight: bold;
      font-size: 1.1em;
    }
    .issue-severity {
      padding: 3px 8px;
      border-radius: 3px;
      color: white;
      font-size: 0.8em;
      font-weight: bold;
    }
    .issue-details {
      margin-top: 10px;
    }
    .issue-location {
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 5px;
      border-radius: 3px;
      margin: 5px 0;
    }
    .issue-recommendation {
      margin-top: 10px;
      font-style: italic;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      text-align: center;
      font-size: 0.9em;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Rapport d'Audit de Sécurité</h1>
    <p>Date: ${new Date().toLocaleDateString()}</p>
    <p>Répertoire analysé: ${options.path}</p>

    <div class="summary">
      <h2>Résumé</h2>
      <p>Nombre total de problèmes: <strong>${issues.length}</strong></p>

      <h3>Problèmes par sévérité</h3>
      <div class="severity-count">
        <div class="severity-item severity-critical">
          Critique: ${issues.filter(issue => issue.severity === 'critical').length}
        </div>
        <div class="severity-item severity-high">
          Élevé: ${issues.filter(issue => issue.severity === 'high').length}
        </div>
        <div class="severity-item severity-medium">
          Moyen: ${issues.filter(issue => issue.severity === 'medium').length}
        </div>
        <div class="severity-item severity-low">
          Faible: ${issues.filter(issue => issue.severity === 'low').length}
        </div>
      </div>

      <h3>Problèmes par type</h3>
      <table>
        <tr>
          <th>Type</th>
          <th>Nombre</th>
        </tr>
        ${Object.entries(issues.reduce((acc, issue) => {
          acc[issue.type] = (acc[issue.type] || 0) + 1;
          return acc;
        }, {})).map(([type, count]) => `
        <tr>
          <td>${type}</td>
          <td>${count}</td>
        </tr>
        `).join('')}
      </table>
    </div>

    <h2>Problèmes détectés</h2>
    ${sortedIssues.map(issue => `
    <div class="issue issue-${issue.severity}">
      <div class="issue-header">
        <div class="issue-title">${issue.name}</div>
        <div class="issue-severity severity-${issue.severity}">${issue.severity.toUpperCase()}</div>
      </div>
      <div class="issue-description">${issue.description}</div>
      <div class="issue-details">
        <div class="issue-location">
          Fichier: ${issue.file}${issue.line > 0 ? `, Ligne: ${issue.line}` : ''}
        </div>
        <div class="issue-recommendation">
          <strong>Recommandation:</strong> ${issue.recommendation}
        </div>
      </div>
    </div>
    `).join('')}

    <div class="footer">
      <p>Rapport généré le ${new Date().toLocaleString()}</p>
    </div>
  </div>
</body>
</html>
  `;

  // Écrire le rapport HTML
  fs.writeFileSync(outputPath, html);
  return outputPath;
}

/**
 * Générer un rapport au format PDF
 */
async function generatePdfReport(issues) {
  // Créer un nouveau document PDF
  const doc = new PDFDocument({ margin: 50 });

  // Pipe le PDF vers un fichier
  doc.pipe(fs.createWriteStream(outputPath));

  // Ajouter le titre et les informations de base
  doc.fontSize(25).text('Rapport d\'Audit de Sécurité', { align: 'center' });
  doc.moveDown();
  doc.fontSize(12).text(`Date: ${new Date().toLocaleDateString()}`);
  doc.text(`Répertoire analysé: ${options.path}`);
  doc.moveDown();

  // Ajouter le résumé
  doc.fontSize(18).text('Résumé', { underline: true });
  doc.moveDown(0.5);
  doc.fontSize(12).text(`Nombre total de problèmes: ${issues.length}`);
  doc.moveDown();

  // Problèmes par sévérité
  doc.fontSize(14).text('Problèmes par sévérité');
  doc.moveDown(0.5);
  doc.text(`Critique: ${issues.filter(issue => issue.severity === 'critical').length}`);
  doc.text(`Élevé: ${issues.filter(issue => issue.severity === 'high').length}`);
  doc.text(`Moyen: ${issues.filter(issue => issue.severity === 'medium').length}`);
  doc.text(`Faible: ${issues.filter(issue => issue.severity === 'low').length}`);
  doc.moveDown();

  // Problèmes par type
  doc.fontSize(14).text('Problèmes par type');
  doc.moveDown(0.5);

  const typeCount = issues.reduce((acc, issue) => {
    acc[issue.type] = (acc[issue.type] || 0) + 1;
    return acc;
  }, {});

  for (const [type, count] of Object.entries(typeCount)) {
    doc.text(`${type}: ${count}`);
  }

  doc.moveDown();

  // Ajouter les problèmes détaillés
  doc.addPage();
  doc.fontSize(18).text('Problèmes détectés', { underline: true });
  doc.moveDown();

  // Trier les problèmes par sévérité
  const sortedIssues = [...issues].sort((a, b) => {
    const severityOrder = {
      critical: 0,
      high: 1,
      medium: 2,
      low: 3
    };
    return severityOrder[a.severity] - severityOrder[b.severity];
  });

  // Ajouter chaque problème
  for (const issue of sortedIssues) {
    // Couleur selon la sévérité
    let color;
    switch (issue.severity) {
      case 'critical': color = '#c0392b'; break;
      case 'high': color = '#e74c3c'; break;
      case 'medium': color = '#f39c12'; break;
      case 'low': color = '#2ecc71'; break;
      default: color = '#333333';
    }

    doc.fontSize(14).fillColor(color).text(`[${issue.severity.toUpperCase()}] ${issue.name}`);
    doc.fontSize(12).fillColor('black').text(issue.description);
    doc.moveDown(0.5);
    doc.fontSize(10).text(`Fichier: ${issue.file}${issue.line > 0 ? `, Ligne: ${issue.line}` : ''}`);
    doc.moveDown(0.5);
    doc.fontSize(12).text(`Recommandation: ${issue.recommendation}`);
    doc.moveDown();

    // Ajouter une ligne de séparation
    doc.moveTo(50, doc.y)
       .lineTo(doc.page.width - 50, doc.y)
       .stroke();
    doc.moveDown();

    // Ajouter une nouvelle page si nécessaire
    if (doc.y > doc.page.height - 150) {
      doc.addPage();
    }
  }

  // Finaliser le document
  doc.end();
  return outputPath;
}

/**
 * Afficher un résumé des problèmes détectés
 */
function displaySummary(issues) {
  console.log('');
  console.log(chalk.blue.bold('=== Résumé de l\'Audit de Sécurité ==='));

  // Compter les problèmes par type et sévérité
  const counts = {
    total: issues.length,
    byType: {},
    bySeverity: {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    }
  };

  for (const issue of issues) {
    // Compter par type
    if (!counts.byType[issue.type]) {
      counts.byType[issue.type] = 0;
    }
    counts.byType[issue.type]++;

    // Compter par sévérité
    counts.bySeverity[issue.severity.toLowerCase()]++;
  }

  // Afficher le nombre total de problèmes
  console.log(chalk.blue(`Nombre total de problèmes: ${counts.total}`));

  // Afficher les problèmes par sévérité
  console.log(chalk.blue('Problèmes par sévérité:'));
  console.log(chalk.red(`  Critique: ${counts.bySeverity.critical}`));
  console.log(chalk.magenta(`  Élevé: ${counts.bySeverity.high}`));
  console.log(chalk.yellow(`  Moyen: ${counts.bySeverity.medium}`));
  console.log(chalk.green(`  Faible: ${counts.bySeverity.low}`));

  // Afficher les problèmes par type
  console.log(chalk.blue('Problèmes par type:'));
  for (const [type, count] of Object.entries(counts.byType)) {
    console.log(`  ${type}: ${count}`);
  }

  console.log('');
  console.log(chalk.blue(`Rapport complet disponible à: ${outputPath}`));
}

/**
 * Tenter de corriger automatiquement les problèmes
 */
async function autoFix(issues) {
  let fixedCount = 0;

  // Filtrer les problèmes qui peuvent être corrigés automatiquement
  const fixableIssues = issues.filter(issue => issue.fixable);

  if (fixableIssues.length === 0) {
    console.log(chalk.yellow('Aucun problème ne peut être corrigé automatiquement.'));
    return 0;
  }

  // Corriger les problèmes ESLint
  const eslintIssues = fixableIssues.filter(issue => issue.type === 'code');
  if (eslintIssues.length > 0) {
    try {
      const eslint = new ESLint({
        useEslintrc: true,
        cwd: options.path,
        fix: true
      });

      // Obtenir les fichiers uniques à corriger
      const filesToFix = [...new Set(eslintIssues.map(issue => path.join(options.path, issue.file)))];

      // Exécuter ESLint avec l'option fix
      const results = await eslint.lintFiles(filesToFix);

      // Appliquer les corrections
      await ESLint.outputFixes(results);

      // Compter les problèmes corrigés
      const fixedIssues = results.reduce((count, result) => count + result.fixableErrorCount + result.fixableWarningCount, 0);
      fixedCount += fixedIssues;

      console.log(chalk.green(`${fixedIssues} problèmes ESLint corrigés.`));
    } catch (error) {
      console.warn(chalk.yellow(`Impossible de corriger les problèmes ESLint: ${error.message}`));
    }
  }

  // Corriger les problèmes de dépendances
  const dependencyIssues = fixableIssues.filter(issue => issue.type === 'dependency');
  if (dependencyIssues.length > 0) {
    try {
      execSync('npm audit fix', {
        cwd: options.path,
        stdio: 'inherit'
      });

      fixedCount += dependencyIssues.length;
      console.log(chalk.green(`${dependencyIssues.length} problèmes de dépendances corrigés.`));
    } catch (error) {
      console.warn(chalk.yellow(`Impossible de corriger les problèmes de dépendances: ${error.message}`));
    }
  }

  return fixedCount;
}

// Fonction utilitaire pour comparer les versions
function compareVersions(version1, version2) {
  // Implémentation simple pour la comparaison de versions
  // Retourne true si version1 est inférieure à version2
  const v1Parts = version1.replace(/[^\d.]/g, '').split('.').map(Number);
  const v2Parts = version2.replace(/[^\d.]/g, '').split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part < v2Part) return true;
    if (v1Part > v2Part) return false;
  }

  return false;
}

// Exécuter la fonction principale
main().catch(error => {
  console.error(chalk.red(`Erreur fatale: ${error.message}`));
  process.exit(1);
});
