#!/usr/bin/env node

/**
 * Script pour tester les fonctionnalités d'analyse comparative et de recommandations
 * 
 * Usage:
 *   node test-benchmarks-recommendations.js [--creator <creatorId>] [--category <category>]
 * 
 * Options:
 *   --creator   ID du créateur à utiliser pour les tests (défaut: génère un nouvel ID)
 *   --category  Catégorie pour l'analyse comparative (défaut: 'all')
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const faker = require('faker');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  creatorId: null,
  category: 'all',
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--creator' && i + 1 < args.length) {
    options.creatorId = args[i + 1];
    i++;
  } else if (args[i] === '--category' && i + 1 < args.length) {
    options.category = args[i + 1];
    i++;
  }
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

async function runTest() {
  console.log('Starting benchmarks and recommendations test...');
  
  try {
    // Créer un créateur de test si nécessaire
    if (!options.creatorId) {
      const creator = await createTestCreator();
      options.creatorId = creator.id;
    }
    
    // Générer des données de test
    await generateTestData(options.creatorId);
    
    // Tester les benchmarks
    await testBenchmarks(options.creatorId, options.category);
    
    // Tester la comparaison
    await testComparison(options.creatorId, options.category);
    
    // Tester les recommandations
    await testRecommendations(options.creatorId);
    
    // Tester les recommandations par type
    await testRecommendationsByType(options.creatorId);
    
    console.log('Benchmarks and recommendations test completed successfully');
  } catch (error) {
    console.error('Error running benchmarks and recommendations test:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function createTestCreator() {
  console.log('Creating test creator...');
  
  const creator = await prisma.user.create({
    data: {
      email: faker.internet.email(),
      username: faker.internet.userName(),
      password: 'password123', // Dans une vraie application, ce serait hashé
      role: 'CREATOR',
      categories: ['fitness', 'lifestyle'],
    },
  });
  
  console.log(`Created test creator: ${creator.username} (${creator.id})`);
  return creator;
}

async function generateTestData(creatorId) {
  console.log(`Generating test data for creator ${creatorId}...`);
  
  // Générer des données d'engagement
  await generateEngagementData(creatorId);
  
  // Générer des données d'audience
  await generateAudienceData(creatorId);
  
  // Générer des données de revenus
  await generateRevenueData(creatorId);
  
  console.log('Test data generated successfully');
}

async function generateEngagementData(creatorId) {
  console.log('Generating engagement data...');
  
  // Créer quelques contenus
  for (let i = 0; i < 10; i++) {
    const contentType = ['POST', 'IMAGE', 'VIDEO', 'ARTICLE'][Math.floor(Math.random() * 4)];
    
    const content = await prisma.content.create({
      data: {
        creatorId,
        contentType,
        title: faker.lorem.sentence(),
        text: contentType === 'ARTICLE' ? faker.lorem.paragraphs(3) : faker.lorem.sentence(),
        imageUrl: contentType === 'IMAGE' ? faker.image.imageUrl() : null,
        videoUrl: contentType === 'VIDEO' ? `https://example.com/videos/${faker.random.uuid()}.mp4` : null,
        isPublished: true,
      },
    });
    
    // Créer des métriques d'engagement pour ce contenu
    await prisma.engagementMetric.create({
      data: {
        contentId: content.id,
        creatorId,
        views: Math.floor(Math.random() * 10000),
        likes: Math.floor(Math.random() * 1000),
        comments: Math.floor(Math.random() * 200),
        shares: Math.floor(Math.random() * 100),
        bookmarks: Math.floor(Math.random() * 50),
        engagementRate: Math.random() * 0.1,
        date: new Date(),
      },
    });
  }
}

async function generateAudienceData(creatorId) {
  console.log('Generating audience data...');
  
  // Créer des métriques d'audience
  await prisma.audienceMetric.create({
    data: {
      creatorId,
      followers: Math.floor(Math.random() * 50000),
      newFollowers: Math.floor(Math.random() * 1000),
      unfollowers: Math.floor(Math.random() * 200),
      growthRate: Math.random() * 0.05,
      demographics: {
        ageGroups: {
          '18-24': Math.random() * 0.3,
          '25-34': Math.random() * 0.4,
          '35-44': Math.random() * 0.2,
          '45-54': Math.random() * 0.1,
        },
        genders: {
          male: Math.random() * 0.6,
          female: Math.random() * 0.4,
        },
        locations: {
          'France': Math.random() * 0.5,
          'United States': Math.random() * 0.2,
          'Canada': Math.random() * 0.1,
          'Other': Math.random() * 0.2,
        },
      },
      date: new Date(),
    },
  });
}

async function generateRevenueData(creatorId) {
  console.log('Generating revenue data...');
  
  // Créer des métriques de revenus
  await prisma.revenueMetric.create({
    data: {
      creatorId,
      amount: Math.random() * 5000,
      source: ['advertising', 'subscriptions', 'donations'][Math.floor(Math.random() * 3)],
      date: new Date(),
    },
  });
}

async function testBenchmarks(creatorId, category) {
  console.log(`Testing benchmarks for creator ${creatorId} in category ${category}...`);
  
  try {
    // Appeler l'API de benchmarks
    const response = await axios.get(`${API_BASE_URL}/analytics/benchmarks/${creatorId}`, {
      params: {
        category,
      },
    });
    
    console.log('Benchmarks:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Vérifier les résultats
    if (response.data.engagement && response.data.audience && response.data.revenue) {
      console.log('Benchmark metrics:');
      console.log(`- Engagement: views (${response.data.engagement.views.percentile}%), engagement rate (${response.data.engagement.engagementRate.percentile}%)`);
      console.log(`- Audience: followers (${response.data.audience.followers.percentile}%), growth (${response.data.audience.growth.percentile}%)`);
      console.log(`- Revenue: amount (${response.data.revenue.amount.percentile}%)`);
    } else {
      console.log('No benchmarks returned');
    }
  } catch (error) {
    console.error(`Error testing benchmarks: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

async function testComparison(creatorId, category) {
  console.log(`Testing comparison for creator ${creatorId} in category ${category}...`);
  
  try {
    // Appeler l'API de comparaison
    const response = await axios.get(`${API_BASE_URL}/analytics/benchmarks/${creatorId}/comparison`, {
      params: {
        category,
        metric: 'all',
      },
    });
    
    console.log('Comparison:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Vérifier les résultats
    if (response.data.strengths && response.data.weaknesses && response.data.recommendations) {
      console.log('Comparison results:');
      console.log('- Strengths:');
      response.data.strengths.forEach(strength => console.log(`  * ${strength}`));
      
      console.log('- Weaknesses:');
      response.data.weaknesses.forEach(weakness => console.log(`  * ${weakness}`));
      
      console.log('- Recommendations:');
      response.data.recommendations.forEach(recommendation => console.log(`  * ${recommendation}`));
    } else {
      console.log('No comparison results returned');
    }
  } catch (error) {
    console.error(`Error testing comparison: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

async function testRecommendations(creatorId) {
  console.log(`Testing recommendations for creator ${creatorId}...`);
  
  try {
    // Appeler l'API de recommandations
    const response = await axios.get(`${API_BASE_URL}/analytics/recommendations/${creatorId}`);
    
    console.log('Recommendations:');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Vérifier les résultats
    if (response.data.recommendations && response.data.recommendations.length > 0) {
      console.log(`Received ${response.data.recommendations.length} recommendations:`);
      
      response.data.recommendations.forEach((recommendation, index) => {
        console.log(`${index + 1}. ${recommendation.title} (${recommendation.type}, impact: ${recommendation.impact})`);
        console.log(`   ${recommendation.description}`);
        console.log('   Actions:');
        recommendation.actions.forEach(action => console.log(`   - ${action}`));
        console.log();
      });
    } else {
      console.log('No recommendations returned');
    }
  } catch (error) {
    console.error(`Error testing recommendations: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

async function testRecommendationsByType(creatorId) {
  console.log(`Testing recommendations by type for creator ${creatorId}...`);
  
  try {
    // Récupérer les types de recommandations disponibles
    const typesResponse = await axios.get(`${API_BASE_URL}/analytics/recommendations/types`);
    
    if (typesResponse.data.types && typesResponse.data.types.length > 0) {
      console.log('Available recommendation types:');
      typesResponse.data.types.forEach(type => {
        console.log(`- ${type}: ${typesResponse.data.descriptions[type]}`);
      });
      
      // Tester un type spécifique
      const testType = typesResponse.data.types[0];
      console.log(`\nTesting recommendations of type ${testType}:`);
      
      const response = await axios.get(`${API_BASE_URL}/analytics/recommendations/${creatorId}/by-type/${testType}`);
      
      if (response.data.recommendations && response.data.recommendations.length > 0) {
        console.log(`Received ${response.data.recommendations.length} ${testType} recommendations:`);
        
        response.data.recommendations.forEach((recommendation, index) => {
          console.log(`${index + 1}. ${recommendation.title} (impact: ${recommendation.impact})`);
          console.log(`   ${recommendation.description}`);
        });
      } else {
        console.log(`No ${testType} recommendations returned`);
      }
    } else {
      console.log('No recommendation types returned');
    }
  } catch (error) {
    console.error(`Error testing recommendations by type: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

// Exécuter le test
runTest();
