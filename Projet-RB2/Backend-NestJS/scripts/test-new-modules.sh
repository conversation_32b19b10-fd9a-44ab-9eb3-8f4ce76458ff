#!/bin/bash

# Script pour tester les nouveaux modules

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Début des tests des nouveaux modules...${NC}"

# Vérifier si les modules existent
if [ ! -d "./src/modules/moderation" ] || [ ! -d "./src/modules/analytics" ]; then
    echo -e "${RED}Erreur: Les modules de modération ou d'analyse n'existent pas.${NC}"
    exit 1
fi

# Exécuter les tests unitaires pour le module de modération
echo -e "${YELLOW}Exécution des tests unitaires pour le module de modération...${NC}"
npx jest src/modules/moderation --coverage
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests unitaires pour le module de modération.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests unitaires pour le module de modération exécutés avec succès.${NC}"

# Exécuter les tests unitaires pour le module d'analyse
echo -e "${YELLOW}Exécution des tests unitaires pour le module d'analyse...${NC}"
npx jest src/modules/analytics --coverage
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests unitaires pour le module d'analyse.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests unitaires pour le module d'analyse exécutés avec succès.${NC}"

# Exécuter les tests e2e pour le module de modération
echo -e "${YELLOW}Exécution des tests e2e pour le module de modération...${NC}"
npx jest test/moderation.e2e-spec.ts
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests e2e pour le module de modération.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests e2e pour le module de modération exécutés avec succès.${NC}"

# Exécuter les tests e2e pour le module d'analyse
echo -e "${YELLOW}Exécution des tests e2e pour le module d'analyse...${NC}"
npx jest test/analytics.e2e-spec.ts
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests e2e pour le module d'analyse.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests e2e pour le module d'analyse exécutés avec succès.${NC}"

echo -e "${GREEN}Tous les tests ont été exécutés avec succès.${NC}"
