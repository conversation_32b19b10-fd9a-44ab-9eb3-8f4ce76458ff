#!/usr/bin/env node

/**
 * Script pour tester les fonctionnalités de gestion des secrets et de chiffrement
 * 
 * Usage:
 *   node test-secrets-encryption.js [--mode <mode>]
 * 
 * Options:
 *   --mode   Mode de test (secrets, encryption, tokenization, all) (défaut: all)
 */

const axios = require('axios');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  mode: 'all',
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--mode' && i + 1 < args.length) {
    options.mode = args[i + 1];
    i++;
  }
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Token d'authentification (simulé pour les tests)
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE1MTYyMzkwMjJ9.KjCZVrGRZ-SFKuAkro-X5mEQ0XBHcXyNh72K1JaK-Qw';

// Configuration Axios
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

async function runTest() {
  console.log('Starting secrets and encryption test...');
  
  try {
    if (options.mode === 'all' || options.mode === 'secrets') {
      await testSecretManager();
      await testApplicationSecrets();
    }
    
    if (options.mode === 'all' || options.mode === 'encryption') {
      await testSensitiveDataEncryption();
    }
    
    if (options.mode === 'all' || options.mode === 'tokenization') {
      await testTokenization();
    }
    
    console.log('Secrets and encryption test completed successfully');
  } catch (error) {
    console.error('Error running secrets and encryption test:', error.message);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    }
  } finally {
    await prisma.$disconnect();
  }
}

async function testSecretManager() {
  console.log('\n=== Testing Secret Manager ===');
  
  try {
    // Créer un nouveau secret
    console.log('Creating a new secret...');
    const createResponse = await api.post('/api/security/secrets', {
      name: 'test-api-key',
      value: 'secret-value-123',
      type: 'api_key',
      description: 'Test API key for the secret manager',
      expiresInDays: 90,
      autoRotate: true,
      tags: ['test', 'api'],
      owner: 'security-team',
      application: 'test-app',
      environment: 'development',
    });
    
    console.log(`Secret created with ID: ${createResponse.data.data.secretId}`);
    const secretId = createResponse.data.data.secretId;
    
    // Récupérer le secret
    console.log('Retrieving the secret...');
    const getResponse = await api.get(`/api/security/secrets/${secretId}`);
    console.log(`Retrieved secret: ${getResponse.data.data.name}`);
    console.log(`Secret value: ${getResponse.data.data.value}`);
    
    // Mettre à jour le secret
    console.log('Updating the secret...');
    const updateResponse = await api.put(`/api/security/secrets/${secretId}`, {
      value: 'updated-secret-value-456',
    });
    console.log(`Secret updated: ${updateResponse.data.message}`);
    
    // Récupérer le secret mis à jour
    console.log('Retrieving the updated secret...');
    const getUpdatedResponse = await api.get(`/api/security/secrets/${secretId}`);
    console.log(`Updated secret value: ${getUpdatedResponse.data.data.value}`);
    
    // Effectuer la rotation du secret
    console.log('Rotating the secret...');
    const rotateResponse = await api.post(`/api/security/secrets/${secretId}/rotate`);
    console.log(`Secret rotated. New secret ID: ${rotateResponse.data.data.newSecretId}`);
    const newSecretId = rotateResponse.data.data.newSecretId;
    
    // Récupérer le nouveau secret
    console.log('Retrieving the new secret after rotation...');
    const getNewResponse = await api.get(`/api/security/secrets/${newSecretId}`);
    console.log(`New secret value: ${getNewResponse.data.data.value}`);
    
    // Lister tous les secrets
    console.log('Listing all secrets...');
    const listResponse = await api.get('/api/security/secrets');
    console.log(`Found ${listResponse.data.data.length} secrets`);
    
    // Supprimer les secrets
    console.log('Deleting the secrets...');
    await api.delete(`/api/security/secrets/${secretId}`);
    await api.delete(`/api/security/secrets/${newSecretId}`);
    console.log('Secrets deleted successfully');
    
    console.log('Secret Manager test completed successfully');
  } catch (error) {
    console.error('Error testing Secret Manager:', error.message);
    throw error;
  }
}

async function testApplicationSecrets() {
  console.log('\n=== Testing Application Secrets ===');
  
  try {
    // Créer un nouveau secret d'application
    console.log('Creating a new application secret...');
    const createResponse = await api.post('/api/security/application-secrets', {
      name: 'database-password',
      value: 'db-password-123',
      application: 'user-service',
      environment: 'development',
      description: 'Database password for user service',
      owner: 'backend-team',
      expiresInDays: 90,
      autoRotate: true,
    });
    
    console.log(`Application secret created with ID: ${createResponse.data.data.secretId}`);
    
    // Lister les applications
    console.log('Listing all applications...');
    const appsResponse = await api.get('/api/security/application-secrets/applications');
    console.log(`Found applications: ${appsResponse.data.data.applications.join(', ')}`);
    
    // Lister les secrets d'une application
    console.log('Listing secrets for user-service...');
    const listResponse = await api.get('/api/security/application-secrets/user-service');
    console.log(`Found ${listResponse.data.data.secrets.length} secrets for user-service`);
    
    // Récupérer un secret d'application
    console.log('Retrieving the application secret...');
    const getResponse = await api.get('/api/security/application-secrets/user-service/database-password');
    console.log(`Retrieved secret: ${getResponse.data.data.name}`);
    console.log(`Secret value: ${getResponse.data.data.value}`);
    
    // Mettre à jour le secret d'application
    console.log('Updating the application secret...');
    const updateResponse = await api.put('/api/security/application-secrets/user-service/database-password', {
      value: 'updated-db-password-456',
    });
    console.log(`Application secret updated: ${updateResponse.data.message}`);
    
    // Effectuer la rotation du secret d'application
    console.log('Rotating the application secret...');
    const rotateResponse = await api.post('/api/security/application-secrets/user-service/database-password/rotate');
    console.log(`Application secret rotated. New secret ID: ${rotateResponse.data.data.newSecretId}`);
    
    // Supprimer le secret d'application
    console.log('Deleting the application secret...');
    await api.delete('/api/security/application-secrets/user-service/database-password');
    console.log('Application secret deleted successfully');
    
    console.log('Application Secrets test completed successfully');
  } catch (error) {
    console.error('Error testing Application Secrets:', error.message);
    throw error;
  }
}

async function testSensitiveDataEncryption() {
  console.log('\n=== Testing Sensitive Data Encryption ===');
  
  try {
    // Données sensibles à chiffrer
    const sensitiveData = {
      creditCard: '4111-1111-1111-1111',
      ssn: '***********',
      personalInfo: {
        fullName: 'John Doe',
        dob: '1980-01-01',
        passport: 'AB123456',
      },
    };
    
    // Chiffrer les données sensibles
    console.log('Encrypting sensitive data...');
    const encryptResponse = await api.post('/api/security/encryption/sensitive-data', {
      data: sensitiveData,
      type: 'pii',
      context: 'user-profile',
    });
    
    console.log('Data encrypted successfully');
    const encryptedData = encryptResponse.data.data.encryptedData;
    
    // Déchiffrer les données sensibles
    console.log('Decrypting sensitive data...');
    const decryptResponse = await api.post('/api/security/encryption/sensitive-data/decrypt', {
      encryptedData,
    });
    
    console.log('Data decrypted successfully');
    console.log('Decrypted data:', decryptResponse.data.data.decryptedData);
    
    // Détection automatique et chiffrement des données sensibles
    console.log('Testing automatic detection and encryption...');
    const userData = {
      username: 'johndoe',
      email: '<EMAIL>',
      password: 'password123',
      creditCard: '4111-1111-1111-1111',
      address: {
        street: '123 Main St',
        city: 'Anytown',
        zipCode: '12345',
        country: 'USA',
      },
    };
    
    const detectResponse = await api.post('/api/security/encryption/sensitive-data/detect-and-encrypt', {
      data: userData,
      tokenize: true,
    });
    
    console.log('Automatic detection and encryption completed');
    console.log('Processed data:', detectResponse.data.data.processedData);
    
    console.log('Sensitive Data Encryption test completed successfully');
  } catch (error) {
    console.error('Error testing Sensitive Data Encryption:', error.message);
    throw error;
  }
}

async function testTokenization() {
  console.log('\n=== Testing Tokenization ===');
  
  try {
    // Données à tokeniser
    const sensitiveData = {
      creditCard: '4111-1111-1111-1111',
      ssn: '***********',
      email: '<EMAIL>',
      phone: '******-123-4567',
    };
    
    // Tokeniser les données
    console.log('Tokenizing data...');
    const tokenizeResponse = await api.post('/api/security/tokenization', {
      data: sensitiveData,
      fieldMappings: {
        creditCard: {
          tokenType: 'pan',
          format: 'numeric',
          preserveLength: true,
          preservePattern: true,
        },
        ssn: {
          tokenType: 'ssn',
          format: 'numeric',
          preservePattern: true,
        },
        email: {
          tokenType: 'email',
          format: 'alphanumeric',
        },
        phone: {
          tokenType: 'phone',
          format: 'numeric',
          preservePattern: true,
        },
      },
    });
    
    console.log('Data tokenized successfully');
    const tokenizedData = tokenizeResponse.data.data.tokenizedData;
    console.log('Tokenized data:', tokenizedData);
    
    // Détokeniser les données
    console.log('Detokenizing data...');
    const detokenizeResponse = await api.post('/api/security/tokenization/detokenize', {
      tokenizedData,
    });
    
    console.log('Data detokenized successfully');
    console.log('Original data:', detokenizeResponse.data.data.originalData);
    
    console.log('Tokenization test completed successfully');
  } catch (error) {
    console.error('Error testing Tokenization:', error.message);
    throw error;
  }
}

// Exécuter le test
runTest();
