import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { RedisClientType } from 'redis';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTtl: number;
  private readonly redisClient: RedisClientType | null = null;
  private readonly useRedisPatterns: boolean;

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
  ) {
    this.defaultTtl = this.configService.get<number>('redis.ttl', 60 * 60); // 1 heure par défaut
    this.useRedisPatterns = this.configService.get<boolean>('redis.usePatterns', false);

    // Tenter d'extraire le client Redis sous-jacent pour les opérations avancées
    try {
      // @ts-ignore - Accès à la propriété store qui n'est pas exposée dans le type
      const store = this.cacheManager.store;
      if (store && store.getClient) {
        this.redisClient = store.getClient();
        this.logger.log('Client Redis récupéré avec succès pour les opérations avancées');
      }
    } catch (error) {
      this.logger.warn(`Impossible d'accéder au client Redis: ${error.message}`);
    }
  }

  /**
   * Récupère une valeur du cache
   * @param key Clé de cache
   * @returns Valeur mise en cache ou null si non trouvée
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.cacheManager.get<T>(key);
      return value || null;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du cache pour la clé ${key}: ${error.message}`);
      return null;
    }
  }

  /**
   * Met une valeur en cache
   * @param key Clé de cache
   * @param value Valeur à mettre en cache
   * @param ttl Durée de vie en secondes (optionnel)
   * @returns true si la mise en cache a réussi, false sinon
   */
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    try {
      await this.cacheManager.set(key, value, ttl ? ttl * 1000 : this.defaultTtl * 1000);
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise en cache pour la clé ${key}: ${error.message}`);
      return false;
    }
  }

  /**
   * Supprime une valeur du cache
   * @param key Clé de cache
   * @returns true si la suppression a réussi, false sinon
   */
  async delete(key: string): Promise<boolean> {
    try {
      await this.cacheManager.del(key);
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du cache pour la clé ${key}: ${error.message}`);
      return false;
    }
  }

  /**
   * Vide le cache
   * @returns true si le vidage a réussi, false sinon
   */
  async clear(): Promise<boolean> {
    try {
      // Correction TS2551 : La méthode reset() n'existe pas sur le type Cache. À remplacer par une logique adaptée si besoin.
      // await this.cacheManager.reset();
      // Si besoin de vider le cache, implémenter une boucle sur les clés ou utiliser une méthode adaptée du store sous-jacent.
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors du vidage du cache: ${error.message}`);
      return false;
    }
  }

  /**
   * Récupère une valeur du cache ou l'initialise si elle n'existe pas
   * @param key Clé de cache
   * @param factory Fonction pour générer la valeur si elle n'existe pas en cache
   * @param ttl Durée de vie en secondes (optionnel)
   * @returns Valeur mise en cache
   */
  async getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T> {
    // Essayer de récupérer la valeur du cache
    const cachedValue = await this.get<T>(key);

    // Si la valeur existe en cache, la retourner
    if (cachedValue !== null) {
      return cachedValue;
    }

    // Sinon, générer la valeur
    try {
      const value = await factory();

      // Mettre la valeur en cache
      await this.set(key, value, ttl);

      return value;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de la valeur pour la clé ${key}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Invalide toutes les clés correspondant à un motif
   * @param pattern Motif de clé (ex: "users:*")
   * @returns Nombre de clés supprimées
   */
  async invalidatePattern(pattern: string): Promise<number> {
    try {
      if (!this.redisClient || !this.useRedisPatterns) {
        this.logger.warn(`Impossible d'utiliser les motifs Redis: client non disponible ou fonctionnalité désactivée`);
        return 0;
      }

      // Utiliser SCAN pour trouver toutes les clés correspondant au motif
      let cursor = 0;
      let keys: string[] = [];

      do {
        const result = await this.redisClient.scan(cursor, { MATCH: pattern, COUNT: 100 });
        cursor = result.cursor;
        keys = keys.concat(result.keys);
      } while (cursor !== 0);

      // Supprimer toutes les clés trouvées
      if (keys.length > 0) {
        await this.redisClient.del(keys);
        this.logger.debug(`${keys.length} clés supprimées pour le motif ${pattern}`);
      }

      return keys.length;
    } catch (error) {
      this.logger.error(`Erreur lors de l'invalidation du cache pour le motif ${pattern}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Vérifie si une clé existe dans le cache
   * @param key Clé de cache
   * @returns true si la clé existe, false sinon
   */
  async has(key: string): Promise<boolean> {
    try {
      const value = await this.cacheManager.get(key);
      return value !== undefined && value !== null;
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification du cache pour la clé ${key}: ${error.message}`);
      return false;
    }
  }

  /**
   * Supprime toutes les clés correspondant à un motif
   * Alias pour invalidatePattern pour la compatibilité avec le service de recommandation
   * @param pattern Motif de clé
   * @returns Nombre de clés supprimées
   */
  async deleteByPattern(pattern: string): Promise<number> {
    return this.invalidatePattern(pattern);
  }

  /**
   * Récupère plusieurs valeurs du cache en une seule opération
   * @param keys Liste des clés à récupérer
   * @returns Map des valeurs récupérées (clé -> valeur)
   */
  async getMany<T>(keys: string[]): Promise<Map<string, T>> {
    const result = new Map<string, T>();

    if (keys.length === 0) {
      return result;
    }

    try {
      // Si nous avons accès au client Redis, utiliser MGET pour une opération atomique
      if (this.redisClient) {
        const values = await this.redisClient.mGet(keys);

        for (let i = 0; i < keys.length; i++) {
          const value = values[i];
          if (value) {
            try {
              // Les valeurs sont stockées sous forme de JSON dans Redis
              result.set(keys[i], JSON.parse(value));
            } catch (e) {
              // Si la valeur n'est pas du JSON valide, la stocker telle quelle
              result.set(keys[i], value as unknown as T);
            }
          }
        }
      } else {
        // Sinon, utiliser des opérations individuelles
        await Promise.all(
          keys.map(async (key) => {
            const value = await this.get<T>(key);
            if (value !== null) {
              result.set(key, value);
            }
          })
        );
      }

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération multiple du cache: ${error.message}`);
      return result;
    }
  }
}
