import { applyDecorators } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';

/**
 * Interface pour définir une réponse d'erreur
 */
interface ErrorResponse {
  status: number;
  description: string;
}

/**
 * Décorateur pour documenter les réponses d'erreur dans Swagger
 * @param errors Liste des erreurs possibles
 * @returns Décorateurs appliqués
 */
export const ApiErrorResponse = (errors: ErrorResponse[]) => {
  const decorators = errors.map(error => 
    ApiResponse({
      status: error.status,
      description: error.description,
      schema: {
        type: 'object',
        properties: {
          statusCode: {
            type: 'number',
            example: error.status,
          },
          message: {
            type: 'string',
            example: error.description,
          },
          error: {
            type: 'string',
            example: getErrorName(error.status),
          },
        },
      },
    })
  );

  return applyDecorators(...decorators);
};

/**
 * Obtient le nom de l'erreur à partir du code de statut HTTP
 * @param status Code de statut HTTP
 * @returns Nom de l'erreur
 */
function getErrorName(status: number): string {
  const statusMap: Record<number, string> = {
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    409: 'Conflict',
    422: 'Unprocessable Entity',
    429: 'Too Many Requests',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout',
  };

  return statusMap[status] || 'Error';
}
