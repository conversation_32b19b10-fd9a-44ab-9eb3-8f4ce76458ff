import { createParamDecorator, ExecutionContext, SetMetadata } from '@nestjs/common';
import { Request } from 'express';

/**
 * Décorateur pour récupérer l'utilisateur de la requête
 */
export const CurrentUser = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    const user = request.user;

    // Correction TS7053 : cast temporaire en any
    return data ? (user as any)?.[data] : user;
  },
);

/**
 * Décorateur pour récupérer l'adresse IP de la requête
 */
export const IpAddress = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return (
      request.headers['x-forwarded-for'] ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress
    );
  },
);

/**
 * Décorateur pour récupérer l'agent utilisateur de la requête
 */
export const UserAgent = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return request.headers['user-agent'];
  },
);

/**
 * Décorateur pour récupérer les paramètres de pagination de la requête
 */
export const Pagination = createParamDecorator(
  (data: { defaultLimit?: number; maxLimit?: number } | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest<Request>();
    const defaultLimit = data?.defaultLimit || 10;
    const maxLimit = data?.maxLimit || 100;

    const page = parseInt(request.query.page as string, 10) || 1;
    let limit = parseInt(request.query.limit as string, 10) || defaultLimit;
    limit = Math.min(limit, maxLimit);

    const sortBy = (request.query.sortBy as string) || 'createdAt';
    const sortOrder = (request.query.sortOrder as string) || 'desc';

    return {
      page,
      limit,
      sortBy,
      sortOrder,
    };
  },
);

/**
 * Décorateur pour marquer une route comme nécessitant un cache
 */
export const Cacheable = (ttl?: number) => SetMetadata('cacheable', { ttl });

/**
 * Décorateur pour marquer une route comme nécessitant une limite de taux
 */
export const RateLimit = (limit: number, windowMs: number) =>
  SetMetadata('rateLimit', { limit, windowMs });

/**
 * Décorateur pour marquer une route comme nécessitant une validation de schéma
 */
export const ValidateSchema = (schema: any) => SetMetadata('schema', schema);

/**
 * Décorateur pour marquer une route comme nécessitant un audit
 */
export const Audit = (action: string, resource: string) =>
  SetMetadata('audit', { action, resource });

/**
 * Décorateur pour marquer une route comme nécessitant une mesure de performance
 */
export const Measure = (name?: string) => SetMetadata('measure', { name });

/**
 * Décorateur pour marquer une route comme nécessitant une journalisation
 */
export const Log = (level?: 'debug' | 'info' | 'warn' | 'error') =>
  SetMetadata('log', { level: level || 'info' });

/**
 * Décorateur pour marquer une route comme nécessitant une compression
 */
export const Compress = () => SetMetadata('compress', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en streaming
 */
export const Stream = () => SetMetadata('stream', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en SSE
 */
export const SSE = () => SetMetadata('sse', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en WebSocket
 */
export const WebSocket = () => SetMetadata('websocket', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL
 */
export const GraphQL = () => SetMetadata('graphql', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en gRPC
 */
export const GRPC = () => SetMetadata('grpc', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en MQTT
 */
export const MQTT = () => SetMetadata('mqtt', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en AMQP
 */
export const AMQP = () => SetMetadata('amqp', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en Kafka
 */
export const Kafka = () => SetMetadata('kafka', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en Redis
 */
export const Redis = () => SetMetadata('redis', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en NATS
 */
export const NATS = () => SetMetadata('nats', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en MQTT
 */
export const MQTT2 = () => SetMetadata('mqtt2', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en STOMP
 */
export const STOMP = () => SetMetadata('stomp', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en Socket.IO
 */
export const SocketIO = () => SetMetadata('socketio', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en WebRTC
 */
export const WebRTC = () => SetMetadata('webrtc', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en HTTP/2
 */
export const HTTP2 = () => SetMetadata('http2', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en HTTP/3
 */
export const HTTP3 = () => SetMetadata('http3', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en QUIC
 */
export const QUIC = () => SetMetadata('quic', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en gRPC-Web
 */
export const GRPCWeb = () => SetMetadata('grpcweb', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over WebSocket
 */
export const GraphQLWS = () => SetMetadata('graphqlws', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over SSE
 */
export const GraphQLSSE = () => SetMetadata('graphqlsse', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over HTTP
 */
export const GraphQLHTTP = () => SetMetadata('graphqlhttp', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over HTTP/2
 */
export const GraphQLHTTP2 = () => SetMetadata('graphqlhttp2', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over HTTP/3
 */
export const GraphQLHTTP3 = () => SetMetadata('graphqlhttp3', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over QUIC
 */
export const GraphQLQUIC = () => SetMetadata('graphqlquic', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over gRPC
 */
export const GraphQLGRPC = () => SetMetadata('graphqlgrpc', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over gRPC-Web
 */
export const GraphQLGRPCWeb = () => SetMetadata('graphqlgrpcweb', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over MQTT
 */
export const GraphQLMQTT = () => SetMetadata('graphqlmqtt', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over AMQP
 */
export const GraphQLAMQP = () => SetMetadata('graphqlamqp', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over Kafka
 */
export const GraphQLKafka = () => SetMetadata('graphqlkafka', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over Redis
 */
export const GraphQLRedis = () => SetMetadata('graphqlredis', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over NATS
 */
export const GraphQLNATS = () => SetMetadata('graphqlnats', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over MQTT
 */
export const GraphQLMQTT2 = () => SetMetadata('graphqlmqtt2', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over STOMP
 */
export const GraphQLSTOMP = () => SetMetadata('graphqlstomp', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over Socket.IO
 */
export const GraphQLSocketIO = () => SetMetadata('graphqlsocketio', true);

/**
 * Décorateur pour marquer une route comme nécessitant une réponse en GraphQL over WebRTC
 */
export const GraphQLWebRTC = () => SetMetadata('graphqlwebrtc', true);
