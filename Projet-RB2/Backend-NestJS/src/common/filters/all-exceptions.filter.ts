import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    // En cas de test, nous pouvons simplement laisser l'exception se propager
    if (process.env.NODE_ENV === 'test') {
      if (exception instanceof HttpException) {
        throw exception;
      }
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'Internal Server Error';
    let details = null;

    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const response = exception.getResponse();
      
      if (typeof response === 'object') {
        message = (response as any).message || message;
        error = (response as any).error || error;
        details = (response as any).details || null;
      } else {
        message = response as string;
      }
    } else if (exception instanceof PrismaClientKnownRequestError) {
      // Gérer les erreurs Prisma
      switch (exception.code) {
        case 'P2002':
          statusCode = HttpStatus.CONFLICT;
          message = 'Unique constraint violation';
          details = exception.meta;
          break;
        case 'P2025':
          statusCode = HttpStatus.NOT_FOUND;
          message = 'Record not found';
          break;
        default:
          statusCode = HttpStatus.BAD_REQUEST;
          message = `Database error: ${exception.message}`;
      }
    }

    // Journaliser l'exception
    this.logger.error(
      `Exception: ${message}`,
      exception instanceof Error ? exception.stack : 'No stack trace',
      ctx.getRequest().url,
    );

    const responseBody = {
      statusCode,
      timestamp: new Date().toISOString(),
      path: httpAdapter.getRequestUrl(ctx.getRequest()),
      message,
      error,
      ...(details && { details }),
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, statusCode);
  }
}
