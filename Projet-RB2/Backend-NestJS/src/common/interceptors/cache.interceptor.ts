import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  Call<PERSON><PERSON><PERSON>,
  Inject,
  Logger,
} from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private reflector: Reflector,
  ) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    // Vérifier si la route est cacheable
    const cacheable = this.reflector.get<{ ttl?: number }>('cacheable', context.getHandler());
    if (!cacheable) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<Request>();
    const ttl = cacheable.ttl || 60; // 60 secondes par défaut
    const cacheKey = this.generateCacheKey(request);

    try {
      // Essayer de récupérer la réponse du cache
      const cachedResponse = await this.cacheManager.get(cacheKey);
      if (cachedResponse) {
        this.logger.debug(`Cache hit for key: ${cacheKey}`);
        return of(cachedResponse);
      }

      // Si pas de cache, exécuter le handler et mettre en cache la réponse
      this.logger.debug(`Cache miss for key: ${cacheKey}`);
      return next.handle().pipe(
        tap(response => {
          this.cacheManager.set(cacheKey, response, ttl * 1000);
        }),
      );
    } catch (error) {
      this.logger.error(`Cache error: ${error.message}`);
      return next.handle();
    }
  }

  private generateCacheKey(request: Request): string {
    const { method, originalUrl } = request;
    const queryParams = JSON.stringify(request.query);
    const bodyParams = JSON.stringify(request.body);
    
    // Générer une clé unique basée sur la méthode, l'URL, les paramètres de requête et le corps
    return `${method}:${originalUrl}:${queryParams}:${bodyParams}`;
  }
}
