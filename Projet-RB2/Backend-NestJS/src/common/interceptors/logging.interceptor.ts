import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, ip, headers } = request;
    const userAgent = headers['user-agent'] || 'unknown';
    const userId = request.user?.id || 'anonymous';

    const now = Date.now();
    
    this.logger.log(
      `Request: ${method} ${url} - User: ${userId} - IP: ${ip} - UserAgent: ${userAgent}`,
    );

    if (Object.keys(body).length > 0) {
      // Ne pas journaliser les mots de passe ou autres données sensibles
      const sanitizedBody = { ...body };
      if (sanitizedBody.password) sanitizedBody.password = '***';
      if (sanitizedBody.passwordConfirmation) sanitizedBody.passwordConfirmation = '***';
      if (sanitizedBody.token) sanitizedBody.token = '***';
      
      this.logger.debug(`Request body: ${JSON.stringify(sanitizedBody)}`);
    }

    return next.handle().pipe(
      tap({
        next: (data) => {
          const response = context.switchToHttp().getResponse();
          const { statusCode } = response;
          const responseTime = Date.now() - now;
          
          this.logger.log(
            `Response: ${method} ${url} - ${statusCode} - ${responseTime}ms`,
          );
          
          if (data && process.env.NODE_ENV === 'development') {
            const sanitizedData = this.sanitizeData(data);
            this.logger.debug(`Response data: ${JSON.stringify(sanitizedData)}`);
          }
        },
        error: (error) => {
          const responseTime = Date.now() - now;
          this.logger.error(
            `Response Error: ${method} ${url} - ${responseTime}ms`,
            error.stack,
          );
        },
      }),
    );
  }

  private sanitizeData(data: any): any {
    if (!data) return data;
    
    // Si c'est un tableau, sanitiser chaque élément
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }
    
    // Si c'est un objet, sanitiser les champs sensibles
    if (typeof data === 'object') {
      const sanitized = { ...data };
      
      // Supprimer les champs sensibles
      const sensitiveFields = ['password', 'token', 'secret'];
      sensitiveFields.forEach(field => {
        if (field in sanitized) {
          sanitized[field] = '***';
        }
      });
      
      // Traiter récursivement les objets imbriqués
      Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
          sanitized[key] = this.sanitizeData(sanitized[key]);
        }
      });
      
      return sanitized;
    }
    
    return data;
  }
}
