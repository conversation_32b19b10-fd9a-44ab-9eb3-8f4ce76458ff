import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  RequestTimeoutException,
  Logger,
} from '@nestjs/common';
import { Observable, throwError, TimeoutError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TimeoutInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TimeoutInterceptor.name);
  private readonly defaultTimeout: number;

  constructor(private readonly configService: ConfigService) {
    this.defaultTimeout = this.configService.get<number>('app.requestTimeout', 30000); // 30 secondes par défaut
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const timeoutValue = this.defaultTimeout;
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;

    return next.handle().pipe(
      timeout(timeoutValue),
      catchError(err => {
        if (err instanceof TimeoutError) {
          this.logger.warn(`Request timeout: ${method} ${url} (${timeoutValue}ms)`);
          return throwError(() => new RequestTimeoutException(
            `Request timeout: The operation took longer than ${timeoutValue}ms to complete.`
          ));
        }
        return throwError(() => err);
      }),
    );
  }
}
