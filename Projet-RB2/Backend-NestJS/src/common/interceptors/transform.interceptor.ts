import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  statusCode: number;
  message: string;
  data: T;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse();
    const statusCode = response.statusCode;

    return next.handle().pipe(
      map(data => {
        // Si la réponse est déjà formatée, la retourner telle quelle
        if (data && data.statusCode && data.message) {
          return data;
        }

        // Déterminer le message en fonction du code de statut
        let message = 'Success';
        if (statusCode >= 200 && statusCode < 300) {
          if (statusCode === 201) {
            message = 'Created successfully';
          } else if (statusCode === 204) {
            message = 'No content';
          }
        }

        // Formater la réponse
        return {
          statusCode,
          message,
          data,
          timestamp: new Date().toISOString(),
        };
      }),
    );
  }
}
