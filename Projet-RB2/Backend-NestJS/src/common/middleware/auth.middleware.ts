import { Injectable, NestMiddleware, Logger, UnauthorizedException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../modules/users/users.service';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthMiddleware.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Récupérer le token d'authentification
      const token = this.extractTokenFromHeader(req);
      
      if (!token) {
        // Pas de token, continuer sans authentification
        return next();
      }
      
      try {
        // Vérifier le token
        const payload = this.jwtService.verify(token, {
          secret: this.configService.get<string>('jwt.secret'),
        });
        
        // Récupérer l'utilisateur
        const user = await this.usersService.findOne(payload.sub);
        
        // Vérifier si l'utilisateur est actif
        if (!user.isActive) {
          throw new UnauthorizedException('User account is disabled');
        }
        
        // Ajouter l'utilisateur à la requête
        req.user = {
          id: user.id,
          email: user.email,
          role: user.role,
        };
      } catch (error) {
        // Token invalide, continuer sans authentification
        this.logger.warn(`Invalid token: ${error.message}`);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  }

  private extractTokenFromHeader(req: Request): string | undefined {
    const [type, token] = (req.headers.authorization || '').split(' ');
    return type === 'Bearer' ? token : undefined;
  }
}
