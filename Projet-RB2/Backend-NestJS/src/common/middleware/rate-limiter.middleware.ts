import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
// @ts-ignore: express-rate-limit n'a pas de déclaration de type pour l'importation ES6, utilisation de require à la place
const rateLimit = require('express-rate-limit');
// @ts-ignore: rate-limit-redis n'a pas de déclaration de type pour l'importation ES6, utilisation de require à la place
const RedisStore = require('rate-limit-redis');
import { createClient } from 'redis';

@Injectable()
export class RateLimiterMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RateLimiterMiddleware.name);
  private limiter: any;

  constructor(private readonly configService: ConfigService) {
    this.initializeLimiter();
  }

  private async initializeLimiter() {
    try {
      // Récupérer la configuration de Redis
      const redisUrl = this.configService.get<string>('redis.url');
      
      // Récupérer la configuration du rate limiter
      const windowMs = this.configService.get<number>('security.rateLimit.windowMs', 15 * 60 * 1000); // 15 minutes par défaut
      const max = this.configService.get<number>('security.rateLimit.max', 100); // 100 requêtes par fenêtre par défaut
      
      // Créer un client Redis
      const client = createClient({
        url: redisUrl,
      });
      
      await client.connect();
      
      // Créer le limiter
      this.limiter = rateLimit({
        windowMs,
        max,
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req: Request) => {
          // Utiliser l'adresse IP comme clé
          return (
            req.headers['x-forwarded-for'] ||
            req.connection.remoteAddress ||
            req.socket.remoteAddress ||
            'unknown'
          ).toString();
        },
        handler: (req: Request, res: Response) => {
          this.logger.warn(`Rate limit exceeded: ${req.ip}`);
          res.status(429).json({
            statusCode: 429,
            message: 'Too many requests, please try again later.',
            error: 'Too Many Requests',
          });
        },
        store: new RedisStore({
          sendCommand: (...args: string[]) => client.sendCommand(args),
          prefix: 'rl:',
        }),
      });
      
      this.logger.log(`Rate limiter initialized: ${max} requests per ${windowMs / 1000} seconds`);
    } catch (error) {
      this.logger.error(`Failed to initialize rate limiter: ${error.message}`);
      
      // Fallback to memory store
      this.limiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // 100 requests per windowMs
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req: Request, res: Response) => {
          this.logger.warn(`Rate limit exceeded: ${req.ip}`);
          res.status(429).json({
            statusCode: 429,
            message: 'Too many requests, please try again later.',
            error: 'Too Many Requests',
          });
        },
      });
      
      this.logger.log('Rate limiter initialized with memory store (fallback)');
    }
  }

  use(req: Request, res: Response, next: NextFunction) {
    if (this.limiter) {
      this.limiter(req, res, next);
    } else {
      next();
    }
  }
}
