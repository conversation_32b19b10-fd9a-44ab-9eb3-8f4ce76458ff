import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SecurityMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityMiddleware.name);

  constructor(private readonly configService: ConfigService) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Ajouter des en-têtes de sécurité supplémentaires
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('Referrer-Policy', 'no-referrer');
    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
    res.setHeader('Expect-CT', 'enforce, max-age=86400');
    res.setHeader('Feature-Policy', "camera 'none'; microphone 'none'; geolocation 'none'");

    // Vérifier les en-têtes de sécurité
    this.checkSecurityHeaders(req);

    // Vérifier les méthodes HTTP
    this.checkHttpMethod(req);

    // Vérifier les paramètres de requête
    this.checkQueryParams(req);

    next();
  }

  private checkSecurityHeaders(req: Request) {
    // Vérifier l'en-tête Content-Type
    const contentType = req.headers['content-type'];
    if (contentType && contentType.includes('text/html')) {
      this.logger.warn(`Suspicious Content-Type header: ${contentType}`);
    }

    // Vérifier l'en-tête User-Agent
    const userAgent = req.headers['user-agent'];
    if (!userAgent) {
      this.logger.warn('Missing User-Agent header');
    }

    // Vérifier l'en-tête Referer
    const referer = req.headers.referer;
    if (referer) {
      try {
        const refererUrl = new URL(referer);
        const allowedDomains = this.configService.get<string[]>('security.cors.origin', []);
        
        if (!allowedDomains.includes('*') && !allowedDomains.includes(refererUrl.origin)) {
          this.logger.warn(`Suspicious Referer header: ${referer}`);
        }
      } catch (error) {
        this.logger.warn(`Invalid Referer header: ${referer}`);
      }
    }
  }

  private checkHttpMethod(req: Request) {
    // Vérifier les méthodes HTTP autorisées
    const allowedMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'];
    if (!allowedMethods.includes(req.method)) {
      this.logger.warn(`Suspicious HTTP method: ${req.method}`);
    }
  }

  private checkQueryParams(req: Request) {
    // Vérifier les paramètres de requête suspects
    const suspiciousParams = ['<script>', 'javascript:', 'data:', 'vbscript:', 'onload=', 'onerror='];
    
    Object.keys(req.query).forEach(key => {
      const value = req.query[key] as string;
      
      if (typeof value === 'string') {
        for (const param of suspiciousParams) {
          if (value.toLowerCase().includes(param)) {
            this.logger.warn(`Suspicious query parameter: ${key}=${value}`);
            break;
          }
        }
      }
    });
  }
}
