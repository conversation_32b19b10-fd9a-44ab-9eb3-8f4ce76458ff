import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';

@Injectable()
export class ConfigService {
  constructor(private configService: NestConfigService) {}

  get(key: string): string {
    return this.configService.get<string>(key);
  }

  getNumber(key: string): number {
    const value = this.get(key);
    return value ? parseInt(value, 10) : undefined;
  }

  getBoolean(key: string): boolean {
    const value = this.get(key);
    return value === 'true' || value === '1';
  }

  // API Keys - Using environment variables for security
  getExternalApiConfig() {
    return {
      recommendationApiKey: this.get('RECOMMENDATION_API_KEY'),
      weatherApiKey: this.get('WEATHER_API_KEY'),
      mapApiKey: this.get('MAP_API_KEY'),
      analyticsApiKey: this.get('ANALYTICS_API_KEY')
    };
  }

  getDatabaseConfig() {
    return {
      host: this.get('DB_HOST'),
      port: this.getNumber('DB_PORT'),
      username: this.get('DB_USERNAME'),
      password: this.get('DB_PASSWORD'),
      database: this.get('DB_DATABASE'),
    };
  }

  getJwtConfig() {
    return {
      secret: this.get('JWT_SECRET'),
      expiresIn: this.get('JWT_EXPIRES_IN') || '1d',
    };
  }

  getAppConfig() {
    return {
      port: this.getNumber('PORT') || 3000,
      environment: this.get('NODE_ENV') || 'development',
      apiPrefix: this.get('API_PREFIX') || 'api',
      appName: this.get('APP_NAME') || 'Retreat And Be API',
    };
  }
}
