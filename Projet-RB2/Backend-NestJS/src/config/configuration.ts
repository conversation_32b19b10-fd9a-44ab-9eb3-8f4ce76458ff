import { ConfigModuleOptions } from '@nestjs/config';
import appConfig from './app.config';
import databaseConfig from './database.config';
import authConfig from './auth.config';
import securityConfig from './security.config';
import notificationsConfig from './notifications.config';
import matchingConfig from './matching.config';
import explanationLearningConfig from './explanation-learning.config';
import queryOptimizationConfig from './query-optimization.config';

export const configModuleOptions: ConfigModuleOptions = {
  isGlobal: true,
  load: [
    appConfig,
    databaseConfig,
    authConfig,
    securityConfig,
    notificationsConfig,
    matchingConfig,
    explanationLearningConfig,
    queryOptimizationConfig,
  ],
  envFilePath: ['.env.local', '.env'],
};
