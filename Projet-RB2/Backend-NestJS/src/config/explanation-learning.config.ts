import { registerAs } from '@nestjs/config';

/**
 * Configuration pour l'intégration des tests A/B et de l'apprentissage continu
 */
export default registerAs('recommendation.explanationLearning', () => ({
  /**
   * Activer/désactiver l'intégration
   */
  enabled: process.env.EXPLANATION_LEARNING_ENABLED !== 'false',

  /**
   * Nombre minimum d'interactions pour déclencher une optimisation
   * Valeurs plus élevées = plus de données nécessaires pour l'optimisation
   * Valeurs plus basses = optimisations plus fréquentes mais potentiellement moins fiables
   */
  minInteractionsForOptimization: parseInt(process.env.EXPLANATION_LEARNING_MIN_INTERACTIONS || '100', 10),

  /**
   * Niveau de confiance minimum pour appliquer les optimisations
   * Valeurs plus élevées = optimisations plus fiables mais moins fréquentes
   * Valeurs plus basses = optimisations plus fréquentes mais potentiellement moins fiables
   */
  minConfidenceLevel: parseFloat(process.env.EXPLANATION_LEARNING_MIN_CONFIDENCE || '0.8'),

  /**
   * Intervalle d'optimisation (en heures)
   * Fréquence à laquelle le système vérifie les tests A/B terminés pour appliquer les optimisations
   */
  optimizationInterval: parseInt(process.env.EXPLANATION_LEARNING_INTERVAL || '24', 10),

  /**
   * Taux d'apprentissage pour les mises à jour des poids des facteurs
   * Valeurs plus élevées = adaptation plus rapide aux résultats des tests A/B
   * Valeurs plus basses = changements plus progressifs
   */
  learningRate: parseFloat(process.env.EXPLANATION_LEARNING_RATE || '0.1'),

  /**
   * Activer/désactiver l'optimisation des templates d'explication
   */
  templateOptimizationEnabled: process.env.EXPLANATION_LEARNING_TEMPLATE_OPTIMIZATION !== 'false',

  /**
   * Activer/désactiver l'optimisation des poids des facteurs
   */
  factorWeightOptimizationEnabled: process.env.EXPLANATION_LEARNING_FACTOR_WEIGHT_OPTIMIZATION !== 'false',

  /**
   * Activer/désactiver le déploiement automatique des variantes gagnantes
   */
  autoDeployWinners: process.env.EXPLANATION_LEARNING_AUTO_DEPLOY === 'true',

  /**
   * Seuil d'amélioration pour le déploiement automatique
   * Valeurs plus élevées = déploiement uniquement pour des améliorations significatives
   * Valeurs plus basses = déploiement plus fréquent
   */
  autoDeployThreshold: parseFloat(process.env.EXPLANATION_LEARNING_AUTO_DEPLOY_THRESHOLD || '0.15'),
}));
