import appConfig from './app.config';
import databaseConfig from './database.config';
import jwtConfig from './jwt.config';
import securityConfig from './security.config';
import redisConfig from './redis.config';
import encryptionConfig from './encryption.config';
import testingConfig from './testing.config';
import continuousLearningConfig from './continuous-learning.config';

export default [
  appConfig,
  databaseConfig,
  jwtConfig,
  securityConfig,
  redisConfig,
  encryptionConfig,
  testingConfig,
  continuousLearningConfig,
];

export {
  appConfig,
  databaseConfig,
  jwtConfig,
  securityConfig,
  redisConfig,
  encryptionConfig,
  testingConfig,
  continuousLearningConfig,
};
