import { registerAs } from '@nestjs/config';

export default registerAs('matching', () => ({
  // Seuil de score pour envoyer une notification
  notificationThreshold: parseInt(process.env.MATCHING_NOTIFICATION_THRESHOLD || '80', 10),
  
  // Nombre maximum de résultats à retourner par défaut
  defaultLimit: parseInt(process.env.MATCHING_DEFAULT_LIMIT || '10', 10),
  
  // Nombre maximum de résultats à retourner (limite absolue)
  maxLimit: parseInt(process.env.MATCHING_MAX_LIMIT || '100', 10),
  
  // Pondérations des facteurs de compatibilité
  weights: {
    skillMatch: parseFloat(process.env.MATCHING_WEIGHT_SKILL || '0.35'),
    locationMatch: parseFloat(process.env.MATCHING_WEIGHT_LOCATION || '0.2'),
    ratingMatch: parseFloat(process.env.MATCHING_WEIGHT_RATING || '0.2'),
    availabilityMatch: parseFloat(process.env.MATCHING_WEIGHT_AVAILABILITY || '0.15'),
    budgetMatch: parseFloat(process.env.MATCHING_WEIGHT_BUDGET || '0.1'),
  },
  
  // Activation du système de notification
  enableNotifications: process.env.MATCHING_ENABLE_NOTIFICATIONS !== 'false',
  
  // Activation de la mise en cache des résultats
  enableCaching: process.env.MATCHING_ENABLE_CACHING !== 'false',
  
  // Durée de vie du cache en secondes
  cacheTtl: parseInt(process.env.MATCHING_CACHE_TTL || '3600', 10),
  
  // Activation de la journalisation détaillée
  enableDetailedLogging: process.env.MATCHING_ENABLE_DETAILED_LOGGING === 'true',
  
  // Activation des rappels pour les matchings non traités
  enableReminders: process.env.MATCHING_ENABLE_REMINDERS !== 'false',
  
  // Intervalle des rappels en heures
  reminderInterval: parseInt(process.env.MATCHING_REMINDER_INTERVAL || '24', 10),
}));
