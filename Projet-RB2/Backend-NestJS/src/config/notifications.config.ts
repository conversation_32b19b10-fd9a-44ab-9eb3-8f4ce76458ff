import { registerAs } from '@nestjs/config';

export default registerAs('notifications', () => ({
  // Activation des notifications par email
  enableEmailNotifications: process.env.NOTIFICATIONS_ENABLE_EMAIL !== 'false',
  
  // Activation des notifications push
  enablePushNotifications: process.env.NOTIFICATIONS_ENABLE_PUSH !== 'false',
  
  // Activation des notifications in-app
  enableInAppNotifications: process.env.NOTIFICATIONS_ENABLE_IN_APP !== 'false',
  
  // Délai minimum entre deux notifications du même type (en minutes)
  throttleInterval: parseInt(process.env.NOTIFICATIONS_THROTTLE_INTERVAL || '15', 10),
  
  // Configuration de l'email
  email: {
    from: process.env.NOTIFICATIONS_EMAIL_FROM || '<EMAIL>',
    replyTo: process.env.NOTIFICATIONS_EMAIL_REPLY_TO || '<EMAIL>',
    templates: {
      matching: process.env.NOTIFICATIONS_EMAIL_TEMPLATE_MATCHING || 'matching-notification',
      reminder: process.env.NOTIFICATIONS_EMAIL_TEMPLATE_REMINDER || 'matching-reminder',
    },
  },
  
  // Configuration des notifications push
  push: {
    vapidPublicKey: process.env.NOTIFICATIONS_PUSH_VAPID_PUBLIC_KEY,
    vapidPrivateKey: process.env.NOTIFICATIONS_PUSH_VAPID_PRIVATE_KEY,
    subject: process.env.NOTIFICATIONS_PUSH_SUBJECT || 'mailto:<EMAIL>',
  },
}));
