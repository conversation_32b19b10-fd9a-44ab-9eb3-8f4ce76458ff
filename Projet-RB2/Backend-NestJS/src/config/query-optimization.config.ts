import { registerAs } from '@nestjs/config';

/**
 * Configuration pour l'optimisation des requêtes
 */
export default registerAs('recommendation.queryOptimization', () => ({
  /**
   * Taille de page par défaut pour les requêtes paginées
   */
  defaultPageSize: parseInt(process.env.QUERY_OPTIMIZATION_DEFAULT_PAGE_SIZE || '50', 10),

  /**
   * Taille de page maximale pour les requêtes paginées
   */
  maxPageSize: parseInt(process.env.QUERY_OPTIMIZATION_MAX_PAGE_SIZE || '500', 10),

  /**
   * Activer/désactiver la mise en cache des requêtes
   */
  enableQueryCache: process.env.QUERY_OPTIMIZATION_ENABLE_CACHE !== 'false',

  /**
   * Durée de vie du cache en secondes
   */
  queryCacheTTL: parseInt(process.env.QUERY_OPTIMIZATION_CACHE_TTL || '300', 10),

  /**
   * Taille maximale du cache (nombre d'entrées)
   */
  queryCacheMaxSize: parseInt(process.env.QUERY_OPTIMIZATION_CACHE_MAX_SIZE || '1000', 10),

  /**
   * Activer/désactiver l'optimisation des requêtes
   */
  enabled: process.env.QUERY_OPTIMIZATION_ENABLED !== 'false',

  /**
   * Activer/désactiver les logs de performance des requêtes
   */
  enablePerformanceLogs: process.env.QUERY_OPTIMIZATION_PERFORMANCE_LOGS === 'true',

  /**
   * Seuil de durée des requêtes pour les logs de performance (en ms)
   */
  performanceLogThreshold: parseInt(process.env.QUERY_OPTIMIZATION_PERFORMANCE_LOG_THRESHOLD || '1000', 10),

  /**
   * Activer/désactiver l'optimisation automatique des requêtes
   */
  autoOptimization: process.env.QUERY_OPTIMIZATION_AUTO_OPTIMIZATION !== 'false',

  /**
   * Stratégies d'optimisation
   */
  strategies: {
    /**
     * Activer/désactiver l'utilisation de curseurs pour les grandes listes
     */
    useCursorPagination: process.env.QUERY_OPTIMIZATION_USE_CURSOR_PAGINATION !== 'false',

    /**
     * Activer/désactiver le traitement par lots pour les grandes opérations
     */
    useBatchProcessing: process.env.QUERY_OPTIMIZATION_USE_BATCH_PROCESSING !== 'false',

    /**
     * Activer/désactiver la sélection de champs spécifiques
     */
    useSelectiveFields: process.env.QUERY_OPTIMIZATION_USE_SELECTIVE_FIELDS !== 'false',

    /**
     * Activer/désactiver l'utilisation d'index composites
     */
    useCompositeIndexes: process.env.QUERY_OPTIMIZATION_USE_COMPOSITE_INDEXES !== 'false',
  },

  /**
   * Configuration des index composites
   */
  compositeIndexes: {
    /**
     * Index pour les recommandations
     */
    recommendation: [
      {
        name: 'recommendation_user_date',
        fields: ['userId', 'createdAt'],
      },
      {
        name: 'recommendation_item_type',
        fields: ['itemId', 'itemType'],
      },
    ],

    /**
     * Index pour les explications
     */
    explanation: [
      {
        name: 'explanation_recommendation',
        fields: ['recommendationId', 'factorType'],
      },
    ],

    /**
     * Index pour les tests A/B
     */
    abTest: [
      {
        name: 'abtest_status_date',
        fields: ['status', 'startDate'],
      },
    ],
  },
}));
