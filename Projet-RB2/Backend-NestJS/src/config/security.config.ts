import { registerAs } from '@nestjs/config';

export default registerAs('security', () => ({
  cors: {
    // En production, utiliser les origines spécifiées dans les variables d'environnement
    // ou les valeurs par défaut sécurisées
    origin: process.env.CORS_ORIGIN
      ? process.env.CORS_ORIGIN.split(',')
      : process.env.NODE_ENV === 'production'
        ? ['https://retreat-and-be.com', 'https://app.retreat-and-be.com']
        : ['http://localhost:3000', 'http://localhost:4200'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language', 'Accept', 'Origin', 'X-Requested-With'],
    exposedHeaders: ['Content-Disposition', 'X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
    maxAge: 86400, // 24 heures
    preflightContinue: false,
    optionsSuccessStatus: 204,
  },
  helmet: {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        // Utiliser des nonces ou des hashes au lieu de 'unsafe-inline' et 'unsafe-eval'
        scriptSrc: ["'self'", process.env.NODE_ENV === 'development' ? "'unsafe-eval'" : null].filter(Boolean),
        styleSrc: ["'self'", 'https://fonts.googleapis.com'],
        fontSrc: ["'self'", 'https://fonts.gstatic.com'],
        imgSrc: ["'self'", 'data:', 'https://res.cloudinary.com'],
        connectSrc: ["'self'", process.env.API_URL].filter(Boolean),
        // Ajouter des directives supplémentaires pour une meilleure sécurité
        objectSrc: ["'none'"],
        upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? true : null,
        blockAllMixedContent: process.env.NODE_ENV === 'production' ? true : null,
        frameAncestors: ["'none'"],
        formAction: ["'self'"],
      },
    },
    crossOriginEmbedderPolicy: process.env.NODE_ENV === 'production' ? true : false,
    crossOriginOpenerPolicy: { policy: 'same-origin' },
    crossOriginResourcePolicy: { policy: process.env.NODE_ENV === 'production' ? 'same-origin' : 'cross-origin' },
    dnsPrefetchControl: { allow: false },
    frameguard: { action: 'deny' },
    hidePoweredBy: true,
    hsts: {
      maxAge: 15552000, // 180 jours
      includeSubDomains: true,
      preload: true
    },
    ieNoOpen: true,
    noSniff: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    xssFilter: true,
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? String(15 * 60 * 1000), 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX ?? '100', 10), // Limite chaque IP à 100 requêtes par fenêtre
    standardHeaders: true, // Renvoie les en-têtes `RateLimit-*` pour informer le client
    legacyHeaders: false, // Désactive les en-têtes `X-RateLimit-*`
  },
  bcrypt: {
    saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS ?? '10', 10),
  },
  twoFactor: {
    window: parseInt(process.env.TWO_FACTOR_WINDOW ?? '2', 10), // Nombre de périodes de 30 secondes
  },
}));
