import { envSchema, EnvSchema } from './validation.schema';

export function validateEnv(config: Record<string, any>): EnvSchema {
  const parsed = envSchema.safeParse(config);
  if (!parsed.success) {
    // Affiche les erreurs de validation de façon lisible
    // (en production, tu pourrais logger ou throw différemment)
    console.error('Erreur de validation de la configuration:', parsed.error.format());
    throw new Error('Configuration .env invalide. Voir les logs pour le détail.');
  }
  return parsed.data;
}
