import { z } from 'zod';

export const envSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().regex(/^\d+$/).transform(Number).default('3000'),
  API_PREFIX: z.string().default('/api/v1'),

  // Database
  DATABASE_URL: z.string(),

  // JWT
  JWT_SECRET: z.string().min(32),
  JWT_REFRESH_SECRET: z.string().min(32),
  JWT_EXPIRATION: z.string().default('1h'),
  JWT_REFRESH_EXPIRATION: z.string().default('7d'),

  // CORS
  CORS_ORIGIN: z.string().default('http://localhost:5173'),

  // Security
  ENCRYPTION_KEY: z.string().min(32),
  ENCRYPTION_IV: z.string().min(16),

  // i18n
  I18N_DEFAULT_LANGUAGE: z.string().default('fr'),
  I18N_FALLBACK_LANGUAGE: z.string().default('en'),

  // Redis (optional)
  REDIS_URL: z.string().optional(),
});

export type EnvSchema = z.infer<typeof envSchema>;
