import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('sensitive_data')
export class SensitiveEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  label: string;

  @Column({ type: 'text' })
  encryptedValue: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  encryptionVersion: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}
