import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';
import { PrismaHealthIndicator } from './indicators/prisma.health';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private prismaHealth: PrismaHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Health check simple' })
  @ApiResponse({ status: 200, description: 'Service en bonne santé' })
  health() {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }

  @Get('check')
  @ApiOperation({ summary: 'Health check complet' })
  @ApiResponse({ status: 200, description: 'Vérification complète de santé' })
  @HealthCheck()
  check() {
    return this.health.check([
      // Vérification de la base de données
      () => this.prismaHealth.isHealthy('database'),

      // Vérification de la mémoire (moins de 300MB utilisés)
      () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),

      // Vérification de l'espace disque (moins de 90% utilisé)
      () => this.disk.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9
      }),
    ]);
  }

  @Get('ready')
  @ApiOperation({ summary: 'Readiness probe pour Kubernetes' })
  @ApiResponse({ status: 200, description: 'Service prêt à recevoir du trafic' })
  @HealthCheck()
  ready() {
    return this.health.check([
      // Vérifications critiques pour la readiness
      () => this.prismaHealth.isHealthy('database'),
    ]);
  }

  @Get('live')
  @ApiOperation({ summary: 'Liveness probe pour Kubernetes' })
  @ApiResponse({ status: 200, description: 'Service vivant' })
  live() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }
}
