import { Injectable, Logger } from '@nestjs/common';
// Assurez-vous d'installer @nestjs/terminus : npm install @nestjs/terminus
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  private readonly logger = new Logger(PrismaHealthIndicator.name);

  constructor(private readonly prismaService: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      // Exécuter une requête simple pour vérifier la connexion à la base de données
      await this.prismaService.$queryRaw`SELECT 1`;
      
      // Utilisation de la méthode protégée getStatus de la classe parente
      return super.getStatus(key, true);
    } catch (error) {
      this.logger.error(`Erreur de connexion à la base de données: ${error.message}`);
      
      throw new HealthCheckError(
        'Prisma health check failed',
        super.getStatus(key, false, { message: error.message }),
      );
    }
  }
}
