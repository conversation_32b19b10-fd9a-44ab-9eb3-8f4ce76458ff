# Internationalisation (i18n) dans Backend-NestJS

Ce document explique comment utiliser le système d'internationalisation dans le backend NestJS de l'application Retreat And Be.

## Structure des fichiers

```
Backend-NestJS/
├── src/
│   └── i18n/
│       ├── locales/
│       │   ├── fr/
│       │   │   └── common.json
│       │   └── en/
│       │       └── common.json
│       ├── interceptors/
│       │   └── language.interceptor.ts
│       ├── i18n.module.ts
│       ├── i18n.service.ts
│       ├── index.ts
│       └── README.md (ce fichier)
```

## Configuration

Le module d'internationalisation est configuré dans le fichier `i18n.module.ts`. Il utilise la bibliothèque `nestjs-i18n` pour gérer les traductions.

La langue par défaut est définie dans le fichier de configuration de l'application (`app.config.ts`).

## Fichiers de traduction

Les fichiers de traduction sont situés dans le dossier `src/i18n/locales/`. Ils sont organisés par langue et par namespace.

- `common.json` : Contient les traductions communes à toute l'application (messages d'erreur, messages de succès, etc.)

## Utilisation dans les contrôleurs et services

### 1. Injecter le service I18nService

```typescript
import { Injectable } from '@nestjs/common';
import { I18nService } from '../../i18n/i18n.service';

@Injectable()
export class MyService {
  constructor(private readonly i18nService: I18nService) {}

  someMethod() {
    const message = this.i18nService.translate('errors.not_found');
    // ...
  }
}
```

### 2. Utiliser le service pour traduire des messages

```typescript
import { Controller, Get } from '@nestjs/common';
import { I18nService } from '../../i18n/i18n.service';

@Controller('my-controller')
export class MyController {
  constructor(private readonly i18nService: I18nService) {}

  @Get()
  findAll() {
    return {
      message: this.i18nService.translate('welcome'),
      data: [/* ... */]
    };
  }
}
```

### 3. Utiliser des paramètres dans les traductions

```typescript
// Dans le fichier de traduction (common.json)
{
  "validation": {
    "min_length": "Ce champ doit contenir au moins {min} caractères"
  }
}

// Dans le code
const message = this.i18nService.translate('validation.min_length', { min: 8 });
// Résultat : "Ce champ doit contenir au moins 8 caractères"
```

### 4. Spécifier la langue

```typescript
const message = this.i18nService.translate('welcome', {}, 'en');
// Utilise la traduction anglaise
```

## Détection de la langue

La langue est automatiquement détectée à partir de la requête HTTP grâce à l'intercepteur `LanguageInterceptor`. L'ordre de détection est le suivant :

1. En-tête HTTP personnalisé (défini dans la configuration de l'application)
2. Paramètre de requête `lang`
3. Cookie `lang`
4. En-tête HTTP `Accept-Language`
5. Langue par défaut (définie dans la configuration de l'application)

## Formatage des dates, nombres et devises

Le service `I18nService` fournit également des méthodes pour formater les dates, nombres et devises selon la langue :

```typescript
// Formater une date
const formattedDate = this.i18nService.formatDate(new Date(), { dateStyle: 'full' });

// Formater un nombre
const formattedNumber = this.i18nService.formatNumber(1234.56);

// Formater une devise
const formattedCurrency = this.i18nService.formatCurrency(1234.56, 'EUR');
```

## Ajouter une nouvelle langue

1. Créez un nouveau dossier dans `src/i18n/locales/` avec le code de la langue (par exemple, `es` pour l'espagnol)
2. Copiez le fichier `common.json` d'une langue existante
3. Traduisez les valeurs dans ce fichier

## Ajouter de nouvelles traductions

1. Identifiez le fichier approprié (`common.json`)
2. Ajoutez votre nouvelle clé et sa traduction dans chaque langue
3. Utilisez la clé dans votre code avec la méthode `translate`

## Intégration avec le frontend

Le frontend utilise également un système d'internationalisation. Les messages d'erreur et autres textes provenant du backend sont traduits selon la langue préférée de l'utilisateur, qui est envoyée dans les en-têtes des requêtes HTTP.

## Ressources

- [Documentation nestjs-i18n](https://nestjs-i18n.com/)
- [Documentation Intl](https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Global_Objects/Intl)
