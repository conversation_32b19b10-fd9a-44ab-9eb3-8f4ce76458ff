import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { I18nModule as NestI18nModule, I18nJsonLoader } from 'nestjs-i18n';
import { join } from 'path';
import { I18nService } from './i18n.service';
import { LanguageInterceptor } from './interceptors/language.interceptor';
import { MockI18nService } from './mock-i18n.service';

@Global()
@Module({
  imports: [
    ConfigModule,
    // Commenté pour éviter les erreurs de démarrage
    /*
    NestI18nModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        fallbackLanguage: configService.get<string>('app.fallbackLanguage', 'fr'),
        loaderOptions: {
          path: join(__dirname, 'locales'),
          watch: configService.get<string>('app.nodeEnv') === 'development',
        },
      }),
      loader: I18nJsonLoader,
    }),
    */
  ],
  providers: [
    {
      provide: 'I18N',
      useClass: MockI18nService,
    },
    {
      provide: I18nService,
      useClass: MockI18nService,
    },
    LanguageInterceptor,
  ],
  exports: [
    'I18N',
    I18nService,
    LanguageInterceptor,
  ],
})
export class I18nModule {}
