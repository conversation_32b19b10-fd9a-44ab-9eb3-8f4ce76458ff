{"welcome": "Welcome to the Retreat And Be API", "errors": {"not_found": "Resource not found", "unauthorized": "Unauthorized", "forbidden": "Access forbidden", "bad_request": "Bad request", "internal_server": "Internal server error", "validation": "Validation error", "conflict": "Resource conflict", "too_many_requests": "Too many requests", "service_unavailable": "Service unavailable"}, "auth": {"login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "logout_success": "Logout successful", "register_success": "Registration successful", "register_failed": "Registration failed", "email_already_exists": "This email is already in use", "invalid_credentials": "Invalid credentials", "account_locked": "Account locked", "account_not_verified": "Account not verified", "verification_email_sent": "Verification email sent", "password_reset_email_sent": "Password reset email sent", "password_reset_success": "Password reset successful", "password_reset_failed": "Password reset failed", "token_expired": "Token expired", "token_invalid": "Invalid token", "mfa_required": "Two-factor authentication required", "mfa_setup_success": "Two-factor authentication setup successful", "mfa_verification_success": "Two-factor authentication verification successful", "mfa_verification_failed": "Two-factor authentication verification failed"}, "users": {"created": "User created successfully", "updated": "User updated successfully", "deleted": "User deleted successfully", "not_found": "User not found", "email_already_exists": "This email is already in use", "invalid_role": "Invalid role", "profile_updated": "Profile updated successfully", "password_changed": "Password changed successfully", "password_invalid": "Invalid password", "account_deactivated": "Account deactivated", "account_reactivated": "Account reactivated"}, "retreats": {"created": "Retreat created successfully", "updated": "Retreat updated successfully", "deleted": "Retreat deleted successfully", "not_found": "Retreat not found", "booking_success": "Booking successful", "booking_failed": "Booking failed", "booking_cancelled": "Booking cancelled", "booking_confirmed": "Booking confirmed", "no_availability": "No availability for this date", "invalid_dates": "Invalid dates", "invalid_capacity": "Invalid capacity", "invalid_price": "Invalid price"}, "professionals": {"created": "Professional created successfully", "updated": "Professional updated successfully", "deleted": "Professional deleted successfully", "not_found": "Professional not found", "application_submitted": "Application submitted successfully", "application_approved": "Application approved", "application_rejected": "Application rejected", "profile_incomplete": "Incomplete profile", "availability_updated": "Availability updated", "service_added": "Service added", "service_updated": "Service updated", "service_deleted": "Service deleted", "service_not_found": "Service not found"}, "validation": {"required": "This field is required", "min_length": "This field must contain at least {min} characters", "max_length": "This field must contain at most {max} characters", "email": "Invalid email", "password_too_weak": "Password too weak", "passwords_not_match": "Passwords do not match", "invalid_date": "Invalid date", "invalid_number": "Invalid number", "invalid_phone": "Invalid phone number", "invalid_url": "Invalid URL", "invalid_format": "Invalid format"}}