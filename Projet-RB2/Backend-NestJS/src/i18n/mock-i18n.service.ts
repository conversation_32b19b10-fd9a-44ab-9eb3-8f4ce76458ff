import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class MockI18nService {
  constructor(
    private readonly configService: ConfigService,
  ) {}

  /**
   * Translate a key
   * @param key Translation key
   * @param args Arguments for interpolation
   * @param lang Language code (optional)
   * @returns Translated string
   */
  translate(key: string, args?: Record<string, any>, lang?: string): string {
    // Retourner la clé telle quelle pour le développement
    return key;
  }

  /**
   * Get the language from the request
   * @param request Express request
   * @returns Language code
   */
  getLanguage(request: Request): string {
    const fallbackLanguage = this.configService.get<string>('I18N_FALLBACK_LANGUAGE', 'fr');
    
    // Try to get language from header
    const headerLang = request.headers['x-custom-lang'] as string;
    if (headerLang) {
      return headerLang;
    }

    // Try to get language from query parameter
    const queryLang = request.query.lang as string;
    if (queryLang) {
      return queryLang;
    }

    // Try to get language from cookie
    const cookieLang = request.cookies?.lang;
    if (cookieLang) {
      return cookieLang;
    }

    // Try to get language from Accept-Language header
    const acceptLanguage = request.headers['accept-language'];
    if (acceptLanguage) {
      const preferredLanguage = acceptLanguage.split(',')[0].trim().split(';')[0].substring(0, 2);
      return preferredLanguage;
    }

    // Return fallback language
    return fallbackLanguage;
  }

  /**
   * Format a date according to the language
   * @param date Date to format
   * @param options Intl.DateTimeFormatOptions
   * @param lang Language code
   * @returns Formatted date
   */
  formatDate(date: Date, options?: Intl.DateTimeFormatOptions, lang?: string): string {
    // Utiliser la langue par défaut configurée si aucune langue n'est spécifiée
    const defaultLang = this.configService.get<string>('I18N_FALLBACK_LANGUAGE', 'fr');
    return new Intl.DateTimeFormat(lang || defaultLang, options).format(date);
  }

  /**
   * Format a number according to the language
   * @param number Number to format
   * @param options Intl.NumberFormatOptions
   * @param lang Language code
   * @returns Formatted number
   */
  formatNumber(number: number, options?: Intl.NumberFormatOptions, lang?: string): string {
    // Utiliser la langue par défaut configurée si aucune langue n'est spécifiée
    const defaultLang = this.configService.get<string>('I18N_FALLBACK_LANGUAGE', 'fr');
    return new Intl.NumberFormat(lang || defaultLang, options).format(number);
  }

  /**
   * Format a currency according to the language
   * @param amount Amount to format
   * @param currency Currency code
   * @param options Intl.NumberFormatOptions
   * @param lang Language code
   * @returns Formatted currency
   */
  formatCurrency(
    amount: number,
    currency: string,
    options?: Intl.NumberFormatOptions,
    lang?: string,
  ): string {
    // Utiliser la langue par défaut configurée si aucune langue n'est spécifiée
    const defaultLang = this.configService.get<string>('I18N_FALLBACK_LANGUAGE', 'fr');
    return new Intl.NumberFormat(lang || defaultLang, {
      style: 'currency',
      currency,
      ...options,
    }).format(amount);
  }
}
