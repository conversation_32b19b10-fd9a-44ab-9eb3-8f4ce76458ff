import { IsString, IsNotEmpty, IsUUID, IsOptional, IsObject, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PayoutMethod } from '../enums/payout-method.enum';

export class CreateAffiliateDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiPropertyOptional({
    description: 'Site web de l\'affilié',
    example: 'https://www.example.com',
  })
  @IsUrl()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional({
    description: 'Biographie de l\'affilié',
    example: 'Je suis un affilié passionné par le bien-être et la méditation.',
  })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    description: 'Méthode de paiement',
    enum: PayoutMethod,
    example: PayoutMethod.BANK_TRANSFER,
  })
  @IsString()
  @IsOptional()
  paymentMethod?: PayoutMethod;

  @ApiPropertyOptional({
    description: 'Détails de paiement',
    example: { accountNumber: '*********', bankName: 'Example Bank' },
  })
  @IsObject()
  @IsOptional()
  paymentDetails?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Métadonnées',
    example: { source: 'website', campaign: 'summer2023' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
