import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsO<PERSON>, IsNumber, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateConversionFromOrderDto {
  @ApiProperty({
    description: 'ID de la commande',
    example: 'order-123',
  })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Montant de la commande',
    example: 99.99,
  })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiPropertyOptional({
    description: 'Métadonnées',
    example: { products: ['product-1', 'product-2'], discount: 10 },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
