import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsO<PERSON>al, IsObject, IsEnum, IsNumber, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ConversionType } from '../enums/conversion-type.enum';

export class CreateConversionDto {
  @ApiProperty({
    description: 'ID de l\'affilié',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  affiliateId: string;

  @ApiProperty({
    description: 'ID de la référence',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  referralId: string;

  @ApiProperty({
    description: 'Type de conversion',
    enum: ConversionType,
    example: ConversionType.PURCHASE,
  })
  @IsEnum(ConversionType)
  @IsNotEmpty()
  type: ConversionType;

  @ApiProperty({
    description: 'Montant de la conversion',
    example: 99.99,
  })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiPropertyOptional({
    description: 'ID de la commande',
    example: 'order-123',
  })
  @IsString()
  @IsOptional()
  orderId?: string;

  @ApiPropertyOptional({
    description: 'Métadonnées',
    example: { products: ['product-1', 'product-2'], discount: 10 },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
