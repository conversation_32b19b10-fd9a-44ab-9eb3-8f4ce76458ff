import { IsString, <PERSON>NotEmpty, <PERSON>UUID, <PERSON><PERSON>ptional, IsObject, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReferralType } from '../enums/referral-type.enum';

export class CreateReferralByCodeDto {
  @ApiProperty({
    description: 'Code de référence',
    example: 'JOHSMI1234',
  })
  @IsString()
  @IsNotEmpty()
  referralCode: string;

  @ApiProperty({
    description: 'ID de l\'utilisateur référé',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  referredUserId: string;

  @ApiPropertyOptional({
    description: 'Type de référence',
    enum: ReferralType,
    example: ReferralType.USER,
  })
  @IsEnum(ReferralType)
  @IsOptional()
  type?: ReferralType;

  @ApiPropertyOptional({
    description: 'M<PERSON><PERSON>onn<PERSON>',
    example: { source: 'website', campaign: 'summer2023' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
