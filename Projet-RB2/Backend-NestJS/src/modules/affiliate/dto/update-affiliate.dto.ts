import { IsString, IsOptional, IsObject, IsUrl, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PayoutMethod } from '../enums/payout-method.enum';
import { AffiliateLevel } from '../enums/affiliate-level.enum';

export class UpdateAffiliateDto {
  @ApiPropertyOptional({
    description: 'Site web de l\'affilié',
    example: 'https://www.example.com',
  })
  @IsUrl()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional({
    description: 'Biographie de l\'affilié',
    example: 'Je suis un affilié passionné par le bien-être et la méditation.',
  })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    description: 'Méthode de paiement',
    enum: PayoutMethod,
    example: PayoutMethod.BANK_TRANSFER,
  })
  @IsEnum(PayoutMethod)
  @IsOptional()
  paymentMethod?: PayoutMethod;

  @ApiPropertyOptional({
    description: 'Détails de paiement',
    example: { accountNumber: '*********', bankName: 'Example Bank' },
  })
  @IsObject()
  @IsOptional()
  paymentDetails?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Niveau de l\'affilié',
    enum: AffiliateLevel,
    example: AffiliateLevel.SILVER,
  })
  @IsEnum(AffiliateLevel)
  @IsOptional()
  level?: AffiliateLevel;

  @ApiPropertyOptional({
    description: 'Taux de commission',
    example: 15,
  })
  @IsOptional()
  commissionRate?: number;

  @ApiPropertyOptional({
    description: 'Métadonnées',
    example: { source: 'website', campaign: 'summer2023' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
