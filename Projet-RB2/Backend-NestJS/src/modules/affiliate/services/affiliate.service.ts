import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { ReferralService } from './referral.service';
import { CommissionService } from './commission.service';
import { CreateAffiliateDto } from '../dto/create-affiliate.dto';
import { UpdateAffiliateDto } from '../dto/update-affiliate.dto';
import { AffiliateStatus } from '../enums/affiliate-status.enum';
import { AffiliateLevel } from '../enums/affiliate-level.enum';

@Injectable()
export class AffiliateService {
  private readonly logger = new Logger(AffiliateService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
    private readonly referralService: ReferralService,
    private readonly commissionService: CommissionService,
  ) {}

  /**
   * Crée un nouvel affilié
   * @param createAffiliateDto Données de l'affilié
   * @returns L'affilié créé
   */
  async create(createAffiliateDto: CreateAffiliateDto) {
    try {
      this.logger.log(`Création d'un nouvel affilié: ${JSON.stringify(createAffiliateDto)}`);
      
      // Vérifier si l'utilisateur existe déjà en tant qu'affilié
      const existingAffiliate = await this.prisma.affiliate.findUnique({
        where: { userId: createAffiliateDto.userId },
      });
      
      if (existingAffiliate) {
        throw new BadRequestException(`L'utilisateur est déjà un affilié`);
      }
      
      // Générer un code de référence unique
      const referralCode = await this.generateReferralCode(createAffiliateDto.userId);
      
      // Créer l'affilié
      const affiliate = await this.prisma.affiliate.create({
        data: {
          userId: createAffiliateDto.userId,
          referralCode,
          website: createAffiliateDto.website,
          bio: createAffiliateDto.bio,
          paymentMethod: createAffiliateDto.paymentMethod,
          paymentDetails: createAffiliateDto.paymentDetails,
          status: AffiliateStatus.PENDING,
          level: AffiliateLevel.STANDARD,
          commissionRate: 10, // Taux de commission par défaut (10%)
          metadata: createAffiliateDto.metadata || {},
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_CREATED',
        payload: {
          affiliateId: affiliate.id,
          userId: affiliate.userId,
          referralCode: affiliate.referralCode,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Affilié créé avec succès: ${affiliate.id}`);
      return affiliate;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère tous les affiliés avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des affiliés
   * @returns Liste paginée des affiliés
   */
  async findAll(page = 1, limit = 10, status?: AffiliateStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    const [affiliates, total] = await Promise.all([
      this.prisma.affiliate.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              
              profilePicture: true,
            },
          },
        },
      }),
      this.prisma.affiliate.count({ where }),
    ]);
    
    return {
      affiliates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / (limit ?? 10)),
    };
  }

  /**
   * Récupère un affilié par son ID
   * @param id ID de l'affilié
   * @returns L'affilié
   */
  async findOne(id: string) {
    const affiliate = await this.prisma.affiliate.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            
            profilePicture: true,
          },
        },
        referrals: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
        },
        commissions: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
        },
        payouts: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });
    
    if (!affiliate) {
      throw new NotFoundException(`Affilié avec l'ID ${id} non trouvé`);
    }
    
    return affiliate;
  }

  /**
   * Récupère un affilié par l'ID de l'utilisateur
   * @param userId ID de l'utilisateur
   * @returns L'affilié
   */
  async findByUserId(userId: string) {
    const affiliate = await this.prisma.affiliate.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            
            profilePicture: true,
          },
        },
        referrals: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
        },
        commissions: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
        },
        payouts: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });
    
    if (!affiliate) {
      throw new NotFoundException(`Affilié avec l'ID utilisateur ${userId} non trouvé`);
    }
    
    return affiliate;
  }

  /**
   * Récupère un affilié par son code de référence
   * @param referralCode Code de référence
   * @returns L'affilié
   */
  async findByReferralCode(referralCode: string) {
    const affiliate = await this.prisma.affiliate.findFirst({
      where: { referralCode },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            
            profilePicture: true,
          },
        },
      },
    });
    
    if (!affiliate) {
      throw new NotFoundException(`Affilié avec le code de référence ${referralCode} non trouvé`);
    }
    
    return affiliate;
  }

  /**
   * Met à jour un affilié
   * @param id ID de l'affilié
   * @param updateAffiliateDto Données de mise à jour
   * @returns L'affilié mis à jour
   */
  async update(id: string, updateAffiliateDto: UpdateAffiliateDto) {
    try {
      // Vérifier si l'affilié existe
      await this.findOne(id);
      
      // Mettre à jour l'affilié
      const affiliate = await this.prisma.affiliate.update({
        where: { id },
        data: updateAffiliateDto,
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_UPDATED',
        payload: {
          affiliateId: affiliate.id,
          updates: updateAffiliateDto,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Affilié mis à jour avec succès: ${affiliate.id}`);
      return affiliate;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Approuve un affilié
   * @param id ID de l'affilié
   * @returns L'affilié approuvé
   */
  async approve(id: string) {
    try {
      // Vérifier si l'affilié existe
      const affiliate = await this.findOne(id);
      
      // Vérifier si l'affilié est déjà approuvé
      if (affiliate.status === AffiliateStatus.ACTIVE) {
        throw new BadRequestException(`L'affilié est déjà approuvé`);
      }
      
      // Mettre à jour le statut de l'affilié
      const updatedAffiliate = await this.prisma.affiliate.update({
        where: { id },
        data: {
          status: AffiliateStatus.ACTIVE,
          approvedAt: new Date(),
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_APPROVED',
        payload: {
          affiliateId: updatedAffiliate.id,
          userId: updatedAffiliate.userId,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Affilié approuvé avec succès: ${updatedAffiliate.id}`);
      return updatedAffiliate;
    } catch (error) {
      this.logger.error(`Erreur lors de l'approbation de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rejette un affilié
   * @param id ID de l'affilié
   * @param reason Raison du rejet
   * @returns L'affilié rejeté
   */
  async reject(id: string, reason?: string) {
    try {
      // Vérifier si l'affilié existe
      const affiliate = await this.findOne(id);
      
      // Vérifier si l'affilié est déjà rejeté
      if (affiliate.status === AffiliateStatus.REJECTED) {
        throw new BadRequestException(`L'affilié est déjà rejeté`);
      }
      
      // Mettre à jour le statut de l'affilié
      const updatedAffiliate = await this.prisma.affiliate.update({
        where: { id },
        data: {
          status: AffiliateStatus.REJECTED,
          rejectionReason: reason,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_REJECTED',
        payload: {
          affiliateId: updatedAffiliate.id,
          userId: updatedAffiliate.userId,
          reason,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Affilié rejeté avec succès: ${updatedAffiliate.id}`);
      return updatedAffiliate;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Suspend un affilié
   * @param id ID de l'affilié
   * @param reason Raison de la suspension
   * @returns L'affilié suspendu
   */
  async suspend(id: string, reason?: string) {
    try {
      // Vérifier si l'affilié existe
      const affiliate = await this.findOne(id);
      
      // Vérifier si l'affilié est déjà suspendu
      if (affiliate.status === AffiliateStatus.SUSPENDED) {
        throw new BadRequestException(`L'affilié est déjà suspendu`);
      }
      
      // Mettre à jour le statut de l'affilié
      const updatedAffiliate = await this.prisma.affiliate.update({
        where: { id },
        data: {
          status: AffiliateStatus.SUSPENDED,
          suspensionReason: reason,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_SUSPENDED',
        payload: {
          affiliateId: updatedAffiliate.id,
          userId: updatedAffiliate.userId,
          reason,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Affilié suspendu avec succès: ${updatedAffiliate.id}`);
      return updatedAffiliate;
    } catch (error) {
      this.logger.error(`Erreur lors de la suspension de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Réactive un affilié
   * @param id ID de l'affilié
   * @returns L'affilié réactivé
   */
  async reactivate(id: string) {
    try {
      // Vérifier si l'affilié existe
      const affiliate = await this.findOne(id);
      
      // Vérifier si l'affilié est déjà actif
      if (affiliate.status === AffiliateStatus.ACTIVE) {
        throw new BadRequestException(`L'affilié est déjà actif`);
      }
      
      // Mettre à jour le statut de l'affilié
      const updatedAffiliate = await this.prisma.affiliate.update({
        where: { id },
        data: {
          status: AffiliateStatus.ACTIVE,
          suspensionReason: null,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_REACTIVATED',
        payload: {
          affiliateId: updatedAffiliate.id,
          userId: updatedAffiliate.userId,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Affilié réactivé avec succès: ${updatedAffiliate.id}`);
      return updatedAffiliate;
    } catch (error) {
      this.logger.error(`Erreur lors de la réactivation de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour le niveau d'un affilié
   * @param id ID de l'affilié
   * @param level Niveau de l'affilié
   * @param commissionRate Taux de commission
   * @returns L'affilié mis à jour
   */
  async updateLevel(id: string, level: AffiliateLevel, commissionRate?: number) {
    try {
      // Vérifier si l'affilié existe
      const affiliate = await this.findOne(id);
      
      // Déterminer le taux de commission en fonction du niveau
      let newCommissionRate = commissionRate;
      
      if (!newCommissionRate) {
        switch (level) {
          case AffiliateLevel.STANDARD:
            newCommissionRate = 10;
            break;
          case AffiliateLevel.SILVER:
            newCommissionRate = 15;
            break;
          case AffiliateLevel.GOLD:
            newCommissionRate = 20;
            break;
          case AffiliateLevel.PLATINUM:
            newCommissionRate = 25;
            break;
          default:
            newCommissionRate = 10;
        }
      }
      
      // Mettre à jour le niveau et le taux de commission de l'affilié
      const updatedAffiliate = await this.prisma.affiliate.update({
        where: { id },
        data: {
          level,
          commissionRate: newCommissionRate,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'AFFILIATE_LEVEL_UPDATED',
        payload: {
          affiliateId: updatedAffiliate.id,
          userId: updatedAffiliate.userId,
          oldLevel: affiliate.level,
          newLevel: level,
          oldCommissionRate: affiliate.commissionRate,
          newCommissionRate: newCommissionRate,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Niveau d'affilié mis à jour avec succès: ${updatedAffiliate.id}, niveau: ${level}, taux: ${newCommissionRate}%`);
      return updatedAffiliate;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du niveau d'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un tableau de bord pour un affilié
   * @param id ID de l'affilié
   * @returns Tableau de bord de l'affilié
   */
  async getDashboard(id: string) {
    try {
      // Vérifier si l'affilié existe
      const affiliate = await this.findOne(id);
      
      // Récupérer les statistiques
      const [
        referralCount,
        conversionCount,
        totalCommission,
        totalPaid,
        pendingCommission,
        recentReferrals,
        recentCommissions,
        monthlyStats,
      ] = await Promise.all([
        this.prisma.referral.count({
          where: { affiliateId: id },
        }),
        this.prisma.conversion.count({
          where: { affiliateId: id },
        }),
        this.prisma.commission.aggregate({
          where: { affiliateId: id },
          _sum: {
            amount: true,
          },
        }),
        this.prisma.payout.aggregate({
          where: { affiliateId: id },
          _sum: {
            amount: true,
          },
        }),
        this.prisma.commission.aggregate({
          where: {
            affiliateId: id,
            status: 'PENDING',
          },
          _sum: {
            amount: true,
          },
        }),
        this.prisma.referral.findMany({
          where: { affiliateId: id },
          orderBy: {
            createdAt: 'desc',
          },
          take: 5,
          include: {
            referredUser: {
              select: {
                id: true,
                email: true,
                name: true,
                
                profilePicture: true,
              },
            },
          },
        }),
        this.prisma.commission.findMany({
          where: { affiliateId: id },
          orderBy: {
            createdAt: 'desc',
          },
          take: 5,
        }),
        this.getMonthlyStats(id),
      ]);
      
      return {
        affiliate: {
          id: affiliate.id,
          userId: affiliate.userId,
          referralCode: affiliate.referralCode,
          status: affiliate.status,
          level: affiliate.level,
          commissionRate: affiliate.commissionRate,
          createdAt: affiliate.createdAt,
        },
        stats: {
          referralCount,
          conversionCount,
          totalCommission: totalCommission._sum.amount || 0,
          totalPaid: totalPaid._sum.amount || 0,
          pendingCommission: pendingCommission._sum.amount || 0,
          availableBalance: (totalCommission._sum.amount || 0) - (totalPaid._sum.amount || 0),
        },
        recentReferrals,
        recentCommissions,
        monthlyStats,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du tableau de bord de l'affilié: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les statistiques mensuelles d'un affilié
   * @param affiliateId ID de l'affilié
   * @returns Statistiques mensuelles
   */
  private async getMonthlyStats(affiliateId: string) {
    // Récupérer les 6 derniers mois
    const months = [];
    const now = new Date();
    
    for (let i = 0; i < 6; i++) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push(month);
    }
    
    // Récupérer les statistiques pour chaque mois
    const stats = await Promise.all(
      months.map(async (month) => {
        const startDate = new Date(month.getFullYear(), month.getMonth(), 1);
        const endDate = new Date(month.getFullYear(), month.getMonth() + 1, 0);
        
        const [
          referralCount,
          conversionCount,
          commissionSum,
        ] = await Promise.all([
          this.prisma.referral.count({
            where: {
              affiliateId,
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            },
          }),
          this.prisma.conversion.count({
            where: {
              affiliateId,
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            },
          }),
          this.prisma.commission.aggregate({
            where: {
              affiliateId,
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            },
            _sum: {
              amount: true,
            },
          }),
        ]);
        
        return {
          month: month.toISOString().substring(0, 7),
          referralCount,
          conversionCount,
          commissionAmount: commissionSum._sum.amount || 0,
        };
      }),
    );
    
    // Trier les statistiques par mois (du plus récent au plus ancien)
    return stats.sort((a, b) => b.month.localeCompare(a.month));
  }

  /**
   * Génère un code de référence unique
   * @param userId ID de l'utilisateur
   * @returns Code de référence
   */
  private async generateReferralCode(userId: string): Promise<string> {
    // Récupérer les informations de l'utilisateur
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        name: true,
        
      },
    });
    
    if (!user) {
      throw new NotFoundException(`Utilisateur avec l'ID ${userId} non trouvé`);
    }
    
    // Générer un code de base à partir du nom de l'utilisateur
    let baseCode = '';
    
    if (user.firstName) {
      baseCode += user.firstName.substring(0, 3).toUpperCase();
    }
    
    if (user.lastName) {
      baseCode += user.lastName.substring(0, 3).toUpperCase();
    }
    
    if (baseCode.length < 3) {
      // Si le code est trop court, ajouter des caractères aléatoires
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      while (baseCode.length < 3) {
        baseCode += chars.charAt(Math.floor(Math.random() * chars.length));
      }
    }
    
    // Ajouter un nombre aléatoire
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    let referralCode = `${baseCode}${randomNum}`;
    
    // Vérifier si le code existe déjà
    let codeExists = await this.prisma.affiliate.findFirst({
      where: { referralCode },
    });
    
    // Si le code existe déjà, générer un nouveau code
    while (codeExists) {
      const newRandomNum = Math.floor(1000 + Math.random() * 9000);
      referralCode = `${baseCode}${newRandomNum}`;
      
      codeExists = await this.prisma.affiliate.findFirst({
        where: { referralCode },
      });
    }
    
    return referralCode;
  }

  /**
   * Récupère les statistiques des affiliés
   * @returns Statistiques des affiliés
   */
  async getAffiliateStats() {
    const [
      totalAffiliates,
      activeAffiliates,
      pendingAffiliates,
      suspendedAffiliates,
      rejectedAffiliates,
      totalReferrals,
      totalConversions,
      totalCommissions,
      totalPayouts,
    ] = await Promise.all([
      this.prisma.affiliate.count(),
      this.prisma.affiliate.count({
        where: { status: AffiliateStatus.ACTIVE },
      }),
      this.prisma.affiliate.count({
        where: { status: AffiliateStatus.PENDING },
      }),
      this.prisma.affiliate.count({
        where: { status: AffiliateStatus.SUSPENDED },
      }),
      this.prisma.affiliate.count({
        where: { status: AffiliateStatus.REJECTED },
      }),
      this.prisma.referral.count(),
      this.prisma.conversion.count(),
      this.prisma.commission.aggregate({
        _sum: {
          amount: true,
        },
      }),
      this.prisma.payout.aggregate({
        _sum: {
          amount: true,
        },
      }),
    ]);
    
    return {
      affiliates: {
        total: totalAffiliates,
        active: activeAffiliates,
        pending: pendingAffiliates,
        suspended: suspendedAffiliates,
        rejected: rejectedAffiliates,
      },
      referrals: totalReferrals,
      conversions: totalConversions,
      commissions: totalCommissions._sum.amount || 0,
      payouts: totalPayouts._sum.amount || 0,
      pendingPayouts: (totalCommissions._sum.amount || 0) - (totalPayouts._sum.amount || 0),
    };
  }
}
