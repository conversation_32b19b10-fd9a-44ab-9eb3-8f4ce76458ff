import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { CommissionService } from './commission.service';
import { ConversionType } from '../enums/conversion-type.enum';
import { ConversionStatus } from '../enums/conversion-status.enum';

@Injectable()
export class ConversionService {
  private readonly logger = new Logger(ConversionService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
    private readonly commissionService: CommissionService,
  ) {}

  /**
   * Crée une nouvelle conversion
   * @param affiliateId ID de l'affilié
   * @param referralId ID de la référence
   * @param type Type de conversion
   * @param amount Montant de la conversion
   * @param orderId ID de la commande
   * @param metadata Métadonnées
   * @returns La conversion créée
   */
  async create(
    affiliateId: string,
    referralId: string,
    type: ConversionType,
    amount: number,
    orderId?: string,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Création d'une nouvelle conversion: affilié ${affiliateId}, référence ${referralId}, montant ${amount}`);
      
      // Vérifier si l'affilié existe
      const affiliate = await this.prisma.affiliate.findUnique({
        where: { id: affiliateId },
      });
      
      if (!affiliate) {
        throw new NotFoundException(`Affilié avec l'ID ${affiliateId} non trouvé`);
      }
      
      // Vérifier si la référence existe
      const referral = await this.prisma.referral.findUnique({
        where: { id: referralId },
      });
      
      if (!referral) {
        throw new NotFoundException(`Référence avec l'ID ${referralId} non trouvée`);
      }
      
      // Vérifier si la référence appartient à l'affilié
      if (referral.affiliateId !== affiliateId) {
        throw new BadRequestException(`La référence n'appartient pas à cet affilié`);
      }
      
      // Créer la conversion
      const conversion = await this.prisma.conversion.create({
        data: {
          affiliateId,
          referralId,
          type,
          amount,
          orderId,
          status: ConversionStatus.PENDING,
          metadata,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'CONVERSION_CREATED',
        payload: {
          conversionId: conversion.id,
          affiliateId: conversion.affiliateId,
          referralId: conversion.referralId,
          type: conversion.type,
          amount: conversion.amount,
        },
        status: 'COMPLETED',
      });
      
      // Créer une commission pour cette conversion
      await this.commissionService.createFromConversion(conversion.id);
      
      this.logger.log(`Conversion créée avec succès: ${conversion.id}`);
      return conversion;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la conversion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée une conversion à partir d'une commande
   * @param orderId ID de la commande
   * @param amount Montant de la commande
   * @param userId ID de l'utilisateur
   * @param metadata Métadonnées
   * @returns La conversion créée ou null si l'utilisateur n'a pas été référé
   */
  async createFromOrder(
    orderId: string,
    amount: number,
    userId: string,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Création d'une conversion à partir de la commande ${orderId} pour l'utilisateur ${userId}`);
      
      // Vérifier si l'utilisateur a été référé
      const referral = await this.prisma.referral.findFirst({
        where: { referredUserId: userId },
      });
      
      if (!referral) {
        this.logger.log(`L'utilisateur ${userId} n'a pas été référé, aucune conversion créée`);
        return null;
      }
      
      // Créer la conversion
      return this.create(
        referral.affiliateId,
        referral.id,
        ConversionType.PURCHASE,
        amount,
        orderId,
        metadata,
      );
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la conversion à partir de la commande: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère toutes les conversions avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des conversions
   * @returns Liste paginée des conversions
   */
  async findAll(page = 1, limit = 10, status?: ConversionStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    const [conversions, total] = await Promise.all([
      this.prisma.conversion.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          affiliate: {
            select: {
              id: true,
              userId: true,
              referralCode: true,
              user: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          referral: {
            include: {
              referredUser: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          commissions: true,
        },
      }),
      this.prisma.conversion.count({ where }),
    ]);
    
    return {
      conversions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère une conversion par son ID
   * @param id ID de la conversion
   * @returns La conversion
   */
  async findOne(id: string) {
    const conversion = await this.prisma.conversion.findUnique({
      where: { id },
      include: {
        affiliate: {
          select: {
            id: true,
            userId: true,
            referralCode: true,
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        referral: {
          include: {
            referredUser: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        commissions: true,
      },
    });
    
    if (!conversion) {
      throw new NotFoundException(`Conversion avec l'ID ${id} non trouvée`);
    }
    
    return conversion;
  }

  /**
   * Récupère les conversions d'un affilié
   * @param affiliateId ID de l'affilié
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des conversions
   * @returns Liste paginée des conversions
   */
  async findByAffiliateId(affiliateId: string, page = 1, limit = 10, status?: ConversionStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {
      affiliateId,
    };
    
    if (status) {
      where.status = status;
    }
    
    const [conversions, total] = await Promise.all([
      this.prisma.conversion.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          referral: {
            include: {
              referredUser: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          commissions: true,
        },
      }),
      this.prisma.conversion.count({ where }),
    ]);
    
    return {
      conversions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les conversions d'une référence
   * @param referralId ID de la référence
   * @returns Liste des conversions
   */
  async findByReferralId(referralId: string) {
    return this.prisma.conversion.findMany({
      where: { referralId },
      include: {
        commissions: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Met à jour le statut d'une conversion
   * @param id ID de la conversion
   * @param status Nouveau statut
   * @returns La conversion mise à jour
   */
  async updateStatus(id: string, status: ConversionStatus) {
    try {
      // Vérifier si la conversion existe
      const conversion = await this.findOne(id);
      
      // Mettre à jour le statut
      const updatedConversion = await this.prisma.conversion.update({
        where: { id },
        data: { status },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'CONVERSION_STATUS_UPDATED',
        payload: {
          conversionId: updatedConversion.id,
          oldStatus: conversion.status,
          newStatus: status,
        },
        status: 'COMPLETED',
      });
      
      // Mettre à jour le statut des commissions associées
      if (status === ConversionStatus.APPROVED) {
        await this.commissionService.approveByConversionId(id);
      } else if (status === ConversionStatus.REJECTED) {
        await this.commissionService.rejectByConversionId(id);
      }
      
      this.logger.log(`Statut de la conversion mis à jour avec succès: ${updatedConversion.id}, statut: ${status}`);
      return updatedConversion;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du statut de la conversion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Approuve une conversion
   * @param id ID de la conversion
   * @returns La conversion approuvée
   */
  async approve(id: string) {
    try {
      // Vérifier si la conversion existe
      const conversion = await this.findOne(id);
      
      // Vérifier si la conversion est déjà approuvée
      if (conversion.status === ConversionStatus.APPROVED) {
        throw new BadRequestException(`La conversion est déjà approuvée`);
      }
      
      // Mettre à jour le statut
      const updatedConversion = await this.prisma.conversion.update({
        where: { id },
        data: {
          status: ConversionStatus.APPROVED,
          approvedAt: new Date(),
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'CONVERSION_APPROVED',
        payload: {
          conversionId: updatedConversion.id,
          affiliateId: updatedConversion.affiliateId,
          amount: updatedConversion.amount,
        },
        status: 'COMPLETED',
      });
      
      // Approuver les commissions associées
      await this.commissionService.approveByConversionId(id);
      
      this.logger.log(`Conversion approuvée avec succès: ${updatedConversion.id}`);
      return updatedConversion;
    } catch (error) {
      this.logger.error(`Erreur lors de l'approbation de la conversion: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rejette une conversion
   * @param id ID de la conversion
   * @param reason Raison du rejet
   * @returns La conversion rejetée
   */
  async reject(id: string, reason?: string) {
    try {
      // Vérifier si la conversion existe
      const conversion = await this.findOne(id);
      
      // Vérifier si la conversion est déjà rejetée
      if (conversion.status === ConversionStatus.REJECTED) {
        throw new BadRequestException(`La conversion est déjà rejetée`);
      }
      
      // Mettre à jour le statut
      const updatedConversion = await this.prisma.conversion.update({
        where: { id },
        data: {
          status: ConversionStatus.REJECTED,
          rejectionReason: reason,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'CONVERSION_REJECTED',
        payload: {
          conversionId: updatedConversion.id,
          affiliateId: updatedConversion.affiliateId,
          reason,
        },
        status: 'COMPLETED',
      });
      
      // Rejeter les commissions associées
      await this.commissionService.rejectByConversionId(id);
      
      this.logger.log(`Conversion rejetée avec succès: ${updatedConversion.id}`);
      return updatedConversion;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet de la conversion: ${error.message}`);
      throw error;
    }
  }
}
