import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { CommissionService } from './commission.service';
import { PayoutStatus } from '../enums/payout-status.enum';
import { PayoutMethod } from '../enums/payout-method.enum';

@Injectable()
export class PayoutService {
  private readonly logger = new Logger(PayoutService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
    private readonly commissionService: CommissionService,
  ) {}

  /**
   * Crée un nouveau paiement
   * @param affiliateId ID de l'affilié
   * @param amount Montant du paiement
   * @param method Méthode de paiement
   * @param commissionIds IDs des commissions
   * @param description Description
   * @param metadata Métadonnées
   * @returns Le paiement créé
   */
  async create(
    affiliateId: string,
    amount: number,
    method: PayoutMethod,
    commissionIds: string[] = [],
    description?: string,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Création d'un nouveau paiement: affilié ${affiliateId}, montant ${amount}`);
      
      // Vérifier si l'affilié existe
      const affiliate = await this.prisma.affiliate.findUnique({
        where: { id: affiliateId },
      });
      
      if (!affiliate) {
        throw new NotFoundException(`Affilié avec l'ID ${affiliateId} non trouvé`);
      }
      
      // Vérifier le solde disponible
      const { availableBalance } = await this.commissionService.getAvailableBalance(affiliateId);
      
      if (amount > availableBalance) {
        throw new BadRequestException(`Montant du paiement (${amount}) supérieur au solde disponible (${availableBalance})`);
      }
      
      // Créer le paiement
      const payout = await this.prisma.payout.create({
        data: {
          affiliateId,
          amount,
          method,
          description,
          status: PayoutStatus.PENDING,
          metadata,
        },
      });
      
      // Associer les commissions au paiement
      if (commissionIds.length > 0) {
        await this.prisma.commission.updateMany({
          where: {
            id: {
              in: commissionIds,
            },
          },
          data: {
            payoutId: payout.id,
          },
        });
      } else {
        // Si aucune commission n'est spécifiée, associer automatiquement les commissions disponibles
        const availableCommissions = await this.commissionService.getAvailableCommissions(affiliateId);
        
        let remainingAmount = amount;
        const commissionsToUpdate = [];
        
        for (const commission of availableCommissions) {
          if (remainingAmount <= 0) break;
          
          commissionsToUpdate.push(commission.id);
          remainingAmount -= commission.amount;
        }
        
        if (commissionsToUpdate.length > 0) {
          await this.prisma.commission.updateMany({
            where: {
              id: {
                in: commissionsToUpdate,
              },
            },
            data: {
              payoutId: payout.id,
            },
          });
        }
      }
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'PAYOUT_CREATED',
        payload: {
          payoutId: payout.id,
          affiliateId: payout.affiliateId,
          amount: payout.amount,
          method: payout.method,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Paiement créé avec succès: ${payout.id}`);
      return payout;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du paiement: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère tous les paiements avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des paiements
   * @returns Liste paginée des paiements
   */
  async findAll(page = 1, limit = 10, status?: PayoutStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    const [payouts, total] = await Promise.all([
      this.prisma.payout.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          affiliate: {
            select: {
              id: true,
              userId: true,
              referralCode: true,
              user: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          commissions: true,
        },
      }),
      this.prisma.payout.count({ where }),
    ]);
    
    return {
      payouts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère un paiement par son ID
   * @param id ID du paiement
   * @returns Le paiement
   */
  async findOne(id: string) {
    const payout = await this.prisma.payout.findUnique({
      where: { id },
      include: {
        affiliate: {
          select: {
            id: true,
            userId: true,
            referralCode: true,
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        commissions: true,
      },
    });
    
    if (!payout) {
      throw new NotFoundException(`Paiement avec l'ID ${id} non trouvé`);
    }
    
    return payout;
  }

  /**
   * Récupère les paiements d'un affilié
   * @param affiliateId ID de l'affilié
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des paiements
   * @returns Liste paginée des paiements
   */
  async findByAffiliateId(affiliateId: string, page = 1, limit = 10, status?: PayoutStatus) {
    const skip = (page - 1) * limit;
    
    const where: any = {
      affiliateId,
    };
    
    if (status) {
      where.status = status;
    }
    
    const [payouts, total] = await Promise.all([
      this.prisma.payout.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          commissions: true,
        },
      }),
      this.prisma.payout.count({ where }),
    ]);
    
    return {
      payouts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Met à jour le statut d'un paiement
   * @param id ID du paiement
   * @param status Nouveau statut
   * @returns Le paiement mis à jour
   */
  async updateStatus(id: string, status: PayoutStatus) {
    try {
      // Vérifier si le paiement existe
      const payout = await this.findOne(id);
      
      // Mettre à jour le statut
      const updatedPayout = await this.prisma.payout.update({
        where: { id },
        data: { status },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'PAYOUT_STATUS_UPDATED',
        payload: {
          payoutId: updatedPayout.id,
          oldStatus: payout.status,
          newStatus: status,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Statut du paiement mis à jour avec succès: ${updatedPayout.id}, statut: ${status}`);
      return updatedPayout;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du statut du paiement: ${error.message}`);
      throw error;
    }
  }

  /**
   * Approuve un paiement
   * @param id ID du paiement
   * @param transactionId ID de la transaction
   * @returns Le paiement approuvé
   */
  async approve(id: string, transactionId?: string) {
    try {
      // Vérifier si le paiement existe
      const payout = await this.findOne(id);
      
      // Vérifier si le paiement est déjà approuvé
      if (payout.status === PayoutStatus.COMPLETED) {
        throw new BadRequestException(`Le paiement est déjà approuvé`);
      }
      
      // Mettre à jour le statut
      const updatedPayout = await this.prisma.payout.update({
        where: { id },
        data: {
          status: PayoutStatus.COMPLETED,
          completedAt: new Date(),
          transactionId,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'PAYOUT_APPROVED',
        payload: {
          payoutId: updatedPayout.id,
          affiliateId: updatedPayout.affiliateId,
          amount: updatedPayout.amount,
          transactionId,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Paiement approuvé avec succès: ${updatedPayout.id}`);
      return updatedPayout;
    } catch (error) {
      this.logger.error(`Erreur lors de l'approbation du paiement: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rejette un paiement
   * @param id ID du paiement
   * @param reason Raison du rejet
   * @returns Le paiement rejeté
   */
  async reject(id: string, reason?: string) {
    try {
      // Vérifier si le paiement existe
      const payout = await this.findOne(id);
      
      // Vérifier si le paiement est déjà rejeté
      if (payout.status === PayoutStatus.REJECTED) {
        throw new BadRequestException(`Le paiement est déjà rejeté`);
      }
      
      // Mettre à jour le statut
      const updatedPayout = await this.prisma.payout.update({
        where: { id },
        data: {
          status: PayoutStatus.REJECTED,
          rejectionReason: reason,
        },
      });
      
      // Dissocier les commissions du paiement
      await this.prisma.commission.updateMany({
        where: {
          payoutId: id,
        },
        data: {
          payoutId: null,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'PAYOUT_REJECTED',
        payload: {
          payoutId: updatedPayout.id,
          affiliateId: updatedPayout.affiliateId,
          reason,
        },
        status: 'COMPLETED',
      });
      
      this.logger.log(`Paiement rejeté avec succès: ${updatedPayout.id}`);
      return updatedPayout;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet du paiement: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un rapport de paiement
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Rapport de paiement
   */
  async generatePayoutReport(startDate: Date, endDate: Date) {
    try {
      // Récupérer les paiements dans la période
      const payouts = await this.prisma.payout.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          affiliate: {
            select: {
              id: true,
              userId: true,
              referralCode: true,
              user: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      // Calculer les statistiques
      const totalAmount = payouts.reduce((sum: number, payout: any) => sum + payout.amount, 0);
      const completedPayouts = payouts.filter((payout: any) => payout.status === PayoutStatus.COMPLETED);
      const completedAmount = completedPayouts.reduce((sum: number, payout: any) => sum + payout.amount, 0);
      const pendingPayouts = payouts.filter((payout: any) => payout.status === PayoutStatus.PENDING);
      const pendingAmount = pendingPayouts.reduce((sum: number, payout: any) => sum + payout.amount, 0);
      const rejectedPayouts = payouts.filter((payout: any) => payout.status === PayoutStatus.REJECTED);
      const rejectedAmount = rejectedPayouts.reduce((sum: number, payout: any) => sum + payout.amount, 0);
      
      // Grouper par méthode de paiement
      const payoutsByMethod: Record<string, any> = {};
      payouts.forEach((payout: any) => {
        if (!payoutsByMethod[payout.method]) {
          payoutsByMethod[payout.method] = {
            count: 0,
            amount: 0,
          };
        }
        
        (payoutsByMethod[payout.method] as any).count++;
        (payoutsByMethod[payout.method] as any).amount += payout.amount;
      });
      
      // Grouper par affilié
      const payoutsByAffiliate: Record<string, any> = {};
      payouts.forEach((payout: any) => {
        const affiliateId = payout.affiliateId;
        
        if (!(payoutsByAffiliate as any)[affiliateId]) {
          payoutsByAffiliate[affiliateId] = {
            affiliate: payout.affiliate,
            count: 0,
            amount: 0,
          };
        }
        
        (payoutsByAffiliate[affiliateId] as any).count++;
        (payoutsByAffiliate[affiliateId] as any).amount += payout.amount;
      });
      
      return {
        period: {
          startDate,
          endDate,
        },
        summary: {
          totalPayouts: payouts.length,
          totalAmount,
          completedPayouts: completedPayouts.length,
          completedAmount,
          pendingPayouts: pendingPayouts.length,
          pendingAmount,
          rejectedPayouts: rejectedPayouts.length,
          rejectedAmount,
        },
        byMethod: payoutsByMethod,
        byAffiliate: Object.values(payoutsByAffiliate),
        payouts,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du rapport de paiement: ${error.message}`);
      throw error;
    }
  }
}
