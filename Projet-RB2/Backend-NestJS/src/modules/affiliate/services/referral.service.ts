import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { ConversionService } from './conversion.service';
import { ReferralStatus } from '../enums/referral-status.enum';
import { ReferralType } from '../enums/referral-type.enum';

@Injectable()
export class ReferralService {
  private readonly logger = new Logger(ReferralService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
  ) {}

  /**
   * Crée une nouvelle référence par code
   * @param referralCode Code de référence
   * @param referredUserId ID de l'utilisateur référé
   * @param type Type de référence
   * @param metadata Métadonnées
   * @returns La référence créée
   */
  async createByCode(
    createReferralByCodeDto: {
      referralCode: string;
      referredUserId: string;
      type?: ReferralType;
      metadata?: Record<string, any>;
    }
  ) {
    try {
      const { referralCode, referredUserId, type = ReferralType.USER, metadata = {} } = createReferralByCodeDto;

      this.logger.log(`Création d'une nouvelle référence par code: ${referralCode}, utilisateur ${referredUserId}`);

      // Trouver l'affilié par code de référence
      const affiliate = await this.prisma.affiliate.findFirst({
        where: { referralCode },
      });

      if (!affiliate) {
        throw new NotFoundException(`Affilié avec le code de référence ${referralCode} non trouvé`);
      }

      // Utiliser la méthode create existante
      return this.create(affiliate.id, referredUserId, type, metadata);
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la référence par code: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée une nouvelle référence
   * @param affiliateId ID de l'affilié
   * @param referredUserId ID de l'utilisateur référé
   * @param type Type de référence
   * @param metadata Métadonnées
   * @returns La référence créée
   */
  async create(
    affiliateId: string,
    referredUserId: string,
    type: ReferralType = ReferralType.USER,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Création d'une nouvelle référence: affilié ${affiliateId}, utilisateur ${referredUserId}`);

      // Vérifier si l'affilié existe
      const affiliate = await this.prisma.affiliate.findUnique({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        throw new NotFoundException(`Affilié avec l'ID ${affiliateId} non trouvé`);
      }

      // Vérifier si l'utilisateur référé existe
      const referredUser = await this.prisma.user.findUnique({
        where: { id: referredUserId },
      });

      if (!referredUser) {
        throw new NotFoundException(`Utilisateur avec l'ID ${referredUserId} non trouvé`);
      }

      // Vérifier si la référence existe déjà
      const existingReferral = await this.prisma.referral.findFirst({
        where: {
          affiliateId,
          referredUserId,
        },
      });

      if (existingReferral) {
        throw new BadRequestException(`Une référence existe déjà pour cet utilisateur`);
      }

      // Créer la référence
      const referral = await this.prisma.referral.create({
        data: {
          affiliateId,
          referredUserId,
          type,
          status: ReferralStatus.PENDING,
          metadata,
        },
      });

      // Émettre un événement
      await this.eventsService.create({
        eventType: 'REFERRAL_CREATED',
        payload: {
          referralId: referral.id,
          affiliateId: referral.affiliateId,
          referredUserId: referral.referredUserId,
          type: referral.type,
        },
        status: 'COMPLETED',
      });

      this.logger.log(`Référence créée avec succès: ${referral.id}`);
      return referral;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la référence: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée une référence à partir d'un code de référence
   * @param referralCode Code de référence
   * @param referredUserId ID de l'utilisateur référé
   * @param type Type de référence
   * @param metadata Métadonnées
   * @returns La référence créée
   */
  async createFromReferralCode(
    referralCode: string,
    referredUserId: string,
    type: ReferralType = ReferralType.USER,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Création d'une référence à partir du code ${referralCode} pour l'utilisateur ${referredUserId}`);

      // Récupérer l'affilié par son code de référence
      const affiliate = await this.prisma.affiliate.findFirst({
        where: { referralCode },
      });

      if (!affiliate) {
        throw new NotFoundException(`Affilié avec le code de référence ${referralCode} non trouvé`);
      }

      // Vérifier si l'affilié est actif
      if (affiliate.status !== 'ACTIVE') {
        throw new BadRequestException(`L'affilié n'est pas actif`);
      }

      // Créer la référence
      return this.create(affiliate.id, referredUserId, type, metadata);
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la référence à partir du code: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère toutes les références avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des références
   * @returns Liste paginée des références
   */
  async findAll(page = 1, limit = 10, status?: ReferralStatus) {
    const skip = (page - 1) * limit;

    const where: any = {};

    if (status) {
      where.status = status;
    }

    const [referrals, total] = await Promise.all([
      this.prisma.referral.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          affiliate: {
            select: {
              id: true,
              userId: true,
              referralCode: true,
              user: {
                select: {
                  id: true,
                  email: true,
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          referredUser: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      }),
      this.prisma.referral.count({ where }),
    ]);

    return {
      referrals,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère une référence par son ID
   * @param id ID de la référence
   * @returns La référence
   */
  async findOne(id: string) {
    const referral = await this.prisma.referral.findUnique({
      where: { id },
      include: {
        affiliate: {
          select: {
            id: true,
            userId: true,
            referralCode: true,
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        referredUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        conversions: true,
      },
    });

    if (!referral) {
      throw new NotFoundException(`Référence avec l'ID ${id} non trouvée`);
    }

    return referral;
  }

  /**
   * Récupère les références d'un affilié
   * @param affiliateId ID de l'affilié
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des références
   * @returns Liste paginée des références
   */
  async findByAffiliateId(affiliateId: string, page = 1, limit = 10, status?: ReferralStatus) {
    const skip = (page - 1) * limit;

    const where: any = {
      affiliateId,
    };

    if (status) {
      where.status = status;
    }

    const [referrals, total] = await Promise.all([
      this.prisma.referral.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          referredUser: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          conversions: true,
        },
      }),
      this.prisma.referral.count({ where }),
    ]);

    return {
      referrals,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les références d'un utilisateur référé
   * @param referredUserId ID de l'utilisateur référé
   * @returns Liste des références
   */
  async findByReferredUserId(referredUserId: string) {
    return this.prisma.referral.findMany({
      where: { referredUserId },
      include: {
        affiliate: {
          select: {
            id: true,
            userId: true,
            referralCode: true,
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        conversions: true,
      },
    });
  }

  /**
   * Met à jour le statut d'une référence
   * @param id ID de la référence
   * @param status Nouveau statut
   * @returns La référence mise à jour
   */
  async updateStatus(id: string, status: ReferralStatus) {
    try {
      // Vérifier si la référence existe
      const referral = await this.findOne(id);

      // Mettre à jour le statut
      const updatedReferral = await this.prisma.referral.update({
        where: { id },
        data: { status },
      });

      // Émettre un événement
      await this.eventsService.create({
        eventType: 'REFERRAL_STATUS_UPDATED',
        payload: {
          referralId: updatedReferral.id,
          oldStatus: referral.status,
          newStatus: status,
        },
        status: 'COMPLETED',
      });

      this.logger.log(`Statut de la référence mis à jour avec succès: ${updatedReferral.id}, statut: ${status}`);
      return updatedReferral;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du statut de la référence: ${error.message}`);
      throw error;
    }
  }

  /**
   * Valide une référence
   * @param id ID de la référence
   * @returns La référence validée
   */
  async validate(id: string) {
    try {
      // Vérifier si la référence existe
      const referral = await this.findOne(id);

      // Vérifier si la référence est déjà validée
      if (referral.status === ReferralStatus.VALIDATED) {
        throw new BadRequestException(`La référence est déjà validée`);
      }

      // Mettre à jour le statut
      const updatedReferral = await this.prisma.referral.update({
        where: { id },
        data: {
          status: ReferralStatus.VALIDATED,
          validatedAt: new Date(),
        },
      });

      // Émettre un événement
      await this.eventsService.create({
        eventType: 'REFERRAL_VALIDATED',
        payload: {
          referralId: updatedReferral.id,
          affiliateId: updatedReferral.affiliateId,
          referredUserId: updatedReferral.referredUserId,
        },
        status: 'COMPLETED',
      });

      this.logger.log(`Référence validée avec succès: ${updatedReferral.id}`);
      return updatedReferral;
    } catch (error) {
      this.logger.error(`Erreur lors de la validation de la référence: ${error.message}`);
      throw error;
    }
  }

  /**
   * Rejette une référence
   * @param id ID de la référence
   * @param reason Raison du rejet
   * @returns La référence rejetée
   */
  async reject(id: string, reason?: string) {
    try {
      // Vérifier si la référence existe
      const referral = await this.findOne(id);

      // Vérifier si la référence est déjà rejetée
      if (referral.status === ReferralStatus.REJECTED) {
        throw new BadRequestException(`La référence est déjà rejetée`);
      }

      // Mettre à jour le statut
      const updatedReferral = await this.prisma.referral.update({
        where: { id },
        data: {
          status: ReferralStatus.REJECTED,
          rejectionReason: reason,
        },
      });

      // Émettre un événement
      await this.eventsService.create({
        eventType: 'REFERRAL_REJECTED',
        payload: {
          referralId: updatedReferral.id,
          affiliateId: updatedReferral.affiliateId,
          referredUserId: updatedReferral.referredUserId,
          reason,
        },
        status: 'COMPLETED',
      });

      this.logger.log(`Référence rejetée avec succès: ${updatedReferral.id}`);
      return updatedReferral;
    } catch (error) {
      this.logger.error(`Erreur lors du rejet de la référence: ${error.message}`);
      throw error;
    }
  }

  /**
   * Vérifie si un utilisateur a été référé
   * @param userId ID de l'utilisateur
   * @returns Vrai si l'utilisateur a été référé
   */
  async isUserReferred(userId: string): Promise<boolean> {
    const referral = await this.prisma.referral.findFirst({
      where: { referredUserId: userId },
    });

    return !!referral;
  }

  /**
   * Récupère la référence d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns La référence de l'utilisateur
   */
  async getUserReferral(userId: string) {
    const referral = await this.prisma.referral.findFirst({
      where: { referredUserId: userId },
      include: {
        affiliate: {
          select: {
            id: true,
            userId: true,
            referralCode: true,
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    return referral;
  }
}
