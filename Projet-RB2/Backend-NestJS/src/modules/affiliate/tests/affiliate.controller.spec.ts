import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateController } from '../controllers/affiliate.controller';
import { AffiliateService } from '../services/affiliate.service';
import { ReferralService } from '../services/referral.service';
import { CommissionService } from '../services/commission.service';
import { PayoutService } from '../services/payout.service';
import { CreateAffiliateDto } from '../dto/create-affiliate.dto';
import { UpdateAffiliateDto } from '../dto/update-affiliate.dto';
import { AffiliateStatus } from '../enums/affiliate-status.enum';
import { AffiliateLevel } from '../enums/affiliate-level.enum';
import { CreateReferralDto } from '../dto/create-referral.dto';
import { CreateReferralByCodeDto } from '../dto/create-referral-by-code.dto';
import { CreatePayoutDto } from '../dto/create-payout.dto';

// Mocks des services
const mockAffiliateService = {
  create: jest.fn(),
  findAll: jest.fn(),
  findOne: jest.fn(),
  findByUserId: jest.fn(),
  findByReferralCode: jest.fn(),
  update: jest.fn(),
  approve: jest.fn(),
  reject: jest.fn(),
  suspend: jest.fn(),
  reactivate: jest.fn(),
  updateLevel: jest.fn(),
  getDashboard: jest.fn(),
  getAffiliateStats: jest.fn(),
};

const mockReferralService = {
  create: jest.fn(),
  createByCode: jest.fn(),
  findByAffiliateId: jest.fn(),
};

const mockCommissionService = {
  findByAffiliateId: jest.fn(),
};

const mockPayoutService = {
  create: jest.fn(),
  findByAffiliateId: jest.fn(),
};

describe('AffiliateController', () => {
  let controller: AffiliateController;
  let affiliateService: AffiliateService;
  let referralService: ReferralService;
  let commissionService: CommissionService;
  let payoutService: PayoutService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateController],
      providers: [
        {
          provide: AffiliateService,
          useValue: mockAffiliateService,
        },
        {
          provide: ReferralService,
          useValue: mockReferralService,
        },
        {
          provide: CommissionService,
          useValue: mockCommissionService,
        },
        {
          provide: PayoutService,
          useValue: mockPayoutService,
        },
      ],
    }).compile();

    controller = module.get<AffiliateController>(AffiliateController);
    affiliateService = module.get<AffiliateService>(AffiliateService);
    referralService = module.get<ReferralService>(ReferralService);
    commissionService = module.get<CommissionService>(CommissionService);
    payoutService = module.get<PayoutService>(PayoutService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create an affiliate', async () => {
      // Arrange
      const createAffiliateDto: CreateAffiliateDto = {
        userId: '123e4567-e89b-12d3-a456-************',
        website: 'https://example.com',
        bio: 'Test bio',
        paymentMethod: 'BANK_TRANSFER' as any,
        paymentDetails: { accountNumber: '*********' },
        metadata: { source: 'website' },
      };

      const expectedAffiliate = {
        id: 'aff-123',
        ...createAffiliateDto,
        referralCode: 'TEST1234',
        status: AffiliateStatus.PENDING,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.create.mockResolvedValue(expectedAffiliate);

      // Act
      const result = await controller.create(createAffiliateDto);

      // Assert
      expect(mockAffiliateService.create).toHaveBeenCalledWith(createAffiliateDto);
      expect(result).toEqual(expectedAffiliate);
    });
  });

  describe('findAll', () => {
    it('should return paginated affiliates', async () => {
      // Arrange
      const page = 1;
      const limit = 10;
      const status = AffiliateStatus.ACTIVE;

      const expectedResult = {
        affiliates: [
          {
            id: 'aff-123',
            userId: '123e4567-e89b-12d3-a456-************',
            referralCode: 'TEST1234',
            status: AffiliateStatus.ACTIVE,
            level: AffiliateLevel.STANDARD,
            commissionRate: 10,
            createdAt: new Date(),
            updatedAt: new Date(),
            user: {
              id: '123e4567-e89b-12d3-a456-************',
              email: '<EMAIL>',
              firstName: 'Test',
              lastName: 'User',
              profilePicture: null,
            },
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockAffiliateService.findAll.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.findAll(page, limit, status);

      // Assert
      expect(mockAffiliateService.findAll).toHaveBeenCalledWith(page, limit, status);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOne', () => {
    it('should return an affiliate by id', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const expectedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: '123e4567-e89b-12d3-a456-************',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          profilePicture: null,
        },
        referrals: [],
        commissions: [],
        payouts: [],
      };

      mockAffiliateService.findOne.mockResolvedValue(expectedAffiliate);

      // Act
      const result = await controller.findOne(affiliateId);

      // Assert
      expect(mockAffiliateService.findOne).toHaveBeenCalledWith(affiliateId);
      expect(result).toEqual(expectedAffiliate);
    });
  });

  describe('findByUserId', () => {
    it('should return an affiliate by user id', async () => {
      // Arrange
      const userId = '123e4567-e89b-12d3-a456-************';
      const expectedAffiliate = {
        id: 'aff-123',
        userId,
        referralCode: 'TEST1234',
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: userId,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          profilePicture: null,
        },
        referrals: [],
        commissions: [],
        payouts: [],
      };

      mockAffiliateService.findByUserId.mockResolvedValue(expectedAffiliate);

      // Act
      const result = await controller.findByUserId(userId);

      // Assert
      expect(mockAffiliateService.findByUserId).toHaveBeenCalledWith(userId);
      expect(result).toEqual(expectedAffiliate);
    });
  });

  describe('findByReferralCode', () => {
    it('should return an affiliate by referral code', async () => {
      // Arrange
      const referralCode = 'TEST1234';
      const expectedAffiliate = {
        id: 'aff-123',
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode,
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
        user: {
          id: '123e4567-e89b-12d3-a456-************',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          profilePicture: null,
        },
      };

      mockAffiliateService.findByReferralCode.mockResolvedValue(expectedAffiliate);

      // Act
      const result = await controller.findByReferralCode(referralCode);

      // Assert
      expect(mockAffiliateService.findByReferralCode).toHaveBeenCalledWith(referralCode);
      expect(result).toEqual(expectedAffiliate);
    });
  });

  describe('update', () => {
    it('should update an affiliate', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const updateAffiliateDto: UpdateAffiliateDto = {
        website: 'https://updated-example.com',
        bio: 'Updated bio',
      };

      const updatedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        website: 'https://updated-example.com',
        bio: 'Updated bio',
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.update.mockResolvedValue(updatedAffiliate);

      // Act
      const result = await controller.update(affiliateId, updateAffiliateDto);

      // Assert
      expect(mockAffiliateService.update).toHaveBeenCalledWith(affiliateId, updateAffiliateDto);
      expect(result).toEqual(updatedAffiliate);
    });
  });

  describe('approve', () => {
    it('should approve an affiliate', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const approvedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        approvedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.approve.mockResolvedValue(approvedAffiliate);

      // Act
      const result = await controller.approve(affiliateId);

      // Assert
      expect(mockAffiliateService.approve).toHaveBeenCalledWith(affiliateId);
      expect(result).toEqual(approvedAffiliate);
    });
  });

  describe('reject', () => {
    it('should reject an affiliate', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const reason = 'Invalid information';
      const rejectedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        status: AffiliateStatus.REJECTED,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        rejectionReason: reason,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.reject.mockResolvedValue(rejectedAffiliate);

      // Act
      const result = await controller.reject(affiliateId, reason);

      // Assert
      expect(mockAffiliateService.reject).toHaveBeenCalledWith(affiliateId, reason);
      expect(result).toEqual(rejectedAffiliate);
    });
  });

  describe('suspend', () => {
    it('should suspend an affiliate', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const reason = 'Terms violation';
      const suspendedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        status: AffiliateStatus.SUSPENDED,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        suspensionReason: reason,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.suspend.mockResolvedValue(suspendedAffiliate);

      // Act
      const result = await controller.suspend(affiliateId, reason);

      // Assert
      expect(mockAffiliateService.suspend).toHaveBeenCalledWith(affiliateId, reason);
      expect(result).toEqual(suspendedAffiliate);
    });
  });

  describe('reactivate', () => {
    it('should reactivate an affiliate', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const reactivatedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.STANDARD,
        commissionRate: 10,
        suspensionReason: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.reactivate.mockResolvedValue(reactivatedAffiliate);

      // Act
      const result = await controller.reactivate(affiliateId);

      // Assert
      expect(mockAffiliateService.reactivate).toHaveBeenCalledWith(affiliateId);
      expect(result).toEqual(reactivatedAffiliate);
    });
  });

  describe('updateLevel', () => {
    it('should update affiliate level', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const level = AffiliateLevel.GOLD;
      const commissionRate = 20;
      const updatedAffiliate = {
        id: affiliateId,
        userId: '123e4567-e89b-12d3-a456-************',
        referralCode: 'TEST1234',
        status: AffiliateStatus.ACTIVE,
        level: AffiliateLevel.GOLD,
        commissionRate: 20,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAffiliateService.updateLevel.mockResolvedValue(updatedAffiliate);

      // Act
      const result = await controller.updateLevel(affiliateId, level, commissionRate);

      // Assert
      expect(mockAffiliateService.updateLevel).toHaveBeenCalledWith(affiliateId, level, commissionRate);
      expect(result).toEqual(updatedAffiliate);
    });
  });

  describe('getDashboard', () => {
    it('should return affiliate dashboard', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const expectedDashboard = {
        affiliate: {
          id: affiliateId,
          userId: '123e4567-e89b-12d3-a456-************',
          referralCode: 'TEST1234',
          status: AffiliateStatus.ACTIVE,
          level: AffiliateLevel.STANDARD,
          commissionRate: 10,
          createdAt: new Date(),
        },
        stats: {
          referralCount: 5,
          conversionCount: 3,
          totalCommission: 150,
          totalPaid: 100,
          pendingCommission: 50,
          availableBalance: 50,
        },
        recentReferrals: [],
        recentCommissions: [],
        monthlyStats: [],
      };

      mockAffiliateService.getDashboard.mockResolvedValue(expectedDashboard);

      // Act
      const result = await controller.getDashboard(affiliateId);

      // Assert
      expect(mockAffiliateService.getDashboard).toHaveBeenCalledWith(affiliateId);
      expect(result).toEqual(expectedDashboard);
    });
  });

  describe('getStats', () => {
    it('should return affiliate stats', async () => {
      // Arrange
      const expectedStats = {
        affiliates: {
          total: 100,
          active: 80,
          pending: 15,
          suspended: 3,
          rejected: 2,
        },
        referrals: 500,
        conversions: 300,
        commissions: 15000,
        payouts: 12000,
        pendingPayouts: 3000,
      };

      mockAffiliateService.getAffiliateStats.mockResolvedValue(expectedStats);

      // Act
      const result = await controller.getStats();

      // Assert
      expect(mockAffiliateService.getAffiliateStats).toHaveBeenCalled();
      expect(result).toEqual(expectedStats);
    });
  });

  describe('createReferral', () => {
    it('should create a referral', async () => {
      // Arrange
      const createReferralDto: CreateReferralDto = {
        affiliateId: 'aff-123',
        referredUserId: 'user-456',
        type: 'DIRECT' as any,
        metadata: { source: 'website' },
      };

      const expectedReferral = {
        id: 'ref-123',
        ...createReferralDto,
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockReferralService.create.mockResolvedValue(expectedReferral);

      // Act
      const result = await controller.createReferral(createReferralDto);

      // Assert
      expect(mockReferralService.create).toHaveBeenCalledWith(createReferralDto);
      expect(result).toEqual(expectedReferral);
    });
  });

  describe('createReferralByCode', () => {
    it('should create a referral by code', async () => {
      // Arrange
      const createReferralByCodeDto: CreateReferralByCodeDto = {
        referralCode: 'TEST1234',
        referredUserId: 'user-456',
        type: 'DIRECT' as any,
        metadata: { source: 'website' },
      };

      const expectedReferral = {
        id: 'ref-123',
        affiliateId: 'aff-123',
        referredUserId: 'user-456',
        type: 'DIRECT',
        status: 'PENDING',
        metadata: { source: 'website' },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockReferralService.createByCode.mockResolvedValue(expectedReferral);

      // Act
      const result = await controller.createReferralByCode(createReferralByCodeDto);

      // Assert
      expect(mockReferralService.createByCode).toHaveBeenCalledWith(createReferralByCodeDto);
      expect(result).toEqual(expectedReferral);
    });
  });

  describe('getReferrals', () => {
    it('should return affiliate referrals', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const page = 1;
      const limit = 10;

      const expectedResult = {
        referrals: [
          {
            id: 'ref-123',
            affiliateId,
            referredUserId: 'user-456',
            type: 'DIRECT',
            status: 'PENDING',
            metadata: { source: 'website' },
            createdAt: new Date(),
            updatedAt: new Date(),
            referredUser: {
              id: 'user-456',
              email: '<EMAIL>',
              firstName: 'Referred',
              lastName: 'User',
              profilePicture: null,
            },
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockReferralService.findByAffiliateId.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.getReferrals(affiliateId, page, limit);

      // Assert
      expect(mockReferralService.findByAffiliateId).toHaveBeenCalledWith(affiliateId, page, limit);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('getCommissions', () => {
    it('should return affiliate commissions', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const page = 1;
      const limit = 10;

      const expectedResult = {
        commissions: [
          {
            id: 'comm-123',
            affiliateId,
            conversionId: 'conv-123',
            amount: 50,
            status: 'PENDING',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockCommissionService.findByAffiliateId.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.getCommissions(affiliateId, page, limit);

      // Assert
      expect(mockCommissionService.findByAffiliateId).toHaveBeenCalledWith(affiliateId, page, limit);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('createPayout', () => {
    it('should create a payout', async () => {
      // Arrange
      const createPayoutDto: CreatePayoutDto = {
        affiliateId: 'aff-123',
        amount: 100,
        method: 'BANK_TRANSFER' as any,
        metadata: { accountNumber: '*********' },
        description: 'Monthly payout',
      };

      const expectedPayout = {
        id: 'pay-123',
        affiliateId: 'aff-123',
        amount: 100,
        method: 'BANK_TRANSFER',
        metadata: { accountNumber: '*********' },
        description: 'Monthly payout',
        status: 'PENDING',
        processedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPayoutService.create.mockResolvedValue(expectedPayout);

      // Act
      const result = await controller.createPayout(createPayoutDto);

      // Assert
      expect(mockPayoutService.create).toHaveBeenCalledWith(createPayoutDto);
      expect(result).toEqual(expectedPayout);
    });
  });

  describe('getPayouts', () => {
    it('should return affiliate payouts', async () => {
      // Arrange
      const affiliateId = 'aff-123';
      const page = 1;
      const limit = 10;

      const expectedResult = {
        payouts: [
          {
            id: 'pay-123',
            affiliateId,
            amount: 100,
            method: 'BANK_TRANSFER',
            metadata: { accountNumber: '*********' },
            description: 'Monthly payout',
            status: 'COMPLETED',
            processedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockPayoutService.findByAffiliateId.mockResolvedValue(expectedResult);

      // Act
      const result = await controller.getPayouts(affiliateId, page, limit);

      // Assert
      expect(mockPayoutService.findByAffiliateId).toHaveBeenCalledWith(affiliateId, page, limit);
      expect(result).toEqual(expectedResult);
    });
  });
});
