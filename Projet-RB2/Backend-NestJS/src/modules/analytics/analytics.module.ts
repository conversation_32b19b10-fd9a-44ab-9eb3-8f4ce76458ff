import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AnalyticsService } from './analytics.service';
import { AnalyticsController } from './analytics.controller';
import { ForecastingController } from './controllers/forecasting.controller';
import { BenchmarkController } from './controllers/benchmark.controller';
import { RecommendationsController } from './controllers/recommendations.controller';
import { EngagementService } from './services/engagement.service';
import { AudienceService } from './services/audience.service';
import { RevenueService } from './services/revenue.service';
import { ForecastingService } from './services/forecasting.service';
import { BenchmarkService } from './services/benchmark.service';
import { PersonalizedRecommendationsService } from './services/personalized-recommendations.service';
import { DashboardService } from './services/dashboard.service';
import { DataCollectionService } from './services/data-collection.service';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    PrismaModule,
  ],
  controllers: [
    AnalyticsController,
    ForecastingController,
    BenchmarkController,
    RecommendationsController,
  ],
  providers: [
    AnalyticsService,
    EngagementService,
    AudienceService,
    RevenueService,
    ForecastingService,
    BenchmarkService,
    PersonalizedRecommendationsService,
    DashboardService,
    DataCollectionService,
  ],
  exports: [
    AnalyticsService,
    EngagementService,
    AudienceService,
    RevenueService,
    ForecastingService,
    BenchmarkService,
    DashboardService,
  ],
  exports: [
    AnalyticsService,
    EngagementService,
    AudienceService,
    RevenueService,
    ForecastingService,
    BenchmarkService,
    PersonalizedRecommendationsService,
    DashboardService,
    DataCollectionService,
  ],
})
export class AnalyticsModule {}
