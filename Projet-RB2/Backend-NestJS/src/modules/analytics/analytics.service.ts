import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EngagementService } from './services/engagement.service';
import { AudienceService } from './services/audience.service';
import { RevenueService } from './services/revenue.service';
import { ForecastingService } from './services/forecasting.service';
import { BenchmarkService } from './services/benchmark.service';
import { DashboardService } from './services/dashboard.service';
import { ContentType } from '@prisma/client';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly engagementService: EngagementService,
    private readonly audienceService: AudienceService,
    private readonly revenueService: RevenueService,
    private readonly forecastingService: ForecastingService,
    private readonly benchmarkService: BenchmarkService,
    private readonly dashboardService: DashboardService,
  ) {}

  async getCreatorMetrics(creatorId: string, filters: {
    startDate?: string;
    endDate?: string;
    contentType?: ContentType;
  }) {
    this.logger.debug(`Getting metrics for creator: ${creatorId}`);
    
    const startDate = filters.startDate ? new Date(filters.startDate) : undefined;
    const endDate = filters.endDate ? new Date(filters.endDate) : undefined;
    
    // Récupérer les métriques d'engagement
    const engagementMetrics = await this.engagementService.getCreatorEngagementMetrics(
      creatorId,
      startDate,
      endDate,
      filters.contentType,
    );
    
    // Récupérer les métriques d'audience
    const audienceMetrics = await this.audienceService.getCreatorAudienceMetrics(
      creatorId,
      startDate,
      endDate,
    );
    
    // Récupérer les métriques de revenus
    const revenueMetrics = await this.revenueService.getCreatorRevenueMetrics(
      creatorId,
      startDate,
      endDate,
    );
    
    // Récupérer le contenu le plus performant
    const topContent = await this.engagementService.getTopPerformingContent(
      creatorId,
      10,
      startDate,
      endDate,
    );
    
    return {
      engagement: engagementMetrics,
      audience: audienceMetrics,
      revenue: revenueMetrics,
      topContent,
      period: {
        startDate: startDate || 'all',
        endDate: endDate || 'now',
      },
    };
  }

  async getContentMetrics(creatorId: string, contentId: string, filters: {
    startDate?: string;
    endDate?: string;
  }) {
    this.logger.debug(`Getting metrics for content: ${contentId}`);
    
    const startDate = filters.startDate ? new Date(filters.startDate) : undefined;
    const endDate = filters.endDate ? new Date(filters.endDate) : undefined;
    
    // Récupérer les métriques d'engagement pour ce contenu
    const engagementMetrics = await this.engagementService.getContentEngagementMetrics(
      contentId,
      startDate,
      endDate,
    );
    
    // Récupérer les métriques de revenus pour ce contenu
    const revenueMetrics = await this.revenueService.getContentRevenueMetrics(
      contentId,
      startDate,
      endDate,
    );
    
    return {
      engagement: engagementMetrics,
      revenue: revenueMetrics,
      period: {
        startDate: startDate || 'all',
        endDate: endDate || 'now',
      },
    };
  }

  async getCreatorSummary(creatorId: string) {
    this.logger.debug(`Getting summary for creator: ${creatorId}`);
    
    // Récupérer les métriques agrégées
    const engagementSummary = await this.engagementService.getCreatorEngagementSummary(creatorId);
    const audienceSummary = await this.audienceService.getCreatorAudienceSummary(creatorId);
    const revenueSummary = await this.revenueService.getCreatorRevenueSummary(creatorId);
    
    // Récupérer le contenu le plus performant
    const topContent = await this.engagementService.getTopPerformingContent(creatorId, 5);
    
    return {
      engagement: engagementSummary,
      audience: audienceSummary,
      revenue: revenueSummary,
      topContent,
    };
  }

  async getCreatorForecasts(creatorId: string) {
    this.logger.debug(`Getting forecasts for creator: ${creatorId}`);
    
    // Récupérer les prévisions d'engagement
    const engagementForecasts = await this.forecastingService.getEngagementForecasts(creatorId);
    
    // Récupérer les prévisions d'audience
    const audienceForecasts = await this.forecastingService.getAudienceForecasts(creatorId);
    
    // Récupérer les prévisions de revenus
    const revenueForecasts = await this.forecastingService.getRevenueForecasts(creatorId);
    
    return {
      engagement: engagementForecasts,
      audience: audienceForecasts,
      revenue: revenueForecasts,
    };
  }

  async getCreatorBenchmarks(creatorId: string, category: string) {
    this.logger.debug(`Getting benchmarks for creator: ${creatorId} in category: ${category}`);
    
    // Récupérer les benchmarks d'engagement
    const engagementBenchmarks = await this.benchmarkService.getEngagementBenchmarks(creatorId, category);
    
    // Récupérer les benchmarks d'audience
    const audienceBenchmarks = await this.benchmarkService.getAudienceBenchmarks(creatorId, category);
    
    // Récupérer les benchmarks de revenus
    const revenueBenchmarks = await this.benchmarkService.getRevenueBenchmarks(creatorId, category);
    
    return {
      engagement: engagementBenchmarks,
      audience: audienceBenchmarks,
      revenue: revenueBenchmarks,
      category,
    };
  }

  async getCreatorDashboards(creatorId: string) {
    this.logger.debug(`Getting dashboards for creator: ${creatorId}`);
    return this.dashboardService.getCreatorDashboards(creatorId);
  }

  async getCreatorDashboard(dashboardId: string) {
    this.logger.debug(`Getting dashboard: ${dashboardId}`);
    return this.dashboardService.getDashboard(dashboardId);
  }

  async createCreatorDashboard(creatorId: string, dashboardData: any) {
    this.logger.debug(`Creating dashboard for creator: ${creatorId}`);
    return this.dashboardService.createDashboard(creatorId, dashboardData);
  }

  async updateCreatorDashboard(dashboardId: string, dashboardData: any) {
    this.logger.debug(`Updating dashboard: ${dashboardId}`);
    return this.dashboardService.updateDashboard(dashboardId, dashboardData);
  }

  async deleteCreatorDashboard(dashboardId: string) {
    this.logger.debug(`Deleting dashboard: ${dashboardId}`);
    return this.dashboardService.deleteDashboard(dashboardId);
  }
}
