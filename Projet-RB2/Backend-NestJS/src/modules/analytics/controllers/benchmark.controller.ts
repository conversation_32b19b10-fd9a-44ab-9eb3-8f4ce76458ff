import {
  <PERSON>,
  Get,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AnalyticsService } from '../analytics.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';

@ApiTags('analytics-benchmarks')
@Controller('analytics/benchmarks')
export class BenchmarkController {
  private readonly logger = new Logger(BenchmarkController.name);

  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get(':creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les benchmarks pour un créateur' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiQuery({ name: 'category', description: 'Catégorie pour le benchmark', required: false })
  @ApiResponse({
    status: 200,
    description: 'Benchmarks récupérés avec succès',
  })
  async getCreatorBenchmarks(
    @Param('creatorId') creatorId: string,
    @Query('category') category: string = 'all',
    @CurrentUser() user: any,
  ) {
    try {
      this.logger.debug(`Getting benchmarks for creator ${creatorId} in category ${category}`);
      
      // Vérifier que l'utilisateur a le droit d'accéder aux benchmarks de ce créateur
      if (user.role !== 'admin' && user.id !== creatorId) {
        throw new HttpException(
          'You are not authorized to access these benchmarks',
          HttpStatus.FORBIDDEN,
        );
      }
      
      const benchmarks = await this.analyticsService.getCreatorBenchmarks(creatorId, category);
      
      return benchmarks;
    } catch (error) {
      this.logger.error(`Error getting benchmarks: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to get benchmarks',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('categories')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les catégories disponibles pour les benchmarks' })
  @ApiResponse({
    status: 200,
    description: 'Catégories récupérées avec succès',
  })
  async getBenchmarkCategories() {
    try {
      this.logger.debug('Getting benchmark categories');
      
      // Récupérer les catégories depuis le service
      const categories = await this.analyticsService.getBenchmarkCategories();
      
      return {
        categories,
      };
    } catch (error) {
      this.logger.error(`Error getting benchmark categories: ${error.message}`);
      throw new HttpException(
        'Failed to get benchmark categories',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':creatorId/comparison')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Comparer les performances d\'un créateur avec les benchmarks' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiQuery({ name: 'category', description: 'Catégorie pour la comparaison', required: false })
  @ApiQuery({ name: 'metric', description: 'Métrique à comparer', required: false })
  @ApiResponse({
    status: 200,
    description: 'Comparaison récupérée avec succès',
  })
  async getCreatorComparison(
    @Param('creatorId') creatorId: string,
    @Query('category') category: string = 'all',
    @Query('metric') metric: string = 'all',
    @CurrentUser() user: any,
  ) {
    try {
      this.logger.debug(`Getting comparison for creator ${creatorId} in category ${category} for metric ${metric}`);
      
      // Vérifier que l'utilisateur a le droit d'accéder à la comparaison de ce créateur
      if (user.role !== 'admin' && user.id !== creatorId) {
        throw new HttpException(
          'You are not authorized to access this comparison',
          HttpStatus.FORBIDDEN,
        );
      }
      
      // Récupérer les benchmarks
      const benchmarks = await this.analyticsService.getCreatorBenchmarks(creatorId, category);
      
      // Récupérer les métriques du créateur
      const creatorMetrics = await this.analyticsService.getCreatorMetrics(creatorId);
      
      // Préparer la comparaison
      const comparison = {
        creatorId,
        category,
        metrics: {},
        strengths: [],
        weaknesses: [],
        recommendations: [],
      };
      
      // Comparer les métriques d'engagement
      if (metric === 'all' || metric === 'engagement') {
        comparison.metrics['engagement'] = {
          views: {
            value: creatorMetrics.engagement.totalViews,
            benchmark: benchmarks.engagement.views,
            difference: this.calculateDifference(
              creatorMetrics.engagement.totalViews,
              benchmarks.engagement.views.categoryAverage,
            ),
          },
          engagementRate: {
            value: creatorMetrics.engagement.engagementRate,
            benchmark: benchmarks.engagement.engagementRate,
            difference: this.calculateDifference(
              creatorMetrics.engagement.engagementRate,
              benchmarks.engagement.engagementRate.categoryAverage,
            ),
          },
        };
        
        // Identifier les forces et faiblesses
        if (comparison.metrics['engagement'].views.difference > 0.2) {
          comparison.strengths.push('Nombre de vues supérieur à la moyenne de la catégorie');
        } else if (comparison.metrics['engagement'].views.difference < -0.2) {
          comparison.weaknesses.push('Nombre de vues inférieur à la moyenne de la catégorie');
          comparison.recommendations.push('Augmenter la visibilité du contenu en améliorant le référencement');
        }
        
        if (comparison.metrics['engagement'].engagementRate.difference > 0.2) {
          comparison.strengths.push('Taux d\'engagement supérieur à la moyenne de la catégorie');
        } else if (comparison.metrics['engagement'].engagementRate.difference < -0.2) {
          comparison.weaknesses.push('Taux d\'engagement inférieur à la moyenne de la catégorie');
          comparison.recommendations.push('Encourager les interactions en posant des questions dans le contenu');
        }
      }
      
      // Comparer les métriques d'audience
      if (metric === 'all' || metric === 'audience') {
        comparison.metrics['audience'] = {
          followers: {
            value: creatorMetrics.audience.totalFollowers,
            benchmark: benchmarks.audience.followers,
            difference: this.calculateDifference(
              creatorMetrics.audience.totalFollowers,
              benchmarks.audience.followers.categoryAverage,
            ),
          },
          growth: {
            value: creatorMetrics.audience.growthRate,
            benchmark: benchmarks.audience.growth,
            difference: this.calculateDifference(
              creatorMetrics.audience.growthRate,
              benchmarks.audience.growth.categoryAverage,
            ),
          },
        };
        
        // Identifier les forces et faiblesses
        if (comparison.metrics['audience'].followers.difference > 0.2) {
          comparison.strengths.push('Nombre de followers supérieur à la moyenne de la catégorie');
        } else if (comparison.metrics['audience'].followers.difference < -0.2) {
          comparison.weaknesses.push('Nombre de followers inférieur à la moyenne de la catégorie');
          comparison.recommendations.push('Développer votre audience en collaborant avec d\'autres créateurs');
        }
        
        if (comparison.metrics['audience'].growth.difference > 0.2) {
          comparison.strengths.push('Taux de croissance supérieur à la moyenne de la catégorie');
        } else if (comparison.metrics['audience'].growth.difference < -0.2) {
          comparison.weaknesses.push('Taux de croissance inférieur à la moyenne de la catégorie');
          comparison.recommendations.push('Publier plus régulièrement pour maintenir l\'engagement de votre audience');
        }
      }
      
      // Comparer les métriques de revenus
      if (metric === 'all' || metric === 'revenue') {
        comparison.metrics['revenue'] = {
          amount: {
            value: creatorMetrics.revenue.totalRevenue,
            benchmark: benchmarks.revenue.amount,
            difference: this.calculateDifference(
              creatorMetrics.revenue.totalRevenue,
              benchmarks.revenue.amount.categoryAverage,
            ),
          },
        };
        
        // Identifier les forces et faiblesses
        if (comparison.metrics['revenue'].amount.difference > 0.2) {
          comparison.strengths.push('Revenus supérieurs à la moyenne de la catégorie');
        } else if (comparison.metrics['revenue'].amount.difference < -0.2) {
          comparison.weaknesses.push('Revenus inférieurs à la moyenne de la catégorie');
          comparison.recommendations.push('Diversifier vos sources de revenus (produits dérivés, partenariats)');
        }
      }
      
      return comparison;
    } catch (error) {
      this.logger.error(`Error getting comparison: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to get comparison',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Calcule la différence relative entre deux valeurs
   * @param value Valeur à comparer
   * @param benchmark Valeur de référence
   * @returns Différence relative (-1 à +inf)
   */
  private calculateDifference(value: number, benchmark: number): number {
    if (benchmark === 0) {
      return value > 0 ? 1 : 0;
    }
    return (value - benchmark) / benchmark;
  }
}
