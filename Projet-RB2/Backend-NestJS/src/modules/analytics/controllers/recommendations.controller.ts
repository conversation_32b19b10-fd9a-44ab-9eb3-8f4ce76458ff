import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { PersonalizedRecommendationsService, RecommendationType } from '../services/personalized-recommendations.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';

@ApiTags('analytics-recommendations')
@Controller('analytics/recommendations')
export class RecommendationsController {
  private readonly logger = new Logger(RecommendationsController.name);

  constructor(private readonly recommendationsService: PersonalizedRecommendationsService) {}

  @Get(':creatorId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir des recommandations personnalisées pour un créateur' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiQuery({ name: 'type', description: 'Type de recommandation', required: false, enum: RecommendationType })
  @ApiQuery({ name: 'limit', description: 'Nombre maximum de recommandations', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: 'Recommandations récupérées avec succès',
  })
  async getRecommendations(
    @Param('creatorId') creatorId: string,
    @Query('type') type?: RecommendationType,
    @Query('limit') limit?: number,
    @CurrentUser() user: any,
  ) {
    try {
      this.logger.debug(`Getting recommendations for creator ${creatorId}`);
      
      // Vérifier que l'utilisateur a le droit d'accéder aux recommandations de ce créateur
      if (user.role !== 'admin' && user.id !== creatorId) {
        throw new HttpException(
          'You are not authorized to access these recommendations',
          HttpStatus.FORBIDDEN,
        );
      }
      
      const recommendations = await this.recommendationsService.getRecommendations(
        creatorId,
        type,
        limit ? parseInt(limit.toString()) : undefined,
      );
      
      return {
        creatorId,
        recommendations,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error getting recommendations: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to get recommendations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':creatorId/by-type/:type')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir des recommandations d\'un type spécifique pour un créateur' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiParam({ name: 'type', description: 'Type de recommandation', enum: RecommendationType })
  @ApiQuery({ name: 'limit', description: 'Nombre maximum de recommandations', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: 'Recommandations récupérées avec succès',
  })
  async getRecommendationsByType(
    @Param('creatorId') creatorId: string,
    @Param('type') type: RecommendationType,
    @Query('limit') limit?: number,
    @CurrentUser() user: any,
  ) {
    try {
      this.logger.debug(`Getting ${type} recommendations for creator ${creatorId}`);
      
      // Vérifier que l'utilisateur a le droit d'accéder aux recommandations de ce créateur
      if (user.role !== 'admin' && user.id !== creatorId) {
        throw new HttpException(
          'You are not authorized to access these recommendations',
          HttpStatus.FORBIDDEN,
        );
      }
      
      // Vérifier que le type est valide
      if (!Object.values(RecommendationType).includes(type)) {
        throw new HttpException(
          `Invalid recommendation type: ${type}`,
          HttpStatus.BAD_REQUEST,
        );
      }
      
      const recommendations = await this.recommendationsService.getRecommendations(
        creatorId,
        type,
        limit ? parseInt(limit.toString()) : undefined,
      );
      
      return {
        creatorId,
        type,
        recommendations,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error getting recommendations by type: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to get recommendations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':creatorId/feedback')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('creator', 'admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Fournir un feedback sur une recommandation' })
  @ApiParam({ name: 'creatorId', description: 'ID du créateur' })
  @ApiResponse({
    status: 200,
    description: 'Feedback enregistré avec succès',
  })
  async provideFeedback(
    @Param('creatorId') creatorId: string,
    @Body() body: {
      recommendationId: string;
      isHelpful: boolean;
      feedback?: string;
      implemented?: boolean;
    },
    @CurrentUser() user: any,
  ) {
    try {
      this.logger.debug(`Receiving feedback for recommendation ${body.recommendationId}`);
      
      // Vérifier que l'utilisateur a le droit de fournir un feedback pour ce créateur
      if (user.role !== 'admin' && user.id !== creatorId) {
        throw new HttpException(
          'You are not authorized to provide feedback for this creator',
          HttpStatus.FORBIDDEN,
        );
      }
      
      // Enregistrer le feedback (à implémenter)
      // Pour l'instant, nous simulons simplement une réponse réussie
      
      return {
        success: true,
        message: 'Feedback recorded successfully',
        recommendationId: body.recommendationId,
      };
    } catch (error) {
      this.logger.error(`Error providing feedback: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Failed to record feedback',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('types')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les types de recommandations disponibles' })
  @ApiResponse({
    status: 200,
    description: 'Types de recommandations récupérés avec succès',
  })
  async getRecommendationTypes() {
    try {
      this.logger.debug('Getting recommendation types');
      
      return {
        types: Object.values(RecommendationType),
        descriptions: {
          [RecommendationType.CONTENT]: 'Recommandations sur le type et la qualité du contenu',
          [RecommendationType.TIMING]: 'Recommandations sur le moment optimal pour publier',
          [RecommendationType.ENGAGEMENT]: 'Recommandations pour augmenter l\'engagement',
          [RecommendationType.MONETIZATION]: 'Recommandations pour optimiser les revenus',
          [RecommendationType.AUDIENCE]: 'Recommandations pour développer et fidéliser l\'audience',
        },
      };
    } catch (error) {
      this.logger.error(`Error getting recommendation types: ${error.message}`);
      throw new HttpException(
        'Failed to get recommendation types',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
