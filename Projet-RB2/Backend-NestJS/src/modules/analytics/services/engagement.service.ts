import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ContentType } from '@prisma/client';

@Injectable()
export class EngagementService {
  private readonly logger = new Logger(EngagementService.name);

  constructor(private readonly prisma: PrismaService) {}

  async getCreatorEngagementMetrics(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
    contentType?: ContentType,
  ) {
    this.logger.debug(`Getting engagement metrics for creator: ${creatorId}`);
    
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }
    
    if (contentType) {
      where.contentType = contentType;
    }

    const metrics = await this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Agréger les métriques
    const totalViews = metrics.reduce((sum, metric) => sum + metric.views, 0);
    const totalLikes = metrics.reduce((sum, metric) => sum + metric.likes, 0);
    const totalComments = metrics.reduce((sum, metric) => sum + metric.comments, 0);
    const totalShares = metrics.reduce((sum, metric) => sum + metric.shares, 0);
    const totalBookmarks = metrics.reduce((sum, metric) => sum + metric.bookmarks, 0);
    const totalClickThroughs = metrics.reduce((sum, metric) => sum + metric.clickThroughs, 0);

    // Calculer les métriques dérivées
    const engagementRate = totalViews > 0
      ? ((totalLikes + totalComments + totalShares) / totalViews) * 100
      : 0;
    
    const clickThroughRate = totalViews > 0
      ? (totalClickThroughs / totalViews) * 100
      : 0;

    // Préparer les données pour les graphiques (par jour)
    const timeSeriesData = this.prepareTimeSeriesData(metrics);

    return {
      summary: {
        totalViews,
        totalLikes,
        totalComments,
        totalShares,
        totalBookmarks,
        totalClickThroughs,
        engagementRate,
        clickThroughRate,
      },
      timeSeries: timeSeriesData,
      period: {
        startDate: startDate || metrics[0]?.date,
        endDate: endDate || metrics[metrics.length - 1]?.date,
      },
    };
  }

  async getContentEngagementMetrics(
    contentId: string,
    startDate?: Date,
    endDate?: Date,
  ) {
    this.logger.debug(`Getting engagement metrics for content: ${contentId}`);
    
    const where: any = { contentId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
    
    if (metrics.length === 0) {
      throw new NotFoundException(`No metrics found for content: ${contentId}`);
    }

    // Agréger les métriques
    const totalViews = metrics.reduce((sum, metric) => sum + metric.views, 0);
    const totalLikes = metrics.reduce((sum, metric) => sum + metric.likes, 0);
    const totalComments = metrics.reduce((sum, metric) => sum + metric.comments, 0);
    const totalShares = metrics.reduce((sum, metric) => sum + metric.shares, 0);
    const totalBookmarks = metrics.reduce((sum, metric) => sum + metric.bookmarks, 0);
    const totalClickThroughs = metrics.reduce((sum, metric) => sum + metric.clickThroughs, 0);

    // Calculer les métriques dérivées
    const engagementRate = totalViews > 0
      ? ((totalLikes + totalComments + totalShares) / totalViews) * 100
      : 0;
    
    const clickThroughRate = totalViews > 0
      ? (totalClickThroughs / totalViews) * 100
      : 0;

    // Préparer les données pour les graphiques (par jour)
    const timeSeriesData = this.prepareTimeSeriesData(metrics);

    return {
      summary: {
        totalViews,
        totalLikes,
        totalComments,
        totalShares,
        totalBookmarks,
        totalClickThroughs,
        engagementRate,
        clickThroughRate,
      },
      timeSeries: timeSeriesData,
      contentType: metrics[0].contentType,
      period: {
        startDate: startDate || metrics[0].date,
        endDate: endDate || metrics[metrics.length - 1].date,
      },
    };
  }

  async getTopPerformingContent(
    creatorId: string,
    limit: number = 10,
    startDate?: Date,
    endDate?: Date,
  ) {
    this.logger.debug(`Getting top performing content for creator: ${creatorId}`);
    
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.engagementMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Agréger par contenu
    const contentMap = new Map();
    
    metrics.forEach(metric => {
      if (!contentMap.has(metric.contentId)) {
        contentMap.set(metric.contentId, {
          contentId: metric.contentId,
          contentType: metric.contentType,
          views: 0,
          likes: 0,
          comments: 0,
          shares: 0,
          bookmarks: 0,
          clickThroughs: 0,
        });
      }
      
      const content = contentMap.get(metric.contentId);
      content.views += metric.views;
      content.likes += metric.likes;
      content.comments += metric.comments;
      content.shares += metric.shares;
      content.bookmarks += metric.bookmarks;
      content.clickThroughs += metric.clickThroughs;
    });
    
    // Convertir en tableau et calculer l'engagement
    const contentArray = Array.from(contentMap.values());
    contentArray.forEach(content => {
      content.engagement = content.likes + content.comments + content.shares;
      content.engagementRate = content.views > 0
        ? (content.engagement / content.views) * 100
        : 0;
    });
    
    // Trier par engagement et limiter
    return contentArray
      .sort((a, b) => b.engagement - a.engagement)
      .slice(0, limit);
  }

  async getCreatorEngagementSummary(creatorId: string) {
    this.logger.debug(`Getting engagement summary for creator: ${creatorId}`);
    
    // Récupérer les métriques des 30 derniers jours
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentMetrics = await this.getCreatorEngagementMetrics(
      creatorId,
      thirtyDaysAgo,
    );
    
    // Récupérer les métriques des 30 jours précédents pour comparaison
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    const previousMetrics = await this.getCreatorEngagementMetrics(
      creatorId,
      sixtyDaysAgo,
      thirtyDaysAgo,
    );
    
    // Calculer les variations
    const calculateChange = (current, previous) => {
      if (previous === 0) return 100; // Si la valeur précédente est 0, considérer comme 100% d'augmentation
      return ((current - previous) / previous) * 100;
    };
    
    return {
      current: recentMetrics.summary,
      changes: {
        viewsChange: calculateChange(
          recentMetrics.summary.totalViews,
          previousMetrics.summary.totalViews,
        ),
        likesChange: calculateChange(
          recentMetrics.summary.totalLikes,
          previousMetrics.summary.totalLikes,
        ),
        commentsChange: calculateChange(
          recentMetrics.summary.totalComments,
          previousMetrics.summary.totalComments,
        ),
        sharesChange: calculateChange(
          recentMetrics.summary.totalShares,
          previousMetrics.summary.totalShares,
        ),
        engagementRateChange: calculateChange(
          recentMetrics.summary.engagementRate,
          previousMetrics.summary.engagementRate,
        ),
      },
      period: {
        startDate: thirtyDaysAgo,
        endDate: new Date(),
      },
    };
  }

  private prepareTimeSeriesData(metrics: any[]) {
    // Regrouper par jour
    const dailyData = new Map();
    
    metrics.forEach(metric => {
      const date = metric.date.toISOString().split('T')[0]; // Format YYYY-MM-DD
      
      if (!dailyData.has(date)) {
        dailyData.set(date, {
          date,
          views: 0,
          likes: 0,
          comments: 0,
          shares: 0,
          bookmarks: 0,
          clickThroughs: 0,
        });
      }
      
      const day = dailyData.get(date);
      day.views += metric.views;
      day.likes += metric.likes;
      day.comments += metric.comments;
      day.shares += metric.shares;
      day.bookmarks += metric.bookmarks;
      day.clickThroughs += metric.clickThroughs;
    });
    
    // Convertir en tableau et trier par date
    return Array.from(dailyData.values())
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }
}
