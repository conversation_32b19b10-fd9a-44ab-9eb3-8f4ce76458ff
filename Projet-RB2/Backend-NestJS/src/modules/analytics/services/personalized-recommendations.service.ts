import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EngagementService } from './engagement.service';
import { AudienceService } from './audience.service';
import { RevenueService } from './revenue.service';
import { ForecastingService } from './forecasting.service';
import { BenchmarkService } from './benchmark.service';
import { ContentType } from '@prisma/client';

export enum RecommendationType {
  CONTENT = 'CONTENT',
  TIMING = 'TIMING',
  ENGAGEMENT = 'ENGAGEMENT',
  MONETIZATION = 'MONETIZATION',
  AUDIENCE = 'AUDIENCE',
}

export interface Recommendation {
  id: string;
  type: RecommendationType;
  title: string;
  description: string;
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
  metrics: string[];
  actions: string[];
  createdAt: Date;
}

@Injectable()
export class PersonalizedRecommendationsService {
  private readonly logger = new Logger(PersonalizedRecommendationsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly engagementService: EngagementService,
    private readonly audienceService: AudienceService,
    private readonly revenueService: RevenueService,
    private readonly forecastingService: ForecastingService,
    private readonly benchmarkService: BenchmarkService,
  ) {}

  /**
   * Génère des recommandations personnalisées pour un créateur
   * @param creatorId ID du créateur
   * @param type Type de recommandation
   * @param limit Nombre maximum de recommandations
   * @returns Liste de recommandations
   */
  async getRecommendations(
    creatorId: string,
    type?: RecommendationType,
    limit: number = 5,
  ): Promise<Recommendation[]> {
    this.logger.debug(`Generating ${type || 'all'} recommendations for creator ${creatorId}`);

    // Récupérer les données nécessaires pour générer des recommandations
    const [
      engagementMetrics,
      audienceMetrics,
      revenueMetrics,
      topContent,
      engagementTrends,
      benchmarks,
    ] = await Promise.all([
      this.engagementService.getCreatorEngagementSummary(creatorId),
      this.audienceService.getCreatorAudienceSummary(creatorId),
      this.revenueService.getCreatorRevenueSummary(creatorId),
      this.engagementService.getTopPerformingContent(creatorId, 10),
      this.forecastingService.analyzeEngagementTrends(creatorId),
      this.benchmarkService.getEngagementBenchmarks(creatorId, 'all'),
    ]);

    // Générer toutes les recommandations possibles
    const allRecommendations: Recommendation[] = [];

    // Ajouter les recommandations selon le type demandé
    if (!type || type === RecommendationType.CONTENT) {
      const contentRecommendations = await this.generateContentRecommendations(
        creatorId,
        topContent,
        engagementMetrics,
      );
      allRecommendations.push(...contentRecommendations);
    }

    if (!type || type === RecommendationType.TIMING) {
      const timingRecommendations = await this.generateTimingRecommendations(
        creatorId,
        engagementMetrics,
      );
      allRecommendations.push(...timingRecommendations);
    }

    if (!type || type === RecommendationType.ENGAGEMENT) {
      const engagementRecommendations = await this.generateEngagementRecommendations(
        creatorId,
        engagementMetrics,
        benchmarks,
        engagementTrends,
      );
      allRecommendations.push(...engagementRecommendations);
    }

    if (!type || type === RecommendationType.MONETIZATION) {
      const monetizationRecommendations = await this.generateMonetizationRecommendations(
        creatorId,
        revenueMetrics,
        engagementMetrics,
      );
      allRecommendations.push(...monetizationRecommendations);
    }

    if (!type || type === RecommendationType.AUDIENCE) {
      const audienceRecommendations = await this.generateAudienceRecommendations(
        creatorId,
        audienceMetrics,
        engagementMetrics,
      );
      allRecommendations.push(...audienceRecommendations);
    }

    // Trier les recommandations par impact et limiter le nombre
    const sortedRecommendations = this.sortRecommendationsByImpact(allRecommendations);
    return sortedRecommendations.slice(0, limit);
  }

  /**
   * Génère des recommandations de contenu
   * @param creatorId ID du créateur
   * @param topContent Contenu le plus performant
   * @param engagementMetrics Métriques d'engagement
   * @returns Recommandations de contenu
   */
  private async generateContentRecommendations(
    creatorId: string,
    topContent: any[],
    engagementMetrics: any,
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Analyser le contenu le plus performant pour identifier les tendances
    if (topContent.length > 0) {
      // Identifier les types de contenu les plus performants
      const contentTypePerformance = new Map<ContentType, number>();
      
      topContent.forEach(content => {
        const currentValue = contentTypePerformance.get(content.contentType) || 0;
        contentTypePerformance.set(content.contentType, currentValue + content.engagementScore);
      });
      
      // Trouver le type de contenu le plus performant
      let bestContentType: ContentType = ContentType.POST;
      let bestScore = 0;
      
      contentTypePerformance.forEach((score, type) => {
        if (score > bestScore) {
          bestScore = score;
          bestContentType = type;
        }
      });
      
      // Recommandation basée sur le type de contenu le plus performant
      recommendations.push({
        id: `content-type-${Date.now()}`,
        type: RecommendationType.CONTENT,
        title: `Créez plus de contenu de type ${this.getContentTypeName(bestContentType)}`,
        description: `Vos contenus de type ${this.getContentTypeName(bestContentType)} génèrent plus d'engagement. Concentrez-vous sur ce format pour maximiser votre impact.`,
        impact: 'HIGH',
        metrics: ['engagement', 'views'],
        actions: [
          `Planifiez au moins 3 nouveaux contenus de type ${this.getContentTypeName(bestContentType)} pour le mois prochain`,
          `Analysez les caractéristiques communes de vos ${this.getContentTypeName(bestContentType)} les plus performants`,
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation basée sur la fréquence de publication
    const publishFrequency = await this.getPublishFrequency(creatorId);
    
    if (publishFrequency < 2) { // Moins de 2 publications par semaine
      recommendations.push({
        id: `publish-frequency-${Date.now()}`,
        type: RecommendationType.CONTENT,
        title: 'Augmentez votre fréquence de publication',
        description: 'Vous publiez moins souvent que la moyenne des créateurs dans votre catégorie. Augmenter votre fréquence de publication peut améliorer votre visibilité et votre engagement.',
        impact: 'MEDIUM',
        metrics: ['views', 'followers'],
        actions: [
          'Établissez un calendrier de publication régulier',
          'Visez au moins 3-4 publications par semaine',
          'Préparez du contenu à l\'avance pour maintenir la régularité',
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation basée sur la diversité du contenu
    const contentDiversity = await this.getContentDiversity(creatorId);
    
    if (contentDiversity < 0.3) { // Faible diversité
      recommendations.push({
        id: `content-diversity-${Date.now()}`,
        type: RecommendationType.CONTENT,
        title: 'Diversifiez vos formats de contenu',
        description: 'Votre contenu manque de diversité en termes de formats. Expérimenter avec différents types de contenu peut attirer de nouveaux publics et augmenter l\'engagement.',
        impact: 'MEDIUM',
        metrics: ['engagement', 'audience_growth'],
        actions: [
          'Essayez au moins un nouveau format de contenu ce mois-ci',
          'Adaptez votre contenu existant à différents formats',
          'Analysez les performances des nouveaux formats pour identifier ce qui fonctionne',
        ],
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Génère des recommandations de timing
   * @param creatorId ID du créateur
   * @param engagementMetrics Métriques d'engagement
   * @returns Recommandations de timing
   */
  private async generateTimingRecommendations(
    creatorId: string,
    engagementMetrics: any,
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Analyser les heures et jours de publication optimaux
    const seasonality = await this.forecastingService.analyzeSeasonality(creatorId);
    
    if (seasonality.length > 0 && seasonality[0].daily && seasonality[0].weekly) {
      const bestHours = seasonality[0].daily.peakHours;
      const bestDays = seasonality[0].weekly.peakDays;
      
      const dayNames = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
      const bestDayNames = bestDays.map(day => dayNames[day]);
      
      recommendations.push({
        id: `optimal-timing-${Date.now()}`,
        type: RecommendationType.TIMING,
        title: 'Optimisez vos horaires de publication',
        description: `Vos publications génèrent plus d'engagement lorsqu'elles sont publiées ${bestDayNames.join(', ')} entre ${bestHours.join('h, ')}h.`,
        impact: 'HIGH',
        metrics: ['engagement', 'views'],
        actions: [
          `Planifiez vos publications principales pour ${bestDayNames.join(' ou ')}`,
          `Publiez de préférence entre ${bestHours[0]}h et ${bestHours[bestHours.length - 1]}h`,
          'Utilisez des outils de planification pour programmer vos publications aux heures optimales',
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation sur la régularité
    const publishingConsistency = await this.getPublishingConsistency(creatorId);
    
    if (publishingConsistency < 0.5) { // Faible consistance
      recommendations.push({
        id: `publishing-consistency-${Date.now()}`,
        type: RecommendationType.TIMING,
        title: 'Améliorez la régularité de vos publications',
        description: 'Votre calendrier de publication manque de régularité. Une publication constante aide votre audience à savoir quand attendre votre nouveau contenu.',
        impact: 'MEDIUM',
        metrics: ['engagement', 'retention'],
        actions: [
          'Établissez un calendrier de publication hebdomadaire',
          'Communiquez ce calendrier à votre audience',
          'Utilisez des outils de planification pour maintenir la régularité',
        ],
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Génère des recommandations d'engagement
   * @param creatorId ID du créateur
   * @param engagementMetrics Métriques d'engagement
   * @param benchmarks Benchmarks
   * @param engagementTrends Tendances d'engagement
   * @returns Recommandations d'engagement
   */
  private async generateEngagementRecommendations(
    creatorId: string,
    engagementMetrics: any,
    benchmarks: any,
    engagementTrends: any[],
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Recommandation basée sur le taux d'engagement par rapport au benchmark
    if (engagementMetrics.engagementRate < benchmarks.engagementRate.categoryAverage) {
      recommendations.push({
        id: `improve-engagement-${Date.now()}`,
        type: RecommendationType.ENGAGEMENT,
        title: 'Améliorez votre taux d\'engagement',
        description: 'Votre taux d\'engagement est inférieur à la moyenne de votre catégorie. Encourager les interactions peut augmenter votre visibilité et fidéliser votre audience.',
        impact: 'HIGH',
        metrics: ['engagement_rate', 'comments', 'likes'],
        actions: [
          'Posez des questions à votre audience dans vos publications',
          'Répondez activement aux commentaires',
          'Créez des sondages et des contenus interactifs',
          'Organisez des concours ou des défis',
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation basée sur les tendances d'engagement
    const viewsTrend = engagementTrends.find(trend => trend.metric === 'views');
    
    if (viewsTrend && viewsTrend.trend === 'decreasing') {
      recommendations.push({
        id: `reverse-views-trend-${Date.now()}`,
        type: RecommendationType.ENGAGEMENT,
        title: 'Inversez la tendance de vos vues',
        description: 'Vos vues sont en baisse. Rafraîchir votre approche de contenu peut aider à attirer à nouveau l\'attention de votre audience.',
        impact: 'HIGH',
        metrics: ['views', 'reach'],
        actions: [
          'Analysez votre contenu récent pour identifier ce qui a changé',
          'Expérimentez avec de nouveaux formats ou sujets',
          'Améliorez vos titres et miniatures pour augmenter le taux de clic',
          'Partagez votre contenu sur d\'autres plateformes pour élargir votre portée',
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation sur l'interaction avec l'audience
    const commentResponseRate = await this.getCommentResponseRate(creatorId);
    
    if (commentResponseRate < 0.3) { // Répond à moins de 30% des commentaires
      recommendations.push({
        id: `comment-response-${Date.now()}`,
        type: RecommendationType.ENGAGEMENT,
        title: 'Augmentez vos interactions avec les commentaires',
        description: 'Vous répondez à un faible pourcentage des commentaires. Interagir davantage avec votre audience peut renforcer la fidélité et encourager plus d\'engagement.',
        impact: 'MEDIUM',
        metrics: ['comments', 'engagement_rate'],
        actions: [
          'Réservez du temps chaque jour pour répondre aux commentaires',
          'Priorisez les questions et les commentaires détaillés',
          'Encouragez les discussions entre vos abonnés',
        ],
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Génère des recommandations de monétisation
   * @param creatorId ID du créateur
   * @param revenueMetrics Métriques de revenus
   * @param engagementMetrics Métriques d'engagement
   * @returns Recommandations de monétisation
   */
  private async generateMonetizationRecommendations(
    creatorId: string,
    revenueMetrics: any,
    engagementMetrics: any,
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Recommandation sur la diversification des revenus
    const revenueSources = await this.getRevenueSources(creatorId);
    
    if (revenueSources.length < 2) {
      recommendations.push({
        id: `diversify-revenue-${Date.now()}`,
        type: RecommendationType.MONETIZATION,
        title: 'Diversifiez vos sources de revenus',
        description: 'Vous dépendez principalement d\'une seule source de revenus. Diversifier peut augmenter vos revenus totaux et réduire les risques.',
        impact: 'HIGH',
        metrics: ['revenue', 'revenue_stability'],
        actions: [
          'Explorez de nouvelles options de monétisation (produits dérivés, abonnements, etc.)',
          'Analysez quelles sources de revenus fonctionnent le mieux pour des créateurs similaires',
          'Testez une nouvelle source de revenus dans les 30 prochains jours',
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation sur l'optimisation des revenus publicitaires
    if (revenueSources.includes('advertising')) {
      const adRevenue = await this.getAdRevenue(creatorId);
      const adImpressions = await this.getAdImpressions(creatorId);
      
      if (adRevenue > 0 && adImpressions > 0) {
        const rpm = (adRevenue / adImpressions) * 1000; // Revenu pour 1000 impressions
        
        if (rpm < 5) { // RPM faible
          recommendations.push({
            id: `optimize-ad-revenue-${Date.now()}`,
            type: RecommendationType.MONETIZATION,
            title: 'Optimisez vos revenus publicitaires',
            description: 'Votre RPM (revenu pour 1000 impressions) est relativement bas. Optimiser le placement et le type de publicités peut augmenter vos revenus sans nécessiter plus de vues.',
            impact: 'MEDIUM',
            metrics: ['ad_revenue', 'rpm'],
            actions: [
              'Analysez quels types de contenu génèrent le meilleur RPM',
              'Expérimentez avec différents placements publicitaires',
              'Créez du contenu sur des sujets avec des CPM plus élevés',
            ],
            createdAt: new Date(),
          });
        }
      }
    }

    return recommendations;
  }

  /**
   * Génère des recommandations d'audience
   * @param creatorId ID du créateur
   * @param audienceMetrics Métriques d'audience
   * @param engagementMetrics Métriques d'engagement
   * @returns Recommandations d'audience
   */
  private async generateAudienceRecommendations(
    creatorId: string,
    audienceMetrics: any,
    engagementMetrics: any,
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];

    // Recommandation sur la croissance de l'audience
    if (audienceMetrics.growthRate < 0.05) { // Croissance inférieure à 5%
      recommendations.push({
        id: `audience-growth-${Date.now()}`,
        type: RecommendationType.AUDIENCE,
        title: 'Accélérez la croissance de votre audience',
        description: 'Votre taux de croissance d\'audience est relativement bas. Des stratégies ciblées peuvent vous aider à attirer de nouveaux abonnés plus rapidement.',
        impact: 'HIGH',
        metrics: ['followers', 'growth_rate'],
        actions: [
          'Collaborez avec d\'autres créateurs pour des projets communs',
          'Optimisez vos titres et descriptions pour la découvrabilité',
          'Partagez votre contenu sur d\'autres plateformes sociales',
          'Créez du contenu qui incite au partage',
        ],
        createdAt: new Date(),
      });
    }

    // Recommandation sur la rétention de l'audience
    const retentionRate = await this.getAudienceRetentionRate(creatorId);
    
    if (retentionRate < 0.7) { // Taux de rétention inférieur à 70%
      recommendations.push({
        id: `audience-retention-${Date.now()}`,
        type: RecommendationType.AUDIENCE,
        title: 'Améliorez la rétention de votre audience',
        description: 'Votre taux de rétention d\'audience est inférieur à la moyenne. Fidéliser votre audience existante est aussi important qu\'en attirer une nouvelle.',
        impact: 'MEDIUM',
        metrics: ['retention_rate', 'returning_viewers'],
        actions: [
          'Créez des séries de contenu pour encourager les visites répétées',
          'Établissez un calendrier de publication cohérent',
          'Engagez-vous avec votre audience via les commentaires et les messages',
          'Demandez des retours à votre audience sur le contenu qu\'ils préfèrent',
        ],
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  /**
   * Trie les recommandations par impact
   * @param recommendations Liste de recommandations
   * @returns Liste triée
   */
  private sortRecommendationsByImpact(recommendations: Recommendation[]): Recommendation[] {
    const impactOrder = {
      'HIGH': 0,
      'MEDIUM': 1,
      'LOW': 2,
    };
    
    return [...recommendations].sort((a, b) => {
      return impactOrder[a.impact] - impactOrder[b.impact];
    });
  }

  /**
   * Récupère la fréquence de publication d'un créateur (publications par semaine)
   * @param creatorId ID du créateur
   * @returns Fréquence de publication
   */
  private async getPublishFrequency(creatorId: string): Promise<number> {
    // Récupérer les publications des 4 dernières semaines
    const fourWeeksAgo = new Date();
    fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28);
    
    const contentCount = await this.prisma.content.count({
      where: {
        creatorId,
        createdAt: {
          gte: fourWeeksAgo,
        },
      },
    });
    
    return contentCount / 4; // Publications par semaine
  }

  /**
   * Récupère la diversité du contenu d'un créateur (0-1)
   * @param creatorId ID du créateur
   * @returns Indice de diversité
   */
  private async getContentDiversity(creatorId: string): Promise<number> {
    // Récupérer la distribution des types de contenu
    const contentTypes = await this.prisma.content.groupBy({
      by: ['contentType'],
      where: {
        creatorId,
      },
      _count: true,
    });
    
    if (contentTypes.length === 0) {
      return 0;
    }
    
    // Calculer l'indice de diversité (0-1)
    const totalContent = contentTypes.reduce((sum, type) => sum + type._count, 0);
    const typeRatios = contentTypes.map(type => type._count / totalContent);
    
    // Utiliser l'indice de diversité de Simpson
    const sumSquaredRatios = typeRatios.reduce((sum, ratio) => sum + ratio * ratio, 0);
    return 1 - sumSquaredRatios;
  }

  /**
   * Récupère la consistance de publication d'un créateur (0-1)
   * @param creatorId ID du créateur
   * @returns Indice de consistance
   */
  private async getPublishingConsistency(creatorId: string): Promise<number> {
    // Récupérer les dates de publication des 8 dernières semaines
    const eightWeeksAgo = new Date();
    eightWeeksAgo.setDate(eightWeeksAgo.getDate() - 56);
    
    const publications = await this.prisma.content.findMany({
      where: {
        creatorId,
        createdAt: {
          gte: eightWeeksAgo,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    
    if (publications.length < 2) {
      return 0;
    }
    
    // Calculer les intervalles entre les publications
    const intervals = [];
    for (let i = 1; i < publications.length; i++) {
      const interval = publications[i].createdAt.getTime() - publications[i-1].createdAt.getTime();
      intervals.push(interval / (1000 * 60 * 60 * 24)); // Convertir en jours
    }
    
    // Calculer l'écart-type des intervalles
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const stdDev = Math.sqrt(variance);
    
    // Calculer la consistance (0-1)
    const consistencyScore = Math.max(0, 1 - (stdDev / avgInterval) / 2);
    return consistencyScore;
  }

  /**
   * Récupère le taux de réponse aux commentaires d'un créateur (0-1)
   * @param creatorId ID du créateur
   * @returns Taux de réponse
   */
  private async getCommentResponseRate(creatorId: string): Promise<number> {
    // Simuler un taux de réponse (à implémenter avec les données réelles)
    return Math.random(); // Valeur aléatoire entre 0 et 1 pour la démonstration
  }

  /**
   * Récupère les sources de revenus d'un créateur
   * @param creatorId ID du créateur
   * @returns Liste des sources de revenus
   */
  private async getRevenueSources(creatorId: string): Promise<string[]> {
    // Simuler des sources de revenus (à implémenter avec les données réelles)
    const possibleSources = ['advertising', 'subscriptions', 'donations', 'merchandise', 'sponsorships'];
    const count = Math.floor(Math.random() * 3) + 1; // 1 à 3 sources
    
    return possibleSources.slice(0, count);
  }

  /**
   * Récupère les revenus publicitaires d'un créateur
   * @param creatorId ID du créateur
   * @returns Revenus publicitaires
   */
  private async getAdRevenue(creatorId: string): Promise<number> {
    // Simuler des revenus publicitaires (à implémenter avec les données réelles)
    return Math.random() * 1000; // 0 à 1000
  }

  /**
   * Récupère les impressions publicitaires d'un créateur
   * @param creatorId ID du créateur
   * @returns Impressions publicitaires
   */
  private async getAdImpressions(creatorId: string): Promise<number> {
    // Simuler des impressions publicitaires (à implémenter avec les données réelles)
    return Math.random() * 100000; // 0 à 100000
  }

  /**
   * Récupère le taux de rétention de l'audience d'un créateur (0-1)
   * @param creatorId ID du créateur
   * @returns Taux de rétention
   */
  private async getAudienceRetentionRate(creatorId: string): Promise<number> {
    // Simuler un taux de rétention (à implémenter avec les données réelles)
    return 0.5 + Math.random() * 0.5; // 0.5 à 1.0
  }

  /**
   * Obtient le nom lisible d'un type de contenu
   * @param contentType Type de contenu
   * @returns Nom lisible
   */
  private getContentTypeName(contentType: ContentType): string {
    const contentTypeNames = {
      [ContentType.POST]: 'publication',
      [ContentType.IMAGE]: 'image',
      [ContentType.VIDEO]: 'vidéo',
      [ContentType.ARTICLE]: 'article',
      [ContentType.COMMENT]: 'commentaire',
      [ContentType.TEXT]: 'texte',
    };
    
    return contentTypeNames[contentType] || contentType;
  }
}
