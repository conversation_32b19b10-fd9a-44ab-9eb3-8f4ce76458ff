import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsService } from '../analytics.service';
import { EngagementService } from '../services/engagement.service';
import { AudienceService } from '../services/audience.service';
import { RevenueService } from '../services/revenue.service';
import { ForecastingService } from '../services/forecasting.service';
import { BenchmarkService } from '../services/benchmark.service';
import { DashboardService } from '../services/dashboard.service';
import { ConfigService } from '@nestjs/config';
import { ContentType } from '@prisma/client';

// Mock des services
const mockEngagementService = {
  getCreatorEngagementMetrics: jest.fn(),
  getContentEngagementMetrics: jest.fn(),
  getTopPerformingContent: jest.fn(),
  getCreatorEngagementSummary: jest.fn(),
};

const mockAudienceService = {
  getCreatorAudienceMetrics: jest.fn(),
  getCreatorAudienceSummary: jest.fn(),
};

const mockRevenueService = {
  getCreatorRevenueMetrics: jest.fn(),
  getContentRevenueMetrics: jest.fn(),
  getCreatorRevenueSummary: jest.fn(),
};

const mockForecastingService = {
  getEngagementForecasts: jest.fn(),
  getAudienceForecasts: jest.fn(),
  getRevenueForecasts: jest.fn(),
};

const mockBenchmarkService = {
  getEngagementBenchmarks: jest.fn(),
  getAudienceBenchmarks: jest.fn(),
  getRevenueBenchmarks: jest.fn(),
};

const mockDashboardService = {
  getCreatorDashboards: jest.fn(),
  getDashboard: jest.fn(),
  createDashboard: jest.fn(),
  updateDashboard: jest.fn(),
  deleteDashboard: jest.fn(),
};

const mockConfigService = {
  get: jest.fn(),
};

describe('AnalyticsService', () => {
  let service: AnalyticsService;
  let engagementService: EngagementService;
  let audienceService: AudienceService;
  let revenueService: RevenueService;
  let forecastingService: ForecastingService;
  let benchmarkService: BenchmarkService;
  let dashboardService: DashboardService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsService,
        {
          provide: EngagementService,
          useValue: mockEngagementService,
        },
        {
          provide: AudienceService,
          useValue: mockAudienceService,
        },
        {
          provide: RevenueService,
          useValue: mockRevenueService,
        },
        {
          provide: ForecastingService,
          useValue: mockForecastingService,
        },
        {
          provide: BenchmarkService,
          useValue: mockBenchmarkService,
        },
        {
          provide: DashboardService,
          useValue: mockDashboardService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);
    engagementService = module.get<EngagementService>(EngagementService);
    audienceService = module.get<AudienceService>(AudienceService);
    revenueService = module.get<RevenueService>(RevenueService);
    forecastingService = module.get<ForecastingService>(ForecastingService);
    benchmarkService = module.get<BenchmarkService>(BenchmarkService);
    dashboardService = module.get<DashboardService>(DashboardService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCreatorMetrics', () => {
    it('should return creator metrics', async () => {
      const creatorId = '123';
      const filters = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
        contentType: ContentType.ARTICLE,
      };

      const engagementMetrics = { summary: { totalViews: 100 } };
      const audienceMetrics = { totalFollowers: 500 };
      const revenueMetrics = { totalRevenue: 1000 };
      const topContent = [{ id: '456', views: 50 }];

      mockEngagementService.getCreatorEngagementMetrics.mockResolvedValue(engagementMetrics);
      mockAudienceService.getCreatorAudienceMetrics.mockResolvedValue(audienceMetrics);
      mockRevenueService.getCreatorRevenueMetrics.mockResolvedValue(revenueMetrics);
      mockEngagementService.getTopPerformingContent.mockResolvedValue(topContent);

      const result = await service.getCreatorMetrics(creatorId, filters);

      expect(engagementService.getCreatorEngagementMetrics).toHaveBeenCalledWith(
        creatorId,
        new Date(filters.startDate),
        new Date(filters.endDate),
        filters.contentType,
      );
      expect(audienceService.getCreatorAudienceMetrics).toHaveBeenCalledWith(
        creatorId,
        new Date(filters.startDate),
        new Date(filters.endDate),
      );
      expect(revenueService.getCreatorRevenueMetrics).toHaveBeenCalledWith(
        creatorId,
        new Date(filters.startDate),
        new Date(filters.endDate),
      );
      expect(engagementService.getTopPerformingContent).toHaveBeenCalledWith(
        creatorId,
        10,
        new Date(filters.startDate),
        new Date(filters.endDate),
      );

      expect(result).toEqual({
        engagement: engagementMetrics,
        audience: audienceMetrics,
        revenue: revenueMetrics,
        topContent,
        period: {
          startDate: new Date(filters.startDate),
          endDate: new Date(filters.endDate),
        },
      });
    });
  });

  describe('getContentMetrics', () => {
    it('should return content metrics', async () => {
      const creatorId = '123';
      const contentId = '456';
      const filters = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };

      const engagementMetrics = { summary: { totalViews: 100 } };
      const revenueMetrics = { totalRevenue: 1000 };

      mockEngagementService.getContentEngagementMetrics.mockResolvedValue(engagementMetrics);
      mockRevenueService.getContentRevenueMetrics.mockResolvedValue(revenueMetrics);

      const result = await service.getContentMetrics(creatorId, contentId, filters);

      expect(engagementService.getContentEngagementMetrics).toHaveBeenCalledWith(
        contentId,
        new Date(filters.startDate),
        new Date(filters.endDate),
      );
      expect(revenueService.getContentRevenueMetrics).toHaveBeenCalledWith(
        contentId,
        new Date(filters.startDate),
        new Date(filters.endDate),
      );

      expect(result).toEqual({
        engagement: engagementMetrics,
        revenue: revenueMetrics,
        period: {
          startDate: new Date(filters.startDate),
          endDate: new Date(filters.endDate),
        },
      });
    });
  });

  describe('getCreatorSummary', () => {
    it('should return creator summary', async () => {
      const creatorId = '123';

      const engagementSummary = { totalViews: 1000 };
      const audienceSummary = { totalFollowers: 500 };
      const revenueSummary = { totalRevenue: 2000 };
      const topContent = [{ id: '456', views: 50 }];

      mockEngagementService.getCreatorEngagementSummary.mockResolvedValue(engagementSummary);
      mockAudienceService.getCreatorAudienceSummary.mockResolvedValue(audienceSummary);
      mockRevenueService.getCreatorRevenueSummary.mockResolvedValue(revenueSummary);
      mockEngagementService.getTopPerformingContent.mockResolvedValue(topContent);

      const result = await service.getCreatorSummary(creatorId);

      expect(engagementService.getCreatorEngagementSummary).toHaveBeenCalledWith(creatorId);
      expect(audienceService.getCreatorAudienceSummary).toHaveBeenCalledWith(creatorId);
      expect(revenueService.getCreatorRevenueSummary).toHaveBeenCalledWith(creatorId);
      expect(engagementService.getTopPerformingContent).toHaveBeenCalledWith(creatorId, 5);

      expect(result).toEqual({
        engagement: engagementSummary,
        audience: audienceSummary,
        revenue: revenueSummary,
        topContent,
      });
    });
  });
});
