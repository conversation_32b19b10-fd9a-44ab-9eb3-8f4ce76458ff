import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuditService } from './services/audit.service';
import { AuditController } from './controllers/audit.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { AuditLogService } from './services/audit-log.service';
import { ComplianceService } from './services/compliance.service';
import { ReportingService } from './services/reporting.service';

@Module({
  imports: [PrismaModule],
  controllers: [AuditController],
  providers: [
    AuditService,
    AuditLogService,
    ComplianceService,
    ReportingService,
  ],
  exports: [AuditService, AuditLogService],
})
export class AuditModule {}
