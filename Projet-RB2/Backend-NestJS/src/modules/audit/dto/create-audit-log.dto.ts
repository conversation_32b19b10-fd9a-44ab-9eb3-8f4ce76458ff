import { IsString, IsNotEmpty, IsOptional, IsObject, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAuditLogDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur qui a effectué l\'action',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Action effectuée',
    example: 'UPDATE',
  })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({
    description: 'Entité concernée',
    example: 'User',
  })
  @IsString()
  @IsNotEmpty()
  entity: string;

  @ApiProperty({
    description: 'ID de l\'entité concernée',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  entityId: string;

  @ApiPropertyOptional({
    description: 'Anciennes valeurs',
    example: { name: '<PERSON>', email: '<EMAIL>' },
  })
  @IsObject()
  @IsOptional()
  oldValues?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Nouvelles valeurs',
    example: { name: 'John Smith', email: '<EMAIL>' },
  })
  @IsObject()
  @IsOptional()
  newValues?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Métadonnées',
    example: { requestId: '123', sessionId: '456' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Adresse IP de l\'utilisateur',
    example: '***********',
  })
  @IsString()
  @IsOptional()
  ip?: string;

  @ApiPropertyOptional({
    description: 'User-Agent de l\'utilisateur',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  })
  @IsString()
  @IsOptional()
  userAgent?: string;
}
