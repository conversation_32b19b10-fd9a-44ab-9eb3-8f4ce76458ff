import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsO<PERSON>al, IsObject, IsArray, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ComplianceType } from '../enums/compliance-type.enum';
import { ComplianceStatus } from '../enums/compliance-status.enum';

export class CreateComplianceCheckDto {
  @ApiProperty({
    description: 'Type de contrôle de conformité',
    enum: ComplianceType,
    example: ComplianceType.GDPR,
  })
  @IsEnum(ComplianceType)
  @IsNotEmpty()
  type: ComplianceType;

  @ApiProperty({
    description: 'Statut de conformité',
    enum: ComplianceStatus,
    example: ComplianceStatus.COMPLIANT,
  })
  @IsEnum(ComplianceStatus)
  @IsNotEmpty()
  status: ComplianceStatus;

  @ApiPropertyOptional({
    description: 'Détails du contrôle de conformité',
    example: {
      privacyPolicyExists: true,
      privacyPolicyLastUpdated: '2023-01-01',
      privacyPolicyVersion: '1.0',
    },
  })
  @IsObject()
  @IsOptional()
  details?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Problèmes identifiés',
    example: [
      {
        description: 'Les journaux d\'accès aux données ne sont pas conservés pendant la durée requise (24 mois)',
        severity: 'HIGH',
        recommendation: 'Configurer la rétention des journaux pour une durée de 24 mois',
      },
    ],
  })
  @IsArray()
  @IsOptional()
  issues?: Array<{
    description: string;
    severity: string;
    recommendation?: string;
  }>;

  @ApiProperty({
    description: 'ID de l\'utilisateur qui a effectué le contrôle',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  performedBy: string;
}
