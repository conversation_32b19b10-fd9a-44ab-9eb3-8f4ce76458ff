import { IsString, IsNotEmpty, IsOptional, IsObject, IsEnum, IsUUID, IsDate } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReportType } from '../enums/report-type.enum';
import { ReportFormat } from '../enums/report-format.enum';
import { Type } from 'class-transformer';

export class CreateReportDto {
  @ApiProperty({
    description: 'Type de rapport',
    enum: ReportType,
    example: ReportType.AUDIT,
  })
  @IsEnum(ReportType)
  @IsNotEmpty()
  type: ReportType;

  @ApiProperty({
    description: 'Format du rapport',
    enum: ReportFormat,
    example: ReportFormat.JSON,
  })
  @IsEnum(ReportFormat)
  @IsNotEmpty()
  format: ReportFormat;

  @ApiProperty({
    description: 'Date de début de la période du rapport',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({
    description: 'Date de fin de la période du rapport',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({
    description: 'ID de l\'utilisateur qui a généré le rapport',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  generatedBy: string;

  @ApiPropertyOptional({
    description: 'Options du rapport',
    example: {
      includeDetails: true,
      groupBy: 'day',
    },
  })
  @IsObject()
  @IsOptional()
  options?: Record<string, any>;
}
