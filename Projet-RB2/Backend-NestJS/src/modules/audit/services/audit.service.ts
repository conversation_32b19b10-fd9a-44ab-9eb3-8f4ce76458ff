import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { AuditLogService } from './audit-log.service';
import { CreateAuditEntryDto } from '../dto/create-audit-entry.dto';
import { AuditOptions } from '../../../shared/interfaces';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly auditLogService: AuditLogService,
  ) {}

  /**
   * Crée une entrée d'audit
   * @param createAuditEntryDto Données de l'entrée d'audit
   * @returns L'entrée d'audit créée
   */
  async createAuditEntry(createAuditEntryDto: CreateAuditEntryDto) {
    try {
      const auditEntry = await this.prisma.auditEntry.create({
        data: {
          userId: createAuditEntryDto.userId,
          action: createAuditEntryDto.action,
          resource: createAuditEntryDto.resource,
          resourceId: createAuditEntryDto.resourceId,
          details: createAuditEntryDto.details || {},
          ip: createAuditEntryDto.ip,
          userAgent: createAuditEntryDto.userAgent,
        },
      });

      this.logger.log(`Entrée d'audit créée: ${auditEntry.id}`);
      return auditEntry;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'entrée d'audit: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère toutes les entrées d'audit avec pagination
   * @param options Options de pagination
   * @returns Liste paginée des entrées d'audit
   */
  async findAll(options?: AuditOptions) {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';
    const userId = options?.userId;
    const action = options?.action;
    const resource = options?.resource;
    const startDate = options?.startDate;
    const endDate = options?.endDate;

    // Construire les filtres
    const where: any = {};
    if (userId) where.userId = userId;
    if (action) where.action = action;
    if (resource) where.resource = resource;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [auditEntries, total] = await Promise.all([
      this.prisma.auditEntry.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
            },
          },
        },
      }),
      this.prisma.auditEntry.count({ where }),
    ]);

    return {
      auditEntries,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère une entrée d'audit par son ID
   * @param id ID de l'entrée d'audit
   * @returns L'entrée d'audit
   */
  async findOne(id: string) {
    return this.prisma.auditEntry.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Récupère les entrées d'audit pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de pagination
   * @returns Liste paginée des entrées d'audit de l'utilisateur
   */
  async findByUser(userId: string, options?: AuditOptions) {
    return this.findAll({
      ...options,
      userId,
      action: options?.action ?? '',
      resource: options?.resource ?? '',
    });
  }

  /**
   * Récupère les entrées d'audit pour une action
   * @param action Action
   * @param options Options de pagination
   * @returns Liste paginée des entrées d'audit pour l'action
   */
  async findByAction(action: string, options?: AuditOptions) {
    return this.findAll({
      ...options,
      action,
      resource: options?.resource ?? '',
    });
  }

  /**
   * Récupère les entrées d'audit pour une ressource
   * @param resource Ressource
   * @param options Options de pagination
   * @returns Liste paginée des entrées d'audit pour la ressource
   */
  async findByResource(resource: string, options?: AuditOptions) {
    return this.findAll({
      ...options,
      resource,
      action: options?.action ?? '',
    });
  }

  /**
   * Récupère les entrées d'audit pour une période
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param options Options de pagination
   * @returns Liste paginée des entrées d'audit pour la période
   */
  async findByDateRange(startDate: Date, endDate: Date, options?: AuditOptions) {
    return this.findAll({
      ...options,
      startDate,
      endDate,
      action: options?.action ?? '',
      resource: options?.resource ?? '',
    });
  }

  /**
   * Génère un rapport d'audit
   * @param options Options de rapport
   * @returns Rapport d'audit
   */
  async generateReport(options: AuditOptions) {
    const { auditEntries, total } = await this.findAll(options);

    // Grouper les entrées par action
    const actionGroups = this.groupByAction(auditEntries);

    // Grouper les entrées par ressource
    const resourceGroups = this.groupByResource(auditEntries);

    // Grouper les entrées par utilisateur
    const userGroups = this.groupByUser(auditEntries);

    // Calculer les statistiques
    const stats = this.calculateStats(auditEntries);

    return {
      total,
      startDate: options.startDate,
      endDate: options.endDate,
      actionGroups,
      resourceGroups,
      userGroups,
      stats,
      generatedAt: new Date(),
    };
  }

  /**
   * Groupe les entrées d'audit par action
   * @param auditEntries Entrées d'audit
   * @returns Entrées groupées par action
   */
  private groupByAction(auditEntries: any[]) {
    const groups: Record<string, any[]> = {};
    
    auditEntries.forEach(entry => {
      if (!groups[entry.action]) {
        groups[entry.action] = [];
      }
      groups[entry.action].push(entry);
    });
    
    return Object.entries(groups).map(([action, entries]) => ({
      action,
      count: entries.length,
      entries,
    }));
  }

  /**
   * Groupe les entrées d'audit par ressource
   * @param auditEntries Entrées d'audit
   * @returns Entrées groupées par ressource
   */
  private groupByResource(auditEntries: any[]) {
    const groups: Record<string, any[]> = {};
    
    auditEntries.forEach(entry => {
      if (!groups[entry.resource]) {
        groups[entry.resource] = [];
      }
      groups[entry.resource].push(entry);
    });
    
    return Object.entries(groups).map(([resource, entries]) => ({
      resource,
      count: entries.length,
      entries,
    }));
  }

  /**
   * Groupe les entrées d'audit par utilisateur
   * @param auditEntries Entrées d'audit
   * @returns Entrées groupées par utilisateur
   */
  private groupByUser(auditEntries: any[]) {
    const groups: Record<string, any[]> = {};
    
    auditEntries.forEach(entry => {
      if (entry.userId) {
        if (!groups[entry.userId]) {
          groups[entry.userId] = [];
        }
        groups[entry.userId].push(entry);
      }
    });
    
    return Object.entries(groups).map(([userId, entries]) => ({
      userId,
      userName: entries[0]?.user?.name || 'Unknown',
      userEmail: entries[0]?.user?.email || 'Unknown',
      count: entries.length,
      entries,
    }));
  }

  /**
   * Calcule les statistiques des entrées d'audit
   * @param auditEntries Entrées d'audit
   * @returns Statistiques
   */
  private calculateStats(auditEntries: any[]) {
    // Compter les actions par jour
    const actionsByDay: Record<string, number> = {};
    
    auditEntries.forEach(entry => {
      const day = entry.createdAt.toISOString().split('T')[0];
      if (!actionsByDay[day]) {
        actionsByDay[day] = 0;
      }
      actionsByDay[day]++;
    });
    
    // Trouver l'utilisateur le plus actif
    const userActions: Record<string, number> = {};
    
    auditEntries.forEach(entry => {
      if (entry.userId) {
        if (!userActions[entry.userId]) {
          userActions[entry.userId] = 0;
        }
        userActions[entry.userId]++;
      }
    });
    
    let mostActiveUserId: string | null = null;
    let mostActiveUserCount = 0;
    
    Object.entries(userActions).forEach(([userId, count]) => {
      if (count > mostActiveUserCount) {
        mostActiveUserId = userId;
        mostActiveUserCount = count;
      }
    });
    
    const mostActiveUser = auditEntries.find(entry => entry.userId === mostActiveUserId)?.user;
    
    return {
      totalEntries: auditEntries.length,
      actionsByDay,
      mostActiveUser: mostActiveUser ? {
        id: mostActiveUser.id,
        name: mostActiveUser.name,
        email: mostActiveUser.email,
        actionCount: mostActiveUserCount,
      } : null,
    };
  }
}
