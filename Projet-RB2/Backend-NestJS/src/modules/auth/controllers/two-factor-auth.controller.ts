import { Controller, Post, Body, UseGuards, Get, Req, UnauthorizedException, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { TwoFactorAuthService } from '../services/two-factor-auth.service';
import { VerifyTwoFactorDto } from '../dto/verify-two-factor.dto';
import { Request } from 'express';

@ApiTags('auth/2fa')
@Controller('auth/2fa')
export class TwoFactorAuthController {
  constructor(private readonly twoFactorAuthService: TwoFactorAuthService) {}

  /**
   * Générer un secret 2FA pour l'utilisateur connecté
   */
  @Post('generate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un secret 2FA' })
  @ApiResponse({ status: 201, description: 'Secret 2FA généré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async generateTwoFactorSecret(@Req() req: Request) {
    const user = req.user as any;
    
    if (!user || !user.id || !user.email) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    return this.twoFactorAuthService.generateTwoFactorSecret(user.id, user.email);
  }

  /**
   * Activer l'authentification à deux facteurs
   */
  @Post('enable')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Activer l\'authentification à deux facteurs' })
  @ApiResponse({ status: 200, description: '2FA activé avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé ou token invalide' })
  async enableTwoFactor(@Req() req: Request, @Body() verifyDto: VerifyTwoFactorDto) {
    const user = req.user as any;
    
    if (!user || !user.id) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    const result = await this.twoFactorAuthService.enableTwoFactor(user.id, verifyDto.token);
    
    return {
      success: result,
      message: 'Two-factor authentication enabled successfully',
    };
  }

  /**
   * Désactiver l'authentification à deux facteurs
   */
  @Post('disable')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Désactiver l\'authentification à deux facteurs' })
  @ApiResponse({ status: 200, description: '2FA désactivé avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async disableTwoFactor(@Req() req: Request, @Body() verifyDto: VerifyTwoFactorDto) {
    const user = req.user as any;
    
    if (!user || !user.id) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    // Vérifier d'abord le token pour s'assurer que c'est bien l'utilisateur
    await this.twoFactorAuthService.verifyTwoFactorToken(user.id, verifyDto.token);
    
    const result = await this.twoFactorAuthService.disableTwoFactor(user.id);
    
    return {
      success: result,
      message: 'Two-factor authentication disabled successfully',
    };
  }

  /**
   * Vérifier un token 2FA
   */
  @Post('verify')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Vérifier un token 2FA' })
  @ApiResponse({ status: 200, description: 'Token 2FA valide' })
  @ApiResponse({ status: 401, description: 'Token 2FA invalide' })
  async verifyTwoFactorToken(@Req() req: Request, @Body() verifyDto: VerifyTwoFactorDto) {
    const user = req.user as any;
    
    if (!user || !user.id) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    const result = await this.twoFactorAuthService.verifyTwoFactorToken(user.id, verifyDto.token);
    
    return {
      success: result,
      message: 'Two-factor token verified successfully',
    };
  }

  /**
   * Générer de nouveaux codes de récupération
   */
  @Post('recovery-codes')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer de nouveaux codes de récupération' })
  @ApiResponse({ status: 201, description: 'Codes de récupération générés avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé ou token invalide' })
  async generateRecoveryCodes(@Req() req: Request, @Body() verifyDto: VerifyTwoFactorDto) {
    const user = req.user as any;
    
    if (!user || !user.id) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    // Vérifier d'abord le token pour s'assurer que c'est bien l'utilisateur
    await this.twoFactorAuthService.verifyTwoFactorToken(user.id, verifyDto.token);
    
    const recoveryCodes = await this.twoFactorAuthService.generateNewRecoveryCodes(user.id);
    
    return {
      success: true,
      recoveryCodes,
      message: 'Recovery codes generated successfully',
    };
  }
}
