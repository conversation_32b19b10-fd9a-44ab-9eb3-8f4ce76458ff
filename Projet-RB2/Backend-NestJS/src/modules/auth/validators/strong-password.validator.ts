import { registerDecorator, ValidationOptions, ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { Injectable } from '@nestjs/common';
import { PasswordPolicyService } from '../services/password-policy.service';

/**
 * Validateur de contrainte pour les mots de passe forts
 * Utilise le service de politique de mots de passe pour valider les mots de passe
 */
@ValidatorConstraint({ name: 'strongPassword', async: true })
@Injectable()
export class StrongPasswordConstraint implements ValidatorConstraintInterface {
  constructor(private readonly passwordPolicyService: PasswordPolicyService) {}

  /**
   * Valider un mot de passe selon la politique configurée
   * @param password Mot de passe à valider
   * @param args Arguments de validation
   */
  async validate(password: string, args: ValidationArguments): Promise<boolean> {
    if (!password) {
      return false;
    }

    const result = await this.passwordPolicyService.validatePassword(password);
    
    // Stocker le résultat pour le message d'erreur
    (args.object as any).__passwordValidationResult = result;
    
    return result.isValid;
  }

  /**
   * Message d'erreur personnalisé
   * @param args Arguments de validation
   */
  defaultMessage(args: ValidationArguments): string {
    const result = (args.object as any).__passwordValidationResult;
    
    if (!result) {
      return 'Password does not meet security requirements';
    }
    
    if (result.breached) {
      return 'This password has been found in a data breach. Please choose a different password.';
    }
    
    if (result.feedback.warning) {
      return `${result.feedback.warning}. ${result.feedback.suggestions.join('. ')}`;
    }
    
    return result.feedback.suggestions.join('. ') || 'Password does not meet security requirements';
  }
}

/**
 * Décorateur pour valider les mots de passe forts
 * @param validationOptions Options de validation
 */
export function IsStrongPassword(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isStrongPassword',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: StrongPasswordConstraint,
    });
  };
}
