import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  HttpStatus,
  HttpCode,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { BookingsService } from './bookings.service';
import { CreateBookingDto } from './dto/create-booking.dto';
import { UpdateBookingDto } from './dto/update-booking.dto';
import { BookingResponseDto } from './dto/booking-response.dto';
import { UserRole } from '../auth/decorators/roles.decorator';

@ApiTags('bookings')
@Controller('bookings')
export class BookingsController {
  constructor(private readonly bookingsService: BookingsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new booking' })
  @ApiResponse({ status: 201, description: 'Booking created successfully', type: BookingResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createBookingDto: CreateBookingDto, @CurrentUser() user: any): Promise<BookingResponseDto> {
    return this.bookingsService.create(createBookingDto, user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all bookings' })
  @ApiResponse({ status: 200, description: 'Return all bookings', type: [BookingResponseDto] })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  async findAll(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: string,
    @Query('retreatId') retreatId?: string,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<{ data: BookingResponseDto[]; total: number; page: number; limit: number }> {
    return this.bookingsService.findAll({
      page,
      limit,
      status,
      retreatId,
      userId,
      startDate,
      endDate,
    });
  }

  @Get('user')
  @ApiOperation({ summary: 'Get bookings for the current user' })
  @ApiResponse({ status: 200, description: 'Return user bookings', type: [BookingResponseDto] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findUserBookings(
    @CurrentUser() user: any,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: string,
  ): Promise<{ data: BookingResponseDto[]; total: number; page: number; limit: number }> {
    return this.bookingsService.findUserBookings(user.id, {
      page,
      limit,
      status,
    });
  }

  @Get('host')
  @ApiOperation({ summary: 'Get bookings for retreats hosted by the current user' })
  @ApiResponse({ status: 200, description: 'Return host bookings', type: [BookingResponseDto] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findHostBookings(
    @CurrentUser() user: any,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: string,
    @Query('retreatId') retreatId?: string,
  ): Promise<{ data: BookingResponseDto[]; total: number; page: number; limit: number }> {
    return this.bookingsService.findHostBookings(user.id, {
      page,
      limit,
      status,
      retreatId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a booking by id' })
  @ApiResponse({ status: 200, description: 'Return a booking', type: BookingResponseDto })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async findOne(@Param('id') id: string, @CurrentUser() user: any): Promise<BookingResponseDto> {
    return this.bookingsService.findOne(id, user.id, user.role);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a booking' })
  @ApiResponse({ status: 200, description: 'Booking updated successfully', type: BookingResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async update(
    @Param('id') id: string,
    @Body() updateBookingDto: UpdateBookingDto,
    @CurrentUser() user: any,
  ): Promise<BookingResponseDto> {
    return this.bookingsService.update(id, updateBookingDto, user.id, user.role);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a booking' })
  @ApiResponse({ status: 204, description: 'Booking deleted successfully' })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string, @CurrentUser() user: any): Promise<void> {
    return this.bookingsService.remove(id, user.id, user.role);
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: 'Cancel a booking' })
  @ApiResponse({ status: 200, description: 'Booking cancelled successfully', type: BookingResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async cancelBooking(@Param('id') id: string, @CurrentUser() user: any): Promise<BookingResponseDto> {
    return this.bookingsService.cancelBooking(id, user.id, user.role);
  }

  @Post(':id/confirm')
  @ApiOperation({ summary: 'Confirm a booking' })
  @ApiResponse({ status: 200, description: 'Booking confirmed successfully', type: BookingResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Booking not found' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async confirmBooking(@Param('id') id: string, @CurrentUser() user: any): Promise<BookingResponseDto> {
    return this.bookingsService.confirmBooking(id, user.id, user.role);
  }
}
