import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsPositive,
  IsOptional,
  Min,
  Max,
  IsUUID,
} from 'class-validator';

export class CreateBookingDto {
  @ApiProperty({
    description: 'ID of the retreat to book',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  retreatId: string;

  @ApiProperty({
    description: 'Number of participants',
    example: 2,
    minimum: 1,
    maximum: 20,
  })
  @IsNumber()
  @IsPositive()
  @Min(1)
  @Max(20)
  participants: number;

  @ApiPropertyOptional({
    description: 'Special requests or notes for the booking',
    example: 'I have dietary restrictions (vegan). Please also arrange airport pickup if possible.',
  })
  @IsString()
  @IsOptional()
  specialRequests?: string;
}
