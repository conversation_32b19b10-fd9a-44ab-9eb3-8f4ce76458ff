import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async get<T>(key: string): Promise<T | null> {
    try {
      return await this.cacheManager.get<T>(key);
    } catch (error) {
      this.logger.error(`<PERSON>rror getting cache key ${key}: ${error.message}`, error.stack);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl);
    } catch (error) {
      this.logger.error(`Error setting cache key ${key}: ${error.message}`, error.stack);
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
    } catch (error) {
      this.logger.error(`Error deleting cache key ${key}: ${error.message}`, error.stack);
    }
  }

  async reset(): Promise<void> {
    try {
      // La méthode reset n'existe pas dans le type Cache standard
      // Nous devons utiliser une approche alternative
      // Comme nous ne pouvons pas accéder directement à toutes les clés, cette méthode est limitée
      this.logger.log('Cache reset requested - this is a placeholder implementation');
      // Dans une implémentation réelle, on pourrait utiliser Redis FLUSHDB ou une approche similaire
    } catch (error: any) {
      this.logger.error(`Error resetting cache: ${error.message}`, error.stack);
    }
  }
}
