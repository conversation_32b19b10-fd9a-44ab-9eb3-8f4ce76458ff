import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseBoolPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { CouponService } from '../services/coupon.service';
// import { PromotionService } from '../services/promotion.service';
import { CouponGenerationService } from '../services/coupon-generation.service';
import { CreateCouponDto } from '../dto/create-coupon.dto';
import { UpdateCouponDto } from '../dto/update-coupon.dto';
import { ApplyCouponDto } from '../dto/apply-coupon.dto';
import { GenerateBulkCouponsDto } from '../dto/generate-bulk-coupons.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';

@ApiTags('coupons')
@Controller('coupons')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CouponController {
  constructor(
    private readonly couponService: CouponService,

    private readonly couponGenerationService: CouponGenerationService,
  ) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer un nouveau coupon' })
  @ApiResponse({ status: 201, description: 'Le coupon a été créé avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  create(@Body() createCouponDto: CreateCouponDto) {
    return this.couponService.create(createCouponDto);
  }

  @Get()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer tous les coupons' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'includeInactive', required: false, type: Boolean, description: 'Inclure les coupons inactifs' })
  @ApiResponse({ status: 200, description: 'Liste des coupons récupérée avec succès.' })
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('includeInactive', ParseBoolPipe) includeInactive?: boolean,
  ) {
    return this.couponService.findAll(
      page ? parseInt(page.toString(), 10) : undefined,
      limit ? parseInt(limit.toString(), 10) : undefined,
      includeInactive,
    );
  }

  @Get('stats')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les statistiques des coupons' })
  @ApiResponse({ status: 200, description: 'Statistiques des coupons récupérées avec succès.' })
  getCouponStats() {
    return this.couponService.getCouponStats();
  }

  @Get('user')
  @ApiOperation({ summary: 'Récupérer les coupons de l\'utilisateur connecté' })
  @ApiResponse({ status: 200, description: 'Liste des coupons de l\'utilisateur récupérée avec succès.' })
  getUserCoupons(@CurrentUser('id') userId: string) {
    return this.couponService.getUserCoupons(userId);
  }

  @Get(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer un coupon par son ID' })
  @ApiResponse({ status: 200, description: 'Le coupon a été récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.couponService.findOne(id);
  }

  @Get('code/:code')
  @ApiOperation({ summary: 'Récupérer un coupon par son code' })
  @ApiResponse({ status: 200, description: 'Le coupon a été récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  findByCode(@Param('code') code: string) {
    return this.couponService.findByCode(code);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour un coupon' })
  @ApiResponse({ status: 200, description: 'Le coupon a été mis à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateCouponDto: UpdateCouponDto,
  ) {
    return this.couponService.update(id, updateCouponDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Supprimer un coupon' })
  @ApiResponse({ status: 200, description: 'Le coupon a été supprimé avec succès.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.couponService.remove(id);
  }

  @Patch(':id/activate')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Activer un coupon' })
  @ApiResponse({ status: 200, description: 'Le coupon a été activé avec succès.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  activate(@Param('id', ParseObjectIdPipe) id: string) {
    return this.couponService.activate(id);
  }

  @Patch(':id/deactivate')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Désactiver un coupon' })
  @ApiResponse({ status: 200, description: 'Le coupon a été désactivé avec succès.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  deactivate(@Param('id', ParseObjectIdPipe) id: string) {
    return this.couponService.deactivate(id);
  }

  @Post('apply')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Appliquer un coupon à une commande' })
  @ApiResponse({ status: 200, description: 'Le coupon a été appliqué avec succès.' })
  @ApiResponse({ status: 400, description: 'Coupon invalide ou données invalides.' })
  @ApiResponse({ status: 404, description: 'Coupon non trouvé.' })
  applyCoupon(@Body() applyCouponDto: ApplyCouponDto) {
    return this.couponService.applyCoupon(applyCouponDto);
  }

  @Post('bulk')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Générer des coupons en masse' })
  @ApiResponse({ status: 201, description: 'Les coupons ont été générés avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  generateBulkCoupons(@Body() generateBulkCouponsDto: GenerateBulkCouponsDto) {
    return this.couponGenerationService.generateBulkCoupons(
      generateBulkCouponsDto.template,
      generateBulkCouponsDto.count,
      generateBulkCouponsDto.prefix,
    );
  }

  @Post('welcome/:userId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Générer un coupon de bienvenue pour un utilisateur' })
  @ApiResponse({ status: 201, description: 'Le coupon de bienvenue a été généré avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  generateWelcomeCoupon(@Param('userId', ParseObjectIdPipe) userId: string) {
    return this.couponGenerationService.generateWelcomeCoupon(userId);
  }

  @Post('birthday/:userId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Générer un coupon d\'anniversaire pour un utilisateur' })
  @ApiResponse({ status: 201, description: 'Le coupon d\'anniversaire a été généré avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  generateBirthdayCoupon(
    @Param('userId', ParseObjectIdPipe) userId: string,
    @Body('userName') userName: string,
  ) {
    return this.couponGenerationService.generateBirthdayCoupon(userId, userName);
  }

  @Post('loyalty/:userId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Générer un coupon de fidélité pour un utilisateur' })
  @ApiResponse({ status: 201, description: 'Le coupon de fidélité a été généré avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  generateLoyaltyCoupon(
    @Param('userId', ParseObjectIdPipe) userId: string,
    @Body('tier') tier: string,
  ) {
    return this.couponGenerationService.generateLoyaltyCoupon(userId, tier);
  }

  @Get('promotions')
  @ApiOperation({ summary: 'Récupérer les promotions actives' })
  @ApiResponse({ status: 200, description: 'Liste des promotions récupérée avec succès.' })
  getActivePromotions() {
    return // TODO: Implémenter la récupération des promotions actives ou supprimer cette méthode si inutile.
    return [];
  }
}
