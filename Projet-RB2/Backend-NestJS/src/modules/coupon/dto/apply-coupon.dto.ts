import { Is<PERSON>tring, IsNotEmpty, IsNumber, IsUUID, IsArray, IsOptional, ValidateNested, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

class OrderItemDto {
  @ApiProperty({
    description: 'ID du produit',
    example: 'product-1',
  })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({
    description: 'Quantité',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({
    description: 'Prix unitaire',
    example: 49.99,
  })
  @IsNumber()
  @Min(0)
  unitPrice: number;

  @ApiPropertyOptional({
    description: 'Catégories du produit',
    type: [String],
    example: ['category-1', 'category-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  categories?: string[];
}

export class ApplyCouponDto {
  @ApiProperty({
    description: 'Code du coupon',
    example: 'SUMMER2023',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID de la commande',
    example: 'order-1',
  })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Montant de la commande',
    example: 99.99,
  })
  @IsNumber()
  @Min(0)
  orderAmount: number;

  @ApiPropertyOptional({
    description: 'Éléments de la commande',
    type: [OrderItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  @IsOptional()
  items?: OrderItemDto[];
}
