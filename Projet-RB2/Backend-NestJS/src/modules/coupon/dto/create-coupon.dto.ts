import {
  <PERSON>S<PERSON>,
  <PERSON><PERSON>num,
  IsNumber,
  IsDate,
  IsOptional,
  IsBoolean,
  IsArray,
  Min,
  Max,
  IsObject,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CouponType } from '../enums/coupon-type.enum';

export class CreateCouponDto {
  @ApiPropertyOptional({
    description: 'Code du coupon (généré automatiquement si non fourni)',
    example: 'SUMMER2023',
  })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiProperty({
    description: 'Type de coupon',
    enum: CouponType,
    example: CouponType.PERCENTAGE,
  })
  @IsEnum(CouponType)
  type: CouponType;

  @ApiProperty({
    description: 'Valeur du coupon (pourcentage ou montant fixe)',
    example: 10,
  })
  @IsNumber()
  @Min(0)
  @Max(100, {
    groups: [CouponType.PERCENTAGE],
    message: 'La valeur d\'un coupon en pourcentage ne peut pas dépasser 100%',
  })
  value: number;

  @ApiPropertyOptional({
    description: 'Montant minimum d\'achat pour appliquer le coupon',
    example: 50,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  minPurchaseAmount?: number;

  @ApiPropertyOptional({
    description: 'Montant maximum de la remise',
    example: 100,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  maxDiscountAmount?: number;

  @ApiPropertyOptional({
    description: 'Date de début de validité du coupon',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({
    description: 'Date de fin de validité du coupon',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional({
    description: 'Limite d\'utilisation du coupon',
    example: 100,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  usageLimit?: number;

  @ApiPropertyOptional({
    description: 'Limite d\'utilisation du coupon par utilisateur',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  perUserLimit?: number;

  @ApiPropertyOptional({
    description: 'Coupon actif',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Description du coupon',
    example: 'Coupon de réduction pour l\'été 2023',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Produits applicables',
    type: [String],
    example: ['product-1', 'product-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  applicableProducts?: string[];

  @ApiPropertyOptional({
    description: 'Produits exclus',
    type: [String],
    example: ['product-3'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  excludedProducts?: string[];

  @ApiPropertyOptional({
    description: 'Catégories applicables',
    type: [String],
    example: ['category-1', 'category-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  applicableCategories?: string[];

  @ApiPropertyOptional({
    description: 'Catégories exclues',
    type: [String],
    example: ['category-3'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  excludedCategories?: string[];

  @ApiPropertyOptional({
    description: 'Métadonnées du coupon',
    example: { campaign: 'summer-2023' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
