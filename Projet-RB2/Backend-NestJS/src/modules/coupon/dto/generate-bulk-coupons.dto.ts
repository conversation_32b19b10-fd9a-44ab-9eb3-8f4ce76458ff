import { <PERSON>N<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateCouponDto } from './create-coupon.dto';

export class GenerateBulkCouponsDto {
  @ApiProperty({
    description: 'Modèle de coupon',
    type: CreateCouponDto,
  })
  @ValidateNested()
  @Type(() => CreateCouponDto)
  template: CreateCouponDto;

  @ApiProperty({
    description: 'Nombre de coupons à générer',
    example: 100,
  })
  @IsNumber()
  @Min(1)
  @Max(1000)
  count: number;

  @ApiPropertyOptional({
    description: 'Préfixe des codes de coupon',
    example: 'SUMMER-',
  })
  @IsString()
  @IsOptional()
  prefix?: string;
}
