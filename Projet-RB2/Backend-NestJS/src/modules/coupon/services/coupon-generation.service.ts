import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CouponType } from '../enums/coupon-type.enum';
import { CreateCouponDto } from '../dto/create-coupon.dto';

@Injectable()
export class CouponGenerationService {
  private readonly logger = new Logger(CouponGenerationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Génère un code de coupon unique
   * @param length Longueur du code
   * @param prefix Préfixe du code
   * @returns Code de coupon
   */
  async generateCouponCode(length = 8, prefix = '') {
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code;
    let isUnique = false;
    
    while (!isUnique) {
      // Générer un code aléatoire
      code = prefix;
      for (let i = 0; i < length; i++) {
        code += characters.charAt(Math.floor(Math.random() * characters.length));
      }
      
      // Vérifier si le code existe déjà
      const existingCoupon = await this.prisma.coupon.findUnique({
        where: { code },
      });
      
      isUnique = !existingCoupon;
    }
    
    return code;
  }

  /**
   * Génère des coupons en masse
   * @param template Modèle de coupon
   * @param count Nombre de coupons à générer
   * @param prefix Préfixe des codes
   * @returns Liste des coupons générés
   */
  async generateBulkCoupons(template: CreateCouponDto, count: number, prefix = '') {
    try {
      this.logger.log(`Génération de ${count} coupons en masse`);
      
      const coupons = [];
      
      for (let i = 0; i < count; i++) {
        // Générer un code unique
        const code = await this.generateCouponCode(8, prefix);
        
        // Créer le coupon
        const coupon = await this.prisma.coupon.create({
          data: {
            code,
            type: template.type,
            value: template.value,
            minPurchaseAmount: template.minPurchaseAmount,
            maxDiscountAmount: template.maxDiscountAmount,
            startDate: template.startDate,
            endDate: template.endDate,
            usageLimit: template.usageLimit || 1, // Par défaut, usage unique
            perUserLimit: template.perUserLimit || 1, // Par défaut, usage unique par utilisateur
            isActive: template.isActive ?? true,
            description: template.description,
            applicableProducts: template.applicableProducts,
            excludedProducts: template.excludedProducts,
            applicableCategories: template.applicableCategories,
            excludedCategories: template.excludedCategories,
            metadata: {
              ...template.metadata,
              bulkGenerated: true,
              batchNumber: Date.now(),
            },
          },
        });
        
        coupons.push(coupon);
      }
      
      this.logger.log(`${coupons.length} coupons générés avec succès`);
      return coupons;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des coupons en masse: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un coupon de bienvenue pour un nouvel utilisateur
   * @param userId ID de l'utilisateur
   * @returns Coupon de bienvenue
   */
  async generateWelcomeCoupon(userId: string) {
    try {
      this.logger.log(`Génération d'un coupon de bienvenue pour l'utilisateur ${userId}`);
      
      // Générer un code unique
      const code = await this.generateCouponCode(8, 'WELCOME-');
      
      // Créer le coupon
      const coupon = await this.prisma.coupon.create({
        data: {
          code,
          type: CouponType.PERCENTAGE,
          value: 10, // 10% de réduction
          minPurchaseAmount: 0,
          maxDiscountAmount: 50, // Maximum 50€ de réduction
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Valable 30 jours
          usageLimit: 1,
          perUserLimit: 1,
          isActive: true,
          description: 'Coupon de bienvenue pour les nouveaux utilisateurs',
          metadata: {
            welcomeCoupon: true,
            userId,
          },
        },
      });
      
      this.logger.log(`Coupon de bienvenue généré avec succès: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du coupon de bienvenue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un coupon d'anniversaire pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param userName Nom de l'utilisateur
   * @returns Coupon d'anniversaire
   */
  async generateBirthdayCoupon(userId: string, userName: string) {
    try {
      this.logger.log(`Génération d'un coupon d'anniversaire pour l'utilisateur ${userId}`);
      
      // Générer un code unique
      const code = await this.generateCouponCode(8, 'BIRTHDAY-');
      
      // Créer le coupon
      const coupon = await this.prisma.coupon.create({
        data: {
          code,
          type: CouponType.PERCENTAGE,
          value: 15, // 15% de réduction
          minPurchaseAmount: 0,
          maxDiscountAmount: 100, // Maximum 100€ de réduction
          startDate: new Date(),
          endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Valable 7 jours
          usageLimit: 1,
          perUserLimit: 1,
          isActive: true,
          description: `Joyeux anniversaire ${userName} ! Profitez de cette offre spéciale.`,
          metadata: {
            birthdayCoupon: true,
            userId,
            year: new Date().getFullYear(),
          },
        },
      });
      
      this.logger.log(`Coupon d'anniversaire généré avec succès: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du coupon d'anniversaire: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un coupon de fidélité pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param tier Niveau de fidélité
   * @returns Coupon de fidélité
   */
  async generateLoyaltyCoupon(userId: string, tier: string) {
    try {
      this.logger.log(`Génération d'un coupon de fidélité pour l'utilisateur ${userId} (niveau ${tier})`);
      
      // Déterminer la valeur du coupon selon le niveau de fidélité
      let value = 5;
      let maxDiscountAmount = 50;
      
      switch (tier) {
        case 'SILVER':
          value = 10;
          maxDiscountAmount = 100;
          break;
        case 'GOLD':
          value = 15;
          maxDiscountAmount = 150;
          break;
        case 'PLATINUM':
          value = 20;
          maxDiscountAmount = 200;
          break;
        default:
          value = 5;
          maxDiscountAmount = 50;
      }
      
      // Générer un code unique
      const code = await this.generateCouponCode(8, `LOYALTY-${tier}-`);
      
      // Créer le coupon
      const coupon = await this.prisma.coupon.create({
        data: {
          code,
          type: CouponType.PERCENTAGE,
          value,
          minPurchaseAmount: 0,
          maxDiscountAmount,
          startDate: new Date(),
          endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // Valable 90 jours
          usageLimit: 1,
          perUserLimit: 1,
          isActive: true,
          description: `Coupon de fidélité pour les membres ${tier}`,
          metadata: {
            loyaltyCoupon: true,
            userId,
            tier,
          },
        },
      });
      
      this.logger.log(`Coupon de fidélité généré avec succès: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du coupon de fidélité: ${error.message}`);
      throw error;
    }
  }
}
