import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CouponStatus } from '../enums/coupon-status.enum';

@Injectable()
export class CouponValidationService {
  private readonly logger = new Logger(CouponValidationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Valide un coupon pour une utilisation
   * @param coupon Coupon à valider
   * @param userId ID de l'utilisateur
   * @param orderId ID de la commande
   * @param orderAmount Montant de la commande
   * @param items Éléments de la commande
   * @returns Résultat de la validation
   */
  async validateCoupon(
    coupon: any,
    userId: string,
    orderId: string,
    orderAmount: number,
    items: any[] = [],
  ) {
    try {
      // Vérifier si le coupon est actif
      if (!coupon.isActive) {
        return {
          isValid: false,
          message: 'Ce coupon n\'est pas actif',
          status: CouponStatus.INACTIVE,
        };
      }
      
      // Vérifier si le coupon est expiré
      if (coupon.endDate && new Date(coupon.endDate) < new Date()) {
        return {
          isValid: false,
          message: 'Ce coupon a expiré',
          status: CouponStatus.EXPIRED,
        };
      }
      
      // Vérifier si le coupon n'est pas encore valide
      if (coupon.startDate && new Date(coupon.startDate) > new Date()) {
        return {
          isValid: false,
          message: 'Ce coupon n\'est pas encore valide',
          status: CouponStatus.NOT_STARTED,
        };
      }
      
      // Vérifier si le coupon a atteint sa limite d'utilisation
      if (coupon.usageLimit) {
        const usageCount = await this.prisma.couponUsage.count({
          where: { couponId: coupon.id },
        });
        
        if (usageCount >= coupon.usageLimit) {
          return {
            isValid: false,
            message: 'Ce coupon a atteint sa limite d\'utilisation',
            status: CouponStatus.USAGE_LIMIT_REACHED,
          };
        }
      }
      
      // Vérifier si l'utilisateur a atteint sa limite d'utilisation
      if (coupon.perUserLimit) {
        const userUsageCount = await this.prisma.couponUsage.count({
          where: {
            couponId: coupon.id,
            userId,
          },
        });
        
        if (userUsageCount >= coupon.perUserLimit) {
          return {
            isValid: false,
            message: 'Vous avez atteint la limite d\'utilisation de ce coupon',
            status: CouponStatus.USER_LIMIT_REACHED,
          };
        }
      }
      
      // Vérifier si le montant minimum d'achat est atteint
      if (coupon.minPurchaseAmount && orderAmount < coupon.minPurchaseAmount) {
        return {
          isValid: false,
          message: `Le montant minimum d'achat pour ce coupon est de ${coupon.minPurchaseAmount}`,
          status: CouponStatus.MIN_PURCHASE_NOT_MET,
        };
      }
      
      // Vérifier si le coupon est applicable aux produits de la commande
      if (coupon.applicableProducts && coupon.applicableProducts.length > 0) {
        const productIds = items.map(item => item.productId);
        const hasApplicableProduct = productIds.some(productId => 
          coupon.applicableProducts.includes(productId)
        );
        
        if (!hasApplicableProduct) {
          return {
            isValid: false,
            message: 'Ce coupon n\'est pas applicable aux produits de votre commande',
            status: CouponStatus.PRODUCTS_NOT_APPLICABLE,
          };
        }
      }
      
      // Vérifier si le coupon exclut des produits de la commande
      if (coupon.excludedProducts && coupon.excludedProducts.length > 0) {
        const productIds = items.map(item => item.productId);
        const hasExcludedProduct = productIds.some(productId => 
          coupon.excludedProducts.includes(productId)
        );
        
        if (hasExcludedProduct) {
          return {
            isValid: false,
            message: 'Ce coupon n\'est pas applicable à certains produits de votre commande',
            status: CouponStatus.PRODUCTS_EXCLUDED,
          };
        }
      }
      
      // Vérifier si le coupon est applicable aux catégories de la commande
      if (coupon.applicableCategories && coupon.applicableCategories.length > 0) {
        const categories = items.flatMap(item => item.categories || []);
        const hasApplicableCategory = categories.some(category => 
          coupon.applicableCategories.includes(category)
        );
        
        if (!hasApplicableCategory) {
          return {
            isValid: false,
            message: 'Ce coupon n\'est pas applicable aux catégories de produits de votre commande',
            status: CouponStatus.CATEGORIES_NOT_APPLICABLE,
          };
        }
      }
      
      // Vérifier si le coupon exclut des catégories de la commande
      if (coupon.excludedCategories && coupon.excludedCategories.length > 0) {
        const categories = items.flatMap(item => item.categories || []);
        const hasExcludedCategory = categories.some(category => 
          coupon.excludedCategories.includes(category)
        );
        
        if (hasExcludedCategory) {
          return {
            isValid: false,
            message: 'Ce coupon n\'est pas applicable à certaines catégories de produits de votre commande',
            status: CouponStatus.CATEGORIES_EXCLUDED,
          };
        }
      }
      
      // Vérifier si le coupon a déjà été utilisé pour cette commande
      const existingUsage = await this.prisma.couponUsage.findFirst({
        where: {
          couponId: coupon.id,
          orderId,
        },
      });
      
      if (existingUsage) {
        return {
          isValid: false,
          message: 'Ce coupon a déjà été utilisé pour cette commande',
          status: CouponStatus.ALREADY_USED,
        };
      }
      
      // Le coupon est valide
      return {
        isValid: true,
        message: 'Coupon valide',
        status: CouponStatus.VALID,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la validation du coupon: ${error.message}`);
      
      return {
        isValid: false,
        message: 'Une erreur est survenue lors de la validation du coupon',
        status: CouponStatus.ERROR,
      };
    }
  }
}
