import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const readFile = promisify(fs.readFile);

@Injectable()
export class FileSecurityService {
  private readonly logger = new Logger(FileSecurityService.name);
  private readonly allowedExtensions: string[];
  private readonly maxFileSize: number;
  private readonly hashAlgorithm: string;

  constructor(private readonly configService: ConfigService) {
    this.allowedExtensions = this.configService.get<string[]>('files.allowedExtensions', [
      '.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.csv'
    ]);
    this.maxFileSize = this.configService.get<number>('files.maxSizeBytes', 10 * 1024 * 1024); // 10MB par défaut
    this.hashAlgorithm = this.configService.get<string>('files.hashAlgorithm', 'sha256');
  }

  /**
   * Vérifie si un fichier est sécurisé
   * @param file Fichier à vérifier
   * @returns Résultat de la vérification
   */
  async validateFile(file: Express.Multer.File): Promise<{ isValid: boolean; reason?: string }> {
    try {
      // Vérifier l'extension
      const fileExtension = path.extname(file.originalname).toLowerCase();
      if (!this.allowedExtensions.includes(fileExtension)) {
        return { isValid: false, reason: `Extension de fichier non autorisée: ${fileExtension}` };
      }

      // Vérifier la taille
      if (file.size > this.maxFileSize) {
        return {
          isValid: false,
          reason: `Taille de fichier dépassée: ${file.size} octets (max: ${this.maxFileSize} octets)`
        };
      }

      // Vérifier le type MIME
      if (!this.isValidMimeType(file.mimetype, fileExtension)) {
        return { isValid: false, reason: `Type MIME invalide: ${file.mimetype}` };
      }

      // Vérifier le contenu du fichier (analyse de base)
      const isSafe = await this.scanFileContent(file);
      if (!isSafe) {
        return { isValid: false, reason: 'Le contenu du fichier semble malveillant' };
      }

      return { isValid: true };
    } catch (error) {
      this.logger.error(`Erreur lors de la validation du fichier: ${error.message}`, error.stack);
      return { isValid: false, reason: `Erreur de validation: ${error.message}` };
    }
  }

  /**
   * Génère un hash pour un fichier
   * @param file Fichier à hacher
   * @returns Hash du fichier
   */
  async generateFileHash(file: Express.Multer.File): Promise<string> {
    try {
      const fileBuffer = file.buffer || await readFile(file.path);
      const hash = crypto.createHash(this.hashAlgorithm);
      hash.update(fileBuffer);
      return hash.digest('hex');
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du hash: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Vérifie l'intégrité d'un fichier en comparant son hash
   * @param filePath Chemin du fichier
   * @param expectedHash Hash attendu
   * @returns Résultat de la vérification
   */
  async verifyFileIntegrity(filePath: string, expectedHash?: string): Promise<boolean> {
    try {
      // Vérifier que le fichier existe
      if (!fs.existsSync(filePath)) {
        this.logger.warn(`Fichier non trouvé: ${filePath}`);
        return false;
      }

      // Vérifier les permissions du fichier
      const stats = fs.statSync(filePath);
      const isReadable = (stats.mode & fs.constants.R_OK) !== 0;
      if (!isReadable) {
        this.logger.warn(`Fichier non lisible: ${filePath}`);
        return false;
      }

      // Si un hash est fourni, le vérifier
      if (expectedHash) {
        const fileBuffer = await readFile(filePath);
        const hash = crypto.createHash(this.hashAlgorithm);
        hash.update(fileBuffer);
        const actualHash = hash.digest('hex');

        if (actualHash !== expectedHash) {
          this.logger.warn(`Hash incorrect pour le fichier ${filePath}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification de l'intégrité: ${error.message}`, error.stack);
      return false;
    }
  }



  /**
   * Vérifie si un type MIME est valide pour une extension donnée
   * @param mimeType Type MIME
   * @param extension Extension de fichier
   * @returns Résultat de la vérification
   */
  private isValidMimeType(mimeType: string, extension: string): boolean {
    const mimeMap: Record<string, string[]> = {
      '.jpg': ['image/jpeg'],
      '.jpeg': ['image/jpeg'],
      '.png': ['image/png'],
      '.gif': ['image/gif'],
      '.pdf': ['application/pdf'],
      '.doc': ['application/msword'],
      '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      '.xls': ['application/vnd.ms-excel'],
      '.xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
      '.ppt': ['application/vnd.ms-powerpoint'],
      '.pptx': ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
      '.txt': ['text/plain'],
      '.csv': ['text/csv', 'application/csv'],
    };

    return mimeMap[extension]?.includes(mimeType) || false;
  }

  /**
   * Analyse le contenu d'un fichier pour détecter des menaces
   * @param file Fichier à analyser
   * @returns Résultat de l'analyse
   */
  private async scanFileContent(file: Express.Multer.File): Promise<boolean> {
    // Implémentation basique - dans un environnement de production,
    // il faudrait utiliser un service d'analyse antivirus plus robuste
    try {
      const fileBuffer = file.buffer || await readFile(file.path);

      // Vérifier les signatures de virus connues (très basique)
      const suspiciousPatterns = [
        Buffer.from('X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*'), // Signature EICAR
        Buffer.from('<script>'), // Potentiel XSS
        Buffer.from('eval('), // Potentiel code malveillant
      ];

      for (const pattern of suspiciousPatterns) {
        if (fileBuffer.includes(pattern)) {
          this.logger.warn(`Motif suspect détecté dans le fichier: ${file.originalname}`);
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de l'analyse du contenu: ${error.message}`, error.stack);
      return false;
    }
  }
}
