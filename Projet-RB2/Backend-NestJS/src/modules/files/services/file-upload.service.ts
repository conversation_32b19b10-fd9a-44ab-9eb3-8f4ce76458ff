import { Injectable, Logger, BadRequestException, UnauthorizedException, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { FileSecurityService } from '../../security/services/file-security.service';
import { SecurityEventService } from '../../security/services/security-event.service';
import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import { join, extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);
  private readonly uploadDir: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly fileSecurityService: FileSecurityService,
    private readonly securityEventService: SecurityEventService,
  ) {
    this.uploadDir = this.configService.get<string>('app.uploadDir', 'uploads');
    this.ensureUploadDirExists();
  }

  /**
   * Ensure the upload directory exists
   */
  private async ensureUploadDirExists(): Promise<void> {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true });
      this.logger.log(`Upload directory created: ${this.uploadDir}`);
    } catch (error) {
      this.logger.error(`Failed to create upload directory: ${error.message}`);
      throw new Error('Failed to create upload directory');
    }
  }

  /**
   * Upload a file
   * @param file File to upload
   * @param userId User ID
   * @param category File category
   * @returns Uploaded file information
   */
  async uploadFile(
    file: Express.Multer.File,
    userId: string,
    category: string,
    ipAddress?: string,
  ) {
    try {
      if (!file) {
        throw new BadRequestException('No file provided');
      }

      // Validate file security
      const validationResult = await this.fileSecurityService.validateFile(file);
      if (!validationResult.valid) {
        // Log security event for invalid file
        await this.securityEventService.logSecurityEvent({
          eventType: 'INVALID_FILE_UPLOAD',
          severity: 'WARNING',
          source: 'FILE_UPLOAD',
          details: {
            filename: file.originalname,
            reason: validationResult.reason,
            userId,
            category,
            ipAddress,
          },
        });

        throw new HttpException(validationResult.reason || 'Invalid file', HttpStatus.BAD_REQUEST);
      }

      // Scan file for malware
      const scanResult = await this.fileSecurityService.scanFileForMalware(file);
      if (!scanResult.clean) {
        // Log security event for malware detection
        await this.securityEventService.logSecurityEvent({
          eventType: 'MALWARE_DETECTED',
          severity: 'ERROR',
          source: 'FILE_UPLOAD',
          details: {
            filename: file.originalname,
            reason: scanResult.reason,
            userId,
            category,
            ipAddress,
          },
        });

        throw new HttpException(
          'File contains malware or suspicious content',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generate a unique filename
      const fileHash = this.generateFileHash(file.buffer);
      const fileExt = extname(file.originalname);
      const fileName = `${uuidv4()}-${fileHash}${fileExt}`;

      // Create subdirectory for the category
      const categoryDir = join(this.uploadDir, category);
      await fs.mkdir(categoryDir, { recursive: true });

      // Save the file securely
      const filePath = join(categoryDir, fileName);
      await fs.writeFile(filePath, file.buffer);

      // Calculate file hash for integrity verification
      const integrityHash = this.generateFileHash(file.buffer);

      // Create a file record in the database
      const fileRecord = await this.prisma.file.create({
        data: {
          name: file.originalname,
          path: filePath,
          mimeType: file.mimetype,
          size: file.size,
          category,
          userId,
          hash: integrityHash,
        },
      });

      // Log successful upload
      await this.securityEventService.logSecurityEvent({
        eventType: 'FILE_UPLOAD_SUCCESS',
        severity: 'INFO',
        source: 'FILE_UPLOAD',
        details: {
          fileId: fileRecord.id,
          filename: file.originalname,
          category,
          userId,
          ipAddress,
        },
      });

      this.logger.log(`File uploaded securely: ${filePath}`);
      return fileRecord;
    } catch (error) {
      this.logger.error(`File upload failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Upload multiple files
   * @param files Files to upload
   * @param userId User ID
   * @param category File category
   * @returns Uploaded files information
   */
  async uploadFiles(
    files: Express.Multer.File[],
    userId: string,
    category: string,
  ) {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided');
    }

    const uploadPromises = files.map(file => this.uploadFile(file, userId, category));
    return Promise.all(uploadPromises);
  }

  /**
   * Generate a hash for a file
   * @param buffer File buffer
   * @returns File hash
   */
  private generateFileHash(buffer: Buffer): string {
    return createHash('sha256').update(buffer).digest('hex').substring(0, 16);
  }

  /**
   * Get file by ID
   * @param id File ID
   * @returns File information
   */
  async getFileById(id: string, userId?: string) {
    const file = await this.prisma.file.findUnique({
      where: { id },
    });

    if (file) {
      // Verify file integrity if possible
      try {
        const isIntact = await this.fileSecurityService.verifyFileIntegrity(file.path);
        if (!isIntact) {
          // Log integrity failure
          await this.securityEventService.logSecurityEvent({
            eventType: 'FILE_INTEGRITY_FAILURE',
            severity: 'ERROR',
            source: 'FILE_UPLOAD',
            details: {
              fileId: file.id,
              filename: file.name,
              userId,
            },
          });

          this.logger.warn(`File integrity check failed for file ${id}`);
          // We still return the file, but it's marked as potentially compromised
          return { ...file, integrityCompromised: true };
        }
      } catch (error) {
        this.logger.error(`Error verifying file integrity: ${error.message}`, error.stack);
      }
    }

    return file;
  }

  /**
   * Get files by user ID and category
   * @param userId User ID
   * @param category File category
   * @returns Files information
   */
  async getFilesByUserAndCategory(userId: string, category: string) {
    return this.prisma.file.findMany({
      where: {
        userId,
        category,
      },
    });
  }

  /**
   * Delete file by ID
   * @param id File ID
   * @returns Deleted file information
   */
  async deleteFile(id: string, userId?: string) {
    const file = await this.prisma.file.findUnique({
      where: { id },
    });

    if (!file) {
      throw new BadRequestException('File not found');
    }

    // Check if the user has permission to delete this file
    if (userId && file.userId !== userId) {
      // Log unauthorized deletion attempt
      await this.securityEventService.logSecurityEvent({
        eventType: 'UNAUTHORIZED_FILE_DELETE_ATTEMPT',
        severity: 'WARNING',
        source: 'FILE_UPLOAD',
        details: {
          fileId: file.id,
          filename: file.name,
          fileOwnerId: file.userId,
          attemptedByUserId: userId,
        },
      });

      throw new UnauthorizedException('You do not have permission to delete this file');
    }

    try {
      // Delete the file from the filesystem
      await fs.unlink(file.path);

      // Log file deletion
      await this.securityEventService.logSecurityEvent({
        eventType: 'FILE_DELETED',
        severity: 'INFO',
        source: 'FILE_UPLOAD',
        details: {
          fileId: file.id,
          filename: file.name,
          userId,
        },
      });

      // Delete the file record from the database
      return this.prisma.file.delete({
        where: { id },
      });
    } catch (error) {
      this.logger.error(`Failed to delete file: ${error.message}`, error.stack);
      throw error;
    }
  }
}
