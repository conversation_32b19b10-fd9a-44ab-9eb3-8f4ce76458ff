import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
// Import Prisma-generated types
import { SocialContentType as ContentType, Prisma } from '../../../../prisma/generated/app';

// Définir notre propre enum MonetizationType puisqu'il n'existe pas dans Prisma
enum MonetizationType {
  PREMIUM = 'PREMIUM',
  SUBSCRIPTION = 'SUBSCRIPTION',
  ONE_TIME = 'ONE_TIME',
  DONATION = 'DONATION'
}

@Injectable()
export class MonetizationService {
  private readonly logger = new Logger(MonetizationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Vérifie que l'utilisateur est le propriétaire du contenu
   */
  async verifyContentOwnership(
    userId: string,
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
  ): Promise<boolean> {
    try {
      let count = 0;

      switch (contentType) {
        case 'video':
          count = await this.prisma.video.count({
            where: {
              id: { in: contentIds },
              authorId: userId,
            },
          });
          break;
        case 'post':
          count = await this.prisma.post.count({
            where: {
              id: { in: contentIds },
              authorId: userId,
            },
          });
          break;
        case 'livestream':
          count = await this.prisma.livestream.count({
            where: {
              id: { in: contentIds },
              hostId: userId,
            },
          });
          break;
      }

      return count === contentIds.length;
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification de la propriété du contenu: ${error.message}`);
      return false;
    }
  }

  /**
   * Vérifie que l'utilisateur a suffisamment de solde
   */
  async verifyUserBalance(userId: string, amount: number): Promise<boolean> {
    try {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId },
        select: { balance: true },
      });

      return wallet && wallet.balance >= amount;
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification du solde: ${error.message}`);
      return false;
    }
  }

  /**
   * Monétiser du contenu
   */
  async monetizeContent(
    userId: string,
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
    monetizationType: 'premium' | 'subscription' | 'oneTime' | 'donation',
    price?: number,
    currency?: string,
    description?: string,
  ) {
    try {
      // Mapper le type de contenu à l'enum Prisma
      const mappedContentType = this.mapContentType(contentType);
      const mappedMonetizationType = this.mapMonetizationType(monetizationType);

      // Créer des configurations de monétisation pour chaque contenu
      const monetizationPromises = contentIds.map(contentId =>
        this.prisma.contentMonetization.create({
          data: {
            contentId,
            contentType: mappedContentType,
            type: mappedMonetizationType,
            price: price || 0,
            currency: currency || 'EUR',
            description: description || '',
            status: 'ACTIVE',
            createdBy: userId,
          },
        }),
      );

      const results = await this.prisma.$transaction(monetizationPromises);

      return {
        success: true,
        transactionIds: results.map((r: { id: string }) => r.id),
        monetizationIds: results.map((r: { id: string }) => r.id),
        message: 'Contenu monétisé avec succès',
        status: 'active',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la monétisation du contenu: ${error.message}`);
      return {
        success: false,
        message: `Erreur lors de la monétisation du contenu: ${error.message}`,
        status: 'rejected',
      };
    }
  }

  /**
   * Convertir en contenu premium
   */
  async convertToPremiumContent(
    userId: string,
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
    price: number,
  ) {
    try {
      // Mapper le type de contenu à l'enum Prisma
      const mappedContentType = this.mapContentType(contentType);

      // Désactiver toute monétisation existante
      await this.prisma.contentMonetization.updateMany({
        where: {
          contentId: { in: contentIds },
          contentType: mappedContentType,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      // Créer des configurations de monétisation premium
      const monetizationPromises = contentIds.map(contentId =>
        this.prisma.contentMonetization.create({
          data: {
            contentId,
            contentType: mappedContentType,
            type: 'PREMIUM',
            price,
            currency: 'EUR',
            status: 'ACTIVE',
            createdBy: userId,
          },
        }),
      );

      const results = await this.prisma.$transaction(monetizationPromises);

      // Mettre à jour les métadonnées du contenu pour indiquer qu'il est premium
      switch (contentType) {
        case 'video':
          await this.prisma.video.updateMany({
            where: { id: { in: contentIds } },
            data: { isPremium: true },
          });
          break;
        case 'post':
          await this.prisma.post.updateMany({
            where: { id: { in: contentIds } },
            data: { isPremium: true },
          });
          break;
        case 'livestream':
          await this.prisma.livestream.updateMany({
            where: { id: { in: contentIds } },
            data: { isPremium: true },
          });
          break;
      }

      return {
        success: true,
        monetizationIds: results.map((r: { id: string }) => r.id),
        message: 'Contenu converti en premium avec succès',
        status: 'active',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la conversion en contenu premium: ${error.message}`);
      return {
        success: false,
        message: `Erreur lors de la conversion en contenu premium: ${error.message}`,
        status: 'rejected',
      };
    }
  }

  /**
   * Configurer les dons pour du contenu
   */
  async setupDonations(
    userId: string,
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
    settings: {
      minAmount?: number;
      suggestedAmounts?: number[];
      thanksMessage?: string;
      goals?: { amount: number; description: string }[];
    },
  ) {
    try {
      // Mapper le type de contenu à l'enum Prisma
      const mappedContentType = this.mapContentType(contentType);

      // Désactiver toute configuration de don existante
      await this.prisma.donationSettings.updateMany({
        where: {
          contentId: { in: contentIds },
          contentType: mappedContentType,
        },
        data: {
          isActive: false,
        },
      });

      // Créer des configurations de dons pour chaque contenu
      const donationPromises = contentIds.map(contentId =>
        this.prisma.donationSettings.create({
          data: {
            contentId,
            contentType: mappedContentType,
            minAmount: settings.minAmount || 1,
            suggestedAmounts: settings.suggestedAmounts || [2, 5, 10, 20],
            thanksMessage: settings.thanksMessage || 'Merci pour votre soutien !',
            goals: settings.goals ? JSON.stringify(settings.goals) : null,
            isActive: true,
            createdBy: userId,
          },
        }),
      );

      const results = await this.prisma.$transaction(donationPromises);

      // Créer également des entrées dans ContentMonetization pour le tracking
      const monetizationPromises = contentIds.map(contentId =>
        this.prisma.contentMonetization.create({
          data: {
            contentId,
            contentType: mappedContentType,
            type: 'DONATION',
            status: 'ACTIVE',
            createdBy: userId,
          },
        }),
      );

      const monetizationResults = await this.prisma.$transaction(monetizationPromises);

      return {
        success: true,
        transactionIds: results.map((r: { id: string }) => r.id),
        monetizationIds: monetizationResults.map((r: { id: string }) => r.id),
        message: 'Configuration des dons réussie',
        status: 'active',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la configuration des dons: ${error.message}`);
      return {
        success: false,
        message: `Erreur lors de la configuration des dons: ${error.message}`,
        status: 'rejected',
      };
    }
  }

  /**
   * Promouvoir du contenu
   */
  async promoteContent(
    userId: string,
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
    budget: number,
    duration: number,
  ) {
    try {
      // Mapper le type de contenu à l'enum Prisma
      const mappedContentType = this.mapContentType(contentType);

      // Calculer la date de fin
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + duration);

      // Débiter le compte de l'utilisateur
      await this.prisma.wallet.update({
        where: { userId },
        data: {
          balance: {
            decrement: budget,
          },
        },
      });

      // Créer des promotions pour chaque contenu
      const promotionPromises = contentIds.map(contentId =>
        this.prisma.contentPromotion.create({
          data: {
            contentId,
            contentType: mappedContentType,
            budget,
            startDate: new Date(),
            endDate,
            status: 'ACTIVE',
            createdBy: userId,
          },
        }),
      );

      const results = await this.prisma.$transaction(promotionPromises);

      // Créer une transaction pour chaque promotion
      const transactionPromises = results.map((promotion: { contentId: string; id: string }) =>
        this.prisma.transaction.create({
          data: {
            userId,
            amount: -budget / contentIds.length, // Diviser le budget équitablement
            currency: 'EUR',
            type: 'PROMOTION',
            status: 'COMPLETED',
            description: `Promotion for ${contentType} ${promotion.contentId}`,
            referenceId: promotion.id,
          },
        }),
      );

      const transactionResults = await this.prisma.$transaction(transactionPromises);

      return {
        success: true,
        transactionIds: transactionResults.map((t: { id: string }) => t.id),
        monetizationIds: results.map((r: { id: string }) => r.id),
        message: 'Promotion configurée avec succès',
        status: 'active',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la promotion du contenu: ${error.message}`);
      return {
        success: false,
        message: `Erreur lors de la promotion du contenu: ${error.message}`,
        status: 'rejected',
      };
    }
  }

  /**
   * Obtenir les statistiques de revenus pour du contenu
   */
  async getContentRevenueStats(
    userId: string,
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
  ) {
    try {
      // Mapper le type de contenu à l'enum Prisma
      const mappedContentType = this.mapContentType(contentType);

      // Récupérer toutes les transactions liées au contenu
      const transactions = await this.prisma.transaction.findMany({
        where: {
          contentId: { in: contentIds },
          contentType: mappedContentType,
          recipientId: userId,
          status: 'COMPLETED',
        },
      });

      // Grouper les transactions par contenu
      const transactionsByContent: Record<string, any[]> = {};
      transactions.forEach((transaction: any) => {
        const contentId = transaction.contentId;
        if (!transactionsByContent[contentId]) {
          transactionsByContent[contentId] = [];
        }
        transactionsByContent[contentId].push(transaction);
      });

      // Calculer les statistiques pour chaque contenu
      const statsResults = contentIds.map(contentId => {
        const contentTransactions = transactionsByContent[contentId] || [];
        const totalRevenue = contentTransactions.reduce(
          (sum: number, t: any) => sum + (t.amount > 0 ? t.amount : 0),
          0,
        );

        // Regrouper par type de transaction
        const bySource: Record<string, number> = {};
        contentTransactions.forEach((t: any) => {
          const type = t.type.toLowerCase();
          if (!bySource[type]) {
            bySource[type] = 0;
          }
          bySource[type] += t.amount > 0 ? t.amount : 0;
        });

        return {
          contentId,
          totalRevenue,
          currency: 'EUR', // Ou utiliser une devise dynamique si nécessaire
          transactionCount: contentTransactions.length,
          lastUpdated: new Date().toISOString(),
          bySource,
        };
      });

      return statsResults;
    } catch (error) {
      this.logger.error(
        `Erreur lors de la récupération des statistiques de revenus: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Obtenir le statut de monétisation pour du contenu
   */
  async getMonetizationStatus(
    contentIds: string[],
    contentType: 'video' | 'post' | 'livestream',
  ) {
    try {
      // Mapper le type de contenu à l'enum Prisma
      const mappedContentType = this.mapContentType(contentType);

      // Récupérer les configurations de monétisation actives
      const monetizationSettings = await this.prisma.contentMonetization.findMany({
        where: {
          contentId: { in: contentIds },
          contentType: mappedContentType,
          status: 'ACTIVE',
        },
      });

      // Grouper par contentId
      const result = {};
      contentIds.forEach(id => {
        result[id] = {
          isMonetized: false,
          status: 'pending',
        };
      });

      monetizationSettings.forEach((setting: any) => {
        const type = this.mapMonetizationTypeReverse(setting.type);
        result[setting.contentId] = {
          isMonetized: true,
          type,
          price: setting.price,
          status: setting.status.toLowerCase(),
        };
      });

      return result;
    } catch (error) {
      this.logger.error(
        `Erreur lors de la récupération du statut de monétisation: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Mapper le type de contenu à l'enum Prisma
   */
  private mapContentType(contentType: string): ContentType {
    switch (contentType) {
      case 'video':
        return ContentType.VIDEO;
      case 'post':
        // POST n'existe pas dans SocialContentType, utiliser BLOG comme alternative
        return ContentType.BLOG;
      case 'livestream':
        return ContentType.LIVESTREAM;
      default:
        throw new Error(`Type de contenu non pris en charge: ${contentType}`);
    }
  }

  /**
   * Mapper le type de monétisation à l'enum Prisma
   */
  private mapMonetizationType(type: string): MonetizationType {
    switch (type) {
      case 'premium':
        return MonetizationType.PREMIUM;
      case 'subscription':
        return MonetizationType.SUBSCRIPTION;
      case 'oneTime':
        return MonetizationType.ONE_TIME;
      case 'donation':
        return MonetizationType.DONATION;
      default:
        throw new Error(`Type de monétisation non pris en charge: ${type}`);
    }
  }

  /**
   * Mapper l'enum Prisma au type de monétisation
   */
  private mapMonetizationTypeReverse(type: MonetizationType): string {
    switch (type) {
      case MonetizationType.PREMIUM:
        return 'premium';
      case MonetizationType.SUBSCRIPTION:
        return 'subscription';
      case MonetizationType.ONE_TIME:
        return 'oneTime';
      case MonetizationType.DONATION:
        return 'donation';
      default:
        return 'unknown';
    }
  }
}