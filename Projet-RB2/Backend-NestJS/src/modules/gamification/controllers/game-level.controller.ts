import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { GameLevelService } from '../services/game-level.service';
import { CreateGameLevelDto } from '../dto/create-game-level.dto';
import { UpdateGameLevelDto } from '../dto/update-game-level.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { PaginationPipe } from '../../../common/pipes/pagination.pipe';
import { PaginationOptions } from '../../../shared/interfaces';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { Cacheable } from '../../../common/decorators';

@ApiTags('game-levels')
@Controller('game-systems/:systemId/levels')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class GameLevelController {
  constructor(private readonly gameLevelService: GameLevelService) {}

  @Post()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer un nouveau niveau de jeu' })
  @ApiResponse({ status: 201, description: 'Le niveau de jeu a été créé avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  @ApiResponse({ status: 409, description: 'Un niveau avec ce numéro existe déjà pour ce système de jeu.' })
  create(
    @Param('systemId', ParseObjectIdPipe) systemId: string,
    @Body() createGameLevelDto: CreateGameLevelDto,
  ) {
    // Assurer que le systemId du DTO correspond au systemId de l'URL
    createGameLevelDto.systemId = systemId;
    return this.gameLevelService.create(createGameLevelDto);
  }

  @Get()
  @Cacheable(60) // Cache pour 60 secondes
  @ApiOperation({ summary: 'Récupérer tous les niveaux d\'un système de jeu' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Champ de tri' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordre de tri' })
  @ApiResponse({ status: 200, description: 'Liste des niveaux de jeu récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Système de jeu non trouvé.' })
  findAll(
    @Param('systemId', ParseObjectIdPipe) systemId: string,
    @Query(new PaginationPipe()) paginationOptions: PaginationOptions,
  ) {
    return this.gameLevelService.findAll(systemId, paginationOptions);
  }

  @Get(':id')
  @Cacheable(30) // Cache pour 30 secondes
  @ApiOperation({ summary: 'Récupérer un niveau de jeu par son ID' })
  @ApiResponse({ status: 200, description: 'Le niveau de jeu a été récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Niveau de jeu non trouvé.' })
  findOne(@Param('id', ParseObjectIdPipe) id: string) {
    return this.gameLevelService.findOne(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour un niveau de jeu' })
  @ApiResponse({ status: 200, description: 'Le niveau de jeu a été mis à jour avec succès.' })
  @ApiResponse({ status: 400, description: 'Données invalides.' })
  @ApiResponse({ status: 404, description: 'Niveau de jeu non trouvé.' })
  @ApiResponse({ status: 409, description: 'Un niveau avec ce numéro existe déjà pour ce système de jeu.' })
  update(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateGameLevelDto: UpdateGameLevelDto,
  ) {
    return this.gameLevelService.update(id, updateGameLevelDto);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Supprimer un niveau de jeu' })
  @ApiResponse({ status: 204, description: 'Le niveau de jeu a été supprimé avec succès.' })
  @ApiResponse({ status: 404, description: 'Niveau de jeu non trouvé.' })
  remove(@Param('id', ParseObjectIdPipe) id: string) {
    return this.gameLevelService.remove(id);
  }
}
