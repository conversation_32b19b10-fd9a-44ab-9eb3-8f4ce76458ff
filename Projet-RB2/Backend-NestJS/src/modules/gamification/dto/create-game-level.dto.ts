import { IsString, <PERSON>NotEmpty, IsObject, <PERSON><PERSON>nt, Min, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateGameLevelDto {
  @ApiProperty({
    description: 'ID du système de jeu',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  systemId: string;

  @ApiProperty({
    description: 'Numéro du niveau',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  level: number;

  @ApiProperty({
    description: 'Nom du niveau',
    example: 'Débutant',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @ApiProperty({
    description: 'Description du niveau',
    example: 'Niveau débutant pour les nouveaux participants',
    minLength: 10,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  description: string;

  @ApiProperty({
    description: 'Points d\'expérience requis pour atteindre ce niveau',
    example: 100,
    minimum: 0,
  })
  @IsInt()
  @Min(0)
  xpRequired: number;

  @ApiProperty({
    description: 'Récompenses du niveau au format JSON',
    example: {
      badge: 'debutant',
      points: 50,
      unlocks: ['feature1', 'feature2'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  rewards: any;
}
