import { IsString, <PERSON>NotEmpty, <PERSON>O<PERSON>, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateGameSystemDto {
  @ApiProperty({
    description: 'Nom du système de jeu',
    example: 'Système de récompenses pour retraites',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Description du système de jeu',
    example: 'Système de gamification pour encourager la participation aux retraites',
    minLength: 10,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  description: string;

  @ApiProperty({
    description: 'Règles du système de jeu au format JSON',
    example: {
      pointsPerActivity: 10,
      levelsRequired: 5,
      badgesEnabled: true,
      leaderboardEnabled: true,
    },
  })
  @IsObject()
  @IsNotEmpty()
  rules: any;
}
