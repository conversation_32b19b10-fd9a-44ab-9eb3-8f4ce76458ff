import { IsString, <PERSON>NotEmpty, IsObject, Is<PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>UUID, IsIn } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { STATUS } from '../../../shared/constants';

export class CreateQuestDto {
  @ApiProperty({
    description: 'ID du système de jeu',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  systemId: string;

  @ApiProperty({
    description: 'Nom de la quête',
    example: 'Première méditation',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Description de la quête',
    example: 'Complétez votre première session de méditation guidée',
    minLength: 10,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  description: string;

  @ApiProperty({
    description: 'Objectifs de la quête au format JSON',
    example: {
      type: 'meditation',
      count: 1,
      duration: 10,
      requirements: ['audio', 'quiet_space'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  objectives: any;

  @ApiProperty({
    description: 'Récompenses de la quête au format JSON',
    example: {
      xp: 50,
      badge: 'first_meditation',
      unlocks: ['guided_meditation_level_2'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  rewards: any;

  @ApiPropertyOptional({
    description: 'Statut de la quête',
    example: 'DRAFT',
    enum: Object.values(STATUS),
    default: 'DRAFT',
  })
  @IsOptional()
  @IsString()
  @IsIn(Object.values(STATUS))
  status?: string;
}
