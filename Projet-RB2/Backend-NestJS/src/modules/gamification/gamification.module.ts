import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { GameSystemService } from './services/game-system.service';
import { GameLevelService } from './services/game-level.service';
import { QuestService } from './services/quest.service';
import { PlayerProgressService } from './services/player-progress.service';
import { GameSystemController } from './controllers/game-system.controller';
import { GameLevelController } from './controllers/game-level.controller';
import { QuestController } from './controllers/quest.controller';
import { PlayerProgressController } from './controllers/player-progress.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [
    GameSystemController,
    GameLevelController,
    QuestController,
    PlayerProgressController,
  ],
  providers: [
    GameSystemService,
    GameLevelService,
    QuestService,
    PlayerProgressService,
  ],
  exports: [
    GameSystemService,
    GameLevelService,
    QuestService,
    PlayerProgressService,
  ],
})
export class GamificationModule {}
