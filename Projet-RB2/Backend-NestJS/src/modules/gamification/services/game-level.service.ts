import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateGameLevelDto } from '../dto/create-game-level.dto';
import { UpdateGameLevelDto } from '../dto/update-game-level.dto';
// import { any } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';

@Injectable()
export class GameLevelService {
  private readonly logger = new Logger(GameLevelService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createanyDto: CreateGameLevelDto): Promise<any> {
    try {
      // Vérifier si le système de jeu existe
      const gameSystem = await this.prisma.gameSystem.findUnique({
        where: { id: createanyDto.systemId },
      });

      if (!gameSystem) {
        throw new NotFoundException(`Système de jeu avec l'ID ${createanyDto.systemId} non trouvé`);
      }

      // Vérifier si un niveau avec le même numéro existe déjà pour ce système
      const existingLevel = await this.prisma.gameLevel.findFirst({
        where: {
          systemId: createanyDto.systemId,
          level: createanyDto.level,
        },
      });

      if (existingLevel) {
        throw new ConflictException(`Un niveau ${createanyDto.level} existe déjà pour ce système de jeu`);
      }

      // Créer le niveau
      const gameLevel = await this.prisma.gameLevel.create({
        data: {
          systemId: createanyDto.systemId,
          level: createanyDto.level,
          name: createanyDto.name,
          description: createanyDto.description,
          xpRequired: createanyDto.xpRequired,
          rewards: createanyDto.rewards,
        },
      });

      this.logger.log(`Niveau de jeu créé avec succès: ${gameLevel.id}`);
      return gameLevel;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du niveau de jeu: ${error.message}`);
      throw error;
    }
  }

  async findAll(systemId: string, options?: PaginationOptions): Promise<{ 
    gameLevels: any[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'level';
    const sortOrder = options?.sortOrder || 'asc';

    // Vérifier si le système de jeu existe
    const gameSystem = await this.prisma.gameSystem.findUnique({
      where: { id: systemId },
    });

    if (!gameSystem) {
      throw new NotFoundException(`Système de jeu avec l'ID ${systemId} non trouvé`);
    }

    const [gameLevels, total] = await Promise.all([
      this.prisma.gameLevel.findMany({
        where: { systemId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.gameLevel.count({
        where: { systemId },
      }),
    ]);

    return {
      gameLevels,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<any> {
    const gameLevel = await this.prisma.gameLevel.findUnique({
      where: { id },
      include: {
        system: true,
        players: true,
      },
    });

    if (!gameLevel) {
      throw new NotFoundException(`Niveau de jeu avec l'ID ${id} non trouvé`);
    }

    return gameLevel;
  }

  async update(id: string, updateanyDto: UpdateGameLevelDto): Promise<any> {
    // Vérifier si le niveau de jeu existe
    const existingLevel = await this.findOne(id);

    // Vérifier si le niveau mis à jour entre en conflit avec un autre niveau
    if (updateanyDto.level !== undefined) {
      const conflictingLevel = await this.prisma.gameLevel.findFirst({
        where: {
          systemId: existingLevel.systemId,
          level: updateanyDto.level,
          id: { not: id },
        },
      });

      if (conflictingLevel) {
        throw new ConflictException(`Un niveau ${updateanyDto.level} existe déjà pour ce système de jeu`);
      }
    }

    try {
      const updatedany = await this.prisma.gameLevel.update({
        where: { id },
        data: updateanyDto,
      });

      this.logger.log(`Niveau de jeu mis à jour avec succès: ${id}`);
      return updatedany;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du niveau de jeu: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si le niveau de jeu existe
    await this.findOne(id);

    try {
      // Supprimer le niveau de jeu et ses relations (progression des joueurs)
      await this.prisma.$transaction([
        this.prisma.playerProgress.deleteMany({
          where: { levelId: id },
        }),
        this.prisma.gameLevel.delete({
          where: { id },
        }),
      ]);

      this.logger.log(`Niveau de jeu supprimé avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du niveau de jeu: ${error.message}`);
      throw error;
    }
  }
}
