import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateGameSystemDto } from '../dto/create-game-system.dto';
import { UpdateGameSystemDto } from '../dto/update-game-system.dto';
// import { any } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';

@Injectable()
export class GameSystemService {
  private readonly logger = new Logger(GameSystemService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createanyDto: CreateGameSystemDto): Promise<any> {
    try {
      // Vérifier si un système de jeu avec le même nom existe déjà
      const existingSystem = await this.prisma.gameSystem.findFirst({
        where: { name: createanyDto.name },
      });

      if (existingSystem) {
        throw new ConflictException(`Un système de jeu avec le nom "${createanyDto.name}" existe déjà`);
      }

      // Créer le système de jeu
      const gameSystem = await this.prisma.gameSystem.create({
        data: {
          name: createanyDto.name,
          description: createanyDto.description,
          rules: createanyDto.rules,
        },
      });

      this.logger.log(`Système de jeu créé avec succès: ${gameSystem.id}`);
      return gameSystem;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du système de jeu: ${error.message}`);
      throw error;
    }
  }

  async findAll(options?: PaginationOptions): Promise<{ 
    gameSystems: any[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [gameSystems, total] = await Promise.all([
      this.prisma.gameSystem.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.gameSystem.count(),
    ]);

    return {
      gameSystems,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<any> {
    const gameSystem = await this.prisma.gameSystem.findUnique({
      where: { id },
      include: {
        levels: true,
        quests: true,
      },
    });

    if (!gameSystem) {
      throw new NotFoundException(`Système de jeu avec l'ID ${id} non trouvé`);
    }

    return gameSystem;
  }

  async update(id: string, updateanyDto: UpdateGameSystemDto): Promise<any> {
    // Vérifier si le système de jeu existe
    await this.findOne(id);

    // Vérifier si le nouveau nom est déjà utilisé par un autre système de jeu
    if (updateanyDto.name) {
      const existingSystem = await this.prisma.gameSystem.findFirst({
        where: {
          name: updateanyDto.name,
          id: { not: id },
        },
      });

      if (existingSystem) {
        throw new ConflictException(`Un système de jeu avec le nom "${updateanyDto.name}" existe déjà`);
      }
    }

    try {
      const updatedany = await this.prisma.gameSystem.update({
        where: { id },
        data: updateanyDto,
      });

      this.logger.log(`Système de jeu mis à jour avec succès: ${id}`);
      return updatedany;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du système de jeu: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si le système de jeu existe
    await this.findOne(id);

    try {
      // Supprimer le système de jeu et ses relations (niveaux, quêtes)
      await this.prisma.$transaction([
        this.prisma.playerProgress.deleteMany({
          where: {
            level: {
              systemId: id,
            },
          },
        }),
        this.prisma.gameLevel.deleteMany({
          where: { systemId: id },
        }),
        this.prisma.quest.deleteMany({
          where: { systemId: id },
        }),
        this.prisma.gameSystem.delete({
          where: { id },
        }),
      ]);

      this.logger.log(`Système de jeu supprimé avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du système de jeu: ${error.message}`);
      throw error;
    }
  }
}
