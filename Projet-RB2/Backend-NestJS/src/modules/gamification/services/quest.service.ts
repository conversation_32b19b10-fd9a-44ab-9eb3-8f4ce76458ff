import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateQuestDto } from '../dto/create-quest.dto';
import { UpdateQuestDto } from '../dto/update-quest.dto';
// import { any } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';
import { STATUS } from '../../../shared/constants';

@Injectable()
export class QuestService {
  private readonly logger = new Logger(QuestService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createanyDto: CreateQuestDto): Promise<any> {
    try {
      // Vérifier si le système de jeu existe
      const gameSystem = await this.prisma.gameSystem.findUnique({
        where: { id: createanyDto.systemId },
      });

      if (!gameSystem) {
        throw new NotFoundException(`Système de jeu avec l'ID ${createanyDto.systemId} non trouvé`);
      }

      // Vérifier si une quête avec le même nom existe déjà pour ce système
      const existingany = await this.prisma.quest.findFirst({
        where: {
          systemId: createanyDto.systemId,
          name: createanyDto.name,
        },
      });

      if (existingany) {
        throw new ConflictException(`Une quête avec le nom "${createanyDto.name}" existe déjà pour ce système de jeu`);
      }

      // Créer la quête
      const quest = await this.prisma.quest.create({
        data: {
          systemId: createanyDto.systemId,
          name: createanyDto.name,
          description: createanyDto.description,
          objectives: createanyDto.objectives,
          rewards: createanyDto.rewards,
          status: createanyDto.status || STATUS.DRAFT,
        },
      });

      this.logger.log(`Quête créée avec succès: ${quest.id}`);
      return quest;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la quête: ${error.message}`);
      throw error;
    }
  }

  async findAll(systemId: string, options?: PaginationOptions): Promise<{ 
    quests: any[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    // Vérifier si le système de jeu existe
    const gameSystem = await this.prisma.gameSystem.findUnique({
      where: { id: systemId },
    });

    if (!gameSystem) {
      throw new NotFoundException(`Système de jeu avec l'ID ${systemId} non trouvé`);
    }

    const [quests, total] = await Promise.all([
      this.prisma.quest.findMany({
        where: { systemId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.quest.count({
        where: { systemId },
      }),
    ]);

    return {
      quests,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<any> {
    const quest = await this.prisma.quest.findUnique({
      where: { id },
      include: {
        system: true,
      },
    });

    if (!quest) {
      throw new NotFoundException(`Quête avec l'ID ${id} non trouvée`);
    }

    return quest;
  }

  async update(id: string, updateanyDto: UpdateQuestDto): Promise<any> {
    // Vérifier si la quête existe
    const existingany = await this.findOne(id);

    // Vérifier si la quête mise à jour entre en conflit avec une autre quête
    if (updateanyDto.name) {
      const conflictingany = await this.prisma.quest.findFirst({
        where: {
          systemId: existingany.systemId,
          name: updateanyDto.name,
          id: { not: id },
        },
      });

      if (conflictingany) {
        throw new ConflictException(`Une quête avec le nom "${updateanyDto.name}" existe déjà pour ce système de jeu`);
      }
    }

    try {
      const updatedany = await this.prisma.quest.update({
        where: { id },
        data: updateanyDto,
      });

      this.logger.log(`Quête mise à jour avec succès: ${id}`);
      return updatedany;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la quête: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si la quête existe
    await this.findOne(id);

    try {
      await this.prisma.quest.delete({
        where: { id },
      });

      this.logger.log(`Quête supprimée avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de la quête: ${error.message}`);
      throw error;
    }
  }

  async updateStatus(id: string, status: string): Promise<any> {
    // Vérifier si la quête existe
    await this.findOne(id);

    // Vérifier si le statut est valide
    const validStatuses = Object.values(STATUS);
    if (!validStatuses.includes(status as any)) {
      throw new ConflictException(`Statut invalide. Les statuts valides sont: ${validStatuses.join(', ')}`);
    }

    try {
      const updatedany = await this.prisma.quest.update({
        where: { id },
        data: { status },
      });

      this.logger.log(`Statut de la quête mis à jour avec succès: ${id} -> ${status}`);
      return updatedany;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du statut de la quête: ${error.message}`);
      throw error;
    }
  }
}
