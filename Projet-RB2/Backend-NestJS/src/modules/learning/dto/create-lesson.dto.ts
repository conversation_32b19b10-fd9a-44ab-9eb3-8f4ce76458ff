import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>nt, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUrl, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateLessonDto {
  @ApiProperty({
    description: 'ID du cours',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  courseId: string;

  @ApiProperty({
    description: 'Titre de la leçon',
    example: 'Introduction à la respiration consciente',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  title: string;

  @ApiPropertyOptional({
    description: 'Contenu de la leçon',
    example: 'Dans cette leçon, nous allons explorer les techniques de respiration consciente...',
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({
    description: 'URL de la vidéo',
    example: 'https://example.com/videos/breathing-techniques.mp4',
  })
  @IsOptional()
  @IsString()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'Ordre de la leçon dans le cours',
    example: 1,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  order: number;
}
