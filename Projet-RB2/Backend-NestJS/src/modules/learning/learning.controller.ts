import { <PERSON>, Get, Post, Body, Param, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { LearningService } from './learning.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';

@ApiTags('Learning')
@Controller('learning')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LearningController {
  private readonly logger = new Logger(LearningController.name);

  constructor(private readonly learningService: LearningService) {}

  @Get('dashboard')
  @ApiOperation({ summary: 'Obtenir le tableau de bord d\'apprentissage' })
  @ApiResponse({ status: 200, description: 'Tableau de bord récupéré avec succès' })
  async getDashboard(@GetUser('id') userId: string) {
    this.logger.log(`Getting learning dashboard for user ${userId}`);
    return await this.learningService.getLearningDashboard(userId);
  }

  @Get('progress')
  @ApiOperation({ summary: 'Obtenir la progression globale' })
  @ApiResponse({ status: 200, description: 'Progression récupérée avec succès' })
  async getProgress(@GetUser('id') userId: string) {
    this.logger.log(`Getting progress for user ${userId}`);
    return await this.learningService.getOverallProgress(userId);
  }

  @Get('recommendations')
  @ApiOperation({ summary: 'Obtenir des cours recommandés' })
  @ApiResponse({ status: 200, description: 'Recommandations récupérées avec succès' })
  async getRecommendations(@GetUser('id') userId: string) {
    this.logger.log(`Getting recommendations for user ${userId}`);
    return await this.learningService.getRecommendedCourses(userId);
  }

  @Post('lessons/:lessonId/complete')
  @ApiOperation({ summary: 'Marquer une leçon comme terminée' })
  @ApiResponse({ status: 200, description: 'Leçon marquée comme terminée' })
  async completeLesson(
    @GetUser('id') userId: string,
    @Param('lessonId') lessonId: string,
  ) {
    this.logger.log(`User ${userId} completing lesson ${lessonId}`);
    return await this.learningService.completeLesson(userId, lessonId);
  }
}
