import { Module } from '@nestjs/common';
import { CourseService } from './services/course.service';
import { LessonService } from './services/lesson.service';
import { EnrollmentService } from './services/enrollment.service';
import { CourseController } from './controllers/course.controller';
import { LessonController } from './controllers/lesson.controller';
import { EnrollmentController } from './controllers/enrollment.controller';
import { PrismaModule } from '../../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [
    CourseController,
    LessonController,
    EnrollmentController,
  ],
  providers: [
    CourseService,
    LessonService,
    EnrollmentService,
  ],
  exports: [
    CourseService,
    LessonService,
    EnrollmentService,
  ],
})
export class LearningModule {}
