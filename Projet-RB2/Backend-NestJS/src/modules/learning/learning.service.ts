import { Injectable, Logger } from '@nestjs/common';
import { CourseService } from './services/course.service';
import { LessonService } from './services/lesson.service';
import { EnrollmentService } from './services/enrollment.service';

/**
 * Service principal du module d'apprentissage
 */
@Injectable()
export class LearningService {
  private readonly logger = new Logger(LearningService.name);

  constructor(
    private readonly courseService: CourseService,
    private readonly lessonService: LessonService,
    private readonly enrollmentService: EnrollmentService,
  ) {}

  /**
   * Obtenir le tableau de bord d'apprentissage d'un utilisateur
   */
  async getLearningDashboard(userId: string) {
    try {
      const [enrollments, progress] = await Promise.all([
        this.enrollmentService.getUserEnrollments(userId),
        this.getOverallProgress(userId),
      ]);

      return {
        enrollments,
        progress,
        recommendations: await this.getRecommendedCourses(userId),
      };
    } catch (error) {
      this.logger.error(`Failed to get learning dashboard for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir la progression globale d'un utilisateur
   */
  async getOverallProgress(userId: string) {
    try {
      const enrollments = await this.enrollmentService.getUserEnrollments(userId);
      
      if (enrollments.length === 0) {
        return { overall: 0, completed: 0, inProgress: 0 };
      }

      const totalProgress = enrollments.reduce((sum, enrollment) => sum + enrollment.progress, 0);
      const averageProgress = totalProgress / enrollments.length;
      
      const completed = enrollments.filter(e => e.progress >= 100).length;
      const inProgress = enrollments.filter(e => e.progress > 0 && e.progress < 100).length;

      return {
        overall: Math.round(averageProgress),
        completed,
        inProgress,
        total: enrollments.length,
      };
    } catch (error) {
      this.logger.error(`Failed to get overall progress for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir des cours recommandés pour un utilisateur
   */
  async getRecommendedCourses(userId: string) {
    try {
      // Logique de recommandation basée sur les intérêts et la progression
      const userEnrollments = await this.enrollmentService.getUserEnrollments(userId);
      const completedCourses = userEnrollments.filter(e => e.progress >= 100);
      
      // Pour l'instant, retourner les cours populaires
      return await this.courseService.getPopularCourses(5);
    } catch (error) {
      this.logger.error(`Failed to get recommended courses for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Marquer une leçon comme terminée
   */
  async completeLesson(userId: string, lessonId: string) {
    try {
      this.logger.log(`User ${userId} completed lesson ${lessonId}`);
      
      // Mettre à jour la progression de l'inscription
      await this.enrollmentService.updateProgress(userId, lessonId);
      
      return { success: true, message: 'Leçon terminée avec succès' };
    } catch (error) {
      this.logger.error(`Failed to complete lesson ${lessonId} for user ${userId}:`, error);
      throw error;
    }
  }
}
