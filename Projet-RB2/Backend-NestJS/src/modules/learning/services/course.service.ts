import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateCourseDto } from '../dto/create-course.dto';
import { UpdateCourseDto } from '../dto/update-course.dto';
import { Course } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';

@Injectable()
export class CourseService {
  private readonly logger = new Logger(CourseService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createCourseDto: CreateCourseDto): Promise<Course> {
    try {
      // Vérifier si un cours avec le même titre existe déjà
      const existingCourse = await this.prisma.course.findFirst({
        where: { title: createCourseDto.title },
      });

      if (existingCourse) {
        throw new ConflictException(`Un cours avec le titre "${createCourseDto.title}" existe déjà`);
      }

      // Créer le cours
      const course = await this.prisma.course.create({
        data: {
          title: createCourseDto.title,
          description: createCourseDto.description,
          level: createCourseDto.level,
          coverImage: createCourseDto.coverImage,
          category: createCourseDto.category,
        },
      });

      this.logger.log(`Cours créé avec succès: ${course.id}`);
      return course;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du cours: ${error.message}`);
      throw error;
    }
  }

  async findAll(options?: PaginationOptions): Promise<{ 
    courses: Course[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [courses, total] = await Promise.all([
      this.prisma.course.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.course.count(),
    ]);

    return {
      courses,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Course> {
    const course = await this.prisma.course.findUnique({
      where: { id },
      include: {
        lessons: {
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    if (!course) {
      throw new NotFoundException(`Cours avec l'ID ${id} non trouvé`);
    }

    return course;
  }

  async update(id: string, updateCourseDto: UpdateCourseDto): Promise<Course> {
    // Vérifier si le cours existe
    await this.findOne(id);

    // Vérifier si le nouveau titre est déjà utilisé par un autre cours
    if (updateCourseDto.title) {
      const existingCourse = await this.prisma.course.findFirst({
        where: {
          title: updateCourseDto.title,
          id: { not: id },
        },
      });

      if (existingCourse) {
        throw new ConflictException(`Un cours avec le titre "${updateCourseDto.title}" existe déjà`);
      }
    }

    try {
      const updatedCourse = await this.prisma.course.update({
        where: { id },
        data: updateCourseDto,
      });

      this.logger.log(`Cours mis à jour avec succès: ${id}`);
      return updatedCourse;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du cours: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si le cours existe
    await this.findOne(id);

    try {
      // Supprimer le cours et ses relations (leçons, inscriptions)
      await this.prisma.$transaction([
        this.prisma.lesson.deleteMany({
          where: { courseId: id },
        }),
        this.prisma.enrollment.deleteMany({
          where: { courseId: id },
        }),
        this.prisma.course.delete({
          where: { id },
        }),
      ]);

      this.logger.log(`Cours supprimé avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du cours: ${error.message}`);
      throw error;
    }
  }

  async findByCategory(category: string, options?: PaginationOptions): Promise<{ 
    courses: Course[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [courses, total] = await Promise.all([
      this.prisma.course.findMany({
        where: { category },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.course.count({
        where: { category },
      }),
    ]);

    return {
      courses,
      total,
      page,
      limit,
    };
  }

  async findByLevel(level: string, options?: PaginationOptions): Promise<{ 
    courses: Course[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [courses, total] = await Promise.all([
      this.prisma.course.findMany({
        where: { level },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.course.count({
        where: { level },
      }),
    ]);

    return {
      courses,
      total,
      page,
      limit,
    };
  }

  async search(query: string, options?: PaginationOptions): Promise<{ 
    courses: Course[]; 
    total: number; 
    page: number; 
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [courses, total] = await Promise.all([
      this.prisma.course.findMany({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { category: { contains: query, mode: 'insensitive' } },
          ],
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.course.count({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { category: { contains: query, mode: 'insensitive' } },
          ],
        },
      }),
    ]);

    return {
      courses,
      total,
      page,
      limit,
    };
  }
}
