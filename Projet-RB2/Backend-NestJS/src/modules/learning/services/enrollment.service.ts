import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateEnrollmentDto } from '../dto/create-enrollment.dto';
import { UpdateEnrollmentDto } from '../dto/update-enrollment.dto';
import { Enrollment } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';

@Injectable()
export class EnrollmentService {
  private readonly logger = new Logger(EnrollmentService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createEnrollmentDto: CreateEnrollmentDto): Promise<Enrollment> {
    try {
      // Vérifier si le cours existe
      const course = await this.prisma.course.findUnique({
        where: { id: createEnrollmentDto.courseId },
      });

      if (!course) {
        throw new NotFoundException(`Cours avec l'ID ${createEnrollmentDto.courseId} non trouvé`);
      }

      // Vérifier si l'utilisateur existe
      const user = await this.prisma.user.findUnique({
        where: { id: createEnrollmentDto.userId },
      });

      if (!user) {
        throw new NotFoundException(`Utilisateur avec l'ID ${createEnrollmentDto.userId} non trouvé`);
      }

      // Vérifier si l'utilisateur est déjà inscrit au cours
      const existingEnrollment = await this.prisma.enrollment.findUnique({
        where: {
          userId_courseId: {
            userId: createEnrollmentDto.userId,
            courseId: createEnrollmentDto.courseId,
          },
        },
      });

      if (existingEnrollment) {
        throw new ConflictException(`L'utilisateur est déjà inscrit à ce cours`);
      }

      // Créer l'inscription
      const enrollment = await this.prisma.enrollment.create({
        data: {
          userId: createEnrollmentDto.userId,
          courseId: createEnrollmentDto.courseId,
          progress: createEnrollmentDto.progress || 0,
        },
      });

      this.logger.log(`Inscription créée avec succès: ${enrollment.id}`);
      return enrollment;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'inscription: ${error.message}`);
      throw error;
    }
  }

  async findAll(options?: PaginationOptions): Promise<{
    enrollments: Enrollment[];
    total: number;
    page: number;
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    const [enrollments, total] = await Promise.all([
      this.prisma.enrollment.findMany({
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          course: true,
        },
      }),
      this.prisma.enrollment.count(),
    ]);

    return {
      enrollments,
      total,
      page,
      limit,
    };
  }

  async findByUser(userId: string, options?: PaginationOptions): Promise<{
    enrollments: Enrollment[];
    total: number;
    page: number;
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    // Vérifier si l'utilisateur existe
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`Utilisateur avec l'ID ${userId} non trouvé`);
    }

    const [enrollments, total] = await Promise.all([
      this.prisma.enrollment.findMany({
        where: { userId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          course: true,
        },
      }),
      this.prisma.enrollment.count({
        where: { userId },
      }),
    ]);

    return {
      enrollments,
      total,
      page,
      limit,
    };
  }

  async findByCourse(courseId: string, options?: PaginationOptions): Promise<{
    enrollments: Enrollment[];
    total: number;
    page: number;
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'createdAt';
    const sortOrder = options?.sortOrder || 'desc';

    // Vérifier si le cours existe
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new NotFoundException(`Cours avec l'ID ${courseId} non trouvé`);
    }

    const [enrollments, total] = await Promise.all([
      this.prisma.enrollment.findMany({
        where: { courseId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.enrollment.count({
        where: { courseId },
      }),
    ]);

    return {
      enrollments,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Enrollment> {
    const enrollment = await this.prisma.enrollment.findUnique({
      where: { id },
      include: {
        course: true,
      },
    });

    if (!enrollment) {
      throw new NotFoundException(`Inscription avec l'ID ${id} non trouvée`);
    }

    return enrollment;
  }

  async update(id: string, updateEnrollmentDto: UpdateEnrollmentDto): Promise<Enrollment> {
    // Vérifier si l'inscription existe
    await this.findOne(id);

    try {
      const updatedEnrollment = await this.prisma.enrollment.update({
        where: { id },
        data: updateEnrollmentDto,
      });

      this.logger.log(`Inscription mise à jour avec succès: ${id}`);
      return updatedEnrollment;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de l'inscription: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si l'inscription existe
    await this.findOne(id);

    try {
      await this.prisma.enrollment.delete({
        where: { id },
      });

      this.logger.log(`Inscription supprimée avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de l'inscription: ${error.message}`);
      throw error;
    }
  }

  async updateProgress(id: string, progress: number): Promise<Enrollment> {
    // Vérifier si l'inscription existe
    await this.findOne(id);

    // Vérifier si la progression est valide
    if (progress < 0 || progress > 100) {
      throw new ConflictException('La progression doit être comprise entre 0 et 100');
    }

    try {
      const updatedEnrollment = await this.prisma.enrollment.update({
        where: { id },
        data: {
          progress,
          completedAt: progress === 100 ? new Date() : null,
        },
      });

      this.logger.log(`Progression de l'inscription mise à jour avec succès: ${id} -> ${progress}%`);
      return updatedEnrollment;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la progression de l'inscription: ${error.message}`);
      throw error;
    }
  }

  async getEnrollmentStats(courseId: string): Promise<{
    totalEnrollments: number;
    completedEnrollments: number;
    averageProgress: number;
    completionRate: number;
  }> {
    // Vérifier si le cours existe
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new NotFoundException(`Cours avec l'ID ${courseId} non trouvé`);
    }

    // Récupérer les statistiques
    const enrollments = await this.prisma.enrollment.findMany({
      where: { courseId },
      select: {
        progress: true,
        completedAt: true,
      },
    });

    const totalEnrollments = enrollments.length;
    const completedEnrollments = enrollments.filter((e: any) => e.completedAt !== null).length;
    const totalProgress = enrollments.reduce((sum: number, e: any) => sum + e.progress, 0);
    const averageProgress = totalEnrollments > 0 ? totalProgress / totalEnrollments : 0;
    const completionRate = totalEnrollments > 0 ? (completedEnrollments / totalEnrollments) * 100 : 0;

    return {
      totalEnrollments,
      completedEnrollments,
      averageProgress,
      completionRate,
    };
  }
}
