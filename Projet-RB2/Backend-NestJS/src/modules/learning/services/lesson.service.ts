import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreateLessonDto } from '../dto/create-lesson.dto';
import { UpdateLessonDto } from '../dto/update-lesson.dto';
import { Lesson } from '@prisma/client';
import { PaginationOptions } from '../../../shared/interfaces';

@Injectable()
export class LessonService {
  private readonly logger = new Logger(LessonService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createLessonDto: CreateLessonDto): Promise<Lesson> {
    try {
      // Vérifier si le cours existe
      const course = await this.prisma.course.findUnique({
        where: { id: createLessonDto.courseId },
      });

      if (!course) {
        throw new NotFoundException(`Cours avec l'ID ${createLessonDto.courseId} non trouvé`);
      }

      // Vérifier si une leçon avec le même ordre existe déjà pour ce cours
      const existingLesson = await this.prisma.lesson.findFirst({
        where: {
          courseId: createLessonDto.courseId,
          order: createLessonDto.order,
        },
      });

      if (existingLesson) {
        throw new ConflictException(`Une leçon avec l'ordre ${createLessonDto.order} existe déjà pour ce cours`);
      }

      // Créer la leçon
      const lesson = await this.prisma.lesson.create({
        data: {
          courseId: createLessonDto.courseId,
          title: createLessonDto.title,
          content: createLessonDto.content,
          videoUrl: createLessonDto.videoUrl,
          order: createLessonDto.order,
        },
      });

      this.logger.log(`Leçon créée avec succès: ${lesson.id}`);
      return lesson;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la leçon: ${error.message}`);
      throw error;
    }
  }

  async findAll(courseId: string, options?: PaginationOptions): Promise<{
    lessons: Lesson[];
    total: number;
    page: number;
    limit: number;
  }> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    const sortBy = options?.sortBy || 'order';
    const sortOrder = options?.sortOrder || 'asc';

    // Vérifier si le cours existe
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new NotFoundException(`Cours avec l'ID ${courseId} non trouvé`);
    }

    const [lessons, total] = await Promise.all([
      this.prisma.lesson.findMany({
        where: { courseId },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      this.prisma.lesson.count({
        where: { courseId },
      }),
    ]);

    return {
      lessons,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<Lesson> {
    const lesson = await this.prisma.lesson.findUnique({
      where: { id },
      include: {
        course: true,
      },
    });

    if (!lesson) {
      throw new NotFoundException(`Leçon avec l'ID ${id} non trouvée`);
    }

    return lesson;
  }

  async update(id: string, updateLessonDto: UpdateLessonDto): Promise<Lesson> {
    // Vérifier si la leçon existe
    const existingLesson = await this.findOne(id);

    // Vérifier si l'ordre mis à jour entre en conflit avec une autre leçon
    if (updateLessonDto.order !== undefined && updateLessonDto.order !== existingLesson.order) {
      const conflictingLesson = await this.prisma.lesson.findFirst({
        where: {
          courseId: existingLesson.courseId,
          order: updateLessonDto.order,
          id: { not: id },
        },
      });

      if (conflictingLesson) {
        throw new ConflictException(`Une leçon avec l'ordre ${updateLessonDto.order} existe déjà pour ce cours`);
      }
    }

    try {
      const updatedLesson = await this.prisma.lesson.update({
        where: { id },
        data: updateLessonDto,
      });

      this.logger.log(`Leçon mise à jour avec succès: ${id}`);
      return updatedLesson;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la leçon: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si la leçon existe
    await this.findOne(id);

    try {
      await this.prisma.lesson.delete({
        where: { id },
      });

      this.logger.log(`Leçon supprimée avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de la leçon: ${error.message}`);
      throw error;
    }
  }

  async reorderLessons(courseId: string, lessonIds: string[]): Promise<Lesson[]> {
    // Vérifier si le cours existe
    const course = await this.prisma.course.findUnique({
      where: { id: courseId },
    });

    if (!course) {
      throw new NotFoundException(`Cours avec l'ID ${courseId} non trouvé`);
    }

    // Vérifier si tous les IDs de leçon appartiennent au cours
    const lessons = await this.prisma.lesson.findMany({
      where: { courseId },
    });

    const lessonIdSet = new Set(lessons.map((lesson: any) => lesson.id));
    const invalidLessonIds = lessonIds.filter(id => !lessonIdSet.has(id));

    if (invalidLessonIds.length > 0) {
      throw new ConflictException(`Les leçons suivantes n'appartiennent pas au cours: ${invalidLessonIds.join(', ')}`);
    }

    // Vérifier si tous les IDs de leçon du cours sont présents
    if (lessonIds.length !== lessons.length) {
      throw new ConflictException(`Tous les IDs de leçon du cours doivent être présents pour la réorganisation`);
    }

    try {
      // Mettre à jour l'ordre des leçons
      const updatedLessons = await Promise.all(
        lessonIds.map((lessonId, index) => {
          return this.prisma.lesson.update({
            where: { id: lessonId },
            data: { order: index + 1 },
          });
        }),
      );

      this.logger.log(`Leçons réorganisées avec succès pour le cours: ${courseId}`);
      return updatedLessons;
    } catch (error) {
      this.logger.error(`Erreur lors de la réorganisation des leçons: ${error.message}`);
      throw error;
    }
  }
}
