import { Test, TestingModule } from '@nestjs/testing';
import { CourseService } from '../services/course.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { CreateCourseDto } from '../dto/create-course.dto';
import { UpdateCourseDto } from '../dto/update-course.dto';
import { PaginationOptions } from '../../../shared/interfaces';

// Mock du PrismaService
const mockPrismaService = {
  course: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  lesson: {
    deleteMany: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
  },
  enrollment: {
    deleteMany: jest.fn(),
    findMany: jest.fn(),
  },
  $transaction: jest.fn().mockImplementation((callback) => Promise.all(callback)),
};

describe('CourseService', () => {
  let service: CourseService;
  // prismaService est déjà mocké via mockPrismaService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CourseService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<CourseService>(CourseService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a course', async () => {
      // Arrange
      const createCourseDto: CreateCourseDto = {
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
      };

      const expectedCourse = {
        id: 'test-id',
        ...createCourseDto,
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
      };

      mockPrismaService.course.findFirst.mockResolvedValue(null);
      mockPrismaService.course.create.mockResolvedValue(expectedCourse);

      // Act
      const result = await service.create(createCourseDto);

      // Assert
      expect(mockPrismaService.course.findFirst).toHaveBeenCalledWith({
        where: { title: createCourseDto.title },
      });
      expect(mockPrismaService.course.create).toHaveBeenCalledWith({
        data: createCourseDto,
      });
      expect(result).toEqual(expectedCourse);
    });

    it('should throw ConflictException if course with same title exists', async () => {
      // Arrange
      const createCourseDto: CreateCourseDto = {
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
      };

      const existingCourse = {
        id: 'existing-id',
        ...createCourseDto,
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
      };

      mockPrismaService.course.findFirst.mockResolvedValue(existingCourse);

      // Act & Assert
      await expect(service.create(createCourseDto)).rejects.toThrow(ConflictException);
      expect(mockPrismaService.course.findFirst).toHaveBeenCalledWith({
        where: { title: createCourseDto.title },
      });
      expect(mockPrismaService.course.create).not.toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return paginated courses', async () => {
      // Arrange
      const paginationOptions: PaginationOptions = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const courses = [
        {
          id: 'test-id-1',
          title: 'Test Course 1',
          description: 'Test Description 1',
          level: 'BEGINNER',
          category: 'Test Category',
          createdAt: new Date(),
          updatedAt: new Date(),
          coverImage: null,
        },
        {
          id: 'test-id-2',
          title: 'Test Course 2',
          description: 'Test Description 2',
          level: 'INTERMEDIATE',
          category: 'Test Category',
          createdAt: new Date(),
          updatedAt: new Date(),
          coverImage: null,
        },
      ];

      const total = 2;

      mockPrismaService.course.findMany.mockResolvedValue(courses);
      mockPrismaService.course.count.mockResolvedValue(total);

      // Act
      const result = await service.findAll(paginationOptions);

      // Assert
      expect(mockPrismaService.course.findMany).toHaveBeenCalledWith({
        skip: 0,
        take: 10,
        orderBy: {
          createdAt: 'desc',
        },
      });
      expect(mockPrismaService.course.count).toHaveBeenCalled();
      expect(result).toEqual({
        courses,
        total,
        page: 1,
        limit: 10,
      });
    });
  });

  describe('findOne', () => {
    it('should return a course by id', async () => {
      // Arrange
      const courseId = 'test-id';
      const expectedCourse = {
        id: courseId,
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
        lessons: [],
      };

      mockPrismaService.course.findUnique.mockResolvedValue(expectedCourse);

      // Act
      const result = await service.findOne(courseId);

      // Assert
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      expect(result).toEqual(expectedCourse);
    });

    it('should throw NotFoundException if course not found', async () => {
      // Arrange
      const courseId = 'non-existent-id';
      mockPrismaService.course.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(courseId)).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
    });
  });

  describe('update', () => {
    it('should update a course', async () => {
      // Arrange
      const courseId = 'test-id';
      const updateCourseDto: UpdateCourseDto = {
        title: 'Updated Course',
        description: 'Updated Description',
      };

      const existingCourse = {
        id: courseId,
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
        lessons: [],
      };

      const updatedCourse = {
        ...existingCourse,
        ...updateCourseDto,
        updatedAt: new Date(),
      };

      mockPrismaService.course.findUnique.mockResolvedValue(existingCourse);
      mockPrismaService.course.findFirst.mockResolvedValue(null);
      mockPrismaService.course.update.mockResolvedValue(updatedCourse);

      // Act
      const result = await service.update(courseId, updateCourseDto);

      // Assert
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      expect(mockPrismaService.course.findFirst).toHaveBeenCalledWith({
        where: {
          title: updateCourseDto.title,
          id: { not: courseId },
        },
      });
      expect(mockPrismaService.course.update).toHaveBeenCalledWith({
        where: { id: courseId },
        data: updateCourseDto,
      });
      expect(result).toEqual(updatedCourse);
    });

    it('should throw NotFoundException if course not found', async () => {
      // Arrange
      const courseId = 'non-existent-id';
      const updateCourseDto: UpdateCourseDto = {
        title: 'Updated Course',
      };

      mockPrismaService.course.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(courseId, updateCourseDto)).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      expect(mockPrismaService.course.update).not.toHaveBeenCalled();
    });

    it('should throw ConflictException if updated title already exists', async () => {
      // Arrange
      const courseId = 'test-id';
      const updateCourseDto: UpdateCourseDto = {
        title: 'Existing Course',
      };

      const existingCourse = {
        id: courseId,
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
        lessons: [],
      };

      const conflictingCourse = {
        id: 'other-id',
        title: 'Existing Course',
        description: 'Other Description',
        level: 'INTERMEDIATE',
        category: 'Other Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
      };

      mockPrismaService.course.findUnique.mockResolvedValue(existingCourse);
      mockPrismaService.course.findFirst.mockResolvedValue(conflictingCourse);

      // Act & Assert
      await expect(service.update(courseId, updateCourseDto)).rejects.toThrow(ConflictException);
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      expect(mockPrismaService.course.findFirst).toHaveBeenCalledWith({
        where: {
          title: updateCourseDto.title,
          id: { not: courseId },
        },
      });
      expect(mockPrismaService.course.update).not.toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should remove a course', async () => {
      // Arrange
      const courseId = 'test-id';
      const existingCourse = {
        id: courseId,
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
        createdAt: new Date(),
        updatedAt: new Date(),
        coverImage: null,
        lessons: [],
      };

      (mockPrismaService.course.findUnique as jest.Mock).mockResolvedValue(existingCourse);
      (mockPrismaService.lesson.deleteMany as jest.Mock).mockResolvedValue({ count: 0 });
      (mockPrismaService.enrollment.deleteMany as jest.Mock).mockResolvedValue({ count: 0 });
      (mockPrismaService.course.delete as jest.Mock).mockResolvedValue(existingCourse);

      // Act
      await service.remove(courseId);

      // Assert
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      expect(mockPrismaService.lesson.deleteMany).toHaveBeenCalledWith({
        where: { courseId },
      });
      expect(mockPrismaService.enrollment.deleteMany).toHaveBeenCalledWith({
        where: { courseId },
      });
      expect(mockPrismaService.course.delete).toHaveBeenCalledWith({
        where: { id: courseId },
      });
    });

    it('should throw NotFoundException if course not found', async () => {
      // Arrange
      const courseId = 'non-existent-id';
      mockPrismaService.course.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(courseId)).rejects.toThrow(NotFoundException);
      expect(mockPrismaService.course.findUnique).toHaveBeenCalledWith({
        where: { id: courseId },
        include: {
          lessons: {
            orderBy: {
              order: 'asc',
            },
          },
        },
      });
      expect(mockPrismaService.course.delete).not.toHaveBeenCalled();
    });
  });

  describe('search', () => {
    it('should search courses by query', async () => {
      // Arrange
      const query = 'test';
      const paginationOptions: PaginationOptions = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      const courses = [
        {
          id: 'test-id-1',
          title: 'Test Course 1',
          description: 'Test Description 1',
          level: 'BEGINNER',
          category: 'Test Category',
          createdAt: new Date(),
          updatedAt: new Date(),
          coverImage: null,
        },
      ];

      const total = 1;

      mockPrismaService.course.findMany.mockResolvedValue(courses);
      mockPrismaService.course.count.mockResolvedValue(total);

      // Act
      const result = await service.search(query, paginationOptions);

      // Assert
      expect(mockPrismaService.course.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { category: { contains: query, mode: 'insensitive' } },
          ],
        },
        skip: 0,
        take: 10,
        orderBy: {
          createdAt: 'desc',
        },
      });
      expect(mockPrismaService.course.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { title: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { category: { contains: query, mode: 'insensitive' } },
          ],
        },
      });
      expect(result).toEqual({
        courses,
        total,
        page: 1,
        limit: 10,
      });
    });
  });
});
