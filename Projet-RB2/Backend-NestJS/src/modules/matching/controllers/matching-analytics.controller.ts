import { Controller, Get, Param, Query, UseGuards, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { MatchingAnalyticsService } from '../services/matching-analytics.service';
import { MatchingResult } from '../dto';
import { User } from '../../users/decorators/user.decorator';

@ApiTags('matching-analytics')
@Controller('matching/analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MatchingAnalyticsController {
  constructor(private readonly analyticsService: MatchingAnalyticsService) {}

  @Get('partner/:partnerId')
  @Roles('ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Get matching analytics for a partner' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiQuery({ name: 'period', description: 'Period (day, week, month, year)', required: false })
  @ApiResponse({ status: 200, description: 'Returns matching analytics for the partner' })
  async getPartnerAnalytics(
    @Param('partnerId') partnerId: string,
    @Query('period') period: string = 'month',
  ) {
    return this.analyticsService.getPartnerMatchingStats(partnerId, period);
  }

  @Post('view')
  @Roles('USER', 'ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Record a matching view event' })
  @ApiResponse({ status: 201, description: 'Event recorded successfully' })
  async recordView(
    @Body() data: { result: MatchingResult; metadata?: any },
    @User() user: any,
  ) {
    await this.analyticsService.recordMatchingView(data.result, user.id, data.metadata);
    return { success: true };
  }

  @Post('contact')
  @Roles('USER', 'ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Record a matching contact event' })
  @ApiResponse({ status: 201, description: 'Event recorded successfully' })
  async recordContact(
    @Body() data: { result: MatchingResult; contactMethod: string; metadata?: any },
    @User() user: any,
  ) {
    await this.analyticsService.recordMatchingContact(
      data.result,
      user.id,
      data.contactMethod,
      data.metadata,
    );
    return { success: true };
  }

  @Post('conversion')
  @Roles('USER', 'ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Record a matching conversion event' })
  @ApiResponse({ status: 201, description: 'Event recorded successfully' })
  async recordConversion(
    @Body() data: { result: MatchingResult; conversionType: string; metadata?: any },
    @User() user: any,
  ) {
    await this.analyticsService.recordMatchingConversion(
      data.result,
      user.id,
      data.conversionType,
      data.metadata,
    );
    return { success: true };
  }

  @Get('dashboard')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get matching analytics dashboard data' })
  @ApiQuery({ name: 'period', description: 'Period (day, week, month, year)', required: false })
  @ApiResponse({ status: 200, description: 'Returns matching analytics dashboard data' })
  async getDashboardData(@Query('period') period: string = 'month') {
    // Cette méthode serait implémentée dans le service d'analyse
    // pour fournir des données agrégées pour le tableau de bord d'administration
    return { message: 'Dashboard data would be returned here' };
  }

  @Get('trends')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get matching trends data' })
  @ApiQuery({ name: 'period', description: 'Period (day, week, month, year)', required: false })
  @ApiResponse({ status: 200, description: 'Returns matching trends data' })
  async getTrendsData(@Query('period') period: string = 'month') {
    // Cette méthode serait implémentée dans le service d'analyse
    // pour fournir des données de tendances pour le tableau de bord d'administration
    return { message: 'Trends data would be returned here' };
  }

  @Get('performance')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get matching performance data' })
  @ApiQuery({ name: 'period', description: 'Period (day, week, month, year)', required: false })
  @ApiResponse({ status: 200, description: 'Returns matching performance data' })
  async getPerformanceData(@Query('period') period: string = 'month') {
    // Cette méthode serait implémentée dans le service d'analyse
    // pour fournir des données de performance pour le tableau de bord d'administration
    return { message: 'Performance data would be returned here' };
  }
}
