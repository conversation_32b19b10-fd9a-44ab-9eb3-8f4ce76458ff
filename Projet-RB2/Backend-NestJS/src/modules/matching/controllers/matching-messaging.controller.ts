import { Controller, Post, Body, UseGuards, Param, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, <PERSON>piParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { MatchingMessagingService } from '../services/matching-messaging.service';
import { MatchingService } from '../services/matching.service';
import { User } from '../../users/decorators/user.decorator';
import { PrismaService } from '../../../prisma/prisma.service';

class SendMessageDto {
  partnerId: string;
  retreatId: string;
  message?: string;
}

class SendFollowUpDto {
  conversationId: string;
  message?: string;
}

@ApiTags('matching-messaging')
@Controller('matching/messaging')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MatchingMessagingController {
  constructor(
    private readonly messagingService: MatchingMessagingService,
    private readonly matchingService: MatchingService,
    private readonly prisma: PrismaService,
  ) {}

  @Post('partner-to-organizer')
  @Roles('PARTNER')
  @ApiOperation({ summary: 'Send a message from partner to retreat organizer' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  async sendPartnerToOrganizerMessage(
    @Body() dto: SendMessageDto,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est bien le propriétaire du partenaire
    const partner = await this.prisma.partner.findUnique({
      where: { id: dto.partnerId },
      select: { userId: true },
    });

    if (!partner || partner.userId !== user.id) {
      throw new BadRequestException('You are not authorized to send messages as this partner');
    }

    // Récupérer les informations de matching
    const matchingResult = await this.matchingService.getMatchingScore(dto.partnerId, dto.retreatId);
    
    if (!matchingResult || matchingResult.score === 0) {
      throw new BadRequestException('No matching found between this partner and retreat');
    }

    // Créer un objet de résultat de matching complet
    const fullMatchingResult = {
      partnerId: dto.partnerId,
      retreatId: dto.retreatId,
      score: matchingResult.score,
      compatibilityFactors: matchingResult.compatibilityFactors,
      partner: await this.prisma.partner.findUnique({
        where: { id: dto.partnerId },
        select: {
          id: true,
          companyName: true,
          description: true,
          category: true,
          type: true,
        },
      }),
      retreat: await this.prisma.retreat.findUnique({
        where: { id: dto.retreatId },
        select: {
          id: true,
          title: true,
          description: true,
          startDate: true,
          endDate: true,
          location: true,
        },
      }),
    };

    // Envoyer le message
    const conversationId = await this.messagingService.sendPartnerToOrganizerMessage(
      fullMatchingResult,
      dto.message,
    );

    return {
      success: true,
      conversationId,
      message: 'Message sent successfully',
    };
  }

  @Post('organizer-to-partner')
  @Roles('USER')
  @ApiOperation({ summary: 'Send a message from retreat organizer to partner' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  async sendOrganizerToPartnerMessage(
    @Body() dto: SendMessageDto,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est bien l'organisateur de la retraite
    const retreat = await this.prisma.retreat.findUnique({
      where: { id: dto.retreatId },
      select: { organizerId: true },
    });

    if (!retreat || retreat.organizerId !== user.id) {
      throw new BadRequestException('You are not authorized to send messages as the organizer of this retreat');
    }

    // Récupérer les informations de matching
    const matchingResult = await this.matchingService.getMatchingScore(dto.partnerId, dto.retreatId);
    
    if (!matchingResult || matchingResult.score === 0) {
      throw new BadRequestException('No matching found between this partner and retreat');
    }

    // Créer un objet de résultat de matching complet
    const fullMatchingResult = {
      partnerId: dto.partnerId,
      retreatId: dto.retreatId,
      score: matchingResult.score,
      compatibilityFactors: matchingResult.compatibilityFactors,
      partner: await this.prisma.partner.findUnique({
        where: { id: dto.partnerId },
        select: {
          id: true,
          companyName: true,
          description: true,
          category: true,
          type: true,
        },
      }),
      retreat: await this.prisma.retreat.findUnique({
        where: { id: dto.retreatId },
        select: {
          id: true,
          title: true,
          description: true,
          startDate: true,
          endDate: true,
          location: true,
        },
      }),
    };

    // Envoyer le message
    const conversationId = await this.messagingService.sendOrganizerToPartnerMessage(
      fullMatchingResult,
      dto.message,
    );

    return {
      success: true,
      conversationId,
      message: 'Message sent successfully',
    };
  }

  @Post('follow-up/:conversationId')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Send a follow-up message in an existing conversation' })
  @ApiParam({ name: 'conversationId', description: 'Conversation ID' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  async sendFollowUpMessage(
    @Param('conversationId') conversationId: string,
    @Body() dto: SendFollowUpDto,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est bien un participant de la conversation
    const conversation = await this.prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: {
          select: {
            userId: true,
          },
        },
      },
    });

    if (!conversation) {
      throw new BadRequestException('Conversation not found');
    }

    const isParticipant = conversation.participants.some(p => p.userId === user.id);
    if (!isParticipant) {
      throw new BadRequestException('You are not a participant of this conversation');
    }

    // Envoyer le message de suivi
    const messageId = await this.messagingService.sendFollowUpMessage(
      conversationId,
      user.id,
      dto.message,
    );

    return {
      success: true,
      messageId,
      message: 'Follow-up message sent successfully',
    };
  }

  @Post('contact-from-matching/:partnerId/:retreatId')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Contact from matching details page' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiParam({ name: 'retreatId', description: 'Retreat ID' })
  @ApiResponse({ status: 201, description: 'Contact initiated successfully' })
  async contactFromMatching(
    @Param('partnerId') partnerId: string,
    @Param('retreatId') retreatId: string,
    @Body() dto: { message?: string },
    @User() user: any,
  ) {
    // Récupérer les informations de l'utilisateur
    const userInfo = await this.prisma.user.findUnique({
      where: { id: user.id },
      select: { id: true, role: true },
    });

    if (!userInfo) {
      throw new BadRequestException('User not found');
    }

    // Récupérer les informations de matching
    const matchingResult = await this.matchingService.getMatchingScore(partnerId, retreatId);
    
    if (!matchingResult || matchingResult.score === 0) {
      throw new BadRequestException('No matching found between this partner and retreat');
    }

    // Créer un objet de résultat de matching complet
    const fullMatchingResult = {
      partnerId,
      retreatId,
      score: matchingResult.score,
      compatibilityFactors: matchingResult.compatibilityFactors,
      partner: await this.prisma.partner.findUnique({
        where: { id: partnerId },
        select: {
          id: true,
          userId: true,
          companyName: true,
          description: true,
          category: true,
          type: true,
        },
      }),
      retreat: await this.prisma.retreat.findUnique({
        where: { id: retreatId },
        select: {
          id: true,
          organizerId: true,
          title: true,
          description: true,
          startDate: true,
          endDate: true,
          location: true,
        },
      }),
    };

    let conversationId: string;

    // Déterminer le type de message à envoyer en fonction du rôle de l'utilisateur
    if (userInfo.role === 'PARTNER' && fullMatchingResult.partner.userId === user.id) {
      // Partenaire qui contacte l'organisateur
      conversationId = await this.messagingService.sendPartnerToOrganizerMessage(
        fullMatchingResult,
        dto.message,
      );
    } else if (fullMatchingResult.retreat.organizerId === user.id) {
      // Organisateur qui contacte le partenaire
      conversationId = await this.messagingService.sendOrganizerToPartnerMessage(
        fullMatchingResult,
        dto.message,
      );
    } else {
      throw new BadRequestException('You are not authorized to initiate contact for this matching');
    }

    return {
      success: true,
      conversationId,
      message: 'Contact initiated successfully',
    };
  }
}
