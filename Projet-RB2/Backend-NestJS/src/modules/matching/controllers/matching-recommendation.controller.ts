import { Controller, Get, Param, UseGuards, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { MatchingRecommendationService } from '../services/matching-recommendation.service';
import { User } from '../../users/decorators/user.decorator';

@ApiTags('matching-recommendations')
@Controller('matching/recommendations')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MatchingRecommendationController {
  constructor(private readonly recommendationService: MatchingRecommendationService) {}

  @Get('partner/:partnerId')
  @Roles('ADMIN', 'PARTNER')
  @ApiOperation({ summary: 'Get recommendations for a partner' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiResponse({ status: 200, description: 'Returns recommendations for the partner' })
  async getPartnerRecommendations(@Param('partnerId') partnerId: string, @User() user: any) {
    // Vérifier que l'utilisateur est le propriétaire du partenaire ou un administrateur
    if (user.role !== 'ADMIN') {
      const partner = await this.validatePartnerOwnership(partnerId, user.id);
      if (!partner) {
        return { error: 'You are not authorized to access this resource' };
      }
    }

    return this.recommendationService.generateRecommendationsOnDemand(partnerId);
  }

  @Post('generate/partner/:partnerId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Generate recommendations for a partner' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiResponse({ status: 201, description: 'Recommendations generated successfully' })
  async generatePartnerRecommendations(@Param('partnerId') partnerId: string) {
    const partner = await this.getPartnerById(partnerId);
    if (!partner) {
      return { error: 'Partner not found' };
    }

    await this.recommendationService.generateRecommendationsForPartner(partner);
    return { success: true, message: 'Recommendations generated successfully' };
  }

  @Post('generate/retreat/:retreatId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Generate recommendations for a retreat' })
  @ApiParam({ name: 'retreatId', description: 'Retreat ID' })
  @ApiResponse({ status: 201, description: 'Recommendations generated successfully' })
  async generateRetreatRecommendations(@Param('retreatId') retreatId: string) {
    const retreat = await this.getRetreatById(retreatId);
    if (!retreat) {
      return { error: 'Retreat not found' };
    }

    await this.recommendationService.generateRecommendationsForRetreat(retreat);
    return { success: true, message: 'Recommendations generated successfully' };
  }

  @Post('generate/all-partners')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Generate recommendations for all partners' })
  @ApiResponse({ status: 201, description: 'Recommendations generation started' })
  async generateAllPartnerRecommendations() {
    // Lancer la tâche de génération de recommandations en arrière-plan
    this.recommendationService.generatePartnerRecommendations();
    return { success: true, message: 'Recommendations generation started' };
  }

  @Post('generate/all-retreats')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Generate recommendations for all retreats' })
  @ApiResponse({ status: 201, description: 'Recommendations generation started' })
  async generateAllRetreatRecommendations() {
    // Lancer la tâche de génération de recommandations en arrière-plan
    this.recommendationService.generateRetreatRecommendations();
    return { success: true, message: 'Recommendations generation started' };
  }

  /**
   * Valide que l'utilisateur est le propriétaire du partenaire
   * @param partnerId ID du partenaire
   * @param userId ID de l'utilisateur
   * @returns Partenaire si l'utilisateur est le propriétaire, null sinon
   */
  private async validatePartnerOwnership(partnerId: string, userId: string) {
    const partner = await this.getPartnerById(partnerId);
    if (!partner || partner.userId !== userId) {
      return null;
    }
    return partner;
  }

  /**
   * Récupère un partenaire par son ID
   * @param partnerId ID du partenaire
   * @returns Partenaire
   */
  private async getPartnerById(partnerId: string) {
    return await this.recommendationService['prisma'].partner.findUnique({
      where: { id: partnerId },
      select: {
        id: true,
        userId: true,
        companyName: true,
        specializations: true,
        category: true,
        type: true,
        coverageAreas: true,
      },
    });
  }

  /**
   * Récupère une retraite par son ID
   * @param retreatId ID de la retraite
   * @returns Retraite
   */
  private async getRetreatById(retreatId: string) {
    return await this.recommendationService['prisma'].retreat.findUnique({
      where: { id: retreatId },
      select: {
        id: true,
        title: true,
        organizerId: true,
        location: true,
        startDate: true,
        endDate: true,
        categories: true,
      },
    });
  }
}
