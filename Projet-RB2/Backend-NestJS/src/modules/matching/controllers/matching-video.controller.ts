import { Controller, Post, Get, Body, Param, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { MatchingVideoService } from '../services/matching-video.service';
import { User } from '../../users/decorators/user.decorator';
import { PrismaService } from '../../../prisma/prisma.service';

class CreateVideoRoomDto {
  partnerId: string;
  retreatId: string;
  title?: string;
  description?: string;
  scheduledFor?: Date;
  duration?: number;
  isPrivate?: boolean;
  password?: string;
}

@ApiTags('matching-video')
@Controller('matching/video')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class MatchingVideoController {
  constructor(
    private readonly videoService: MatchingVideoService,
    private readonly prisma: PrismaService,
  ) {}

  @Post('rooms')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Create a video conference room for a matching' })
  @ApiResponse({ status: 201, description: 'Video room created successfully' })
  async createVideoRoom(
    @Body() dto: CreateVideoRoomDto,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est autorisé à créer une salle pour ce matching
    const isPartner = await this.prisma.partner.findFirst({
      where: {
        id: dto.partnerId,
        userId: user.id,
      },
    });

    const isOrganizer = await this.prisma.retreat.findFirst({
      where: {
        id: dto.retreatId,
        organizerId: user.id,
      },
    });

    if (!isPartner && !isOrganizer) {
      throw new HttpException('You are not authorized to create a video room for this matching', HttpStatus.FORBIDDEN);
    }

    const videoRoom = await this.videoService.createVideoRoom(
      dto.partnerId,
      dto.retreatId,
      user.id,
      {
        title: dto.title,
        description: dto.description,
        scheduledFor: dto.scheduledFor,
        duration: dto.duration,
        isPrivate: dto.isPrivate,
        password: dto.password,
      },
    );

    return {
      success: true,
      videoRoom,
      message: 'Video room created successfully',
    };
  }

  @Get('rooms/:partnerId/:retreatId')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Get video rooms for a matching' })
  @ApiParam({ name: 'partnerId', description: 'Partner ID' })
  @ApiParam({ name: 'retreatId', description: 'Retreat ID' })
  @ApiResponse({ status: 200, description: 'Returns video rooms for the matching' })
  async getVideoRooms(
    @Param('partnerId') partnerId: string,
    @Param('retreatId') retreatId: string,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est autorisé à voir les salles pour ce matching
    const isPartner = await this.prisma.partner.findFirst({
      where: {
        id: partnerId,
        userId: user.id,
      },
    });

    const isOrganizer = await this.prisma.retreat.findFirst({
      where: {
        id: retreatId,
        organizerId: user.id,
      },
    });

    if (!isPartner && !isOrganizer && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to view video rooms for this matching', HttpStatus.FORBIDDEN);
    }

    const videoRooms = await this.videoService.getVideoRooms(partnerId, retreatId);

    return {
      success: true,
      videoRooms,
    };
  }

  @Get('rooms/details/:roomId')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Get details of a video room' })
  @ApiParam({ name: 'roomId', description: 'Room ID' })
  @ApiResponse({ status: 200, description: 'Returns details of the video room' })
  async getVideoRoomDetails(
    @Param('roomId') roomId: string,
    @User() user: any,
  ) {
    // La vérification des autorisations est faite dans le service
    const videoRoom = await this.videoService.getVideoRoomDetails(roomId);

    // Vérifier que l'utilisateur est autorisé à voir les détails de cette salle
    const isPartner = videoRoom.partner.user.id === user.id;
    const isOrganizer = videoRoom.retreat.organizer.id === user.id;

    if (!isPartner && !isOrganizer && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to view details of this video room', HttpStatus.FORBIDDEN);
    }

    return {
      success: true,
      videoRoom,
    };
  }

  @Get('join/:roomId')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'Generate a join link for a video room' })
  @ApiParam({ name: 'roomId', description: 'Room ID' })
  @ApiResponse({ status: 200, description: 'Returns a join link for the video room' })
  async generateJoinLink(
    @Param('roomId') roomId: string,
    @User() user: any,
  ) {
    // La vérification des autorisations est faite dans le service
    const joinUrl = await this.videoService.generateJoinLink(roomId, user.id);

    return {
      success: true,
      joinUrl,
    };
  }

  @Post('rooms/:roomId/end')
  @Roles('USER', 'PARTNER')
  @ApiOperation({ summary: 'End a video room' })
  @ApiParam({ name: 'roomId', description: 'Room ID' })
  @ApiResponse({ status: 200, description: 'Video room ended successfully' })
  async endVideoRoom(
    @Param('roomId') roomId: string,
    @User() user: any,
  ) {
    // Récupérer les détails de la salle
    const videoRoom = await this.prisma.matchingVideoRoom.findUnique({
      where: { id: roomId },
      include: {
        partner: {
          select: {
            userId: true,
          },
        },
        retreat: {
          select: {
            organizerId: true,
          },
        },
      },
    });

    if (!videoRoom) {
      throw new HttpException('Video room not found', HttpStatus.NOT_FOUND);
    }

    // Vérifier que l'utilisateur est autorisé à terminer cette salle
    const isHost = videoRoom.retreat.organizerId === user.id;
    const isCreator = videoRoom.createdBy === user.id;

    if (!isHost && !isCreator && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to end this video room', HttpStatus.FORBIDDEN);
    }

    // Mettre à jour le statut de la salle
    await this.prisma.matchingVideoRoom.update({
      where: { id: roomId },
      data: { status: 'ENDED' },
    });

    return {
      success: true,
      message: 'Video room ended successfully',
    };
  }
}
