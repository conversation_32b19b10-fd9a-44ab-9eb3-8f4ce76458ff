import { <PERSON>, Get, Post, Body, Param, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MatchingService } from './matching.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';

@ApiTags('Matching')
@Controller('matching')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MatchingController {
  private readonly logger = new Logger(MatchingController.name);

  constructor(private readonly matchingService: MatchingService) {}

  @Post('find')
  @ApiOperation({ summary: 'Trouver des matches' })
  @ApiResponse({ status: 200, description: 'Matches trouvés avec succès' })
  async findMatches(
    @GetUser('id') userId: string,
    @Body() criteria: any,
  ) {
    this.logger.log(`Finding matches for user ${userId}`);
    return await this.matchingService.findMatches(userId, criteria);
  }

  @Get('compatibility/:targetUserId')
  @ApiOperation({ summary: 'Calculer la compatibilité avec un autre utilisateur' })
  @ApiResponse({ status: 200, description: 'Score de compatibilité calculé' })
  async getCompatibilityScore(
    @GetUser('id') userId: string,
    @Param('targetUserId') targetUserId: string,
  ) {
    this.logger.log(`Calculating compatibility between ${userId} and ${targetUserId}`);
    return await this.matchingService.calculateCompatibilityScore(userId, targetUserId);
  }
}
