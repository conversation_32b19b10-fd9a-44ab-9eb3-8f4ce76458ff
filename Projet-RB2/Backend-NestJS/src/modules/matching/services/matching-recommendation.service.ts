import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { MatchingService } from './matching.service';
import { MatchingNotificationService } from './matching-notification.service';
import { MatchingAnalyticsService } from './matching-analytics.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MatchingCriteriaDto, MatchingResult } from '../dto';
import { PartnerCategory, PartnerType } from '../../../prisma/prisma-types';

@Injectable()
export class MatchingRecommendationService {
  private readonly logger = new Logger(MatchingRecommendationService.name);
  private readonly enableRecommendations: boolean;
  private readonly recommendationThreshold: number;
  private readonly maxRecommendationsPerUser: number;
  private readonly recommendationCooldownHours: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly matchingService: MatchingService,
    private readonly notificationService: MatchingNotificationService,
    private readonly analyticsService: MatchingAnalyticsService,
  ) {
    this.enableRecommendations = this.configService.get<boolean>('matching.enableRecommendations', true);
    this.recommendationThreshold = this.configService.get<number>('matching.recommendationThreshold', 85);
    this.maxRecommendationsPerUser = this.configService.get<number>('matching.maxRecommendationsPerUser', 5);
    this.recommendationCooldownHours = this.configService.get<number>('matching.recommendationCooldownHours', 24);
  }

  /**
   * Génère des recommandations proactives pour les partenaires
   * Exécuté tous les jours à minuit
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async generatePartnerRecommendations() {
    if (!this.enableRecommendations) {
      return;
    }

    this.logger.log('Generating partner recommendations...');

    try {
      // Récupérer tous les partenaires actifs
      const partners = await this.prisma.partner.findMany({
        where: {
          status: 'ACTIVE',
        },
        select: {
          id: true,
          userId: true,
          companyName: true,
          specializations: true,
          category: true,
          type: true,
          coverageAreas: true,
        },
      });

      this.logger.log(`Found ${partners.length} active partners`);

      // Pour chaque partenaire, générer des recommandations
      for (const partner of partners) {
        await this.generateRecommendationsForPartner(partner);
      }

      this.logger.log('Partner recommendations generation completed');
    } catch (error) {
      this.logger.error(`Error generating partner recommendations: ${error.message}`, error.stack);
    }
  }

  /**
   * Génère des recommandations proactives pour les organisateurs de retraites
   * Exécuté tous les jours à 2h du matin
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async generateRetreatRecommendations() {
    if (!this.enableRecommendations) {
      return;
    }

    this.logger.log('Generating retreat recommendations...');

    try {
      // Récupérer toutes les retraites publiées à venir
      const retreats = await this.prisma.retreat.findMany({
        where: {
          status: 'PUBLISHED',
          startDate: {
            gte: new Date(),
          },
        },
        select: {
          id: true,
          title: true,
          organizerId: true,
          location: true,
          startDate: true,
          endDate: true,
          categories: true,
        },
      });

      this.logger.log(`Found ${retreats.length} upcoming retreats`);

      // Pour chaque retraite, générer des recommandations
      for (const retreat of retreats) {
        await this.generateRecommendationsForRetreat(retreat);
      }

      this.logger.log('Retreat recommendations generation completed');
    } catch (error) {
      this.logger.error(`Error generating retreat recommendations: ${error.message}`, error.stack);
    }
  }

  /**
   * Génère des recommandations pour un partenaire spécifique
   * @param partner Partenaire
   */
  async generateRecommendationsForPartner(partner: any) {
    try {
      // Vérifier si le partenaire a déjà reçu des recommandations récemment
      const lastRecommendation = await this.prisma.notification.findFirst({
        where: {
          userId: partner.userId,
          type: 'RECOMMENDATION',
          createdAt: {
            gte: new Date(Date.now() - this.recommendationCooldownHours * 60 * 60 * 1000),
          },
        },
      });

      if (lastRecommendation) {
        this.logger.debug(`Partner ${partner.id} already received recommendations recently`);
        return;
      }

      // Construire les critères de matching
      const criteria: MatchingCriteriaDto = {
        partnerId: partner.id,
        dateRange: {
          start: new Date().toISOString().split('T')[0],
          end: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90 jours
        },
        limit: 10,
      };

      // Rechercher des retraites correspondantes
      const matchingResponse = await this.matchingService.findRetreatsForPartner(criteria);

      // Filtrer les résultats avec un score supérieur au seuil
      const highQualityMatches = matchingResponse.results.filter(
        result => result.score >= this.recommendationThreshold,
      );

      if (highQualityMatches.length === 0) {
        this.logger.debug(`No high quality matches found for partner ${partner.id}`);
        return;
      }

      // Limiter le nombre de recommandations
      const limitedMatches = highQualityMatches.slice(0, this.maxRecommendationsPerUser);

      // Envoyer une notification avec les recommandations
      await this.notificationService.sendMatchingNotification({
        result: limitedMatches[0],
        userId: partner.userId,
        title: `${limitedMatches.length} nouvelles retraites recommandées`,
        message: `Nous avons trouvé ${limitedMatches.length} retraites qui correspondent parfaitement à votre profil.`,
        data: {
          matches: limitedMatches.map(match => ({
            retreatId: match.retreatId,
            score: match.score,
            title: match.retreat?.title,
            startDate: match.retreat?.startDate,
            endDate: match.retreat?.endDate,
            location: match.retreat?.location,
          })),
        }
      });

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordAnalyticsEvent({
        eventType: 'RECOMMENDATION_SENT',
        partnerId: partner.id,
        userId: partner.userId,
        resultCount: limitedMatches.length,
        score: limitedMatches[0].score,
        timestamp: new Date(),
      });

      this.logger.log(`Sent ${limitedMatches.length} recommendations to partner ${partner.id}`);
    } catch (error) {
      this.logger.error(`Error generating recommendations for partner ${partner.id}: ${error.message}`, error.stack);
    }
  }

  /**
   * Génère des recommandations pour une retraite spécifique
   * @param retreat Retraite
   */
  async generateRecommendationsForRetreat(retreat: any) {
    try {
      // Vérifier si l'organisateur a déjà reçu des recommandations récemment
      const lastRecommendation = await this.prisma.notification.findFirst({
        where: {
          userId: retreat.organizerId,
          type: 'RECOMMENDATION',
          createdAt: {
            gte: new Date(Date.now() - this.recommendationCooldownHours * 60 * 60 * 1000),
          },
        },
      });

      if (lastRecommendation) {
        this.logger.debug(`Retreat organizer ${retreat.organizerId} already received recommendations recently`);
        return;
      }

      // Construire les critères de matching
      const criteria: MatchingCriteriaDto = {
        retreatId: retreat.id,
        categories: retreat.categories?.map(cat => cat.name as PartnerCategory) || [],
        limit: 10,
      };

      // Rechercher des partenaires correspondants
      const matchingResponse = await this.matchingService.findPartnersForRetreat(criteria);

      // Filtrer les résultats avec un score supérieur au seuil
      const highQualityMatches = matchingResponse.results.filter(
        result => result.score >= this.recommendationThreshold,
      );

      if (highQualityMatches.length === 0) {
        this.logger.debug(`No high quality matches found for retreat ${retreat.id}`);
        return;
      }

      // Limiter le nombre de recommandations
      const limitedMatches = highQualityMatches.slice(0, this.maxRecommendationsPerUser);

      // Envoyer une notification avec les recommandations
      await this.notificationService.sendMatchingNotification({
        result: limitedMatches[0],
        userId: retreat.organizerId,
        title: `${limitedMatches.length} partenaires recommandés pour votre retraite`,
        message: `Nous avons trouvé ${limitedMatches.length} partenaires qui correspondent parfaitement à votre retraite "${retreat.title}".`,
        data: {
          retreatId: retreat.id,
          matches: limitedMatches.map(match => ({
            partnerId: match.partnerId,
            score: match.score,
            companyName: match.partner?.companyName,
            category: match.partner?.category,
            type: match.partner?.type,
          })),
        }
      });

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordAnalyticsEvent({
        eventType: 'RECOMMENDATION_SENT',
        retreatId: retreat.id,
        userId: retreat.organizerId,
        resultCount: limitedMatches.length,
        score: limitedMatches[0].score,
        timestamp: new Date(),
      });

      this.logger.log(`Sent ${limitedMatches.length} recommendations to retreat organizer ${retreat.organizerId}`);
    } catch (error) {
      this.logger.error(`Error generating recommendations for retreat ${retreat.id}: ${error.message}`, error.stack);
    }
  }

  /**
   * Génère des recommandations à la demande pour un partenaire
   * @param partnerId ID du partenaire
   * @returns Résultats du matching
   */
  async generateRecommendationsOnDemand(partnerId: string): Promise<MatchingResult[]> {
    try {
      // Récupérer le partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: partnerId },
        select: {
          id: true,
          userId: true,
          companyName: true,
          specializations: true,
          category: true,
          type: true,
          coverageAreas: true,
        },
      });

      if (!partner) {
        throw new Error(`Partner with ID ${partnerId} not found`);
      }

      // Construire les critères de matching
      const criteria: MatchingCriteriaDto = {
        partnerId: partner.id,
        dateRange: {
          start: new Date().toISOString().split('T')[0],
          end: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90 jours
        },
        limit: 10,
      };

      // Rechercher des retraites correspondantes
      const matchingResponse = await this.matchingService.findRetreatsForPartner(criteria);

      // Filtrer les résultats avec un score supérieur au seuil
      const highQualityMatches = matchingResponse.results.filter(
        result => result.score >= this.recommendationThreshold,
      );

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordAnalyticsEvent({
        eventType: 'RECOMMENDATION_REQUESTED',
        partnerId: partner.id,
        userId: partner.userId,
        resultCount: highQualityMatches.length,
        score: highQualityMatches.length > 0 ? highQualityMatches[0].score : 0,
        timestamp: new Date(),
      });

      return highQualityMatches;
    } catch (error) {
      this.logger.error(`Error generating on-demand recommendations for partner ${partnerId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
