import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { MatchingAnalyticsService } from './matching-analytics.service';

@Injectable()
export class MatchingVideoService {
  private readonly logger = new Logger(MatchingVideoService.name);
  private readonly socialPlatformVideoUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly analyticsService: MatchingAnalyticsService,
  ) {
    this.socialPlatformVideoUrl = this.configService.get<string>('microservices.socialPlatformVideo.url', 'http://social-platform-video:3002');
    this.apiKey = this.configService.get<string>('microservices.socialPlatformVideo.apiKey', 'default-api-key');
  }

  /**
   * Crée une salle de vidéoconférence pour un matching
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @param createdBy ID de l'utilisateur qui crée la salle
   * @param options Options supplémentaires
   * @returns Informations sur la salle créée
   */
  async createVideoRoom(
    partnerId: string,
    retreatId: string,
    createdBy: string,
    options?: {
      title?: string;
      description?: string;
      scheduledFor?: Date;
      duration?: number; // en minutes
      isPrivate?: boolean;
      password?: string;
    },
  ): Promise<any> {
    try {
      // Vérifier que le matching existe
      const matchingScore = await this.prisma.matchingScore.findFirst({
        where: {
          partnerId,
          retreatId,
        },
      });

      if (!matchingScore) {
        throw new HttpException('Matching not found', HttpStatus.NOT_FOUND);
      }

      // Récupérer les informations du partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: partnerId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!partner) {
        throw new HttpException('Partner not found', HttpStatus.NOT_FOUND);
      }

      // Récupérer les informations de la retraite
      const retreat = await this.prisma.retreat.findUnique({
        where: { id: retreatId },
        include: {
          organizer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new HttpException('Retreat not found', HttpStatus.NOT_FOUND);
      }

      // Vérifier que l'utilisateur est autorisé à créer une salle
      if (createdBy !== partner.userId && createdBy !== retreat.organizerId) {
        throw new HttpException('Unauthorized to create a video room for this matching', HttpStatus.FORBIDDEN);
      }

      // Préparer les données pour la création de la salle
      const roomTitle = options?.title || `Discussion: ${partner.companyName} - ${retreat.title}`;
      const roomDescription = options?.description || `Salle de discussion pour le matching entre ${partner.companyName} et la retraite "${retreat.title}" (Score: ${matchingScore.score}%)`;
      
      const participants = [
        {
          userId: partner.userId,
          email: partner.user.email,
          name: `${partner.user.firstName} ${partner.user.lastName}`,
          role: 'participant',
        },
        {
          userId: retreat.organizerId,
          email: retreat.organizer.email,
          name: `${retreat.organizer.firstName} ${retreat.organizer.lastName}`,
          role: 'host',
        },
      ];

      // Créer la salle via l'API du microservice Social-Platform-Video
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/rooms`, {
          title: roomTitle,
          description: roomDescription,
          scheduledFor: options?.scheduledFor || new Date(),
          duration: options?.duration || 60, // 60 minutes par défaut
          isPrivate: options?.isPrivate !== undefined ? options.isPrivate : true,
          password: options?.password,
          participants,
          metadata: {
            matchingId: `${partnerId}-${retreatId}`,
            matchingScore: matchingScore.score,
            partnerId,
            retreatId,
            createdBy,
          },
        }, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error creating video room: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error creating video room',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Enregistrer la salle dans la base de données
      const videoRoom = await this.prisma.matchingVideoRoom.create({
        data: {
          externalRoomId: data.id,
          partnerId,
          retreatId,
          title: roomTitle,
          description: roomDescription,
          scheduledFor: options?.scheduledFor || new Date(),
          duration: options?.duration || 60,
          isPrivate: options?.isPrivate !== undefined ? options.isPrivate : true,
          password: options?.password,
          createdBy,
          status: 'SCHEDULED',
          joinUrl: data.joinUrl,
          hostUrl: data.hostUrl,
        },
      });

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordMatchingInteraction({
        partnerId,
        retreatId,
        eventType: 'VIDEO_ROOM_CREATED',
        userId: createdBy,
        metadata: {
          roomId: videoRoom.id,
          externalRoomId: data.id,
          scheduledFor: options?.scheduledFor || new Date(),
        },
      });

      return {
        id: videoRoom.id,
        externalRoomId: data.id,
        title: roomTitle,
        description: roomDescription,
        scheduledFor: options?.scheduledFor || new Date(),
        duration: options?.duration || 60,
        isPrivate: options?.isPrivate !== undefined ? options.isPrivate : true,
        status: 'SCHEDULED',
        joinUrl: data.joinUrl,
        hostUrl: data.hostUrl,
        createdAt: videoRoom.createdAt,
      };
    } catch (error) {
      this.logger.error(`Error in createVideoRoom: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error creating video room', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les salles de vidéoconférence pour un matching
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @returns Liste des salles de vidéoconférence
   */
  async getVideoRooms(partnerId: string, retreatId: string): Promise<any[]> {
    try {
      const videoRooms = await this.prisma.matchingVideoRoom.findMany({
        where: {
          partnerId,
          retreatId,
        },
        orderBy: {
          scheduledFor: 'desc',
        },
      });

      return videoRooms;
    } catch (error) {
      this.logger.error(`Error in getVideoRooms: ${error.message}`, error.stack);
      throw new HttpException('Error retrieving video rooms', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les détails d'une salle de vidéoconférence
   * @param roomId ID de la salle
   * @returns Détails de la salle
   */
  async getVideoRoomDetails(roomId: string): Promise<any> {
    try {
      const videoRoom = await this.prisma.matchingVideoRoom.findUnique({
        where: { id: roomId },
        include: {
          partner: {
            select: {
              id: true,
              companyName: true,
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          retreat: {
            select: {
              id: true,
              title: true,
              organizer: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!videoRoom) {
        throw new HttpException('Video room not found', HttpStatus.NOT_FOUND);
      }

      // Récupérer les détails à jour depuis le microservice Social-Platform-Video
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/rooms/${videoRoom.externalRoomId}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error retrieving video room details: ${error.message}`, error.stack);
            // Si la salle n'existe plus dans le microservice, on retourne quand même les données locales
            if (error.response?.status === 404) {
              return [{ data: null }];
            }
            throw new HttpException(
              error.response?.data || 'Error retrieving video room details',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Mettre à jour le statut si nécessaire
      if (data) {
        if (data.status !== videoRoom.status) {
          await this.prisma.matchingVideoRoom.update({
            where: { id: roomId },
            data: { status: data.status },
          });
          videoRoom.status = data.status;
        }
      }

      return {
        ...videoRoom,
        externalDetails: data,
      };
    } catch (error) {
      this.logger.error(`Error in getVideoRoomDetails: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error retrieving video room details', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Génère un lien de participation pour une salle de vidéoconférence
   * @param roomId ID de la salle
   * @param userId ID de l'utilisateur
   * @returns Lien de participation
   */
  async generateJoinLink(roomId: string, userId: string): Promise<string> {
    try {
      const videoRoom = await this.prisma.matchingVideoRoom.findUnique({
        where: { id: roomId },
        include: {
          partner: {
            select: {
              userId: true,
            },
          },
          retreat: {
            select: {
              organizerId: true,
            },
          },
        },
      });

      if (!videoRoom) {
        throw new HttpException('Video room not found', HttpStatus.NOT_FOUND);
      }

      // Vérifier que l'utilisateur est autorisé à rejoindre la salle
      if (userId !== videoRoom.partner.userId && userId !== videoRoom.retreat.organizerId) {
        throw new HttpException('Unauthorized to join this video room', HttpStatus.FORBIDDEN);
      }

      // Déterminer si l'utilisateur est l'hôte ou un participant
      const isHost = userId === videoRoom.retreat.organizerId;
      const joinUrl = isHost ? videoRoom.hostUrl : videoRoom.joinUrl;

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordMatchingInteraction({
        partnerId: videoRoom.partnerId,
        retreatId: videoRoom.retreatId,
        eventType: 'VIDEO_ROOM_JOIN_LINK_GENERATED',
        userId,
        metadata: {
          roomId: videoRoom.id,
          externalRoomId: videoRoom.externalRoomId,
          isHost,
        },
      });

      return joinUrl;
    } catch (error) {
      this.logger.error(`Error in generateJoinLink: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error generating join link', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
