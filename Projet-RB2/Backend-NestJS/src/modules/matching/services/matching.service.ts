import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import {
  MatchingCriteriaDto,
  MatchingResultDto,
  MatchingResponseDto,
  CompatibilityFactorsDto,
  PartnerMatchDto,
  RetreatMatchDto
} from '../dto';
import { Partner, Retreat, PartnerStatus } from '../../../prisma/prisma-types';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { MatchingNotificationService } from './matching-notification.service';
import { MatchingAnalyticsService } from './matching-analytics.service';

@Injectable()
export class MatchingService {
  private readonly logger = new Logger(MatchingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly notificationService: MatchingNotificationService,
    private readonly analyticsService: MatchingAnalyticsService,
  ) {}

  /**
   * Trouve des partenaires correspondant aux critères pour une retraite
   * @param criteria Critères de recherche
   * @returns Résultats du matching
   */
  async findPartnersForRetreat(criteria: MatchingCriteriaDto): Promise<MatchingResponseDto> {
    const startTime = Date.now();

    try {
      // Récupérer la retraite si un ID est fourni
      let retreat: Retreat | null = null;
      if (criteria.retreatId) {
        retreat = await this.prisma.retreat.findUnique({
          where: { id: criteria.retreatId },
          include: {
            categories: true,
          },
        });

        if (!retreat) {
          throw new NotFoundException(`Retraite avec l'ID ${criteria.retreatId} non trouvée`);
        }
      }

      // Construire la requête de recherche de partenaires
      const partnersQuery = {
        where: {
          status: PartnerStatus.ACTIVE,
          ...(criteria.categories && {
            category: { in: criteria.categories },
          }),
          ...(criteria.types && {
            type: { in: criteria.types },
          }),
          ...(criteria.specializations && {
            specializations: {
              hasSome: criteria.specializations,
            },
          }),
          ...(criteria.languages && {
            languages: {
              hasSome: criteria.languages,
            },
          }),
          ...(criteria.minExperience && {
            completedServices: {
              gte: criteria.minExperience,
            },
          }),
          ...(criteria.location?.country && {
            coverageAreas: {
              path: ['countries'],
              array_contains: [criteria.location.country],
            },
          }),
        },
        include: {
          reviews: {
            select: {
              rating: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      };

      // Exécuter la requête
      const partners = await this.prisma.partner.findMany(partnersQuery);

      // Calculer les scores de compatibilité
      const results = partners.map(partner => this.calculateMatchingScore(partner, retreat, criteria));

      // Trier par score décroissant
      results.sort((a, b) => b.score - a.score);

      // Limiter le nombre de résultats si demandé
      const limitedResults = criteria.limit ? results.slice(0, criteria.limit) : results;

      // Émettre un événement pour le suivi des matchings
      this.eventEmitter.emit('matching.performed', {
        criteria,
        resultsCount: results.length,
        topScore: results.length > 0 ? results[0].score : 0,
        timestamp: new Date(),
        executionTimeMs: Date.now() - startTime,
      });

      // Envoyer des notifications pour les matchings de haute qualité
      if (criteria.retreatId && results.length > 0) {
        try {
          // Récupérer l'utilisateur associé à la retraite
          const retreat = await this.prisma.retreat.findUnique({
            where: { id: criteria.retreatId },
            select: { organizerId: true },
          });

          if (retreat && retreat.organizerId) {
            await this.notificationService.sendBatchMatchingNotifications(
              results.slice(0, 5), // Limiter aux 5 meilleurs matchings
              retreat.organizerId
            );

            // Enregistrer l'événement d'analyse
            await this.analyticsService.recordAnalyticsEvent({
              eventType: 'MATCHING_PERFORMED',
              retreatId: criteria.retreatId,
              userId: retreat.organizerId,
              resultCount: results.length,
              score: results.length > 0 ? results[0].score : 0,
              executionTimeMs: Date.now() - startTime,
              criteriaUsed: criteria,
              timestamp: new Date(),
            });
          }
        } catch (error) {
          this.logger.error(`Error sending notifications: ${error.message}`, error.stack);
        }
      }

      const executionTimeMs = Date.now() - startTime;

      return {
        results: limitedResults,
        total: results.length,
        executionTimeMs,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du matching de partenaires: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Trouve des retraites correspondant aux critères pour un partenaire
   * @param criteria Critères de recherche
   * @returns Résultats du matching
   */
  async findRetreatsForPartner(criteria: MatchingCriteriaDto): Promise<MatchingResponseDto> {
    const startTime = Date.now();

    try {
      // Récupérer le partenaire si un ID est fourni
      let partner: Partner | null = null;
      if (criteria.partnerId) {
        partner = await this.prisma.partner.findUnique({
          where: { id: criteria.partnerId },
          include: {
            reviews: {
              select: {
                rating: true,
              },
            },
          },
        });

        if (!partner) {
          throw new NotFoundException(`Partenaire avec l'ID ${criteria.partnerId} non trouvé`);
        }
      }

      // Construire la requête de recherche de retraites
      const retreatsQuery = {
        where: {
          status: 'PUBLISHED',
          ...(criteria.dateRange && {
            startDate: { gte: new Date(criteria.dateRange.start) },
            endDate: { lte: new Date(criteria.dateRange.end) },
          }),
          ...(criteria.maxBudget && {
            price: { lte: criteria.maxBudget },
          }),
          ...(criteria.location?.country && {
            location: { contains: criteria.location.country },
          }),
          ...(criteria.minCapacity && {
            capacity: { gte: criteria.minCapacity },
          }),
        },
        include: {
          categories: true,
          organizer: true,
          reviews: {
            select: {
              rating: true,
            },
          },
        },
      };

      // Exécuter la requête
      const retreats = await this.prisma.retreat.findMany(retreatsQuery);

      // Calculer les scores de compatibilité
      const results = retreats.map(retreat => this.calculateMatchingScoreForRetreat(retreat, partner, criteria));

      // Trier par score décroissant
      results.sort((a, b) => b.score - a.score);

      // Limiter le nombre de résultats si demandé
      const limitedResults = criteria.limit ? results.slice(0, criteria.limit) : results;

      // Émettre un événement pour le suivi des matchings
      this.eventEmitter.emit('matching.performed', {
        criteria,
        resultsCount: results.length,
        topScore: results.length > 0 ? results[0].score : 0,
        timestamp: new Date(),
        executionTimeMs: Date.now() - startTime,
      });

      // Envoyer des notifications pour les matchings de haute qualité
      if (criteria.partnerId && results.length > 0) {
        try {
          // Récupérer l'utilisateur associé au partenaire
          const partner = await this.prisma.partner.findUnique({
            where: { id: criteria.partnerId },
            select: { userId: true },
          });

          if (partner && partner.userId) {
            await this.notificationService.sendBatchMatchingNotifications(
              results.slice(0, 5), // Limiter aux 5 meilleurs matchings
              partner.userId
            );

            // Enregistrer l'événement d'analyse
            await this.analyticsService.recordAnalyticsEvent({
              eventType: 'MATCHING_PERFORMED',
              partnerId: criteria.partnerId,
              userId: partner.userId,
              resultCount: results.length,
              score: results.length > 0 ? results[0].score : 0,
              executionTimeMs: Date.now() - startTime,
              criteriaUsed: criteria,
              timestamp: new Date(),
            });
          }
        } catch (error) {
          this.logger.error(`Error sending notifications: ${error.message}`, error.stack);
        }
      }

      const executionTimeMs = Date.now() - startTime;

      return {
        results: limitedResults,
        total: results.length,
        executionTimeMs,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du matching de retraites: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calcule le score de compatibilité entre un partenaire et une retraite
   * @param partner Partenaire
   * @param retreat Retraite (optionnel)
   * @param criteria Critères de recherche
   * @returns Résultat du matching
   */
  private calculateMatchingScore(
    partner: any,
    retreat: Retreat | null,
    criteria: MatchingCriteriaDto,
  ): MatchingResultDto {
    // Initialiser les facteurs de compatibilité
    const compatibilityFactors: CompatibilityFactorsDto = {
      skillMatch: 0,
      availabilityMatch: 0,
      locationMatch: 0,
      ratingMatch: 0,
      budgetMatch: 0,
    };

    // Calculer le score de compatibilité des compétences
    if (criteria.specializations && criteria.specializations.length > 0) {
      const matchingSpecializations = partner.specializations.filter(spec =>
        criteria.specializations.includes(spec)
      ).length;
      compatibilityFactors.skillMatch = Math.min(
        100,
        (matchingSpecializations / criteria.specializations.length) * 100
      );
    } else {
      compatibilityFactors.skillMatch = 100;
    }

    // Calculer le score de compatibilité des langues
    if (criteria.languages && criteria.languages.length > 0) {
      const matchingLanguages = partner.languages.filter(lang =>
        criteria.languages.includes(lang)
      ).length;
      const languageScore = Math.min(
        100,
        (matchingLanguages / criteria.languages.length) * 100
      );
      compatibilityFactors.skillMatch = (compatibilityFactors.skillMatch + languageScore) / 2;
    }

    // Calculer le score de compatibilité de localisation
    if (criteria.location) {
      compatibilityFactors.locationMatch =
        partner.coverageAreas &&
        partner.coverageAreas.countries &&
        partner.coverageAreas.countries.includes(criteria.location.country) ? 100 : 0;

      if (criteria.location.region &&
          partner.coverageAreas &&
          partner.coverageAreas.regions &&
          partner.coverageAreas.regions.includes(criteria.location.region)) {
        compatibilityFactors.locationMatch = 100;
      }
    } else {
      compatibilityFactors.locationMatch = 100;
    }

    // Calculer le score de compatibilité des évaluations
    if (partner.reviews && partner.reviews.length > 0) {
      const avgRating = partner.reviews.reduce((sum, review) => sum + review.rating, 0) / partner.reviews.length;
      compatibilityFactors.ratingMatch = (avgRating / 5) * 100;

      if (criteria.minRating && avgRating < criteria.minRating) {
        compatibilityFactors.ratingMatch = 0;
      }
    } else {
      compatibilityFactors.ratingMatch = 50; // Score neutre pour les partenaires sans avis
    }

    // Calculer le score de compatibilité de disponibilité
    compatibilityFactors.availabilityMatch = 100; // Par défaut, on suppose que le partenaire est disponible

    // Calculer le score de compatibilité de budget
    compatibilityFactors.budgetMatch = 100; // Par défaut, on suppose que le budget est compatible

    // Calculer le score global (moyenne pondérée des facteurs)
    const weights = {
      skillMatch: 0.35,
      locationMatch: 0.2,
      ratingMatch: 0.2,
      availabilityMatch: 0.15,
      budgetMatch: 0.1,
    };

    const score = Math.round(
      compatibilityFactors.skillMatch * weights.skillMatch +
      compatibilityFactors.locationMatch * weights.locationMatch +
      compatibilityFactors.ratingMatch * weights.ratingMatch +
      compatibilityFactors.availabilityMatch * weights.availabilityMatch +
      compatibilityFactors.budgetMatch * weights.budgetMatch
    );

    // Créer l'objet de résultat
    const partnerMatch: PartnerMatchDto = {
      id: partner.id,
      companyName: partner.companyName,
      type: partner.type,
      category: partner.category,
      description: partner.description,
      logo: partner.logo,
      website: partner.website,
      specializations: partner.specializations,
      languages: partner.languages,
      averageRating: partner.reviews && partner.reviews.length > 0
        ? partner.reviews.reduce((sum, review) => sum + review.rating, 0) / partner.reviews.length
        : undefined,
      totalReviews: partner.reviews ? partner.reviews.length : 0,
      completedServices: partner.completedServices,
    };

    return {
      partnerId: partner.id,
      retreatId: retreat?.id,
      score,
      compatibilityFactors,
      partner: partnerMatch,
      retreat: retreat ? {
        id: retreat.id,
        title: retreat.title,
        description: retreat.description,
        startDate: retreat.startDate,
        endDate: retreat.endDate,
        location: retreat.location,
        capacity: retreat.capacity,
        price: retreat.price,
        images: retreat.images,
      } : undefined,
    };
  }

  /**
   * Calcule le score de compatibilité entre une retraite et un partenaire
   * @param retreat Retraite
   * @param partner Partenaire (optionnel)
   * @param criteria Critères de recherche
   * @returns Résultat du matching
   */
  /**
   * Récupère le score de matching entre un partenaire et une retraite
   * @param partnerId ID du partenaire
   * @param retreatId ID de la retraite
   * @returns Résultat du matching
   */
  async getMatchingScore(partnerId: string, retreatId: string): Promise<MatchingResultDto | null> {
    try {
      // Récupérer le partenaire
      const partner = await this.prisma.partner.findUnique({
        where: { id: partnerId },
        include: {
          reviews: {
            select: {
              rating: true,
            },
          },
        },
      });

      if (!partner) {
        this.logger.warn(`Partenaire avec l'ID ${partnerId} non trouvé`);
        return null;
      }

      // Récupérer la retraite
      const retreat = await this.prisma.retreat.findUnique({
        where: { id: retreatId },
        include: {
          categories: true,
          reviews: {
            select: {
              rating: true,
            },
          },
        },
      });

      if (!retreat) {
        this.logger.warn(`Retraite avec l'ID ${retreatId} non trouvée`);
        return null;
      }

      // Calculer le score de matching
      const result = this.calculateMatchingScore(partner, retreat, {});

      // Enregistrer l'événement d'analyse
      await this.analyticsService.recordAnalyticsEvent({
        eventType: 'MATCHING_SCORE_REQUESTED',
        partnerId,
        retreatId,
        score: result.score,
        timestamp: new Date(),
      });

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du score de matching: ${error.message}`, error.stack);
      return null;
    }
  }

  private calculateMatchingScoreForRetreat(
    retreat: any,
    partner: Partner | null,
    criteria: MatchingCriteriaDto,
  ): MatchingResultDto {
    // Initialiser les facteurs de compatibilité
    const compatibilityFactors: CompatibilityFactorsDto = {
      skillMatch: 0,
      availabilityMatch: 0,
      locationMatch: 0,
      ratingMatch: 0,
      budgetMatch: 0,
    };

    // Calculer le score de compatibilité des compétences
    compatibilityFactors.skillMatch = 100; // Par défaut

    if (partner && partner.specializations) {
      const retreatCategories = retreat.categories.map(cat => cat.name);
      const matchingSpecializations = partner.specializations.filter(spec =>
        retreatCategories.includes(spec)
      ).length;

      if (retreatCategories.length > 0) {
        compatibilityFactors.skillMatch = Math.min(
          100,
          (matchingSpecializations / retreatCategories.length) * 100
        );
      }
    }

    // Calculer le score de compatibilité de localisation
    if (partner && partner.coverageAreas && partner.coverageAreas.countries) {
      const retreatCountry = retreat.location.split(',').pop().trim();
      compatibilityFactors.locationMatch =
        partner.coverageAreas.countries.includes(retreatCountry) ? 100 : 0;
    } else {
      compatibilityFactors.locationMatch = 100; // Par défaut
    }

    // Calculer le score de compatibilité des évaluations
    if (retreat.reviews && retreat.reviews.length > 0) {
      const avgRating = retreat.reviews.reduce((sum, review) => sum + review.rating, 0) / retreat.reviews.length;
      compatibilityFactors.ratingMatch = (avgRating / 5) * 100;
    } else {
      compatibilityFactors.ratingMatch = 50; // Score neutre pour les retraites sans avis
    }

    // Calculer le score de compatibilité de disponibilité
    compatibilityFactors.availabilityMatch = 100; // Par défaut

    // Calculer le score de compatibilité de budget
    if (criteria.maxBudget) {
      compatibilityFactors.budgetMatch = retreat.price <= criteria.maxBudget ? 100 : 0;
    } else {
      compatibilityFactors.budgetMatch = 100; // Par défaut
    }

    // Calculer le score global (moyenne pondérée des facteurs)
    const weights = {
      skillMatch: 0.35,
      locationMatch: 0.2,
      ratingMatch: 0.15,
      availabilityMatch: 0.15,
      budgetMatch: 0.15,
    };

    const score = Math.round(
      compatibilityFactors.skillMatch * weights.skillMatch +
      compatibilityFactors.locationMatch * weights.locationMatch +
      compatibilityFactors.ratingMatch * weights.ratingMatch +
      compatibilityFactors.availabilityMatch * weights.availabilityMatch +
      compatibilityFactors.budgetMatch * weights.budgetMatch
    );

    // Créer l'objet de résultat
    const retreatMatch: RetreatMatchDto = {
      id: retreat.id,
      title: retreat.title,
      description: retreat.description,
      startDate: retreat.startDate,
      endDate: retreat.endDate,
      location: retreat.location,
      capacity: retreat.capacity,
      price: retreat.price,
      images: retreat.images,
    };

    return {
      partnerId: partner?.id,
      retreatId: retreat.id,
      score,
      compatibilityFactors,
      partner: partner ? {
        id: partner.id,
        companyName: partner.companyName,
        type: partner.type,
        category: partner.category,
        description: partner.description,
        logo: partner.logo,
        website: partner.website,
        specializations: partner.specializations,
        languages: partner.languages,
        averageRating: partner.reviews && partner.reviews.length > 0
          ? partner.reviews.reduce((sum, review) => sum + review.rating, 0) / partner.reviews.length
          : undefined,
        totalReviews: partner.reviews ? partner.reviews.length : 0,
        completedServices: partner.completedServices,
      } : undefined,
      retreat: retreatMatch,
    };
  }
}
