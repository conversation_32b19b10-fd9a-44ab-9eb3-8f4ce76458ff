import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';

@Injectable()
export class MessagingIntegrationService {
  private readonly logger = new Logger(MessagingIntegrationService.name);
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get<string>('microservices.messaging.url') || 'http://localhost:3002';
  }

  async sendMessage(data: any): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/api/messages`, data).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error sending message to messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to send message to messaging service: ${error.message}`);
      throw error;
    }
  }

  async getMessages(conversationId: string, options?: { limit?: number; before?: string }): Promise<any> {
    try {
      let url = `${this.baseUrl}/api/conversations/${conversationId}/messages`;
      
      if (options) {
        const params = new URLSearchParams();
        if (options.limit) params.append('limit', options.limit.toString());
        if (options.before) params.append('before', options.before);
        if (params.toString()) url += `?${params.toString()}`;
      }

      const { data: response } = await firstValueFrom(
        this.httpService.get(url).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error getting messages from messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to get messages from messaging service: ${error.message}`);
      throw error;
    }
  }

  async createConversation(data: any): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/api/conversations`, data).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error creating conversation in messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to create conversation in messaging service: ${error.message}`);
      throw error;
    }
  }

  async getConversation(conversationId: string): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/conversations/${conversationId}`).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error getting conversation from messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to get conversation from messaging service: ${error.message}`);
      throw error;
    }
  }

  async getUserConversations(userId: string): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/users/${userId}/conversations`).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error getting user conversations from messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to get user conversations from messaging service: ${error.message}`);
      throw error;
    }
  }

  async addParticipant(conversationId: string, userId: string): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/api/conversations/${conversationId}/participants`, { userId }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error adding participant to conversation in messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to add participant to conversation in messaging service: ${error.message}`);
      throw error;
    }
  }

  async removeParticipant(conversationId: string, userId: string): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.delete(`${this.baseUrl}/api/conversations/${conversationId}/participants/${userId}`).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error removing participant from conversation in messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to remove participant from conversation in messaging service: ${error.message}`);
      throw error;
    }
  }

  async markAsRead(conversationId: string, userId: string): Promise<any> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/api/conversations/${conversationId}/read`, { userId }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error marking conversation as read in messaging service: ${error.message}`);
            throw error;
          }),
        ),
      );
      return response;
    } catch (error) {
      this.logger.error(`Failed to mark conversation as read in messaging service: ${error.message}`);
      throw error;
    }
  }
}
