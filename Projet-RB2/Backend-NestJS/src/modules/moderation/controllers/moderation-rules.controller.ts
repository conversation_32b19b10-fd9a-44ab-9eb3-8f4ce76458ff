import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { ModerationRulesService, CreateRuleDto, UpdateRuleDto, RuleSeverity } from '../services/moderation-rules.service';
import { ContentType } from '@prisma/client';

@ApiTags('moderation-rules')
@Controller('moderation/rules')
export class ModerationRulesController {
  private readonly logger = new Logger(ModerationRulesController.name);

  constructor(private readonly moderationRulesService: ModerationRulesService) {}

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all moderation rules' })
  @ApiQuery({ name: 'contentType', enum: ContentType, required: false })
  @ApiQuery({ name: 'activeOnly', type: Boolean, required: false })
  @ApiResponse({
    status: 200,
    description: 'Returns all moderation rules',
  })
  async getAllRules(
    @Query('contentType') contentType?: ContentType,
    @Query('activeOnly') activeOnly?: boolean,
  ) {
    this.logger.debug(`Getting all moderation rules for content type: ${contentType || 'all'}`);
    return this.moderationRulesService.getAllRules(
      contentType,
      activeOnly === 'true' || activeOnly === true,
    );
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get moderation rule by ID' })
  @ApiParam({ name: 'id', description: 'Rule ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the moderation rule',
  })
  @ApiResponse({
    status: 404,
    description: 'Rule not found',
  })
  async getRuleById(@Param('id') id: string) {
    this.logger.debug(`Getting moderation rule with ID: ${id}`);
    return this.moderationRulesService.getRuleById(id);
  }

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new moderation rule' })
  @ApiResponse({
    status: 201,
    description: 'Rule created successfully',
  })
  @ApiResponse({
    status: 409,
    description: 'Rule with the same name already exists',
  })
  async createRule(
    @Body() createRuleDto: CreateRuleDto,
    @CurrentUser('id') userId: string,
  ) {
    this.logger.debug(`Creating new moderation rule: ${createRuleDto.name}`);
    return this.moderationRulesService.createRule(createRuleDto, userId);
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a moderation rule' })
  @ApiParam({ name: 'id', description: 'Rule ID' })
  @ApiResponse({
    status: 200,
    description: 'Rule updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Rule not found',
  })
  async updateRule(
    @Param('id') id: string,
    @Body() updateRuleDto: UpdateRuleDto,
    @CurrentUser('id') userId: string,
  ) {
    this.logger.debug(`Updating moderation rule with ID: ${id}`);
    return this.moderationRulesService.updateRule(id, updateRuleDto, userId);
  }

  @Put(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Toggle moderation rule status' })
  @ApiParam({ name: 'id', description: 'Rule ID' })
  @ApiResponse({
    status: 200,
    description: 'Rule status updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Rule not found',
  })
  async toggleRuleStatus(
    @Param('id') id: string,
    @Body() body: { isActive: boolean },
    @CurrentUser('id') userId: string,
  ) {
    this.logger.debug(`${body.isActive ? 'Activating' : 'Deactivating'} moderation rule with ID: ${id}`);
    return this.moderationRulesService.toggleRuleStatus(id, body.isActive, userId);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a moderation rule' })
  @ApiParam({ name: 'id', description: 'Rule ID' })
  @ApiResponse({
    status: 200,
    description: 'Rule deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Rule not found',
  })
  async deleteRule(
    @Param('id') id: string,
    @CurrentUser('id') userId: string,
  ) {
    this.logger.debug(`Deleting moderation rule with ID: ${id}`);
    return this.moderationRulesService.deleteRule(id, userId);
  }

  @Post('test')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Test a moderation rule against content' })
  @ApiResponse({
    status: 200,
    description: 'Returns test results',
  })
  @ApiResponse({
    status: 404,
    description: 'Rule not found',
  })
  async testRule(
    @Body() body: { ruleId: string; content: string },
  ) {
    this.logger.debug(`Testing moderation rule with ID: ${body.ruleId}`);
    return this.moderationRulesService.testRule(body.ruleId, body.content);
  }
}
