import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Logger,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { ReputationService, ReputationAction } from '../services/reputation.service';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';

@ApiTags('moderation-reputation')
@Controller('moderation/reputation')
export class ReputationController {
  private readonly logger = new Logger(ReputationController.name);

  constructor(private readonly reputationService: ReputationService) {}

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user reputation' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns user reputation information',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async getUserReputation(@Param('userId') userId: string) {
    this.logger.debug(`Getting reputation for user: ${userId}`);
    try {
      return await this.reputationService.getUserReputation(userId);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting user reputation: ${error.message}`);
      throw error;
    }
  }

  @Get('my-reputation')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user reputation' })
  @ApiResponse({
    status: 200,
    description: 'Returns current user reputation information',
  })
  async getMyReputation(@CurrentUser('id') userId: string) {
    this.logger.debug(`Getting reputation for current user: ${userId}`);
    return await this.reputationService.getUserReputation(userId);
  }

  @Get('privileges')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all available reputation privileges' })
  @ApiResponse({
    status: 200,
    description: 'Returns all available reputation privileges',
  })
  async getAllPrivileges() {
    this.logger.debug('Getting all reputation privileges');
    return this.reputationService.getAllPrivileges();
  }

  @Get('user/:userId/has-privilege/:privilegeId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Check if user has a specific privilege' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiParam({ name: 'privilegeId', description: 'Privilege ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the user has the specified privilege',
  })
  async hasPrivilege(
    @Param('userId') userId: string,
    @Param('privilegeId') privilegeId: string,
  ) {
    this.logger.debug(`Checking if user ${userId} has privilege ${privilegeId}`);
    return {
      userId,
      privilegeId,
      hasPrivilege: await this.reputationService.hasPrivilege(userId, privilegeId),
    };
  }

  @Get('user/:userId/is-trusted')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Check if user is trusted' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the user is trusted',
  })
  async isTrustedUser(@Param('userId') userId: string) {
    this.logger.debug(`Checking if user ${userId} is trusted`);
    return {
      userId,
      isTrusted: await this.reputationService.isTrustedUser(userId),
    };
  }

  @Post('user/:userId/update')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Manually update user reputation' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns updated user reputation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden action',
  })
  async updateUserReputation(
    @Param('userId') userId: string,
    @Body() body: { action: ReputationAction; contentId?: string; reportId?: string },
    @CurrentUser('id') adminId: string,
  ) {
    this.logger.debug(`Manually updating reputation for user ${userId} with action ${body.action}`);
    
    // Vérifier que l'action est valide
    if (!Object.values(ReputationAction).includes(body.action)) {
      throw new ForbiddenException(`Invalid reputation action: ${body.action}`);
    }
    
    return await this.reputationService.updateReputation({
      userId,
      action: body.action,
      contentId: body.contentId,
      reportId: body.reportId,
      moderatorId: adminId,
    });
  }
}
