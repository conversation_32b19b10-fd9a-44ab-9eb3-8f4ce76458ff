import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TextModerationService } from './services/text-moderation.service';
import { ImageModerationService } from './services/image-moderation.service';
import { ReportService } from './services/report.service';
import { NotificationService } from './services/notification.service';
import { ReputationService, ReputationAction } from './services/reputation.service';
import { ModerationLearningService } from './services/moderation-learning.service';
import { ModerationRulesService } from './services/moderation-rules.service';
import { ContentType, ReportStatus, ActionType } from '@prisma/client';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class ModerationService {
  private readonly logger = new Logger(ModerationService.name);
  private readonly useTrustedModeration: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly textModerationService: TextModerationService,
    private readonly imageModerationService: ImageModerationService,
    private readonly reportService: ReportService,
    private readonly notificationService: NotificationService,
    private readonly reputationService: ReputationService,
    private readonly moderationLearningService: ModerationLearningService,
    private readonly moderationRulesService: ModerationRulesService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.useTrustedModeration = this.configService.get<boolean>('MODERATION_USE_TRUSTED_USERS', true);
  }

  async moderateContent(content: any, contentType: ContentType, userId?: string) {
    this.logger.debug(`Moderating content of type: ${contentType}`);

    let moderationResult;
    let isTrustedUser = false;

    // Vérifier si l'utilisateur est de confiance pour la modération
    if (userId && this.useTrustedModeration) {
      isTrustedUser = await this.reputationService.isTrustedUser(userId);
    }

    // Récupérer les règles de modération actives
    const activeRules = await this.moderationRulesService.getAllRules(contentType, true);

    switch (contentType) {
      case ContentType.TEXT:
      case ContentType.COMMENT:
      case ContentType.ARTICLE:
        moderationResult = await this.textModerationService.moderateText(content.text);
        break;
      case ContentType.IMAGE:
      case ContentType.VIDEO:
        moderationResult = await this.imageModerationService.moderateImage({
          imageUrl: content.imageUrl,
          base64Image: content.base64Image,
        });
        break;
      default:
        this.logger.warn(`Unsupported content type for moderation: ${contentType}`);
        return {
          isInappropriate: false,
          severity: null,
          matchedRules: [],
          confidence: 0,
        };
    }

    // Si l'utilisateur est de confiance et que le contenu est jugé inapproprié avec une confiance faible,
    // on peut considérer le contenu comme approprié
    if (isTrustedUser && moderationResult.isInappropriate && moderationResult.confidence < 0.85) {
      this.logger.debug(`Trusted user ${userId} content overriding AI moderation decision`);

      // Enregistrer cette action pour l'apprentissage continu
      await this.moderationLearningService.addModerationFeedback({
        contentId: content.id || 'unknown',
        contentType,
        moderationResult: false, // Le contenu est considéré comme approprié
        moderatorId: userId,
        confidence: 0.9, // Haute confiance dans la décision de l'utilisateur de confiance
      });

      // Mettre à jour le résultat
      moderationResult.isInappropriate = false;
      moderationResult.confidence = 0.9;
      moderationResult.trustedOverride = true;

      // Mettre à jour la réputation de l'utilisateur
      await this.reputationService.updateReputation({
        userId,
        action: ReputationAction.TRUSTED_MODERATION,
        contentId: content.id || 'unknown',
      });
    }

    // Si le contenu est inapproprié, mettre à jour la réputation de l'utilisateur
    if (moderationResult.isInappropriate && userId) {
      await this.reputationService.updateReputation({
        userId,
        action: ReputationAction.CONTENT_FLAGGED,
        contentId: content.id || 'unknown',
      });

      // Émettre un événement pour notifier du contenu inapproprié
      this.eventEmitter.emit('content.flagged', {
        contentId: content.id || 'unknown',
        contentType,
        userId,
        result: moderationResult,
      });
    }

    return moderationResult;
  }

  async getReports(filters: any) {
    return this.reportService.findAll(filters);
  }

  async getReport(id: string) {
    return this.reportService.findById(id);
  }

  async createReport(reportData: any) {
    const report = await this.reportService.create(reportData);

    // Send notification about new report
    await this.notificationService.notifyNewReport(report.id, report.contentType);

    // Mettre à jour la réputation du rapporteur
    if (reportData.reporterId) {
      await this.reputationService.updateReputation({
        userId: reportData.reporterId,
        action: ReputationAction.REPORT_CREATED,
        reportId: report.id,
        contentId: reportData.contentId,
      });
    }

    // Si le rapporteur est un utilisateur de confiance, traiter le rapport en priorité
    if (reportData.reporterId) {
      const isTrusted = await this.reputationService.isTrustedUser(reportData.reporterId);

      if (isTrusted) {
        // Mettre à jour le rapport pour indiquer qu'il provient d'un utilisateur de confiance
        await this.reportService.update(report.id, {
          priority: 'HIGH',
          metadata: {
            ...report.metadata,
            fromTrustedUser: true,
          },
        });

        // Émettre un événement pour notifier du rapport prioritaire
        this.eventEmitter.emit('report.trusted_user_report', {
          reportId: report.id,
          reporterId: reportData.reporterId,
          contentId: reportData.contentId,
          contentType: report.contentType,
        });
      }
    }

    return report;
  }

  async updateReport(id: string, updateData: any) {
    return this.reportService.update(id, updateData);
  }

  async addModerationAction(reportId: string, actionData: any) {
    const action = await this.reportService.addModerationAction(reportId, actionData);

    // Get the report to access its content type and status
    const report = await this.reportService.findById(reportId);

    // Send notification based on the action type
    if (actionData.action === ActionType.ASSIGN) {
      await this.notificationService.notifyReportAssigned(
        reportId,
        report.contentType,
        actionData.moderatorId,
      );
    } else if (actionData.action === ActionType.ESCALATE) {
      await this.notificationService.notifyReportEscalated(
        reportId,
        report.contentType,
        actionData.moderatorId,
      );
    } else {
      // For other actions (APPROVE, REJECT, etc.)
      await this.notificationService.notifyReportUpdated(
        reportId,
        report.contentType,
        report.status,
      );
    }

    // Mettre à jour la réputation du rapporteur en fonction de l'action
    if (report.reporterId) {
      if (actionData.action === ActionType.APPROVE) {
        // Le signalement était valide, augmenter la réputation du rapporteur
        await this.reputationService.updateReputation({
          userId: report.reporterId,
          action: ReputationAction.ACCURATE_REPORT,
          reportId,
          contentId: report.contentId,
        });
      } else if (actionData.action === ActionType.REJECT) {
        // Le signalement était invalide, diminuer la réputation du rapporteur
        await this.reputationService.updateReputation({
          userId: report.reporterId,
          action: ReputationAction.FALSE_REPORT,
          reportId,
          contentId: report.contentId,
        });
      }
    }

    // Ajouter le feedback pour l'apprentissage continu
    if (actionData.action === ActionType.APPROVE || actionData.action === ActionType.REJECT) {
      await this.moderationLearningService.addModerationFeedback({
        contentId: report.contentId,
        contentType: report.contentType,
        moderationResult: actionData.action === ActionType.APPROVE,
        moderatorId: actionData.moderatorId,
        confidence: 1.0, // Confiance maximale pour les décisions humaines
        reportId,
      });
    }

    // Émettre un événement pour notifier de l'action de modération
    this.eventEmitter.emit('moderation.action_added', {
      reportId,
      action: actionData.action,
      moderatorId: actionData.moderatorId,
      contentId: report.contentId,
      contentType: report.contentType,
    });

    return action;
  }

  async getReportStats() {
    return this.reportService.getReportStats();
  }
}
