import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ReportService } from './report.service';
import { FilterReportsDto } from '../dto/filter-reports.dto';
import { ReportStatus, ContentType, ActionType } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly reportService: ReportService,
    private readonly configService: ConfigService,
  ) {}

  async getStats() {
    this.logger.debug('Getting moderation dashboard statistics');
    
    // Get basic report statistics
    const reportStats = await this.reportService.getReportStats();
    
    // Get content type distribution
    const contentTypeDistribution = await this.getContentTypeDistribution();
    
    // Get reports over time (last 30 days)
    const reportsOverTime = await this.getReportsOverTime(30);
    
    // Get average resolution time
    const averageResolutionTime = await this.getAverageResolutionTime();
    
    return {
      reportStats,
      contentTypeDistribution,
      reportsOverTime,
      averageResolutionTime,
    };
  }

  async getFilteredReports(filterDto: FilterReportsDto) {
    this.logger.debug(`Getting filtered reports: ${JSON.stringify(filterDto)}`);
    
    const {
      status,
      contentType,
      startDate,
      endDate,
      reporterId,
      skip,
      take,
      sortBy,
      sortOrder,
    } = filterDto;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (contentType) {
      where.contentType = contentType;
    }
    
    if (reporterId) {
      where.reporterId = reporterId;
    }
    
    if (startDate || endDate) {
      where.createdAt = {};
      
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }
    
    const orderBy: any = {};
    if (sortBy) {
      orderBy[sortBy] = sortOrder || 'desc';
    } else {
      orderBy.createdAt = 'desc';
    }
    
    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where,
        skip: skip ? Number(skip) : 0,
        take: take ? Number(take) : 10,
        orderBy,
        include: {
          moderationActions: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 1,
          },
        },
      }),
      this.prisma.report.count({ where }),
    ]);
    
    return { reports, total };
  }

  async getPendingReports() {
    this.logger.debug('Getting pending reports');
    
    const reports = await this.prisma.report.findMany({
      where: {
        status: ReportStatus.PENDING,
      },
      orderBy: {
        createdAt: 'asc', // Oldest first
      },
      take: 10,
      include: {
        moderationActions: true,
      },
    });
    
    return reports;
  }

  async getReportsByModerator(moderatorId: string) {
    this.logger.debug(`Getting reports handled by moderator: ${moderatorId}`);
    
    // Find all reports that have moderation actions by this moderator
    const reportsWithActions = await this.prisma.report.findMany({
      where: {
        moderationActions: {
          some: {
            moderatorId,
          },
        },
      },
      include: {
        moderationActions: {
          where: {
            moderatorId,
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });
    
    return reportsWithActions;
  }

  async getPerformanceMetrics(startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting performance metrics from ${startDate} to ${endDate}`);
    
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = startDate;
    }
    if (endDate) {
      dateFilter.lte = endDate;
    }
    
    const where = Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {};
    
    // Total reports in the period
    const totalReports = await this.prisma.report.count({ where });
    
    // Reports by status
    const reportsByStatus = await this.getReportsByStatus(where);
    
    // Average time to first action
    const avgTimeToFirstAction = await this.getAverageTimeToFirstAction(dateFilter);
    
    // Average resolution time
    const avgResolutionTime = await this.getAverageResolutionTime(dateFilter);
    
    // Reports by day
    const reportsByDay = await this.getReportsByDay(startDate, endDate);
    
    return {
      totalReports,
      reportsByStatus,
      avgTimeToFirstAction,
      avgResolutionTime,
      reportsByDay,
    };
  }

  async getModeratorsPerformance(startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting moderators performance from ${startDate} to ${endDate}`);
    
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = startDate;
    }
    if (endDate) {
      dateFilter.lte = endDate;
    }
    
    // Get all moderation actions in the period
    const moderationActions = await this.prisma.moderationAction.findMany({
      where: dateFilter.length > 0 ? { createdAt: dateFilter } : {},
      include: {
        report: true,
      },
    });
    
    // Group actions by moderator
    const moderatorStats = {};
    
    for (const action of moderationActions) {
      if (!moderatorStats[action.moderatorId]) {
        moderatorStats[action.moderatorId] = {
          totalActions: 0,
          actionsByType: {},
          averageResponseTime: 0,
          totalResponseTime: 0,
        };
      }
      
      // Increment total actions
      moderatorStats[action.moderatorId].totalActions++;
      
      // Increment actions by type
      if (!moderatorStats[action.moderatorId].actionsByType[action.action]) {
        moderatorStats[action.moderatorId].actionsByType[action.action] = 0;
      }
      moderatorStats[action.moderatorId].actionsByType[action.action]++;
      
      // Calculate response time (time between report creation and action)
      const responseTime = action.createdAt.getTime() - action.report.createdAt.getTime();
      moderatorStats[action.moderatorId].totalResponseTime += responseTime;
    }
    
    // Calculate average response time for each moderator
    for (const moderatorId in moderatorStats) {
      if (moderatorStats[moderatorId].totalActions > 0) {
        moderatorStats[moderatorId].averageResponseTime = 
          moderatorStats[moderatorId].totalResponseTime / moderatorStats[moderatorId].totalActions;
      }
    }
    
    return moderatorStats;
  }

  async updateNotificationSettings(settings: {
    moderatorId: string;
    emailNotifications: boolean;
    pushNotifications: boolean;
    reportTypes: string[];
  }) {
    this.logger.debug(`Updating notification settings for moderator: ${settings.moderatorId}`);
    
    // In a real implementation, this would update the moderator's notification settings in the database
    // For now, we'll just return the settings
    return {
      success: true,
      settings,
    };
  }

  // Helper methods
  private async getContentTypeDistribution() {
    const contentTypes = Object.values(ContentType);
    const distribution = await Promise.all(
      contentTypes.map(async (type) => {
        const count = await this.prisma.report.count({
          where: { contentType: type },
        });
        return { contentType: type, count };
      }),
    );
    
    return distribution;
  }

  private async getReportsOverTime(days: number) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const reports = await this.prisma.report.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        createdAt: true,
        status: true,
      },
    });
    
    // Group by day
    const reportsByDay = {};
    
    for (const report of reports) {
      const day = report.createdAt.toISOString().split('T')[0];
      
      if (!reportsByDay[day]) {
        reportsByDay[day] = {
          date: day,
          total: 0,
          [ReportStatus.PENDING]: 0,
          [ReportStatus.IN_REVIEW]: 0,
          [ReportStatus.APPROVED]: 0,
          [ReportStatus.REJECTED]: 0,
          [ReportStatus.ESCALATED]: 0,
        };
      }
      
      reportsByDay[day].total++;
      reportsByDay[day][report.status]++;
    }
    
    return Object.values(reportsByDay);
  }

  private async getAverageResolutionTime(dateFilter?: any) {
    const resolvedReports = await this.prisma.report.findMany({
      where: {
        status: {
          in: [ReportStatus.APPROVED, ReportStatus.REJECTED],
        },
        ...(dateFilter && { createdAt: dateFilter }),
      },
      include: {
        moderationActions: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });
    
    if (resolvedReports.length === 0) {
      return 0;
    }
    
    let totalResolutionTime = 0;
    let countWithActions = 0;
    
    for (const report of resolvedReports) {
      if (report.moderationActions.length > 0) {
        const resolutionTime = report.moderationActions[0].createdAt.getTime() - report.createdAt.getTime();
        totalResolutionTime += resolutionTime;
        countWithActions++;
      }
    }
    
    return countWithActions > 0 ? totalResolutionTime / countWithActions : 0;
  }

  private async getReportsByStatus(where: any) {
    const statuses = Object.values(ReportStatus);
    const reportsByStatus = {};
    
    for (const status of statuses) {
      const count = await this.prisma.report.count({
        where: {
          ...where,
          status,
        },
      });
      
      reportsByStatus[status] = count;
    }
    
    return reportsByStatus;
  }

  private async getAverageTimeToFirstAction(dateFilter: any) {
    const reportsWithActions = await this.prisma.report.findMany({
      where: {
        moderationActions: {
          some: {},
        },
        ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter }),
      },
      include: {
        moderationActions: {
          orderBy: {
            createdAt: 'asc',
          },
          take: 1,
        },
      },
    });
    
    if (reportsWithActions.length === 0) {
      return 0;
    }
    
    let totalTimeToFirstAction = 0;
    
    for (const report of reportsWithActions) {
      if (report.moderationActions.length > 0) {
        const timeToFirstAction = report.moderationActions[0].createdAt.getTime() - report.createdAt.getTime();
        totalTimeToFirstAction += timeToFirstAction;
      }
    }
    
    return totalTimeToFirstAction / reportsWithActions.length;
  }

  private async getReportsByDay(startDate?: Date, endDate?: Date) {
    const start = startDate || new Date(new Date().setDate(new Date().getDate() - 30));
    const end = endDate || new Date();
    
    const reports = await this.prisma.report.findMany({
      where: {
        createdAt: {
          gte: start,
          lte: end,
        },
      },
      select: {
        createdAt: true,
        status: true,
      },
    });
    
    // Group by day
    const reportsByDay = {};
    
    for (const report of reports) {
      const day = report.createdAt.toISOString().split('T')[0];
      
      if (!reportsByDay[day]) {
        reportsByDay[day] = {
          date: day,
          count: 0,
        };
      }
      
      reportsByDay[day].count++;
    }
    
    // Convert to array and sort by date
    return Object.values(reportsByDay).sort((a: any, b: any) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  }
}
