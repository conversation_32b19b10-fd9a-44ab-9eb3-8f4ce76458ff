import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ReportStatus, ContentType, ActionType } from '@prisma/client';
import { TextModerationService } from './text-moderation.service';
import { ImageModerationService } from './image-moderation.service';
import axios from 'axios';

interface ModerationFeedback {
  contentId: string;
  contentType: ContentType;
  moderationResult: boolean;
  moderatorId: string;
  confidence: number;
  reportId?: string;
}

interface TrainingData {
  contentType: ContentType;
  content: string;
  isInappropriate: boolean;
  confidence: number;
  metadata: any;
}

interface ModelMetrics {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  falsePositives: number;
  falseNegatives: number;
  truePositives: number;
  trueNegatives: number;
}

@Injectable()
export class ModerationLearningService {
  private readonly logger = new Logger(ModerationLearningService.name);
  private readonly agentIAUrl: string;
  private readonly trainingEnabled: boolean;
  private readonly trainingThreshold: number;
  private readonly trainingBatchSize: number;
  private readonly feedbackQueue: ModerationFeedback[] = [];
  private readonly trainingData: Map<ContentType, TrainingData[]> = new Map();
  private modelMetrics: Map<ContentType, ModelMetrics> = new Map();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly textModerationService: TextModerationService,
    private readonly imageModerationService: ImageModerationService,
  ) {
    this.agentIAUrl = this.configService.get<string>('AGENT_IA_URL', 'http://localhost:3030');
    this.trainingEnabled = this.configService.get<boolean>('MODERATION_TRAINING_ENABLED', true);
    this.trainingThreshold = this.configService.get<number>('MODERATION_TRAINING_THRESHOLD', 50);
    this.trainingBatchSize = this.configService.get<number>('MODERATION_TRAINING_BATCH_SIZE', 100);
    
    // Initialiser les collections de données d'entraînement
    this.trainingData.set(ContentType.TEXT, []);
    this.trainingData.set(ContentType.IMAGE, []);
    this.trainingData.set(ContentType.VIDEO, []);
    this.trainingData.set(ContentType.COMMENT, []);
    this.trainingData.set(ContentType.POST, []);
    
    // Initialiser les métriques des modèles
    this.initializeModelMetrics();
  }

  private initializeModelMetrics() {
    const defaultMetrics: ModelMetrics = {
      accuracy: 0,
      precision: 0,
      recall: 0,
      f1Score: 0,
      falsePositives: 0,
      falseNegatives: 0,
      truePositives: 0,
      trueNegatives: 0,
    };
    
    this.modelMetrics.set(ContentType.TEXT, { ...defaultMetrics });
    this.modelMetrics.set(ContentType.IMAGE, { ...defaultMetrics });
    this.modelMetrics.set(ContentType.VIDEO, { ...defaultMetrics });
    this.modelMetrics.set(ContentType.COMMENT, { ...defaultMetrics });
    this.modelMetrics.set(ContentType.POST, { ...defaultMetrics });
  }

  /**
   * Ajoute un feedback de modération à la file d'attente
   * @param feedback Feedback de modération
   */
  async addModerationFeedback(feedback: ModerationFeedback) {
    this.logger.debug(`Adding moderation feedback for content ${feedback.contentId}`);
    
    // Ajouter le feedback à la file d'attente
    this.feedbackQueue.push(feedback);
    
    // Si la file d'attente atteint le seuil, traiter les feedbacks
    if (this.feedbackQueue.length >= this.trainingThreshold && this.trainingEnabled) {
      await this.processFeedbackQueue();
    }
  }

  /**
   * Traite la file d'attente des feedbacks
   */
  @Cron(CronExpression.EVERY_HOUR)
  async processFeedbackQueue() {
    if (!this.trainingEnabled || this.feedbackQueue.length === 0) {
      return;
    }
    
    this.logger.debug(`Processing ${this.feedbackQueue.length} moderation feedbacks`);
    
    try {
      // Traiter les feedbacks par type de contenu
      const feedbacksByType = this.groupFeedbacksByType();
      
      for (const [contentType, feedbacks] of feedbacksByType.entries()) {
        if (feedbacks.length === 0) continue;
        
        // Collecter les données d'entraînement
        await this.collectTrainingData(contentType, feedbacks);
        
        // Si suffisamment de données sont collectées, entraîner le modèle
        const trainingDataForType = this.trainingData.get(contentType) || [];
        if (trainingDataForType.length >= this.trainingBatchSize) {
          await this.trainModel(contentType, trainingDataForType);
          
          // Vider les données d'entraînement après l'entraînement
          this.trainingData.set(contentType, []);
        }
      }
      
      // Vider la file d'attente
      this.feedbackQueue.length = 0;
      
      this.logger.debug('Feedback queue processed successfully');
    } catch (error) {
      this.logger.error(`Error processing feedback queue: ${error.message}`);
    }
  }

  /**
   * Groupe les feedbacks par type de contenu
   */
  private groupFeedbacksByType(): Map<ContentType, ModerationFeedback[]> {
    const result = new Map<ContentType, ModerationFeedback[]>();
    
    for (const feedback of this.feedbackQueue) {
      if (!result.has(feedback.contentType)) {
        result.set(feedback.contentType, []);
      }
      
      result.get(feedback.contentType)?.push(feedback);
    }
    
    return result;
  }

  /**
   * Collecte les données d'entraînement à partir des feedbacks
   * @param contentType Type de contenu
   * @param feedbacks Feedbacks de modération
   */
  private async collectTrainingData(contentType: ContentType, feedbacks: ModerationFeedback[]) {
    this.logger.debug(`Collecting training data for ${contentType}`);
    
    for (const feedback of feedbacks) {
      try {
        // Récupérer le contenu depuis la base de données
        let content: string = '';
        let metadata: any = {};
        
        switch (contentType) {
          case ContentType.TEXT:
          case ContentType.COMMENT:
          case ContentType.POST:
            const textContent = await this.prisma.content.findUnique({
              where: { id: feedback.contentId },
              select: { text: true, metadata: true },
            });
            
            if (textContent) {
              content = textContent.text || '';
              metadata = textContent.metadata || {};
            }
            break;
            
          case ContentType.IMAGE:
            const imageContent = await this.prisma.content.findUnique({
              where: { id: feedback.contentId },
              select: { imageUrl: true, metadata: true },
            });
            
            if (imageContent) {
              content = imageContent.imageUrl || '';
              metadata = imageContent.metadata || {};
            }
            break;
            
          case ContentType.VIDEO:
            const videoContent = await this.prisma.content.findUnique({
              where: { id: feedback.contentId },
              select: { videoUrl: true, metadata: true },
            });
            
            if (videoContent) {
              content = videoContent.videoUrl || '';
              metadata = videoContent.metadata || {};
            }
            break;
        }
        
        if (!content) {
          this.logger.warn(`No content found for ${contentType} with ID ${feedback.contentId}`);
          continue;
        }
        
        // Ajouter aux données d'entraînement
        const trainingDataForType = this.trainingData.get(contentType) || [];
        trainingDataForType.push({
          contentType,
          content,
          isInappropriate: feedback.moderationResult,
          confidence: feedback.confidence,
          metadata,
        });
        
        this.trainingData.set(contentType, trainingDataForType);
        
        // Mettre à jour les métriques du modèle
        this.updateModelMetrics(contentType, feedback);
      } catch (error) {
        this.logger.error(`Error collecting training data for ${contentType}: ${error.message}`);
      }
    }
  }

  /**
   * Met à jour les métriques du modèle
   * @param contentType Type de contenu
   * @param feedback Feedback de modération
   */
  private updateModelMetrics(contentType: ContentType, feedback: ModerationFeedback) {
    const metrics = this.modelMetrics.get(contentType) || {
      accuracy: 0,
      precision: 0,
      recall: 0,
      f1Score: 0,
      falsePositives: 0,
      falseNegatives: 0,
      truePositives: 0,
      trueNegatives: 0,
    };
    
    // Déterminer si c'est un vrai positif, faux positif, etc.
    const aiPrediction = feedback.confidence >= 0.7; // Seuil de confiance pour l'IA
    const humanDecision = feedback.moderationResult;
    
    if (aiPrediction && humanDecision) {
      // Vrai positif
      metrics.truePositives++;
    } else if (aiPrediction && !humanDecision) {
      // Faux positif
      metrics.falsePositives++;
    } else if (!aiPrediction && humanDecision) {
      // Faux négatif
      metrics.falseNegatives++;
    } else {
      // Vrai négatif
      metrics.trueNegatives++;
    }
    
    // Calculer les métriques
    const total = metrics.truePositives + metrics.trueNegatives + metrics.falsePositives + metrics.falseNegatives;
    
    if (total > 0) {
      metrics.accuracy = (metrics.truePositives + metrics.trueNegatives) / total;
    }
    
    if (metrics.truePositives + metrics.falsePositives > 0) {
      metrics.precision = metrics.truePositives / (metrics.truePositives + metrics.falsePositives);
    }
    
    if (metrics.truePositives + metrics.falseNegatives > 0) {
      metrics.recall = metrics.truePositives / (metrics.truePositives + metrics.falseNegatives);
    }
    
    if (metrics.precision + metrics.recall > 0) {
      metrics.f1Score = 2 * (metrics.precision * metrics.recall) / (metrics.precision + metrics.recall);
    }
    
    this.modelMetrics.set(contentType, metrics);
  }

  /**
   * Entraîne le modèle avec les données collectées
   * @param contentType Type de contenu
   * @param trainingData Données d'entraînement
   */
  private async trainModel(contentType: ContentType, trainingData: TrainingData[]) {
    if (!this.trainingEnabled || trainingData.length === 0) {
      return;
    }
    
    this.logger.debug(`Training model for ${contentType} with ${trainingData.length} samples`);
    
    try {
      // Envoyer les données d'entraînement au microservice Agent IA
      const response = await axios.post(`${this.agentIAUrl}/api/moderation/train`, {
        contentType,
        trainingData,
      });
      
      if (response.status === 200) {
        this.logger.debug(`Model for ${contentType} trained successfully`);
        
        // Émettre un événement pour notifier de l'entraînement réussi
        this.eventEmitter.emit('moderation.model_trained', {
          contentType,
          samplesCount: trainingData.length,
          metrics: this.modelMetrics.get(contentType),
        });
      } else {
        this.logger.error(`Error training model for ${contentType}: ${response.statusText}`);
      }
    } catch (error) {
      this.logger.error(`Error training model for ${contentType}: ${error.message}`);
    }
  }

  /**
   * Récupère les métriques du modèle
   * @param contentType Type de contenu
   * @returns Métriques du modèle
   */
  getModelMetrics(contentType?: ContentType) {
    if (contentType) {
      return this.modelMetrics.get(contentType);
    }
    
    // Retourner toutes les métriques
    const allMetrics: Record<string, ModelMetrics> = {};
    
    for (const [type, metrics] of this.modelMetrics.entries()) {
      allMetrics[type] = metrics;
    }
    
    return allMetrics;
  }

  /**
   * Gestionnaire d'événement pour les actions de modération
   */
  @OnEvent('moderation.action_added')
  async handleModerationAction(payload: {
    reportId: string;
    action: ActionType;
    moderatorId: string;
    contentId: string;
    contentType: ContentType;
  }) {
    this.logger.debug(`Handling moderation action for report ${payload.reportId}`);
    
    // Déterminer si le contenu est inapproprié en fonction de l'action
    const isInappropriate = payload.action === ActionType.APPROVE;
    
    // Ajouter le feedback à la file d'attente
    await this.addModerationFeedback({
      contentId: payload.contentId,
      contentType: payload.contentType,
      moderationResult: isInappropriate,
      moderatorId: payload.moderatorId,
      confidence: 1.0, // Confiance maximale pour les décisions humaines
      reportId: payload.reportId,
    });
  }
}
