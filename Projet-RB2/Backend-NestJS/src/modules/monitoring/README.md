# Système de Monitoring et d'Alertes

Ce module fournit un système complet de monitoring et d'alertes pour l'application Retreat And Be.

## Vue d'ensemble

Le système de monitoring permet de :

1. **Collecter des métriques** sur les performances et l'utilisation de l'application
2. **Détecter des anomalies** dans le fonctionnement de l'application
3. **Générer des alertes** en cas de problème
4. **Visualiser les métriques** via Prometheus et Grafana
5. **Analyser les tendances** pour anticiper les problèmes

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Application    │────▶│  Prometheus     │────▶│  Grafana        │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                                               │
        │                                               │
        ▼                                               ▼
┌─────────────────┐                           ┌─────────────────┐
│                 │                           │                 │
│  Base de        │                           │  Système        │
│  données        │                           │  d'alertes      │
│                 │                           │                 │
└─────────────────┘                           └─────────────────┘
```

## Composants

### PrometheusService

Service pour l'intégration avec Prometheus, permettant de collecter et d'exposer des métriques.

```typescript
// Exemple d'utilisation
prometheusService.incrementCounter('recommendation_requests_total', { type: 'RETREAT', strategy: 'HYBRID' });
prometheusService.observeHistogram('recommendation_response_time_seconds', responseTime, { type: 'RETREAT' });
```

### AlertService

Service pour l'envoi d'alertes via différents canaux (Slack, email, SMS, base de données).

```typescript
// Exemple d'utilisation
alertService.sendAlert({
  level: 'warning',
  source: 'recommendation',
  title: 'Temps de réponse élevé',
  message: 'Le temps de réponse pour les recommandations est supérieur à 500ms.',
  metadata: { responseTime: 750, threshold: 500 },
});
```

### RecommendationMonitoringService

Service spécifique pour le monitoring du système de recommandation.

```typescript
// Exemple d'utilisation
recommendationMonitoringService.recordRecommendationRequest(
  'RETREAT',
  'HYBRID',
  'recommendations',
  150, // temps de réponse en ms
);
```

## Métriques collectées

### Métriques générales

- **http_requests_total** : Nombre total de requêtes HTTP
- **http_request_duration_seconds** : Durée des requêtes HTTP
- **http_errors_total** : Nombre total d'erreurs HTTP
- **nodejs_memory_usage_bytes** : Utilisation de la mémoire par Node.js
- **nodejs_eventloop_lag_seconds** : Latence de l'event loop Node.js

### Métriques de recommandation

- **recommendation_requests_total** : Nombre total de requêtes de recommandation
- **recommendation_errors_total** : Nombre total d'erreurs de recommandation
- **recommendation_response_time_seconds** : Temps de réponse des requêtes de recommandation
- **recommendation_cache_hit_rate** : Taux de hit du cache pour les recommandations
- **recommendation_quality** : Qualité des recommandations basée sur les interactions utilisateur
- **recommendation_interactions_total** : Nombre total d'interactions utilisateur avec les recommandations

## Alertes

### Niveaux d'alerte

- **info** : Information non critique
- **warning** : Avertissement nécessitant une attention
- **error** : Erreur nécessitant une intervention
- **critical** : Erreur critique nécessitant une intervention immédiate

### Canaux d'alerte

- **Slack** : Envoi d'alertes via Slack
- **Email** : Envoi d'alertes par email
- **SMS** : Envoi d'alertes par SMS
- **Base de données** : Enregistrement des alertes dans la base de données

### Seuils d'alerte pour les recommandations

- **Temps de réponse** : Alerte si > 500ms (configurable)
- **Taux d'erreur** : Alerte si > 5% (configurable)
- **Taux de hit du cache** : Alerte si < 80% (configurable)
- **Qualité des recommandations** : Alerte si < 0.6 (configurable)

## Configuration

La configuration du système de monitoring se fait via des variables d'environnement ou le fichier `monitoring.config.ts`.

### Variables d'environnement

```
# Prometheus
MONITORING_PROMETHEUS_ENABLED=true

# Alertes
MONITORING_ALERTS_ENABLED=true
MONITORING_ALERTS_SLACK_ENABLED=true
MONITORING_ALERTS_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/xxx/yyy/zzz
MONITORING_ALERTS_SLACK_CHANNEL=#alerts
MONITORING_ALERTS_EMAIL_ENABLED=false
MONITORING_ALERTS_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
MONITORING_ALERTS_SMS_ENABLED=false
MONITORING_ALERTS_SMS_RECIPIENTS=+33612345678,+33687654321
MONITORING_ALERTS_DATABASE_ENABLED=true
MONITORING_ALERTS_DATABASE_RETENTION_DAYS=30
MONITORING_ALERTS_THROTTLING_ENABLED=true
MONITORING_ALERTS_THROTTLING_WINDOW_SECONDS=300
MONITORING_ALERTS_THROTTLING_MAX_ALERTS_PER_WINDOW=5

# Recommandations
MONITORING_RECOMMENDATION_ENABLED=true
MONITORING_RECOMMENDATION_THRESHOLD_RESPONSE_TIME=500
MONITORING_RECOMMENDATION_THRESHOLD_ERROR_RATE=5
MONITORING_RECOMMENDATION_THRESHOLD_CACHE_HIT_RATE=80
MONITORING_RECOMMENDATION_THRESHOLD_QUALITY=0.6
```

## Endpoints

### Métriques Prometheus

```
GET /metrics
```

Retourne les métriques au format Prometheus.

### Alertes

```
GET /api/v1/monitoring/alerts
```

Récupère les alertes avec pagination et filtrage.

```
GET /api/v1/monitoring/alerts/:id
```

Récupère une alerte par son ID.

```
POST /api/v1/monitoring/alerts
```

Crée une nouvelle alerte.

## Intégration avec Grafana

Pour visualiser les métriques collectées, vous pouvez utiliser Grafana. Un tableau de bord prédéfini est disponible dans le dossier `grafana/dashboards`.

### Installation de Grafana

```bash
docker run -d -p 3000:3000 --name grafana grafana/grafana
```

### Configuration de la source de données Prometheus

1. Accédez à Grafana à l'adresse http://localhost:3000
2. Connectez-vous avec les identifiants par défaut (admin/admin)
3. Allez dans Configuration > Data Sources > Add data source
4. Sélectionnez Prometheus
5. Configurez l'URL de Prometheus (http://localhost:9090)
6. Cliquez sur Save & Test

### Import du tableau de bord

1. Allez dans Create > Import
2. Téléchargez le fichier JSON du tableau de bord depuis `grafana/dashboards/recommendation-dashboard.json`
3. Cliquez sur Load
4. Sélectionnez la source de données Prometheus
5. Cliquez sur Import

## Bonnes pratiques

1. **Monitoring proactif** : Configurez des alertes pour détecter les problèmes avant qu'ils n'affectent les utilisateurs
2. **Seuils adaptés** : Ajustez les seuils d'alerte en fonction des performances réelles de l'application
3. **Réduction du bruit** : Utilisez le throttling pour éviter les alertes répétitives
4. **Documentation des incidents** : Documentez les incidents et les actions correctives
5. **Revue régulière** : Analysez régulièrement les métriques et les alertes pour identifier les tendances

## Dépannage

### Les métriques ne sont pas exposées

1. Vérifiez que le service Prometheus est activé (`MONITORING_PROMETHEUS_ENABLED=true`)
2. Vérifiez que l'endpoint `/metrics` est accessible
3. Vérifiez les logs pour les erreurs d'initialisation

### Les alertes ne sont pas envoyées

1. Vérifiez que le service d'alertes est activé (`MONITORING_ALERTS_ENABLED=true`)
2. Vérifiez que les canaux d'alerte sont correctement configurés
3. Vérifiez les logs pour les erreurs d'envoi d'alertes
4. Vérifiez que le throttling n'est pas trop restrictif

### Les métriques de recommandation ne sont pas collectées

1. Vérifiez que le service de monitoring des recommandations est activé (`MONITORING_RECOMMENDATION_ENABLED=true`)
2. Vérifiez que le middleware et l'intercepteur sont correctement configurés
3. Vérifiez les logs pour les erreurs de collecte de métriques
