import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  ValidationPipe,
  ParseEnumPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { PrismaService } from '../../../prisma/prisma.service';
import { AlertService, Alert } from '../services/alert.service';
import { CreateAlertDto } from '../dto/create-alert.dto';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';
import { PaginatedResponseDto } from '../../../common/dto/paginated-response.dto';

/**
 * Contrôleur pour gérer les alertes
 */
@ApiTags('Monitoring')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('api/v1/monitoring/alerts')
export class AlertsController {
  private readonly logger = new Logger(AlertsController.name);

  constructor(
    private readonly alertService: AlertService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * Récupère les alertes
   */
  @Get()
  @Roles('ADMIN')
  @ApiOperation({
    summary: 'Get alerts',
    description: 'Retrieves alerts with pagination and filtering options',
  })
  @ApiQuery({ name: 'page', type: Number, required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Number of items per page' })
  @ApiQuery({ name: 'level', enum: ['info', 'warning', 'error', 'critical'], required: false, description: 'Filter by alert level' })
  @ApiQuery({ name: 'source', type: String, required: false, description: 'Filter by alert source' })
  @ApiQuery({ name: 'search', type: String, required: false, description: 'Search in title and message' })
  @ApiPaginatedResponse({ type: 'Alert' })
  async getAlerts(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('level') level?: 'info' | 'warning' | 'error' | 'critical',
    @Query('source') source?: string,
    @Query('search') search?: string,
  ): Promise<PaginatedResponseDto<any>> {
    this.logger.log(`Getting alerts: page=${page}, limit=${limit}, level=${level}, source=${source}, search=${search}`);
    
    // Construire la requête
    const where: any = {};
    
    if (level) {
      where.level = level;
    }
    
    if (source) {
      where.source = source;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Récupérer les alertes
    const [alerts, total] = await Promise.all([
      this.prisma.alert.findMany({
        where,
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prisma.alert.count({ where }),
    ]);
    
    return {
      items: alerts,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Récupère une alerte par son ID
   */
  @Get(':id')
  @Roles('ADMIN')
  @ApiOperation({
    summary: 'Get alert by ID',
    description: 'Retrieves an alert by its ID',
  })
  @ApiParam({ name: 'id', type: String, description: 'Alert ID' })
  @ApiResponse({ status: 200, description: 'Alert found' })
  @ApiResponse({ status: 404, description: 'Alert not found' })
  async getAlertById(@Param('id') id: string): Promise<any> {
    this.logger.log(`Getting alert by ID: ${id}`);
    
    const alert = await this.prisma.alert.findUnique({
      where: { id },
    });
    
    if (!alert) {
      return { statusCode: 404, message: 'Alert not found' };
    }
    
    return alert;
  }

  /**
   * Crée une alerte
   */
  @Post()
  @Roles('ADMIN')
  @ApiOperation({
    summary: 'Create alert',
    description: 'Creates a new alert',
  })
  @ApiBody({ type: CreateAlertDto })
  @ApiResponse({ status: 201, description: 'Alert created' })
  async createAlert(
    @Body(new ValidationPipe()) createAlertDto: CreateAlertDto,
  ): Promise<{ success: boolean }> {
    this.logger.log(`Creating alert: ${createAlertDto.title}`);
    
    const alert: Alert = {
      level: createAlertDto.level,
      source: createAlertDto.source,
      title: createAlertDto.title,
      message: createAlertDto.message,
      metadata: createAlertDto.metadata,
    };
    
    await this.alertService.sendAlert(alert);
    
    return { success: true };
  }
}
