import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsString, IsNotEmpty, IsOptional, IsObject } from 'class-validator';

/**
 * DTO pour la création d'une alerte
 */
export class CreateAlertDto {
  @ApiProperty({
    description: 'Alert level',
    enum: ['info', 'warning', 'error', 'critical'],
    example: 'warning',
  })
  @IsEnum(['info', 'warning', 'error', 'critical'])
  @IsNotEmpty()
  level: 'info' | 'warning' | 'error' | 'critical';

  @ApiProperty({
    description: 'Alert source',
    example: 'recommendation',
  })
  @IsString()
  @IsNotEmpty()
  source: string;

  @ApiProperty({
    description: 'Alert title',
    example: 'High response time',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Alert message',
    example: 'The response time for the recommendation API is above the threshold.',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiPropertyOptional({
    description: 'Alert metadata',
    example: { responseTime: 1500, threshold: 1000 },
    type: 'object',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
