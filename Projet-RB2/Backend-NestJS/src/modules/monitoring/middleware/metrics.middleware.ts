import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { MetricsService } from '../services/metrics.service';

@Injectable()
export class MetricsMiddleware implements NestMiddleware {
  private readonly logger = new Logger(MetricsMiddleware.name);

  constructor(private readonly metricsService: MetricsService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const method = req.method;
    const route = this.extractRoute(req.path);

    // Continuer avec la requête
    next();

    // Capturer les métriques à la fin de la réponse
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const statusCode = res.statusCode;

      try {
        this.metricsService.recordHttpRequest(method, route, statusCode, duration);
        
        // Logger les requêtes lentes
        if (duration > 1000) {
          this.logger.warn(`Requête lente détectée: ${method} ${route} - ${duration}ms`);
        }
      } catch (error) {
        this.logger.error('Erreur lors de l\'enregistrement des métriques:', error);
      }
    });
  }

  /**
   * Extraire la route en supprimant les paramètres dynamiques
   */
  private extractRoute(path: string): string {
    // Remplacer les IDs par des placeholders pour grouper les métriques
    return path
      .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id') // UUIDs
      .replace(/\/\d+/g, '/:id') // Nombres
      .replace(/\/[a-zA-Z0-9]{20,}/g, '/:token'); // Tokens longs
  }
}
