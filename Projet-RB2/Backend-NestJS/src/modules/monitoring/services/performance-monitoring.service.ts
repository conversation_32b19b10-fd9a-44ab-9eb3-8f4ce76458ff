import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import { MetricsService } from './metrics.service';
import { performance } from 'perf_hooks';

interface PerformanceAlert {
  type: 'memory' | 'cpu' | 'database' | 'response_time';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
}

@Injectable()
export class PerformanceMonitoringService {
  private readonly logger = new Logger(PerformanceMonitoringService.name);
  private readonly alerts: PerformanceAlert[] = [];
  private readonly performanceHistory: any[] = [];

  // Seuils d'alerte configurables
  private readonly thresholds = {
    memory: {
      warning: 500 * 1024 * 1024, // 500MB
      critical: 1024 * 1024 * 1024, // 1GB
    },
    cpu: {
      warning: 70, // 70%
      critical: 90, // 90%
    },
    responseTime: {
      warning: 1000, // 1 seconde
      critical: 3000, // 3 secondes
    },
    database: {
      connectionWarning: 80, // 80% des connexions max
      connectionCritical: 95, // 95% des connexions max
      queryTimeWarning: 500, // 500ms
      queryTimeCritical: 2000, // 2 secondes
    },
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly metricsService: MetricsService,
  ) {}

  /**
   * Surveillance continue des performances
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async monitorPerformance() {
    try {
      const metrics = await this.collectPerformanceMetrics();
      this.performanceHistory.push(metrics);

      // Garder seulement les 60 dernières minutes
      if (this.performanceHistory.length > 60) {
        this.performanceHistory.shift();
      }

      // Vérifier les seuils et générer des alertes
      await this.checkThresholds(metrics);

      this.logger.debug('Métriques de performance collectées', metrics);
    } catch (error) {
      this.logger.error('Erreur lors de la surveillance des performances:', error);
    }
  }

  /**
   * Collecter les métriques de performance
   */
  async collectPerformanceMetrics() {
    const startTime = performance.now();

    // Métriques système
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    // Test de performance de la base de données
    const dbStartTime = performance.now();
    try {
      await this.prismaService.$queryRaw`SELECT 1`;
    } catch (error) {
      this.logger.error('Erreur de connexion à la base de données:', error);
    }
    const dbResponseTime = performance.now() - dbStartTime;

    // Métriques d'application
    const appMetrics = await this.metricsService.getApplicationMetrics();

    const endTime = performance.now();
    const collectionTime = endTime - startTime;

    return {
      timestamp: new Date(),
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
        heapUsedMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotalMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        total: cpuUsage.user + cpuUsage.system,
      },
      database: {
        responseTime: dbResponseTime,
        isConnected: dbResponseTime < 5000, // Considéré comme connecté si < 5s
      },
      application: {
        uptime: process.uptime(),
        collectionTime,
        ...appMetrics.application,
      },
      business: appMetrics.database,
    };
  }

  /**
   * Vérifier les seuils et générer des alertes
   */
  private async checkThresholds(metrics: any) {
    const alerts: PerformanceAlert[] = [];

    // Vérification mémoire
    if (metrics.memory.heapUsed > this.thresholds.memory.critical) {
      alerts.push({
        type: 'memory',
        severity: 'critical',
        message: `Utilisation mémoire critique: ${metrics.memory.heapUsedMB}MB`,
        value: metrics.memory.heapUsed,
        threshold: this.thresholds.memory.critical,
        timestamp: new Date(),
      });
    } else if (metrics.memory.heapUsed > this.thresholds.memory.warning) {
      alerts.push({
        type: 'memory',
        severity: 'medium',
        message: `Utilisation mémoire élevée: ${metrics.memory.heapUsedMB}MB`,
        value: metrics.memory.heapUsed,
        threshold: this.thresholds.memory.warning,
        timestamp: new Date(),
      });
    }

    // Vérification base de données
    if (metrics.database.responseTime > this.thresholds.database.queryTimeCritical) {
      alerts.push({
        type: 'database',
        severity: 'critical',
        message: `Temps de réponse DB critique: ${metrics.database.responseTime.toFixed(2)}ms`,
        value: metrics.database.responseTime,
        threshold: this.thresholds.database.queryTimeCritical,
        timestamp: new Date(),
      });
    } else if (metrics.database.responseTime > this.thresholds.database.queryTimeWarning) {
      alerts.push({
        type: 'database',
        severity: 'medium',
        message: `Temps de réponse DB élevé: ${metrics.database.responseTime.toFixed(2)}ms`,
        value: metrics.database.responseTime,
        threshold: this.thresholds.database.queryTimeWarning,
        timestamp: new Date(),
      });
    }

    // Ajouter les nouvelles alertes
    this.alerts.push(...alerts);

    // Garder seulement les 100 dernières alertes
    if (this.alerts.length > 100) {
      this.alerts.splice(0, this.alerts.length - 100);
    }

    // Logger les alertes critiques
    alerts.forEach(alert => {
      if (alert.severity === 'critical') {
        this.logger.error(`ALERTE CRITIQUE: ${alert.message}`);
      } else if (alert.severity === 'high') {
        this.logger.warn(`ALERTE ÉLEVÉE: ${alert.message}`);
      }
    });
  }

  /**
   * Obtenir les métriques de performance actuelles
   */
  getCurrentMetrics() {
    return this.performanceHistory[this.performanceHistory.length - 1] || null;
  }

  /**
   * Obtenir l'historique des performances
   */
  getPerformanceHistory(minutes: number = 60) {
    return this.performanceHistory.slice(-minutes);
  }

  /**
   * Obtenir les alertes actives
   */
  getActiveAlerts(severity?: string) {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    let alerts = this.alerts.filter(alert => alert.timestamp > oneHourAgo);

    if (severity) {
      alerts = alerts.filter(alert => alert.severity === severity);
    }

    return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Obtenir un résumé des performances
   */
  getPerformanceSummary() {
    const currentMetrics = this.getCurrentMetrics();
    const alerts = this.getActiveAlerts();
    const criticalAlerts = alerts.filter(a => a.severity === 'critical');
    const warningAlerts = alerts.filter(a => a.severity === 'medium' || a.severity === 'high');

    return {
      status: criticalAlerts.length > 0 ? 'critical' : 
              warningAlerts.length > 0 ? 'warning' : 'healthy',
      currentMetrics,
      alerts: {
        total: alerts.length,
        critical: criticalAlerts.length,
        warning: warningAlerts.length,
        recent: alerts.slice(0, 5),
      },
      uptime: process.uptime(),
      timestamp: new Date(),
    };
  }

  /**
   * Générer un rapport de performance détaillé
   */
  async generatePerformanceReport() {
    const summary = this.getPerformanceSummary();
    const history = this.getPerformanceHistory();
    const systemMetrics = await this.metricsService.getSystemMetrics();

    // Calculer des statistiques sur l'historique
    const memoryStats = this.calculateStats(history.map(h => h.memory.heapUsedMB));
    const dbStats = this.calculateStats(history.map(h => h.database.responseTime));

    return {
      summary,
      statistics: {
        memory: {
          average: memoryStats.average,
          min: memoryStats.min,
          max: memoryStats.max,
          trend: this.calculateTrend(history.map(h => h.memory.heapUsedMB)),
        },
        database: {
          averageResponseTime: dbStats.average,
          minResponseTime: dbStats.min,
          maxResponseTime: dbStats.max,
          trend: this.calculateTrend(history.map(h => h.database.responseTime)),
        },
      },
      systemMetrics,
      recommendations: this.generateRecommendations(summary, history),
      timestamp: new Date(),
    };
  }

  /**
   * Calculer des statistiques sur un tableau de valeurs
   */
  private calculateStats(values: number[]) {
    if (values.length === 0) return { average: 0, min: 0, max: 0 };

    const sum = values.reduce((a, b) => a + b, 0);
    return {
      average: sum / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
    };
  }

  /**
   * Calculer la tendance (croissante, décroissante, stable)
   */
  private calculateTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';

    const recent = values.slice(-10); // 10 dernières valeurs
    const older = values.slice(-20, -10); // 10 valeurs précédentes

    if (recent.length === 0 || older.length === 0) return 'stable';

    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;

    const diff = (recentAvg - olderAvg) / olderAvg;

    if (diff > 0.1) return 'increasing';
    if (diff < -0.1) return 'decreasing';
    return 'stable';
  }

  /**
   * Générer des recommandations basées sur les performances
   */
  private generateRecommendations(summary: any, history: any[]): string[] {
    const recommendations: string[] = [];

    // Recommandations mémoire
    if (summary.currentMetrics?.memory.heapUsedMB > 400) {
      recommendations.push('Considérer l\'optimisation de l\'utilisation mémoire');
    }

    // Recommandations base de données
    if (summary.currentMetrics?.database.responseTime > 100) {
      recommendations.push('Optimiser les requêtes de base de données');
    }

    // Recommandations basées sur les tendances
    const memoryTrend = this.calculateTrend(history.map(h => h.memory?.heapUsedMB || 0));
    if (memoryTrend === 'increasing') {
      recommendations.push('Surveiller la croissance de l\'utilisation mémoire');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performances optimales - aucune action requise');
    }

    return recommendations;
  }
}
