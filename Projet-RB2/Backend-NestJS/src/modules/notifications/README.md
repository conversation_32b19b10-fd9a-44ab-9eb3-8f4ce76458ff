# 📱 Module de Notifications

## Vue d'ensemble

Le module Notifications fournit un système complet de communication multi-canal avec personnalisation, templates dynamiques et analytics d'engagement.

## Fonctionnalités

### ✅ Notifications Multi-Canal
- **Email**: Templates HTML responsives
- **Push**: Notifications mobiles et web
- **SMS**: Messages texte courts
- **In-App**: Notifications dans l'application

### ✅ Personnalisation Avancée
- **Templates dynamiques**: Contenu adaptatif
- **Segmentation**: Ciblage précis des utilisateurs
- **Préférences**: Contrôle utilisateur complet
- **Timing optimal**: Envoi au meilleur moment

### ✅ Automation et Workflows
- **Déclencheurs**: Événements automatiques
- **Séquences**: Campagnes multi-étapes
- **Conditions**: Logique conditionnelle
- **A/B Testing**: Optimisation des messages

### ✅ Analytics et Reporting
- **Taux d'ouverture**: Métriques d'engagement
- **Clics et conversions**: Suivi des actions
- **Désabonnements**: Analyse de l'attrition
- **Performance**: Optimisation continue

## Architecture

```
notifications/
├── controllers/
│   ├── notifications.controller.ts      # Gestion des notifications
│   ├── templates.controller.ts          # Templates de messages
│   ├── preferences.controller.ts        # Préférences utilisateur
│   └── campaigns.controller.ts          # Campagnes marketing
├── services/
│   ├── notifications.service.ts         # Service principal
│   ├── email.service.ts                # Service email
│   ├── push.service.ts                 # Service push
│   ├── sms.service.ts                  # Service SMS
│   ├── template.service.ts             # Gestion des templates
│   ├── preference.service.ts           # Préférences utilisateur
│   ├── campaign.service.ts             # Campagnes marketing
│   └── analytics.service.ts            # Analytics notifications
├── dto/
│   ├── send-notification.dto.ts        # DTO d'envoi
│   ├── create-template.dto.ts          # DTO de template
│   └── update-preferences.dto.ts       # DTO de préférences
└── interfaces/
    ├── notification.interface.ts       # Interface notification
    └── template.interface.ts           # Interface template
```

## Types de Notifications

### 📧 Email
- **Transactionnelles**: Confirmations, reçus
- **Marketing**: Newsletters, promotions
- **Système**: Alertes, mises à jour
- **Personnalisées**: Messages individuels

### 📱 Push Notifications
- **Temps réel**: Notifications instantanées
- **Géolocalisées**: Basées sur la position
- **Programmées**: Envoi différé
- **Riches**: Avec images et actions

### 💬 SMS
- **Urgentes**: Alertes critiques
- **Codes**: Vérification 2FA
- **Rappels**: Rendez-vous, événements
- **Marketing**: Promotions courtes

### 🔔 In-App
- **Badges**: Compteurs de notifications
- **Banners**: Messages en overlay
- **Modales**: Notifications importantes
- **Toast**: Messages discrets

## Utilisation

### Envoyer une notification
```typescript
POST /notifications/send
{
  "userId": "user-123",
  "type": "welcome",
  "channels": ["email", "push"],
  "data": {
    "userName": "John Doe",
    "welcomeBonus": 50
  },
  "scheduling": {
    "sendAt": "2024-01-15T10:00:00Z"
  }
}
```

### Créer un template
```typescript
POST /notifications/templates
{
  "name": "welcome-email",
  "type": "email",
  "subject": "Bienvenue {{userName}} !",
  "content": {
    "html": "<h1>Bienvenue {{userName}}</h1><p>Votre bonus: {{welcomeBonus}}€</p>",
    "text": "Bienvenue {{userName}}! Votre bonus: {{welcomeBonus}}€"
  },
  "variables": ["userName", "welcomeBonus"]
}
```

### Configurer les préférences
```typescript
PUT /notifications/preferences
{
  "email": {
    "enabled": true,
    "frequency": "daily",
    "types": ["system", "marketing"]
  },
  "push": {
    "enabled": true,
    "quiet_hours": {
      "start": "22:00",
      "end": "08:00"
    }
  },
  "sms": {
    "enabled": false
  }
}
```

## Templates Dynamiques

### 🎨 Système de Templates
```typescript
interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'push' | 'sms' | 'in-app';
  subject?: string;
  content: {
    html?: string;
    text: string;
    push?: {
      title: string;
      body: string;
      icon?: string;
      image?: string;
    };
  };
  variables: string[];
  conditions?: TemplateCondition[];
}
```

### 🔄 Variables Dynamiques
- **Utilisateur**: nom, email, préférences
- **Contexte**: date, heure, localisation
- **Données**: métriques, statistiques
- **Personnalisation**: recommandations, contenu adapté

### 📊 Conditions
```typescript
// Template conditionnel
{
  "conditions": [
    {
      "if": "user.subscription === 'premium'",
      "then": "template-premium",
      "else": "template-standard"
    }
  ]
}
```

## Automation

### ⚡ Déclencheurs Automatiques
- **Événements utilisateur**: Inscription, achat, connexion
- **Comportement**: Inactivité, abandon de panier
- **Temporels**: Anniversaires, rappels
- **Système**: Maintenance, mises à jour

### 🔄 Workflows
```typescript
// Séquence d'onboarding
const onboardingWorkflow = {
  trigger: 'user.registered',
  steps: [
    {
      delay: '0h',
      template: 'welcome-email',
      channels: ['email']
    },
    {
      delay: '24h',
      template: 'getting-started',
      channels: ['email', 'push'],
      condition: 'user.lastLogin < 24h'
    },
    {
      delay: '72h',
      template: 'tips-and-tricks',
      channels: ['email'],
      condition: 'user.completedProfile < 50%'
    }
  ]
};
```

## Analytics

### 📊 Métriques Clés
- **Taux de livraison**: Notifications délivrées
- **Taux d'ouverture**: Messages ouverts
- **Taux de clic**: Actions effectuées
- **Taux de conversion**: Objectifs atteints
- **Désabonnements**: Opt-outs

### 📈 Rapports
```typescript
GET /notifications/analytics
{
  "period": "last_30_days",
  "metrics": {
    "sent": 15420,
    "delivered": 14890,
    "opened": 8934,
    "clicked": 2156,
    "converted": 432
  },
  "rates": {
    "delivery": 96.6,
    "open": 60.0,
    "click": 24.1,
    "conversion": 20.0
  }
}
```

## Intégrations

### 📧 Providers Email
- **SendGrid**: Service principal
- **Mailgun**: Backup
- **Amazon SES**: Volume élevé
- **SMTP**: Serveurs personnalisés

### 📱 Push Services
- **Firebase**: Android et iOS
- **Apple Push**: iOS natif
- **Web Push**: Navigateurs
- **OneSignal**: Multi-plateforme

### 💬 SMS Providers
- **Twilio**: Service principal
- **Nexmo**: International
- **Amazon SNS**: Intégration AWS

## Tests

- **Tests unitaires**: 95% de couverture
- **Tests d'intégration**: Validation des envois
- **Tests de charge**: Performance à grande échelle

```bash
npm run test src/modules/notifications
```

## Performance

### Optimisations
- ✅ Queue de traitement asynchrone
- ✅ Batch processing pour volumes élevés
- ✅ Cache des templates et préférences
- ✅ Retry automatique en cas d'échec
- ✅ Monitoring en temps réel
