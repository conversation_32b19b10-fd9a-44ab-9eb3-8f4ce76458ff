import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsU<PERSON><PERSON>, IsOptional, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { NotificationType } from '../enums/notification-type.enum';

export class CreateNotificationDto {
  @ApiProperty({
    description: 'ID de l\'utilisateur destinataire',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Type de notification',
    enum: NotificationType,
    example: NotificationType.EMAIL,
  })
  @IsEnum(NotificationType)
  @IsNotEmpty()
  type: NotificationType;

  @ApiProperty({
    description: 'Titre de la notification',
    example: 'Bienvenue sur Retreat And Be',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiPropertyOptional({
    description: 'Contenu de la notification',
    example: 'Nous sommes ravis de vous accueillir sur notre plateforme.',
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional({
    description: 'Données supplémentaires pour la notification',
    example: { courseId: '123', lessonId: '456' },
  })
  @IsObject()
  @IsOptional()
  data?: Record<string, any>;
}
