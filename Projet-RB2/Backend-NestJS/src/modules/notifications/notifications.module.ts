import { <PERSON>du<PERSON> } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { EmailService } from './services/email.service';
import { PushService } from './services/push.service';
import { TemplateService } from './services/template.service';

@Module({
  imports: [PrismaModule],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    EmailService,
    PushService,
    TemplateService,
  ],
  exports: [NotificationsService],
})
export class NotificationsModule {}
