import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { EmailService } from './services/email.service';
import { PushService } from './services/push.service';
import { TemplateService } from './services/template.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { NotificationType } from './enums/notification-type.enum';
import { NotificationStatus } from './enums/notification-status.enum';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly pushService: PushService,
    private readonly templateService: TemplateService,
  ) {}

  async create(createNotificationDto: CreateNotificationDto) {
    try {
      // Créer la notification dans la base de données
      const notification = await this.prisma.notification.create({
        data: {
          userId: createNotificationDto.userId,
          type: createNotificationDto.type,
          title: createNotificationDto.title,
          content: createNotificationDto.content,
          data: createNotificationDto.data || {},
          status: NotificationStatus.PENDING,
        },
      });

      // Envoyer la notification selon son type
      await this.sendNotification(notification);

      return notification;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la notification: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée une notification avec les paramètres compatibles avec le service de matching
   */
  async createNotification(data: {
    userId: string;
    type: string;
    title: string;
    message: string;
    data?: Record<string, any>;
    severity?: string;
    actionRequired?: boolean;
  }) {
    try {
      // Convertir les paramètres pour les adapter au format attendu
      const createDto: CreateNotificationDto = {
        userId: data.userId,
        type: NotificationType.IN_APP,
        title: data.title,
        content: data.message,
        data: {
          ...data.data,
          severity: data.severity || 'INFO',
          actionRequired: data.actionRequired || false
        }
      };

      // Utiliser la méthode create existante
      return this.create(createDto);
    } catch (error) {
      this.logger.error(`Erreur lors de la création de la notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findAll(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [notifications, total] = await Promise.all([
      this.prisma.notification.findMany({
        where: { userId },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.notification.count({
        where: { userId },
      }),
    ]);

    return {
      notifications,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string) {
    return this.prisma.notification.findUnique({
      where: { id },
    });
  }

  async markAsRead(id: string) {
    return this.prisma.notification.update({
      where: { id },
      data: { status: NotificationStatus.READ },
    });
  }

  async markAllAsRead(userId: string) {
    return this.prisma.notification.updateMany({
      where: {
        userId,
        status: NotificationStatus.DELIVERED,
      },
      data: { status: NotificationStatus.READ },
    });
  }

  async remove(id: string) {
    return this.prisma.notification.delete({
      where: { id },
    });
  }

  async sendNotification(notification: any) {
    try {
      // Récupérer l'utilisateur
      const user = await this.prisma.user.findUnique({
        where: { id: notification.userId },
      });

      if (!user) {
        throw new Error(`Utilisateur avec l'ID ${notification.userId} non trouvé`);
      }

      // Préparer le contenu de la notification
      const content = notification.content || await this.templateService.renderTemplate(
        notification.type,
        {
          ...notification.data,
          user: {
            name: user.name,
            email: user.email,
          },
        },
      );

      // Envoyer la notification selon son type
      switch (notification.type) {
        case NotificationType.EMAIL:
          await this.emailService.send({
            to: user.email,
            subject: notification.title,
            html: content,
          });
          break;
        case NotificationType.PUSH:
          if (user.pushToken) {
            await this.pushService.send({
              token: user.pushToken,
              title: notification.title,
              body: content,
              data: notification.data,
            });
          }
          break;
        case NotificationType.IN_APP:
          // La notification est déjà créée dans la base de données
          break;
        default:
          this.logger.warn(`Type de notification non pris en charge: ${notification.type}`);
      }

      // Mettre à jour le statut de la notification
      await this.prisma.notification.update({
        where: { id: notification.id },
        data: { status: NotificationStatus.DELIVERED },
      });

      this.logger.log(`Notification ${notification.id} envoyée avec succès`);
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi de la notification ${notification.id}: ${error.message}`);

      // Mettre à jour le statut de la notification en cas d'erreur
      await this.prisma.notification.update({
        where: { id: notification.id },
        data: {
          status: NotificationStatus.FAILED,
          error: error.message,
        },
      });

      throw error;
    }
  }

  async resendFailedNotifications() {
    const failedNotifications = await this.prisma.notification.findMany({
      where: { status: NotificationStatus.FAILED },
    });

    this.logger.log(`Tentative de renvoi de ${failedNotifications.length} notifications échouées`);

    for (const notification of failedNotifications) {
      try {
        await this.sendNotification(notification);
      } catch (error) {
        this.logger.error(`Échec du renvoi de la notification ${notification.id}: ${error.message}`);
      }
    }
  }
}
