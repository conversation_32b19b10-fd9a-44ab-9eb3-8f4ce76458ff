import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  from?: string;
  cc?: string[];
  bcc?: string[];
  attachments?: any[];
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: any;

  constructor(private readonly configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const emailConfig = this.configService.get('email');

    if (!emailConfig) {
      this.logger.warn('Configuration email manquante, le service d\'email est désactivé');
      return;
    }

    this.transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: {
        user: emailConfig.user,
        pass: emailConfig.password,
      },
    });

    // Vérifier la connexion
    this.transporter.verify((error: Error | null) => {
      if (error) {
        this.logger.error(`Erreur de connexion au serveur SMTP: ${error.message}`);
      } else {
        this.logger.log('Connexion au serveur SMTP établie avec succès');
      }
    });
  }

  async send(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn('Tentative d\'envoi d\'email alors que le service est désactivé');
      return false;
    }

    try {
      const emailConfig = this.configService.get('email');

      const mailOptions = {
        from: options.from || emailConfig.defaultFrom,
        to: options.to,
        cc: options.cc,
        bcc: options.bcc,
        subject: options.subject,
        html: options.html,
        attachments: options.attachments,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email envoyé: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi de l'email: ${error.message}`);
      throw error;
    }
  }

  async sendTemplate(template: string, data: any, options: Omit<EmailOptions, 'html'>): Promise<boolean> {
    try {
      // Dans une implémentation réelle, on utiliserait un moteur de template comme Handlebars
      // pour rendre le template HTML avec les données
      const html = `<p>Ceci est un email de test pour le template ${template} avec les données: ${JSON.stringify(data)}</p>`;

      return this.send({
        ...options,
        html,
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi du template email: ${error.message}`);
      throw error;
    }
  }
}
