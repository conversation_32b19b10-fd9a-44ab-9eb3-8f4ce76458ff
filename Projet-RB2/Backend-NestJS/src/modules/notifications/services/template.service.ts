import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as Handlebars from 'handlebars';

@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);
  private templates: Map<string, HandlebarsTemplateDelegate> = new Map();

  constructor(private readonly configService: ConfigService) {
    this.loadTemplates();
  }

  private loadTemplates() {
    try {
      const templatesDir = this.configService.get<string>('app.templatesDir', 'src/templates');
      const templateFiles = fs.readdirSync(templatesDir);
      
      for (const file of templateFiles) {
        if (file.endsWith('.hbs')) {
          const templateName = path.basename(file, '.hbs');
          const templateContent = fs.readFileSync(path.join(templatesDir, file), 'utf8');
          const template = Handlebars.compile(templateContent);
          
          this.templates.set(templateName, template);
          this.logger.log(`Template chargé: ${templateName}`);
        }
      }
      
      this.logger.log(`${this.templates.size} templates chargés`);
    } catch (error) {
      this.logger.error(`Erreur lors du chargement des templates: ${error.message}`);
    }
  }

  async renderTemplate(templateName: string, data: any): Promise<string> {
    try {
      const template = this.templates.get(templateName);
      
      if (!template) {
        // Utiliser un template par défaut si le template demandé n'existe pas
        return this.renderDefaultTemplate(templateName, data);
      }
      
      return template(data);
    } catch (error) {
      this.logger.error(`Erreur lors du rendu du template ${templateName}: ${error.message}`);
      return this.renderDefaultTemplate(templateName, data);
    }
  }

  private renderDefaultTemplate(templateName: string, data: any): string {
    // Template par défaut simple
    const defaultTemplate = `
      <html>
        <head>
          <title>{{title}}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
            .content { padding: 20px; }
            .footer { background-color: #f1f1f1; padding: 10px; text-align: center; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>{{title}}</h1>
            </div>
            <div class="content">
              <p>Bonjour {{user.name}},</p>
              <p>{{message}}</p>
            </div>
            <div class="footer">
              <p>© ${new Date().getFullYear()} Retreat And Be. Tous droits réservés.</p>
            </div>
          </div>
        </body>
      </html>
    `;
    
    const template = Handlebars.compile(defaultTemplate);
    return template({
      title: data.title || 'Notification',
      user: data.user || { name: 'Utilisateur' },
      message: data.message || `Ceci est une notification de type ${templateName}.`,
      ...data,
    });
  }
}
