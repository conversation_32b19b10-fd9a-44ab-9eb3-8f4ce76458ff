import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query,
  Patch,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { PartnerService } from '../services/partner.service';
import { CreatePartnerDto } from '../dto/create-partner.dto';
import { PartnerDocumentDto } from '../dto/partner-document.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { PartnerStatus } from '../../../prisma/prisma-types';

@ApiTags('partners')
@Controller('partners')
export class PartnerController {
  constructor(private readonly partnerService: PartnerService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Register as a partner' })
  @ApiResponse({ status: 201, description: 'Partner registration submitted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid data or user already registered as partner' })
  @HttpCode(HttpStatus.CREATED)
  @ApiBearerAuth()
  async register(@Body() createPartnerDto: CreatePartnerDto, @CurrentUser() user: any) {
    // If userId is not provided in the DTO, use the current user's ID
    if (!createPartnerDto.userId) {
      createPartnerDto.userId = user.id;
    }

    return this.partnerService.create(createPartnerDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get all partners' })
  @ApiResponse({ status: 200, description: 'Returns all partners' })
  @ApiQuery({ name: 'status', required: false, enum: PartnerStatus })
  @ApiBearerAuth()
  async findAll(@Query('status') status?: PartnerStatus) {
    if (status) {
      return this.partnerService.findByStatus(status);
    }
    return this.partnerService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get partner by ID' })
  @ApiResponse({ status: 200, description: 'Returns the partner details' })
  @ApiResponse({ status: 404, description: 'Partner not found' })
  @ApiBearerAuth()
  async findOne(@Param('id') id: string) {
    return this.partnerService.findOne(id);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Update partner status' })
  @ApiResponse({ status: 200, description: 'Partner status updated successfully' })
  @ApiResponse({ status: 404, description: 'Partner not found' })
  @ApiBearerAuth()
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: PartnerStatus,
  ) {
    return this.partnerService.updateStatus(id, status);
  }

  @Post(':id/documents')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add a document to a partner' })
  @ApiResponse({ status: 201, description: 'Document added successfully' })
  @ApiResponse({ status: 404, description: 'Partner not found' })
  @ApiBearerAuth()
  async addDocument(
    @Param('id') id: string,
    @Body() documentDto: PartnerDocumentDto,
    @CurrentUser() user: any,
  ) {
    // Check if the user is the partner or an admin
    const partner = await this.partnerService.findOne(id);
    if (partner.userId !== user.id && user.role !== 'ADMIN') {
      throw new Error('You do not have permission to add documents to this partner');
    }

    return this.partnerService.addDocument(id, documentDto);
  }

  @Get(':id/documents')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all documents for a partner' })
  @ApiResponse({ status: 200, description: 'Returns all documents for the partner' })
  @ApiResponse({ status: 404, description: 'Partner not found' })
  @ApiBearerAuth()
  async getDocuments(@Param('id') id: string, @CurrentUser() user: any) {
    // Check if the user is the partner or an admin
    const partner = await this.partnerService.findOne(id);
    if (partner.userId !== user.id && user.role !== 'ADMIN') {
      throw new Error('You do not have permission to view documents for this partner');
    }

    return this.partnerService.getDocuments(id);
  }

  @Delete('documents/:documentId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Delete a document' })
  @ApiResponse({ status: 200, description: 'Document deleted successfully' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  @ApiBearerAuth()
  async deleteDocument(
    @Param('documentId') documentId: string,
    @CurrentUser() user: any,
  ) {
    // Get the document to check permissions
    const document = await this.partnerService.deleteDocument(documentId);

    // Check if the user is the partner or an admin
    const partner = await this.partnerService.findOne(document.partnerId);
    if (partner.userId !== user.id && user.role !== 'ADMIN') {
      throw new Error('You do not have permission to delete this document');
    }

    return document;
  }
}
