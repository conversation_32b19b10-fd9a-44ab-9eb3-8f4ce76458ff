import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsOptional } from 'class-validator';

export enum DocumentType {
  IDENTITY = 'IDENTITY',
  BUSINESS_REGISTRATION = 'BUSINESS_REGISTRATION',
  INSURANCE = 'INSURANCE',
  CERTIFICATION = 'CERTIFICATION',
  TAX_DOCUMENT = 'TAX_DOCUMENT',
  BANK_DETAILS = 'BANK_DETAILS',
  OTHER = 'OTHER',
}

export class PartnerDocumentDto {
  @ApiProperty({
    description: 'Document type',
    enum: DocumentType,
    example: DocumentType.BUSINESS_REGISTRATION,
  })
  @IsEnum(DocumentType)
  @IsNotEmpty()
  type: DocumentType;

  @ApiProperty({
    description: 'Document description',
    example: 'Business registration certificate',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'File ID from the file upload service',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  fileId: string;

  @ApiProperty({
    description: 'Additional metadata (JSON)',
    example: { issueDate: '2023-01-01', expiryDate: '2024-01-01' },
    required: false,
  })
  @IsOptional()
  metadata?: Record<string, any>;
}
