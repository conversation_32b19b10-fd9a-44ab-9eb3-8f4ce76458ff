import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { CreatePartnerDto } from '../dto/create-partner.dto';
import { PartnerDocumentDto } from '../dto/partner-document.dto';
import { PartnerStatus } from '../../../prisma/prisma-types';

@Injectable()
export class PartnerService {
  private readonly logger = new Logger(PartnerService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Create a new partner
   * @param createPartnerDto Partner creation data
   * @returns The created partner
   */
  async create(createPartnerDto: CreatePartnerDto) {
    try {
      this.logger.log(`Creating new partner: ${JSON.stringify(createPartnerDto)}`);

      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: createPartnerDto.userId },
      });

      if (!user) {
        throw new BadRequestException(`User with ID ${createPartnerDto.userId} not found`);
      }

      // Check if user is already a partner
      const existingPartner = await this.prisma.partner.findUnique({
        where: { userId: createPartnerDto.userId },
      });

      if (existingPartner) {
        throw new BadRequestException(`User is already registered as a partner`);
      }

      // Create the partner
      const partner = await this.prisma.partner.create({
        data: {
          userId: createPartnerDto.userId,
          companyName: createPartnerDto.companyName,
          type: createPartnerDto.type,
          category: createPartnerDto.category,
          description: createPartnerDto.description,
          status: PartnerStatus.PENDING,
          logo: createPartnerDto.logo,
          website: createPartnerDto.website,
          specializations: createPartnerDto.specializations,
          languages: createPartnerDto.languages,
          coverageAreas: createPartnerDto.coverageAreas,
          insurance: createPartnerDto.insurance,
        },
      });

      this.logger.log(`Partner created successfully: ${partner.id}`);
      return partner;
    } catch (error) {
      this.logger.error(`Error creating partner: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find all partners
   * @returns List of all partners
   */
  async findAll() {
    return this.prisma.partner.findMany({
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
          },
        },
      },
    });
  }

  /**
   * Find partners by status
   * @param status Partner status
   * @returns List of partners with the specified status
   */
  async findByStatus(status: PartnerStatus) {
    return this.prisma.partner.findMany({
      where: { status },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
          },
        },
      },
    });
  }

  /**
   * Find a partner by ID
   * @param id Partner ID
   * @returns Partner details
   */
  async findOne(id: string) {
    const partner = await this.prisma.partner.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            image: true,
          },
        },
        badges: true,
        verifications: true,
        services: true,
        reviews: true,
        documents: true,
      },
    });

    if (!partner) {
      throw new NotFoundException(`Partner with ID ${id} not found`);
    }

    return partner;
  }

  /**
   * Update partner status
   * @param id Partner ID
   * @param status New status
   * @returns Updated partner
   */
  async updateStatus(id: string, status: PartnerStatus) {
    const partner = await this.prisma.partner.findUnique({
      where: { id },
    });

    if (!partner) {
      throw new NotFoundException(`Partner with ID ${id} not found`);
    }

    return this.prisma.partner.update({
      where: { id },
      data: { status },
    });
  }

  /**
   * Add a document to a partner
   * @param partnerId Partner ID
   * @param documentDto Document data
   * @returns The created document
   */
  async addDocument(partnerId: string, documentDto: PartnerDocumentDto) {
    try {
      this.logger.log(`Adding document to partner ${partnerId}: ${JSON.stringify(documentDto)}`);

      // Check if partner exists
      const partner = await this.prisma.partner.findUnique({
        where: { id: partnerId },
      });

      if (!partner) {
        throw new NotFoundException(`Partner with ID ${partnerId} not found`);
      }

      // Create the document
      const document = await this.prisma.partnerDocument.create({
        data: {
          partnerId,
          type: documentDto.type,
          description: documentDto.description,
          fileId: documentDto.fileId,
          metadata: documentDto.metadata,
        },
      });

      this.logger.log(`Document added successfully: ${document.id}`);
      return document;
    } catch (error) {
      this.logger.error(`Error adding document: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all documents for a partner
   * @param partnerId Partner ID
   * @returns List of partner documents
   */
  async getDocuments(partnerId: string) {
    const partner = await this.prisma.partner.findUnique({
      where: { id: partnerId },
    });

    if (!partner) {
      throw new NotFoundException(`Partner with ID ${partnerId} not found`);
    }

    return this.prisma.partnerDocument.findMany({
      where: { partnerId },
    });
  }

  /**
   * Delete a document
   * @param documentId Document ID
   * @returns The deleted document
   */
  async deleteDocument(documentId: string) {
    const document = await this.prisma.partnerDocument.findUnique({
      where: { id: documentId },
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    return this.prisma.partnerDocument.delete({
      where: { id: documentId },
    });
  }
}
