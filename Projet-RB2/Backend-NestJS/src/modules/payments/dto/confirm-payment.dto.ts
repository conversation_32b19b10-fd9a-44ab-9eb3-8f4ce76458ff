import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class ConfirmPaymentDto {
  @ApiProperty({
    description: 'ID of the payment intent to confirm',
    example: 'pi_3NqLkSJHR94LkQpZ1gEDz7Xm',
  })
  @IsString()
  @IsNotEmpty()
  paymentIntentId: string;

  @ApiProperty({
    description: 'ID of the payment method to use',
    example: 'pm_1NqLkSJHR94LkQpZ1gEDz7Xm',
  })
  @IsString()
  @IsNotEmpty()
  paymentMethodId: string;
}
