import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { PerformanceService } from '../services/performance.service';
import { MetricsService } from '../services/metrics.service';
import { MonitoringService } from '../services/monitoring.service';
import { AlertService } from '../services/alert.service';
import { ProfilingService } from '../services/profiling.service';
import { CreateAlertDto } from '../dto/create-alert.dto';
import { UpdateAlertDto } from '../dto/update-alert.dto';
import { CreateEndpointDto } from '../dto/create-endpoint.dto';
import { CreateServiceDto } from '../dto/create-service.dto';
import { UpdateMonitoringConfigDto } from '../dto/update-monitoring-config.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { AlertStatus } from '../enums/alert-status.enum';
import { AlertSeverity } from '../enums/alert-severity.enum';

@ApiTags('performance')
@Controller('performance')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class PerformanceController {
  constructor(
    private readonly performanceService: PerformanceService,
    private readonly metricsService: MetricsService,
    private readonly monitoringService: MonitoringService,
    private readonly alertService: AlertService,
    private readonly profilingService: ProfilingService,
  ) {}

  @Get('report')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Générer un rapport de performance' })
  @ApiResponse({ status: 200, description: 'Rapport de performance généré avec succès.' })
  generateReport() {
    return this.performanceService.generatePerformanceReport();
  }

  @Get('reports')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les rapports de performance' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiResponse({ status: 200, description: 'Liste des rapports de performance récupérée avec succès.' })
  getReports(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.performanceService.getPerformanceReports(
      page ? parseInt(page.toString(), 10) : undefined,
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  @Get('reports/:id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer un rapport de performance par son ID' })
  @ApiResponse({ status: 200, description: 'Rapport de performance récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Rapport de performance non trouvé.' })
  getReportById(@Param('id', ParseObjectIdPipe) id: string) {
    return this.performanceService.getPerformanceReportById(id);
  }

  @Get('reports/latest')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer le dernier rapport de performance' })
  @ApiResponse({ status: 200, description: 'Dernier rapport de performance récupéré avec succès.' })
  getLatestReport() {
    return this.performanceService.getLatestPerformanceReport();
  }

  @Get('metrics/system')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les métriques système' })
  @ApiResponse({ status: 200, description: 'Métriques système récupérées avec succès.' })
  getSystemMetrics() {
    return this.metricsService.getSystemMetrics();
  }

  @Get('metrics/api')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les métriques API' })
  @ApiResponse({ status: 200, description: 'Métriques API récupérées avec succès.' })
  getApiMetrics() {
    return this.metricsService.getApiMetrics();
  }

  @Get('metrics/database')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les métriques de base de données' })
  @ApiResponse({ status: 200, description: 'Métriques de base de données récupérées avec succès.' })
  getDatabaseMetrics() {
    return this.metricsService.getDatabaseMetrics();
  }

  @Get('metrics/cache')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les métriques de cache' })
  @ApiResponse({ status: 200, description: 'Métriques de cache récupérées avec succès.' })
  getCacheMetrics() {
    return this.metricsService.getCacheMetrics();
  }

  @Get('metrics/custom')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les métriques personnalisées' })
  @ApiResponse({ status: 200, description: 'Métriques personnalisées récupérées avec succès.' })
  getCustomMetrics() {
    return this.metricsService.getCustomMetrics();
  }

  @Post('metrics/custom')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Enregistrer une métrique personnalisée' })
  @ApiResponse({ status: 201, description: 'Métrique personnalisée enregistrée avec succès.' })
  recordCustomMetric(
    @Body('name') name: string,
    @Body('value') value: number,
    @Body('unit') unit: string,
    @Body('description') description?: string,
  ) {
    return this.metricsService.recordCustomMetric(name, value, unit, description);
  }

  @Get('thresholds')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les seuils de performance' })
  @ApiResponse({ status: 200, description: 'Seuils de performance récupérés avec succès.' })
  getThresholds() {
    return this.performanceService.getPerformanceThresholds();
  }

  @Post('thresholds')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Définir des seuils de performance' })
  @ApiResponse({ status: 201, description: 'Seuils de performance définis avec succès.' })
  setThresholds(@Body() thresholds: any[]) {
    return this.performanceService.setPerformanceThresholds(thresholds);
  }

  @Get('monitoring/status')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer le statut global du monitoring' })
  @ApiResponse({ status: 200, description: 'Statut global du monitoring récupéré avec succès.' })
  getMonitoringStatus() {
    return this.monitoringService.getStatus();
  }

  @Get('monitoring/config')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer la configuration de monitoring' })
  @ApiResponse({ status: 200, description: 'Configuration de monitoring récupérée avec succès.' })
  getMonitoringConfig() {
    return this.monitoringService.getConfig();
  }

  @Patch('monitoring/config')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour la configuration de monitoring' })
  @ApiResponse({ status: 200, description: 'Configuration de monitoring mise à jour avec succès.' })
  updateMonitoringConfig(@Body() updateMonitoringConfigDto: UpdateMonitoringConfigDto) {
    return this.monitoringService.updateConfig(
      updateMonitoringConfigDto.enabled,
      updateMonitoringConfigDto.interval,
      updateMonitoringConfigDto.alertThreshold,
      updateMonitoringConfigDto.retentionDays,
    );
  }

  @Get('monitoring/endpoints')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les endpoints surveillés' })
  @ApiResponse({ status: 200, description: 'Liste des endpoints surveillés récupérée avec succès.' })
  getEndpoints() {
    return this.monitoringService.getEndpoints();
  }

  @Post('monitoring/endpoints')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Ajouter un endpoint à surveiller' })
  @ApiResponse({ status: 201, description: 'Endpoint ajouté avec succès.' })
  addEndpoint(@Body() createEndpointDto: CreateEndpointDto) {
    return this.monitoringService.addEndpoint(
      createEndpointDto.url,
      createEndpointDto.name,
      createEndpointDto.description,
    );
  }

  @Delete('monitoring/endpoints')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Supprimer un endpoint à surveiller' })
  @ApiResponse({ status: 200, description: 'Endpoint supprimé avec succès.' })
  removeEndpoint(@Body('url') url: string) {
    return this.monitoringService.removeEndpoint(url);
  }

  @Get('monitoring/services')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les services surveillés' })
  @ApiResponse({ status: 200, description: 'Liste des services surveillés récupérée avec succès.' })
  getServices() {
    return this.monitoringService.getServices();
  }

  @Post('monitoring/services')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Ajouter un service à surveiller' })
  @ApiResponse({ status: 201, description: 'Service ajouté avec succès.' })
  addService(@Body() createServiceDto: CreateServiceDto) {
    return this.monitoringService.addService(
      createServiceDto.name,
      createServiceDto.description,
      createServiceDto.healthCheckUrl,
    );
  }

  @Delete('monitoring/services')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Supprimer un service à surveiller' })
  @ApiResponse({ status: 200, description: 'Service supprimé avec succès.' })
  removeService(@Body('name') name: string) {
    return this.monitoringService.removeService(name);
  }

  @Get('alerts')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les alertes' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Numéro de page' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'éléments par page' })
  @ApiQuery({ name: 'status', required: false, enum: AlertStatus, description: 'Statut des alertes' })
  @ApiQuery({ name: 'severity', required: false, enum: AlertSeverity, description: 'Sévérité des alertes' })
  @ApiResponse({ status: 200, description: 'Liste des alertes récupérée avec succès.' })
  getAlerts(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: AlertStatus,
    @Query('severity') severity?: AlertSeverity,
  ) {
    return this.alertService.findAll(
      page ? parseInt(page.toString(), 10) : undefined,
      limit ? parseInt(limit.toString(), 10) : undefined,
      status,
      severity,
    );
  }

  @Get('alerts/stats')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les statistiques des alertes' })
  @ApiResponse({ status: 200, description: 'Statistiques des alertes récupérées avec succès.' })
  getAlertStats() {
    return this.alertService.getAlertStats();
  }

  @Get('alerts/recent')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les alertes récentes' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre d\'alertes à récupérer' })
  @ApiResponse({ status: 200, description: 'Liste des alertes récentes récupérée avec succès.' })
  getRecentAlerts(@Query('limit') limit?: number) {
    return this.alertService.getRecentAlerts(
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  @Get('alerts/:id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer une alerte par son ID' })
  @ApiResponse({ status: 200, description: 'Alerte récupérée avec succès.' })
  @ApiResponse({ status: 404, description: 'Alerte non trouvée.' })
  getAlertById(@Param('id', ParseObjectIdPipe) id: string) {
    return this.alertService.findOne(id);
  }

  @Post('alerts')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Créer une alerte' })
  @ApiResponse({ status: 201, description: 'Alerte créée avec succès.' })
  createAlert(@Body() createAlertDto: CreateAlertDto) {
    return this.alertService.createAlert(createAlertDto);
  }

  @Patch('alerts/:id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Mettre à jour une alerte' })
  @ApiResponse({ status: 200, description: 'Alerte mise à jour avec succès.' })
  @ApiResponse({ status: 404, description: 'Alerte non trouvée.' })
  updateAlert(
    @Param('id', ParseObjectIdPipe) id: string,
    @Body() updateAlertDto: UpdateAlertDto,
  ) {
    return this.alertService.update(id, updateAlertDto);
  }

  @Post('alerts/:id/acknowledge')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Accuser réception d\'une alerte' })
  @ApiResponse({ status: 200, description: 'Accusé de réception de l\'alerte effectué avec succès.' })
  @ApiResponse({ status: 404, description: 'Alerte non trouvée.' })
  acknowledgeAlert(
    @Param('id', ParseObjectIdPipe) id: string,
    @CurrentUser('id') userId: string,
    @Body('comment') comment?: string,
  ) {
    return this.alertService.acknowledgeAlert(id, userId, comment);
  }

  @Post('alerts/:id/resolve')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Résoudre une alerte' })
  @ApiResponse({ status: 200, description: 'Alerte résolue avec succès.' })
  @ApiResponse({ status: 404, description: 'Alerte non trouvée.' })
  resolveAlert(
    @Param('id', ParseObjectIdPipe) id: string,
    @CurrentUser('id') userId: string,
    @Body('resolution') resolution?: string,
  ) {
    return this.alertService.resolveAlert(id, userId, resolution);
  }

  @Get('profiling/results')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les résultats de profilage' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre de résultats à récupérer' })
  @ApiResponse({ status: 200, description: 'Résultats de profilage récupérés avec succès.' })
  getProfilingResults(@Query('limit') limit?: number) {
    return this.profilingService.getProfilingResults(
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  @Get('profiling/results/:name')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer les résultats de profilage par nom' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Nombre de résultats à récupérer' })
  @ApiResponse({ status: 200, description: 'Résultats de profilage récupérés avec succès.' })
  getProfilingResultsByName(
    @Param('name') name: string,
    @Query('limit') limit?: number,
  ) {
    return this.profilingService.getProfilingResultsByName(
      name,
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  @Get('profiling/results/id/:id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Récupérer un résultat de profilage par son ID' })
  @ApiResponse({ status: 200, description: 'Résultat de profilage récupéré avec succès.' })
  @ApiResponse({ status: 404, description: 'Résultat de profilage non trouvé.' })
  getProfilingResultById(@Param('id') id: string) {
    return this.profilingService.getProfilingResultById(id);
  }

  @Post('profiling/start')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Démarrer une session de profilage' })
  @ApiResponse({ status: 201, description: 'Session de profilage démarrée avec succès.' })
  startProfiling(
    @Body('name') name: string,
    @Body('metadata') metadata?: Record<string, any>,
  ) {
    return { sessionId: this.profilingService.startProfiling(name, metadata) };
  }

  @Post('profiling/end')
  @Roles('ADMIN')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Terminer une session de profilage' })
  @ApiResponse({ status: 200, description: 'Session de profilage terminée avec succès.' })
  endProfiling(
    @Body('sessionId') sessionId: string,
    @Body('result') result?: Record<string, any>,
  ) {
    return this.profilingService.endProfiling(sessionId, result);
  }

  @Post('profiling/measurement')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Ajouter une mesure à une session de profilage' })
  @ApiResponse({ status: 201, description: 'Mesure ajoutée avec succès.' })
  addMeasurement(
    @Body('sessionId') sessionId: string,
    @Body('name') name: string,
    @Body('value') value: number,
    @Body('unit') unit: string,
    @Body('metadata') metadata?: Record<string, any>,
  ) {
    return this.profilingService.addMeasurement(sessionId, name, value, unit, metadata);
  }
}
