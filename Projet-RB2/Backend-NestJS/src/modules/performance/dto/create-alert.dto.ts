import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AlertSeverity } from '../enums/alert-severity.enum';

export class CreateAlertDto {
  @ApiProperty({
    description: 'Type d\'alerte',
    example: 'SYSTEM',
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Sévérité de l\'alerte',
    enum: AlertSeverity,
    example: AlertSeverity.WARNING,
  })
  @IsEnum(AlertSeverity)
  @IsNotEmpty()
  severity: AlertSeverity;

  @ApiProperty({
    description: 'Message de l\'alerte',
    example: 'Utilisation CPU élevée: 85%',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Source de l\'alerte',
    example: 'MONITORING',
  })
  @IsString()
  @IsNotEmpty()
  source: string;

  @ApiPropertyOptional({
    description: 'Métadonn<PERSON> de l\'alerte',
    example: { metricType: 'CPU_USAGE', value: 85, threshold: 80 },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
