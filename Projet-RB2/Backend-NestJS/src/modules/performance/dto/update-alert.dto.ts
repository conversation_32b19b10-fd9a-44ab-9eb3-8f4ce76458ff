import { IsString, IsEnum, IsOptional, IsObject, IsUUID, IsDate } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { AlertStatus } from '../enums/alert-status.enum';
import { AlertSeverity } from '../enums/alert-severity.enum';
import { Type } from 'class-transformer';

export class UpdateAlertDto {
  @ApiPropertyOptional({
    description: 'Statut de l\'alerte',
    enum: AlertStatus,
    example: AlertStatus.ACKNOWLEDGED,
  })
  @IsEnum(AlertStatus)
  @IsOptional()
  status?: AlertStatus;

  @ApiPropertyOptional({
    description: 'Sévérité de l\'alerte',
    enum: AlertSeverity,
    example: AlertSeverity.WARNING,
  })
  @IsEnum(AlertSeverity)
  @IsOptional()
  severity?: AlertSeverity;

  @ApiPropertyOptional({
    description: 'Message de l\'alerte',
    example: 'Utilisation CPU élevée: 85%',
  })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiPropertyOptional({
    description: 'ID de l\'utilisateur qui a accusé réception de l\'alerte',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  acknowledgedBy?: string;

  @ApiPropertyOptional({
    description: 'Date d\'accusé de réception de l\'alerte',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  acknowledgedAt?: Date;

  @ApiPropertyOptional({
    description: 'ID de l\'utilisateur qui a résolu l\'alerte',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  resolvedBy?: string;

  @ApiPropertyOptional({
    description: 'Date de résolution de l\'alerte',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  resolvedAt?: Date;

  @ApiPropertyOptional({
    description: 'Commentaire',
    example: 'Redémarrage du serveur effectué',
  })
  @IsString()
  @IsOptional()
  comment?: string;

  @ApiPropertyOptional({
    description: 'Résolution',
    example: 'Augmentation des ressources CPU allouées',
  })
  @IsString()
  @IsOptional()
  resolution?: string;

  @ApiPropertyOptional({
    description: 'Métadonnées de l\'alerte',
    example: { metricType: 'CPU_USAGE', value: 85, threshold: 80 },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
