import { IsB<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateMonitoringConfigDto {
  @ApiPropertyOptional({
    description: 'Activer/désactiver le monitoring',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @ApiPropertyOptional({
    description: 'Intervalle de monitoring en ms',
    example: 60000,
  })
  @IsNumber()
  @Min(1000)
  @IsOptional()
  interval?: number;

  @ApiPropertyOptional({
    description: 'Seuil d\'alerte',
    example: 3,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  alertThreshold?: number;

  @ApiPropertyOptional({
    description: 'Jours de rétention des données',
    example: 30,
  })
  @IsNumber()
  @Min(1)
  @Max(365)
  @IsOptional()
  retentionDays?: number;
}
