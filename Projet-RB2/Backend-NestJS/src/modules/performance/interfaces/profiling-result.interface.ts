export interface ProfilingMeasurement {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  metadata: Record<string, any>;
}

export interface ProfilingResult {
  id: string;
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  metadata: Record<string, any>;
  measurements: ProfilingMeasurement[];
  memory?: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  error?: string;
  stack?: string;
}
