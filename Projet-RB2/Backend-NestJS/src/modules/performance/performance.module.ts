import { Module } from '@nestjs/common';
import { PerformanceService } from './services/performance.service';
import { PerformanceController } from './controllers/performance.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { MetricsService } from './services/metrics.service';
import { MonitoringService } from './services/monitoring.service';
import { AlertService } from './services/alert.service';
import { ProfilingService } from './services/profiling.service';
// TODO: Implement this service
// import { LoggingService } from './services/logging.service';
import { EventsModule } from '../events/events.module';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    PrismaModule,
    EventsModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [PerformanceController],
  providers: [
    PerformanceService,
    MetricsService,
    MonitoringService,
    AlertService,
    ProfilingService,
    // TODO: Implement this service
    // LoggingService,
  ],
  exports: [
    PerformanceService,
    MetricsService,
    MonitoringService,
    AlertService,
  ],
})
export class PerformanceModule {}
