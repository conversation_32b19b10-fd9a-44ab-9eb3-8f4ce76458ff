import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventsService } from '../../events/events.service';
import { CreateAlertDto } from '../dto/create-alert.dto';
import { UpdateAlertDto } from '../dto/update-alert.dto';
import { AlertStatus } from '../enums/alert-status.enum';
import { AlertSeverity } from '../enums/alert-severity.enum';

@Injectable()
export class AlertService {
  private readonly logger = new Logger(AlertService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventsService: EventsService,
  ) {}

  /**
   * Crée une alerte
   * @param createAlertDto Données de l'alerte
   * @returns L'alerte créée
   */
  async createAlert(createAlertDto: CreateAlertDto) {
    try {
      this.logger.log(`Création d'une alerte: ${createAlertDto.message}`);
      
      // Créer l'alerte
      const alert = await this.prisma.alert.create({
        data: {
          type: createAlertDto.type,
          severity: createAlertDto.severity,
          message: createAlertDto.message,
          source: createAlertDto.source,
          status: AlertStatus.OPEN,
          metadata: createAlertDto.metadata || {},
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'ALERT_CREATED',
        payload: {
          alertId: alert.id,
          type: alert.type,
          severity: alert.severity,
          message: alert.message,
        },
        status: 'COMPLETED',
      });
      
      // Envoyer une notification si l'alerte est critique
      if (alert.severity === AlertSeverity.CRITICAL) {
        // Dans une implémentation réelle, on enverrait une notification
        this.logger.warn(`Alerte critique: ${alert.message}`);
      }
      
      return alert;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'alerte: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère toutes les alertes avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @param status Statut des alertes
   * @param severity Sévérité des alertes
   * @returns Liste paginée des alertes
   */
  async findAll(page = 1, limit = 10, status?: AlertStatus, severity?: AlertSeverity) {
    const skip = (page - 1) * limit;
    
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (severity) {
      where.severity = severity;
    }
    
    const [alerts, total] = await Promise.all([
      this.prisma.alert.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.alert.count({ where }),
    ]);
    
    return {
      alerts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère une alerte par son ID
   * @param id ID de l'alerte
   * @returns L'alerte
   */
  async findOne(id: string) {
    return this.prisma.alert.findUnique({
      where: { id },
    });
  }

  /**
   * Met à jour une alerte
   * @param id ID de l'alerte
   * @param updateAlertDto Données de mise à jour
   * @returns L'alerte mise à jour
   */
  async update(id: string, updateAlertDto: UpdateAlertDto) {
    try {
      // Récupérer l'alerte existante
      const existingAlert = await this.findOne(id);
      
      if (!existingAlert) {
        throw new Error(`Alerte avec l'ID ${id} non trouvée`);
      }
      
      // Mettre à jour l'alerte
      const alert = await this.prisma.alert.update({
        where: { id },
        data: updateAlertDto,
      });
      
      // Émettre un événement si le statut a changé
      if (updateAlertDto.status && updateAlertDto.status !== existingAlert.status) {
        await this.eventsService.create({
          eventType: 'ALERT_STATUS_CHANGED',
          payload: {
            alertId: alert.id,
            oldStatus: existingAlert.status,
            newStatus: alert.status,
          },
          status: 'COMPLETED',
        });
      }
      
      return alert;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de l'alerte: ${error.message}`);
      throw error;
    }
  }

  /**
   * Accuse réception d'une alerte
   * @param id ID de l'alerte
   * @param userId ID de l'utilisateur
   * @param comment Commentaire
   * @returns L'alerte mise à jour
   */
  async acknowledgeAlert(id: string, userId: string, comment?: string) {
    try {
      // Récupérer l'alerte existante
      const existingAlert = await this.findOne(id);
      
      if (!existingAlert) {
        throw new Error(`Alerte avec l'ID ${id} non trouvée`);
      }
      
      // Vérifier si l'alerte est déjà résolue
      if (existingAlert.status === AlertStatus.RESOLVED) {
        throw new Error(`L'alerte est déjà résolue`);
      }
      
      // Mettre à jour l'alerte
      const alert = await this.prisma.alert.update({
        where: { id },
        data: {
          status: AlertStatus.ACKNOWLEDGED,
          acknowledgedBy: userId,
          acknowledgedAt: new Date(),
          comment,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'ALERT_ACKNOWLEDGED',
        payload: {
          alertId: alert.id,
          acknowledgedBy: userId,
          comment,
        },
        status: 'COMPLETED',
      });
      
      return alert;
    } catch (error) {
      this.logger.error(`Erreur lors de l'accusé de réception de l'alerte: ${error.message}`);
      throw error;
    }
  }

  /**
   * Résout une alerte
   * @param id ID de l'alerte
   * @param userId ID de l'utilisateur
   * @param resolution Résolution
   * @returns L'alerte mise à jour
   */
  async resolveAlert(id: string, userId: string, resolution?: string) {
    try {
      // Récupérer l'alerte existante
      const existingAlert = await this.findOne(id);
      
      if (!existingAlert) {
        throw new Error(`Alerte avec l'ID ${id} non trouvée`);
      }
      
      // Vérifier si l'alerte est déjà résolue
      if (existingAlert.status === AlertStatus.RESOLVED) {
        throw new Error(`L'alerte est déjà résolue`);
      }
      
      // Mettre à jour l'alerte
      const alert = await this.prisma.alert.update({
        where: { id },
        data: {
          status: AlertStatus.RESOLVED,
          resolvedBy: userId,
          resolvedAt: new Date(),
          resolution,
        },
      });
      
      // Émettre un événement
      await this.eventsService.create({
        eventType: 'ALERT_RESOLVED',
        payload: {
          alertId: alert.id,
          resolvedBy: userId,
          resolution,
        },
        status: 'COMPLETED',
      });
      
      return alert;
    } catch (error) {
      this.logger.error(`Erreur lors de la résolution de l'alerte: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les alertes récentes
   * @param limit Nombre d'alertes à récupérer
   * @returns Liste des alertes récentes
   */
  async getRecentAlerts(limit = 10) {
    return this.prisma.alert.findMany({
      where: {
        status: {
          in: [AlertStatus.OPEN, AlertStatus.ACKNOWLEDGED],
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });
  }

  /**
   * Récupère les alertes par type
   * @param type Type d'alerte
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des alertes par type
   */
  async findByType(type: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    const [alerts, total] = await Promise.all([
      this.prisma.alert.findMany({
        where: { type },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.alert.count({ where: { type } }),
    ]);
    
    return {
      alerts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les alertes par sévérité
   * @param severity Sévérité des alertes
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des alertes par sévérité
   */
  async findBySeverity(severity: AlertSeverity, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    const [alerts, total] = await Promise.all([
      this.prisma.alert.findMany({
        where: { severity },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.alert.count({ where: { severity } }),
    ]);
    
    return {
      alerts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les alertes par statut
   * @param status Statut des alertes
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des alertes par statut
   */
  async findByStatus(status: AlertStatus, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    const [alerts, total] = await Promise.all([
      this.prisma.alert.findMany({
        where: { status },
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.alert.count({ where: { status } }),
    ]);
    
    return {
      alerts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère les statistiques des alertes
   * @returns Statistiques des alertes
   */
  async getAlertStats() {
    const [
      totalAlerts,
      openAlerts,
      acknowledgedAlerts,
      resolvedAlerts,
      criticalAlerts,
      warningAlerts,
      infoAlerts,
    ] = await Promise.all([
      this.prisma.alert.count(),
      this.prisma.alert.count({ where: { status: AlertStatus.OPEN } }),
      this.prisma.alert.count({ where: { status: AlertStatus.ACKNOWLEDGED } }),
      this.prisma.alert.count({ where: { status: AlertStatus.RESOLVED } }),
      this.prisma.alert.count({ where: { severity: AlertSeverity.CRITICAL } }),
      this.prisma.alert.count({ where: { severity: AlertSeverity.WARNING } }),
      this.prisma.alert.count({ where: { severity: AlertSeverity.INFO } }),
    ]);
    
    return {
      total: totalAlerts,
      byStatus: {
        open: openAlerts,
        acknowledged: acknowledgedAlerts,
        resolved: resolvedAlerts,
      },
      bySeverity: {
        critical: criticalAlerts,
        warning: warningAlerts,
        info: infoAlerts,
      },
      activeAlerts: openAlerts + acknowledgedAlerts,
    };
  }
}
