import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { PerformanceMetricType } from '../enums/performance-metric-type.enum';
import { PerformanceMetric } from '../interfaces/performance-metric.interface';
import * as os from 'os';

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private readonly metricCache: Map<string, PerformanceMetric> = new Map();
  private readonly metricHistory: Map<string, PerformanceMetric[]> = new Map();
  private readonly historyLimit = 100;

  constructor(private readonly prisma: PrismaService) {
    // Initialiser le cache des métriques
    this.initializeMetricCache();
  }

  /**
   * Initialise le cache des métriques
   */
  private initializeMetricCache() {
    // Initialiser les métriques système
    this.metricCache.set(PerformanceMetricType.CPU_USAGE, {
      type: PerformanceMetricType.CPU_USAGE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.MEMORY_USAGE, {
      type: PerformanceMetricType.MEMORY_USAGE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.DISK_USAGE, {
      type: PerformanceMetricType.DISK_USAGE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    // Initialiser les métriques API
    this.metricCache.set(PerformanceMetricType.API_REQUESTS_PER_MINUTE, {
      type: PerformanceMetricType.API_REQUESTS_PER_MINUTE,
      value: 0,
      unit: 'req/min',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.API_AVERAGE_RESPONSE_TIME, {
      type: PerformanceMetricType.API_AVERAGE_RESPONSE_TIME,
      value: 0,
      unit: 'ms',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.API_ERROR_RATE, {
      type: PerformanceMetricType.API_ERROR_RATE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    // Initialiser les métriques de base de données
    this.metricCache.set(PerformanceMetricType.DB_AVERAGE_QUERY_TIME, {
      type: PerformanceMetricType.DB_AVERAGE_QUERY_TIME,
      value: 0,
      unit: 'ms',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.DB_QUERIES_PER_MINUTE, {
      type: PerformanceMetricType.DB_QUERIES_PER_MINUTE,
      value: 0,
      unit: 'queries/min',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.DB_CONNECTION_POOL_USAGE, {
      type: PerformanceMetricType.DB_CONNECTION_POOL_USAGE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    // Initialiser les métriques de cache
    this.metricCache.set(PerformanceMetricType.CACHE_HIT_RATE, {
      type: PerformanceMetricType.CACHE_HIT_RATE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    this.metricCache.set(PerformanceMetricType.CACHE_USAGE, {
      type: PerformanceMetricType.CACHE_USAGE,
      value: 0,
      unit: '%',
      timestamp: new Date(),
      status: 'HEALTHY',
    });

    // Initialiser l'historique des métriques
    this.metricCache.forEach((_, key) => {
      this.metricHistory.set(key, []);
    });
  }

  /**
   * Récupère les métriques système
   * @returns Métriques système
   */
  async getSystemMetrics() {
    try {
      // Mettre à jour les métriques système
      await this.updateSystemMetrics();

      return {
        cpuUsage: this.metricCache.get(PerformanceMetricType.CPU_USAGE),
        memoryUsage: this.metricCache.get(PerformanceMetricType.MEMORY_USAGE),
        diskUsage: this.metricCache.get(PerformanceMetricType.DISK_USAGE),
        uptime: os.uptime(),
        loadAverage: os.loadavg(),
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques système: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les métriques API
   * @returns Métriques API
   */
  async getApiMetrics() {
    try {
      return {
        requestsPerMinute: this.metricCache.get(PerformanceMetricType.API_REQUESTS_PER_MINUTE),
        averageResponseTime: this.metricCache.get(PerformanceMetricType.API_AVERAGE_RESPONSE_TIME),
        errorRate: this.metricCache.get(PerformanceMetricType.API_ERROR_RATE),
        requestsHistory: this.getMetricHistory(PerformanceMetricType.API_REQUESTS_PER_MINUTE),
        responseTimeHistory: this.getMetricHistory(PerformanceMetricType.API_AVERAGE_RESPONSE_TIME),
        errorRateHistory: this.getMetricHistory(PerformanceMetricType.API_ERROR_RATE),
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques API: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les métriques de base de données
   * @returns Métriques de base de données
   */
  async getDatabaseMetrics() {
    try {
      // Mettre à jour les métriques de base de données
      await this.updateDatabaseMetrics();

      return {
        averageQueryTime: this.metricCache.get(PerformanceMetricType.DB_AVERAGE_QUERY_TIME),
        queriesPerMinute: this.metricCache.get(PerformanceMetricType.DB_QUERIES_PER_MINUTE),
        connectionPoolUsage: this.metricCache.get(PerformanceMetricType.DB_CONNECTION_POOL_USAGE),
        queryTimeHistory: this.getMetricHistory(PerformanceMetricType.DB_AVERAGE_QUERY_TIME),
        queriesHistory: this.getMetricHistory(PerformanceMetricType.DB_QUERIES_PER_MINUTE),
        poolUsageHistory: this.getMetricHistory(PerformanceMetricType.DB_CONNECTION_POOL_USAGE),
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques de base de données: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les métriques de cache
   * @returns Métriques de cache
   */
  async getCacheMetrics() {
    try {
      return {
        hitRate: this.metricCache.get(PerformanceMetricType.CACHE_HIT_RATE),
        usage: this.metricCache.get(PerformanceMetricType.CACHE_USAGE),
        hitRateHistory: this.getMetricHistory(PerformanceMetricType.CACHE_HIT_RATE),
        usageHistory: this.getMetricHistory(PerformanceMetricType.CACHE_USAGE),
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques de cache: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les métriques personnalisées
   * @returns Métriques personnalisées
   */
  async getCustomMetrics() {
    try {
      // Récupérer les métriques personnalisées depuis la base de données
      const customMetrics = await this.prisma.customMetric.findMany({
        orderBy: {
          name: 'asc',
        },
      });

      return customMetrics.map(metric => ({
        name: metric.name,
        value: metric.value,
        unit: metric.unit,
        timestamp: metric.timestamp,
        description: metric.description,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques personnalisées: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enregistre une métrique API
   * @param endpoint Endpoint API
   * @param responseTime Temps de réponse en ms
   * @param statusCode Code de statut HTTP
   */
  async recordApiMetric(endpoint: string, responseTime: number, statusCode: number) {
    try {
      // Enregistrer la métrique dans la base de données
      await this.prisma.apiMetric.create({
        data: {
          endpoint,
          responseTime,
          statusCode,
          timestamp: new Date(),
        },
      });

      // Mettre à jour les métriques API en cache
      await this.updateApiMetrics();
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la métrique API: ${error.message}`);
    }
  }

  /**
   * Enregistre une métrique de base de données
   * @param query Requête SQL
   * @param executionTime Temps d'exécution en ms
   * @param success Succès de la requête
   */
  async recordDatabaseMetric(query: string, executionTime: number, success: boolean) {
    try {
      // Enregistrer la métrique dans la base de données
      await this.prisma.databaseMetric.create({
        data: {
          query,
          executionTime,
          success,
          timestamp: new Date(),
        },
      });

      // Mettre à jour les métriques de base de données en cache
      await this.updateDatabaseMetrics();
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la métrique de base de données: ${error.message}`);
    }
  }

  /**
   * Enregistre une métrique de cache
   * @param key Clé de cache
   * @param hit Hit ou miss
   * @param executionTime Temps d'exécution en ms
   */
  async recordCacheMetric(key: string, hit: boolean, executionTime: number) {
    try {
      // Enregistrer la métrique dans la base de données
      await this.prisma.cacheMetric.create({
        data: {
          key,
          hit,
          executionTime,
          timestamp: new Date(),
        },
      });

      // Mettre à jour les métriques de cache en cache
      await this.updateCacheMetrics();
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la métrique de cache: ${error.message}`);
    }
  }

  /**
   * Enregistre une métrique personnalisée
   * @param name Nom de la métrique
   * @param value Valeur de la métrique
   * @param unit Unité de la métrique
   * @param description Description de la métrique
   */
  async recordCustomMetric(name: string, value: number, unit: string, description?: string) {
    try {
      // Enregistrer la métrique dans la base de données
      await this.prisma.customMetric.create({
        data: {
          name,
          value,
          unit,
          description,
          timestamp: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de la métrique personnalisée: ${error.message}`);
    }
  }

  /**
   * Met à jour les métriques système
   */
  private async updateSystemMetrics() {
    try {
      // Calculer l'utilisation du CPU
      const cpuUsage = await this.calculateCpuUsage();

      // Calculer l'utilisation de la mémoire
      const memoryUsage = this.calculateMemoryUsage();

      // Calculer l'utilisation du disque (simulé)
      const diskUsage = 60; // Simulé à 60%

      // Mettre à jour les métriques en cache
      this.updateMetric(PerformanceMetricType.CPU_USAGE, cpuUsage);
      this.updateMetric(PerformanceMetricType.MEMORY_USAGE, memoryUsage);
      this.updateMetric(PerformanceMetricType.DISK_USAGE, diskUsage);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des métriques système: ${error.message}`);
    }
  }

  /**
   * Met à jour les métriques API
   */
  private async updateApiMetrics() {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

      // Calculer le nombre de requêtes par minute
      const requestCount = await this.prisma.apiMetric.count({
        where: {
          timestamp: {
            gte: oneMinuteAgo,
          },
        },
      });

      // Calculer le temps de réponse moyen
      const apiMetrics = await this.prisma.apiMetric.findMany({
        where: {
          timestamp: {
            gte: oneMinuteAgo,
          },
        },
        select: {
          responseTime: true,
          statusCode: true,
        },
      });

      const totalResponseTime = apiMetrics.reduce((sum: number, metric: any) => sum + metric.responseTime, 0);
      const averageResponseTime = apiMetrics.length > 0 ? totalResponseTime / apiMetrics.length : 0;

      // Calculer le taux d'erreur
      const errorCount = apiMetrics.filter((metric: any) => metric.statusCode >= 400).length;
      const errorRate = apiMetrics.length > 0 ? (errorCount / apiMetrics.length) * 100 : 0;

      // Mettre à jour les métriques en cache
      this.updateMetric(PerformanceMetricType.API_REQUESTS_PER_MINUTE, requestCount);
      this.updateMetric(PerformanceMetricType.API_AVERAGE_RESPONSE_TIME, averageResponseTime);
      this.updateMetric(PerformanceMetricType.API_ERROR_RATE, errorRate);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des métriques API: ${error.message}`);
    }
  }

  /**
   * Met à jour les métriques de base de données
   */
  private async updateDatabaseMetrics() {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

      // Calculer le nombre de requêtes par minute
      const queryCount = await this.prisma.databaseMetric.count({
        where: {
          timestamp: {
            gte: oneMinuteAgo,
          },
        },
      });

      // Calculer le temps d'exécution moyen
      const dbMetrics = await this.prisma.databaseMetric.findMany({
        where: {
          timestamp: {
            gte: oneMinuteAgo,
          },
        },
        select: {
          executionTime: true,
          success: true,
        },
      });

      const totalExecutionTime = dbMetrics.reduce((sum, metric) => sum + metric.executionTime, 0);
      const averageQueryTime = dbMetrics.length > 0 ? totalExecutionTime / dbMetrics.length : 0;

      // Simuler l'utilisation du pool de connexions
      const connectionPoolUsage = 50; // Simulé à 50%

      // Mettre à jour les métriques en cache
      this.updateMetric(PerformanceMetricType.DB_QUERIES_PER_MINUTE, queryCount);
      this.updateMetric(PerformanceMetricType.DB_AVERAGE_QUERY_TIME, averageQueryTime);
      this.updateMetric(PerformanceMetricType.DB_CONNECTION_POOL_USAGE, connectionPoolUsage);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des métriques de base de données: ${error.message}`);
    }
  }

  /**
   * Met à jour les métriques de cache
   */
  private async updateCacheMetrics() {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Calculer le taux de hit
      const cacheMetrics = await this.prisma.cacheMetric.findMany({
        where: {
          timestamp: {
            gte: oneHourAgo,
          },
        },
        select: {
          hit: true,
        },
      });

      const hitCount = cacheMetrics.filter(metric => metric.hit).length;
      const hitRate = cacheMetrics.length > 0 ? (hitCount / cacheMetrics.length) * 100 : 0;

      // Simuler l'utilisation du cache
      const cacheUsage = 40; // Simulé à 40%

      // Mettre à jour les métriques en cache
      this.updateMetric(PerformanceMetricType.CACHE_HIT_RATE, hitRate);
      this.updateMetric(PerformanceMetricType.CACHE_USAGE, cacheUsage);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des métriques de cache: ${error.message}`);
    }
  }

  /**
   * Met à jour une métrique en cache
   * @param type Type de métrique
   * @param value Valeur de la métrique
   */
  private updateMetric(type: string, value: number) {
    const metric = this.metricCache.get(type);

    if (metric) {
      // Déterminer le statut de la métrique
      const status = this.determineMetricStatus(type, value);

      // Mettre à jour la métrique
      const updatedMetric: PerformanceMetric = {
        ...metric,
        value,
        timestamp: new Date(),
        status,
      };

      // Mettre à jour le cache
      this.metricCache.set(type, updatedMetric);

      // Ajouter à l'historique
      this.addToMetricHistory(type, updatedMetric);
    }
  }

  /**
   * Détermine le statut d'une métrique
   * @param type Type de métrique
   * @param value Valeur de la métrique
   * @returns Statut de la métrique
   */
  private determineMetricStatus(type: string, value: number): 'HEALTHY' | 'DEGRADED' | 'WARNING' | 'CRITICAL' {
    // Définir les seuils pour chaque type de métrique
    const thresholds: Record<string, { warning: number; critical: number; isHigherBad: boolean }> = {
      [PerformanceMetricType.CPU_USAGE]: { warning: 70, critical: 90, isHigherBad: true },
      [PerformanceMetricType.MEMORY_USAGE]: { warning: 70, critical: 90, isHigherBad: true },
      [PerformanceMetricType.DISK_USAGE]: { warning: 70, critical: 90, isHigherBad: true },
      [PerformanceMetricType.API_REQUESTS_PER_MINUTE]: { warning: 1000, critical: 2000, isHigherBad: true },
      [PerformanceMetricType.API_AVERAGE_RESPONSE_TIME]: { warning: 200, critical: 500, isHigherBad: true },
      [PerformanceMetricType.API_ERROR_RATE]: { warning: 1, critical: 5, isHigherBad: true },
      [PerformanceMetricType.DB_AVERAGE_QUERY_TIME]: { warning: 100, critical: 300, isHigherBad: true },
      [PerformanceMetricType.DB_QUERIES_PER_MINUTE]: { warning: 1000, critical: 2000, isHigherBad: true },
      [PerformanceMetricType.DB_CONNECTION_POOL_USAGE]: { warning: 70, critical: 90, isHigherBad: true },
      [PerformanceMetricType.CACHE_HIT_RATE]: { warning: 70, critical: 50, isHigherBad: false },
      [PerformanceMetricType.CACHE_USAGE]: { warning: 70, critical: 90, isHigherBad: true },
    };

    const threshold = thresholds[type];

    if (!threshold) {
      return 'HEALTHY';
    }

    if (threshold.isHigherBad) {
      if (value >= threshold.critical) {
        return 'CRITICAL';
      } else if (value >= threshold.warning) {
        return 'WARNING';
      } else if (value >= threshold.warning * 0.8) {
        return 'DEGRADED';
      } else {
        return 'HEALTHY';
      }
    } else {
      if (value <= threshold.critical) {
        return 'CRITICAL';
      } else if (value <= threshold.warning) {
        return 'WARNING';
      } else if (value <= threshold.warning * 1.2) {
        return 'DEGRADED';
      } else {
        return 'HEALTHY';
      }
    }
  }

  /**
   * Ajoute une métrique à l'historique
   * @param type Type de métrique
   * @param metric Métrique
   */
  private addToMetricHistory(type: string, metric: PerformanceMetric) {
    const history = this.metricHistory.get(type) || [];

    // Ajouter la métrique à l'historique
    history.push({ ...metric });

    // Limiter la taille de l'historique
    if (history.length > this.historyLimit) {
      history.shift();
    }

    // Mettre à jour l'historique
    this.metricHistory.set(type, history);
  }

  /**
   * Récupère l'historique d'une métrique
   * @param type Type de métrique
   * @returns Historique de la métrique
   */
  private getMetricHistory(type: string) {
    return this.metricHistory.get(type) || [];
  }

  /**
   * Calcule l'utilisation du CPU
   * @returns Utilisation du CPU en pourcentage
   */
  private async calculateCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.getCpuInfo();

      // Attendre 100ms pour mesurer l'utilisation du CPU
      setTimeout(() => {
        const endMeasure = this.getCpuInfo();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;

        const cpuUsage = 100 - Math.floor((idleDifference / totalDifference) * 100);
        resolve(cpuUsage);
      }, 100);
    });
  }

  /**
   * Récupère les informations CPU
   * @returns Informations CPU
   */
  private getCpuInfo() {
    const cpus = os.cpus();
    let idle = 0;
    let total = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        total += cpu.times[type];
      }
      idle += cpu.times.idle;
    }

    return { idle, total };
  }

  /**
   * Calcule l'utilisation de la mémoire
   * @returns Utilisation de la mémoire en pourcentage
   */
  private calculateMemoryUsage(): number {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    return Math.floor((usedMemory / totalMemory) * 100);
  }
}
