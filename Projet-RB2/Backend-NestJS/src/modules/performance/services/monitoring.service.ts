import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import { MetricsService } from './metrics.service';
import { AlertService } from './alert.service';
import { PerformanceMetricType } from '../enums/performance-metric-type.enum';
import { MonitoringStatus } from '../enums/monitoring-status.enum';
import { AlertSeverity } from '../enums/alert-severity.enum';
import { EventsService } from '../../events/events.service';

@Injectable()
export class MonitoringService implements OnModuleInit {
  private readonly logger = new Logger(MonitoringService.name);
  private isMonitoringEnabled = true;
  private monitoringInterval = 60000; // 1 minute par défaut
  private readonly endpoints: string[] = [];
  private readonly services: string[] = [];

  constructor(
    private readonly prisma: PrismaService,
    private readonly metricsService: MetricsService,
    private readonly alertService: AlertService,
    private readonly eventsService: EventsService,
  ) {}

  /**
   * Initialisation du module
   */
  async onModuleInit() {
    // Charger la configuration de monitoring
    await this.loadMonitoringConfig();

    // Démarrer le monitoring
    this.startMonitoring();
  }

  /**
   * Charge la configuration de monitoring
   */
  private async loadMonitoringConfig() {
    try {
      // Récupérer la configuration depuis la base de données
      const config = await this.prisma.monitoringConfig.findFirst();

      if (config) {
        this.isMonitoringEnabled = config.enabled;
        this.monitoringInterval = config.interval;

        // Charger les endpoints à surveiller
        const endpoints = await this.prisma.monitoredEndpoint.findMany({
          where: { enabled: true },
        });

        this.endpoints.length = 0;
        endpoints.forEach(endpoint => this.endpoints.push(endpoint.url));

        // Charger les services à surveiller
        const services = await this.prisma.monitoredService.findMany({
          where: { enabled: true },
        });

        this.services.length = 0;
        services.forEach(service => this.services.push(service.name));

        this.logger.log(`Configuration de monitoring chargée: ${this.endpoints.length} endpoints, ${this.services.length} services`);
      } else {
        // Créer une configuration par défaut
        await this.prisma.monitoringConfig.create({
          data: {
            enabled: true,
            interval: 60000,
            alertThreshold: 3,
            retentionDays: 30,
          },
        });

        this.logger.log('Configuration de monitoring par défaut créée');
      }
    } catch (error) {
      this.logger.error(`Erreur lors du chargement de la configuration de monitoring: ${error.message}`);
    }
  }

  /**
   * Démarre le monitoring
   */
  startMonitoring() {
    if (this.isMonitoringEnabled) {
      this.logger.log('Monitoring démarré');

      // Le monitoring est géré par les tâches cron
    } else {
      this.logger.log('Monitoring désactivé');
    }
  }

  /**
   * Arrête le monitoring
   */
  stopMonitoring() {
    this.isMonitoringEnabled = false;
    this.logger.log('Monitoring arrêté');
  }

  /**
   * Tâche cron pour collecter les métriques système
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async collectSystemMetrics() {
    if (!this.isMonitoringEnabled) return;

    try {
      this.logger.debug('Collecte des métriques système');

      // Récupérer les métriques système
      const systemMetrics = await this.metricsService.getSystemMetrics();

      // Vérifier les seuils d'alerte
      if (systemMetrics.cpuUsage.status === 'CRITICAL') {
        await this.alertService.createAlert({
          type: 'SYSTEM',
          severity: AlertSeverity.CRITICAL,
          message: `Utilisation CPU critique: ${systemMetrics.cpuUsage.value}%`,
          source: 'MONITORING',
          metadata: {
            metricType: PerformanceMetricType.CPU_USAGE,
            value: systemMetrics.cpuUsage.value,
            threshold: 90,
          },
        });
      } else if (systemMetrics.cpuUsage.status === 'WARNING') {
        await this.alertService.createAlert({
          type: 'SYSTEM',
          severity: AlertSeverity.WARNING,
          message: `Utilisation CPU élevée: ${systemMetrics.cpuUsage.value}%`,
          source: 'MONITORING',
          metadata: {
            metricType: PerformanceMetricType.CPU_USAGE,
            value: systemMetrics.cpuUsage.value,
            threshold: 70,
          },
        });
      }

      if (systemMetrics.memoryUsage.status === 'CRITICAL') {
        await this.alertService.createAlert({
          type: 'SYSTEM',
          severity: AlertSeverity.CRITICAL,
          message: `Utilisation mémoire critique: ${systemMetrics.memoryUsage.value}%`,
          source: 'MONITORING',
          metadata: {
            metricType: PerformanceMetricType.MEMORY_USAGE,
            value: systemMetrics.memoryUsage.value,
            threshold: 90,
          },
        });
      } else if (systemMetrics.memoryUsage.status === 'WARNING') {
        await this.alertService.createAlert({
          type: 'SYSTEM',
          severity: AlertSeverity.WARNING,
          message: `Utilisation mémoire élevée: ${systemMetrics.memoryUsage.value}%`,
          source: 'MONITORING',
          metadata: {
            metricType: PerformanceMetricType.MEMORY_USAGE,
            value: systemMetrics.memoryUsage.value,
            threshold: 70,
          },
        });
      }

      if (systemMetrics.diskUsage.status === 'CRITICAL') {
        await this.alertService.createAlert({
          type: 'SYSTEM',
          severity: AlertSeverity.CRITICAL,
          message: `Utilisation disque critique: ${systemMetrics.diskUsage.value}%`,
          source: 'MONITORING',
          metadata: {
            metricType: PerformanceMetricType.DISK_USAGE,
            value: systemMetrics.diskUsage.value,
            threshold: 90,
          },
        });
      } else if (systemMetrics.diskUsage.status === 'WARNING') {
        await this.alertService.createAlert({
          type: 'SYSTEM',
          severity: AlertSeverity.WARNING,
          message: `Utilisation disque élevée: ${systemMetrics.diskUsage.value}%`,
          source: 'MONITORING',
          metadata: {
            metricType: PerformanceMetricType.DISK_USAGE,
            value: systemMetrics.diskUsage.value,
            threshold: 70,
          },
        });
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la collecte des métriques système: ${error.message}`);
    }
  }

  /**
   * Tâche cron pour surveiller les endpoints
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async monitorEndpoints() {
    if (!this.isMonitoringEnabled || this.endpoints.length === 0) return;

    try {
      this.logger.debug(`Surveillance de ${this.endpoints.length} endpoints`);

      for (const endpoint of this.endpoints) {
        try {
          const startTime = Date.now();
          const response = await fetch(endpoint);
          const responseTime = Date.now() - startTime;

          // Enregistrer la métrique
          await this.metricsService.recordApiMetric(endpoint, responseTime, response.status);

          // Vérifier le statut de la réponse
          if (!response.ok) {
            await this.alertService.createAlert({
              type: 'ENDPOINT',
              severity: AlertSeverity.WARNING,
              message: `Endpoint ${endpoint} a retourné un statut ${response.status}`,
              source: 'MONITORING',
              metadata: {
                endpoint,
                statusCode: response.status,
                responseTime,
              },
            });
          }

          // Vérifier le temps de réponse
          if (responseTime > 2000) {
            await this.alertService.createAlert({
              type: 'ENDPOINT',
              severity: AlertSeverity.WARNING,
              message: `Endpoint ${endpoint} a un temps de réponse lent: ${responseTime}ms`,
              source: 'MONITORING',
              metadata: {
                endpoint,
                responseTime,
                threshold: 2000,
              },
            });
          }

          // Mettre à jour le statut de l'endpoint
          await this.prisma.monitoredEndpoint.updateMany({
            where: { url: endpoint },
            data: {
              lastChecked: new Date(),
              lastStatus: response.ok ? MonitoringStatus.HEALTHY : MonitoringStatus.UNHEALTHY,
              lastResponseTime: responseTime,
              lastStatusCode: response.status,
            },
          });
        } catch (error) {
          this.logger.error(`Erreur lors de la surveillance de l'endpoint ${endpoint}: ${error.message}`);

          // Créer une alerte
          await this.alertService.createAlert({
            type: 'ENDPOINT',
            severity: AlertSeverity.CRITICAL,
            message: `Endpoint ${endpoint} est inaccessible: ${error.message}`,
            source: 'MONITORING',
            metadata: {
              endpoint,
              error: error.message,
            },
          });

          // Mettre à jour le statut de l'endpoint
          await this.prisma.monitoredEndpoint.updateMany({
            where: { url: endpoint },
            data: {
              lastChecked: new Date(),
              lastStatus: MonitoringStatus.DOWN,
              lastResponseTime: null,
              lastStatusCode: null,
            },
          });
        }
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la surveillance des endpoints: ${error.message}`);
    }
  }

  /**
   * Tâche cron pour surveiller les services
   */
  @Cron(CronExpression.EVERY_10_MINUTES)
  async monitorServices() {
    if (!this.isMonitoringEnabled || this.services.length === 0) return;

    try {
      this.logger.debug(`Surveillance de ${this.services.length} services`);

      for (const service of this.services) {
        try {
          // Simuler la vérification du service
          const isHealthy = Math.random() > 0.1; // 90% de chance d'être en bonne santé

          // Mettre à jour le statut du service
          await this.prisma.monitoredService.updateMany({
            where: { name: service },
            data: {
              lastChecked: new Date(),
              lastStatus: isHealthy ? MonitoringStatus.HEALTHY : MonitoringStatus.UNHEALTHY,
            },
          });

          if (!isHealthy) {
            await this.alertService.createAlert({
              type: 'SERVICE',
              severity: AlertSeverity.WARNING,
              message: `Service ${service} est en mauvaise santé`,
              source: 'MONITORING',
              metadata: {
                service,
              },
            });
          }
        } catch (error) {
          this.logger.error(`Erreur lors de la surveillance du service ${service}: ${error.message}`);

          // Créer une alerte
          await this.alertService.createAlert({
            type: 'SERVICE',
            severity: AlertSeverity.CRITICAL,
            message: `Service ${service} est inaccessible: ${error.message}`,
            source: 'MONITORING',
            metadata: {
              service,
              error: error.message,
            },
          });

          // Mettre à jour le statut du service
          await this.prisma.monitoredService.updateMany({
            where: { name: service },
            data: {
              lastChecked: new Date(),
              lastStatus: MonitoringStatus.DOWN,
            },
          });
        }
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la surveillance des services: ${error.message}`);
    }
  }

  /**
   * Tâche cron pour nettoyer les anciennes données de monitoring
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupMonitoringData() {
    if (!this.isMonitoringEnabled) return;

    try {
      this.logger.debug('Nettoyage des anciennes données de monitoring');

      // Récupérer la configuration
      const config = await this.prisma.monitoringConfig.findFirst();

      if (!config) return;

      const retentionDays = config.retentionDays || 30;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // Supprimer les anciennes métriques API
      const deletedApiMetrics = await this.prisma.apiMetric.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // Supprimer les anciennes métriques de base de données
      const deletedDbMetrics = await this.prisma.databaseMetric.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // Supprimer les anciennes métriques de cache
      const deletedCacheMetrics = await this.prisma.cacheMetric.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // Supprimer les anciennes alertes résolues
      const deletedAlerts = await this.prisma.alert.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
          status: 'RESOLVED',
        },
      });

      this.logger.log(`Nettoyage terminé: ${deletedApiMetrics.count} métriques API, ${deletedDbMetrics.count} métriques DB, ${deletedCacheMetrics.count} métriques cache, ${deletedAlerts.count} alertes`);
    } catch (error) {
      this.logger.error(`Erreur lors du nettoyage des données de monitoring: ${error.message}`);
    }
  }

  /**
   * Ajoute un endpoint à surveiller
   * @param url URL de l'endpoint
   * @param name Nom de l'endpoint
   * @param description Description de l'endpoint
   * @returns L'endpoint créé
   */
  async addEndpoint(url: string, name: string, description?: string) {
    try {
      // Vérifier si l'endpoint existe déjà
      const existingEndpoint = await this.prisma.monitoredEndpoint.findFirst({
        where: { url },
      });

      if (existingEndpoint) {
        return existingEndpoint;
      }

      // Créer l'endpoint
      const endpoint = await this.prisma.monitoredEndpoint.create({
        data: {
          url,
          name,
          description,
          enabled: true,
          lastStatus: MonitoringStatus.UNKNOWN,
        },
      });

      // Ajouter l'endpoint à la liste
      if (!this.endpoints.includes(url)) {
        this.endpoints.push(url);
      }

      this.logger.log(`Endpoint ajouté: ${url}`);
      return endpoint;
    } catch (error) {
      this.logger.error(`Erreur lors de l'ajout de l'endpoint: ${error.message}`);
      throw error;
    }
  }

  /**
   * Supprime un endpoint à surveiller
   * @param url URL de l'endpoint
   * @returns L'endpoint supprimé
   */
  async removeEndpoint(url: string) {
    try {
      // Supprimer l'endpoint
      const endpoint = await this.prisma.monitoredEndpoint.deleteMany({
        where: { url },
      });

      // Supprimer l'endpoint de la liste
      const index = this.endpoints.indexOf(url);
      if (index !== -1) {
        this.endpoints.splice(index, 1);
      }

      this.logger.log(`Endpoint supprimé: ${url}`);
      return endpoint;
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de l'endpoint: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ajoute un service à surveiller
   * @param name Nom du service
   * @param description Description du service
   * @param healthCheckUrl URL de vérification de santé du service
   * @returns Le service créé
   */
  async addService(name: string, description?: string, healthCheckUrl?: string) {
    try {
      // Vérifier si le service existe déjà
      const existingService = await this.prisma.monitoredService.findFirst({
        where: { name },
      });

      if (existingService) {
        return existingService;
      }

      // Créer le service
      const service = await this.prisma.monitoredService.create({
        data: {
          name,
          description,
          healthCheckUrl,
          enabled: true,
          lastStatus: MonitoringStatus.UNKNOWN,
        },
      });

      // Ajouter le service à la liste
      if (!this.services.includes(name)) {
        this.services.push(name);
      }

      this.logger.log(`Service ajouté: ${name}`);
      return service;
    } catch (error) {
      this.logger.error(`Erreur lors de l'ajout du service: ${error.message}`);
      throw error;
    }
  }

  /**
   * Supprime un service à surveiller
   * @param name Nom du service
   * @returns Le service supprimé
   */
  async removeService(name: string) {
    try {
      // Supprimer le service
      const service = await this.prisma.monitoredService.deleteMany({
        where: { name },
      });

      // Supprimer le service de la liste
      const index = this.services.indexOf(name);
      if (index !== -1) {
        this.services.splice(index, 1);
      }

      this.logger.log(`Service supprimé: ${name}`);
      return service;
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du service: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour la configuration de monitoring
   * @param enabled Activer/désactiver le monitoring
   * @param interval Intervalle de monitoring en ms
   * @param alertThreshold Seuil d'alerte
   * @param retentionDays Jours de rétention des données
   * @returns La configuration mise à jour
   */
  async updateConfig(enabled?: boolean, interval?: number, alertThreshold?: number, retentionDays?: number) {
    try {
      // Récupérer la configuration existante
      const existingConfig = await this.prisma.monitoringConfig.findFirst();

      if (!existingConfig) {
        // Créer une nouvelle configuration
        const config = await this.prisma.monitoringConfig.create({
          data: {
            enabled: enabled ?? true,
            interval: interval ?? 60000,
            alertThreshold: alertThreshold ?? 3,
            retentionDays: retentionDays ?? 30,
          },
        });

        // Mettre à jour les variables locales
        this.isMonitoringEnabled = config.enabled;
        this.monitoringInterval = config.interval;

        this.logger.log('Configuration de monitoring créée');
        return config;
      }

      // Mettre à jour la configuration
      const config = await this.prisma.monitoringConfig.update({
        where: { id: existingConfig.id },
        data: {
          enabled: enabled !== undefined ? enabled : existingConfig.enabled,
          interval: interval !== undefined ? interval : existingConfig.interval,
          alertThreshold: alertThreshold !== undefined ? alertThreshold : existingConfig.alertThreshold,
          retentionDays: retentionDays !== undefined ? retentionDays : existingConfig.retentionDays,
        },
      });

      // Mettre à jour les variables locales
      this.isMonitoringEnabled = config.enabled;
      this.monitoringInterval = config.interval;

      // Si le monitoring est activé/désactivé, démarrer/arrêter le monitoring
      if (enabled !== undefined) {
        if (enabled) {
          this.startMonitoring();
        } else {
          this.stopMonitoring();
        }
      }

      this.logger.log('Configuration de monitoring mise à jour');
      return config;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la configuration de monitoring: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère la configuration de monitoring
   * @returns La configuration de monitoring
   */
  async getConfig() {
    try {
      // Récupérer la configuration
      const config = await this.prisma.monitoringConfig.findFirst();

      if (!config) {
        // Créer une configuration par défaut
        return this.updateConfig();
      }

      return config;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de la configuration de monitoring: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les endpoints surveillés
   * @returns Liste des endpoints surveillés
   */
  async getEndpoints() {
    try {
      return this.prisma.monitoredEndpoint.findMany({
        orderBy: {
          name: 'asc',
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des endpoints: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les services surveillés
   * @returns Liste des services surveillés
   */
  async getServices() {
    try {
      return this.prisma.monitoredService.findMany({
        orderBy: {
          name: 'asc',
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des services: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère le statut global du monitoring
   * @returns Statut global du monitoring
   */
  async getStatus() {
    try {
      // Récupérer les statuts des endpoints
      const endpoints = await this.prisma.monitoredEndpoint.findMany({
        select: {
          lastStatus: true,
        },
      });

      // Récupérer les statuts des services
      const services = await this.prisma.monitoredService.findMany({
        select: {
          lastStatus: true,
        },
      });

      // Récupérer les alertes actives
      const activeAlerts = await this.prisma.alert.count({
        where: {
          status: {
            in: ['OPEN', 'ACKNOWLEDGED'],
          },
        },
      });

      // Récupérer les métriques système
      const systemMetrics = await this.metricsService.getSystemMetrics();

      // Déterminer le statut global
      let status = MonitoringStatus.HEALTHY;

      // Vérifier les endpoints
      const unhealthyEndpoints = endpoints.filter(e => e.lastStatus !== MonitoringStatus.HEALTHY).length;
      if (unhealthyEndpoints > 0) {
        status = MonitoringStatus.DEGRADED;
      }

      // Vérifier les services
      const unhealthyServices = services.filter(s => s.lastStatus !== MonitoringStatus.HEALTHY).length;
      if (unhealthyServices > 0) {
        status = MonitoringStatus.DEGRADED;
      }

      // Vérifier les alertes
      if (activeAlerts > 0) {
        status = MonitoringStatus.WARNING;
      }

      // Vérifier les métriques système
      if (
        systemMetrics.cpuUsage.status === 'CRITICAL' ||
        systemMetrics.memoryUsage.status === 'CRITICAL' ||
        systemMetrics.diskUsage.status === 'CRITICAL'
      ) {
        status = MonitoringStatus.CRITICAL;
      } else if (
        systemMetrics.cpuUsage.status === 'WARNING' ||
        systemMetrics.memoryUsage.status === 'WARNING' ||
        systemMetrics.diskUsage.status === 'WARNING'
      ) {
        status = status === MonitoringStatus.HEALTHY ? MonitoringStatus.WARNING : status;
      }

      return {
        status,
        isMonitoringEnabled: this.isMonitoringEnabled,
        endpoints: {
          total: endpoints.length,
          healthy: endpoints.filter(e => e.lastStatus === MonitoringStatus.HEALTHY).length,
          unhealthy: endpoints.filter(e => e.lastStatus === MonitoringStatus.UNHEALTHY).length,
          down: endpoints.filter(e => e.lastStatus === MonitoringStatus.DOWN).length,
          unknown: endpoints.filter(e => e.lastStatus === MonitoringStatus.UNKNOWN).length,
        },
        services: {
          total: services.length,
          healthy: services.filter(s => s.lastStatus === MonitoringStatus.HEALTHY).length,
          unhealthy: services.filter(s => s.lastStatus === MonitoringStatus.UNHEALTHY).length,
          down: services.filter(s => s.lastStatus === MonitoringStatus.DOWN).length,
          unknown: services.filter(s => s.lastStatus === MonitoringStatus.UNKNOWN).length,
        },
        alerts: {
          active: activeAlerts,
        },
        systemMetrics: {
          cpuUsage: systemMetrics.cpuUsage,
          memoryUsage: systemMetrics.memoryUsage,
          diskUsage: systemMetrics.diskUsage,
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du statut global: ${error.message}`);
      throw error;
    }
  }
}
