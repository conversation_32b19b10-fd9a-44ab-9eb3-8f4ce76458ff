import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { MetricsService } from './metrics.service';
import { MonitoringService } from './monitoring.service';
import { AlertService } from './alert.service';
import { ProfilingService } from './profiling.service';
import { PerformanceReport } from '../interfaces/performance-report.interface';
import { PerformanceThreshold } from '../interfaces/performance-threshold.interface';
import { PerformanceMetricType } from '../enums/performance-metric-type.enum';
import { PerformanceStatus } from '../enums/performance-status.enum';

@Injectable()
export class PerformanceService {
  private readonly logger = new Logger(PerformanceService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly metricsService: MetricsService,
    private readonly monitoringService: MonitoringService,
    private readonly alertService: AlertService,
    private readonly profilingService: ProfilingService,
  ) {}

  /**
   * Génère un rapport de performance complet
   * @returns Rapport de performance
   */
  async generatePerformanceReport(): Promise<PerformanceReport> {
    try {
      this.logger.log('Génération d\'un rapport de performance complet');
      
      // Récupérer les métriques de performance
      const [
        systemMetrics,
        apiMetrics,
        databaseMetrics,
        cacheMetrics,
        customMetrics,
      ] = await Promise.all([
        this.metricsService.getSystemMetrics(),
        this.metricsService.getApiMetrics(),
        this.metricsService.getDatabaseMetrics(),
        this.metricsService.getCacheMetrics(),
        this.metricsService.getCustomMetrics(),
      ]);
      
      // Récupérer les alertes récentes
      const recentAlerts = await this.alertService.getRecentAlerts();
      
      // Récupérer les résultats de profilage
      const profilingResults = await this.profilingService.getProfilingResults();
      
      // Évaluer le statut global de performance
      const performanceStatus = this.evaluatePerformanceStatus({
        systemMetrics,
        apiMetrics,
        databaseMetrics,
        cacheMetrics,
      });
      
      // Générer des recommandations
      const recommendations = this.generateRecommendations({
        systemMetrics,
        apiMetrics,
        databaseMetrics,
        cacheMetrics,
        performanceStatus,
        recentAlerts,
      });
      
      // Créer le rapport
      const report: PerformanceReport = {
        timestamp: new Date(),
        performanceStatus,
        metrics: {
          system: systemMetrics,
          api: apiMetrics,
          database: databaseMetrics,
          cache: cacheMetrics,
          custom: customMetrics,
        },
        alerts: recentAlerts,
        profiling: profilingResults,
        recommendations,
      };
      
      // Enregistrer le rapport dans la base de données
      await this.savePerformanceReport(report);
      
      this.logger.log('Rapport de performance généré avec succès');
      return report;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du rapport de performance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les rapports de performance avec pagination
   * @param page Numéro de page
   * @param limit Nombre d'éléments par page
   * @returns Liste paginée des rapports de performance
   */
  async getPerformanceReports(page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    
    const [reports, total] = await Promise.all([
      this.prisma.performanceReport.findMany({
        skip,
        take: limit,
        orderBy: {
          timestamp: 'desc',
        },
      }),
      this.prisma.performanceReport.count(),
    ]);
    
    return {
      reports,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Récupère un rapport de performance par son ID
   * @param id ID du rapport
   * @returns Le rapport de performance
   */
  async getPerformanceReportById(id: string) {
    return this.prisma.performanceReport.findUnique({
      where: { id },
    });
  }

  /**
   * Récupère le dernier rapport de performance
   * @returns Le dernier rapport de performance
   */
  async getLatestPerformanceReport() {
    return this.prisma.performanceReport.findFirst({
      orderBy: {
        timestamp: 'desc',
      },
    });
  }

  /**
   * Définit des seuils de performance
   * @param thresholds Seuils de performance
   * @returns Les seuils de performance mis à jour
   */
  async setPerformanceThresholds(thresholds: PerformanceThreshold[]) {
    try {
      this.logger.log(`Définition de ${thresholds.length} seuils de performance`);
      
      // Supprimer les seuils existants
      await this.prisma.performanceThreshold.deleteMany({
        where: {
          metricType: {
            in: thresholds.map(threshold => threshold.metricType),
          },
        },
      });
      
      // Créer les nouveaux seuils
      const createdThresholds = await Promise.all(
        thresholds.map(threshold =>
          this.prisma.performanceThreshold.create({
            data: {
              metricType: threshold.metricType,
              warningThreshold: threshold.warningThreshold,
              criticalThreshold: threshold.criticalThreshold,
              enabled: threshold.enabled ?? true,
              description: threshold.description,
            },
          }),
        ),
      );
      
      this.logger.log(`${createdThresholds.length} seuils de performance définis avec succès`);
      return createdThresholds;
    } catch (error) {
      this.logger.error(`Erreur lors de la définition des seuils de performance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les seuils de performance
   * @returns Liste des seuils de performance
   */
  async getPerformanceThresholds() {
    return this.prisma.performanceThreshold.findMany({
      orderBy: {
        metricType: 'asc',
      },
    });
  }

  /**
   * Évalue le statut de performance global
   * @param metrics Métriques de performance
   * @returns Statut de performance
   */
  private evaluatePerformanceStatus(metrics: Record<string, any>): PerformanceStatus {
    // Récupérer les métriques critiques
    const criticalMetrics = [
      metrics.systemMetrics.cpuUsage,
      metrics.systemMetrics.memoryUsage,
      metrics.apiMetrics.averageResponseTime,
      metrics.databaseMetrics.averageQueryTime,
    ];
    
    // Vérifier si des métriques sont critiques
    const hasCriticalMetrics = criticalMetrics.some(metric => metric.status === 'CRITICAL');
    if (hasCriticalMetrics) {
      return PerformanceStatus.CRITICAL;
    }
    
    // Vérifier si des métriques sont en alerte
    const hasWarningMetrics = criticalMetrics.some(metric => metric.status === 'WARNING');
    if (hasWarningMetrics) {
      return PerformanceStatus.WARNING;
    }
    
    // Vérifier si des métriques sont dégradées
    const hasDegradedMetrics = criticalMetrics.some(metric => metric.status === 'DEGRADED');
    if (hasDegradedMetrics) {
      return PerformanceStatus.DEGRADED;
    }
    
    return PerformanceStatus.HEALTHY;
  }

  /**
   * Génère des recommandations basées sur les métriques
   * @param data Données de performance
   * @returns Liste des recommandations
   */
  private generateRecommendations(data: Record<string, any>): string[] {
    const recommendations: string[] = [];
    
    // Recommandations basées sur les métriques système
    if (data.systemMetrics.cpuUsage.value > 80) {
      recommendations.push('Optimiser l\'utilisation du CPU ou augmenter les ressources CPU disponibles.');
    }
    
    if (data.systemMetrics.memoryUsage.value > 80) {
      recommendations.push('Optimiser l\'utilisation de la mémoire ou augmenter la mémoire disponible.');
    }
    
    // Recommandations basées sur les métriques API
    if (data.apiMetrics.averageResponseTime.value > 500) {
      recommendations.push('Optimiser les temps de réponse de l\'API en améliorant les performances des endpoints les plus lents.');
    }
    
    if (data.apiMetrics.errorRate.value > 1) {
      recommendations.push('Réduire le taux d\'erreur de l\'API en corrigeant les endpoints problématiques.');
    }
    
    // Recommandations basées sur les métriques de base de données
    if (data.databaseMetrics.averageQueryTime.value > 100) {
      recommendations.push('Optimiser les requêtes de base de données en ajoutant des index ou en réécrivant les requêtes les plus lentes.');
    }
    
    if (data.databaseMetrics.connectionPoolUsage.value > 80) {
      recommendations.push('Augmenter la taille du pool de connexions à la base de données ou optimiser l\'utilisation des connexions.');
    }
    
    // Recommandations basées sur les métriques de cache
    if (data.cacheMetrics.hitRate.value < 70) {
      recommendations.push('Améliorer l\'utilisation du cache en optimisant les stratégies de mise en cache.');
    }
    
    // Recommandations basées sur le statut global
    if (data.performanceStatus === PerformanceStatus.CRITICAL) {
      recommendations.push('Résoudre immédiatement les problèmes critiques de performance pour éviter les interruptions de service.');
    }
    
    // Recommandations basées sur les alertes récentes
    if (data.recentAlerts.length > 0) {
      recommendations.push('Analyser et résoudre les alertes récentes pour améliorer la stabilité du système.');
    }
    
    return recommendations;
  }

  /**
   * Enregistre un rapport de performance dans la base de données
   * @param report Rapport de performance
   * @returns Le rapport enregistré
   */
  private async savePerformanceReport(report: PerformanceReport) {
    return this.prisma.performanceReport.create({
      data: {
        timestamp: report.timestamp,
        status: report.performanceStatus,
        metrics: report.metrics,
        alerts: report.alerts,
        profiling: report.profiling,
        recommendations: report.recommendations,
      },
    });
  }
}
