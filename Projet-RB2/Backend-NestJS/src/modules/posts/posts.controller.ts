import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Res, HttpStatus } from '@nestjs/common';
import { PostsService } from './posts.service';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { sanitizeHtml } from '../../utils/sanitize-html';

@ApiTags('posts')
@Controller('posts')
export class PostsController {
  constructor(private readonly postsService: PostsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new post' })
  @ApiResponse({ status: 201, description: 'The post has been successfully created.' })
  create(@Body() createPostDto: CreatePostDto) {
    return this.postsService.create(createPostDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all posts' })
  @ApiResponse({ status: 200, description: 'Return all posts.' })
  findAll() {
    return this.postsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a post by id' })
  @ApiResponse({ status: 200, description: 'Return the post.' })
  @ApiResponse({ status: 404, description: 'Post not found.' })
  findOne(@Param('id') id: string) {
    return this.postsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a post' })
  @ApiResponse({ status: 200, description: 'The post has been successfully updated.' })
  @ApiResponse({ status: 404, description: 'Post not found.' })
  update(@Param('id') id: string, @Body() updatePostDto: UpdatePostDto) {
    return this.postsService.update(id, updatePostDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a post' })
  @ApiResponse({ status: 200, description: 'The post has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Post not found.' })
  remove(@Param('id') id: string) {
    return this.postsService.remove(id);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search posts by title or content' })
  @ApiResponse({ status: 200, description: 'Return matching posts.' })
  search(@Query('query') query: string) {
    return this.postsService.search(query);
  }

  @Get('by-author/:authorId')
  @ApiOperation({ summary: 'Get posts by author' })
  @ApiResponse({ status: 200, description: 'Return posts by author.' })
  findByAuthor(@Param('authorId') authorId: string) {
    return this.postsService.findByAuthor(authorId);
  }

  // Fixed XSS vulnerability by sanitizing user content
  @Get('preview/:id')
  @ApiOperation({ summary: 'Get a post preview with HTML content' })
  @ApiResponse({ status: 200, description: 'Return the post preview.' })
  @ApiResponse({ status: 404, description: 'Post not found.' })
  async getPostPreview(@Param('id') id: string, @Res() res: Response) {
    const post = await this.postsService.findOne(id);

    // Sanitize user content to prevent XSS attacks
    const sanitizedTitle = sanitizeHtml(post.title);
    const sanitizedAuthorName = sanitizeHtml(post.authorName);
    const sanitizedContent = sanitizeHtml(post.content);

    const html = `
      <html>
        <head>
          <title>${sanitizedTitle}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; }
            .content { line-height: 1.6; }
          </style>
        </head>
        <body>
          <h1>${sanitizedTitle}</h1>
          <div class="author">By: ${sanitizedAuthorName}</div>
          <div class="content">${sanitizedContent}</div>
        </body>
      </html>
    `;

    res.status(HttpStatus.OK).send(html);
  }
}
