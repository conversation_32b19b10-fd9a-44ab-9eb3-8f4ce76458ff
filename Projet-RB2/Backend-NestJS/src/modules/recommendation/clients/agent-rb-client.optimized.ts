import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom, map, timeout } from 'rxjs';
import { AxiosError } from 'axios';
import { RecommendationType } from '../enums/recommendation-type.enum';
import * as CircuitBreaker from 'opossum';
import * as pLimit from 'p-limit';
import { RecommendationCacheService } from '../services/recommendation-cache.service';

/**
 * Client optimisé pour communiquer avec le service Agent-RB
 */
@Injectable()
export class AgentRbClient {
  private readonly logger = new Logger(AgentRbClient.name);
  private readonly baseUrl: string;
  private readonly timeoutMs: number;
  private readonly circuitBreaker: {
    enabled: boolean;
    failureThreshold: number;
    resetTimeout: number;
  };
  private readonly breakers: Map<string, CircuitBreaker> = new Map();
  private readonly requestLimiter: ReturnType<typeof pLimit>;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly cacheService: RecommendationCacheService,
  ) {
    // Récupérer la configuration
    const agentRbConfig = this.configService.get('recommendation.agentRb');
    
    this.baseUrl = agentRbConfig.serviceUrl;
    this.timeoutMs = agentRbConfig.timeoutMs;
    this.circuitBreaker = agentRbConfig.circuitBreaker;
    
    // Créer un limiteur de requêtes parallèles
    this.requestLimiter = pLimit(agentRbConfig.maxConcurrentRequests);
    
    if (!this.baseUrl) {
      this.logger.error('Agent-RB service URL is not defined in configuration');
      throw new Error('Agent-RB service URL is not defined');
    }
    
    this.logger.log(`AgentRbClient initialized with baseUrl: ${this.baseUrl}`);
    this.logger.log(`Circuit breaker: ${this.circuitBreaker.enabled ? 'enabled' : 'disabled'}`);
    this.logger.log(`Max concurrent requests: ${agentRbConfig.maxConcurrentRequests}`);
  }

  /**
   * Récupère les détails d'une retraite
   * @param retreatId ID de la retraite
   * @returns Détails de la retraite
   */
  async getRetreat(retreatId: string): Promise<any> {
    this.logger.debug(`Fetching retreat details for ID: ${retreatId}`);
    
    return this.cacheService.getCachedItemDetails(
      RecommendationType.RETREAT,
      retreatId,
      () => this.executeRequest(`getRetreat:${retreatId}`, () => 
        firstValueFrom(
          this.httpService.get(`${this.baseUrl}/api/retreats/${retreatId}`)
            .pipe(
              timeout(this.timeoutMs),
              map(res => res.data),
              catchError(this.handleError('getRetreat', retreatId)),
            ),
        )
      )
    );
  }

  /**
   * Récupère une liste de retraites
   * @param filters Filtres à appliquer
   * @returns Liste de retraites
   */
  async getRetreats(filters?: Record<string, any>): Promise<any> {
    this.logger.debug(`Fetching retreats with filters: ${JSON.stringify(filters)}`);
    
    return this.executeRequest('getRetreats', () => 
      firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/retreats`, { params: filters })
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getRetreats')),
          ),
      )
    );
  }

  /**
   * Récupère les détails d'un partenaire
   * @param partnerId ID du partenaire
   * @returns Détails du partenaire
   */
  async getPartner(partnerId: string): Promise<any> {
    this.logger.debug(`Fetching partner details for ID: ${partnerId}`);
    
    return this.cacheService.getCachedItemDetails(
      RecommendationType.PARTNER,
      partnerId,
      () => this.executeRequest(`getPartner:${partnerId}`, () => 
        firstValueFrom(
          this.httpService.get(`${this.baseUrl}/api/partners/${partnerId}`)
            .pipe(
              timeout(this.timeoutMs),
              map(res => res.data),
              catchError(this.handleError('getPartner', partnerId)),
            ),
        )
      )
    );
  }

  /**
   * Récupère une liste de partenaires
   * @param filters Filtres à appliquer
   * @returns Liste de partenaires
   */
  async getPartners(filters?: Record<string, any>): Promise<any> {
    this.logger.debug(`Fetching partners with filters: ${JSON.stringify(filters)}`);
    
    return this.executeRequest('getPartners', () => 
      firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/partners`, { params: filters })
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getPartners')),
          ),
      )
    );
  }

  /**
   * Récupère les détails d'un cours
   * @param courseId ID du cours
   * @returns Détails du cours
   */
  async getCourse(courseId: string): Promise<any> {
    this.logger.debug(`Fetching course details for ID: ${courseId}`);
    
    return this.cacheService.getCachedItemDetails(
      RecommendationType.COURSE,
      courseId,
      () => this.executeRequest(`getCourse:${courseId}`, () => 
        firstValueFrom(
          this.httpService.get(`${this.baseUrl}/api/courses/${courseId}`)
            .pipe(
              timeout(this.timeoutMs),
              map(res => res.data),
              catchError(this.handleError('getCourse', courseId)),
            ),
        )
      )
    );
  }

  /**
   * Récupère une liste de cours
   * @param filters Filtres à appliquer
   * @returns Liste de cours
   */
  async getCourses(filters?: Record<string, any>): Promise<any> {
    this.logger.debug(`Fetching courses with filters: ${JSON.stringify(filters)}`);
    
    return this.executeRequest('getCourses', () => 
      firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/courses`, { params: filters })
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getCourses')),
          ),
      )
    );
  }

  /**
   * Récupère les détails d'un élément en fonction de son type
   * @param type Type d'élément
   * @param itemId ID de l'élément
   * @returns Détails de l'élément
   */
  async getItem(type: RecommendationType, itemId: string): Promise<any> {
    switch (type) {
      case RecommendationType.RETREAT:
        return this.getRetreat(itemId);
      case RecommendationType.PARTNER:
        return this.getPartner(itemId);
      case RecommendationType.COURSE:
        return this.getCourse(itemId);
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  }

  /**
   * Récupère une liste d'éléments en fonction de leur type
   * @param type Type d'élément
   * @param filters Filtres à appliquer
   * @returns Liste d'éléments
   */
  async getItems(type: RecommendationType, filters?: Record<string, any>): Promise<any> {
    switch (type) {
      case RecommendationType.RETREAT:
        return this.getRetreats(filters);
      case RecommendationType.PARTNER:
        return this.getPartners(filters);
      case RecommendationType.COURSE:
        return this.getCourses(filters);
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  }

  /**
   * Exécute une requête avec circuit breaker et limitation de requêtes parallèles
   * @param operationKey Clé d'opération pour le circuit breaker
   * @param requestFn Fonction de requête
   * @returns Résultat de la requête
   */
  private async executeRequest<T>(operationKey: string, requestFn: () => Promise<T>): Promise<T> {
    try {
      // Utiliser le circuit breaker si activé
      if (this.circuitBreaker.enabled) {
        // Créer un circuit breaker s'il n'existe pas déjà
        if (!this.breakers.has(operationKey)) {
          this.breakers.set(
            operationKey,
            new CircuitBreaker(requestFn, {
              failureThreshold: this.circuitBreaker.failureThreshold / 100, // Convertir le pourcentage en fraction
              resetTimeout: this.circuitBreaker.resetTimeout,
              errorThresholdPercentage: this.circuitBreaker.failureThreshold,
              timeout: this.timeoutMs * 1.5, // Timeout légèrement plus long que celui de la requête
            }),
          );
          
          // Ajouter des écouteurs d'événements
          const breaker = this.breakers.get(operationKey);
          breaker.on('open', () => this.logger.warn(`Circuit breaker for ${operationKey} is open`));
          breaker.on('close', () => this.logger.log(`Circuit breaker for ${operationKey} is closed`));
          breaker.on('halfOpen', () => this.logger.log(`Circuit breaker for ${operationKey} is half-open`));
        }
        
        // Exécuter la requête avec le circuit breaker et le limiteur
        return this.requestLimiter(() => this.breakers.get(operationKey).fire());
      }
      
      // Exécuter la requête avec le limiteur uniquement
      return this.requestLimiter(requestFn);
    } catch (error) {
      // Si le circuit breaker est ouvert
      if (error.message === 'Breaker is open') {
        this.logger.error(`Circuit breaker for ${operationKey} is open, request rejected`);
        throw new HttpException('Service temporarily unavailable', HttpStatus.SERVICE_UNAVAILABLE);
      }
      
      throw error;
    }
  }

  /**
   * Gère les erreurs des requêtes HTTP
   * @param operation Nom de l'opération
   * @param id ID de l'élément (optionnel)
   * @returns Fonction de gestion d'erreur
   */
  private handleError(operation: string, id?: string) {
    return (error: AxiosError) => {
      const idInfo = id ? ` with ID ${id}` : '';
      this.logger.error(`${operation}${idInfo} failed: ${error.message}`);
      
      if (error.response) {
        // La requête a été faite et le serveur a répondu avec un code d'erreur
        this.logger.error(`Response status: ${error.response.status}`);
        this.logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
        
        if (error.response.status === 404) {
          throw new HttpException(`Resource not found${idInfo}`, HttpStatus.NOT_FOUND);
        }
        
        throw new HttpException(
          error.response.data['message'] || `Error in ${operation}`,
          error.response.status,
        );
      } else if (error.request) {
        // La requête a été faite mais aucune réponse n'a été reçue
        throw new HttpException('No response from server', HttpStatus.SERVICE_UNAVAILABLE);
      } else {
        // Une erreur s'est produite lors de la configuration de la requête
        throw new HttpException('Request configuration error', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      
      throw error;
    };
  }
}
