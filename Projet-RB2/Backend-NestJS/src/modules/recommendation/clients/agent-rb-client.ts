import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom, map, timeout } from 'rxjs';
import { AxiosError } from 'axios';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Client pour communiquer avec le service Agent-RB
 */
@Injectable()
export class AgentRbClient {
  private readonly logger = new Logger(AgentRbClient.name);
  private readonly baseUrl: string;
  private readonly timeoutMs: number = 30000; // 30 secondes par défaut

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get<string>('AGENT_RB_SERVICE_URL');
    
    if (!this.baseUrl) {
      this.logger.error('AGENT_RB_SERVICE_URL is not defined in environment variables');
      throw new Error('AGENT_RB_SERVICE_URL is not defined');
    }
    
    const configTimeout = this.configService.get<number>('AGENT_RB_TIMEOUT_MS');
    if (configTimeout) {
      this.timeoutMs = configTimeout;
    }
    
    this.logger.log(`AgentRbClient initialized with baseUrl: ${this.baseUrl}`);
  }

  /**
   * Récupère les détails d'une retraite
   * @param retreatId ID de la retraite
   * @returns Détails de la retraite
   */
  async getRetreat(retreatId: string): Promise<any> {
    this.logger.debug(`Fetching retreat details for ID: ${retreatId}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/retreats/${retreatId}`)
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getRetreat', retreatId)),
          ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error fetching retreat details: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère une liste de retraites
   * @param filters Filtres à appliquer
   * @returns Liste de retraites
   */
  async getRetreats(filters?: Record<string, any>): Promise<any> {
    this.logger.debug(`Fetching retreats with filters: ${JSON.stringify(filters)}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/retreats`, { params: filters })
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getRetreats')),
          ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error fetching retreats: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les détails d'un partenaire
   * @param partnerId ID du partenaire
   * @returns Détails du partenaire
   */
  async getPartner(partnerId: string): Promise<any> {
    this.logger.debug(`Fetching partner details for ID: ${partnerId}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/partners/${partnerId}`)
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getPartner', partnerId)),
          ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error fetching partner details: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère une liste de partenaires
   * @param filters Filtres à appliquer
   * @returns Liste de partenaires
   */
  async getPartners(filters?: Record<string, any>): Promise<any> {
    this.logger.debug(`Fetching partners with filters: ${JSON.stringify(filters)}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/partners`, { params: filters })
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getPartners')),
          ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error fetching partners: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les détails d'un cours
   * @param courseId ID du cours
   * @returns Détails du cours
   */
  async getCourse(courseId: string): Promise<any> {
    this.logger.debug(`Fetching course details for ID: ${courseId}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/courses/${courseId}`)
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getCourse', courseId)),
          ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error fetching course details: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère une liste de cours
   * @param filters Filtres à appliquer
   * @returns Liste de cours
   */
  async getCourses(filters?: Record<string, any>): Promise<any> {
    this.logger.debug(`Fetching courses with filters: ${JSON.stringify(filters)}`);
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/api/courses`, { params: filters })
          .pipe(
            timeout(this.timeoutMs),
            map(res => res.data),
            catchError(this.handleError('getCourses')),
          ),
      );
      
      return response;
    } catch (error) {
      this.logger.error(`Error fetching courses: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les détails d'un élément en fonction de son type
   * @param type Type d'élément
   * @param itemId ID de l'élément
   * @returns Détails de l'élément
   */
  async getItem(type: RecommendationType, itemId: string): Promise<any> {
    switch (type) {
      case RecommendationType.RETREAT:
        return this.getRetreat(itemId);
      case RecommendationType.PARTNER:
        return this.getPartner(itemId);
      case RecommendationType.COURSE:
        return this.getCourse(itemId);
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  }

  /**
   * Récupère une liste d'éléments en fonction de leur type
   * @param type Type d'élément
   * @param filters Filtres à appliquer
   * @returns Liste d'éléments
   */
  async getItems(type: RecommendationType, filters?: Record<string, any>): Promise<any> {
    switch (type) {
      case RecommendationType.RETREAT:
        return this.getRetreats(filters);
      case RecommendationType.PARTNER:
        return this.getPartners(filters);
      case RecommendationType.COURSE:
        return this.getCourses(filters);
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  }

  /**
   * Gère les erreurs des requêtes HTTP
   * @param operation Nom de l'opération
   * @param id ID de l'élément (optionnel)
   * @returns Fonction de gestion d'erreur
   */
  private handleError(operation: string, id?: string) {
    return (error: AxiosError) => {
      const idInfo = id ? ` with ID ${id}` : '';
      this.logger.error(`${operation}${idInfo} failed: ${error.message}`);
      
      if (error.response) {
        // La requête a été faite et le serveur a répondu avec un code d'erreur
        this.logger.error(`Response status: ${error.response.status}`);
        this.logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
        
        if (error.response.status === 404) {
          throw new HttpException(`Resource not found${idInfo}`, HttpStatus.NOT_FOUND);
        }
        
        throw new HttpException(
          error.response.data['message'] || `Error in ${operation}`,
          error.response.status,
        );
      } else if (error.request) {
        // La requête a été faite mais aucune réponse n'a été reçue
        throw new HttpException('No response from server', HttpStatus.SERVICE_UNAVAILABLE);
      } else {
        // Une erreur s'est produite lors de la configuration de la requête
        throw new HttpException('Request configuration error', HttpStatus.INTERNAL_SERVER_ERROR);
      }
      
      throw error;
    };
  }
}
