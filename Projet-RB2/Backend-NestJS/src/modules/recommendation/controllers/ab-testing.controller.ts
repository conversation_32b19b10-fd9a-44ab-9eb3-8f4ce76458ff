import { Controller, Get, Post, Body, Param, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ABTestingService } from '../services/ab-testing.service';
import { ABTestGroup } from '../enums/ab-test-group.enum';

/**
 * Contrôleur pour la gestion des tests A/B des recommandations
 */
@ApiTags('ab-testing')
@Controller('ab-testing')
export class ABTestingController {
  private readonly logger = new Logger(ABTestingController.name);

  constructor(private readonly abTestingService: ABTestingService) {}

  /**
   * R<PERSON>cupère les résultats des tests A/B
   */
  @Get('results')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les résultats des tests A/B' })
  @ApiResponse({ status: 200, description: 'Résultats des tests A/B' })
  async getResults() {
    this.logger.log('Récupération des résultats des tests A/B');
    return this.abTestingService.getResults();
  }

  /**
   * Récupère le groupe de test d'un utilisateur
   * @param userId ID de l'utilisateur
   */
  @Get('user/:userId/group')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer le groupe de test d\'un utilisateur' })
  @ApiResponse({ status: 200, description: 'Groupe de test de l\'utilisateur' })
  async getUserGroup(@Param('userId') userId: string) {
    this.logger.log(`Récupération du groupe de test pour l'utilisateur ${userId}`);
    const group = await this.abTestingService.getTestGroup(userId);
    return { userId, group };
  }

  /**
   * Enregistre une interaction dans le cadre d'un test A/B
   * @param data Données de l'interaction
   */
  @Post('interaction')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une interaction dans le cadre d\'un test A/B' })
  @ApiResponse({ status: 201, description: 'Interaction enregistrée' })
  async trackInteraction(
    @Body() data: {
      userId: string;
      itemId: string;
      interactionType: string;
      group: ABTestGroup;
    },
  ) {
    const { userId, itemId, interactionType, group } = data;
    this.logger.log(`Enregistrement d'une interaction ${interactionType} pour l'utilisateur ${userId} dans le groupe ${group}`);
    
    await this.abTestingService.trackInteraction(userId, itemId, interactionType, group);
    
    return { success: true };
  }

  /**
   * Déclenche manuellement le calcul des métriques
   */
  @Post('calculate-metrics')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Déclencher manuellement le calcul des métriques' })
  @ApiResponse({ status: 201, description: 'Calcul des métriques déclenché' })
  async calculateMetrics() {
    this.logger.log('Déclenchement manuel du calcul des métriques');
    
    // Lancer le calcul des métriques en arrière-plan
    this.abTestingService.calculateMetrics().catch(error => {
      this.logger.error(`Erreur lors du calcul des métriques: ${error.message}`);
    });
    
    return { success: true, message: 'Calcul des métriques déclenché en arrière-plan' };
  }
}
