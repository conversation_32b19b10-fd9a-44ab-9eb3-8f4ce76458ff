import {
  <PERSON>,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { AdvancedContextService } from '../services/advanced-context.service';
import {
  AdvancedContextConfig,
  ContextSource,
  ContextSourceType,
  ContextDetectionStrategy,
  ContextAdaptationStrategy,
  ContextWeightingStrategy,
  ContextPersistenceStrategy,
  ContextualRecommendationRequest,
} from '../interfaces/advanced-context.interface';

/**
 * Controller for advanced contextual recommendations
 */
@ApiTags('Advanced Contextual Recommendations')
@Controller('recommendation/context')
export class AdvancedContextController {
  private readonly logger = new Logger(AdvancedContextController.name);

  constructor(private readonly advancedContextService: AdvancedContextService) {}

  /**
   * Get advanced context configuration
   */
  @ApiOperation({ summary: 'Get advanced context configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Advanced context configuration' })
  @Get('config')
  getContextConfig() {
    try {
      return this.advancedContextService.getContextConfig();
    } catch (error) {
      this.logger.error(`Error getting advanced context configuration: ${error.message}`);
      throw new HttpException('Failed to get advanced context configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update advanced context configuration
   */
  @ApiOperation({ summary: 'Update advanced context configuration' })
  @ApiBody({ description: 'Advanced context configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated advanced context configuration' })
  @Put('config')
  updateContextConfig(@Body() config: Partial<AdvancedContextConfig>) {
    try {
      return this.advancedContextService.updateContextConfig(config);
    } catch (error) {
      this.logger.error(`Error updating advanced context configuration: ${error.message}`);
      throw new HttpException('Failed to update advanced context configuration', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get context data for a user
   */
  @ApiOperation({ summary: 'Get context data for a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiBody({ description: 'Explicit context data', required: false })
  @ApiResponse({ status: HttpStatus.OK, description: 'Context data' })
  @Post('data/:userId')
  async getContextData(
    @Param('userId') userId: string,
    @Body() body?: { explicitContext?: Record<string, any> },
  ) {
    try {
      const contextData = await this.advancedContextService.getContextData(userId, body?.explicitContext);
      return contextData;
    } catch (error) {
      this.logger.error(`Error getting context data for user ${userId}: ${error.message}`);
      throw new HttpException('Failed to get context data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get contextual recommendations
   */
  @ApiOperation({ summary: 'Get contextual recommendations' })
  @ApiBody({ description: 'Contextual recommendation request' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Contextual recommendations' })
  @Post('recommendations')
  async getContextualRecommendations(@Body() request: ContextualRecommendationRequest) {
    try {
      const result = await this.advancedContextService.getContextualRecommendations(request);
      return result;
    } catch (error) {
      this.logger.error(`Error getting contextual recommendations for user ${request.userId}: ${error.message}`);
      throw new HttpException('Failed to get contextual recommendations', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get available context sources
   */
  @ApiOperation({ summary: 'Get available context sources' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Available context sources' })
  @Get('sources')
  getContextSources() {
    try {
      const config = this.advancedContextService.getContextConfig();
      return {
        sources: config.contextSources,
        count: config.contextSources.length,
      };
    } catch (error) {
      this.logger.error(`Error getting context sources: ${error.message}`);
      throw new HttpException('Failed to get context sources', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update context source
   */
  @ApiOperation({ summary: 'Update context source' })
  @ApiParam({ name: 'type', description: 'Context source type' })
  @ApiBody({ description: 'Context source configuration' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated context source' })
  @Put('sources/:type')
  updateContextSource(
    @Param('type') type: ContextSourceType,
    @Body() source: Partial<ContextSource>,
  ) {
    try {
      const config = this.advancedContextService.getContextConfig();
      
      // Find the source to update
      const sourceIndex = config.contextSources.findIndex(s => s.type === type);
      if (sourceIndex === -1) {
        throw new HttpException(`Context source ${type} not found`, HttpStatus.NOT_FOUND);
      }
      
      // Update the source
      config.contextSources[sourceIndex] = {
        ...config.contextSources[sourceIndex],
        ...source,
        type, // Ensure type doesn't change
      };
      
      // Update the configuration
      const updatedConfig = this.advancedContextService.updateContextConfig({
        contextSources: config.contextSources,
      });
      
      return {
        source: updatedConfig.contextSources[sourceIndex],
        message: `Context source ${type} updated successfully`,
      };
    } catch (error) {
      this.logger.error(`Error updating context source ${type}: ${error.message}`);
      throw new HttpException(`Failed to update context source: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get context adaptation strategies
   */
  @ApiOperation({ summary: 'Get context adaptation strategies' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Context adaptation strategies' })
  @Get('adaptation-strategies')
  getAdaptationStrategies() {
    try {
      const config = this.advancedContextService.getContextConfig();
      return {
        strategies: config.adaptationStrategies,
        count: config.adaptationStrategies.length,
      };
    } catch (error) {
      this.logger.error(`Error getting adaptation strategies: ${error.message}`);
      throw new HttpException('Failed to get adaptation strategies', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update context adaptation strategies
   */
  @ApiOperation({ summary: 'Update context adaptation strategies' })
  @ApiBody({ description: 'Context adaptation strategies' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Updated context adaptation strategies' })
  @Put('adaptation-strategies')
  updateAdaptationStrategies(@Body() body: { strategies: ContextAdaptationStrategy[] }) {
    try {
      const updatedConfig = this.advancedContextService.updateContextConfig({
        adaptationStrategies: body.strategies,
      });
      
      return {
        strategies: updatedConfig.adaptationStrategies,
        count: updatedConfig.adaptationStrategies.length,
        message: 'Context adaptation strategies updated successfully',
      };
    } catch (error) {
      this.logger.error(`Error updating adaptation strategies: ${error.message}`);
      throw new HttpException('Failed to update adaptation strategies', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
