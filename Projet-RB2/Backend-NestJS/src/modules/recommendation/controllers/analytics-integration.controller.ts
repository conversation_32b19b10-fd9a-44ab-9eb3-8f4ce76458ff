import { Controller, Get, Post, Body, Param, Query, UseGuards, Req, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../auth/enums/user-role.enum';
import { AnalyticsIntegrationService, AnalyticsEventType } from '../services/analytics-integration.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Contrôleur pour les analytics des recommandations
 */
@ApiTags('Recommendation Analytics Integration')
@Controller('recommendations/analytics-integration')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AnalyticsIntegrationController {
  constructor(private readonly analyticsService: AnalyticsIntegrationService) {}

  /**
   * Enregistre un événement de visualisation de recommandation
   */
  @Post('view/:type/:id')
  @ApiOperation({ summary: 'Enregistre un événement de visualisation de recommandation' })
  @ApiResponse({ status: 200, description: 'Événement enregistré avec succès' })
  @ApiParam({ name: 'type', enum: RecommendationType, description: 'Type de recommandation' })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  async trackRecommendationView(
    @Param('id') recommendationId: string,
    @Param('type') type: RecommendationType,
    @Body() data: any,
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      const result = await this.analyticsService.trackRecommendationViewed(
        userId,
        recommendationId,
        type,
        data.metadata,
      );
      
      return {
        success: true,
        message: 'Événement de visualisation enregistré avec succès',
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'événement de visualisation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un événement de clic sur une recommandation
   */
  @Post('click/:type/:id')
  @ApiOperation({ summary: 'Enregistre un événement de clic sur une recommandation' })
  @ApiResponse({ status: 200, description: 'Événement enregistré avec succès' })
  @ApiParam({ name: 'type', enum: RecommendationType, description: 'Type de recommandation' })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  async trackRecommendationClick(
    @Param('id') recommendationId: string,
    @Param('type') type: RecommendationType,
    @Body() data: any,
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      const result = await this.analyticsService.trackRecommendationClicked(
        userId,
        recommendationId,
        type,
        data.metadata,
      );
      
      return {
        success: true,
        message: 'Événement de clic enregistré avec succès',
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'événement de clic',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un événement de visualisation d'explication
   */
  @Post('explanation-view/:type/:id')
  @ApiOperation({ summary: 'Enregistre un événement de visualisation d\'explication' })
  @ApiResponse({ status: 200, description: 'Événement enregistré avec succès' })
  @ApiParam({ name: 'type', enum: RecommendationType, description: 'Type de recommandation' })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  async trackExplanationView(
    @Param('id') recommendationId: string,
    @Param('type') type: RecommendationType,
    @Body() data: { explanationId: string; metadata?: any },
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      if (!data.explanationId) {
        throw new HttpException(
          'L\'ID de l\'explication est requis',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      const result = await this.analyticsService.trackExplanationViewed(
        userId,
        recommendationId,
        type,
        data.explanationId,
        data.metadata,
      );
      
      return {
        success: true,
        message: 'Événement de visualisation d\'explication enregistré avec succès',
        data: result,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'événement de visualisation d\'explication',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un événement de feedback sur une explication
   */
  @Post('explanation-feedback/:type/:id')
  @ApiOperation({ summary: 'Enregistre un événement de feedback sur une explication' })
  @ApiResponse({ status: 200, description: 'Événement enregistré avec succès' })
  @ApiParam({ name: 'type', enum: RecommendationType, description: 'Type de recommandation' })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  async trackExplanationFeedback(
    @Param('id') recommendationId: string,
    @Param('type') type: RecommendationType,
    @Body() data: { explanationId: string; feedbackType: string; metadata?: any },
    @Req() req: any,
  ) {
    try {
      const userId = req.user.id;
      
      if (!data.explanationId || !data.feedbackType) {
        throw new HttpException(
          'L\'ID de l\'explication et le type de feedback sont requis',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      const result = await this.analyticsService.trackExplanationFeedback(
        userId,
        recommendationId,
        type,
        data.explanationId,
        data.feedbackType,
        data.metadata,
      );
      
      return {
        success: true,
        message: 'Événement de feedback sur l\'explication enregistré avec succès',
        data: result,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'événement de feedback sur l\'explication',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère les événements d'analytics pour un utilisateur
   * Réservé aux administrateurs
   */
  @Get('events/user/:userId')
  @ApiOperation({ summary: 'Récupère les événements d\'analytics pour un utilisateur' })
  @ApiResponse({ status: 200, description: 'Événements récupérés avec succès' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @Roles(UserRole.ADMIN)
  async getUserEvents(
    @Param('userId') userId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('eventType') eventType?: AnalyticsEventType,
  ) {
    try {
      // Cette méthode serait implémentée dans le service d'analytics
      // Pour l'instant, on retourne un message d'information
      return {
        success: true,
        message: 'Cette fonctionnalité sera implémentée dans une version future',
        data: {
          userId,
          startDate,
          endDate,
          eventType,
        },
      };
    } catch (error) {
      throw new HttpException(
        'Erreur lors de la récupération des événements d\'analytics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
