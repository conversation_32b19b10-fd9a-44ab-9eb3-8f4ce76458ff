import { Controller, Post, Get, Param, Query, Body, UseGuards, Req, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ContinuousLearningPipelineService } from '../services/continuous-learning-pipeline.service';
import { ModelTrainingService } from '../services/model-training.service';
import { PipelineExecutionStatus, ModelVersion } from '../interfaces/continuous-learning-pipeline.interface';

interface RequestWithUser {
  user: {
    id: string;
    roles: string[];
  };
}

/**
 * Contrôleur pour le pipeline d'apprentissage continu
 */
@ApiTags('Continuous Learning Pipeline')
@Controller('recommendations/learning-pipeline')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ContinuousLearningPipelineController {
  private readonly logger = new Logger(ContinuousLearningPipelineController.name);

  constructor(
    private readonly continuousLearningPipelineService: ContinuousLearningPipelineService,
    private readonly modelTrainingService: ModelTrainingService,
  ) {}

  /**
   * Déclencher manuellement le pipeline d'apprentissage continu
   */
  @Post('run')
  @Roles('ADMIN', 'DATA_SCIENTIST')
  @ApiOperation({ summary: 'Déclencher manuellement le pipeline d'apprentissage continu' })
  @ApiResponse({ status: 202, description: 'Pipeline démarré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async runPipeline(@Req() req: RequestWithUser) {
    try {
      this.logger.log(`Démarrage manuel du pipeline par l'utilisateur ${req.user.id}`);

      // Démarrer le pipeline de manière asynchrone
      this.continuousLearningPipelineService.runPipeline()
        .then(result => {
          this.logger.log(`Pipeline terminé avec le statut: ${result.status}`);
        })
        .catch(error => {
          this.logger.error(`Erreur lors de l'exécution du pipeline: ${error.message}`);
        });

      return {
        success: true,
        message: 'Pipeline démarré avec succès',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Erreur lors du démarrage du pipeline: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors du démarrage du pipeline',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtenir l'état d'exécution du pipeline
   */
  @Get('status/:id')
  @Roles('ADMIN', 'DATA_SCIENTIST')
  @ApiOperation({ summary: 'Obtenir l'état d'exécution du pipeline' })
  @ApiParam({ name: 'id', description: 'ID de l'exécution du pipeline' })
  @ApiResponse({ status: 200, description: 'État du pipeline récupéré avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Exécution du pipeline non trouvée' })
  async getPipelineStatus(@Param('id') id: string, @Req() req: RequestWithUser) {
    try {
      this.logger.log(`Récupération de l'état du pipeline ${id} par l'utilisateur ${req.user.id}`);

      // Récupérer l'état du pipeline depuis la base de données
      const pipelineStatus = await this.getPipelineExecutionFromDatabase(id);

      if (!pipelineStatus) {
        throw new HttpException('Exécution du pipeline non trouvée', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: pipelineStatus,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de l'état du pipeline: ${error.message}`);
      throw error;
    }
  }

  /**
   * Obtenir la liste des exécutions récentes du pipeline
   */
  @Get('executions')
  @Roles('ADMIN', 'DATA_SCIENTIST')
  @ApiOperation({ summary: 'Obtenir la liste des exécutions récentes du pipeline' })
  @ApiQuery({ name: 'limit', description: 'Nombre maximum d'exécutions à récupérer', required: false, type: Number })
  @ApiQuery({ name: 'status', description: 'Filtrer par statut (completed, failed, running)', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Liste des exécutions récupérée avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getPipelineExecutions(
    @Query('limit') limit: number = 10,
    @Query('status') status?: 'completed' | 'failed' | 'running',
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Récupération des exécutions récentes du pipeline par l'utilisateur ${req.user.id}`);

      // Récupérer les exécutions du pipeline depuis la base de données
      const executions = await this.getPipelineExecutionsFromDatabase(limit, status);

      return {
        success: true,
        data: executions,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des exécutions du pipeline: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des exécutions du pipeline',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Obtenir la liste des modèles disponibles
   */
  @Get('models')
  @Roles('ADMIN', 'DATA_SCIENTIST')
  @ApiOperation({ summary: 'Obtenir la liste des modèles disponibles' })
  @ApiQuery({ name: 'status', description: 'Filtrer par statut (deployed, ready, archived)', required: false, type: String })
  @ApiQuery({ name: 'type', description: 'Filtrer par type de modèle', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Liste des modèles récupérée avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  async getModels(
    @Query('status') status?: 'deployed' | 'ready' | 'archived',
    @Query('type') type?: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Récupération des modèles par l'utilisateur ${req.user.id}`);

      // Récupérer les modèles depuis la base de données
      const models = await this.getModelsFromDatabase(status, type);

      return {
        success: true,
        data: models,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des modèles: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la récupération des modèles',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Déployer un modèle spécifique
   */
  @Post('models/:id/deploy')
  @Roles('ADMIN', 'DATA_SCIENTIST')
  @ApiOperation({ summary: 'Déployer un modèle spécifique' })
  @ApiParam({ name: 'id', description: 'ID du modèle à déployer' })
  @ApiResponse({ status: 200, description: 'Modèle déployé avec succès' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Modèle non trouvé' })
  async deployModel(@Param('id') id: string, @Req() req: RequestWithUser) {
    try {
      this.logger.log(`Déploiement du modèle ${id} par l'utilisateur ${req.user.id}`);

      // Récupérer le modèle depuis la base de données
      const model = await this.getModelFromDatabase(id);

      if (!model) {
        throw new HttpException('Modèle non trouvé', HttpStatus.NOT_FOUND);
      }

      // Déployer le modèle
      const deploymentResult = await this.deployModelFromDatabase(model);

      return {
        success: true,
        data: deploymentResult,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du déploiement du modèle: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupérer une exécution du pipeline depuis la base de données
   * @param id ID de l'exécution du pipeline
   * @returns État de l'exécution du pipeline
   */
  private async getPipelineExecutionFromDatabase(id: string): Promise<PipelineExecutionStatus | null> {
    // Dans une implémentation réelle, cela récupérerait les données depuis la base de données
    // Pour l'instant, nous retournons des données simulées

    if (id === 'latest') {
      return {
        id: 'pipeline-123',
        startTime: new Date(Date.now() - 3600000), // 1 heure plus tôt
        endTime: new Date(Date.now() - 1800000), // 30 minutes plus tôt
        status: 'completed',
        steps: [
          { name: 'data_collection', status: 'completed', startTime: new Date(Date.now() - 3600000), endTime: new Date(Date.now() - 3500000) },
          { name: 'data_preprocessing', status: 'completed', startTime: new Date(Date.now() - 3500000), endTime: new Date(Date.now() - 3300000) },
          { name: 'feature_extraction', status: 'completed', startTime: new Date(Date.now() - 3300000), endTime: new Date(Date.now() - 3000000) },
          { name: 'model_training', status: 'completed', startTime: new Date(Date.now() - 3000000), endTime: new Date(Date.now() - 2400000) },
          { name: 'model_evaluation', status: 'completed', startTime: new Date(Date.now() - 2400000), endTime: new Date(Date.now() - 2100000) },
          { name: 'model_deployment', status: 'completed', startTime: new Date(Date.now() - 2100000), endTime: new Date(Date.now() - 1800000) },
        ],
      };
    }

    if (id === 'running') {
      return {
        id: 'pipeline-124',
        startTime: new Date(Date.now() - 1800000), // 30 minutes plus tôt
        status: 'running',
        steps: [
          { name: 'data_collection', status: 'completed', startTime: new Date(Date.now() - 1800000), endTime: new Date(Date.now() - 1700000) },
          { name: 'data_preprocessing', status: 'completed', startTime: new Date(Date.now() - 1700000), endTime: new Date(Date.now() - 1500000) },
          { name: 'feature_extraction', status: 'completed', startTime: new Date(Date.now() - 1500000), endTime: new Date(Date.now() - 1200000) },
          { name: 'model_training', status: 'running', startTime: new Date(Date.now() - 1200000) },
          { name: 'model_evaluation', status: 'pending' },
          { name: 'model_deployment', status: 'pending' },
        ],
      };
    }

    if (id === 'failed') {
      return {
        id: 'pipeline-122',
        startTime: new Date(Date.now() - 7200000), // 2 heures plus tôt
        endTime: new Date(Date.now() - 6900000), // 1h55 plus tôt
        status: 'failed',
        steps: [
          { name: 'data_collection', status: 'completed', startTime: new Date(Date.now() - 7200000), endTime: new Date(Date.now() - 7100000) },
          { name: 'data_preprocessing', status: 'completed', startTime: new Date(Date.now() - 7100000), endTime: new Date(Date.now() - 7000000) },
          { name: 'feature_extraction', status: 'failed', startTime: new Date(Date.now() - 7000000), endTime: new Date(Date.now() - 6900000), error: 'Erreur lors de l\'extraction des caractéristiques' },
          { name: 'model_training', status: 'pending' },
          { name: 'model_evaluation', status: 'pending' },
          { name: 'model_deployment', status: 'pending' },
        ],
        error: 'Erreur lors de l\'extraction des caractéristiques',
      };
    }

    // Si l'ID correspond à un format spécifique, générer une exécution simulée
    if (id.startsWith('pipeline-')) {
      return {
        id,
        startTime: new Date(Date.now() - 3600000), // 1 heure plus tôt
        endTime: new Date(Date.now() - 1800000), // 30 minutes plus tôt
        status: 'completed',
        steps: [
          { name: 'data_collection', status: 'completed', startTime: new Date(Date.now() - 3600000), endTime: new Date(Date.now() - 3500000) },
          { name: 'data_preprocessing', status: 'completed', startTime: new Date(Date.now() - 3500000), endTime: new Date(Date.now() - 3300000) },
          { name: 'feature_extraction', status: 'completed', startTime: new Date(Date.now() - 3300000), endTime: new Date(Date.now() - 3000000) },
          { name: 'model_training', status: 'completed', startTime: new Date(Date.now() - 3000000), endTime: new Date(Date.now() - 2400000) },
          { name: 'model_evaluation', status: 'completed', startTime: new Date(Date.now() - 2400000), endTime: new Date(Date.now() - 2100000) },
          { name: 'model_deployment', status: 'completed', startTime: new Date(Date.now() - 2100000), endTime: new Date(Date.now() - 1800000) },
        ],
      };
    }

    return null;
  }

  /**
   * Récupérer les exécutions du pipeline depuis la base de données
   * @param limit Nombre maximum d'exécutions à récupérer
   * @param status Filtrer par statut
   * @returns Liste des exécutions du pipeline
   */
  private async getPipelineExecutionsFromDatabase(
    limit: number,
    status?: 'completed' | 'failed' | 'running',
  ): Promise<PipelineExecutionStatus[]> {
    // Dans une implémentation réelle, cela récupérerait les données depuis la base de données
    // Pour l'instant, nous retournons des données simulées

    const executions: PipelineExecutionStatus[] = [
      {
        id: 'pipeline-123',
        startTime: new Date(Date.now() - 3600000), // 1 heure plus tôt
        endTime: new Date(Date.now() - 1800000), // 30 minutes plus tôt
        status: 'completed',
        steps: [
          { name: 'data_collection', status: 'completed' },
          { name: 'data_preprocessing', status: 'completed' },
          { name: 'feature_extraction', status: 'completed' },
          { name: 'model_training', status: 'completed' },
          { name: 'model_evaluation', status: 'completed' },
          { name: 'model_deployment', status: 'completed' },
        ],
      },
      {
        id: 'pipeline-124',
        startTime: new Date(Date.now() - 1800000), // 30 minutes plus tôt
        status: 'running',
        steps: [
          { name: 'data_collection', status: 'completed' },
          { name: 'data_preprocessing', status: 'completed' },
          { name: 'feature_extraction', status: 'completed' },
          { name: 'model_training', status: 'running' },
          { name: 'model_evaluation', status: 'pending' },
          { name: 'model_deployment', status: 'pending' },
        ],
      },
      {
        id: 'pipeline-122',
        startTime: new Date(Date.now() - 7200000), // 2 heures plus tôt
        endTime: new Date(Date.now() - 6900000), // 1h55 plus tôt
        status: 'failed',
        steps: [
          { name: 'data_collection', status: 'completed' },
          { name: 'data_preprocessing', status: 'completed' },
          { name: 'feature_extraction', status: 'failed' },
          { name: 'model_training', status: 'pending' },
          { name: 'model_evaluation', status: 'pending' },
          { name: 'model_deployment', status: 'pending' },
        ],
        error: 'Erreur lors de l\'extraction des caractéristiques',
      },
      {
        id: 'pipeline-121',
        startTime: new Date(Date.now() - 86400000), // 1 jour plus tôt
        endTime: new Date(Date.now() - 84600000), // 23h30 plus tôt
        status: 'completed',
        steps: [
          { name: 'data_collection', status: 'completed' },
          { name: 'data_preprocessing', status: 'completed' },
          { name: 'feature_extraction', status: 'completed' },
          { name: 'model_training', status: 'completed' },
          { name: 'model_evaluation', status: 'completed' },
          { name: 'model_deployment', status: 'completed' },
        ],
      },
    ];

    // Filtrer par statut si spécifié
    let filteredExecutions = executions;
    if (status) {
      filteredExecutions = executions.filter(execution => execution.status === status);
    }

    // Limiter le nombre de résultats
    return filteredExecutions.slice(0, limit);
  }

  /**
   * Récupérer les modèles depuis la base de données
   * @param status Filtrer par statut
   * @param type Filtrer par type de modèle
   * @returns Liste des modèles
   */
  private async getModelsFromDatabase(
    status?: 'deployed' | 'ready' | 'archived',
    type?: string,
  ): Promise<ModelVersion[]> {
    // Dans une implémentation réelle, cela récupérerait les données depuis la base de données
    // Pour l'instant, nous retournons des données simulées

    const models: ModelVersion[] = [
      {
        id: 'model-123',
        modelType: 'collaborative_filtering',
        version: 'collaborative_filtering-2023-05-15-abcd1234',
        createdAt: new Date(Date.now() - 86400000), // 1 jour plus tôt
        trainedWith: {
          dataSize: 10000,
          dataRange: {
            start: new Date(Date.now() - 2592000000), // 30 jours plus tôt
            end: new Date(Date.now() - 86400000), // 1 jour plus tôt
          },
          hyperparameters: {
            learningRate: 0.01,
            regularization: 0.001,
            numFactors: 100,
            numIterations: 20,
          },
        },
        performance: {
          trainingMetrics: {
            'rmse': 0.82,
            'mae': 0.65,
            'precision': 0.78,
            'recall': 0.72,
            'f1': 0.75,
          },
          validationMetrics: {
            'rmse': 0.85,
            'mae': 0.68,
            'precision': 0.75,
            'recall': 0.70,
            'f1': 0.72,
          },
        },
        status: 'deployed',
      },
      {
        id: 'model-124',
        modelType: 'content_based',
        version: 'content_based-2023-05-10-efgh5678',
        createdAt: new Date(Date.now() - 432000000), // 5 jours plus tôt
        trainedWith: {
          dataSize: 8000,
          dataRange: {
            start: new Date(Date.now() - 2592000000), // 30 jours plus tôt
            end: new Date(Date.now() - 432000000), // 5 jours plus tôt
          },
          hyperparameters: {
            featureWeights: {
              category: 0.5,
              tags: 0.3,
              description: 0.2,
            },
          },
        },
        performance: {
          trainingMetrics: {
            'accuracy': 0.82,
            'precision': 0.80,
            'recall': 0.78,
            'f1': 0.79,
          },
          validationMetrics: {
            'accuracy': 0.79,
            'precision': 0.77,
            'recall': 0.75,
            'f1': 0.76,
          },
        },
        status: 'archived',
      },
      {
        id: 'model-125',
        modelType: 'hybrid',
        version: 'hybrid-2023-05-12-ijkl9012',
        createdAt: new Date(Date.now() - 259200000), // 3 jours plus tôt
        trainedWith: {
          dataSize: 12000,
          dataRange: {
            start: new Date(Date.now() - 2592000000), // 30 jours plus tôt
            end: new Date(Date.now() - 259200000), // 3 jours plus tôt
          },
          hyperparameters: {
            collaborativeWeight: 0.6,
            contentBasedWeight: 0.4,
          },
        },
        performance: {
          trainingMetrics: {
            'accuracy': 0.88,
            'precision': 0.85,
            'recall': 0.83,
            'f1': 0.84,
            'ndcg': 0.90,
            'diversity': 0.75,
          },
          validationMetrics: {
            'accuracy': 0.85,
            'precision': 0.82,
            'recall': 0.80,
            'f1': 0.81,
            'ndcg': 0.87,
            'diversity': 0.72,
          },
        },
        status: 'ready',
      },
    ];

    // Filtrer par statut si spécifié
    let filteredModels = models;
    if (status) {
      filteredModels = filteredModels.filter(model => model.status === status);
    }

    // Filtrer par type si spécifié
    if (type) {
      filteredModels = filteredModels.filter(model => model.modelType === type);
    }

    return filteredModels;
  }

  /**
   * Récupérer un modèle depuis la base de données
   * @param id ID du modèle
   * @returns Modèle
   */
  private async getModelFromDatabase(id: string): Promise<ModelVersion | null> {
    // Récupérer tous les modèles
    const models = await this.getModelsFromDatabase();

    // Trouver le modèle avec l'ID spécifié
    return models.find(model => model.id === id) || null;
  }

  /**
   * Déployer un modèle
   * @param model Modèle à déployer
   * @returns Résultat du déploiement
   */
  private async deployModelFromDatabase(model: ModelVersion): Promise<any> {
    // Dans une implémentation réelle, cela déploierait le modèle
    // Pour l'instant, nous simulons le déploiement

    // Mettre à jour le statut du modèle
    model.status = 'deployed';

    return {
      modelId: model.id,
      modelVersion: model.version,
      modelType: model.modelType,
      deploymentTime: new Date(),
      status: 'deployed',
    };
  }
}