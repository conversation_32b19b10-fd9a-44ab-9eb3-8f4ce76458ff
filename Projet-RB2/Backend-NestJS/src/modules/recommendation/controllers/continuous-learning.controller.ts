import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { ContinuousLearningService } from '../services/continuous-learning.service';
import {
  LearningMetricsRequestDto,
  UserModelRequestDto,
  LearningEventDto,
  ContinuousLearningParamsDto,
  LearningMetricsResponseDto,
  UserModelResponseDto,
} from '../dto/continuous-learning.dto';

/**
 * Contrôleur pour le module d'apprentissage continu
 */
@ApiTags('Apprentissage Continu')
@Controller('recommendation/continuous-learning')
export class ContinuousLearningController {
  private readonly logger = new Logger(ContinuousLearningController.name);

  constructor(private readonly continuousLearningService: ContinuousLearningService) {}

  /**
   * Récupère les métriques d'apprentissage pour l'utilisateur courant
   */
  @Get('metrics/me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques d\'apprentissage pour l\'utilisateur courant' })
  @ApiResponse({ status: 200, description: 'Métriques récupérées avec succès', type: LearningMetricsResponseDto })
  async getMyLearningMetrics(
    @CurrentUser('id') userId: string,
    @Query() query: LearningMetricsRequestDto,
  ) {
    try {
      this.logger.log(`Récupération des métriques d'apprentissage pour l'utilisateur ${userId}`);
      
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);
      
      const metrics = await this.continuousLearningService.getLearningMetrics(
        userId,
        startDate,
        endDate,
      );
      
      if (!metrics) {
        throw new HttpException(
          'Métriques d\'apprentissage non trouvées',
          HttpStatus.NOT_FOUND,
        );
      }
      
      return metrics;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques d'apprentissage: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la récupération des métriques d\'apprentissage',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère les métriques d'apprentissage pour un utilisateur spécifique (admin uniquement)
   */
  @Get('metrics/user/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les métriques d\'apprentissage pour un utilisateur spécifique (admin uniquement)' })
  @ApiResponse({ status: 200, description: 'Métriques récupérées avec succès', type: LearningMetricsResponseDto })
  async getUserLearningMetrics(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query() query: LearningMetricsRequestDto,
  ) {
    try {
      this.logger.log(`Récupération des métriques d'apprentissage pour l'utilisateur ${userId} (par admin)`);
      
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);
      
      const metrics = await this.continuousLearningService.getLearningMetrics(
        userId,
        startDate,
        endDate,
      );
      
      if (!metrics) {
        throw new HttpException(
          'Métriques d\'apprentissage non trouvées',
          HttpStatus.NOT_FOUND,
        );
      }
      
      return metrics;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques d'apprentissage: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la récupération des métriques d\'apprentissage',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère le modèle utilisateur pour l'utilisateur courant
   */
  @Get('model/me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer le modèle utilisateur pour l\'utilisateur courant' })
  @ApiResponse({ status: 200, description: 'Modèle récupéré avec succès', type: UserModelResponseDto })
  async getMyUserModel(@CurrentUser('id') userId: string) {
    try {
      this.logger.log(`Récupération du modèle utilisateur pour l'utilisateur ${userId}`);
      
      const userModel = await this.continuousLearningService.getUserModel(userId);
      
      if (!userModel) {
        throw new HttpException(
          'Modèle utilisateur non trouvé',
          HttpStatus.NOT_FOUND,
        );
      }
      
      // Convertir le modèle utilisateur en DTO de réponse
      const response: UserModelResponseDto = {
        userId: userModel.userId,
        lastUpdated: userModel.lastUpdated,
        preferences: userModel.preferences,
        categoryInterests: userModel.categoryInterests,
        tagInterests: userModel.tagInterests,
        context: userModel.context,
        metrics: userModel.metrics,
      };
      
      return response;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du modèle utilisateur: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la récupération du modèle utilisateur',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère le modèle utilisateur pour un utilisateur spécifique (admin uniquement)
   */
  @Get('model/user/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer le modèle utilisateur pour un utilisateur spécifique (admin uniquement)' })
  @ApiResponse({ status: 200, description: 'Modèle récupéré avec succès', type: UserModelResponseDto })
  async getUserModel(@Param('userId', ParseUUIDPipe) userId: string) {
    try {
      this.logger.log(`Récupération du modèle utilisateur pour l'utilisateur ${userId} (par admin)`);
      
      const userModel = await this.continuousLearningService.getUserModel(userId);
      
      if (!userModel) {
        throw new HttpException(
          'Modèle utilisateur non trouvé',
          HttpStatus.NOT_FOUND,
        );
      }
      
      // Convertir le modèle utilisateur en DTO de réponse
      const response: UserModelResponseDto = {
        userId: userModel.userId,
        lastUpdated: userModel.lastUpdated,
        preferences: userModel.preferences,
        categoryInterests: userModel.categoryInterests,
        tagInterests: userModel.tagInterests,
        context: userModel.context,
        metrics: userModel.metrics,
      };
      
      return response;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du modèle utilisateur: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la récupération du modèle utilisateur',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un événement d'apprentissage (admin uniquement)
   */
  @Post('event/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer un événement d\'apprentissage (admin uniquement)' })
  @ApiResponse({ status: 201, description: 'Événement enregistré avec succès' })
  async recordLearningEvent(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() eventDto: LearningEventDto,
  ) {
    try {
      this.logger.log(`Enregistrement d'un événement d'apprentissage pour l'utilisateur ${userId}`);
      
      // Vérifier que l'utilisateur existe
      const userModel = await this.continuousLearningService.getUserModel(userId);
      if (!userModel) {
        throw new HttpException(
          'Utilisateur non trouvé',
          HttpStatus.NOT_FOUND,
        );
      }
      
      // Enregistrer l'événement
      // Note: Cette méthode n'est pas encore implémentée dans le service
      // await this.continuousLearningService.recordLearningEvent(
      //   userId,
      //   eventDto.eventType,
      //   eventDto.data,
      //   eventDto.estimatedImpact,
      // );
      
      return {
        success: true,
        message: 'Événement d\'apprentissage enregistré avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement d'apprentissage: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'événement d\'apprentissage',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Met à jour les paramètres d'apprentissage continu (admin uniquement)
   */
  @Post('params')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les paramètres d\'apprentissage continu (admin uniquement)' })
  @ApiResponse({ status: 200, description: 'Paramètres mis à jour avec succès' })
  async updateLearningParams(@Body() paramsDto: ContinuousLearningParamsDto) {
    try {
      this.logger.log('Mise à jour des paramètres d\'apprentissage continu');
      
      // Note: Cette méthode n'est pas encore implémentée dans le service
      // await this.continuousLearningService.updateLearningParams(paramsDto);
      
      return {
        success: true,
        message: 'Paramètres d\'apprentissage continu mis à jour avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des paramètres d'apprentissage continu: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la mise à jour des paramètres d\'apprentissage continu',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
