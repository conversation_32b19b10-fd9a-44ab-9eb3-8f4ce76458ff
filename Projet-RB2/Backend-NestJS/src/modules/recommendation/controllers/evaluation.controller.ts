import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { EvaluationFrameworkService, EvaluationType, EvaluationOptions } from '../services/evaluation/evaluation-framework.service';
import { MetricsService, MetricCategory } from '../services/evaluation/metrics.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

@ApiTags('evaluation')
@Controller('recommendation/evaluation')
@UseGuards(JwtAuthGuard, RolesGuard)
export class EvaluationController {
  constructor(
    private readonly evaluationFrameworkService: EvaluationFrameworkService,
    private readonly metricsService: MetricsService,
  ) {}
  
  @Post('algorithms/:algorithmId')
  @Roles('admin', 'data_scientist')
  @ApiOperation({ summary: 'Evaluate an algorithm' })
  @ApiParam({ name: 'algorithmId', description: 'Algorithm ID' })
  @ApiResponse({ status: 201, description: 'Evaluation result' })
  async evaluateAlgorithm(
    @Param('algorithmId') algorithmId: string,
    @Body() options: {
      type: RecommendationType;
      evaluationOptions?: EvaluationOptions;
    },
  ) {
    return this.evaluationFrameworkService.evaluateAlgorithm(
      algorithmId,
      options.type,
      options.evaluationOptions,
    );
  }
  
  @Post('compare')
  @Roles('admin', 'data_scientist')
  @ApiOperation({ summary: 'Compare multiple algorithms' })
  @ApiResponse({ status: 201, description: 'Comparison results' })
  async compareAlgorithms(
    @Body() options: {
      algorithmIds: string[];
      type: RecommendationType;
      evaluationOptions?: EvaluationOptions;
    },
  ) {
    return this.evaluationFrameworkService.compareAlgorithms(
      options.algorithmIds,
      options.type,
      options.evaluationOptions,
    );
  }
  
  @Get('metrics')
  @Roles('admin', 'data_scientist')
  @ApiOperation({ summary: 'Get available metrics' })
  @ApiQuery({ name: 'category', enum: MetricCategory, required: false })
  @ApiResponse({ status: 200, description: 'List of metrics' })
  async getMetrics(@Query('category') category?: MetricCategory) {
    return this.metricsService.getAllMetrics(category);
  }
  
  @Get('metrics/:metricId')
  @Roles('admin', 'data_scientist')
  @ApiOperation({ summary: 'Get metric details' })
  @ApiParam({ name: 'metricId', description: 'Metric ID' })
  @ApiResponse({ status: 200, description: 'Metric details' })
  async getMetric(@Param('metricId') metricId: string) {
    return this.metricsService.getMetric(metricId);
  }
}
