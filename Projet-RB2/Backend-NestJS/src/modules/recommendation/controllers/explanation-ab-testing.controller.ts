import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  UseGuards, 
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiParam, 
  ApiQuery, 
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../auth/decorators/current-user.decorator';
import { ExplanationABTestingService } from '../services/explanation-ab-testing.service';
import { 
  CreateExplanationABTestDto, 
  UpdateExplanationABTestDto, 
  UpdateExplanationVariantDto,
  RecordExplanationABTestInteractionDto,
  ExplanationABTestResponseDto,
  ExplanationVariantResponseDto,
  ExplanationABTestResultsResponseDto,
} from '../dto/explanation-ab-testing.dto';

/**
 * Contrôleur pour les tests A/B des explications
 */
@ApiTags('explanation-ab-testing')
@Controller('explanation-ab-testing')
export class ExplanationABTestingController {
  private readonly logger = new Logger(ExplanationABTestingController.name);

  constructor(private readonly explanationABTestingService: ExplanationABTestingService) {}

  /**
   * Crée un nouveau test A/B d'explications
   * @param dto DTO de création du test
   * @returns Test créé
   */
  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer un nouveau test A/B d\'explications' })
  @ApiBody({ type: CreateExplanationABTestDto })
  @ApiResponse({ 
    status: 201, 
    description: 'Test A/B créé avec succès', 
    type: ExplanationABTestResponseDto 
  })
  async createTest(@Body() dto: CreateExplanationABTestDto): Promise<ExplanationABTestResponseDto> {
    this.logger.log(`Création d'un nouveau test A/B d'explications: ${dto.name}`);
    return this.explanationABTestingService.createTest(dto);
  }

  /**
   * Récupère tous les tests A/B d'explications
   * @param status Filtre par statut
   * @returns Liste des tests
   */
  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer tous les tests A/B d\'explications' })
  @ApiQuery({ 
    name: 'status', 
    required: false, 
    description: 'Filtre par statut (ACTIVE, PAUSED, COMPLETED, CANCELLED)' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Liste des tests A/B', 
    type: [ExplanationABTestResponseDto] 
  })
  async getAllTests(@Query('status') status?: string): Promise<ExplanationABTestResponseDto[]> {
    this.logger.log(`Récupération de tous les tests A/B d'explications${status ? ` avec statut ${status}` : ''}`);
    return this.explanationABTestingService.getAllTests(status);
  }

  /**
   * Récupère un test A/B d'explications par son ID
   * @param id ID du test
   * @returns Test
   */
  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer un test A/B d\'explications par son ID' })
  @ApiParam({ name: 'id', description: 'ID du test A/B' })
  @ApiResponse({ 
    status: 200, 
    description: 'Test A/B trouvé', 
    type: ExplanationABTestResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async getTestById(@Param('id') id: string): Promise<ExplanationABTestResponseDto> {
    this.logger.log(`Récupération du test A/B d'explications avec l'ID ${id}`);
    return this.explanationABTestingService.getTestById(id);
  }

  /**
   * Met à jour un test A/B d'explications
   * @param id ID du test
   * @param dto DTO de mise à jour du test
   * @returns Test mis à jour
   */
  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour un test A/B d\'explications' })
  @ApiParam({ name: 'id', description: 'ID du test A/B' })
  @ApiBody({ type: UpdateExplanationABTestDto })
  @ApiResponse({ 
    status: 200, 
    description: 'Test A/B mis à jour', 
    type: ExplanationABTestResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async updateTest(
    @Param('id') id: string, 
    @Body() dto: UpdateExplanationABTestDto
  ): Promise<ExplanationABTestResponseDto> {
    this.logger.log(`Mise à jour du test A/B d'explications avec l'ID ${id}`);
    return this.explanationABTestingService.updateTest(id, dto);
  }

  /**
   * Met à jour une variante d'explication
   * @param id ID de la variante
   * @param dto DTO de mise à jour de la variante
   * @returns Variante mise à jour
   */
  @Put('variant/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour une variante d\'explication' })
  @ApiParam({ name: 'id', description: 'ID de la variante' })
  @ApiBody({ type: UpdateExplanationVariantDto })
  @ApiResponse({ 
    status: 200, 
    description: 'Variante mise à jour', 
    type: ExplanationVariantResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Variante non trouvée' })
  async updateVariant(
    @Param('id') id: string, 
    @Body() dto: UpdateExplanationVariantDto
  ): Promise<ExplanationVariantResponseDto> {
    this.logger.log(`Mise à jour de la variante d'explication avec l'ID ${id}`);
    return this.explanationABTestingService.updateVariant(id, dto);
  }

  /**
   * Supprime un test A/B d'explications
   * @param id ID du test
   * @returns Résultat de la suppression
   */
  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer un test A/B d\'explications' })
  @ApiParam({ name: 'id', description: 'ID du test A/B' })
  @ApiResponse({ 
    status: 200, 
    description: 'Test A/B supprimé', 
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async deleteTest(@Param('id') id: string): Promise<{ success: boolean; message: string }> {
    this.logger.log(`Suppression du test A/B d'explications avec l'ID ${id}`);
    return this.explanationABTestingService.deleteTest(id);
  }

  /**
   * Assigne un utilisateur à une variante d'un test A/B
   * @param testId ID du test
   * @param userId ID de l'utilisateur
   * @returns Assignation
   */
  @Post(':testId/assign/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Assigner un utilisateur à une variante d\'un test A/B' })
  @ApiParam({ name: 'testId', description: 'ID du test A/B' })
  @ApiParam({ name: 'userId', description: 'ID de l\'utilisateur' })
  @ApiResponse({ 
    status: 201, 
    description: 'Utilisateur assigné à une variante', 
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        userId: { type: 'string' },
        testId: { type: 'string' },
        variantId: { type: 'string' },
        assignedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async assignUserToVariant(
    @Param('testId') testId: string,
    @Param('userId') userId: string,
  ) {
    this.logger.log(`Assignation de l'utilisateur ${userId} à une variante du test A/B ${testId}`);
    return this.explanationABTestingService.assignUserToVariant(userId, testId);
  }

  /**
   * Récupère la variante assignée à l'utilisateur courant pour un test A/B
   * @param testId ID du test
   * @param user Utilisateur courant
   * @returns Variante assignée
   */
  @Get(':testId/my-variant')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer la variante assignée à l\'utilisateur courant pour un test A/B' })
  @ApiParam({ name: 'testId', description: 'ID du test A/B' })
  @ApiResponse({ 
    status: 200, 
    description: 'Variante assignée à l\'utilisateur', 
    type: ExplanationVariantResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé ou utilisateur non assigné' })
  async getMyVariant(
    @Param('testId') testId: string,
    @CurrentUser() user: any,
  ): Promise<ExplanationVariantResponseDto | null> {
    this.logger.log(`Récupération de la variante de l'utilisateur ${user.id} pour le test A/B ${testId}`);
    return this.explanationABTestingService.getUserVariant(user.id, testId);
  }

  /**
   * Enregistre une interaction avec un test A/B
   * @param testId ID du test
   * @param dto DTO d'interaction
   * @param user Utilisateur courant
   * @returns Résultat de l'enregistrement
   */
  @Post(':testId/interaction')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une interaction avec un test A/B' })
  @ApiParam({ name: 'testId', description: 'ID du test A/B' })
  @ApiBody({ type: RecordExplanationABTestInteractionDto })
  @ApiResponse({ 
    status: 200, 
    description: 'Interaction enregistrée', 
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé ou utilisateur non assigné' })
  async recordInteraction(
    @Param('testId') testId: string,
    @Body() dto: RecordExplanationABTestInteractionDto,
    @CurrentUser() user: any,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`Enregistrement d'une interaction ${dto.interactionType} pour l'utilisateur ${user.id} et le test A/B ${testId}`);
    return this.explanationABTestingService.recordInteraction(user.id, testId, dto);
  }

  /**
   * Calcule les résultats d'un test A/B
   * @param id ID du test
   * @returns Résultats du test
   */
  @Get(':id/results')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Calculer les résultats d\'un test A/B' })
  @ApiParam({ name: 'id', description: 'ID du test A/B' })
  @ApiResponse({ 
    status: 200, 
    description: 'Résultats du test A/B', 
    type: ExplanationABTestResultsResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Test A/B non trouvé' })
  async calculateTestResults(@Param('id') id: string): Promise<ExplanationABTestResultsResponseDto> {
    this.logger.log(`Calcul des résultats du test A/B d'explications avec l'ID ${id}`);
    return this.explanationABTestingService.calculateTestResults(id);
  }
}
