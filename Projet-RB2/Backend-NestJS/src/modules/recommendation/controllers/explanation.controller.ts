import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CurrentUser } from '../../../common/decorators';
import { ExplanationService } from '../services/explanation.service';
import { ContinuousLearningService } from '../services/continuous-learning.service';
import { ExplanationRequestDto, ExplanationResponseDto, ExplanationEventDto, ExplanationStatsResponseDto } from '../dto/explanation.dto';

/**
 * Contrôleur pour les explications des recommandations
 */
@ApiTags('Explications')
@Controller('recommendation/explanations')
export class ExplanationController {
  private readonly logger = new Logger(ExplanationController.name);

  constructor(
    private readonly explanationService: ExplanationService,
    private readonly continuousLearningService: ContinuousLearningService,
  ) {}

  /**
   * Récupère une explication pour une recommandation
   */
  @Get(':recommendationId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer une explication pour une recommandation' })
  @ApiParam({ name: 'recommendationId', description: 'ID de la recommandation', type: 'string' })
  @ApiResponse({ status: 200, description: 'Explication récupérée avec succès', type: ExplanationResponseDto })
  async getExplanation(
    @CurrentUser('id') userId: string,
    @Param('recommendationId', ParseUUIDPipe) recommendationId: string,
    @Query() query: ExplanationRequestDto,
  ) {
    try {
      this.logger.log(`Récupération de l'explication pour la recommandation ${recommendationId} (utilisateur ${userId})`);
      
      // Récupérer la recommandation
      const recommendation = await this.getRecommendation(userId, recommendationId);
      
      if (!recommendation) {
        throw new HttpException(
          'Recommandation non trouvée',
          HttpStatus.NOT_FOUND,
        );
      }
      
      // Générer l'explication
      const explanation = await this.explanationService.generateExplanation(userId, recommendation);
      
      // Convertir l'explication au format de réponse
      const response: ExplanationResponseDto = {
        recommendationId,
        userId,
        itemId: recommendation.itemId,
        itemType: recommendation.itemType,
        strategy: recommendation.strategy,
        score: recommendation.score,
        factors: explanation.factors.map(factor => ({
          type: factor.type,
          name: factor.name,
          description: factor.value,
          weight: factor.weight,
        })),
        summary: explanation.text,
        timestamp: new Date(),
        metadata: {
          confidence: explanation.confidence,
          sources: explanation.sources,
        },
      };
      
      return response;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de l'explication: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la récupération de l\'explication',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un événement d'interaction avec une explication
   */
  @Post(':recommendationId/events')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer un événement d\'interaction avec une explication' })
  @ApiParam({ name: 'recommendationId', description: 'ID de la recommandation', type: 'string' })
  @ApiBody({ type: ExplanationEventDto })
  @ApiResponse({ status: 201, description: 'Événement enregistré avec succès' })
  async recordExplanationEvent(
    @CurrentUser('id') userId: string,
    @Param('recommendationId', ParseUUIDPipe) recommendationId: string,
    @Body() eventDto: ExplanationEventDto,
  ) {
    try {
      this.logger.log(`Enregistrement d'un événement d'explication ${eventDto.eventType} pour la recommandation ${recommendationId}`);
      
      // Vérifier que la recommandation existe
      const recommendation = await this.getRecommendation(userId, recommendationId);
      
      if (!recommendation) {
        throw new HttpException(
          'Recommandation non trouvée',
          HttpStatus.NOT_FOUND,
        );
      }
      
      // Vérifier que l'ID de la recommandation dans le DTO correspond à celui de l'URL
      if (eventDto.recommendationId !== recommendationId) {
        throw new HttpException(
          'L\'ID de la recommandation dans le corps de la requête ne correspond pas à celui de l\'URL',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // Enregistrer l'événement
      await this.recordEvent(userId, eventDto);
      
      return {
        statusCode: HttpStatus.CREATED,
        message: 'Événement d\'explication enregistré avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement d'explication: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de l\'enregistrement de l\'événement d\'explication',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère les statistiques d'explications (admin uniquement)
   */
  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les statistiques d\'explications (admin uniquement)' })
  @ApiQuery({ name: 'startDate', required: true, type: String, description: 'Date de début (format ISO)' })
  @ApiQuery({ name: 'endDate', required: true, type: String, description: 'Date de fin (format ISO)' })
  @ApiResponse({ status: 200, description: 'Statistiques récupérées avec succès', type: ExplanationStatsResponseDto })
  async getExplanationStats(
    @Query('startDate') startDateStr: string,
    @Query('endDate') endDateStr: string,
  ) {
    try {
      this.logger.log(`Récupération des statistiques d'explications du ${startDateStr} au ${endDateStr}`);
      
      const startDate = new Date(startDateStr);
      const endDate = new Date(endDateStr);
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new HttpException(
          'Dates invalides',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      if (startDate > endDate) {
        throw new HttpException(
          'La date de début doit être antérieure à la date de fin',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // Récupérer les statistiques
      // Note: Cette méthode n'est pas encore implémentée dans le service
      // const stats = await this.explanationService.getExplanationStats(startDate, endDate);
      
      // Statistiques fictives pour le moment
      const stats: ExplanationStatsResponseDto = {
        totalExplanations: 1250,
        byStrategy: {
          CONTENT_BASED: 450,
          COLLABORATIVE: 350,
          HYBRID: 300,
          MATRIX_FACTORIZATION: 150,
        },
        byItemType: {
          RETREAT: 800,
          COURSE: 450,
        },
        topFactors: [
          {
            type: 'category',
            name: 'Yoga',
            count: 320,
            averageWeight: 0.75,
          },
          {
            type: 'tag',
            name: 'Méditation',
            count: 280,
            averageWeight: 0.68,
          },
        ],
        interactionRate: 0.42,
        conversionRate: 0.18,
      };
      
      return stats;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des statistiques d'explications: ${error.message}`);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        'Erreur lors de la récupération des statistiques d\'explications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère une recommandation
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @returns Recommandation ou null si non trouvée
   */
  private async getRecommendation(userId: string, recommendationId: string): Promise<any> {
    try {
      // Cette méthode devrait être implémentée pour récupérer la recommandation
      // depuis la base de données ou un service approprié
      return {
        id: recommendationId,
        userId,
        itemId: 'item123',
        itemType: 'RETREAT',
        strategy: 'HYBRID',
        score: 0.85,
        sources: ['content-based', 'collaborative', 'contextual'],
        metadata: {
          category: 'yoga',
          tags: ['meditation', 'mindfulness'],
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de la recommandation: ${error.message}`);
      return null;
    }
  }

  /**
   * Enregistre un événement d'explication
   * @param userId ID de l'utilisateur
   * @param event Événement d'explication
   */
  private async recordEvent(userId: string, event: ExplanationEventDto): Promise<void> {
    try {
      // Cette méthode devrait être implémentée pour enregistrer l'événement
      // dans la base de données ou un service approprié
      this.logger.debug(`Enregistrement de l'événement ${event.eventType} pour la recommandation ${event.recommendationId}`);
      
      // Simuler l'enregistrement de l'événement
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement: ${error.message}`);
      throw error;
    }
  }
}
