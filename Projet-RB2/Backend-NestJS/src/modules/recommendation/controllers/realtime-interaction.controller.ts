import { Controller, Post, Body, UseGuards, Logger, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RealtimeRecommendationService } from '../services/realtime-recommendation.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Contrôleur pour les interactions en temps réel
 */
@ApiTags('realtime-interactions')
@Controller('realtime-interactions')
export class RealtimeInteractionController {
  private readonly logger = new Logger(RealtimeInteractionController.name);

  constructor(private readonly realtimeRecommendationService: RealtimeRecommendationService) {}

  /**
   * Enregistre une interaction utilisateur en temps réel
   * @param req Requête avec les informations de l'utilisateur
   * @param data Données de l'interaction
   * @returns Confirmation de l'enregistrement
   */
  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enregistrer une interaction en temps réel' })
  @ApiResponse({ status: 201, description: 'Interaction enregistrée' })
  async trackInteraction(
    @Req() req,
    @Body() data: {
      itemId: string;
      itemType: RecommendationType;
      interactionType: string;
      metadata?: Record<string, any>;
    },
  ) {
    const userId = req.user.id;
    const { itemId, itemType, interactionType, metadata } = data;
    
    this.logger.log(`Enregistrement d'une interaction ${interactionType} pour l'utilisateur ${userId} sur l'item ${itemId}`);
    
    await this.realtimeRecommendationService.trackInteraction(
      userId,
      itemId,
      itemType,
      interactionType,
      metadata || {},
    );
    
    return { success: true };
  }
}
