import { Controller, Post, Body, Param, UseGuards, Req, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ModerationIntegrationService } from '../services/moderation-integration.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

interface RequestWithUser {
  user: {
    id: string;
    roles: string[];
  };
}

/**
 * Contrôleur pour la modération des recommandations
 */
@ApiTags('recommendation-moderation')
@Controller('recommendations/moderation')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class RecommendationModerationController {
  private readonly logger = new Logger(RecommendationModerationController.name);

  constructor(private readonly moderationService: ModerationIntegrationService) {}

  /**
   * Signaler une recommandation inappropriée
   */
  @Post('report/:type/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Signaler une recommandation inappropriée' })
  @ApiParam({ name: 'type', description: 'Type de recommandation', enum: RecommendationType })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  @ApiResponse({ status: 201, description: 'Signalement enregistré avec succès' })
  @ApiResponse({ status: 400, description: 'Données de requête invalides' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async reportRecommendation(
    @Param('type') type: RecommendationType,
    @Param('id') id: string,
    @Body() reportData: { reason: string },
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Signalement de la recommandation ${id} de type ${type} par l'utilisateur ${req.user.id}`);

      if (!reportData.reason) {
        throw new HttpException('La raison du signalement est requise', HttpStatus.BAD_REQUEST);
      }

      const result = await this.moderationService.reportRecommendation(
        id,
        type,
        req.user.id,
        reportData.reason,
      );

      return {
        success: true,
        message: 'Signalement enregistré avec succès',
        data: result,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du signalement de la recommandation: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors du signalement de la recommandation',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Vérifier si une recommandation est autorisée
   */
  @Post('check/:type/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Vérifier si une recommandation est autorisée' })
  @ApiParam({ name: 'type', description: 'Type de recommandation', enum: RecommendationType })
  @ApiParam({ name: 'id', description: 'ID de la recommandation' })
  @ApiResponse({ status: 200, description: 'Statut de la recommandation' })
  @ApiResponse({ status: 401, description: 'Non autorisé' })
  async checkRecommendation(
    @Param('type') type: RecommendationType,
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    try {
      this.logger.log(`Vérification de la recommandation ${id} de type ${type}`);

      const isAllowed = await this.moderationService.isRecommendationAllowed(id, type);

      return {
        success: true,
        data: {
          id,
          type,
          isAllowed,
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification de la recommandation: ${error.message}`);
      throw new HttpException(
        error.message || 'Erreur lors de la vérification de la recommandation',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
