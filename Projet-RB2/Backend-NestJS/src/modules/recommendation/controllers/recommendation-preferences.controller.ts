import { Controller, Get, Put, Body, UseGuards, Logger, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PersonalizationService } from '../services/personalization.service';
import { RecommendationPreferencesDto } from '../dto/recommendation-preferences.dto';

/**
 * Contrôleur pour la gestion des préférences de recommandation
 */
@ApiTags('recommendation-preferences')
@Controller('recommendation-preferences')
export class RecommendationPreferencesController {
  private readonly logger = new Logger(RecommendationPreferencesController.name);

  constructor(private readonly personalizationService: PersonalizationService) {}

  /**
   * Récupère les préférences de recommandation de l'utilisateur
   * @param req Requête avec les informations de l'utilisateur
   * @returns Préférences de recommandation
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupérer les préférences de recommandation' })
  @ApiResponse({ status: 200, description: 'Préférences de recommandation', type: RecommendationPreferencesDto })
  async getPreferences(@Req() req) {
    const userId = req.user.id;
    this.logger.log(`Récupération des préférences de recommandation pour l'utilisateur ${userId}`);
    
    const preferences = await this.personalizationService.getUserPreferences(userId);
    
    return {
      strategy: preferences.recommendationStrategy,
      diversification: preferences.diversification || {
        enabled: true,
        weight: 0.3,
        method: 'MMR',
      },
      matrixFactorization: preferences.matrixFactorization || {
        method: 'SVD',
        numFactors: 20,
        regularization: 0.1,
        numIterations: 50,
      },
      hybrid: preferences.hybrid || {
        method: 'WEIGHTED',
        contentBasedWeight: 0.6,
        collaborativeWeight: 0.4,
        interactionThreshold: 10,
      },
      categories: preferences.categories || {
        preferred: [],
        avoided: [],
      },
      maxRecommendations: preferences.maxRecommendations || 10,
      includeMetadata: preferences.includeMetadata !== false,
    };
  }

  /**
   * Met à jour les préférences de recommandation de l'utilisateur
   * @param req Requête avec les informations de l'utilisateur
   * @param preferencesDto Nouvelles préférences de recommandation
   * @returns Préférences de recommandation mises à jour
   */
  @Put()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour les préférences de recommandation' })
  @ApiResponse({ status: 200, description: 'Préférences de recommandation mises à jour', type: RecommendationPreferencesDto })
  async updatePreferences(@Req() req, @Body() preferencesDto: RecommendationPreferencesDto) {
    const userId = req.user.id;
    this.logger.log(`Mise à jour des préférences de recommandation pour l'utilisateur ${userId}`);
    
    // Mettre à jour les préférences
    const updatedPreferences = await this.personalizationService.updateUserPreferences(userId, {
      recommendationStrategy: preferencesDto.strategy,
      diversification: preferencesDto.diversification,
      matrixFactorization: preferencesDto.matrixFactorization,
      hybrid: preferencesDto.hybrid,
      categories: preferencesDto.categories,
      maxRecommendations: preferencesDto.maxRecommendations,
      includeMetadata: preferencesDto.includeMetadata,
    });
    
    // Invalider le cache des recommandations pour cet utilisateur
    await this.personalizationService.invalidateUserCache(userId);
    
    return {
      strategy: updatedPreferences.recommendationStrategy,
      diversification: updatedPreferences.diversification,
      matrixFactorization: updatedPreferences.matrixFactorization,
      hybrid: updatedPreferences.hybrid,
      categories: updatedPreferences.categories,
      maxRecommendations: updatedPreferences.maxRecommendations,
      includeMetadata: updatedPreferences.includeMetadata,
    };
  }
}
