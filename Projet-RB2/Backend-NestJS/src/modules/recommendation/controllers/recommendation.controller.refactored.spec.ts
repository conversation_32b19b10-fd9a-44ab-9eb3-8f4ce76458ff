import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus } from '@nestjs/common';
import { RecommendationController } from './recommendation.controller.refactored';
import { RecommendationService } from '../services/recommendation.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { GetRecommendationsDto } from '../dto/get-recommendations.dto';
import { RecordInteractionDto } from '../dto/record-interaction.dto';
import { UpdatePreferencesDto } from '../dto/update-preferences.dto';
import { NotFoundException } from '@nestjs/common';

// Mock du service de recommandation
const mockRecommendationService = {
  getRecommendations: jest.fn(),
  getTrendingRecommendations: jest.fn(),
  getSimilarItems: jest.fn(),
  recordInteraction: jest.fn(),
  updatePreferences: jest.fn(),
};

describe('RecommendationController', () => {
  let controller: RecommendationController;
  let service: RecommendationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RecommendationController],
      providers: [
        {
          provide: RecommendationService,
          useValue: mockRecommendationService,
        },
      ],
    }).compile();

    controller = module.get<RecommendationController>(RecommendationController);
    service = module.get<RecommendationService>(RecommendationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getRecommendations', () => {
    it('should return paginated recommendations', async () => {
      // Arrange
      const userId = 'user123';
      const query: GetRecommendationsDto = {
        type: RecommendationType.RETREAT,
        strategy: RecommendationStrategy.HYBRID,
        hybridMethod: HybridMethod.WEIGHTED,
        limit: 10,
        page: 1,
      };

      const mockRecommendations = {
        items: [
          {
            id: 'item1',
            type: RecommendationType.RETREAT,
            title: 'Test Retreat',
            score: 0.95,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockRecommendationService.getRecommendations.mockResolvedValue(mockRecommendations);

      // Act
      const result = await controller.getRecommendations(userId, query);

      // Assert
      expect(service.getRecommendations).toHaveBeenCalledWith(
        userId,
        query.type,
        expect.objectContaining({
          strategy: query.strategy,
          hybridMethod: query.hybridMethod,
          limit: query.limit,
          page: query.page,
        }),
      );

      expect(result).toEqual({
        items: mockRecommendations.items,
        meta: {
          total: mockRecommendations.total,
          page: mockRecommendations.page,
          limit: mockRecommendations.limit,
          totalPages: 1,
        },
      });
    });

    it('should handle errors from the service', async () => {
      // Arrange
      const userId = 'user123';
      const query: GetRecommendationsDto = {
        type: RecommendationType.RETREAT,
      };

      const error = new Error('Test error');
      mockRecommendationService.getRecommendations.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getRecommendations(userId, query)).rejects.toThrow(error);
    });
  });

  describe('getTrendingRecommendations', () => {
    it('should return paginated trending recommendations', async () => {
      // Arrange
      const userId = 'user123';
      const query: GetRecommendationsDto = {
        type: RecommendationType.RETREAT,
        limit: 10,
        page: 1,
      };

      const mockRecommendations = {
        items: [
          {
            id: 'item1',
            type: RecommendationType.RETREAT,
            title: 'Trending Retreat',
            score: 0.95,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockRecommendationService.getTrendingRecommendations.mockResolvedValue(mockRecommendations);

      // Act
      const result = await controller.getTrendingRecommendations(userId, query);

      // Assert
      expect(service.getTrendingRecommendations).toHaveBeenCalledWith(
        query.type,
        expect.objectContaining({
          limit: query.limit,
          page: query.page,
        }),
      );

      expect(result).toEqual({
        items: mockRecommendations.items,
        meta: {
          total: mockRecommendations.total,
          page: mockRecommendations.page,
          limit: mockRecommendations.limit,
          totalPages: 1,
        },
      });
    });
  });

  describe('getSimilarItems', () => {
    it('should return paginated similar items', async () => {
      // Arrange
      const userId = 'user123';
      const type = RecommendationType.RETREAT;
      const itemId = 'item123';
      const query: GetRecommendationsDto = {
        limit: 10,
        page: 1,
      };

      const mockSimilarItems = {
        items: [
          {
            id: 'item2',
            type: RecommendationType.RETREAT,
            title: 'Similar Retreat',
            score: 0.85,
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      };

      mockRecommendationService.getSimilarItems.mockResolvedValue(mockSimilarItems);

      // Act
      const result = await controller.getSimilarItems(userId, type, itemId, query);

      // Assert
      expect(service.getSimilarItems).toHaveBeenCalledWith(
        itemId,
        type,
        expect.objectContaining({
          limit: query.limit,
          page: query.page,
        }),
      );

      expect(result).toEqual({
        items: mockSimilarItems.items,
        meta: {
          total: mockSimilarItems.total,
          page: mockSimilarItems.page,
          limit: mockSimilarItems.limit,
          totalPages: 1,
        },
      });
    });

    it('should handle not found errors', async () => {
      // Arrange
      const userId = 'user123';
      const type = RecommendationType.RETREAT;
      const itemId = 'nonexistent';
      const query: GetRecommendationsDto = {};

      const error = new NotFoundException(`Item with id ${itemId} not found`);
      mockRecommendationService.getSimilarItems.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getSimilarItems(userId, type, itemId, query)).rejects.toThrow(NotFoundException);
    });
  });

  describe('recordInteraction', () => {
    it('should record an interaction and return success', async () => {
      // Arrange
      const userId = 'user123';
      const interactionDto: RecordInteractionDto = {
        itemId: 'item123',
        type: RecommendationType.RETREAT,
        interactionType: 'VIEW',
      };

      mockRecommendationService.recordInteraction.mockResolvedValue(undefined);

      // Act
      const result = await controller.recordInteraction(userId, interactionDto);

      // Assert
      expect(service.recordInteraction).toHaveBeenCalledWith(userId, interactionDto);
      expect(result).toEqual({ success: true });
    });
  });

  describe('updatePreferences', () => {
    it('should update preferences and return success with updated preferences', async () => {
      // Arrange
      const userId = 'user123';
      const preferencesDto: UpdatePreferencesDto = {
        preferredCategories: ['Yoga', 'Meditation'],
      };

      const updatedPreferences = {
        preferredCategories: ['Yoga', 'Meditation'],
        preferredLevels: ['BEGINNER'],
      };

      mockRecommendationService.updatePreferences.mockResolvedValue(updatedPreferences);

      // Act
      const result = await controller.updatePreferences(userId, preferencesDto);

      // Assert
      expect(service.updatePreferences).toHaveBeenCalledWith(userId, preferencesDto);
      expect(result).toEqual({
        success: true,
        preferences: updatedPreferences,
      });
    });
  });
});
