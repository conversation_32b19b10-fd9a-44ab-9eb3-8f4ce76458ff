import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseEnumPipe,
  HttpStatus,
  HttpCode,
  ValidationPipe,
  BadRequestException,
  NotFoundException,
  Logger,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { RecommendationService } from '../services/recommendation.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { RecordInteractionDto } from '../dto/record-interaction.dto';
import { UpdatePreferencesDto } from '../dto/update-preferences.dto';
import { GetRecommendationsDto } from '../dto/get-recommendations.dto';
import { RecommendationResponseDto } from '../dto/recommendation-response.dto';
import { ApiPaginatedResponse } from '../../../common/decorators/api-paginated-response.decorator';
import { PaginatedResponseDto } from '../../../common/dto/paginated-response.dto';
import { ApiErrorResponse } from '../../../common/decorators/api-error-response.decorator';
import { RecommendationMonitoringInterceptor } from '../interceptors/recommendation-monitoring.interceptor';

@ApiTags('Recommendations')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@UseInterceptors(RecommendationMonitoringInterceptor)
@Controller('api/v1/recommendations')
export class RecommendationController {
  private readonly logger = new Logger(RecommendationController.name);

  constructor(private readonly recommendationService: RecommendationService) {}

  /**
   * Récupère des recommandations personnalisées pour l'utilisateur connecté
   */
  @Get()
  @ApiOperation({
    summary: 'Récupérer des recommandations personnalisées',
    description: 'Récupère des recommandations personnalisées pour l\'utilisateur connecté en fonction de ses préférences et de son historique d\'interactions.',
  })
  @ApiPaginatedResponse(RecommendationResponseDto)
  @ApiErrorResponse([
    { status: HttpStatus.BAD_REQUEST, description: 'Paramètres de requête invalides' },
    { status: HttpStatus.UNAUTHORIZED, description: 'Utilisateur non authentifié' },
  ])
  async getRecommendations(
    @CurrentUser('id') userId: string,
    @Query(new ValidationPipe({ transform: true })) query: GetRecommendationsDto,
  ): Promise<PaginatedResponseDto<RecommendationResponseDto>> {
    this.logger.log(`Récupération des recommandations pour l'utilisateur ${userId}`);

    try {
      const { items, total, page, limit } = await this.recommendationService.getRecommendations(
        userId,
        query.type,
        {
          strategy: query.strategy,
          hybridMethod: query.hybridMethod,
          limit: query.limit,
          page: query.page,
          filters: query.filters,
          excludeIds: query.excludeIds,
          includeMetadata: query.includeMetadata,
          sortBy: query.sortBy,
          sortOrder: query.sortOrder,
        },
      );

      return {
        items,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère des recommandations tendance
   */
  @Get('trending')
  @ApiOperation({
    summary: 'Récupérer des recommandations tendance',
    description: 'Récupère les éléments les plus populaires ou tendance, indépendamment des préférences de l\'utilisateur.',
  })
  @ApiPaginatedResponse(RecommendationResponseDto)
  @ApiErrorResponse([
    { status: HttpStatus.BAD_REQUEST, description: 'Paramètres de requête invalides' },
    { status: HttpStatus.UNAUTHORIZED, description: 'Utilisateur non authentifié' },
  ])
  async getTrendingRecommendations(
    @CurrentUser('id') userId: string,
    @Query(new ValidationPipe({ transform: true })) query: GetRecommendationsDto,
  ): Promise<PaginatedResponseDto<RecommendationResponseDto>> {
    this.logger.log(`Récupération des recommandations tendance pour l'utilisateur ${userId}`);

    try {
      const { items, total, page, limit } = await this.recommendationService.getTrendingRecommendations(
        query.type,
        {
          limit: query.limit,
          page: query.page,
          filters: query.filters,
          excludeIds: query.excludeIds,
          includeMetadata: query.includeMetadata,
          sortBy: query.sortBy,
          sortOrder: query.sortOrder,
        },
      );

      return {
        items,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations tendance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère des éléments similaires à un élément spécifié
   */
  @Get('similar/:type/:itemId')
  @ApiOperation({
    summary: 'Récupérer des éléments similaires',
    description: 'Récupère des éléments similaires à un élément spécifié, basé sur le contenu et les caractéristiques.',
  })
  @ApiParam({ name: 'type', enum: RecommendationType, description: 'Type d\'élément' })
  @ApiParam({ name: 'itemId', description: 'ID de l\'élément de référence' })
  @ApiPaginatedResponse(RecommendationResponseDto)
  @ApiErrorResponse([
    { status: HttpStatus.BAD_REQUEST, description: 'Paramètres de requête invalides' },
    { status: HttpStatus.NOT_FOUND, description: 'Élément non trouvé' },
    { status: HttpStatus.UNAUTHORIZED, description: 'Utilisateur non authentifié' },
  ])
  async getSimilarItems(
    @CurrentUser('id') userId: string,
    @Param('type', new ParseEnumPipe(RecommendationType)) type: RecommendationType,
    @Param('itemId', ParseObjectIdPipe) itemId: string,
    @Query(new ValidationPipe({ transform: true })) query: GetRecommendationsDto,
  ): Promise<PaginatedResponseDto<RecommendationResponseDto>> {
    this.logger.log(`Récupération des éléments similaires à ${itemId} de type ${type} pour l'utilisateur ${userId}`);

    try {
      const { items, total, page, limit } = await this.recommendationService.getSimilarItems(
        itemId,
        type,
        {
          limit: query.limit,
          page: query.page,
          filters: query.filters,
          excludeIds: query.excludeIds,
          includeMetadata: query.includeMetadata,
          sortBy: query.sortBy,
          sortOrder: query.sortOrder,
        },
      );

      return {
        items,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Erreur lors de la récupération des éléments similaires: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enregistre une interaction utilisateur avec un élément
   */
  @Post('interactions')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Enregistrer une interaction',
    description: 'Enregistre une interaction de l\'utilisateur avec un élément (vue, like, etc.).',
  })
  @ApiBody({ type: RecordInteractionDto })
  @ApiResponse({ status: HttpStatus.CREATED, description: 'Interaction enregistrée avec succès.' })
  @ApiErrorResponse([
    { status: HttpStatus.BAD_REQUEST, description: 'Données d\'interaction invalides' },
    { status: HttpStatus.UNAUTHORIZED, description: 'Utilisateur non authentifié' },
  ])
  async recordInteraction(
    @CurrentUser('id') userId: string,
    @Body(new ValidationPipe()) interactionDto: RecordInteractionDto,
  ): Promise<{ success: boolean }> {
    this.logger.log(`Enregistrement d'une interaction ${interactionDto.interactionType} pour l'utilisateur ${userId}`);

    try {
      await this.recommendationService.recordInteraction(userId, interactionDto);
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour les préférences de recommandation de l'utilisateur
   */
  @Post('preferences')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Mettre à jour les préférences',
    description: 'Met à jour les préférences de recommandation de l\'utilisateur.',
  })
  @ApiBody({ type: UpdatePreferencesDto })
  @ApiResponse({ status: HttpStatus.OK, description: 'Préférences mises à jour avec succès.' })
  @ApiErrorResponse([
    { status: HttpStatus.BAD_REQUEST, description: 'Données de préférences invalides' },
    { status: HttpStatus.UNAUTHORIZED, description: 'Utilisateur non authentifié' },
  ])
  async updatePreferences(
    @CurrentUser('id') userId: string,
    @Body(new ValidationPipe()) preferencesDto: UpdatePreferencesDto,
  ): Promise<{ success: boolean; preferences: Record<string, any> }> {
    this.logger.log(`Mise à jour des préférences pour l'utilisateur ${userId}`);

    try {
      const preferences = await this.recommendationService.updatePreferences(userId, preferencesDto);
      return { success: true, preferences };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des préférences: ${error.message}`);
      throw error;
    }
  }
}
