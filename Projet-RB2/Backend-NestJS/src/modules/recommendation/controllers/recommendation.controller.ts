import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseEnumPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { RecommendationService } from '../services/recommendation.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../../../common/decorators';
import { ParseObjectIdPipe } from '../../../common/pipes/parse-object-id.pipe';
import { RecordInteractionDto } from '../dto/record-interaction.dto';
import { UpdatePreferencesDto } from '../dto/update-preferences.dto';
import { PersonalizationService } from '../services/personalization.service';
import { ExternalDataType } from '../interfaces/external-data.interface';

@ApiTags('recommendations')
@Controller('recommendations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RecommendationController {
  constructor(
    private readonly recommendationService: RecommendationService,
    private readonly personalizationService: PersonalizationService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Récupérer des recommandations pour l\'utilisateur connecté' })
  @ApiQuery({ name: 'type', enum: RecommendationType, required: false })
  @ApiQuery({ name: 'strategy', enum: RecommendationStrategy, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({ status: 200, description: 'Liste des recommandations récupérée avec succès.' })
  async getRecommendations(
    @CurrentUser('id') userId: string,
    @Query('type') type?: RecommendationType,
    @Query('strategy') strategy?: RecommendationStrategy,
    @Query('limit') limit?: number,
  ) {
    return this.recommendationService.getRecommendations(userId, type, {
      strategy,
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
    });
  }

  @Get('personalized')
  @ApiOperation({ summary: 'Récupérer des recommandations personnalisées pour l\'utilisateur connecté' })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({ status: 200, description: 'Liste des recommandations personnalisées récupérée avec succès.' })
  async getPersonalizedRecommendations(
    @CurrentUser('id') userId: string,
    @Query('limit') limit?: number,
  ) {
    return this.recommendationService.getPersonalizedRecommendations(userId, {
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
    });
  }

  @Get('trending')
  @ApiOperation({ summary: 'Récupérer des recommandations tendance' })
  @ApiQuery({ name: 'type', enum: RecommendationType, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({ status: 200, description: 'Liste des recommandations tendance récupérée avec succès.' })
  async getTrendingRecommendations(
    @Query('type') type?: RecommendationType,
    @Query('limit') limit?: number,
  ) {
    return this.recommendationService.getTrendingRecommendations(type, {
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
    });
  }

  @Get('similar/:type/:itemId')
  @ApiOperation({ summary: 'Récupérer des éléments similaires à un élément' })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiResponse({ status: 200, description: 'Liste des éléments similaires récupérée avec succès.' })
  async getSimilarItems(
    @Param('type', new ParseEnumPipe(RecommendationType)) type: RecommendationType,
    @Param('itemId', ParseObjectIdPipe) itemId: string,
    @Query('limit') limit?: number,
  ) {
    return this.recommendationService.getSimilarItems(itemId, type, {
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
    });
  }

  @Post('interactions')
  @ApiOperation({ summary: 'Enregistrer une interaction utilisateur' })
  @ApiResponse({ status: 201, description: 'L\'interaction a été enregistrée avec succès.' })
  async recordInteraction(
    @CurrentUser('id') userId: string,
    @Body() recordInteractionDto: RecordInteractionDto,
  ) {
    return this.recommendationService.recordInteraction(
      userId,
      recordInteractionDto.itemId,
      recordInteractionDto.type,
      recordInteractionDto.interactionType,
      recordInteractionDto.metadata,
    );
  }

  @Get('preferences')
  @ApiOperation({ summary: 'Récupérer les préférences de recommandation de l\'utilisateur connecté' })
  @ApiResponse({ status: 200, description: 'Préférences de recommandation récupérées avec succès.' })
  async getUserPreferences(@CurrentUser('id') userId: string) {
    return this.personalizationService.getUserPreferences(userId);
  }

  @Post('preferences')
  @ApiOperation({ summary: 'Mettre à jour les préférences de recommandation de l\'utilisateur connecté' })
  @ApiResponse({ status: 200, description: 'Préférences de recommandation mises à jour avec succès.' })
  async updateUserPreferences(
    @CurrentUser('id') userId: string,
    @Body() updatePreferencesDto: UpdatePreferencesDto,
  ) {
    return this.personalizationService.updateUserPreferences(userId, updatePreferencesDto);
  }

  @Get('hybrid-external')
  @ApiOperation({ summary: 'Récupérer des recommandations hybrides enrichies avec des données externes' })
  @ApiQuery({ name: 'type', enum: RecommendationType, required: false })
  @ApiQuery({ name: 'hybridMethod', enum: HybridMethod, required: false })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiQuery({ name: 'externalDataTypes', isArray: true, enum: ExternalDataType, required: false })
  @ApiResponse({ status: 200, description: 'Liste des recommandations hybrides enrichies récupérée avec succès.' })
  async getHybridExternalRecommendations(
    @CurrentUser('id') userId: string,
    @Query('type') type?: RecommendationType,
    @Query('hybridMethod') hybridMethod?: HybridMethod,
    @Query('limit') limit?: number,
    @Query('externalDataTypes') externalDataTypes?: ExternalDataType[],
  ) {
    return this.recommendationService.getRecommendations(userId, type || RecommendationType.RETREAT, {
      strategy: RecommendationStrategy.HYBRID,
      hybridMethod,
      limit: limit ? parseInt(limit.toString(), 10) : undefined,
      enrichWithExternalData: true,
      externalDataTypes: externalDataTypes || Object.values(ExternalDataType),
      minExternalDataRelevanceScore: 0.5,
      maxExternalDataItemsPerType: 3,
    });
  }
}
