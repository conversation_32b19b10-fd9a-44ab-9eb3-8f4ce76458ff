import { Controller, Get, Query, Param, UseGuards, Lo<PERSON>, <PERSON>q } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { VisualizationService } from '../services/visualization.service';

/**
 * Contrôleur pour la visualisation des recommandations
 */
@ApiTags('recommendation-visualization')
@Controller('recommendation-visualization')
export class VisualizationController {
  private readonly logger = new Logger(VisualizationController.name);

  constructor(private readonly visualizationService: VisualizationService) {}

  /**
   * Génère un graphe de similarité entre les utilisateurs
   * @param limit Nombre maximum d'utilisateurs à inclure
   * @param minSimilarity Similarité minimum entre les utilisateurs
   * @returns Graphe de similarité
   */
  @Get('user-similarity-graph')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un graphe de similarité entre les utilisateurs' })
  @ApiResponse({ status: 200, description: 'Graphe de similarité' })
  async getUserSimilarityGraph(
    @Query('limit') limit?: number,
    @Query('minSimilarity') minSimilarity?: number,
  ) {
    this.logger.log(`Génération d'un graphe de similarité entre les utilisateurs (limit=${limit}, minSimilarity=${minSimilarity})`);
    
    return this.visualizationService.generateUserSimilarityGraph(
      limit ? parseInt(limit.toString(), 10) : undefined,
      minSimilarity ? parseFloat(minSimilarity.toString()) : undefined,
    );
  }

  /**
   * Génère un graphe de recommandations pour l'utilisateur courant
   * @param req Requête avec les informations de l'utilisateur
   * @param limit Nombre maximum de recommandations à inclure
   * @returns Graphe de recommandations
   */
  @Get('my-recommendation-graph')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un graphe de recommandations pour l\'utilisateur courant' })
  @ApiResponse({ status: 200, description: 'Graphe de recommandations' })
  async getCurrentUserRecommendationGraph(
    @Req() req,
    @Query('limit') limit?: number,
  ) {
    const userId = req.user.id;
    
    this.logger.log(`Génération d'un graphe de recommandations pour l'utilisateur ${userId} (limit=${limit})`);
    
    return this.visualizationService.generateUserRecommendationGraph(
      userId,
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  /**
   * Génère un graphe de recommandations pour un utilisateur spécifique
   * @param userId ID de l'utilisateur
   * @param limit Nombre maximum de recommandations à inclure
   * @returns Graphe de recommandations
   */
  @Get('user-recommendation-graph/:userId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer un graphe de recommandations pour un utilisateur spécifique' })
  @ApiResponse({ status: 200, description: 'Graphe de recommandations' })
  async getUserRecommendationGraph(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
  ) {
    this.logger.log(`Génération d'un graphe de recommandations pour l'utilisateur ${userId} (limit=${limit})`);
    
    return this.visualizationService.generateUserRecommendationGraph(
      userId,
      limit ? parseInt(limit.toString(), 10) : undefined,
    );
  }

  /**
   * Génère une carte de chaleur des recommandations
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Carte de chaleur
   */
  @Get('recommendation-heatmap')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Générer une carte de chaleur des recommandations' })
  @ApiResponse({ status: 200, description: 'Carte de chaleur' })
  async getRecommendationHeatmap(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log(`Génération d'une carte de chaleur des recommandations du ${startDate || 'début'} au ${endDate || 'aujourd\'hui'}`);
    
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.visualizationService.generateRecommendationHeatmap(start, end);
  }
}
