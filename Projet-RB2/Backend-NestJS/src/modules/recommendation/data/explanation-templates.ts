/**
 * Templates d'explication pour le système de recommandation
 * 
 * Ce fichier contient les templates d'explication utilisés par le système de recommandation
 * pour générer des explications personnalisées pour les utilisateurs.
 */

import { ExplanationTemplate } from '../interfaces/explanation.interface';

/**
 * Templates d'explication par défaut
 */
export const DEFAULT_EXPLANATION_TEMPLATES: ExplanationTemplate[] = [
  // Templates pour les facteurs basés sur les catégories
  {
    id: 'category-basic',
    name: 'Catégorie - Basique',
    description: 'Template d\'explication basique pour les facteurs de catégorie',
    factorType: 'category',
    template: 'Cette retraite correspond à votre intérêt pour {{categoryName}}.',
    variables: ['categoryName'],
    language: 'fr',
  },
  {
    id: 'category-detailed',
    name: 'Catégorie - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs de catégorie',
    factorType: 'category',
    template: 'Cette retraite correspond à votre intérêt pour {{categoryName}}. Vous avez consulté {{viewCount}} retraites dans cette catégorie au cours des {{period}} derniers jours.',
    variables: ['categoryName', 'viewCount', 'period'],
    language: 'fr',
  },
  {
    id: 'category-personalized',
    name: 'Catégorie - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs de catégorie',
    factorType: 'category',
    template: 'Cette retraite correspond parfaitement à votre passion pour {{categoryName}}. Nous avons remarqué que vous avez réservé {{bookingCount}} retraites similaires et que vous avez donné d\'excellentes évaluations à des retraites de {{categoryName}}.',
    variables: ['categoryName', 'bookingCount'],
    language: 'fr',
  },

  // Templates pour les facteurs basés sur les tags
  {
    id: 'tag-basic',
    name: 'Tag - Basique',
    description: 'Template d\'explication basique pour les facteurs de tag',
    factorType: 'tag',
    template: 'Cette retraite contient des éléments liés à {{tagName}}.',
    variables: ['tagName'],
    language: 'fr',
  },
  {
    id: 'tag-detailed',
    name: 'Tag - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs de tag',
    factorType: 'tag',
    template: 'Cette retraite contient des éléments liés à {{tagName}}, un sujet qui semble vous intéresser d\'après vos recherches récentes et vos interactions sur la plateforme.',
    variables: ['tagName'],
    language: 'fr',
  },
  {
    id: 'tag-personalized',
    name: 'Tag - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs de tag',
    factorType: 'tag',
    template: 'Cette retraite met l\'accent sur {{tagName}}, un élément que vous avez explicitement mentionné dans vos préférences. Nous avons constaté que vous interagissez {{interactionFrequency}} avec ce type de contenu.',
    variables: ['tagName', 'interactionFrequency'],
    language: 'fr',
  },

  // Templates pour les facteurs basés sur la similarité
  {
    id: 'similarity-basic',
    name: 'Similarité - Basique',
    description: 'Template d\'explication basique pour les facteurs de similarité',
    factorType: 'similarity',
    template: 'Cette retraite est similaire à d\'autres retraites que vous avez appréciées.',
    variables: [],
    language: 'fr',
  },
  {
    id: 'similarity-detailed',
    name: 'Similarité - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs de similarité',
    factorType: 'similarity',
    template: 'Cette retraite est similaire à {{retreatName}} que vous avez {{interactionType}} récemment. Elle partage {{similarityPercentage}}% de caractéristiques communes.',
    variables: ['retreatName', 'interactionType', 'similarityPercentage'],
    language: 'fr',
  },
  {
    id: 'similarity-personalized',
    name: 'Similarité - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs de similarité',
    factorType: 'similarity',
    template: 'Cette retraite présente de nombreuses similitudes avec {{retreatName}}, que vous avez particulièrement apprécié(e) ({{rating}}/5). Les points communs incluent {{commonFeatures}}.',
    variables: ['retreatName', 'rating', 'commonFeatures'],
    language: 'fr',
  },

  // Templates pour les facteurs basés sur la popularité
  {
    id: 'popularity-basic',
    name: 'Popularité - Basique',
    description: 'Template d\'explication basique pour les facteurs de popularité',
    factorType: 'popularity',
    template: 'Cette retraite est populaire parmi nos utilisateurs.',
    variables: [],
    language: 'fr',
  },
  {
    id: 'popularity-detailed',
    name: 'Popularité - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs de popularité',
    factorType: 'popularity',
    template: 'Cette retraite est très populaire avec une note moyenne de {{averageRating}}/5 basée sur {{reviewCount}} avis. Elle a été réservée {{bookingCount}} fois au cours du dernier mois.',
    variables: ['averageRating', 'reviewCount', 'bookingCount'],
    language: 'fr',
  },
  {
    id: 'popularity-personalized',
    name: 'Popularité - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs de popularité',
    factorType: 'popularity',
    template: 'Cette retraite est très appréciée par des utilisateurs ayant des préférences similaires aux vôtres. Elle a reçu une note moyenne de {{averageRating}}/5 de la part de personnes partageant votre intérêt pour {{commonInterests}}.',
    variables: ['averageRating', 'commonInterests'],
    language: 'fr',
  },

  // Templates pour les facteurs basés sur le contexte
  {
    id: 'context-basic',
    name: 'Contexte - Basique',
    description: 'Template d\'explication basique pour les facteurs de contexte',
    factorType: 'context',
    template: 'Cette retraite correspond à votre contexte actuel.',
    variables: [],
    language: 'fr',
  },
  {
    id: 'context-detailed',
    name: 'Contexte - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs de contexte',
    factorType: 'context',
    template: 'Cette retraite correspond à votre contexte actuel : {{contextFactors}}. Elle se déroule pendant {{season}}, une période que vous semblez préférer d\'après vos recherches précédentes.',
    variables: ['contextFactors', 'season'],
    language: 'fr',
  },
  {
    id: 'context-personalized',
    name: 'Contexte - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs de contexte',
    factorType: 'context',
    template: 'Cette retraite est parfaitement adaptée à votre situation actuelle. Elle se déroule à {{distance}} km de votre localisation, pendant {{season}}, et correspond à vos contraintes de {{constraints}}.',
    variables: ['distance', 'season', 'constraints'],
    language: 'fr',
  },

  // Templates pour les facteurs basés sur les interactions
  {
    id: 'interaction-basic',
    name: 'Interaction - Basique',
    description: 'Template d\'explication basique pour les facteurs d\'interaction',
    factorType: 'interaction',
    template: 'Cette retraite est basée sur vos interactions récentes.',
    variables: [],
    language: 'fr',
  },
  {
    id: 'interaction-detailed',
    name: 'Interaction - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs d\'interaction',
    factorType: 'interaction',
    template: 'Cette retraite est recommandée en fonction de vos {{interactionCount}} interactions récentes sur la plateforme, notamment votre intérêt pour {{interactionTopics}}.',
    variables: ['interactionCount', 'interactionTopics'],
    language: 'fr',
  },
  {
    id: 'interaction-personalized',
    name: 'Interaction - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs d\'interaction',
    factorType: 'interaction',
    template: 'Cette retraite est recommandée en fonction de votre comportement récent sur la plateforme. Nous avons remarqué que vous avez {{interactionVerb}} {{interactionObject}} {{interactionTime}}, ce qui suggère un intérêt pour ce type de retraite.',
    variables: ['interactionVerb', 'interactionObject', 'interactionTime'],
    language: 'fr',
  },

  // Templates pour les facteurs basés sur les préférences explicites
  {
    id: 'preference-basic',
    name: 'Préférence - Basique',
    description: 'Template d\'explication basique pour les facteurs de préférence',
    factorType: 'preference',
    template: 'Cette retraite correspond à vos préférences.',
    variables: [],
    language: 'fr',
  },
  {
    id: 'preference-detailed',
    name: 'Préférence - Détaillé',
    description: 'Template d\'explication détaillé pour les facteurs de préférence',
    factorType: 'preference',
    template: 'Cette retraite correspond à vos préférences explicites pour {{preferenceCategory}} que vous avez définies dans votre profil.',
    variables: ['preferenceCategory'],
    language: 'fr',
  },
  {
    id: 'preference-personalized',
    name: 'Préférence - Personnalisé',
    description: 'Template d\'explication personnalisé pour les facteurs de préférence',
    factorType: 'preference',
    template: 'Cette retraite correspond parfaitement à vos préférences explicites. Elle offre {{preferenceMatch}} que vous avez indiqué(e) comme étant {{importanceLevel}} pour vous.',
    variables: ['preferenceMatch', 'importanceLevel'],
    language: 'fr',
  },
];

/**
 * Obtient un template d'explication par son ID
 * @param id ID du template
 * @returns Template d'explication ou undefined si non trouvé
 */
export function getTemplateById(id: string): ExplanationTemplate | undefined {
  return DEFAULT_EXPLANATION_TEMPLATES.find(template => template.id === id);
}

/**
 * Obtient des templates d'explication par type de facteur
 * @param factorType Type de facteur
 * @param language Langue des templates (par défaut: 'fr')
 * @returns Templates d'explication correspondants
 */
export function getTemplatesByFactorType(factorType: string, language: string = 'fr'): ExplanationTemplate[] {
  return DEFAULT_EXPLANATION_TEMPLATES.filter(
    template => template.factorType === factorType && template.language === language
  );
}

/**
 * Obtient un template d'explication par type de facteur et niveau de détail
 * @param factorType Type de facteur
 * @param detailLevel Niveau de détail ('basic', 'detailed', 'personalized')
 * @param language Langue du template (par défaut: 'fr')
 * @returns Template d'explication ou undefined si non trouvé
 */
export function getTemplateByFactorTypeAndDetailLevel(
  factorType: string,
  detailLevel: 'basic' | 'detailed' | 'personalized',
  language: string = 'fr'
): ExplanationTemplate | undefined {
  const templates = getTemplatesByFactorType(factorType, language);
  const detailSuffix = `-${detailLevel}`;
  
  return templates.find(template => template.id.endsWith(detailSuffix));
}
