import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsObject, IsBoolean, IsEnum, IsNumber } from 'class-validator';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { VisualizationType } from '../enums/visualization-type.enum';

/**
 * DTO pour les facteurs d'explication
 */
export class ExplanationFactorDto {
  @ApiProperty({ description: 'Nom du facteur' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Description du facteur' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Poids du facteur dans la recommandation', example: 0.75 })
  @IsNumber()
  weight: number;

  @ApiProperty({ description: 'Données supplémentaires sur le facteur', required: false })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

/**
 * DTO pour les visualisations d'explication
 */
export class ExplanationVisualizationDto {
  @ApiProperty({
    description: 'Type de visualisation',
    example: 'BAR_CHART',
    enum: VisualizationType
  })
  @IsEnum(VisualizationType)
  type: VisualizationType;

  @ApiProperty({ description: 'Titre de la visualisation' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Description de la visualisation' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Données pour la visualisation' })
  @IsObject()
  data: Record<string, any>;

  @ApiProperty({ description: 'Options de configuration pour la visualisation', required: false })
  @IsObject()
  @IsOptional()
  options?: Record<string, any>;

  @ApiProperty({ description: 'Identifiant unique de la visualisation', required: false })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ description: 'Ordre d\'affichage de la visualisation', required: false })
  @IsNumber()
  @IsOptional()
  order?: number;
}

/**
 * DTO pour les explications améliorées des recommandations
 */
export class EnhancedExplanationDto {
  @ApiProperty({ description: 'ID de la recommandation' })
  @IsString()
  recommendationId: string;

  @ApiProperty({ description: 'Type de recommandation', enum: RecommendationType })
  @IsEnum(RecommendationType)
  recommendationType: RecommendationType;

  @ApiProperty({ description: 'Explication générale de la recommandation' })
  @IsString()
  generalExplanation: string;

  @ApiProperty({ description: 'Explication personnalisée pour l\'utilisateur' })
  @IsString()
  @IsOptional()
  personalizedExplanation?: string;

  @ApiProperty({ description: 'Facteurs qui ont influencé la recommandation', type: [ExplanationFactorDto] })
  @IsArray()
  factors: ExplanationFactorDto[];

  @ApiProperty({ description: 'Visualisations pour expliquer la recommandation', type: [ExplanationVisualizationDto], required: false })
  @IsArray()
  @IsOptional()
  visualizations?: ExplanationVisualizationDto[];

  @ApiProperty({ description: 'Indique si l\'explication est basée sur le profil utilisateur' })
  @IsBoolean()
  isPersonalized: boolean;

  @ApiProperty({ description: 'Métadonnées supplémentaires', required: false })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

/**
 * DTO pour la requête d'explication améliorée
 */
export class EnhancedExplanationRequestDto {
  @ApiProperty({ description: 'ID de la recommandation' })
  @IsString()
  recommendationId: string;

  @ApiProperty({ description: 'Type de recommandation', enum: RecommendationType })
  @IsEnum(RecommendationType)
  recommendationType: RecommendationType;

  @ApiProperty({ description: 'Inclure des visualisations dans l\'explication', default: true })
  @IsBoolean()
  @IsOptional()
  includeVisualizations?: boolean;

  @ApiProperty({ description: 'Niveau de détail de l\'explication', example: 'DETAILED', default: 'STANDARD' })
  @IsString()
  @IsOptional()
  detailLevel?: 'BASIC' | 'STANDARD' | 'DETAILED';

  @ApiProperty({ description: 'Langue de l\'explication', example: 'fr', default: 'fr' })
  @IsString()
  @IsOptional()
  language?: string;
}
