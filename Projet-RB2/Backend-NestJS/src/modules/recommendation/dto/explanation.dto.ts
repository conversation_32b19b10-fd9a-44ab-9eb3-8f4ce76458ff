import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsObject, IsOptional, IsNumber, IsArray, IsBoolean, IsDateString, IsUUID, Min, Max, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * DTO pour un facteur d'explication
 */
export class ExplanationFactorDto {
  @ApiProperty({
    description: 'Type de facteur',
    example: 'category',
    enum: ['category', 'tag', 'similarity', 'popularity', 'context', 'interaction', 'other'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['category', 'tag', 'similarity', 'popularity', 'context', 'interaction', 'other'])
  type: 'category' | 'tag' | 'similarity' | 'popularity' | 'context' | 'interaction' | 'other';

  @ApiProperty({
    description: 'Nom du facteur',
    example: 'Yoga',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description du facteur',
    example: 'Vous avez montré un intérêt pour le yoga',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Poids du facteur dans la recommandation (0-1)',
    example: 0.8,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  weight: number;

  @ApiPropertyOptional({
    description: 'Données supplémentaires spécifiques au facteur',
    example: { interactionCount: 5, lastInteraction: '2023-01-15T10:30:00Z' },
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;
}

/**
 * DTO pour les requêtes d'explication
 */
export class ExplanationRequestDto {
  @ApiProperty({
    description: 'ID de la recommandation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  recommendationId: string;

  @ApiPropertyOptional({
    description: 'Nombre maximum de facteurs à inclure',
    example: 3,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  maxFactors?: number;

  @ApiPropertyOptional({
    description: 'Seuil minimum pour inclure un facteur',
    example: 0.1,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  @Type(() => Number)
  factorThreshold?: number;

  @ApiPropertyOptional({
    description: 'Inclure les facteurs négatifs',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeNegativeFactors?: boolean;

  @ApiPropertyOptional({
    description: 'Niveau de détail de l\'explication',
    example: 'detailed',
    enum: ['basic', 'detailed', 'technical'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['basic', 'detailed', 'technical'])
  detailLevel?: 'basic' | 'detailed' | 'technical';

  @ApiPropertyOptional({
    description: 'Langue de l\'explication',
    example: 'fr',
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiPropertyOptional({
    description: 'Format de l\'explication',
    example: 'text',
    enum: ['text', 'html', 'json'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['text', 'html', 'json'])
  format?: 'text' | 'html' | 'json';
}

/**
 * DTO pour les réponses d'explication
 */
export class ExplanationResponseDto {
  @ApiProperty({
    description: 'ID de la recommandation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  recommendationId: string;

  @ApiProperty({
    description: 'ID de l\'utilisateur',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'ID de l\'élément recommandé',
    example: '123e4567-e89b-12d3-a456-************',
  })
  itemId: string;

  @ApiProperty({
    description: 'Type d\'élément recommandé',
    example: 'RETREAT',
    enum: Object.values(RecommendationType),
  })
  itemType: RecommendationType;

  @ApiProperty({
    description: 'Stratégie de recommandation utilisée',
    example: 'HYBRID',
    enum: Object.values(RecommendationStrategy),
  })
  strategy: RecommendationStrategy;

  @ApiProperty({
    description: 'Score de la recommandation',
    example: 0.85,
  })
  score: number;

  @ApiProperty({
    description: 'Facteurs qui ont influencé la recommandation',
    type: [ExplanationFactorDto],
  })
  factors: ExplanationFactorDto[];

  @ApiProperty({
    description: 'Résumé textuel de l\'explication',
    example: 'Cette retraite vous est recommandée car elle correspond à votre intérêt pour le yoga et la méditation.',
  })
  summary: string;

  @ApiProperty({
    description: 'Timestamp de la génération de l\'explication',
    example: '2023-06-15T10:30:00Z',
  })
  timestamp: Date;

  @ApiPropertyOptional({
    description: 'Métadonnées supplémentaires',
    example: { version: '1.0', generationTime: 120 },
  })
  metadata?: Record<string, any>;
}

/**
 * DTO pour les événements d'explication
 */
export class ExplanationEventDto {
  @ApiProperty({
    description: 'ID de la recommandation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  recommendationId: string;

  @ApiProperty({
    description: 'Type d\'événement',
    example: 'view',
    enum: ['view', 'expand', 'click', 'dismiss', 'feedback'],
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(['view', 'expand', 'click', 'dismiss', 'feedback'])
  eventType: 'view' | 'expand' | 'click' | 'dismiss' | 'feedback';

  @ApiPropertyOptional({
    description: 'Données de l\'événement',
    example: { feedbackRating: 4, comment: 'Très pertinent' },
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;
}

/**
 * DTO pour les statistiques d'explications
 */
export class ExplanationStatsResponseDto {
  @ApiProperty({
    description: 'Nombre total d\'explications générées',
    example: 1250,
  })
  totalExplanations: number;

  @ApiProperty({
    description: 'Nombre d\'explications par stratégie',
    example: {
      CONTENT_BASED: 450,
      COLLABORATIVE: 350,
      HYBRID: 300,
      MATRIX_FACTORIZATION: 150,
    },
  })
  byStrategy: Record<string, number>;

  @ApiProperty({
    description: 'Nombre d\'explications par type d\'élément',
    example: {
      RETREAT: 800,
      COURSE: 450,
    },
  })
  byItemType: Record<string, number>;

  @ApiProperty({
    description: 'Facteurs les plus fréquents',
    example: [
      {
        type: 'category',
        name: 'Yoga',
        count: 320,
        averageWeight: 0.75,
      },
      {
        type: 'tag',
        name: 'Méditation',
        count: 280,
        averageWeight: 0.68,
      },
    ],
  })
  topFactors: {
    type: string;
    name: string;
    count: number;
    averageWeight: number;
  }[];

  @ApiProperty({
    description: 'Taux d\'interaction avec les explications',
    example: 0.42,
  })
  interactionRate: number;

  @ApiProperty({
    description: 'Taux de conversion après explication',
    example: 0.18,
  })
  conversionRate: number;
}
