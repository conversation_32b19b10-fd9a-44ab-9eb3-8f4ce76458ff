import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsArray, IsNumber, Min, Max, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { ExternalDataType } from '../interfaces/external-data.interface';

/**
 * DTO pour les options d'enrichissement avec des données externes
 */
export class ExternalDataEnrichmentDto {
  @ApiProperty({
    description: 'Activer l\'enrichissement avec des données externes',
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  enrichWithExternalData?: boolean;

  @ApiProperty({
    description: 'Types de données externes à inclure',
    required: false,
    enum: ExternalDataType,
    isArray: true,
  })
  @IsEnum(ExternalDataType, { each: true })
  @IsArray()
  @IsOptional()
  externalDataTypes?: ExternalDataType[];

  @ApiProperty({
    description: 'Score de pertinence minimum pour les données externes (entre 0 et 1)',
    required: false,
    minimum: 0,
    maximum: 1,
    default: 0.5,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  @Type(() => Number)
  minExternalDataRelevanceScore?: number;

  @ApiProperty({
    description: 'Nombre maximum d\'éléments par type de données externes',
    required: false,
    minimum: 1,
    default: 3,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  @Type(() => Number)
  maxExternalDataItemsPerType?: number;
}
