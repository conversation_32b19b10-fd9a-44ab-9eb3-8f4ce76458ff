import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsOptional,
  IsInt,
  Min,
  Max,
  IsArray,
  IsString,
  IsObject,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';

/**
 * DTO pour les requêtes de recommandations
 */
export class GetRecommendationsDto {
  @ApiPropertyOptional({
    enum: RecommendationType,
    description: 'Type d\'élément à recommander',
    example: RecommendationType.RETREAT,
  })
  @IsEnum(RecommendationType)
  @IsOptional()
  type?: RecommendationType = RecommendationType.RETREAT;

  @ApiPropertyOptional({
    enum: RecommendationStrategy,
    description: 'Stratégie de recommandation à utiliser',
    example: RecommendationStrategy.HYBRID,
  })
  @IsEnum(RecommendationStrategy)
  @IsOptional()
  strategy?: RecommendationStrategy;

  @ApiPropertyOptional({
    enum: HybridMethod,
    description: 'Méthode hybride à utiliser (uniquement si strategy=HYBRID)',
    example: HybridMethod.WEIGHTED,
  })
  @IsEnum(HybridMethod)
  @IsOptional()
  hybridMethod?: HybridMethod;

  @ApiPropertyOptional({
    type: Number,
    description: 'Nombre maximum de recommandations à retourner',
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10,
  })
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({
    type: Number,
    description: 'Numéro de page pour la pagination',
    minimum: 1,
    default: 1,
    example: 1,
  })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({
    type: 'object',
    description: 'Filtres à appliquer aux recommandations',
    example: { category: 'yoga', minDuration: 3 },
  })
  @IsObject()
  @IsOptional()
  filters?: Record<string, any>;

  @ApiPropertyOptional({
    type: [String],
    description: 'IDs des éléments à exclure des recommandations',
    example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  excludeIds?: string[];

  @ApiPropertyOptional({
    type: Boolean,
    description: 'Inclure les métadonnées détaillées dans les résultats',
    default: true,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  includeMetadata?: boolean = true;

  @ApiPropertyOptional({
    type: String,
    description: 'Champ sur lequel trier les résultats',
    example: 'score',
  })
  @IsString()
  @IsOptional()
  sortBy?: string = 'score';

  @ApiPropertyOptional({
    enum: ['asc', 'desc'],
    description: 'Ordre de tri',
    default: 'desc',
    example: 'desc',
  })
  @IsEnum(['asc', 'desc'])
  @IsOptional()
  sortOrder?: 'asc' | 'desc' = 'desc';
}
