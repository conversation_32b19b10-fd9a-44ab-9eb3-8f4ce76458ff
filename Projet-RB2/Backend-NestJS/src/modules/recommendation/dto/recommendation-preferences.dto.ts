import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsNumber, Min, Max, IsBoolean, IsObject, ValidateNested, IsArray, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { FactorizationMethod } from '../enums/factorization-method.enum';

/**
 * DTO pour les paramètres de diversification
 */
export class DiversificationParamsDto {
  @ApiPropertyOptional({ description: 'Activer la diversification', default: true })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({ description: 'Poids de la diversification (entre 0 et 1)', default: 0.3 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  weight?: number;

  @ApiPropertyOptional({ 
    description: 'Méthode de diversification', 
    enum: ['MMR', 'DETERMINANTAL', 'CLUSTERING'],
    default: 'MMR'
  })
  @IsOptional()
  @IsEnum(['MMR', 'DETERMINANTAL', 'CLUSTERING'])
  method?: 'MMR' | 'DETERMINANTAL' | 'CLUSTERING';
}

/**
 * DTO pour les paramètres de factorisation matricielle
 */
export class MatrixFactorizationParamsDto {
  @ApiPropertyOptional({ 
    description: 'Méthode de factorisation', 
    enum: FactorizationMethod,
    default: FactorizationMethod.SVD
  })
  @IsOptional()
  @IsEnum(FactorizationMethod)
  method?: FactorizationMethod;

  @ApiPropertyOptional({ description: 'Nombre de facteurs latents', default: 20 })
  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(100)
  numFactors?: number;

  @ApiPropertyOptional({ description: 'Paramètre de régularisation', default: 0.1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  regularization?: number;

  @ApiPropertyOptional({ description: 'Nombre d\'itérations', default: 50 })
  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(200)
  numIterations?: number;
}

/**
 * DTO pour les paramètres hybrides
 */
export class HybridParamsDto {
  @ApiPropertyOptional({ 
    description: 'Méthode hybride', 
    enum: HybridMethod,
    default: HybridMethod.WEIGHTED
  })
  @IsOptional()
  @IsEnum(HybridMethod)
  method?: HybridMethod;

  @ApiPropertyOptional({ description: 'Poids pour les recommandations basées sur le contenu', default: 0.6 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  contentBasedWeight?: number;

  @ApiPropertyOptional({ description: 'Poids pour les recommandations collaboratives', default: 0.4 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  collaborativeWeight?: number;

  @ApiPropertyOptional({ description: 'Seuil d\'interactions pour la méthode de commutation', default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  interactionThreshold?: number;
}

/**
 * DTO pour les catégories préférées
 */
export class CategoryPreferencesDto {
  @ApiPropertyOptional({ description: 'Catégories préférées', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferred?: string[];

  @ApiPropertyOptional({ description: 'Catégories à éviter', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  avoided?: string[];
}

/**
 * DTO pour les préférences de recommandation
 */
export class RecommendationPreferencesDto {
  @ApiPropertyOptional({ 
    description: 'Stratégie de recommandation préférée', 
    enum: RecommendationStrategy,
    default: RecommendationStrategy.HYBRID
  })
  @IsOptional()
  @IsEnum(RecommendationStrategy)
  strategy?: RecommendationStrategy;

  @ApiPropertyOptional({ description: 'Paramètres pour la diversification' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DiversificationParamsDto)
  diversification?: DiversificationParamsDto;

  @ApiPropertyOptional({ description: 'Paramètres pour la factorisation matricielle' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MatrixFactorizationParamsDto)
  matrixFactorization?: MatrixFactorizationParamsDto;

  @ApiPropertyOptional({ description: 'Paramètres pour la méthode hybride' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => HybridParamsDto)
  hybrid?: HybridParamsDto;

  @ApiPropertyOptional({ description: 'Préférences de catégories' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => CategoryPreferencesDto)
  categories?: CategoryPreferencesDto;

  @ApiPropertyOptional({ description: 'Nombre maximum de recommandations à afficher', default: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  maxRecommendations?: number;

  @ApiPropertyOptional({ description: 'Inclure les métadonnées détaillées dans les résultats', default: true })
  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean;
}
