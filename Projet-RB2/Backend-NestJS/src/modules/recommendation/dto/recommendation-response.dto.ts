import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * DTO pour les métadonnées d'une recommandation
 */
export class RecommendationMetadataDto {
  @ApiPropertyOptional({
    description: 'Catégorie de l\'élément',
    example: 'Yoga',
  })
  category?: string;

  @ApiPropertyOptional({
    description: 'Niveau de l\'élément',
    example: 'INTERMEDIATE',
  })
  level?: string;

  @ApiPropertyOptional({
    description: 'Localisation de l\'élément',
    example: 'Paris, France',
  })
  location?: string;

  @ApiPropertyOptional({
    description: 'Durée de l\'élément (en jours)',
    example: 7,
  })
  duration?: number;

  @ApiPropertyOptional({
    type: [String],
    description: 'Tags associés à l\'élément',
    example: ['yoga', 'meditation', 'wellness'],
  })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Prix de l\'élément',
    example: 1200,
  })
  price?: number;

  @ApiPropertyOptional({
    description: 'Devise du prix',
    example: 'EUR',
  })
  currency?: string;

  @ApiPropertyOptional({
    description: 'Date de début (pour les événements)',
    example: '2023-07-15',
  })
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Date de fin (pour les événements)',
    example: '2023-07-22',
  })
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Nombre d\'avis',
    example: 42,
  })
  reviewCount?: number;

  @ApiPropertyOptional({
    description: 'Note moyenne',
    example: 4.7,
  })
  averageRating?: number;

  @ApiPropertyOptional({
    description: 'Disponibilité',
    example: true,
  })
  available?: boolean;

  @ApiPropertyOptional({
    description: 'Nombre de places restantes',
    example: 5,
  })
  remainingSpots?: number;

  @ApiPropertyOptional({
    description: 'Propriétés additionnelles spécifiques au type d\'élément',
    example: { hasPool: true, isBeachfront: false },
  })
  additionalProperties?: Record<string, any>;
}

/**
 * DTO pour une recommandation
 */
export class RecommendationResponseDto {
  @ApiProperty({
    description: 'ID unique de l\'élément',
    example: '60d21b4667d0d8992e610c85',
  })
  id: string;

  @ApiProperty({
    enum: RecommendationType,
    description: 'Type d\'élément',
    example: RecommendationType.RETREAT,
  })
  type: RecommendationType;

  @ApiProperty({
    description: 'Titre de l\'élément',
    example: 'Retraite de Yoga dans les Alpes',
  })
  title: string;

  @ApiPropertyOptional({
    description: 'Description de l\'élément',
    example: 'Une semaine de détente et de reconnexion avec la nature...',
  })
  description?: string;

  @ApiProperty({
    description: 'Score de recommandation (entre 0 et 1)',
    minimum: 0,
    maximum: 1,
    example: 0.95,
  })
  score: number;

  @ApiPropertyOptional({
    type: [String],
    description: 'Sources de la recommandation',
    example: ['content-based', 'collaborative'],
  })
  sources?: string[];

  @ApiPropertyOptional({
    type: [String],
    description: 'Raisons de la recommandation',
    example: ['Basé sur vos préférences', 'Similaire à vos activités précédentes'],
  })
  reasons?: string[];

  @ApiPropertyOptional({
    description: 'URL de l\'image principale',
    example: 'https://example.com/images/retreat-123.jpg',
  })
  imageUrl?: string;

  @ApiPropertyOptional({
    description: 'URL de l\'élément',
    example: 'https://retreatandbe.com/retreats/retraite-yoga-alpes',
  })
  url?: string;

  @ApiPropertyOptional({
    type: RecommendationMetadataDto,
    description: 'Métadonnées détaillées de l\'élément',
  })
  metadata?: RecommendationMetadataDto;
}
