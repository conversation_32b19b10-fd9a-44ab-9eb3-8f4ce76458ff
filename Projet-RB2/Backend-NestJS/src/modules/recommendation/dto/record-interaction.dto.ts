import { IsString, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RecommendationType } from '../enums/recommendation-type.enum';

export class RecordInteractionDto {
  @ApiProperty({
    description: 'ID de l\'élément',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  itemId: string;

  @ApiProperty({
    description: 'Type d\'élément',
    enum: RecommendationType,
    example: RecommendationType.COURSE,
  })
  @IsEnum(RecommendationType)
  @IsNotEmpty()
  type: RecommendationType;

  @ApiProperty({
    description: 'Type d\'interaction',
    example: 'VIEW',
  })
  @IsString()
  @IsNotEmpty()
  interactionType: string;

  @ApiPropertyOptional({
    description: 'Métadonnées de l\'interaction',
    example: { duration: 300, progress: 0.5 },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
