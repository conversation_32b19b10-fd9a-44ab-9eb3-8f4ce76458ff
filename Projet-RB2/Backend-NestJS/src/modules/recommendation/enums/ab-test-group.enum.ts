/**
 * Groupes pour les tests A/B des recommandations
 */
export enum ABTestGroup {
  /**
   * Groupe de contrôle utilisant l'algorithme hybride standard
   */
  CONTROL = 'CONTROL',
  
  /**
   * Groupe de test utilisant l'algorithme de factorisation matricielle
   */
  MATRIX_FACTORIZATION = 'MATRIX_FACTORIZATION',
  
  /**
   * Groupe de test utilisant l'algorithme contextuel
   */
  CONTEXTUAL = 'CONTEXTUAL',
  
  /**
   * Groupe de test utilisant l'algorithme de deep learning
   */
  DEEP_LEARNING = 'DEEP_LEARNING',
}
