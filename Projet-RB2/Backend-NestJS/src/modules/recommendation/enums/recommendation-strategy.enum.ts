/**
 * Stratégies de recommandation disponibles
 */
export enum RecommendationStrategy {
  /**
   * Recommandations basées sur les attributs des items et les préférences utilisateur
   */
  CONTENT_BASED = 'CONTENT_BASED',

  /**
   * Recommandations basées sur les comportements similaires d'utilisateurs
   */
  COLLABORATIVE = 'COLLABORATIVE',

  /**
   * Combinaison des approches basées sur le contenu et le filtrage collaboratif
   */
  HYBRID = 'HYBRID',

  /**
   * Recommandations basées sur des modèles de factorisation matricielle
   */
  MATRIX_FACTORIZATION = 'MATRIX_FACTORIZATION',

  /**
   * Recommandations adaptées au contexte (saison, localisation, etc.)
   */
  CONTEXTUAL = 'CONTEXTUAL',

  /**
   * Recommandations basées sur des règles métier et l'expertise du domaine
   */
  KNOWLEDGE_BASED = 'KNOWLEDGE_BASED',

  /**
   * Recommandations basées sur des modèles d'apprentissage profond (deep learning)
   */
  DEEP_LEARNING = 'DEEP_LEARNING',

  /**
   * Recommandations en temps réel basées sur le comportement immédiat de l'utilisateur
   */
  REALTIME = 'REALTIME',
}
