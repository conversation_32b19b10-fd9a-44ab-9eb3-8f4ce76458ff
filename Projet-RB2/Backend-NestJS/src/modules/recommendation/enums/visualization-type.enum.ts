/**
 * Types de visualisation pour les explications de recommandation
 */
export enum VisualizationType {
  /** Graphique en barres */
  BAR_CHART = 'BAR_CHART',
  
  /** Graphique en ligne */
  LINE_CHART = 'LINE_CHART',
  
  /** Graphique en camembert */
  PIE_CHART = 'PIE_CHART',
  
  /** Graphique radar */
  RADAR_CHART = 'RADAR_CHART',
  
  /** Graphique de comparaison */
  COMPARISON_CHART = 'COMPARISON_CHART',
  
  /** Timeline */
  TIMELINE = 'TIMELINE',
  
  /** Tableau */
  TABLE = 'TABLE',
  
  /** Carte de chaleur */
  HEATMAP = 'HEATMAP',
  
  /** Nuage de points */
  SCATTER_PLOT = 'SCATTER_PLOT',
  
  /** Graphique en bulles */
  BUBBLE_CHART = 'BUBBLE_CHART',
  
  /** Graphique en aires */
  AREA_CHART = 'AREA_CHART',
  
  /** Diagramme de Sankey */
  SANKEY_DIAGRAM = 'SANKEY_DIAGRAM',
  
  /** Carte */
  MAP = 'MAP',
  
  /** Jauge */
  GAUGE = 'GAUGE',
  
  /** Graphique en entonnoir */
  FUNNEL_CHART = 'FUNNEL_CHART',
  
  /** Graphique en cascade */
  WATERFALL_CHART = 'WATERFALL_CHART',
  
  /** Graphique en boîte */
  BOX_PLOT = 'BOX_PLOT',
  
  /** Graphique en arbre */
  TREE_MAP = 'TREE_MAP',
  
  /** Graphique en réseau */
  NETWORK_GRAPH = 'NETWORK_GRAPH',
  
  /** Graphique en étoile */
  STAR_CHART = 'STAR_CHART',
  
  /** Graphique en anneau */
  DONUT_CHART = 'DONUT_CHART',
  
  /** Graphique en polaire */
  POLAR_CHART = 'POLAR_CHART',
  
  /** Graphique en escalier */
  STEP_CHART = 'STEP_CHART',
  
  /** Graphique en chandelier */
  CANDLESTICK_CHART = 'CANDLESTICK_CHART',
  
  /** Graphique en histogramme */
  HISTOGRAM = 'HISTOGRAM',
  
  /** Graphique en violon */
  VIOLIN_PLOT = 'VIOLIN_PLOT',
  
  /** Graphique en ridgeline */
  RIDGELINE_PLOT = 'RIDGELINE_PLOT',
  
  /** Graphique en parallèle */
  PARALLEL_COORDINATES = 'PARALLEL_COORDINATES',
  
  /** Graphique en sunburst */
  SUNBURST_CHART = 'SUNBURST_CHART',
  
  /** Graphique en calendrier */
  CALENDAR_HEATMAP = 'CALENDAR_HEATMAP',
  
  /** Graphique en stream */
  STREAM_GRAPH = 'STREAM_GRAPH',
  
  /** Graphique en alluvial */
  ALLUVIAL_DIAGRAM = 'ALLUVIAL_DIAGRAM',
  
  /** Graphique en chord */
  CHORD_DIAGRAM = 'CHORD_DIAGRAM',
  
  /** Graphique en dendrogramme */
  DENDROGRAM = 'DENDROGRAM',
  
  /** Graphique en force */
  FORCE_DIRECTED_GRAPH = 'FORCE_DIRECTED_GRAPH',
  
  /** Graphique en arc */
  ARC_DIAGRAM = 'ARC_DIAGRAM',
  
  /** Graphique en matrice */
  MATRIX_PLOT = 'MATRIX_PLOT',
  
  /** Graphique en contour */
  CONTOUR_PLOT = 'CONTOUR_PLOT',
  
  /** Graphique en surface */
  SURFACE_PLOT = 'SURFACE_PLOT',
  
  /** Graphique en 3D */
  THREE_D_PLOT = 'THREE_D_PLOT',
  
  /** Autre type de visualisation */
  OTHER = 'OTHER',
}
