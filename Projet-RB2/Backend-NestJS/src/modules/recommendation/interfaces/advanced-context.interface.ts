/**
 * Interfaces for the advanced contextual recommendation system
 */

/**
 * Advanced context configuration
 */
export interface AdvancedContextConfig {
  /**
   * Enable/disable advanced context features
   */
  enabled: boolean;

  /**
   * Context sources to use
   */
  contextSources: ContextSource[];

  /**
   * Context detection strategies
   */
  detectionStrategies: ContextDetectionStrategy[];

  /**
   * Context adaptation strategies
   */
  adaptationStrategies: ContextAdaptationStrategy[];

  /**
   * Context weighting configuration
   */
  contextWeighting: ContextWeightingConfig;

  /**
   * Context persistence configuration
   */
  contextPersistence: ContextPersistenceConfig;

  /**
   * Additional context parameters
   */
  params?: Record<string, any>;
}

/**
 * Context source
 */
export interface ContextSource {
  /**
   * Source type
   */
  type: ContextSourceType;

  /**
   * Source name
   */
  name: string;

  /**
   * Source enabled/disabled
   */
  enabled: boolean;

  /**
   * Source priority (higher = more important)
   */
  priority: number;

  /**
   * Source configuration
   */
  config?: Record<string, any>;

  /**
   * Source refresh interval (in seconds)
   */
  refreshInterval?: number;
}

/**
 * Context source type
 */
export enum ContextSourceType {
  /**
   * User location
   */
  LOCATION = 'LOCATION',

  /**
   * Weather data
   */
  WEATHER = 'WEATHER',

  /**
   * Time of day
   */
  TIME_OF_DAY = 'TIME_OF_DAY',

  /**
   * Day of week
   */
  DAY_OF_WEEK = 'DAY_OF_WEEK',

  /**
   * Season
   */
  SEASON = 'SEASON',

  /**
   * User device
   */
  DEVICE = 'DEVICE',

  /**
   * User activity
   */
  ACTIVITY = 'ACTIVITY',

  /**
   * Local events
   */
  LOCAL_EVENTS = 'LOCAL_EVENTS',

  /**
   * User mood
   */
  MOOD = 'MOOD',

  /**
   * User calendar
   */
  CALENDAR = 'CALENDAR',

  /**
   * Social context
   */
  SOCIAL = 'SOCIAL',

  /**
   * Health data
   */
  HEALTH = 'HEALTH',

  /**
   * Custom context
   */
  CUSTOM = 'CUSTOM',
}

/**
 * Context detection strategy
 */
export enum ContextDetectionStrategy {
  /**
   * Rule-based detection
   */
  RULE_BASED = 'RULE_BASED',

  /**
   * Machine learning-based detection
   */
  ML_BASED = 'ML_BASED',

  /**
   * User-provided context
   */
  USER_PROVIDED = 'USER_PROVIDED',

  /**
   * Hybrid detection
   */
  HYBRID = 'HYBRID',
}

/**
 * Context adaptation strategy
 */
export enum ContextAdaptationStrategy {
  /**
   * Pre-filtering
   */
  PRE_FILTERING = 'PRE_FILTERING',

  /**
   * Post-filtering
   */
  POST_FILTERING = 'POST_FILTERING',

  /**
   * Contextual modeling
   */
  CONTEXTUAL_MODELING = 'CONTEXTUAL_MODELING',

  /**
   * Hybrid adaptation
   */
  HYBRID = 'HYBRID',
}

/**
 * Context weighting configuration
 */
export interface ContextWeightingConfig {
  /**
   * Enable/disable context weighting
   */
  enabled: boolean;

  /**
   * Weighting strategy
   */
  strategy: ContextWeightingStrategy;

  /**
   * Default weights for different context types
   */
  defaultWeights: Record<ContextSourceType, number>;

  /**
   * User-specific weights
   */
  userWeights?: Record<string, Record<ContextSourceType, number>>;
}

/**
 * Context weighting strategy
 */
export enum ContextWeightingStrategy {
  /**
   * Static weighting
   */
  STATIC = 'STATIC',

  /**
   * Dynamic weighting based on user behavior
   */
  DYNAMIC = 'DYNAMIC',

  /**
   * Adaptive weighting
   */
  ADAPTIVE = 'ADAPTIVE',
}

/**
 * Context persistence configuration
 */
export interface ContextPersistenceConfig {
  /**
   * Enable/disable context persistence
   */
  enabled: boolean;

  /**
   * Persistence strategy
   */
  strategy: ContextPersistenceStrategy;

  /**
   * Time-to-live for context data (in seconds)
   */
  ttl: number;
}

/**
 * Context persistence strategy
 */
export enum ContextPersistenceStrategy {
  /**
   * In-memory persistence
   */
  IN_MEMORY = 'IN_MEMORY',

  /**
   * Database persistence
   */
  DATABASE = 'DATABASE',

  /**
   * Cache persistence
   */
  CACHE = 'CACHE',
}

/**
 * Context data
 */
export interface ContextData {
  /**
   * User ID
   */
  userId: string;

  /**
   * Context timestamp
   */
  timestamp: Date;

  /**
   * Context values
   */
  values: Record<ContextSourceType, any>;

  /**
   * Context confidence scores
   */
  confidence?: Record<ContextSourceType, number>;

  /**
   * Context metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Contextual recommendation request
 */
export interface ContextualRecommendationRequest {
  /**
   * User ID
   */
  userId: string;

  /**
   * Explicit context data
   */
  explicitContext?: Record<string, any>;

  /**
   * Number of recommendations to return
   */
  limit?: number;

  /**
   * Context adaptation parameters
   */
  adaptationParams?: Record<string, any>;
}

/**
 * Contextual recommendation result
 */
export interface ContextualRecommendationResult {
  /**
   * Recommended items
   */
  items: any[];

  /**
   * Context data used for recommendations
   */
  contextData: ContextData;

  /**
   * Context influence on recommendations
   */
  contextInfluence: Record<ContextSourceType, number>;
}
