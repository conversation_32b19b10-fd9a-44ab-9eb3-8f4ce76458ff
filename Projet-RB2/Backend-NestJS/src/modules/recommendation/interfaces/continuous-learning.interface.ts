import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * Interface pour les paramètres d'apprentissage continu
 */
export interface ContinuousLearningParams {
  /** Taux d'apprentissage pour les mises à jour incrémentielles */
  learningRate: number;

  /** Facteur d'oubli pour les anciennes interactions */
  forgettingFactor: number;

  /** Seuil pour détecter les changements de comportement */
  changeDetectionThreshold: number;

  /** Fenêtre temporelle pour l'analyse des interactions récentes (en heures) */
  recentInteractionsWindow: number;

  /** Nombre minimum d'interactions pour déclencher une mise à jour */
  minInteractionsForUpdate: number;

  /** Intervalle de mise à jour des modèles (en minutes) */
  modelUpdateInterval: number;

  /** Poids des différents types d'interactions */
  interactionWeights: Record<string, number>;

  /** Activer/désactiver la détection des comportements aberrants */
  enableOutlierDetection: boolean;

  /** Seuil pour la détection des comportements aberrants */
  outlierDetectionThreshold: number;
}

/**
 * Interface pour les métriques d'apprentissage
 */
export interface LearningMetrics {
  /** ID de l'utilisateur */
  userId: string;

  /** Période d'analyse */
  period: {
    start: Date;
    end: Date;
  };

  /** Nombre de mises à jour du modèle */
  modelUpdates: number;

  /** Changements détectés */
  detectedChanges: {
    /** Type de changement */
    type: 'preference' | 'behavior' | 'context';
    
    /** Timestamp du changement */
    timestamp: Date;
    
    /** Description du changement */
    description: string;
    
    /** Magnitude du changement (0-1) */
    magnitude: number;
  }[];

  /** Performances avant/après apprentissage */
  performance: {
    /** Précision avant apprentissage */
    precisionBefore: number;
    
    /** Précision après apprentissage */
    precisionAfter: number;
    
    /** Rappel avant apprentissage */
    recallBefore: number;
    
    /** Rappel après apprentissage */
    recallAfter: number;
    
    /** Score F1 avant apprentissage */
    f1ScoreBefore: number;
    
    /** Score F1 après apprentissage */
    f1ScoreAfter: number;
  };

  /** Statistiques des interactions */
  interactionStats: {
    /** Nombre total d'interactions */
    total: number;
    
    /** Nombre d'interactions par type */
    byType: Record<string, number>;
    
    /** Nombre d'interactions par stratégie */
    byStrategy: Record<RecommendationStrategy, number>;
    
    /** Nombre d'interactions par type d'élément */
    byItemType: Record<RecommendationType, number>;
  };
}

/**
 * Interface pour un événement d'apprentissage
 */
export interface LearningEvent {
  /** ID de l'événement */
  id: string;

  /** ID de l'utilisateur */
  userId: string;

  /** Type d'événement */
  eventType: 'model_update' | 'behavior_change' | 'preference_change' | 'outlier_detected';

  /** Timestamp de l'événement */
  timestamp: Date;

  /** Données de l'événement */
  data: Record<string, any>;

  /** Impact estimé sur les recommandations (0-1) */
  estimatedImpact: number;
}

/**
 * Interface pour un modèle utilisateur
 */
export interface UserModel {
  /** ID de l'utilisateur */
  userId: string;

  /** Dernière mise à jour */
  lastUpdated: Date;

  /** Préférences de l'utilisateur */
  preferences: Record<string, number>;

  /** Intérêts par catégorie */
  categoryInterests: Record<string, number>;

  /** Intérêts par tag */
  tagInterests: Record<string, number>;

  /** Historique des interactions récentes */
  recentInteractions: {
    /** ID de l'élément */
    itemId: string;
    
    /** Type d'élément */
    itemType: RecommendationType;
    
    /** Type d'interaction */
    interactionType: string;
    
    /** Timestamp de l'interaction */
    timestamp: Date;
    
    /** Poids de l'interaction */
    weight: number;
  }[];

  /** Contexte utilisateur */
  context: {
    /** Localisation */
    location?: {
      latitude?: number;
      longitude?: number;
      country?: string;
      city?: string;
    };
    
    /** Appareil */
    device?: string;
    
    /** Heure de la journée préférée */
    preferredTimeOfDay?: number[];
    
    /** Jour de la semaine préféré */
    preferredDayOfWeek?: number[];
  };

  /** Métriques du modèle */
  metrics: {
    /** Précision */
    precision: number;
    
    /** Rappel */
    recall: number;
    
    /** Score F1 */
    f1Score: number;
    
    /** Nombre de mises à jour */
    updates: number;
  };
}
