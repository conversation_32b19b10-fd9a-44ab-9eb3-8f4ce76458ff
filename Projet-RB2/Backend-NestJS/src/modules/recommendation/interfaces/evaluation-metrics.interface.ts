import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * Interface pour les métriques d'évaluation des recommandations
 */
export interface EvaluationMetrics {
  /** ID de l'évaluation */
  id?: string;
  
  /** Timestamp de l'évaluation */
  timestamp: Date;
  
  /** Période d'évaluation */
  period: {
    start: Date;
    end: Date;
  };
  
  /** Type de recommandation évalué */
  recommendationType?: RecommendationType;
  
  /** Stratégie de recommandation évaluée */
  recommendationStrategy?: RecommendationStrategy;
  
  /** Métriques de précision */
  accuracyMetrics: AccuracyMetrics;
  
  /** Métriques d'engagement */
  engagementMetrics: EngagementMetrics;
  
  /** Métriques commerciales */
  businessMetrics: BusinessMetrics;
  
  /** Métriques de diversité et d'équité */
  diversityMetrics: DiversityMetrics;
  
  /** Métriques de performance */
  performanceMetrics: PerformanceMetrics;
  
  /** Métriques personnalisées */
  customMetrics?: Record<string, any>;
}

/**
 * Interface pour les métriques de précision
 */
export interface AccuracyMetrics {
  /** Précision (vrais positifs / (vrais positifs + faux positifs)) */
  precision: number;
  
  /** Rappel (vrais positifs / (vrais positifs + faux négatifs)) */
  recall: number;
  
  /** Score F1 (2 * (précision * rappel) / (précision + rappel)) */
  f1Score: number;
  
  /** Précision moyenne (MAP) */
  meanAveragePrecision?: number;
  
  /** Gain cumulatif actualisé normalisé (NDCG) */
  ndcg?: number;
  
  /** Erreur quadratique moyenne (MSE) */
  meanSquaredError?: number;
  
  /** Erreur absolue moyenne (MAE) */
  meanAbsoluteError?: number;
  
  /** Matrice de confusion */
  confusionMatrix?: {
    truePositives: number;
    falsePositives: number;
    trueNegatives: number;
    falseNegatives: number;
  };
}

/**
 * Interface pour les métriques d'engagement
 */
export interface EngagementMetrics {
  /** Taux de clics (CTR) */
  clickThroughRate: number;
  
  /** Temps moyen passé (en secondes) */
  averageTimeSpent: number;
  
  /** Taux de rebond */
  bounceRate: number;
  
  /** Nombre moyen de pages vues */
  averagePageViews: number;
  
  /** Taux d'interaction */
  interactionRate: number;
  
  /** Détails des interactions par type */
  interactionsByType: Record<string, number>;
  
  /** Taux de retour */
  returnRate?: number;
  
  /** Score d'engagement global (0-1) */
  overallEngagementScore: number;
}

/**
 * Interface pour les métriques commerciales
 */
export interface BusinessMetrics {
  /** Taux de conversion */
  conversionRate: number;
  
  /** Revenu généré */
  revenue: number;
  
  /** Valeur moyenne des commandes */
  averageOrderValue?: number;
  
  /** Retour sur investissement (ROI) */
  roi?: number;
  
  /** Coût par acquisition (CPA) */
  costPerAcquisition?: number;
  
  /** Valeur vie client (LTV) */
  customerLifetimeValue?: number;
  
  /** Taux de rétention */
  retentionRate?: number;
  
  /** Impact sur les revenus (%) */
  revenueImpact: number;
}

/**
 * Interface pour les métriques de diversité et d'équité
 */
export interface DiversityMetrics {
  /** Score de diversité (0-1) */
  diversityScore: number;
  
  /** Score d'équité (0-1) */
  fairnessScore: number;
  
  /** Score de couverture du catalogue (0-1) */
  catalogCoverage: number;
  
  /** Entropie des recommandations */
  entropy?: number;
  
  /** Indice de Gini */
  giniIndex?: number;
  
  /** Score de surprise */
  surpriseScore?: number;
  
  /** Score de nouveauté */
  noveltyScore?: number;
  
  /** Biais détectés */
  detectedBiases?: Array<{
    type: string;
    description: string;
    severity: number;
  }>;
}

/**
 * Interface pour les métriques de performance
 */
export interface PerformanceMetrics {
  /** Temps de réponse moyen (ms) */
  averageResponseTime: number;
  
  /** Temps de réponse au 95e percentile (ms) */
  p95ResponseTime: number;
  
  /** Temps de réponse au 99e percentile (ms) */
  p99ResponseTime: number;
  
  /** Utilisation CPU (%) */
  cpuUsage: number;
  
  /** Utilisation mémoire (MB) */
  memoryUsage: number;
  
  /** Nombre de requêtes par seconde */
  requestsPerSecond: number;
  
  /** Taux d'erreur (%) */
  errorRate: number;
  
  /** Taux d'utilisation du cache (%) */
  cacheHitRate?: number;
  
  /** Temps moyen de génération des recommandations (ms) */
  averageGenerationTime: number;
}

/**
 * Interface pour les résultats d'évaluation comparative
 */
export interface ComparativeEvaluation {
  /** ID de l'évaluation */
  id?: string;
  
  /** Timestamp de l'évaluation */
  timestamp: Date;
  
  /** Période d'évaluation */
  period: {
    start: Date;
    end: Date;
  };
  
  /** Stratégies comparées */
  strategies: Array<{
    strategy: RecommendationStrategy;
    metrics: EvaluationMetrics;
  }>;
  
  /** Stratégie gagnante */
  winningStrategy?: {
    strategy: RecommendationStrategy;
    improvements: {
      accuracy: number;
      engagement: number;
      business: number;
      diversity: number;
      performance: number;
      overall: number;
    };
    confidenceLevel: number;
  };
  
  /** Recommandations basées sur l'évaluation */
  recommendations: string[];
}

/**
 * Interface pour les paramètres d'évaluation
 */
export interface EvaluationParameters {
  /** Période d'évaluation */
  period?: {
    start: Date;
    end: Date;
  };
  
  /** Type de recommandation à évaluer */
  recommendationType?: RecommendationType;
  
  /** Stratégie de recommandation à évaluer */
  recommendationStrategy?: RecommendationStrategy;
  
  /** Comparer avec d'autres stratégies */
  compareWithStrategies?: RecommendationStrategy[];
  
  /** Taille de l'échantillon pour l'évaluation */
  sampleSize?: number;
  
  /** Métriques à inclure */
  includeMetrics?: {
    accuracy?: boolean;
    engagement?: boolean;
    business?: boolean;
    diversity?: boolean;
    performance?: boolean;
    custom?: boolean;
  };
  
  /** Seuils d'alerte */
  alertThresholds?: {
    precision?: number;
    recall?: number;
    f1Score?: number;
    clickThroughRate?: number;
    conversionRate?: number;
    diversityScore?: number;
    fairnessScore?: number;
    responseTime?: number;
    errorRate?: number;
  };
}
