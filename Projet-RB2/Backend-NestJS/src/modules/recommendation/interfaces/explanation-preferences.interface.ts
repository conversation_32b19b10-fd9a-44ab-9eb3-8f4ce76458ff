/**
 * Interface pour les préférences d'explication d'un utilisateur
 */
export interface ExplanationPreferences {
  /** ID de l'utilisateur */
  userId: string;
  
  /** Style d'explication préféré */
  preferredStyle: ExplanationStyle;
  
  /** Niveau de détail préféré */
  detailLevel: DetailLevel;
  
  /** Format d'explication préféré */
  preferredFormat: ExplanationFormat[];
  
  /** Facteurs à mettre en évidence */
  highlightedFactors: string[];
  
  /** Facteurs à masquer */
  hiddenFactors: string[];
  
  /** Langue préférée pour les explications */
  language: string;
  
  /** Préférences culturelles */
  culturalPreferences: CulturalPreferences;
  
  /** Préférences d'accessibilité */
  accessibilityPreferences: AccessibilityPreferences;
  
  /** Historique des interactions avec les explications */
  interactionHistory?: ExplanationInteraction[];
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Style d'explication
 */
export enum ExplanationStyle {
  /** Style technique avec des détails précis */
  TECHNICAL = 'TECHNICAL',
  
  /** Style conversationnel et amical */
  CONVERSATIONAL = 'CONVERSATIONAL',
  
  /** Style concis et direct */
  CONCISE = 'CONCISE',
  
  /** Style narratif et descriptif */
  NARRATIVE = 'NARRATIVE',
  
  /** Style éducatif avec des informations supplémentaires */
  EDUCATIONAL = 'EDUCATIONAL',
}

/**
 * Niveau de détail pour les explications
 */
export enum DetailLevel {
  /** Très peu de détails, juste l'essentiel */
  MINIMAL = 'MINIMAL',
  
  /** Niveau de détail modéré */
  MODERATE = 'MODERATE',
  
  /** Niveau de détail élevé */
  DETAILED = 'DETAILED',
  
  /** Niveau de détail très élevé avec des informations supplémentaires */
  COMPREHENSIVE = 'COMPREHENSIVE',
}

/**
 * Format d'explication
 */
export enum ExplanationFormat {
  /** Texte simple */
  TEXT = 'TEXT',
  
  /** Représentation visuelle (graphiques, icônes) */
  VISUAL = 'VISUAL',
  
  /** Combinaison de texte et d'éléments visuels */
  MIXED = 'MIXED',
  
  /** Format interactif permettant d'explorer les détails */
  INTERACTIVE = 'INTERACTIVE',
}

/**
 * Préférences culturelles pour les explications
 */
export interface CulturalPreferences {
  /** Région culturelle (ex: 'western', 'eastern', 'african', etc.) */
  region?: string;
  
  /** Préférences de style de communication */
  communicationStyle?: 'direct' | 'indirect' | 'contextual';
  
  /** Préférences pour les exemples et métaphores */
  examplePreferences?: string[];
  
  /** Sensibilité aux références culturelles */
  culturalSensitivity?: 'low' | 'medium' | 'high';
}

/**
 * Préférences d'accessibilité pour les explications
 */
export interface AccessibilityPreferences {
  /** Préférence pour un texte à contraste élevé */
  highContrast?: boolean;
  
  /** Préférence pour un texte plus grand */
  largeText?: boolean;
  
  /** Préférence pour des descriptions alternatives des éléments visuels */
  screenReaderOptimized?: boolean;
  
  /** Préférence pour éviter les animations */
  reduceMotion?: boolean;
  
  /** Préférence pour un langage simplifié */
  simplifiedLanguage?: boolean;
}

/**
 * Interaction avec une explication
 */
export interface ExplanationInteraction {
  /** ID de l'explication */
  explanationId: string;
  
  /** Type d'interaction */
  interactionType: ExplanationInteractionType;
  
  /** Timestamp de l'interaction */
  timestamp: Date;
  
  /** Durée de l'interaction (en secondes) */
  duration?: number;
  
  /** Feedback explicite (note de 1 à 5) */
  rating?: number;
  
  /** Commentaire de l'utilisateur */
  comment?: string;
  
  /** Facteurs consultés */
  factorsViewed?: string[];
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Type d'interaction avec une explication
 */
export enum ExplanationInteractionType {
  /** Vue de l'explication */
  VIEW = 'VIEW',
  
  /** Expansion de l'explication pour voir plus de détails */
  EXPAND = 'EXPAND',
  
  /** Clic sur un élément de l'explication */
  CLICK = 'CLICK',
  
  /** Fermeture ou rejet de l'explication */
  DISMISS = 'DISMISS',
  
  /** Partage de l'explication */
  SHARE = 'SHARE',
  
  /** Feedback explicite sur l'explication */
  FEEDBACK = 'FEEDBACK',
  
  /** Modification des préférences d'explication */
  PREFERENCE_CHANGE = 'PREFERENCE_CHANGE',
}
