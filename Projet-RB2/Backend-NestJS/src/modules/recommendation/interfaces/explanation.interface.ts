import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * Interface pour un facteur d'explication
 */
export interface ExplanationFactor {
  /** Type de facteur */
  type: 'category' | 'tag' | 'similarity' | 'popularity' | 'context' | 'interaction' | 'other';
  
  /** Nom du facteur */
  name: string;
  
  /** Description du facteur */
  description: string;
  
  /** Poids du facteur dans la recommandation (0-1) */
  weight: number;
  
  /** Données supplémentaires spécifiques au facteur */
  data?: Record<string, any>;
}

/**
 * Interface pour une explication de recommandation
 */
export interface RecommendationExplanation {
  /** ID de la recommandation */
  recommendationId: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** ID de l'élément recommandé */
  itemId: string;
  
  /** Type d'élément recommandé */
  itemType: RecommendationType;
  
  /** Stratégie de recommandation utilisée */
  strategy: RecommendationStrategy;
  
  /** Score de la recommandation */
  score: number;
  
  /** Facteurs qui ont influencé la recommandation */
  factors: ExplanationFactor[];
  
  /** Résumé textuel de l'explication */
  summary: string;
  
  /** Timestamp de la génération de l'explication */
  timestamp: Date;
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Interface pour les options de génération d'explications
 */
export interface ExplanationOptions {
  /** Nombre maximum de facteurs à inclure */
  maxFactors?: number;
  
  /** Seuil minimum pour inclure un facteur */
  factorThreshold?: number;
  
  /** Inclure les facteurs négatifs */
  includeNegativeFactors?: boolean;
  
  /** Niveau de détail de l'explication */
  detailLevel?: 'basic' | 'detailed' | 'technical';
  
  /** Langue de l'explication */
  language?: string;
  
  /** Format de l'explication */
  format?: 'text' | 'html' | 'json';
}

/**
 * Interface pour un modèle d'explication
 */
export interface ExplanationTemplate {
  /** ID du modèle */
  id: string;
  
  /** Nom du modèle */
  name: string;
  
  /** Description du modèle */
  description: string;
  
  /** Type de facteur concerné */
  factorType: string;
  
  /** Modèle de texte pour l'explication */
  template: string;
  
  /** Variables disponibles dans le modèle */
  variables: string[];
  
  /** Langue du modèle */
  language: string;
}

/**
 * Interface pour les statistiques d'explications
 */
export interface ExplanationStats {
  /** Nombre total d'explications générées */
  totalExplanations: number;
  
  /** Nombre d'explications par stratégie */
  byStrategy: Record<RecommendationStrategy, number>;
  
  /** Nombre d'explications par type d'élément */
  byItemType: Record<RecommendationType, number>;
  
  /** Facteurs les plus fréquents */
  topFactors: {
    type: string;
    name: string;
    count: number;
    averageWeight: number;
  }[];
  
  /** Taux d'interaction avec les explications */
  interactionRate: number;
  
  /** Taux de conversion après explication */
  conversionRate: number;
}

/**
 * Interface pour un événement d'explication
 */
export interface ExplanationEvent {
  /** ID de l'événement */
  id: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** ID de la recommandation */
  recommendationId: string;
  
  /** Type d'événement */
  eventType: 'view' | 'expand' | 'click' | 'dismiss' | 'feedback';
  
  /** Données de l'événement */
  data?: Record<string, any>;
  
  /** Timestamp de l'événement */
  timestamp: Date;
}
