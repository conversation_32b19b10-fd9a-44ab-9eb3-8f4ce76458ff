import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Types de données externes
 */
export enum ExternalDataType {
  /** Tendances */
  TREND = 'TREND',
  
  /** Actualités */
  NEWS = 'NEWS',
  
  /** Météo */
  WEATHER = 'WEATHER',
  
  /** Événements */
  EVENT = 'EVENT',
  
  /** Transport */
  TRANSPORT = 'TRANSPORT',
  
  /** Santé */
  HEALTH = 'HEALTH',
  
  /** Environnement */
  ENVIRONMENT = 'ENVIRONMENT',
}

/**
 * Interface pour les données externes
 */
export interface ExternalData {
  /** Identifiant unique */
  id?: string;
  
  /** Type de données */
  type: ExternalDataType;
  
  /** Source des données */
  source: string;
  
  /** Titre */
  title: string;
  
  /** Contenu */
  content?: string;
  
  /** URL */
  url?: string;
  
  /** URL de l'image */
  imageUrl?: string;
  
  /** Score de pertinence (0-1) */
  relevanceScore?: number;
  
  /** Métadonnées */
  metadata?: Record<string, any>;
  
  /** Date de création */
  createdAt?: Date;
  
  /** Date d'expiration */
  expiresAt?: Date;
  
  /** Types de recommandation applicables */
  applicableTypes?: RecommendationType[];
}

/**
 * Interface pour une source de données externe
 */
export interface ExternalDataSource {
  /** Nom de la source */
  name: string;
  
  /** Activée ou non */
  enabled: boolean;
  
  /** URL de l'API */
  url: string;
  
  /** Nom de la clé API */
  apiKeyName?: string;
  
  /** Type de données */
  dataType: ExternalDataType;
  
  /** Paramètres spécifiques */
  params?: Record<string, any>;
  
  /** Intervalle de rafraîchissement (en heures) */
  refreshInterval?: number;
  
  /** Fonction de transformation des données */
  transform?: (data: any) => ExternalData[];
}

/**
 * Interface pour les options de récupération de données externes
 */
export interface ExternalDataOptions {
  /** Utilisateur spécifique */
  userId?: string;
  
  /** Type de recommandation */
  recommendationType?: RecommendationType;
  
  /** Localisation */
  location?: {
    latitude?: number;
    longitude?: number;
    city?: string;
    country?: string;
  };
  
  /** Date */
  date?: Date;
  
  /** Nombre maximum de résultats */
  limit?: number;
  
  /** Types de données à inclure */
  includeTypes?: ExternalDataType[];
  
  /** Types de données à exclure */
  excludeTypes?: ExternalDataType[];
  
  /** Sources à inclure */
  includeSources?: string[];
  
  /** Sources à exclure */
  excludeSources?: string[];
  
  /** Score de pertinence minimum */
  minRelevanceScore?: number;
  
  /** Mots-clés */
  keywords?: string[];
}

/**
 * Interface pour les résultats d'enrichissement
 */
export interface EnrichmentResult {
  /** Identifiant de l'élément enrichi */
  itemId: string;
  
  /** Type d'élément */
  itemType: string;
  
  /** Données externes associées */
  externalData: ExternalData[];
  
  /** Score d'enrichissement global */
  enrichmentScore: number;
  
  /** Métadonnées d'enrichissement */
  metadata?: Record<string, any>;
}

/**
 * Interface pour les options d'enrichissement
 */
export interface EnrichmentOptions {
  /** Types de données à utiliser */
  dataTypes?: ExternalDataType[];
  
  /** Score de pertinence minimum */
  minRelevanceScore?: number;
  
  /** Nombre maximum de données par type */
  maxItemsPerType?: number;
  
  /** Stratégie de fusion */
  mergeStrategy?: 'append' | 'replace' | 'merge';
  
  /** Champs à enrichir */
  targetFields?: string[];
}
