/**
 * Interfaces for the performance optimization system
 */

/**
 * Cache configuration
 */
export interface CacheConfig {
  /**
   * Enable/disable caching
   */
  enabled: boolean;

  /**
   * Cache strategy
   */
  strategy: CacheStrategy;

  /**
   * Time-to-live for cached items (in seconds)
   */
  ttl: number;

  /**
   * Maximum number of items to cache
   */
  maxItems: number;

  /**
   * Cache invalidation strategy
   */
  invalidationStrategy: CacheInvalidationStrategy;

  /**
   * Cache key generation strategy
   */
  keyStrategy: CacheKeyStrategy;

  /**
   * Additional cache parameters
   */
  params?: Record<string, any>;
}

/**
 * Cache strategy
 */
export enum CacheStrategy {
  /**
   * Cache recommendations for each user
   */
  PER_USER = 'PER_USER',

  /**
   * Cache recommendations for user segments
   */
  PER_SEGMENT = 'PER_SEGMENT',

  /**
   * Cache recommendations for specific contexts
   */
  CONTEXTUAL = 'CONTEXTUAL',

  /**
   * Cache individual components of recommendations
   */
  COMPONENT_BASED = 'COMPONENT_BASED',

  /**
   * Cache based on request parameters
   */
  REQUEST_BASED = 'REQUEST_BASED',
}

/**
 * Cache invalidation strategy
 */
export enum CacheInvalidationStrategy {
  /**
   * Invalidate cache after a fixed time
   */
  TIME_BASED = 'TIME_BASED',

  /**
   * Invalidate cache when user data changes
   */
  USER_CHANGE = 'USER_CHANGE',

  /**
   * Invalidate cache when item data changes
   */
  ITEM_CHANGE = 'ITEM_CHANGE',

  /**
   * Invalidate cache when model changes
   */
  MODEL_CHANGE = 'MODEL_CHANGE',

  /**
   * Invalidate cache based on user interactions
   */
  INTERACTION_BASED = 'INTERACTION_BASED',

  /**
   * Combination of multiple strategies
   */
  HYBRID = 'HYBRID',
}

/**
 * Cache key strategy
 */
export enum CacheKeyStrategy {
  /**
   * Simple key based on user ID
   */
  USER_ID = 'USER_ID',

  /**
   * Key based on user ID and request parameters
   */
  REQUEST_PARAMS = 'REQUEST_PARAMS',

  /**
   * Key based on user segment
   */
  USER_SEGMENT = 'USER_SEGMENT',

  /**
   * Key based on context
   */
  CONTEXT = 'CONTEXT',

  /**
   * Custom key generation
   */
  CUSTOM = 'CUSTOM',
}

/**
 * Database optimization configuration
 */
export interface DatabaseOptimizationConfig {
  /**
   * Enable/disable database optimization
   */
  enabled: boolean;

  /**
   * Query optimization strategy
   */
  queryStrategy: QueryOptimizationStrategy;

  /**
   * Index optimization strategy
   */
  indexStrategy: IndexOptimizationStrategy;

  /**
   * Data partitioning strategy
   */
  partitioningStrategy: DataPartitioningStrategy;

  /**
   * Batch processing configuration
   */
  batchProcessing: BatchProcessingConfig;

  /**
   * Connection pool configuration
   */
  connectionPool: ConnectionPoolConfig;

  /**
   * Additional database optimization parameters
   */
  params?: Record<string, any>;
}

/**
 * Query optimization strategy
 */
export enum QueryOptimizationStrategy {
  /**
   * Optimize queries for specific use cases
   */
  USE_CASE_SPECIFIC = 'USE_CASE_SPECIFIC',

  /**
   * Use query hints
   */
  QUERY_HINTS = 'QUERY_HINTS',

  /**
   * Use materialized views
   */
  MATERIALIZED_VIEWS = 'MATERIALIZED_VIEWS',

  /**
   * Use stored procedures
   */
  STORED_PROCEDURES = 'STORED_PROCEDURES',

  /**
   * Use query caching
   */
  QUERY_CACHING = 'QUERY_CACHING',
}

/**
 * Index optimization strategy
 */
export enum IndexOptimizationStrategy {
  /**
   * Create indexes for common queries
   */
  COMMON_QUERIES = 'COMMON_QUERIES',

  /**
   * Create indexes for specific use cases
   */
  USE_CASE_SPECIFIC = 'USE_CASE_SPECIFIC',

  /**
   * Create composite indexes
   */
  COMPOSITE = 'COMPOSITE',

  /**
   * Create partial indexes
   */
  PARTIAL = 'PARTIAL',

  /**
   * Create covering indexes
   */
  COVERING = 'COVERING',
}

/**
 * Data partitioning strategy
 */
export enum DataPartitioningStrategy {
  /**
   * No partitioning
   */
  NONE = 'NONE',

  /**
   * Partition by user
   */
  BY_USER = 'BY_USER',

  /**
   * Partition by item
   */
  BY_ITEM = 'BY_ITEM',

  /**
   * Partition by time
   */
  BY_TIME = 'BY_TIME',

  /**
   * Partition by region
   */
  BY_REGION = 'BY_REGION',

  /**
   * Custom partitioning
   */
  CUSTOM = 'CUSTOM',
}

/**
 * Batch processing configuration
 */
export interface BatchProcessingConfig {
  /**
   * Enable/disable batch processing
   */
  enabled: boolean;

  /**
   * Batch size
   */
  batchSize: number;

  /**
   * Concurrency level
   */
  concurrency: number;

  /**
   * Retry configuration
   */
  retry: {
    /**
     * Maximum number of retries
     */
    maxRetries: number;

    /**
     * Delay between retries (in milliseconds)
     */
    delay: number;

    /**
     * Backoff factor for retry delay
     */
    backoffFactor: number;
  };
}

/**
 * Connection pool configuration
 */
export interface ConnectionPoolConfig {
  /**
   * Minimum number of connections
   */
  min: number;

  /**
   * Maximum number of connections
   */
  max: number;

  /**
   * Connection idle timeout (in milliseconds)
   */
  idleTimeout: number;

  /**
   * Connection acquisition timeout (in milliseconds)
   */
  acquireTimeout: number;

  /**
   * Connection creation timeout (in milliseconds)
   */
  createTimeout: number;
}
