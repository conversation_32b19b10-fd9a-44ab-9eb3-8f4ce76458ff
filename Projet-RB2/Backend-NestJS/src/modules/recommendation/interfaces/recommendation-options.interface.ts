import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { FactorizationMethod } from '../enums/factorization-method.enum';
import { ExternalDataType } from './external-data.interface';

/**
 * Options pour les requêtes de recommandation
 */
export interface RecommendationOptions {
  /** Nombre maximum de recommandations à retourner */
  limit?: number;

  /** Stratégie de recommandation à utiliser */
  strategy?: RecommendationStrategy;

  /** Méthode hybride à utiliser (uniquement si strategy=HYBRID) */
  hybridMethod?: HybridMethod;

  /** Méthode de factorisation à utiliser (uniquement si strategy=MATRIX_FACTORIZATION) */
  factorizationMethod?: FactorizationMethod;

  /** Poids pour les recommandations basées sur le contenu (méthode hybride pondérée) */
  contentBasedWeight?: number;

  /** Poids pour les recommandations collaboratives (méthode hybride pondérée) */
  collaborativeWeight?: number;

  /** Seuil d'interactions pour la méthode hybride de commutation */
  interactionThreshold?: number;

  /** Inclure les métadonnées détaillées dans les résultats */
  includeMetadata?: boolean;

  /** Inclure les données externes dans les résultats */
  includeExternalData?: boolean;

  /** Enrichir les recommandations avec des données externes */
  enrichWithExternalData?: boolean;

  /** Types de données externes à inclure dans l'enrichissement */
  externalDataTypes?: ExternalDataType[];

  /** Score de pertinence minimum pour les données externes */
  minExternalDataRelevanceScore?: number;

  /** Nombre maximum d'éléments par type de données externes */
  maxExternalDataItemsPerType?: number;

  /** Inclure les explications dans les résultats */
  includeExplanations?: boolean;

  /** IDs des éléments à exclure des recommandations */
  excludeIds?: string[];

  /** Filtres à appliquer aux recommandations */
  filters?: Record<string, any>;

  /** Curseur pour la pagination */
  cursor?: string;

  /** Numéro de page pour la pagination */
  page?: number;

  /** Champ de tri */
  sortBy?: string;

  /** Ordre de tri */
  sortOrder?: 'asc' | 'desc';

  /** Nombre de facteurs latents pour les modèles de factorisation matricielle */
  numFactors?: number;

  /** Paramètre de régularisation pour les modèles de factorisation matricielle */
  regularization?: number;

  /** Nombre d'itérations pour les modèles de factorisation matricielle */
  numIterations?: number;

  /** Facteur d'apprentissage pour les modèles de factorisation matricielle */
  learningRate?: number;

  /** Informations contextuelles pour les recommandations contextuelles */
  context?: {
    /** Localisation de l'utilisateur */
    location?: {
      latitude?: number;
      longitude?: number;
      country?: string;
      city?: string;
    };
    /** Saison actuelle */
    season?: 'SPRING' | 'SUMMER' | 'FALL' | 'WINTER';
    /** Jour de la semaine */
    dayOfWeek?: number;
    /** Heure de la journée */
    timeOfDay?: number;
    /** Appareil utilisé */
    device?: 'MOBILE' | 'TABLET' | 'DESKTOP';
    /** Autres informations contextuelles */
    [key: string]: any;
  };

  /** Paramètres pour la diversification des recommandations */
  diversification?: {
    /** Activer la diversification */
    enabled?: boolean;
    /** Poids de la diversification (entre 0 et 1) */
    weight?: number;
    /** Méthode de diversification */
    method?: 'MMR' | 'DETERMINANTAL' | 'CLUSTERING';
  };
}
