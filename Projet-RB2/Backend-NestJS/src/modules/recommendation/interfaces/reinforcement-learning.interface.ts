/**
 * Interface pour l'agent d'apprentissage par renforcement
 */
export interface ReinforcementLearningAgent {
  /** ID de l'agent */
  id: string;
  
  /** Nom de l'agent */
  name: string;
  
  /** Description de l'agent */
  description: string;
  
  /** Type d'agent */
  agentType: RLAgentType;
  
  /** État actuel de l'agent */
  state: RLAgentState;
  
  /** Configuration de l'agent */
  config: RLAgentConfig;
  
  /** Statistiques de performance */
  stats: RLAgentStats;
  
  /** Date de création */
  createdAt: Date;
  
  /** Date de dernière mise à jour */
  updatedAt: Date;
}

/**
 * Types d'agents d'apprentissage par renforcement
 */
export enum RLAgentType {
  /** Agent Q-Learning */
  Q_LEARNING = 'Q_LEARNING',
  
  /** Agent SARSA (State-Action-Reward-State-Action) */
  SARSA = 'SARSA',
  
  /** Agent DQN (Deep Q-Network) */
  DQN = 'DQN',
  
  /** Agent A2C (Advantage Actor-Critic) */
  A2C = 'A2C',
  
  /** Agent personnalisé */
  CUSTOM = 'CUSTOM',
}

/**
 * États possibles d'un agent d'apprentissage par renforcement
 */
export enum RLAgentState {
  /** Agent en cours d'initialisation */
  INITIALIZING = 'INITIALIZING',
  
  /** Agent prêt mais inactif */
  IDLE = 'IDLE',
  
  /** Agent en cours d'apprentissage */
  LEARNING = 'LEARNING',
  
  /** Agent en cours d'exploitation (utilisation des connaissances acquises) */
  EXPLOITING = 'EXPLOITING',
  
  /** Agent en pause */
  PAUSED = 'PAUSED',
  
  /** Agent arrêté */
  STOPPED = 'STOPPED',
  
  /** Agent en erreur */
  ERROR = 'ERROR',
}

/**
 * Configuration d'un agent d'apprentissage par renforcement
 */
export interface RLAgentConfig {
  /** Taux d'apprentissage (alpha) */
  learningRate: number;
  
  /** Facteur d'actualisation (gamma) */
  discountFactor: number;
  
  /** Taux d'exploration (epsilon) */
  explorationRate: number;
  
  /** Stratégie d'exploration */
  explorationStrategy: ExplorationStrategy;
  
  /** Taille de la mémoire d'expérience */
  experienceMemorySize: number;
  
  /** Taille du lot d'apprentissage */
  batchSize: number;
  
  /** Fréquence de mise à jour du réseau cible (pour DQN) */
  targetNetworkUpdateFrequency?: number;
  
  /** Paramètres spécifiques à l'agent */
  agentSpecificParams?: Record<string, any>;
}

/**
 * Stratégies d'exploration
 */
export enum ExplorationStrategy {
  /** Epsilon-greedy */
  EPSILON_GREEDY = 'EPSILON_GREEDY',
  
  /** Softmax */
  SOFTMAX = 'SOFTMAX',
  
  /** UCB (Upper Confidence Bound) */
  UCB = 'UCB',
  
  /** Thompson Sampling */
  THOMPSON_SAMPLING = 'THOMPSON_SAMPLING',
}

/**
 * Statistiques de performance d'un agent d'apprentissage par renforcement
 */
export interface RLAgentStats {
  /** Nombre total d'épisodes */
  totalEpisodes: number;
  
  /** Nombre total d'étapes */
  totalSteps: number;
  
  /** Récompense cumulée */
  cumulativeReward: number;
  
  /** Récompense moyenne par épisode */
  averageRewardPerEpisode: number;
  
  /** Taux de convergence */
  convergenceRate: number;
  
  /** Taux d'exploration actuel */
  currentExplorationRate: number;
  
  /** Historique des récompenses */
  rewardHistory: number[];
  
  /** Historique des taux d'erreur */
  errorHistory: number[];
  
  /** Métriques spécifiques à l'agent */
  agentSpecificMetrics?: Record<string, any>;
}

/**
 * Interface pour un état dans l'apprentissage par renforcement
 */
export interface RLState {
  /** Identifiant de l'état */
  id: string;
  
  /** Caractéristiques de l'état */
  features: Record<string, number>;
  
  /** Contexte de l'état */
  context?: Record<string, any>;
}

/**
 * Interface pour une action dans l'apprentissage par renforcement
 */
export interface RLAction {
  /** Identifiant de l'action */
  id: string;
  
  /** Type d'action */
  actionType: string;
  
  /** Paramètres de l'action */
  parameters: Record<string, any>;
}

/**
 * Interface pour une récompense dans l'apprentissage par renforcement
 */
export interface RLReward {
  /** Valeur de la récompense */
  value: number;
  
  /** Composantes de la récompense */
  components: RewardComponent[];
  
  /** Métadonnées de la récompense */
  metadata?: Record<string, any>;
}

/**
 * Composante d'une récompense
 */
export interface RewardComponent {
  /** Nom de la composante */
  name: string;
  
  /** Valeur de la composante */
  value: number;
  
  /** Poids de la composante */
  weight: number;
}

/**
 * Interface pour une expérience dans l'apprentissage par renforcement
 */
export interface RLExperience {
  /** État actuel */
  state: RLState;
  
  /** Action choisie */
  action: RLAction;
  
  /** Récompense obtenue */
  reward: RLReward;
  
  /** État suivant */
  nextState: RLState;
  
  /** Indique si l'épisode est terminé */
  done: boolean;
  
  /** Timestamp de l'expérience */
  timestamp: Date;
}

/**
 * Interface pour un épisode dans l'apprentissage par renforcement
 */
export interface RLEpisode {
  /** ID de l'épisode */
  id: string;
  
  /** ID de l'agent */
  agentId: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** Séquence d'expériences */
  experiences: RLExperience[];
  
  /** Récompense totale */
  totalReward: number;
  
  /** Nombre d'étapes */
  steps: number;
  
  /** Date de début */
  startTime: Date;
  
  /** Date de fin */
  endTime: Date;
  
  /** Métadonnées de l'épisode */
  metadata?: Record<string, any>;
}

/**
 * Interface pour la fonction de récompense
 */
export interface RewardFunction {
  /** Calcule la récompense pour une interaction */
  calculateReward(
    interaction: ExplanationInteraction,
    context: RewardContext,
  ): RLReward;
  
  /** Obtient les composantes de la récompense */
  getRewardComponents(): RewardComponent[];
  
  /** Met à jour les poids des composantes */
  updateComponentWeights(weights: Record<string, number>): void;
}

/**
 * Interface pour le contexte de la récompense
 */
export interface RewardContext {
  /** Utilisateur */
  user: {
    id: string;
    preferences: Record<string, any>;
  };
  
  /** Explication */
  explanation: {
    id: string;
    factors: string[];
    template: string;
  };
  
  /** Recommandation */
  recommendation: {
    id: string;
    itemType: string;
    itemId: string;
  };
  
  /** Historique des interactions */
  interactionHistory?: ExplanationInteraction[];
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Interface pour une interaction avec une explication
 */
export interface ExplanationInteraction {
  /** ID de l'interaction */
  id: string;
  
  /** ID de l'utilisateur */
  userId: string;
  
  /** ID de l'explication */
  explanationId: string;
  
  /** Type d'interaction */
  interactionType: string;
  
  /** Données de l'interaction */
  data: Record<string, any>;
  
  /** Durée de l'interaction (en secondes) */
  duration?: number;
  
  /** Date de l'interaction */
  timestamp: Date;
}
