/**
 * Social graph strategy
 */
export enum SocialGraphStrategy {
  EXPLICIT_CONNECTIONS = 'explicit_connections',
  INTERACTION_BASED = 'interaction_based',
  SIMILARITY_BASED = 'similarity_based',
  COMMUNITY_DETECTION = 'community_detection',
}

/**
 * Social influence strategy
 */
export enum SocialInfluenceStrategy {
  WEIGHTED_AVERAGE = 'weighted_average',
  TRUST_BASED = 'trust_based',
  EXPERTISE_BASED = 'expertise_based',
  RECENCY_BASED = 'recency_based',
}

/**
 * Group recommendation strategy
 */
export enum GroupRecommendationStrategy {
  AVERAGE_SATISFACTION = 'average_satisfaction',
  LEAST_MISERY = 'least_misery',
  MAXIMUM_SATISFACTION = 'maximum_satisfaction',
  FAIRNESS = 'fairness',
  PLURALITY_VOTING = 'plurality_voting',
}

/**
 * Social relationship type
 */
export enum SocialRelationshipType {
  FRIEND = 'friend',
  FAMILY = 'family',
  COLLEAGUE = 'colleague',
  ACQUAINTANCE = 'acquaintance',
  FOLLOWER = 'follower',
}

/**
 * Social graph configuration
 */
export interface SocialGraphConfig {
  enabled: boolean;
  strategies: SocialGraphStrategy[];
  relationshipTypes: SocialRelationshipType[];
  maxConnectionDepth: number;
  minRelationshipStrength: number;
  refreshInterval: number; // hours
}

/**
 * Social influence configuration
 */
export interface SocialInfluenceConfig {
  enabled: boolean;
  strategies: SocialInfluenceStrategy[];
  maxInfluenceWeight: number;
  minInfluenceThreshold: number;
  decayFactor: number;
}

/**
 * Group recommendation configuration
 */
export interface GroupRecommendationConfig {
  enabled: boolean;
  strategies: GroupRecommendationStrategy[];
  defaultStrategy: GroupRecommendationStrategy;
  maxGroupSize: number;
  minSatisfactionThreshold: number;
}

/**
 * Social recommendation configuration
 */
export interface SocialRecommendationConfig {
  enabled: boolean;
  socialGraph: SocialGraphConfig;
  socialInfluence: SocialInfluenceConfig;
  groupRecommendation: GroupRecommendationConfig;
}

/**
 * Social relationship
 */
export interface SocialRelationship {
  userId: string;
  type: SocialRelationshipType;
  strength: number;
  established: Date;
  lastInteraction: Date;
  interactionCount: number;
}

/**
 * Social user
 */
export interface SocialUser {
  id: string;
  name: string;
  interests: string[];
  relationships: SocialRelationship[];
  influenceScore: number;
  expertiseAreas: string[];
  lastActive: Date;
}

/**
 * Social group
 */
export interface SocialGroup {
  id: string;
  name: string;
  description: string;
  memberIds: string[];
  createdBy: string;
  createdAt: Date;
  lastActive: Date;
  commonInterests: string[];
}

/**
 * Social recommendation request
 */
export interface SocialRecommendationRequest {
  userId: string;
  limit?: number;
  maxConnectionDepth?: number;
  strategy?: SocialInfluenceStrategy;
  context?: Record<string, any>;
}

/**
 * Group recommendation request
 */
export interface GroupRecommendationRequest {
  groupId: string;
  memberIds: string[];
  limit?: number;
  strategy?: GroupRecommendationStrategy;
  context?: Record<string, any>;
}

/**
 * Social recommendation result
 */
export interface SocialRecommendationResult {
  items: any[];
  socialFactors: Record<string, any>;
  socialExplanation: string;
}

/**
 * Group recommendation result
 */
export interface GroupRecommendationResult {
  items: any[];
  groupId: string;
  memberIds: string[];
  strategy: GroupRecommendationStrategy;
  satisfactionMetrics: GroupSatisfactionMetrics;
  explanation: string;
}

/**
 * Group member preference
 */
export interface GroupMemberPreference {
  userId: string;
  itemPreferences: {
    itemId: string;
    score: number;
    item: any;
  }[];
}

/**
 * Group satisfaction metrics
 */
export interface GroupSatisfactionMetrics {
  average: number;
  minimum: number;
  maximum: number;
  variance: number;
}

/**
 * Social influence
 */
export interface SocialInfluence {
  userId: string;
  influencerId: string;
  weight: number;
  type: SocialInfluenceStrategy;
  timestamp: Date;
}

/**
 * Social recommendation explanation
 */
export interface SocialRecommendationExplanation {
  itemId: string;
  userId: string;
  influencers: {
    userId: string;
    weight: number;
    relationship: SocialRelationshipType;
  }[];
  explanation: string;
}

/**
 * Group recommendation explanation
 */
export interface GroupRecommendationExplanation {
  itemId: string;
  groupId: string;
  memberSatisfaction: Record<string, number>;
  strategy: GroupRecommendationStrategy;
  explanation: string;
}
