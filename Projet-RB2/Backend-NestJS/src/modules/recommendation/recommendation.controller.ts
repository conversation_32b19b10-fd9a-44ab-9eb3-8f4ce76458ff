import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RecommendationService } from './recommendation.service';
import { GetRecommendationsDto } from './dto/get-recommendations.dto';
import { RecordInteractionDto } from './dto/record-interaction.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';

@ApiTags('Recommendations')
@Controller('recommendations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RecommendationController {
  private readonly logger = new Logger(RecommendationController.name);

  constructor(private readonly recommendationService: RecommendationService) {}

  @Get()
  @ApiOperation({ summary: 'Obtenir des recommandations personnalisées' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recommandations récupérées avec succès',
    type: RecommendationResponseDto,
  })
  async getRecommendations(
    @GetUser('id') userId: string,
    @Query() dto: GetRecommendationsDto,
  ): Promise<RecommendationResponseDto> {
    this.logger.log(`Getting recommendations for user ${userId}`);
    return await this.recommendationService.getRecommendations(userId, dto);
  }

  @Post('interactions')
  @ApiOperation({ summary: 'Enregistrer une interaction avec une recommandation' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Interaction enregistrée avec succès',
  })
  async recordInteraction(
    @GetUser('id') userId: string,
    @Body() dto: RecordInteractionDto,
  ): Promise<{ message: string }> {
    this.logger.log(`Recording interaction for user ${userId}`);
    await this.recommendationService.recordInteraction(userId, dto);
    return { message: 'Interaction enregistrée avec succès' };
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Obtenir les métriques de performance' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Métriques récupérées avec succès',
  })
  async getMetrics(@GetUser('id') userId: string): Promise<any> {
    this.logger.log(`Getting metrics for user ${userId}`);
    return await this.recommendationService.getPerformanceMetrics(userId);
  }

  @Post('training/trigger')
  @ApiOperation({ summary: 'Déclencher l\'entraînement du modèle' })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Entraînement déclenché avec succès',
  })
  async triggerTraining(): Promise<{ message: string }> {
    this.logger.log('Triggering model training');
    await this.recommendationService.triggerModelTraining();
    return { message: 'Entraînement du modèle déclenché' };
  }
}
