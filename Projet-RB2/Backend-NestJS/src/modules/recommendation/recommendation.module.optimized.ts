import { <PERSON><PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { LearningModule } from '../learning/learning.module';
import { CacheModule } from '../../cache/cache.module';
import { MonitoringModule } from '../monitoring/monitoring.module';

// Configuration
import recommendationConfig from '../../config/recommendation.config';

// Controllers
import { RecommendationController } from './controllers/recommendation.controller.refactored';
import { SocialVideoRecommendationController } from './controllers/social-video-recommendation.controller';

// Services
import { RecommendationService } from './services/recommendation.service.optimized';
import { ContentBasedService } from './services/content-based.service';
import { CollaborativeFilteringService } from './services/collaborative-filtering.service';
import { HybridRecommendationService } from './services/hybrid-recommendation.service';
import { PersonalizationService } from './services/personalization.service';
import { SocialVideoRecommendationService } from './services/social-video-recommendation.service';
import { AgentRbIntegrationService } from './services/agent-rb-integration.service.optimized';
import { RecommendationCacheService } from './services/recommendation-cache.service';

// Clients
import { AgentRbClient } from './clients/agent-rb-client.optimized';

// Middleware
import { RecommendationMonitoringMiddleware } from './middleware/recommendation-monitoring.middleware';

/**
 * Module de recommandation optimisé
 */
@Module({
  imports: [
    PrismaModule,
    UsersModule,
    LearningModule,
    CacheModule,
    MonitoringModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule.forFeature(recommendationConfig),
  ],
  controllers: [
    RecommendationController,
    SocialVideoRecommendationController,
  ],
  providers: [
    RecommendationService,
    ContentBasedService,
    CollaborativeFilteringService,
    HybridRecommendationService,
    PersonalizationService,
    SocialVideoRecommendationService,
    AgentRbIntegrationService,
    RecommendationCacheService,
    AgentRbClient,
  ],
  exports: [
    RecommendationService,
    SocialVideoRecommendationService,
    AgentRbIntegrationService,
    RecommendationCacheService,
  ],
})
export class RecommendationModule {
  /**
   * Configure les middlewares
   * @param consumer Consommateur de middleware
   */
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RecommendationMonitoringMiddleware)
      .forRoutes(
        { path: 'api/v1/recommendations', method: RequestMethod.GET },
        { path: 'api/v1/recommendations/trending', method: RequestMethod.GET },
        { path: 'api/v1/recommendations/similar/*', method: RequestMethod.GET },
      );
  }
}
