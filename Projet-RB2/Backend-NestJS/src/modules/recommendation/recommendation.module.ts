import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from '../../prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { LearningModule } from '../learning/learning.module';
import { CacheModule } from '../../cache/cache.module';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Controllers
import { RecommendationController } from './controllers/recommendation.controller';
import { SocialVideoRecommendationController } from './controllers/social-video-recommendation.controller';
import { ABTestingController } from './controllers/ab-testing.controller';
import { RecommendationPreferencesController } from './controllers/recommendation-preferences.controller';
import { RealtimeInteractionController } from './controllers/realtime-interaction.controller';
import { CollaborativeSharingController } from './controllers/collaborative-sharing.controller';
import { AnalyticsController } from './controllers/analytics.controller';
import { VisualizationController } from './controllers/visualization.controller';
import { ReportGeneratorController } from './controllers/report-generator.controller';
import { ContinuousLearningController } from './controllers/continuous-learning.controller';
import { ExplanationController } from './controllers/explanation.controller';
import { ExplanationABTestingController } from './controllers/explanation-ab-testing.controller';
import { ExplanationLearningIntegrationController } from './controllers/explanation-learning-integration.controller';
import { ExplanationPreferencesController } from './controllers/explanation-preferences.controller';
import { ReinforcementLearningController } from './controllers/reinforcement-learning.controller';
import { ExplanationTranslationController } from './controllers/explanation-translation.controller';
import { ExplanationAnalyticsController } from './controllers/explanation-analytics.controller';
import { DiversityFairnessController } from './controllers/diversity-fairness.controller';
import { ContextualRecommendationController } from './controllers/contextual-recommendation.controller';
import { SocialGroupRecommendationController } from './controllers/social-group-recommendation.controller';
import { MultiCriteriaRecommendationController } from './controllers/multi-criteria-recommendation.controller';
import { ExternalDataController } from './controllers/external-data.controller';
import { RecommendationModerationController } from './controllers/recommendation-moderation.controller';
import { EnhancedExplanationController } from './controllers/enhanced-explanation.controller';
import { FeedbackController } from './controllers/feedback.controller';

// Services
import { RecommendationService } from './services/recommendation.service';
import { ContentBasedService } from './services/content-based.service';
import { CollaborativeFilteringService } from './services/collaborative-filtering.service';
import { HybridRecommendationService } from './services/hybrid-recommendation.service';
import { PersonalizationService } from './services/personalization.service';
import { SocialVideoRecommendationService } from './services/social-video-recommendation.service';
import { MatrixFactorizationService } from './services/matrix-factorization.service';
import { ContextualRecommendationService } from './services/contextual-recommendation.service';
import { DiversityFilterService } from './services/diversity-filter.service';
import { RecommendationCacheService } from './services/recommendation-cache.service';
import { RecommendationPreloaderService } from './services/recommendation-preloader.service';
import { ExplanationLearningIntegrationService } from './services/explanation-learning-integration.service';
import { QueryOptimizationService } from './services/query-optimization.service';
import { ExplanationPreferencesService } from './services/explanation-preferences.service';
import { ReinforcementLearningService } from './services/reinforcement-learning.service';
import { ExplanationTranslationService } from './services/explanation-translation.service';
import { ExplanationAnalyticsService } from './services/explanation-analytics.service';
import { DiversityFilterService } from './services/diversity-filter.service';
import { FairnessService } from './services/fairness.service';
import { ContextDetectionService } from './services/context-detection.service';
import { ContextualRecommendationService } from './services/contextual-recommendation.service';
import { SeasonalRecommendationService } from './services/seasonal-recommendation.service';
import { GroupRecommendationService } from './services/group-recommendation.service';
import { SocialRecommendationService } from './services/social-recommendation.service';
import { CollaborativePlanningService } from './services/collaborative-planning.service';
import { DeepLearningService } from './services/deep-learning.service';
import { ABTestingService } from './services/ab-testing.service';
import { ExternalDataService } from './services/external-data.service';
import { WeatherConnectorService } from './services/connectors/weather-connector.service';
import { EventsConnectorService } from './services/connectors/events-connector.service';
import { TransportConnectorService } from './services/connectors/transport-connector.service';
import { RealtimeRecommendationService } from './services/realtime-recommendation.service';
import { ExplanationService } from './services/explanation.service';
import { CollaborativeSharingService } from './services/collaborative-sharing.service';
import { AnalyticsService } from './services/analytics.service';
import { VisualizationService } from './services/visualization.service';
import { ReportGeneratorService } from './services/report-generator.service';
import { ContinuousLearningService } from './services/continuous-learning.service';
import { MultiCriteriaRecommendationService } from './services/multi-criteria-recommendation.service';

// Nouveaux services pour le Sprint 4
import { ModerationIntegrationService } from './services/moderation-integration.service';
import { EnhancedExplanationService } from './services/enhanced-explanation.service';
import { FeedbackService } from './services/feedback.service';
import { AnalyticsIntegrationService } from './services/analytics-integration.service';
import { NotificationIntegrationService } from './services/notification-integration.service';

// Nouveaux services pour le Sprint 5
import { DataPreprocessingService } from './services/data-preprocessing.service';
import { FeatureExtractionService } from './services/feature-extraction.service';
import { ModelTrainingService } from './services/model-training.service';
import { ContinuousLearningPipelineService } from './services/continuous-learning-pipeline.service';
import { HierarchicalPreferenceService } from './services/hierarchical-preference.service';
import { PreferenceInferenceService } from './services/preference-inference.service';
import { TemporalPreferenceService } from './services/temporal-preference.service';
import { PerformanceMonitoringService } from './services/performance-monitoring.service';
import { PerformanceDashboardController } from './controllers/performance-dashboard.controller';
import { DiversityFairnessService } from './services/diversity-fairness.service';
import { TransparencyService } from './services/transparency.service';
import { TransparencyController } from './controllers/transparency.controller';
import { PerformanceOptimizationService } from './services/performance-optimization.service';
import { ScalabilityService } from './services/scalability.service';
import { PerformanceOptimizationController } from './controllers/performance-optimization.controller';
import { ScalabilityController } from './controllers/scalability.controller';
import { AdvancedContextService } from './services/advanced-context.service';
import { AdvancedContextController } from './controllers/advanced-context.controller';
import { GenerativeAIService } from './services/generative-ai.service';
import { GenerativeAIController } from './controllers/generative-ai.controller';
import { ProactiveRecommendationService } from './services/proactive-recommendation.service';
import { ProactiveRecommendationController } from './controllers/proactive-recommendation.controller';
import { SocialRecommendationService } from './services/social-recommendation.service';
import { GroupRecommendationService } from './services/group-recommendation.service';
import { SocialGroupRecommendationController } from './controllers/social-group-recommendation.controller';
import { TestingDocumentationService } from './services/testing-documentation.service';
import { TestingDocumentationController } from './controllers/testing-documentation.controller';
import { ContinuousLearningPipelineController } from './controllers/continuous-learning-pipeline.controller';

/**
 * Module de recommandation amélioré
 * Intègre des algorithmes avancés et des optimisations de performance
 */
@Module({
  imports: [
    PrismaModule,
    UsersModule,
    LearningModule,
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
    CacheModule,
    ScheduleModule.forRoot(), // Nécessaire pour les tâches planifiées (Cron)
    EventEmitterModule.forRoot(), // Nécessaire pour les événements
  ],
  controllers: [
    RecommendationController,
    SocialVideoRecommendationController,
    ABTestingController,
    RecommendationPreferencesController,
    RealtimeInteractionController,
    CollaborativeSharingController,
    AnalyticsController,
    VisualizationController,
    ReportGeneratorController,
    ContinuousLearningController,
    ExplanationController,
    ExplanationABTestingController,
    ExplanationLearningIntegrationController,
    ExplanationPreferencesController,
    ReinforcementLearningController,
    ExplanationTranslationController,
    ExplanationAnalyticsController,
    DiversityFairnessController,
    ContextualRecommendationController,
    SocialGroupRecommendationController,
    MultiCriteriaRecommendationController,
    ExternalDataController,
    RecommendationModerationController,
    EnhancedExplanationController,
    FeedbackController,
    AnalyticsIntegrationController,
    ContinuousLearningPipelineController,
    PerformanceDashboardController,
    TransparencyController,
    PerformanceOptimizationController,
    ScalabilityController,
    AdvancedContextController,
    GenerativeAIController,
    ProactiveRecommendationController,
    SocialGroupRecommendationController,
    TestingDocumentationController
  ],
  providers: [
    // Services principaux
    RecommendationService,
    ContentBasedService,
    CollaborativeFilteringService,
    HybridRecommendationService,
    PersonalizationService,
    SocialVideoRecommendationService,

    // Nouveaux algorithmes
    MatrixFactorizationService,
    ContextualRecommendationService,
    DiversityFilterService,
    MultiCriteriaRecommendationService,

    // Services d'optimisation
    RecommendationCacheService,
    RecommendationPreloaderService,

    // Services d'apprentissage automatique
    DeepLearningService,
    ContinuousLearningService,
    ExplanationLearningIntegrationService,

    // Services d'optimisation
    QueryOptimizationService,

    // Services d'A/B testing
    ABTestingService,

    // Services d'intégration de données externes
    ExternalDataService,
    WeatherConnectorService,
    EventsConnectorService,
    TransportConnectorService,

    // Services de recommandations en temps réel
    RealtimeRecommendationService,

    // Services d'explications des recommandations
    ExplanationService,
    ExplanationPreferencesService,
    ReinforcementLearningService,
    ExplanationTranslationService,
    ExplanationAnalyticsService,
    DiversityFilterService,
    FairnessService,
    ContextDetectionService,
    ContextualRecommendationService,
    SeasonalRecommendationService,
    GroupRecommendationService,
    SocialRecommendationService,
    CollaborativePlanningService,

    // Services de partage collaboratif
    CollaborativeSharingService,

    // Services d'analyse
    AnalyticsService,

    // Services de visualisation
    VisualizationService,

    // Services de génération de rapports
    ReportGeneratorService,

    // Nouveaux services pour le Sprint 4
    ModerationIntegrationService,
    EnhancedExplanationService,
    FeedbackService,
    AnalyticsIntegrationService,
    NotificationIntegrationService,

    // Nouveaux services pour le Sprint 5
    DataPreprocessingService,
    FeatureExtractionService,
    ModelTrainingService,
    ContinuousLearningPipelineService,
    HierarchicalPreferenceService,
    PreferenceInferenceService,
    TemporalPreferenceService,
    PerformanceMonitoringService,
    DiversityFairnessService,
    TransparencyService,
    PerformanceOptimizationService,
    ScalabilityService,
    AdvancedContextService,
    GenerativeAIService,
    ProactiveRecommendationService,
    SocialRecommendationService,
    GroupRecommendationService,
    TestingDocumentationService,
  ],
  exports: [
    // Services principaux
    RecommendationService,
    SocialVideoRecommendationService,

    // Nouveaux algorithmes
    MatrixFactorizationService,
    ContextualRecommendationService,
    DiversityFilterService,
    MultiCriteriaRecommendationService,

    // Services d'optimisation
    RecommendationCacheService,
    RecommendationPreloaderService,

    // Services d'apprentissage automatique
    DeepLearningService,
    ContinuousLearningService,

    // Services d'A/B testing
    ABTestingService,

    // Services d'intégration de données externes
    ExternalDataService,

    // Services de recommandations en temps réel
    RealtimeRecommendationService,

    // Services d'explications des recommandations
    ExplanationService,
    ExplanationPreferencesService,
    ReinforcementLearningService,
    ExplanationTranslationService,
    ExplanationAnalyticsService,
    DiversityFilterService,
    FairnessService,
    ContextDetectionService,
    ContextualRecommendationService,
    SeasonalRecommendationService,
    GroupRecommendationService,
    SocialRecommendationService,
    CollaborativePlanningService,

    // Services de partage collaboratif
    CollaborativeSharingService,

    // Services d'analyse
    AnalyticsService,

    // Services de visualisation
    VisualizationService,

    // Services de génération de rapports
    ReportGeneratorService,

    // Nouveaux services pour le Sprint 4
    ModerationIntegrationService,
    EnhancedExplanationService,
    FeedbackService,
    AnalyticsIntegrationService,
    NotificationIntegrationService,

    // Nouveaux services pour le Sprint 5
    DataPreprocessingService,
    FeatureExtractionService,
    ModelTrainingService,
    ContinuousLearningPipelineService,
    HierarchicalPreferenceService,
    PreferenceInferenceService,
    TemporalPreferenceService,
    PerformanceMonitoringService,
    DiversityFairnessService,
    TransparencyService,
    PerformanceOptimizationService,
    ScalabilityService,
    AdvancedContextService,
    GenerativeAIService,
    ProactiveRecommendationService,
    SocialRecommendationService,
    GroupRecommendationService,
    TestingDocumentationService,
  ],
})
export class RecommendationModule {}
