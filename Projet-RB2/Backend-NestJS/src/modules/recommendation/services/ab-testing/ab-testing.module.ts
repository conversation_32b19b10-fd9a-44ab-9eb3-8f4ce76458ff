import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../../../prisma/prisma.module';
import { ABTestingService } from './ab-testing.service';
import { StatisticalAnalysisService } from './statistical-analysis.service';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
  ],
  providers: [
    ABTestingService,
    StatisticalAnalysisService,
  ],
  exports: [
    ABTestingService,
    StatisticalAnalysisService,
  ],
})
export class ABTestingModule {}
