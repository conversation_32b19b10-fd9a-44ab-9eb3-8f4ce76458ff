import { Injectable, Logger } from '@nestjs/common';

/**
 * Résultat d'un test de signification
 */
export interface SignificanceTestResult {
  /** Indique si le résultat est statistiquement significatif */
  significant: boolean;
  
  /** Valeur p */
  pValue: number;
  
  /** Intervalle de confiance */
  confidenceInterval: [number, number];
  
  /** Taille d'effet */
  effectSize: number;
  
  /** Puissance statistique */
  statisticalPower?: number;
}

/**
 * Service d'analyse statistique
 * Fournit des méthodes pour l'analyse statistique des tests A/B
 */
@Injectable()
export class StatisticalAnalysisService {
  private readonly logger = new Logger(StatisticalAnalysisService.name);
  
  constructor() {
    this.logger.log('StatisticalAnalysisService initialized');
  }
  
  /**
   * Teste la signification statistique entre deux échantillons
   * @param controlValue Valeur moyenne du groupe de contrôle
   * @param variantValue Valeur moyenne du groupe de variante
   * @param controlSampleSize Taille de l'échantillon du groupe de contrôle
   * @param variantSampleSize Taille de l'échantillon du groupe de variante
   * @param confidenceLevel Niveau de confiance (0-1)
   * @returns Résultat du test de signification
   */
  testSignificance(
    controlValue: number,
    variantValue: number,
    controlSampleSize: number,
    variantSampleSize: number,
    confidenceLevel: number = 0.95,
  ): SignificanceTestResult {
    this.logger.debug(`Testing significance: control=${controlValue}, variant=${variantValue}`);
    
    // Calculer la différence
    const difference = variantValue - controlValue;
    
    // Calculer l'erreur standard
    // Note: Dans une implémentation réelle, on utiliserait les écarts-types des échantillons
    // Ici, on utilise une approximation
    const controlStdDev = controlValue * 0.2; // 20% de la moyenne comme approximation
    const variantStdDev = variantValue * 0.2;
    
    const controlVariance = Math.pow(controlStdDev, 2) / controlSampleSize;
    const variantVariance = Math.pow(variantStdDev, 2) / variantSampleSize;
    
    const standardError = Math.sqrt(controlVariance + variantVariance);
    
    // Calculer la statistique Z
    const zScore = difference / standardError;
    
    // Calculer la valeur p
    const pValue = this.calculatePValue(zScore);
    
    // Calculer l'intervalle de confiance
    const alpha = 1 - confidenceLevel;
    const zCritical = this.getZCritical(alpha / 2);
    const marginOfError = zCritical * standardError;
    
    const confidenceInterval: [number, number] = [
      difference - marginOfError,
      difference + marginOfError,
    ];
    
    // Calculer la taille d'effet
    const effectSize = difference / ((controlStdDev + variantStdDev) / 2);
    
    // Déterminer la signification
    const significant = pValue < (1 - confidenceLevel);
    
    // Calculer la puissance statistique
    const statisticalPower = this.calculateStatisticalPower(
      effectSize,
      controlSampleSize,
      variantSampleSize,
      alpha,
    );
    
    return {
      significant,
      pValue,
      confidenceInterval,
      effectSize,
      statisticalPower,
    };
  }
  
  /**
   * Calcule la taille d'échantillon requise
   * @param expectedEffect Effet attendu
   * @param baselineConversion Taux de conversion de base
   * @param confidenceLevel Niveau de confiance (0-1)
   * @param power Puissance statistique (0-1)
   * @returns Taille d'échantillon requise par groupe
   */
  calculateRequiredSampleSize(
    expectedEffect: number,
    baselineConversion: number,
    confidenceLevel: number = 0.95,
    power: number = 0.8,
  ): number {
    this.logger.debug(`Calculating required sample size for effect=${expectedEffect}`);
    
    const alpha = 1 - confidenceLevel;
    const beta = 1 - power;
    
    const zAlpha = this.getZCritical(alpha / 2);
    const zBeta = this.getZCritical(beta);
    
    const p1 = baselineConversion;
    const p2 = baselineConversion * (1 + expectedEffect);
    
    const pBar = (p1 + p2) / 2;
    const variance = pBar * (1 - pBar) * 2;
    
    const numerator = Math.pow(zAlpha + zBeta, 2) * variance;
    const denominator = Math.pow(p1 - p2, 2);
    
    return Math.ceil(numerator / denominator);
  }
  
  /**
   * Calcule la valeur p à partir d'un score Z
   * @param zScore Score Z
   * @returns Valeur p
   */
  private calculatePValue(zScore: number): number {
    // Approximation de la fonction de répartition de la loi normale
    // Utilise l'approximation de Abramowitz et Stegun
    const absZ = Math.abs(zScore);
    const t = 1 / (1 + 0.2316419 * absZ);
    const d = 0.3989423 * Math.exp(-zScore * zScore / 2);
    
    const p = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    
    return zScore > 0 ? 1 - p : p;
  }
  
  /**
   * Obtient la valeur critique Z pour un niveau alpha donné
   * @param alpha Niveau alpha
   * @returns Valeur critique Z
   */
  private getZCritical(alpha: number): number {
    // Approximation de l'inverse de la fonction de répartition de la loi normale
    // Utilise l'approximation de Abramowitz et Stegun
    
    // Cas particuliers
    if (alpha === 0.05) return 1.96;
    if (alpha === 0.025) return 1.96;
    if (alpha === 0.01) return 2.576;
    if (alpha === 0.005) return 2.807;
    
    // Approximation générale
    const y = Math.sqrt(-2 * Math.log(alpha));
    
    return y - (2.30753 + 0.27061 * y) / (1 + 0.99229 * y + 0.04481 * y * y);
  }
  
  /**
   * Calcule la puissance statistique
   * @param effectSize Taille d'effet
   * @param n1 Taille de l'échantillon du groupe 1
   * @param n2 Taille de l'échantillon du groupe 2
   * @param alpha Niveau alpha
   * @returns Puissance statistique
   */
  private calculateStatisticalPower(
    effectSize: number,
    n1: number,
    n2: number,
    alpha: number,
  ): number {
    const zAlpha = this.getZCritical(alpha / 2);
    const denominator = Math.sqrt(1/n1 + 1/n2);
    const zBeta = effectSize / denominator - zAlpha;
    
    // Convertir zBeta en puissance (1 - beta)
    return 1 - this.calculatePValue(-zBeta);
  }
  
  /**
   * Détecte les anomalies dans les données
   * @param data Données à analyser
   * @param threshold Seuil de détection (nombre d'écarts-types)
   * @returns Indices des anomalies
   */
  detectAnomalies(data: number[], threshold: number = 3): number[] {
    if (data.length === 0) {
      return [];
    }
    
    // Calculer la moyenne et l'écart-type
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    
    // Détecter les anomalies
    const anomalies: number[] = [];
    
    for (let i = 0; i < data.length; i++) {
      const zScore = Math.abs((data[i] - mean) / stdDev);
      
      if (zScore > threshold) {
        anomalies.push(i);
      }
    }
    
    return anomalies;
  }
  
  /**
   * Calcule les statistiques descriptives
   * @param data Données à analyser
   * @returns Statistiques descriptives
   */
  calculateDescriptiveStatistics(data: number[]): {
    mean: number;
    median: number;
    stdDev: number;
    min: number;
    max: number;
    q1: number;
    q3: number;
  } {
    if (data.length === 0) {
      return {
        mean: NaN,
        median: NaN,
        stdDev: NaN,
        min: NaN,
        max: NaN,
        q1: NaN,
        q3: NaN,
      };
    }
    
    // Trier les données
    const sortedData = [...data].sort((a, b) => a - b);
    
    // Calculer les statistiques
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const stdDev = Math.sqrt(variance);
    const min = sortedData[0];
    const max = sortedData[sortedData.length - 1];
    
    // Calculer la médiane
    const midIndex = Math.floor(sortedData.length / 2);
    const median = sortedData.length % 2 === 0
      ? (sortedData[midIndex - 1] + sortedData[midIndex]) / 2
      : sortedData[midIndex];
    
    // Calculer les quartiles
    const q1Index = Math.floor(sortedData.length / 4);
    const q3Index = Math.floor(sortedData.length * 3 / 4);
    
    const q1 = sortedData[q1Index];
    const q3 = sortedData[q3Index];
    
    return {
      mean,
      median,
      stdDev,
      min,
      max,
      q1,
      q3,
    };
  }
}
