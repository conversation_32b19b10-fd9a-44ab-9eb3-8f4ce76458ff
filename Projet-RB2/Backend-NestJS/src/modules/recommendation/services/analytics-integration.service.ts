import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * Types d'événements d'analytics
 */
export enum AnalyticsEventType {
  RECOMMENDATION_GENERATED = 'recommendation_generated',
  RECOMMENDATION_VIEWED = 'recommendation_viewed',
  RECOMMENDATION_CLICKED = 'recommendation_clicked',
  RECOMMENDATION_FEEDBACK = 'recommendation_feedback',
  EXPLANATION_VIEWED = 'explanation_viewed',
  EXPLANATION_FEEDBACK = 'explanation_feedback',
}

/**
 * Interface pour les données d'événement d'analytics
 */
export interface AnalyticsEventData {
  userId: string;
  recommendationId?: string;
  recommendationType?: RecommendationType;
  explanationId?: string;
  feedbackType?: string;
  metadata?: Record<string, any>;
}

/**
 * Service d'intégration avec le service d'analytics
 * Permet de suivre les interactions avec les recommandations et les explications
 */
@Injectable()
export class AnalyticsIntegrationService {
  private readonly logger = new Logger(AnalyticsIntegrationService.name);
  private readonly analyticsServiceUrl: string;
  private readonly apiKey: string;
  private readonly localEvents: Array<{
    id: string;
    eventType: AnalyticsEventType;
    eventData: AnalyticsEventData;
    timestamp: Date;
  }> = [];

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.analyticsServiceUrl = this.configService.get<string>('ANALYTICS_SERVICE_URL', 'http://localhost:3003');
    this.apiKey = this.configService.get<string>('ANALYTICS_SERVICE_API_KEY', 'default-api-key');
  }

  /**
   * Enregistre un événement d'analytics
   * @param eventType Type d'événement
   * @param eventData Données de l'événement
   * @returns Résultat de l'opération
   */
  async trackEvent(eventType: AnalyticsEventType, eventData: AnalyticsEventData): Promise<any> {
    try {
      this.logger.log(`Enregistrement d'un événement d'analytics de type ${eventType}`);

      // Si le service d'analytics n'est pas disponible, on stocke l'événement en local
      if (!this.isAnalyticsServiceAvailable()) {
        return this.storeEventLocally(eventType, eventData);
      }

      // Envoyer l'événement au service d'analytics
      const { data } = await firstValueFrom(
        this.httpService.post(
          `${this.analyticsServiceUrl}/api/analytics/events`,
          {
            event_name: eventType,
            event_data: {
              ...eventData,
              timestamp: new Date().toISOString(),
            },
          },
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      // Émettre un événement pour informer les autres services
      this.eventEmitter.emit('analytics.event', {
        eventType,
        eventData,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement d'analytics: ${error.message}`);
      
      // En cas d'erreur avec le service d'analytics, on stocke l'événement en local
      return this.storeEventLocally(eventType, eventData);
    }
  }

  /**
   * Enregistre un événement de génération de recommandation
   * @param userId ID de l'utilisateur
   * @param recommendationIds IDs des recommandations générées
   * @param recommendationType Type de recommandation
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async trackRecommendationGenerated(
    userId: string,
    recommendationIds: string[],
    recommendationType: RecommendationType,
    metadata?: Record<string, any>,
  ): Promise<any> {
    return this.trackEvent(AnalyticsEventType.RECOMMENDATION_GENERATED, {
      userId,
      recommendationType,
      metadata: {
        ...metadata,
        recommendationIds,
        count: recommendationIds.length,
      },
    });
  }

  /**
   * Enregistre un événement de visualisation de recommandation
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async trackRecommendationViewed(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
    metadata?: Record<string, any>,
  ): Promise<any> {
    return this.trackEvent(AnalyticsEventType.RECOMMENDATION_VIEWED, {
      userId,
      recommendationId,
      recommendationType,
      metadata,
    });
  }

  /**
   * Enregistre un événement de clic sur une recommandation
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async trackRecommendationClicked(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
    metadata?: Record<string, any>,
  ): Promise<any> {
    return this.trackEvent(AnalyticsEventType.RECOMMENDATION_CLICKED, {
      userId,
      recommendationId,
      recommendationType,
      metadata,
    });
  }

  /**
   * Enregistre un événement de feedback sur une recommandation
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param feedbackType Type de feedback
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async trackRecommendationFeedback(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
    feedbackType: string,
    metadata?: Record<string, any>,
  ): Promise<any> {
    return this.trackEvent(AnalyticsEventType.RECOMMENDATION_FEEDBACK, {
      userId,
      recommendationId,
      recommendationType,
      feedbackType,
      metadata,
    });
  }

  /**
   * Enregistre un événement de visualisation d'explication
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param explanationId ID de l'explication
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async trackExplanationViewed(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
    explanationId: string,
    metadata?: Record<string, any>,
  ): Promise<any> {
    return this.trackEvent(AnalyticsEventType.EXPLANATION_VIEWED, {
      userId,
      recommendationId,
      recommendationType,
      explanationId,
      metadata,
    });
  }

  /**
   * Enregistre un événement de feedback sur une explication
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @param explanationId ID de l'explication
   * @param feedbackType Type de feedback
   * @param metadata Métadonnées supplémentaires
   * @returns Résultat de l'opération
   */
  async trackExplanationFeedback(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
    explanationId: string,
    feedbackType: string,
    metadata?: Record<string, any>,
  ): Promise<any> {
    return this.trackEvent(AnalyticsEventType.EXPLANATION_FEEDBACK, {
      userId,
      recommendationId,
      recommendationType,
      explanationId,
      feedbackType,
      metadata,
    });
  }

  /**
   * Vérifie si le service d'analytics est disponible
   * @returns true si le service est disponible, false sinon
   */
  private isAnalyticsServiceAvailable(): boolean {
    // Cette méthode pourrait être améliorée pour vérifier réellement la disponibilité du service
    // Pour l'instant, on vérifie simplement si l'URL et l'API key sont définies
    return !!this.analyticsServiceUrl && !!this.apiKey && this.analyticsServiceUrl !== 'http://localhost:3003';
  }

  /**
   * Stocke un événement en local (fallback si le service d'analytics n'est pas disponible)
   * @param eventType Type d'événement
   * @param eventData Données de l'événement
   * @returns Résultat de l'opération
   */
  private storeEventLocally(eventType: AnalyticsEventType, eventData: AnalyticsEventData): any {
    try {
      // Créer un ID unique pour l'événement
      const eventId = uuidv4();
      
      // Enregistrer l'événement en mémoire
      const event = {
        id: eventId,
        eventType,
        eventData,
        timestamp: new Date(),
      };
      
      this.localEvents.push(event);
      
      // Émettre un événement pour informer les autres services
      this.eventEmitter.emit('analytics.event', {
        eventType,
        eventData,
        timestamp: new Date(),
      });
      
      // Enregistrer également dans les logs pour persistance temporaire
      this.logger.debug(`Événement d'analytics enregistré localement: ${JSON.stringify(event)}`);

      return {
        success: true,
        message: 'Événement d\'analytics enregistré localement',
        data: event,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement local de l'événement d'analytics: ${error.message}`);
      throw new HttpException('Erreur lors de l\'enregistrement de l\'événement d\'analytics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
