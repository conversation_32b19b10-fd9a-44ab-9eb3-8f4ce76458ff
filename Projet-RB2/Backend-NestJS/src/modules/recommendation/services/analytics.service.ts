import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * Service d'analyse des recommandations
 * Fournit des métriques et des analyses sur l'efficacité des recommandations
 */
@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Récupère les métriques globales des recommandations
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques globales
   */
  async getGlobalMetrics(
    startDate?: Date,
    endDate?: Date,
  ): Promise<GlobalMetrics> {
    try {
      // Définir la période d'analyse
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 derniers jours par défaut
      const end = endDate || new Date();
      
      this.logger.log(`Récupération des métriques globales du ${start.toISOString()} au ${end.toISOString()}`);
      
      // Récupérer le nombre total de recommandations générées
      const totalRecommendations = await this.prisma.recommendation.count({
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
      });
      
      // Récupérer le nombre total d'interactions
      const totalInteractions = await this.prisma.userInteraction.count({
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
      });
      
      // Récupérer le nombre d'interactions par type
      const interactionsByType = await this.prisma.userInteraction.groupBy({
        by: ['interactionType'],
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
        _count: {
          interactionType: true,
        },
      });
      
      // Calculer les taux de conversion
      const viewCount = interactionsByType.find(i => i.interactionType === 'VIEW')?._count.interactionType || 0;
      const clickCount = interactionsByType.find(i => i.interactionType === 'CLICK')?._count.interactionType || 0;
      const likeCount = interactionsByType.find(i => i.interactionType === 'LIKE')?._count.interactionType || 0;
      const bookmarkCount = interactionsByType.find(i => i.interactionType === 'BOOKMARK')?._count.interactionType || 0;
      const enrollCount = interactionsByType.find(i => i.interactionType === 'ENROLL')?._count.interactionType || 0;
      const purchaseCount = interactionsByType.find(i => i.interactionType === 'PURCHASE')?._count.interactionType || 0;
      
      const clickThroughRate = viewCount > 0 ? (clickCount / viewCount) * 100 : 0;
      const conversionRate = viewCount > 0 ? ((enrollCount + purchaseCount) / viewCount) * 100 : 0;
      const engagementRate = viewCount > 0 ? ((likeCount + bookmarkCount) / viewCount) * 100 : 0;
      
      // Récupérer les métriques par type de recommandation
      const metricsByType = await Promise.all(
        Object.values(RecommendationType).map(async (type) => {
          const typeMetrics = await this.getMetricsByType(type, start, end);
          return {
            type,
            ...typeMetrics,
          };
        })
      );
      
      // Récupérer les métriques par stratégie de recommandation
      const metricsByStrategy = await Promise.all(
        Object.values(RecommendationStrategy).map(async (strategy) => {
          const strategyMetrics = await this.getMetricsByStrategy(strategy, start, end);
          return {
            strategy,
            ...strategyMetrics,
          };
        })
      );
      
      // Récupérer les métriques par jour
      const dailyMetrics = await this.getDailyMetrics(start, end);
      
      return {
        period: {
          start,
          end,
        },
        totalRecommendations,
        totalInteractions,
        interactionCounts: Object.fromEntries(
          interactionsByType.map(i => [i.interactionType, i._count.interactionType])
        ),
        rates: {
          clickThroughRate,
          conversionRate,
          engagementRate,
        },
        byType: metricsByType,
        byStrategy: metricsByStrategy,
        daily: dailyMetrics,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques globales: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les métriques des recommandations pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques de l'utilisateur
   */
  async getUserMetrics(
    userId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<UserMetrics> {
    try {
      // Définir la période d'analyse
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 derniers jours par défaut
      const end = endDate || new Date();
      
      this.logger.log(`Récupération des métriques pour l'utilisateur ${userId} du ${start.toISOString()} au ${end.toISOString()}`);
      
      // Récupérer le nombre total de recommandations générées pour l'utilisateur
      const totalRecommendations = await this.prisma.recommendation.count({
        where: {
          userId,
          createdAt: {
            gte: start,
            lte: end,
          },
        },
      });
      
      // Récupérer le nombre total d'interactions de l'utilisateur
      const totalInteractions = await this.prisma.userInteraction.count({
        where: {
          userId,
          createdAt: {
            gte: start,
            lte: end,
          },
        },
      });
      
      // Récupérer le nombre d'interactions par type
      const interactionsByType = await this.prisma.userInteraction.groupBy({
        by: ['interactionType'],
        where: {
          userId,
          createdAt: {
            gte: start,
            lte: end,
          },
        },
        _count: {
          interactionType: true,
        },
      });
      
      // Calculer les taux de conversion
      const viewCount = interactionsByType.find(i => i.interactionType === 'VIEW')?._count.interactionType || 0;
      const clickCount = interactionsByType.find(i => i.interactionType === 'CLICK')?._count.interactionType || 0;
      const likeCount = interactionsByType.find(i => i.interactionType === 'LIKE')?._count.interactionType || 0;
      const bookmarkCount = interactionsByType.find(i => i.interactionType === 'BOOKMARK')?._count.interactionType || 0;
      const enrollCount = interactionsByType.find(i => i.interactionType === 'ENROLL')?._count.interactionType || 0;
      const purchaseCount = interactionsByType.find(i => i.interactionType === 'PURCHASE')?._count.interactionType || 0;
      
      const clickThroughRate = viewCount > 0 ? (clickCount / viewCount) * 100 : 0;
      const conversionRate = viewCount > 0 ? ((enrollCount + purchaseCount) / viewCount) * 100 : 0;
      const engagementRate = viewCount > 0 ? ((likeCount + bookmarkCount) / viewCount) * 100 : 0;
      
      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.prisma.userPreference.findUnique({
        where: { userId },
        select: { preferences: true },
      });
      
      // Récupérer les catégories préférées de l'utilisateur
      const preferredCategories = userPreferences?.preferences?.preferredCategories || [];
      
      // Récupérer les interactions par catégorie
      const interactionsByCategory = await this.prisma.userInteraction.groupBy({
        by: ['metadata'],
        where: {
          userId,
          createdAt: {
            gte: start,
            lte: end,
          },
        },
        _count: {
          interactionType: true,
        },
      });
      
      // Transformer les résultats pour obtenir les catégories
      const categoryInteractions = interactionsByCategory
        .filter(i => i.metadata && typeof i.metadata === 'object' && 'category' in i.metadata)
        .map(i => ({
          category: i.metadata.category as string,
          count: i._count.interactionType,
          isPreferred: preferredCategories.includes(i.metadata.category as string),
        }))
        .sort((a, b) => b.count - a.count);
      
      return {
        userId,
        period: {
          start,
          end,
        },
        totalRecommendations,
        totalInteractions,
        interactionCounts: Object.fromEntries(
          interactionsByType.map(i => [i.interactionType, i._count.interactionType])
        ),
        rates: {
          clickThroughRate,
          conversionRate,
          engagementRate,
        },
        categoryInteractions,
        preferences: userPreferences?.preferences || {},
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques de l'utilisateur: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les métriques par type de recommandation
   * @param type Type de recommandation
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques par type
   */
  private async getMetricsByType(
    type: RecommendationType,
    startDate: Date,
    endDate: Date,
  ): Promise<TypeMetrics> {
    try {
      // Récupérer le nombre total de recommandations générées pour ce type
      const totalRecommendations = await this.prisma.recommendation.count({
        where: {
          type,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
      
      // Récupérer le nombre total d'interactions pour ce type
      const totalInteractions = await this.prisma.userInteraction.count({
        where: {
          itemType: type,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
      
      // Récupérer le nombre d'interactions par type d'interaction
      const interactionsByType = await this.prisma.userInteraction.groupBy({
        by: ['interactionType'],
        where: {
          itemType: type,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          interactionType: true,
        },
      });
      
      // Calculer les taux de conversion
      const viewCount = interactionsByType.find(i => i.interactionType === 'VIEW')?._count.interactionType || 0;
      const clickCount = interactionsByType.find(i => i.interactionType === 'CLICK')?._count.interactionType || 0;
      const likeCount = interactionsByType.find(i => i.interactionType === 'LIKE')?._count.interactionType || 0;
      const bookmarkCount = interactionsByType.find(i => i.interactionType === 'BOOKMARK')?._count.interactionType || 0;
      const enrollCount = interactionsByType.find(i => i.interactionType === 'ENROLL')?._count.interactionType || 0;
      const purchaseCount = interactionsByType.find(i => i.interactionType === 'PURCHASE')?._count.interactionType || 0;
      
      const clickThroughRate = viewCount > 0 ? (clickCount / viewCount) * 100 : 0;
      const conversionRate = viewCount > 0 ? ((enrollCount + purchaseCount) / viewCount) * 100 : 0;
      const engagementRate = viewCount > 0 ? ((likeCount + bookmarkCount) / viewCount) * 100 : 0;
      
      return {
        totalRecommendations,
        totalInteractions,
        interactionCounts: Object.fromEntries(
          interactionsByType.map(i => [i.interactionType, i._count.interactionType])
        ),
        rates: {
          clickThroughRate,
          conversionRate,
          engagementRate,
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques par type: ${error.message}`);
      return {
        totalRecommendations: 0,
        totalInteractions: 0,
        interactionCounts: {},
        rates: {
          clickThroughRate: 0,
          conversionRate: 0,
          engagementRate: 0,
        },
      };
    }
  }

  /**
   * Récupère les métriques par stratégie de recommandation
   * @param strategy Stratégie de recommandation
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques par stratégie
   */
  private async getMetricsByStrategy(
    strategy: RecommendationStrategy,
    startDate: Date,
    endDate: Date,
  ): Promise<StrategyMetrics> {
    try {
      // Récupérer le nombre total de recommandations générées pour cette stratégie
      const totalRecommendations = await this.prisma.recommendation.count({
        where: {
          metadata: {
            path: ['strategy'],
            equals: strategy,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
      
      // Récupérer le nombre total d'interactions pour cette stratégie
      const totalInteractions = await this.prisma.userInteraction.count({
        where: {
          metadata: {
            path: ['recommendationStrategy'],
            equals: strategy,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });
      
      // Récupérer le nombre d'interactions par type d'interaction
      const interactionsByType = await this.prisma.userInteraction.groupBy({
        by: ['interactionType'],
        where: {
          metadata: {
            path: ['recommendationStrategy'],
            equals: strategy,
          },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          interactionType: true,
        },
      });
      
      // Calculer les taux de conversion
      const viewCount = interactionsByType.find(i => i.interactionType === 'VIEW')?._count.interactionType || 0;
      const clickCount = interactionsByType.find(i => i.interactionType === 'CLICK')?._count.interactionType || 0;
      const likeCount = interactionsByType.find(i => i.interactionType === 'LIKE')?._count.interactionType || 0;
      const bookmarkCount = interactionsByType.find(i => i.interactionType === 'BOOKMARK')?._count.interactionType || 0;
      const enrollCount = interactionsByType.find(i => i.interactionType === 'ENROLL')?._count.interactionType || 0;
      const purchaseCount = interactionsByType.find(i => i.interactionType === 'PURCHASE')?._count.interactionType || 0;
      
      const clickThroughRate = viewCount > 0 ? (clickCount / viewCount) * 100 : 0;
      const conversionRate = viewCount > 0 ? ((enrollCount + purchaseCount) / viewCount) * 100 : 0;
      const engagementRate = viewCount > 0 ? ((likeCount + bookmarkCount) / viewCount) * 100 : 0;
      
      return {
        totalRecommendations,
        totalInteractions,
        interactionCounts: Object.fromEntries(
          interactionsByType.map(i => [i.interactionType, i._count.interactionType])
        ),
        rates: {
          clickThroughRate,
          conversionRate,
          engagementRate,
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques par stratégie: ${error.message}`);
      return {
        totalRecommendations: 0,
        totalInteractions: 0,
        interactionCounts: {},
        rates: {
          clickThroughRate: 0,
          conversionRate: 0,
          engagementRate: 0,
        },
      };
    }
  }

  /**
   * Récupère les métriques quotidiennes
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Métriques quotidiennes
   */
  private async getDailyMetrics(
    startDate: Date,
    endDate: Date,
  ): Promise<DailyMetrics[]> {
    try {
      // Récupérer les interactions par jour
      const interactionsByDay = await this.prisma.userInteraction.groupBy({
        by: ['createdAt'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          interactionType: true,
        },
      });
      
      // Récupérer les recommandations par jour
      const recommendationsByDay = await this.prisma.recommendation.groupBy({
        by: ['createdAt'],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        _count: {
          id: true,
        },
      });
      
      // Créer un dictionnaire des jours
      const days: Record<string, DailyMetrics> = {};
      
      // Ajouter les interactions
      interactionsByDay.forEach(interaction => {
        const date = interaction.createdAt.toISOString().split('T')[0];
        
        if (!days[date]) {
          days[date] = {
            date: new Date(date),
            totalRecommendations: 0,
            totalInteractions: 0,
            interactionCounts: {},
          };
        }
        
        days[date].totalInteractions += interaction._count.interactionType;
      });
      
      // Ajouter les recommandations
      recommendationsByDay.forEach(recommendation => {
        const date = recommendation.createdAt.toISOString().split('T')[0];
        
        if (!days[date]) {
          days[date] = {
            date: new Date(date),
            totalRecommendations: 0,
            totalInteractions: 0,
            interactionCounts: {},
          };
        }
        
        days[date].totalRecommendations += recommendation._count.id;
      });
      
      // Convertir le dictionnaire en tableau et trier par date
      return Object.values(days).sort((a, b) => a.date.getTime() - b.date.getTime());
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques quotidiennes: ${error.message}`);
      return [];
    }
  }

  /**
   * Génère un rapport d'analyse des recommandations
   * Exécuté une fois par semaine
   */
  @Cron(CronExpression.EVERY_WEEK)
  async generateWeeklyReport(): Promise<void> {
    try {
      this.logger.log('Génération du rapport hebdomadaire d\'analyse des recommandations');
      
      // Définir la période d'analyse (semaine précédente)
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      // Récupérer les métriques globales
      const globalMetrics = await this.getGlobalMetrics(startDate, endDate);
      
      // Enregistrer le rapport
      await this.prisma.analyticsReport.create({
        data: {
          type: 'WEEKLY',
          startDate,
          endDate,
          metrics: globalMetrics,
        },
      });
      
      this.logger.log('Rapport hebdomadaire généré avec succès');
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du rapport hebdomadaire: ${error.message}`);
    }
  }

  /**
   * Génère un rapport d'analyse des recommandations
   * Exécuté une fois par mois
   */
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async generateMonthlyReport(): Promise<void> {
    try {
      this.logger.log('Génération du rapport mensuel d\'analyse des recommandations');
      
      // Définir la période d'analyse (mois précédent)
      const endDate = new Date();
      const startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 1, 1);
      
      // Récupérer les métriques globales
      const globalMetrics = await this.getGlobalMetrics(startDate, endDate);
      
      // Enregistrer le rapport
      await this.prisma.analyticsReport.create({
        data: {
          type: 'MONTHLY',
          startDate,
          endDate,
          metrics: globalMetrics,
        },
      });
      
      this.logger.log('Rapport mensuel généré avec succès');
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du rapport mensuel: ${error.message}`);
    }
  }
}

/**
 * Interface pour les métriques globales
 */
export interface GlobalMetrics {
  /** Période d'analyse */
  period: {
    start: Date;
    end: Date;
  };
  
  /** Nombre total de recommandations générées */
  totalRecommendations: number;
  
  /** Nombre total d'interactions */
  totalInteractions: number;
  
  /** Nombre d'interactions par type */
  interactionCounts: Record<string, number>;
  
  /** Taux de conversion */
  rates: {
    /** Taux de clics (CTR) */
    clickThroughRate: number;
    
    /** Taux de conversion (inscriptions + achats) */
    conversionRate: number;
    
    /** Taux d'engagement (likes + bookmarks) */
    engagementRate: number;
  };
  
  /** Métriques par type de recommandation */
  byType: Array<{
    type: RecommendationType;
  } & TypeMetrics>;
  
  /** Métriques par stratégie de recommandation */
  byStrategy: Array<{
    strategy: RecommendationStrategy;
  } & StrategyMetrics>;
  
  /** Métriques quotidiennes */
  daily: DailyMetrics[];
}

/**
 * Interface pour les métriques d'un utilisateur
 */
export interface UserMetrics {
  /** ID de l'utilisateur */
  userId: string;
  
  /** Période d'analyse */
  period: {
    start: Date;
    end: Date;
  };
  
  /** Nombre total de recommandations générées */
  totalRecommendations: number;
  
  /** Nombre total d'interactions */
  totalInteractions: number;
  
  /** Nombre d'interactions par type */
  interactionCounts: Record<string, number>;
  
  /** Taux de conversion */
  rates: {
    /** Taux de clics (CTR) */
    clickThroughRate: number;
    
    /** Taux de conversion (inscriptions + achats) */
    conversionRate: number;
    
    /** Taux d'engagement (likes + bookmarks) */
    engagementRate: number;
  };
  
  /** Interactions par catégorie */
  categoryInteractions: Array<{
    category: string;
    count: number;
    isPreferred: boolean;
  }>;
  
  /** Préférences de l'utilisateur */
  preferences: Record<string, any>;
}

/**
 * Interface pour les métriques par type de recommandation
 */
export interface TypeMetrics {
  /** Nombre total de recommandations générées */
  totalRecommendations: number;
  
  /** Nombre total d'interactions */
  totalInteractions: number;
  
  /** Nombre d'interactions par type */
  interactionCounts: Record<string, number>;
  
  /** Taux de conversion */
  rates: {
    /** Taux de clics (CTR) */
    clickThroughRate: number;
    
    /** Taux de conversion (inscriptions + achats) */
    conversionRate: number;
    
    /** Taux d'engagement (likes + bookmarks) */
    engagementRate: number;
  };
}

/**
 * Interface pour les métriques par stratégie de recommandation
 */
export interface StrategyMetrics {
  /** Nombre total de recommandations générées */
  totalRecommendations: number;
  
  /** Nombre total d'interactions */
  totalInteractions: number;
  
  /** Nombre d'interactions par type */
  interactionCounts: Record<string, number>;
  
  /** Taux de conversion */
  rates: {
    /** Taux de clics (CTR) */
    clickThroughRate: number;
    
    /** Taux de conversion (inscriptions + achats) */
    conversionRate: number;
    
    /** Taux d'engagement (likes + bookmarks) */
    engagementRate: number;
  };
}

/**
 * Interface pour les métriques quotidiennes
 */
export interface DailyMetrics {
  /** Date */
  date: Date;
  
  /** Nombre total de recommandations générées */
  totalRecommendations: number;
  
  /** Nombre total d'interactions */
  totalInteractions: number;
  
  /** Nombre d'interactions par type */
  interactionCounts: Record<string, number>;
}
