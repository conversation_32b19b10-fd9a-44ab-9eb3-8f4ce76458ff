import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

/**
 * Service de partage collaboratif de recommandations
 * Permet aux utilisateurs de partager des recommandations avec d'autres utilisateurs
 */
@Injectable()
export class CollaborativeSharingService {
  private readonly logger = new Logger(CollaborativeSharingService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Partage une recommandation avec un autre utilisateur
   * @param fromUserId ID de l'utilisateur qui partage
   * @param toUserId ID de l'utilisateur destinataire
   * @param itemId ID de l'élément partagé
   * @param itemType Type de l'élément partagé
   * @param message Message personnalisé
   * @returns Recommandation partagée
   */
  async shareRecommendation(
    fromUserId: string,
    toUserId: string,
    itemId: string,
    itemType: RecommendationType,
    message?: string,
  ): Promise<SharedRecommendation> {
    try {
      this.logger.log(`Partage d'une recommandation de ${fromUserId} à ${toUserId}`);
      
      // Vérifier si l'utilisateur destinataire existe
      const toUser = await this.prisma.user.findUnique({
        where: { id: toUserId },
        select: { id: true },
      });
      
      if (!toUser) {
        throw new Error(`L'utilisateur destinataire ${toUserId} n'existe pas`);
      }
      
      // Vérifier si l'élément existe
      const item = await this.getItemDetails(itemId, itemType);
      
      if (!item) {
        throw new Error(`L'élément ${itemId} de type ${itemType} n'existe pas`);
      }
      
      // Créer le partage
      const sharedRecommendation = await this.prisma.sharedRecommendation.create({
        data: {
          fromUserId,
          toUserId,
          itemId,
          itemType,
          message: message || '',
          status: 'PENDING',
          createdAt: new Date(),
        },
      });
      
      // Enregistrer une interaction pour l'utilisateur qui partage
      await this.prisma.userInteraction.create({
        data: {
          userId: fromUserId,
          itemId,
          itemType,
          interactionType: 'SHARE',
          metadata: {
            sharedWith: toUserId,
            sharedRecommendationId: sharedRecommendation.id,
          },
        },
      });
      
      // Retourner la recommandation partagée avec les détails de l'élément
      return {
        id: sharedRecommendation.id,
        fromUserId,
        toUserId,
        itemId,
        itemType,
        message: sharedRecommendation.message,
        status: sharedRecommendation.status,
        createdAt: sharedRecommendation.createdAt,
        item,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du partage de la recommandation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les recommandations partagées avec un utilisateur
   * @param userId ID de l'utilisateur
   * @param status Statut des recommandations à récupérer
   * @param limit Nombre maximum de recommandations à récupérer
   * @returns Liste des recommandations partagées
   */
  async getSharedRecommendations(
    userId: string,
    status?: SharedRecommendationStatus,
    limit = 10,
  ): Promise<SharedRecommendation[]> {
    try {
      this.logger.log(`Récupération des recommandations partagées avec l'utilisateur ${userId}`);
      
      // Récupérer les recommandations partagées
      const sharedRecommendations = await this.prisma.sharedRecommendation.findMany({
        where: {
          toUserId: userId,
          status: status || undefined,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });
      
      // Récupérer les détails des éléments
      const itemIds = sharedRecommendations.map(sr => sr.itemId);
      const itemTypes = [...new Set(sharedRecommendations.map(sr => sr.itemType))];
      
      const itemsDetails = await this.getItemsDetails(itemIds, itemTypes);
      
      // Récupérer les détails des utilisateurs
      const userIds = [...new Set(sharedRecommendations.map(sr => sr.fromUserId))];
      const usersDetails = await this.getUsersDetails(userIds);
      
      // Combiner les données
      return sharedRecommendations.map(sr => ({
        id: sr.id,
        fromUserId: sr.fromUserId,
        toUserId: sr.toUserId,
        itemId: sr.itemId,
        itemType: sr.itemType as RecommendationType,
        message: sr.message,
        status: sr.status as SharedRecommendationStatus,
        createdAt: sr.createdAt,
        item: itemsDetails[sr.itemId] || null,
        fromUser: usersDetails[sr.fromUserId] || null,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations partagées: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les recommandations partagées par un utilisateur
   * @param userId ID de l'utilisateur
   * @param limit Nombre maximum de recommandations à récupérer
   * @returns Liste des recommandations partagées
   */
  async getSharedByUserRecommendations(
    userId: string,
    limit = 10,
  ): Promise<SharedRecommendation[]> {
    try {
      this.logger.log(`Récupération des recommandations partagées par l'utilisateur ${userId}`);
      
      // Récupérer les recommandations partagées
      const sharedRecommendations = await this.prisma.sharedRecommendation.findMany({
        where: {
          fromUserId: userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });
      
      // Récupérer les détails des éléments
      const itemIds = sharedRecommendations.map(sr => sr.itemId);
      const itemTypes = [...new Set(sharedRecommendations.map(sr => sr.itemType))];
      
      const itemsDetails = await this.getItemsDetails(itemIds, itemTypes);
      
      // Récupérer les détails des utilisateurs
      const userIds = [...new Set(sharedRecommendations.map(sr => sr.toUserId))];
      const usersDetails = await this.getUsersDetails(userIds);
      
      // Combiner les données
      return sharedRecommendations.map(sr => ({
        id: sr.id,
        fromUserId: sr.fromUserId,
        toUserId: sr.toUserId,
        itemId: sr.itemId,
        itemType: sr.itemType as RecommendationType,
        message: sr.message,
        status: sr.status as SharedRecommendationStatus,
        createdAt: sr.createdAt,
        item: itemsDetails[sr.itemId] || null,
        toUser: usersDetails[sr.toUserId] || null,
      }));
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des recommandations partagées: ${error.message}`);
      return [];
    }
  }

  /**
   * Met à jour le statut d'une recommandation partagée
   * @param id ID de la recommandation partagée
   * @param userId ID de l'utilisateur qui met à jour le statut
   * @param status Nouveau statut
   * @returns Recommandation partagée mise à jour
   */
  async updateSharedRecommendationStatus(
    id: string,
    userId: string,
    status: SharedRecommendationStatus,
  ): Promise<SharedRecommendation> {
    try {
      this.logger.log(`Mise à jour du statut de la recommandation partagée ${id} à ${status}`);
      
      // Vérifier si la recommandation partagée existe et appartient à l'utilisateur
      const sharedRecommendation = await this.prisma.sharedRecommendation.findFirst({
        where: {
          id,
          toUserId: userId,
        },
      });
      
      if (!sharedRecommendation) {
        throw new Error(`La recommandation partagée ${id} n'existe pas ou n'appartient pas à l'utilisateur ${userId}`);
      }
      
      // Mettre à jour le statut
      const updatedSharedRecommendation = await this.prisma.sharedRecommendation.update({
        where: { id },
        data: { status },
      });
      
      // Si le statut est ACCEPTED, enregistrer une interaction
      if (status === 'ACCEPTED') {
        await this.prisma.userInteraction.create({
          data: {
            userId,
            itemId: sharedRecommendation.itemId,
            itemType: sharedRecommendation.itemType,
            interactionType: 'ACCEPT_SHARED',
            metadata: {
              sharedRecommendationId: id,
              sharedByUserId: sharedRecommendation.fromUserId,
            },
          },
        });
      }
      
      // Récupérer les détails de l'élément
      const item = await this.getItemDetails(sharedRecommendation.itemId, sharedRecommendation.itemType as RecommendationType);
      
      // Récupérer les détails de l'utilisateur qui a partagé
      const fromUser = await this.getUserDetails(sharedRecommendation.fromUserId);
      
      // Retourner la recommandation partagée mise à jour
      return {
        id: updatedSharedRecommendation.id,
        fromUserId: updatedSharedRecommendation.fromUserId,
        toUserId: updatedSharedRecommendation.toUserId,
        itemId: updatedSharedRecommendation.itemId,
        itemType: updatedSharedRecommendation.itemType as RecommendationType,
        message: updatedSharedRecommendation.message,
        status: updatedSharedRecommendation.status as SharedRecommendationStatus,
        createdAt: updatedSharedRecommendation.createdAt,
        item,
        fromUser,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du statut de la recommandation partagée: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les détails d'un élément
   * @param itemId ID de l'élément
   * @param itemType Type de l'élément
   * @returns Détails de l'élément
   */
  private async getItemDetails(
    itemId: string,
    itemType: RecommendationType,
  ): Promise<ItemDetails | null> {
    try {
      switch (itemType) {
        case RecommendationType.COURSE:
          const course = await this.prisma.course.findUnique({
            where: { id: itemId },
            select: {
              id: true,
              title: true,
              description: true,
              category: true,
              level: true,
              imageUrl: true,
              metadata: true,
            },
          });
          
          if (course) {
            return {
              id: course.id,
              title: course.title,
              description: course.description,
              type: RecommendationType.COURSE,
              imageUrl: course.imageUrl,
              metadata: {
                category: course.category,
                level: course.level,
                ...course.metadata,
              },
            };
          }
          break;
          
        case RecommendationType.RETREAT:
          const retreat = await this.prisma.retreat.findUnique({
            where: { id: itemId },
            select: {
              id: true,
              title: true,
              description: true,
              location: true,
              startDate: true,
              endDate: true,
              imageUrl: true,
              metadata: true,
            },
          });
          
          if (retreat) {
            return {
              id: retreat.id,
              title: retreat.title,
              description: retreat.description,
              type: RecommendationType.RETREAT,
              imageUrl: retreat.imageUrl,
              metadata: {
                location: retreat.location,
                startDate: retreat.startDate,
                endDate: retreat.endDate,
                ...retreat.metadata,
              },
            };
          }
          break;
          
        case RecommendationType.PARTNER:
          const partner = await this.prisma.partner.findUnique({
            where: { id: itemId },
            select: {
              id: true,
              name: true,
              description: true,
              imageUrl: true,
              metadata: true,
            },
          });
          
          if (partner) {
            return {
              id: partner.id,
              title: partner.name,
              description: partner.description,
              type: RecommendationType.PARTNER,
              imageUrl: partner.imageUrl,
              metadata: partner.metadata || {},
            };
          }
          break;
      }
      
      return null;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails de l'élément: ${error.message}`);
      return null;
    }
  }

  /**
   * Récupère les détails de plusieurs éléments
   * @param itemIds IDs des éléments
   * @param itemTypes Types des éléments
   * @returns Détails des éléments
   */
  private async getItemsDetails(
    itemIds: string[],
    itemTypes: string[],
  ): Promise<Record<string, ItemDetails>> {
    try {
      const result: Record<string, ItemDetails> = {};
      
      // Récupérer les détails des cours
      if (itemTypes.includes(RecommendationType.COURSE)) {
        const courses = await this.prisma.course.findMany({
          where: {
            id: {
              in: itemIds,
            },
          },
          select: {
            id: true,
            title: true,
            description: true,
            category: true,
            level: true,
            imageUrl: true,
            metadata: true,
          },
        });
        
        courses.forEach(course => {
          result[course.id] = {
            id: course.id,
            title: course.title,
            description: course.description,
            type: RecommendationType.COURSE,
            imageUrl: course.imageUrl,
            metadata: {
              category: course.category,
              level: course.level,
              ...course.metadata,
            },
          };
        });
      }
      
      // Récupérer les détails des retraites
      if (itemTypes.includes(RecommendationType.RETREAT)) {
        const retreats = await this.prisma.retreat.findMany({
          where: {
            id: {
              in: itemIds,
            },
          },
          select: {
            id: true,
            title: true,
            description: true,
            location: true,
            startDate: true,
            endDate: true,
            imageUrl: true,
            metadata: true,
          },
        });
        
        retreats.forEach(retreat => {
          result[retreat.id] = {
            id: retreat.id,
            title: retreat.title,
            description: retreat.description,
            type: RecommendationType.RETREAT,
            imageUrl: retreat.imageUrl,
            metadata: {
              location: retreat.location,
              startDate: retreat.startDate,
              endDate: retreat.endDate,
              ...retreat.metadata,
            },
          };
        });
      }
      
      // Récupérer les détails des partenaires
      if (itemTypes.includes(RecommendationType.PARTNER)) {
        const partners = await this.prisma.partner.findMany({
          where: {
            id: {
              in: itemIds,
            },
          },
          select: {
            id: true,
            name: true,
            description: true,
            imageUrl: true,
            metadata: true,
          },
        });
        
        partners.forEach(partner => {
          result[partner.id] = {
            id: partner.id,
            title: partner.name,
            description: partner.description,
            type: RecommendationType.PARTNER,
            imageUrl: partner.imageUrl,
            metadata: partner.metadata || {},
          };
        });
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails des éléments: ${error.message}`);
      return {};
    }
  }

  /**
   * Récupère les détails d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Détails de l'utilisateur
   */
  private async getUserDetails(userId: string): Promise<UserDetails | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              avatarUrl: true,
            },
          },
        },
      });
      
      if (user) {
        return {
          id: user.id,
          email: user.email,
          firstName: user.profile?.firstName || '',
          lastName: user.profile?.lastName || '',
          avatarUrl: user.profile?.avatarUrl || '',
        };
      }
      
      return null;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails de l'utilisateur: ${error.message}`);
      return null;
    }
  }

  /**
   * Récupère les détails de plusieurs utilisateurs
   * @param userIds IDs des utilisateurs
   * @returns Détails des utilisateurs
   */
  private async getUsersDetails(userIds: string[]): Promise<Record<string, UserDetails>> {
    try {
      const result: Record<string, UserDetails> = {};
      
      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: userIds,
          },
        },
        select: {
          id: true,
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              avatarUrl: true,
            },
          },
        },
      });
      
      users.forEach(user => {
        result[user.id] = {
          id: user.id,
          email: user.email,
          firstName: user.profile?.firstName || '',
          lastName: user.profile?.lastName || '',
          avatarUrl: user.profile?.avatarUrl || '',
        };
      });
      
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails des utilisateurs: ${error.message}`);
      return {};
    }
  }
}

/**
 * Statut d'une recommandation partagée
 */
export type SharedRecommendationStatus = 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'VIEWED';

/**
 * Interface pour une recommandation partagée
 */
export interface SharedRecommendation {
  /** ID de la recommandation partagée */
  id: string;
  
  /** ID de l'utilisateur qui a partagé */
  fromUserId: string;
  
  /** ID de l'utilisateur destinataire */
  toUserId: string;
  
  /** ID de l'élément partagé */
  itemId: string;
  
  /** Type de l'élément partagé */
  itemType: RecommendationType;
  
  /** Message personnalisé */
  message: string;
  
  /** Statut de la recommandation partagée */
  status: SharedRecommendationStatus;
  
  /** Date de création */
  createdAt: Date;
  
  /** Détails de l'élément partagé */
  item?: ItemDetails | null;
  
  /** Détails de l'utilisateur qui a partagé */
  fromUser?: UserDetails | null;
  
  /** Détails de l'utilisateur destinataire */
  toUser?: UserDetails | null;
}

/**
 * Interface pour les détails d'un élément
 */
export interface ItemDetails {
  /** ID de l'élément */
  id: string;
  
  /** Titre de l'élément */
  title: string;
  
  /** Description de l'élément */
  description: string;
  
  /** Type de l'élément */
  type: RecommendationType;
  
  /** URL de l'image */
  imageUrl?: string;
  
  /** Métadonnées de l'élément */
  metadata: Record<string, any>;
}

/**
 * Interface pour les détails d'un utilisateur
 */
export interface UserDetails {
  /** ID de l'utilisateur */
  id: string;
  
  /** Email de l'utilisateur */
  email: string;
  
  /** Prénom de l'utilisateur */
  firstName: string;
  
  /** Nom de l'utilisateur */
  lastName: string;
  
  /** URL de l'avatar */
  avatarUrl: string;
}
