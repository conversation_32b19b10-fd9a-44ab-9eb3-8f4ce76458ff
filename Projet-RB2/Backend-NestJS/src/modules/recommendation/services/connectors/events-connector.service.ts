import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ExternalData, ExternalDataType } from '../../interfaces/external-data.interface';
import { RecommendationType } from '../../enums/recommendation-type.enum';

/**
 * Service de connexion à l'API d'événements
 * Récupère les données d'événements locaux pour enrichir les recommandations
 */
@Injectable()
export class EventsConnectorService {
  private readonly logger = new Logger(EventsConnectorService.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly enabled: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.apiKey = this.configService.get<string>('recommendation.externalData.apiKeys.eventful', '');
    this.baseUrl = 'https://api.eventful.com/json/events';
    this.enabled = this.configService.get<boolean>('recommendation.externalData.sources.eventful.enabled', false);
    
    this.logger.log(`EventsConnectorService initialized with enabled=${this.enabled}`);
  }

  /**
   * Récupère les événements à proximité d'une localisation
   * @param location Localisation (ville ou coordonnées)
   * @param radius Rayon de recherche en km
   * @param categories Catégories d'événements
   * @param startDate Date de début
   * @param endDate Date de fin
   * @param limit Nombre maximum d'événements
   * @returns Données d'événements formatées
   */
  async getNearbyEvents(
    location: string | { lat: number; lon: number },
    radius: number = 10,
    categories: string[] = [],
    startDate?: Date,
    endDate?: Date,
    limit: number = 10
  ): Promise<ExternalData[]> {
    if (!this.enabled || !this.apiKey) {
      this.logger.warn('Events connector is disabled or API key is missing');
      return [];
    }

    try {
      // Construire les paramètres de la requête
      const params: Record<string, any> = {
        app_key: this.apiKey,
        page_size: limit,
        sort_order: 'popularity',
        include: 'categories,price,links,images',
      };

      // Ajouter la localisation
      if (typeof location === 'string') {
        params.location = location;
      } else {
        params.where = `${location.lat},${location.lon}`;
      }

      // Ajouter le rayon
      params.within = radius;
      params.units = 'km';

      // Ajouter les catégories
      if (categories.length > 0) {
        params.category = categories.join(',');
      }

      // Ajouter les dates
      if (startDate) {
        params.date = `${this.formatDate(startDate)}`;
        
        if (endDate) {
          params.date += `-${this.formatDate(endDate)}`;
        }
      }

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/search`, { params })
      );

      // Vérifier la réponse
      if (response.status !== 200) {
        throw new Error(`Events API returned status ${response.status}`);
      }

      // Transformer les données
      return this.transformEventsData(response.data);
    } catch (error) {
      this.logger.error(`Error fetching events data: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les détails d'un événement
   * @param eventId ID de l'événement
   * @returns Données de l'événement formatées
   */
  async getEventDetails(eventId: string): Promise<ExternalData | null> {
    if (!this.enabled || !this.apiKey) {
      this.logger.warn('Events connector is disabled or API key is missing');
      return null;
    }

    try {
      // Construire les paramètres de la requête
      const params = {
        app_key: this.apiKey,
        include: 'categories,price,links,images',
      };

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/get`, { 
          params: {
            ...params,
            id: eventId,
          }
        })
      );

      // Vérifier la réponse
      if (response.status !== 200) {
        throw new Error(`Events API returned status ${response.status}`);
      }

      // Transformer les données
      const events = this.transformEventsData([response.data]);
      return events.length > 0 ? events[0] : null;
    } catch (error) {
      this.logger.error(`Error fetching event details: ${error.message}`);
      return null;
    }
  }

  /**
   * Transforme les données d'événements en format standardisé
   * @param data Données brutes de l'API
   * @returns Données d'événements formatées
   */
  private transformEventsData(data: any[]): ExternalData[] {
    try {
      return data.map(event => {
        // Extraire les informations principales
        const title = event.title;
        const description = event.description || '';
        const venue = event.venue_name;
        const city = event.city_name;
        const country = event.country_name;
        const startTime = new Date(event.start_time);
        const endTime = event.stop_time ? new Date(event.stop_time) : undefined;
        
        // Extraire les catégories
        const categories = event.categories?.category?.map(cat => cat.name) || [];
        
        // Extraire les images
        const images = event.images?.image?.map(img => img.url) || [];
        const imageUrl = images.length > 0 ? images[0] : undefined;
        
        // Extraire le prix
        const price = event.price ? event.price : 'Gratuit';
        
        // Créer l'objet de données externes
        return {
          type: ExternalDataType.EVENT,
          source: 'Eventful',
          title: `${title} à ${venue}, ${city}`,
          content: description,
          url: event.url,
          imageUrl,
          relevanceScore: 0.75,
          metadata: {
            eventId: event.id,
            title,
            venue,
            address: event.venue_address,
            city,
            region: event.region_name,
            country,
            postalCode: event.postal_code,
            latitude: parseFloat(event.latitude),
            longitude: parseFloat(event.longitude),
            startTime,
            endTime,
            categories,
            tags: event.tags?.tag?.map(t => t.name) || [],
            price,
            images,
            performers: event.performers?.performer?.map(p => p.name) || [],
          },
          applicableTypes: [
            RecommendationType.RETREAT,
            RecommendationType.ACTIVITY,
            RecommendationType.EVENT,
          ],
        };
      });
    } catch (error) {
      this.logger.error(`Error transforming events data: ${error.message}`);
      return [];
    }
  }

  /**
   * Formate une date pour l'API Eventful
   * @param date Date à formater
   * @returns Date formatée (YYYYMMDD)
   */
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}00`;
  }
}
