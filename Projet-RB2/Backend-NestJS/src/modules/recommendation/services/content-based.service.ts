import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';

@Injectable()
export class ContentBasedService {
  private readonly logger = new Logger(ContentBasedService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Génère des recommandations basées sur le contenu pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations basées sur le contenu de type ${type} pour l'utilisateur ${userId}`);
      
      // Récupérer le profil de l'utilisateur
      const userProfile = await this.getUserProfile(userId);
      
      // Générer les recommandations selon le type
      let recommendations;
      switch (type) {
        case RecommendationType.COURSE:
          recommendations = await this.getRecommendedCourses(userId, userProfile, options);
          break;
        case RecommendationType.RETREAT:
          recommendations = await this.getRecommendedRetreats(userId, userProfile, options);
          break;
        case RecommendationType.PARTNER:
          recommendations = await this.getRecommendedPartners(userId, userProfile, options);
          break;
        default:
          throw new Error(`Type de recommandation non pris en charge: ${type}`);
      }
      
      this.logger.log(`${recommendations.length} recommandations basées sur le contenu générées pour l'utilisateur ${userId}`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations basées sur le contenu: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère des recommandations similaires à un élément
   * @param itemId ID de l'élément
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations similaires
   */
  async getSimilarItems(
    itemId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération d'éléments similaires à ${itemId} de type ${type}`);
      
      // Récupérer les caractéristiques de l'élément
      const itemFeatures = await this.getItemFeatures(itemId, type);
      
      if (!itemFeatures) {
        throw new Error(`Élément ${itemId} de type ${type} non trouvé`);
      }
      
      // Générer les recommandations selon le type
      let similarItems;
      switch (type) {
        case RecommendationType.COURSE:
          similarItems = await this.getSimilarCourses(itemId, itemFeatures, options);
          break;
        case RecommendationType.RETREAT:
          similarItems = await this.getSimilarRetreats(itemId, itemFeatures, options);
          break;
        case RecommendationType.PARTNER:
          similarItems = await this.getSimilarPartners(itemId, itemFeatures, options);
          break;
        default:
          throw new Error(`Type de recommandation non pris en charge: ${type}`);
      }
      
      this.logger.log(`${similarItems.length} éléments similaires générés pour ${itemId}`);
      return similarItems;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'éléments similaires: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour le modèle de recommandation pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Résultat de la mise à jour
   */
  async updateUserModel(userId: string) {
    try {
      this.logger.log(`Mise à jour du modèle de recommandation basé sur le contenu pour l'utilisateur ${userId}`);
      
      // Récupérer les interactions de l'utilisateur
      const userInteractions = await this.prisma.userInteraction.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 100,
      });
      
      // Extraire les préférences de l'utilisateur
      const userPreferences = this.extractUserPreferences(userInteractions);
      
      // Mettre à jour le profil de l'utilisateur
      await this.prisma.userProfile.upsert({
        where: { userId },
        update: {
          preferences: userPreferences,
          lastUpdated: new Date(),
        },
        create: {
          userId,
          preferences: userPreferences,
          lastUpdated: new Date(),
        },
      });
      
      this.logger.log(`Modèle de recommandation basé sur le contenu mis à jour pour l'utilisateur ${userId}`);
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du modèle de recommandation: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Récupère le profil d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Profil de l'utilisateur
   */
  private async getUserProfile(userId: string) {
    // Récupérer le profil de l'utilisateur
    let userProfile = await this.prisma.userProfile.findUnique({
      where: { userId },
    });
    
    // Si le profil n'existe pas, le créer
    if (!userProfile) {
      // Mettre à jour le modèle de l'utilisateur
      await this.updateUserModel(userId);
      
      // Récupérer le profil mis à jour
      userProfile = await this.prisma.userProfile.findUnique({
        where: { userId },
      });
    }
    
    return userProfile?.preferences || {};
  }

  /**
   * Extrait les préférences d'un utilisateur à partir de ses interactions
   * @param userInteractions Interactions de l'utilisateur
   * @returns Préférences de l'utilisateur
   */
  private extractUserPreferences(userInteractions: any[]) {
    // Initialiser les préférences
    const preferences: Record<string, any> = {
      categories: {},
      levels: {},
      tags: {},
      locations: {},
      durations: {},
    };
    
    // Analyser les interactions
    userInteractions.forEach(interaction => {
      // Pondérer l'interaction selon son type
      let weight = 1;
      switch (interaction.interactionType) {
        case 'VIEW':
          weight = 1;
          break;
        case 'LIKE':
          weight = 3;
          break;
        case 'ENROLL':
          weight = 5;
          break;
        case 'COMPLETE':
          weight = 10;
          break;
        default:
          weight = 1;
      }
      
      // Ajouter les préférences selon le type d'élément
      switch (interaction.itemType) {
        case RecommendationType.COURSE:
          // Ajouter la catégorie
          if (interaction.metadata?.category) {
            preferences.categories[interaction.metadata.category] = 
              (preferences.categories[interaction.metadata.category] || 0) + weight;
          }
          
          // Ajouter le niveau
          if (interaction.metadata?.level) {
            preferences.levels[interaction.metadata.level] = 
              (preferences.levels[interaction.metadata.level] || 0) + weight;
          }
          
          // Ajouter les tags
          if (interaction.metadata?.tags) {
            interaction.metadata.tags.forEach(tag => {
              preferences.tags[tag] = (preferences.tags[tag] || 0) + weight;
            });
          }
          break;
          
        case RecommendationType.RETREAT:
          // Ajouter la localisation
          if (interaction.metadata?.location) {
            preferences.locations[interaction.metadata.location] = 
              (preferences.locations[interaction.metadata.location] || 0) + weight;
          }
          
          // Ajouter la durée
          if (interaction.metadata?.duration) {
            preferences.durations[interaction.metadata.duration] = 
              (preferences.durations[interaction.metadata.duration] || 0) + weight;
          }
          
          // Ajouter la catégorie
          if (interaction.metadata?.category) {
            preferences.categories[interaction.metadata.category] = 
              (preferences.categories[interaction.metadata.category] || 0) + weight;
          }
          break;
          
        case RecommendationType.PARTNER:
          // Ajouter la spécialité
          if (interaction.metadata?.specialty) {
            preferences.categories[interaction.metadata.specialty] = 
              (preferences.categories[interaction.metadata.specialty] || 0) + weight;
          }
          
          // Ajouter la localisation
          if (interaction.metadata?.location) {
            preferences.locations[interaction.metadata.location] = 
              (preferences.locations[interaction.metadata.location] || 0) + weight;
          }
          break;
      }
    });
    
    return preferences;
  }

  /**
   * Récupère les caractéristiques d'un élément
   * @param itemId ID de l'élément
   * @param type Type de recommandation
   * @returns Caractéristiques de l'élément
   */
  private async getItemFeatures(itemId: string, type: RecommendationType) {
    switch (type) {
      case RecommendationType.COURSE:
        const course = await this.prisma.course.findUnique({
          where: { id: itemId },
        });
        
        if (!course) return null;
        
        return {
          category: course.category,
          level: course.level,
          tags: course.tags || [],
        };
        
      case RecommendationType.RETREAT:
        // Simuler la récupération des caractéristiques d'une retraite
        // Dans une implémentation réelle, on utiliserait une requête à la base de données
        return {
          category: 'Yoga',
          location: 'Bali',
          duration: 7,
          tags: ['meditation', 'wellness', 'nature'],
        };
        
      case RecommendationType.PARTNER:
        // Simuler la récupération des caractéristiques d'un partenaire
        // Dans une implémentation réelle, on utiliserait une requête à la base de données
        return {
          specialty: 'Yoga',
          location: 'Paris',
          tags: ['meditation', 'wellness'],
        };
        
      default:
        return null;
    }
  }

  /**
   * Calcule la similarité entre deux ensembles de caractéristiques
   * @param features1 Premier ensemble de caractéristiques
   * @param features2 Deuxième ensemble de caractéristiques
   * @returns Score de similarité
   */
  private calculateSimilarity(features1: Record<string, any>, features2: Record<string, any>) {
    let score = 0;
    let maxScore = 0;
    
    // Comparer les catégories
    if (features1.category && features2.category) {
      maxScore += 3;
      if (features1.category === features2.category) {
        score += 3;
      }
    }
    
    // Comparer les niveaux
    if (features1.level && features2.level) {
      maxScore += 2;
      if (features1.level === features2.level) {
        score += 2;
      }
    }
    
    // Comparer les localisations
    if (features1.location && features2.location) {
      maxScore += 3;
      if (features1.location === features2.location) {
        score += 3;
      }
    }
    
    // Comparer les durées
    if (features1.duration && features2.duration) {
      maxScore += 2;
      const durationDiff = Math.abs(features1.duration - features2.duration);
      if (durationDiff === 0) {
        score += 2;
      } else if (durationDiff <= 2) {
        score += 1;
      }
    }
    
    // Comparer les tags
    if (features1.tags && features2.tags) {
      const tags1 = new Set(features1.tags);
      const tags2 = new Set(features2.tags);
      
      const commonTags = [...tags1].filter(tag => tags2.has(tag));
      const allTags = new Set([...tags1, ...tags2]);
      
      maxScore += 5;
      score += 5 * (commonTags.length / allTags.size);
    }
    
    // Normaliser le score
    return maxScore > 0 ? score / maxScore : 0;
  }

  /**
   * Récupère les cours recommandés pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param userProfile Profil de l'utilisateur
   * @param options Options de recommandation
   * @returns Liste des cours recommandés
   */
  private async getRecommendedCourses(
    userId: string,
    userProfile: Record<string, any>,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Récupérer les cours déjà suivis par l'utilisateur
    const userEnrollments = await this.prisma.enrollment.findMany({
      where: { userId },
      select: { courseId: true },
    });
    
    const enrolledCourseIds = userEnrollments.map(enrollment => enrollment.courseId);
    
    // Récupérer tous les cours
    const allCourses = await this.prisma.course.findMany({
      where: {
        id: { notIn: enrolledCourseIds },
      },
    });
    
    // Calculer le score de chaque cours
    const scoredCourses = allCourses.map(course => {
      let score = 0;
      
      // Score basé sur la catégorie
      if (course.category && userProfile.categories) {
        const categoryScore = userProfile.categories[course.category] || 0;
        score += categoryScore * 0.5;
      }
      
      // Score basé sur le niveau
      if (course.level && userProfile.levels) {
        const levelScore = userProfile.levels[course.level] || 0;
        score += levelScore * 0.3;
      }
      
      // Score basé sur les tags
      if (course.tags && userProfile.tags) {
        const tagScores = course.tags.map(tag => userProfile.tags[tag] || 0);
        const avgTagScore = tagScores.length > 0 
          ? tagScores.reduce((sum, score) => sum + score, 0) / tagScores.length 
          : 0;
        score += avgTagScore * 0.2;
      }
      
      return {
        id: course.id,
        type: RecommendationType.COURSE,
        title: course.title,
        description: course.description,
        score,
        metadata: {
          category: course.category,
          level: course.level,
          tags: course.tags,
        },
      };
    });
    
    // Trier les cours par score et limiter le nombre de résultats
    return scoredCourses
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les retraites recommandées pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param userProfile Profil de l'utilisateur
   * @param options Options de recommandation
   * @returns Liste des retraites recommandées
   */
  private async getRecommendedRetreats(
    userId: string,
    userProfile: Record<string, any>,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Simuler la récupération des retraites
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    const retreats = Array.from({ length: 20 }, (_, i) => ({
      id: `retreat-${i + 1}`,
      title: `Retraite ${i + 1}`,
      description: `Description de la retraite ${i + 1}`,
      category: ['Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition'][i % 5],
      location: ['Bali', 'Thailand', 'France', 'Spain', 'Portugal', 'Italy', 'Greece', 'Morocco'][i % 8],
      duration: [3, 5, 7, 10, 14][i % 5],
      tags: [
        ['yoga', 'meditation', 'wellness'],
        ['fitness', 'nutrition', 'wellness'],
        ['meditation', 'nature', 'silence'],
        ['yoga', 'beach', 'relaxation'],
        ['nutrition', 'detox', 'health'],
      ][i % 5],
    }));
    
    // Calculer le score de chaque retraite
    const scoredRetreats = retreats.map(retreat => {
      let score = 0;
      
      // Score basé sur la catégorie
      if (retreat.category && userProfile.categories) {
        const categoryScore = userProfile.categories[retreat.category] || 0;
        score += categoryScore * 0.3;
      }
      
      // Score basé sur la localisation
      if (retreat.location && userProfile.locations) {
        const locationScore = userProfile.locations[retreat.location] || 0;
        score += locationScore * 0.3;
      }
      
      // Score basé sur la durée
      if (retreat.duration && userProfile.durations) {
        const durationScore = userProfile.durations[retreat.duration] || 0;
        score += durationScore * 0.2;
      }
      
      // Score basé sur les tags
      if (retreat.tags && userProfile.tags) {
        const tagScores = retreat.tags.map(tag => userProfile.tags[tag] || 0);
        const avgTagScore = tagScores.length > 0 
          ? tagScores.reduce((sum, score) => sum + score, 0) / tagScores.length 
          : 0;
        score += avgTagScore * 0.2;
      }
      
      return {
        id: retreat.id,
        type: RecommendationType.RETREAT,
        title: retreat.title,
        description: retreat.description,
        score,
        metadata: {
          category: retreat.category,
          location: retreat.location,
          duration: retreat.duration,
          tags: retreat.tags,
        },
      };
    });
    
    // Trier les retraites par score et limiter le nombre de résultats
    return scoredRetreats
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les partenaires recommandés pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param userProfile Profil de l'utilisateur
   * @param options Options de recommandation
   * @returns Liste des partenaires recommandés
   */
  private async getRecommendedPartners(
    userId: string,
    userProfile: Record<string, any>,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Simuler la récupération des partenaires
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    const partners = Array.from({ length: 15 }, (_, i) => ({
      id: `partner-${i + 1}`,
      title: `Partenaire ${i + 1}`,
      description: `Description du partenaire ${i + 1}`,
      specialty: ['Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition'][i % 5],
      location: ['Paris', 'London', 'Berlin', 'Madrid', 'Rome', 'Amsterdam', 'Brussels'][i % 7],
      tags: [
        ['yoga', 'meditation', 'wellness'],
        ['fitness', 'nutrition', 'wellness'],
        ['meditation', 'coaching', 'mindfulness'],
        ['yoga', 'pilates', 'stretching'],
        ['nutrition', 'detox', 'health'],
      ][i % 5],
      rating: (Math.random() * 2 + 3).toFixed(1),
    }));
    
    // Calculer le score de chaque partenaire
    const scoredPartners = partners.map(partner => {
      let score = 0;
      
      // Score basé sur la spécialité
      if (partner.specialty && userProfile.categories) {
        const specialtyScore = userProfile.categories[partner.specialty] || 0;
        score += specialtyScore * 0.4;
      }
      
      // Score basé sur la localisation
      if (partner.location && userProfile.locations) {
        const locationScore = userProfile.locations[partner.location] || 0;
        score += locationScore * 0.3;
      }
      
      // Score basé sur les tags
      if (partner.tags && userProfile.tags) {
        const tagScores = partner.tags.map(tag => userProfile.tags[tag] || 0);
        const avgTagScore = tagScores.length > 0 
          ? tagScores.reduce((sum, score) => sum + score, 0) / tagScores.length 
          : 0;
        score += avgTagScore * 0.2;
      }
      
      // Score basé sur la note
      score += parseFloat(partner.rating) * 0.1;
      
      return {
        id: partner.id,
        type: RecommendationType.PARTNER,
        title: partner.title,
        description: partner.description,
        score,
        metadata: {
          specialty: partner.specialty,
          location: partner.location,
          tags: partner.tags,
          rating: partner.rating,
        },
      };
    });
    
    // Trier les partenaires par score et limiter le nombre de résultats
    return scoredPartners
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les cours similaires à un cours
   * @param courseId ID du cours
   * @param courseFeatures Caractéristiques du cours
   * @param options Options de recommandation
   * @returns Liste des cours similaires
   */
  private async getSimilarCourses(
    courseId: string,
    courseFeatures: Record<string, any>,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Récupérer tous les cours sauf celui spécifié
    const allCourses = await this.prisma.course.findMany({
      where: {
        id: { not: courseId },
      },
    });
    
    // Calculer la similarité de chaque cours
    const similarCourses = allCourses.map(course => {
      const courseFeatures2 = {
        category: course.category,
        level: course.level,
        tags: course.tags || [],
      };
      
      const similarity = this.calculateSimilarity(courseFeatures, courseFeatures2);
      
      return {
        id: course.id,
        type: RecommendationType.COURSE,
        title: course.title,
        description: course.description,
        score: similarity,
        metadata: {
          category: course.category,
          level: course.level,
          tags: course.tags,
        },
      };
    });
    
    // Trier les cours par similarité et limiter le nombre de résultats
    return similarCourses
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les retraites similaires à une retraite
   * @param retreatId ID de la retraite
   * @param retreatFeatures Caractéristiques de la retraite
   * @param options Options de recommandation
   * @returns Liste des retraites similaires
   */
  private async getSimilarRetreats(
    retreatId: string,
    retreatFeatures: Record<string, any>,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Simuler la récupération des retraites
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    const retreats = Array.from({ length: 20 }, (_, i) => ({
      id: `retreat-${i + 1}`,
      title: `Retraite ${i + 1}`,
      description: `Description de la retraite ${i + 1}`,
      category: ['Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition'][i % 5],
      location: ['Bali', 'Thailand', 'France', 'Spain', 'Portugal', 'Italy', 'Greece', 'Morocco'][i % 8],
      duration: [3, 5, 7, 10, 14][i % 5],
      tags: [
        ['yoga', 'meditation', 'wellness'],
        ['fitness', 'nutrition', 'wellness'],
        ['meditation', 'nature', 'silence'],
        ['yoga', 'beach', 'relaxation'],
        ['nutrition', 'detox', 'health'],
      ][i % 5],
    })).filter(retreat => retreat.id !== retreatId);
    
    // Calculer la similarité de chaque retraite
    const similarRetreats = retreats.map(retreat => {
      const retreatFeatures2 = {
        category: retreat.category,
        location: retreat.location,
        duration: retreat.duration,
        tags: retreat.tags,
      };
      
      const similarity = this.calculateSimilarity(retreatFeatures, retreatFeatures2);
      
      return {
        id: retreat.id,
        type: RecommendationType.RETREAT,
        title: retreat.title,
        description: retreat.description,
        score: similarity,
        metadata: {
          category: retreat.category,
          location: retreat.location,
          duration: retreat.duration,
          tags: retreat.tags,
        },
      };
    });
    
    // Trier les retraites par similarité et limiter le nombre de résultats
    return similarRetreats
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère les partenaires similaires à un partenaire
   * @param partnerId ID du partenaire
   * @param partnerFeatures Caractéristiques du partenaire
   * @param options Options de recommandation
   * @returns Liste des partenaires similaires
   */
  private async getSimilarPartners(
    partnerId: string,
    partnerFeatures: Record<string, any>,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;
    
    // Simuler la récupération des partenaires
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    const partners = Array.from({ length: 15 }, (_, i) => ({
      id: `partner-${i + 1}`,
      title: `Partenaire ${i + 1}`,
      description: `Description du partenaire ${i + 1}`,
      specialty: ['Yoga', 'Meditation', 'Wellness', 'Fitness', 'Nutrition'][i % 5],
      location: ['Paris', 'London', 'Berlin', 'Madrid', 'Rome', 'Amsterdam', 'Brussels'][i % 7],
      tags: [
        ['yoga', 'meditation', 'wellness'],
        ['fitness', 'nutrition', 'wellness'],
        ['meditation', 'coaching', 'mindfulness'],
        ['yoga', 'pilates', 'stretching'],
        ['nutrition', 'detox', 'health'],
      ][i % 5],
    })).filter(partner => partner.id !== partnerId);
    
    // Calculer la similarité de chaque partenaire
    const similarPartners = partners.map(partner => {
      const partnerFeatures2 = {
        specialty: partner.specialty,
        location: partner.location,
        tags: partner.tags,
      };
      
      const similarity = this.calculateSimilarity(partnerFeatures, partnerFeatures2);
      
      return {
        id: partner.id,
        type: RecommendationType.PARTNER,
        title: partner.title,
        description: partner.description,
        score: similarity,
        metadata: {
          specialty: partner.specialty,
          location: partner.location,
          tags: partner.tags,
        },
      };
    });
    
    // Trier les partenaires par similarité et limiter le nombre de résultats
    return similarPartners
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }
}
