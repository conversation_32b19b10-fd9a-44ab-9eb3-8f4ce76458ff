import { Test, TestingModule } from '@nestjs/testing';
import { ContextualRecommendationService } from './contextual-recommendation.service';
import { ContentBasedService } from './content-based.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { Logger } from '@nestjs/common';

describe('ContextualRecommendationService', () => {
  let service: ContextualRecommendationService;
  let contentBasedService: ContentBasedService;
  let prismaService: PrismaService;

  // Mock des données
  const mockUserProfile = {
    userId: 'user1',
    location: {
      country: 'France',
      city: 'Paris'
    },
    preferences: {
      categories: ['yoga', 'meditation']
    }
  };

  const mockBaseRecommendations = [
    {
      id: 'item1',
      type: RecommendationType.RETREAT,
      title: 'Retreat 1',
      description: 'Description 1',
      score: 0.8,
      sources: ['content-based'],
      metadata: {
        category: 'yoga',
        season: 'SUMMER',
        location: {
          country: 'France',
          city: 'Nice'
        }
      }
    },
    {
      id: 'item2',
      type: RecommendationType.RETREAT,
      title: 'Retreat 2',
      description: 'Description 2',
      score: 0.7,
      sources: ['content-based'],
      metadata: {
        category: 'meditation',
        season: 'WINTER',
        location: {
          country: 'Switzerland',
          city: 'Zurich'
        }
      }
    },
    {
      id: 'item3',
      type: RecommendationType.COURSE,
      title: 'Course 1',
      description: 'Description 3',
      score: 0.9,
      sources: ['content-based'],
      metadata: {
        category: 'Meditation',
        tags: ['beginner', 'morning']
      }
    }
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContextualRecommendationService,
        {
          provide: ContentBasedService,
          useValue: {
            getRecommendations: jest.fn().mockResolvedValue(mockBaseRecommendations),
            countRecommendations: jest.fn().mockResolvedValue(mockBaseRecommendations.length)
          }
        },
        {
          provide: PrismaService,
          useValue: {
            userProfile: {
              findUnique: jest.fn().mockResolvedValue(mockUserProfile)
            }
          }
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn()
          }
        }
      ],
    }).compile();

    service = module.get<ContextualRecommendationService>(ContextualRecommendationService);
    contentBasedService = module.get<ContentBasedService>(ContentBasedService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Espionner les méthodes privées
    jest.spyOn<any, any>(service, 'getUserContext').mockResolvedValue({
      location: {
        country: 'France',
        city: 'Paris'
      },
      season: 'SUMMER',
      dayOfWeek: 1,
      timeOfDay: 9,
      device: 'DESKTOP'
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRecommendations', () => {
    it('should return contextually filtered and scored recommendations', async () => {
      const recommendations = await service.getRecommendations(
        'user1',
        RecommendationType.RETREAT,
        { limit: 2 }
      );

      // Vérifications
      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeLessThanOrEqual(2);
      
      // Vérifier que les sources incluent 'contextual'
      recommendations.forEach(rec => {
        expect(rec.sources).toContain('contextual');
      });
      
      // Vérifier que les recommandations sont triées par score
      for (let i = 0; i < recommendations.length - 1; i++) {
        expect(recommendations[i].score).toBeGreaterThanOrEqual(recommendations[i + 1].score);
      }
      
      // Vérifier que les recommandations contextuelles sont priorisées
      // (par exemple, les retraites en été en France devraient avoir un score plus élevé)
      const summerFranceRetreat = recommendations.find(
        r => r.metadata.season === 'SUMMER' && r.metadata.location?.country === 'France'
      );
      
      if (summerFranceRetreat && recommendations.length > 1) {
        const otherRetreat = recommendations.find(r => r.id !== summerFranceRetreat.id);
        if (otherRetreat) {
          expect(summerFranceRetreat.score).toBeGreaterThanOrEqual(otherRetreat.score);
        }
      }
    });

    it('should handle empty base recommendations', async () => {
      // Simuler aucune recommandation de base
      (contentBasedService.getRecommendations as jest.Mock).mockResolvedValueOnce([]);
      
      const recommendations = await service.getRecommendations(
        'user1',
        RecommendationType.COURSE
      );
      
      // Vérifications
      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBe(0);
    });
    
    it('should apply contextual boost for meditation courses in the morning', async () => {
      // Simuler le matin (8h)
      jest.spyOn<any, any>(service, 'getUserContext').mockResolvedValueOnce({
        location: {
          country: 'France',
          city: 'Paris'
        },
        season: 'SUMMER',
        dayOfWeek: 1,
        timeOfDay: 8, // Matin
        device: 'DESKTOP'
      });
      
      const recommendations = await service.getRecommendations(
        'user1',
        RecommendationType.COURSE
      );
      
      // Vérifier que les cours de méditation ont un boost contextuel
      const meditationCourse = recommendations.find(
        r => r.metadata.category === 'Meditation'
      );
      
      if (meditationCourse) {
        expect(meditationCourse.contextualBoost).toBeGreaterThan(0);
      }
    });
  });

  describe('countRecommendations', () => {
    it('should return the count from the content-based service', async () => {
      const count = await service.countRecommendations(
        'user1',
        RecommendationType.RETREAT
      );
      
      expect(count).toBe(mockBaseRecommendations.length);
      expect(contentBasedService.countRecommendations).toHaveBeenCalledWith(
        'user1',
        RecommendationType.RETREAT,
        expect.any(Object)
      );
    });
  });
});
