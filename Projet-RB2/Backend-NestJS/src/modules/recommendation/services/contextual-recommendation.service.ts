import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { ContentBasedService } from './content-based.service';

/**
 * Service de recommandation contextuelle
 * Adapte les recommandations au contexte de l'utilisateur (saison, localisation, etc.)
 */
@Injectable()
export class ContextualRecommendationService {
  private readonly logger = new Logger(ContextualRecommendationService.name);
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly contentBasedService: ContentBasedService,
  ) {}
  
  /**
   * Génère des recommandations contextuelles pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations contextuelles de type ${type} pour l'utilisateur ${userId}`);
      
      // Récupérer le contexte de l'utilisateur
      const context = options.context || await this.getUserContext(userId);
      
      // Récupérer les recommandations de base (basées sur le contenu)
      const baseRecommendations = await this.contentBasedService.getRecommendations(
        userId,
        type,
        {
          ...options,
          limit: (options.limit || 10) * 3, // Récupérer plus de recommandations pour avoir plus de choix
        },
      );
      
      if (baseRecommendations.length === 0) {
        this.logger.log(`Aucune recommandation de base trouvée pour l'utilisateur ${userId}`);
        return [];
      }
      
      // Appliquer les filtres contextuels
      const filteredRecommendations = this.applyContextualFilters(baseRecommendations, context, type);
      
      // Recalculer les scores en fonction du contexte
      const scoredRecommendations = this.applyContextualScoring(filteredRecommendations, context, type);
      
      // Trier les recommandations par score et limiter le nombre de résultats
      const limit = options.limit || 10;
      return scoredRecommendations
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
        .map(recommendation => ({
          ...recommendation,
          sources: [...(recommendation.sources || []), 'contextual'],
        }));
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de recommandations contextuelles: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Compte le nombre de recommandations disponibles
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Nombre de recommandations
   */
  async countRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ): Promise<number> {
    return this.contentBasedService.countRecommendations(userId, type, options);
  }
  
  /**
   * Récupère le contexte de l'utilisateur
   * @param userId ID de l'utilisateur
   * @returns Contexte de l'utilisateur
   */
  private async getUserContext(userId: string) {
    // Récupérer la date actuelle
    const now = new Date();
    const month = now.getMonth();
    const hour = now.getHours();
    const dayOfWeek = now.getDay();
    
    // Déterminer la saison
    let season: 'SPRING' | 'SUMMER' | 'FALL' | 'WINTER';
    if (month >= 2 && month <= 4) {
      season = 'SPRING';
    } else if (month >= 5 && month <= 7) {
      season = 'SUMMER';
    } else if (month >= 8 && month <= 10) {
      season = 'FALL';
    } else {
      season = 'WINTER';
    }
    
    // Récupérer la localisation de l'utilisateur
    const userProfile = await this.prisma.userProfile.findUnique({
      where: { userId },
      select: {
        location: true,
        preferences: true,
      },
    });
    
    // Construire le contexte
    return {
      location: userProfile?.location || {},
      season,
      dayOfWeek,
      timeOfDay: hour,
      device: 'DESKTOP', // Par défaut, à remplacer par la détection réelle
    };
  }
  
  /**
   * Applique des filtres contextuels aux recommandations
   * @param recommendations Recommandations de base
   * @param context Contexte de l'utilisateur
   * @param type Type de recommandation
   * @returns Recommandations filtrées
   */
  private applyContextualFilters(recommendations: any[], context: any, type: RecommendationType) {
    // Filtrer les recommandations en fonction du contexte
    return recommendations.filter(recommendation => {
      const metadata = recommendation.metadata || {};
      
      // Filtrer par saison si applicable
      if (type === RecommendationType.RETREAT && metadata.season) {
        if (metadata.season !== context.season) {
          return false;
        }
      }
      
      // Filtrer par localisation si applicable
      if (context.location && context.location.country && metadata.location) {
        // Priorité aux retraites locales, mais ne pas exclure complètement les autres
        if (metadata.location.country !== context.location.country) {
          // Garder quand même certaines recommandations non locales (20%)
          if (Math.random() > 0.2) {
            return false;
          }
        }
      }
      
      return true;
    });
  }
  
  /**
   * Applique un scoring contextuel aux recommandations
   * @param recommendations Recommandations filtrées
   * @param context Contexte de l'utilisateur
   * @param type Type de recommandation
   * @returns Recommandations avec scores ajustés
   */
  private applyContextualScoring(recommendations: any[], context: any, type: RecommendationType) {
    // Ajuster les scores en fonction du contexte
    return recommendations.map(recommendation => {
      const metadata = recommendation.metadata || {};
      let contextualBoost = 0;
      
      // Boost par saison
      if (type === RecommendationType.RETREAT && metadata.season === context.season) {
        contextualBoost += 0.2;
      }
      
      // Boost par localisation
      if (context.location && context.location.country && metadata.location) {
        if (metadata.location.country === context.location.country) {
          contextualBoost += 0.15;
        }
      }
      
      // Boost par jour de la semaine (exemple: cours de yoga le weekend)
      if (type === RecommendationType.COURSE && metadata.category === 'Yoga' && (context.dayOfWeek === 0 || context.dayOfWeek === 6)) {
        contextualBoost += 0.1;
      }
      
      // Boost par heure de la journée (exemple: méditation le matin ou le soir)
      if (type === RecommendationType.COURSE && metadata.category === 'Meditation') {
        if (context.timeOfDay < 10 || context.timeOfDay > 20) {
          contextualBoost += 0.1;
        }
      }
      
      // Appliquer le boost contextuel
      return {
        ...recommendation,
        score: recommendation.score * (1 + contextualBoost),
        contextualBoost,
      };
    });
  }
}
