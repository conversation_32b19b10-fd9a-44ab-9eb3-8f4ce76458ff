import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { SchedulerRegistry } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import { ContinuousLearningService } from './continuous-learning.service';
import { RecommendationType } from '../enums/recommendation-type.enum';

describe('ContinuousLearningService', () => {
  let service: ContinuousLearningService;
  let prismaService: PrismaService;
  let configService: ConfigService;

  const mockPrismaService = {
    userModel: {
      findMany: jest.fn(),
      upsert: jest.fn(),
    },
    userInteraction: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
    learningEvent: {
      create: jest.fn(),
      findMany: jest.fn(),
    },
    item: {
      findMany: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue: any) => {
      const config = {
        'recommendation.continuousLearning.learningRate': 0.1,
        'recommendation.continuousLearning.forgettingFactor': 0.95,
        'recommendation.continuousLearning.changeDetectionThreshold': 0.3,
        'recommendation.continuousLearning.recentInteractionsWindow': 24,
        'recommendation.continuousLearning.minInteractionsForUpdate': 5,
        'recommendation.continuousLearning.modelUpdateInterval': 15,
        'recommendation.continuousLearning.interactionWeights': {
          VIEW: 0.1,
          LIKE: 0.5,
          DISLIKE: -0.5,
          BOOKMARK: 0.7,
          SHARE: 0.8,
          PURCHASE: 1.0,
          RECOMMENDATION_VIEW: 0.05,
        },
        'recommendation.continuousLearning.enableOutlierDetection': true,
        'recommendation.continuousLearning.outlierDetectionThreshold': 2.5,
      };
      return config[key] || defaultValue;
    }),
  };

  const mockSchedulerRegistry = {
    addInterval: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContinuousLearningService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: SchedulerRegistry,
          useValue: mockSchedulerRegistry,
        },
      ],
    }).compile();

    service = module.get<ContinuousLearningService>(ContinuousLearningService);
    prismaService = module.get<PrismaService>(PrismaService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should initialize the service and load user models', async () => {
      // Mock the loadUserModels method
      const loadUserModelsSpy = jest.spyOn(service as any, 'loadUserModels').mockResolvedValue(undefined);
      
      await service.onModuleInit();
      
      expect(loadUserModelsSpy).toHaveBeenCalled();
      expect(mockSchedulerRegistry.addInterval).toHaveBeenCalledWith('interactionQueueProcessor', expect.any(Number));
    });
  });

  describe('processInteraction', () => {
    it('should add an interaction to the queue', async () => {
      const userId = 'user123';
      const itemId = 'item123';
      const itemType = RecommendationType.RETREAT;
      const interactionType = 'VIEW';
      const metadata = { source: 'test' };
      
      // Access the private interactionQueue property
      const interactionQueue = (service as any).interactionQueue;
      
      await service.processInteraction(userId, itemId, itemType, interactionType, metadata);
      
      expect(interactionQueue.length).toBe(1);
      expect(interactionQueue[0]).toEqual({
        userId,
        itemId,
        itemType,
        interactionType,
        timestamp: expect.any(Date),
        metadata,
      });
    });
  });

  describe('getUserModel', () => {
    it('should return a user model if it exists', async () => {
      const userId = 'user123';
      const mockUserModel = {
        userId,
        lastUpdated: new Date(),
        preferences: {},
        categoryInterests: {},
        tagInterests: {},
        recentInteractions: [],
        context: {},
        metrics: {
          precision: 0,
          recall: 0,
          f1Score: 0,
          updates: 0,
        },
      };
      
      // Set the user model in the private userModels map
      (service as any).userModels.set(userId, mockUserModel);
      
      const result = await service.getUserModel(userId);
      
      expect(result).toEqual(mockUserModel);
    });
    
    it('should return null if the user model does not exist', async () => {
      const userId = 'nonexistent';
      
      const result = await service.getUserModel(userId);
      
      expect(result).toBeNull();
    });
  });

  describe('getLearningMetrics', () => {
    it('should return learning metrics for a user', async () => {
      const userId = 'user123';
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      
      const mockUserModel = {
        userId,
        lastUpdated: new Date(),
        preferences: {},
        categoryInterests: {},
        tagInterests: {},
        recentInteractions: [],
        context: {},
        metrics: {
          precision: 0.8,
          recall: 0.7,
          f1Score: 0.75,
          updates: 10,
        },
      };
      
      // Set the user model in the private userModels map
      (service as any).userModels.set(userId, mockUserModel);
      
      // Mock the prisma service responses
      mockPrismaService.learningEvent.findMany.mockResolvedValue([
        {
          id: 'event1',
          userId,
          eventType: 'model_update',
          data: {},
          estimatedImpact: 0.5,
          timestamp: new Date('2023-01-15'),
        },
        {
          id: 'event2',
          userId,
          eventType: 'behavior_change',
          data: { description: 'Change in category preferences' },
          estimatedImpact: 0.3,
          timestamp: new Date('2023-01-20'),
        },
      ]);
      
      mockPrismaService.userInteraction.findMany.mockResolvedValue([
        {
          userId,
          itemId: 'item1',
          itemType: 'RETREAT',
          interactionType: 'VIEW',
          metadata: {},
          createdAt: new Date('2023-01-10'),
        },
        {
          userId,
          itemId: 'item2',
          itemType: 'COURSE',
          interactionType: 'LIKE',
          metadata: {},
          createdAt: new Date('2023-01-15'),
        },
      ]);
      
      const result = await service.getLearningMetrics(userId, startDate, endDate);
      
      expect(result).toBeDefined();
      expect(result?.userId).toBe(userId);
      expect(result?.period.start).toEqual(startDate);
      expect(result?.period.end).toEqual(endDate);
      expect(result?.modelUpdates).toBe(1);
      expect(result?.detectedChanges.length).toBe(1);
      expect(result?.interactionStats.total).toBe(2);
    });
    
    it('should return null if the user model does not exist', async () => {
      const userId = 'nonexistent';
      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      
      const result = await service.getLearningMetrics(userId, startDate, endDate);
      
      expect(result).toBeNull();
    });
  });
});
