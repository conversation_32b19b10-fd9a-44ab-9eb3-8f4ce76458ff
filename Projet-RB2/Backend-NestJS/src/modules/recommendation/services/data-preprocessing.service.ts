import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  RawInteractionData,
  ProcessedFeatureData,
  DataPreprocessingConfig
} from '../interfaces/continuous-learning-pipeline.interface';

/**
 * Service for preprocessing data for the continuous learning pipeline
 */
@Injectable()
export class DataPreprocessingService {
  private readonly logger = new Logger(DataPreprocessingService.name);
  private defaultConfig: DataPreprocessingConfig = {
    cleansingOptions: {
      removeOutliers: true,
      fillMissingValues: true,
      normalizeFeatures: true,
    },
    samplingOptions: {
      balanceClasses: true,
      undersampleMajorityClass: false,
      oversampleMinorityClass: true,
    },
    transformationOptions: {
      applyPCA: false,
      applyScaling: true,
      applyEncoding: true,
    },
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // Load configuration from environment or use defaults
    const configFromEnv = this.configService.get<DataPreprocessingConfig>('PREPROCESSING_CONFIG');
    if (configFromEnv) {
      this.defaultConfig = {
        ...this.defaultConfig,
        ...configFromEnv,
      };
    }
  }

  /**
   * Preprocess raw interaction data for model training
   * @param rawData Raw interaction data
   * @param config Preprocessing configuration
   * @returns Processed feature data
   */
  async preprocessData(
    rawData: RawInteractionData[],
    config?: Partial<DataPreprocessingConfig>,
  ): Promise<ProcessedFeatureData[]> {
    try {
      this.logger.log(`Preprocessing ${rawData.length} raw interaction records`);

      // Merge provided config with defaults
      const mergedConfig: DataPreprocessingConfig = {
        ...this.defaultConfig,
        ...config,
      };

      // Apply data cleansing
      let cleanedData = rawData;
      if (mergedConfig.cleansingOptions.removeOutliers) {
        cleanedData = this.removeOutliers(cleanedData);
      }

      if (mergedConfig.cleansingOptions.fillMissingValues) {
        cleanedData = this.fillMissingValues(cleanedData);
      }

      // Extract features from cleaned data
      let processedData = await this.extractFeatures(cleanedData);

      // Apply transformations
      if (mergedConfig.transformationOptions.applyScaling) {
        processedData = this.applyScaling(processedData);
      }

      if (mergedConfig.transformationOptions.applyEncoding) {
        processedData = this.applyEncoding(processedData);
      }

      if (mergedConfig.transformationOptions.applyPCA) {
        processedData = this.applyPCA(processedData);
      }

      // Apply sampling strategies
      if (mergedConfig.samplingOptions.balanceClasses) {
        if (mergedConfig.samplingOptions.undersampleMajorityClass) {
          processedData = this.undersampleMajorityClass(processedData);
        }

        if (mergedConfig.samplingOptions.oversampleMinorityClass) {
          processedData = this.oversampleMinorityClass(processedData);
        }
      }

      this.logger.log(`Preprocessing complete. Generated ${processedData.length} processed records`);

      // Emit event for monitoring
      this.eventEmitter.emit('data.preprocessing.completed', {
        inputSize: rawData.length,
        outputSize: processedData.length,
        config: mergedConfig,
        timestamp: new Date(),
      });

      return processedData;
    } catch (error) {
      this.logger.error(`Error preprocessing data: ${error.message}`);

      // Emit error event
      this.eventEmitter.emit('data.preprocessing.error', {
        error: error.message,
        timestamp: new Date(),
      });

      throw error;
    }
  }

  /**
   * Remove outliers from the dataset
   * @param data Raw interaction data
   * @returns Cleaned data without outliers
   */
  private removeOutliers(data: RawInteractionData[]): RawInteractionData[] {
    this.logger.debug('Removing outliers from dataset');

    // Implementation of outlier detection and removal
    // For now, we'll use a simple approach based on timestamp
    // In a real implementation, this would use statistical methods

    const now = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(now.getFullYear() - 1);

    return data.filter(item => {
      // Filter out interactions older than one year
      return item.timestamp >= oneYearAgo;
    });
  }

  /**
   * Fill missing values in the dataset
   * @param data Raw interaction data
   * @returns Data with filled missing values
   */
  private fillMissingValues(data: RawInteractionData[]): RawInteractionData[] {
    this.logger.debug('Filling missing values in dataset');

    // Implementation of missing value imputation
    return data.map(item => {
      // Ensure metadata exists
      if (!item.metadata) {
        item.metadata = {};
      }

      // Ensure timestamp exists
      if (!item.timestamp) {
        item.timestamp = new Date();
      }

      return item;
    });
  }

  /**
   * Extract features from raw data
   * @param data Cleaned raw data
   * @returns Processed feature data
   */
  private async extractFeatures(data: RawInteractionData[]): Promise<ProcessedFeatureData[]> {
    this.logger.debug('Extracting features from cleaned data');

    const processedData: ProcessedFeatureData[] = [];

    for (const item of data) {
      // Get user features
      const userFeatures = await this.getUserFeatures(item.userId);

      // Get item features
      const itemFeatures = await this.getItemFeatures(item.itemId, item.itemType);

      // Get interaction features
      const interactionFeatures = this.getInteractionFeatures(item);

      // Combine all features
      const features = {
        ...userFeatures,
        ...itemFeatures,
        ...interactionFeatures,
      };

      // Determine label based on interaction type
      const label = this.getLabelFromInteraction(item.interactionType);

      processedData.push({
        userId: item.userId,
        itemId: item.itemId,
        itemType: item.itemType,
        features,
        label,
        timestamp: item.timestamp,
      });
    }

    return processedData;
  }

  /**
   * Get features related to a user
   * @param userId User ID
   * @returns User features
   */
  private async getUserFeatures(userId: string): Promise<Record<string, number>> {
    // In a real implementation, this would fetch user data from the database
    // and extract relevant features

    // For now, return placeholder features
    return {
      user_activity_level: Math.random(), // Placeholder
      user_preference_strength: Math.random(), // Placeholder
    };
  }

  /**
   * Get features related to an item
   * @param itemId Item ID
   * @param itemType Item type
   * @returns Item features
   */
  private async getItemFeatures(itemId: string, itemType: string): Promise<Record<string, number>> {
    // In a real implementation, this would fetch item data from the database
    // and extract relevant features

    // For now, return placeholder features
    return {
      item_popularity: Math.random(), // Placeholder
      item_recency: Math.random(), // Placeholder
    };
  }

  /**
   * Get features related to the interaction
   * @param interaction Interaction data
   * @returns Interaction features
   */
  private getInteractionFeatures(interaction: RawInteractionData): Record<string, number> {
    // Extract features from the interaction itself

    // For now, return placeholder features
    return {
      interaction_recency: this.calculateRecency(interaction.timestamp),
      interaction_strength: this.getInteractionStrength(interaction.interactionType),
    };
  }

  /**
   * Calculate recency score based on timestamp
   * @param timestamp Interaction timestamp
   * @returns Recency score between 0 and 1
   */
  private calculateRecency(timestamp: Date): number {
    const now = new Date();
    const diffInDays = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60 * 24);

    // Exponential decay function: more recent = closer to 1
    return Math.exp(-diffInDays / 30); // 30-day half-life
  }

  /**
   * Get interaction strength based on type
   * @param interactionType Type of interaction
   * @returns Strength score between 0 and 1
   */
  private getInteractionStrength(interactionType: string): number {
    // Map interaction types to strength scores
    const strengthMap = {
      'VIEW': 0.2,
      'CLICK': 0.4,
      'BOOKMARK': 0.6,
      'PURCHASE': 0.8,
      'REVIEW': 0.9,
      'SHARE': 0.7,
    };

    return strengthMap[interactionType] || 0.5;
  }

  /**
   * Get label value from interaction type
   * @param interactionType Type of interaction
   * @returns Label value (typically 0 or 1 for binary classification)
   */
  private getLabelFromInteraction(interactionType: string): number {
    // Map interaction types to label values
    // For binary classification: 1 = positive, 0 = negative
    const positiveInteractions = ['BOOKMARK', 'PURCHASE', 'REVIEW', 'SHARE', 'LIKE'];

    return positiveInteractions.includes(interactionType) ? 1 : 0;
  }

  /**
   * Apply scaling to features
   * @param data Processed feature data
   * @returns Data with scaled features
   */
  private applyScaling(data: ProcessedFeatureData[]): ProcessedFeatureData[] {
    this.logger.debug('Applying scaling to features');

    // Calculate min and max for each feature
    const featureStats = this.calculateFeatureStats(data);

    // Apply min-max scaling to each feature
    return data.map(item => {
      const scaledFeatures = { ...item.features };

      for (const [feature, value] of Object.entries(scaledFeatures)) {
        if (typeof value === 'number') {
          const min = featureStats[feature]?.min || 0;
          const max = featureStats[feature]?.max || 1;

          // Avoid division by zero
          if (max > min) {
            scaledFeatures[feature] = (value - min) / (max - min);
          } else {
            scaledFeatures[feature] = 0;
          }
        }
      }

      return {
        ...item,
        features: scaledFeatures,
      };
    });
  }

  /**
   * Calculate statistics for each feature
   * @param data Processed feature data
   * @returns Statistics for each feature
   */
  private calculateFeatureStats(data: ProcessedFeatureData[]): Record<string, { min: number; max: number; mean: number; std: number }> {
    const stats: Record<string, { min: number; max: number; sum: number; sumSquared: number; count: number }> = {};

    // First pass: calculate min, max, sum
    for (const item of data) {
      for (const [feature, value] of Object.entries(item.features)) {
        if (typeof value === 'number') {
          if (!stats[feature]) {
            stats[feature] = {
              min: value,
              max: value,
              sum: value,
              sumSquared: value * value,
              count: 1,
            };
          } else {
            stats[feature].min = Math.min(stats[feature].min, value);
            stats[feature].max = Math.max(stats[feature].max, value);
            stats[feature].sum += value;
            stats[feature].sumSquared += value * value;
            stats[feature].count += 1;
          }
        }
      }
    }

    // Calculate mean and standard deviation
    const finalStats: Record<string, { min: number; max: number; mean: number; std: number }> = {};

    for (const [feature, stat] of Object.entries(stats)) {
      const mean = stat.sum / stat.count;
      const variance = (stat.sumSquared / stat.count) - (mean * mean);
      const std = Math.sqrt(Math.max(0, variance)); // Ensure non-negative

      finalStats[feature] = {
        min: stat.min,
        max: stat.max,
        mean,
        std,
      };
    }

    return finalStats;
  }

  /**
   * Apply encoding to categorical features
   * @param data Processed feature data
   * @returns Data with encoded features
   */
  private applyEncoding(data: ProcessedFeatureData[]): ProcessedFeatureData[] {
    this.logger.debug('Applying encoding to categorical features');

    // For now, we'll assume all features are numerical
    // In a real implementation, this would identify categorical features
    // and apply one-hot encoding or other encoding methods

    return data;
  }

  /**
   * Apply Principal Component Analysis (PCA)
   * @param data Processed feature data
   * @returns Data with PCA applied
   */
  private applyPCA(data: ProcessedFeatureData[]): ProcessedFeatureData[] {
    this.logger.debug('Applying PCA to features');

    // PCA implementation would go here
    // This is a complex operation that would typically use a library
    // For now, we'll return the data unchanged

    return data;
  }

  /**
   * Undersample the majority class to balance the dataset
   * @param data Processed feature data
   * @returns Balanced dataset
   */
  private undersampleMajorityClass(data: ProcessedFeatureData[]): ProcessedFeatureData[] {
    this.logger.debug('Undersampling majority class');

    // Count instances of each class
    const classCounts = data.reduce((counts, item) => {
      const label = item.label.toString();
      counts[label] = (counts[label] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    // Find minority class
    let minorityClass = '';
    let minorityCount = Infinity;

    for (const [label, count] of Object.entries(classCounts)) {
      if (count < minorityCount) {
        minorityCount = count;
        minorityClass = label;
      }
    }

    // Group data by class
    const dataByClass: Record<string, ProcessedFeatureData[]> = {};

    for (const item of data) {
      const label = item.label.toString();
      if (!dataByClass[label]) {
        dataByClass[label] = [];
      }
      dataByClass[label].push(item);
    }

    // Undersample majority classes to match minority class
    const balancedData: ProcessedFeatureData[] = [];

    for (const [label, items] of Object.entries(dataByClass)) {
      // If this is the minority class, keep all instances
      if (label === minorityClass) {
        balancedData.push(...items);
        continue;
      }

      // Otherwise, randomly sample to match minority class size
      const shuffled = [...items].sort(() => 0.5 - Math.random());
      balancedData.push(...shuffled.slice(0, minorityCount));
    }

    return balancedData;
  }

  /**
   * Oversample the minority class to balance the dataset
   * @param data Processed feature data
   * @returns Balanced dataset
   */
  private oversampleMinorityClass(data: ProcessedFeatureData[]): ProcessedFeatureData[] {
    this.logger.debug('Oversampling minority class');

    // Count instances of each class
    const classCounts = data.reduce((counts, item) => {
      const label = item.label.toString();
      counts[label] = (counts[label] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    // Find majority class
    let majorityClass = '';
    let majorityCount = 0;

    for (const [label, count] of Object.entries(classCounts)) {
      if (count > majorityCount) {
        majorityCount = count;
        majorityClass = label;
      }
    }

    // Group data by class
    const dataByClass: Record<string, ProcessedFeatureData[]> = {};

    for (const item of data) {
      const label = item.label.toString();
      if (!dataByClass[label]) {
        dataByClass[label] = [];
      }
      dataByClass[label].push(item);
    }

    // Oversample minority classes to match majority class
    const balancedData: ProcessedFeatureData[] = [];

    for (const [label, items] of Object.entries(dataByClass)) {
      // If this is the majority class, keep all instances
      if (label === majorityClass) {
        balancedData.push(...items);
        continue;
      }

      // Otherwise, oversample to match majority class size
      const itemsNeeded = majorityCount - items.length;

      // Add all original items
      balancedData.push(...items);

      // Add duplicates with small random variations
      for (let i = 0; i < itemsNeeded; i++) {
        const randomIndex = Math.floor(Math.random() * items.length);
        const originalItem = items[randomIndex];

        // Create a copy with slight variations in features
        const newItem = {
          ...originalItem,
          features: { ...originalItem.features },
        };

        // Add small random variations to features
        for (const [feature, value] of Object.entries(newItem.features)) {
          if (typeof value === 'number') {
            // Add random noise (±5%)
            newItem.features[feature] = value * (0.95 + Math.random() * 0.1);
          }
        }

        balancedData.push(newItem);
      }
    }

    return balancedData;
  }
}
