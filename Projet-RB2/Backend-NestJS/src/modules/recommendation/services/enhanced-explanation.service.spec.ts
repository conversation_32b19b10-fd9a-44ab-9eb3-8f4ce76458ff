import { Test, TestingModule } from '@nestjs/testing';
import { EnhancedExplanationService } from './enhanced-explanation.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { NotFoundException } from '@nestjs/common';

describe('EnhancedExplanationService', () => {
  let service: EnhancedExplanationService;

  const mockPrismaService = {
    retreat: {
      findUnique: jest.fn(),
    },
    course: {
      findUnique: jest.fn(),
    },
    video: {
      findUnique: jest.fn(),
    },
    article: {
      findUnique: jest.fn(),
    },
    event: {
      findUnique: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnhancedExplanationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<EnhancedExplanationService>(EnhancedExplanationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateEnhancedExplanation', () => {
    it('should throw NotFoundException if recommendation does not exist', async () => {
      // Mock the recommendation not found
      mockPrismaService.retreat.findUnique.mockResolvedValue(null);

      await expect(
        service.generateEnhancedExplanation('rec1', RecommendationType.RETREAT, 'user1'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should generate explanation without user profile if not found', async () => {
      // Mock the recommendation found
      mockPrismaService.retreat.findUnique.mockResolvedValue({
        id: 'rec1',
        title: 'Yoga Retreat',
        description: 'A relaxing yoga retreat',
      });

      // Mock user not found
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      const result = await service.generateEnhancedExplanation('rec1', RecommendationType.RETREAT, 'user1');

      expect(result).toBeDefined();
      expect(result.recommendationId).toBe('rec1');
      expect(result.recommendationType).toBe(RecommendationType.RETREAT);
      expect(result.generalExplanation).toBeDefined();
      expect(result.personalizedExplanation).toBeUndefined();
      expect(result.isPersonalized).toBe(false);
      expect(result.factors).toHaveLength(2); // Default factors
    });

    it('should generate personalized explanation with user profile', async () => {
      // Mock the recommendation found
      mockPrismaService.retreat.findUnique.mockResolvedValue({
        id: 'rec1',
        title: 'Yoga Retreat',
        description: 'A relaxing yoga retreat',
      });

      // Mock user found
      mockPrismaService.user.findUnique.mockResolvedValue({
        id: 'user1',
        firstName: 'John',
        lastName: 'Doe',
        interests: [
          { name: 'yoga' },
          { name: 'meditation' },
        ],
        preferences: {
          categories: ['wellness', 'fitness'],
        },
      });

      const result = await service.generateEnhancedExplanation('rec1', RecommendationType.RETREAT, 'user1');

      expect(result).toBeDefined();
      expect(result.recommendationId).toBe('rec1');
      expect(result.recommendationType).toBe(RecommendationType.RETREAT);
      expect(result.generalExplanation).toBeDefined();
      expect(result.personalizedExplanation).toBeDefined();
      expect(result.isPersonalized).toBe(true);
      expect(result.factors).toBeDefined();
    });

    it('should include visualizations when requested', async () => {
      // Mock the recommendation found
      mockPrismaService.retreat.findUnique.mockResolvedValue({
        id: 'rec1',
        title: 'Yoga Retreat',
        description: 'A relaxing yoga retreat',
      });

      const result = await service.generateEnhancedExplanation(
        'rec1',
        RecommendationType.RETREAT,
        'user1',
        { includeVisualizations: true },
      );

      expect(result).toBeDefined();
      expect(result.visualizations).toBeDefined();
      expect(result.visualizations.length).toBeGreaterThan(0);
    });

    it('should not include visualizations when not requested', async () => {
      // Mock the recommendation found
      mockPrismaService.retreat.findUnique.mockResolvedValue({
        id: 'rec1',
        title: 'Yoga Retreat',
        description: 'A relaxing yoga retreat',
      });

      const result = await service.generateEnhancedExplanation(
        'rec1',
        RecommendationType.RETREAT,
        'user1',
        { includeVisualizations: false },
      );

      expect(result).toBeDefined();
      expect(result.visualizations).toHaveLength(0);
    });

    it('should adjust detail level based on options', async () => {
      // Mock the recommendation found
      mockPrismaService.retreat.findUnique.mockResolvedValue({
        id: 'rec1',
        title: 'Yoga Retreat',
        description: 'A relaxing yoga retreat',
      });

      // Test BASIC level
      const basicResult = await service.generateEnhancedExplanation(
        'rec1',
        RecommendationType.RETREAT,
        'user1',
        { detailLevel: 'BASIC' },
      );

      expect(basicResult).toBeDefined();
      expect(basicResult.factors.length).toBeLessThanOrEqual(2);

      // Test DETAILED level
      const detailedResult = await service.generateEnhancedExplanation(
        'rec1',
        RecommendationType.RETREAT,
        'user1',
        { detailLevel: 'DETAILED' },
      );

      expect(detailedResult).toBeDefined();
      expect(detailedResult.factors.length).toBeGreaterThan(basicResult.factors.length);
    });

    it('should handle different recommendation types', async () => {
      // Mock different recommendation types
      mockPrismaService.course.findUnique.mockResolvedValue({
        id: 'course1',
        title: 'Yoga Course',
        description: 'A comprehensive yoga course',
      });

      const result = await service.generateEnhancedExplanation('course1', RecommendationType.COURSE, 'user1');

      expect(result).toBeDefined();
      expect(result.recommendationId).toBe('course1');
      expect(result.recommendationType).toBe(RecommendationType.COURSE);
    });
  });
});
