import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Profil d'utilisateur simulé
 */
export interface SimulatedUserProfile {
  /** Identifiant de l'utilisateur */
  id: string;
  
  /** Segment d'utilisateur */
  segment: string;
  
  /** Préférences */
  preferences: Record<string, number>;
  
  /** Comportement */
  behavior: {
    /** Probabilité de clic */
    clickProbability: number;
    /** Probabilité de conversion */
    conversionProbability: number;
    /** Temps moyen passé sur un élément (secondes) */
    averageTimeSpent: number;
    /** Nombre moyen d'interactions par session */
    interactionsPerSession: number;
  };
  
  /** Contexte */
  context?: Record<string, any>;
}

/**
 * Résultat de simulation
 */
export interface SimulationResult {
  /** Durée de la simulation (ms) */
  duration: number;
  
  /** Nombre total d'interactions */
  totalInteractions: number;
  
  /** Interactions par utilisateur */
  userInteractions: Record<string, any[]>;
  
  /** Métriques agrégées */
  aggregateMetrics: Record<string, number>;
  
  /** Métriques par segment */
  segmentMetrics: Record<string, Record<string, number>>;
}

/**
 * Service de simulation d'utilisateurs
 * Permet de générer des utilisateurs simulés et d'exécuter des simulations
 */
@Injectable()
export class UserSimulationService {
  private readonly logger = new Logger(UserSimulationService.name);
  
  constructor(private readonly configService: ConfigService) {
    this.logger.log('UserSimulationService initialized');
  }
  
  /**
   * Génère des utilisateurs simulés
   * @param count Nombre d'utilisateurs à générer
   * @param segments Segments d'utilisateurs
   * @returns Utilisateurs simulés
   */
  async generateUsers(
    count: number,
    segments?: string[],
  ): Promise<SimulatedUserProfile[]> {
    this.logger.log(`Generating ${count} simulated users`);
    
    const users: SimulatedUserProfile[] = [];
    const segmentsToUse = segments || ['new', 'casual', 'engaged', 'power'];
    
    for (let i = 0; i < count; i++) {
      const segmentIndex = Math.floor(Math.random() * segmentsToUse.length);
      const segment = segmentsToUse[segmentIndex];
      
      users.push(this.generateUserProfile(i.toString(), segment));
    }
    
    return users;
  }
  
  /**
   * Exécute une simulation
   * @param algorithmId Identifiant de l'algorithme
   * @param type Type de recommandation
   * @param users Utilisateurs simulés
   * @param options Options de simulation
   * @returns Résultat de la simulation
   */
  async runSimulation(
    algorithmId: string,
    type: string,
    users: SimulatedUserProfile[],
    options: any = {},
  ): Promise<SimulationResult> {
    this.logger.log(`Running simulation for algorithm ${algorithmId} with ${users.length} users`);
    
    const startTime = Date.now();
    
    // Initialiser les résultats
    const userInteractions: Record<string, any[]> = {};
    let totalInteractions = 0;
    
    // Simuler les interactions pour chaque utilisateur
    for (const user of users) {
      const interactions = await this.simulateUserInteractions(
        user,
        algorithmId,
        type,
        options,
      );
      
      userInteractions[user.id] = interactions;
      totalInteractions += interactions.length;
    }
    
    // Calculer les métriques agrégées
    const aggregateMetrics = this.calculateAggregateMetrics(userInteractions);
    
    // Calculer les métriques par segment
    const segmentMetrics = this.calculateSegmentMetrics(users, userInteractions);
    
    const endTime = Date.now();
    
    return {
      duration: endTime - startTime,
      totalInteractions,
      userInteractions,
      aggregateMetrics,
      segmentMetrics,
    };
  }
  
  /**
   * Génère un profil d'utilisateur simulé
   * @param id Identifiant de l'utilisateur
   * @param segment Segment d'utilisateur
   * @returns Profil d'utilisateur simulé
   */
  private generateUserProfile(id: string, segment: string): SimulatedUserProfile {
    // Générer des préférences aléatoires
    const preferences: Record<string, number> = {};
    const categories = ['yoga', 'meditation', 'wellness', 'fitness', 'nutrition', 'mindfulness'];
    
    for (const category of categories) {
      preferences[category] = Math.random();
    }
    
    // Définir le comportement en fonction du segment
    let behavior;
    
    switch (segment) {
      case 'new':
        behavior = {
          clickProbability: 0.1,
          conversionProbability: 0.01,
          averageTimeSpent: 10,
          interactionsPerSession: 3,
        };
        break;
      case 'casual':
        behavior = {
          clickProbability: 0.2,
          conversionProbability: 0.05,
          averageTimeSpent: 30,
          interactionsPerSession: 5,
        };
        break;
      case 'engaged':
        behavior = {
          clickProbability: 0.4,
          conversionProbability: 0.1,
          averageTimeSpent: 60,
          interactionsPerSession: 10,
        };
        break;
      case 'power':
        behavior = {
          clickProbability: 0.6,
          conversionProbability: 0.2,
          averageTimeSpent: 120,
          interactionsPerSession: 20,
        };
        break;
      default:
        behavior = {
          clickProbability: 0.3,
          conversionProbability: 0.05,
          averageTimeSpent: 30,
          interactionsPerSession: 5,
        };
    }
    
    return {
      id,
      segment,
      preferences,
      behavior,
      context: {
        device: Math.random() > 0.5 ? 'mobile' : 'desktop',
        time: ['morning', 'afternoon', 'evening'][Math.floor(Math.random() * 3)],
        location: ['home', 'work', 'travel'][Math.floor(Math.random() * 3)],
      },
    };
  }
  
  /**
   * Simule les interactions d'un utilisateur
   * @param user Profil d'utilisateur
   * @param algorithmId Identifiant de l'algorithme
   * @param type Type de recommandation
   * @param options Options de simulation
   * @returns Interactions simulées
   */
  private async simulateUserInteractions(
    user: SimulatedUserProfile,
    algorithmId: string,
    type: string,
    options: any,
  ): Promise<any[]> {
    const interactions = [];
    const interactionCount = Math.floor(
      user.behavior.interactionsPerSession * (0.8 + Math.random() * 0.4)
    );
    
    // Simuler les recommandations
    const recommendations = await this.simulateRecommendations(
      user,
      algorithmId,
      type,
      options,
    );
    
    // Simuler les interactions avec les recommandations
    for (let i = 0; i < Math.min(interactionCount, recommendations.length); i++) {
      const recommendation = recommendations[i];
      
      // Simuler un clic
      if (Math.random() < user.behavior.clickProbability) {
        interactions.push({
          type: 'click',
          itemId: recommendation.id,
          timestamp: new Date(),
          metadata: {
            position: i,
            timeSpent: Math.floor(user.behavior.averageTimeSpent * (0.5 + Math.random())),
          },
        });
        
        // Simuler une conversion
        if (Math.random() < user.behavior.conversionProbability) {
          interactions.push({
            type: 'conversion',
            itemId: recommendation.id,
            timestamp: new Date(),
            metadata: {
              value: Math.floor(50 + Math.random() * 200),
              conversionType: Math.random() > 0.5 ? 'booking' : 'inquiry',
            },
          });
        }
      }
    }
    
    return interactions;
  }
  
  /**
   * Simule des recommandations
   * @param user Profil d'utilisateur
   * @param algorithmId Identifiant de l'algorithme
   * @param type Type de recommandation
   * @param options Options de simulation
   * @returns Recommandations simulées
   */
  private async simulateRecommendations(
    user: SimulatedUserProfile,
    algorithmId: string,
    type: string,
    options: any,
  ): Promise<any[]> {
    // Dans une implémentation réelle, on appellerait le service de recommandation
    // Ici, on génère des recommandations simulées
    
    const recommendations = [];
    const count = options.limit || 10;
    
    for (let i = 0; i < count; i++) {
      recommendations.push({
        id: `item_${i}`,
        title: `Recommendation ${i}`,
        score: Math.random(),
        relevance: this.calculateRelevance(user.preferences),
      });
    }
    
    return recommendations.sort((a, b) => b.relevance - a.relevance);
  }
  
  /**
   * Calcule la pertinence d'une recommandation
   * @param preferences Préférences de l'utilisateur
   * @returns Score de pertinence
   */
  private calculateRelevance(preferences: Record<string, number>): number {
    // Simuler un score de pertinence basé sur les préférences
    const preferenceValues = Object.values(preferences);
    const avgPreference = preferenceValues.reduce((sum, val) => sum + val, 0) / preferenceValues.length;
    
    // Ajouter un peu de bruit
    return avgPreference * (0.7 + Math.random() * 0.6);
  }
  
  /**
   * Calcule les métriques agrégées
   * @param userInteractions Interactions par utilisateur
   * @returns Métriques agrégées
   */
  private calculateAggregateMetrics(userInteractions: Record<string, any[]>): Record<string, number> {
    let totalClicks = 0;
    let totalConversions = 0;
    let totalValue = 0;
    let totalInteractions = 0;
    
    for (const interactions of Object.values(userInteractions)) {
      totalInteractions += interactions.length;
      
      for (const interaction of interactions) {
        if (interaction.type === 'click') {
          totalClicks++;
        } else if (interaction.type === 'conversion') {
          totalConversions++;
          totalValue += interaction.metadata?.value || 0;
        }
      }
    }
    
    return {
      clickThroughRate: totalClicks / Object.keys(userInteractions).length,
      conversionRate: totalConversions / totalClicks || 0,
      averageValue: totalValue / totalConversions || 0,
      interactionsPerUser: totalInteractions / Object.keys(userInteractions).length,
    };
  }
  
  /**
   * Calcule les métriques par segment
   * @param users Utilisateurs simulés
   * @param userInteractions Interactions par utilisateur
   * @returns Métriques par segment
   */
  private calculateSegmentMetrics(
    users: SimulatedUserProfile[],
    userInteractions: Record<string, any[]>,
  ): Record<string, Record<string, number>> {
    const segmentInteractions: Record<string, any[]> = {};
    
    // Regrouper les interactions par segment
    for (const user of users) {
      const segment = user.segment;
      
      if (!segmentInteractions[segment]) {
        segmentInteractions[segment] = [];
      }
      
      segmentInteractions[segment].push(...(userInteractions[user.id] || []));
    }
    
    // Calculer les métriques pour chaque segment
    const segmentMetrics: Record<string, Record<string, number>> = {};
    
    for (const [segment, interactions] of Object.entries(segmentInteractions)) {
      const clicks = interactions.filter(i => i.type === 'click').length;
      const conversions = interactions.filter(i => i.type === 'conversion').length;
      const values = interactions
        .filter(i => i.type === 'conversion')
        .map(i => i.metadata?.value || 0);
      
      const totalValue = values.reduce((sum, val) => sum + val, 0);
      
      segmentMetrics[segment] = {
        clickThroughRate: clicks / users.filter(u => u.segment === segment).length,
        conversionRate: conversions / clicks || 0,
        averageValue: totalValue / conversions || 0,
        interactionsPerUser: interactions.length / users.filter(u => u.segment === segment).length,
      };
    }
    
    return segmentMetrics;
  }
}
