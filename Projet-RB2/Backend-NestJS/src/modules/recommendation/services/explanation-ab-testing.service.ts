import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { ExplanationVariantType } from '@prisma/client';
import { 
  CreateExplanationABTestDto, 
  UpdateExplanationABTestDto, 
  UpdateExplanationVariantDto,
  RecordExplanationABTestInteractionDto,
} from '../dto/explanation-ab-testing.dto';
import { 
  ExplanationABTestInterface, 
  ExplanationVariantInterface,
  ExplanationABTestAssignmentInterface,
  ExplanationABTestResultsInterface,
} from '../interfaces/explanation-ab-testing.interface';
import { ExplanationService } from './explanation.service';

/**
 * Service pour les tests A/B des explications
 */
@Injectable()
export class ExplanationABTestingService {
  private readonly logger = new Logger(ExplanationABTestingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly explanationService: ExplanationService,
  ) {}

  /**
   * Crée un nouveau test A/B d'explications
   * @param dto DTO de création du test
   * @returns Test créé
   */
  async createTest(dto: CreateExplanationABTestDto): Promise<ExplanationABTestInterface> {
    try {
      this.logger.log(`Création d'un nouveau test A/B d'explications: ${dto.name}`);
      
      // Vérifier que les dates sont valides
      const startDate = new Date(dto.startDate);
      const endDate = new Date(dto.endDate);
      
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new BadRequestException('Dates invalides');
      }
      
      if (startDate >= endDate) {
        throw new BadRequestException('La date de début doit être antérieure à la date de fin');
      }
      
      // Vérifier que la somme des allocations est égale à 1
      const totalAllocation = dto.variants.reduce((sum, variant) => sum + variant.allocation, 0);
      if (Math.abs(totalAllocation - 1) > 0.001) {
        throw new BadRequestException('La somme des allocations doit être égale à 1');
      }
      
      // Créer le test
      const test = await this.prisma.explanationABTest.create({
        data: {
          name: dto.name,
          description: dto.description,
          startDate,
          endDate,
          status: 'ACTIVE',
          variants: {
            create: dto.variants.map(variant => ({
              name: variant.name,
              description: variant.description,
              type: variant.type,
              configuration: variant.configuration,
              allocation: variant.allocation,
            })),
          },
        },
        include: {
          variants: true,
        },
      });
      
      return test;
    } catch (error) {
      this.logger.error(`Erreur lors de la création du test A/B: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère tous les tests A/B d'explications
   * @param status Filtre par statut
   * @returns Liste des tests
   */
  async getAllTests(status?: string): Promise<ExplanationABTestInterface[]> {
    try {
      const where = status ? { status } : {};
      
      const tests = await this.prisma.explanationABTest.findMany({
        where,
        include: {
          variants: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return tests;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des tests A/B: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère un test A/B d'explications par son ID
   * @param id ID du test
   * @returns Test
   */
  async getTestById(id: string): Promise<ExplanationABTestInterface> {
    try {
      const test = await this.prisma.explanationABTest.findUnique({
        where: { id },
        include: {
          variants: true,
        },
      });
      
      if (!test) {
        throw new NotFoundException(`Test A/B avec l'ID ${id} non trouvé`);
      }
      
      return test;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du test A/B ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour un test A/B d'explications
   * @param id ID du test
   * @param dto DTO de mise à jour du test
   * @returns Test mis à jour
   */
  async updateTest(id: string, dto: UpdateExplanationABTestDto): Promise<ExplanationABTestInterface> {
    try {
      // Vérifier que le test existe
      const existingTest = await this.getTestById(id);
      
      // Préparer les données de mise à jour
      const updateData: any = {};
      
      if (dto.name) updateData.name = dto.name;
      if (dto.description) updateData.description = dto.description;
      if (dto.status) updateData.status = dto.status;
      
      if (dto.startDate) {
        const startDate = new Date(dto.startDate);
        if (isNaN(startDate.getTime())) {
          throw new BadRequestException('Date de début invalide');
        }
        updateData.startDate = startDate;
      }
      
      if (dto.endDate) {
        const endDate = new Date(dto.endDate);
        if (isNaN(endDate.getTime())) {
          throw new BadRequestException('Date de fin invalide');
        }
        updateData.endDate = endDate;
      }
      
      // Vérifier que la date de début est antérieure à la date de fin
      if (updateData.startDate && updateData.endDate && updateData.startDate >= updateData.endDate) {
        throw new BadRequestException('La date de début doit être antérieure à la date de fin');
      } else if (updateData.startDate && !updateData.endDate && updateData.startDate >= existingTest.endDate) {
        throw new BadRequestException('La date de début doit être antérieure à la date de fin');
      } else if (!updateData.startDate && updateData.endDate && existingTest.startDate >= updateData.endDate) {
        throw new BadRequestException('La date de début doit être antérieure à la date de fin');
      }
      
      // Mettre à jour le test
      const updatedTest = await this.prisma.explanationABTest.update({
        where: { id },
        data: updateData,
        include: {
          variants: true,
        },
      });
      
      return updatedTest;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du test A/B ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour une variante d'explication
   * @param id ID de la variante
   * @param dto DTO de mise à jour de la variante
   * @returns Variante mise à jour
   */
  async updateVariant(id: string, dto: UpdateExplanationVariantDto): Promise<ExplanationVariantInterface> {
    try {
      // Vérifier que la variante existe
      const existingVariant = await this.prisma.explanationVariant.findUnique({
        where: { id },
      });
      
      if (!existingVariant) {
        throw new NotFoundException(`Variante avec l'ID ${id} non trouvée`);
      }
      
      // Préparer les données de mise à jour
      const updateData: any = {};
      
      if (dto.name) updateData.name = dto.name;
      if (dto.description) updateData.description = dto.description;
      if (dto.configuration) updateData.configuration = dto.configuration;
      if (dto.allocation !== undefined) updateData.allocation = dto.allocation;
      
      // Si l'allocation est modifiée, vérifier que la somme des allocations reste égale à 1
      if (dto.allocation !== undefined) {
        const variants = await this.prisma.explanationVariant.findMany({
          where: {
            testId: existingVariant.testId,
            id: {
              not: id,
            },
          },
        });
        
        const totalAllocation = variants.reduce((sum, variant) => sum + variant.allocation, 0) + dto.allocation;
        if (Math.abs(totalAllocation - 1) > 0.001) {
          throw new BadRequestException('La somme des allocations doit être égale à 1');
        }
      }
      
      // Mettre à jour la variante
      const updatedVariant = await this.prisma.explanationVariant.update({
        where: { id },
        data: updateData,
      });
      
      return updatedVariant;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la variante ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Supprime un test A/B d'explications
   * @param id ID du test
   * @returns Résultat de la suppression
   */
  async deleteTest(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // Vérifier que le test existe
      await this.getTestById(id);
      
      // Supprimer le test (les relations seront supprimées en cascade)
      await this.prisma.explanationABTest.delete({
        where: { id },
      });
      
      return {
        success: true,
        message: `Test A/B avec l'ID ${id} supprimé avec succès`,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du test A/B ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Assigne un utilisateur à une variante d'un test A/B
   * @param userId ID de l'utilisateur
   * @param testId ID du test
   * @returns Assignation
   */
  async assignUserToVariant(userId: string, testId: string): Promise<ExplanationABTestAssignmentInterface> {
    try {
      // Vérifier que le test existe et est actif
      const test = await this.prisma.explanationABTest.findUnique({
        where: { 
          id: testId,
          status: 'ACTIVE',
        },
        include: {
          variants: true,
        },
      });
      
      if (!test) {
        throw new NotFoundException(`Test A/B actif avec l'ID ${testId} non trouvé`);
      }
      
      // Vérifier si l'utilisateur est déjà assigné à ce test
      const existingAssignment = await this.prisma.explanationABTestAssignment.findUnique({
        where: {
          userId_testId: {
            userId,
            testId,
          },
        },
      });
      
      if (existingAssignment) {
        return existingAssignment;
      }
      
      // Sélectionner une variante en fonction des allocations
      const variant = this.selectVariantBasedOnAllocation(test.variants);
      
      // Créer l'assignation
      const assignment = await this.prisma.explanationABTestAssignment.create({
        data: {
          userId,
          testId,
          variantId: variant.id,
        },
      });
      
      return assignment;
    } catch (error) {
      this.logger.error(`Erreur lors de l'assignation de l'utilisateur ${userId} au test A/B ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sélectionne une variante en fonction des allocations
   * @param variants Liste des variantes
   * @returns Variante sélectionnée
   */
  private selectVariantBasedOnAllocation(variants: ExplanationVariantInterface[]): ExplanationVariantInterface {
    // Générer un nombre aléatoire entre 0 et 1
    const random = Math.random();
    
    // Sélectionner la variante en fonction des allocations
    let cumulativeAllocation = 0;
    
    for (const variant of variants) {
      cumulativeAllocation += variant.allocation;
      if (random <= cumulativeAllocation) {
        return variant;
      }
    }
    
    // Si aucune variante n'est sélectionnée (ce qui ne devrait pas arriver), retourner la première
    return variants[0];
  }

  /**
   * Récupère la variante assignée à un utilisateur pour un test A/B
   * @param userId ID de l'utilisateur
   * @param testId ID du test
   * @returns Variante assignée
   */
  async getUserVariant(userId: string, testId: string): Promise<ExplanationVariantInterface | null> {
    try {
      // Vérifier si l'utilisateur est assigné à ce test
      const assignment = await this.prisma.explanationABTestAssignment.findUnique({
        where: {
          userId_testId: {
            userId,
            testId,
          },
        },
        include: {
          variant: true,
        },
      });
      
      if (!assignment) {
        return null;
      }
      
      return assignment.variant;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de la variante de l'utilisateur ${userId} pour le test A/B ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enregistre une interaction avec un test A/B
   * @param userId ID de l'utilisateur
   * @param testId ID du test
   * @param dto DTO d'interaction
   * @returns Résultat de l'enregistrement
   */
  async recordInteraction(
    userId: string,
    testId: string,
    dto: RecordExplanationABTestInteractionDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Vérifier si l'utilisateur est assigné à ce test
      const assignment = await this.prisma.explanationABTestAssignment.findUnique({
        where: {
          userId_testId: {
            userId,
            testId,
          },
        },
      });
      
      if (!assignment) {
        throw new NotFoundException(`Utilisateur ${userId} non assigné au test A/B ${testId}`);
      }
      
      // Enregistrer l'interaction
      await this.prisma.explanationABTestInteraction.create({
        data: {
          userId,
          testId,
          variantId: assignment.variantId,
          recommendationId: dto.recommendationId,
          interactionType: dto.interactionType,
          data: dto.data || {},
        },
      });
      
      return {
        success: true,
        message: `Interaction enregistrée avec succès`,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'interaction pour l'utilisateur ${userId} et le test A/B ${testId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calcule les résultats d'un test A/B
   * @param testId ID du test
   * @returns Résultats du test
   */
  async calculateTestResults(testId: string): Promise<ExplanationABTestResultsInterface> {
    try {
      // Récupérer le test
      const test = await this.getTestById(testId);
      
      // Récupérer les assignations
      const assignments = await this.prisma.explanationABTestAssignment.findMany({
        where: { testId },
        include: {
          variant: true,
        },
      });
      
      // Récupérer les interactions
      const interactions = await this.prisma.explanationABTestInteraction.findMany({
        where: { testId },
      });
      
      // Calculer les résultats par variante
      const variantResults = [];
      const controlVariant = test.variants.find(v => v.type === ExplanationVariantType.SIMPLE) || test.variants[0];
      
      for (const variant of test.variants) {
        // Compter les utilisateurs assignés à cette variante
        const variantAssignments = assignments.filter(a => a.variantId === variant.id);
        const userCount = variantAssignments.length;
        
        // Compter les interactions pour cette variante
        const variantInteractions = interactions.filter(i => i.variantId === variant.id);
        const impressions = variantInteractions.filter(i => i.interactionType === 'VIEW').length;
        const expandInteractions = variantInteractions.filter(i => i.interactionType === 'EXPAND').length;
        const clickInteractions = variantInteractions.filter(i => i.interactionType === 'CLICK').length;
        const feedbackInteractions = variantInteractions.filter(i => i.interactionType === 'FEEDBACK').length;
        
        // Calculer les métriques
        const interactionRate = impressions > 0 ? (expandInteractions + clickInteractions + feedbackInteractions) / impressions : 0;
        const conversionRate = impressions > 0 ? clickInteractions / impressions : 0;
        
        // Calculer le temps moyen passé sur les explications
        const timeSpentInteractions = variantInteractions.filter(i => i.data && i.data.timeSpent);
        const averageTimeSpent = timeSpentInteractions.length > 0
          ? timeSpentInteractions.reduce((sum, i) => sum + i.data.timeSpent, 0) / timeSpentInteractions.length
          : 0;
        
        // Calculer le score de satisfaction
        const feedbackRatingInteractions = variantInteractions.filter(i => i.data && i.data.feedbackRating);
        const satisfactionScore = feedbackRatingInteractions.length > 0
          ? feedbackRatingInteractions.reduce((sum, i) => sum + i.data.feedbackRating, 0) / feedbackRatingInteractions.length
          : 0;
        
        variantResults.push({
          variantId: variant.id,
          variantName: variant.name,
          variantType: variant.type,
          userCount,
          impressions,
          interactions: expandInteractions + clickInteractions + feedbackInteractions,
          interactionRate,
          conversionRate,
          averageTimeSpent,
          satisfactionScore,
        });
      }
      
      // Déterminer la variante gagnante
      let winner = null;
      if (variantResults.length > 1) {
        const controlResult = variantResults.find(r => r.variantId === controlVariant.id);
        const nonControlResults = variantResults.filter(r => r.variantId !== controlVariant.id);
        
        // Trouver la variante avec le meilleur taux de conversion
        const bestVariant = nonControlResults.reduce((best, current) => {
          return current.conversionRate > best.conversionRate ? current : best;
        }, nonControlResults[0]);
        
        // Calculer l'amélioration par rapport à la variante de contrôle
        const improvement = controlResult && controlResult.conversionRate > 0
          ? (bestVariant.conversionRate - controlResult.conversionRate) / controlResult.conversionRate
          : 0;
        
        // Calculer le niveau de confiance (simplifié)
        const confidenceLevel = 0.95; // Valeur fixe pour l'exemple
        
        winner = {
          variantId: bestVariant.variantId,
          variantName: bestVariant.variantName,
          improvement,
          confidenceLevel,
        };
      }
      
      // Générer des recommandations
      const recommendations = [];
      
      if (winner) {
        recommendations.push(`La variante "${winner.variantName}" a montré une amélioration de ${(winner.improvement * 100).toFixed(1)}% du taux de conversion.`);
        
        if (winner.improvement > 0.1) {
          recommendations.push(`Envisager d'implémenter la variante "${winner.variantName}" comme option par défaut.`);
        }
      }
      
      // Ajouter des recommandations basées sur les métriques
      const highTimeSpentVariant = variantResults.reduce((best, current) => {
        return current.averageTimeSpent > best.averageTimeSpent ? current : best;
      }, variantResults[0]);
      
      if (highTimeSpentVariant.averageTimeSpent > 20) {
        recommendations.push(`Les utilisateurs passent beaucoup de temps (${highTimeSpentVariant.averageTimeSpent.toFixed(1)}s) sur les explications de la variante "${highTimeSpentVariant.variantName}".`);
      }
      
      const highSatisfactionVariant = variantResults.reduce((best, current) => {
        return current.satisfactionScore > best.satisfactionScore ? current : best;
      }, variantResults[0]);
      
      if (highSatisfactionVariant.satisfactionScore > 4) {
        recommendations.push(`La variante "${highSatisfactionVariant.variantName}" a reçu un score de satisfaction élevé (${highSatisfactionVariant.satisfactionScore.toFixed(1)}/5).`);
      }
      
      // Enregistrer les métriques
      await this.prisma.explanationABTestMetrics.create({
        data: {
          testId,
          metrics: {
            variantResults,
            winner,
            recommendations,
          },
        },
      });
      
      return {
        testId,
        testName: test.name,
        period: {
          start: test.startDate,
          end: test.endDate,
        },
        totalUsers: assignments.length,
        variantResults,
        winner,
        recommendations,
      };
    } catch (error) {
      this.logger.error(`Erreur lors du calcul des résultats du test A/B ${testId}: ${error.message}`);
      throw error;
    }
  }
}
