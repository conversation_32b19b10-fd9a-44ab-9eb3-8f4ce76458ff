import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { QueryOptimizationService } from './query-optimization.service';

/**
 * Interface pour les métriques d'explication
 */
export interface ExplanationMetrics {
  /** Nombre total d'explications */
  totalExplanations: number;
  
  /** Nombre d'explications vues */
  viewedExplanations: number;
  
  /** Taux de consultation des explications */
  viewRate: number;
  
  /** Temps moyen passé sur les explications (en secondes) */
  averageTimeSpent: number;
  
  /** Taux de clics sur les explications */
  clickThroughRate: number;
  
  /** Taux de conversion après avoir vu une explication */
  conversionRate: number;
  
  /** Score moyen de satisfaction */
  averageSatisfactionScore: number;
  
  /** Métriques par type de facteur */
  metricsByFactorType: Record<string, {
    count: number;
    viewRate: number;
    clickThroughRate: number;
    conversionRate: number;
    averageSatisfactionScore: number;
  }>;
  
  /** Métriques par segment utilisateur */
  metricsByUserSegment: Record<string, {
    count: number;
    viewRate: number;
    clickThroughRate: number;
    conversionRate: number;
    averageSatisfactionScore: number;
  }>;
}

/**
 * Interface pour les tendances d'explication
 */
export interface ExplanationTrends {
  /** Période de temps */
  period: 'day' | 'week' | 'month';
  
  /** Données de tendance */
  data: Array<{
    /** Date */
    date: string;
    
    /** Nombre d'explications */
    explanationCount: number;
    
    /** Taux de consultation */
    viewRate: number;
    
    /** Taux de clics */
    clickThroughRate: number;
    
    /** Taux de conversion */
    conversionRate: number;
  }>;
}

/**
 * Interface pour l'impact des explications
 */
export interface ExplanationImpact {
  /** Impact sur l'engagement utilisateur */
  userEngagement: {
    /** Changement dans le temps passé sur la plateforme */
    timeSpentChange: number;
    
    /** Changement dans le nombre de pages vues */
    pageViewsChange: number;
    
    /** Changement dans le taux de rebond */
    bounceRateChange: number;
  };
  
  /** Impact sur les conversions */
  conversions: {
    /** Changement dans le taux de conversion */
    conversionRateChange: number;
    
    /** Changement dans le panier moyen */
    averageOrderValueChange: number;
    
    /** Changement dans le taux d'abandon de panier */
    cartAbandonmentRateChange: number;
  };
  
  /** Impact sur la rétention */
  retention: {
    /** Changement dans le taux de rétention */
    retentionRateChange: number;
    
    /** Changement dans la fréquence des visites */
    visitFrequencyChange: number;
    
    /** Changement dans le taux de désabonnement */
    churnRateChange: number;
  };
}

/**
 * Interface pour les paramètres de filtrage des analyses
 */
export interface AnalyticsFilterParams {
  /** Date de début */
  startDate?: Date;
  
  /** Date de fin */
  endDate?: Date;
  
  /** Types de facteur */
  factorTypes?: string[];
  
  /** Segments utilisateur */
  userSegments?: string[];
  
  /** Langues */
  languages?: string[];
  
  /** Types d'explication */
  explanationTypes?: string[];
  
  /** Styles d'explication */
  explanationStyles?: string[];
}

/**
 * Service pour l'analyse avancée des explications
 */
@Injectable()
export class ExplanationAnalyticsService {
  private readonly logger = new Logger(ExplanationAnalyticsService.name);
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly queryOptimizationService: QueryOptimizationService,
  ) {
    // Écouter les événements d'interaction avec les explications
    this.eventEmitter.on('explanation.viewed', this.handleExplanationViewed.bind(this));
    this.eventEmitter.on('explanation.clicked', this.handleExplanationClicked.bind(this));
    this.eventEmitter.on('explanation.feedback', this.handleExplanationFeedback.bind(this));
    this.eventEmitter.on('explanation.conversion', this.handleExplanationConversion.bind(this));
  }
  
  /**
   * Récupère les métriques d'explication
   * @param filters Paramètres de filtrage
   * @returns Métriques d'explication
   */
  async getExplanationMetrics(filters?: AnalyticsFilterParams): Promise<ExplanationMetrics> {
    try {
      // Construire la clause where pour les filtres
      const where = this.buildWhereClause(filters);
      
      // Récupérer les données de base
      const [
        totalExplanations,
        viewEvents,
        clickEvents,
        conversionEvents,
        feedbackEvents,
      ] = await Promise.all([
        this.prisma.explanation.count({ where }),
        this.prisma.explanationEvent.count({
          where: {
            ...where,
            eventType: 'VIEW',
          },
        }),
        this.prisma.explanationEvent.count({
          where: {
            ...where,
            eventType: 'CLICK',
          },
        }),
        this.prisma.explanationEvent.count({
          where: {
            ...where,
            eventType: 'CONVERSION',
          },
        }),
        this.prisma.explanationEvent.findMany({
          where: {
            ...where,
            eventType: 'FEEDBACK',
          },
          select: {
            data: true,
          },
        }),
      ]);
      
      // Calculer les métriques de base
      const viewRate = totalExplanations > 0 ? viewEvents / totalExplanations : 0;
      const clickThroughRate = viewEvents > 0 ? clickEvents / viewEvents : 0;
      const conversionRate = viewEvents > 0 ? conversionEvents / viewEvents : 0;
      
      // Calculer le score moyen de satisfaction
      let averageSatisfactionScore = 0;
      if (feedbackEvents.length > 0) {
        const totalScore = feedbackEvents.reduce((sum, event) => {
          const data = event.data as any;
          return sum + (data.rating || 0);
        }, 0);
        averageSatisfactionScore = totalScore / feedbackEvents.length;
      }
      
      // Récupérer les métriques par type de facteur
      const factorTypeMetrics = await this.getMetricsByFactorType(filters);
      
      // Récupérer les métriques par segment utilisateur
      const userSegmentMetrics = await this.getMetricsByUserSegment(filters);
      
      // Récupérer le temps moyen passé sur les explications
      const timeSpentData = await this.prisma.explanationEvent.findMany({
        where: {
          ...where,
          eventType: 'VIEW',
        },
        select: {
          data: true,
        },
      });
      
      let averageTimeSpent = 0;
      if (timeSpentData.length > 0) {
        const totalTimeSpent = timeSpentData.reduce((sum, event) => {
          const data = event.data as any;
          return sum + (data.duration || 0);
        }, 0);
        averageTimeSpent = totalTimeSpent / timeSpentData.length;
      }
      
      return {
        totalExplanations,
        viewedExplanations: viewEvents,
        viewRate,
        averageTimeSpent,
        clickThroughRate,
        conversionRate,
        averageSatisfactionScore,
        metricsByFactorType: factorTypeMetrics,
        metricsByUserSegment: userSegmentMetrics,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des métriques d'explication: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Récupère les tendances d'explication
   * @param period Période de temps
   * @param filters Paramètres de filtrage
   * @returns Tendances d'explication
   */
  async getExplanationTrends(
    period: 'day' | 'week' | 'month' = 'day',
    filters?: AnalyticsFilterParams,
  ): Promise<ExplanationTrends> {
    try {
      // Déterminer l'intervalle de temps
      const { startDate, endDate, interval } = this.getTimeInterval(period, filters);
      
      // Construire la clause where pour les filtres
      const where = this.buildWhereClause({
        ...filters,
        startDate,
        endDate,
      });
      
      // Récupérer les données de tendance
      const trendData = await this.prisma.$queryRaw`
        SELECT
          DATE_TRUNC(${interval}, "createdAt") as date,
          COUNT(*) as explanation_count,
          SUM(CASE WHEN "eventType" = 'VIEW' THEN 1 ELSE 0 END) as view_count,
          SUM(CASE WHEN "eventType" = 'CLICK' THEN 1 ELSE 0 END) as click_count,
          SUM(CASE WHEN "eventType" = 'CONVERSION' THEN 1 ELSE 0 END) as conversion_count
        FROM "explanation_events"
        WHERE "createdAt" BETWEEN ${startDate} AND ${endDate}
        GROUP BY DATE_TRUNC(${interval}, "createdAt")
        ORDER BY date ASC
      `;
      
      // Formater les données de tendance
      const data = trendData.map((item: any) => {
        const explanationCount = parseInt(item.explanation_count);
        const viewCount = parseInt(item.view_count);
        const clickCount = parseInt(item.click_count);
        const conversionCount = parseInt(item.conversion_count);
        
        return {
          date: item.date.toISOString().split('T')[0],
          explanationCount,
          viewRate: explanationCount > 0 ? viewCount / explanationCount : 0,
          clickThroughRate: viewCount > 0 ? clickCount / viewCount : 0,
          conversionRate: viewCount > 0 ? conversionCount / viewCount : 0,
        };
      });
      
      return {
        period,
        data,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des tendances d'explication: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Récupère l'impact des explications
   * @param filters Paramètres de filtrage
   * @returns Impact des explications
   */
  async getExplanationImpact(filters?: AnalyticsFilterParams): Promise<ExplanationImpact> {
    try {
      // Dans une implémentation réelle, on calculerait ces métriques à partir des données réelles
      // Pour l'instant, on retourne des données fictives
      
      return {
        userEngagement: {
          timeSpentChange: 0.15, // +15%
          pageViewsChange: 0.08, // +8%
          bounceRateChange: -0.12, // -12%
        },
        conversions: {
          conversionRateChange: 0.22, // +22%
          averageOrderValueChange: 0.05, // +5%
          cartAbandonmentRateChange: -0.18, // -18%
        },
        retention: {
          retentionRateChange: 0.1, // +10%
          visitFrequencyChange: 0.25, // +25%
          churnRateChange: -0.15, // -15%
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de l'impact des explications: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Récupère les métriques par type de facteur
   * @param filters Paramètres de filtrage
   * @returns Métriques par type de facteur
   */
  private async getMetricsByFactorType(
    filters?: AnalyticsFilterParams,
  ): Promise<Record<string, any>> {
    // Construire la clause where pour les filtres
    const where = this.buildWhereClause(filters);
    
    // Récupérer les données par type de facteur
    const factorTypeData = await this.prisma.explanation.groupBy({
      by: ['factorType'],
      where,
      _count: {
        id: true,
      },
    });
    
    // Récupérer les événements par type de facteur
    const factorTypeEvents = await this.prisma.explanationEvent.findMany({
      where,
      include: {
        explanation: {
          select: {
            factorType: true,
          },
        },
      },
    });
    
    // Calculer les métriques par type de facteur
    const metrics: Record<string, any> = {};
    
    for (const factorType of factorTypeData) {
      const count = factorType._count.id;
      const events = factorTypeEvents.filter(
        event => event.explanation?.factorType === factorType.factorType,
      );
      
      const viewEvents = events.filter(event => event.eventType === 'VIEW');
      const clickEvents = events.filter(event => event.eventType === 'CLICK');
      const conversionEvents = events.filter(event => event.eventType === 'CONVERSION');
      const feedbackEvents = events.filter(event => event.eventType === 'FEEDBACK');
      
      const viewRate = count > 0 ? viewEvents.length / count : 0;
      const clickThroughRate = viewEvents.length > 0 ? clickEvents.length / viewEvents.length : 0;
      const conversionRate = viewEvents.length > 0 ? conversionEvents.length / viewEvents.length : 0;
      
      let averageSatisfactionScore = 0;
      if (feedbackEvents.length > 0) {
        const totalScore = feedbackEvents.reduce((sum, event) => {
          const data = event.data as any;
          return sum + (data.rating || 0);
        }, 0);
        averageSatisfactionScore = totalScore / feedbackEvents.length;
      }
      
      metrics[factorType.factorType] = {
        count,
        viewRate,
        clickThroughRate,
        conversionRate,
        averageSatisfactionScore,
      };
    }
    
    return metrics;
  }
  
  /**
   * Récupère les métriques par segment utilisateur
   * @param filters Paramètres de filtrage
   * @returns Métriques par segment utilisateur
   */
  private async getMetricsByUserSegment(
    filters?: AnalyticsFilterParams,
  ): Promise<Record<string, any>> {
    // Dans une implémentation réelle, on récupérerait les segments utilisateur
    // Pour l'instant, on utilise des segments fictifs
    
    const userSegments = [
      'new_users',
      'returning_users',
      'frequent_users',
      'high_value_users',
    ];
    
    // Générer des métriques fictives pour chaque segment
    const metrics: Record<string, any> = {};
    
    for (const segment of userSegments) {
      metrics[segment] = {
        count: Math.floor(Math.random() * 1000) + 100,
        viewRate: Math.random() * 0.5 + 0.3,
        clickThroughRate: Math.random() * 0.4 + 0.2,
        conversionRate: Math.random() * 0.3 + 0.1,
        averageSatisfactionScore: Math.random() * 2 + 3, // 3-5 sur 5
      };
    }
    
    return metrics;
  }
  
  /**
   * Construit une clause where pour les filtres
   * @param filters Paramètres de filtrage
   * @returns Clause where
   */
  private buildWhereClause(filters?: AnalyticsFilterParams): any {
    if (!filters) {
      return {};
    }
    
    const where: any = {};
    
    if (filters.startDate || filters.endDate) {
      where.createdAt = {};
      
      if (filters.startDate) {
        where.createdAt.gte = filters.startDate;
      }
      
      if (filters.endDate) {
        where.createdAt.lte = filters.endDate;
      }
    }
    
    if (filters.factorTypes && filters.factorTypes.length > 0) {
      where.factorType = {
        in: filters.factorTypes,
      };
    }
    
    if (filters.languages && filters.languages.length > 0) {
      where.language = {
        in: filters.languages,
      };
    }
    
    if (filters.explanationTypes && filters.explanationTypes.length > 0) {
      where.type = {
        in: filters.explanationTypes,
      };
    }
    
    return where;
  }
  
  /**
   * Détermine l'intervalle de temps pour les tendances
   * @param period Période de temps
   * @param filters Paramètres de filtrage
   * @returns Intervalle de temps
   */
  private getTimeInterval(
    period: 'day' | 'week' | 'month',
    filters?: AnalyticsFilterParams,
  ): { startDate: Date; endDate: Date; interval: string } {
    const endDate = filters?.endDate || new Date();
    let startDate = filters?.startDate;
    let interval: string;
    
    if (!startDate) {
      const now = new Date();
      
      switch (period) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          interval = 'hour';
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          interval = 'day';
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          interval = 'day';
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          interval = 'hour';
      }
    } else {
      // Déterminer l'intervalle en fonction de la différence entre les dates
      const diffInDays = Math.floor((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
      
      if (diffInDays <= 1) {
        interval = 'hour';
      } else if (diffInDays <= 31) {
        interval = 'day';
      } else {
        interval = 'week';
      }
    }
    
    return { startDate, endDate, interval };
  }
  
  /**
   * Gère l'événement de consultation d'une explication
   * @param payload Payload de l'événement
   */
  private handleExplanationViewed(payload: any): void {
    // Dans une implémentation réelle, on enregistrerait cet événement
    this.logger.debug(`Explication consultée: ${JSON.stringify(payload)}`);
  }
  
  /**
   * Gère l'événement de clic sur une explication
   * @param payload Payload de l'événement
   */
  private handleExplanationClicked(payload: any): void {
    // Dans une implémentation réelle, on enregistrerait cet événement
    this.logger.debug(`Clic sur une explication: ${JSON.stringify(payload)}`);
  }
  
  /**
   * Gère l'événement de feedback sur une explication
   * @param payload Payload de l'événement
   */
  private handleExplanationFeedback(payload: any): void {
    // Dans une implémentation réelle, on enregistrerait cet événement
    this.logger.debug(`Feedback sur une explication: ${JSON.stringify(payload)}`);
  }
  
  /**
   * Gère l'événement de conversion après une explication
   * @param payload Payload de l'événement
   */
  private handleExplanationConversion(payload: any): void {
    // Dans une implémentation réelle, on enregistrerait cet événement
    this.logger.debug(`Conversion après une explication: ${JSON.stringify(payload)}`);
  }
}
