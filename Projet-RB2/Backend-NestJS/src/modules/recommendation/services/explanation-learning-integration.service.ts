import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { OnEvent } from '@nestjs/event-emitter';
import { ExplanationService } from './explanation.service';
import { ExplanationABTestingService } from './explanation-ab-testing.service';
import { ContinuousLearningService } from './continuous-learning.service';
import { ExplanationABTestResultsInterface } from '../interfaces/explanation-ab-testing.interface';
import { ExplanationVariantType } from '@prisma/client';

/**
 * Service d'intégration entre les tests A/B d'explications et l'apprentissage continu
 * Permet d'améliorer automatiquement les explications en fonction des résultats des tests A/B
 */
@Injectable()
export class ExplanationLearningIntegrationService implements OnModuleInit {
  private readonly logger = new Logger(ExplanationLearningIntegrationService.name);
  private readonly integrationParams: {
    enabled: boolean;
    minInteractionsForOptimization: number;
    minConfidenceLevel: number;
    optimizationInterval: number;
    learningRate: number;
    templateOptimizationEnabled: boolean;
    factorWeightOptimizationEnabled: boolean;
    autoDeployWinners: boolean;
    autoDeployThreshold: number;
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly explanationService: ExplanationService,
    private readonly abTestingService: ExplanationABTestingService,
    private readonly continuousLearningService: ContinuousLearningService,
  ) {
    // Initialiser les paramètres d'intégration à partir de la configuration
    this.integrationParams = {
      enabled: this.configService.get<boolean>('recommendation.explanationLearning.enabled', true),
      minInteractionsForOptimization: this.configService.get<number>('recommendation.explanationLearning.minInteractionsForOptimization', 100),
      minConfidenceLevel: this.configService.get<number>('recommendation.explanationLearning.minConfidenceLevel', 0.8),
      optimizationInterval: this.configService.get<number>('recommendation.explanationLearning.optimizationInterval', 24),
      learningRate: this.configService.get<number>('recommendation.explanationLearning.learningRate', 0.1),
      templateOptimizationEnabled: this.configService.get<boolean>('recommendation.explanationLearning.templateOptimizationEnabled', true),
      factorWeightOptimizationEnabled: this.configService.get<boolean>('recommendation.explanationLearning.factorWeightOptimizationEnabled', true),
      autoDeployWinners: this.configService.get<boolean>('recommendation.explanationLearning.autoDeployWinners', false),
      autoDeployThreshold: this.configService.get<number>('recommendation.explanationLearning.autoDeployThreshold', 0.15),
    };
  }

  /**
   * Initialisation du module
   */
  async onModuleInit() {
    this.logger.log('Initialisation du service d\'intégration des tests A/B et de l\'apprentissage continu');
    
    if (!this.integrationParams.enabled) {
      this.logger.log('Service d\'intégration désactivé dans la configuration');
      return;
    }
    
    this.logger.log('Service d\'intégration initialisé');
  }

  /**
   * Écoute les événements de fin de test A/B
   * @param payload Données de l'événement
   */
  @OnEvent('explanation.abtest.completed')
  async handleABTestCompleted(payload: { testId: string }) {
    this.logger.log(`Événement de fin de test A/B reçu pour le test ${payload.testId}`);
    
    if (!this.integrationParams.enabled) {
      this.logger.log('Service d\'intégration désactivé, événement ignoré');
      return;
    }
    
    try {
      // Récupérer les résultats du test A/B
      const results = await this.abTestingService.calculateTestResults(payload.testId);
      
      // Analyser les résultats et appliquer les optimisations
      await this.analyzeAndOptimize(results);
    } catch (error) {
      this.logger.error(`Erreur lors du traitement de l'événement de fin de test A/B: ${error.message}`);
    }
  }

  /**
   * Analyse les résultats d'un test A/B et applique les optimisations
   * @param results Résultats du test A/B
   */
  private async analyzeAndOptimize(results: ExplanationABTestResultsInterface) {
    this.logger.log(`Analyse des résultats du test A/B ${results.testId}`);
    
    // Vérifier si le test a un gagnant clair
    if (!results.winner) {
      this.logger.log('Aucun gagnant clair identifié, pas d\'optimisation à appliquer');
      return;
    }
    
    // Vérifier si le gagnant a une amélioration significative
    if (results.winner.improvement < this.integrationParams.autoDeployThreshold) {
      this.logger.log(`L'amélioration (${results.winner.improvement}) est inférieure au seuil (${this.integrationParams.autoDeployThreshold}), pas d'optimisation à appliquer`);
      return;
    }
    
    // Vérifier si le niveau de confiance est suffisant
    if (results.winner.confidenceLevel < this.integrationParams.minConfidenceLevel) {
      this.logger.log(`Le niveau de confiance (${results.winner.confidenceLevel}) est inférieur au seuil (${this.integrationParams.minConfidenceLevel}), pas d'optimisation à appliquer`);
      return;
    }
    
    // Récupérer la variante gagnante
    const winnerVariant = results.variantResults.find(v => v.variantId === results.winner?.variantId);
    if (!winnerVariant) {
      this.logger.error('Variante gagnante non trouvée dans les résultats');
      return;
    }
    
    // Appliquer les optimisations en fonction du type de variante
    await this.applyOptimizations(winnerVariant.variantType, results);
    
    // Déployer automatiquement la variante gagnante si configuré
    if (this.integrationParams.autoDeployWinners) {
      await this.deployWinningVariant(results);
    }
    
    // Enregistrer l'événement d'apprentissage
    await this.recordLearningEvent(results);
  }

  /**
   * Applique les optimisations en fonction du type de variante
   * @param variantType Type de variante
   * @param results Résultats du test A/B
   */
  private async applyOptimizations(
    variantType: ExplanationVariantType,
    results: ExplanationABTestResultsInterface,
  ) {
    this.logger.log(`Application des optimisations pour la variante de type ${variantType}`);
    
    // Optimiser les poids des facteurs si activé
    if (this.integrationParams.factorWeightOptimizationEnabled) {
      await this.optimizeFactorWeights(variantType, results);
    }
    
    // Optimiser les templates d'explication si activé
    if (this.integrationParams.templateOptimizationEnabled) {
      await this.optimizeExplanationTemplates(variantType, results);
    }
  }

  /**
   * Optimise les poids des facteurs en fonction des résultats du test A/B
   * @param variantType Type de variante
   * @param results Résultats du test A/B
   */
  private async optimizeFactorWeights(
    variantType: ExplanationVariantType,
    results: ExplanationABTestResultsInterface,
  ) {
    this.logger.log(`Optimisation des poids des facteurs pour la variante de type ${variantType}`);
    
    try {
      // Récupérer la configuration de la variante gagnante
      const winnerVariant = await this.prisma.explanationVariant.findUnique({
        where: { id: results.winner?.variantId },
      });
      
      if (!winnerVariant) {
        this.logger.error('Variante gagnante non trouvée');
        return;
      }
      
      const configuration = winnerVariant.configuration as Record<string, any>;
      
      // Ajuster les poids des facteurs dans le service d'explication
      if (configuration.factorWeights) {
        await this.explanationService.updateFactorWeights(configuration.factorWeights);
        this.logger.log('Poids des facteurs mis à jour avec succès');
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'optimisation des poids des facteurs: ${error.message}`);
    }
  }

  /**
   * Optimise les templates d'explication en fonction des résultats du test A/B
   * @param variantType Type de variante
   * @param results Résultats du test A/B
   */
  private async optimizeExplanationTemplates(
    variantType: ExplanationVariantType,
    results: ExplanationABTestResultsInterface,
  ) {
    this.logger.log(`Optimisation des templates d'explication pour la variante de type ${variantType}`);
    
    try {
      // Récupérer la configuration de la variante gagnante
      const winnerVariant = await this.prisma.explanationVariant.findUnique({
        where: { id: results.winner?.variantId },
      });
      
      if (!winnerVariant) {
        this.logger.error('Variante gagnante non trouvée');
        return;
      }
      
      const configuration = winnerVariant.configuration as Record<string, any>;
      
      // Mettre à jour les templates d'explication si présents dans la configuration
      if (configuration.templates) {
        for (const [factorType, template] of Object.entries(configuration.templates)) {
          // Rechercher le template existant
          const existingTemplate = await this.prisma.explanationTemplate.findFirst({
            where: {
              factorType,
              language: 'fr', // Langue par défaut
            },
          });
          
          if (existingTemplate) {
            // Mettre à jour le template existant
            await this.prisma.explanationTemplate.update({
              where: { id: existingTemplate.id },
              data: {
                template: template as string,
                updatedAt: new Date(),
              },
            });
            this.logger.log(`Template pour le facteur ${factorType} mis à jour`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'optimisation des templates d'explication: ${error.message}`);
    }
  }

  /**
   * Déploie la variante gagnante comme configuration par défaut
   * @param results Résultats du test A/B
   */
  private async deployWinningVariant(results: ExplanationABTestResultsInterface) {
    this.logger.log(`Déploiement de la variante gagnante pour le test ${results.testId}`);
    
    try {
      // Récupérer la variante gagnante
      const winnerVariant = await this.prisma.explanationVariant.findUnique({
        where: { id: results.winner?.variantId },
      });
      
      if (!winnerVariant) {
        this.logger.error('Variante gagnante non trouvée');
        return;
      }
      
      // Mettre à jour la configuration par défaut du service d'explication
      await this.explanationService.updateDefaultConfiguration(winnerVariant.configuration as Record<string, any>);
      
      // Mettre à jour le statut du test
      await this.prisma.explanationABTest.update({
        where: { id: results.testId },
        data: {
          status: 'COMPLETED',
          updatedAt: new Date(),
        },
      });
      
      this.logger.log(`Variante gagnante déployée avec succès pour le test ${results.testId}`);
    } catch (error) {
      this.logger.error(`Erreur lors du déploiement de la variante gagnante: ${error.message}`);
    }
  }

  /**
   * Enregistre un événement d'apprentissage
   * @param results Résultats du test A/B
   */
  private async recordLearningEvent(results: ExplanationABTestResultsInterface) {
    try {
      // Créer un événement d'apprentissage pour le système de recommandation
      await this.prisma.learningEvent.create({
        data: {
          userId: 'system', // Événement système
          eventType: 'model_update',
          data: {
            source: 'explanation_ab_test',
            testId: results.testId,
            testName: results.testName,
            winnerVariantId: results.winner?.variantId,
            winnerVariantName: results.winner?.variantName,
            improvement: results.winner?.improvement,
            confidenceLevel: results.winner?.confidenceLevel,
            recommendations: results.recommendations,
          },
          estimatedImpact: results.winner?.improvement || 0.5,
        },
      });
      
      this.logger.log(`Événement d'apprentissage enregistré pour le test ${results.testId}`);
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement d'apprentissage: ${error.message}`);
    }
  }

  /**
   * Optimisation périodique des explications
   * Exécutée une fois par jour
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async periodicOptimization() {
    if (!this.integrationParams.enabled) {
      return;
    }
    
    this.logger.log('Exécution de l\'optimisation périodique des explications');
    
    try {
      // Récupérer les tests A/B terminés récemment
      const recentlyCompletedTests = await this.prisma.explanationABTest.findMany({
        where: {
          status: 'COMPLETED',
          updatedAt: {
            gte: new Date(Date.now() - this.integrationParams.optimizationInterval * 60 * 60 * 1000),
          },
        },
      });
      
      this.logger.log(`${recentlyCompletedTests.length} tests A/B terminés récemment`);
      
      // Analyser et optimiser chaque test
      for (const test of recentlyCompletedTests) {
        const results = await this.abTestingService.calculateTestResults(test.id);
        await this.analyzeAndOptimize(results);
      }
      
      this.logger.log('Optimisation périodique des explications terminée');
    } catch (error) {
      this.logger.error(`Erreur lors de l'optimisation périodique des explications: ${error.message}`);
    }
  }
}
