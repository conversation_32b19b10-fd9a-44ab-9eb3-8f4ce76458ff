import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { 
  ExplanationPreferences,
  ExplanationStyle,
  DetailLevel,
  ExplanationFormat,
} from '../interfaces/explanation-preferences.interface';
import { UpdateExplanationPreferencesDto } from '../dto/explanation-preferences.dto';
import { OnEvent } from '@nestjs/event-emitter';

/**
 * Service pour gérer les préférences d'explication des utilisateurs
 */
@Injectable()
export class ExplanationPreferencesService {
  private readonly logger = new Logger(ExplanationPreferencesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Récupère les préférences d'explication d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Préférences d'explication
   */
  async getUserPreferences(userId: string): Promise<ExplanationPreferences> {
    try {
      // Récupérer les préférences existantes
      const preferences = await this.prisma.explanationPreferences.findUnique({
        where: { userId },
      });

      // Si les préférences existent, les retourner
      if (preferences) {
        return this.mapDbPreferencesToInterface(preferences);
      }

      // Sinon, créer des préférences par défaut
      return this.createDefaultPreferences(userId);
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des préférences d'explication: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour les préférences d'explication d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param updateDto DTO de mise à jour des préférences
   * @returns Préférences d'explication mises à jour
   */
  async updateUserPreferences(
    userId: string,
    updateDto: UpdateExplanationPreferencesDto,
  ): Promise<ExplanationPreferences> {
    try {
      // Vérifier si l'utilisateur existe
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true },
      });

      if (!user) {
        throw new NotFoundException(`Utilisateur avec l'ID ${userId} non trouvé`);
      }

      // Mettre à jour ou créer les préférences
      const updatedPreferences = await this.prisma.explanationPreferences.upsert({
        where: { userId },
        update: {
          preferredStyle: updateDto.preferredStyle,
          detailLevel: updateDto.detailLevel,
          preferredFormat: updateDto.preferredFormat,
          highlightedFactors: updateDto.highlightedFactors,
          hiddenFactors: updateDto.hiddenFactors,
          language: updateDto.language,
          culturalPreferences: updateDto.culturalPreferences ? JSON.stringify(updateDto.culturalPreferences) : undefined,
          accessibilityPreferences: updateDto.accessibilityPreferences ? JSON.stringify(updateDto.accessibilityPreferences) : undefined,
          metadata: updateDto.metadata ? JSON.stringify(updateDto.metadata) : undefined,
          updatedAt: new Date(),
        },
        create: {
          userId,
          preferredStyle: updateDto.preferredStyle || ExplanationStyle.CONVERSATIONAL,
          detailLevel: updateDto.detailLevel || DetailLevel.MODERATE,
          preferredFormat: updateDto.preferredFormat || [ExplanationFormat.MIXED],
          highlightedFactors: updateDto.highlightedFactors || [],
          hiddenFactors: updateDto.hiddenFactors || [],
          language: updateDto.language || this.configService.get<string>('app.defaultLanguage', 'fr'),
          culturalPreferences: updateDto.culturalPreferences ? JSON.stringify(updateDto.culturalPreferences) : '{}',
          accessibilityPreferences: updateDto.accessibilityPreferences ? JSON.stringify(updateDto.accessibilityPreferences) : '{}',
          metadata: updateDto.metadata ? JSON.stringify(updateDto.metadata) : null,
        },
      });

      // Enregistrer l'événement de changement de préférence
      await this.recordPreferenceChangeEvent(userId, updateDto);

      return this.mapDbPreferencesToInterface(updatedPreferences);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des préférences d'explication: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée des préférences d'explication par défaut pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Préférences d'explication par défaut
   */
  async createDefaultPreferences(userId: string): Promise<ExplanationPreferences> {
    try {
      // Vérifier si l'utilisateur existe
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, language: true },
      });

      if (!user) {
        throw new NotFoundException(`Utilisateur avec l'ID ${userId} non trouvé`);
      }

      // Créer des préférences par défaut
      const defaultPreferences = await this.prisma.explanationPreferences.create({
        data: {
          userId,
          preferredStyle: ExplanationStyle.CONVERSATIONAL,
          detailLevel: DetailLevel.MODERATE,
          preferredFormat: [ExplanationFormat.MIXED],
          highlightedFactors: [],
          hiddenFactors: [],
          language: user.language || this.configService.get<string>('app.defaultLanguage', 'fr'),
          culturalPreferences: '{}',
          accessibilityPreferences: '{}',
        },
      });

      return this.mapDbPreferencesToInterface(defaultPreferences);
    } catch (error) {
      this.logger.error(`Erreur lors de la création des préférences d'explication par défaut: ${error.message}`);
      throw error;
    }
  }

  /**
   * Supprime les préférences d'explication d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Résultat de la suppression
   */
  async deleteUserPreferences(userId: string): Promise<{ success: boolean }> {
    try {
      await this.prisma.explanationPreferences.delete({
        where: { userId },
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression des préférences d'explication: ${error.message}`);
      return { success: false };
    }
  }

  /**
   * Réagit à l'événement de changement de langue d'un utilisateur
   * @param payload Payload de l'événement
   */
  @OnEvent('user.language.changed')
  async handleLanguageChangeEvent(payload: { userId: string; language: string }) {
    try {
      const { userId, language } = payload;

      // Mettre à jour la langue des préférences d'explication
      await this.prisma.explanationPreferences.updateMany({
        where: { userId },
        data: { language },
      });

      this.logger.log(`Langue des préférences d'explication mise à jour pour l'utilisateur ${userId}: ${language}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de la langue des préférences d'explication: ${error.message}`);
    }
  }

  /**
   * Enregistre un événement de changement de préférence
   * @param userId ID de l'utilisateur
   * @param updateDto DTO de mise à jour des préférences
   */
  private async recordPreferenceChangeEvent(
    userId: string,
    updateDto: UpdateExplanationPreferencesDto,
  ): Promise<void> {
    try {
      await this.prisma.explanationEvent.create({
        data: {
          userId,
          eventType: 'PREFERENCE_CHANGE',
          data: {
            changes: updateDto,
          },
        },
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'événement de changement de préférence: ${error.message}`);
    }
  }

  /**
   * Convertit les préférences de la base de données en interface
   * @param dbPreferences Préférences de la base de données
   * @returns Interface des préférences
   */
  private mapDbPreferencesToInterface(dbPreferences: any): ExplanationPreferences {
    return {
      userId: dbPreferences.userId,
      preferredStyle: dbPreferences.preferredStyle as ExplanationStyle,
      detailLevel: dbPreferences.detailLevel as DetailLevel,
      preferredFormat: dbPreferences.preferredFormat as ExplanationFormat[],
      highlightedFactors: dbPreferences.highlightedFactors,
      hiddenFactors: dbPreferences.hiddenFactors,
      language: dbPreferences.language,
      culturalPreferences: typeof dbPreferences.culturalPreferences === 'string'
        ? JSON.parse(dbPreferences.culturalPreferences)
        : dbPreferences.culturalPreferences,
      accessibilityPreferences: typeof dbPreferences.accessibilityPreferences === 'string'
        ? JSON.parse(dbPreferences.accessibilityPreferences)
        : dbPreferences.accessibilityPreferences,
      metadata: dbPreferences.metadata
        ? typeof dbPreferences.metadata === 'string'
          ? JSON.parse(dbPreferences.metadata)
          : dbPreferences.metadata
        : undefined,
    };
  }
}
