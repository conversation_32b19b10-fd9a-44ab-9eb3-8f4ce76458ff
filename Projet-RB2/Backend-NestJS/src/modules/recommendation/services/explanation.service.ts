import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * Service d'explications des recommandations
 * Génère des explications personnalisées pour les recommandations
 */
@Injectable()
export class ExplanationService {
  private readonly logger = new Logger(ExplanationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Génère des explications pour une recommandation
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explications de la recommandation
   */
  async generateExplanation(
    userId: string,
    recommendation: any,
  ): Promise<RecommendationExplanation> {
    try {
      this.logger.debug(`Génération d'explications pour la recommandation ${recommendation.id} pour l'utilisateur ${userId}`);
      
      // Récupérer les sources de la recommandation
      const sources = recommendation.sources || [];
      
      // Récupérer le type de recommandation
      const type = recommendation.type;
      
      // Récupérer les métadonnées de la recommandation
      const metadata = recommendation.metadata || {};
      
      // Générer les explications en fonction des sources
      const explanations: string[] = [];
      const factors: ExplanationFactor[] = [];
      
      // Explication basée sur le contenu
      if (sources.includes('content-based')) {
        const contentExplanation = await this.generateContentBasedExplanation(userId, recommendation);
        if (contentExplanation) {
          explanations.push(contentExplanation.text);
          factors.push(...contentExplanation.factors);
        }
      }
      
      // Explication basée sur le filtrage collaboratif
      if (sources.includes('collaborative')) {
        const collaborativeExplanation = await this.generateCollaborativeExplanation(userId, recommendation);
        if (collaborativeExplanation) {
          explanations.push(collaborativeExplanation.text);
          factors.push(...collaborativeExplanation.factors);
        }
      }
      
      // Explication basée sur la factorisation matricielle
      if (sources.includes('matrix-factorization')) {
        const matrixExplanation = await this.generateMatrixFactorizationExplanation(userId, recommendation);
        if (matrixExplanation) {
          explanations.push(matrixExplanation.text);
          factors.push(...matrixExplanation.factors);
        }
      }
      
      // Explication basée sur le contexte
      if (sources.includes('contextual')) {
        const contextualExplanation = await this.generateContextualExplanation(userId, recommendation);
        if (contextualExplanation) {
          explanations.push(contextualExplanation.text);
          factors.push(...contextualExplanation.factors);
        }
      }
      
      // Explication basée sur le deep learning
      if (sources.includes('deep-learning')) {
        const deepLearningExplanation = await this.generateDeepLearningExplanation(userId, recommendation);
        if (deepLearningExplanation) {
          explanations.push(deepLearningExplanation.text);
          factors.push(...deepLearningExplanation.factors);
        }
      }
      
      // Explication basée sur le temps réel
      if (sources.includes('realtime')) {
        const realtimeExplanation = await this.generateRealtimeExplanation(userId, recommendation);
        if (realtimeExplanation) {
          explanations.push(realtimeExplanation.text);
          factors.push(...realtimeExplanation.factors);
        }
      }
      
      // Si aucune explication spécifique n'a été générée, ajouter une explication générique
      if (explanations.length === 0) {
        explanations.push(this.generateGenericExplanation(type));
      }
      
      // Générer l'explication finale
      const explanation: RecommendationExplanation = {
        text: explanations.join(' '),
        factors: factors,
        sources: sources,
        confidence: this.calculateConfidence(recommendation, factors),
      };
      
      return explanation;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'explications: ${error.message}`);
      
      // Retourner une explication générique en cas d'erreur
      return {
        text: this.generateGenericExplanation(recommendation.type),
        factors: [],
        sources: recommendation.sources || [],
        confidence: 0.5,
      };
    }
  }

  /**
   * Génère une explication basée sur le contenu
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explication basée sur le contenu
   */
  private async generateContentBasedExplanation(
    userId: string,
    recommendation: any,
  ): Promise<{ text: string; factors: ExplanationFactor[] } | null> {
    try {
      const factors: ExplanationFactor[] = [];
      const metadata = recommendation.metadata || {};
      
      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.prisma.userPreference.findUnique({
        where: { userId },
        select: { preferences: true },
      });
      
      const preferences = userPreferences?.preferences || {};
      
      // Vérifier si la catégorie correspond aux préférences
      if (metadata.category && preferences.preferredCategories?.includes(metadata.category)) {
        factors.push({
          type: 'CATEGORY_MATCH',
          name: 'Catégorie',
          value: metadata.category,
          weight: 0.8,
        });
      }
      
      // Vérifier si les tags correspondent aux préférences
      if (metadata.tags && Array.isArray(metadata.tags) && preferences.preferredTags) {
        const matchingTags = metadata.tags.filter(tag => preferences.preferredTags.includes(tag));
        if (matchingTags.length > 0) {
          factors.push({
            type: 'TAG_MATCH',
            name: 'Tags',
            value: matchingTags.join(', '),
            weight: 0.6,
          });
        }
      }
      
      // Vérifier si le niveau correspond aux préférences
      if (metadata.level && preferences.preferredLevels?.includes(metadata.level)) {
        factors.push({
          type: 'LEVEL_MATCH',
          name: 'Niveau',
          value: metadata.level,
          weight: 0.5,
        });
      }
      
      // Générer le texte d'explication
      let text = '';
      if (factors.length > 0) {
        if (factors.some(f => f.type === 'CATEGORY_MATCH')) {
          text += `Cette recommandation correspond à votre intérêt pour la catégorie "${metadata.category}". `;
        }
        
        if (factors.some(f => f.type === 'TAG_MATCH')) {
          const matchingTags = factors.find(f => f.type === 'TAG_MATCH')?.value || '';
          text += `Elle contient des sujets qui vous intéressent: ${matchingTags}. `;
        }
        
        if (factors.some(f => f.type === 'LEVEL_MATCH')) {
          text += `Le niveau "${metadata.level}" correspond à vos préférences. `;
        }
      }
      
      if (text) {
        return { text, factors };
      }
      
      return null;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'explications basées sur le contenu: ${error.message}`);
      return null;
    }
  }

  /**
   * Génère une explication basée sur le filtrage collaboratif
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explication basée sur le filtrage collaboratif
   */
  private async generateCollaborativeExplanation(
    userId: string,
    recommendation: any,
  ): Promise<{ text: string; factors: ExplanationFactor[] } | null> {
    try {
      // Récupérer les utilisateurs similaires qui ont interagi avec cet item
      const similarUsers = await this.prisma.userSimilarity.findMany({
        where: {
          userId,
          similarity: {
            gte: 0.6, // Seuil de similarité
          },
        },
        orderBy: {
          similarity: 'desc',
        },
        take: 5,
      });
      
      if (similarUsers.length === 0) {
        return null;
      }
      
      const similarUserIds = similarUsers.map(u => u.similarUserId);
      
      // Vérifier si des utilisateurs similaires ont interagi avec cet item
      const interactions = await this.prisma.userInteraction.findMany({
        where: {
          userId: {
            in: similarUserIds,
          },
          itemId: recommendation.id,
          interactionType: {
            in: ['LIKE', 'BOOKMARK', 'ENROLL', 'PURCHASE'],
          },
        },
        select: {
          userId: true,
          interactionType: true,
        },
      });
      
      if (interactions.length === 0) {
        return null;
      }
      
      // Compter les types d'interactions
      const interactionCounts: Record<string, number> = {};
      interactions.forEach(interaction => {
        interactionCounts[interaction.interactionType] = (interactionCounts[interaction.interactionType] || 0) + 1;
      });
      
      // Déterminer le type d'interaction le plus fréquent
      let mostFrequentType = '';
      let maxCount = 0;
      
      for (const [type, count] of Object.entries(interactionCounts)) {
        if (count > maxCount) {
          mostFrequentType = type;
          maxCount = count;
        }
      }
      
      // Générer le facteur d'explication
      const factor: ExplanationFactor = {
        type: 'SIMILAR_USERS',
        name: 'Utilisateurs similaires',
        value: `${interactions.length} utilisateurs`,
        weight: Math.min(0.9, 0.5 + (interactions.length / 10) * 0.4),
      };
      
      // Générer le texte d'explication
      let text = '';
      
      if (mostFrequentType === 'LIKE') {
        text = `${interactions.length} utilisateurs avec des intérêts similaires aux vôtres ont aimé cette recommandation.`;
      } else if (mostFrequentType === 'BOOKMARK') {
        text = `${interactions.length} utilisateurs avec des intérêts similaires aux vôtres ont enregistré cette recommandation.`;
      } else if (mostFrequentType === 'ENROLL') {
        text = `${interactions.length} utilisateurs avec des intérêts similaires aux vôtres se sont inscrits à ce cours.`;
      } else if (mostFrequentType === 'PURCHASE') {
        text = `${interactions.length} utilisateurs avec des intérêts similaires aux vôtres ont acheté cette recommandation.`;
      } else {
        text = `${interactions.length} utilisateurs avec des intérêts similaires aux vôtres ont interagi avec cette recommandation.`;
      }
      
      return { text, factors: [factor] };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'explications collaboratives: ${error.message}`);
      return null;
    }
  }

  /**
   * Génère une explication basée sur la factorisation matricielle
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explication basée sur la factorisation matricielle
   */
  private async generateMatrixFactorizationExplanation(
    userId: string,
    recommendation: any,
  ): Promise<{ text: string; factors: ExplanationFactor[] } | null> {
    // Pour la factorisation matricielle, nous utilisons une explication générique
    // car les facteurs latents sont difficiles à interpréter
    const factor: ExplanationFactor = {
      type: 'MATRIX_FACTORIZATION',
      name: 'Modèle prédictif',
      value: 'Facteurs latents',
      weight: 0.7,
    };
    
    const text = 'Notre modèle prédictif avancé a identifié des motifs cachés dans vos préférences qui correspondent à cette recommandation.';
    
    return { text, factors: [factor] };
  }

  /**
   * Génère une explication basée sur le contexte
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explication basée sur le contexte
   */
  private async generateContextualExplanation(
    userId: string,
    recommendation: any,
  ): Promise<{ text: string; factors: ExplanationFactor[] } | null> {
    try {
      const factors: ExplanationFactor[] = [];
      const metadata = recommendation.metadata || {};
      
      // Récupérer le contexte actuel
      const now = new Date();
      const month = now.getMonth();
      const season = this.getSeason(month);
      
      // Vérifier si la recommandation est saisonnière
      if (metadata.seasons && Array.isArray(metadata.seasons) && metadata.seasons.includes(season)) {
        factors.push({
          type: 'SEASONAL_MATCH',
          name: 'Saison',
          value: season,
          weight: 0.6,
        });
      }
      
      // Vérifier si la recommandation est liée à un événement actuel
      if (metadata.events && Array.isArray(metadata.events)) {
        const currentEvents = await this.getCurrentEvents();
        const matchingEvents = metadata.events.filter(event => currentEvents.includes(event));
        
        if (matchingEvents.length > 0) {
          factors.push({
            type: 'EVENT_MATCH',
            name: 'Événement',
            value: matchingEvents.join(', '),
            weight: 0.7,
          });
        }
      }
      
      // Vérifier si la recommandation est proche géographiquement
      if (metadata.location) {
        const userLocation = await this.getUserLocation(userId);
        
        if (userLocation && this.isNearby(userLocation, metadata.location)) {
          factors.push({
            type: 'LOCATION_MATCH',
            name: 'Localisation',
            value: metadata.location,
            weight: 0.8,
          });
        }
      }
      
      // Générer le texte d'explication
      let text = '';
      
      if (factors.some(f => f.type === 'SEASONAL_MATCH')) {
        text += `Cette recommandation est particulièrement pertinente pour la saison actuelle (${season}). `;
      }
      
      if (factors.some(f => f.type === 'EVENT_MATCH')) {
        const events = factors.find(f => f.type === 'EVENT_MATCH')?.value || '';
        text += `Elle est liée à des événements actuels: ${events}. `;
      }
      
      if (factors.some(f => f.type === 'LOCATION_MATCH')) {
        const location = factors.find(f => f.type === 'LOCATION_MATCH')?.value || '';
        text += `Elle se trouve à proximité de votre localisation (${location}). `;
      }
      
      if (text) {
        return { text, factors };
      }
      
      return null;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'explications contextuelles: ${error.message}`);
      return null;
    }
  }

  /**
   * Génère une explication basée sur le deep learning
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explication basée sur le deep learning
   */
  private async generateDeepLearningExplanation(
    userId: string,
    recommendation: any,
  ): Promise<{ text: string; factors: ExplanationFactor[] } | null> {
    // Pour le deep learning, nous utilisons une explication générique
    // car les modèles de deep learning sont souvent des "boîtes noires"
    const factor: ExplanationFactor = {
      type: 'DEEP_LEARNING',
      name: 'Intelligence artificielle',
      value: 'Modèle neuronal',
      weight: 0.85,
    };
    
    const text = 'Notre système d\'intelligence artificielle avancé a analysé vos préférences et comportements pour vous proposer cette recommandation personnalisée.';
    
    return { text, factors: [factor] };
  }

  /**
   * Génère une explication basée sur le temps réel
   * @param userId ID de l'utilisateur
   * @param recommendation Recommandation à expliquer
   * @returns Explication basée sur le temps réel
   */
  private async generateRealtimeExplanation(
    userId: string,
    recommendation: any,
  ): Promise<{ text: string; factors: ExplanationFactor[] } | null> {
    try {
      // Récupérer les interactions récentes de l'utilisateur
      const recentInteractions = await this.prisma.userInteraction.findMany({
        where: {
          userId,
          createdAt: {
            gte: new Date(Date.now() - 30 * 60 * 1000), // 30 dernières minutes
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 5,
        select: {
          itemId: true,
          itemType: true,
          interactionType: true,
        },
      });
      
      if (recentInteractions.length === 0) {
        return null;
      }
      
      // Récupérer les détails des items récents
      const recentItemIds = recentInteractions.map(i => i.itemId);
      const recentItems = await this.getItemsDetails(recentItemIds);
      
      if (Object.keys(recentItems).length === 0) {
        return null;
      }
      
      // Trouver des similarités entre les items récents et la recommandation
      const similarities: { type: string; value: string }[] = [];
      
      // Vérifier les catégories
      if (recommendation.metadata?.category) {
        const matchingCategories = Object.values(recentItems)
          .filter(item => item.category === recommendation.metadata.category)
          .map(item => item.title);
        
        if (matchingCategories.length > 0) {
          similarities.push({
            type: 'category',
            value: recommendation.metadata.category,
          });
        }
      }
      
      // Vérifier les tags
      if (recommendation.metadata?.tags && Array.isArray(recommendation.metadata.tags)) {
        const recentTags = new Set<string>();
        
        Object.values(recentItems).forEach(item => {
          if (item.tags && Array.isArray(item.tags)) {
            item.tags.forEach(tag => recentTags.add(tag));
          }
        });
        
        const matchingTags = recommendation.metadata.tags.filter(tag => recentTags.has(tag));
        
        if (matchingTags.length > 0) {
          similarities.push({
            type: 'tags',
            value: matchingTags.join(', '),
          });
        }
      }
      
      if (similarities.length === 0) {
        return null;
      }
      
      // Générer le facteur d'explication
      const factor: ExplanationFactor = {
        type: 'REALTIME_BEHAVIOR',
        name: 'Comportement récent',
        value: `${recentInteractions.length} interactions récentes`,
        weight: 0.9,
      };
      
      // Générer le texte d'explication
      let text = 'Basé sur votre activité récente, ';
      
      if (similarities.some(s => s.type === 'category')) {
        const category = similarities.find(s => s.type === 'category')?.value || '';
        text += `nous avons remarqué votre intérêt pour la catégorie "${category}". `;
      }
      
      if (similarities.some(s => s.type === 'tags')) {
        const tags = similarities.find(s => s.type === 'tags')?.value || '';
        text += `cette recommandation contient des sujets similaires à ceux que vous avez consultés récemment: ${tags}. `;
      }
      
      return { text, factors: [factor] };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'explications en temps réel: ${error.message}`);
      return null;
    }
  }

  /**
   * Génère une explication générique
   * @param type Type de recommandation
   * @returns Explication générique
   */
  private generateGenericExplanation(type: RecommendationType): string {
    switch (type) {
      case RecommendationType.COURSE:
        return 'Ce cours a été sélectionné pour vous en fonction de vos intérêts et préférences.';
      case RecommendationType.RETREAT:
        return 'Cette retraite a été sélectionnée pour vous en fonction de vos intérêts et préférences.';
      case RecommendationType.PARTNER:
        return 'Ce partenaire a été sélectionné pour vous en fonction de vos intérêts et préférences.';
      case RecommendationType.SOCIAL_VIDEO:
        return 'Cette vidéo a été sélectionnée pour vous en fonction de vos intérêts et préférences.';
      default:
        return 'Cette recommandation a été sélectionnée pour vous en fonction de vos intérêts et préférences.';
    }
  }

  /**
   * Calcule le niveau de confiance d'une recommandation
   * @param recommendation Recommandation
   * @param factors Facteurs d'explication
   * @returns Niveau de confiance (entre 0 et 1)
   */
  private calculateConfidence(recommendation: any, factors: ExplanationFactor[]): number {
    if (factors.length === 0) {
      return 0.5; // Confiance moyenne par défaut
    }
    
    // Calculer la confiance en fonction des poids des facteurs
    const totalWeight = factors.reduce((sum, factor) => sum + factor.weight, 0);
    const weightedConfidence = totalWeight / factors.length;
    
    // Ajuster la confiance en fonction du score de la recommandation
    const score = recommendation.score || 0.5;
    
    // Combiner le score et la confiance basée sur les facteurs
    return 0.3 * score + 0.7 * weightedConfidence;
  }

  /**
   * Récupère la saison actuelle
   * @param month Mois (0-11)
   * @returns Saison
   */
  private getSeason(month: number): string {
    if (month >= 2 && month <= 4) {
      return 'SPRING';
    } else if (month >= 5 && month <= 7) {
      return 'SUMMER';
    } else if (month >= 8 && month <= 10) {
      return 'FALL';
    } else {
      return 'WINTER';
    }
  }

  /**
   * Récupère les événements actuels
   * @returns Liste des événements actuels
   */
  private async getCurrentEvents(): Promise<string[]> {
    // Cette méthode pourrait être implémentée pour récupérer les événements actuels
    // depuis une API externe ou une base de données
    return ['MINDFULNESS_MONTH', 'WELLNESS_WEEK'];
  }

  /**
   * Récupère la localisation d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Localisation de l'utilisateur
   */
  private async getUserLocation(userId: string): Promise<string | null> {
    try {
      const userProfile = await this.prisma.userProfile.findUnique({
        where: { userId },
        select: { location: true },
      });
      
      return userProfile?.location || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Vérifie si deux localisations sont proches
   * @param location1 Première localisation
   * @param location2 Deuxième localisation
   * @returns true si les localisations sont proches
   */
  private isNearby(location1: string, location2: string): boolean {
    // Cette méthode pourrait être implémentée pour vérifier si deux localisations sont proches
    // en utilisant une API de géocodage ou une base de données de localisations
    return location1.toLowerCase() === location2.toLowerCase();
  }

  /**
   * Récupère les détails des items
   * @param itemIds IDs des items
   * @returns Détails des items
   */
  private async getItemsDetails(itemIds: string[]): Promise<Record<string, ItemDetails>> {
    try {
      const result: Record<string, ItemDetails> = {};
      
      // Récupérer les détails des cours
      const courses = await this.prisma.course.findMany({
        where: {
          id: {
            in: itemIds,
          },
        },
        select: {
          id: true,
          title: true,
          category: true,
          metadata: true,
        },
      });
      
      courses.forEach(course => {
        result[course.id] = {
          title: course.title,
          category: course.category,
          tags: course.metadata?.tags || [],
        };
      });
      
      // Récupérer les détails des retraites
      const retreats = await this.prisma.retreat.findMany({
        where: {
          id: {
            in: itemIds,
          },
        },
        select: {
          id: true,
          title: true,
          metadata: true,
        },
      });
      
      retreats.forEach(retreat => {
        result[retreat.id] = {
          title: retreat.title,
          category: retreat.metadata?.category || 'retreat',
          tags: retreat.metadata?.tags || [],
        };
      });
      
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails des items: ${error.message}`);
      return {};
    }
  }
}

/**
 * Interface pour les explications des recommandations
 */
export interface RecommendationExplanation {
  /** Texte d'explication */
  text: string;
  
  /** Facteurs d'explication */
  factors: ExplanationFactor[];
  
  /** Sources de la recommandation */
  sources: string[];
  
  /** Niveau de confiance (entre 0 et 1) */
  confidence: number;
}

/**
 * Interface pour les facteurs d'explication
 */
export interface ExplanationFactor {
  /** Type de facteur */
  type: string;
  
  /** Nom du facteur */
  name: string;
  
  /** Valeur du facteur */
  value: string;
  
  /** Poids du facteur (entre 0 et 1) */
  weight: number;
}

/**
 * Interface pour les détails d'un item
 */
interface ItemDetails {
  /** Titre de l'item */
  title: string;
  
  /** Catégorie de l'item */
  category: string;
  
  /** Tags de l'item */
  tags: string[];
}
