import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { Cron, CronExpression } from '@nestjs/schedule';
import { firstValueFrom } from 'rxjs';
import {
  ExternalData,
  ExternalDataType,
  ExternalDataSource,
  ExternalDataOptions,
  EnrichmentOptions,
  EnrichmentResult
} from '../interfaces/external-data.interface';
import { WeatherConnectorService } from './connectors/weather-connector.service';
import { EventsConnectorService } from './connectors/events-connector.service';
import { TransportConnectorService } from './connectors/transport-connector.service';

/**
 * Service d'intégration de données externes pour les recommandations
 * Enrichit les recommandations avec des données provenant de sources externes
 */
@Injectable()
export class ExternalDataService {
  private readonly logger = new Logger(ExternalDataService.name);
  private readonly enabled: boolean;
  private readonly apiKeys: Record<string, string>;
  private readonly refreshInterval: number; // en heures
  private readonly sources: ExternalDataSource[];
  private isRefreshing = false;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly weatherConnector: WeatherConnectorService,
    private readonly eventsConnector: EventsConnectorService,
    private readonly transportConnector: TransportConnectorService,
  ) {
    this.enabled = this.configService.get<boolean>('recommendation.externalData.enabled', false);
    this.refreshInterval = this.configService.get<number>('recommendation.externalData.refreshInterval', 24);

    // Configurer les clés API pour les différentes sources
    this.apiKeys = {
      googleTrends: this.configService.get<string>('recommendation.externalData.apiKeys.googleTrends', ''),
      newsApi: this.configService.get<string>('recommendation.externalData.apiKeys.newsApi', ''),
      openWeatherMap: this.configService.get<string>('recommendation.externalData.apiKeys.openWeatherMap', ''),
      eventful: this.configService.get<string>('recommendation.externalData.apiKeys.eventful', ''),
      googleMaps: this.configService.get<string>('recommendation.externalData.apiKeys.googleMaps', ''),
      sncf: this.configService.get<string>('recommendation.externalData.apiKeys.sncf', ''),
    };

    // Configurer les sources de données externes
    this.sources = [
      {
        name: 'GoogleTrends',
        enabled: this.configService.get<boolean>('recommendation.externalData.sources.googleTrends.enabled', false),
        url: 'https://trends.google.com/trends/api/dailytrends',
        apiKeyName: 'googleTrends',
        dataType: ExternalDataType.TREND,
      },
      {
        name: 'NewsAPI',
        enabled: this.configService.get<boolean>('recommendation.externalData.sources.newsApi.enabled', false),
        url: 'https://newsapi.org/v2/top-headlines',
        apiKeyName: 'newsApi',
        dataType: ExternalDataType.NEWS,
      },
      {
        name: 'OpenWeatherMap',
        enabled: this.configService.get<boolean>('recommendation.externalData.sources.openWeatherMap.enabled', false),
        url: 'https://api.openweathermap.org/data/2.5/weather',
        apiKeyName: 'openWeatherMap',
        dataType: ExternalDataType.WEATHER,
      },
      {
        name: 'Eventful',
        enabled: this.configService.get<boolean>('recommendation.externalData.sources.eventful.enabled', false),
        url: 'https://api.eventful.com/json/events/search',
        apiKeyName: 'eventful',
        dataType: ExternalDataType.EVENT,
      },
      {
        name: 'GoogleMaps',
        enabled: this.configService.get<boolean>('recommendation.externalData.sources.googleMaps.enabled', false),
        url: 'https://maps.googleapis.com/maps/api/directions/json',
        apiKeyName: 'googleMaps',
        dataType: ExternalDataType.TRANSPORT,
      },
      {
        name: 'SNCF',
        enabled: this.configService.get<boolean>('recommendation.externalData.sources.sncf.enabled', false),
        url: 'https://api.sncf.com/v1/coverage/sncf/journeys',
        apiKeyName: 'sncf',
        dataType: ExternalDataType.TRANSPORT,
      },
    ];

    this.logger.log(`ExternalDataService initialized with enabled=${this.enabled}`);
    if (this.enabled) {
      this.logger.log(`Configured sources: ${this.sources.filter(s => s.enabled).map(s => s.name).join(', ')}`);
    }
  }

  /**
   * Récupère les données externes pour enrichir les recommandations
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @returns Données externes
   */
  async getExternalData(userId: string, type: RecommendationType): Promise<ExternalData[]> {
    if (!this.enabled) {
      return [];
    }

    try {
      // Récupérer les données externes stockées en base de données
      const externalData = await this.prisma.externalData.findMany({
        where: {
          OR: [
            { userId }, // Données spécifiques à l'utilisateur
            { userId: null }, // Données globales
          ],
          applicableTypes: {
            has: type,
          },
          expiresAt: {
            gt: new Date(), // Données non expirées
          },
        },
        orderBy: {
          relevanceScore: 'desc',
        },
        take: 20, // Limiter le nombre de résultats
      });

      return externalData.map(data => ({
        id: data.id,
        type: data.dataType as ExternalDataType,
        source: data.source,
        title: data.title,
        content: data.content,
        url: data.url,
        imageUrl: data.imageUrl,
        relevanceScore: data.relevanceScore,
        metadata: data.metadata as Record<string, any>,
        createdAt: data.createdAt,
        expiresAt: data.expiresAt,
      }));
    } catch (error) {
      this.logger.error(`Error getting external data: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les données externes avec des options avancées
   * @param options Options de récupération
   * @returns Données externes
   */
  async getExternalDataWithOptions(options: ExternalDataOptions): Promise<ExternalData[]> {
    if (!this.enabled) {
      return [];
    }

    try {
      // Construire la requête
      const where: any = {
        expiresAt: {
          gt: new Date(), // Données non expirées
        },
      };

      // Filtrer par utilisateur
      if (options.userId) {
        where.OR = [
          { userId: options.userId },
          { userId: null },
        ];
      }

      // Filtrer par type de recommandation
      if (options.recommendationType) {
        where.applicableTypes = {
          has: options.recommendationType,
        };
      }

      // Filtrer par types de données
      if (options.includeTypes && options.includeTypes.length > 0) {
        where.dataType = {
          in: options.includeTypes,
        };
      }

      // Exclure certains types de données
      if (options.excludeTypes && options.excludeTypes.length > 0) {
        if (where.dataType) {
          where.dataType.notIn = options.excludeTypes;
        } else {
          where.dataType = {
            notIn: options.excludeTypes,
          };
        }
      }

      // Filtrer par sources
      if (options.includeSources && options.includeSources.length > 0) {
        where.source = {
          in: options.includeSources,
        };
      }

      // Exclure certaines sources
      if (options.excludeSources && options.excludeSources.length > 0) {
        if (where.source) {
          where.source.notIn = options.excludeSources;
        } else {
          where.source = {
            notIn: options.excludeSources,
          };
        }
      }

      // Filtrer par score de pertinence minimum
      if (options.minRelevanceScore) {
        where.relevanceScore = {
          gte: options.minRelevanceScore,
        };
      }

      // Récupérer les données
      const externalData = await this.prisma.externalData.findMany({
        where,
        orderBy: {
          relevanceScore: 'desc',
        },
        take: options.limit || 20,
      });

      return externalData.map(data => ({
        id: data.id,
        type: data.dataType as ExternalDataType,
        source: data.source,
        title: data.title,
        content: data.content,
        url: data.url,
        imageUrl: data.imageUrl,
        relevanceScore: data.relevanceScore,
        metadata: data.metadata as Record<string, any>,
        createdAt: data.createdAt,
        expiresAt: data.expiresAt,
      }));
    } catch (error) {
      this.logger.error(`Error getting external data with options: ${error.message}`);
      return [];
    }
  }

  /**
   * Récupère les données externes basées sur la localisation
   * @param location Localisation (ville ou coordonnées)
   * @param options Options supplémentaires
   * @returns Données externes
   */
  async getLocationBasedData(
    location: string | { latitude: number; longitude: number },
    options: Partial<ExternalDataOptions> = {}
  ): Promise<ExternalData[]> {
    if (!this.enabled) {
      return [];
    }

    try {
      const results: ExternalData[] = [];

      // Récupérer les données météo
      if (!options.excludeTypes || !options.excludeTypes.includes(ExternalDataType.WEATHER)) {
        const weatherLocation = typeof location === 'string'
          ? location
          : { lat: location.latitude, lon: location.longitude };

        const weatherData = await this.weatherConnector.getCurrentWeather(weatherLocation);
        results.push(...weatherData);

        // Récupérer les prévisions météo si demandé
        if (options.date) {
          const forecastData = await this.weatherConnector.getForecast(weatherLocation, 5);
          results.push(...forecastData);
        }
      }

      // Récupérer les événements à proximité
      if (!options.excludeTypes || !options.excludeTypes.includes(ExternalDataType.EVENT)) {
        const eventsLocation = typeof location === 'string'
          ? location
          : { lat: location.latitude, lon: location.longitude };

        const eventsData = await this.eventsConnector.getNearbyEvents(
          eventsLocation,
          10, // 10km de rayon
          [], // Toutes catégories
          options.date,
          undefined,
          options.limit || 5
        );
        results.push(...eventsData);
      }

      return results;
    } catch (error) {
      this.logger.error(`Error getting location-based data: ${error.message}`);
      return [];
    }
  }

  /**
   * Enrichit les recommandations avec des données externes
   * @param userId ID de l'utilisateur
   * @param recommendations Recommandations à enrichir
   * @returns Recommandations enrichies
   */
  async enrichRecommendations(userId: string, recommendations: any[]): Promise<any[]> {
    if (!this.enabled || recommendations.length === 0) {
      return recommendations;
    }

    try {
      // Récupérer le type de recommandation
      const type = recommendations[0].type;

      // Récupérer les données externes
      const externalData = await this.getExternalData(userId, type);
      if (externalData.length === 0) {
        return recommendations;
      }

      // Enrichir les recommandations
      return recommendations.map(recommendation => {
        // Trouver les données externes pertinentes pour cette recommandation
        const relevantData = this.findRelevantExternalData(recommendation, externalData);

        if (relevantData.length > 0) {
          return {
            ...recommendation,
            externalData: relevantData,
          };
        }

        return recommendation;
      });
    } catch (error) {
      this.logger.error(`Error enriching recommendations: ${error.message}`);
      return recommendations;
    }
  }

  /**
   * Enrichit un élément avec des données externes
   * @param item Élément à enrichir
   * @param options Options d'enrichissement
   * @returns Résultat d'enrichissement
   */
  async enrichItem(item: any, options: EnrichmentOptions = {}): Promise<EnrichmentResult> {
    if (!this.enabled || !item) {
      return {
        itemId: item.id,
        itemType: item.type || 'unknown',
        externalData: [],
        enrichmentScore: 0,
      };
    }

    try {
      // Déterminer le type d'élément
      const itemType = item.type || (item.retreatType ? 'RETREAT' : 'unknown');

      // Récupérer les données externes
      let externalData: ExternalData[] = [];

      // Si l'élément a une localisation, récupérer les données basées sur la localisation
      if (item.location || (item.latitude && item.longitude)) {
        const location = item.location || { latitude: item.latitude, longitude: item.longitude };
        const locationData = await this.getLocationBasedData(location, {
          includeTypes: options.dataTypes,
          minRelevanceScore: options.minRelevanceScore,
          limit: options.maxItemsPerType,
        });
        externalData.push(...locationData);
      }

      // Récupérer d'autres données externes
      const otherData = await this.getExternalDataWithOptions({
        recommendationType: this.mapItemTypeToRecommendationType(itemType),
        includeTypes: options.dataTypes,
        minRelevanceScore: options.minRelevanceScore,
        limit: options.maxItemsPerType,
        keywords: this.extractKeywords(item),
      });

      // Fusionner les données
      externalData = this.mergeExternalData(externalData, otherData, options.mergeStrategy || 'append');

      // Calculer le score d'enrichissement
      const enrichmentScore = this.calculateEnrichmentScore(externalData);

      return {
        itemId: item.id,
        itemType,
        externalData,
        enrichmentScore,
        metadata: {
          enrichedAt: new Date(),
          dataTypes: [...new Set(externalData.map(data => data.type))],
          sources: [...new Set(externalData.map(data => data.source))],
        },
      };
    } catch (error) {
      this.logger.error(`Error enriching item: ${error.message}`);
      return {
        itemId: item.id,
        itemType: item.type || 'unknown',
        externalData: [],
        enrichmentScore: 0,
      };
    }
  }

  /**
   * Enrichit plusieurs éléments avec des données externes
   * @param items Éléments à enrichir
   * @param options Options d'enrichissement
   * @returns Résultats d'enrichissement
   */
  async enrichItems(items: any[], options: EnrichmentOptions = {}): Promise<EnrichmentResult[]> {
    if (!this.enabled || !items || items.length === 0) {
      return [];
    }

    try {
      // Enrichir chaque élément
      const results: EnrichmentResult[] = [];

      for (const item of items) {
        const result = await this.enrichItem(item, options);
        results.push(result);
      }

      return results;
    } catch (error) {
      this.logger.error(`Error enriching items: ${error.message}`);
      return [];
    }
  }

  /**
   * Trouve les données externes pertinentes pour une recommandation
   * @param recommendation Recommandation
   * @param externalData Données externes disponibles
   * @returns Données externes pertinentes
   */
  private findRelevantExternalData(recommendation: any, externalData: ExternalData[]): ExternalData[] {
    // Extraire les mots-clés de la recommandation
    const keywords = this.extractKeywords(recommendation);

    if (keywords.length === 0) {
      return [];
    }

    // Calculer la pertinence de chaque donnée externe
    const scoredData = externalData.map(data => {
      const score = this.calculateRelevanceScore(data, keywords);
      return { ...data, matchScore: score };
    });

    // Filtrer et trier les données par score de pertinence
    return scoredData
      .filter(data => data.matchScore > 0.3) // Seuil minimum de pertinence
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, 3) // Limiter à 3 données externes par recommandation
      .map(({ matchScore, ...data }) => data); // Supprimer le score de correspondance
  }

  /**
   * Extrait les mots-clés d'une recommandation
   * @param recommendation Recommandation
   * @returns Mots-clés
   */
  private extractKeywords(recommendation: any): string[] {
    const keywords = new Set<string>();

    // Extraire les mots-clés du titre
    if (recommendation.title) {
      const titleWords = recommendation.title.toLowerCase().split(/\s+/);
      titleWords.forEach(word => {
        if (word.length > 3) {
          keywords.add(word);
        }
      });
    }

    // Extraire les mots-clés de la description
    if (recommendation.description) {
      const descWords = recommendation.description.toLowerCase().split(/\s+/);
      descWords.forEach(word => {
        if (word.length > 3) {
          keywords.add(word);
        }
      });
    }

    // Extraire les mots-clés des métadonnées
    if (recommendation.metadata) {
      // Catégorie
      if (recommendation.metadata.category) {
        keywords.add(recommendation.metadata.category.toLowerCase());
      }

      // Tags
      if (recommendation.metadata.tags && Array.isArray(recommendation.metadata.tags)) {
        recommendation.metadata.tags.forEach((tag: string) => {
          keywords.add(tag.toLowerCase());
        });
      }
    }

    return Array.from(keywords);
  }

  /**
   * Fusionne deux ensembles de données externes
   * @param data1 Premier ensemble de données
   * @param data2 Deuxième ensemble de données
   * @param strategy Stratégie de fusion
   * @returns Données fusionnées
   */
  private mergeExternalData(
    data1: ExternalData[],
    data2: ExternalData[],
    strategy: 'append' | 'replace' | 'merge' = 'append'
  ): ExternalData[] {
    switch (strategy) {
      case 'replace':
        return data2;
      case 'merge':
        // Fusionner en évitant les doublons (basés sur le titre)
        const merged = [...data1];
        const titles = new Set(data1.map(d => d.title));

        for (const item of data2) {
          if (!titles.has(item.title)) {
            merged.push(item);
            titles.add(item.title);
          }
        }

        return merged;
      case 'append':
      default:
        return [...data1, ...data2];
    }
  }

  /**
   * Calcule le score d'enrichissement global
   * @param data Données externes
   * @returns Score d'enrichissement (entre 0 et 1)
   */
  private calculateEnrichmentScore(data: ExternalData[]): number {
    if (data.length === 0) {
      return 0;
    }

    // Calculer la moyenne pondérée des scores de pertinence
    const totalScore = data.reduce((sum, item) => sum + (item.relevanceScore || 0.5), 0);
    const avgScore = totalScore / data.length;

    // Appliquer un bonus basé sur la diversité des sources et des types
    const uniqueSources = new Set(data.map(d => d.source)).size;
    const uniqueTypes = new Set(data.map(d => d.type)).size;

    const diversityBonus = Math.min(0.2, (uniqueSources + uniqueTypes) * 0.05);

    return Math.min(1, avgScore + diversityBonus);
  }

  /**
   * Mappe un type d'élément à un type de recommandation
   * @param itemType Type d'élément
   * @returns Type de recommandation
   */
  private mapItemTypeToRecommendationType(itemType: string): RecommendationType {
    switch (itemType.toUpperCase()) {
      case 'RETREAT':
        return RecommendationType.RETREAT;
      case 'COURSE':
        return RecommendationType.COURSE;
      case 'PARTNER':
        return RecommendationType.PARTNER;
      case 'EVENT':
        return RecommendationType.EVENT;
      case 'ACTIVITY':
        return RecommendationType.ACTIVITY;
      default:
        return RecommendationType.RETREAT;
    }
  }

  /**
   * Calcule le score de pertinence d'une donnée externe par rapport à des mots-clés
   * @param data Donnée externe
   * @param keywords Mots-clés
   * @returns Score de pertinence (entre 0 et 1)
   */
  private calculateRelevanceScore(data: ExternalData, keywords: string[]): number {
    if (keywords.length === 0) {
      return 0;
    }

    let matches = 0;

    // Vérifier les correspondances dans le titre
    if (data.title) {
      const title = data.title.toLowerCase();
      keywords.forEach(keyword => {
        if (title.includes(keyword)) {
          matches += 2; // Le titre a un poids plus important
        }
      });
    }

    // Vérifier les correspondances dans le contenu
    if (data.content) {
      const content = data.content.toLowerCase();
      keywords.forEach(keyword => {
        if (content.includes(keyword)) {
          matches += 1;
        }
      });
    }

    // Normaliser le score
    return Math.min(1, matches / (keywords.length * 3));
  }

  /**
   * Rafraîchit les données externes
   * Exécuté toutes les X heures (configurable)
   */
  @Cron('0 */6 * * *') // Toutes les 6 heures par défaut
  async refreshExternalData(): Promise<void> {
    if (!this.enabled || this.isRefreshing) {
      return;
    }

    this.isRefreshing = true;
    this.logger.log('Starting refresh of external data');

    try {
      // Récupérer les données de chaque source activée
      for (const source of this.sources.filter(s => s.enabled)) {
        await this.fetchDataFromSource(source);
      }

      // Supprimer les données expirées
      await this.cleanupExpiredData();

      this.logger.log('External data refresh completed');
    } catch (error) {
      this.logger.error(`Error refreshing external data: ${error.message}`);
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Récupère les données d'une source externe
   * @param source Source de données
   */
  private async fetchDataFromSource(source: ExternalDataSource): Promise<void> {
    try {
      this.logger.log(`Fetching data from ${source.name}`);

      // Construire les paramètres de la requête
      const params: Record<string, string> = {};

      // Ajouter la clé API si nécessaire
      if (source.apiKeyName && this.apiKeys[source.apiKeyName]) {
        params.apiKey = this.apiKeys[source.apiKeyName];
      }

      // Ajouter des paramètres spécifiques à chaque source
      switch (source.name) {
        case 'GoogleTrends':
          params.geo = 'FR';
          params.hl = 'fr';
          break;
        case 'NewsAPI':
          params.country = 'fr';
          params.category = 'health';
          break;
        case 'OpenWeatherMap':
          params.q = 'Paris';
          params.units = 'metric';
          params.lang = 'fr';
          break;
      }

      // Effectuer la requête
      const response = await firstValueFrom(
        this.httpService.get(source.url, { params })
      );

      // Traiter les données selon la source
      const processedData = this.processSourceData(source, response.data);

      // Enregistrer les données en base de données
      await this.saveExternalData(processedData);

      this.logger.log(`Fetched ${processedData.length} items from ${source.name}`);
    } catch (error) {
      this.logger.error(`Error fetching data from ${source.name}: ${error.message}`);
    }
  }

  /**
   * Traite les données brutes d'une source externe
   * @param source Source de données
   * @param rawData Données brutes
   * @returns Données traitées
   */
  private processSourceData(source: ExternalDataSource, rawData: any): Partial<ExternalData>[] {
    const processedData: Partial<ExternalData>[] = [];

    try {
      switch (source.name) {
        case 'GoogleTrends':
          // Traitement spécifique pour Google Trends
          if (rawData.default && rawData.default.trendingSearchesDays) {
            rawData.default.trendingSearchesDays.forEach((day: any) => {
              day.trendingSearches.forEach((trend: any) => {
                processedData.push({
                  type: 'TREND' as ExternalDataType,
                  source: source.name,
                  title: trend.title.query,
                  content: trend.articles[0]?.snippet || '',
                  url: trend.articles[0]?.url || '',
                  imageUrl: trend.image?.imageUrl || '',
                  relevanceScore: 0.7,
                  metadata: {
                    trafficCount: trend.formattedTraffic,
                    relatedQueries: trend.relatedQueries.map((q: any) => q.query),
                  },
                  applicableTypes: [RecommendationType.COURSE, RecommendationType.RETREAT],
                });
              });
            });
          }
          break;

        case 'NewsAPI':
          // Traitement spécifique pour News API
          if (rawData.articles) {
            rawData.articles.forEach((article: any) => {
              processedData.push({
                type: 'NEWS' as ExternalDataType,
                source: source.name,
                title: article.title,
                content: article.description || '',
                url: article.url,
                imageUrl: article.urlToImage || '',
                relevanceScore: 0.8,
                metadata: {
                  author: article.author,
                  publishedAt: article.publishedAt,
                  sourceName: article.source.name,
                },
                applicableTypes: [RecommendationType.COURSE, RecommendationType.RETREAT, RecommendationType.PARTNER],
              });
            });
          }
          break;

        case 'OpenWeatherMap':
          // Traitement spécifique pour OpenWeatherMap
          if (rawData.weather && rawData.main) {
            processedData.push({
              type: 'WEATHER' as ExternalDataType,
              source: source.name,
              title: `Météo: ${rawData.weather[0].description}`,
              content: `Température: ${rawData.main.temp}°C, Ressenti: ${rawData.main.feels_like}°C`,
              url: '',
              imageUrl: `https://openweathermap.org/img/wn/${rawData.weather[0].icon}@2x.png`,
              relevanceScore: 0.6,
              metadata: {
                temperature: rawData.main.temp,
                humidity: rawData.main.humidity,
                windSpeed: rawData.wind.speed,
                location: rawData.name,
                country: rawData.sys.country,
              },
              applicableTypes: [RecommendationType.RETREAT],
            });
          }
          break;
      }
    } catch (error) {
      this.logger.error(`Error processing data from ${source.name}: ${error.message}`);
    }

    return processedData;
  }

  /**
   * Enregistre les données externes en base de données
   * @param data Données à enregistrer
   */
  private async saveExternalData(data: Partial<ExternalData>[]): Promise<void> {
    if (data.length === 0) {
      return;
    }

    try {
      // Calculer la date d'expiration (par défaut: 24h)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + this.refreshInterval);

      // Enregistrer chaque donnée
      for (const item of data) {
        await this.prisma.externalData.create({
          data: {
            dataType: item.type as string,
            source: item.source,
            title: item.title,
            content: item.content || '',
            url: item.url || '',
            imageUrl: item.imageUrl || '',
            relevanceScore: item.relevanceScore || 0.5,
            metadata: item.metadata || {},
            applicableTypes: item.applicableTypes || [RecommendationType.COURSE],
            createdAt: new Date(),
            expiresAt,
          },
        });
      }
    } catch (error) {
      this.logger.error(`Error saving external data: ${error.message}`);
    }
  }

  /**
   * Supprime les données externes expirées
   */
  private async cleanupExpiredData(): Promise<void> {
    try {
      const result = await this.prisma.externalData.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      this.logger.log(`Deleted ${result.count} expired external data items`);
    } catch (error) {
      this.logger.error(`Error cleaning up expired data: ${error.message}`);
    }
  }
}

/**
 * Interface pour une source de données externe
 */
interface ExternalDataSource {
  name: string;
  enabled: boolean;
  url: string;
  apiKeyName?: string;
  dataType: string;
}

/**
 * Types de données externes
 */
export type ExternalDataType = 'TREND' | 'NEWS' | 'WEATHER' | 'EVENT';

/**
 * Interface pour les données externes
 */
export interface ExternalData {
  id: string;
  type: ExternalDataType;
  source: string;
  title: string;
  content: string;
  url: string;
  imageUrl: string;
  relevanceScore: number;
  metadata: Record<string, any>;
  applicableTypes: RecommendationType[];
  createdAt: Date;
  expiresAt: Date;
}
