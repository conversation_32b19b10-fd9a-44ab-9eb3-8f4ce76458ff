import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * Interface pour les métriques d'équité
 */
export interface FairnessMetrics {
  /** Score d'équité global (0-1) */
  overallFairnessScore: number;
  
  /** Score d'équité de représentation des catégories (0-1) */
  categoryRepresentationScore: number;
  
  /** Score d'équité de représentation des prix (0-1) */
  priceRepresentationScore: number;
  
  /** Score d'équité de représentation des localisations (0-1) */
  locationRepresentationScore: number;
  
  /** Score d'équité de représentation des fournisseurs (0-1) */
  providerRepresentationScore: number;
  
  /** Score d'équité de représentation des thèmes (0-1) */
  themeRepresentationScore: number;
  
  /** Distribution des catégories */
  categoryDistribution: Record<string, number>;
  
  /** Distribution des plages de prix */
  priceDistribution: Record<string, number>;
  
  /** Distribution des localisations */
  locationDistribution: Record<string, number>;
  
  /** Distribution des fournisseurs */
  providerDistribution: Record<string, number>;
  
  /** Distribution des thèmes */
  themeDistribution: Record<string, number>;
  
  /** Biais détectés */
  detectedBiases: Array<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

/**
 * Interface pour les contraintes d'équité
 */
export interface FairnessConstraints {
  /** Poids de l'équité des catégories (0-1) */
  categoryWeight: number;
  
  /** Poids de l'équité des prix (0-1) */
  priceWeight: number;
  
  /** Poids de l'équité des localisations (0-1) */
  locationWeight: number;
  
  /** Poids de l'équité des fournisseurs (0-1) */
  providerWeight: number;
  
  /** Poids de l'équité des thèmes (0-1) */
  themeWeight: number;
  
  /** Distribution cible des catégories */
  targetCategoryDistribution?: Record<string, number>;
  
  /** Distribution cible des plages de prix */
  targetPriceDistribution?: Record<string, number>;
  
  /** Distribution cible des localisations */
  targetLocationDistribution?: Record<string, number>;
  
  /** Distribution cible des fournisseurs */
  targetProviderDistribution?: Record<string, number>;
  
  /** Distribution cible des thèmes */
  targetThemeDistribution?: Record<string, number>;
  
  /** Score d'équité minimum (0-1) */
  minFairnessScore: number;
}

/**
 * Interface pour les options d'équité
 */
export interface FairnessOptions {
  /** Contraintes d'équité */
  constraints: FairnessConstraints;
  
  /** Facteur d'équilibre entre pertinence et équité (0-1) */
  fairnessFactor: number;
  
  /** Taille maximale de la liste de recommandations */
  maxRecommendations: number;
  
  /** Appliquer une correction d'équité */
  applyFairnessCorrection: boolean;
}

/**
 * Service pour l'équité algorithmique des recommandations
 */
@Injectable()
export class FairnessService {
  private readonly logger = new Logger(FairnessService.name);
  private defaultConstraints: FairnessConstraints;
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // Initialiser les contraintes par défaut
    this.defaultConstraints = {
      categoryWeight: 0.2,
      priceWeight: 0.2,
      locationWeight: 0.2,
      providerWeight: 0.2,
      themeWeight: 0.2,
      minFairnessScore: 0.7,
    };
  }
  
  /**
   * Applique des contraintes d'équité à une liste de recommandations
   * @param userId ID de l'utilisateur
   * @param recommendations Liste de recommandations
   * @param options Options d'équité
   * @returns Liste de recommandations équitable
   */
  async applyFairness(
    userId: string,
    recommendations: any[],
    options?: Partial<FairnessOptions>,
  ): Promise<any[]> {
    try {
      // Fusionner les options avec les valeurs par défaut
      const fullOptions: FairnessOptions = {
        constraints: options?.constraints || this.defaultConstraints,
        fairnessFactor: options?.fairnessFactor !== undefined ? options.fairnessFactor : 0.3,
        maxRecommendations: options?.maxRecommendations || 10,
        applyFairnessCorrection: options?.applyFairnessCorrection !== undefined ? options.applyFairnessCorrection : true,
      };
      
      // Si la liste est vide, retourner une liste vide
      if (!recommendations || recommendations.length === 0) {
        return [];
      }
      
      // Calculer les métriques d'équité actuelles
      const currentMetrics = await this.calculateFairnessMetrics(recommendations);
      
      // Si les métriques actuelles sont suffisantes et qu'on ne veut pas appliquer de correction, retourner la liste telle quelle
      if (
        currentMetrics.overallFairnessScore >= fullOptions.constraints.minFairnessScore &&
        !fullOptions.applyFairnessCorrection
      ) {
        return recommendations.slice(0, fullOptions.maxRecommendations);
      }
      
      // Récupérer les distributions cibles si elles ne sont pas spécifiées
      const constraints = await this.enrichConstraints(fullOptions.constraints);
      
      // Appliquer l'algorithme d'équité
      const fairRecommendations = await this.applyFairnessAlgorithm(
        userId,
        recommendations,
        { ...fullOptions, constraints },
      );
      
      // Émettre un événement d'équité
      this.eventEmitter.emit('recommendation.fairness.applied', {
        userId,
        originalCount: recommendations.length,
        fairCount: fairRecommendations.length,
        originalMetrics: currentMetrics,
        finalMetrics: await this.calculateFairnessMetrics(fairRecommendations),
      });
      
      return fairRecommendations.slice(0, fullOptions.maxRecommendations);
    } catch (error) {
      this.logger.error(`Erreur lors de l'application de l'équité: ${error.message}`);
      // En cas d'erreur, retourner la liste originale
      return recommendations.slice(0, options?.maxRecommendations || 10);
    }
  }
  
  /**
   * Calcule les métriques d'équité d'une liste de recommandations
   * @param recommendations Liste de recommandations
   * @returns Métriques d'équité
   */
  async calculateFairnessMetrics(recommendations: any[]): Promise<FairnessMetrics> {
    // Calculer les distributions
    const categoryDistribution = this.calculateDistribution(recommendations, 'category');
    const priceDistribution = this.calculateDistribution(recommendations, 'price', (price) => this.getPriceRange(price));
    const locationDistribution = this.calculateDistribution(recommendations, 'location');
    const providerDistribution = this.calculateDistribution(recommendations, 'providerId');
    const themeDistribution = this.calculateDistribution(recommendations, 'theme');
    
    // Récupérer les distributions idéales
    const idealDistributions = await this.getIdealDistributions();
    
    // Calculer les scores d'équité
    const categoryRepresentationScore = this.calculateDistributionSimilarity(
      categoryDistribution,
      idealDistributions.categoryDistribution,
    );
    
    const priceRepresentationScore = this.calculateDistributionSimilarity(
      priceDistribution,
      idealDistributions.priceDistribution,
    );
    
    const locationRepresentationScore = this.calculateDistributionSimilarity(
      locationDistribution,
      idealDistributions.locationDistribution,
    );
    
    const providerRepresentationScore = this.calculateDistributionSimilarity(
      providerDistribution,
      idealDistributions.providerDistribution,
    );
    
    const themeRepresentationScore = this.calculateDistributionSimilarity(
      themeDistribution,
      idealDistributions.themeDistribution,
    );
    
    // Calculer le score global
    const overallFairnessScore = (
      categoryRepresentationScore * 0.2 +
      priceRepresentationScore * 0.2 +
      locationRepresentationScore * 0.2 +
      providerRepresentationScore * 0.2 +
      themeRepresentationScore * 0.2
    );
    
    // Détecter les biais
    const detectedBiases = this.detectBiases({
      categoryDistribution,
      priceDistribution,
      locationDistribution,
      providerDistribution,
      themeDistribution,
      idealDistributions,
    });
    
    return {
      overallFairnessScore,
      categoryRepresentationScore,
      priceRepresentationScore,
      locationRepresentationScore,
      providerRepresentationScore,
      themeRepresentationScore,
      categoryDistribution,
      priceDistribution,
      locationDistribution,
      providerDistribution,
      themeDistribution,
      detectedBiases,
    };
  }
  
  /**
   * Applique l'algorithme d'équité
   * @param userId ID de l'utilisateur
   * @param recommendations Liste de recommandations
   * @param options Options d'équité
   * @returns Liste de recommandations équitable
   */
  private async applyFairnessAlgorithm(
    userId: string,
    recommendations: any[],
    options: FairnessOptions,
  ): Promise<any[]> {
    // Copier la liste de recommandations
    const originalRecommendations = [...recommendations];
    const fairRecommendations = [];
    
    // Récupérer les distributions cibles
    const {
      targetCategoryDistribution,
      targetPriceDistribution,
      targetLocationDistribution,
      targetProviderDistribution,
      targetThemeDistribution,
    } = options.constraints;
    
    // Initialiser les compteurs de distribution
    const currentCategoryDistribution: Record<string, number> = {};
    const currentPriceDistribution: Record<string, number> = {};
    const currentLocationDistribution: Record<string, number> = {};
    const currentProviderDistribution: Record<string, number> = {};
    const currentThemeDistribution: Record<string, number> = {};
    
    // Initialiser les distributions cibles
    Object.keys(targetCategoryDistribution || {}).forEach(key => currentCategoryDistribution[key] = 0);
    Object.keys(targetPriceDistribution || {}).forEach(key => currentPriceDistribution[key] = 0);
    Object.keys(targetLocationDistribution || {}).forEach(key => currentLocationDistribution[key] = 0);
    Object.keys(targetProviderDistribution || {}).forEach(key => currentProviderDistribution[key] = 0);
    Object.keys(targetThemeDistribution || {}).forEach(key => currentThemeDistribution[key] = 0);
    
    // Trier les recommandations par score
    originalRecommendations.sort((a, b) => b.score - a.score);
    
    // Ajouter les recommandations une par une en maximisant l'équité
    while (
      fairRecommendations.length < options.maxRecommendations &&
      originalRecommendations.length > 0
    ) {
      let bestRecommendation = null;
      let bestScore = -Infinity;
      
      for (const recommendation of originalRecommendations) {
        // Calculer le score d'équité si on ajoute cette recommandation
        const category = recommendation.category || 'unknown';
        const priceRange = this.getPriceRange(recommendation.price);
        const location = recommendation.location || 'unknown';
        const providerId = recommendation.providerId || 'unknown';
        const theme = recommendation.theme || 'unknown';
        
        // Créer des copies des distributions actuelles
        const tempCategoryDist = { ...currentCategoryDistribution };
        const tempPriceDist = { ...currentPriceDistribution };
        const tempLocationDist = { ...currentLocationDistribution };
        const tempProviderDist = { ...currentProviderDistribution };
        const tempThemeDist = { ...currentThemeDistribution };
        
        // Mettre à jour les distributions temporaires
        tempCategoryDist[category] = (tempCategoryDist[category] || 0) + 1;
        tempPriceDist[priceRange] = (tempPriceDist[priceRange] || 0) + 1;
        tempLocationDist[location] = (tempLocationDist[location] || 0) + 1;
        tempProviderDist[providerId] = (tempProviderDist[providerId] || 0) + 1;
        tempThemeDist[theme] = (tempThemeDist[theme] || 0) + 1;
        
        // Normaliser les distributions
        const totalItems = fairRecommendations.length + 1;
        Object.keys(tempCategoryDist).forEach(key => tempCategoryDist[key] = tempCategoryDist[key] / totalItems);
        Object.keys(tempPriceDist).forEach(key => tempPriceDist[key] = tempPriceDist[key] / totalItems);
        Object.keys(tempLocationDist).forEach(key => tempLocationDist[key] = tempLocationDist[key] / totalItems);
        Object.keys(tempProviderDist).forEach(key => tempProviderDist[key] = tempProviderDist[key] / totalItems);
        Object.keys(tempThemeDist).forEach(key => tempThemeDist[key] = tempThemeDist[key] / totalItems);
        
        // Calculer les scores de similarité avec les distributions cibles
        const categorySimilarity = this.calculateDistributionSimilarity(
          tempCategoryDist,
          targetCategoryDistribution || {},
        );
        
        const priceSimilarity = this.calculateDistributionSimilarity(
          tempPriceDist,
          targetPriceDistribution || {},
        );
        
        const locationSimilarity = this.calculateDistributionSimilarity(
          tempLocationDist,
          targetLocationDistribution || {},
        );
        
        const providerSimilarity = this.calculateDistributionSimilarity(
          tempProviderDist,
          targetProviderDistribution || {},
        );
        
        const themeSimilarity = this.calculateDistributionSimilarity(
          tempThemeDist,
          targetThemeDistribution || {},
        );
        
        // Calculer le score d'équité global
        const fairnessScore = (
          categorySimilarity * options.constraints.categoryWeight +
          priceSimilarity * options.constraints.priceWeight +
          locationSimilarity * options.constraints.locationWeight +
          providerSimilarity * options.constraints.providerWeight +
          themeSimilarity * options.constraints.themeWeight
        );
        
        // Calculer le score de pertinence normalisé
        const relevanceScore = recommendation.score / originalRecommendations[0].score;
        
        // Calculer le score combiné
        const combinedScore = (
          options.fairnessFactor * fairnessScore +
          (1 - options.fairnessFactor) * relevanceScore
        );
        
        // Mettre à jour la meilleure recommandation
        if (combinedScore > bestScore) {
          bestScore = combinedScore;
          bestRecommendation = recommendation;
        }
      }
      
      // Ajouter la meilleure recommandation
      if (bestRecommendation) {
        fairRecommendations.push(bestRecommendation);
        originalRecommendations.splice(originalRecommendations.indexOf(bestRecommendation), 1);
        
        // Mettre à jour les distributions actuelles
        const category = bestRecommendation.category || 'unknown';
        const priceRange = this.getPriceRange(bestRecommendation.price);
        const location = bestRecommendation.location || 'unknown';
        const providerId = bestRecommendation.providerId || 'unknown';
        const theme = bestRecommendation.theme || 'unknown';
        
        currentCategoryDistribution[category] = (currentCategoryDistribution[category] || 0) + 1;
        currentPriceDistribution[priceRange] = (currentPriceDistribution[priceRange] || 0) + 1;
        currentLocationDistribution[location] = (currentLocationDistribution[location] || 0) + 1;
        currentProviderDistribution[providerId] = (currentProviderDistribution[providerId] || 0) + 1;
        currentThemeDistribution[theme] = (currentThemeDistribution[theme] || 0) + 1;
      } else {
        break;
      }
    }
    
    return fairRecommendations;
  }
  
  /**
   * Enrichit les contraintes d'équité avec les distributions cibles
   * @param constraints Contraintes d'équité
   * @returns Contraintes d'équité enrichies
   */
  private async enrichConstraints(constraints: FairnessConstraints): Promise<FairnessConstraints> {
    // Si toutes les distributions cibles sont déjà spécifiées, retourner les contraintes telles quelles
    if (
      constraints.targetCategoryDistribution &&
      constraints.targetPriceDistribution &&
      constraints.targetLocationDistribution &&
      constraints.targetProviderDistribution &&
      constraints.targetThemeDistribution
    ) {
      return constraints;
    }
    
    // Récupérer les distributions idéales
    const idealDistributions = await this.getIdealDistributions();
    
    // Enrichir les contraintes
    return {
      ...constraints,
      targetCategoryDistribution: constraints.targetCategoryDistribution || idealDistributions.categoryDistribution,
      targetPriceDistribution: constraints.targetPriceDistribution || idealDistributions.priceDistribution,
      targetLocationDistribution: constraints.targetLocationDistribution || idealDistributions.locationDistribution,
      targetProviderDistribution: constraints.targetProviderDistribution || idealDistributions.providerDistribution,
      targetThemeDistribution: constraints.targetThemeDistribution || idealDistributions.themeDistribution,
    };
  }
  
  /**
   * Récupère les distributions idéales
   * @returns Distributions idéales
   */
  private async getIdealDistributions(): Promise<{
    categoryDistribution: Record<string, number>;
    priceDistribution: Record<string, number>;
    locationDistribution: Record<string, number>;
    providerDistribution: Record<string, number>;
    themeDistribution: Record<string, number>;
  }> {
    try {
      // Récupérer les distributions à partir de la base de données
      // Dans une implémentation réelle, on calculerait ces distributions à partir des données
      
      // Distribution des catégories
      const categoryDistribution = {
        'yoga': 0.25,
        'meditation': 0.2,
        'wellness': 0.15,
        'fitness': 0.15,
        'spiritual': 0.1,
        'nature': 0.1,
        'other': 0.05,
      };
      
      // Distribution des plages de prix
      const priceDistribution = {
        'budget': 0.3,
        'standard': 0.5,
        'premium': 0.2,
      };
      
      // Distribution des localisations
      const locationDistribution = {
        'europe': 0.4,
        'asia': 0.3,
        'americas': 0.2,
        'africa': 0.05,
        'oceania': 0.05,
      };
      
      // Distribution des fournisseurs
      // Dans une implémentation réelle, on récupérerait cette distribution à partir de la base de données
      const providerDistribution = {
        'small': 0.4,
        'medium': 0.4,
        'large': 0.2,
      };
      
      // Distribution des thèmes
      const themeDistribution = {
        'relaxation': 0.3,
        'self-discovery': 0.2,
        'healing': 0.15,
        'adventure': 0.15,
        'learning': 0.1,
        'other': 0.1,
      };
      
      return {
        categoryDistribution,
        priceDistribution,
        locationDistribution,
        providerDistribution,
        themeDistribution,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des distributions idéales: ${error.message}`);
      
      // Retourner des distributions par défaut en cas d'erreur
      return {
        categoryDistribution: { 'unknown': 1 },
        priceDistribution: { 'unknown': 1 },
        locationDistribution: { 'unknown': 1 },
        providerDistribution: { 'unknown': 1 },
        themeDistribution: { 'unknown': 1 },
      };
    }
  }
  
  /**
   * Calcule la distribution d'un attribut dans une liste de recommandations
   * @param recommendations Liste de recommandations
   * @param attribute Attribut à analyser
   * @param transform Fonction de transformation optionnelle
   * @returns Distribution de l'attribut
   */
  private calculateDistribution(
    recommendations: any[],
    attribute: string,
    transform?: (value: any) => string,
  ): Record<string, number> {
    const distribution: Record<string, number> = {};
    const total = recommendations.length;
    
    if (total === 0) {
      return distribution;
    }
    
    // Compter les occurrences
    recommendations.forEach(recommendation => {
      let value = recommendation[attribute];
      
      if (transform) {
        value = transform(value);
      }
      
      value = value || 'unknown';
      
      distribution[value] = (distribution[value] || 0) + 1;
    });
    
    // Normaliser
    Object.keys(distribution).forEach(key => {
      distribution[key] = distribution[key] / total;
    });
    
    return distribution;
  }
  
  /**
   * Calcule la similarité entre deux distributions
   * @param distribution1 Première distribution
   * @param distribution2 Deuxième distribution
   * @returns Score de similarité (0-1)
   */
  private calculateDistributionSimilarity(
    distribution1: Record<string, number>,
    distribution2: Record<string, number>,
  ): number {
    // Si l'une des distributions est vide, retourner 0
    if (
      Object.keys(distribution1).length === 0 ||
      Object.keys(distribution2).length === 0
    ) {
      return 0;
    }
    
    // Créer un ensemble de toutes les clés
    const allKeys = new Set([
      ...Object.keys(distribution1),
      ...Object.keys(distribution2),
    ]);
    
    // Calculer la distance de Hellinger
    let sumSquaredDiff = 0;
    
    allKeys.forEach(key => {
      const value1 = distribution1[key] || 0;
      const value2 = distribution2[key] || 0;
      
      const diff = Math.sqrt(value1) - Math.sqrt(value2);
      sumSquaredDiff += diff * diff;
    });
    
    const hellingerDistance = Math.sqrt(sumSquaredDiff) / Math.sqrt(2);
    
    // Convertir la distance en similarité (1 - distance)
    return 1 - hellingerDistance;
  }
  
  /**
   * Détecte les biais dans les distributions
   * @param params Paramètres pour la détection de biais
   * @returns Liste des biais détectés
   */
  private detectBiases(params: {
    categoryDistribution: Record<string, number>;
    priceDistribution: Record<string, number>;
    locationDistribution: Record<string, number>;
    providerDistribution: Record<string, number>;
    themeDistribution: Record<string, number>;
    idealDistributions: {
      categoryDistribution: Record<string, number>;
      priceDistribution: Record<string, number>;
      locationDistribution: Record<string, number>;
      providerDistribution: Record<string, number>;
      themeDistribution: Record<string, number>;
    };
  }): Array<{
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }> {
    const biases = [];
    const {
      categoryDistribution,
      priceDistribution,
      locationDistribution,
      providerDistribution,
      themeDistribution,
      idealDistributions,
    } = params;
    
    // Détecter les biais de catégorie
    const categoryBias = this.detectDistributionBias(
      'category',
      categoryDistribution,
      idealDistributions.categoryDistribution,
    );
    
    if (categoryBias) {
      biases.push(categoryBias);
    }
    
    // Détecter les biais de prix
    const priceBias = this.detectDistributionBias(
      'price',
      priceDistribution,
      idealDistributions.priceDistribution,
    );
    
    if (priceBias) {
      biases.push(priceBias);
    }
    
    // Détecter les biais de localisation
    const locationBias = this.detectDistributionBias(
      'location',
      locationDistribution,
      idealDistributions.locationDistribution,
    );
    
    if (locationBias) {
      biases.push(locationBias);
    }
    
    // Détecter les biais de fournisseur
    const providerBias = this.detectDistributionBias(
      'provider',
      providerDistribution,
      idealDistributions.providerDistribution,
    );
    
    if (providerBias) {
      biases.push(providerBias);
    }
    
    // Détecter les biais de thème
    const themeBias = this.detectDistributionBias(
      'theme',
      themeDistribution,
      idealDistributions.themeDistribution,
    );
    
    if (themeBias) {
      biases.push(themeBias);
    }
    
    return biases;
  }
  
  /**
   * Détecte les biais dans une distribution
   * @param type Type de distribution
   * @param actual Distribution actuelle
   * @param ideal Distribution idéale
   * @returns Biais détecté ou null
   */
  private detectDistributionBias(
    type: string,
    actual: Record<string, number>,
    ideal: Record<string, number>,
  ): {
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  } | null {
    // Calculer la similarité
    const similarity = this.calculateDistributionSimilarity(actual, ideal);
    
    // Si la similarité est suffisante, pas de biais
    if (similarity >= 0.8) {
      return null;
    }
    
    // Trouver les clés les plus sur-représentées et sous-représentées
    let maxOverrepresentation = -Infinity;
    let maxUnderrepresentation = -Infinity;
    let overrepresentedKey = '';
    let underrepresentedKey = '';
    
    const allKeys = new Set([...Object.keys(actual), ...Object.keys(ideal)]);
    
    allKeys.forEach(key => {
      const actualValue = actual[key] || 0;
      const idealValue = ideal[key] || 0;
      const diff = actualValue - idealValue;
      
      if (diff > maxOverrepresentation) {
        maxOverrepresentation = diff;
        overrepresentedKey = key;
      }
      
      if (-diff > maxUnderrepresentation) {
        maxUnderrepresentation = -diff;
        underrepresentedKey = key;
      }
    });
    
    // Déterminer la sévérité
    let severity: 'low' | 'medium' | 'high' = 'low';
    
    if (similarity < 0.5) {
      severity = 'high';
    } else if (similarity < 0.7) {
      severity = 'medium';
    }
    
    // Créer la description
    let description = `Biais détecté dans la distribution des ${type}s: `;
    
    if (overrepresentedKey) {
      description += `sur-représentation de "${overrepresentedKey}" `;
    }
    
    if (underrepresentedKey) {
      description += `${overrepresentedKey ? 'et ' : ''}sous-représentation de "${underrepresentedKey}" `;
    }
    
    return {
      type: `${type}_bias`,
      description,
      severity,
    };
  }
  
  /**
   * Détermine la plage de prix d'une retraite
   * @param price Prix de la retraite
   * @returns Plage de prix
   */
  private getPriceRange(price: number): string {
    if (price < 500) return 'budget';
    if (price < 1500) return 'standard';
    return 'premium';
  }
}
