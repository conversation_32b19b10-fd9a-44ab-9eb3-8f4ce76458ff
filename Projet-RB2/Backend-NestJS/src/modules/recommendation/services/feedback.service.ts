import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import { AnalyticsIntegrationService } from './analytics-integration.service';

/**
 * Types de feedback possibles
 */
export enum FeedbackType {
  // Feedback de base
  RELEVANT = 'RELEVANT',
  NOT_RELEVANT = 'NOT_RELEVANT',
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  SAVE = 'SAVE',
  HIDE = 'HIDE',
  REPORT = 'REPORT',

  // Feedback détaillé
  DETAILED_POSITIVE = 'DETAILED_POSITIVE',
  DETAILED_NEGATIVE = 'DETAILED_NEGATIVE',
  DETAILED_NEUTRAL = 'DETAILED_NEUTRAL',

  // Feedback spécifique
  ACCURATE = 'ACCURATE',
  INACCURATE = 'INACCURATE',
  USEFUL = 'USEFUL',
  NOT_USEFUL = 'NOT_USEFUL',
  TIMELY = 'TIMELY',
  UNTIMELY = 'UNTIMELY',
  DIVERSE = 'DIVERSE',
  NOT_DIVERSE = 'NOT_DIVERSE',
  PERSONALIZED = 'PERSONALIZED',
  NOT_PERSONALIZED = 'NOT_PERSONALIZED',
}

/**
 * Raisons de signalement
 */
export enum ReportReason {
  INAPPROPRIATE = 'INAPPROPRIATE',
  MISLEADING = 'MISLEADING',
  OFFENSIVE = 'OFFENSIVE',
  SPAM = 'SPAM',
  OTHER = 'OTHER',
}

/**
 * Interface pour les données de feedback
 */
export interface FeedbackData {
  userId: string;
  recommendationId: string;
  recommendationType: RecommendationType;
  feedbackType: FeedbackType;
  comment?: string;
  rating?: number;
  metadata?: Record<string, any>;
  aspects?: { name: string; rating: number }[];
  isUseful?: boolean;
  hasActedUpon?: boolean;
  suggestions?: string;
}

/**
 * Interface pour les données de signalement
 */
export interface ReportData {
  userId: string;
  recommendationId: string;
  recommendationType: RecommendationType;
  reason: ReportReason;
  description: string;
  metadata?: Record<string, any>;
}

/**
 * Interface pour un feedback stocké
 */
interface StoredFeedback {
  id: string;
  userId: string;
  recommendationId: string;
  recommendationType: string;
  feedbackType: string;
  comment?: string;
  rating?: number;
  metadata?: Record<string, any>;
  aspects?: { name: string; rating: number }[];
  isUseful?: boolean;
  hasActedUpon?: boolean;
  suggestions?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface pour un signalement stocké
 */
interface StoredReport {
  id: string;
  userId: string;
  recommendationId: string;
  recommendationType: string;
  reason: string;
  description: string;
  status: 'PENDING' | 'REVIEWED' | 'RESOLVED' | 'REJECTED';
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  resolution?: string;
}

/**
 * Service pour gérer le feedback utilisateur sur les recommandations
 * Version temporaire avec stockage en mémoire en attendant la migration de la base de données
 */
@Injectable()
export class FeedbackService {
  private readonly logger = new Logger(FeedbackService.name);
  private feedbacks: StoredFeedback[] = [];
  private reports: StoredReport[] = [];

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly analyticsService: AnalyticsIntegrationService,
  ) {}

  /**
   * Enregistre un feedback utilisateur sur une recommandation
   * @param feedbackData Données du feedback
   * @returns Résultat de l'opération
   */
  async recordFeedback(feedbackData: FeedbackData): Promise<any> {
    try {
      this.logger.log(`Enregistrement d'un feedback de type ${feedbackData.feedbackType} pour la recommandation ${feedbackData.recommendationId}`);

      // Vérifier si l'utilisateur a déjà donné un feedback pour cette recommandation
      const existingFeedbackIndex = this.feedbacks.findIndex(
        f => f.userId === feedbackData.userId &&
             f.recommendationId === feedbackData.recommendationId &&
             f.recommendationType === feedbackData.recommendationType &&
             f.feedbackType === feedbackData.feedbackType
      );

      let feedback: StoredFeedback;

      if (existingFeedbackIndex !== -1) {
        // Mettre à jour le feedback existant
        feedback = {
          ...this.feedbacks[existingFeedbackIndex],
          comment: feedbackData.comment,
          rating: feedbackData.rating,
          metadata: feedbackData.metadata || {},
          aspects: feedbackData.aspects,
          isUseful: feedbackData.isUseful,
          hasActedUpon: feedbackData.hasActedUpon,
          suggestions: feedbackData.suggestions,
          updatedAt: new Date(),
        };
        this.feedbacks[existingFeedbackIndex] = feedback;
      } else {
        // Créer un nouveau feedback
        feedback = {
          id: uuidv4(),
          userId: feedbackData.userId,
          recommendationId: feedbackData.recommendationId,
          recommendationType: feedbackData.recommendationType,
          feedbackType: feedbackData.feedbackType,
          comment: feedbackData.comment,
          rating: feedbackData.rating,
          metadata: feedbackData.metadata || {},
          aspects: feedbackData.aspects,
          isUseful: feedbackData.isUseful,
          hasActedUpon: feedbackData.hasActedUpon,
          suggestions: feedbackData.suggestions,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        this.feedbacks.push(feedback);
      }

      // Si c'est un feedback détaillé, enregistrer également un feedback simple correspondant
      if (feedbackData.feedbackType === FeedbackType.DETAILED_POSITIVE) {
        await this.recordSimpleFeedback(feedbackData, FeedbackType.LIKE);
      } else if (feedbackData.feedbackType === FeedbackType.DETAILED_NEGATIVE) {
        await this.recordSimpleFeedback(feedbackData, FeedbackType.DISLIKE);
      }

      // Émettre un événement pour informer les autres services
      this.eventEmitter.emit('recommendation.feedback', {
        userId: feedbackData.userId,
        recommendationId: feedbackData.recommendationId,
        recommendationType: feedbackData.recommendationType,
        feedbackType: feedbackData.feedbackType,
        timestamp: new Date(),
      });

      // Enregistrer également dans les logs pour persistance temporaire
      this.logger.debug(`Feedback enregistré: ${JSON.stringify(feedback)}`);

      // Suivre l'événement dans le service d'analytics
      try {
        await this.analyticsService.trackRecommendationFeedback(
          feedbackData.userId,
          feedbackData.recommendationId,
          feedbackData.recommendationType,
          feedbackData.feedbackType,
          {
            comment: feedbackData.comment,
            rating: feedbackData.rating,
            metadata: feedbackData.metadata,
          },
        );
      } catch (analyticsError) {
        // Ne pas bloquer le processus si l'enregistrement dans analytics échoue
        this.logger.warn(`Erreur lors de l'enregistrement du feedback dans analytics: ${analyticsError.message}`);
      }

      return {
        success: true,
        data: feedback,
        message: 'Feedback enregistré avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du feedback: ${error.message}`);
      throw new HttpException(
        'Erreur lors de l\'enregistrement du feedback',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère les feedbacks d'un utilisateur pour une recommandation
   * @param userId ID de l'utilisateur
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @returns Liste des feedbacks
   */
  async getUserFeedbackForRecommendation(
    userId: string,
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<StoredFeedback[]> {
    try {
      const feedbacks = this.feedbacks.filter(
        f => f.userId === userId &&
             f.recommendationId === recommendationId &&
             f.recommendationType === recommendationType
      ).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return feedbacks;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des feedbacks: ${error.message}`);
      throw new HttpException(
        'Erreur lors de la récupération des feedbacks',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère tous les feedbacks d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de pagination et de filtrage
   * @returns Liste des feedbacks
   */
  async getUserFeedbacks(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      feedbackType?: FeedbackType;
      recommendationType?: RecommendationType;
    } = {},
  ): Promise<any> {
    try {
      const page = options.page || 1;
      const limit = options.limit || 10;
      const skip = (page - 1) * limit;

      // Filtrer les feedbacks
      let filteredFeedbacks = this.feedbacks.filter(f => f.userId === userId);

      if (options.feedbackType) {
        filteredFeedbacks = filteredFeedbacks.filter(f => f.feedbackType === options.feedbackType);
      }

      if (options.recommendationType) {
        filteredFeedbacks = filteredFeedbacks.filter(f => f.recommendationType === options.recommendationType);
      }

      // Trier par date de création (plus récent d'abord)
      filteredFeedbacks.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      // Appliquer la pagination
      const total = filteredFeedbacks.length;
      const paginatedFeedbacks = filteredFeedbacks.slice(skip, skip + limit);

      return {
        data: paginatedFeedbacks,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des feedbacks: ${error.message}`);
      throw new HttpException(
        'Erreur lors de la récupération des feedbacks',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Supprime un feedback
   * @param feedbackId ID du feedback
   * @param userId ID de l'utilisateur (pour vérification)
   * @returns Résultat de l'opération
   */
  async deleteFeedback(feedbackId: string, userId: string): Promise<any> {
    try {
      // Vérifier que le feedback appartient à l'utilisateur
      const feedbackIndex = this.feedbacks.findIndex(f => f.id === feedbackId);

      if (feedbackIndex === -1) {
        throw new HttpException('Feedback non trouvé', HttpStatus.NOT_FOUND);
      }

      const feedback = this.feedbacks[feedbackIndex];

      if (feedback.userId !== userId) {
        throw new HttpException('Vous n\'êtes pas autorisé à supprimer ce feedback', HttpStatus.FORBIDDEN);
      }

      // Supprimer le feedback
      this.feedbacks.splice(feedbackIndex, 1);

      return {
        success: true,
        message: 'Feedback supprimé avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression du feedback: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enregistre un feedback détaillé
   * @param feedbackData Données du feedback détaillé
   * @returns Résultat de l'opération
   */
  async recordDetailedFeedback(feedbackData: FeedbackData): Promise<any> {
    try {
      this.logger.log(`Enregistrement d'un feedback détaillé pour la recommandation ${feedbackData.recommendationId}`);

      // Déterminer le type de feedback détaillé en fonction de la note
      let detailedFeedbackType = FeedbackType.DETAILED_NEUTRAL;

      if (feedbackData.rating && feedbackData.rating >= 4) {
        detailedFeedbackType = FeedbackType.DETAILED_POSITIVE;
      } else if (feedbackData.rating && feedbackData.rating <= 2) {
        detailedFeedbackType = FeedbackType.DETAILED_NEGATIVE;
      }

      // Enregistrer le feedback détaillé
      const result = await this.recordFeedback({
        ...feedbackData,
        feedbackType: detailedFeedbackType,
      });

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du feedback détaillé: ${error.message}`);
      throw new HttpException(
        'Erreur lors de l\'enregistrement du feedback détaillé',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un signalement de recommandation inappropriée
   * @param reportData Données du signalement
   * @returns Résultat de l'opération
   */
  async reportRecommendation(reportData: ReportData): Promise<any> {
    try {
      this.logger.log(`Signalement d'une recommandation ${reportData.recommendationId} pour la raison ${reportData.reason}`);

      // Créer un nouveau signalement
      const report: StoredReport = {
        id: uuidv4(),
        userId: reportData.userId,
        recommendationId: reportData.recommendationId,
        recommendationType: reportData.recommendationType,
        reason: reportData.reason,
        description: reportData.description,
        status: 'PENDING',
        metadata: reportData.metadata || {},
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.reports.push(report);

      // Émettre un événement pour informer les autres services
      this.eventEmitter.emit('recommendation.report', {
        userId: reportData.userId,
        recommendationId: reportData.recommendationId,
        recommendationType: reportData.recommendationType,
        reason: reportData.reason,
        timestamp: new Date(),
      });

      // Enregistrer également dans les logs pour persistance temporaire
      this.logger.debug(`Signalement enregistré: ${JSON.stringify(report)}`);

      // Suivre l'événement dans le service d'analytics
      try {
        await this.analyticsService.trackRecommendationReport(
          reportData.userId,
          reportData.recommendationId,
          reportData.recommendationType,
          reportData.reason,
          {
            description: reportData.description,
            metadata: reportData.metadata,
          },
        );
      } catch (analyticsError) {
        // Ne pas bloquer le processus si l'enregistrement dans analytics échoue
        this.logger.warn(`Erreur lors de l'enregistrement du signalement dans analytics: ${analyticsError.message}`);
      }

      // Enregistrer également un feedback de type REPORT
      await this.recordFeedback({
        userId: reportData.userId,
        recommendationId: reportData.recommendationId,
        recommendationType: reportData.recommendationType,
        feedbackType: FeedbackType.REPORT,
        comment: reportData.description,
        metadata: {
          reportId: report.id,
          reason: reportData.reason,
          ...reportData.metadata,
        },
      });

      return {
        success: true,
        data: report,
        message: 'Signalement enregistré avec succès',
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du signalement: ${error.message}`);
      throw new HttpException(
        'Erreur lors de l\'enregistrement du signalement',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère les signalements pour une recommandation
   * @param recommendationId ID de la recommandation
   * @param recommendationType Type de recommandation
   * @returns Liste des signalements
   */
  async getReportsForRecommendation(
    recommendationId: string,
    recommendationType: RecommendationType,
  ): Promise<StoredReport[]> {
    try {
      const reports = this.reports.filter(
        r => r.recommendationId === recommendationId &&
             r.recommendationType === recommendationType
      ).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return reports;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des signalements: ${error.message}`);
      throw new HttpException(
        'Erreur lors de la récupération des signalements',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Enregistre un feedback simple (comme/n'aime pas)
   * @param feedbackData Données du feedback
   * @returns Résultat de l'opération
   */
  async recordSimpleFeedback(
    feedbackData: FeedbackData,
    simpleFeedbackType?: FeedbackType,
  ): Promise<any> {
    try {
      // Si un type de feedback simple est spécifié, l'utiliser
      const feedbackType = simpleFeedbackType || feedbackData.feedbackType;

      // Enregistrer le feedback simple
      return this.recordFeedback({
        ...feedbackData,
        feedbackType,
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement du feedback simple: ${error.message}`);
      throw new HttpException(
        'Erreur lors de l\'enregistrement du feedback simple',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
