import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import {
  GenerativeAIConfig,
  AIModelConfig,
  AIModelProvider,
  ExplanationGenerationConfig,
  ExplanationGenerationType,
  ExplanationTone,
  ExplanationLength,
  ExplanationPersonalization,
  ContentGenerationConfig,
  ContentGenerationType,
  ContentGenerationTrigger,
  NLPRecommendationConfig,
  NLPProcessingStrategy,
  NLPTextSource,
  AIRateLimitingConfig,
} from '../interfaces/generative-ai.interface';
import { RecommendationService } from './recommendation.service';
import { TransparencyService } from './transparency.service';

/**
 * Service for generative AI integration
 */
@Injectable()
export class GenerativeAIService implements OnModuleInit {
  private readonly logger = new Logger(GenerativeAIService.name);
  private aiConfig: GenerativeAIConfig;
  private readonly tokenUsage: Map<string, { count: number; resetTime: Date }> = new Map();
  private readonly promptTemplates: Map<string, string> = new Map();
  private readonly contentCache: Map<string, { content: string; timestamp: Date }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly recommendationService: RecommendationService,
    private readonly transparencyService: TransparencyService,
  ) {
    // Initialize configurations
    this.initializeConfigurations();
  }

  /**
   * Initialize on module initialization
   */
  async onModuleInit() {
    try {
      // Initialize prompt templates
      this.initializePromptTemplates();

      this.logger.log('Generative AI service initialized');
    } catch (error) {
      this.logger.error(`Error initializing generative AI service: ${error.message}`);
    }
  }

  /**
   * Initialize configurations
   */
  private initializeConfigurations(): void {
    try {
      // Initialize AI configuration
      this.aiConfig = {
        enabled: this.configService.get<boolean>('GENERATIVE_AI_ENABLED', true),
        modelConfig: {
          provider: this.configService.get<AIModelProvider>('AI_MODEL_PROVIDER', AIModelProvider.OPENAI),
          model: this.configService.get<string>('AI_MODEL_NAME', 'gpt-3.5-turbo'),
          apiKey: this.configService.get<string>('AI_API_KEY', ''),
          endpoint: this.configService.get<string>('AI_ENDPOINT', ''),
          parameters: {
            temperature: this.configService.get<number>('AI_TEMPERATURE', 0.7),
            maxTokens: this.configService.get<number>('AI_MAX_TOKENS', 500),
            topP: this.configService.get<number>('AI_TOP_P', 1),
            frequencyPenalty: this.configService.get<number>('AI_FREQUENCY_PENALTY', 0),
            presencePenalty: this.configService.get<number>('AI_PRESENCE_PENALTY', 0),
          },
          fallback: {
            provider: AIModelProvider.LOCAL,
            model: 'template-based',
          },
        },
        explanationConfig: {
          enabled: true,
          types: [
            ExplanationGenerationType.FEATURE_BASED,
            ExplanationGenerationType.HISTORY_BASED,
            ExplanationGenerationType.CONTEXT_BASED,
            ExplanationGenerationType.NARRATIVE,
          ],
          tone: ExplanationTone.FRIENDLY,
          length: ExplanationLength.BRIEF,
          personalization: ExplanationPersonalization.MODERATE,
        },
        contentGenerationConfig: {
          enabled: true,
          types: [
            ContentGenerationType.DESCRIPTION,
            ContentGenerationType.SUMMARY,
            ContentGenerationType.HIGHLIGHTS,
            ContentGenerationType.TIPS,
          ],
          maxLength: 500,
          triggers: [
            ContentGenerationTrigger.ON_RECOMMENDATION,
            ContentGenerationTrigger.ON_ITEM_VIEW,
          ],
        },
        nlpRecommendationConfig: {
          enabled: true,
          strategies: [
            NLPProcessingStrategy.KEYWORD_EXTRACTION,
            NLPProcessingStrategy.SEMANTIC_SIMILARITY,
          ],
          textSources: [
            NLPTextSource.USER_QUERIES,
            NLPTextSource.ITEM_DESCRIPTIONS,
          ],
          languages: ['en', 'fr'],
        },
        rateLimiting: {
          enabled: true,
          requestsPerMinute: 60,
          maxTokensPerRequest: 1000,
          maxTokensPerDay: 100000,
        },
      };

      this.logger.log('Generative AI configurations initialized');
    } catch (error) {
      this.logger.error(`Error initializing configurations: ${error.message}`);
    }
  }

  /**
   * Initialize prompt templates
   */
  private initializePromptTemplates(): void {
    try {
      // Feature-based explanation template
      this.promptTemplates.set('feature_explanation',
        'Generate a brief, friendly explanation for why {item_name} was recommended to the user. ' +
        'Focus on these key features: {features}. ' +
        'The user has shown interest in: {user_interests}. ' +
        'Keep the explanation under 50 words and make it sound natural.'
      );

      // History-based explanation template
      this.promptTemplates.set('history_explanation',
        'Generate a brief, friendly explanation for why {item_name} was recommended based on the user\'s history. ' +
        'The user previously {interaction_type} these items: {history_items}. ' +
        'Keep the explanation under 50 words and make it sound natural.'
      );

      // Context-based explanation template
      this.promptTemplates.set('context_explanation',
        'Generate a brief, friendly explanation for why {item_name} was recommended based on the user\'s current context. ' +
        'Current context: {context}. ' +
        'Keep the explanation under 50 words and make it sound natural.'
      );

      // Narrative explanation template
      this.promptTemplates.set('narrative_explanation',
        'Create a brief, engaging narrative about why {item_name} might be perfect for the user. ' +
        'Use these details: {details}. ' +
        'Make it personal, friendly, and under 75 words.'
      );

      // Item description template
      this.promptTemplates.set('item_description',
        'Generate an engaging description for {item_name}, which is a {item_type}. ' +
        'Key features: {features}. ' +
        'Target audience: {audience}. ' +
        'Keep it under 100 words and highlight what makes it special.'
      );

      // Item summary template
      this.promptTemplates.set('item_summary',
        'Create a concise summary of {item_name}. ' +
        'Include only the most essential information: {key_points}. ' +
        'Keep it under 50 words.'
      );

      // Item highlights template
      this.promptTemplates.set('item_highlights',
        'List 3-5 key highlights of {item_name} in bullet point format. ' +
        'Focus on these aspects: {aspects}.'
      );

      // Personalized tips template
      this.promptTemplates.set('personalized_tips',
        'Generate 3 personalized tips for a user interested in {item_name}. ' +
        'The user has these preferences: {preferences}. ' +
        'Make the tips practical and specific.'
      );

      this.logger.log('Prompt templates initialized');
    } catch (error) {
      this.logger.error(`Error initializing prompt templates: ${error.message}`);
    }
  }

  /**
   * Get generative AI configuration
   * @returns Generative AI configuration
   */
  getAIConfig(): GenerativeAIConfig {
    return this.aiConfig;
  }

  /**
   * Update generative AI configuration
   * @param config New generative AI configuration
   * @returns Updated generative AI configuration
   */
  updateAIConfig(config: Partial<GenerativeAIConfig>): GenerativeAIConfig {
    this.aiConfig = {
      ...this.aiConfig,
      ...config,
    };

    this.logger.log('Generative AI configuration updated');
    this.eventEmitter.emit('ai.config.updated', this.aiConfig);

    return this.aiConfig;
  }

  /**
   * Generate explanation for a recommendation
   * @param item Recommended item
   * @param userId User ID
   * @param type Explanation type
   * @param context Additional context
   * @returns Generated explanation
   */
  async generateExplanation(
    item: any,
    userId: string,
    type: ExplanationGenerationType = ExplanationGenerationType.FEATURE_BASED,
    context?: Record<string, any>,
  ): Promise<string> {
    try {
      if (!this.aiConfig.enabled || !this.aiConfig.explanationConfig.enabled) {
        return this.generateFallbackExplanation(item, type);
      }

      // Check rate limits
      if (!this.checkRateLimit(userId)) {
        return this.generateFallbackExplanation(item, type);
      }

      // Get user information
      const userInfo = await this.getUserInfo(userId);

      // Prepare prompt based on explanation type
      const prompt = await this.prepareExplanationPrompt(item, userInfo, type, context);

      // Generate explanation using AI
      const explanation = await this.generateAIResponse(prompt, userId);

      // Track token usage
      this.trackTokenUsage(userId, prompt.length + explanation.length);

      return explanation;
    } catch (error) {
      this.logger.error(`Error generating explanation: ${error.message}`);
      return this.generateFallbackExplanation(item, type);
    }
  }

  /**
   * Generate content for an item
   * @param item Item to generate content for
   * @param userId User ID
   * @param type Content type
   * @param context Additional context
   * @returns Generated content
   */
  async generateContent(
    item: any,
    userId: string,
    type: ContentGenerationType = ContentGenerationType.DESCRIPTION,
    context?: Record<string, any>,
  ): Promise<string> {
    try {
      if (!this.aiConfig.enabled || !this.aiConfig.contentGenerationConfig.enabled) {
        return this.generateFallbackContent(item, type);
      }

      // Check cache first
      const cacheKey = `${type}_${item.id}`;
      const cachedContent = this.contentCache.get(cacheKey);

      if (cachedContent && (new Date().getTime() - cachedContent.timestamp.getTime()) < 24 * 60 * 60 * 1000) {
        return cachedContent.content;
      }

      // Check rate limits
      if (!this.checkRateLimit(userId)) {
        return this.generateFallbackContent(item, type);
      }

      // Get user information if needed for personalization
      const userInfo = type === ContentGenerationType.TIPS ? await this.getUserInfo(userId) : null;

      // Prepare prompt based on content type
      const prompt = this.prepareContentPrompt(item, type, userInfo, context);

      // Generate content using AI
      const content = await this.generateAIResponse(prompt, userId);

      // Track token usage
      this.trackTokenUsage(userId, prompt.length + content.length);

      // Cache the generated content
      this.contentCache.set(cacheKey, {
        content,
        timestamp: new Date(),
      });

      return content;
    } catch (error) {
      this.logger.error(`Error generating content: ${error.message}`);
      return this.generateFallbackContent(item, type);
    }
  }

  /**
   * Get NLP-based recommendations
   * @param query User query
   * @param userId User ID
   * @param limit Number of recommendations to return
   * @param context Additional context
   * @returns NLP-based recommendations
   */
  async getNLPRecommendations(
    query: string,
    userId: string,
    limit: number = 10,
    context?: Record<string, any>,
  ): Promise<any[]> {
    try {
      if (!this.aiConfig.enabled || !this.aiConfig.nlpRecommendationConfig.enabled) {
        // Fall back to regular recommendations
        return this.recommendationService.getRecommendations(userId, limit);
      }

      // Check rate limits
      if (!this.checkRateLimit(userId)) {
        // Fall back to regular recommendations
        return this.recommendationService.getRecommendations(userId, limit);
      }

      // Process the query using NLP
      const processedQuery = await this.processQueryWithNLP(query, userId);

      // Get recommendations based on processed query
      const recommendations = await this.recommendationService.getRecommendations(
        userId,
        limit,
        {
          keywords: processedQuery.keywords,
          entities: processedQuery.entities,
          sentiment: processedQuery.sentiment,
          context,
        },
      );

      // Track token usage
      this.trackTokenUsage(userId, query.length + JSON.stringify(processedQuery).length);

      return recommendations;
    } catch (error) {
      this.logger.error(`Error getting NLP recommendations: ${error.message}`);

      // Fall back to regular recommendations
      return this.recommendationService.getRecommendations(userId, limit);
    }
  }

  /**
   * Process query with NLP
   * @param query User query
   * @param userId User ID
   * @returns Processed query
   */
  private async processQueryWithNLP(query: string, userId: string): Promise<any> {
    try {
      // In a real implementation, this would use a proper NLP service
      // For now, we'll use a simple implementation

      // Extract keywords
      const keywords = this.extractKeywords(query);

      // Extract entities
      const entities = this.extractEntities(query);

      // Analyze sentiment
      const sentiment = this.analyzeSentiment(query);

      return {
        keywords,
        entities,
        sentiment,
        originalQuery: query,
      };
    } catch (error) {
      this.logger.error(`Error processing query with NLP: ${error.message}`);

      // Return basic processed query
      return {
        keywords: [query],
        entities: [],
        sentiment: 'neutral',
        originalQuery: query,
      };
    }
  }

  /**
   * Extract keywords from text
   * @param text Text to extract keywords from
   * @returns Extracted keywords
   */
  private extractKeywords(text: string): string[] {
    // In a real implementation, this would use a proper NLP service
    // For now, we'll use a simple implementation

    // Remove punctuation and convert to lowercase
    const cleanText = text.toLowerCase().replace(/[^\w\s]/g, '');

    // Split into words
    const words = cleanText.split(/\s+/);

    // Remove stopwords
    const stopwords = ['a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'from', 'up', 'down', 'of', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now', 'd', 'll', 'm', 'o', 're', 've', 'y', 'ain', 'aren', 'couldn', 'didn', 'doesn', 'hadn', 'hasn', 'haven', 'isn', 'ma', 'mightn', 'mustn', 'needn', 'shan', 'shouldn', 'wasn', 'weren', 'won', 'wouldn'];
    const keywords = words.filter(word => !stopwords.includes(word) && word.length > 2);

    // Return unique keywords
    return [...new Set(keywords)];
  }

  /**
   * Extract entities from text
   * @param text Text to extract entities from
   * @returns Extracted entities
   */
  private extractEntities(text: string): any[] {
    // In a real implementation, this would use a proper NLP service
    // For now, we'll use a simple implementation

    const entities = [];

    // Look for location entities
    const locationRegex = /\b(paris|london|new york|tokyo|sydney|france|uk|usa|japan|australia)\b/i;
    const locationMatch = text.match(locationRegex);
    if (locationMatch) {
      entities.push({
        type: 'location',
        value: locationMatch[0],
      });
    }

    // Look for activity entities
    const activityRegex = /\b(yoga|meditation|retreat|wellness|fitness|nutrition|massage|spa|hiking|swimming)\b/i;
    const activityMatch = text.match(activityRegex);
    if (activityMatch) {
      entities.push({
        type: 'activity',
        value: activityMatch[0],
      });
    }

    // Look for time entities
    const timeRegex = /\b(morning|afternoon|evening|night|today|tomorrow|weekend|weekday|monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/i;
    const timeMatch = text.match(timeRegex);
    if (timeMatch) {
      entities.push({
        type: 'time',
        value: timeMatch[0],
      });
    }

    // Look for duration entities
    const durationRegex = /\b(\d+)\s*(day|days|hour|hours|minute|minutes|week|weeks|month|months)\b/i;
    const durationMatch = text.match(durationRegex);
    if (durationMatch) {
      entities.push({
        type: 'duration',
        value: durationMatch[0],
        amount: parseInt(durationMatch[1]),
        unit: durationMatch[2],
      });
    }

    return entities;
  }

  /**
   * Analyze sentiment of text
   * @param text Text to analyze
   * @returns Sentiment (positive, negative, neutral)
   */
  private analyzeSentiment(text: string): string {
    // In a real implementation, this would use a proper NLP service
    // For now, we'll use a simple implementation

    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'terrific', 'outstanding', 'superb', 'awesome', 'best', 'better', 'happy', 'excited', 'love', 'like', 'enjoy', 'pleasant', 'positive', 'nice', 'beautiful', 'perfect', 'ideal', 'relaxing', 'relaxed', 'calm', 'peaceful', 'tranquil'];
    const negativeWords = ['bad', 'terrible', 'horrible', 'awful', 'poor', 'worst', 'worse', 'unhappy', 'sad', 'angry', 'upset', 'hate', 'dislike', 'unpleasant', 'negative', 'ugly', 'imperfect', 'stressful', 'stressed', 'anxious', 'tense', 'chaotic'];

    // Count positive and negative words
    let positiveCount = 0;
    let negativeCount = 0;

    // Convert to lowercase and split into words
    const words = text.toLowerCase().split(/\s+/);

    for (const word of words) {
      const cleanWord = word.replace(/[^\w]/g, '');
      if (positiveWords.includes(cleanWord)) {
        positiveCount++;
      } else if (negativeWords.includes(cleanWord)) {
        negativeCount++;
      }
    }

    // Determine sentiment
    if (positiveCount > negativeCount) {
      return 'positive';
    } else if (negativeCount > positiveCount) {
      return 'negative';
    } else {
      return 'neutral';
    }
  }

  /**
   * Prepare explanation prompt
   * @param item Item to explain
   * @param userInfo User information
   * @param type Explanation type
   * @param context Additional context
   * @returns Prepared prompt
   */
  private async prepareExplanationPrompt(
    item: any,
    userInfo: any,
    type: ExplanationGenerationType,
    context?: Record<string, any>,
  ): Promise<string> {
    try {
      // Get the appropriate template
      let templateKey: string;
      let templateData: Record<string, any> = {
        item_name: item.title || item.name || 'this item',
      };

      switch (type) {
        case ExplanationGenerationType.FEATURE_BASED:
          templateKey = 'feature_explanation';
          templateData.features = this.getItemFeatures(item);
          templateData.user_interests = this.getUserInterests(userInfo);
          break;

        case ExplanationGenerationType.HISTORY_BASED:
          templateKey = 'history_explanation';
          templateData.interaction_type = this.getInteractionType(userInfo);
          templateData.history_items = this.getHistoryItems(userInfo);
          break;

        case ExplanationGenerationType.CONTEXT_BASED:
          templateKey = 'context_explanation';
          templateData.context = this.getContextDescription(context);
          break;

        case ExplanationGenerationType.NARRATIVE:
          templateKey = 'narrative_explanation';
          templateData.details = this.getNarrativeDetails(item, userInfo, context);
          break;

        default:
          templateKey = 'feature_explanation';
          templateData.features = this.getItemFeatures(item);
          templateData.user_interests = this.getUserInterests(userInfo);
      }

      // Get the template
      const template = this.promptTemplates.get(templateKey) || '';

      // Fill in the template
      let prompt = template;
      for (const [key, value] of Object.entries(templateData)) {
        prompt = prompt.replace(`{${key}}`, String(value));
      }

      // Add tone and personalization based on configuration
      prompt = this.adjustPromptForToneAndPersonalization(prompt);

      return prompt;
    } catch (error) {
      this.logger.error(`Error preparing explanation prompt: ${error.message}`);
      return `Generate a brief explanation for why ${item.title || item.name || 'this item'} was recommended.`;
    }
  }

  /**
   * Prepare content prompt
   * @param item Item to generate content for
   * @param type Content type
   * @param userInfo User information
   * @param context Additional context
   * @returns Prepared prompt
   */
  private prepareContentPrompt(
    item: any,
    type: ContentGenerationType,
    userInfo?: any,
    context?: Record<string, any>,
  ): string {
    try {
      // Get the appropriate template
      let templateKey: string;
      let templateData: Record<string, any> = {
        item_name: item.title || item.name || 'this item',
        item_type: item.type || 'item',
      };

      switch (type) {
        case ContentGenerationType.DESCRIPTION:
          templateKey = 'item_description';
          templateData.features = this.getItemFeatures(item);
          templateData.audience = this.getTargetAudience(item);
          break;

        case ContentGenerationType.SUMMARY:
          templateKey = 'item_summary';
          templateData.key_points = this.getItemKeyPoints(item);
          break;

        case ContentGenerationType.HIGHLIGHTS:
          templateKey = 'item_highlights';
          templateData.aspects = this.getItemAspects(item);
          break;

        case ContentGenerationType.TIPS:
          templateKey = 'personalized_tips';
          templateData.preferences = userInfo ? this.getUserPreferences(userInfo) : 'wellness and self-care';
          break;

        default:
          templateKey = 'item_description';
          templateData.features = this.getItemFeatures(item);
          templateData.audience = this.getTargetAudience(item);
      }

      // Get the template
      const template = this.promptTemplates.get(templateKey) || '';

      // Fill in the template
      let prompt = template;
      for (const [key, value] of Object.entries(templateData)) {
        prompt = prompt.replace(`{${key}}`, String(value));
      }

      return prompt;
    } catch (error) {
      this.logger.error(`Error preparing content prompt: ${error.message}`);
      return `Generate content for ${item.title || item.name || 'this item'}.`;
    }
  }

  /**
   * Generate AI response
   * @param prompt Prompt to send to AI
   * @param userId User ID
   * @returns AI response
   */
  private async generateAIResponse(prompt: string, userId: string): Promise<string> {
    try {
      // In a real implementation, this would call an AI API
      // For now, we'll return a mock response

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate a mock response based on the prompt
      if (prompt.includes('explanation')) {
        return this.generateMockExplanation(prompt);
      } else if (prompt.includes('description')) {
        return this.generateMockDescription(prompt);
      } else if (prompt.includes('summary')) {
        return this.generateMockSummary(prompt);
      } else if (prompt.includes('highlights')) {
        return this.generateMockHighlights(prompt);
      } else if (prompt.includes('tips')) {
        return this.generateMockTips(prompt);
      } else {
        return 'Generated content based on your request.';
      }
    } catch (error) {
      this.logger.error(`Error generating AI response: ${error.message}`);
      return 'Unable to generate content at this time.';
    }
  }

  /**
   * Generate fallback explanation
   * @param item Item to explain
   * @param type Explanation type
   * @returns Fallback explanation
   */
  private generateFallbackExplanation(item: any, type: ExplanationGenerationType): string {
    // Use template-based fallback
    const itemName = item.title || item.name || 'this item';

    switch (type) {
      case ExplanationGenerationType.FEATURE_BASED:
        return `${itemName} was recommended based on its features that match your preferences.`;

      case ExplanationGenerationType.HISTORY_BASED:
        return `${itemName} was recommended based on your previous activity.`;

      case ExplanationGenerationType.CONTEXT_BASED:
        return `${itemName} was recommended based on your current context.`;

      case ExplanationGenerationType.NARRATIVE:
        return `We thought you might enjoy ${itemName} as part of your wellness journey.`;

      default:
        return `${itemName} was recommended for you.`;
    }
  }

  /**
   * Generate fallback content
   * @param item Item to generate content for
   * @param type Content type
   * @returns Fallback content
   */
  private generateFallbackContent(item: any, type: ContentGenerationType): string {
    // Use template-based fallback
    const itemName = item.title || item.name || 'this item';

    switch (type) {
      case ContentGenerationType.DESCRIPTION:
        return `${itemName} is a ${item.type || 'wellness offering'} designed to enhance your wellbeing.`;

      case ContentGenerationType.SUMMARY:
        return `${itemName}: A ${item.type || 'wellness offering'} for your wellbeing needs.`;

      case ContentGenerationType.HIGHLIGHTS:
        return `- Quality experience\n- Wellness focused\n- Professionally designed`;

      case ContentGenerationType.TIPS:
        return `1. Schedule time for this activity\n2. Prepare properly\n3. Reflect on your experience afterward`;

      default:
        return `${itemName} - wellness content`;
    }
  }

  /**
   * Generate mock explanation
   * @param prompt Explanation prompt
   * @returns Mock explanation
   */
  private generateMockExplanation(prompt: string): string {
    // Extract item name from prompt
    const itemNameMatch = prompt.match(/for why (.*?) was recommended/);
    const itemName = itemNameMatch ? itemNameMatch[1] : 'this item';

    // Generate different explanations based on prompt content
    if (prompt.includes('feature')) {
      return `${itemName} was recommended because its focus on mindfulness and relaxation aligns perfectly with your interest in stress reduction techniques.`;
    } else if (prompt.includes('history')) {
      return `Based on your enjoyment of meditation workshops, we thought ${itemName} would be a great next step in your wellness journey.`;
    } else if (prompt.includes('context')) {
      return `Since it's currently evening and you're in a urban location, ${itemName} offers a perfect opportunity to unwind after a busy day.`;
    } else if (prompt.includes('narrative')) {
      return `Imagine ending your day with ${itemName}, letting the calming atmosphere wash away the day's stress and prepare you for a restful night.`;
    } else {
      return `We recommended ${itemName} based on your preferences and activity.`;
    }
  }

  /**
   * Generate mock description
   * @param prompt Description prompt
   * @returns Mock description
   */
  private generateMockDescription(prompt: string): string {
    // Extract item name from prompt
    const itemNameMatch = prompt.match(/for (.*?), which is/);
    const itemName = itemNameMatch ? itemNameMatch[1] : 'this item';

    return `${itemName} offers a transformative wellness experience designed to rejuvenate both body and mind. With expert guidance and a serene environment, participants can expect to develop mindfulness skills while enjoying a break from daily stresses. Perfect for both beginners and experienced practitioners looking to deepen their practice.`;
  }

  /**
   * Generate mock summary
   * @param prompt Summary prompt
   * @returns Mock summary
   */
  private generateMockSummary(prompt: string): string {
    // Extract item name from prompt
    const itemNameMatch = prompt.match(/of (.*?)\./);
    const itemName = itemNameMatch ? itemNameMatch[1] : 'this item';

    return `${itemName} combines guided meditation, gentle movement, and breathwork in a 90-minute session designed to reduce stress and enhance mental clarity.`;
  }

  /**
   * Generate mock highlights
   * @param prompt Highlights prompt
   * @returns Mock highlights
   */
  private generateMockHighlights(prompt: string): string {
    // Extract item name from prompt
    const itemNameMatch = prompt.match(/of (.*?) in/);
    const itemName = itemNameMatch ? itemNameMatch[1] : 'this item';

    return `• Expert instructors with 10+ years of experience\n• Small group setting for personalized attention\n• Proven techniques for stress reduction\n• Beautiful natural surroundings\n• All equipment provided`;
  }

  /**
   * Generate mock tips
   * @param prompt Tips prompt
   * @returns Mock tips
   */
  private generateMockTips(prompt: string): string {
    // Extract item name from prompt
    const itemNameMatch = prompt.match(/in (.*?)\./);
    const itemName = itemNameMatch ? itemNameMatch[1] : 'this item';

    return `1. Arrive 15 minutes early to settle in and prepare your mind for ${itemName}.\n2. Wear comfortable, layered clothing to adjust to the room temperature during the session.\n3. Keep a journal nearby to record insights and feelings after your experience.`;
  }

  /**
   * Adjust prompt for tone and personalization
   * @param prompt Prompt to adjust
   * @returns Adjusted prompt
   */
  private adjustPromptForToneAndPersonalization(prompt: string): string {
    // Add tone instruction
    let adjustedPrompt = prompt;

    switch (this.aiConfig.explanationConfig.tone) {
      case ExplanationTone.FORMAL:
        adjustedPrompt += ' Use a formal, professional tone.';
        break;

      case ExplanationTone.CASUAL:
        adjustedPrompt += ' Use a casual, conversational tone.';
        break;

      case ExplanationTone.ENTHUSIASTIC:
        adjustedPrompt += ' Use an enthusiastic, energetic tone.';
        break;

      case ExplanationTone.PROFESSIONAL:
        adjustedPrompt += ' Use a professional, informative tone.';
        break;

      case ExplanationTone.FRIENDLY:
        adjustedPrompt += ' Use a friendly, warm tone.';
        break;
    }

    // Add personalization instruction
    switch (this.aiConfig.explanationConfig.personalization) {
      case ExplanationPersonalization.GENERIC:
        // No additional instruction
        break;

      case ExplanationPersonalization.MODERATE:
        adjustedPrompt += ' Make it somewhat personalized to the user.';
        break;

      case ExplanationPersonalization.HIGH:
        adjustedPrompt += ' Make it highly personalized to the user\'s specific interests and needs.';
        break;
    }

    return adjustedPrompt;
  }

  /**
   * Get item features
   * @param item Item
   * @returns Item features as string
   */
  private getItemFeatures(item: any): string {
    const features = [];

    if (item.categories) {
      features.push(`categories: ${Array.isArray(item.categories) ? item.categories.join(', ') : item.categories}`);
    }

    if (item.tags) {
      features.push(`tags: ${Array.isArray(item.tags) ? item.tags.join(', ') : item.tags}`);
    }

    if (item.attributes) {
      const attributeStr = Object.entries(item.attributes)
        .filter(([_, value]) => value === true)
        .map(([key]) => key)
        .join(', ');

      if (attributeStr) {
        features.push(`attributes: ${attributeStr}`);
      }
    }

    if (item.duration) {
      features.push(`duration: ${item.duration}`);
    }

    if (item.location) {
      features.push(`location: ${item.location}`);
    }

    if (item.price) {
      features.push(`price: ${item.price}`);
    }

    return features.join('; ') || 'no specific features available';
  }

  /**
   * Get user interests
   * @param userInfo User information
   * @returns User interests as string
   */
  private getUserInterests(userInfo: any): string {
    if (!userInfo) {
      return 'wellness and self-care';
    }

    const interests = [];

    if (userInfo.preferences?.categories) {
      interests.push(Array.isArray(userInfo.preferences.categories)
        ? userInfo.preferences.categories.join(', ')
        : userInfo.preferences.categories);
    }

    if (userInfo.preferences?.tags) {
      interests.push(Array.isArray(userInfo.preferences.tags)
        ? userInfo.preferences.tags.join(', ')
        : userInfo.preferences.tags);
    }

    if (userInfo.interests) {
      interests.push(Array.isArray(userInfo.interests)
        ? userInfo.interests.join(', ')
        : userInfo.interests);
    }

    return interests.join('; ') || 'wellness and self-care';
  }

  /**
   * Get interaction type
   * @param userInfo User information
   * @returns Interaction type as string
   */
  private getInteractionType(userInfo: any): string {
    if (!userInfo || !userInfo.history || userInfo.history.length === 0) {
      return 'viewed';
    }

    // Count interaction types
    const interactionCounts: Record<string, number> = {};

    for (const historyItem of userInfo.history) {
      const type = historyItem.interactionType || 'VIEW';
      interactionCounts[type] = (interactionCounts[type] || 0) + 1;
    }

    // Find most common interaction type
    let maxCount = 0;
    let mostCommonType = 'viewed';

    for (const [type, count] of Object.entries(interactionCounts)) {
      if (count > maxCount) {
        maxCount = count;
        mostCommonType = type.toLowerCase();
      }
    }

    // Map to readable form
    const typeMap: Record<string, string> = {
      'view': 'viewed',
      'bookmark': 'bookmarked',
      'like': 'liked',
      'purchase': 'purchased',
      'rate': 'rated highly',
      'share': 'shared',
      'comment': 'commented on',
    };

    return typeMap[mostCommonType] || 'interacted with';
  }

  /**
   * Get history items
   * @param userInfo User information
   * @returns History items as string
   */
  private getHistoryItems(userInfo: any): string {
    if (!userInfo || !userInfo.history || userInfo.history.length === 0) {
      return 'similar wellness activities';
    }

    // Get item names from history
    const itemNames = userInfo.history
      .filter(h => h.item)
      .map(h => h.item.title || h.item.name)
      .filter(Boolean);

    if (itemNames.length === 0) {
      return 'similar wellness activities';
    }

    // Take up to 3 most recent items
    const recentItems = itemNames.slice(0, 3);

    return recentItems.join(', ');
  }

  /**
   * Get context description
   * @param context Context
   * @returns Context description as string
   */
  private getContextDescription(context?: Record<string, any>): string {
    if (!context) {
      return 'your current situation';
    }

    const contextDescriptions = [];

    if (context.location) {
      contextDescriptions.push(`you're in ${context.location}`);
    }

    if (context.timeOfDay) {
      contextDescriptions.push(`it's ${context.timeOfDay}`);
    }

    if (context.weather) {
      contextDescriptions.push(`the weather is ${context.weather}`);
    }

    if (context.mood) {
      contextDescriptions.push(`you're feeling ${context.mood}`);
    }

    if (context.activity) {
      contextDescriptions.push(`you're ${context.activity}`);
    }

    return contextDescriptions.join(', ') || 'your current situation';
  }

  /**
   * Get narrative details
   * @param item Item
   * @param userInfo User information
   * @param context Context
   * @returns Narrative details as string
   */
  private getNarrativeDetails(item: any, userInfo: any, context?: Record<string, any>): string {
    const details = [];

    // Add item details
    if (item.categories) {
      const categories = Array.isArray(item.categories) ? item.categories.join(', ') : item.categories;
      details.push(`it focuses on ${categories}`);
    }

    // Add user preference details
    if (userInfo && userInfo.preferences) {
      const preferences = this.getUserInterests(userInfo);
      details.push(`you're interested in ${preferences}`);
    }

    // Add context details
    if (context) {
      const contextDesc = this.getContextDescription(context);
      details.push(contextDesc);
    }

    return details.join('; ') || 'it matches your wellness journey';
  }

  /**
   * Get target audience
   * @param item Item
   * @returns Target audience as string
   */
  private getTargetAudience(item: any): string {
    if (!item) {
      return 'wellness enthusiasts';
    }

    if (item.targetAudience) {
      return item.targetAudience;
    }

    // Try to infer from categories or tags
    if (item.categories) {
      const categories = Array.isArray(item.categories) ? item.categories : [item.categories];

      if (categories.includes('beginner') || categories.includes('beginners')) {
        return 'beginners in wellness practices';
      }

      if (categories.includes('advanced')) {
        return 'experienced wellness practitioners';
      }

      if (categories.includes('family') || categories.includes('families')) {
        return 'families looking for wellness activities';
      }
    }

    return 'wellness enthusiasts';
  }

  /**
   * Get item key points
   * @param item Item
   * @returns Item key points as string
   */
  private getItemKeyPoints(item: any): string {
    if (!item) {
      return 'wellness benefits';
    }

    const keyPoints = [];

    if (item.description) {
      keyPoints.push(item.description.substring(0, 100));
    }

    if (item.benefits) {
      keyPoints.push(`benefits: ${Array.isArray(item.benefits) ? item.benefits.join(', ') : item.benefits}`);
    }

    if (item.features) {
      keyPoints.push(`features: ${Array.isArray(item.features) ? item.features.join(', ') : item.features}`);
    }

    if (item.duration) {
      keyPoints.push(`duration: ${item.duration}`);
    }

    if (item.location) {
      keyPoints.push(`location: ${item.location}`);
    }

    return keyPoints.join('; ') || 'wellness benefits';
  }

  /**
   * Get item aspects
   * @param item Item
   * @returns Item aspects as string
   */
  private getItemAspects(item: any): string {
    if (!item) {
      return 'quality, experience, benefits';
    }

    const aspects = [];

    if (item.categories) {
      aspects.push('category focus');
    }

    if (item.benefits) {
      aspects.push('health benefits');
    }

    if (item.features) {
      aspects.push('special features');
    }

    if (item.instructor || item.instructors) {
      aspects.push('instructor expertise');
    }

    if (item.location) {
      aspects.push('location');
    }

    if (item.duration) {
      aspects.push('time commitment');
    }

    if (item.price) {
      aspects.push('value for money');
    }

    return aspects.join(', ') || 'quality, experience, benefits';
  }

  /**
   * Get user preferences
   * @param userInfo User information
   * @returns User preferences as string
   */
  private getUserPreferences(userInfo: any): string {
    return this.getUserInterests(userInfo);
  }

  /**
   * Get user info
   * @param userId User ID
   * @returns User information
   */
  private async getUserInfo(userId: string): Promise<any> {
    try {
      // In a real implementation, this would fetch user data from the database
      // For now, we'll return mock data
      return {
        id: userId,
        preferences: {
          categories: ['wellness', 'meditation', 'yoga'],
          tags: ['relaxation', 'mindfulness', 'stress-relief'],
        },
        interests: ['mental health', 'physical fitness', 'spiritual growth'],
        history: [
          {
            interactionType: 'VIEW',
            item: { title: 'Meditation Workshop', categories: ['meditation'] },
          },
          {
            interactionType: 'BOOKMARK',
            item: { title: 'Yoga Retreat', categories: ['yoga'] },
          },
          {
            interactionType: 'PURCHASE',
            item: { title: 'Mindfulness Course', categories: ['mindfulness'] },
          },
        ],
      };
    } catch (error) {
      this.logger.error(`Error getting user info: ${error.message}`);
      return {};
    }
  }

  /**
   * Check rate limit
   * @param userId User ID
   * @returns Whether request is allowed
   */
  private checkRateLimit(userId: string): boolean {
    if (!this.aiConfig.rateLimiting.enabled) {
      return true;
    }

    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();

      // Check if user has a token usage record
      let usage = this.tokenUsage.get(userId);

      // If no record or it's from a previous day, create a new one
      if (!usage || usage.resetTime.getTime() < today) {
        usage = {
          count: 0,
          resetTime: new Date(today),
        };
        this.tokenUsage.set(userId, usage);
      }

      // Check if user has exceeded daily limit
      if (usage.count >= this.aiConfig.rateLimiting.maxTokensPerDay) {
        this.logger.warn(`User ${userId} has exceeded daily token limit`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error checking rate limit: ${error.message}`);
      return true; // Allow in case of error
    }
  }

  /**
   * Track token usage
   * @param userId User ID
   * @param tokenCount Token count
   */
  private trackTokenUsage(userId: string, tokenCount: number): void {
    if (!this.aiConfig.rateLimiting.enabled) {
      return;
    }

    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();

      // Get or create usage record
      let usage = this.tokenUsage.get(userId);

      if (!usage || usage.resetTime.getTime() < today) {
        usage = {
          count: 0,
          resetTime: new Date(today),
        };
      }

      // Update token count
      usage.count += tokenCount;

      // Save updated usage
      this.tokenUsage.set(userId, usage);

      this.logger.debug(`User ${userId} token usage: ${usage.count}/${this.aiConfig.rateLimiting.maxTokensPerDay}`);
    } catch (error) {
      this.logger.error(`Error tracking token usage: ${error.message}`);
    }
  }

  /**
   * Clean up token usage records
   */
  @Cron('0 0 * * *') // Every day at midnight
  private cleanupTokenUsage(): void {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();

      // Remove expired records
      for (const [userId, usage] of this.tokenUsage.entries()) {
        if (usage.resetTime.getTime() < today) {
          this.tokenUsage.delete(userId);
        }
      }

      this.logger.log(`Cleaned up token usage records, remaining: ${this.tokenUsage.size}`);
    } catch (error) {
      this.logger.error(`Error cleaning up token usage: ${error.message}`);
    }
  }
}