import { Injectable, Logger } from '@nestjs/common';
import { ContentBasedService } from './content-based.service';
import { CollaborativeFilteringService } from './collaborative-filtering.service';
import { MatrixFactorizationService } from './matrix-factorization.service';
import { ContextualRecommendationService } from './contextual-recommendation.service';
import { DiversityFilterService } from './diversity-filter.service';
import { ExternalDataService } from './external-data.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { EnrichmentOptions } from '../interfaces/external-data.interface';

/**
 * Service de recommandation hybride
 * Combine plusieurs approches de recommandation pour améliorer la qualité des résultats
 */
@Injectable()
export class HybridRecommendationService {
  private readonly logger = new Logger(HybridRecommendationService.name);

  constructor(
    private readonly contentBasedService: ContentBasedService,
    private readonly collaborativeFilteringService: CollaborativeFilteringService,
    private readonly matrixFactorizationService: MatrixFactorizationService,
    private readonly contextualRecommendationService: ContextualRecommendationService,
    private readonly diversityFilterService: DiversityFilterService,
    private readonly externalDataService: ExternalDataService,
  ) {}

  /**
   * Génère des recommandations hybrides pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations hybrides de type ${type} pour l'utilisateur ${userId}`);

      // Déterminer la méthode hybride à utiliser
      const method = options.hybridMethod || HybridMethod.WEIGHTED;

      // Générer les recommandations selon la méthode
      let recommendations;
      switch (method) {
        case HybridMethod.WEIGHTED:
          recommendations = await this.getWeightedRecommendations(userId, type, options);
          break;
        case HybridMethod.SWITCHING:
          recommendations = await this.getSwitchingRecommendations(userId, type, options);
          break;
        case HybridMethod.CASCADING:
          recommendations = await this.getCascadingRecommendations(userId, type, options);
          break;
        case HybridMethod.MIXED:
        default:
          recommendations = await this.getMixedRecommendations(userId, type, options);
          break;
      }

      this.logger.log(`${recommendations.length} recommandations hybrides générées pour l'utilisateur ${userId}`);

      // Enrichir les recommandations avec des données externes si demandé
      if (options.enrichWithExternalData) {
        try {
          const enrichmentOptions: EnrichmentOptions = {
            dataTypes: options.externalDataTypes,
            minRelevanceScore: options.minExternalDataRelevanceScore || 0.5,
            maxItemsPerType: options.maxExternalDataItemsPerType || 3,
            mergeStrategy: 'merge',
          };

          const enrichmentResults = await this.externalDataService.enrichItems(recommendations, enrichmentOptions);

          // Fusionner les résultats d'enrichissement avec les recommandations
          return recommendations.map(recommendation => {
            const enrichment = enrichmentResults.find(result => result.itemId === recommendation.id);

            if (enrichment && enrichment.externalData.length > 0) {
              return {
                ...recommendation,
                externalData: enrichment.externalData,
                enrichmentScore: enrichment.enrichmentScore,
                enrichmentMetadata: enrichment.metadata,
              };
            }

            return recommendation;
          });
        } catch (error) {
          this.logger.error(`Erreur lors de l'enrichissement des recommandations: ${error.message}`);
          // En cas d'erreur, retourner les recommandations sans enrichissement
        }
      }

      return recommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations hybrides: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère des recommandations hybrides pondérées
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  private async getWeightedRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;

    // Définir les poids pour chaque méthode (total = 1.0)
    const contentBasedWeight = options.contentBasedWeight || 0.3;
    const collaborativeWeight = options.collaborativeWeight || 0.3;
    const matrixFactorizationWeight = 0.2;
    const contextualWeight = 0.2;

    // Récupérer les recommandations de chaque méthode
    const [
      contentBasedRecommendations,
      collaborativeRecommendations,
      matrixFactorizationRecommendations,
      contextualRecommendations
    ] = await Promise.all([
      this.contentBasedService.getRecommendations(userId, type, {
        ...options,
        limit: limit * 2, // Récupérer plus de recommandations pour avoir plus de choix
      }),
      this.collaborativeFilteringService.getRecommendations(userId, type, {
        ...options,
        limit: limit * 2,
      }),
      this.matrixFactorizationService.getRecommendations(userId, type, {
        ...options,
        limit: limit * 2,
      }),
      this.contextualRecommendationService.getRecommendations(userId, type, {
        ...options,
        limit: limit * 2,
      }),
    ]);

    // Fusionner les recommandations
    const recommendationsMap = new Map();

    // Ajouter les recommandations basées sur le contenu
    contentBasedRecommendations.forEach(recommendation => {
      recommendationsMap.set(recommendation.id, {
        ...recommendation,
        score: recommendation.score * contentBasedWeight,
        sources: ['content-based'],
      });
    });

    // Ajouter les recommandations collaboratives
    collaborativeRecommendations.forEach(recommendation => {
      if (recommendationsMap.has(recommendation.id)) {
        // Combiner les scores
        const existingRecommendation = recommendationsMap.get(recommendation.id);
        existingRecommendation.score += recommendation.score * collaborativeWeight;
        existingRecommendation.sources.push('collaborative');

        // Fusionner les métadonnées
        existingRecommendation.metadata = {
          ...existingRecommendation.metadata,
          ...recommendation.metadata,
        };
      } else {
        recommendationsMap.set(recommendation.id, {
          ...recommendation,
          score: recommendation.score * collaborativeWeight,
          sources: ['collaborative'],
        });
      }
    });

    // Ajouter les recommandations par factorisation matricielle
    matrixFactorizationRecommendations.forEach(recommendation => {
      if (recommendationsMap.has(recommendation.id)) {
        // Combiner les scores
        const existingRecommendation = recommendationsMap.get(recommendation.id);
        existingRecommendation.score += recommendation.score * matrixFactorizationWeight;
        existingRecommendation.sources.push('matrix-factorization');

        // Fusionner les métadonnées
        existingRecommendation.metadata = {
          ...existingRecommendation.metadata,
          ...recommendation.metadata,
        };
      } else {
        recommendationsMap.set(recommendation.id, {
          ...recommendation,
          score: recommendation.score * matrixFactorizationWeight,
          sources: ['matrix-factorization'],
        });
      }
    });

    // Ajouter les recommandations contextuelles
    contextualRecommendations.forEach(recommendation => {
      if (recommendationsMap.has(recommendation.id)) {
        // Combiner les scores
        const existingRecommendation = recommendationsMap.get(recommendation.id);
        existingRecommendation.score += recommendation.score * contextualWeight;
        existingRecommendation.sources.push('contextual');

        // Fusionner les métadonnées
        existingRecommendation.metadata = {
          ...existingRecommendation.metadata,
          ...recommendation.metadata,
        };
      } else {
        recommendationsMap.set(recommendation.id, {
          ...recommendation,
          score: recommendation.score * contextualWeight,
          sources: ['contextual'],
        });
      }
    });

    // Transformer la map en tableau
    const recommendations = Array.from(recommendationsMap.values());

    // Trier les recommandations par score et limiter le nombre de résultats
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Génère des recommandations hybrides par commutation
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  private async getSwitchingRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;

    // Déterminer quelle méthode utiliser en fonction du contexte et des interactions

    // Récupérer le nombre d'interactions de l'utilisateur
    const interactionCount = await this.getUserInteractionCount(userId);

    // Définir le seuil d'interactions
    const interactionThreshold = options.interactionThreshold || 10;

    // Récupérer le contexte de l'utilisateur (si disponible)
    const context = options.context || {};

    // Choisir la méthode en fonction des critères
    if (interactionCount >= interactionThreshold * 2) {
      // Beaucoup d'interactions : utiliser la factorisation matricielle
      const recommendations = await this.matrixFactorizationService.getRecommendations(userId, type, options);

      // Ajouter la source
      return recommendations.map(recommendation => ({
        ...recommendation,
        sources: ['matrix-factorization'],
      }));
    } else if (interactionCount >= interactionThreshold) {
      // Nombre moyen d'interactions : utiliser le filtrage collaboratif
      const recommendations = await this.collaborativeFilteringService.getRecommendations(userId, type, options);

      // Ajouter la source
      return recommendations.map(recommendation => ({
        ...recommendation,
        sources: ['collaborative'],
      }));
    } else if (context && (context.location || context.season || context.timeOfDay)) {
      // Peu d'interactions mais contexte disponible : utiliser les recommandations contextuelles
      const recommendations = await this.contextualRecommendationService.getRecommendations(userId, type, options);

      // Ajouter la source
      return recommendations.map(recommendation => ({
        ...recommendation,
        sources: ['contextual'],
      }));
    } else {
      // Peu d'interactions et pas de contexte : utiliser la recommandation basée sur le contenu
      const recommendations = await this.contentBasedService.getRecommendations(userId, type, options);

      // Ajouter la source
      return recommendations.map(recommendation => ({
        ...recommendation,
        sources: ['content-based'],
      }));
    }
  }

  /**
   * Génère des recommandations hybrides en cascade
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  private async getCascadingRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;

    // Récupérer les recommandations basées sur le contenu
    const contentBasedRecommendations = await this.contentBasedService.getRecommendations(userId, type, {
      ...options,
      limit: limit * 2, // Récupérer plus de recommandations pour avoir plus de choix
    });

    // Si aucune recommandation basée sur le contenu, utiliser le filtrage collaboratif
    if (contentBasedRecommendations.length === 0) {
      const collaborativeRecommendations = await this.collaborativeFilteringService.getRecommendations(userId, type, options);

      // Ajouter la source
      return collaborativeRecommendations.map(recommendation => ({
        ...recommendation,
        sources: ['collaborative'],
      }));
    }

    // Récupérer les recommandations collaboratives pour les éléments recommandés par le contenu
    const contentBasedItemIds = contentBasedRecommendations.map(recommendation => recommendation.id);

    // Simuler la récupération des scores collaboratifs pour ces éléments
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    const collaborativeScores = new Map();

    for (const itemId of contentBasedItemIds) {
      // Simuler un score collaboratif
      collaborativeScores.set(itemId, Math.random());
    }

    // Combiner les scores
    const recommendations = contentBasedRecommendations.map(recommendation => {
      const collaborativeScore = collaborativeScores.get(recommendation.id) || 0;

      return {
        ...recommendation,
        score: recommendation.score * 0.6 + collaborativeScore * 0.4,
        sources: ['content-based', 'collaborative'],
      };
    });

    // Trier les recommandations par score et limiter le nombre de résultats
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Génère des recommandations hybrides mixtes
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  private async getMixedRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions = {},
  ) {
    const limit = options.limit || 10;

    // Calculer le nombre de recommandations à récupérer pour chaque méthode
    // Répartir équitablement entre les 4 méthodes
    const methodCount = 4;
    const baseLimit = Math.floor(limit / methodCount);
    const remainder = limit % methodCount;

    const contentBasedLimit = baseLimit + (remainder > 0 ? 1 : 0);
    const collaborativeLimit = baseLimit + (remainder > 1 ? 1 : 0);
    const matrixFactorizationLimit = baseLimit + (remainder > 2 ? 1 : 0);
    const contextualLimit = baseLimit;

    // Récupérer les recommandations de chaque méthode
    const [
      contentBasedRecommendations,
      collaborativeRecommendations,
      matrixFactorizationRecommendations,
      contextualRecommendations
    ] = await Promise.all([
      this.contentBasedService.getRecommendations(userId, type, {
        ...options,
        limit: contentBasedLimit,
      }),
      this.collaborativeFilteringService.getRecommendations(userId, type, {
        ...options,
        limit: collaborativeLimit,
      }),
      this.matrixFactorizationService.getRecommendations(userId, type, {
        ...options,
        limit: matrixFactorizationLimit,
      }),
      this.contextualRecommendationService.getRecommendations(userId, type, {
        ...options,
        limit: contextualLimit,
      }),
    ]);

    // Ajouter la source à chaque recommandation
    const taggedContentBasedRecommendations = contentBasedRecommendations.map(recommendation => ({
      ...recommendation,
      sources: ['content-based'],
    }));

    const taggedCollaborativeRecommendations = collaborativeRecommendations.map(recommendation => ({
      ...recommendation,
      sources: ['collaborative'],
    }));

    const taggedMatrixFactorizationRecommendations = matrixFactorizationRecommendations.map(recommendation => ({
      ...recommendation,
      sources: ['matrix-factorization'],
    }));

    const taggedContextualRecommendations = contextualRecommendations.map(recommendation => ({
      ...recommendation,
      sources: ['contextual'],
    }));

    // Fusionner les recommandations
    const recommendations = [
      ...taggedContentBasedRecommendations,
      ...taggedCollaborativeRecommendations,
      ...taggedMatrixFactorizationRecommendations,
      ...taggedContextualRecommendations,
    ];

    // Éliminer les doublons (garder celui avec le score le plus élevé)
    const uniqueRecommendations = [];
    const seenIds = new Set();

    for (const recommendation of recommendations) {
      if (!seenIds.has(recommendation.id)) {
        uniqueRecommendations.push(recommendation);
        seenIds.add(recommendation.id);
      } else {
        // Trouver la recommandation existante
        const existingIndex = uniqueRecommendations.findIndex(r => r.id === recommendation.id);
        if (existingIndex !== -1) {
          const existing = uniqueRecommendations[existingIndex];

          // Remplacer si le score est plus élevé
          if (recommendation.score > existing.score) {
            uniqueRecommendations[existingIndex] = recommendation;
          }
        }
      }
    }

    // Trier les recommandations par score et limiter au nombre demandé
    return uniqueRecommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Récupère le nombre d'interactions d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Nombre d'interactions
   */
  private async getUserInteractionCount(userId: string) {
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    return Math.floor(Math.random() * 20); // Simuler un nombre d'interactions
  }
}
