import { Test, TestingModule } from '@nestjs/testing';
import { MatrixFactorizationService } from './matrix-factorization.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { FactorizationMethod } from '../enums/factorization-method.enum';
import { Logger } from '@nestjs/common';

describe('MatrixFactorizationService', () => {
  let service: MatrixFactorizationService;
  let prismaService: PrismaService;

  // Mock des données
  const mockUserInteractions = [
    {
      userId: 'user1',
      itemId: 'item1',
      interactionType: 'VIEW',
      createdAt: new Date(),
      metadata: {}
    },
    {
      userId: 'user1',
      itemId: 'item2',
      interactionType: 'LIKE',
      createdAt: new Date(),
      metadata: {}
    },
    {
      userId: 'user1',
      itemId: 'item3',
      interactionType: 'BOOKMARK',
      createdAt: new Date(),
      metadata: {}
    },
    {
      userId: 'user2',
      itemId: 'item1',
      interactionType: 'LIKE',
      createdAt: new Date(),
      metadata: {}
    },
    {
      userId: 'user2',
      itemId: 'item3',
      interactionType: 'PURCHASE',
      createdAt: new Date(),
      metadata: {}
    }
  ];

  const mockItems = [
    { id: 'item1', title: 'Item 1', description: 'Description 1', metadata: { category: 'cat1' } },
    { id: 'item2', title: 'Item 2', description: 'Description 2', metadata: { category: 'cat2' } },
    { id: 'item3', title: 'Item 3', description: 'Description 3', metadata: { category: 'cat1' } },
    { id: 'item4', title: 'Item 4', description: 'Description 4', metadata: { category: 'cat3' } },
    { id: 'item5', title: 'Item 5', description: 'Description 5', metadata: { category: 'cat2' } }
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MatrixFactorizationService,
        {
          provide: PrismaService,
          useValue: {
            userInteraction: {
              findMany: jest.fn().mockResolvedValue(mockUserInteractions)
            },
            course: {
              findMany: jest.fn().mockResolvedValue(mockItems)
            },
            retreat: {
              findMany: jest.fn().mockResolvedValue(mockItems)
            },
            partner: {
              findMany: jest.fn().mockResolvedValue(mockItems)
            }
          }
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn()
          }
        }
      ],
    }).compile();

    service = module.get<MatrixFactorizationService>(MatrixFactorizationService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Espionner les méthodes privées
    jest.spyOn<any, any>(service, 'getUserItemInteractions').mockResolvedValue(mockUserInteractions);
    jest.spyOn<any, any>(service, 'getItemsByType').mockResolvedValue(mockItems);
    jest.spyOn<any, any>(service, 'trainModel').mockResolvedValue(undefined);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRecommendations', () => {
    it('should return recommendations for a user', async () => {
      // Configurer les facteurs pour le test
      const userFactors = new Map();
      userFactors.set('user1', [0.1, 0.2, 0.3]);
      
      const itemFactors = new Map();
      itemFactors.set('item1', [0.2, 0.1, 0.3]);
      itemFactors.set('item2', [0.3, 0.2, 0.1]);
      itemFactors.set('item3', [0.1, 0.3, 0.2]);
      itemFactors.set('item4', [0.2, 0.2, 0.2]);
      itemFactors.set('item5', [0.3, 0.3, 0.3]);
      
      // Accéder aux propriétés privées pour le test
      Object.defineProperty(service, 'userFactors', { value: userFactors });
      Object.defineProperty(service, 'itemFactors', { value: itemFactors });
      
      const options = {
        limit: 3,
        excludeIds: ['item3'] // Exclure un item
      };
      
      const recommendations = await service.getRecommendations('user1', RecommendationType.COURSE, options);
      
      // Vérifications
      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeLessThanOrEqual(options.limit);
      expect(recommendations.some(r => r.id === 'item3')).toBe(false); // L'item exclu ne doit pas être présent
      
      // Vérifier que les recommandations sont triées par score
      for (let i = 0; i < recommendations.length - 1; i++) {
        expect(recommendations[i].score).toBeGreaterThanOrEqual(recommendations[i + 1].score);
      }
    });
    
    it('should handle empty user factors', async () => {
      // Configurer un utilisateur sans facteurs
      const userFactors = new Map();
      const itemFactors = new Map();
      
      // Accéder aux propriétés privées pour le test
      Object.defineProperty(service, 'userFactors', { value: userFactors });
      Object.defineProperty(service, 'itemFactors', { value: itemFactors });
      
      const recommendations = await service.getRecommendations('user3', RecommendationType.COURSE);
      
      // Vérifications
      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBe(0);
    });
  });

  describe('countRecommendations', () => {
    it('should return the count of available recommendations', async () => {
      const count = await service.countRecommendations('user1', RecommendationType.COURSE);
      
      expect(count).toBe(mockItems.length);
    });
    
    it('should exclude specified items from count', async () => {
      const options = {
        excludeIds: ['item1', 'item3']
      };
      
      const count = await service.countRecommendations('user1', RecommendationType.COURSE, options);
      
      expect(count).toBe(mockItems.length - options.excludeIds.length);
    });
  });

  // Test des méthodes d'entraînement
  describe('training methods', () => {
    it('should train model with SVD method', async () => {
      // Restaurer l'implémentation réelle pour ce test
      jest.spyOn<any, any>(service, 'trainModel').mockRestore();
      jest.spyOn<any, any>(service, 'trainSVD').mockResolvedValue(undefined);
      
      const options = {
        factorizationMethod: FactorizationMethod.SVD,
        numFactors: 10,
        learningRate: 0.01,
        regularization: 0.1,
        numIterations: 5
      };
      
      // Appeler la méthode privée directement pour le test
      await (service as any).trainModel('user1', RecommendationType.COURSE, options);
      
      // Vérifier que la méthode SVD a été appelée avec les bons paramètres
      expect((service as any).trainSVD).toHaveBeenCalledWith(
        expect.any(Array),
        options.numFactors,
        options.learningRate,
        options.regularization,
        options.numIterations
      );
    });
  });
});
