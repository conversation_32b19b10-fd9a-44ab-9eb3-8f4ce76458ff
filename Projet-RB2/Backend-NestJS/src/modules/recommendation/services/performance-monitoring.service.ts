import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import {
  PerformanceMetric,
  MetricValue,
  MetricCategory,
  PerformanceAlert,
  AlertSeverity,
  PerformanceReport,
  PerformanceDataQuery,
  AlertConfig,
} from '../interfaces/performance-monitoring.interface';

/**
 * Service for monitoring recommendation system performance
 */
@Injectable()
export class PerformanceMonitoringService {
  private readonly logger = new Logger(PerformanceMonitoringService.name);
  private readonly metrics: Map<string, PerformanceMetric> = new Map();
  private readonly alerts: Map<string, PerformanceAlert> = new Map();
  private readonly alertConfigs: Map<string, AlertConfig> = new Map();
  private readonly monitoringEnabled: boolean;
  private readonly alertingEnabled: boolean;
  private readonly metricRetentionDays: number;
  private readonly alertRetentionDays: number;
  private readonly defaultAggregationInterval: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.monitoringEnabled = this.configService.get<boolean>('PERFORMANCE_MONITORING_ENABLED', true);
    this.alertingEnabled = this.configService.get<boolean>('PERFORMANCE_ALERTING_ENABLED', true);
    this.metricRetentionDays = this.configService.get<number>('METRIC_RETENTION_DAYS', 90);
    this.alertRetentionDays = this.configService.get<number>('ALERT_RETENTION_DAYS', 30);
    this.defaultAggregationInterval = this.configService.get<string>('DEFAULT_AGGREGATION_INTERVAL', 'hour');

    // Initialize metrics and alert configs
    this.initializeMetrics().catch(error => {
      this.logger.error(`Error initializing metrics: ${error.message}`);
    });

    this.initializeAlertConfigs().catch(error => {
      this.logger.error(`Error initializing alert configs: ${error.message}`);
    });
  }

  /**
   * Initialize metrics
   */
  private async initializeMetrics(): Promise<void> {
    try {
      // In a real implementation, this would load metrics from the database
      // For now, we'll create some sample metrics

      // Accuracy metrics
      this.registerMetric({
        id: 'precision',
        name: 'Precision',
        description: 'Ratio of relevant items among recommended items',
        category: MetricCategory.ACCURACY,
        unit: 'percentage',
        values: [],
        thresholds: {
          warning: 0.7,
          critical: 0.5,
          direction: 'below',
        },
      });

      this.registerMetric({
        id: 'recall',
        name: 'Recall',
        description: 'Ratio of recommended relevant items among all relevant items',
        category: MetricCategory.ACCURACY,
        unit: 'percentage',
        values: [],
        thresholds: {
          warning: 0.6,
          critical: 0.4,
          direction: 'below',
        },
      });

      this.registerMetric({
        id: 'f1_score',
        name: 'F1 Score',
        description: 'Harmonic mean of precision and recall',
        category: MetricCategory.ACCURACY,
        unit: 'score',
        values: [],
        thresholds: {
          warning: 0.65,
          critical: 0.45,
          direction: 'below',
        },
      });

      // Diversity metrics
      this.registerMetric({
        id: 'category_diversity',
        name: 'Category Diversity',
        description: 'Diversity of categories in recommendations',
        category: MetricCategory.DIVERSITY,
        unit: 'score',
        values: [],
        thresholds: {
          warning: 0.5,
          critical: 0.3,
          direction: 'below',
        },
      });

      this.registerMetric({
        id: 'item_similarity',
        name: 'Item Similarity',
        description: 'Average similarity between recommended items',
        category: MetricCategory.DIVERSITY,
        unit: 'score',
        values: [],
        thresholds: {
          warning: 0.8,
          critical: 0.9,
          direction: 'above',
        },
      });

      // Performance metrics
      this.registerMetric({
        id: 'response_time',
        name: 'Response Time',
        description: 'Average time to generate recommendations',
        category: MetricCategory.PERFORMANCE,
        unit: 'milliseconds',
        values: [],
        thresholds: {
          warning: 200,
          critical: 500,
          direction: 'above',
        },
      });

      this.registerMetric({
        id: 'throughput',
        name: 'Throughput',
        description: 'Number of recommendations per second',
        category: MetricCategory.PERFORMANCE,
        unit: 'requests/second',
        values: [],
        thresholds: {
          warning: 10,
          critical: 5,
          direction: 'below',
        },
      });

      // Business metrics
      this.registerMetric({
        id: 'click_through_rate',
        name: 'Click-Through Rate',
        description: 'Percentage of recommendations that are clicked',
        category: MetricCategory.BUSINESS,
        unit: 'percentage',
        values: [],
        thresholds: {
          warning: 0.05,
          critical: 0.02,
          direction: 'below',
        },
      });

      this.registerMetric({
        id: 'conversion_rate',
        name: 'Conversion Rate',
        description: 'Percentage of recommendations that lead to a purchase',
        category: MetricCategory.BUSINESS,
        unit: 'percentage',
        values: [],
        thresholds: {
          warning: 0.01,
          critical: 0.005,
          direction: 'below',
        },
      });

      // User satisfaction metrics
      this.registerMetric({
        id: 'user_satisfaction',
        name: 'User Satisfaction',
        description: 'Average user rating of recommendations',
        category: MetricCategory.USER_SATISFACTION,
        unit: 'rating',
        values: [],
        thresholds: {
          warning: 3.5,
          critical: 3.0,
          direction: 'below',
        },
      });

      this.logger.log(`Initialized ${this.metrics.size} performance metrics`);
    } catch (error) {
      this.logger.error(`Error initializing metrics: ${error.message}`);
      throw error;
    }
  }

  /**
   * Initialize alert configurations
   */
  private async initializeAlertConfigs(): Promise<void> {
    try {
      // In a real implementation, this would load alert configs from the database
      // For now, we'll create alert configs based on the metrics

      for (const [metricId, metric] of this.metrics.entries()) {
        if (metric.thresholds) {
          this.alertConfigs.set(metricId, {
            id: uuidv4(),
            metricId,
            name: `${metric.name} Alert`,
            description: `Alert for ${metric.name}`,
            thresholds: metric.thresholds,
            enabled: true,
            notificationChannels: ['email', 'dashboard'],
            cooldownMinutes: 60,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      }

      this.logger.log(`Initialized ${this.alertConfigs.size} alert configurations`);
    } catch (error) {
      this.logger.error(`Error initializing alert configs: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a new metric
   * @param metric Metric to register
   * @returns Registered metric
   */
  registerMetric(metric: PerformanceMetric): PerformanceMetric {
    if (!this.monitoringEnabled) {
      return metric;
    }

    try {
      // Generate ID if not provided
      if (!metric.id) {
        metric.id = uuidv4();
      }

      // Initialize values array if not provided
      if (!metric.values) {
        metric.values = [];
      }

      // Store the metric
      this.metrics.set(metric.id, metric);

      this.logger.debug(`Registered metric: ${metric.name} (${metric.id})`);

      return metric;
    } catch (error) {
      this.logger.error(`Error registering metric ${metric.name}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Record a metric value
   * @param metricId Metric ID
   * @param value Metric value
   * @param timestamp Timestamp (defaults to now)
   * @param metadata Additional metadata
   * @returns Updated metric
   */
  recordMetricValue(
    metricId: string,
    value: number,
    timestamp: Date = new Date(),
    metadata?: Record<string, any>,
  ): PerformanceMetric {
    if (!this.monitoringEnabled) {
      return null;
    }

    try {
      // Get the metric
      const metric = this.metrics.get(metricId);
      if (!metric) {
        throw new Error(`Metric not found: ${metricId}`);
      }

      // Create the metric value
      const metricValue: MetricValue = {
        value,
        timestamp,
        metadata,
      };

      // Add the value to the metric
      metric.values.push(metricValue);

      // Sort values by timestamp (newest first)
      metric.values.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      // Limit the number of values to retain
      this.pruneMetricValues(metric);

      // Check for alerts
      if (this.alertingEnabled) {
        this.checkAlerts(metric, metricValue);
      }

      // Emit event
      this.eventEmitter.emit('metric.recorded', {
        metricId,
        metricName: metric.name,
        value,
        timestamp,
        metadata,
      });

      return metric;
    } catch (error) {
      this.logger.error(`Error recording metric value for ${metricId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Prune old metric values
   * @param metric Metric to prune
   */
  private pruneMetricValues(metric: PerformanceMetric): void {
    if (!metric.values || metric.values.length === 0) {
      return;
    }

    const now = new Date();
    const cutoff = new Date(now.getTime() - this.metricRetentionDays * 24 * 60 * 60 * 1000);

    // Remove values older than the cutoff
    metric.values = metric.values.filter(value => value.timestamp >= cutoff);
  }

  /**
   * Check if a metric value should trigger alerts
   * @param metric Metric
   * @param value Metric value
   */
  private checkAlerts(metric: PerformanceMetric, value: MetricValue): void {
    if (!this.alertingEnabled || !metric.thresholds) {
      return;
    }

    try {
      const alertConfig = this.alertConfigs.get(metric.id);
      if (!alertConfig || !alertConfig.enabled) {
        return;
      }

      const thresholds = alertConfig.thresholds;
      const direction = thresholds.direction;

      // Check for critical threshold
      if (thresholds.critical !== undefined) {
        const isCritical = direction === 'above'
          ? value.value >= thresholds.critical
          : value.value <= thresholds.critical;

        if (isCritical) {
          this.createAlert(metric, value, AlertSeverity.CRITICAL, thresholds.critical);
          return;
        }
      }

      // Check for warning threshold
      if (thresholds.warning !== undefined) {
        const isWarning = direction === 'above'
          ? value.value >= thresholds.warning
          : value.value <= thresholds.warning;

        if (isWarning) {
          this.createAlert(metric, value, AlertSeverity.WARNING, thresholds.warning);
          return;
        }
      }
    } catch (error) {
      this.logger.error(`Error checking alerts for metric ${metric.id}: ${error.message}`);
    }
  }

  /**
   * Create a new alert
   * @param metric Metric
   * @param value Metric value
   * @param severity Alert severity
   * @param threshold Threshold that triggered the alert
   * @returns Created alert
   */
  private createAlert(
    metric: PerformanceMetric,
    value: MetricValue,
    severity: AlertSeverity,
    threshold: number,
  ): PerformanceAlert {
    try {
      // Check for cooldown period
      if (this.isAlertInCooldown(metric.id, severity)) {
        this.logger.debug(`Alert for metric ${metric.id} is in cooldown period, skipping`);
        return null;
      }

      // Create the alert
      const alert: PerformanceAlert = {
        id: uuidv4(),
        metricId: metric.id,
        metricName: metric.name,
        severity,
        message: this.generateAlertMessage(metric, value, severity, threshold),
        timestamp: value.timestamp,
        value: value.value,
        threshold,
        acknowledged: false,
        resolved: false,
        metadata: {
          ...value.metadata,
          category: metric.category,
          unit: metric.unit,
        },
      };

      // Store the alert
      this.alerts.set(alert.id, alert);

      // Emit event
      this.eventEmitter.emit('alert.created', alert);

      this.logger.log(`Created ${severity} alert for metric ${metric.name}: ${alert.message}`);

      // Send notifications
      this.sendAlertNotifications(alert);

      return alert;
    } catch (error) {
      this.logger.error(`Error creating alert for metric ${metric.id}: ${error.message}`);
      return null;
    }
  }

  /**
   * Check if an alert for a metric is in cooldown period
   * @param metricId Metric ID
   * @param severity Alert severity
   * @returns Whether the alert is in cooldown
   */
  private isAlertInCooldown(metricId: string, severity: AlertSeverity): boolean {
    // Get the alert config
    const alertConfig = this.alertConfigs.get(metricId);
    if (!alertConfig || !alertConfig.cooldownMinutes) {
      return false;
    }

    // Find the most recent alert for this metric and severity
    const recentAlerts = Array.from(this.alerts.values())
      .filter(alert =>
        alert.metricId === metricId &&
        alert.severity === severity
      )
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    if (recentAlerts.length === 0) {
      return false;
    }

    const mostRecentAlert = recentAlerts[0];
    const now = new Date();
    const cooldownMs = alertConfig.cooldownMinutes * 60 * 1000;

    // Check if the most recent alert is within the cooldown period
    return (now.getTime() - mostRecentAlert.timestamp.getTime()) < cooldownMs;
  }

  /**
   * Generate an alert message
   * @param metric Metric
   * @param value Metric value
   * @param severity Alert severity
   * @param threshold Threshold that triggered the alert
   * @returns Alert message
   */
  private generateAlertMessage(
    metric: PerformanceMetric,
    value: MetricValue,
    severity: AlertSeverity,
    threshold: number,
  ): string {
    const direction = metric.thresholds.direction;
    const comparison = direction === 'above' ? 'exceeded' : 'fell below';

    return `${metric.name} ${comparison} ${severity.toLowerCase()} threshold of ${threshold} ${metric.unit} (current value: ${value.value} ${metric.unit})`;
  }

  /**
   * Send notifications for an alert
   * @param alert Alert to send notifications for
   */
  private sendAlertNotifications(alert: PerformanceAlert): void {
    try {
      // Get the alert config
      const alertConfig = this.alertConfigs.get(alert.metricId);
      if (!alertConfig || !alertConfig.notificationChannels) {
        return;
      }

      // Send notifications to each channel
      for (const channel of alertConfig.notificationChannels) {
        switch (channel) {
          case 'email':
            this.sendEmailNotification(alert);
            break;
          case 'dashboard':
            // Dashboard notifications are handled automatically
            break;
          case 'slack':
            this.sendSlackNotification(alert);
            break;
          default:
            this.logger.warn(`Unknown notification channel: ${channel}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error sending alert notifications: ${error.message}`);
    }
  }

  /**
   * Send an email notification for an alert
   * @param alert Alert to send notification for
   */
  private sendEmailNotification(alert: PerformanceAlert): void {
    // In a real implementation, this would send an email
    this.logger.debug(`[EMAIL] Alert: ${alert.message}`);
  }

  /**
   * Send a Slack notification for an alert
   * @param alert Alert to send notification for
   */
  private sendSlackNotification(alert: PerformanceAlert): void {
    // In a real implementation, this would send a Slack message
    this.logger.debug(`[SLACK] Alert: ${alert.message}`);
  }

  /**
   * Get all metrics
   * @param category Optional category filter
   * @returns List of metrics
   */
  getMetrics(category?: MetricCategory): PerformanceMetric[] {
    const metrics = Array.from(this.metrics.values());

    if (category) {
      return metrics.filter(metric => metric.category === category);
    }

    return metrics;
  }

  /**
   * Get a specific metric
   * @param metricId Metric ID
   * @returns Metric or null if not found
   */
  getMetric(metricId: string): PerformanceMetric {
    return this.metrics.get(metricId) || null;
  }

  /**
   * Get metric values
   * @param metricId Metric ID
   * @param startDate Start date
   * @param endDate End date
   * @param limit Maximum number of values to return
   * @returns Metric values
   */
  getMetricValues(
    metricId: string,
    startDate?: Date,
    endDate?: Date,
    limit?: number,
  ): MetricValue[] {
    const metric = this.metrics.get(metricId);
    if (!metric) {
      return [];
    }

    let values = [...metric.values];

    // Filter by date range
    if (startDate || endDate) {
      values = values.filter(value => {
        if (startDate && value.timestamp < startDate) {
          return false;
        }
        if (endDate && value.timestamp > endDate) {
          return false;
        }
        return true;
      });
    }

    // Sort by timestamp (newest first)
    values.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply limit
    if (limit && limit > 0) {
      values = values.slice(0, limit);
    }

    return values;
  }

  /**
   * Get all alerts
   * @param metricId Optional metric ID filter
   * @param severity Optional severity filter
   * @param resolved Optional resolved filter
   * @param startDate Optional start date
   * @param endDate Optional end date
   * @param limit Maximum number of alerts to return
   * @returns List of alerts
   */
  getAlerts(
    metricId?: string,
    severity?: AlertSeverity,
    resolved?: boolean,
    startDate?: Date,
    endDate?: Date,
    limit?: number,
  ): PerformanceAlert[] {
    let alerts = Array.from(this.alerts.values());

    // Apply filters
    if (metricId) {
      alerts = alerts.filter(alert => alert.metricId === metricId);
    }

    if (severity) {
      alerts = alerts.filter(alert => alert.severity === severity);
    }

    if (resolved !== undefined) {
      alerts = alerts.filter(alert => alert.resolved === resolved);
    }

    if (startDate || endDate) {
      alerts = alerts.filter(alert => {
        if (startDate && alert.timestamp < startDate) {
          return false;
        }
        if (endDate && alert.timestamp > endDate) {
          return false;
        }
        return true;
      });
    }

    // Sort by timestamp (newest first)
    alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply limit
    if (limit && limit > 0) {
      alerts = alerts.slice(0, limit);
    }

    return alerts;
  }

  /**
   * Get a specific alert
   * @param alertId Alert ID
   * @returns Alert or null if not found
   */
  getAlert(alertId: string): PerformanceAlert {
    return this.alerts.get(alertId) || null;
  }

  /**
   * Acknowledge an alert
   * @param alertId Alert ID
   * @param acknowledgedBy User who acknowledged the alert
   * @returns Updated alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy?: string): PerformanceAlert {
    const alert = this.alerts.get(alertId);
    if (!alert) {
      return null;
    }

    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = new Date();

    // Emit event
    this.eventEmitter.emit('alert.acknowledged', alert);

    this.logger.debug(`Alert ${alertId} acknowledged by ${acknowledgedBy || 'system'}`);

    return alert;
  }

  /**
   * Resolve an alert
   * @param alertId Alert ID
   * @returns Updated alert
   */
  resolveAlert(alertId: string): PerformanceAlert {
    const alert = this.alerts.get(alertId);
    if (!alert) {
      return null;
    }

    alert.resolved = true;
    alert.resolvedAt = new Date();

    // Emit event
    this.eventEmitter.emit('alert.resolved', alert);

    this.logger.debug(`Alert ${alertId} resolved`);

    return alert;
  }

  /**
   * Generate a performance report
   * @param name Report name
   * @param description Report description
   * @param startDate Start date
   * @param endDate End date
   * @param metricIds Metric IDs to include
   * @returns Generated report
   */
  generateReport(
    name: string,
    description: string,
    startDate: Date,
    endDate: Date,
    metricIds?: string[],
  ): PerformanceReport {
    try {
      // Get metrics to include
      let metrics = metricIds
        ? metricIds.map(id => this.metrics.get(id)).filter(Boolean)
        : Array.from(this.metrics.values());

      // Get values for each metric
      const reportMetrics = metrics.map(metric => {
        const values = this.getMetricValues(metric.id, startDate, endDate);

        // Calculate summary statistics
        const summary = this.calculateMetricSummary(values);

        return {
          metricId: metric.id,
          metricName: metric.name,
          category: metric.category,
          values,
          summary,
        };
      });

      // Get alerts for the period
      const alerts = this.getAlerts(undefined, undefined, undefined, startDate, endDate);

      // Generate recommendations
      const recommendations = this.generateRecommendations(reportMetrics, alerts);

      // Create the report
      const report: PerformanceReport = {
        id: uuidv4(),
        name,
        description,
        startDate,
        endDate,
        metrics: reportMetrics,
        alerts,
        recommendations,
        createdAt: new Date(),
      };

      this.logger.log(`Generated performance report: ${name}`);

      return report;
    } catch (error) {
      this.logger.error(`Error generating performance report: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate summary statistics for metric values
   * @param values Metric values
   * @returns Summary statistics
   */
  private calculateMetricSummary(values: MetricValue[]): any {
    if (!values || values.length === 0) {
      return {
        min: 0,
        max: 0,
        avg: 0,
        median: 0,
        trend: 'stable',
      };
    }

    // Extract numeric values
    const numericValues = values.map(v => v.value);

    // Calculate min, max, avg
    const min = Math.min(...numericValues);
    const max = Math.max(...numericValues);
    const avg = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length;

    // Calculate median
    const sortedValues = [...numericValues].sort((a, b) => a - b);
    const middle = Math.floor(sortedValues.length / 2);
    const median = sortedValues.length % 2 === 0
      ? (sortedValues[middle - 1] + sortedValues[middle]) / 2
      : sortedValues[middle];

    // Calculate trend
    const trend = this.calculateTrend(values);

    return {
      min,
      max,
      avg,
      median,
      trend,
    };
  }

  /**
   * Calculate trend for metric values
   * @param values Metric values
   * @returns Trend direction
   */
  private calculateTrend(values: MetricValue[]): 'up' | 'down' | 'stable' {
    if (!values || values.length < 2) {
      return 'stable';
    }

    // Sort by timestamp (oldest first)
    const sortedValues = [...values].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // Get first and last values
    const firstValue = sortedValues[0].value;
    const lastValue = sortedValues[sortedValues.length - 1].value;

    // Calculate percentage change
    const percentChange = (lastValue - firstValue) / firstValue * 100;

    // Determine trend
    if (percentChange > 5) {
      return 'up';
    } else if (percentChange < -5) {
      return 'down';
    } else {
      return 'stable';
    }
  }

  /**
   * Generate recommendations based on metrics and alerts
   * @param metrics Metrics data
   * @param alerts Alerts
   * @returns List of recommendations
   */
  private generateRecommendations(metrics: any[], alerts: PerformanceAlert[]): string[] {
    const recommendations: string[] = [];

    // Check for critical alerts
    const criticalAlerts = alerts.filter(alert => alert.severity === AlertSeverity.CRITICAL);
    if (criticalAlerts.length > 0) {
      recommendations.push(`Address ${criticalAlerts.length} critical alerts, starting with ${criticalAlerts[0].metricName}.`);
    }

    // Check for metrics with negative trends
    const metricsWithNegativeTrends = metrics.filter(metric =>
      metric.summary.trend === 'down' &&
      (metric.category === MetricCategory.ACCURACY ||
       metric.category === MetricCategory.RELEVANCE ||
       metric.category === MetricCategory.USER_SATISFACTION)
    );

    if (metricsWithNegativeTrends.length > 0) {
      recommendations.push(`Investigate declining ${metricsWithNegativeTrends.map(m => m.metricName).join(', ')} metrics.`);
    }

    // Check for performance issues
    const performanceMetrics = metrics.filter(metric => metric.category === MetricCategory.PERFORMANCE);
    const poorPerformanceMetrics = performanceMetrics.filter(metric =>
      (metric.metricName === 'Response Time' && metric.summary.avg > 200) ||
      (metric.metricName === 'Throughput' && metric.summary.avg < 10)
    );

    if (poorPerformanceMetrics.length > 0) {
      recommendations.push(`Optimize system performance to improve ${poorPerformanceMetrics.map(m => m.metricName).join(', ')}.`);
    }

    // Check for diversity issues
    const diversityMetrics = metrics.filter(metric => metric.category === MetricCategory.DIVERSITY);
    const poorDiversityMetrics = diversityMetrics.filter(metric =>
      (metric.metricName === 'Category Diversity' && metric.summary.avg < 0.5) ||
      (metric.metricName === 'Item Similarity' && metric.summary.avg > 0.8)
    );

    if (poorDiversityMetrics.length > 0) {
      recommendations.push(`Improve recommendation diversity by adjusting diversity parameters.`);
    }

    // Add generic recommendations if none specific
    if (recommendations.length === 0) {
      recommendations.push('Continue monitoring system performance.');
      recommendations.push('Consider A/B testing to optimize recommendation algorithms.');
    }

    return recommendations;
  }

  /**
   * Scheduled task to clean up old data
   */
  @Cron('0 0 * * *') // Run at midnight every day
  async cleanupOldData(): Promise<void> {
    try {
      this.logger.log('Running scheduled data cleanup');

      // Clean up old metric values
      let metricsCleanedCount = 0;
      for (const metric of this.metrics.values()) {
        const originalLength = metric.values.length;
        this.pruneMetricValues(metric);
        metricsCleanedCount += originalLength - metric.values.length;
      }

      // Clean up old alerts
      const now = new Date();
      const alertCutoff = new Date(now.getTime() - this.alertRetentionDays * 24 * 60 * 60 * 1000);

      let alertsCleanedCount = 0;
      for (const [alertId, alert] of this.alerts.entries()) {
        if (alert.timestamp < alertCutoff) {
          this.alerts.delete(alertId);
          alertsCleanedCount++;
        }
      }

      this.logger.log(`Cleaned up ${metricsCleanedCount} metric values and ${alertsCleanedCount} alerts`);
    } catch (error) {
      this.logger.error(`Error cleaning up old data: ${error.message}`);
    }
  }

  /**
   * Listen for recommendation events
   * @param event Recommendation event
   */
  @OnEvent('recommendation.generated')
  async handleRecommendationEvent(event: any): Promise<void> {
    if (!this.monitoringEnabled) {
      return;
    }

    try {
      // Record response time
      if (event.responseTime) {
        this.recordMetricValue('response_time', event.responseTime, event.timestamp, {
          userId: event.userId,
          requestId: event.requestId,
        });
      }

      // Record other metrics if available
      if (event.metrics) {
        for (const [metricId, value] of Object.entries(event.metrics)) {
          if (this.metrics.has(metricId)) {
            this.recordMetricValue(metricId, value as number, event.timestamp, {
              userId: event.userId,
              requestId: event.requestId,
            });
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error handling recommendation event: ${error.message}`);
    }
  }

  /**
   * Listen for user interaction events
   * @param event User interaction event
   */
  @OnEvent('user.interaction')
  async handleUserInteractionEvent(event: any): Promise<void> {
    if (!this.monitoringEnabled) {
      return;
    }

    try {
      // Record click-through rate for recommendations
      if (event.interactionType === 'CLICK' && event.recommendationId) {
        this.recordMetricValue('click_through_rate', 1, event.timestamp, {
          userId: event.userId,
          recommendationId: event.recommendationId,
          itemId: event.itemId,
        });
      }

      // Record conversion rate for recommendations
      if (event.interactionType === 'PURCHASE' && event.recommendationId) {
        this.recordMetricValue('conversion_rate', 1, event.timestamp, {
          userId: event.userId,
          recommendationId: event.recommendationId,
          itemId: event.itemId,
          value: event.value,
        });
      }

      // Record user satisfaction if available
      if (event.interactionType === 'RATE' && event.rating !== undefined) {
        this.recordMetricValue('user_satisfaction', event.rating, event.timestamp, {
          userId: event.userId,
          recommendationId: event.recommendationId,
          itemId: event.itemId,
        });
      }
    } catch (error) {
      this.logger.error(`Error handling user interaction event: ${error.message}`);
    }
  }
}