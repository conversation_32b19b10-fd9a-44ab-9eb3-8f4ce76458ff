import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../../prisma/prisma.service';
import { CACHE_MANAGER, Cache } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * Niveau de cache
 */
export enum CacheLevel {
  /** Cache de premier niveau (mémoire) */
  L1 = 'L1',
  
  /** Cache de second niveau (Redis) */
  L2 = 'L2',
  
  /** Cache de troisième niveau (base de données) */
  L3 = 'L3',
}

/**
 * Stratégie de cache
 */
export enum CacheStrategy {
  /** Cache simple avec TTL */
  SIMPLE = 'SIMPLE',
  
  /** Cache avec invalidation sélective */
  SELECTIVE = 'SELECTIVE',
  
  /** Cache avec préchargement */
  PRELOADING = 'PRELOADING',
  
  /** Cache adaptatif */
  ADAPTIVE = 'ADAPTIVE',
}

/**
 * Configuration de cache
 */
export interface CacheConfig {
  /** Niveau de cache */
  level: CacheLevel;
  
  /** Stratégie de cache */
  strategy: CacheStrategy;
  
  /** TTL en secondes */
  ttl: number;
  
  /** Taille maximale du cache */
  maxSize?: number;
  
  /** Préfixe des clés */
  keyPrefix?: string;
  
  /** Métadonnées */
  metadata?: Record<string, any>;
}

/**
 * Statistiques de performance
 */
export interface PerformanceStats {
  /** Temps de réponse moyen (ms) */
  averageResponseTime: number;
  
  /** Temps de réponse médian (ms) */
  medianResponseTime: number;
  
  /** Temps de réponse au 95e percentile (ms) */
  p95ResponseTime: number;
  
  /** Nombre de requêtes par seconde */
  requestsPerSecond: number;
  
  /** Taux de succès du cache */
  cacheHitRate: number;
  
  /** Utilisation CPU (%) */
  cpuUsage: number;
  
  /** Utilisation mémoire (MB) */
  memoryUsage: number;
  
  /** Nombre de requêtes à la base de données */
  databaseQueries: number;
  
  /** Temps moyen des requêtes à la base de données (ms) */
  averageDatabaseQueryTime: number;
}

/**
 * Service d'optimisation des performances
 * Fournit des mécanismes pour optimiser les performances du système de recommandation
 */
@Injectable()
export class PerformanceOptimizationService {
  private readonly logger = new Logger(PerformanceOptimizationService.name);
  private readonly cacheConfigs: Map<string, CacheConfig> = new Map();
  private readonly performanceStats: Map<string, number[]> = new Map();
  private readonly cacheHitStats: Map<string, { hits: number; misses: number }> = new Map();
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {
    this.logger.log('PerformanceOptimizationService initialized');
    this.initializeCacheConfigs();
  }
  
  /**
   * Initialise les configurations de cache
   */
  private initializeCacheConfigs() {
    // Configuration pour les recommandations personnalisées
    this.cacheConfigs.set('recommendations', {
      level: CacheLevel.L1,
      strategy: CacheStrategy.ADAPTIVE,
      ttl: this.configService.get<number>('recommendation.cache.ttl.recommendations', 1800),
      keyPrefix: 'rec',
    });
    
    // Configuration pour les recommandations tendance
    this.cacheConfigs.set('trending', {
      level: CacheLevel.L2,
      strategy: CacheStrategy.PRELOADING,
      ttl: this.configService.get<number>('recommendation.cache.ttl.trending', 3600),
      keyPrefix: 'trend',
    });
    
    // Configuration pour les éléments similaires
    this.cacheConfigs.set('similar', {
      level: CacheLevel.L1,
      strategy: CacheStrategy.SELECTIVE,
      ttl: this.configService.get<number>('recommendation.cache.ttl.similar', 7200),
      keyPrefix: 'sim',
    });
    
    // Configuration pour les détails d'un élément
    this.cacheConfigs.set('itemDetails', {
      level: CacheLevel.L2,
      strategy: CacheStrategy.SIMPLE,
      ttl: this.configService.get<number>('recommendation.cache.ttl.itemDetails', 86400),
      keyPrefix: 'item',
    });
    
    this.logger.log(`Initialized ${this.cacheConfigs.size} cache configurations`);
  }
  
  /**
   * Récupère une valeur du cache
   * @param key Clé du cache
   * @param configKey Configuration de cache à utiliser
   * @returns Valeur du cache
   */
  async getCachedValue<T>(key: string, configKey: string): Promise<T | null> {
    const config = this.cacheConfigs.get(configKey);
    
    if (!config) {
      this.logger.warn(`Cache config not found for key: ${configKey}`);
      return null;
    }
    
    const cacheKey = `${config.keyPrefix}:${key}`;
    
    try {
      const startTime = Date.now();
      const value = await this.cacheManager.get<T>(cacheKey);
      const endTime = Date.now();
      
      this.recordCacheAccess(configKey, value !== null);
      this.recordPerformanceMetric('cacheAccessTime', endTime - startTime);
      
      return value;
    } catch (error) {
      this.logger.error(`Error getting cached value for ${cacheKey}: ${error.message}`);
      this.recordCacheAccess(configKey, false);
      return null;
    }
  }
  
  /**
   * Stocke une valeur dans le cache
   * @param key Clé du cache
   * @param value Valeur à stocker
   * @param configKey Configuration de cache à utiliser
   * @param customTtl TTL personnalisé (en secondes)
   */
  async setCachedValue<T>(
    key: string,
    value: T,
    configKey: string,
    customTtl?: number,
  ): Promise<void> {
    const config = this.cacheConfigs.get(configKey);
    
    if (!config) {
      this.logger.warn(`Cache config not found for key: ${configKey}`);
      return;
    }
    
    const cacheKey = `${config.keyPrefix}:${key}`;
    const ttl = customTtl || config.ttl;
    
    try {
      const startTime = Date.now();
      await this.cacheManager.set(cacheKey, value, ttl * 1000);
      const endTime = Date.now();
      
      this.recordPerformanceMetric('cacheSetTime', endTime - startTime);
    } catch (error) {
      this.logger.error(`Error setting cached value for ${cacheKey}: ${error.message}`);
    }
  }
  
  /**
   * Invalide une entrée du cache
   * @param key Clé du cache
   * @param configKey Configuration de cache à utiliser
   */
  async invalidateCacheEntry(key: string, configKey: string): Promise<void> {
    const config = this.cacheConfigs.get(configKey);
    
    if (!config) {
      this.logger.warn(`Cache config not found for key: ${configKey}`);
      return;
    }
    
    const cacheKey = `${config.keyPrefix}:${key}`;
    
    try {
      await this.cacheManager.del(cacheKey);
    } catch (error) {
      this.logger.error(`Error invalidating cache for ${cacheKey}: ${error.message}`);
    }
  }
  
  /**
   * Invalide toutes les entrées d'une configuration de cache
   * @param configKey Configuration de cache à invalider
   */
  async invalidateCache(configKey: string): Promise<void> {
    const config = this.cacheConfigs.get(configKey);
    
    if (!config) {
      this.logger.warn(`Cache config not found for key: ${configKey}`);
      return;
    }
    
    try {
      // Note: Cette méthode dépend de l'implémentation du cache
      // Pour Redis, on pourrait utiliser KEYS et DEL
      // Pour un cache en mémoire, on pourrait réinitialiser le store
      
      // Exemple simplifié:
      await this.cacheManager.reset();
      
      this.logger.log(`Invalidated cache for ${configKey}`);
    } catch (error) {
      this.logger.error(`Error invalidating cache for ${configKey}: ${error.message}`);
    }
  }
  
  /**
   * Précharge des données dans le cache
   * @param configKey Configuration de cache à précharger
   */
  async preloadCache(configKey: string): Promise<void> {
    const config = this.cacheConfigs.get(configKey);
    
    if (!config) {
      this.logger.warn(`Cache config not found for key: ${configKey}`);
      return;
    }
    
    if (config.strategy !== CacheStrategy.PRELOADING) {
      this.logger.warn(`Cache config ${configKey} is not configured for preloading`);
      return;
    }
    
    this.logger.log(`Preloading cache for ${configKey}`);
    
    try {
      switch (configKey) {
        case 'trending':
          await this.preloadTrendingRecommendations();
          break;
        case 'similar':
          await this.preloadSimilarItems();
          break;
        case 'itemDetails':
          await this.preloadItemDetails();
          break;
        default:
          this.logger.warn(`No preloading implementation for ${configKey}`);
      }
    } catch (error) {
      this.logger.error(`Error preloading cache for ${configKey}: ${error.message}`);
    }
  }
  
  /**
   * Optimise une requête à la base de données
   * @param queryName Nom de la requête
   * @param queryFn Fonction de requête
   * @returns Résultat de la requête
   */
  async optimizeQuery<T>(queryName: string, queryFn: () => Promise<T>): Promise<T> {
    this.logger.debug(`Optimizing query: ${queryName}`);
    
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const endTime = Date.now();
      
      this.recordPerformanceMetric('queryTime', endTime - startTime);
      this.recordPerformanceMetric(`queryTime:${queryName}`, endTime - startTime);
      
      return result;
    } catch (error) {
      const endTime = Date.now();
      this.recordPerformanceMetric('queryErrorTime', endTime - startTime);
      this.recordPerformanceMetric(`queryErrorTime:${queryName}`, endTime - startTime);
      
      throw error;
    }
  }
  
  /**
   * Récupère les statistiques de performance
   * @returns Statistiques de performance
   */
  getPerformanceStats(): PerformanceStats {
    const responseTimeSamples = this.performanceStats.get('responseTime') || [];
    const queryTimeSamples = this.performanceStats.get('queryTime') || [];
    
    // Calculer les statistiques de cache
    let totalHits = 0;
    let totalMisses = 0;
    
    for (const stats of this.cacheHitStats.values()) {
      totalHits += stats.hits;
      totalMisses += stats.misses;
    }
    
    const cacheHitRate = totalHits / (totalHits + totalMisses) || 0;
    
    // Calculer les statistiques de temps de réponse
    responseTimeSamples.sort((a, b) => a - b);
    const averageResponseTime = responseTimeSamples.reduce((sum, time) => sum + time, 0) / responseTimeSamples.length || 0;
    const medianResponseTime = responseTimeSamples[Math.floor(responseTimeSamples.length / 2)] || 0;
    const p95Index = Math.floor(responseTimeSamples.length * 0.95);
    const p95ResponseTime = responseTimeSamples[p95Index] || 0;
    
    // Calculer les statistiques de requêtes
    const averageDatabaseQueryTime = queryTimeSamples.reduce((sum, time) => sum + time, 0) / queryTimeSamples.length || 0;
    
    return {
      averageResponseTime,
      medianResponseTime,
      p95ResponseTime,
      requestsPerSecond: this.calculateRequestsPerSecond(),
      cacheHitRate,
      cpuUsage: this.getCpuUsage(),
      memoryUsage: this.getMemoryUsage(),
      databaseQueries: queryTimeSamples.length,
      averageDatabaseQueryTime,
    };
  }
  
  /**
   * Enregistre une métrique de performance
   * @param metricName Nom de la métrique
   * @param value Valeur de la métrique
   */
  recordPerformanceMetric(metricName: string, value: number): void {
    if (!this.performanceStats.has(metricName)) {
      this.performanceStats.set(metricName, []);
    }
    
    const samples = this.performanceStats.get(metricName);
    samples.push(value);
    
    // Limiter le nombre d'échantillons
    const maxSamples = 1000;
    if (samples.length > maxSamples) {
      samples.shift();
    }
  }
  
  /**
   * Enregistre un accès au cache
   * @param configKey Configuration de cache
   * @param isHit Indique si c'est un hit ou un miss
   */
  private recordCacheAccess(configKey: string, isHit: boolean): void {
    if (!this.cacheHitStats.has(configKey)) {
      this.cacheHitStats.set(configKey, { hits: 0, misses: 0 });
    }
    
    const stats = this.cacheHitStats.get(configKey);
    
    if (isHit) {
      stats.hits++;
    } else {
      stats.misses++;
    }
  }
  
  /**
   * Calcule le nombre de requêtes par seconde
   * @returns Nombre de requêtes par seconde
   */
  private calculateRequestsPerSecond(): number {
    const responseTimeSamples = this.performanceStats.get('responseTime') || [];
    const timeWindow = 60 * 1000; // 1 minute en millisecondes
    const now = Date.now();
    
    // Compter les requêtes dans la dernière minute
    // Note: Ceci est une simplification, en réalité il faudrait stocker les timestamps
    const recentSamplesCount = responseTimeSamples.length;
    
    return recentSamplesCount / 60;
  }
  
  /**
   * Récupère l'utilisation CPU
   * @returns Utilisation CPU (%)
   */
  private getCpuUsage(): number {
    // Note: Dans un environnement réel, on utiliserait des métriques système
    // Exemple simplifié:
    return Math.random() * 50; // 0-50%
  }
  
  /**
   * Récupère l'utilisation mémoire
   * @returns Utilisation mémoire (MB)
   */
  private getMemoryUsage(): number {
    // Note: Dans un environnement réel, on utiliserait des métriques système
    // Exemple simplifié:
    const memoryUsage = process.memoryUsage();
    return Math.round(memoryUsage.heapUsed / 1024 / 1024);
  }
  
  /**
   * Tâche planifiée pour précharger les caches
   */
  @Cron(CronExpression.EVERY_HOUR)
  async preloadCaches() {
    this.logger.log('Preloading caches');
    
    for (const [configKey, config] of this.cacheConfigs.entries()) {
      if (config.strategy === CacheStrategy.PRELOADING) {
        await this.preloadCache(configKey);
      }
    }
  }
  
  /**
   * Tâche planifiée pour nettoyer les métriques de performance
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  cleanupPerformanceMetrics() {
    this.logger.log('Cleaning up performance metrics');
    
    // Garder seulement les 1000 derniers échantillons pour chaque métrique
    for (const [metricName, samples] of this.performanceStats.entries()) {
      const maxSamples = 1000;
      if (samples.length > maxSamples) {
        this.performanceStats.set(metricName, samples.slice(-maxSamples));
      }
    }
  }
  
  // Méthodes de préchargement
  
  private async preloadTrendingRecommendations() {
    // TODO: Implémenter le préchargement des recommandations tendance
  }
  
  private async preloadSimilarItems() {
    // TODO: Implémenter le préchargement des éléments similaires
  }
  
  private async preloadItemDetails() {
    // TODO: Implémenter le préchargement des détails d'éléments
  }
}
