import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../../../prisma/prisma.module';
import { CacheModule } from '@nestjs/cache-manager';
import { PerformanceOptimizationService } from './performance-optimization.service';

@Module({
  imports: [
    ConfigModule,
    PrismaModule,
    CacheModule.register({
      isGlobal: true,
      ttl: 60 * 60 * 1000, // 1 hour
    }),
  ],
  providers: [
    PerformanceOptimizationService,
  ],
  exports: [
    PerformanceOptimizationService,
  ],
})
export class PerformanceModule {}
