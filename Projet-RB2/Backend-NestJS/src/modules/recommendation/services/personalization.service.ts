import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { HybridMethod } from '../enums/hybrid-method.enum';
import { FactorizationMethod } from '../enums/factorization-method.enum';
import { RecommendationCacheService } from './recommendation-cache.service';

@Injectable()
export class PersonalizationService {
  private readonly logger = new Logger(PersonalizationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly recommendationCache: RecommendationCacheService,
  ) {}

  /**
   * Récupère les préférences de recommandation d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Préférences de recommandation
   */
  async getUserPreferences(userId: string) {
    try {
      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.prisma.userPreference.findUnique({
        where: { userId },
      });

      if (userPreferences) {
        return userPreferences.preferences || this.getDefaultPreferences();
      }

      // Si aucune préférence n'existe, créer des préférences par défaut
      return this.createDefaultPreferences(userId);
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des préférences de l'utilisateur: ${error.message}`);
      return this.getDefaultPreferences();
    }
  }

  /**
   * Met à jour les préférences de recommandation d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param preferences Nouvelles préférences
   * @returns Préférences mises à jour
   */
  async updateUserPreferences(userId: string, preferences: Record<string, any>) {
    try {
      // Récupérer les préférences existantes
      const existingPreferences = await this.getUserPreferences(userId);

      // Fusionner les préférences
      const mergedPreferences = {
        ...existingPreferences,
        ...preferences,
      };

      // Mettre à jour les préférences
      const updatedPreferences = await this.prisma.userPreference.upsert({
        where: { userId },
        update: {
          preferences: mergedPreferences,
        },
        create: {
          userId,
          preferences: mergedPreferences,
        },
      });

      return updatedPreferences.preferences;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des préférences de l'utilisateur: ${error.message}`);
      throw error;
    }
  }

  /**
   * Filtre les recommandations selon les préférences de l'utilisateur
   * @param userId ID de l'utilisateur
   * @param recommendations Liste des recommandations
   * @param userPreferences Préférences de l'utilisateur
   * @returns Liste des recommandations filtrées
   */
  async filterRecommendations(
    userId: string,
    recommendations: any[],
    userPreferences: Record<string, any> = {},
  ) {
    try {
      // Si aucune préférence n'est fournie, récupérer les préférences de l'utilisateur
      if (Object.keys(userPreferences).length === 0) {
        userPreferences = await this.getUserPreferences(userId);
      }

      // Filtrer les recommandations selon les préférences
      let filteredRecommendations = [...recommendations];

      // Filtrer par catégorie
      if (userPreferences.excludedCategories && userPreferences.excludedCategories.length > 0) {
        filteredRecommendations = filteredRecommendations.filter(recommendation => {
          const category = recommendation.metadata?.category;
          return category && !userPreferences.excludedCategories.includes(category);
        });
      }

      // Filtrer par niveau
      if (userPreferences.preferredLevels && userPreferences.preferredLevels.length > 0) {
        filteredRecommendations = filteredRecommendations.filter(recommendation => {
          const level = recommendation.metadata?.level;
          return !level || userPreferences.preferredLevels.includes(level);
        });
      }

      // Filtrer par localisation
      if (userPreferences.preferredLocations && userPreferences.preferredLocations.length > 0) {
        filteredRecommendations = filteredRecommendations.filter(recommendation => {
          const location = recommendation.metadata?.location;
          return !location || userPreferences.preferredLocations.includes(location);
        });
      }

      // Appliquer un boost aux recommandations selon les préférences
      filteredRecommendations = filteredRecommendations.map(recommendation => {
        let boost = 1;

        // Boost basé sur la catégorie
        if (recommendation.metadata?.category && userPreferences.preferredCategories) {
          const category = recommendation.metadata.category;
          if (userPreferences.preferredCategories.includes(category)) {
            boost *= 1.2;
          }
        }

        // Boost basé sur les tags
        if (recommendation.metadata?.tags && userPreferences.preferredTags) {
          const tags = recommendation.metadata.tags;
          const commonTags = tags.filter(tag => userPreferences.preferredTags.includes(tag));
          if (commonTags.length > 0) {
            boost *= 1 + (commonTags.length * 0.1);
          }
        }

        return {
          ...recommendation,
          score: recommendation.score * boost,
        };
      });

      // Trier les recommandations par score
      return filteredRecommendations.sort((a, b) => b.score - a.score);
    } catch (error) {
      this.logger.error(`Erreur lors du filtrage des recommandations: ${error.message}`);
      return recommendations;
    }
  }

  /**
   * Crée des préférences par défaut pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Préférences par défaut
   */
  private async createDefaultPreferences(userId: string) {
    try {
      const defaultPreferences = this.getDefaultPreferences();

      // Créer les préférences par défaut
      const userPreferences = await this.prisma.userPreference.create({
        data: {
          userId,
          preferences: defaultPreferences,
        },
      });

      return userPreferences.preferences;
    } catch (error) {
      this.logger.error(`Erreur lors de la création des préférences par défaut: ${error.message}`);
      return this.getDefaultPreferences();
    }
  }

  /**
   * Récupère les préférences par défaut
   * @returns Préférences par défaut
   */
  private getDefaultPreferences() {
    return {
      // Stratégie de recommandation par défaut
      recommendationStrategy: RecommendationStrategy.HYBRID,

      // Types de contenu préférés
      preferredTypes: [RecommendationType.COURSE, RecommendationType.RETREAT],

      // Préférences de catégories
      categories: {
        preferred: [],
        avoided: [],
      },

      // Rétrocompatibilité avec l'ancien format
      preferredCategories: [],
      excludedCategories: [],
      preferredLevels: [],
      preferredLocations: [],
      preferredTags: [],

      // Paramètres de diversification
      diversification: {
        enabled: true,
        weight: 0.3,
        method: 'MMR',
      },

      // Paramètres de factorisation matricielle
      matrixFactorization: {
        method: FactorizationMethod.SVD,
        numFactors: 20,
        regularization: 0.1,
        numIterations: 50,
      },

      // Paramètres hybrides
      hybrid: {
        method: HybridMethod.WEIGHTED,
        contentBasedWeight: 0.6,
        collaborativeWeight: 0.4,
        interactionThreshold: 10,
      },

      // Paramètres généraux
      maxRecommendations: 10,
      includeMetadata: true,
    };
  }

  /**
   * Invalide le cache des recommandations pour un utilisateur
   * @param userId ID de l'utilisateur
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      this.logger.log(`Invalidation du cache des recommandations pour l'utilisateur ${userId}`);
      await this.recommendationCache.invalidateUserCache(userId);
    } catch (error) {
      this.logger.error(`Erreur lors de l'invalidation du cache: ${error.message}`);
    }
  }
}
