import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import { HierarchicalPreferenceService } from './hierarchical-preference.service';
import {
  PreferenceValue,
  PreferenceSource,
  PreferenceInferenceRule,
  PreferenceEvent,
} from '../interfaces/hierarchical-preference.interface';

/**
 * Service for inferring user preferences from behavior
 */
@Injectable()
export class PreferenceInferenceService {
  private readonly logger = new Logger(PreferenceInferenceService.name);
  private readonly inferenceRules: PreferenceInferenceRule[] = [];
  private readonly inferenceEnabled: boolean;
  private readonly minConfidenceThreshold: number;
  private readonly maxInferencesPerDay: number;
  private readonly inferenceCounts: Map<string, { count: number; lastReset: Date }> = new Map();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly preferenceService: HierarchicalPreferenceService,
  ) {
    this.inferenceEnabled = this.configService.get<boolean>('PREFERENCE_INFERENCE_ENABLED', true);
    this.minConfidenceThreshold = this.configService.get<number>('PREFERENCE_INFERENCE_MIN_CONFIDENCE', 0.6);
    this.maxInferencesPerDay = this.configService.get<number>('PREFERENCE_INFERENCE_MAX_PER_DAY', 10);

    // Load inference rules
    this.loadInferenceRules().catch(error => {
      this.logger.error(`Error loading inference rules: ${error.message}`);
    });
  }

  /**
   * Load inference rules from the database
   */
  private async loadInferenceRules(): Promise<void> {
    try {
      // In a real implementation, this would load rules from the database
      // For now, we'll create some sample rules
      this.inferenceRules.push(
        this.createInteractionBasedRule(
          'category_preference_from_views',
          'Infer category preferences from view patterns',
          {
            interactionType: 'VIEW',
            minCount: 5,
            timeWindowDays: 7,
          },
          ['preferences', 'categories'],
          0.7,
        ),
        this.createInteractionBasedRule(
          'topic_preference_from_bookmarks',
          'Infer topic preferences from bookmarks',
          {
            interactionType: 'BOOKMARK',
            minCount: 2,
            timeWindowDays: 30,
          },
          ['preferences', 'topics'],
          0.8,
        ),
        this.createProfileBasedRule(
          'language_preference_from_profile',
          'Infer language preferences from user profile',
          {
            profileField: 'language',
          },
          ['preferences', 'language'],
          0.9,
        ),
        this.createContextBasedRule(
          'location_preference_from_context',
          'Infer location preferences from user context',
          {
            contextField: 'location',
            minOccurrences: 3,
          },
          ['preferences', 'location'],
          0.75,
        ),
      );

      this.logger.log(`Loaded ${this.inferenceRules.length} inference rules`);
    } catch (error) {
      this.logger.error(`Error loading inference rules: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create an interaction-based inference rule
   */
  private createInteractionBasedRule(
    id: string,
    description: string,
    parameters: any,
    path: string[],
    confidence: number,
  ): PreferenceInferenceRule {
    return {
      id,
      name: id,
      description,
      condition: {
        type: 'interaction',
        parameters,
      },
      action: {
        path,
        key: 'inferred',
        value: true,
        confidence,
      },
      priority: 1,
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Create a profile-based inference rule
   */
  private createProfileBasedRule(
    id: string,
    description: string,
    parameters: any,
    path: string[],
    confidence: number,
  ): PreferenceInferenceRule {
    return {
      id,
      name: id,
      description,
      condition: {
        type: 'profile',
        parameters,
      },
      action: {
        path,
        key: 'inferred',
        value: true,
        confidence,
      },
      priority: 2,
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Create a context-based inference rule
   */
  private createContextBasedRule(
    id: string,
    description: string,
    parameters: any,
    path: string[],
    confidence: number,
  ): PreferenceInferenceRule {
    return {
      id,
      name: id,
      description,
      condition: {
        type: 'context',
        parameters,
      },
      action: {
        path,
        key: 'inferred',
        value: true,
        confidence,
      },
      priority: 3,
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Process a user interaction to infer preferences
   * @param userId User ID
   * @param interactionType Type of interaction
   * @param itemId Item ID
   * @param itemType Item type
   * @param metadata Additional metadata
   */
  async processInteraction(
    userId: string,
    interactionType: string,
    itemId: string,
    itemType: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    if (!this.inferenceEnabled) {
      return;
    }

    try {
      // Check if we've reached the daily limit for this user
      if (this.hasReachedDailyLimit(userId)) {
        this.logger.debug(`Skipping inference for user ${userId}: daily limit reached`);
        return;
      }

      this.logger.debug(`Processing ${interactionType} interaction for user ${userId} with ${itemType} ${itemId}`);

      // Get item details
      const itemDetails = await this.getItemDetails(itemId, itemType);
      if (!itemDetails) {
        this.logger.debug(`Skipping inference: no details found for ${itemType} ${itemId}`);
        return;
      }

      // Find applicable rules
      const applicableRules = this.inferenceRules.filter(rule =>
        rule.enabled &&
        rule.condition.type === 'interaction' &&
        this.isRuleApplicable(rule, interactionType, itemDetails, metadata)
      );

      if (applicableRules.length === 0) {
        this.logger.debug(`No applicable inference rules for ${interactionType} on ${itemType}`);
        return;
      }

      // Apply each rule
      for (const rule of applicableRules) {
        await this.applyInferenceRule(userId, rule, itemDetails, metadata);
      }

      // Increment the inference count for this user
      this.incrementInferenceCount(userId);
    } catch (error) {
      this.logger.error(`Error processing interaction for inference: ${error.message}`);
    }
  }

  /**
   * Check if a rule is applicable to an interaction
   * @param rule Inference rule
   * @param interactionType Type of interaction
   * @param itemDetails Item details
   * @param metadata Additional metadata
   * @returns Whether the rule is applicable
   */
  private isRuleApplicable(
    rule: PreferenceInferenceRule,
    interactionType: string,
    itemDetails: any,
    metadata?: Record<string, any>,
  ): boolean {
    const params = rule.condition.parameters;

    // Check if the interaction type matches
    if (params.interactionType && params.interactionType !== interactionType) {
      return false;
    }

    // Additional checks based on rule parameters
    // For example, check if the item has required properties
    if (params.requiredItemProperties) {
      for (const prop of params.requiredItemProperties) {
        if (!itemDetails[prop]) {
          return false;
        }
      }
    }

    // Check metadata conditions if specified
    if (params.metadataConditions && metadata) {
      for (const [key, value] of Object.entries(params.metadataConditions)) {
        if (metadata[key] !== value) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Apply an inference rule to generate a preference
   * @param userId User ID
   * @param rule Inference rule
   * @param itemDetails Item details
   * @param metadata Additional metadata
   */
  private async applyInferenceRule(
    userId: string,
    rule: PreferenceInferenceRule,
    itemDetails: any,
    metadata?: Record<string, any>,
  ): Promise<void> {
    try {
      // Extract the value to set based on the rule and item details
      const value = this.extractValueFromRule(rule, itemDetails, metadata);
      if (value === undefined) {
        this.logger.debug(`Skipping rule ${rule.id}: could not extract value`);
        return;
      }

      // Create the preference value
      const preferenceValue: PreferenceValue = {
        value,
        confidence: rule.action.confidence,
        source: PreferenceSource.IMPLICIT,
        timestamp: new Date(),
        metadata: {
          ruleId: rule.id,
          ruleName: rule.name,
          itemId: itemDetails.id,
          itemType: itemDetails.type,
          ...metadata,
        },
      };

      // Set the preference
      const path = [...rule.action.path];
      if (rule.action.key) {
        path.push(rule.action.key);
      }

      await this.preferenceService.setPreference(userId, path, preferenceValue);

      this.logger.debug(`Applied inference rule ${rule.id} for user ${userId}: set ${path.join('.')} to ${value}`);

      // Emit an event
      this.eventEmitter.emit('preference.inferred', {
        userId,
        ruleId: rule.id,
        path,
        value: preferenceValue,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Error applying inference rule ${rule.id} for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Extract a value from an item based on a rule
   * @param rule Inference rule
   * @param itemDetails Item details
   * @param metadata Additional metadata
   * @returns Extracted value
   */
  private extractValueFromRule(
    rule: PreferenceInferenceRule,
    itemDetails: any,
    metadata?: Record<string, any>,
  ): any {
    const params = rule.condition.parameters;

    // Different extraction strategies based on rule type
    if (rule.condition.type === 'interaction') {
      // For category-based rules
      if (rule.action.path.includes('categories') && itemDetails.categories) {
        return itemDetails.categories;
      }

      // For topic-based rules
      if (rule.action.path.includes('topics') && itemDetails.tags) {
        return itemDetails.tags;
      }

      // For location-based rules
      if (rule.action.path.includes('location') && itemDetails.location) {
        return itemDetails.location;
      }
    }

    // For profile-based rules
    if (rule.condition.type === 'profile' && params.profileField) {
      return itemDetails[params.profileField];
    }

    // For context-based rules
    if (rule.condition.type === 'context' && params.contextField && metadata) {
      return metadata[params.contextField];
    }

    // Default: use the value specified in the rule
    return rule.action.value;
  }

  /**
   * Get details about an item
   * @param itemId Item ID
   * @param itemType Item type
   * @returns Item details
   */
  private async getItemDetails(itemId: string, itemType: string): Promise<any> {
    try {
      // In a real implementation, this would fetch item details from the database
      // For now, we'll return mock data
      switch (itemType.toLowerCase()) {
        case 'retreat':
          return {
            id: itemId,
            type: 'retreat',
            title: `Retreat ${itemId}`,
            description: 'A relaxing retreat',
            categories: ['wellness', 'meditation'],
            tags: ['relaxation', 'mindfulness'],
            location: 'mountain',
          };

        case 'course':
          return {
            id: itemId,
            type: 'course',
            title: `Course ${itemId}`,
            description: 'An educational course',
            categories: ['education', 'self-improvement'],
            tags: ['learning', 'skills'],
            instructor: 'Expert Teacher',
          };

        case 'partner':
          return {
            id: itemId,
            type: 'partner',
            name: `Partner ${itemId}`,
            description: 'A wellness partner',
            categories: ['wellness', 'coaching'],
            services: ['consultation', 'coaching'],
            location: 'urban',
          };

        case 'content':
          return {
            id: itemId,
            type: 'content',
            title: `Content ${itemId}`,
            description: 'Informative content',
            categories: ['article', 'wellness'],
            tags: ['information', 'tips'],
            author: 'Content Creator',
          };

        default:
          return null;
      }
    } catch (error) {
      this.logger.error(`Error getting item details for ${itemType} ${itemId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Check if a user has reached the daily inference limit
   * @param userId User ID
   * @returns Whether the user has reached the limit
   */
  private hasReachedDailyLimit(userId: string): boolean {
    const userCount = this.inferenceCounts.get(userId);
    if (!userCount) {
      return false;
    }

    // Check if we need to reset the count (new day)
    const now = new Date();
    const lastReset = userCount.lastReset;
    if (now.getDate() !== lastReset.getDate() ||
        now.getMonth() !== lastReset.getMonth() ||
        now.getFullYear() !== lastReset.getFullYear()) {
      // Reset for a new day
      this.inferenceCounts.set(userId, { count: 0, lastReset: now });
      return false;
    }

    return userCount.count >= this.maxInferencesPerDay;
  }

  /**
   * Increment the inference count for a user
   * @param userId User ID
   */
  private incrementInferenceCount(userId: string): void {
    const now = new Date();
    const userCount = this.inferenceCounts.get(userId);

    if (!userCount) {
      this.inferenceCounts.set(userId, { count: 1, lastReset: now });
      return;
    }

    // Check if we need to reset the count (new day)
    const lastReset = userCount.lastReset;
    if (now.getDate() !== lastReset.getDate() ||
        now.getMonth() !== lastReset.getMonth() ||
        now.getFullYear() !== lastReset.getFullYear()) {
      // Reset for a new day
      this.inferenceCounts.set(userId, { count: 1, lastReset: now });
      return;
    }

    // Increment the count
    this.inferenceCounts.set(userId, {
      count: userCount.count + 1,
      lastReset: userCount.lastReset
    });
  }

  /**
   * Process user profile changes to infer preferences
   * @param userId User ID
   * @param profileData Profile data
   */
  async processProfileChange(userId: string, profileData: any): Promise<void> {
    if (!this.inferenceEnabled) {
      return;
    }

    try {
      this.logger.debug(`Processing profile change for user ${userId}`);

      // Find applicable rules
      const applicableRules = this.inferenceRules.filter(rule =>
        rule.enabled &&
        rule.condition.type === 'profile' &&
        this.isProfileRuleApplicable(rule, profileData)
      );

      if (applicableRules.length === 0) {
        this.logger.debug(`No applicable inference rules for profile change`);
        return;
      }

      // Apply each rule
      for (const rule of applicableRules) {
        await this.applyProfileInferenceRule(userId, rule, profileData);
      }
    } catch (error) {
      this.logger.error(`Error processing profile change for inference: ${error.message}`);
    }
  }

  /**
   * Check if a profile rule is applicable
   * @param rule Inference rule
   * @param profileData Profile data
   * @returns Whether the rule is applicable
   */
  private isProfileRuleApplicable(rule: PreferenceInferenceRule, profileData: any): boolean {
    const params = rule.condition.parameters;

    // Check if the required profile field exists
    if (params.profileField && profileData[params.profileField] === undefined) {
      return false;
    }

    return true;
  }

  /**
   * Apply a profile inference rule
   * @param userId User ID
   * @param rule Inference rule
   * @param profileData Profile data
   */
  private async applyProfileInferenceRule(
    userId: string,
    rule: PreferenceInferenceRule,
    profileData: any,
  ): Promise<void> {
    try {
      const params = rule.condition.parameters;
      const fieldValue = profileData[params.profileField];

      if (fieldValue === undefined) {
        return;
      }

      // Create the preference value
      const preferenceValue: PreferenceValue = {
        value: fieldValue,
        confidence: rule.action.confidence,
        source: PreferenceSource.IMPLICIT,
        timestamp: new Date(),
        metadata: {
          ruleId: rule.id,
          ruleName: rule.name,
          profileField: params.profileField,
        },
      };

      // Set the preference
      const path = [...rule.action.path];
      if (rule.action.key) {
        path.push(rule.action.key);
      }

      await this.preferenceService.setPreference(userId, path, preferenceValue);

      this.logger.debug(`Applied profile inference rule ${rule.id} for user ${userId}: set ${path.join('.')} to ${fieldValue}`);
    } catch (error) {
      this.logger.error(`Error applying profile inference rule ${rule.id} for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Listen for user interaction events
   * @param event Interaction event
   */
  @OnEvent('user.interaction')
  async handleInteractionEvent(event: any): Promise<void> {
    if (!event.userId || !event.interactionType || !event.itemId || !event.itemType) {
      return;
    }

    await this.processInteraction(
      event.userId,
      event.interactionType,
      event.itemId,
      event.itemType,
      event.metadata,
    );
  }

  /**
   * Listen for user profile update events
   * @param event Profile update event
   */
  @OnEvent('user.profile.updated')
  async handleProfileUpdateEvent(event: any): Promise<void> {
    if (!event.userId || !event.profileData) {
      return;
    }

    await this.processProfileChange(event.userId, event.profileData);
  }
}