import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';
import { v4 as uuidv4 } from 'uuid';
import { RecommendationService } from './recommendation.service';
import { AdvancedContextService } from './advanced-context.service';
import { GenerativeAIService } from './generative-ai.service';
import {
  ProactiveRecommendationConfig,
  NeedsPredictionStrategy,
  NotificationStrategy,
  AnticipationStrategy,
  ProactiveRecommendationTrigger,
  ProactiveRecommendationPriority,
  ProactiveRecommendation,
  UserNeed,
  NotificationConfig,
} from '../interfaces/proactive-recommendation.interface';

/**
 * Service for proactive recommendations
 */
@Injectable()
export class ProactiveRecommendationService implements OnModuleInit {
  private readonly logger = new Logger(ProactiveRecommendationService.name);
  private proactiveConfig: ProactiveRecommendationConfig;
  private readonly pendingRecommendations: Map<string, ProactiveRecommendation[]> = new Map();
  private readonly userNeeds: Map<string, UserNeed[]> = new Map();
  private readonly notificationHistory: Map<string, { timestamp: Date; count: number }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly recommendationService: RecommendationService,
    private readonly contextService: AdvancedContextService,
    private readonly aiService: GenerativeAIService,
  ) {
    // Initialize configurations
    this.initializeConfigurations();
  }

  /**
   * Initialize on module initialization
   */
  async onModuleInit() {
    try {
      this.logger.log('Proactive recommendation service initialized');
    } catch (error) {
      this.logger.error(`Error initializing proactive recommendation service: ${error.message}`);
    }
  }

  /**
   * Initialize configurations
   */
  private initializeConfigurations(): void {
    try {
      // Initialize proactive configuration
      this.proactiveConfig = {
        enabled: this.configService.get<boolean>('PROACTIVE_RECOMMENDATIONS_ENABLED', true),
        needsPrediction: {
          enabled: true,
          strategies: [
            NeedsPredictionStrategy.PATTERN_BASED,
            NeedsPredictionStrategy.CONTEXT_BASED,
            NeedsPredictionStrategy.SCHEDULE_BASED,
          ],
          confidenceThreshold: 0.7,
          maxPredictionsPerUser: 5,
        },
        notifications: {
          enabled: true,
          strategies: [
            NotificationStrategy.PUSH,
            NotificationStrategy.EMAIL,
            NotificationStrategy.IN_APP,
          ],
          maxPerDay: 3,
          quietHours: {
            enabled: true,
            start: 22, // 10 PM
            end: 8, // 8 AM
          },
          cooldown: {
            enabled: true,
            minutes: 120, // 2 hours
          },
        },
        anticipation: {
          enabled: true,
          strategies: [
            AnticipationStrategy.SEASONAL,
            AnticipationStrategy.LIFE_EVENT,
            AnticipationStrategy.BEHAVIORAL,
          ],
          lookAheadDays: 14,
          minConfidence: 0.6,
        },
      };

      this.logger.log('Proactive recommendation configurations initialized');
    } catch (error) {
      this.logger.error(`Error initializing configurations: ${error.message}`);
    }
  }

  /**
   * Get proactive recommendation configuration
   * @returns Proactive recommendation configuration
   */
  getProactiveConfig(): ProactiveRecommendationConfig {
    return this.proactiveConfig;
  }

  /**
   * Update proactive recommendation configuration
   * @param config New proactive recommendation configuration
   * @returns Updated proactive recommendation configuration
   */
  updateProactiveConfig(config: Partial<ProactiveRecommendationConfig>): ProactiveRecommendationConfig {
    this.proactiveConfig = {
      ...this.proactiveConfig,
      ...config,
    };

    this.logger.log('Proactive recommendation configuration updated');
    this.eventEmitter.emit('proactive.config.updated', this.proactiveConfig);

    return this.proactiveConfig;
  }

  /**
   * Get pending proactive recommendations for a user
   * @param userId User ID
   * @param limit Maximum number of recommendations to return
   * @param minPriority Minimum priority level
   * @returns Pending proactive recommendations
   */
  async getPendingRecommendations(
    userId: string,
    limit: number = 10,
    minPriority: ProactiveRecommendationPriority = ProactiveRecommendationPriority.LOW,
  ): Promise<ProactiveRecommendation[]> {
    try {
      if (!this.proactiveConfig.enabled) {
        return [];
      }

      // Get pending recommendations for user
      const userRecommendations = this.pendingRecommendations.get(userId) || [];
      
      // Filter by priority and limit
      return userRecommendations
        .filter(rec => rec.priority >= minPriority)
        .sort((a, b) => b.priority - a.priority)
        .slice(0, limit);
    } catch (error) {
      this.logger.error(`Error getting pending recommendations for user ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Generate proactive recommendations for a user
   * @param userId User ID
   * @param trigger What triggered the recommendation generation
   * @returns Generated proactive recommendations
   */
  async generateProactiveRecommendations(
    userId: string,
    trigger: ProactiveRecommendationTrigger = ProactiveRecommendationTrigger.MANUAL,
  ): Promise<ProactiveRecommendation[]> {
    try {
      if (!this.proactiveConfig.enabled) {
        return [];
      }

      // Predict user needs
      const predictedNeeds = await this.predictUserNeeds(userId);
      
      // Generate recommendations based on needs
      const recommendations: ProactiveRecommendation[] = [];
      
      for (const need of predictedNeeds) {
        // Get recommendations for this need
        const needRecommendations = await this.getRecommendationsForNeed(userId, need);
        
        // Add to list
        recommendations.push(...needRecommendations);
      }
      
      // Store pending recommendations
      this.storePendingRecommendations(userId, recommendations);
      
      // Send notifications if appropriate
      if (trigger !== ProactiveRecommendationTrigger.NOTIFICATION_CHECK) {
        await this.checkAndSendNotifications(userId);
      }
      
      return recommendations;
    } catch (error) {
      this.logger.error(`Error generating proactive recommendations for user ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Predict user needs
   * @param userId User ID
   * @returns Predicted user needs
   */
  private async predictUserNeeds(userId: string): Promise<UserNeed[]> {
    try {
      if (!this.proactiveConfig.needsPrediction.enabled) {
        return [];
      }

      const predictedNeeds: UserNeed[] = [];
      
      // Get user context
      const contextData = await this.contextService.getContextData(userId);
      
      // Apply different prediction strategies
      for (const strategy of this.proactiveConfig.needsPrediction.strategies) {
        switch (strategy) {
          case NeedsPredictionStrategy.PATTERN_BASED:
            const patternNeeds = await this.predictNeedsFromPatterns(userId);
            predictedNeeds.push(...patternNeeds);
            break;
            
          case NeedsPredictionStrategy.CONTEXT_BASED:
            const contextNeeds = await this.predictNeedsFromContext(userId, contextData);
            predictedNeeds.push(...contextNeeds);
            break;
            
          case NeedsPredictionStrategy.SCHEDULE_BASED:
            const scheduleNeeds = await this.predictNeedsFromSchedule(userId, contextData);
            predictedNeeds.push(...scheduleNeeds);
            break;
        }
      }
      
      // Filter by confidence threshold and deduplicate
      const filteredNeeds = predictedNeeds
        .filter(need => need.confidence >= this.proactiveConfig.needsPrediction.confidenceThreshold)
        .reduce((unique, need) => {
          // Check if we already have a need of this type
          const existingIndex = unique.findIndex(n => n.type === need.type);
          
          if (existingIndex === -1) {
            // New need type, add it
            unique.push(need);
          } else if (need.confidence > unique[existingIndex].confidence) {
            // Higher confidence for same need type, replace it
            unique[existingIndex] = need;
          }
          
          return unique;
        }, [] as UserNeed[]);
      
      // Limit to max predictions per user
      const limitedNeeds = filteredNeeds
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, this.proactiveConfig.needsPrediction.maxPredictionsPerUser);
      
      // Store user needs
      this.userNeeds.set(userId, limitedNeeds);
      
      return limitedNeeds;
    } catch (error) {
      this.logger.error(`Error predicting user needs for ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Predict needs from user patterns
   * @param userId User ID
   * @returns Predicted needs from patterns
   */
  private async predictNeedsFromPatterns(userId: string): Promise<UserNeed[]> {
    try {
      // In a real implementation, this would analyze user behavior patterns
      // For now, we'll return mock data
      
      // Get user history
      const userHistory = await this.getUserHistory(userId);
      
      const needs: UserNeed[] = [];
      
      // Check for regular activity patterns
      if (userHistory.some(h => h.item.categories.includes('yoga'))) {
        needs.push({
          id: uuidv4(),
          userId,
          type: 'activity',
          description: 'Regular yoga practice',
          confidence: 0.85,
          context: {
            category: 'yoga',
            frequency: 'weekly',
          },
          timestamp: new Date(),
        });
      }
      
      // Check for wellness interests
      if (userHistory.some(h => h.item.categories.includes('meditation'))) {
        needs.push({
          id: uuidv4(),
          userId,
          type: 'wellness',
          description: 'Stress reduction techniques',
          confidence: 0.75,
          context: {
            category: 'meditation',
            focus: 'stress-reduction',
          },
          timestamp: new Date(),
        });
      }
      
      return needs;
    } catch (error) {
      this.logger.error(`Error predicting needs from patterns for ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Predict needs from user context
   * @param userId User ID
   * @param contextData User context data
   * @returns Predicted needs from context
   */
  private async predictNeedsFromContext(userId: string, contextData: any): Promise<UserNeed[]> {
    try {
      const needs: UserNeed[] = [];
      
      // Check weather context
      if (contextData.values.WEATHER) {
        const weather = contextData.values.WEATHER;
        
        if (weather.condition === 'rainy' || weather.condition === 'snowy') {
          needs.push({
            id: uuidv4(),
            userId,
            type: 'activity',
            description: 'Indoor wellness activities',
            confidence: 0.8,
            context: {
              weather: weather.condition,
              indoor: true,
            },
            timestamp: new Date(),
          });
        }
      }
      
      // Check mood context
      if (contextData.values.MOOD) {
        const mood = contextData.values.MOOD;
        
        if (mood.mood === 'stressed') {
          needs.push({
            id: uuidv4(),
            userId,
            type: 'wellness',
            description: 'Stress relief activities',
            confidence: 0.9,
            context: {
              mood: 'stressed',
              focus: 'relaxation',
            },
            timestamp: new Date(),
          });
        } else if (mood.mood === 'tired') {
          needs.push({
            id: uuidv4(),
            userId,
            type: 'wellness',
            description: 'Energy boosting activities',
            confidence: 0.85,
            context: {
              mood: 'tired',
              focus: 'energy',
            },
            timestamp: new Date(),
          });
        }
      }
      
      return needs;
    } catch (error) {
      this.logger.error(`Error predicting needs from context for ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Predict needs from user schedule
   * @param userId User ID
   * @param contextData User context data
   * @returns Predicted needs from schedule
   */
  private async predictNeedsFromSchedule(userId: string, contextData: any): Promise<UserNeed[]> {
    try {
      const needs: UserNeed[] = [];
      
      // Check calendar context
      if (contextData.values.CALENDAR) {
        const calendar = contextData.values.CALENDAR;
        
        // Check for busy schedule
        if (calendar.busyHours > 6) {
          needs.push({
            id: uuidv4(),
            userId,
            type: 'wellness',
            description: 'Quick wellness breaks',
            confidence: 0.8,
            context: {
              busySchedule: true,
              duration: 'short',
            },
            timestamp: new Date(),
          });
        }
        
        // Check for upcoming events
        if (calendar.events && calendar.events.length > 0) {
          const hasTravel = calendar.events.some(e => e.type === 'travel');
          
          if (hasTravel) {
            needs.push({
              id: uuidv4(),
              userId,
              type: 'wellness',
              description: 'Travel wellness tips',
              confidence: 0.85,
              context: {
                upcomingTravel: true,
              },
              timestamp: new Date(),
            });
          }
        }
      }
      
      return needs;
    } catch (error) {
      this.logger.error(`Error predicting needs from schedule for ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Get recommendations for a user need
   * @param userId User ID
   * @param need User need
   * @returns Recommendations for the need
   */
  private async getRecommendationsForNeed(userId: string, need: UserNeed): Promise<ProactiveRecommendation[]> {
    try {
      // Get recommendations based on need
      const items = await this.recommendationService.getRecommendations(userId, 3, {
        filters: need.context,
      });
      
      if (items.length === 0) {
        return [];
      }
      
      // Convert to proactive recommendations
      return items.map(item => {
        // Determine priority based on need confidence
        let priority = ProactiveRecommendationPriority.LOW;
        if (need.confidence > 0.9) {
          priority = ProactiveRecommendationPriority.HIGH;
        } else if (need.confidence > 0.8) {
          priority = ProactiveRecommendationPriority.MEDIUM;
        }
        
        return {
          id: uuidv4(),
          userId,
          itemId: item.id,
          needId: need.id,
          priority,
          trigger: ProactiveRecommendationTrigger.NEED_PREDICTION,
          explanation: `Based on your ${need.type}: ${need.description}`,
          timestamp: new Date(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week
          item,
        };
      });
    } catch (error) {
      this.logger.error(`Error getting recommendations for need ${need.id}: ${error.message}`);
      return [];
    }
  }

  /**
   * Store pending recommendations for a user
   * @param userId User ID
   * @param recommendations Recommendations to store
   */
  private storePendingRecommendations(userId: string, recommendations: ProactiveRecommendation[]): void {
    try {
      // Get existing recommendations
      const existing = this.pendingRecommendations.get(userId) || [];
      
      // Add new recommendations, avoiding duplicates
      const combined = [...existing];
      
      for (const rec of recommendations) {
        // Check if we already have this item recommended
        const existingIndex = combined.findIndex(r => r.itemId === rec.itemId);
        
        if (existingIndex === -1) {
          // New recommendation, add it
          combined.push(rec);
        } else if (rec.priority > combined[existingIndex].priority) {
          // Higher priority for same item, replace it
          combined[existingIndex] = rec;
        }
      }
      
      // Remove expired recommendations
      const now = new Date();
      const valid = combined.filter(rec => rec.expiresAt > now);
      
      // Store updated list
      this.pendingRecommendations.set(userId, valid);
      
      this.logger.debug(`Stored ${valid.length} pending recommendations for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error storing pending recommendations for ${userId}: ${error.message}`);
    }
  }

  /**
   * Check and send notifications for pending recommendations
   * @param userId User ID
   */
  private async checkAndSendNotifications(userId: string): Promise<void> {
    try {
      if (!this.proactiveConfig.notifications.enabled) {
        return;
      }

      // Check notification limits
      if (!this.canSendNotification(userId)) {
        return;
      }
      
      // Get pending recommendations
      const pending = this.pendingRecommendations.get(userId) || [];
      
      // Sort by priority (highest first)
      const sorted = [...pending].sort((a, b) => b.priority - a.priority);
      
      // Take top recommendation
      const topRec = sorted[0];
      
      if (!topRec) {
        return;
      }
      
      // Send notification
      await this.sendNotification(userId, topRec);
      
      // Update notification history
      this.updateNotificationHistory(userId);
    } catch (error) {
      this.logger.error(`Error checking notifications for ${userId}: ${error.message}`);
    }
  }

  /**
   * Check if we can send a notification to a user
   * @param userId User ID
   * @returns Whether we can send a notification
   */
  private canSendNotification(userId: string): boolean {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      // Check quiet hours
      if (this.proactiveConfig.notifications.quietHours.enabled) {
        const hour = now.getHours();
        const { start, end } = this.proactiveConfig.notifications.quietHours;
        
        if (hour >= start || hour < end) {
          return false;
        }
      }
      
      // Check notification history
      const history = this.notificationHistory.get(userId);
      
      if (!history) {
        return true;
      }
      
      // Check daily limit
      if (history.timestamp.getTime() >= today.getTime() && 
          history.count >= this.proactiveConfig.notifications.maxPerDay) {
        return false;
      }
      
      // Check cooldown
      if (this.proactiveConfig.notifications.cooldown.enabled) {
        const cooldownMs = this.proactiveConfig.notifications.cooldown.minutes * 60 * 1000;
        const timeSinceLastMs = now.getTime() - history.timestamp.getTime();
        
        if (timeSinceLastMs < cooldownMs) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Error checking notification limits for ${userId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Send notification for a recommendation
   * @param userId User ID
   * @param recommendation Recommendation to notify about
   */
  private async sendNotification(userId: string, recommendation: ProactiveRecommendation): Promise<void> {
    try {
      // In a real implementation, this would send actual notifications
      // For now, we'll just log it
      
      this.logger.log(`Sending notification to user ${userId} for item ${recommendation.itemId}`);
      
      // Generate notification content
      const title = `Recommended for you: ${recommendation.item.title}`;
      const body = recommendation.explanation;
      
      // Emit notification event
      this.eventEmitter.emit('proactive.notification.sent', {
        userId,
        recommendation,
        notification: {
          title,
          body,
          timestamp: new Date(),
        },
      });
      
      // Mark as notified
      recommendation.notified = true;
      recommendation.notifiedAt = new Date();
    } catch (error) {
      this.logger.error(`Error sending notification for ${userId}: ${error.message}`);
    }
  }

  /**
   * Update notification history for a user
   * @param userId User ID
   */
  private updateNotificationHistory(userId: string): void {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      // Get existing history
      const history = this.notificationHistory.get(userId);
      
      if (!history || history.timestamp.getTime() < today.getTime()) {
        // New day, reset count
        this.notificationHistory.set(userId, {
          timestamp: now,
          count: 1,
        });
      } else {
        // Update existing count
        history.timestamp = now;
        history.count++;
      }
    } catch (error) {
      this.logger.error(`Error updating notification history for ${userId}: ${error.message}`);
    }
  }

  /**
   * Get user history
   * @param userId User ID
   * @returns User history
   */
  private async getUserHistory(userId: string): Promise<any[]> {
    try {
      // In a real implementation, this would fetch user history from the database
      // For now, we'll return mock data
      return [
        {
          id: '1',
          userId,
          itemId: '101',
          interactionType: 'VIEW',
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          item: {
            id: '101',
            title: 'Morning Yoga Session',
            categories: ['yoga', 'morning'],
          },
        },
        {
          id: '2',
          userId,
          itemId: '102',
          interactionType: 'BOOKMARK',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          item: {
            id: '102',
            title: 'Meditation for Beginners',
            categories: ['meditation', 'beginner'],
          },
        },
        {
          id: '3',
          userId,
          itemId: '103',
          interactionType: 'PURCHASE',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          item: {
            id: '103',
            title: 'Stress Relief Workshop',
            categories: ['workshop', 'stress-relief'],
          },
        },
      ];
    } catch (error) {
      this.logger.error(`Error getting user history for ${userId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Scheduled proactive recommendation generation
   */
  @Cron('0 */4 * * *') // Every 4 hours
  async scheduledRecommendationGeneration(): Promise<void> {
    try {
      this.logger.log('Running scheduled proactive recommendation generation');
      
      // In a real implementation, this would get active users from the database
      // For now, we'll use a mock list
      const activeUserIds = ['user1', 'user2', 'user3'];
      
      // Generate recommendations for each user
      for (const userId of activeUserIds) {
        await this.generateProactiveRecommendations(userId, ProactiveRecommendationTrigger.SCHEDULED);
      }
      
      this.logger.log('Scheduled proactive recommendation generation completed');
    } catch (error) {
      this.logger.error(`Error in scheduled recommendation generation: ${error.message}`);
    }
  }

  /**
   * Scheduled notification check
   */
  @Cron('0 */2 * * *') // Every 2 hours
  async scheduledNotificationCheck(): Promise<void> {
    try {
      this.logger.log('Running scheduled notification check');
      
      // Get users with pending recommendations
      const userIds = Array.from(this.pendingRecommendations.keys());
      
      // Check notifications for each user
      for (const userId of userIds) {
        await this.checkAndSendNotifications(userId);
      }
      
      this.logger.log('Scheduled notification check completed');
    } catch (error) {
      this.logger.error(`Error in scheduled notification check: ${error.message}`);
    }
  }

  /**
   * Clean up expired recommendations
   */
  @Cron('0 0 * * *') // Every day at midnight
  private cleanupExpiredRecommendations(): void {
    try {
      const now = new Date();
      let totalRemoved = 0;
      
      // Check each user's recommendations
      for (const [userId, recommendations] of this.pendingRecommendations.entries()) {
        const valid = recommendations.filter(rec => rec.expiresAt > now);
        const removed = recommendations.length - valid.length;
        
        if (removed > 0) {
          this.pendingRecommendations.set(userId, valid);
          totalRemoved += removed;
        }
      }
      
      this.logger.log(`Cleaned up ${totalRemoved} expired recommendations`);
    } catch (error) {
      this.logger.error(`Error cleaning up expired recommendations: ${error.message}`);
    }
  }
}
