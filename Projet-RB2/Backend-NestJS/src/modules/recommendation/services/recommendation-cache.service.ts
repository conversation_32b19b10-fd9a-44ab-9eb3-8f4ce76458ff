import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../../../cache/cache.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { RecommendationResponseDto } from '../dto/recommendation-response.dto';

/**
 * Service de cache pour le système de recommandation
 */
@Injectable()
export class RecommendationCacheService {
  private readonly logger = new Logger(RecommendationCacheService.name);
  private readonly enabled: boolean;
  private readonly ttl: {
    recommendations: number;
    trending: number;
    similar: number;
    itemDetails: number;
  };

  constructor(
    private readonly cacheService: CacheService,
    private readonly configService: ConfigService,
  ) {
    this.enabled = this.configService.get<boolean>('recommendation.cache.enabled', true);

    this.ttl = {
      recommendations: this.configService.get<number>('recommendation.cache.ttl.recommendations', 30 * 60), // 30 minutes
      trending: this.configService.get<number>('recommendation.cache.ttl.trending', 60 * 60), // 1 heure
      similar: this.configService.get<number>('recommendation.cache.ttl.similar', 2 * 60 * 60), // 2 heures
      itemDetails: this.configService.get<number>('recommendation.cache.ttl.itemDetails', 24 * 60 * 60), // 24 heures
    };

    this.logger.log(`RecommendationCacheService initialized with enabled=${this.enabled}`);
    if (this.enabled) {
      this.logger.log(`Cache TTLs: recommendations=${this.ttl.recommendations}s, trending=${this.ttl.trending}s, similar=${this.ttl.similar}s, itemDetails=${this.ttl.itemDetails}s`);
    }
  }

  /**
   * Génère une clé de cache pour les recommandations personnalisées
   * @param userId ID de l'utilisateur
   * @param type Type d'élément
   * @param options Options de recommandation
   * @returns Clé de cache
   */
  private generateRecommendationsKey(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions,
  ): string {
    const { strategy, hybridMethod, limit, page, filters, excludeIds, sortBy, sortOrder } = options;

    // Créer un objet avec les options pertinentes pour la clé de cache
    const cacheKeyOptions = {
      strategy: strategy || 'default',
      hybridMethod: hybridMethod || 'default',
      limit: limit || 10,
      page: page || 1,
      filters: filters || {},
      excludeIds: excludeIds || [],
      sortBy: sortBy || 'score',
      sortOrder: sortOrder || 'desc',
    };

    // Générer une clé de cache basée sur les options
    return `recommendations:user:${userId}:type:${type}:${JSON.stringify(cacheKeyOptions)}`;
  }

  /**
   * Génère une clé de cache pour les recommandations tendance
   * @param type Type d'élément
   * @param options Options de recommandation
   * @returns Clé de cache
   */
  private generateTrendingKey(
    type: RecommendationType,
    options: RecommendationOptions,
  ): string {
    const { limit, page, filters, excludeIds, sortBy, sortOrder } = options;

    // Créer un objet avec les options pertinentes pour la clé de cache
    const cacheKeyOptions = {
      limit: limit || 10,
      page: page || 1,
      filters: filters || {},
      excludeIds: excludeIds || [],
      sortBy: sortBy || 'score',
      sortOrder: sortOrder || 'desc',
    };

    // Générer une clé de cache basée sur les options
    return `recommendations:trending:type:${type}:${JSON.stringify(cacheKeyOptions)}`;
  }

  /**
   * Génère une clé de cache pour les éléments similaires
   * @param itemId ID de l'élément de référence
   * @param type Type d'élément
   * @param options Options de recommandation
   * @returns Clé de cache
   */
  private generateSimilarKey(
    itemId: string,
    type: RecommendationType,
    options: RecommendationOptions,
  ): string {
    const { limit, page, filters, excludeIds, sortBy, sortOrder } = options;

    // Créer un objet avec les options pertinentes pour la clé de cache
    const cacheKeyOptions = {
      limit: limit || 10,
      page: page || 1,
      filters: filters || {},
      excludeIds: excludeIds || [],
      sortBy: sortBy || 'score',
      sortOrder: sortOrder || 'desc',
    };

    // Générer une clé de cache basée sur les options
    return `recommendations:similar:item:${itemId}:type:${type}:${JSON.stringify(cacheKeyOptions)}`;
  }

  /**
   * Génère une clé de cache pour les détails d'un élément
   * @param type Type d'élément
   * @param itemId ID de l'élément
   * @returns Clé de cache
   */
  private generateItemDetailsKey(
    type: RecommendationType,
    itemId: string,
  ): string {
    return `item:${type}:${itemId}`;
  }

  /**
   * Récupère des recommandations personnalisées du cache ou les génère et les met en cache
   * @param userId ID de l'utilisateur
   * @param type Type d'élément
   * @param options Options de recommandation
   * @param generator Fonction pour générer les recommandations en cas de cache miss
   * @returns Recommandations personnalisées
   */
  async getCachedRecommendations(
    userId: string,
    type: RecommendationType,
    options: RecommendationOptions,
    generator: () => Promise<{
      items: RecommendationResponseDto[];
      total: number;
      page: number;
      limit: number;
    }>,
  ): Promise<{
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    if (!this.enabled) {
      return generator();
    }

    const cacheKey = this.generateRecommendationsKey(userId, type, options);

    // Utiliser getOrSet avec un TTL spécifique pour les recommandations
    const result = await this.cacheService.getOrSet(
      cacheKey,
      generator,
      this.ttl.recommendations,
    );

    // Enregistrer des métriques de cache (hit/miss)
    this.logger.debug(`Cache ${result ? 'hit' : 'miss'} pour les recommandations de l'utilisateur ${userId}`);

    return result;
  }

  /**
   * Récupère des recommandations personnalisées en batch pour plusieurs utilisateurs
   * @param userIds Liste des IDs d'utilisateurs
   * @param type Type d'élément
   * @param options Options de recommandation
   * @returns Map des recommandations par utilisateur
   */
  async getCachedRecommendationsBatch(
    userIds: string[],
    type: RecommendationType,
    options: RecommendationOptions,
  ): Promise<Map<string, {
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }>> {
    if (!this.enabled || userIds.length === 0) {
      return new Map();
    }

    // Générer les clés de cache pour chaque utilisateur
    const cacheKeys = userIds.map(userId => ({
      userId,
      key: this.generateRecommendationsKey(userId, type, options),
    }));

    // Récupérer toutes les valeurs en cache en une seule opération
    const cacheKeysArray = cacheKeys.map(item => item.key);
    const cachedValues = await this.cacheService.getMany<{
      items: RecommendationResponseDto[];
      total: number;
      page: number;
      limit: number;
    }>(cacheKeysArray);

    // Construire le résultat
    const result = new Map<string, {
      items: RecommendationResponseDto[];
      total: number;
      page: number;
      limit: number;
    }>();

    for (const { userId, key } of cacheKeys) {
      const cachedValue = cachedValues.get(key);
      if (cachedValue) {
        result.set(userId, cachedValue);
      }
    }

    return result;
  }

  /**
   * Récupère des recommandations tendance du cache ou les génère et les met en cache
   * @param type Type d'élément
   * @param options Options de recommandation
   * @param generator Fonction pour générer les recommandations en cas de cache miss
   * @returns Recommandations tendance
   */
  async getCachedTrendingRecommendations(
    type: RecommendationType,
    options: RecommendationOptions,
    generator: () => Promise<{
      items: RecommendationResponseDto[];
      total: number;
      page: number;
      limit: number;
    }>,
  ): Promise<{
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    if (!this.enabled) {
      return generator();
    }

    const cacheKey = this.generateTrendingKey(type, options);

    return this.cacheService.getOrSet(
      cacheKey,
      generator,
      this.ttl.trending,
    );
  }

  /**
   * Récupère des éléments similaires du cache ou les génère et les met en cache
   * @param itemId ID de l'élément de référence
   * @param type Type d'élément
   * @param options Options de recommandation
   * @param generator Fonction pour générer les éléments similaires en cas de cache miss
   * @returns Éléments similaires
   */
  async getCachedSimilarItems(
    itemId: string,
    type: RecommendationType,
    options: RecommendationOptions,
    generator: () => Promise<{
      items: RecommendationResponseDto[];
      total: number;
      page: number;
      limit: number;
    }>,
  ): Promise<{
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    if (!this.enabled) {
      return generator();
    }

    const cacheKey = this.generateSimilarKey(itemId, type, options);

    return this.cacheService.getOrSet(
      cacheKey,
      generator,
      this.ttl.similar,
    );
  }

  /**
   * Récupère les détails d'un élément du cache ou les génère et les met en cache
   * @param type Type d'élément
   * @param itemId ID de l'élément
   * @param generator Fonction pour générer les détails en cas de cache miss
   * @returns Détails de l'élément
   */
  async getCachedItemDetails<T>(
    type: RecommendationType,
    itemId: string,
    generator: () => Promise<T>,
  ): Promise<T> {
    if (!this.enabled) {
      return generator();
    }

    const cacheKey = this.generateItemDetailsKey(type, itemId);

    return this.cacheService.getOrSet(
      cacheKey,
      generator,
      this.ttl.itemDetails,
    );
  }

  /**
   * Invalide le cache pour un utilisateur spécifique
   * @param userId ID de l'utilisateur
   */
  async invalidateUserCache(userId: string): Promise<void> {
    if (!this.enabled) {
      return;
    }

    const pattern = `recommendations:user:${userId}:*`;
    await this.cacheService.deleteByPattern(pattern);
    this.logger.debug(`Invalidated cache for user ${userId}`);
  }

  /**
   * Invalide le cache pour un élément spécifique
   * @param type Type d'élément
   * @param itemId ID de l'élément
   */
  async invalidateItemCache(type: RecommendationType, itemId: string): Promise<void> {
    if (!this.enabled) {
      return;
    }

    // Invalider les détails de l'élément
    const itemKey = this.generateItemDetailsKey(type, itemId);
    await this.cacheService.delete(itemKey);

    // Invalider les recommandations similaires
    const similarPattern = `recommendations:similar:item:${itemId}:*`;
    await this.cacheService.deleteByPattern(similarPattern);

    this.logger.debug(`Invalidated cache for item ${itemId} of type ${type}`);
  }
}
