import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { ContentBasedService } from './content-based.service';
import { CollaborativeFilteringService } from './collaborative-filtering.service';
import { HybridRecommendationService } from './hybrid-recommendation.service';
import { PersonalizationService } from './personalization.service';
import { AgentRbIntegrationService } from './agent-rb-integration.service.optimized';
import { RecommendationCacheService } from './recommendation-cache.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';
import { RecordInteractionDto } from '../dto/record-interaction.dto';
import { UpdatePreferencesDto } from '../dto/update-preferences.dto';
import { RecommendationResponseDto } from '../dto/recommendation-response.dto';
import { PaginatedResponseDto } from '../../../common/dto/paginated-response.dto';

/**
 * Service principal de recommandation optimisé
 */
@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);
  private readonly defaultStrategy: RecommendationStrategy;
  private readonly defaultHybridMethod: string;
  private readonly enablePreloading: boolean;
  private readonly preloadingInterval: number;
  private preloadingTimer: NodeJS.Timeout | null = null;

  constructor(
    private readonly prisma: PrismaService,
    private readonly contentBasedService: ContentBasedService,
    private readonly collaborativeFilteringService: CollaborativeFilteringService,
    private readonly hybridRecommendationService: HybridRecommendationService,
    private readonly personalizationService: PersonalizationService,
    private readonly agentRbIntegration: AgentRbIntegrationService,
    private readonly cacheService: RecommendationCacheService,
    private readonly configService: ConfigService,
  ) {
    // Récupérer la configuration
    this.defaultStrategy = this.configService.get<RecommendationStrategy>(
      'recommendation.strategies.default',
      RecommendationStrategy.HYBRID,
    );
    
    this.defaultHybridMethod = this.configService.get<string>(
      'recommendation.strategies.defaultHybridMethod',
      'WEIGHTED',
    );
    
    this.enablePreloading = this.configService.get<boolean>(
      'recommendation.performance.enablePreloading',
      true,
    );
    
    this.preloadingInterval = this.configService.get<number>(
      'recommendation.performance.preloadingInterval',
      3600,
    ) * 1000; // Convertir en millisecondes
    
    this.logger.log(`RecommendationService initialized with defaultStrategy=${this.defaultStrategy}`);
    
    // Démarrer le préchargement si activé
    if (this.enablePreloading) {
      this.startPreloading();
    }
  }

  /**
   * Récupère des recommandations personnalisées pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type d'élément à recommander
   * @param options Options de recommandation
   * @returns Recommandations personnalisées
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType = RecommendationType.RETREAT,
    options: RecommendationOptions = {},
  ): Promise<{
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.logger.log(`Getting recommendations for user ${userId}, type ${type}`);
    
    // Utiliser le cache pour les recommandations
    return this.cacheService.getCachedRecommendations(
      userId,
      type,
      options,
      async () => {
        try {
          // Définir les valeurs par défaut
          const limit = options.limit || 10;
          const page = options.page || 1;
          const strategy = options.strategy || this.defaultStrategy;
          const includeMetadata = options.includeMetadata !== false;
          
          // Calculer l'offset pour la pagination
          const offset = (page - 1) * limit;
          
          // Obtenir les recommandations brutes selon la stratégie
          let rawRecommendations: any[];
          let totalCount: number;
          
          switch (strategy) {
            case RecommendationStrategy.CONTENT_BASED:
              rawRecommendations = await this.contentBasedService.getRecommendations(userId, type, options);
              totalCount = await this.contentBasedService.countRecommendations(userId, type, options);
              break;
              
            case RecommendationStrategy.COLLABORATIVE:
              rawRecommendations = await this.collaborativeFilteringService.getRecommendations(userId, type, options);
              totalCount = await this.collaborativeFilteringService.countRecommendations(userId, type, options);
              break;
              
            case RecommendationStrategy.HYBRID:
            default:
              rawRecommendations = await this.hybridRecommendationService.getRecommendations(userId, type, options);
              totalCount = await this.hybridRecommendationService.countRecommendations(userId, type, options);
              break;
          }
          
          // Appliquer la pagination
          const paginatedRecommendations = rawRecommendations.slice(offset, offset + limit);
          
          // Enrichir les recommandations avec les données d'Agent-RB
          const enrichedRecommendations = await this.agentRbIntegration.enrichRecommendations(
            paginatedRecommendations,
            type,
            includeMetadata,
          );
          
          // Enregistrer cette interaction de recommandation
          await this.recordRecommendationView(userId, enrichedRecommendations, type);
          
          return {
            items: enrichedRecommendations,
            total: totalCount,
            page,
            limit,
          };
        } catch (error) {
          this.logger.error(`Error getting recommendations: ${error.message}`);
          throw error;
        }
      }
    );
  }

  /**
   * Récupère des recommandations tendance
   * @param type Type d'élément à recommander
   * @param options Options de recommandation
   * @returns Recommandations tendance
   */
  async getTrendingRecommendations(
    type: RecommendationType = RecommendationType.RETREAT,
    options: RecommendationOptions = {},
  ): Promise<{
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.logger.log(`Getting trending recommendations for type ${type}`);
    
    // Utiliser le cache pour les recommandations tendance
    return this.cacheService.getCachedTrendingRecommendations(
      type,
      options,
      async () => {
        try {
          // Définir les valeurs par défaut
          const limit = options.limit || 10;
          const page = options.page || 1;
          const includeMetadata = options.includeMetadata !== false;
          
          // Calculer l'offset pour la pagination
          const offset = (page - 1) * limit;
          
          // Obtenir les recommandations tendance
          const rawRecommendations = await this.getTrendingItems(type, options);
          const totalCount = rawRecommendations.length;
          
          // Appliquer la pagination
          const paginatedRecommendations = rawRecommendations.slice(offset, offset + limit);
          
          // Enrichir les recommandations avec les données d'Agent-RB
          const enrichedRecommendations = await this.agentRbIntegration.enrichRecommendations(
            paginatedRecommendations,
            type,
            includeMetadata,
          );
          
          return {
            items: enrichedRecommendations,
            total: totalCount,
            page,
            limit,
          };
        } catch (error) {
          this.logger.error(`Error getting trending recommendations: ${error.message}`);
          throw error;
        }
      }
    );
  }

  /**
   * Récupère des éléments similaires à un élément spécifié
   * @param itemId ID de l'élément de référence
   * @param type Type d'élément
   * @param options Options de recommandation
   * @returns Éléments similaires
   */
  async getSimilarItems(
    itemId: string,
    type: RecommendationType = RecommendationType.RETREAT,
    options: RecommendationOptions = {},
  ): Promise<{
    items: RecommendationResponseDto[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.logger.log(`Getting similar items for ${type} ${itemId}`);
    
    // Utiliser le cache pour les éléments similaires
    return this.cacheService.getCachedSimilarItems(
      itemId,
      type,
      options,
      async () => {
        try {
          // Vérifier que l'élément existe
          try {
            await this.agentRbIntegration.enrichRecommendation({ id: itemId }, type, false);
          } catch (error) {
            if (error instanceof NotFoundException) {
              throw error;
            }
            // Si l'erreur n'est pas une NotFoundException, on continue
            this.logger.warn(`Could not verify item existence: ${error.message}`);
          }
          
          // Définir les valeurs par défaut
          const limit = options.limit || 10;
          const page = options.page || 1;
          const includeMetadata = options.includeMetadata !== false;
          
          // Calculer l'offset pour la pagination
          const offset = (page - 1) * limit;
          
          // Obtenir les éléments similaires
          const rawSimilarItems = await this.contentBasedService.getSimilarItems(itemId, type, options);
          const totalCount = await this.contentBasedService.countSimilarItems(itemId, type, options);
          
          // Appliquer la pagination
          const paginatedSimilarItems = rawSimilarItems.slice(offset, offset + limit);
          
          // Enrichir les recommandations avec les données d'Agent-RB
          const enrichedSimilarItems = await this.agentRbIntegration.enrichRecommendations(
            paginatedSimilarItems,
            type,
            includeMetadata,
          );
          
          return {
            items: enrichedSimilarItems,
            total: totalCount,
            page,
            limit,
          };
        } catch (error) {
          this.logger.error(`Error getting similar items: ${error.message}`);
          throw error;
        }
      }
    );
  }

  /**
   * Enregistre une interaction utilisateur avec un élément
   * @param userId ID de l'utilisateur
   * @param interactionDto Données de l'interaction
   */
  async recordInteraction(
    userId: string,
    interactionDto: RecordInteractionDto,
  ): Promise<void> {
    this.logger.log(`Recording interaction for user ${userId} with ${interactionDto.type} ${interactionDto.itemId}`);
    
    try {
      // Vérifier que l'élément existe
      try {
        await this.agentRbIntegration.enrichRecommendation(
          { id: interactionDto.itemId },
          interactionDto.type,
          false,
        );
      } catch (error) {
        if (error instanceof NotFoundException) {
          throw error;
        }
        // Si l'erreur n'est pas une NotFoundException, on continue
        this.logger.warn(`Could not verify item existence: ${error.message}`);
      }
      
      // Enregistrer l'interaction dans la base de données
      await this.prisma.userInteraction.create({
        data: {
          userId,
          itemId: interactionDto.itemId,
          itemType: interactionDto.type,
          interactionType: interactionDto.interactionType,
          metadata: interactionDto.metadata || {},
        },
      });
      
      // Mettre à jour le modèle de recommandation si nécessaire
      await this.personalizationService.updateUserModel(userId);
      
      // Invalider le cache des recommandations pour cet utilisateur
      await this.cacheService.invalidateUserCache(userId);
    } catch (error) {
      this.logger.error(`Error recording interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour les préférences de recommandation de l'utilisateur
   * @param userId ID de l'utilisateur
   * @param preferencesDto Nouvelles préférences
   * @returns Préférences mises à jour
   */
  async updatePreferences(
    userId: string,
    preferencesDto: UpdatePreferencesDto,
  ): Promise<Record<string, any>> {
    this.logger.log(`Updating preferences for user ${userId}`);
    
    try {
      // Récupérer les préférences actuelles
      let userPreferences = await this.prisma.userPreferences.findUnique({
        where: { userId },
      });
      
      // Créer ou mettre à jour les préférences
      if (userPreferences) {
        userPreferences = await this.prisma.userPreferences.update({
          where: { userId },
          data: {
            preferences: {
              ...userPreferences.preferences,
              ...preferencesDto,
            },
          },
        });
      } else {
        userPreferences = await this.prisma.userPreferences.create({
          data: {
            userId,
            preferences: preferencesDto,
          },
        });
      }
      
      // Mettre à jour le modèle de recommandation
      await this.personalizationService.updateUserModel(userId);
      
      // Invalider le cache des recommandations pour cet utilisateur
      await this.cacheService.invalidateUserCache(userId);
      
      return userPreferences.preferences;
    } catch (error) {
      this.logger.error(`Error updating preferences: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère les éléments tendance
   * @param type Type d'élément
   * @param options Options de recommandation
   * @returns Éléments tendance
   */
  private async getTrendingItems(
    type: RecommendationType,
    options: RecommendationOptions,
  ): Promise<any[]> {
    // Récupérer les interactions récentes pour ce type d'élément
    const recentInteractions = await this.prisma.userInteraction.findMany({
      where: {
        itemType: type,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 derniers jours
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    
    // Compter les interactions par élément
    const interactionCounts: Record<string, number> = {};
    recentInteractions.forEach(interaction => {
      interactionCounts[interaction.itemId] = (interactionCounts[interaction.itemId] || 0) + 1;
    });
    
    // Trier les éléments par nombre d'interactions
    const sortedItems = Object.entries(interactionCounts)
      .sort(([, countA], [, countB]) => countB - countA)
      .map(([itemId, count]) => ({
        id: itemId,
        score: count / Math.max(...Object.values(interactionCounts)),
        sources: ['trending'],
        reasons: ['Populaire en ce moment'],
      }));
    
    // Appliquer les filtres si nécessaire
    let filteredItems = sortedItems;
    if (options.excludeIds && options.excludeIds.length > 0) {
      filteredItems = filteredItems.filter(item => !options.excludeIds.includes(item.id));
    }
    
    return filteredItems;
  }

  /**
   * Enregistre une interaction de vue de recommandation
   * @param userId ID de l'utilisateur
   * @param recommendations Recommandations vues
   * @param type Type d'élément
   */
  private async recordRecommendationView(
    userId: string,
    recommendations: RecommendationResponseDto[],
    type: RecommendationType,
  ): Promise<void> {
    try {
      // Enregistrer une interaction de type "RECOMMENDATION_VIEW" pour chaque élément
      const interactionPromises = recommendations.map(recommendation =>
        this.prisma.userInteraction.create({
          data: {
            userId,
            itemId: recommendation.id,
            itemType: type,
            interactionType: 'RECOMMENDATION_VIEW',
            metadata: {
              score: recommendation.score,
              sources: recommendation.sources,
              position: recommendations.indexOf(recommendation),
            },
          },
        })
      );
      
      await Promise.all(interactionPromises);
    } catch (error) {
      // Ne pas bloquer le flux principal en cas d'erreur
      this.logger.error(`Error recording recommendation view: ${error.message}`);
    }
  }

  /**
   * Démarre le préchargement périodique des éléments populaires
   */
  private startPreloading(): void {
    this.logger.log(`Starting preloading with interval ${this.preloadingInterval / 1000} seconds`);
    
    // Précharger immédiatement
    this.preloadPopularItems();
    
    // Configurer le préchargement périodique
    this.preloadingTimer = setInterval(() => {
      this.preloadPopularItems();
    }, this.preloadingInterval);
  }

  /**
   * Précharge les éléments populaires
   */
  private async preloadPopularItems(): Promise<void> {
    try {
      this.logger.log('Preloading popular items');
      
      // Récupérer les éléments populaires pour chaque type
      const popularRetreats = await this.getPopularItemIds(RecommendationType.RETREAT, 50);
      const popularPartners = await this.getPopularItemIds(RecommendationType.PARTNER, 30);
      const popularCourses = await this.getPopularItemIds(RecommendationType.COURSE, 30);
      
      // Précharger les éléments populaires
      await Promise.all([
        this.agentRbIntegration.preloadPopularItems(RecommendationType.RETREAT, popularRetreats),
        this.agentRbIntegration.preloadPopularItems(RecommendationType.PARTNER, popularPartners),
        this.agentRbIntegration.preloadPopularItems(RecommendationType.COURSE, popularCourses),
      ]);
      
      this.logger.log('Preloading completed');
    } catch (error) {
      this.logger.error(`Error preloading popular items: ${error.message}`);
    }
  }

  /**
   * Récupère les IDs des éléments populaires
   * @param type Type d'élément
   * @param limit Nombre maximum d'éléments
   * @returns IDs des éléments populaires
   */
  private async getPopularItemIds(type: RecommendationType, limit: number): Promise<string[]> {
    // Récupérer les interactions récentes pour ce type d'élément
    const recentInteractions = await this.prisma.userInteraction.findMany({
      where: {
        itemType: type,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 derniers jours
        },
      },
      select: {
        itemId: true,
      },
    });
    
    // Compter les interactions par élément
    const interactionCounts: Record<string, number> = {};
    recentInteractions.forEach(interaction => {
      interactionCounts[interaction.itemId] = (interactionCounts[interaction.itemId] || 0) + 1;
    });
    
    // Trier les éléments par nombre d'interactions et prendre les plus populaires
    return Object.entries(interactionCounts)
      .sort(([, countA], [, countB]) => countB - countA)
      .slice(0, limit)
      .map(([itemId]) => itemId);
  }
}
