import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ContentBasedService } from './content-based.service';
import { CollaborativeFilteringService } from './collaborative-filtering.service';
import { HybridRecommendationService } from './hybrid-recommendation.service';
import { PersonalizationService } from './personalization.service';
import { MatrixFactorizationService } from './matrix-factorization.service';
import { ContextualRecommendationService } from './contextual-recommendation.service';
import { DiversityFilterService } from './diversity-filter.service';
import { DeepLearningService } from './deep-learning.service';
import { ABTestingService } from './ab-testing.service';
import { ABTestGroup } from '../enums/ab-test-group.enum';
import { ExternalDataService } from './external-data.service';
import { RealtimeRecommendationService } from './realtime-recommendation.service';
import { ExplanationService, RecommendationExplanation } from './explanation.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';
import { RecommendationOptions } from '../interfaces/recommendation-options.interface';

/**
 * Service principal de recommandation
 * Coordonne les différentes stratégies de recommandation
 */
@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);
  private readonly defaultStrategy = RecommendationStrategy.HYBRID;

  constructor(
    private readonly prisma: PrismaService,
    private readonly contentBasedService: ContentBasedService,
    private readonly collaborativeFilteringService: CollaborativeFilteringService,
    private readonly hybridRecommendationService: HybridRecommendationService,
    private readonly personalizationService: PersonalizationService,
    private readonly matrixFactorizationService: MatrixFactorizationService,
    private readonly contextualRecommendationService: ContextualRecommendationService,
    private readonly diversityFilterService: DiversityFilterService,
    private readonly deepLearningService: DeepLearningService,
    private readonly abTestingService: ABTestingService,
    private readonly externalDataService: ExternalDataService,
    private readonly realtimeRecommendationService: RealtimeRecommendationService,
    private readonly explanationService: ExplanationService,
  ) {}

  /**
   * Génère des recommandations pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations
   */
  async getRecommendations(
    userId: string,
    type: RecommendationType = RecommendationType.COURSE,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations de type ${type} pour l'utilisateur ${userId}`);

      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.personalizationService.getUserPreferences(userId);

      // Déterminer la stratégie de recommandation
      let strategy = options.strategy;
      let testGroup = null;

      // Si aucune stratégie n'est spécifiée, vérifier si l'utilisateur fait partie d'un test A/B
      if (!strategy) {
        testGroup = await this.abTestingService.getTestGroup(userId);

        if (testGroup) {
          // Utiliser la stratégie correspondant au groupe de test
          strategy = this.abTestingService.getStrategyForGroup(testGroup);
          this.logger.log(`Utilisateur ${userId} dans le groupe de test ${testGroup}, utilisation de la stratégie ${strategy}`);
        } else {
          // Utiliser la stratégie par défaut ou celle des préférences utilisateur
          strategy = userPreferences.recommendationStrategy || this.defaultStrategy;
        }
      }

      // Générer les recommandations selon la stratégie
      let recommendations;
      switch (strategy) {
        case RecommendationStrategy.CONTENT_BASED:
          recommendations = await this.contentBasedService.getRecommendations(userId, type, options);
          break;
        case RecommendationStrategy.COLLABORATIVE:
          recommendations = await this.collaborativeFilteringService.getRecommendations(userId, type, options);
          break;
        case RecommendationStrategy.MATRIX_FACTORIZATION:
          recommendations = await this.matrixFactorizationService.getRecommendations(userId, type, options);
          break;
        case RecommendationStrategy.CONTEXTUAL:
          recommendations = await this.contextualRecommendationService.getRecommendations(userId, type, options);
          break;
        case RecommendationStrategy.KNOWLEDGE_BASED:
          // Utiliser le service basé sur le contenu comme fallback pour le moment
          recommendations = await this.contentBasedService.getRecommendations(userId, type, {
            ...options,
            filters: {
              ...(options.filters || {}),
              // Ajouter des filtres spécifiques basés sur les connaissances du domaine
              // Par exemple, filtrer par niveau d'expertise, par catégorie, etc.
            },
          });
          break;

        case RecommendationStrategy.DEEP_LEARNING:
          // Utiliser le service d'apprentissage profond
          recommendations = await this.deepLearningService.getRecommendations(userId, type, options);
          break;

        case RecommendationStrategy.REALTIME:
          // Utiliser le service de recommandations en temps réel
          recommendations = await this.realtimeRecommendationService.getRecommendations(userId, type, options);
          break;
        case RecommendationStrategy.HYBRID:
        default:
          recommendations = await this.hybridRecommendationService.getRecommendations(userId, type, options);
          break;
      }

      // Appliquer le filtre de diversité si activé
      if (options.diversification?.enabled !== false) {
        recommendations = this.diversityFilterService.applyDiversityFilter(recommendations, options);
      }

      // Enregistrer les recommandations générées
      await this.saveRecommendations(userId, type, recommendations);

      // Filtrer les recommandations selon les préférences de l'utilisateur
      const filteredRecommendations = await this.personalizationService.filterRecommendations(
        userId,
        recommendations,
        userPreferences,
      );

      // Si l'utilisateur fait partie d'un test A/B, enregistrer une interaction de type VIEW
      if (testGroup) {
        // Enregistrer une interaction pour chaque recommandation (max 10 pour éviter de surcharger)
        const maxTracking = Math.min(filteredRecommendations.length, 10);
        for (let i = 0; i < maxTracking; i++) {
          await this.abTestingService.trackInteraction(
            userId,
            filteredRecommendations[i].id,
            'VIEW',
            testGroup as ABTestGroup,
          );
        }
      }

      // Enrichir les recommandations avec des données externes si demandé
      let enrichedRecommendations = filteredRecommendations;
      if (options.includeExternalData !== false) {
        enrichedRecommendations = await this.externalDataService.enrichRecommendations(
          userId,
          filteredRecommendations
        );
      }

      // Ajouter des explications aux recommandations si demandé
      let explainedRecommendations = enrichedRecommendations;
      if (options.includeExplanations !== false) {
        explainedRecommendations = await this.addExplanationsToRecommendations(
          userId,
          enrichedRecommendations
        );
      }

      this.logger.log(`${explainedRecommendations.length} recommandations générées pour l'utilisateur ${userId}`);
      return explainedRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ajoute des explications aux recommandations
   * @param userId ID de l'utilisateur
   * @param recommendations Liste des recommandations
   * @returns Liste des recommandations avec explications
   */
  private async addExplanationsToRecommendations(
    userId: string,
    recommendations: any[],
  ): Promise<any[]> {
    try {
      // Générer les explications pour chaque recommandation
      const explainedRecommendations = await Promise.all(
        recommendations.map(async (recommendation) => {
          const explanation = await this.explanationService.generateExplanation(userId, recommendation);

          return {
            ...recommendation,
            explanation,
          };
        })
      );

      return explainedRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de l'ajout des explications: ${error.message}`);
      return recommendations;
    }
  }

  /**
   * Génère des recommandations similaires à un élément
   * @param itemId ID de l'élément
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations similaires
   */
  async getSimilarItems(
    itemId: string,
    type: RecommendationType = RecommendationType.COURSE,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération d'éléments similaires à ${itemId} de type ${type}`);

      // Utiliser le service de recommandation basé sur le contenu
      const similarItems = await this.contentBasedService.getSimilarItems(itemId, type, options);

      this.logger.log(`${similarItems.length} éléments similaires générés pour ${itemId}`);
      return similarItems;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération d'éléments similaires: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère des recommandations personnalisées pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param options Options de recommandation
   * @returns Liste des recommandations personnalisées
   */
  async getPersonalizedRecommendations(
    userId: string,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations personnalisées pour l'utilisateur ${userId}`);

      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.personalizationService.getUserPreferences(userId);

      // Générer des recommandations pour chaque type préféré
      const recommendations = [];

      for (const type of userPreferences.preferredTypes || [RecommendationType.COURSE]) {
        const typeRecommendations = await this.getRecommendations(userId, type, {
          ...options,
          limit: Math.floor((options.limit || 10) / (userPreferences.preferredTypes?.length || 1)),
        });

        recommendations.push(...typeRecommendations);
      }

      // Trier les recommandations par score
      const sortedRecommendations = recommendations.sort((a, b) => b.score - a.score);

      // Limiter le nombre de recommandations
      const limitedRecommendations = sortedRecommendations.slice(0, options.limit || 10);

      this.logger.log(`${limitedRecommendations.length} recommandations personnalisées générées pour l'utilisateur ${userId}`);
      return limitedRecommendations;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations personnalisées: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère des recommandations tendance
   * @param type Type de recommandation
   * @param options Options de recommandation
   * @returns Liste des recommandations tendance
   */
  async getTrendingRecommendations(
    type: RecommendationType = RecommendationType.COURSE,
    options: RecommendationOptions = {},
  ) {
    try {
      this.logger.log(`Génération de recommandations tendance de type ${type}`);

      // Récupérer les éléments les plus populaires
      let trendingItems;

      switch (type) {
        case RecommendationType.COURSE:
          trendingItems = await this.getTrendingCourses(options);
          break;
        case RecommendationType.RETREAT:
          trendingItems = await this.getTrendingRetreats(options);
          break;
        case RecommendationType.PARTNER:
          trendingItems = await this.getTrendingPartners(options);
          break;
        default:
          throw new Error(`Type de recommandation non pris en charge: ${type}`);
      }

      this.logger.log(`${trendingItems.length} recommandations tendance générées`);
      return trendingItems;
    } catch (error) {
      this.logger.error(`Erreur lors de la génération des recommandations tendance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Enregistre les interactions d'un utilisateur avec un élément
   * @param userId ID de l'utilisateur
   * @param itemId ID de l'élément
   * @param type Type de recommandation
   * @param interactionType Type d'interaction
   * @param metadata Métadonnées de l'interaction
   * @returns L'interaction enregistrée
   */
  async recordInteraction(
    userId: string,
    itemId: string,
    type: RecommendationType,
    interactionType: string,
    metadata: Record<string, any> = {},
  ) {
    try {
      this.logger.log(`Enregistrement d'une interaction ${interactionType} de l'utilisateur ${userId} avec l'élément ${itemId}`);

      // Enregistrer l'interaction
      const interaction = await this.prisma.userInteraction.create({
        data: {
          userId,
          itemId,
          itemType: type,
          interactionType,
          metadata,
        },
      });

      // Mettre à jour le modèle de recommandation
      await this.updateRecommendationModel(userId);

      this.logger.log(`Interaction enregistrée: ${interaction.id}`);
      return interaction;
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Met à jour le modèle de recommandation pour un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Résultat de la mise à jour
   */
  private async updateRecommendationModel(userId: string) {
    try {
      this.logger.log(`Mise à jour des modèles de recommandation pour l'utilisateur ${userId}`);

      // Mettre à jour tous les modèles de recommandation
      const updatePromises = [
        // Modèles existants
        this.contentBasedService.updateUserModel(userId),
        this.collaborativeFilteringService.updateUserModel(userId),

        // Nouveaux modèles
        this.matrixFactorizationService.trainModel(userId, RecommendationType.COURSE),
        this.matrixFactorizationService.trainModel(userId, RecommendationType.RETREAT),
        this.matrixFactorizationService.trainModel(userId, RecommendationType.PARTNER),
      ];

      // Exécuter toutes les mises à jour en parallèle
      await Promise.all(updatePromises);

      this.logger.log(`Modèles de recommandation mis à jour pour l'utilisateur ${userId}`);
      return { success: true };
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour du modèle de recommandation: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * Enregistre les recommandations générées
   * @param userId ID de l'utilisateur
   * @param type Type de recommandation
   * @param recommendations Recommandations générées
   */
  private async saveRecommendations(
    userId: string,
    type: RecommendationType,
    recommendations: any[],
  ) {
    try {
      // Supprimer les anciennes recommandations
      await this.prisma.recommendation.deleteMany({
        where: {
          userId,
          type,
        },
      });

      // Enregistrer les nouvelles recommandations
      await this.prisma.recommendation.createMany({
        data: recommendations.map((recommendation, index) => ({
          userId,
          itemId: recommendation.id,
          type,
          score: recommendation.score,
          rank: index + 1,
          metadata: recommendation.metadata || {},
        })),
      });
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement des recommandations: ${error.message}`);
    }
  }

  /**
   * Récupère les cours tendance
   * @param options Options de recommandation
   * @returns Liste des cours tendance
   */
  private async getTrendingCourses(options: RecommendationOptions = {}) {
    const limit = options.limit || 10;

    // Récupérer les cours avec le plus d'inscriptions récentes
    const trendingCourses = await this.prisma.course.findMany({
      take: limit,
      orderBy: {
        enrollments: {
          _count: 'desc',
        },
      },
      include: {
        _count: {
          select: {
            enrollments: true,
          },
        },
      },
    });

    // Transformer les résultats
    return trendingCourses.map(course => ({
      id: course.id,
      type: RecommendationType.COURSE,
      title: course.title,
      description: course.description,
      score: course._count.enrollments / 100, // Normaliser le score
      metadata: {
        enrollmentCount: course._count.enrollments,
        level: course.level,
        category: course.category,
      },
    }));
  }

  /**
   * Récupère les retraites tendance
   * @param options Options de recommandation
   * @returns Liste des retraites tendance
   */
  private async getTrendingRetreats(options: RecommendationOptions = {}) {
    const limit = options.limit || 10;

    // Simuler la récupération des retraites tendance
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    return Array.from({ length: limit }, (_, i) => ({
      id: `retreat-${i + 1}`,
      type: RecommendationType.RETREAT,
      title: `Retraite tendance ${i + 1}`,
      description: `Description de la retraite tendance ${i + 1}`,
      score: 1 - i * 0.1,
      metadata: {
        location: `Lieu ${i + 1}`,
        duration: Math.floor(Math.random() * 10) + 1,
        category: `Catégorie ${i % 3 + 1}`,
      },
    }));
  }

  /**
   * Récupère les partenaires tendance
   * @param options Options de recommandation
   * @returns Liste des partenaires tendance
   */
  private async getTrendingPartners(options: RecommendationOptions = {}) {
    const limit = options.limit || 10;

    // Simuler la récupération des partenaires tendance
    // Dans une implémentation réelle, on utiliserait une requête à la base de données
    return Array.from({ length: limit }, (_, i) => ({
      id: `partner-${i + 1}`,
      type: RecommendationType.PARTNER,
      title: `Partenaire tendance ${i + 1}`,
      description: `Description du partenaire tendance ${i + 1}`,
      score: 1 - i * 0.1,
      metadata: {
        specialty: `Spécialité ${i % 5 + 1}`,
        rating: (Math.random() * 2 + 3).toFixed(1),
        location: `Lieu ${i % 10 + 1}`,
      },
    }));
  }
}
