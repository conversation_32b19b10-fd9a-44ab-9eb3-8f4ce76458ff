import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import {
  ReinforcementLearningAgent,
  RLAgentType,
  RLAgentState,
  ExplorationStrategy,
  RLState,
  RLAction,
  RLReward,
  RLExperience,
  RLEpisode,
  RewardFunction,
  RewardContext,
  ExplanationInteraction,
} from '../interfaces/reinforcement-learning.interface';
import {
  CreateRLAgentDto,
  UpdateRLAgentDto,
  RecordExplanationInteractionDto,
  RewardFunctionConfigDto,
} from '../dto/reinforcement-learning.dto';

/**
 * Service pour l'apprentissage par renforcement
 */
@Injectable()
export class ReinforcementLearningService {
  private readonly logger = new Logger(ReinforcementLearningService.name);
  private readonly agents: Map<string, ReinforcementLearningAgent> = new Map();
  private readonly episodes: Map<string, RLEpisode> = new Map();
  private readonly rewardFunctions: Map<string, RewardFunction> = new Map();
  private readonly defaultRewardFunction: RewardFunction;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // Initialiser la fonction de récompense par défaut
    this.defaultRewardFunction = this.createDefaultRewardFunction();
    
    // Charger les agents existants
    this.loadAgents();
  }

  /**
   * Charge les agents existants depuis la base de données
   */
  private async loadAgents(): Promise<void> {
    try {
      const dbAgents = await this.prisma.rlAgent.findMany({
        include: {
          stats: true,
        },
      });

      for (const dbAgent of dbAgents) {
        const agent = this.mapDbAgentToInterface(dbAgent);
        this.agents.set(agent.id, agent);
        
        // Créer une fonction de récompense pour cet agent
        this.rewardFunctions.set(agent.id, this.createRewardFunction(agent.id));
        
        this.logger.log(`Agent chargé: ${agent.name} (${agent.id})`);
      }

      this.logger.log(`${dbAgents.length} agents chargés`);
    } catch (error) {
      this.logger.error(`Erreur lors du chargement des agents: ${error.message}`);
    }
  }

  /**
   * Crée un nouvel agent d'apprentissage par renforcement
   * @param createDto DTO de création d'agent
   * @returns Agent créé
   */
  async createAgent(createDto: CreateRLAgentDto): Promise<ReinforcementLearningAgent> {
    try {
      // Créer l'agent dans la base de données
      const dbAgent = await this.prisma.rlAgent.create({
        data: {
          name: createDto.name,
          description: createDto.description,
          agentType: createDto.agentType,
          state: RLAgentState.INITIALIZING,
          config: {
            learningRate: createDto.config.learningRate,
            discountFactor: createDto.config.discountFactor,
            explorationRate: createDto.config.explorationRate,
            explorationStrategy: createDto.config.explorationStrategy,
            experienceMemorySize: createDto.config.experienceMemorySize,
            batchSize: createDto.config.batchSize,
            targetNetworkUpdateFrequency: createDto.config.targetNetworkUpdateFrequency,
            agentSpecificParams: createDto.config.agentSpecificParams
              ? JSON.stringify(createDto.config.agentSpecificParams)
              : null,
          },
          stats: {
            create: {
              totalEpisodes: 0,
              totalSteps: 0,
              cumulativeReward: 0,
              averageRewardPerEpisode: 0,
              convergenceRate: 0,
              currentExplorationRate: createDto.config.explorationRate,
              rewardHistory: JSON.stringify([]),
              errorHistory: JSON.stringify([]),
            },
          },
        },
        include: {
          stats: true,
        },
      });

      // Convertir en interface
      const agent = this.mapDbAgentToInterface(dbAgent);
      
      // Ajouter à la map des agents
      this.agents.set(agent.id, agent);
      
      // Créer une fonction de récompense pour cet agent
      this.rewardFunctions.set(agent.id, this.createRewardFunction(agent.id));
      
      // Mettre à jour l'état de l'agent
      await this.updateAgentState(agent.id, RLAgentState.IDLE);
      
      // Émettre un événement de création d'agent
      this.eventEmitter.emit('rl.agent.created', { agentId: agent.id });
      
      this.logger.log(`Agent créé: ${agent.name} (${agent.id})`);
      
      return agent;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Récupère un agent par son ID
   * @param agentId ID de l'agent
   * @returns Agent
   */
  async getAgent(agentId: string): Promise<ReinforcementLearningAgent> {
    // Vérifier si l'agent est en mémoire
    if (this.agents.has(agentId)) {
      return this.agents.get(agentId);
    }
    
    // Sinon, le récupérer depuis la base de données
    const dbAgent = await this.prisma.rlAgent.findUnique({
      where: { id: agentId },
      include: {
        stats: true,
      },
    });
    
    if (!dbAgent) {
      throw new NotFoundException(`Agent avec l'ID ${agentId} non trouvé`);
    }
    
    // Convertir en interface
    const agent = this.mapDbAgentToInterface(dbAgent);
    
    // Ajouter à la map des agents
    this.agents.set(agent.id, agent);
    
    // Créer une fonction de récompense pour cet agent s'il n'existe pas déjà
    if (!this.rewardFunctions.has(agent.id)) {
      this.rewardFunctions.set(agent.id, this.createRewardFunction(agent.id));
    }
    
    return agent;
  }

  /**
   * Récupère tous les agents
   * @returns Liste des agents
   */
  async getAllAgents(): Promise<ReinforcementLearningAgent[]> {
    const dbAgents = await this.prisma.rlAgent.findMany({
      include: {
        stats: true,
      },
    });
    
    return dbAgents.map(dbAgent => this.mapDbAgentToInterface(dbAgent));
  }

  /**
   * Met à jour un agent
   * @param agentId ID de l'agent
   * @param updateDto DTO de mise à jour
   * @returns Agent mis à jour
   */
  async updateAgent(agentId: string, updateDto: UpdateRLAgentDto): Promise<ReinforcementLearningAgent> {
    // Vérifier si l'agent existe
    const existingAgent = await this.getAgent(agentId);
    
    // Préparer les données de mise à jour
    const updateData: any = {};
    
    if (updateDto.name !== undefined) {
      updateData.name = updateDto.name;
    }
    
    if (updateDto.description !== undefined) {
      updateData.description = updateDto.description;
    }
    
    if (updateDto.state !== undefined) {
      updateData.state = updateDto.state;
    }
    
    if (updateDto.config) {
      updateData.config = {
        update: {},
      };
      
      if (updateDto.config.learningRate !== undefined) {
        updateData.config.update.learningRate = updateDto.config.learningRate;
      }
      
      if (updateDto.config.discountFactor !== undefined) {
        updateData.config.update.discountFactor = updateDto.config.discountFactor;
      }
      
      if (updateDto.config.explorationRate !== undefined) {
        updateData.config.update.explorationRate = updateDto.config.explorationRate;
        
        // Mettre également à jour le taux d'exploration dans les statistiques
        updateData.stats = {
          update: {
            currentExplorationRate: updateDto.config.explorationRate,
          },
        };
      }
      
      if (updateDto.config.explorationStrategy !== undefined) {
        updateData.config.update.explorationStrategy = updateDto.config.explorationStrategy;
      }
      
      if (updateDto.config.experienceMemorySize !== undefined) {
        updateData.config.update.experienceMemorySize = updateDto.config.experienceMemorySize;
      }
      
      if (updateDto.config.batchSize !== undefined) {
        updateData.config.update.batchSize = updateDto.config.batchSize;
      }
      
      if (updateDto.config.targetNetworkUpdateFrequency !== undefined) {
        updateData.config.update.targetNetworkUpdateFrequency = updateDto.config.targetNetworkUpdateFrequency;
      }
      
      if (updateDto.config.agentSpecificParams !== undefined) {
        updateData.config.update.agentSpecificParams = JSON.stringify(updateDto.config.agentSpecificParams);
      }
    }
    
    // Mettre à jour l'agent dans la base de données
    const dbAgent = await this.prisma.rlAgent.update({
      where: { id: agentId },
      data: updateData,
      include: {
        stats: true,
      },
    });
    
    // Convertir en interface
    const updatedAgent = this.mapDbAgentToInterface(dbAgent);
    
    // Mettre à jour la map des agents
    this.agents.set(updatedAgent.id, updatedAgent);
    
    // Émettre un événement de mise à jour d'agent
    this.eventEmitter.emit('rl.agent.updated', { agentId: updatedAgent.id });
    
    this.logger.log(`Agent mis à jour: ${updatedAgent.name} (${updatedAgent.id})`);
    
    return updatedAgent;
  }

  /**
   * Supprime un agent
   * @param agentId ID de l'agent
   * @returns Résultat de la suppression
   */
  async deleteAgent(agentId: string): Promise<{ success: boolean }> {
    // Vérifier si l'agent existe
    await this.getAgent(agentId);
    
    // Supprimer l'agent de la base de données
    await this.prisma.rlAgent.delete({
      where: { id: agentId },
    });
    
    // Supprimer l'agent de la map des agents
    this.agents.delete(agentId);
    
    // Supprimer la fonction de récompense associée
    this.rewardFunctions.delete(agentId);
    
    // Émettre un événement de suppression d'agent
    this.eventEmitter.emit('rl.agent.deleted', { agentId });
    
    this.logger.log(`Agent supprimé: ${agentId}`);
    
    return { success: true };
  }

  /**
   * Met à jour l'état d'un agent
   * @param agentId ID de l'agent
   * @param state Nouvel état
   * @returns Agent mis à jour
   */
  async updateAgentState(agentId: string, state: RLAgentState): Promise<ReinforcementLearningAgent> {
    // Vérifier si l'agent existe
    const existingAgent = await this.getAgent(agentId);
    
    // Mettre à jour l'état dans la base de données
    const dbAgent = await this.prisma.rlAgent.update({
      where: { id: agentId },
      data: { state },
      include: {
        stats: true,
      },
    });
    
    // Convertir en interface
    const updatedAgent = this.mapDbAgentToInterface(dbAgent);
    
    // Mettre à jour la map des agents
    this.agents.set(updatedAgent.id, updatedAgent);
    
    // Émettre un événement de changement d'état
    this.eventEmitter.emit('rl.agent.stateChanged', { agentId, state });
    
    this.logger.log(`État de l'agent mis à jour: ${updatedAgent.name} (${updatedAgent.id}) -> ${state}`);
    
    return updatedAgent;
  }

  /**
   * Démarre l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  async startLearning(agentId: string): Promise<ReinforcementLearningAgent> {
    // Vérifier si l'agent existe
    const agent = await this.getAgent(agentId);
    
    // Vérifier si l'agent est dans un état valide pour démarrer l'apprentissage
    if (agent.state !== RLAgentState.IDLE && agent.state !== RLAgentState.PAUSED) {
      throw new BadRequestException(`L'agent ne peut pas démarrer l'apprentissage depuis l'état ${agent.state}`);
    }
    
    // Mettre à jour l'état de l'agent
    return this.updateAgentState(agentId, RLAgentState.LEARNING);
  }

  /**
   * Met en pause l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  async pauseLearning(agentId: string): Promise<ReinforcementLearningAgent> {
    // Vérifier si l'agent existe
    const agent = await this.getAgent(agentId);
    
    // Vérifier si l'agent est en cours d'apprentissage
    if (agent.state !== RLAgentState.LEARNING) {
      throw new BadRequestException(`L'agent n'est pas en cours d'apprentissage (état actuel: ${agent.state})`);
    }
    
    // Mettre à jour l'état de l'agent
    return this.updateAgentState(agentId, RLAgentState.PAUSED);
  }

  /**
   * Arrête l'apprentissage d'un agent
   * @param agentId ID de l'agent
   * @returns Agent mis à jour
   */
  async stopLearning(agentId: string): Promise<ReinforcementLearningAgent> {
    // Vérifier si l'agent existe
    const agent = await this.getAgent(agentId);
    
    // Vérifier si l'agent est en cours d'apprentissage ou en pause
    if (agent.state !== RLAgentState.LEARNING && agent.state !== RLAgentState.PAUSED) {
      throw new BadRequestException(`L'agent n'est pas en cours d'apprentissage ou en pause (état actuel: ${agent.state})`);
    }
    
    // Mettre à jour l'état de l'agent
    return this.updateAgentState(agentId, RLAgentState.IDLE);
  }

  /**
   * Enregistre une interaction avec une explication
   * @param userId ID de l'utilisateur
   * @param interactionDto DTO de l'interaction
   * @returns Récompense calculée
   */
  async recordInteraction(
    userId: string,
    interactionDto: RecordExplanationInteractionDto,
  ): Promise<RLReward> {
    try {
      // Récupérer l'explication
      const explanation = await this.prisma.explanation.findUnique({
        where: { id: interactionDto.explanationId },
        include: {
          recommendation: true,
        },
      });
      
      if (!explanation) {
        throw new NotFoundException(`Explication avec l'ID ${interactionDto.explanationId} non trouvée`);
      }
      
      // Récupérer les préférences de l'utilisateur
      const userPreferences = await this.prisma.explanationPreferences.findUnique({
        where: { userId },
      });
      
      // Créer le contexte de récompense
      const rewardContext: RewardContext = {
        user: {
          id: userId,
          preferences: userPreferences ? JSON.parse(JSON.stringify(userPreferences)) : {},
        },
        explanation: {
          id: explanation.id,
          factors: explanation.factors,
          template: explanation.template,
        },
        recommendation: {
          id: explanation.recommendationId,
          itemType: explanation.recommendation.itemType,
          itemId: explanation.recommendation.itemId,
        },
      };
      
      // Créer l'objet d'interaction
      const interaction: ExplanationInteraction = {
        id: uuidv4(),
        userId,
        explanationId: interactionDto.explanationId,
        interactionType: interactionDto.interactionType,
        data: interactionDto.data,
        duration: interactionDto.duration,
        timestamp: new Date(),
      };
      
      // Enregistrer l'interaction dans la base de données
      await this.prisma.explanationEvent.create({
        data: {
          userId,
          explanationId: interactionDto.explanationId,
          eventType: interactionDto.interactionType,
          data: interactionDto.data,
          metadata: {
            duration: interactionDto.duration,
          },
        },
      });
      
      // Calculer la récompense
      const reward = this.calculateReward(interaction, rewardContext);
      
      // Émettre un événement d'interaction
      this.eventEmitter.emit('rl.interaction.recorded', {
        userId,
        explanationId: interactionDto.explanationId,
        interactionType: interactionDto.interactionType,
        reward: reward.value,
      });
      
      // Mettre à jour l'agent actif (si disponible)
      await this.updateAgentWithInteraction(interaction, rewardContext, reward);
      
      return reward;
    } catch (error) {
      this.logger.error(`Erreur lors de l'enregistrement de l'interaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calcule la récompense pour une interaction
   * @param interaction Interaction avec l'explication
   * @param context Contexte de la récompense
   * @returns Récompense calculée
   */
  private calculateReward(
    interaction: ExplanationInteraction,
    context: RewardContext,
  ): RLReward {
    // Utiliser la fonction de récompense par défaut
    return this.defaultRewardFunction.calculateReward(interaction, context);
  }

  /**
   * Met à jour un agent avec une nouvelle interaction
   * @param interaction Interaction avec l'explication
   * @param context Contexte de la récompense
   * @param reward Récompense calculée
   */
  private async updateAgentWithInteraction(
    interaction: ExplanationInteraction,
    context: RewardContext,
    reward: RLReward,
  ): Promise<void> {
    // Récupérer l'agent actif
    const activeAgents = Array.from(this.agents.values()).filter(
      agent => agent.state === RLAgentState.LEARNING,
    );
    
    if (activeAgents.length === 0) {
      return;
    }
    
    // Pour chaque agent actif, mettre à jour les statistiques
    for (const agent of activeAgents) {
      // Mettre à jour les statistiques de l'agent
      await this.updateAgentStats(agent.id, reward.value);
      
      // TODO: Implémenter la logique d'apprentissage spécifique à chaque type d'agent
    }
  }

  /**
   * Met à jour les statistiques d'un agent
   * @param agentId ID de l'agent
   * @param reward Récompense obtenue
   */
  private async updateAgentStats(agentId: string, reward: number): Promise<void> {
    try {
      // Récupérer les statistiques actuelles
      const dbStats = await this.prisma.rlAgentStats.findUnique({
        where: { agentId },
      });
      
      if (!dbStats) {
        return;
      }
      
      // Mettre à jour les statistiques
      const rewardHistory = JSON.parse(dbStats.rewardHistory || '[]');
      rewardHistory.push(reward);
      
      // Limiter l'historique à 100 valeurs
      if (rewardHistory.length > 100) {
        rewardHistory.shift();
      }
      
      // Calculer la nouvelle récompense moyenne
      const totalReward = dbStats.cumulativeReward + reward;
      const totalEpisodes = dbStats.totalEpisodes + 1;
      const averageReward = totalReward / totalEpisodes;
      
      // Mettre à jour dans la base de données
      await this.prisma.rlAgentStats.update({
        where: { agentId },
        data: {
          totalSteps: dbStats.totalSteps + 1,
          totalEpisodes,
          cumulativeReward: totalReward,
          averageRewardPerEpisode: averageReward,
          rewardHistory: JSON.stringify(rewardHistory),
        },
      });
      
      // Mettre à jour l'agent en mémoire
      const agent = this.agents.get(agentId);
      if (agent) {
        agent.stats = {
          ...agent.stats,
          totalSteps: dbStats.totalSteps + 1,
          totalEpisodes,
          cumulativeReward: totalReward,
          averageRewardPerEpisode: averageReward,
          rewardHistory,
        };
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour des statistiques de l'agent: ${error.message}`);
    }
  }

  /**
   * Configure la fonction de récompense
   * @param agentId ID de l'agent
   * @param config Configuration de la fonction de récompense
   * @returns Résultat de la configuration
   */
  async configureRewardFunction(
    agentId: string,
    config: RewardFunctionConfigDto,
  ): Promise<{ success: boolean }> {
    // Vérifier si l'agent existe
    await this.getAgent(agentId);
    
    // Récupérer la fonction de récompense
    const rewardFunction = this.rewardFunctions.get(agentId) || this.defaultRewardFunction;
    
    // Mettre à jour les poids des composantes
    rewardFunction.updateComponentWeights(config.componentWeights);
    
    // Enregistrer la configuration dans la base de données
    await this.prisma.rlAgent.update({
      where: { id: agentId },
      data: {
        metadata: {
          rewardFunctionConfig: config,
        },
      },
    });
    
    this.logger.log(`Fonction de récompense configurée pour l'agent ${agentId}`);
    
    return { success: true };
  }

  /**
   * Crée la fonction de récompense par défaut
   * @returns Fonction de récompense
   */
  private createDefaultRewardFunction(): RewardFunction {
    return {
      calculateReward: (interaction: ExplanationInteraction, context: RewardContext): RLReward => {
        // Composantes de base de la récompense
        const components = [
          { name: 'base', value: 0.1, weight: 0.1 },
        ];
        
        // Récompense pour les clics
        if (interaction.interactionType === 'CLICK') {
          components.push({ name: 'click', value: 0.5, weight: 0.5 });
        }
        
        // Récompense pour le temps passé
        if (interaction.duration) {
          const timeValue = Math.min(interaction.duration / 10, 1);
          components.push({ name: 'timeSpent', value: timeValue, weight: 0.3 });
        }
        
        // Récompense pour le feedback explicite
        if (interaction.interactionType === 'FEEDBACK' && interaction.data.rating) {
          const ratingValue = interaction.data.rating / 5;
          components.push({ name: 'explicitFeedback', value: ratingValue, weight: 0.8 });
        }
        
        // Récompense pour la conversion
        if (interaction.interactionType === 'CONVERSION') {
          components.push({ name: 'conversion', value: 1.0, weight: 1.0 });
        }
        
        // Calculer la récompense totale
        let totalValue = 0;
        let totalWeight = 0;
        
        for (const component of components) {
          totalValue += component.value * component.weight;
          totalWeight += component.weight;
        }
        
        const normalizedValue = totalWeight > 0 ? totalValue / totalWeight : 0;
        
        return {
          value: normalizedValue,
          components,
          metadata: {
            interactionType: interaction.interactionType,
            timestamp: interaction.timestamp.toISOString(),
          },
        };
      },
      
      getRewardComponents: () => [
        { name: 'base', value: 0.1, weight: 0.1 },
        { name: 'click', value: 0.5, weight: 0.5 },
        { name: 'timeSpent', value: 0.3, weight: 0.3 },
        { name: 'explicitFeedback', value: 0.8, weight: 0.8 },
        { name: 'conversion', value: 1.0, weight: 1.0 },
      ],
      
      updateComponentWeights: (weights: Record<string, number>) => {
        // Cette méthode serait implémentée pour mettre à jour les poids des composantes
        // dans une implémentation réelle
      },
    };
  }

  /**
   * Crée une fonction de récompense pour un agent spécifique
   * @param agentId ID de l'agent
   * @returns Fonction de récompense
   */
  private createRewardFunction(agentId: string): RewardFunction {
    // Pour l'instant, utiliser la fonction de récompense par défaut
    // Dans une implémentation réelle, on pourrait personnaliser la fonction
    // en fonction du type d'agent et de sa configuration
    return this.defaultRewardFunction;
  }

  /**
   * Convertit un agent de la base de données en interface
   * @param dbAgent Agent de la base de données
   * @returns Interface de l'agent
   */
  private mapDbAgentToInterface(dbAgent: any): ReinforcementLearningAgent {
    return {
      id: dbAgent.id,
      name: dbAgent.name,
      description: dbAgent.description,
      agentType: dbAgent.agentType as RLAgentType,
      state: dbAgent.state as RLAgentState,
      config: {
        learningRate: dbAgent.config.learningRate,
        discountFactor: dbAgent.config.discountFactor,
        explorationRate: dbAgent.config.explorationRate,
        explorationStrategy: dbAgent.config.explorationStrategy as ExplorationStrategy,
        experienceMemorySize: dbAgent.config.experienceMemorySize,
        batchSize: dbAgent.config.batchSize,
        targetNetworkUpdateFrequency: dbAgent.config.targetNetworkUpdateFrequency,
        agentSpecificParams: dbAgent.config.agentSpecificParams
          ? JSON.parse(dbAgent.config.agentSpecificParams)
          : undefined,
      },
      stats: {
        totalEpisodes: dbAgent.stats.totalEpisodes,
        totalSteps: dbAgent.stats.totalSteps,
        cumulativeReward: dbAgent.stats.cumulativeReward,
        averageRewardPerEpisode: dbAgent.stats.averageRewardPerEpisode,
        convergenceRate: dbAgent.stats.convergenceRate,
        currentExplorationRate: dbAgent.stats.currentExplorationRate,
        rewardHistory: dbAgent.stats.rewardHistory ? JSON.parse(dbAgent.stats.rewardHistory) : [],
        errorHistory: dbAgent.stats.errorHistory ? JSON.parse(dbAgent.stats.errorHistory) : [],
        agentSpecificMetrics: dbAgent.stats.agentSpecificMetrics
          ? JSON.parse(dbAgent.stats.agentSpecificMetrics)
          : undefined,
      },
      createdAt: dbAgent.createdAt,
      updatedAt: dbAgent.updatedAt,
    };
  }
}
