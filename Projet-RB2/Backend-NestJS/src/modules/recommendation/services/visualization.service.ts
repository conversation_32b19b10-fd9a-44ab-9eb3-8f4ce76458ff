import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { RecommendationStrategy } from '../enums/recommendation-strategy.enum';

/**
 * Service de visualisation des recommandations
 * Génère des données pour la visualisation des recommandations
 */
@Injectable()
export class VisualizationService {
  private readonly logger = new Logger(VisualizationService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Génère un graphe de similarité entre les utilisateurs
   * @param limit Nombre maximum d'utilisateurs à inclure
   * @param minSimilarity Similarité minimum entre les utilisateurs
   * @returns Graphe de similarité
   */
  async generateUserSimilarityGraph(
    limit = 50,
    minSimilarity = 0.5,
  ): Promise<UserSimilarityGraph> {
    try {
      this.logger.log(`Génération d'un graphe de similarité entre les utilisateurs (limit=${limit}, minSimilarity=${minSimilarity})`);
      
      // Récupérer les similarités entre utilisateurs
      const similarities = await this.prisma.userSimilarity.findMany({
        where: {
          similarity: {
            gte: minSimilarity,
          },
        },
        orderBy: {
          similarity: 'desc',
        },
        take: limit * 5, // Récupérer plus de similarités pour avoir un graphe plus complet
      });
      
      // Récupérer les utilisateurs impliqués
      const userIds = new Set<string>();
      similarities.forEach(similarity => {
        userIds.add(similarity.userId);
        userIds.add(similarity.similarUserId);
      });
      
      // Limiter le nombre d'utilisateurs
      const limitedUserIds = Array.from(userIds).slice(0, limit);
      
      // Récupérer les détails des utilisateurs
      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: limitedUserIds,
          },
        },
        select: {
          id: true,
          email: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              avatarUrl: true,
            },
          },
        },
      });
      
      // Créer les nœuds du graphe
      const nodes = users.map(user => ({
        id: user.id,
        label: user.profile?.firstName ? `${user.profile.firstName} ${user.profile.lastName || ''}` : user.email,
        avatar: user.profile?.avatarUrl || '',
      }));
      
      // Créer les liens du graphe
      const links = similarities
        .filter(similarity => 
          limitedUserIds.includes(similarity.userId) && 
          limitedUserIds.includes(similarity.similarUserId)
        )
        .map(similarity => ({
          source: similarity.userId,
          target: similarity.similarUserId,
          value: similarity.similarity,
        }));
      
      return {
        nodes,
        links,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du graphe de similarité: ${error.message}`);
      return {
        nodes: [],
        links: [],
      };
    }
  }

  /**
   * Génère un graphe de recommandations pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param limit Nombre maximum de recommandations à inclure
   * @returns Graphe de recommandations
   */
  async generateUserRecommendationGraph(
    userId: string,
    limit = 20,
  ): Promise<RecommendationGraph> {
    try {
      this.logger.log(`Génération d'un graphe de recommandations pour l'utilisateur ${userId} (limit=${limit})`);
      
      // Récupérer les recommandations de l'utilisateur
      const recommendations = await this.prisma.recommendation.findMany({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });
      
      // Récupérer les interactions de l'utilisateur
      const interactions = await this.prisma.userInteraction.findMany({
        where: {
          userId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit * 2,
      });
      
      // Récupérer les détails des items
      const itemIds = new Set<string>();
      recommendations.forEach(recommendation => itemIds.add(recommendation.itemId));
      interactions.forEach(interaction => itemIds.add(interaction.itemId));
      
      const itemsDetails = await this.getItemsDetails(Array.from(itemIds));
      
      // Créer le nœud utilisateur
      const userNode = {
        id: userId,
        label: 'Utilisateur',
        type: 'user',
      };
      
      // Créer les nœuds des recommandations
      const recommendationNodes = recommendations.map(recommendation => ({
        id: recommendation.id,
        label: 'Recommandation',
        type: 'recommendation',
        itemId: recommendation.itemId,
        recommendationType: recommendation.type,
        score: recommendation.score,
        createdAt: recommendation.createdAt,
      }));
      
      // Créer les nœuds des items
      const itemNodes = Array.from(itemIds)
        .filter(itemId => itemsDetails[itemId])
        .map(itemId => ({
          id: itemId,
          label: itemsDetails[itemId].title,
          type: 'item',
          itemType: itemsDetails[itemId].type,
          category: itemsDetails[itemId].category,
        }));
      
      // Créer les liens entre l'utilisateur et les recommandations
      const userToRecommendationLinks = recommendationNodes.map(node => ({
        source: userId,
        target: node.id,
        type: 'receives',
      }));
      
      // Créer les liens entre les recommandations et les items
      const recommendationToItemLinks = recommendationNodes.map(node => ({
        source: node.id,
        target: node.itemId,
        type: 'recommends',
        score: node.score,
      }));
      
      // Créer les liens entre l'utilisateur et les items (interactions)
      const userToItemLinks = interactions.map(interaction => ({
        source: userId,
        target: interaction.itemId,
        type: interaction.interactionType.toLowerCase(),
        timestamp: interaction.createdAt,
      }));
      
      // Combiner tous les nœuds et liens
      const nodes = [userNode, ...recommendationNodes, ...itemNodes];
      const links = [...userToRecommendationLinks, ...recommendationToItemLinks, ...userToItemLinks];
      
      return {
        nodes,
        links,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du graphe de recommandations: ${error.message}`);
      return {
        nodes: [],
        links: [],
      };
    }
  }

  /**
   * Génère une carte de chaleur des recommandations
   * @param startDate Date de début de la période d'analyse
   * @param endDate Date de fin de la période d'analyse
   * @returns Carte de chaleur
   */
  async generateRecommendationHeatmap(
    startDate?: Date,
    endDate?: Date,
  ): Promise<RecommendationHeatmap> {
    try {
      // Définir la période d'analyse
      const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 derniers jours par défaut
      const end = endDate || new Date();
      
      this.logger.log(`Génération d'une carte de chaleur des recommandations du ${start.toISOString()} au ${end.toISOString()}`);
      
      // Récupérer les recommandations par type et stratégie
      const recommendations = await this.prisma.recommendation.groupBy({
        by: ['type', 'metadata'],
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
        _count: {
          id: true,
        },
      });
      
      // Récupérer les interactions par type et stratégie
      const interactions = await this.prisma.userInteraction.groupBy({
        by: ['itemType', 'interactionType', 'metadata'],
        where: {
          createdAt: {
            gte: start,
            lte: end,
          },
        },
        _count: {
          id: true,
        },
      });
      
      // Créer la carte de chaleur
      const heatmap: Record<string, Record<string, number>> = {};
      
      // Initialiser la carte de chaleur avec toutes les combinaisons possibles
      Object.values(RecommendationType).forEach(type => {
        heatmap[type] = {};
        Object.values(RecommendationStrategy).forEach(strategy => {
          heatmap[type][strategy] = 0;
        });
      });
      
      // Remplir la carte de chaleur avec les recommandations
      recommendations.forEach(recommendation => {
        const type = recommendation.type;
        const strategy = recommendation.metadata?.strategy || 'UNKNOWN';
        
        if (heatmap[type] && typeof heatmap[type][strategy] === 'number') {
          heatmap[type][strategy] += recommendation._count.id;
        }
      });
      
      // Créer la carte de chaleur des interactions
      const interactionHeatmap: Record<string, Record<string, Record<string, number>>> = {};
      
      // Initialiser la carte de chaleur des interactions
      Object.values(RecommendationType).forEach(type => {
        interactionHeatmap[type] = {};
        Object.values(RecommendationStrategy).forEach(strategy => {
          interactionHeatmap[type][strategy] = {
            VIEW: 0,
            CLICK: 0,
            LIKE: 0,
            BOOKMARK: 0,
            ENROLL: 0,
            PURCHASE: 0,
          };
        });
      });
      
      // Remplir la carte de chaleur des interactions
      interactions.forEach(interaction => {
        const type = interaction.itemType;
        const interactionType = interaction.interactionType;
        const strategy = interaction.metadata?.recommendationStrategy || 'UNKNOWN';
        
        if (
          interactionHeatmap[type] &&
          interactionHeatmap[type][strategy] &&
          typeof interactionHeatmap[type][strategy][interactionType] === 'number'
        ) {
          interactionHeatmap[type][strategy][interactionType] += interaction._count.id;
        }
      });
      
      return {
        period: {
          start,
          end,
        },
        recommendations: heatmap,
        interactions: interactionHeatmap,
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la génération de la carte de chaleur: ${error.message}`);
      return {
        period: {
          start: new Date(),
          end: new Date(),
        },
        recommendations: {},
        interactions: {},
      };
    }
  }

  /**
   * Récupère les détails des items
   * @param itemIds IDs des items
   * @returns Détails des items
   */
  private async getItemsDetails(itemIds: string[]): Promise<Record<string, ItemDetails>> {
    try {
      const result: Record<string, ItemDetails> = {};
      
      // Récupérer les détails des cours
      const courses = await this.prisma.course.findMany({
        where: {
          id: {
            in: itemIds,
          },
        },
        select: {
          id: true,
          title: true,
          category: true,
        },
      });
      
      courses.forEach(course => {
        result[course.id] = {
          title: course.title,
          type: RecommendationType.COURSE,
          category: course.category,
        };
      });
      
      // Récupérer les détails des retraites
      const retreats = await this.prisma.retreat.findMany({
        where: {
          id: {
            in: itemIds,
          },
        },
        select: {
          id: true,
          title: true,
          metadata: true,
        },
      });
      
      retreats.forEach(retreat => {
        result[retreat.id] = {
          title: retreat.title,
          type: RecommendationType.RETREAT,
          category: retreat.metadata?.category || 'retreat',
        };
      });
      
      // Récupérer les détails des partenaires
      const partners = await this.prisma.partner.findMany({
        where: {
          id: {
            in: itemIds,
          },
        },
        select: {
          id: true,
          name: true,
          metadata: true,
        },
      });
      
      partners.forEach(partner => {
        result[partner.id] = {
          title: partner.name,
          type: RecommendationType.PARTNER,
          category: partner.metadata?.category || 'partner',
        };
      });
      
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des détails des items: ${error.message}`);
      return {};
    }
  }
}

/**
 * Interface pour un graphe de similarité entre utilisateurs
 */
export interface UserSimilarityGraph {
  /** Nœuds du graphe (utilisateurs) */
  nodes: Array<{
    /** ID de l'utilisateur */
    id: string;
    
    /** Nom de l'utilisateur */
    label: string;
    
    /** URL de l'avatar */
    avatar: string;
  }>;
  
  /** Liens du graphe (similarités) */
  links: Array<{
    /** ID de l'utilisateur source */
    source: string;
    
    /** ID de l'utilisateur cible */
    target: string;
    
    /** Valeur de la similarité */
    value: number;
  }>;
}

/**
 * Interface pour un graphe de recommandations
 */
export interface RecommendationGraph {
  /** Nœuds du graphe */
  nodes: Array<{
    /** ID du nœud */
    id: string;
    
    /** Nom du nœud */
    label: string;
    
    /** Type de nœud */
    type: string;
    
    /** Autres propriétés */
    [key: string]: any;
  }>;
  
  /** Liens du graphe */
  links: Array<{
    /** ID du nœud source */
    source: string;
    
    /** ID du nœud cible */
    target: string;
    
    /** Type de lien */
    type: string;
    
    /** Autres propriétés */
    [key: string]: any;
  }>;
}

/**
 * Interface pour une carte de chaleur des recommandations
 */
export interface RecommendationHeatmap {
  /** Période d'analyse */
  period: {
    start: Date;
    end: Date;
  };
  
  /** Carte de chaleur des recommandations */
  recommendations: Record<string, Record<string, number>>;
  
  /** Carte de chaleur des interactions */
  interactions: Record<string, Record<string, Record<string, number>>>;
}

/**
 * Interface pour les détails d'un item
 */
interface ItemDetails {
  /** Titre de l'item */
  title: string;
  
  /** Type de l'item */
  type: RecommendationType;
  
  /** Catégorie de l'item */
  category: string;
}
