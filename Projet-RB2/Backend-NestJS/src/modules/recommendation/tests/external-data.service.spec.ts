import { Test, TestingModule } from '@nestjs/testing';
import { ExternalDataService } from '../services/external-data.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { RecommendationType } from '../enums/recommendation-type.enum';
import { of } from 'rxjs';

// Mock pour les services
const mockPrismaService = {
  externalData: {
    findMany: jest.fn(),
    create: jest.fn(),
    deleteMany: jest.fn(),
  },
};

const mockConfigService = {
  get: jest.fn(),
};

const mockHttpService = {
  get: jest.fn(),
};

describe('ExternalDataService', () => {
  let service: ExternalDataService;
  let prismaService: PrismaService;
  let configService: ConfigService;
  let httpService: HttpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ExternalDataService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<ExternalDataService>(ExternalDataService);
    prismaService = module.get<PrismaService>(PrismaService);
    configService = module.get<ConfigService>(ConfigService);
    httpService = module.get<HttpService>(HttpService);

    // Réinitialiser les mocks avant chaque test
    jest.clearAllMocks();
    
    // Configurer les mocks par défaut
    mockConfigService.get.mockImplementation((key, defaultValue) => {
      if (key === 'recommendation.externalData.enabled') return true;
      if (key === 'recommendation.externalData.refreshInterval') return 24;
      if (key === 'recommendation.externalData.apiKeys.googleTrends') return 'google-api-key';
      if (key === 'recommendation.externalData.apiKeys.newsApi') return 'news-api-key';
      if (key === 'recommendation.externalData.apiKeys.openWeatherMap') return 'weather-api-key';
      if (key === 'recommendation.externalData.sources.googleTrends.enabled') return true;
      if (key === 'recommendation.externalData.sources.newsApi.enabled') return true;
      if (key === 'recommendation.externalData.sources.openWeatherMap.enabled') return true;
      return defaultValue;
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getExternalData', () => {
    it('should return external data for a user and type', async () => {
      const userId = 'user-123';
      const type = RecommendationType.COURSE;
      
      // Configurer les mocks
      const mockExternalData = [
        {
          id: 'data1',
          dataType: 'TREND',
          source: 'GoogleTrends',
          title: 'Trending Topic 1',
          content: 'Content about trending topic 1',
          url: 'https://example.com/trend1',
          imageUrl: 'https://example.com/trend1.jpg',
          relevanceScore: 0.8,
          metadata: { trafficCount: '1M+' },
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
        {
          id: 'data2',
          dataType: 'NEWS',
          source: 'NewsAPI',
          title: 'News Article 1',
          content: 'Content of news article 1',
          url: 'https://example.com/news1',
          imageUrl: 'https://example.com/news1.jpg',
          relevanceScore: 0.7,
          metadata: { author: 'John Doe' },
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      ];

      mockPrismaService.externalData.findMany.mockResolvedValue(mockExternalData);

      // Appeler la méthode à tester
      const result = await service.getExternalData(userId, type);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('data1');
      expect(result[0].type).toBe('TREND');
      expect(result[0].source).toBe('GoogleTrends');
      expect(result[0].title).toBe('Trending Topic 1');
      expect(result[0].content).toBe('Content about trending topic 1');
      expect(result[0].url).toBe('https://example.com/trend1');
      expect(result[0].imageUrl).toBe('https://example.com/trend1.jpg');
      expect(result[0].relevanceScore).toBe(0.8);
      expect(result[0].metadata).toEqual({ trafficCount: '1M+' });

      expect(result[1].id).toBe('data2');
      expect(result[1].type).toBe('NEWS');
      expect(result[1].source).toBe('NewsAPI');

      // Vérifier que les méthodes du PrismaService ont été appelées avec les bons paramètres
      expect(mockPrismaService.externalData.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { userId },
            { userId: null },
          ],
          applicableTypes: {
            has: type,
          },
          expiresAt: {
            gt: expect.any(Date),
          },
        },
        orderBy: {
          relevanceScore: 'desc',
        },
        take: 20,
      });
    });
  });

  describe('enrichRecommendations', () => {
    it('should enrich recommendations with external data', async () => {
      const userId = 'user-123';
      const recommendations = [
        {
          id: 'rec1',
          type: RecommendationType.COURSE,
          title: 'Yoga Course',
          description: 'A course about yoga',
          metadata: {
            category: 'yoga',
            tags: ['wellness', 'mindfulness'],
          },
        },
        {
          id: 'rec2',
          type: RecommendationType.RETREAT,
          title: 'Meditation Retreat',
          description: 'A retreat for meditation',
          metadata: {
            category: 'meditation',
            tags: ['wellness', 'mindfulness'],
          },
        },
      ];
      
      // Configurer les mocks
      const mockExternalData = [
        {
          id: 'data1',
          type: 'TREND',
          source: 'GoogleTrends',
          title: 'Yoga Trends',
          content: 'Yoga is trending',
          url: 'https://example.com/yoga-trend',
          imageUrl: 'https://example.com/yoga.jpg',
          relevanceScore: 0.8,
          metadata: { trafficCount: '1M+' },
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
        {
          id: 'data2',
          type: 'NEWS',
          source: 'NewsAPI',
          title: 'Meditation Benefits',
          content: 'New study on meditation benefits',
          url: 'https://example.com/meditation-news',
          imageUrl: 'https://example.com/meditation.jpg',
          relevanceScore: 0.7,
          metadata: { author: 'John Doe' },
          createdAt: new Date(),
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      ];

      // Mock pour getExternalData
      jest.spyOn(service, 'getExternalData').mockResolvedValue(mockExternalData);

      // Appeler la méthode à tester
      const result = await service.enrichRecommendations(userId, recommendations);

      // Vérifier les résultats
      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      
      // La première recommandation devrait avoir des données externes liées au yoga
      expect(result[0].id).toBe('rec1');
      expect(result[0].externalData).toBeDefined();
      expect(result[0].externalData).toContainEqual(expect.objectContaining({
        id: 'data1',
        title: 'Yoga Trends',
      }));
      
      // La deuxième recommandation devrait avoir des données externes liées à la méditation
      expect(result[1].id).toBe('rec2');
      expect(result[1].externalData).toBeDefined();
      expect(result[1].externalData).toContainEqual(expect.objectContaining({
        id: 'data2',
        title: 'Meditation Benefits',
      }));

      // Vérifier que getExternalData a été appelé avec les bons paramètres
      expect(service.getExternalData).toHaveBeenCalledWith(userId, RecommendationType.COURSE);
    });
  });

  describe('refreshExternalData', () => {
    it('should refresh external data from sources', async () => {
      // Configurer les mocks
      mockHttpService.get.mockImplementation((url, options) => {
        if (url.includes('trends.google.com')) {
          return of({
            data: {
              default: {
                trendingSearchesDays: [
                  {
                    trendingSearches: [
                      {
                        title: { query: 'Yoga' },
                        articles: [{ snippet: 'Yoga is good', url: 'https://example.com/yoga' }],
                        image: { imageUrl: 'https://example.com/yoga.jpg' },
                        formattedTraffic: '1M+',
                        relatedQueries: [{ query: 'Yoga benefits' }],
                      },
                    ],
                  },
                ],
              },
            },
          });
        } else if (url.includes('newsapi.org')) {
          return of({
            data: {
              articles: [
                {
                  title: 'Meditation Benefits',
                  description: 'New study on meditation benefits',
                  url: 'https://example.com/meditation-news',
                  urlToImage: 'https://example.com/meditation.jpg',
                  author: 'John Doe',
                  publishedAt: '2023-01-01T12:00:00Z',
                  source: { name: 'Health News' },
                },
              ],
            },
          });
        } else if (url.includes('openweathermap.org')) {
          return of({
            data: {
              weather: [{ description: 'Sunny', icon: '01d' }],
              main: { temp: 25, feels_like: 26, humidity: 50 },
              wind: { speed: 5 },
              name: 'Paris',
              sys: { country: 'FR' },
            },
          });
        }
        return of({ data: {} });
      });

      mockPrismaService.externalData.create.mockResolvedValue({});
      mockPrismaService.externalData.deleteMany.mockResolvedValue({ count: 5 });

      // Appeler la méthode à tester
      await service.refreshExternalData();

      // Vérifier que les méthodes ont été appelées
      expect(mockHttpService.get).toHaveBeenCalledTimes(3); // Une fois pour chaque source
      expect(mockPrismaService.externalData.create).toHaveBeenCalled();
      expect(mockPrismaService.externalData.deleteMany).toHaveBeenCalled();
    });
  });
});
