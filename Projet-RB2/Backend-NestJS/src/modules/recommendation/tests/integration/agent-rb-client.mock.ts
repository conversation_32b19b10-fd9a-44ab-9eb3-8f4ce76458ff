/**
 * Mock pour le client Agent-RB
 */

import { Injectable, NotFoundException } from '@nestjs/common';
import { RecommendationType } from '../../enums/recommendation-type.enum';
import { getMockItemById, getMockDataByType } from './mocks';

/**
 * Client mocké pour Agent-RB
 */
@Injectable()
export class MockAgentRbClient {
  /**
   * Récupère les détails d'une retraite
   * @param retreatId ID de la retraite
   * @returns Détails de la retraite
   */
  async getRetreat(retreatId: string): Promise<any> {
    const retreat = getMockItemById(RecommendationType.RETREAT, retreatId);
    
    if (!retreat) {
      throw new NotFoundException(`Retreat not found: ${retreatId}`);
    }
    
    return retreat;
  }

  /**
   * Récupère une liste de retraites
   * @param filters Filtres à appliquer
   * @returns Liste de retraites
   */
  async getRetreats(filters?: Record<string, any>): Promise<any> {
    const retreats = getMockDataByType(RecommendationType.RETREAT);
    
    // Appliquer les filtres si nécessaire
    if (filters) {
      return this.applyFilters(retreats, filters);
    }
    
    return retreats;
  }

  /**
   * Récupère les détails d'un partenaire
   * @param partnerId ID du partenaire
   * @returns Détails du partenaire
   */
  async getPartner(partnerId: string): Promise<any> {
    const partner = getMockItemById(RecommendationType.PARTNER, partnerId);
    
    if (!partner) {
      throw new NotFoundException(`Partner not found: ${partnerId}`);
    }
    
    return partner;
  }

  /**
   * Récupère une liste de partenaires
   * @param filters Filtres à appliquer
   * @returns Liste de partenaires
   */
  async getPartners(filters?: Record<string, any>): Promise<any> {
    const partners = getMockDataByType(RecommendationType.PARTNER);
    
    // Appliquer les filtres si nécessaire
    if (filters) {
      return this.applyFilters(partners, filters);
    }
    
    return partners;
  }

  /**
   * Récupère les détails d'un cours
   * @param courseId ID du cours
   * @returns Détails du cours
   */
  async getCourse(courseId: string): Promise<any> {
    const course = getMockItemById(RecommendationType.COURSE, courseId);
    
    if (!course) {
      throw new NotFoundException(`Course not found: ${courseId}`);
    }
    
    return course;
  }

  /**
   * Récupère une liste de cours
   * @param filters Filtres à appliquer
   * @returns Liste de cours
   */
  async getCourses(filters?: Record<string, any>): Promise<any> {
    const courses = getMockDataByType(RecommendationType.COURSE);
    
    // Appliquer les filtres si nécessaire
    if (filters) {
      return this.applyFilters(courses, filters);
    }
    
    return courses;
  }

  /**
   * Récupère les détails d'un élément en fonction de son type
   * @param type Type d'élément
   * @param itemId ID de l'élément
   * @returns Détails de l'élément
   */
  async getItem(type: RecommendationType, itemId: string): Promise<any> {
    switch (type) {
      case RecommendationType.RETREAT:
        return this.getRetreat(itemId);
      case RecommendationType.PARTNER:
        return this.getPartner(itemId);
      case RecommendationType.COURSE:
        return this.getCourse(itemId);
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  }

  /**
   * Récupère une liste d'éléments en fonction de leur type
   * @param type Type d'élément
   * @param filters Filtres à appliquer
   * @returns Liste d'éléments
   */
  async getItems(type: RecommendationType, filters?: Record<string, any>): Promise<any> {
    switch (type) {
      case RecommendationType.RETREAT:
        return this.getRetreats(filters);
      case RecommendationType.PARTNER:
        return this.getPartners(filters);
      case RecommendationType.COURSE:
        return this.getCourses(filters);
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  }

  /**
   * Applique des filtres à une liste d'éléments
   * @param items Liste d'éléments
   * @param filters Filtres à appliquer
   * @returns Liste d'éléments filtrée
   */
  private applyFilters(items: any[], filters: Record<string, any>): any[] {
    return items.filter(item => {
      // Vérifier chaque filtre
      for (const [key, value] of Object.entries(filters)) {
        // Ignorer les filtres de pagination
        if (['limit', 'offset', 'page'].includes(key)) {
          continue;
        }
        
        // Vérifier si l'élément correspond au filtre
        if (item[key] !== undefined) {
          // Filtre sur un tableau
          if (Array.isArray(item[key])) {
            if (Array.isArray(value)) {
              // Vérifier si au moins une valeur correspond
              if (!value.some(v => item[key].includes(v))) {
                return false;
              }
            } else {
              // Vérifier si la valeur est dans le tableau
              if (!item[key].includes(value)) {
                return false;
              }
            }
          }
          // Filtre sur une valeur simple
          else if (item[key] !== value) {
            return false;
          }
        } else {
          return false;
        }
      }
      
      return true;
    });
  }
}
