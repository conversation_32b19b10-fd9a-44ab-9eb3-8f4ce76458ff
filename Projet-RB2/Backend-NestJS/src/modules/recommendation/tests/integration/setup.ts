/**
 * Configuration pour les tests d'intégration du système de recommandation
 */

import { Test } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import * as request from 'supertest';
import { PrismaService } from '../../../../prisma/prisma.service';
import { RecommendationModule } from '../../recommendation.module.refactored';
import { AuthModule } from '../../../auth/auth.module';
import { UsersModule } from '../../../users/users.module';

// Augmenter le timeout pour les tests
jest.setTimeout(30000);

// Supprimer les logs pendant les tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Variables globales pour les tests
let app: INestApplication;
let prisma: PrismaService;
let testUserToken: string;
let testAdminToken: string;

/**
 * Initialise l'application pour les tests d'intégration
 */
export async function initializeApp() {
  // Créer un module de test
  const moduleRef = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: '.env.test',
      }),
      JwtModule.registerAsync({
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => ({
          secret: configService.get<string>('JWT_SECRET') || 'test-secret',
          signOptions: {
            expiresIn: '1h',
          },
        }),
        inject: [ConfigService],
      }),
      RecommendationModule,
      AuthModule,
      UsersModule,
    ],
  }).compile();

  // Créer l'application
  app = moduleRef.createNestApplication();
  
  // Configurer l'application
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  
  // Initialiser l'application
  await app.init();
  
  // Récupérer le service Prisma
  prisma = moduleRef.get<PrismaService>(PrismaService);
  
  return {
    app,
    prisma,
    moduleRef,
  };
}

/**
 * Nettoie la base de données de test
 */
export async function cleanupDatabase(prisma: PrismaService) {
  // Supprimer les données de test dans l'ordre inverse des dépendances
  await prisma.userInteraction.deleteMany();
  await prisma.userPreferences.deleteMany();
  await prisma.user.deleteMany();
}

/**
 * Crée un utilisateur de test
 */
export async function createTestUser(prisma: PrismaService) {
  return await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // 'password'
      firstName: 'Test',
      lastName: 'User',
      role: 'USER',
    },
  });
}

/**
 * Crée un administrateur de test
 */
export async function createTestAdmin(prisma: PrismaService) {
  return await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // 'password'
      firstName: 'Test',
      lastName: 'Admin',
      role: 'ADMIN',
    },
  });
}

/**
 * Génère un token JWT pour un utilisateur
 */
export async function generateUserToken(app: INestApplication, email: string, password: string) {
  const response = await request(app.getHttpServer())
    .post('/api/auth/login')
    .send({ email, password });
  
  return response.body.access_token;
}

/**
 * Initialise les données de test pour les recommandations
 */
export async function initializeTestData(prisma: PrismaService, userId: string) {
  // Créer des préférences utilisateur
  await prisma.userPreferences.create({
    data: {
      userId,
      preferences: {
        preferredCategories: ['Yoga', 'Meditation'],
        preferredLevels: ['BEGINNER', 'INTERMEDIATE'],
        preferredLocations: ['France', 'Italy'],
      },
    },
  });
  
  // Créer des interactions utilisateur
  await prisma.userInteraction.createMany({
    data: [
      {
        userId,
        itemId: 'retreat-1',
        itemType: 'RETREAT',
        interactionType: 'VIEW',
        metadata: { duration: 120 },
      },
      {
        userId,
        itemId: 'retreat-2',
        itemType: 'RETREAT',
        interactionType: 'LIKE',
        metadata: {},
      },
      {
        userId,
        itemId: 'partner-1',
        itemType: 'PARTNER',
        interactionType: 'VIEW',
        metadata: { duration: 60 },
      },
    ],
  });
}

/**
 * Configuration globale avant tous les tests
 */
beforeAll(async () => {
  const { app: testApp, prisma: testPrisma } = await initializeApp();
  app = testApp;
  prisma = testPrisma;
  
  // Nettoyer la base de données
  await cleanupDatabase(prisma);
  
  // Créer des utilisateurs de test
  const testUser = await createTestUser(prisma);
  const testAdmin = await createTestAdmin(prisma);
  
  // Générer des tokens
  testUserToken = await generateUserToken(app, '<EMAIL>', 'password');
  testAdminToken = await generateUserToken(app, '<EMAIL>', 'password');
  
  // Initialiser les données de test
  await initializeTestData(prisma, testUser.id);
});

/**
 * Configuration globale après tous les tests
 */
afterAll(async () => {
  // Nettoyer la base de données
  await cleanupDatabase(prisma);
  
  // Fermer l'application
  await app.close();
});

/**
 * Exporte les variables globales pour les tests
 */
export {
  app,
  prisma,
  testUserToken,
  testAdminToken,
};
