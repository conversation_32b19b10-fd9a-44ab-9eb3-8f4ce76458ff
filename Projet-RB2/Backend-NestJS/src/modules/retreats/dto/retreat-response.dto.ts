import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../auth/decorators/roles.decorator';

class HostDto {
  @ApiProperty({ description: 'ID of the host', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'First name of the host', example: '<PERSON>' })
  firstName: string;

  @ApiProperty({ description: 'Last name of the host', example: 'Doe' })
  lastName: string;

  @ApiProperty({ description: 'Email of the host', example: '<EMAIL>' })
  email: string;

  @ApiPropertyOptional({ description: 'Profile picture of the host', example: '/uploads/users/123/profile.jpg' })
  image?: string;
}

export class RetreatResponseDto {
  @ApiProperty({ description: 'ID of the retreat', example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ description: 'Title of the retreat', example: 'Yoga Retreat in Bali' })
  title: string;

  @ApiProperty({ description: 'Description of the retreat', example: 'A relaxing yoga retreat in the heart of Bali' })
  description: string;

  @ApiProperty({ description: 'Location of the retreat', example: 'Ubud, Bali, Indonesia' })
  location: string;

  @ApiProperty({ description: 'Start date of the retreat', example: '2023-06-15T00:00:00.000Z' })
  startDate: Date;

  @ApiProperty({ description: 'End date of the retreat', example: '2023-06-22T00:00:00.000Z' })
  endDate: Date;

  @ApiProperty({ description: 'Price of the retreat', example: 1500 })
  price: number;

  @ApiProperty({ description: 'Maximum number of participants', example: 20 })
  capacity: number;

  @ApiProperty({
    description: 'Status of the retreat',
    enum: Status,
    example: Status.PUBLISHED,
  })
  status: Status;

  @ApiProperty({
    description: 'Categories of the retreat',
    type: [String],
    example: ['yoga', 'meditation', 'wellness'],
  })
  categories: string[];

  @ApiProperty({
    description: 'Amenities offered during the retreat',
    type: [String],
    example: ['wifi', 'pool', 'spa', 'organic meals'],
  })
  amenities: string[];

  @ApiProperty({
    description: 'Images of the retreat',
    type: [String],
    example: ['/uploads/retreats/1/image1.jpg', '/uploads/retreats/1/image2.jpg'],
  })
  images: string[];

  @ApiProperty({ description: 'Host of the retreat', type: HostDto })
  host: HostDto;

  @ApiProperty({ description: 'Creation date of the retreat', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date of the retreat', example: '2023-01-02T00:00:00.000Z' })
  updatedAt: Date;
}
