import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsPositive,
  IsDateString,
  IsEnum,
  IsOptional,
  IsArray,
  Min,
  Max,
  ArrayMinSize,
  ArrayMaxSize,
} from 'class-validator';
import { Status } from '../../auth/decorators/roles.decorator';

export class UpdateRetreatDto {
  @ApiPropertyOptional({ description: 'Title of the retreat', example: 'Yoga Retreat in Bali' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ description: 'Description of the retreat', example: 'A relaxing yoga retreat in the heart of Bali' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: 'Location of the retreat', example: 'Ubud, Bali, Indonesia' })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiPropertyOptional({ description: 'Start date of the retreat', example: '2023-06-15T00:00:00.000Z' })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({ description: 'End date of the retreat', example: '2023-06-22T00:00:00.000Z' })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Price of the retreat', example: 1500 })
  @IsNumber()
  @IsPositive()
  @Min(0)
  @IsOptional()
  price?: number;

  @ApiPropertyOptional({ description: 'Maximum number of participants', example: 20 })
  @IsNumber()
  @IsPositive()
  @Min(1)
  @Max(1000)
  @IsOptional()
  capacity?: number;

  @ApiPropertyOptional({
    description: 'Status of the retreat',
    enum: Status,
    example: Status.PUBLISHED,
  })
  @IsEnum(Status)
  @IsOptional()
  status?: Status;

  @ApiPropertyOptional({
    description: 'Categories of the retreat',
    type: [String],
    example: ['yoga', 'meditation', 'wellness'],
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  @ArrayMaxSize(10)
  @IsOptional()
  categories?: string[];

  @ApiPropertyOptional({
    description: 'Amenities offered during the retreat',
    type: [String],
    example: ['wifi', 'pool', 'spa', 'organic meals'],
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  @ArrayMaxSize(20)
  @IsOptional()
  amenities?: string[];

  @ApiPropertyOptional({
    description: 'Images of the retreat',
    type: [String],
    example: ['/uploads/retreats/1/image1.jpg', '/uploads/retreats/1/image2.jpg'],
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(0)
  @ArrayMaxSize(10)
  @IsOptional()
  images?: string[];
}
