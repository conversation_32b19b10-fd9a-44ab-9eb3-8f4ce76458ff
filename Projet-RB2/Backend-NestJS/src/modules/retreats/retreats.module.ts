import { Modu<PERSON> } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RetreatsController } from './retreats.controller';
import { RetreatsService } from './retreats.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { diskStorage } from 'multer';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as fs from 'fs';

@Module({
  imports: [
    PrismaModule,
    MulterModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const uploadDir = configService.get<string>('UPLOAD_DIR') || './uploads';
        
        // Ensure upload directory exists
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        
        return {
          storage: diskStorage({
            destination: (req, file, cb) => {
              const retreatId = req.params.id;
              const retreatDir = path.join(uploadDir, 'retreats', retreatId);
              
              // Create retreat directory if it doesn't exist
              if (!fs.existsSync(retreatDir)) {
                fs.mkdirSync(retreatDir, { recursive: true });
              }
              
              cb(null, retreatDir);
            },
            filename: (req, file, cb) => {
              const fileExtension = path.extname(file.originalname);
              const fileName = `${uuidv4()}${fileExtension}`;
              cb(null, fileName);
            },
          }),
          limits: {
            fileSize: configService.get<number>('MAX_FILE_SIZE') || 5 * 1024 * 1024, // 5MB
          },
          fileFilter: (req, file, cb) => {
            // Accept only images
            if (!file.mimetype.match(/^image\/(jpeg|png|gif|webp)$/)) {
              return cb(new Error('Only image files are allowed'), false);
            }
            cb(null, true);
          },
        };
      },
    }),
  ],
  controllers: [RetreatsController],
  providers: [RetreatsService],
  exports: [RetreatsService],
})
export class RetreatsModule {}
