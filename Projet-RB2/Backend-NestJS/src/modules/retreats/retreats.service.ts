import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateRetreatDto } from './dto/create-retreat.dto';
import { UpdateRetreatDto } from './dto/update-retreat.dto';
import { RetreatResponseDto } from './dto/retreat-response.dto';
import { UserRole, Status } from '../auth/decorators/roles.decorator';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RetreatsService {
  private readonly logger = new Logger(RetreatsService.name);
  private readonly uploadDir: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR') || './uploads';
    // Ensure upload directory exists
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
  }

  async create(createRetreatDto: CreateRetreatDto, userId: string): Promise<RetreatResponseDto> {
    try {
      // Check if user exists and has appropriate role
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (![UserRole.ADMIN, UserRole.HOST, UserRole.ORGANIZER].includes(user.role as UserRole)) {
        throw new ForbiddenException('You do not have permission to create retreats');
      }

      // Create retreat
      const retreat = await this.prisma.retreat.create({
        data: {
          title: createRetreatDto.title,
          description: createRetreatDto.description,
          location: createRetreatDto.location,
          startDate: new Date(createRetreatDto.startDate),
          endDate: new Date(createRetreatDto.endDate),
          price: createRetreatDto.price,
          capacity: createRetreatDto.capacity,
          status: createRetreatDto.status || Status.DRAFT,
          categories: createRetreatDto.categories || [],
          amenities: createRetreatDto.amenities || [],
          images: createRetreatDto.images || [],
          host: {
            connect: { id: userId },
          },
        },
      });

      this.logger.log(`Retreat created successfully: ${retreat.id}`);
      return retreat;
    } catch (error) {
      this.logger.error(`Error creating retreat: ${error.message}`);
      throw error;
    }
  }

  async findAll(params: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    startDate?: string;
    endDate?: string;
    minPrice?: number;
    maxPrice?: number;
    location?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ data: RetreatResponseDto[]; total: number; page: number; limit: number }> {
    const {
      page = 1,
      limit = 10,
      search,
      category,
      startDate,
      endDate,
      minPrice,
      maxPrice,
      location,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = params;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      status: Status.PUBLISHED,
    };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { location: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) {
      where.categories = {
        has: category,
      };
    }

    if (startDate) {
      where.startDate = {
        gte: new Date(startDate),
      };
    }

    if (endDate) {
      where.endDate = {
        lte: new Date(endDate),
      };
    }

    if (minPrice !== undefined) {
      where.price = {
        ...where.price,
        gte: minPrice,
      };
    }

    if (maxPrice !== undefined) {
      where.price = {
        ...where.price,
        lte: maxPrice,
      };
    }

    if (location) {
      where.location = {
        contains: location,
        mode: 'insensitive',
      };
    }

    // Build orderBy
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    try {
      const [retreats, total] = await Promise.all([
        this.prisma.retreat.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            host: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                image: true,
              },
            },
          },
        }),
        this.prisma.retreat.count({ where }),
      ]);

      return {
        data: retreats,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error finding retreats: ${error.message}`);
      throw error;
    }
  }

  async findOne(id: string): Promise<RetreatResponseDto> {
    try {
      const retreat = await this.prisma.retreat.findUnique({
        where: { id },
        include: {
          host: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new NotFoundException(`Retreat with ID ${id} not found`);
      }

      return retreat;
    } catch (error) {
      this.logger.error(`Error finding retreat: ${error.message}`);
      throw error;
    }
  }

  async update(id: string, updateRetreatDto: UpdateRetreatDto, userId: string): Promise<RetreatResponseDto> {
    try {
      // Check if retreat exists
      const retreat = await this.prisma.retreat.findUnique({
        where: { id },
        include: {
          host: {
            select: {
              id: true,
              role: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new NotFoundException(`Retreat with ID ${id} not found`);
      }

      // Check if user has permission to update
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true },
      });

      if (
        user.role !== UserRole.ADMIN &&
        retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to update this retreat');
      }

      // Update retreat
      const updatedRetreat = await this.prisma.retreat.update({
        where: { id },
        data: {
          ...(updateRetreatDto.title && { title: updateRetreatDto.title }),
          ...(updateRetreatDto.description && { description: updateRetreatDto.description }),
          ...(updateRetreatDto.location && { location: updateRetreatDto.location }),
          ...(updateRetreatDto.startDate && { startDate: new Date(updateRetreatDto.startDate) }),
          ...(updateRetreatDto.endDate && { endDate: new Date(updateRetreatDto.endDate) }),
          ...(updateRetreatDto.price !== undefined && { price: updateRetreatDto.price }),
          ...(updateRetreatDto.capacity !== undefined && { capacity: updateRetreatDto.capacity }),
          ...(updateRetreatDto.status && { status: updateRetreatDto.status }),
          ...(updateRetreatDto.categories && { categories: updateRetreatDto.categories }),
          ...(updateRetreatDto.amenities && { amenities: updateRetreatDto.amenities }),
          ...(updateRetreatDto.images && { images: updateRetreatDto.images }),
        },
        include: {
          host: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });

      this.logger.log(`Retreat updated successfully: ${id}`);
      return updatedRetreat;
    } catch (error) {
      this.logger.error(`Error updating retreat: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string, userId: string): Promise<void> {
    try {
      // Check if retreat exists
      const retreat = await this.prisma.retreat.findUnique({
        where: { id },
        include: {
          host: {
            select: {
              id: true,
              role: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new NotFoundException(`Retreat with ID ${id} not found`);
      }

      // Check if user has permission to delete
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true },
      });

      if (
        user.role !== UserRole.ADMIN &&
        retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to delete this retreat');
      }

      // Delete retreat
      await this.prisma.retreat.delete({
        where: { id },
      });

      this.logger.log(`Retreat deleted successfully: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting retreat: ${error.message}`);
      throw error;
    }
  }

  async getFeatured(): Promise<RetreatResponseDto[]> {
    try {
      return this.prisma.retreat.findMany({
        where: {
          status: Status.PUBLISHED,
          // Add any other criteria for featured retreats
        },
        take: 6,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          host: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(`Error getting featured retreats: ${error.message}`);
      throw error;
    }
  }

  async getUpcoming(): Promise<RetreatResponseDto[]> {
    try {
      return this.prisma.retreat.findMany({
        where: {
          status: Status.PUBLISHED,
          startDate: {
            gte: new Date(),
          },
        },
        take: 10,
        orderBy: {
          startDate: 'asc',
        },
        include: {
          host: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(`Error getting upcoming retreats: ${error.message}`);
      throw error;
    }
  }

  async getCategories(): Promise<string[]> {
    try {
      const retreats = await this.prisma.retreat.findMany({
        where: {
          status: Status.PUBLISHED,
        },
        select: {
          categories: true,
        },
      });

      // Extract unique categories
      const categoriesSet = new Set<string>();
      retreats.forEach((retreat) => {
        retreat.categories.forEach((category) => {
          categoriesSet.add(category);
        });
      });

      return Array.from(categoriesSet);
    } catch (error) {
      this.logger.error(`Error getting retreat categories: ${error.message}`);
      throw error;
    }
  }

  async uploadImages(id: string, files: Array<Express.Multer.File>, userId: string): Promise<{ urls: string[] }> {
    try {
      // Check if retreat exists
      const retreat = await this.prisma.retreat.findUnique({
        where: { id },
        include: {
          host: {
            select: {
              id: true,
              role: true,
            },
          },
        },
      });

      if (!retreat) {
        throw new NotFoundException(`Retreat with ID ${id} not found`);
      }

      // Check if user has permission to upload images
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, role: true },
      });

      if (
        user.role !== UserRole.ADMIN &&
        retreat.host.id !== userId
      ) {
        throw new ForbiddenException('You do not have permission to upload images for this retreat');
      }

      if (!files || files.length === 0) {
        throw new BadRequestException('No files uploaded');
      }

      // Create retreat images directory if it doesn't exist
      const retreatImagesDir = path.join(this.uploadDir, 'retreats', id);
      if (!fs.existsSync(retreatImagesDir)) {
        fs.mkdirSync(retreatImagesDir, { recursive: true });
      }

      // Save files and get URLs
      const urls: string[] = [];
      for (const file of files) {
        const fileExtension = path.extname(file.originalname);
        const fileName = `${uuidv4()}${fileExtension}`;
        const filePath = path.join(retreatImagesDir, fileName);

        // Write file to disk
        fs.writeFileSync(filePath, file.buffer);

        // Generate URL
        const fileUrl = `/uploads/retreats/${id}/${fileName}`;
        urls.push(fileUrl);
      }

      // Update retreat with new image URLs
      await this.prisma.retreat.update({
        where: { id },
        data: {
          images: {
            push: urls,
          },
        },
      });

      return { urls };
    } catch (error) {
      this.logger.error(`Error uploading retreat images: ${error.message}`);
      throw error;
    }
  }

  async getReviews(id: string): Promise<any[]> {
    try {
      // Check if retreat exists
      const retreat = await this.prisma.retreat.findUnique({
        where: { id },
      });

      if (!retreat) {
        throw new NotFoundException(`Retreat with ID ${id} not found`);
      }

      // Get reviews for the retreat
      const reviews = await this.prisma.retreatReview.findMany({
        where: {
          retreatId: id,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              image: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return reviews;
    } catch (error) {
      this.logger.error(`Error getting retreat reviews: ${error.message}`);
      throw error;
    }
  }
}
