# Service de Sécurité pour les Microservices

Ce document décrit les améliorations apportées au `MicroserviceSecurityService` pour renforcer la sécurité des communications entre microservices.

## Fonctionnalités implémentées

### 1. Validation avancée des certificats

Le service offre désormais une validation complète des certificats mTLS avec les vérifications suivantes :
- Validation de la chaîne de confiance
- Vérification des dates de validité
- Vérification de la révocation via CRL
- Validation des empreintes de certificats
- Vérification de la signature

### 2. Rotation automatique des clés

Le service implémente désormais une rotation automatique des clés avec :
- Rotation programmée via CRON (hebdomadaire par défaut)
- Rotation manuelle via API
- Conservation des anciennes clés pendant une période de transition
- Gestion des versions de clés
- Journalisation des événements de rotation

### 3. Journalisation sécurisée

Toutes les opérations de sécurité sont désormais journalisées avec :
- Horodatage précis
- Détails de l'opération
- Informations sur les versions de clés
- Préparation pour l'intégration avec un service de journalisation sécurisé

## API exposées

Le service expose les endpoints suivants via le `MicroserviceSecurityController` :

### Vérification de l'état de santé
```
GET /api/security/microservice/health
```
Retourne l'état de santé du service, y compris la validité des certificats et l'état de la rotation des clés.

### Rotation des clés
```
POST /api/security/microservice/keys/rotate
```
Force la rotation des clés de service. Nécessite le rôle 'admin'.

### Récupération de la clé publique
```
GET /api/security/microservice/keys/public
```
Récupère la clé publique du service et sa version.

### Validation des certificats
```
POST /api/security/microservice/certificates/validate
```
Valide les certificats du service. Nécessite le rôle 'admin'.

## Configuration

Le service peut être configuré via les variables d'environnement suivantes :

| Variable | Description | Valeur par défaut |
|----------|-------------|------------------|
| `ZERO_TRUST_MTLS_ENABLED` | Active/désactive mTLS | `false` |
| `E2E_ENCRYPTION_ENABLED` | Active/désactive le chiffrement de bout en bout | `true` |
| `MTLS_CERT_PATH` | Chemin du certificat client | `./certs/client.crt` |
| `MTLS_KEY_PATH` | Chemin de la clé privée client | `./certs/client.key` |
| `MTLS_CA_PATH` | Chemin du certificat CA | `./certs/ca.crt` |
| `MTLS_CRL_PATH` | Chemin de la liste de révocation | `./certs/ca.crl` |
| `MTLS_REJECT_UNAUTHORIZED` | Rejette les connexions non autorisées | `true` |
| `AUTO_KEY_ROTATION` | Active/désactive la rotation automatique des clés | `true` |
| `KEY_ROTATION_INTERVAL` | Intervalle de rotation des clés (ms) | `604800000` (7 jours) |
| `CERT_VALIDATION_INTERVAL` | Intervalle de validation des certificats (ms) | `86400000` (1 jour) |

## Utilisation

Pour utiliser le service dans un autre module :

```typescript
import { MicroserviceSecurityService } from '../security/services/microservice-security.service';

@Injectable()
export class MonService {
  constructor(private readonly microserviceSecurityService: MicroserviceSecurityService) {}

  async appelSecurise() {
    // Appel sécurisé à un autre microservice
    const resultat = await this.microserviceSecurityService.get(
      'https://autre-service.retreatandbe.internal',
      '/api/ressource',
    );
    return resultat;
  }
}
```

## Sécurité

Ce service implémente les meilleures pratiques de sécurité pour les communications entre microservices :
- Authentification mutuelle via mTLS
- Chiffrement de bout en bout des données
- Rotation régulière des clés cryptographiques
- Validation complète des certificats
- Journalisation sécurisée des événements

## Maintenance

Pour maintenir ce service :
1. Vérifier régulièrement les journaux pour détecter les problèmes
2. Surveiller l'état de santé via l'endpoint `/api/security/microservice/health`
3. Mettre à jour les certificats CA et les listes de révocation
4. Vérifier que la rotation automatique des clés fonctionne correctement
