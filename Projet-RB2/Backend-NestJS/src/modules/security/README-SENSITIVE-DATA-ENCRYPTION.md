# Service de Chiffrement des Données Sensibles

Ce document décrit le service de chiffrement des données sensibles au repos implémenté dans le module de sécurité.

## Fonctionnalités

Le service `SensitiveDataEncryptionService` offre les fonctionnalités suivantes :

1. **Identification automatique des données sensibles** :
   - Détection basée sur les noms de champs
   - Détection basée sur des patterns (expressions régulières)
   - Support pour différents types de données sensibles (financières, personnelles, santé, etc.)

2. **Chiffrement des données sensibles** :
   - Chiffrement complet d'objets
   - Chiffrement sélectif des champs sensibles uniquement
   - Chiffrement de fichiers contenant des données sensibles

3. **Gestion des clés** :
   - Intégration avec le service de gestion des clés
   - Rotation automatique des clés
   - Versionnement des clés

4. **API REST** :
   - Endpoints pour le chiffrement/déchiffrement
   - Endpoints pour l'identification des données sensibles
   - Endpoints pour la gestion des configurations

## Types de données sensibles supportés

Le service prend en charge les types de données sensibles suivants :

- **Financières** : numéros de carte de crédit, comptes bancaires, IBAN, etc.
- **Personnelles** : noms, adresses, numéros de sécurité sociale, passeports, etc.
- **Santé** : dossiers médicaux, identifiants de santé, données biométriques, etc.
- **Authentification** : mots de passe, clés privées, tokens, etc.
- **Localisation** : coordonnées GPS, adresses IP, etc.

## Patterns de détection

Le service utilise les expressions régulières suivantes pour détecter automatiquement les données sensibles :

- **Cartes de crédit** : `\b(?:\d[ -]*?){13,16}\b`
- **Numéros de sécurité sociale** : `\b\d{3}[-\s]?\d{2}[-\s]?\d{4}\b`
- **Emails** : `\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b`
- **Numéros de téléphone** : `\b(?:\+\d{1,3}[-\s]?)?\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}\b`
- **Adresses IP** : `\b(?:\d{1,3}\.){3}\d{1,3}\b`
- **Mots de passe** : `\b(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}\b`

## Configuration

Le service peut être configuré via les variables d'environnement suivantes :

| Variable | Description | Valeur par défaut |
|----------|-------------|------------------|
| `SENSITIVE_DATA_ENCRYPTION_ENABLED` | Active/désactive le chiffrement des données sensibles | `true` |
| `SENSITIVE_DATA_ENCRYPTION_ALGORITHM` | Algorithme de chiffrement | `aes-256-gcm` |
| `SENSITIVE_DATA_FIELDS` | Liste des noms de champs sensibles | Liste prédéfinie |
| `SENSITIVE_DATA_TYPES` | Types de données sensibles à surveiller | `financial,personal,health,authentication,location` |

## Utilisation

### Chiffrement d'un objet complet

```typescript
import { SensitiveDataEncryptionService } from '../security/services/sensitive-data-encryption.service';

@Injectable()
export class MonService {
  constructor(private readonly sensitiveDataEncryptionService: SensitiveDataEncryptionService) {}

  async protegerDonnees(data: any) {
    const encryptedData = await this.sensitiveDataEncryptionService.encryptSensitiveData(data, 'financial');
    return encryptedData;
  }
}
```

### Chiffrement sélectif des champs sensibles

```typescript
async sauvegarderUtilisateur(user: User) {
  // Chiffrer uniquement les champs sensibles
  const userWithEncryptedFields = await this.sensitiveDataEncryptionService.encryptSensitiveFields(user);
  
  // Sauvegarder l'utilisateur avec les champs sensibles chiffrés
  return this.userRepository.save(userWithEncryptedFields);
}
```

### Identification des champs sensibles

```typescript
identifierChampsSensibles(data: any) {
  const sensitiveFields = this.sensitiveDataEncryptionService.identifySensitiveFields(data);
  return sensitiveFields;
}
```

## Sécurité

Ce service implémente les meilleures pratiques de sécurité pour la protection des données sensibles :

- Utilisation d'algorithmes de chiffrement robustes (AES-256-GCM par défaut)
- Intégration avec le service de gestion des clés pour la rotation automatique
- Versionnement des clés pour assurer la compatibilité
- Journalisation sécurisée des opérations de chiffrement/déchiffrement
- Détection automatique des données sensibles

## Intégration avec d'autres services

Le service de chiffrement des données sensibles s'intègre avec les services suivants :

- **KeyManagementService** : pour la gestion des clés de chiffrement
- **VaultService** : pour le stockage sécurisé des clés
- **CryptoLoggingService** : pour la journalisation des opérations cryptographiques

## Endpoints API

Le service expose les endpoints suivants via le `SensitiveDataEncryptionController` :

- `POST /api/security/sensitive-data/encrypt` : Chiffre des données sensibles
- `POST /api/security/sensitive-data/decrypt` : Déchiffre des données sensibles
- `POST /api/security/sensitive-data/encrypt-fields` : Chiffre les champs sensibles d'un objet
- `POST /api/security/sensitive-data/decrypt-fields` : Déchiffre les champs sensibles d'un objet
- `POST /api/security/sensitive-data/identify-fields` : Identifie les champs sensibles dans un objet
- `GET /api/security/sensitive-data/sensitive-fields` : Obtient la liste des champs sensibles configurés
- `GET /api/security/sensitive-data/sensitive-types` : Obtient la liste des types de données sensibles configurés
- `POST /api/security/sensitive-data/add-sensitive-field` : Ajoute un champ sensible à la configuration
