import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query, 
  UseGuards, 
  Res,
  HttpStatus,
  HttpException
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ComplianceService, ComplianceFramework } from '../services/compliance.service';
import * as fs from 'fs';
import * as path from 'path';

interface GenerateReportDto {
  framework: ComplianceFramework;
  format?: 'json' | 'html' | 'pdf';
}

interface PrepareDocumentationDto {
  framework: ComplianceFramework;
  title: string;
  description: string;
}

@ApiTags('compliance')
@Controller('api/security/compliance')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ComplianceController {
  constructor(private readonly complianceService: ComplianceService) {}

  /**
   * Get all available compliance frameworks
   */
  @Get('frameworks')
  @ApiOperation({ summary: 'Get all available compliance frameworks' })
  @ApiResponse({ status: 200, description: 'List of compliance frameworks' })
  getFrameworks() {
    return Object.values(ComplianceFramework);
  }

  /**
   * Run compliance checks for a specific framework
   */
  @Post('check/:framework')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Run compliance checks for a specific framework' })
  @ApiResponse({ status: 200, description: 'Compliance check results' })
  async runComplianceChecks(@Param('framework') framework: ComplianceFramework) {
    try {
      const report = await this.complianceService.runComplianceChecks(framework);
      return {
        statusCode: HttpStatus.OK,
        data: report,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to run compliance checks',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Run compliance checks for all frameworks
   */
  @Post('check/all')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Run compliance checks for all frameworks' })
  @ApiResponse({ status: 200, description: 'Compliance check results for all frameworks' })
  async runAllComplianceChecks() {
    try {
      const reports = await this.complianceService.runAllComplianceChecks();
      return {
        statusCode: HttpStatus.OK,
        data: Array.from(reports.entries()).map(([framework, report]) => ({
          framework,
          report,
        })),
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to run compliance checks',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Generate a compliance report
   */
  @Post('reports/generate')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Generate a compliance report' })
  @ApiResponse({ status: 200, description: 'Generated report information' })
  @ApiBody({ type: Object, description: 'Report generation options' })
  async generateReport(@Body() dto: GenerateReportDto) {
    try {
      // Run compliance checks to get latest data
      const report = await this.complianceService.runComplianceChecks(dto.framework);
      
      // Generate report in requested format
      const reportPath = await this.complianceService.generateComplianceReport(
        report.id,
        dto.format || 'json',
      );
      
      return {
        statusCode: HttpStatus.OK,
        data: {
          reportId: report.id,
          framework: dto.framework,
          format: dto.format || 'json',
          path: reportPath,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to generate compliance report',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Download a compliance report
   */
  @Get('reports/download/:reportId')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Download a compliance report' })
  @ApiResponse({ status: 200, description: 'Report file' })
  async downloadReport(
    @Param('reportId') reportId: string,
    @Query('format') format: 'json' | 'html' | 'pdf' = 'json',
    @Res() res: Response,
  ) {
    try {
      const reportPath = await this.complianceService.generateComplianceReport(reportId, format);
      
      if (!fs.existsSync(reportPath)) {
        throw new Error(`Report not found: ${reportPath}`);
      }
      
      const filename = path.basename(reportPath);
      
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.setHeader('Content-Type', this.getContentType(format));
      
      const fileStream = fs.createReadStream(reportPath);
      fileStream.pipe(res);
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Report not found',
          error: error.message,
        },
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Prepare documentation for external audits
   */
  @Post('documentation/prepare')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Prepare documentation for external audits' })
  @ApiResponse({ status: 200, description: 'Generated documentation information' })
  @ApiBody({ type: Object, description: 'Documentation preparation options' })
  async prepareAuditDocumentation(@Body() dto: PrepareDocumentationDto) {
    try {
      const documentation = await this.complianceService.prepareAuditDocumentation(
        dto.framework,
        dto.title,
        dto.description,
      );
      
      return {
        statusCode: HttpStatus.OK,
        data: documentation,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to prepare audit documentation',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Download audit documentation
   */
  @Get('documentation/download/:docId')
  @Roles('admin', 'security')
  @ApiOperation({ summary: 'Download audit documentation' })
  @ApiResponse({ status: 200, description: 'Documentation file' })
  async downloadDocumentation(
    @Param('docId') docId: string,
    @Res() res: Response,
  ) {
    try {
      const docsDir = path.join(process.cwd(), 'compliance-docs');
      const docPath = path.join(docsDir, `${docId}.md`);
      
      if (!fs.existsSync(docPath)) {
        throw new Error(`Documentation not found: ${docPath}`);
      }
      
      const filename = path.basename(docPath);
      
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.setHeader('Content-Type', 'text/markdown');
      
      const fileStream = fs.createReadStream(docPath);
      fileStream.pipe(res);
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Documentation not found',
          error: error.message,
        },
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Get content type based on format
   */
  private getContentType(format: string): string {
    switch (format) {
      case 'json':
        return 'application/json';
      case 'html':
        return 'text/html';
      case 'pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }
}
