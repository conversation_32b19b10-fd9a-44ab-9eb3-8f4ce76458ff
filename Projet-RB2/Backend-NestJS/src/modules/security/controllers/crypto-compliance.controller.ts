import { Controller, Get, Post, Body } from '@nestjs/common';
import { CryptoComplianceService, ComplianceReport, ComplianceRequirement } from '../services/crypto-compliance.service';

import { Res } from '@nestjs/common';
import { Response } from 'express';

@Controller('crypto-compliance')
export class CryptoComplianceController {
  constructor(private readonly complianceService: CryptoComplianceService) {}

  /**
   * Endpoint pour exécuter tous les contrôles de conformité et obtenir un rapport
   */
  @Get('report')
  async getComplianceReport(): Promise<ComplianceReport> {
    return this.complianceService.runComplianceChecks();
  }

  /**
   * Endpoint pour obtenir le rapport de conformité au format Markdown
   */
  @Get('report/markdown')
  async getMarkdownReport(@Res() res: Response) {
    const md = await this.complianceService.generateMarkdownReport();
    res.setHeader('Content-Type', 'text/markdown');
    res.send(md);
  }

  /**
   * Endpoint pour lister toutes les exigences de conformité
   */
  @Get('requirements')
  getRequirements(): ComplianceRequirement[] {
    return this.complianceService.getRequirements();
  }

  /**
   * Endpoint pour ajouter dynamiquement une exigence de conformité personnalisée
   * (exemple d'usage avancé)
   */
  @Post('requirements')
  addRequirement(@Body() requirement: ComplianceRequirement) {
    this.complianceService.addRequirement(requirement);
    return { status: 'ok' };
  }
}
