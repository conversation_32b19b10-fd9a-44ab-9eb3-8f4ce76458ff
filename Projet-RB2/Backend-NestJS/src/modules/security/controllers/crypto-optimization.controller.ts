import { <PERSON>, Get, Post, Body, Param, UseGuards, Logger, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CryptoProfilingService } from '../services/crypto-profiling.service';
import { CryptoCacheService } from '../services/crypto-cache.service';
import { CryptoOptimizationService, OptimizationParams } from '../services/crypto-optimization.service';

@ApiTags('crypto-optimization')
@Controller('security/crypto-optimization')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CryptoOptimizationController {
  private readonly logger = new Logger(CryptoOptimizationController.name);

  constructor(
    private readonly profilingService: CryptoProfilingService,
    private readonly cacheService: CryptoCacheService,
    private readonly optimizationService: CryptoOptimizationService,
  ) {}

  @Get('metrics')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Get performance metrics' })
  @ApiResponse({ status: 200, description: 'Return performance metrics' })
  getMetrics(@Query('operationType') operationType?: string): any {
    this.logger.log(`Getting performance metrics for ${operationType || 'all operations'}`);
    return {
      metrics: this.profilingService.getMetrics(operationType),
      statistics: this.profilingService.getStatistics(operationType),
    };
  }

  @Get('metrics/export')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Export performance metrics' })
  @ApiResponse({ status: 200, description: 'Return performance metrics in CSV format' })
  exportMetrics(@Query('format') format: 'json' | 'csv' = 'json'): string {
    this.logger.log(`Exporting performance metrics in ${format} format`);
    return format === 'csv'
      ? this.profilingService.exportMetricsCsv()
      : this.profilingService.exportMetricsJson();
  }

  @Post('metrics/clear')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Clear performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics cleared' })
  clearMetrics(): { success: boolean; message: string } {
    this.logger.log('Clearing performance metrics');
    this.profilingService.clearMetrics();
    return { success: true, message: 'Performance metrics cleared' };
  }

  @Get('cache/stats')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Get cache statistics' })
  @ApiResponse({ status: 200, description: 'Return cache statistics' })
  getCacheStats(): any {
    this.logger.log('Getting cache statistics');
    return this.cacheService.getStats();
  }

  @Get('cache/entries')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Get cache entries' })
  @ApiResponse({ status: 200, description: 'Return cache entries' })
  getCacheEntries(): any {
    this.logger.log('Getting cache entries');
    return this.cacheService.getEntries();
  }

  @Post('cache/clear')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Clear cache' })
  @ApiResponse({ status: 200, description: 'Cache cleared' })
  clearCache(): { success: boolean; message: string } {
    this.logger.log('Clearing cache');
    this.cacheService.clear();
    return { success: true, message: 'Cache cleared' };
  }

  @Get('params')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Get optimization parameters' })
  @ApiResponse({ status: 200, description: 'Return optimization parameters' })
  getParams(@Query('operationType') operationType?: string): any {
    if (operationType) {
      this.logger.log(`Getting optimization parameters for ${operationType}`);
      return this.optimizationService.getParams(operationType);
    } else {
      this.logger.log('Getting all optimization parameters');
      return this.optimizationService.getAllParams();
    }
  }

  @Post('params/:operationType')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Set optimization parameters' })
  @ApiParam({ name: 'operationType', description: 'Operation type' })
  @ApiResponse({ status: 200, description: 'Optimization parameters set' })
  setParams(
    @Param('operationType') operationType: string,
    @Body() params: OptimizationParams,
  ): { success: boolean; message: string } {
    this.logger.log(`Setting optimization parameters for ${operationType}`);
    this.optimizationService.setParams(operationType, params);
    return { success: true, message: `Optimization parameters set for ${operationType}` };
  }

  @Post('params/:operationType/reset')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Reset optimization parameters' })
  @ApiParam({ name: 'operationType', description: 'Operation type' })
  @ApiResponse({ status: 200, description: 'Optimization parameters reset' })
  resetParams(
    @Param('operationType') operationType: string,
  ): { success: boolean; message: string } {
    this.logger.log(`Resetting optimization parameters for ${operationType}`);
    this.optimizationService.resetParams(operationType);
    return { success: true, message: `Optimization parameters reset for ${operationType}` };
  }

  @Post('optimize/:operationType')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Optimize parameters for an operation type' })
  @ApiParam({ name: 'operationType', description: 'Operation type' })
  @ApiResponse({ status: 200, description: 'Parameters optimized' })
  optimize(
    @Param('operationType') operationType: string,
  ): { success: boolean; message: string } {
    this.logger.log(`Optimizing parameters for ${operationType}`);
    this.optimizationService.optimizeParams(operationType);
    return { success: true, message: `Parameters optimized for ${operationType}` };
  }

  @Post('optimize/auto')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Auto-optimize all parameters' })
  @ApiResponse({ status: 200, description: 'All parameters auto-optimized' })
  autoOptimize(): { success: boolean; message: string } {
    this.logger.log('Auto-optimizing all parameters');
    this.optimizationService.autoOptimize();
    return { success: true, message: 'All parameters auto-optimized' };
  }

  @Get('report')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Generate optimization report' })
  @ApiResponse({ status: 200, description: 'Return optimization report' })
  generateReport(): any {
    this.logger.log('Generating optimization report');
    return this.optimizationService.generateReport();
  }

  @Get('profiling/report')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Generate profiling report' })
  @ApiResponse({ status: 200, description: 'Return profiling report' })
  generateProfilingReport(): any {
    this.logger.log('Generating profiling report');
    return this.profilingService.generateReport();
  }

  @Post('profiling/sampling-rate')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Set profiling sampling rate' })
  @ApiResponse({ status: 200, description: 'Profiling sampling rate set' })
  setSamplingRate(
    @Body('rate') rate: number,
  ): { success: boolean; message: string } {
    this.logger.log(`Setting profiling sampling rate to ${rate}`);
    this.profilingService.setSamplingRate(rate);
    return { success: true, message: `Profiling sampling rate set to ${rate}` };
  }

  @Post('profiling/threshold')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Set profiling threshold' })
  @ApiResponse({ status: 200, description: 'Profiling threshold set' })
  setThreshold(
    @Body('operationType') operationType: string,
    @Body('threshold') threshold: number,
  ): { success: boolean; message: string } {
    this.logger.log(`Setting profiling threshold for ${operationType} to ${threshold}ms`);
    this.profilingService.setThreshold(operationType, threshold);
    return { success: true, message: `Profiling threshold for ${operationType} set to ${threshold}ms` };
  }
}
