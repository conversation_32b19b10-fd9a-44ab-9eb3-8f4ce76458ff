import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CryptoTestingService } from '../services/crypto-testing.service';
import { CryptoPerformanceService } from '../services/crypto-performance.service';
import { CryptoLoggingService } from '../services/crypto-logging.service';

@ApiTags('crypto-testing')
@Controller('crypto-testing')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CryptoTestingController {
  constructor(
    private readonly cryptoTestingService: CryptoTestingService,
    private readonly cryptoPerformanceService: CryptoPerformanceService,
    private readonly cryptoLoggingService: CryptoLoggingService,
  ) {}

  @Get('run-tests')
  @Roles('admin')
  @ApiOperation({ summary: 'Exécute tous les tests cryptographiques' })
  @ApiResponse({ status: 200, description: 'Tests exécutés avec succès' })
  async runTests() {
    try {
      const report = await this.cryptoTestingService.runAllTests();
      return {
        statusCode: HttpStatus.OK,
        data: report,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to run tests',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('run-performance-tests')
  @Roles('admin')
  @ApiOperation({ summary: 'Exécute des tests de performance cryptographique' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        iterations: { type: 'number', default: 100 },
        dataSize: { type: 'number', default: 1024 },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Tests de performance exécutés avec succès' })
  async runPerformanceTests(
    @Body() body: { iterations?: number; dataSize?: number },
  ) {
    try {
      const report = await this.cryptoTestingService.runPerformanceTests(
        body.iterations,
        body.dataSize,
      );
      return {
        statusCode: HttpStatus.OK,
        data: report,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to run performance tests',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('performance-metrics')
  @Roles('admin')
  @ApiOperation({ summary: 'Récupère les métriques de performance cryptographique' })
  @ApiResponse({ status: 200, description: 'Métriques de performance récupérées avec succès' })
  async getPerformanceMetrics() {
    try {
      const metrics = this.cryptoPerformanceService.calculatePerformanceMetrics();
      return {
        statusCode: HttpStatus.OK,
        data: metrics,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get performance metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('performance-report')
  @Roles('admin')
  @ApiOperation({ summary: 'Génère un rapport de performance cryptographique' })
  @ApiResponse({ status: 200, description: 'Rapport de performance généré avec succès' })
  async generatePerformanceReport() {
    try {
      const report = await this.cryptoPerformanceService.generatePerformanceReport();
      return {
        statusCode: HttpStatus.OK,
        data: report,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to generate performance report',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('verify-log-integrity')
  @Roles('admin')
  @ApiOperation({ summary: 'Vérifie l\'intégrité des journaux cryptographiques' })
  @ApiResponse({ status: 200, description: 'Intégrité des journaux vérifiée avec succès' })
  async verifyLogIntegrity() {
    try {
      const result = await this.cryptoLoggingService.verifyLogIntegrity();
      return {
        statusCode: HttpStatus.OK,
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to verify log integrity',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('optimize')
  @Roles('admin')
  @ApiOperation({ summary: 'Exécute une opération cryptographique avec optimisation' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        operation: { type: 'string', enum: ['encrypt', 'decrypt', 'sign', 'verify'] },
        algorithm: { type: 'string' },
        data: { type: 'string' },
        iterations: { type: 'number', default: 1 },
      },
      required: ['operation', 'data'],
    },
  })
  @ApiResponse({ status: 200, description: 'Opération exécutée avec succès' })
  async optimizeOperation(
    @Body() body: { operation: string; algorithm?: string; data: string; iterations?: number },
  ) {
    try {
      const { operation, algorithm, data, iterations = 1 } = body;
      
      // Simuler une opération cryptographique
      const startTime = Date.now();
      
      // Exécuter l'opération avec optimisation
      await this.cryptoPerformanceService.executeWithOptimization(async () => {
        // Simuler une opération cryptographique
        await new Promise(resolve => setTimeout(resolve, 10));
        return true;
      }, `${operation}-${algorithm}-${data.substring(0, 10)}`);
      
      const executionTimeMs = Date.now() - startTime;
      
      return {
        statusCode: HttpStatus.OK,
        data: {
          operation,
          algorithm,
          dataLength: data.length,
          iterations,
          executionTimeMs,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to optimize operation',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
