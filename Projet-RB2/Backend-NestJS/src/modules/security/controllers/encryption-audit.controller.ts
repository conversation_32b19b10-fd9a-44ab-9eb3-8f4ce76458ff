import { Controller, Get, Post, Body, Query, HttpCode, HttpStatus } from '@nestjs/common';
import { EncryptionAuditService } from '../services/encryption-audit.service';
import { EncryptionAuditReport, EncryptionAuditOptions } from '../interfaces/encryption-audit.interfaces';

@Controller('encryption-audit')
export class EncryptionAuditController {
  constructor(private readonly auditService: EncryptionAuditService) {}

  /**
   * Endpoint pour loguer un événement d'audit (exemple générique)
   */
  @Post('log-event')
  @HttpCode(HttpStatus.CREATED)
  async logEvent(@Body() body: { eventType: string; details: any; userId?: string; resourceId?: string }) {
    await this.auditService.logEvent(body.eventType, body.details, body.userId, body.resourceId);
    return { success: true };
  }

  /**
   * Endpoint pour récupérer les événements d'audit (filtrage possible)
   */
  @Get('events')
  async getEvents(@Query('userId') userId?: string, @Query('resourceId') resourceId?: string, @Query('eventType') eventType?: string) {
    const filters = { userId, resourceId, eventType };
    const events = await this.auditService.getEvents(filters);
    return { events };
  }

  /**
   * Endpoint pour générer un rapport d'audit (squelette, à enrichir selon besoins)
   */
  @Post('generate-report')
  async generateReport(@Body() options: EncryptionAuditOptions): Promise<EncryptionAuditReport> {
    // TODO: Implémenter la génération réelle du rapport
    return {
      id: 'demo',
      timestamp: new Date(),
      summary: {
        totalServices: 0,
        passedServices: 0,
        failedServices: 0,
        warningServices: 0,
        criticalVulnerabilities: 0,
        highVulnerabilities: 0,
        mediumVulnerabilities: 0,
        lowVulnerabilities: 0,
      },
      serviceResults: [],
      overallStatus: 'PASS',
      recommendations: []
    };
  }
}
