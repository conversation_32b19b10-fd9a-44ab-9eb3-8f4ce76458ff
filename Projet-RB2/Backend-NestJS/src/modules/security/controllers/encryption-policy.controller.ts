import { Controller, Get, Post, Body, Param, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { EncryptionPolicyService, EncryptionPolicy, SensitiveDataType, SensitivityLevel, EncryptionType } from '../services/encryption-policy.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';

@ApiTags('encryption-policy')
@Controller('security/encryption-policy')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EncryptionPolicyController {
  private readonly logger = new Logger(EncryptionPolicyController.name);

  constructor(private readonly policyService: EncryptionPolicyService) {}

  @Get()
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Get all encryption policies' })
  @ApiResponse({ status: 200, description: 'Return all encryption policies' })
  getAllPolicies(): { [key: string]: EncryptionPolicy } {
    this.logger.log('Getting all encryption policies');
    const policiesMap = this.policyService.getAllPolicies();
    const policiesObj = {};
    
    // Convert Map to object for API response
    for (const [key, value] of policiesMap.entries()) {
      policiesObj[key] = value;
    }
    
    return policiesObj;
  }

  @Get('data-type/:type')
  @Roles('admin', 'security-admin', 'developer')
  @ApiOperation({ summary: 'Get encryption policy for a data type' })
  @ApiParam({ name: 'type', enum: SensitiveDataType, description: 'Data type' })
  @ApiResponse({ status: 200, description: 'Return encryption policy for the specified data type' })
  getPolicyForDataType(@Param('type') type: SensitiveDataType): EncryptionPolicy {
    this.logger.log(`Getting encryption policy for data type: ${type}`);
    return this.policyService.getPolicyForDataType(type);
  }

  @Get('sensitivity/:level')
  @Roles('admin', 'security-admin', 'developer')
  @ApiOperation({ summary: 'Get encryption policy for a sensitivity level' })
  @ApiParam({ name: 'level', enum: SensitivityLevel, description: 'Sensitivity level' })
  @ApiResponse({ status: 200, description: 'Return encryption policy for the specified sensitivity level' })
  getPolicyForSensitivityLevel(@Param('level') level: SensitivityLevel): EncryptionPolicy {
    this.logger.log(`Getting encryption policy for sensitivity level: ${level}`);
    return this.policyService.getPolicyForSensitivityLevel(level);
  }

  @Get(':name')
  @Roles('admin', 'security-admin', 'developer')
  @ApiOperation({ summary: 'Get encryption policy by name' })
  @ApiParam({ name: 'name', description: 'Policy name' })
  @ApiResponse({ status: 200, description: 'Return encryption policy with the specified name' })
  getPolicy(@Param('name') name: string): EncryptionPolicy {
    this.logger.log(`Getting encryption policy: ${name}`);
    return this.policyService.getPolicy(name);
  }

  @Post()
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Create or update encryption policy' })
  @ApiResponse({ status: 201, description: 'Policy created or updated successfully' })
  setPolicy(
    @Body('name') name: string,
    @Body('policy') policy: EncryptionPolicy,
  ): { success: boolean; message: string } {
    this.logger.log(`Setting encryption policy: ${name}`);
    this.policyService.setPolicy(name, policy);
    return { success: true, message: `Policy ${name} created or updated successfully` };
  }

  @Post('map/data-type')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Map data type to encryption policy' })
  @ApiResponse({ status: 201, description: 'Data type mapped to policy successfully' })
  mapDataTypeToPolicy(
    @Body('dataType') dataType: SensitiveDataType,
    @Body('policyName') policyName: string,
  ): { success: boolean; message: string } {
    this.logger.log(`Mapping data type ${dataType} to policy: ${policyName}`);
    this.policyService.mapDataTypeToPolicy(dataType, policyName);
    return { success: true, message: `Data type ${dataType} mapped to policy ${policyName} successfully` };
  }

  @Post('map/sensitivity')
  @Roles('admin', 'security-admin')
  @ApiOperation({ summary: 'Map sensitivity level to encryption policy' })
  @ApiResponse({ status: 201, description: 'Sensitivity level mapped to policy successfully' })
  mapSensitivityLevelToPolicy(
    @Body('level') level: SensitivityLevel,
    @Body('policyName') policyName: string,
  ): { success: boolean; message: string } {
    this.logger.log(`Mapping sensitivity level ${level} to policy: ${policyName}`);
    this.policyService.mapSensitivityLevelToPolicy(level, policyName);
    return { success: true, message: `Sensitivity level ${level} mapped to policy ${policyName} successfully` };
  }

  @Get('encryption-type/:type/available')
  @Roles('admin', 'security-admin', 'developer')
  @ApiOperation({ summary: 'Check if encryption type is available' })
  @ApiParam({ name: 'type', enum: EncryptionType, description: 'Encryption type' })
  @ApiResponse({ status: 200, description: 'Return availability status of the encryption type' })
  isEncryptionTypeAvailable(@Param('type') type: EncryptionType): { available: boolean } {
    this.logger.log(`Checking if encryption type is available: ${type}`);
    const available = this.policyService.isEncryptionTypeAvailable(type);
    return { available };
  }

  @Get('homomorphic/allowed')
  @Roles('admin', 'security-admin', 'developer')
  @ApiOperation({ summary: 'Check if homomorphic encryption is allowed for a context' })
  @ApiResponse({ status: 200, description: 'Return whether homomorphic encryption is allowed' })
  isHomomorphicAllowed(
    @Body() context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
    },
  ): { allowed: boolean } {
    this.logger.log(`Checking if homomorphic encryption is allowed for context: ${JSON.stringify(context)}`);
    const allowed = this.policyService.isHomomorphicAllowed(context);
    return { allowed };
  }

  @Get('quantum-resistant/required')
  @Roles('admin', 'security-admin', 'developer')
  @ApiOperation({ summary: 'Check if quantum-resistant encryption is required for a context' })
  @ApiResponse({ status: 200, description: 'Return whether quantum-resistant encryption is required' })
  isQuantumResistantRequired(
    @Body() context: {
      dataType?: SensitiveDataType;
      sensitivityLevel?: SensitivityLevel;
      policyName?: string;
    },
  ): { required: boolean } {
    this.logger.log(`Checking if quantum-resistant encryption is required for context: ${JSON.stringify(context)}`);
    const required = this.policyService.isQuantumResistantRequired(context);
    return { required };
  }
}
