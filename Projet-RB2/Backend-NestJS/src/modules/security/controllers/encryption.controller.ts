import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpException,
  HttpStatus,
  Req,
  Res,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { KeyManagementService } from '../services/key-management.service';
import { VaultService } from '../services/vault.service';
import { EndToEndEncryptionService } from '../services/end-to-end-encryption.service';
import { MicroserviceSecurityService } from '../services/microservice-security.service';
import { CertificateManagementService } from '../services/certificate-management.service';
import { Request, Response } from 'express';

@ApiTags('encryption')
@Controller('encryption')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EncryptionController {
  constructor(
    private readonly keyManagementService: KeyManagementService,
    private readonly vaultService: VaultService,
    private readonly e2eEncryptionService: EndToEndEncryptionService,
    private readonly microserviceSecurityService: MicroserviceSecurityService,
    private readonly certificateManagementService: CertificateManagementService,
  ) {}

  @Get('keys')
  @Roles('admin')
  @ApiOperation({ summary: 'Liste les clés actives' })
  @ApiResponse({ status: 200, description: 'Liste des clés actives' })
  async listActiveKeys() {
    try {
      const keys = await this.keyManagementService.listActiveKeys();
      return {
        statusCode: HttpStatus.OK,
        data: keys.map(key => ({
          id: key.id,
          algorithm: key.algorithm,
          purpose: key.purpose,
          createdAt: key.createdAt,
          expiresAt: key.expiresAt,
          status: key.status,
        })),
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to list active keys',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('keys/rotate/:keyId')
  @Roles('admin')
  @ApiOperation({ summary: 'Effectue la rotation d\'une clé' })
  @ApiResponse({ status: 200, description: 'Clé rotée avec succès' })
  async rotateKey(@Param('keyId') keyId: string) {
    try {
      const newKeyId = await this.keyManagementService.rotateKey(keyId);
      return {
        statusCode: HttpStatus.OK,
        message: 'Key rotated successfully',
        data: { oldKeyId: keyId, newKeyId },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to rotate key',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('vault/status')
  @Roles('admin')
  @ApiOperation({ summary: 'Vérifie le statut de l\'intégration avec Vault' })
  @ApiResponse({ status: 200, description: 'Statut de l\'intégration avec Vault' })
  async getVaultStatus() {
    try {
      const isInitialized = this.vaultService.isInitialized();
      return {
        statusCode: HttpStatus.OK,
        data: {
          enabled: isInitialized,
          status: isInitialized ? 'connected' : 'disconnected',
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get Vault status',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('keys/create')
  @Roles('admin')
  @ApiOperation({ summary: 'Crée une nouvelle clé de chiffrement' })
  @ApiResponse({ status: 201, description: 'Clé créée avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'financial' },
        algorithm: { type: 'string', example: 'aes-256-gcm' },
        purpose: { type: 'string', enum: ['encryption', 'signing', 'hmac', 'general'], example: 'encryption' },
        rotationInterval: { type: 'number', example: 604800000 },
        autoRotate: { type: 'boolean', example: true },
      },
      required: ['name', 'algorithm', 'purpose'],
    },
  })
  async createKey(
    @Body() body: { name: string; algorithm: string; purpose: 'encryption' | 'signing' | 'hmac' | 'general'; rotationInterval?: number; autoRotate?: boolean },
  ) {
    try {
      const { name, algorithm, purpose, rotationInterval, autoRotate } = body;
      const keyId = await this.keyManagementService.createKey(name, algorithm, purpose, {
        rotationInterval,
        autoRotate,
      });
      return {
        statusCode: HttpStatus.CREATED,
        message: 'Key created successfully',
        data: { keyId },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to create key',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('e2e/generate-keypair')
  @Roles('admin', 'user')
  @ApiOperation({ summary: 'Génère une paire de clés pour le chiffrement de bout en bout' })
  @ApiResponse({ status: 201, description: 'Paire de clés générée avec succès' })
  async generateE2EKeyPair() {
    try {
      const keyPair = await this.e2eEncryptionService.generateUserKeyPair();
      return {
        statusCode: HttpStatus.CREATED,
        data: {
          publicKey: keyPair.publicKey,
          privateKey: keyPair.privateKey,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to generate key pair',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('e2e/encrypt')
  @Roles('admin', 'user')
  @ApiOperation({ summary: 'Chiffre un message avec une clé publique' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        publicKey: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Message chiffré avec succès' })
  async encryptMessage(
    @Body() body: { message: string; publicKey: string },
  ) {
    try {
      const encryptedMessage = await this.e2eEncryptionService.encryptMessage(
        body.message,
        body.publicKey,
      );
      return {
        statusCode: HttpStatus.OK,
        data: { encryptedMessage },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to encrypt message',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('e2e/decrypt')
  @Roles('admin', 'user')
  @ApiOperation({ summary: 'Déchiffre un message avec une clé privée' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        encryptedMessage: { type: 'string' },
        privateKey: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Message déchiffré avec succès' })
  async decryptMessage(
    @Body() body: { encryptedMessage: string; privateKey: string },
  ) {
    try {
      const decryptedMessage = await this.e2eEncryptionService.decryptMessage(
        body.encryptedMessage,
        body.privateKey,
      );
      return {
        statusCode: HttpStatus.OK,
        data: { decryptedMessage },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to decrypt message',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('certificates/generate')
  @Roles('admin')
  @ApiOperation({ summary: 'Génère des certificats pour un service' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        serviceName: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Certificats générés avec succès' })
  async generateCertificates(
    @Body() body: { serviceName: string },
  ) {
    try {
      const certificates = await this.certificateManagementService.generateServiceCertificates(
        body.serviceName,
      );
      return {
        statusCode: HttpStatus.CREATED,
        data: {
          certPath: certificates.certPath,
          keyPath: certificates.keyPath,
          caCertPath: certificates.caCertPath,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to generate certificates',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('certificates/download/:serviceName/:fileType')
  @Roles('admin')
  @ApiOperation({ summary: 'Télécharge un certificat ou une clé' })
  @ApiResponse({ status: 200, description: 'Fichier téléchargé avec succès' })
  async downloadCertificate(
    @Param('serviceName') serviceName: string,
    @Param('fileType') fileType: 'cert' | 'key' | 'ca',
    @Res() res: Response,
  ) {
    try {
      let filePath: string;
      let fileName: string;
      let contentType: string;

      switch (fileType) {
        case 'cert':
          filePath = `./certs/${serviceName}.crt`;
          fileName = `${serviceName}.crt`;
          contentType = 'application/x-x509-ca-cert';
          break;
        case 'key':
          filePath = `./certs/${serviceName}.key`;
          fileName = `${serviceName}.key`;
          contentType = 'application/octet-stream';
          break;
        case 'ca':
          filePath = './certs/ca.crt';
          fileName = 'ca.crt';
          contentType = 'application/x-x509-ca-cert';
          break;
        default:
          throw new HttpException(
            {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Invalid file type',
            },
            HttpStatus.BAD_REQUEST,
          );
      }

      return res.download(filePath, fileName, {
        headers: {
          'Content-Type': contentType,
        },
      });
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to download certificate',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('microservice/public-key')
  @Roles('admin', 'service')
  @ApiOperation({ summary: 'Récupère la clé publique de ce service' })
  @ApiResponse({ status: 200, description: 'Clé publique récupérée avec succès' })
  async getPublicKey() {
    try {
      const publicKey = this.microserviceSecurityService.getPublicKey();
      if (!publicKey) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Public key not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }
      return {
        statusCode: HttpStatus.OK,
        data: { publicKey },
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get public key',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('microservice/register-key')
  @Roles('admin', 'service')
  @ApiOperation({ summary: 'Enregistre la clé publique d\'un service' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        serviceId: { type: 'string' },
        publicKey: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Clé publique enregistrée avec succès' })
  async registerServiceKey(
    @Body() body: { serviceId: string; publicKey: string },
  ) {
    try {
      this.microserviceSecurityService.registerServicePublicKey(
        body.serviceId,
        body.publicKey,
      );
      return {
        statusCode: HttpStatus.CREATED,
        message: 'Service public key registered successfully',
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to register service public key',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
