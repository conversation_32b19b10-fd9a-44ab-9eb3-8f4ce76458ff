import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { IncidentManagementService, CreateIncidentDto, UpdateIncidentDto } from '../services/incident-management.service';

@ApiTags('security-incidents')
@Controller('security/incidents')
@UseGuards(JwtAuthGuard, RolesGuard)
export class IncidentManagementController {
  constructor(private readonly incidentService: IncidentManagementService) {}

  /**
   * Récupère tous les incidents de sécurité avec pagination et filtres
   */
  @Get()
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupère tous les incidents de sécurité' })
  @ApiResponse({ status: 200, description: 'Liste des incidents de sécurité' })
  async getIncidents(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: string,
    @Query('severity') severity?: string,
    @Query('type') type?: string,
    @Query('assignedToId') assignedToId?: string,
  ) {
    try {
      const result = await this.incidentService.getIncidents(
        +page,
        +limit,
        status,
        severity,
        type,
        assignedToId,
      );

      return {
        statusCode: HttpStatus.OK,
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve security incidents',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère un incident de sécurité par son ID
   */
  @Get(':id')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupère un incident de sécurité par son ID' })
  @ApiResponse({ status: 200, description: 'Incident de sécurité' })
  @ApiResponse({ status: 404, description: 'Incident non trouvé' })
  async getIncidentById(@Param('id') id: string) {
    try {
      const incident = await this.incidentService.getIncidentById(id);

      return {
        statusCode: HttpStatus.OK,
        data: incident,
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Security incident not found',
            error: error.message,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve security incident',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Crée un nouvel incident de sécurité
   */
  @Post()
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Crée un nouvel incident de sécurité' })
  @ApiResponse({ status: 201, description: 'Incident de sécurité créé' })
  async createIncident(@Body() createIncidentDto: CreateIncidentDto, @Req() req: any) {
    try {
      // Si aucun reportedById n'est fourni, utiliser l'ID de l'utilisateur authentifié
      if (!createIncidentDto.reportedById && req.user) {
        createIncidentDto.reportedById = req.user.id;
      }

      const incident = await this.incidentService.createIncident(createIncidentDto);

      return {
        statusCode: HttpStatus.CREATED,
        message: 'Security incident created successfully',
        data: incident,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to create security incident',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Met à jour un incident de sécurité
   */
  @Put(':id')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Met à jour un incident de sécurité' })
  @ApiResponse({ status: 200, description: 'Incident de sécurité mis à jour' })
  @ApiResponse({ status: 404, description: 'Incident non trouvé' })
  async updateIncident(
    @Param('id') id: string,
    @Body() updateIncidentDto: UpdateIncidentDto,
    @Req() req: any,
  ) {
    try {
      const incident = await this.incidentService.updateIncident(
        id,
        updateIncidentDto,
        req.user?.id,
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Security incident updated successfully',
        data: incident,
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Security incident not found',
            error: error.message,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to update security incident',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Supprime un incident de sécurité
   */
  @Delete(':id')
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprime un incident de sécurité' })
  @ApiResponse({ status: 200, description: 'Incident de sécurité supprimé' })
  @ApiResponse({ status: 404, description: 'Incident non trouvé' })
  async deleteIncident(@Param('id') id: string, @Req() req: any) {
    try {
      await this.incidentService.deleteIncident(id, req.user?.id);

      return {
        statusCode: HttpStatus.OK,
        message: 'Security incident deleted successfully',
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Security incident not found',
            error: error.message,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to delete security incident',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Ajoute une activité à un incident
   */
  @Post(':id/activities')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Ajoute une activité à un incident' })
  @ApiResponse({ status: 201, description: 'Activité ajoutée' })
  @ApiResponse({ status: 404, description: 'Incident non trouvé' })
  async addIncidentActivity(
    @Param('id') id: string,
    @Body() activityData: { action: string; details?: string; metadata?: Record<string, any> },
    @Req() req: any,
  ) {
    try {
      // Vérifier si l'incident existe
      await this.incidentService.getIncidentById(id);

      // Ajouter l'activité
      const activity = await this.incidentService.addIncidentActivity(id, {
        ...activityData,
        userId: req.user?.id,
      });

      return {
        statusCode: HttpStatus.CREATED,
        message: 'Activity added to security incident',
        data: activity,
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Security incident not found',
            error: error.message,
          },
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to add activity to security incident',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
