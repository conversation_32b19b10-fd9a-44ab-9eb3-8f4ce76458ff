import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { SecurityMonitoringService, SecurityMetrics, ActiveThreat } from '../services/security-monitoring.service';
import { IncidentManagementService } from '../services/incident-management.service';

@ApiTags('security-dashboard')
@Controller('security/dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SecurityDashboardController {
  constructor(
    private readonly securityMonitoringService: SecurityMonitoringService,
    private readonly incidentManagementService: IncidentManagementService,
  ) {}

  /**
   * <PERSON><PERSON><PERSON><PERSON> les métriques de sécurité pour le tableau de bord
   */
  @Get('metrics')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupère les métriques de sécurité' })
  @ApiResponse({ status: 200, description: 'Métriques de sécurité' })
  async getSecurityMetrics(@Query('timeframe') timeframe: 'day' | 'week' | 'month' = 'day') {
    try {
      const metrics = await this.securityMonitoringService.getSecurityMetrics(timeframe);

      return {
        statusCode: HttpStatus.OK,
        data: metrics,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve security metrics',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère les menaces actives
   */
  @Get('threats')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupère les menaces actives' })
  @ApiResponse({ status: 200, description: 'Menaces actives' })
  async getActiveThreats() {
    try {
      const threats = await this.securityMonitoringService.getActiveThreats();

      return {
        statusCode: HttpStatus.OK,
        data: threats,
      };
    } catch (error) {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve active threats',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère une menace active par son ID
   */
  @Get('threats/:id')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Récupère une menace active par son ID' })
  @ApiResponse({ status: 200, description: 'Menace active' })
  @ApiResponse({ status: 404, description: 'Menace non trouvée' })
  async getActiveThreatById(@Param('id') id: string) {
    try {
      const threat = await this.securityMonitoringService.getActiveThreatById(id);

      if (!threat) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Active threat not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        statusCode: HttpStatus.OK,
        data: threat,
      };
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to retrieve active threat',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Met à jour le statut d'une menace active
   */
  @Put('threats/:id')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Met à jour le statut d\'une menace active' })
  @ApiResponse({ status: 200, description: 'Menace mise à jour' })
  @ApiResponse({ status: 404, description: 'Menace non trouvée' })
  async updateActiveThreatStatus(
    @Param('id') id: string,
    @Body() data: { status: string; mitigationSteps?: string[] },
  ) {
    try {
      const threat = await this.securityMonitoringService.updateActiveThreatStatus(
        id,
        data.status,
        data.mitigationSteps,
      );

      if (!threat) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Active threat not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        statusCode: HttpStatus.OK,
        message: 'Active threat status updated successfully',
        data: threat,
      };
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to update active threat status',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Supprime une menace active
   */
  @Delete('threats/:id')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprime une menace active' })
  @ApiResponse({ status: 200, description: 'Menace supprimée' })
  @ApiResponse({ status: 404, description: 'Menace non trouvée' })
  async removeActiveThreat(@Param('id') id: string) {
    try {
      const success = await this.securityMonitoringService.removeActiveThreat(id);

      if (!success) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Active threat not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        statusCode: HttpStatus.OK,
        message: 'Active threat removed successfully',
      };
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to remove active threat',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Crée un incident à partir d'une menace active
   */
  @Post('threats/:id/create-incident')
  @Roles('ADMIN', 'SECURITY')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Crée un incident à partir d\'une menace active' })
  @ApiResponse({ status: 201, description: 'Incident créé' })
  @ApiResponse({ status: 404, description: 'Menace non trouvée' })
  async createIncidentFromThreat(
    @Param('id') id: string,
    @Body() data: { assignedToId?: string; reportedById?: string },
  ) {
    try {
      const threat = await this.securityMonitoringService.getActiveThreatById(id);

      if (!threat) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Active threat not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Créer un incident à partir de la menace
      const incident = await this.incidentManagementService.createIncident({
        title: `Incident from threat: ${threat.type}`,
        description: `Security incident created from active threat of type ${threat.type} detected at ${threat.detectedAt.toISOString()}`,
        severity: this.mapThreatSeverityToIncidentSeverity(threat.severity),
        type: this.mapThreatTypeToIncidentType(threat.type),
        source: threat.source,
        assignedToId: data.assignedToId,
        reportedById: data.reportedById,
        affectedAssets: threat.affectedAssets,
        metadata: {
          threatId: threat.id,
          threatDetails: threat.details,
        },
      });

      // Mettre à jour le statut de la menace
      await this.securityMonitoringService.updateActiveThreatStatus(id, 'INCIDENT_CREATED');

      return {
        statusCode: HttpStatus.CREATED,
        message: 'Incident created from active threat successfully',
        data: {
          incidentId: incident.id,
          threatId: threat.id,
        },
      };
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }

      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to create incident from active threat',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Mappe la sévérité d'une menace à la sévérité d'un incident
   * @param threatSeverity Sévérité de la menace
   * @returns Sévérité de l'incident
   */
  private mapThreatSeverityToIncidentSeverity(
    threatSeverity: string,
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const mapping: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
      LOW: 'LOW',
      MEDIUM: 'MEDIUM',
      HIGH: 'HIGH',
      CRITICAL: 'CRITICAL',
      INFO: 'LOW',
      WARNING: 'MEDIUM',
      ERROR: 'HIGH',
    };

    return mapping[threatSeverity] || 'MEDIUM';
  }

  /**
   * Mappe le type d'une menace au type d'un incident
   * @param threatType Type de la menace
   * @returns Type de l'incident
   */
  private mapThreatTypeToIncidentType(threatType: string): string {
    const mapping: Record<string, string> = {
      BRUTE_FORCE_ATTEMPT: 'UNAUTHORIZED_ACCESS',
      DENIAL_OF_SERVICE: 'DENIAL_OF_SERVICE',
      MALWARE_DETECTED: 'MALWARE',
      RANSOMWARE_DETECTED: 'RANSOMWARE',
      SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
      DATA_BREACH: 'DATA_BREACH',
      SYSTEM_COMPROMISE: 'SYSTEM_COMPROMISE',
      UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
      NETWORK_INTRUSION: 'NETWORK_INTRUSION',
      ACCOUNT_COMPROMISE: 'ACCOUNT_COMPROMISE',
    };

    return mapping[threatType] || 'SUSPICIOUS_ACTIVITY';
  }
}
