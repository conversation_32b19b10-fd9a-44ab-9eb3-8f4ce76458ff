import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SecurityNotificationService } from '../services/security-notification.service';

@ApiTags('security-notifications')
@Controller('security/notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SecurityNotificationController {
  constructor(private readonly notificationService: SecurityNotificationService) {}

  /**
   * Récupère les notifications de sécurité de l'utilisateur
   */
  @Get()
  @ApiOperation({ summary: 'Get user security notifications' })
  @ApiResponse({ status: 200, description: 'Returns user security notifications' })
  async getUserNotifications(
    @Req() req: any,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('unreadOnly') unreadOnly = false,
    @Query('severity') severity?: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL',
  ) {
    try {
      const userId = req.user.id;
      return await this.notificationService.getUserNotifications(userId, {
        page: +page,
        limit: +limit,
        unreadOnly: ['true', '1'].includes(String(unreadOnly)) || Boolean(unreadOnly === true),
        severity,
      });
    } catch (error) {
      throw new HttpException(
        `Failed to get user notifications: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Récupère le nombre de notifications non lues
   */
  @Get('unread-count')
  @ApiOperation({ summary: 'Get unread notifications count' })
  @ApiResponse({ status: 200, description: 'Returns unread notifications count' })
  async getUnreadCount(@Req() req: any) {
    try {
      const userId = req.user.id;
      const { unreadCount } = await this.notificationService.getUserNotifications(userId, {
        limit: 1,
        unreadOnly: true,
      });
      return { unreadCount };
    } catch (error) {
      throw new HttpException(
        `Failed to get unread count: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Marque une notification comme lue
   */
  @Post(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  async markAsRead(@Param('id') id: string, @Req() req: any) {
    try {
      const userId = req.user.id;
      return await this.notificationService.markNotificationAsRead(id, userId);
    } catch (error) {
      throw new HttpException(
        `Failed to mark notification as read: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Marque toutes les notifications comme lues
   */
  @Post('mark-all-read')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  async markAllAsRead(@Req() req: any) {
    try {
      const userId = req.user.id;
      const count = await this.notificationService.markAllNotificationsAsRead(userId);
      return { count };
    } catch (error) {
      throw new HttpException(
        `Failed to mark all notifications as read: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
