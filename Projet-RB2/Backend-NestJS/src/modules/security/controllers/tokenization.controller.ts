import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { TokenizationService } from '../services/tokenization.service';
import { TokenFormat, TokenizationOptions, TokenizationType } from '../interfaces/tokenization.interfaces';

/**
 * DTO pour la requête de tokenisation
 */
class TokenizeRequestDto {
  /**
   * Donnée à tokeniser
   * @example "4111-1111-1111-1111"
   */
  data: string;

  /**
   * Type de donnée à tokeniser
   * @example "PAYMENT_CARD"
   */
  type?: TokenizationType;

  /**
   * Durée de validité du token en secondes
   * @example 86400
   */
  expiresIn?: number;

  /**
   * Si le token peut être détokenisé
   * @example true
   */
  detokenizable?: boolean;

  /**
   * Format du token
   * @example "PRESERVING"
   */
  format?: TokenFormat;

  /**
   * Conserver le format de la donnée originale
   * @example true
   */
  preserveFormat?: boolean;

  /**
   * Métadonnées supplémentaires
   * @example { "userId": "123", "purpose": "payment" }
   */
  metadata?: Record<string, any>;
}

/**
 * DTO pour la requête de détokenisation
 */
class DetokenizeRequestDto {
  /**
   * Token à détokeniser
   * @example "4XXX-XXXX-XXXX-1111"
   */
  token: string;
}

/**
 * Contrôleur pour le service de tokenisation
 */
@ApiTags('tokenization')
@Controller('api/security/tokenization')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TokenizationController {
  constructor(private readonly tokenizationService: TokenizationService) {}

  /**
   * Tokenise une donnée sensible
   */
  @Post('tokenize')
  @Roles('admin', 'financial-manager', 'security-officer')
  @ApiOperation({ summary: 'Tokenize sensitive data' })
  @ApiResponse({ status: 201, description: 'Data tokenized successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async tokenize(@Body() request: TokenizeRequestDto) {
    try {
      const options: Partial<TokenizationOptions> = {
        type: request.type,
        expiresIn: request.expiresIn,
        detokenizable: request.detokenizable,
        format: request.format,
        preserveFormat: request.preserveFormat,
        metadata: request.metadata,
      };

      const result = await this.tokenizationService.tokenize(request.data, options);
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to tokenize data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Détokenise une donnée (récupère la donnée originale à partir du token)
   */
  @Post('detokenize')
  @Roles('admin', 'financial-manager', 'security-officer')
  @ApiOperation({ summary: 'Detokenize data' })
  @ApiResponse({ status: 200, description: 'Data detokenized successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  @ApiResponse({ status: 404, description: 'Token not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async detokenize(@Body() request: DetokenizeRequestDto) {
    try {
      const originalData = await this.tokenizationService.detokenize(request.token);
      return { originalData };
    } catch (error) {
      if (error.message === 'Token not found') {
        throw new HttpException('Token not found', HttpStatus.NOT_FOUND);
      } else if (error.message === 'Token is expired' || error.message === 'Token is revoked') {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      } else {
        throw new HttpException(
          error.message || 'Failed to detokenize data',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  /**
   * Révoque un token (le rend inutilisable)
   */
  @Delete('token/:tokenValue')
  @Roles('admin', 'security-officer')
  @ApiOperation({ summary: 'Revoke a token' })
  @ApiResponse({ status: 200, description: 'Token revoked successfully' })
  @ApiResponse({ status: 404, description: 'Token not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async revokeToken(@Param('tokenValue') tokenValue: string) {
    try {
      await this.tokenizationService.revokeToken(tokenValue);
      return { message: 'Token revoked successfully' };
    } catch (error) {
      if (error.message === 'Token not found') {
        throw new HttpException('Token not found', HttpStatus.NOT_FOUND);
      } else {
        throw new HttpException(
          error.message || 'Failed to revoke token',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  /**
   * Récupère les statistiques de tokenisation
   */
  @Get('stats')
  @Roles('admin', 'security-officer')
  @ApiOperation({ summary: 'Get tokenization statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getTokenizationStats() {
    try {
      const stats = await this.tokenizationService.getTokenizationStats();
      return stats;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get tokenization statistics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
