import { CryptoCacheService } from '../services/crypto-cache.service';

/**
 * Options pour le décorateur CacheCrypto
 */
export interface CacheCryptoOptions {
  ttl?: number; // Durée de vie en millisecondes
  condition?: (...args: any[]) => boolean; // Condition pour mettre en cache
}

/**
 * Décorateur pour mettre en cache les résultats des méthodes cryptographiques
 * @param operationType Type d'opération (ex: 'encrypt', 'decrypt', 'homomorphic.add')
 * @param options Options de cache (optionnel)
 * @returns Décorateur de méthode
 */
export function CacheCrypto(operationType: string, options: CacheCryptoOptions = {}) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    // Stocker la méthode originale
    const originalMethod = descriptor.value;

    // Remplacer la méthode par une version avec cache
    descriptor.value = async function (...args: any[]) {
      // Obtenir le service de cache
      const cacheService = this.cacheService as CryptoCacheService;
      
      // Si le service de cache n'est pas disponible, exécuter la méthode originale
      if (!cacheService) {
        return originalMethod.apply(this, args);
      }
      
      // Vérifier la condition de mise en cache
      if (options.condition && !options.condition(...args)) {
        return originalMethod.apply(this, args);
      }
      
      // Vérifier si le résultat est déjà en cache
      const cachedResult = cacheService.get(operationType, args);
      if (cachedResult !== null) {
        return cachedResult;
      }
      
      // Exécuter la méthode originale
      const result = await originalMethod.apply(this, args);
      
      // Mettre le résultat en cache
      cacheService.set(operationType, args, result, options.ttl);
      
      return result;
    };

    return descriptor;
  };
}
