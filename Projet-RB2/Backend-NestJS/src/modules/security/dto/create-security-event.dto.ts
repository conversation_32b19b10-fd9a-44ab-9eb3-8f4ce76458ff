import { IsString, IsOptional, IsObject, IsEnum, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Énumération des niveaux de sévérité des événements de sécurité
 */
export enum SecurityEventSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

/**
 * DTO pour la création d'un événement de sécurité
 */
export class CreateSecurityEventDto {
  @ApiProperty({
    description: 'Type d\'événement de sécurité',
    example: 'LOGIN_ATTEMPT'
  })
  @IsString()
  eventType: string;

  @ApiProperty({
    description: 'Source de l\'événement',
    example: 'AuthService'
  })
  @IsString()
  source: string;

  @ApiProperty({
    description: 'Sévérité de l\'événement',
    enum: SecurityEventSeverity,
    example: SecurityEventSeverity.WARNING
  })
  @IsEnum(SecurityEventSeverity)
  severity: SecurityEventSeverity;

  @ApiProperty({
    description: 'Détails de l\'événement',
    example: { userId: '123', ipAddress: '***********', action: 'login' }
  })
  @IsObject()
  details: Record<string, any>;

  @ApiProperty({
    description: 'Date et heure de l\'événement',
    example: '2023-01-01T12:00:00Z'
  })
  @IsDateString()
  @IsOptional()
  timestamp?: string;
}
