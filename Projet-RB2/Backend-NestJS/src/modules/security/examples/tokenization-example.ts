/**
 * Exemple d'utilisation du service de tokenisation
 *
 * Ce fichier montre comment utiliser le TokenizationService
 * pour tokeniser et détokeniser des données sensibles.
 */

import { TokenizationService } from '../services/tokenization.service';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { KeyManagementService } from '../services/key-management.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TokenFormat, TokenizationType } from '../interfaces/tokenization.interfaces';

async function runTokenizationExample() {
  console.log('Démarrage de l\'exemple de tokenisation...');

  // Créer les services nécessaires
  const configService = new ConfigService({
    'security.tokenization.enabled': true,
    'security.tokenization.defaultExpirationTime': 86400,
    'security.tokenization.defaultTokenFormat': 'UUID',
    'security.tokenization.preserveFormatByDefault': false,
    'security.vault.enabled': false,
    'security.tokenization.cleanupInterval': 3600,
  });

  // Créer des instances de services avec les paramètres requis
  const prismaService = new PrismaService(configService);
  const vaultService = { isInitialized: () => false }; // Mock du VaultService
  const keyManagementService = new KeyManagementService(configService, vaultService as any);
  const eventEmitter = new EventEmitter2();

  // Créer une instance du service de tokenisation
  const tokenizationService = new TokenizationService(
    configService,
    prismaService,
    keyManagementService,
    eventEmitter,
  );

  // Initialiser les services
  await keyManagementService.onModuleInit();
  await tokenizationService.onModuleInit();

  if (!tokenizationService.isEnabled()) {
    console.error('Le service de tokenisation n\'est pas activé');
    return;
  }

  console.log('Service de tokenisation initialisé');

  try {
    // Exemple 1: Tokenisation et détokenisation de base
    console.log('\nExemple 1: Tokenisation et détokenisation de base');
    const sensitiveData = 'données-sensibles-123';
    console.log(`Données sensibles: ${sensitiveData}`);

    const tokenizationResult = await tokenizationService.tokenize(sensitiveData);
    console.log(`Token généré: ${tokenizationResult.token}`);
    console.log(`ID du token: ${tokenizationResult.tokenId}`);
    console.log(`Date d'expiration: ${tokenizationResult.expiresAt}`);

    const originalData = await tokenizationService.detokenize(tokenizationResult.token);
    console.log(`Données détokenisées: ${originalData}`);

    // Exemple 2: Tokenisation d'une carte de paiement avec préservation du format
    console.log('\nExemple 2: Tokenisation d\'une carte de paiement');
    const cardNumber = '4111-1111-1111-1111';
    console.log(`Numéro de carte: ${cardNumber}`);

    const cardTokenizationResult = await tokenizationService.tokenize(cardNumber, {
      type: TokenizationType.PAYMENT_CARD,
      format: TokenFormat.PRESERVING,
      preserveFormat: true,
      expiresIn: 3600, // 1 heure
      metadata: {
        userId: 'user-123',
        purpose: 'payment',
      },
    });

    console.log(`Token de carte: ${cardTokenizationResult.token}`);
    console.log(`ID du token: ${cardTokenizationResult.tokenId}`);
    console.log(`Date d'expiration: ${cardTokenizationResult.expiresAt}`);

    const originalCardNumber = await tokenizationService.detokenize(cardTokenizationResult.token);
    console.log(`Numéro de carte détokenisé: ${originalCardNumber}`);

    // Exemple 3: Tokenisation de données bancaires
    console.log('\nExemple 3: Tokenisation de données bancaires');
    const accountNumber = '***************************';
    console.log(`Numéro de compte: ${accountNumber}`);

    const accountTokenizationResult = await tokenizationService.tokenize(accountNumber, {
      type: TokenizationType.BANK_ACCOUNT,
      format: TokenFormat.PRESERVING,
      preserveFormat: true,
    });

    console.log(`Token de compte: ${accountTokenizationResult.token}`);

    const originalAccountNumber = await tokenizationService.detokenize(accountTokenizationResult.token);
    console.log(`Numéro de compte détokenisé: ${originalAccountNumber}`);

    // Exemple 4: Révocation d'un token
    console.log('\nExemple 4: Révocation d\'un token');
    console.log(`Révocation du token: ${tokenizationResult.token}`);

    await tokenizationService.revokeToken(tokenizationResult.token);
    console.log('Token révoqué avec succès');

    try {
      await tokenizationService.detokenize(tokenizationResult.token);
    } catch (error) {
      console.log(`Erreur attendue lors de la détokenisation: ${error.message}`);
    }

    // Exemple 5: Statistiques de tokenisation
    console.log('\nExemple 5: Statistiques de tokenisation');
    const stats = await tokenizationService.getTokenizationStats();

    console.log(`Nombre total de tokens: ${stats.totalTokens}`);
    console.log(`Tokens actifs: ${stats.activeTokens}`);
    console.log(`Tokens expirés: ${stats.expiredTokens}`);
    console.log(`Tokens révoqués: ${stats.revokedTokens}`);
    console.log('Tokens par type:');

    for (const [type, count] of Object.entries(stats.tokensByType)) {
      console.log(`  ${type}: ${count}`);
    }

    console.log('\nExemple terminé avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'exécution de l\'exemple:', error);
  }
}

// Exécuter l'exemple si ce fichier est appelé directement
if (require.main === module) {
  runTokenizationExample().catch(console.error);
}

export { runTokenizationExample };
