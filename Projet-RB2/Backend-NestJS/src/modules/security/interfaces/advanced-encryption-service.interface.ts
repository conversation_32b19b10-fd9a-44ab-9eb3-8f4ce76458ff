import { IEncryptionService } from './encryption-service.interface';

/**
 * Interface pour les services de chiffrement avancés
 * Étend l'interface de base avec des méthodes spécifiques aux chiffrements avancés
 */
export interface IAdvancedEncryptionService extends IEncryptionService {
  /**
   * Initialise le service de chiffrement
   * @returns Promise qui se résout lorsque l'initialisation est terminée
   */
  initialize(): Promise<void>;

  /**
   * Charge des clés existantes
   * @param keys Clés à charger (format dépendant du service)
   */
  loadKeys(keys: any): Promise<void>;

  /**
   * Vérifie si l'environnement est compatible avec le service
   * @returns true si l'environnement est compatible
   */
  checkEnvironment?(): Promise<boolean>;

  /**
   * Retourne le niveau de sécurité du chiffrement
   * @returns Niveau de sécurité (ex: 128, 192, 256)
   */
  getSecurityLevel(): number;
}
