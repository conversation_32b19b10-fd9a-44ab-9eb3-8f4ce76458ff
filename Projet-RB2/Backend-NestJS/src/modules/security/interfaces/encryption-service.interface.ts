/**
 * Interface commune pour tous les services de chiffrement
 * Cette interface définit les méthodes que tous les services de chiffrement doivent implémenter
 */
export interface IEncryptionService {
  /**
   * Vérifie si le service de chiffrement est activé
   * @returns true si le service est activé
   */
  isEnabled(): boolean;

  /**
   * <PERSON><PERSON><PERSON> des données
   * @param data Données à chiffrer (chaîne de caractères ou Buffer)
   * @returns Données chiffrées sous forme de Buffer
   */
  encrypt(data: string | Buffer): Promise<Buffer>;

  /**
   * Déchiffre des données
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées sous forme de Buffer
   */
  decrypt(encryptedData: Buffer): Promise<Buffer>;

  /**
   * Génère une nouvelle paire de clés
   * @returns Paire de clés générée (format dépendant du service)
   */
  generateKeys(): Promise<any>;

  /**
   * Effectue une rotation des clés
   * @returns Nouvelles clés générées
   */
  rotateKeys?(): Promise<any>;

  /**
   * Retourne le type de chiffrement utilisé par le service
   * @returns Type de chiffrement (ex: "AES", "RSA", "QUANTUM", "HOMOMORPHIC")
   */
  getEncryptionType(): string;

  /**
   * Retourne les métadonnées du service de chiffrement
   * @returns Métadonnées du service (algorithme, taille de clé, etc.)
   */
  getMetadata(): Record<string, any>;
}
