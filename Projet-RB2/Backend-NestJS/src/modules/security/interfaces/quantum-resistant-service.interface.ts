import { IAdvancedEncryptionService } from './advanced-encryption-service.interface';

/**
 * Interface pour le service de chiffrement résistant aux ordinateurs quantiques
 * Étend l'interface de chiffrement avancé avec des méthodes spécifiques au chiffrement post-quantique
 */
export interface IQuantumResistantService extends IAdvancedEncryptionService {
  /**
   * Retourne l'algorithme post-quantique utilisé
   * @returns Algorithme post-quantique (ex: "kyber", "ntru", "mceliece", "sike", "hybrid")
   */
  getAlgorithm(): string;

  /**
   * Vérifie si le mode natif est disponible
   * @returns true si le mode natif est disponible
   */
  isNativeModeAvailable(): Promise<boolean>;

  /**
   * Retourne le mode de fonctionnement actuel
   * @returns Mode de fonctionnement (ex: "NATIVE", "SIMULATION")
   */
  getOperationMode(): string;

  /**
   * Chiffre des données en mode hybride (classique + post-quantique)
   * @param data Données à chiffrer
   * @returns Données chiffrées
   */
  hybridEncrypt?(data: Buffer | string): Promise<Buffer>;

  /**
   * Déchiffre des données en mode hybride
   * @param encryptedData Données chiffrées
   * @returns Données déchiffrées
   */
  hybridDecrypt?(encryptedData: Buffer): Promise<Buffer>;
}
