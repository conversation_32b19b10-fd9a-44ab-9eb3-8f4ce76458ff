import { SecretType, SecretMetadata, Secret } from '../services/secret-manager.service';

/**
 * Interface pour le service de gestion des secrets
 */
export interface ISecretManagerService {
  /**
   * Crée un nouveau secret
   * @param name Nom du secret
   * @param value Valeur du secret
   * @param type Type du secret
   * @param options Options supplémentaires
   * @returns ID du secret créé
   */
  createSecret(
    name: string,
    value: string,
    type?: SecretType,
    options?: {
      description?: string;
      expiresInDays?: number;
      rotationInterval?: number;
      autoRotate?: boolean;
      tags?: string[];
      owner?: string;
      application?: string;
      environment?: string;
    },
  ): Promise<string>;

  /**
   * Récupère un secret par son ID
   * @param secretId ID du secret
   * @returns Secret avec ses métadonnées
   */
  getSecret(secretId: string): Promise<Secret>;

  /**
   * Récupère un secret par son nom et son type
   * @param name Nom du secret
   * @param type Type du secret
   * @returns Secret avec ses métadonnées
   */
  getSecretByName(name: string, type?: SecretType): Promise<Secret>;

  /**
   * Met à jour la valeur d'un secret
   * @param secretId ID du secret
   * @param newValue Nouvelle valeur du secret
   * @returns Métadonnées mises à jour
   */
  updateSecret(secretId: string, newValue: string): Promise<SecretMetadata>;

  /**
   * Effectue la rotation d'un secret
   * @param secretId ID du secret
   * @returns ID du nouveau secret
   */
  rotateSecret(secretId: string): Promise<string>;

  /**
   * Supprime un secret
   * @param secretId ID du secret
   */
  deleteSecret(secretId: string): Promise<void>;

  /**
   * Liste tous les secrets
   * @param filters Filtres optionnels
   * @returns Liste des métadonnées des secrets
   */
  listSecrets(filters?: {
    type?: SecretType;
    application?: string;
    environment?: string;
    tags?: string[];
  }): Promise<SecretMetadata[]>;
}
