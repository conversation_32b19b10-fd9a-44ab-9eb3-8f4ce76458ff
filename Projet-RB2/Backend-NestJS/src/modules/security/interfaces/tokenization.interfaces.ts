/**
 * Interfaces pour le service de tokenisation
 */

/**
 * Types de données sensibles pouvant être tokenisées
 */
export enum TokenizationType {
  PAYMENT_CARD = 'PAYMENT_CARD',
  BANK_ACCOUNT = 'BANK_ACCOUNT',
  PERSONAL_DATA = 'PERSONAL_DATA',
  HEALTH_DATA = 'HEALTH_DATA',
  CREDENTIALS = 'CREDENTIALS',
  CUSTOM = 'CUSTOM'
}

/**
 * Statut d'un token
 */
export enum TokenStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED'
}

/**
 * Interface pour les options de tokenisation
 */
export interface TokenizationOptions {
  type: TokenizationType;
  expiresIn?: number; // Durée de validité en secondes
  detokenizable?: boolean; // Si le token peut être détokenisé
  format?: TokenFormat; // Format du token
  preserveFormat?: boolean; // Conserver le format de la donnée originale
  metadata?: Record<string, any>; // Métadonnées supplémentaires
}

/**
 * Format du token généré
 */
export enum TokenFormat {
  ALPHANUMERIC = 'ALPHANUMERIC', // Caractères alphanumériques aléatoires
  NUMERIC = 'NUMERIC', // Chiffres uniquement
  UUID = 'UUID', // Format UUID
  PRESERVING = 'PRESERVING' // Préserve le format de la donnée originale (ex: XXXX-XXXX-XXXX-1234 pour une carte)
}

/**
 * Interface pour un token
 */
export interface Token {
  id: string; // Identifiant unique du token
  value: string; // Valeur du token
  type: TokenizationType; // Type de donnée tokenisée
  createdAt: Date; // Date de création
  expiresAt?: Date; // Date d'expiration
  status: TokenStatus; // Statut du token
  metadata?: Record<string, any>; // Métadonnées
}

/**
 * Interface pour les résultats de tokenisation
 */
export interface TokenizationResult {
  token: string; // Valeur du token
  tokenId: string; // Identifiant unique du token
  expiresAt?: Date; // Date d'expiration
  metadata?: Record<string, any>; // Métadonnées
}

/**
 * Interface pour les statistiques de tokenisation
 */
export interface TokenizationStats {
  totalTokens: number;
  activeTokens: number;
  expiredTokens: number;
  revokedTokens: number;
  tokensByType: Record<TokenizationType, number>;
}

/**
 * Interface pour les événements de tokenisation
 */
export interface TokenizationEvent {
  tokenId: string;
  type: 'created' | 'used' | 'expired' | 'revoked';
  timestamp: Date;
  metadata?: Record<string, any>;
}
