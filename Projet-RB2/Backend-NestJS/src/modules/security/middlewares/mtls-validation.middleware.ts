import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

/**
 * Interface pour les options de validation mTLS
 */
export interface MTLSValidationOptions {
  enabled: boolean;
  trustedCNs: string[];
  trustedOUs: string[];
  crlPath?: string;
  ocspEnabled: boolean;
}

/**
 * Middleware pour la validation des certificats mTLS
 * Ce middleware vérifie que les certificats clients sont valides et de confiance
 */
@Injectable()
export class MTLSValidationMiddleware implements NestMiddleware {
  private readonly logger = new Logger(MTLSValidationMiddleware.name);
  private readonly options: MTLSValidationOptions;
  private crl: Buffer | null = null;

  constructor(private readonly configService: ConfigService) {
    this.options = {
      enabled: this.configService.get<boolean>('ZERO_TRUST_MTLS_ENABLED', false),
      trustedCNs: this.configService.get<string>('MTLS_TRUSTED_CNS', '')
        .split(',')
        .map(cn => cn.trim())
        .filter(cn => cn.length > 0),
      trustedOUs: this.configService.get<string>('MTLS_TRUSTED_OUS', '')
        .split(',')
        .map(ou => ou.trim())
        .filter(ou => ou.length > 0),
      crlPath: this.configService.get<string>('MTLS_CRL_PATH'),
      ocspEnabled: this.configService.get<boolean>('MTLS_OCSP_ENABLED', false),
    };

    // Charger la CRL si elle est configurée
    if (this.options.enabled && this.options.crlPath) {
      try {
        const crlPath = path.resolve(process.cwd(), this.options.crlPath);
        if (fs.existsSync(crlPath)) {
          this.crl = fs.readFileSync(crlPath);
          this.logger.log(`Loaded CRL from ${crlPath}`);
        } else {
          this.logger.warn(`CRL file not found: ${crlPath}`);
        }
      } catch (error) {
        this.logger.error('Failed to load CRL:', error);
      }
    }
  }

  /**
   * Méthode principale du middleware
   * @param req Requête Express
   * @param res Réponse Express
   * @param next Fonction next
   */
  use(req: Request, res: Response, next: NextFunction) {
    if (!this.options.enabled) {
      return next();
    }

    try {
      // Récupérer le certificat client depuis la requête
      const clientCert = (req as any).socket?.getPeerCertificate?.();

      // Vérifier si un certificat client est présent
      if (!clientCert || Object.keys(clientCert).length === 0) {
        this.logger.warn('No client certificate provided');
        return res.status(403).json({
          statusCode: 403,
          message: 'Client certificate required',
        });
      }

      // Vérifier si le certificat est valide
      if ((req as any).socket?.authorized === false) {
        this.logger.warn('Client certificate is not authorized');
        return res.status(403).json({
          statusCode: 403,
          message: 'Invalid client certificate',
        });
      }

      // Vérifier si le certificat est révoqué
      if (this.crl && this.isCertificateRevoked(clientCert)) {
        this.logger.warn(`Certificate with serial ${clientCert.serialNumber} is revoked`);
        return res.status(403).json({
          statusCode: 403,
          message: 'Certificate is revoked',
        });
      }

      // Vérifier si le CN est de confiance
      if (
        this.options.trustedCNs.length > 0 &&
        !this.options.trustedCNs.includes(clientCert.subject.CN)
      ) {
        this.logger.warn(`Certificate CN ${clientCert.subject.CN} is not trusted`);
        return res.status(403).json({
          statusCode: 403,
          message: 'Certificate CN is not trusted',
        });
      }

      // Vérifier si l'OU est de confiance
      if (
        this.options.trustedOUs.length > 0 &&
        (!clientCert.subject.OU || !this.options.trustedOUs.includes(clientCert.subject.OU))
      ) {
        this.logger.warn(`Certificate OU ${clientCert.subject.OU} is not trusted`);
        return res.status(403).json({
          statusCode: 403,
          message: 'Certificate OU is not trusted',
        });
      }

      // Ajouter les informations du certificat à la requête pour une utilisation ultérieure
      (req as any).clientCertificate = {
        subject: clientCert.subject,
        issuer: clientCert.issuer,
        serialNumber: clientCert.serialNumber,
        fingerprint: clientCert.fingerprint,
        validFrom: clientCert.valid_from,
        validTo: clientCert.valid_to,
      };

      next();
    } catch (error) {
      this.logger.error('Error validating client certificate:', error);
      return res.status(500).json({
        statusCode: 500,
        message: 'Error validating client certificate',
      });
    }
  }

  /**
   * Vérifie si un certificat est révoqué
   * @param cert Certificat à vérifier
   * @returns true si le certificat est révoqué
   */
  private isCertificateRevoked(cert: any): boolean {
    if (!this.crl) {
      return false;
    }

    try {
      // Dans une implémentation réelle, on utiliserait une bibliothèque comme node-forge
      // pour parser la CRL et vérifier si le certificat est révoqué
      // Ici, on simule avec une vérification basique
      const serialNumber = cert.serialNumber;
      const crlText = this.crl.toString('utf8');
      
      // Vérification simplifiée (à remplacer par une vérification réelle)
      return crlText.includes(serialNumber);
    } catch (error) {
      this.logger.error('Error checking certificate revocation:', error);
      return false;
    }
  }
}
