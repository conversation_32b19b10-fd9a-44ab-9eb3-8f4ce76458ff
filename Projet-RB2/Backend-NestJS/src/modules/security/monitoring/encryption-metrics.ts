import { Injectable } from '@nestjs/common';
import { Counter, Gauge, Histogram } from 'prom-client';

/**
 * Service pour collecter et exposer les métriques liées à l'encryption
 */
@Injectable()
export class EncryptionMetrics {
  private static instance: EncryptionMetrics;
  
  // Compteurs pour les opérations d'encryption
  private encryptionOperationsCounter: Counter;
  private decryptionOperationsCounter: Counter;
  private errorCounter: Counter;
  
  // Histogrammes pour mesurer les temps d'exécution
  private encryptionDurationHistogram: Histogram;
  private decryptionDurationHistogram: Histogram;
  private keyRotationDurationHistogram: Histogram;
  
  // Jauges pour les métriques d'état
  private keyAgeGauge: Gauge;
  private totalKeysGauge: Gauge;

  private constructor() {
    // Initialiser les métriques Prometheus
    this.encryptionOperationsCounter = new Counter({
      name: 'encryption_operations_total',
      help: 'Total number of encryption operations',
      labelNames: ['service', 'status']
    });
    
    this.decryptionOperationsCounter = new Counter({
      name: 'decryption_operations_total',
      help: 'Total number of decryption operations',
      labelNames: ['service', 'status']
    });
    
    this.errorCounter = new Counter({
      name: 'encryption_errors_total',
      help: 'Total number of encryption/decryption errors',
      labelNames: ['operation', 'error_type']
    });
    
    this.encryptionDurationHistogram = new Histogram({
      name: 'encryption_duration_seconds',
      help: 'Duration of encryption operations in seconds',
      labelNames: ['service'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
    });
    
    this.decryptionDurationHistogram = new Histogram({
      name: 'decryption_duration_seconds',
      help: 'Duration of decryption operations in seconds',
      labelNames: ['service'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
    });
    
    this.keyRotationDurationHistogram = new Histogram({
      name: 'key_rotation_duration_seconds',
      help: 'Duration of key rotation operations in seconds',
      labelNames: ['operation', 'status'],
      buckets: [0.1, 0.5, 1, 5, 10, 30, 60]
    });
    
    this.keyAgeGauge = new Gauge({
      name: 'encryption_key_age_days',
      help: 'Age of encryption keys in days',
      labelNames: ['key_id', 'algorithm']
    });
    
    this.totalKeysGauge = new Gauge({
      name: 'encryption_total_keys',
      help: 'Total number of encryption keys',
      labelNames: ['status']
    });
  }

  /**
   * Obtenir l'instance singleton des métriques d'encryption
   */
  public static getInstance(): EncryptionMetrics {
    if (!EncryptionMetrics.instance) {
      EncryptionMetrics.instance = new EncryptionMetrics();
    }
    return EncryptionMetrics.instance;
  }

  /**
   * Incrémenter le compteur d'opérations d'encryption
   */
  public incrementEncryption(service: string, status: 'success' | 'failure'): void {
    this.encryptionOperationsCounter.inc({ service, status });
  }

  /**
   * Incrémenter le compteur d'opérations de décryption
   */
  public incrementDecryption(service: string, status: 'success' | 'failure'): void {
    this.decryptionOperationsCounter.inc({ service, status });
  }

  /**
   * Incrémenter le compteur d'erreurs
   */
  public incrementError(operation: string, errorType: string): void {
    this.errorCounter.inc({ operation, error_type: errorType });
  }

  /**
   * Observer la durée d'une opération d'encryption
   */
  public observeEncryptionDuration(service: string, durationMs: number): void {
    this.encryptionDurationHistogram.observe({ service }, durationMs / 1000);
  }

  /**
   * Observer la durée d'une opération de décryption
   */
  public observeDecryptionDuration(service: string, durationMs: number): void {
    this.decryptionDurationHistogram.observe({ service }, durationMs / 1000);
  }

  /**
   * Observer la durée d'une opération
   */
  public observeOperation(operation: string, status: string, durationMs: number): void {
    this.keyRotationDurationHistogram.observe({ operation, status }, durationMs / 1000);
  }

  /**
   * Mettre à jour l'âge d'une clé
   */
  public setKeyAge(keyId: string, algorithm: string, ageInDays: number): void {
    this.keyAgeGauge.set({ key_id: keyId, algorithm }, ageInDays);
  }

  /**
   * Mettre à jour le nombre total de clés
   */
  public setTotalKeys(status: 'active' | 'archived', count: number): void {
    this.totalKeysGauge.set({ status }, count);
  }
}
