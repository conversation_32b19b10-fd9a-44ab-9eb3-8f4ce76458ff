import { Modu<PERSON> } from '@nestjs/common';
import { SecurityService } from './security.service';
import { CryptoComplianceService } from './services/crypto-compliance.service';
import { CryptoComplianceController } from './controllers/crypto-compliance.controller';
import { SecurityController } from './security.controller';
import { IncidentManagementController } from './controllers/incident-management.controller';
import { SecurityDashboardController } from './controllers/security-dashboard.controller';
import { EncryptionController } from './controllers/encryption.controller';
import { CryptoTestingController } from './controllers/crypto-testing.controller';
import { CertificateController } from './controllers/certificate.controller';
import { CertificateMonitoringController } from './controllers/certificate-monitoring.controller';
import { MicroserviceSecurityController } from './controllers/microservice-security.controller';
import { SensitiveDataEncryptionController } from './controllers/sensitive-data-encryption.controller';
import { FileSecurityController } from './controllers/file-security.controller';
import { SecurityNotificationController } from './controllers/security-notification.controller';
import { PrismaModule } from '../../prisma/prisma.module';
import { AnomalyDetectionService } from './services/anomaly-detection.service';
import { RateLimiterService } from './services/rate-limiter.service';
import { ContentSecurityService } from './services/content-security.service';
import { SecurityMonitoringService } from './services/security-monitoring.service';
import { SecurityEventService } from './services/security-event.service';
import { FileSecurityService } from './services/file-security.service';
import { SecurityNotificationService } from './services/security-notification.service';
import { EventsModule } from '../events/events.module';
import { EncryptionModule } from './encryption.module';
import { SecurityTrainingService } from './services/security-training.service';
import { IncidentManagementService } from './services/incident-management.service';
import { AutomatedSecurityAuditService } from './services/automated-security-audit.service';
import { DependencyMonitoringService } from './services/dependency-monitoring.service';
import { CacheModule } from '../cache/cache.module';

@Module({
  imports: [
    PrismaModule,
    EventsModule,
    EncryptionModule,
    CacheModule,
  ],
  controllers: [
    SecurityController,
    EncryptionController,
    CryptoTestingController,
    CertificateController,
    CertificateMonitoringController,
    MicroserviceSecurityController,
    SensitiveDataEncryptionController,
    CryptoComplianceController,
    FileSecurityController,
    SecurityNotificationController,
    IncidentManagementController,
    SecurityDashboardController
  ],
  providers: [
    SecurityService,
    AnomalyDetectionService,
    RateLimiterService,
    ContentSecurityService,
    SecurityMonitoringService,
    SecurityEventService,
    FileSecurityService,
    CryptoComplianceService,
    SecurityTrainingService,
    SecurityNotificationService,
    IncidentManagementService,
    AutomatedSecurityAuditService,
    DependencyMonitoringService,
  ],
  exports: [
    SecurityService,
    AnomalyDetectionService,
    RateLimiterService,
    ContentSecurityService,
    SecurityMonitoringService,
    SecurityEventService,
    FileSecurityService,
    EncryptionModule,
    SecurityTrainingService,
    SecurityNotificationService,
    IncidentManagementService,
  ],
})
export class SecurityModule {}
