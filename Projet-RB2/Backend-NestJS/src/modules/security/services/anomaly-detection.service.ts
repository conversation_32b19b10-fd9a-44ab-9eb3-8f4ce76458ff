import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { SecurityEventService } from './security-event.service';
import { SecurityEventSeverity } from '../dto/create-security-event.dto';

interface RequestPattern {
  ip: string;
  path: string;
  method: string;
  userAgent: string;
  timestamp: Date;
  userId?: string;
}

@Injectable()
export class AnomalyDetectionService {
  private readonly logger = new Logger(AnomalyDetectionService.name);
  private readonly requestPatterns: Map<string, RequestPattern[]> = new Map();
  private readonly blockedIPs: Set<string> = new Set();
  private readonly ipRequestCounts: Map<string, number> = new Map();
  private readonly suspiciousPaths: string[] = [
    '/admin',
    '/wp-admin',
    '/phpmyadmin',
    '/wp-login',
    '/.env',
    '/config',
    '/.git',
  ];

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly securityEventService: SecurityEventService,
  ) {
    // Charger les IPs bloquées depuis la base de données
    this.loadBlockedIPs();

    // Nettoyer les compteurs de requêtes toutes les heures
    setInterval(() => {
      this.ipRequestCounts.clear();
      this.logger.debug('Compteurs de requêtes par IP réinitialisés');
    }, 60 * 60 * 1000); // 1 heure
  }

  private async loadBlockedIPs() {
    try {
      const blockedIPRecords = await this.prisma.blockedIP.findMany({
        where: {
          expiresAt: {
            gt: new Date(),
          },
        },
      });

      blockedIPRecords.forEach((record: any) => {
        this.blockedIPs.add(record.ip);
      });

      this.logger.log(`${this.blockedIPs.size} IPs bloquées chargées depuis la base de données`);
    } catch (error) {
      this.logger.error(`Erreur lors du chargement des IPs bloquées: ${error.message}`);
    }
  }

  async analyzeRequest(req: Request): Promise<boolean> {
    const ip = this.getClientIP(req);
    const path = req.path;
    const method = req.method;
    const userAgent = req.headers['user-agent'] || 'unknown';
    // Utiliser une assertion de type pour accéder à la propriété id
    const userId = req.user ? (req.user as any).id : undefined;

    // Vérifier si l'IP est bloquée
    if (this.isIPBlocked(ip)) {
      this.logger.warn(`Requête bloquée de l'IP ${ip}`);
      return false;
    }

    // Incrémenter le compteur de requêtes pour cette IP
    this.incrementIPRequestCount(ip);

    // Créer un modèle de requête
    const requestPattern: RequestPattern = {
      ip,
      path,
      method,
      userAgent,
      timestamp: new Date(),
      userId,
    };

    // Ajouter le modèle à l'historique
    this.addRequestPattern(ip, requestPattern);

    // Analyser les anomalies
    const anomalies = await this.detectAnomalies(requestPattern);

    // Si des anomalies sont détectées, enregistrer un événement de sécurité
    if (anomalies.length > 0) {
      await this.securityEventService.logSecurityEvent({
        eventType: 'ANOMALY_DETECTED',
        severity: SecurityEventSeverity.WARNING,
        source: 'ANOMALY_DETECTION',
        details: {
          ip,
          path,
          method,
          userAgent,
          anomalies,
          description: `Anomalies détectées pour l'IP ${ip}: ${anomalies.join(', ')}`
        }
      });

      // Si trop d'anomalies sont détectées, bloquer l'IP
      if (anomalies.length >= 3) {
        await this.blockIP(ip, 'Trop d\'anomalies détectées');
        return false;
      }
    }

    return true;
  }

  private getClientIP(req: Request): string {
    return (
      req.headers['x-forwarded-for'] ||
      req.socket.remoteAddress ||
      'unknown'
    ).toString();
  }

  private isIPBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  private incrementIPRequestCount(ip: string) {
    const currentCount = this.ipRequestCounts.get(ip) || 0;
    this.ipRequestCounts.set(ip, currentCount + 1);

    // Vérifier si l'IP dépasse le seuil de requêtes
    const threshold = this.configService.get<number>('security.rateLimit.max', 100);
    if (currentCount + 1 > threshold) {
      this.logger.warn(`L'IP ${ip} a dépassé le seuil de requêtes (${threshold})`);
      this.blockIP(ip, 'Dépassement du seuil de requêtes');
    }
  }

  private addRequestPattern(ip: string, pattern: RequestPattern) {
    if (!this.requestPatterns.has(ip)) {
      this.requestPatterns.set(ip, []);
    }

    const patterns = this.requestPatterns.get(ip);
    patterns.push(pattern);

    // Limiter le nombre de modèles stockés
    if (patterns.length > 100) {
      patterns.shift();
    }
  }

  private async detectAnomalies(pattern: RequestPattern): Promise<string[]> {
    const anomalies: string[] = [];

    // Vérifier les chemins suspects
    if (this.suspiciousPaths.some(p => pattern.path.includes(p))) {
      anomalies.push('Chemin suspect');
    }

    // Vérifier les outils de scan de sécurité
    if (this.isSecurityScanTool(pattern.userAgent)) {
      anomalies.push('Outil de scan de sécurité détecté');
    }

    // Vérifier les méthodes HTTP inhabituelles
    if (['OPTIONS', 'TRACE', 'CONNECT'].includes(pattern.method)) {
      anomalies.push('Méthode HTTP inhabituelle');
    }

    // Vérifier le taux de requêtes
    const requestCount = this.ipRequestCounts.get(pattern.ip) || 0;
    if (requestCount > 50) {
      anomalies.push('Taux de requêtes élevé');
    }

    return anomalies;
  }

  private isSecurityScanTool(userAgent: string): boolean {
    const securityTools = [
      'nmap',
      'nikto',
      'sqlmap',
      'burp',
      'zap',
      'acunetix',
      'nessus',
      'metasploit',
      'hydra',
      'dirbuster',
    ];

    return securityTools.some(tool => userAgent.toLowerCase().includes(tool));
  }

  async blockIP(ip: string, reason: string, duration: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      // Ajouter l'IP à la liste des IPs bloquées
      this.blockedIPs.add(ip);

      // Calculer la date d'expiration
      const expiresAt = new Date(Date.now() + duration);

      // Enregistrer l'IP bloquée dans la base de données
      await this.prisma.blockedIP.create({
        data: {
          ip,
          reason,
          expiresAt,
        },
      });

      // Enregistrer un événement de sécurité
      await this.securityEventService.logSecurityEvent({
        eventType: 'IP_BLOCKED',
        severity: SecurityEventSeverity.ERROR,
        source: 'ANOMALY_DETECTION',
        details: {
          ip,
          reason,
          expiresAt,
          description: `IP ${ip} bloquée pour la raison: ${reason}`
        }
      });

      this.logger.warn(`IP ${ip} bloquée jusqu'au ${expiresAt.toISOString()} pour la raison: ${reason}`);
    } catch (error) {
      this.logger.error(`Erreur lors du blocage de l'IP ${ip}: ${error.message}`);
    }
  }

  async unblockIP(ip: string): Promise<void> {
    try {
      // Supprimer l'IP de la liste des IPs bloquées
      this.blockedIPs.delete(ip);

      // Supprimer l'IP bloquée de la base de données
      await this.prisma.blockedIP.deleteMany({
        where: { ip },
      });

      // Enregistrer un événement de sécurité
      await this.securityEventService.logSecurityEvent({
        eventType: 'IP_UNBLOCKED',
        severity: SecurityEventSeverity.INFO,
        source: 'ANOMALY_DETECTION',
        details: {
          ip,
          description: `IP ${ip} débloquée`
        }
      });

      this.logger.log(`IP ${ip} débloquée`);
    } catch (error) {
      this.logger.error(`Erreur lors du déblocage de l'IP ${ip}: ${error.message}`);
    }
  }

  async getBlockedIPs() {
    return this.prisma.blockedIP.findMany({
      where: {
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
