import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import * as path from 'path';
import * as child_process from 'child_process';
import { promisify } from 'util';
import { CertificateManagementService } from './certificate-management.service';

const exec = promisify(child_process.exec);

/**
 * Interface pour les informations de certificat révoqué
 */
export interface RevokedCertificate {
  serialNumber: string;
  revocationDate: Date;
  reason: string;
}

/**
 * Service de gestion des listes de révocation de certificats (CRL)
 * Ce service gère la création, la mise à jour et la distribution des CRL
 * ainsi que la vérification du statut de révocation des certificats
 */
@Injectable()
export class CertificateRevocationService implements OnModuleInit {
  private readonly logger = new Logger(CertificateRevocationService.name);
  private readonly certsDir: string;
  private readonly crlPath: string;
  private readonly indexPath: string;
  private readonly crlnumberPath: string;
  private readonly opensslConfigPath: string;
  private readonly crlUpdateInterval: number; // en heures
  private readonly ocspEnabled: boolean;
  private readonly ocspPort: number;
  private readonly ocspUrl: string;
  private revokedCertificates: Map<string, RevokedCertificate> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly certificateManagementService: CertificateManagementService
  ) {
    this.certsDir = this.configService.get<string>('CERTS_DIR', './certs');
    this.crlPath = this.configService.get<string>('MTLS_CRL_PATH', path.join(this.certsDir, 'ca.crl'));
    this.indexPath = path.join(this.certsDir, 'index.txt');
    this.crlnumberPath = path.join(this.certsDir, 'crlnumber');
    this.opensslConfigPath = path.join(this.certsDir, 'openssl.cnf');
    this.crlUpdateInterval = this.configService.get<number>('CRL_UPDATE_INTERVAL_HOURS', 24);
    this.ocspEnabled = this.configService.get<boolean>('MTLS_OCSP_ENABLED', false);
    this.ocspPort = this.configService.get<number>('OCSP_PORT', 9080);
    this.ocspUrl = this.configService.get<string>('OCSP_URL', `http://localhost:${this.ocspPort}`);
  }

  /**
   * Initialisation du service
   */
  async onModuleInit() {
    try {
      await this.ensureDirectoriesAndFiles();
      await this.loadRevokedCertificates();
      
      if (this.ocspEnabled) {
        await this.startOcspResponder();
      }
    } catch (error) {
      this.logger.error('Failed to initialize certificate revocation service:', error);
    }
  }

  /**
   * S'assure que les répertoires et fichiers nécessaires existent
   */
  private async ensureDirectoriesAndFiles(): Promise<void> {
    try {
      // Créer le répertoire des certificats s'il n'existe pas
      if (!fsSync.existsSync(this.certsDir)) {
        await fs.mkdir(this.certsDir, { recursive: true });
        this.logger.log(`Created certificates directory: ${this.certsDir}`);
      }

      // Créer le fichier index.txt s'il n'existe pas
      if (!fsSync.existsSync(this.indexPath)) {
        await fs.writeFile(this.indexPath, '');
        this.logger.log(`Created index file: ${this.indexPath}`);
      }

      // Créer le fichier crlnumber s'il n'existe pas
      if (!fsSync.existsSync(this.crlnumberPath)) {
        await fs.writeFile(this.crlnumberPath, '01\n');
        this.logger.log(`Created CRL number file: ${this.crlnumberPath}`);
      }

      // Créer le fichier de configuration OpenSSL s'il n'existe pas
      if (!fsSync.existsSync(this.opensslConfigPath)) {
        await this.createOpenSSLConfig();
        this.logger.log(`Created OpenSSL config file: ${this.opensslConfigPath}`);
      }
    } catch (error) {
      this.logger.error('Failed to ensure directories and files:', error);
      throw error;
    }
  }

  /**
   * Crée le fichier de configuration OpenSSL
   */
  private async createOpenSSLConfig(): Promise<void> {
    const config = `
[ ca ]
default_ca = CA_default

[ CA_default ]
dir = ${this.certsDir}
certs = $dir
new_certs_dir = $dir/newcerts
database = $dir/index.txt
serial = $dir/serial
RANDFILE = $dir/.rand
private_key = $dir/ca.key
certificate = $dir/ca.crt
default_days = 365
default_crl_days = 30
default_md = sha256
preserve = no
policy = policy_match
crl_extensions = crl_ext

[ policy_match ]
countryName = match
stateOrProvinceName = match
organizationName = match
organizationalUnitName = optional
commonName = supplied
emailAddress = optional

[ req ]
default_bits = 2048
default_keyfile = privkey.pem
distinguished_name = req_distinguished_name
attributes = req_attributes
x509_extensions = v3_ca
string_mask = utf8only

[ req_distinguished_name ]
countryName = Country Name (2 letter code)
countryName_default = FR
stateOrProvinceName = State or Province Name (full name)
stateOrProvinceName_default = Ile-de-France
localityName = Locality Name (eg, city)
localityName_default = Paris
organizationName = Organization Name (eg, company)
organizationName_default = Retreat And Be
organizationalUnitName = Organizational Unit Name (eg, section)
organizationalUnitName_default = Security
commonName = Common Name (e.g. server FQDN or YOUR name)
commonName_max = 64
emailAddress = Email Address
emailAddress_max = 64

[ req_attributes ]
challengePassword = A challenge password
challengePassword_min = 4
challengePassword_max = 20
unstructuredName = An optional company name

[ v3_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

[ v3_intermediate_ca ]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
basicConstraints = critical, CA:true, pathlen:0
keyUsage = critical, digitalSignature, cRLSign, keyCertSign

[ usr_cert ]
basicConstraints = CA:FALSE
nsCertType = client, email
nsComment = "OpenSSL Generated Client Certificate"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth, emailProtection

[ server_cert ]
basicConstraints = CA:FALSE
nsCertType = server
nsComment = "OpenSSL Generated Server Certificate"
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer:always
keyUsage = critical, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth

[ crl_ext ]
authorityKeyIdentifier = keyid:always

[ ocsp ]
basicConstraints = CA:FALSE
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid,issuer
keyUsage = critical, digitalSignature
extendedKeyUsage = critical, OCSPSigning
`;

    await fs.writeFile(this.opensslConfigPath, config);
  }

  /**
   * Charge la liste des certificats révoqués depuis le fichier index.txt
   */
  private async loadRevokedCertificates(): Promise<void> {
    try {
      if (!fsSync.existsSync(this.indexPath)) {
        return;
      }

      const indexContent = await fs.readFile(this.indexPath, 'utf-8');
      const lines = indexContent.split('\n').filter(line => line.trim().length > 0);

      this.revokedCertificates.clear();

      for (const line of lines) {
        // Format: R\tRevocation-Date\tExpiry-Date\tSerial-Number\tunknown\tSubject
        if (line.startsWith('R')) {
          const parts = line.split('\t');
          if (parts.length >= 4) {
            const serialNumber = parts[3];
            const revocationDate = new Date(parts[1]);
            const reason = 'unknown'; // Le fichier index.txt ne stocke pas la raison

            this.revokedCertificates.set(serialNumber, {
              serialNumber,
              revocationDate,
              reason
            });
          }
        }
      }

      this.logger.log(`Loaded ${this.revokedCertificates.size} revoked certificates`);
    } catch (error) {
      this.logger.error('Failed to load revoked certificates:', error);
    }
  }

  /**
   * Révoque un certificat
   * @param certPath Chemin du certificat à révoquer
   * @param reason Raison de la révocation
   * @returns true si la révocation a réussi
   */
  async revokeCertificate(certPath: string, reason: string = 'unspecified'): Promise<boolean> {
    try {
      const caKeyPath = path.join(this.certsDir, 'ca.key');
      const caCertPath = path.join(this.certsDir, 'ca.crt');

      // Vérifier si les fichiers nécessaires existent
      if (!fsSync.existsSync(caKeyPath) || !fsSync.existsSync(caCertPath) || !fsSync.existsSync(certPath)) {
        this.logger.error(`Required files not found for certificate revocation: ${certPath}`);
        return false;
      }

      // Extraire le numéro de série du certificat
      const { stdout: certInfo } = await exec(`openssl x509 -in "${certPath}" -noout -serial`);
      const serialMatch = certInfo.match(/serial=([0-9A-Fa-f]+)/);
      
      if (!serialMatch || !serialMatch[1]) {
        this.logger.error(`Could not extract serial number from certificate: ${certPath}`);
        return false;
      }
      
      const serialNumber = serialMatch[1];

      // Révoquer le certificat
      await exec(
        `openssl ca -config "${this.opensslConfigPath}" -revoke "${certPath}" -keyfile "${caKeyPath}" -cert "${caCertPath}" -crl_reason ${reason}`
      );

      // Générer la CRL
      await this.generateCRL();

      // Mettre à jour la liste des certificats révoqués
      const now = new Date();
      this.revokedCertificates.set(serialNumber, {
        serialNumber,
        revocationDate: now,
        reason
      });

      this.logger.log(`Revoked certificate: ${certPath}, serial: ${serialNumber}, reason: ${reason}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to revoke certificate ${certPath}:`, error);
      return false;
    }
  }

  /**
   * Génère la liste de révocation de certificats (CRL)
   * @returns Chemin du fichier CRL
   */
  async generateCRL(): Promise<string> {
    try {
      const caKeyPath = path.join(this.certsDir, 'ca.key');
      const caCertPath = path.join(this.certsDir, 'ca.crt');

      // Vérifier si les fichiers nécessaires existent
      if (!fsSync.existsSync(caKeyPath) || !fsSync.existsSync(caCertPath)) {
        this.logger.error('CA key or certificate not found');
        throw new Error('CA key or certificate not found');
      }

      // Générer la CRL
      await exec(
        `openssl ca -config "${this.opensslConfigPath}" -gencrl -keyfile "${caKeyPath}" -cert "${caCertPath}" -out "${this.crlPath}"`
      );

      this.logger.log(`Generated CRL: ${this.crlPath}`);
      return this.crlPath;
    } catch (error) {
      this.logger.error('Failed to generate CRL:', error);
      throw error;
    }
  }

  /**
   * Vérifie si un certificat est révoqué
   * @param certPath Chemin du certificat à vérifier
   * @returns true si le certificat est révoqué
   */
  async isCertificateRevoked(certPath: string): Promise<boolean> {
    try {
      // Extraire le numéro de série du certificat
      const { stdout: certInfo } = await exec(`openssl x509 -in "${certPath}" -noout -serial`);
      const serialMatch = certInfo.match(/serial=([0-9A-Fa-f]+)/);
      
      if (!serialMatch || !serialMatch[1]) {
        this.logger.error(`Could not extract serial number from certificate: ${certPath}`);
        return false;
      }
      
      const serialNumber = serialMatch[1];

      // Vérifier si le certificat est dans la liste des certificats révoqués
      return this.revokedCertificates.has(serialNumber);
    } catch (error) {
      this.logger.error(`Failed to check if certificate is revoked: ${certPath}`, error);
      return false;
    }
  }

  /**
   * Obtient les informations d'un certificat révoqué
   * @param serialNumber Numéro de série du certificat
   * @returns Informations du certificat révoqué ou null si le certificat n'est pas révoqué
   */
  getRevokedCertificateInfo(serialNumber: string): RevokedCertificate | null {
    return this.revokedCertificates.get(serialNumber) || null;
  }

  /**
   * Obtient la liste de tous les certificats révoqués
   * @returns Liste des certificats révoqués
   */
  getAllRevokedCertificates(): RevokedCertificate[] {
    return Array.from(this.revokedCertificates.values());
  }

  /**
   * Démarre le répondeur OCSP
   */
  private async startOcspResponder(): Promise<void> {
    try {
      const caKeyPath = path.join(this.certsDir, 'ca.key');
      const caCertPath = path.join(this.certsDir, 'ca.crt');

      // Vérifier si les fichiers nécessaires existent
      if (!fsSync.existsSync(caKeyPath) || !fsSync.existsSync(caCertPath)) {
        this.logger.error('CA key or certificate not found');
        return;
      }

      // Créer un certificat pour le répondeur OCSP
      const ocspKeyPath = path.join(this.certsDir, 'ocsp.key');
      const ocspCertPath = path.join(this.certsDir, 'ocsp.crt');
      const ocspCsrPath = path.join(this.certsDir, 'ocsp.csr');

      // Générer la clé privée pour le répondeur OCSP
      await exec(`openssl genrsa -out "${ocspKeyPath}" 2048`);

      // Générer la demande de signature de certificat (CSR)
      await exec(
        `openssl req -new -key "${ocspKeyPath}" -out "${ocspCsrPath}" -subj "/C=FR/ST=Ile-de-France/L=Paris/O=Retreat And Be/OU=Security/CN=ocsp.retreatandbe.com"`
      );

      // Signer la CSR avec la CA pour générer le certificat OCSP
      await exec(
        `openssl ca -config "${this.opensslConfigPath}" -extensions ocsp -days 365 -notext -md sha256 -in "${ocspCsrPath}" -out "${ocspCertPath}"`
      );

      // Démarrer le répondeur OCSP en arrière-plan
      const ocspProcess = child_process.spawn('openssl', [
        'ocsp',
        '-port', this.ocspPort.toString(),
        '-index', this.indexPath,
        '-CA', caCertPath,
        '-rsigner', ocspCertPath,
        '-rkey', ocspKeyPath,
        '-text',
        '-out', path.join(this.certsDir, 'ocsp.log')
      ], {
        detached: true,
        stdio: 'ignore'
      });

      ocspProcess.unref();

      this.logger.log(`OCSP responder started on port ${this.ocspPort}`);
    } catch (error) {
      this.logger.error('Failed to start OCSP responder:', error);
    }
  }

  /**
   * Vérifie le statut d'un certificat via OCSP
   * @param certPath Chemin du certificat à vérifier
   * @returns Statut du certificat (good, revoked, unknown)
   */
  async checkCertificateStatusViaOCSP(certPath: string): Promise<'good' | 'revoked' | 'unknown'> {
    try {
      if (!this.ocspEnabled) {
        return 'unknown';
      }

      const caCertPath = path.join(this.certsDir, 'ca.crt');

      // Vérifier si les fichiers nécessaires existent
      if (!fsSync.existsSync(caCertPath) || !fsSync.existsSync(certPath)) {
        this.logger.error('CA certificate or target certificate not found');
        return 'unknown';
      }

      // Interroger le répondeur OCSP
      const { stdout } = await exec(
        `openssl ocsp -issuer "${caCertPath}" -cert "${certPath}" -url ${this.ocspUrl} -resp_text`
      );

      if (stdout.includes('OCSP Response Status: successful')) {
        if (stdout.includes('Cert Status: good')) {
          return 'good';
        } else if (stdout.includes('Cert Status: revoked')) {
          return 'revoked';
        }
      }

      return 'unknown';
    } catch (error) {
      this.logger.error(`Failed to check certificate status via OCSP: ${certPath}`, error);
      return 'unknown';
    }
  }

  /**
   * Met à jour périodiquement la CRL
   * Cette méthode est exécutée selon l'intervalle configuré
   */
  @Cron(CronExpression.EVERY_HOUR)
  async scheduledCRLUpdate(): Promise<void> {
    try {
      // Vérifier si c'est le moment de mettre à jour la CRL
      const crlStat = fsSync.existsSync(this.crlPath) ? await fs.stat(this.crlPath) : null;
      const now = new Date();
      
      if (!crlStat || ((now.getTime() - crlStat.mtime.getTime()) / (1000 * 60 * 60)) >= this.crlUpdateInterval) {
        this.logger.log('Scheduled CRL update triggered');
        await this.generateCRL();
      }
    } catch (error) {
      this.logger.error('Scheduled CRL update failed:', error);
    }
  }
}
