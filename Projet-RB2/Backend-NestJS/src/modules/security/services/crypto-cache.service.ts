import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createHash } from 'crypto';

/**
 * Interface pour les entrées de cache
 */
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

/**
 * Service de cache pour les opérations cryptographiques
 * Ce service permet de mettre en cache les résultats des opérations cryptographiques
 * coûteuses pour éviter de les recalculer.
 */
@Injectable()
export class CryptoCacheService {
  private readonly logger = new Logger(CryptoCacheService.name);
  private readonly cache: Map<string, CacheEntry<any>> = new Map();
  private readonly enabled: boolean;
  private readonly defaultTtl: number; // Durée de vie par défaut en millisecondes
  private readonly maxSize: number; // Taille maximale du cache (nombre d'entrées)
  private readonly cleanupInterval: NodeJS.Timeout;
  private readonly statsInterval: NodeJS.Timeout;
  private hits = 0;
  private misses = 0;

  constructor(private readonly configService: ConfigService) {
    this.enabled = this.configService.get<boolean>('CRYPTO_CACHE_ENABLED', true);
    this.defaultTtl = this.configService.get<number>('CRYPTO_CACHE_TTL', 60 * 60 * 1000); // 1 heure par défaut
    this.maxSize = this.configService.get<number>('CRYPTO_CACHE_MAX_SIZE', 1000);

    // Nettoyer le cache périodiquement
    this.cleanupInterval = setInterval(() => this.cleanup(), 60 * 1000); // Toutes les minutes

    // Afficher les statistiques périodiquement
    this.statsInterval = setInterval(() => this.logStats(), 10 * 60 * 1000); // Toutes les 10 minutes
  }

  /**
   * Génère une clé de cache à partir des paramètres
   * @param operationType Type d'opération
   * @param params Paramètres de l'opération
   * @returns Clé de cache
   */
  private generateKey(operationType: string, params: any[]): string {
    // Convertir les paramètres en chaîne JSON
    const paramsString = JSON.stringify(params, (key, value) => {
      // Traiter les Buffer spécialement
      if (Buffer.isBuffer(value)) {
        return {
          type: 'Buffer',
          data: value.toString('base64'),
        };
      }
      return value;
    });

    // Générer un hash SHA-256 de la chaîne
    return `${operationType}:${createHash('sha256').update(paramsString).digest('hex')}`;
  }

  /**
   * Récupère une valeur du cache
   * @param operationType Type d'opération
   * @param params Paramètres de l'opération
   * @returns Valeur mise en cache ou null si non trouvée
   */
  get<T>(operationType: string, params: any[]): T | null {
    if (!this.enabled) {
      this.misses++;
      return null;
    }

    const key = this.generateKey(operationType, params);
    const entry = this.cache.get(key);

    if (!entry) {
      this.misses++;
      return null;
    }

    // Vérifier si l'entrée est expirée
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.misses++;
      return null;
    }

    // Incrémenter le compteur de hits
    entry.hits++;
    this.hits++;

    return entry.value;
  }

  /**
   * Met une valeur en cache
   * @param operationType Type d'opération
   * @param params Paramètres de l'opération
   * @param value Valeur à mettre en cache
   * @param ttl Durée de vie en millisecondes (optionnel)
   */
  set<T>(operationType: string, params: any[], value: T, ttl?: number): void {
    if (!this.enabled) {
      return;
    }

    const key = this.generateKey(operationType, params);
    
    // Vérifier si le cache a atteint sa taille maximale
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTtl,
      hits: 0,
    });
  }

  /**
   * Supprime une entrée du cache
   * @param operationType Type d'opération
   * @param params Paramètres de l'opération
   * @returns true si l'entrée a été supprimée, false sinon
   */
  delete(operationType: string, params: any[]): boolean {
    const key = this.generateKey(operationType, params);
    return this.cache.delete(key);
  }

  /**
   * Vide le cache
   */
  clear(): void {
    this.cache.clear();
    this.logger.log('Cache cleared');
  }

  /**
   * Nettoie les entrées expirées du cache
   */
  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.logger.debug(`Cleaned up ${expiredCount} expired cache entries`);
    }
  }

  /**
   * Supprime l'entrée la plus ancienne du cache
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestKey = key;
        oldestTimestamp = entry.timestamp;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.logger.debug(`Evicted oldest cache entry: ${oldestKey}`);
    }
  }

  /**
   * Affiche les statistiques du cache
   */
  private logStats(): void {
    const totalRequests = this.hits + this.misses;
    const hitRate = totalRequests > 0 ? (this.hits / totalRequests) * 100 : 0;

    this.logger.log(
      `Cache stats: ${this.cache.size} entries, ${this.hits} hits, ${this.misses} misses, ${hitRate.toFixed(2)}% hit rate`,
    );
  }

  /**
   * Retourne les statistiques du cache
   * @returns Statistiques du cache
   */
  getStats(): Record<string, any> {
    const totalRequests = this.hits + this.misses;
    const hitRate = totalRequests > 0 ? (this.hits / totalRequests) * 100 : 0;

    return {
      enabled: this.enabled,
      size: this.cache.size,
      maxSize: this.maxSize,
      hits: this.hits,
      misses: this.misses,
      hitRate: hitRate,
      defaultTtl: this.defaultTtl,
    };
  }

  /**
   * Retourne les entrées du cache
   * @returns Entrées du cache
   */
  getEntries(): Record<string, any>[] {
    const entries: Record<string, any>[] = [];

    for (const [key, entry] of this.cache.entries()) {
      entries.push({
        key,
        hits: entry.hits,
        age: Date.now() - entry.timestamp,
        ttl: entry.ttl,
        remaining: Math.max(0, entry.timestamp + entry.ttl - Date.now()),
      });
    }

    return entries;
  }

  /**
   * Nettoie les ressources lors de la destruction du service
   */
  onModuleDestroy() {
    clearInterval(this.cleanupInterval);
    clearInterval(this.statsInterval);
  }
}
