import { Injectable, Logger } from '@nestjs/common';

export interface ComplianceRequirement {
  id: string;
  label: string;
  description: string;
  regulation: 'PCI DSS' | 'GDPR' | 'SOC2' | 'HIPAA' | string;
  check: () => Promise<ComplianceCheckResult>;
}

export interface ComplianceCheckResult {
  requirementId: string;
  passed: boolean;
  details?: string;
  timestamp: Date;
}

export interface ComplianceReport {
  generatedAt: Date;
  results: ComplianceCheckResult[];
  summary: {
    passed: number;
    failed: number;
    total: number;
  };
}

import { KeyManagementService } from './key-management.service';

@Injectable()
export class CryptoComplianceService {
  private readonly logger = new Logger(CryptoComplianceService.name);
  private requirements: ComplianceRequirement[] = [];

  constructor(private readonly keyManagementService: KeyManagementService) {
    this.initDefaultRequirements();
  }

  /**
   * Initialise les exigences de conformité par défaut (exemples)
   */
  private initDefaultRequirements() {
    this.requirements = [
      {
        id: 'pci-key-length',
        label: 'Longueur minimale des clés',
        description: 'Les clés de chiffrement doivent avoir une longueur minimale de 256 bits (AES, RSA 2048+, ECC 256+)',
        regulation: 'PCI DSS',
        check: async () => {
          // Vérification réelle de la longueur des clés actives
          try {
            const keyMetadata = await this.keyManagementService.fetchKeyMetadata();
            const nonCompliantKeys: string[] = [];
            for (const [keyId, metadata] of Object.entries(keyMetadata)) {
              if (metadata.status === 'active' || metadata.status === 'rotating') {
                const algo = metadata.algorithm || 'aes-256-gcm';
                let minLengthBits = 256;
                if (algo.startsWith('rsa')) minLengthBits = 2048;
                if (algo.startsWith('ecc')) minLengthBits = 256;
                // Récupérer la clé réelle pour calculer sa longueur
                let keyLengthBits = 0;
                try {
                  const keyObj = await this.keyManagementService.getKey(keyId);
                  keyLengthBits = keyObj.key.length * 8;
                } catch {}
                if (keyLengthBits < minLengthBits) {
                  nonCompliantKeys.push(`${keyId} (${algo}, ${keyLengthBits} bits)`);
                }
              }
            }
            return {
              requirementId: 'pci-key-length',
              passed: nonCompliantKeys.length === 0,
              details: nonCompliantKeys.length === 0
                ? 'Toutes les clés actives sont conformes à la longueur minimale.'
                : `Clés non conformes : ${nonCompliantKeys.join(', ')}`,
              timestamp: new Date(),
            };
          } catch (e) {
            return {
              requirementId: 'pci-key-length',
              passed: false,
              details: `Erreur lors de la vérification des clés : ${e}`,
              timestamp: new Date(),
            };
          }
        },
      },
      {
        id: 'gdpr-data-encryption',
        label: 'Chiffrement des données personnelles',
        description: 'Toutes les données personnelles doivent être chiffrées au repos et en transit',
        regulation: 'GDPR',
        check: async () => {
          // Contrôle réel GDPR : toutes les clés utilisées pour les données personnelles doivent être actives
          // (Exemple : on considère que les clés dont le purpose est 'encryption' et le nom contient 'user' ou 'personal' sont liées aux données personnelles)
          try {
            const keyMetadata = await this.keyManagementService.fetchKeyMetadata();
            const nonCompliantKeys: string[] = [];
            for (const [keyId, metadata] of Object.entries(keyMetadata)) {
              if (
                metadata.purpose === 'encryption' &&
                (keyId.includes('user') || keyId.includes('personal'))
              ) {
                if (metadata.status !== 'active') {
                  nonCompliantKeys.push(`${keyId} (status: ${metadata.status})`);
                }
              }
            }
            return {
              requirementId: 'gdpr-data-encryption',
              passed: nonCompliantKeys.length === 0,
              details: nonCompliantKeys.length === 0
                ? 'Toutes les clés de données personnelles sont actives et conformes.'
                : `Clés non conformes pour données personnelles : ${nonCompliantKeys.join(', ')}`,
              timestamp: new Date(),
            };
          } catch (e) {
            return {
              requirementId: 'gdpr-data-encryption',
              passed: false,
              details: `Erreur lors de la vérification GDPR : ${e}`,
              timestamp: new Date(),
            };
          }
        },
      },
      {
        id: 'soc2-key-rotation',
        label: 'Rotation automatique des clés de production',
        description: 'Toutes les clés de chiffrement en production doivent être configurées pour la rotation automatique',
        regulation: 'SOC2',
        check: async () => {
          try {
            const keyMetadata = await this.keyManagementService.fetchKeyMetadata();
            const nonCompliantKeys: string[] = [];
            for (const [keyId, metadata] of Object.entries(keyMetadata)) {
              // On considère que les clés de production ont un id contenant 'prod' ou 'production'
              if (
                metadata.purpose === 'encryption' &&
                (keyId.includes('prod') || keyId.includes('production'))
              ) {
                if (!metadata.rotationPolicy || !metadata.rotationPolicy.autoRotate) {
                  nonCompliantKeys.push(`${keyId} (autoRotate: ${metadata.rotationPolicy ? metadata.rotationPolicy.autoRotate : 'absent'})`);
                }
              }
            }
            return {
              requirementId: 'soc2-key-rotation',
              passed: nonCompliantKeys.length === 0,
              details: nonCompliantKeys.length === 0
                ? 'Toutes les clés de production sont configurées pour la rotation automatique.'
                : `Clés non conformes (rotation automatique absente) : ${nonCompliantKeys.join(', ')}`,
              timestamp: new Date(),
            };
          } catch (e) {
            return {
              requirementId: 'soc2-key-rotation',
              passed: false,
              details: `Erreur lors de la vérification SOC2 : ${e}`,
              timestamp: new Date(),
            };
          }
        },
      },
      // Ajouter d'autres exigences ici
    ];
  }

  /**
   * Exécute tous les contrôles de conformité et génère un rapport
   */
  async runComplianceChecks(): Promise<ComplianceReport> {
    const results: ComplianceCheckResult[] = [];
    for (const req of this.requirements) {
      try {
        const result = await req.check();
        results.push(result);
      } catch (error) {
        results.push({
          requirementId: req.id,
          passed: false,
          details: `Erreur lors du contrôle: ${error}`,
          timestamp: new Date(),
        });
      }
    }
    const summary = {
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
      total: results.length,
    };
    this.logger.log(`Rapport de conformité généré: ${summary.passed} OK, ${summary.failed} NOK`);
    return {
      generatedAt: new Date(),
      results,
      summary,
    };
  }

  /**
   * Génère un rapport de conformité au format Markdown
   */
  async generateMarkdownReport(): Promise<string> {
    const report = await this.runComplianceChecks();
    let md = `# Rapport de conformité cryptographique\n\n`;
    md += `**Généré le :** ${report.generatedAt.toLocaleString()}\n\n`;
    md += `| Exigence | Statut | Détails |\n|---|---|---|\n`;
    for (const r of report.results) {
      md += `| ${r.requirementId} | ${r.passed ? '✅ Conforme' : '❌ Non conforme'} | ${r.details ? r.details.replace(/\n/g, ' ') : ''} |\n`;
    }
    md += `\n**Résumé :** ${report.summary.passed} conformes, ${report.summary.failed} non conformes, ${report.summary.total} contrôles au total.\n`;
    return md;
  }

  /**
   * Retourne la liste des exigences de conformité
   */
  getRequirements(): ComplianceRequirement[] {
    return this.requirements;
  }

  /**
   * Ajoute dynamiquement une exigence de conformité
   */
  addRequirement(requirement: ComplianceRequirement) {
    this.requirements.push(requirement);
  }
}
