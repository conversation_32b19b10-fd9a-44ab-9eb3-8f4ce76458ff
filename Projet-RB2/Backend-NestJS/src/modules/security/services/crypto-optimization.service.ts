import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CryptoProfilingService } from './crypto-profiling.service';
import { CryptoCacheService } from './crypto-cache.service';

/**
 * Interface pour les paramètres d'optimisation
 */
export interface OptimizationParams {
  // Paramètres généraux
  batchSize?: number;
  parallelism?: number;
  
  // Paramètres homomorphiques
  polyModulusDegree?: number;
  securityLevel?: number;
  scale?: number;
  
  // Paramètres post-quantiques
  keySize?: number;
  algorithm?: string;
  
  // Paramètres de cache
  cacheEnabled?: boolean;
  cacheTtl?: number;
  
  // Autres paramètres
  [key: string]: any;
}

/**
 * Service d'optimisation pour les opérations cryptographiques
 * Ce service optimise les paramètres des opérations cryptographiques
 * en fonction des métriques de performance.
 */
@Injectable()
export class CryptoOptimizationService {
  private readonly logger = new Logger(CryptoOptimizationService.name);
  private readonly optimizationParams: Map<string, OptimizationParams> = new Map();
  private readonly defaultParams: OptimizationParams = {
    batchSize: 100,
    parallelism: 4,
    polyModulusDegree: 8192,
    securityLevel: 128,
    scale: 1 << 30,
    keySize: 3072,
    algorithm: 'hybrid',
    cacheEnabled: true,
    cacheTtl: 60 * 60 * 1000, // 1 heure
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly profilingService: CryptoProfilingService,
    private readonly cacheService: CryptoCacheService,
  ) {
    // Initialiser les paramètres d'optimisation par défaut
    this.initializeDefaultParams();
    
    // Écouter les événements de performance
    this.listenToPerformanceEvents();
  }

  /**
   * Initialise les paramètres d'optimisation par défaut
   */
  private initializeDefaultParams(): void {
    // Paramètres généraux
    this.defaultParams.batchSize = this.configService.get<number>('CRYPTO_OPTIMIZATION_BATCH_SIZE', 100);
    this.defaultParams.parallelism = this.configService.get<number>('CRYPTO_OPTIMIZATION_PARALLELISM', 4);
    
    // Paramètres homomorphiques
    this.defaultParams.polyModulusDegree = this.configService.get<number>('HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE', 8192);
    this.defaultParams.securityLevel = this.configService.get<number>('HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL', 128);
    this.defaultParams.scale = this.configService.get<number>('HOMOMORPHIC_ENCRYPTION_SCALE', 1 << 30);
    
    // Paramètres post-quantiques
    this.defaultParams.keySize = this.configService.get<number>('QUANTUM_RESISTANT_KEY_SIZE', 3072);
    this.defaultParams.algorithm = this.configService.get<string>('QUANTUM_RESISTANT_ALGORITHM', 'hybrid');
    
    // Paramètres de cache
    this.defaultParams.cacheEnabled = this.configService.get<boolean>('CRYPTO_CACHE_ENABLED', true);
    this.defaultParams.cacheTtl = this.configService.get<number>('CRYPTO_CACHE_TTL', 60 * 60 * 1000);
    
    // Initialiser les paramètres pour chaque type d'opération
    this.optimizationParams.set('homomorphic.encrypt', {
      ...this.defaultParams,
      batchSize: 50,
    });
    
    this.optimizationParams.set('homomorphic.decrypt', {
      ...this.defaultParams,
      batchSize: 50,
    });
    
    this.optimizationParams.set('homomorphic.add', {
      ...this.defaultParams,
      batchSize: 200,
    });
    
    this.optimizationParams.set('homomorphic.multiply', {
      ...this.defaultParams,
      batchSize: 100,
    });
    
    this.optimizationParams.set('homomorphic.divide', {
      ...this.defaultParams,
      batchSize: 50,
    });
    
    this.optimizationParams.set('quantum.encrypt', {
      ...this.defaultParams,
      algorithm: 'hybrid',
    });
    
    this.optimizationParams.set('quantum.decrypt', {
      ...this.defaultParams,
      algorithm: 'hybrid',
    });
    
    this.logger.log('Default optimization parameters initialized');
  }

  /**
   * Écoute les événements de performance
   */
  private listenToPerformanceEvents(): void {
    this.eventEmitter.on('crypto.performance.threshold.exceeded', (data: any) => {
      this.logger.warn(`Performance threshold exceeded for ${data.operationType}: ${data.durationMs.toFixed(2)}ms`);
      this.optimizeParams(data.operationType);
    });
  }

  /**
   * Optimise les paramètres pour un type d'opération
   * @param operationType Type d'opération
   */
  optimizeParams(operationType: string): void {
    // Obtenir les métriques de performance
    const metrics = this.profilingService.getMetrics(operationType);
    if (metrics.length < 10) {
      this.logger.debug(`Not enough metrics to optimize ${operationType}`);
      return;
    }
    
    // Obtenir les paramètres actuels
    const currentParams = this.getParams(operationType);
    
    // Calculer les statistiques
    const stats = this.profilingService.getStatistics(operationType);
    
    // Optimiser les paramètres en fonction des statistiques
    const newParams: OptimizationParams = { ...currentParams };
    
    // Optimiser la taille des lots
    if (stats.avgDuration > 100 && currentParams.batchSize > 10) {
      newParams.batchSize = Math.max(10, Math.floor(currentParams.batchSize * 0.8));
      this.logger.log(`Reducing batch size for ${operationType} to ${newParams.batchSize}`);
    } else if (stats.avgDuration < 50 && stats.avgMemoryDelta < 10 * 1024 * 1024) {
      newParams.batchSize = Math.min(1000, Math.floor(currentParams.batchSize * 1.2));
      this.logger.log(`Increasing batch size for ${operationType} to ${newParams.batchSize}`);
    }
    
    // Optimiser le parallélisme
    if (stats.avgDuration > 200 && currentParams.parallelism < 8) {
      newParams.parallelism = Math.min(8, currentParams.parallelism + 1);
      this.logger.log(`Increasing parallelism for ${operationType} to ${newParams.parallelism}`);
    } else if (stats.avgDuration < 50 && currentParams.parallelism > 1) {
      newParams.parallelism = Math.max(1, currentParams.parallelism - 1);
      this.logger.log(`Reducing parallelism for ${operationType} to ${newParams.parallelism}`);
    }
    
    // Optimiser les paramètres homomorphiques
    if (operationType.startsWith('homomorphic.')) {
      if (stats.avgDuration > 500 && currentParams.polyModulusDegree > 4096) {
        newParams.polyModulusDegree = 4096;
        this.logger.log(`Reducing polyModulusDegree for ${operationType} to ${newParams.polyModulusDegree}`);
      } else if (stats.avgDuration < 100 && currentParams.polyModulusDegree < 16384) {
        newParams.polyModulusDegree = 16384;
        this.logger.log(`Increasing polyModulusDegree for ${operationType} to ${newParams.polyModulusDegree}`);
      }
    }
    
    // Optimiser les paramètres de cache
    if (stats.avgDuration > 100) {
      newParams.cacheEnabled = true;
      newParams.cacheTtl = Math.max(currentParams.cacheTtl, 60 * 60 * 1000); // Au moins 1 heure
      this.logger.log(`Enabling cache for ${operationType} with TTL ${newParams.cacheTtl}ms`);
    }
    
    // Mettre à jour les paramètres
    this.setParams(operationType, newParams);
    
    // Émettre un événement d'optimisation
    this.eventEmitter.emit('crypto.optimization.params.updated', {
      operationType,
      oldParams: currentParams,
      newParams,
      stats,
    });
  }

  /**
   * Retourne les paramètres d'optimisation pour un type d'opération
   * @param operationType Type d'opération
   * @returns Paramètres d'optimisation
   */
  getParams(operationType: string): OptimizationParams {
    return this.optimizationParams.get(operationType) || { ...this.defaultParams };
  }

  /**
   * Définit les paramètres d'optimisation pour un type d'opération
   * @param operationType Type d'opération
   * @param params Paramètres d'optimisation
   */
  setParams(operationType: string, params: OptimizationParams): void {
    this.optimizationParams.set(operationType, { ...params });
    this.logger.log(`Optimization parameters updated for ${operationType}`);
  }

  /**
   * Réinitialise les paramètres d'optimisation pour un type d'opération
   * @param operationType Type d'opération
   */
  resetParams(operationType: string): void {
    this.optimizationParams.set(operationType, { ...this.defaultParams });
    this.logger.log(`Optimization parameters reset for ${operationType}`);
  }

  /**
   * Retourne tous les paramètres d'optimisation
   * @returns Tous les paramètres d'optimisation
   */
  getAllParams(): Record<string, OptimizationParams> {
    const params: Record<string, OptimizationParams> = {};
    for (const [operationType, optimizationParams] of this.optimizationParams.entries()) {
      params[operationType] = { ...optimizationParams };
    }
    return params;
  }

  /**
   * Génère un rapport d'optimisation
   * @returns Rapport d'optimisation
   */
  generateReport(): Record<string, any> {
    const report: Record<string, any> = {
      timestamp: new Date(),
      defaultParams: { ...this.defaultParams },
      operationTypes: {},
    };
    
    for (const [operationType, params] of this.optimizationParams.entries()) {
      const stats = this.profilingService.getStatistics(operationType);
      report.operationTypes[operationType] = {
        params: { ...params },
        stats,
      };
    }
    
    return report;
  }

  /**
   * Optimise automatiquement tous les paramètres
   */
  autoOptimize(): void {
    this.logger.log('Auto-optimizing all parameters');
    
    for (const operationType of this.optimizationParams.keys()) {
      this.optimizeParams(operationType);
    }
    
    this.logger.log('Auto-optimization completed');
  }
}
