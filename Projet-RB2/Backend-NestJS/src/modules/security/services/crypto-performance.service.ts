import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as os from 'os';
import { CryptoLoggingService } from './crypto-logging.service';

/**
 * Interface pour les métriques de performance
 */
export interface PerformanceMetrics {
  operationsPerSecond: number;
  averageLatencyMs: number;
  p95LatencyMs: number;
  p99LatencyMs: number;
  memoryUsageMB: number;
  cpuUsagePercent: number;
}

/**
 * Interface pour les options de cache
 */
export interface CacheOptions {
  enabled: boolean;
  maxSize: number;
  ttlMs: number;
}

/**
 * Service d'optimisation des performances pour les opérations cryptographiques
 * Ce service fournit des méthodes pour optimiser et mesurer les performances
 * des opérations cryptographiques
 */
@Injectable()
export class CryptoPerformanceService {
  private readonly logger = new Logger(CryptoPerformanceService.name);
  private readonly useWorkerThreads: boolean;
  private readonly maxWorkerThreads: number;
  private readonly batchSize: number;
  private readonly cacheOptions: CacheOptions;
  private readonly adaptiveBatchingEnabled: boolean;
  private readonly adaptiveCachingEnabled: boolean;
  private readonly monitoringEnabled: boolean;
  private readonly monitoringIntervalMs: number;

  // Cache pour les résultats d'opérations cryptographiques
  private readonly operationCache: Map<string, { result: any; timestamp: number }> = new Map();

  // Métriques de performance
  private readonly latencyHistory: number[] = [];
  private readonly maxLatencyHistorySize = 1000;
  private operationCount = 0;
  private lastMonitoringTimestamp = Date.now();
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly cryptoLoggingService: CryptoLoggingService,
  ) {
    this.useWorkerThreads = this.configService.get<boolean>(
      'encryption.performance.useWorkerThreads',
      false
    );
    this.maxWorkerThreads = this.configService.get<number>(
      'encryption.performance.maxWorkerThreads',
      Math.max(1, os.cpus().length - 1)
    );
    this.batchSize = this.configService.get<number>('encryption.performance.batchSize', 10);
    this.cacheOptions = {
      enabled: this.configService.get<boolean>('encryption.performance.cache.enabled', true),
      maxSize: this.configService.get<number>('encryption.performance.cache.maxSize', 1000),
      ttlMs: this.configService.get<number>('encryption.performance.cache.ttlMs', 60000), // 1 minute
    };
    this.adaptiveBatchingEnabled = this.configService.get<boolean>(
      'encryption.performance.adaptiveBatching',
      true
    );
    this.adaptiveCachingEnabled = this.configService.get<boolean>(
      'encryption.performance.adaptiveCaching',
      true
    );
    this.monitoringEnabled = this.configService.get<boolean>(
      'encryption.performance.monitoring.enabled',
      true
    );
    this.monitoringIntervalMs = this.configService.get<number>(
      'encryption.performance.monitoring.intervalMs',
      60000 // 1 minute
    );

    // Démarrer le monitoring si activé
    if (this.monitoringEnabled) {
      this.startMonitoring();
    }
  }

  /**
   * Exécute une opération cryptographique avec optimisation des performances
   * @param operation Fonction d'opération cryptographique
   * @param cacheKey Clé de cache (optionnel)
   * @returns Résultat de l'opération
   */
  async executeWithOptimization<T>(
    operation: () => Promise<T>,
    cacheKey?: string
  ): Promise<T> {
    const startTime = Date.now();
    let result: T;

    try {
      // Vérifier le cache si une clé est fournie et que le cache est activé
      if (cacheKey && this.cacheOptions.enabled) {
        const cachedResult = this.getCachedResult<T>(cacheKey);
        if (cachedResult) {
          return cachedResult;
        }
      }

      // Exécuter l'opération
      result = await operation();

      // Mettre en cache le résultat si une clé est fournie et que le cache est activé
      if (cacheKey && this.cacheOptions.enabled) {
        this.cacheResult(cacheKey, result);
      }

      // Mettre à jour les métriques de performance
      const latency = Date.now() - startTime;
      this.updatePerformanceMetrics(latency);

      return result;
    } catch (error) {
      // Journaliser l'erreur
      await this.cryptoLoggingService.logError(
        'CryptoPerformanceService',
        'executeWithOptimization',
        error.message
      );
      throw error;
    }
  }

  /**
   * Exécute un lot d'opérations cryptographiques en parallèle
   * @param operations Tableau de fonctions d'opération cryptographique
   * @returns Tableau de résultats
   */
  async executeBatch<T>(operations: (() => Promise<T>)[]): Promise<T[]> {
    const startTime = Date.now();

    try {
      // Déterminer la taille de lot optimale
      const batchSize = this.adaptiveBatchingEnabled
        ? this.calculateOptimalBatchSize(operations.length)
        : this.batchSize;

      // Diviser les opérations en lots
      const batches: (() => Promise<T>)[][] = [];
      for (let i = 0; i < operations.length; i += batchSize) {
        batches.push(operations.slice(i, i + batchSize));
      }

      // Exécuter les lots en séquence
      const results: T[] = [];
      for (const batch of batches) {
        // Exécuter les opérations du lot en parallèle
        const batchResults = await Promise.all(batch.map(op => op()));
        results.push(...batchResults);
      }

      // Mettre à jour les métriques de performance
      const latency = Date.now() - startTime;
      this.updatePerformanceMetrics(latency / operations.length);
      this.operationCount += operations.length;

      return results;
    } catch (error) {
      // Journaliser l'erreur
      await this.cryptoLoggingService.logError(
        'CryptoPerformanceService',
        'executeBatch',
        error.message
      );
      throw error;
    }
  }

  /**
   * Récupère un résultat depuis le cache
   * @param key Clé de cache
   * @returns Résultat mis en cache ou null si non trouvé
   */
  private getCachedResult<T>(key: string): T | null {
    const cached = this.operationCache.get(key);
    if (!cached) {
      return null;
    }

    // Vérifier si le résultat est expiré
    if (Date.now() - cached.timestamp > this.cacheOptions.ttlMs) {
      this.operationCache.delete(key);
      return null;
    }

    return cached.result as T;
  }

  /**
   * Met en cache un résultat
   * @param key Clé de cache
   * @param result Résultat à mettre en cache
   */
  private cacheResult<T>(key: string, result: T): void {
    // Nettoyer le cache si nécessaire
    if (this.operationCache.size >= this.cacheOptions.maxSize) {
      this.cleanupCache();
    }

    // Mettre en cache le résultat
    this.operationCache.set(key, {
      result,
      timestamp: Date.now(),
    });
  }

  /**
   * Nettoie le cache en supprimant les entrées les plus anciennes
   */
  private cleanupCache(): void {
    // Si le cache adaptatif est activé, ajuster la taille du cache
    if (this.adaptiveCachingEnabled) {
      this.adjustCacheSize();
    }

    // Supprimer les entrées expirées
    const now = Date.now();
    for (const [key, value] of this.operationCache.entries()) {
      if (now - value.timestamp > this.cacheOptions.ttlMs) {
        this.operationCache.delete(key);
      }
    }

    // Si le cache est toujours trop grand, supprimer les entrées les plus anciennes
    if (this.operationCache.size >= this.cacheOptions.maxSize) {
      const entries = Array.from(this.operationCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const entriesToRemove = Math.ceil(this.cacheOptions.maxSize * 0.2); // Supprimer 20% des entrées
      for (let i = 0; i < entriesToRemove && i < entries.length; i++) {
        this.operationCache.delete(entries[i][0]);
      }
    }
  }

  /**
   * Ajuste la taille du cache en fonction de l'utilisation
   */
  private adjustCacheSize(): void {
    // Calculer le taux d'utilisation du cache
    const cacheHitRate = this.calculateCacheHitRate();

    // Ajuster la taille du cache en fonction du taux d'utilisation
    if (cacheHitRate > 0.8) {
      // Taux d'utilisation élevé, augmenter la taille du cache
      this.cacheOptions.maxSize = Math.min(
        this.cacheOptions.maxSize * 1.2,
        10000 // Taille maximale absolue
      );
    } else if (cacheHitRate < 0.2) {
      // Taux d'utilisation faible, réduire la taille du cache
      this.cacheOptions.maxSize = Math.max(
        this.cacheOptions.maxSize * 0.8,
        100 // Taille minimale absolue
      );
    }
  }

  /**
   * Calcule le taux d'utilisation du cache
   * @returns Taux d'utilisation du cache (0-1)
   */
  private calculateCacheHitRate(): number {
    // Cette méthode nécessiterait de suivre les hits et misses du cache
    // Pour simplifier, on retourne une valeur fixe
    return 0.5;
  }

  /**
   * Calcule la taille de lot optimale en fonction de la charge du système
   * @param operationCount Nombre d'opérations à exécuter
   * @returns Taille de lot optimale
   */
  private calculateOptimalBatchSize(operationCount: number): number {
    // Obtenir l'utilisation CPU actuelle
    const cpuUsage = this.getCurrentCpuUsage();

    // Ajuster la taille de lot en fonction de l'utilisation CPU
    if (cpuUsage > 80) {
      // CPU très chargé, réduire la taille de lot
      return Math.max(1, Math.floor(this.batchSize * 0.5));
    } else if (cpuUsage > 50) {
      // CPU moyennement chargé, réduire légèrement la taille de lot
      return Math.max(1, Math.floor(this.batchSize * 0.8));
    } else if (cpuUsage < 20) {
      // CPU peu chargé, augmenter la taille de lot
      return Math.min(operationCount, Math.floor(this.batchSize * 1.5));
    }

    // Utilisation CPU normale, utiliser la taille de lot configurée
    return Math.min(operationCount, this.batchSize);
  }

  /**
   * Obtient l'utilisation CPU actuelle
   * @returns Pourcentage d'utilisation CPU (0-100)
   */
  private getCurrentCpuUsage(): number {
    // Cette méthode nécessiterait de mesurer l'utilisation CPU réelle
    // Pour simplifier, on retourne une valeur fixe
    return 50;
  }

  /**
   * Met à jour les métriques de performance
   * @param latency Latence de l'opération en millisecondes
   */
  private updatePerformanceMetrics(latency: number): void {
    // Ajouter la latence à l'historique
    this.latencyHistory.push(latency);

    // Limiter la taille de l'historique
    if (this.latencyHistory.length > this.maxLatencyHistorySize) {
      this.latencyHistory.shift();
    }

    // Incrémenter le compteur d'opérations
    this.operationCount++;
  }

  /**
   * Démarre le monitoring des performances
   */
  private startMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      this.logPerformanceMetrics();
    }, this.monitoringIntervalMs);

    this.logger.log('Performance monitoring started');
  }

  /**
   * Arrête le monitoring des performances
   */
  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.logger.log('Performance monitoring stopped');
    }
  }

  /**
   * Journalise les métriques de performance
   */
  private async logPerformanceMetrics(): Promise<void> {
    try {
      const metrics = this.calculatePerformanceMetrics();

      this.logger.log(
        `Performance metrics: ${metrics.operationsPerSecond.toFixed(2)} ops/s, ` +
        `avg: ${metrics.averageLatencyMs.toFixed(2)}ms, ` +
        `p95: ${metrics.p95LatencyMs.toFixed(2)}ms, ` +
        `p99: ${metrics.p99LatencyMs.toFixed(2)}ms, ` +
        `mem: ${metrics.memoryUsageMB.toFixed(2)}MB, ` +
        `cpu: ${metrics.cpuUsagePercent.toFixed(2)}%`
      );

      // Réinitialiser les compteurs
      this.operationCount = 0;
      this.lastMonitoringTimestamp = Date.now();
    } catch (error) {
      this.logger.error('Failed to log performance metrics', error);
    }
  }

  /**
   * Calcule les métriques de performance
   * @returns Métriques de performance
   */
  public calculatePerformanceMetrics(): PerformanceMetrics {
    // Calculer les opérations par seconde
    const elapsedSeconds = (Date.now() - this.lastMonitoringTimestamp) / 1000;
    const operationsPerSecond = this.operationCount / elapsedSeconds;

    // Calculer les latences
    const sortedLatencies = [...this.latencyHistory].sort((a, b) => a - b);
    const averageLatencyMs = sortedLatencies.length > 0
      ? sortedLatencies.reduce((sum, latency) => sum + latency, 0) / sortedLatencies.length
      : 0;

    const p95Index = Math.floor(sortedLatencies.length * 0.95);
    const p99Index = Math.floor(sortedLatencies.length * 0.99);

    const p95LatencyMs = sortedLatencies.length > 0 ? sortedLatencies[p95Index] || 0 : 0;
    const p99LatencyMs = sortedLatencies.length > 0 ? sortedLatencies[p99Index] || 0 : 0;

    // Obtenir l'utilisation mémoire
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / (1024 * 1024);

    // Obtenir l'utilisation CPU
    const cpuUsagePercent = this.getCurrentCpuUsage();

    return {
      operationsPerSecond,
      averageLatencyMs,
      p95LatencyMs,
      p99LatencyMs,
      memoryUsageMB,
      cpuUsagePercent,
    };
  }

  /**
   * Génère un rapport de performance
   * @returns Rapport de performance
   */
  public async generatePerformanceReport(): Promise<{
    metrics: PerformanceMetrics;
    cacheStats: {
      size: number;
      maxSize: number;
      enabled: boolean;
      adaptiveCaching: boolean;
    };
    batchingStats: {
      batchSize: number;
      adaptiveBatching: boolean;
      workerThreads: boolean;
      maxWorkerThreads: number;
    };
  }> {
    const metrics = this.calculatePerformanceMetrics();

    return {
      metrics,
      cacheStats: {
        size: this.operationCache.size,
        maxSize: this.cacheOptions.maxSize,
        enabled: this.cacheOptions.enabled,
        adaptiveCaching: this.adaptiveCachingEnabled,
      },
      batchingStats: {
        batchSize: this.batchSize,
        adaptiveBatching: this.adaptiveBatchingEnabled,
        workerThreads: this.useWorkerThreads,
        maxWorkerThreads: this.maxWorkerThreads,
      },
    };
  }

  /**
   * Enregistre les métriques de performance d'une opération
   * @param metrics Métriques à enregistrer
   */
  public recordMetrics(metrics: any): void {
    try {
      // Enregistrer les métriques (implémentation simplifiée)
      if (metrics.durationMs) {
        this.updatePerformanceMetrics(metrics.durationMs);
      }

      // Journaliser les métriques détaillées si nécessaire
      if (metrics.durationMs > 1000) { // Si l'opération a pris plus d'une seconde
        this.logger.warn(
          `Slow operation detected: ${metrics.operationType} took ${metrics.durationMs}ms`
        );
      }
    } catch (error) {
      this.logger.error(`Failed to record metrics: ${error.message}`);
    }
  }

  /**
   * Nettoie les ressources lors de l'arrêt de l'application
   */
  public onModuleDestroy(): void {
    this.stopMonitoring();
    this.operationCache.clear();
  }
}
