import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SecurityEventService } from './security-event.service';
import { SecurityEventSeverity } from '../dto/create-security-event.dto';
import { SecurityNotificationService } from './security-notification.service';
import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';
import * as child_process from 'child_process';
import axios from 'axios';

const exec = util.promisify(child_process.exec);
const readFile = util.promisify(fs.readFile);
const writeFile = util.promisify(fs.writeFile);

/**
 * Interface pour les résultats d'audit de dépendances
 */
interface DependencyAuditResult {
  metadata: {
    vulnerabilities: {
      info: number;
      low: number;
      moderate: number;
      high: number;
      critical: number;
      total: number;
    };
    dependencies: {
      total: number;
    };
  };
  advisories: Record<string, {
    title: string;
    severity: string;
    vulnerable_versions: string;
    recommendation: string;
    url: string;
  }>;
}

/**
 * Service pour la surveillance des vulnérabilités dans les dépendances
 * Vérifie régulièrement les dépendances pour détecter les vulnérabilités
 */
@Injectable()
export class DependencyMonitoringService {
  private readonly logger = new Logger(DependencyMonitoringService.name);
  private readonly monitoringEnabled: boolean;
  private readonly reportPath: string;
  private readonly notifyOnSeverity: string[];
  private readonly githubToken: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly securityEventService: SecurityEventService,
    private readonly notificationService: SecurityNotificationService,
  ) {
    this.monitoringEnabled = this.configService.get<boolean>('DEPENDENCY_MONITORING_ENABLED', true);
    this.reportPath = this.configService.get<string>('DEPENDENCY_REPORT_PATH', './dependency-reports');
    this.notifyOnSeverity = this.configService.get<string>('DEPENDENCY_NOTIFY_SEVERITY', 'high,critical').split(',');
    this.githubToken = this.configService.get<string>('GITHUB_TOKEN', '');
    
    // Créer le répertoire des rapports s'il n'existe pas
    if (!fs.existsSync(this.reportPath)) {
      fs.mkdirSync(this.reportPath, { recursive: true });
    }
  }

  /**
   * Vérifier les dépendances pour les vulnérabilités
   * Exécuté tous les jours à 5h du matin
   */
  @Cron(CronExpression.EVERY_DAY_AT_5AM)
  async checkDependencies() {
    if (!this.monitoringEnabled) {
      this.logger.log('Dependency monitoring is disabled');
      return;
    }
    
    this.logger.log('Starting dependency vulnerability check');
    
    try {
      // Exécuter npm audit
      const { stdout } = await exec('npm audit --json');
      
      // Analyser les résultats
      const auditResults: DependencyAuditResult = JSON.parse(stdout);
      
      // Générer un rapport
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const reportPath = path.join(this.reportPath, `dependency-audit-${timestamp}.json`);
      
      await writeFile(reportPath, JSON.stringify(auditResults, null, 2));
      
      // Enregistrer l'événement
      await this.securityEventService.logSecurityEvent({
        eventType: 'DEPENDENCY_CHECK_COMPLETED',
        severity: SecurityEventSeverity.INFO,
        source: 'DEPENDENCY_MONITORING',
        details: {
          vulnerabilities: auditResults.metadata?.vulnerabilities,
          reportPath,
        },
      });
      
      // Vérifier s'il y a des vulnérabilités à notifier
      const criticalCount = auditResults.metadata?.vulnerabilities?.critical || 0;
      const highCount = auditResults.metadata?.vulnerabilities?.high || 0;
      
      if ((this.notifyOnSeverity.includes('critical') && criticalCount > 0) ||
          (this.notifyOnSeverity.includes('high') && highCount > 0)) {
        
        // Créer un résumé des vulnérabilités
        const vulnerabilitySummary = this.createVulnerabilitySummary(auditResults);
        
        // Enregistrer un événement d'alerte
        await this.securityEventService.logSecurityEvent({
          eventType: 'CRITICAL_DEPENDENCIES_DETECTED',
          severity: SecurityEventSeverity.HIGH,
          source: 'DEPENDENCY_MONITORING',
          details: {
            critical: criticalCount,
            high: highCount,
            summary: vulnerabilitySummary,
            reportPath,
          },
        });
        
        // Envoyer une notification
        await this.notificationService.sendSecurityNotification({
          title: `Vulnérabilités critiques détectées dans les dépendances`,
          message: `${criticalCount} vulnérabilités critiques et ${highCount} vulnérabilités élevées ont été détectées dans les dépendances.`,
          details: vulnerabilitySummary,
          severity: 'high',
          source: 'DEPENDENCY_MONITORING',
        });
        
        this.logger.warn(`Dependency check detected ${criticalCount} critical and ${highCount} high severity vulnerabilities`);
      } else {
        this.logger.log('Dependency check completed. No critical vulnerabilities detected.');
      }
      
      // Vérifier les mises à jour de sécurité disponibles
      await this.checkSecurityUpdates();
    } catch (error) {
      this.logger.error(`Error during dependency check: ${error.message}`, error.stack);
      
      // Enregistrer l'événement d'erreur
      await this.securityEventService.logSecurityEvent({
        eventType: 'DEPENDENCY_CHECK_FAILED',
        severity: SecurityEventSeverity.ERROR,
        source: 'DEPENDENCY_MONITORING',
        details: {
          error: error.message,
        },
      });
    }
  }

  /**
   * Vérifier les mises à jour de sécurité disponibles via GitHub Dependabot
   */
  private async checkSecurityUpdates() {
    if (!this.githubToken) {
      this.logger.warn('GitHub token not configured, skipping security updates check');
      return;
    }
    
    try {
      // Récupérer les alertes de sécurité via l'API GitHub
      const response = await axios.get('https://api.github.com/repos/owner/repo/dependabot/alerts', {
        headers: {
          'Authorization': `token ${this.githubToken}`,
          'Accept': 'application/vnd.github.v3+json',
        },
      });
      
      const alerts = response.data;
      
      // Filtrer les alertes ouvertes
      const openAlerts = alerts.filter(alert => alert.state === 'open');
      
      if (openAlerts.length > 0) {
        // Enregistrer un événement
        await this.securityEventService.logSecurityEvent({
          eventType: 'GITHUB_SECURITY_ALERTS_DETECTED',
          severity: SecurityEventSeverity.WARNING,
          source: 'DEPENDENCY_MONITORING',
          details: {
            alertCount: openAlerts.length,
            alerts: openAlerts.map(alert => ({
              id: alert.number,
              title: alert.security_advisory.summary,
              severity: alert.security_advisory.severity,
              package: alert.security_advisory.package.name,
              url: alert.html_url,
            })),
          },
        });
        
        this.logger.warn(`GitHub Dependabot detected ${openAlerts.length} open security alerts`);
      } else {
        this.logger.log('No open GitHub Dependabot alerts detected');
      }
    } catch (error) {
      this.logger.error(`Error checking GitHub security alerts: ${error.message}`, error.stack);
    }
  }

  /**
   * Créer un résumé des vulnérabilités
   * @param auditResults Résultats de l'audit de dépendances
   */
  private createVulnerabilitySummary(auditResults: DependencyAuditResult): string {
    let summary = `Résumé des vulnérabilités:\n\n`;
    
    // Ajouter les statistiques
    summary += `Total: ${auditResults.metadata.vulnerabilities.total} vulnérabilités\n`;
    summary += `Critique: ${auditResults.metadata.vulnerabilities.critical}\n`;
    summary += `Élevé: ${auditResults.metadata.vulnerabilities.high}\n`;
    summary += `Modéré: ${auditResults.metadata.vulnerabilities.moderate}\n`;
    summary += `Faible: ${auditResults.metadata.vulnerabilities.low}\n\n`;
    
    // Ajouter les détails des vulnérabilités critiques et élevées
    summary += `Détails des vulnérabilités critiques et élevées:\n\n`;
    
    for (const [id, advisory] of Object.entries(auditResults.advisories)) {
      if (advisory.severity === 'critical' || advisory.severity === 'high') {
        summary += `- ${advisory.title} (${advisory.severity})\n`;
        summary += `  Versions vulnérables: ${advisory.vulnerable_versions}\n`;
        summary += `  Recommandation: ${advisory.recommendation}\n`;
        summary += `  Plus d'informations: ${advisory.url}\n\n`;
      }
    }
    
    return summary;
  }
}
