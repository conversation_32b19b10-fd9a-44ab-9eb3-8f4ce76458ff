import { Injectable, Logger, Inject } from '@nestjs/common';
import { EncryptionPolicyService, EncryptionType, SensitiveDataType, SensitivityLevel } from './encryption-policy.service';
import { IEncryptionService } from '../interfaces/encryption-service.interface';
import { IHomomorphicEncryptionService } from '../interfaces/homomorphic-encryption-service.interface';
import { IQuantumResistantService } from '../interfaces/quantum-resistant-service.interface';
import { ISensitiveDataEncryptionService } from '../interfaces/sensitive-data-encryption-service.interface';

/**
 * Service de fabrique pour créer les services de chiffrement appropriés
 * Ce service crée les services de chiffrement en fonction du contexte et des politiques
 */
@Injectable()
export class EncryptionFactoryService {
  private readonly logger = new Logger(EncryptionFactoryService.name);

  constructor(
    private readonly policyService: EncryptionPolicyService,
    @Inject('IEncryptionService') private readonly standardEncryptionService: IEncryptionService,
    @Inject('IHomomorphicEncryptionService') private readonly homomorphicEncryptionService: IHomomorphicEncryptionService,
    @Inject('IQuantumResistantService') private readonly quantumResistantService: IQuantumResistantService,
    @Inject('ISensitiveDataEncryptionService') private readonly sensitiveDataEncryptionService: ISensitiveDataEncryptionService,
  ) {
    // Vérifier si les services sont disponibles
    this.logger.log('Initializing EncryptionFactoryService');
  }

  /**
   * Crée un service de chiffrement en fonction du contexte
   * @param context Contexte de chiffrement
   * @returns Service de chiffrement approprié
   */
  createEncryptionService(context: {
    dataType?: SensitiveDataType;
    sensitivityLevel?: SensitivityLevel;
    policyName?: string;
    requireHomomorphic?: boolean;
    requireQuantumResistant?: boolean;
  }): IEncryptionService {
    // Déterminer le type de chiffrement à utiliser
    let encryptionType = this.policyService.determineEncryptionType(context);

    // Surcharger le type de chiffrement si des exigences spécifiques sont fournies
    if (context.requireHomomorphic) {
      encryptionType = EncryptionType.HOMOMORPHIC;
    } else if (context.requireQuantumResistant) {
      encryptionType = EncryptionType.QUANTUM_RESISTANT;
    }

    // Créer le service approprié
    switch (encryptionType) {
      case EncryptionType.HOMOMORPHIC:
        if (!this.homomorphicEncryptionService.isEnabled()) {
          this.logger.warn('Homomorphic encryption service is not enabled, falling back to standard encryption');
          return this.standardEncryptionService;
        }
        return this.homomorphicEncryptionService;

      case EncryptionType.QUANTUM_RESISTANT:
        if (!this.quantumResistantService.isEnabled()) {
          this.logger.warn('Quantum-resistant encryption service is not enabled, falling back to standard encryption');
          return this.standardEncryptionService;
        }
        return this.quantumResistantService;

      case EncryptionType.HYBRID:
        if (this.quantumResistantService.isEnabled()) {
          return this.quantumResistantService;
        }
        this.logger.warn('Hybrid encryption requested but quantum-resistant service is not enabled, falling back to standard encryption');
        return this.standardEncryptionService;

      case EncryptionType.STANDARD:
      default:
        return this.standardEncryptionService;
    }
  }

  /**
   * Crée un service de chiffrement homomorphique si disponible
   * @returns Service de chiffrement homomorphique ou null si non disponible
   */
  createHomomorphicEncryptionService(): IHomomorphicEncryptionService | null {
    if (!this.homomorphicEncryptionService.isEnabled()) {
      this.logger.warn('Homomorphic encryption service is not enabled');
      return null;
    }
    return this.homomorphicEncryptionService;
  }

  /**
   * Crée un service de chiffrement résistant aux ordinateurs quantiques si disponible
   * @returns Service de chiffrement résistant aux ordinateurs quantiques ou null si non disponible
   */
  createQuantumResistantService(): IQuantumResistantService | null {
    if (!this.quantumResistantService.isEnabled()) {
      this.logger.warn('Quantum-resistant encryption service is not enabled');
      return null;
    }
    return this.quantumResistantService;
  }

  /**
   * Crée un service de chiffrement des données sensibles
   * @returns Service de chiffrement des données sensibles
   */
  createSensitiveDataEncryptionService(): ISensitiveDataEncryptionService {
    return this.sensitiveDataEncryptionService;
  }

  /**
   * Vérifie si un type de chiffrement est disponible
   * @param encryptionType Type de chiffrement à vérifier
   * @returns true si le type de chiffrement est disponible
   */
  isEncryptionTypeAvailable(encryptionType: EncryptionType): boolean {
    switch (encryptionType) {
      case EncryptionType.HOMOMORPHIC:
        return this.homomorphicEncryptionService.isEnabled();
      case EncryptionType.QUANTUM_RESISTANT:
      case EncryptionType.HYBRID:
        return this.quantumResistantService.isEnabled();
      case EncryptionType.STANDARD:
        return true;
      default:
        return false;
    }
  }
}
