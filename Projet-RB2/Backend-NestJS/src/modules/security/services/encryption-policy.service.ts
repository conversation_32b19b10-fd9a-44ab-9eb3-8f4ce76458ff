import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Types de données sensibles
 */
export enum SensitiveDataType {
  PERSONAL = 'personal',
  FINANCIAL = 'financial',
  HEALTH = 'health',
  AUTHENTICATION = 'authentication',
  COMMUNICATION = 'communication',
  ANALYTICS = 'analytics',
  GENERAL = 'general',
}

/**
 * Niveaux de sensibilité des données
 */
export enum SensitivityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Types de chiffrement disponibles
 */
export enum EncryptionType {
  STANDARD = 'standard',
  QUANTUM_RESISTANT = 'quantum-resistant',
  HOMOMORPHIC = 'homomorphic',
  HYBRID = 'hybrid',
}

/**
 * Interface pour les politiques de chiffrement
 */
export interface EncryptionPolicy {
  /**
   * Type de chiffrement à utiliser
   */
  encryptionType: EncryptionType;

  /**
   * Algorithme spécifique à utiliser
   */
  algorithm: string;

  /**
   * Taille de clé à utiliser
   */
  keySize: number;

  /**
   * Durée de vie des clés en jours
   */
  keyRotationDays: number;

  /**
   * Indique si le chiffrement déterministe est autorisé
   */
  allowDeterministic: boolean;

  /**
   * Indique si le chiffrement homomorphique est autorisé
   */
  allowHomomorphic: boolean;

  /**
   * Indique si le chiffrement post-quantique est requis
   */
  requireQuantumResistant: boolean;
}

/**
 * Service de gestion des politiques de chiffrement
 * Ce service centralise la configuration et les politiques de chiffrement pour tous les services
 */
@Injectable()
export class EncryptionPolicyService {
  private readonly logger = new Logger(EncryptionPolicyService.name);
  private readonly policies: Map<string, EncryptionPolicy> = new Map();
  private readonly dataTypeMapping: Map<SensitiveDataType, EncryptionPolicy> = new Map();
  private readonly sensitivityMapping: Map<SensitivityLevel, EncryptionPolicy> = new Map();

  constructor(private readonly configService: ConfigService) {
    this.initializePolicies();
  }

  /**
   * Initialise les politiques de chiffrement à partir de la configuration
   */
  private initializePolicies(): void {
    // Politique par défaut
    const defaultPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.STANDARD,
      algorithm: 'AES-256-GCM',
      keySize: 256,
      keyRotationDays: 90,
      allowDeterministic: false,
      allowHomomorphic: false,
      requireQuantumResistant: false,
    };
    this.policies.set('default', defaultPolicy);

    // Politique pour les données personnelles
    const personalPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.STANDARD,
      algorithm: 'AES-256-GCM',
      keySize: 256,
      keyRotationDays: 90,
      allowDeterministic: false,
      allowHomomorphic: false,
      requireQuantumResistant: false,
    };
    this.policies.set('personal', personalPolicy);
    this.dataTypeMapping.set(SensitiveDataType.PERSONAL, personalPolicy);

    // Politique pour les données financières
    const financialPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.HYBRID,
      algorithm: 'AES-256-GCM+RSA-4096',
      keySize: 4096,
      keyRotationDays: 60,
      allowDeterministic: false,
      allowHomomorphic: true,
      requireQuantumResistant: true,
    };
    this.policies.set('financial', financialPolicy);
    this.dataTypeMapping.set(SensitiveDataType.FINANCIAL, financialPolicy);

    // Politique pour les données de santé
    const healthPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.HYBRID,
      algorithm: 'AES-256-GCM+RSA-4096',
      keySize: 4096,
      keyRotationDays: 60,
      allowDeterministic: false,
      allowHomomorphic: false,
      requireQuantumResistant: true,
    };
    this.policies.set('health', healthPolicy);
    this.dataTypeMapping.set(SensitiveDataType.HEALTH, healthPolicy);

    // Politique pour les données d'authentification
    const authPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.QUANTUM_RESISTANT,
      algorithm: 'hybrid',
      keySize: 3072,
      keyRotationDays: 30,
      allowDeterministic: false,
      allowHomomorphic: false,
      requireQuantumResistant: true,
    };
    this.policies.set('authentication', authPolicy);
    this.dataTypeMapping.set(SensitiveDataType.AUTHENTICATION, authPolicy);

    // Politique pour les données de communication
    const communicationPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.QUANTUM_RESISTANT,
      algorithm: 'hybrid',
      keySize: 3072,
      keyRotationDays: 60,
      allowDeterministic: false,
      allowHomomorphic: false,
      requireQuantumResistant: true,
    };
    this.policies.set('communication', communicationPolicy);
    this.dataTypeMapping.set(SensitiveDataType.COMMUNICATION, communicationPolicy);

    // Politique pour les données d'analytique
    const analyticsPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.HOMOMORPHIC,
      algorithm: 'CKKS',
      keySize: 8192,
      keyRotationDays: 90,
      allowDeterministic: false,
      allowHomomorphic: true,
      requireQuantumResistant: false,
    };
    this.policies.set('analytics', analyticsPolicy);
    this.dataTypeMapping.set(SensitiveDataType.ANALYTICS, analyticsPolicy);

    // Politique pour les données générales
    const generalPolicy: EncryptionPolicy = {
      encryptionType: EncryptionType.STANDARD,
      algorithm: 'AES-256-GCM',
      keySize: 256,
      keyRotationDays: 90,
      allowDeterministic: true,
      allowHomomorphic: false,
      requireQuantumResistant: false,
    };
    this.policies.set('general', generalPolicy);
    this.dataTypeMapping.set(SensitiveDataType.GENERAL, generalPolicy);

    // Mapping par niveau de sensibilité
    this.sensitivityMapping.set(SensitivityLevel.LOW, generalPolicy);
    this.sensitivityMapping.set(SensitivityLevel.MEDIUM, personalPolicy);
    this.sensitivityMapping.set(SensitivityLevel.HIGH, financialPolicy);
    this.sensitivityMapping.set(SensitivityLevel.CRITICAL, authPolicy);

    this.logger.log('Encryption policies initialized');
  }

  /**
   * Retourne la politique de chiffrement pour un type de données sensibles
   * @param dataType Type de données sensibles
   * @returns Politique de chiffrement
   */
  getPolicyForDataType(dataType: SensitiveDataType): EncryptionPolicy {
    const policy = this.dataTypeMapping.get(dataType);
    if (!policy) {
      this.logger.warn(`No encryption policy found for data type ${dataType}, using default`);
      return this.policies.get('default');
    }
    return policy;
  }

  /**
   * Retourne la politique de chiffrement pour un niveau de sensibilité
   * @param level Niveau de sensibilité
   * @returns Politique de chiffrement
   */
  getPolicyForSensitivityLevel(level: SensitivityLevel): EncryptionPolicy {
    const policy = this.sensitivityMapping.get(level);
    if (!policy) {
      this.logger.warn(`No encryption policy found for sensitivity level ${level}, using default`);
      return this.policies.get('default');
    }
    return policy;
  }

  /**
   * Retourne la politique de chiffrement par son nom
   * @param policyName Nom de la politique
   * @returns Politique de chiffrement
   */
  getPolicy(policyName: string): EncryptionPolicy {
    const policy = this.policies.get(policyName);
    if (!policy) {
      this.logger.warn(`No encryption policy found with name ${policyName}, using default`);
      return this.policies.get('default');
    }
    return policy;
  }

  /**
   * Retourne toutes les politiques de chiffrement
   * @returns Map des politiques de chiffrement
   */
  getAllPolicies(): Map<string, EncryptionPolicy> {
    return this.policies;
  }

  /**
   * Ajoute ou met à jour une politique de chiffrement
   * @param policyName Nom de la politique
   * @param policy Politique de chiffrement
   */
  setPolicy(policyName: string, policy: EncryptionPolicy): void {
    this.policies.set(policyName, policy);
    this.logger.log(`Encryption policy ${policyName} updated`);
  }

  /**
   * Associe un type de données sensibles à une politique de chiffrement
   * @param dataType Type de données sensibles
   * @param policyName Nom de la politique
   */
  mapDataTypeToPolicy(dataType: SensitiveDataType, policyName: string): void {
    const policy = this.policies.get(policyName);
    if (!policy) {
      this.logger.error(`Cannot map data type ${dataType} to non-existent policy ${policyName}`);
      return;
    }
    this.dataTypeMapping.set(dataType, policy);
    this.logger.log(`Data type ${dataType} mapped to policy ${policyName}`);
  }

  /**
   * Associe un niveau de sensibilité à une politique de chiffrement
   * @param level Niveau de sensibilité
   * @param policyName Nom de la politique
   */
  mapSensitivityLevelToPolicy(level: SensitivityLevel, policyName: string): void {
    const policy = this.policies.get(policyName);
    if (!policy) {
      this.logger.error(`Cannot map sensitivity level ${level} to non-existent policy ${policyName}`);
      return;
    }
    this.sensitivityMapping.set(level, policy);
    this.logger.log(`Sensitivity level ${level} mapped to policy ${policyName}`);
  }

  /**
   * Détermine le type de chiffrement à utiliser en fonction du contexte
   * @param context Contexte de chiffrement (type de données, niveau de sensibilité, etc.)
   * @returns Type de chiffrement à utiliser
   */
  determineEncryptionType(context: {
    dataType?: SensitiveDataType;
    sensitivityLevel?: SensitivityLevel;
    policyName?: string;
  }): EncryptionType {
    let policy: EncryptionPolicy;

    if (context.policyName) {
      policy = this.getPolicy(context.policyName);
    } else if (context.dataType) {
      policy = this.getPolicyForDataType(context.dataType);
    } else if (context.sensitivityLevel) {
      policy = this.getPolicyForSensitivityLevel(context.sensitivityLevel);
    } else {
      policy = this.getPolicy('default');
    }

    return policy.encryptionType;
  }

  /**
   * Vérifie si le chiffrement homomorphique est autorisé pour un contexte donné
   * @param context Contexte de chiffrement
   * @returns true si le chiffrement homomorphique est autorisé
   */
  isHomomorphicAllowed(context: {
    dataType?: SensitiveDataType;
    sensitivityLevel?: SensitivityLevel;
    policyName?: string;
  }): boolean {
    let policy: EncryptionPolicy;

    if (context.policyName) {
      policy = this.getPolicy(context.policyName);
    } else if (context.dataType) {
      policy = this.getPolicyForDataType(context.dataType);
    } else if (context.sensitivityLevel) {
      policy = this.getPolicyForSensitivityLevel(context.sensitivityLevel);
    } else {
      policy = this.getPolicy('default');
    }

    return policy.allowHomomorphic;
  }

  /**
   * Vérifie si un type de chiffrement spécifique est disponible
   * @param type Type de chiffrement à vérifier
   * @returns true si le type de chiffrement est disponible
   */
  isEncryptionTypeAvailable(type: EncryptionType): boolean {
    // Vérifier si le type de chiffrement est disponible dans l'environnement actuel
    switch (type) {
      case EncryptionType.STANDARD:
        // Le chiffrement standard est toujours disponible
        return true;
      case EncryptionType.QUANTUM_RESISTANT:
        // Vérifier si les bibliothèques de chiffrement post-quantique sont disponibles
        return this.configService.get<boolean>('encryption.quantumResistant.enabled', false);
      case EncryptionType.HOMOMORPHIC:
        // Vérifier si les bibliothèques de chiffrement homomorphique sont disponibles
        return this.configService.get<boolean>('encryption.homomorphic.enabled', false);
      case EncryptionType.HYBRID:
        // Le chiffrement hybride nécessite que le chiffrement standard soit disponible
        return true;
      default:
        this.logger.warn(`Unknown encryption type: ${type}`);
        return false;
    }
  }

  /**
   * Vérifie si le chiffrement post-quantique est requis pour un contexte donné
   * @param context Contexte de chiffrement
   * @returns true si le chiffrement post-quantique est requis
   */
  isQuantumResistantRequired(context: {
    dataType?: SensitiveDataType;
    sensitivityLevel?: SensitivityLevel;
    policyName?: string;
  }): boolean {
    let policy: EncryptionPolicy;

    if (context.policyName) {
      policy = this.getPolicy(context.policyName);
    } else if (context.dataType) {
      policy = this.getPolicyForDataType(context.dataType);
    } else if (context.sensitivityLevel) {
      policy = this.getPolicyForSensitivityLevel(context.sensitivityLevel);
    } else {
      policy = this.getPolicy('default');
    }

    return policy.requireQuantumResistant;
  }
}
