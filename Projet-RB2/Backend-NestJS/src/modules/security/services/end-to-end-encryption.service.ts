import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { KeyManagementService } from './key-management.service';
import { VaultService } from './vault.service';

/**
 * Interface pour les données chiffrées de bout en bout
 */
export interface E2EEncryptedData {
  encryptedData: string;
  iv: string;
  authTag: string;
  ephemeralPublicKey: string;
}

/**
 * Interface pour une paire de clés
 */
export interface KeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * Interface pour les clés d'un utilisateur
 */
export interface UserKeys {
  identityKeyPair: KeyPair;
  signedPreKey: KeyPair;
  oneTimePreKeys: KeyPair[];
  registrationId: string;
}

/**
 * Interface pour les clés publiques d'un utilisateur
 */
export interface UserPublicKeys {
  identityKey: string;
  signedPreKey: string;
  oneTimePreKeys: string[];
  registrationId: string;
}

/**
 * Interface pour une session de chiffrement
 */
export interface E2ESession {
  sessionId: string;
  userId: string;
  recipientId: string;
  rootKey: string;
  chainKeys: {
    sending: string;
    receiving: string;
  };
  messageKeys: Map<number, string>;
  skippedMessageKeys: Map<number, string>;
  sendingRatchetKey: KeyPair;
  receivingRatchetKey: {
    publicKey: string;
  };
  sendingCounter: number;
  receivingCounter: number;
  previousCounter: number;
  maxSkip: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface pour un message chiffré avec le protocole Double Ratchet
 */
export interface E2EEncryptedMessage {
  header: {
    ratchetKey: string;
    counter: number;
    previousCounter: number;
  };
  ciphertext: string;
  iv: string;
  authTag: string;
  hmac: string;
}

/**
 * Service de chiffrement de bout en bout
 * Ce service fournit des méthodes pour le chiffrement et le déchiffrement de bout en bout
 * en utilisant un protocole inspiré de Double Ratchet (Signal Protocol)
 * pour offrir un chiffrement de bout en bout avec forward secrecy
 */
@Injectable()
export class EndToEndEncryptionService implements OnModuleInit {
  private readonly logger = new Logger(EndToEndEncryptionService.name);
  private readonly ALGORITHM = 'aes-256-gcm';
  private readonly CURVE = 'prime256v1'; // Courbe elliptique NIST P-256
  private readonly MAX_SKIP = 100; // Maximum de messages sautés autorisés
  private readonly MAX_ONE_TIME_KEYS = 20; // Nombre maximum de clés à usage unique
  private keyRotationInterval: NodeJS.Timeout | null = null;

  // Stockage des clés et des sessions
  private userKeys: Map<string, UserKeys> = new Map();
  private sessions: Map<string, E2ESession> = new Map();
  private initialized = false;

  // Options de configuration
  private readonly options: {
    enabled: boolean;
    storeKeysInVault: boolean;
    autoRotateKeys: boolean;
    keyRotationInterval: number; // en millisecondes
    sessionTimeout: number; // en millisecondes
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly keyManagementService: KeyManagementService,
    private readonly vaultService: VaultService,
  ) {
    this.options = {
      enabled: this.configService.get<boolean>('E2E_ENCRYPTION_ENABLED', true),
      storeKeysInVault: this.configService.get<boolean>('E2E_STORE_KEYS_IN_VAULT', false),
      autoRotateKeys: this.configService.get<boolean>('E2E_AUTO_ROTATE_KEYS', true),
      keyRotationInterval: this.configService.get<number>('E2E_KEY_ROTATION_INTERVAL', 7 * 24 * 60 * 60 * 1000), // 7 jours par défaut
      sessionTimeout: this.configService.get<number>('E2E_SESSION_TIMEOUT', 30 * 24 * 60 * 60 * 1000), // 30 jours par défaut
    };
  }

  /**
   * Initialise le service
   */
  async onModuleInit() {
    if (!this.options.enabled) {
      this.logger.log('End-to-end encryption is disabled');
      return;
    }

    try {
      this.logger.log('Initializing end-to-end encryption service...');

      // Charger les clés et les sessions depuis le stockage persistant
      await this.loadKeysAndSessions();

      // Planifier la rotation automatique des clés si activée
      if (this.options.autoRotateKeys) {
        this.scheduleKeyRotation();
      }

      this.initialized = true;
      this.logger.log('End-to-end encryption service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize end-to-end encryption service:', error);
      throw error;
    }
  }

  /**
   * Vérifie si le service est activé et initialisé
   */
  isEnabled(): boolean {
    return this.options.enabled && this.initialized;
  }

  /**
   * Chiffre des données avec la clé publique du destinataire
   * @param data Données à chiffrer
   * @param recipientPublicKey Clé publique du destinataire
   * @returns Données chiffrées
   */
  async encrypt(data: string, recipientPublicKey: string): Promise<E2EEncryptedData> {
    try {
      // Générer une paire de clés éphémère
      const ephemeralKeyPair = await this.generateEphemeralKeyPair();

      // Dériver un secret partagé
      const sharedSecret = await this.deriveSharedSecret(
        ephemeralKeyPair.privateKey,
        recipientPublicKey
      );

      // Chiffrer les données avec le secret partagé
      const iv = crypto.randomBytes(12);
      const cipher = crypto.createCipheriv(this.ALGORITHM, sharedSecret, iv);

      let encryptedData = cipher.update(data, 'utf8', 'base64');
      encryptedData += cipher.final('base64');

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();

      return {
        encryptedData,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        ephemeralPublicKey: ephemeralKeyPair.publicKey
      };
    } catch (error) {
      this.logger.error('Encryption failed:', error);
      throw error;
    }
  }

  /**
   * Déchiffre des données avec la clé privée du destinataire
   * @param encryptedData Données chiffrées
   * @param privateKey Clé privée du destinataire
   * @returns Données déchiffrées
   */
  async decrypt(encryptedData: E2EEncryptedData, privateKey: string): Promise<string> {
    try {
      // Dériver un secret partagé
      const sharedSecret = await this.deriveSharedSecret(
        privateKey,
        encryptedData.ephemeralPublicKey
      );

      // Déchiffrer les données
      const iv = Buffer.from(encryptedData.iv, 'base64');
      const authTag = Buffer.from(encryptedData.authTag, 'base64');

      const decipher = crypto.createDecipheriv(this.ALGORITHM, sharedSecret, iv);
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encryptedData.encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Decryption failed:', error);
      throw error;
    }
  }

  /**
   * Génère une paire de clés éphémère pour l'échange de clés
   * @returns Paire de clés éphémère
   */
  async generateEphemeralKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
    try {
      // Générer une paire de clés ECDH
      const ecdh = crypto.createECDH(this.CURVE);
      ecdh.generateKeys();

      return {
        publicKey: ecdh.getPublicKey('base64'),
        privateKey: ecdh.getPrivateKey('base64')
      };
    } catch (error) {
      this.logger.error('Failed to generate ephemeral key pair:', error);
      throw error;
    }
  }

  /**
   * Dérive un secret partagé à partir d'une clé privée et d'une clé publique
   * @param privateKey Clé privée
   * @param publicKey Clé publique
   * @returns Secret partagé
   */
  async deriveSharedSecret(privateKey: string, publicKey: string): Promise<Buffer> {
    try {
      const ecdh = crypto.createECDH(this.CURVE);
      ecdh.setPrivateKey(Buffer.from(privateKey, 'base64'));

      const publicKeyBuffer = Buffer.from(publicKey, 'base64');
      const sharedSecret = ecdh.computeSecret(publicKeyBuffer);

      // Dériver une clé de chiffrement à partir du secret partagé
      return crypto.createHash('sha256').update(sharedSecret).digest();
    } catch (error) {
      this.logger.error('Failed to derive shared secret:', error);
      throw error;
    }
  }

  /**
   * Chiffre un message simple pour un destinataire spécifique
   * Cette méthode utilise un chiffrement simple sans le protocole Double Ratchet
   * @param message Message à chiffrer
   * @param recipientPublicKey Clé publique du destinataire
   * @returns Message chiffré
   */
  async encryptSimpleMessage(message: string, recipientPublicKey: string): Promise<string> {
    const encryptedData = await this.encrypt(message, recipientPublicKey);
    return JSON.stringify(encryptedData);
  }

  /**
   * Déchiffre un message simple avec la clé privée du destinataire
   * Cette méthode utilise un déchiffrement simple sans le protocole Double Ratchet
   * @param encryptedMessage Message chiffré
   * @param privateKey Clé privée du destinataire
   * @returns Message déchiffré
   */
  async decryptSimpleMessage(encryptedMessage: string, privateKey: string): Promise<string> {
    const encryptedData = JSON.parse(encryptedMessage) as E2EEncryptedData;
    return this.decrypt(encryptedData, privateKey);
  }

  /**
   * Génère une paire de clés pour un utilisateur
   * @returns Paire de clés
   */
  async generateUserKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
    const ecdh = crypto.createECDH(this.CURVE);
    ecdh.generateKeys();

    return {
      publicKey: ecdh.getPublicKey('base64'),
      privateKey: ecdh.getPrivateKey('base64')
    };
  }

  /**
   * Charge les clés et les sessions depuis le stockage persistant
   * Cette méthode est appelée lors de l'initialisation du service
   */
  private async loadKeysAndSessions(): Promise<void> {
    try {
      this.logger.log('Loading keys and sessions from storage...');

      // Charger les clés des utilisateurs
      if (this.options.storeKeysInVault && this.vaultService.isInitialized()) {
        // Charger les clés depuis Vault si configuré
        await this.loadKeysFromVault();
      } else {
        // Sinon, charger les clés depuis le service de gestion des clés
        await this.loadKeysFromKeyManagementService();
      }

      // Charger les sessions
      await this.loadSessions();

      this.logger.log(`Loaded ${this.userKeys.size} user keys and ${this.sessions.size} sessions`);
    } catch (error) {
      this.logger.error('Failed to load keys and sessions:', error);
      throw new Error('Failed to load encryption keys and sessions');
    }
  }

  /**
   * Charge les clés des utilisateurs depuis Vault
   */
  private async loadKeysFromVault(): Promise<void> {
    try {
      // Récupérer la liste des clés depuis Vault
      const keysList = await this.vaultService.listSecrets('e2e-encryption/user-keys');

      if (keysList && keysList.length > 0) {
        // Charger chaque clé individuellement
        for (const userId of keysList) {
          const keyData = await this.vaultService.readSecret(`e2e-encryption/user-keys/${userId}`);

          if (keyData && typeof keyData === 'object') {
            const userKeysStr = keyData.data as string;
            if (userKeysStr) {
              const userKeys = JSON.parse(userKeysStr) as UserKeys;
              this.userKeys.set(userId, userKeys);
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('Failed to load keys from Vault:', error);
      throw error;
    }
  }

  /**
   * Charge les clés des utilisateurs depuis le service de gestion des clés
   */
  private async loadKeysFromKeyManagementService(): Promise<void> {
    try {
      // Essayer de récupérer les clés une par une en utilisant un préfixe connu
      // Puisque getKeyMetadata est privé, nous devons utiliser une approche différente

      // Liste des utilisateurs connus (dans un environnement réel, cela pourrait être récupéré depuis un service d'utilisateurs)
      const knownUserIds = await this.getKnownUserIds();

      for (const userId of knownUserIds) {
        const keyId = `e2e-user-${userId}`;

        try {
          const keyData = await this.keyManagementService.getKey(keyId);

          if (keyData && keyData.key) {
            // Déchiffrer et parser les données des clés
            const userKeysJson = keyData.key.toString('utf8');
            const userKeys = JSON.parse(userKeysJson) as UserKeys;
            this.userKeys.set(userId, userKeys);
          }
        } catch (error) {
          // Ignorer les erreurs pour les clés individuelles
          this.logger.debug(`No key found for user ${userId}`);
        }
      }

      this.logger.log(`Loaded keys for ${this.userKeys.size} users from key management service`);
    } catch (error) {
      this.logger.error('Failed to load keys from key management service:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des identifiants d'utilisateurs connus
   * Dans un environnement réel, cette méthode pourrait interroger un service d'utilisateurs
   */
  private async getKnownUserIds(): Promise<string[]> {
    // Implémentation simplifiée - dans un environnement réel, cela pourrait être récupéré depuis un service d'utilisateurs
    // ou depuis une base de données
    return ['admin', 'system', 'test-user-1', 'test-user-2'];
  }

  /**
   * Charge les sessions de chiffrement
   */
  private async loadSessions(): Promise<void> {
    try {
      // Essayer de récupérer les sessions une par une en utilisant un préfixe connu
      // Puisque getKeyMetadata est privé, nous devons utiliser une approche différente

      // Récupérer les sessions depuis Vault si disponible
      if (this.vaultService.isInitialized()) {
        await this.loadSessionsFromVault();
        return;
      }

      // Sinon, essayer de récupérer les sessions connues
      const knownSessionIds = await this.getKnownSessionIds();

      for (const sessionId of knownSessionIds) {
        try {
          const sessionData = await this.keyManagementService.getKey(sessionId);

          if (sessionData && sessionData.key) {
            // Déchiffrer et parser les données de session
            const sessionJson = sessionData.key.toString('utf8');
            let session = JSON.parse(sessionJson) as E2ESession;

            // Convertir les Maps (qui sont sérialisées comme des objets)
            // Convertir les clés de string à number
            const messageKeysObj = session.messageKeys || {};
            const messageKeysMap = new Map<number, string>();
            Object.entries(messageKeysObj).forEach(([key, value]) => {
              messageKeysMap.set(parseInt(key, 10), value as string);
            });
            session.messageKeys = messageKeysMap;

            const skippedKeysObj = session.skippedMessageKeys || {};
            const skippedKeysMap = new Map<number, string>();
            Object.entries(skippedKeysObj).forEach(([key, value]) => {
              skippedKeysMap.set(parseInt(key, 10), value as string);
            });
            session.skippedMessageKeys = skippedKeysMap;

            // Convertir les dates
            session.createdAt = new Date(session.createdAt);
            session.updatedAt = new Date(session.updatedAt);

            // Vérifier si la session n'est pas expirée
            if (session.updatedAt.getTime() + this.options.sessionTimeout > Date.now()) {
              this.sessions.set(sessionId, session);
            }
          }
        } catch (error) {
          // Ignorer les erreurs pour les sessions individuelles
          this.logger.debug(`No session found with ID ${sessionId}`);
        }
      }

      this.logger.log(`Loaded ${this.sessions.size} active sessions`);
    } catch (error) {
      this.logger.error('Failed to load sessions:', error);
      throw error;
    }
  }

  /**
   * Charge les sessions depuis Vault
   */
  private async loadSessionsFromVault(): Promise<void> {
    try {
      // Récupérer la liste des sessions depuis Vault
      const sessionsList = await this.vaultService.listSecrets('e2e-encryption/sessions');

      if (sessionsList && sessionsList.length > 0) {
        // Charger chaque session individuellement
        for (const sessionId of sessionsList) {
          const sessionData = await this.vaultService.readSecret(`e2e-encryption/sessions/${sessionId}`);

          if (sessionData && typeof sessionData === 'object') {
            const sessionStr = sessionData.data as string;
            if (sessionStr) {
              let session = JSON.parse(sessionStr) as E2ESession;

              // Convertir les Maps (qui sont sérialisées comme des objets)
              // Convertir les clés de string à number
              const messageKeysObj = session.messageKeys || {};
              const messageKeysMap = new Map<number, string>();
              Object.entries(messageKeysObj).forEach(([key, value]) => {
                messageKeysMap.set(parseInt(key, 10), value as string);
              });
              session.messageKeys = messageKeysMap;

              const skippedKeysObj = session.skippedMessageKeys || {};
              const skippedKeysMap = new Map<number, string>();
              Object.entries(skippedKeysObj).forEach(([key, value]) => {
                skippedKeysMap.set(parseInt(key, 10), value as string);
              });
              session.skippedMessageKeys = skippedKeysMap;

              // Convertir les dates
              session.createdAt = new Date(session.createdAt);
              session.updatedAt = new Date(session.updatedAt);

              // Vérifier si la session n'est pas expirée
              if (session.updatedAt.getTime() + this.options.sessionTimeout > Date.now()) {
                this.sessions.set(sessionId, session);
              }
            }
          }
        }
      }

      this.logger.log(`Loaded ${this.sessions.size} active sessions from Vault`);
    } catch (error) {
      this.logger.error('Failed to load sessions from Vault:', error);
      throw error;
    }
  }

  /**
   * Récupère la liste des identifiants de sessions connues
   * Dans un environnement réel, cette méthode pourrait interroger une base de données
   */
  private async getKnownSessionIds(): Promise<string[]> {
    // Implémentation simplifiée - dans un environnement réel, cela pourrait être récupéré depuis une base de données
    return ['session-admin-system', 'session-test-user-1-test-user-2'];
  }

  /**
   * Planifie la rotation automatique des clés
   * Cette méthode configure un intervalle pour la rotation périodique des clés
   */
  private scheduleKeyRotation(): void {
    // Annuler l'intervalle existant s'il y en a un
    if (this.keyRotationInterval) {
      clearInterval(this.keyRotationInterval);
    }

    // Effectuer la rotation des clés périodiquement
    this.keyRotationInterval = setInterval(async () => {
      try {
        await this.rotateKeys();
      } catch (error) {
        this.logger.error('Error during scheduled key rotation:', error);
      }
    }, this.options.keyRotationInterval);

    this.logger.log('Key rotation scheduler started');
  }

  /**
   * Effectue la rotation des clés
   * Cette méthode est appelée périodiquement pour renouveler les clés
   */
  private async rotateKeys(): Promise<void> {
    try {
      this.logger.log('Starting key rotation...');

      // Parcourir toutes les clés d'utilisateurs
      for (const [userId, userKeys] of this.userKeys.entries()) {
        // Générer une nouvelle clé pré-signée
        const newSignedPreKey = await this.generateUserKeyPair();

        // Mettre à jour les clés de l'utilisateur
        userKeys.signedPreKey = newSignedPreKey;

        // Générer de nouvelles clés à usage unique si nécessaire
        if (userKeys.oneTimePreKeys.length < this.MAX_ONE_TIME_KEYS / 2) {
          const newKeysCount = this.MAX_ONE_TIME_KEYS - userKeys.oneTimePreKeys.length;

          for (let i = 0; i < newKeysCount; i++) {
            const newOneTimeKey = await this.generateUserKeyPair();
            userKeys.oneTimePreKeys.push(newOneTimeKey);
          }
        }

        // Sauvegarder les clés mises à jour
        await this.saveUserKeys(userId, userKeys);
      }

      this.logger.log('Key rotation completed successfully');
    } catch (error) {
      this.logger.error('Failed to rotate keys:', error);
      throw error;
    }
  }

  /**
   * Sauvegarde les clés d'un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @param userKeys Clés de l'utilisateur
   */
  private async saveUserKeys(userId: string, userKeys: UserKeys): Promise<void> {
    try {
      const keyId = `e2e-user-${userId}`;

      if (this.options.storeKeysInVault && this.vaultService.isInitialized()) {
        // Sauvegarder dans Vault
        await this.vaultService.writeSecret(`e2e-encryption/user-keys/${userId}`, { data: JSON.stringify(userKeys) });
      } else {
        // Sauvegarder via le service de gestion des clés
        // Puisque createOrUpdateKey n'existe pas, utiliser createKey
        try {
          // Essayer d'abord de récupérer la clé existante pour voir si elle existe déjà
          await this.keyManagementService.getKey(keyId);

          // Si la clé existe, nous ne pouvons pas la mettre à jour directement
          // Dans un environnement réel, nous pourrions implémenter une méthode de mise à jour
          this.logger.warn(`Cannot update existing key ${keyId} directly. Consider implementing a key update mechanism.`);
        } catch (error) {
          // La clé n'existe pas, nous pouvons la créer
          await this.keyManagementService.createKey(
            userId,
            'aes-256-gcm',
            'encryption',
            {
              rotationInterval: this.options.keyRotationInterval,
              autoRotate: true
            }
          );
        }
      }
    } catch (error) {
      this.logger.error(`Failed to save keys for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Initialise les clés pour un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @returns Clés générées pour l'utilisateur
   */
  public async initializeUserKeys(userId: string): Promise<UserKeys> {
    try {
      // Vérifier si l'utilisateur a déjà des clés
      if (this.userKeys.has(userId)) {
        const existingKeys = this.userKeys.get(userId);
        if (existingKeys) {
          return existingKeys;
        }
      }

      // Générer les clés d'identité
      const identityKeyPair = await this.generateUserKeyPair();

      // Générer la clé pré-signée
      const signedPreKey = await this.generateUserKeyPair();

      // Générer les clés à usage unique
      const oneTimePreKeys: KeyPair[] = [];
      for (let i = 0; i < this.MAX_ONE_TIME_KEYS; i++) {
        oneTimePreKeys.push(await this.generateUserKeyPair());
      }

      // Générer un ID d'enregistrement unique
      const registrationId = crypto.randomBytes(4).toString('hex');

      // Créer l'objet de clés
      const userKeys: UserKeys = {
        identityKeyPair,
        signedPreKey,
        oneTimePreKeys,
        registrationId
      };

      // Stocker les clés
      this.userKeys.set(userId, userKeys);

      // Sauvegarder les clés
      await this.saveUserKeys(userId, userKeys);

      this.logger.log(`Initialized keys for user: ${userId}`);
      return userKeys;
    } catch (error) {
      this.logger.error(`Failed to initialize keys for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les clés publiques d'un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @returns Clés publiques de l'utilisateur ou null si l'utilisateur n'existe pas
   */
  public getUserPublicKeys(userId: string): UserPublicKeys | null {
    const userKeys = this.userKeys.get(userId);
    if (!userKeys) {
      return null;
    }

    return {
      identityKey: userKeys.identityKeyPair.publicKey,
      signedPreKey: userKeys.signedPreKey.publicKey,
      oneTimePreKeys: userKeys.oneTimePreKeys.map(key => key.publicKey),
      registrationId: userKeys.registrationId
    };
  }

  /**
   * Crée une session de chiffrement entre deux utilisateurs
   * @param userId Identifiant de l'utilisateur local
   * @param recipientId Identifiant du destinataire
   * @param recipientPublicKeys Clés publiques du destinataire
   * @returns Identifiant de la session créée
   */
  public async createSession(
    userId: string,
    recipientId: string,
    recipientPublicKeys: UserPublicKeys
  ): Promise<string> {
    try {
      // Vérifier si l'utilisateur local a des clés
      const userKeys = this.userKeys.get(userId);
      if (!userKeys) {
        throw new Error(`User ${userId} has no keys initialized`);
      }

      // Générer un identifiant de session unique
      const sessionId = `session-${userId}-${recipientId}-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

      // Dériver une clé racine à partir des clés d'identité
      const sharedSecret = await this.deriveSharedSecret(
        userKeys.identityKeyPair.privateKey,
        recipientPublicKeys.identityKey
      );
      const rootKey = crypto.createHash('sha256').update(sharedSecret).digest('base64');

      // Générer une paire de clés de ratchet initiale
      const sendingRatchetKey = await this.generateUserKeyPair();

      // Dériver les clés de chaîne initiales
      const sendingChainKey = crypto.createHmac('sha256', rootKey)
        .update('sending_chain')
        .digest('base64');

      const receivingChainKey = crypto.createHmac('sha256', rootKey)
        .update('receiving_chain')
        .digest('base64');

      // Créer la session
      const session: E2ESession = {
        sessionId,
        userId,
        recipientId,
        rootKey,
        chainKeys: {
          sending: sendingChainKey,
          receiving: receivingChainKey
        },
        messageKeys: new Map<number, string>(),
        skippedMessageKeys: new Map<number, string>(),
        sendingRatchetKey,
        receivingRatchetKey: {
          publicKey: recipientPublicKeys.signedPreKey
        },
        sendingCounter: 0,
        receivingCounter: 0,
        previousCounter: 0,
        maxSkip: this.MAX_SKIP,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Stocker la session
      this.sessions.set(sessionId, session);

      // Sauvegarder la session
      await this.saveSession(session);

      this.logger.log(`Created session between ${userId} and ${recipientId}: ${sessionId}`);
      return sessionId;
    } catch (error) {
      this.logger.error('Failed to create session:', error);
      throw error;
    }
  }

  /**
   * Récupère une session par son identifiant
   * @param sessionId Identifiant de la session
   * @returns Session ou null si elle n'existe pas
   */
  public getSession(sessionId: string): E2ESession | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Met à jour une session existante
   * @param session Session à mettre à jour
   */
  public async updateSession(session: E2ESession): Promise<void> {
    try {
      // Mettre à jour la date de modification
      session.updatedAt = new Date();

      // Mettre à jour la session dans le cache
      this.sessions.set(session.sessionId, session);

      // Sauvegarder la session
      await this.saveSession(session);
    } catch (error) {
      this.logger.error(`Failed to update session ${session.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Supprime une session
   * @param sessionId Identifiant de la session à supprimer
   */
  public async deleteSession(sessionId: string): Promise<void> {
    try {
      // Supprimer la session du cache
      this.sessions.delete(sessionId);

      // Supprimer la session du stockage persistant
      if (this.options.storeKeysInVault && this.vaultService.isInitialized()) {
        await this.vaultService.deleteSecret(`e2e-encryption/sessions/${sessionId}`);
      } else {
        // Dans un environnement réel, nous aurions besoin d'implémenter une méthode de suppression
        this.logger.warn(`Cannot delete session ${sessionId} from key management service. Consider implementing a deletion mechanism.`);
      }

      this.logger.log(`Deleted session: ${sessionId}`);
    } catch (error) {
      this.logger.error(`Failed to delete session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Sauvegarde une session dans le stockage persistant
   * @param session Session à sauvegarder
   */
  private async saveSession(session: E2ESession): Promise<void> {
    try {
      // Convertir les Maps en objets pour la sérialisation
      const sessionToSave = { ...session };
      const messageKeysObj: Record<string, string> = {};
      const skippedMessageKeysObj: Record<string, string> = {};

      session.messageKeys.forEach((value, key) => {
        messageKeysObj[key.toString()] = value;
      });

      session.skippedMessageKeys.forEach((value, key) => {
        skippedMessageKeysObj[key.toString()] = value;
      });

      sessionToSave.messageKeys = messageKeysObj as any;
      sessionToSave.skippedMessageKeys = skippedMessageKeysObj as any;

      if (this.options.storeKeysInVault && this.vaultService.isInitialized()) {
        // Sauvegarder dans Vault
        await this.vaultService.writeSecret(
          `e2e-encryption/sessions/${session.sessionId}`,
          { data: JSON.stringify(sessionToSave) }
        );
      } else {
        // Dans un environnement réel, nous aurions besoin d'implémenter une méthode de sauvegarde
        this.logger.warn(`Cannot save session ${session.sessionId} to key management service. Consider implementing a session storage mechanism.`);
      }
    } catch (error) {
      this.logger.error(`Failed to save session ${session.sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Chiffre un message avec le protocole Double Ratchet
   * @param sessionId Identifiant de la session
   * @param message Message à chiffrer
   * @returns Message chiffré
   */
  public async encryptMessage(sessionId: string, message: string): Promise<string> {
    try {
      // Récupérer la session
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Dériver la clé de message
      const messageKey = await this.deriveMessageKey(session.chainKeys.sending, session.sendingCounter);

      // Mettre à jour la clé de chaîne d'envoi (ratchet symétrique)
      session.chainKeys.sending = await this.ratchetChainKey(session.chainKeys.sending);

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Chiffrer le message
      const cipher = crypto.createCipheriv(this.ALGORITHM, Buffer.from(messageKey, 'base64'), iv);
      let ciphertext = cipher.update(message, 'utf8', 'base64');
      ciphertext += cipher.final('base64');

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag().toString('base64');

      // Créer l'en-tête du message
      const header = {
        ratchetKey: session.sendingRatchetKey.publicKey,
        counter: session.sendingCounter,
        previousCounter: session.previousCounter
      };

      // Incrémenter le compteur d'envoi
      session.sendingCounter++;

      // Mettre à jour la session
      session.updatedAt = new Date();
      await this.saveSession(session);

      // Créer le message chiffré
      const encryptedMessage: E2EEncryptedMessage = {
        header,
        ciphertext,
        iv: iv.toString('base64'),
        authTag,
        hmac: this.calculateHMAC(header, session.rootKey)
      };

      return JSON.stringify(encryptedMessage);
    } catch (error) {
      this.logger.error(`Failed to encrypt message for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Déchiffre un message avec le protocole Double Ratchet
   * @param sessionId Identifiant de la session
   * @param encryptedMessageStr Message chiffré sérialisé
   * @returns Message déchiffré
   */
  public async decryptMessage(sessionId: string, encryptedMessageStr: string): Promise<string> {
    try {
      // Récupérer la session
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Parser le message chiffré
      const encryptedMessage = JSON.parse(encryptedMessageStr) as E2EEncryptedMessage;

      // Vérifier l'authenticité du message
      const calculatedHMAC = this.calculateHMAC(encryptedMessage.header, session.rootKey);
      if (calculatedHMAC !== encryptedMessage.hmac) {
        throw new Error('Message authentication failed: HMAC mismatch');
      }

      // Vérifier si la clé de ratchet a changé (ratchet DH)
      if (encryptedMessage.header.ratchetKey !== session.receivingRatchetKey.publicKey) {
        // Sauvegarder les clés de message sautées
        await this.skipMessageKeys(session, encryptedMessage.header.previousCounter);

        // Mettre à jour la clé de ratchet de réception
        session.receivingRatchetKey.publicKey = encryptedMessage.header.ratchetKey;
        session.previousCounter = session.receivingCounter;
        session.receivingCounter = 0;

        // Dériver une nouvelle clé de chaîne de réception
        const sharedSecret = await this.deriveSharedSecret(
          session.sendingRatchetKey.privateKey,
          session.receivingRatchetKey.publicKey
        );

        const derivedKey = crypto.createHash('sha256').update(sharedSecret).digest('base64');
        session.chainKeys.receiving = crypto.createHmac('sha256', derivedKey)
          .update('receiving')
          .digest('base64');
      }

      // Récupérer ou dériver la clé de message
      let messageKey: string;

      // Vérifier si la clé de message est dans les clés sautées
      if (session.skippedMessageKeys.has(encryptedMessage.header.counter)) {
        const skippedKey = session.skippedMessageKeys.get(encryptedMessage.header.counter);
        if (!skippedKey) {
          throw new Error(`Skipped message key not found for counter ${encryptedMessage.header.counter}`);
        }
        messageKey = skippedKey;
        session.skippedMessageKeys.delete(encryptedMessage.header.counter);
      } else if (encryptedMessage.header.counter < session.receivingCounter) {
        // Message déjà reçu ou trop ancien
        throw new Error('Message replay detected or too old');
      } else {
        // Dériver les clés de message pour les messages manquants
        for (let i = session.receivingCounter; i < encryptedMessage.header.counter; i++) {
          const skippedKey = await this.deriveMessageKey(session.chainKeys.receiving, i);
          session.skippedMessageKeys.set(i, skippedKey);
          session.chainKeys.receiving = await this.ratchetChainKey(session.chainKeys.receiving);
        }

        // Dériver la clé de message pour ce message
        messageKey = await this.deriveMessageKey(session.chainKeys.receiving, encryptedMessage.header.counter);

        // Mettre à jour la clé de chaîne de réception
        session.chainKeys.receiving = await this.ratchetChainKey(session.chainKeys.receiving);

        // Mettre à jour le compteur de réception
        session.receivingCounter = encryptedMessage.header.counter + 1;
      }

      // Déchiffrer le message
      const iv = Buffer.from(encryptedMessage.iv, 'base64');
      const authTag = Buffer.from(encryptedMessage.authTag, 'base64');
      const decipher = crypto.createDecipheriv(
        this.ALGORITHM,
        Buffer.from(messageKey, 'base64'),
        iv
      );

      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encryptedMessage.ciphertext, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      // Mettre à jour la session
      session.updatedAt = new Date();
      await this.saveSession(session);

      return decrypted;
    } catch (error) {
      this.logger.error(`Failed to decrypt message for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Dérive une clé de message à partir d'une clé de chaîne et d'un compteur
   * @param chainKey Clé de chaîne
   * @param counter Compteur de message
   * @returns Clé de message dérivée
   */
  private async deriveMessageKey(chainKey: string, counter: number): Promise<string> {
    // Dériver une clé de message à partir de la clé de chaîne et du compteur
    const hmac = crypto.createHmac('sha256', chainKey);
    hmac.update(`message-key-${counter}`);
    return hmac.digest('base64');
  }

  /**
   * Fait avancer la clé de chaîne d'un cran (ratchet symétrique)
   * @param chainKey Clé de chaîne actuelle
   * @returns Nouvelle clé de chaîne
   */
  private async ratchetChainKey(chainKey: string): Promise<string> {
    // Faire avancer la clé de chaîne d'un cran
    const hmac = crypto.createHmac('sha256', chainKey);
    hmac.update('next-chain-key');
    return hmac.digest('base64');
  }

  /**
   * Sauvegarde les clés de message sautées lors d'un changement de clé de ratchet
   * @param session Session de chiffrement
   * @param untilCounter Compteur jusqu'auquel sauvegarder les clés
   */
  private async skipMessageKeys(session: E2ESession, untilCounter: number): Promise<void> {
    if (session.receivingCounter + session.maxSkip < untilCounter) {
      throw new Error(`Too many skipped messages: ${untilCounter - session.receivingCounter}`);
    }

    // Sauvegarder les clés de message sautées
    for (let i = session.receivingCounter; i < untilCounter; i++) {
      const messageKey = await this.deriveMessageKey(session.chainKeys.receiving, i);
      session.skippedMessageKeys.set(i, messageKey);
      session.chainKeys.receiving = await this.ratchetChainKey(session.chainKeys.receiving);
    }

    // Mettre à jour le compteur de réception
    session.receivingCounter = untilCounter;
  }

  /**
   * Calcule un HMAC pour l'authentification des messages
   * @param header En-tête du message
   * @param key Clé pour le HMAC
   * @returns HMAC calculé
   */
  private calculateHMAC(header: any, key: string): string {
    const hmac = crypto.createHmac('sha256', key);
    hmac.update(JSON.stringify(header));
    return hmac.digest('base64');
  }
}
