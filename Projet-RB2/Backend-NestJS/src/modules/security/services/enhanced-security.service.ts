import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { VaultService } from './vault.service';
import { KeyRotationService } from './key-rotation.service';
import { RateLimiterService } from './rate-limiter.service';
import * as crypto from 'crypto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EncryptionMetrics } from '../monitoring/encryption-metrics';

/**
 * Service de sécurité amélioré
 * Fournit des fonctionnalités avancées de sécurité pour l'application
 */
@Injectable()
export class EnhancedSecurityService implements OnModuleInit {
  private readonly logger = new Logger(EnhancedSecurityService.name);
  private readonly metrics: EncryptionMetrics;
  private readonly secretCache: Map<string, { data: any; expiry: number }> = new Map();
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private localEncryptionKey: Buffer;

  constructor(
    private readonly configService: ConfigService,
    private readonly vaultService: VaultService,
    private readonly keyRotationService: KeyRotationService,
    private readonly rateLimiterService: RateLimiterService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.metrics = EncryptionMetrics.getInstance();
    
    // Initialiser la clé de chiffrement locale pour le fallback
    const localKey = this.configService.get<string>('LOCAL_ENCRYPTION_KEY');
    if (localKey) {
      this.localEncryptionKey = Buffer.from(localKey, 'hex');
    } else {
      // Générer une clé aléatoire si aucune n'est configurée
      this.localEncryptionKey = crypto.randomBytes(32);
      this.logger.warn('No LOCAL_ENCRYPTION_KEY found, generated a random one for this session');
    }
  }

  async onModuleInit() {
    // Configurer les écouteurs d'événements
    this.setupEventListeners();
    
    // Planifier la rotation des clés
    await this.scheduleKeyRotation();
    
    this.logger.log('Enhanced security service initialized');
  }

  /**
   * Configurer les écouteurs d'événements pour la sécurité
   */
  private setupEventListeners() {
    // Écouter les événements de rotation des clés
    this.eventEmitter.on('security.key.rotated', (data) => {
      this.logger.log(`Key rotated: ${data.keyId}`);
      this.invalidateCache(`secret/keys/${data.keyId}`);
    });
    
    // Écouter les événements de tentative d'authentification échouée
    this.eventEmitter.on('security.auth.failed', async (data) => {
      this.logger.warn(`Failed authentication attempt for user: ${data.username}, IP: ${data.ip}`);
      
      // Incrémenter le compteur de tentatives échouées
      const key = `auth:failed:${data.ip}`;
      await this.rateLimiterService.increment(key, 1, 24 * 60 * 60 * 1000); // 24 heures
      
      // Vérifier si l'IP doit être bloquée
      const attempts = await this.rateLimiterService.get(key);
      if (attempts > 5) {
        this.logger.warn(`Blocking IP ${data.ip} due to too many failed authentication attempts`);
        await this.blockIP(data.ip);
      }
    });
  }

  /**
   * Planifier la rotation automatique des clés
   */
  private async scheduleKeyRotation() {
    try {
      // Planifier la rotation des clés JWT
      const jwtRotationInterval = this.configService.get<number>('JWT_ROTATION_INTERVAL', 30) * 24 * 60 * 60 * 1000; // 30 jours par défaut
      
      setInterval(async () => {
        try {
          this.logger.log('Rotating JWT keys');
          await this.rotateJWTKeys();
        } catch (error) {
          this.logger.error(`Failed to rotate JWT keys: ${error.message}`);
        }
      }, jwtRotationInterval);
      
      // Planifier la rotation des clés de chiffrement
      await this.keyRotationService.scheduleRotation();
      
      this.logger.log('Key rotation scheduled successfully');
    } catch (error) {
      this.logger.error(`Failed to schedule key rotation: ${error.message}`);
    }
  }

  /**
   * Effectuer la rotation des clés JWT
   */
  private async rotateJWTKeys() {
    try {
      // Générer une nouvelle paire de clés RSA
      const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
        modulusLength: 2048,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });
      
      // Stocker les nouvelles clés dans Vault
      const timestamp = Date.now();
      const keyId = `jwt-${timestamp}`;
      
      await this.vaultService.writeSecret(`secret/data/jwt-keys/${keyId}`, {
        publicKey,
        privateKey,
        createdAt: timestamp,
        expiresAt: timestamp + (this.configService.get<number>('JWT_KEY_EXPIRY', 60) * 24 * 60 * 60 * 1000) // 60 jours par défaut
      });
      
      // Mettre à jour la clé active
      await this.vaultService.writeSecret('secret/data/jwt-keys/active', { keyId });
      
      // Émettre un événement pour la rotation des clés
      this.eventEmitter.emit('security.key.rotated', { keyId, type: 'jwt' });
      
      this.logger.log(`JWT keys rotated successfully, new key ID: ${keyId}`);
      return keyId;
    } catch (error) {
      this.logger.error(`Failed to rotate JWT keys: ${error.message}`);
      throw error;
    }
  }

  /**
   * Obtenir la clé JWT active
   */
  async getActiveJWTKey(): Promise<{ keyId: string; publicKey: string; privateKey: string }> {
    try {
      // Vérifier le cache
      const cachedKey = this.getCachedSecret('jwt-keys-active');
      if (cachedKey) {
        return cachedKey;
      }
      
      // Récupérer l'ID de la clé active
      const activeKeyData = await this.vaultService.getSecret('secret/data/jwt-keys/active');
      if (!activeKeyData || !activeKeyData.data || !activeKeyData.data.keyId) {
        this.logger.warn('No active JWT key found, generating a new one');
        const keyId = await this.rotateJWTKeys();
        const keyData = await this.vaultService.getSecret(`secret/data/jwt-keys/${keyId}`);
        return {
          keyId,
          ...keyData.data
        };
      }
      
      // Récupérer la clé active
      const keyId = activeKeyData.data.keyId;
      const keyData = await this.vaultService.getSecret(`secret/data/jwt-keys/${keyId}`);
      
      if (!keyData || !keyData.data) {
        this.logger.warn(`Active JWT key ${keyId} not found, generating a new one`);
        const newKeyId = await this.rotateJWTKeys();
        const newKeyData = await this.vaultService.getSecret(`secret/data/jwt-keys/${newKeyId}`);
        return {
          keyId: newKeyId,
          ...newKeyData.data
        };
      }
      
      const result = {
        keyId,
        ...keyData.data
      };
      
      // Mettre en cache
      this.cacheSecret('jwt-keys-active', result, this.DEFAULT_CACHE_TTL);
      
      return result;
    } catch (error) {
      this.logger.error(`Failed to get active JWT key: ${error.message}`);
      
      // Fallback: générer une paire de clés temporaire
      const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
        modulusLength: 2048,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });
      
      return {
        keyId: 'fallback-key',
        publicKey,
        privateKey
      };
    }
  }

  /**
   * Bloquer une adresse IP
   * @param ip Adresse IP à bloquer
   */
  private async blockIP(ip: string): Promise<void> {
    try {
      // Ajouter l'IP à la liste des IPs bloquées
      const key = `security:blocked:ip:${ip}`;
      await this.rateLimiterService.set(key, 1, 24 * 60 * 60 * 1000); // Bloquer pendant 24 heures
      
      // Émettre un événement pour le blocage d'IP
      this.eventEmitter.emit('security.ip.blocked', { ip });
    } catch (error) {
      this.logger.error(`Failed to block IP ${ip}: ${error.message}`);
    }
  }

  /**
   * Vérifier si une adresse IP est bloquée
   * @param ip Adresse IP à vérifier
   */
  async isIPBlocked(ip: string): Promise<boolean> {
    try {
      const key = `security:blocked:ip:${ip}`;
      const value = await this.rateLimiterService.get(key);
      return value > 0;
    } catch (error) {
      this.logger.error(`Failed to check if IP ${ip} is blocked: ${error.message}`);
      return false;
    }
  }

  /**
   * Récupérer un secret depuis le cache
   * @param key Clé du secret
   */
  private getCachedSecret(key: string): any {
    const cached = this.secretCache.get(key);
    
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }
    
    // Supprimer l'entrée expirée du cache
    if (cached) {
      this.secretCache.delete(key);
    }
    
    return null;
  }

  /**
   * Mettre en cache un secret
   * @param key Clé du secret
   * @param data Données du secret
   * @param ttl Durée de vie du cache en millisecondes
   */
  private cacheSecret(key: string, data: any, ttl: number): void {
    this.secretCache.set(key, {
      data,
      expiry: Date.now() + ttl,
    });
  }

  /**
   * Invalider le cache pour une clé donnée
   * @param key Clé du secret
   */
  private invalidateCache(key: string): void {
    this.secretCache.delete(key);
  }
}
