import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HomomorphicEncryptionService } from './homomorphic-encryption.service';

describe('HomomorphicEncryptionService', () => {
  let service: HomomorphicEncryptionService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HomomorphicEncryptionService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key, defaultValue) => {
              if (key === 'HOMOMORPHIC_ENCRYPTION_ENABLED') return true;
              if (key === 'HOMOMORPHIC_ENCRYPTION_SCHEME') return 'BFV';
              if (key === 'HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL') return 128;
              if (key === 'HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE') return 4096;
              if (key === 'HOMOMORPHIC_ENCRYPTION_PLAINTEXT_MODULUS') return 1024;
              if (key === 'HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS') return [60, 40, 40, 60];
              return defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<HomomorphicEncryptionService>(HomomorphicEncryptionService);
    configService = module.get<ConfigService>(ConfigService);
    
    // Initialize the service manually since we're not using the real module lifecycle
    await service.onModuleInit();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Basic operations', () => {
    it('should encrypt and decrypt a value correctly', async () => {
      // Skip if homomorphic encryption is not enabled
      if (!service.isEnabled()) {
        console.log('Homomorphic encryption is not enabled, skipping test');
        return;
      }

      const originalValue = 42;
      const encrypted = await service.encrypt(originalValue);
      expect(encrypted).toBeDefined();
      expect(Buffer.isBuffer(encrypted)).toBe(true);

      const decrypted = await service.decrypt(encrypted);
      expect(decrypted).toBeCloseTo(originalValue, 0);
    });

    it('should perform homomorphic addition', async () => {
      // Skip if homomorphic encryption is not enabled
      if (!service.isEnabled()) {
        console.log('Homomorphic encryption is not enabled, skipping test');
        return;
      }

      const value1 = 10;
      const value2 = 20;
      const encrypted1 = await service.encrypt(value1);
      const encrypted2 = await service.encrypt(value2);

      const encryptedSum = await service.add(encrypted1, encrypted2);
      expect(encryptedSum).toBeDefined();
      expect(Buffer.isBuffer(encryptedSum)).toBe(true);

      const decryptedSum = await service.decrypt(encryptedSum);
      expect(decryptedSum).toBeCloseTo(value1 + value2, 0);
    });

    it('should perform homomorphic multiplication', async () => {
      // Skip if homomorphic encryption is not enabled
      if (!service.isEnabled()) {
        console.log('Homomorphic encryption is not enabled, skipping test');
        return;
      }

      const value1 = 5;
      const value2 = 7;
      const encrypted1 = await service.encrypt(value1);
      const encrypted2 = await service.encrypt(value2);

      const encryptedProduct = await service.multiply(encrypted1, encrypted2);
      expect(encryptedProduct).toBeDefined();
      expect(Buffer.isBuffer(encryptedProduct)).toBe(true);

      const decryptedProduct = await service.decrypt(encryptedProduct);
      expect(decryptedProduct).toBeCloseTo(value1 * value2, 0);
    });

    it('should calculate average homomorphically', async () => {
      // Skip if homomorphic encryption is not enabled
      if (!service.isEnabled()) {
        console.log('Homomorphic encryption is not enabled, skipping test');
        return;
      }

      const values = [10, 20, 30, 40, 50];
      const encryptedValues = await Promise.all(values.map(v => service.encrypt(v)));

      const encryptedAverage = await service.average(encryptedValues);
      expect(encryptedAverage).toBeDefined();
      expect(Buffer.isBuffer(encryptedAverage)).toBe(true);

      const decryptedAverage = await service.decrypt(encryptedAverage);
      const expectedAverage = values.reduce((sum, val) => sum + val, 0) / values.length;
      expect(decryptedAverage).toBeCloseTo(expectedAverage, 0);
    });
  });

  describe('Key generation', () => {
    it('should generate key pairs', async () => {
      // Skip if homomorphic encryption is not enabled
      if (!service.isEnabled()) {
        console.log('Homomorphic encryption is not enabled, skipping test');
        return;
      }

      const keyPair = await service.generateKeyPair();
      expect(keyPair).toBeDefined();
      expect(keyPair.publicKey).toBeDefined();
      expect(keyPair.privateKey).toBeDefined();
      expect(typeof keyPair.publicKey).toBe('string');
      expect(typeof keyPair.privateKey).toBe('string');
    });
  });
});
