import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from '../../../prisma/prisma.service';
import { SecurityEventService } from './security-event.service';
import { SecurityNotificationService } from './security-notification.service';
import { SecurityMonitoringService } from './security-monitoring.service';

/**
 * Interface pour les incidents de sécurité
 */
export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  status: 'OPEN' | 'INVESTIGATING' | 'CONTAINED' | 'REMEDIATED' | 'RESOLVED' | 'CLOSED';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: string;
  source: string;
  detectedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  assignedToId?: string;
  reportedById?: string;
  affectedAssets: string[];
  tags: string[];
  metadata?: Record<string, any>;
}

/**
 * Interface pour les activités d'incident
 */
export interface IncidentActivity {
  id: string;
  incidentId: string;
  userId?: string;
  action: string;
  details?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Interface pour la création d'un incident
 */
export interface CreateIncidentDto {
  title: string;
  description: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: string;
  source: string;
  affectedAssets?: string[];
  tags?: string[];
  assignedToId?: string;
  reportedById?: string;
  relatedAlertIds?: string[];
  relatedEventIds?: string[];
  metadata?: Record<string, any>;
}

/**
 * Interface pour la mise à jour d'un incident
 */
export interface UpdateIncidentDto {
  title?: string;
  description?: string;
  status?: 'OPEN' | 'INVESTIGATING' | 'CONTAINED' | 'REMEDIATED' | 'RESOLVED' | 'CLOSED';
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  assignedToId?: string;
  affectedAssets?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * Service de gestion des incidents de sécurité
 */
@Injectable()
export class IncidentManagementService {
  private readonly logger = new Logger(IncidentManagementService.name);
  private readonly incidentTypes = [
    'UNAUTHORIZED_ACCESS',
    'DATA_BREACH',
    'MALWARE',
    'PHISHING',
    'DENIAL_OF_SERVICE',
    'INSIDER_THREAT',
    'SUSPICIOUS_ACTIVITY',
    'POLICY_VIOLATION',
    'SYSTEM_COMPROMISE',
    'NETWORK_INTRUSION',
    'ACCOUNT_COMPROMISE',
    'PHYSICAL_SECURITY',
    'SOCIAL_ENGINEERING',
    'RANSOMWARE',
    'API_ABUSE',
    'CONFIGURATION_ERROR',
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly securityEventService: SecurityEventService,
    private readonly securityNotificationService: SecurityNotificationService,
    private readonly securityMonitoringService: SecurityMonitoringService,
  ) {
    this.subscribeToSecurityEvents();
  }

  /**
   * S'abonne aux événements de sécurité pour détecter les incidents potentiels
   */
  private subscribeToSecurityEvents(): void {
    // S'abonner aux alertes de sécurité
    this.eventEmitter.on('security.alert.created', async (alert: any) => {
      try {
        // Vérifier si l'alerte doit générer un incident
        if (this.shouldCreateIncidentFromAlert(alert)) {
          await this.createIncidentFromAlert(alert);
        }
      } catch (error) {
        this.logger.error(`Failed to process security alert: ${error.message}`, error.stack);
      }
    });

    // S'abonner aux événements de sécurité critiques
    this.eventEmitter.on('security.event.logged', async (event: any) => {
      try {
        // Vérifier si l'événement doit générer un incident
        if (this.shouldCreateIncidentFromEvent(event)) {
          await this.createIncidentFromEvent(event);
        }
      } catch (error) {
        this.logger.error(`Failed to process security event: ${error.message}`, error.stack);
      }
    });
  }

  /**
   * Détermine si une alerte doit générer un incident
   * @param alert Alerte de sécurité
   * @returns true si l'alerte doit générer un incident
   */
  private shouldCreateIncidentFromAlert(alert: any): boolean {
    // Les alertes de priorité HIGH ou CRITICAL génèrent automatiquement un incident
    if (alert.priority === 'HIGH' || alert.priority === 'CRITICAL') {
      return true;
    }

    // Certains types d'alertes génèrent toujours un incident
    const criticalAlertTypes = [
      'DATA_BREACH',
      'RANSOMWARE_DETECTED',
      'SYSTEM_COMPROMISE',
      'UNAUTHORIZED_ACCESS',
      'MALWARE_DETECTED',
      'EXCESSIVE_FAILED_LOGINS',
    ];

    return criticalAlertTypes.includes(alert.type);
  }

  /**
   * Détermine si un événement doit générer un incident
   * @param event Événement de sécurité
   * @returns true si l'événement doit générer un incident
   */
  private shouldCreateIncidentFromEvent(event: any): boolean {
    // Les événements CRITICAL génèrent automatiquement un incident
    if (event.severity === 'CRITICAL') {
      return true;
    }

    // Certains types d'événements génèrent toujours un incident
    const criticalEventTypes = [
      'UNAUTHORIZED_ACCESS',
      'DATA_BREACH',
      'MALWARE_DETECTED',
      'RANSOMWARE_DETECTED',
      'SYSTEM_COMPROMISE',
      'NETWORK_INTRUSION',
      'ACCOUNT_COMPROMISE',
    ];

    return criticalEventTypes.includes(event.type);
  }

  /**
   * Crée un incident à partir d'une alerte
   * @param alert Alerte de sécurité
   * @returns Incident créé
   */
  private async createIncidentFromAlert(alert: any): Promise<SecurityIncident> {
    try {
      const details = typeof alert.details === 'string' ? JSON.parse(alert.details) : alert.details;

      const incidentData: CreateIncidentDto = {
        title: `Incident from alert: ${alert.type}`,
        description: alert.message || `Security incident created from alert ${alert.type}`,
        severity: this.mapAlertPriorityToIncidentSeverity(alert.priority),
        type: this.mapAlertTypeToIncidentType(alert.type),
        source: 'SECURITY_ALERT',
        reportedById: alert.userId,
        relatedAlertIds: [alert.id],
        metadata: {
          alertId: alert.id,
          alertDetails: details,
        },
      };

      return this.createIncident(incidentData);
    } catch (error) {
      this.logger.error(`Failed to create incident from alert: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Crée un incident à partir d'un événement
   * @param event Événement de sécurité
   * @returns Incident créé
   */
  private async createIncidentFromEvent(event: any): Promise<SecurityIncident> {
    try {
      const details = typeof event.details === 'string' ? JSON.parse(event.details) : event.details;

      const incidentData: CreateIncidentDto = {
        title: `Incident from event: ${event.type}`,
        description: details?.description || `Security incident created from event ${event.type}`,
        severity: this.mapEventSeverityToIncidentSeverity(event.severity),
        type: this.mapEventTypeToIncidentType(event.type),
        source: event.source || 'SECURITY_EVENT',
        reportedById: event.userId,
        relatedEventIds: [event.id],
        metadata: {
          eventId: event.id,
          eventDetails: details,
        },
      };

      return this.createIncident(incidentData);
    } catch (error) {
      this.logger.error(`Failed to create incident from event: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mappe la priorité d'une alerte à la sévérité d'un incident
   * @param alertPriority Priorité de l'alerte
   * @returns Sévérité de l'incident
   */
  private mapAlertPriorityToIncidentSeverity(
    alertPriority: string,
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const mapping: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
      LOW: 'LOW',
      MEDIUM: 'MEDIUM',
      HIGH: 'HIGH',
      CRITICAL: 'CRITICAL',
    };

    return mapping[alertPriority] || 'MEDIUM';
  }

  /**
   * Mappe la sévérité d'un événement à la sévérité d'un incident
   * @param eventSeverity Sévérité de l'événement
   * @returns Sévérité de l'incident
   */
  private mapEventSeverityToIncidentSeverity(
    eventSeverity: string,
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const mapping: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
      INFO: 'LOW',
      WARNING: 'MEDIUM',
      ERROR: 'HIGH',
      CRITICAL: 'CRITICAL',
    };

    return mapping[eventSeverity] || 'MEDIUM';
  }

  /**
   * Mappe le type d'une alerte au type d'un incident
   * @param alertType Type de l'alerte
   * @returns Type de l'incident
   */
  private mapAlertTypeToIncidentType(alertType: string): string {
    const mapping: Record<string, string> = {
      EXCESSIVE_FAILED_LOGINS: 'UNAUTHORIZED_ACCESS',
      EXCESSIVE_RATE_LIMITING: 'DENIAL_OF_SERVICE',
      MALWARE_DETECTED: 'MALWARE',
      RANSOMWARE_DETECTED: 'RANSOMWARE',
      SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
      DATA_BREACH: 'DATA_BREACH',
      SYSTEM_COMPROMISE: 'SYSTEM_COMPROMISE',
    };

    return mapping[alertType] || 'SUSPICIOUS_ACTIVITY';
  }

  /**
   * Mappe le type d'un événement au type d'un incident
   * @param eventType Type de l'événement
   * @returns Type de l'incident
   */
  private mapEventTypeToIncidentType(eventType: string): string {
    const mapping: Record<string, string> = {
      UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
      DATA_BREACH: 'DATA_BREACH',
      MALWARE_DETECTED: 'MALWARE',
      RANSOMWARE_DETECTED: 'RANSOMWARE',
      SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
      SYSTEM_COMPROMISE: 'SYSTEM_COMPROMISE',
      NETWORK_INTRUSION: 'NETWORK_INTRUSION',
      ACCOUNT_COMPROMISE: 'ACCOUNT_COMPROMISE',
      PHISHING_ATTEMPT: 'PHISHING',
      POLICY_VIOLATION: 'POLICY_VIOLATION',
    };

    return mapping[eventType] || 'SUSPICIOUS_ACTIVITY';
  }

  /**
   * Crée un nouvel incident de sécurité
   * @param data Données de l'incident
   * @returns Incident créé
   */
  async createIncident(data: CreateIncidentDto): Promise<SecurityIncident> {
    try {
      // Vérifier si le type d'incident est valide
      if (!this.incidentTypes.includes(data.type)) {
        this.logger.warn(`Invalid incident type: ${data.type}. Using SUSPICIOUS_ACTIVITY instead.`);
        data.type = 'SUSPICIOUS_ACTIVITY';
      }

      // Créer l'incident
      const incident = await this.prisma.securityIncident.create({
        data: {
          title: data.title,
          description: data.description,
          severity: data.severity,
          type: data.type,
          source: data.source,
          affectedAssets: data.affectedAssets || [],
          tags: data.tags || [],
          assignedToId: data.assignedToId,
          reportedById: data.reportedById,
          metadata: data.metadata || {},
        },
      });

      // Ajouter l'activité de création
      await this.addIncidentActivity(incident.id, {
        action: 'CREATE',
        details: 'Incident created',
        userId: data.reportedById,
      });

      // Lier les alertes associées
      if (data.relatedAlertIds && data.relatedAlertIds.length > 0) {
        await this.linkAlertsToIncident(incident.id, data.relatedAlertIds);
      }

      // Lier les événements associés
      if (data.relatedEventIds && data.relatedEventIds.length > 0) {
        await this.linkEventsToIncident(incident.id, data.relatedEventIds);
      }

      // Émettre un événement de création d'incident
      this.eventEmitter.emit('security.incident.created', {
        incidentId: incident.id,
        title: incident.title,
        severity: incident.severity,
        type: incident.type,
        timestamp: new Date(),
      });

      // Notifier les administrateurs
      await this.notifyIncidentCreated(incident);

      this.logger.log(`Security incident created: ${incident.id} - ${incident.title}`);
      return incident;
    } catch (error) {
      this.logger.error(`Failed to create incident: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Récupère un incident par son ID
   * @param id ID de l'incident
   * @returns Incident
   */
  async getIncidentById(id: string): Promise<SecurityIncident> {
    try {
      const incident = await this.prisma.securityIncident.findUnique({
        where: { id },
        include: {
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          reportedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          activities: {
            orderBy: {
              timestamp: 'desc',
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          relatedAlerts: {
            include: {
              alert: true,
            },
          },
        },
      });

      if (!incident) {
        throw new Error(`Incident with ID ${id} not found`);
      }

      return incident as unknown as SecurityIncident;
    } catch (error) {
      this.logger.error(`Failed to get incident by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Récupère tous les incidents avec pagination et filtres
   * @param page Numéro de page
   * @param limit Nombre d'incidents par page
   * @param status Statut des incidents
   * @param severity Sévérité des incidents
   * @param type Type des incidents
   * @param assignedToId ID de l'utilisateur assigné
   * @returns Liste paginée des incidents
   */
  async getIncidents(
    page = 1,
    limit = 10,
    status?: string,
    severity?: string,
    type?: string,
    assignedToId?: string,
  ): Promise<{ incidents: SecurityIncident[]; total: number; page: number; limit: number }> {
    try {
      const skip = (page - 1) * limit;
      const where: any = {};

      if (status) {
        where.status = status;
      }

      if (severity) {
        where.severity = severity;
      }

      if (type) {
        where.type = type;
      }

      if (assignedToId) {
        where.assignedToId = assignedToId;
      }

      const [incidents, total] = await Promise.all([
        this.prisma.securityIncident.findMany({
          where,
          skip,
          take: limit,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            assignedTo: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            reportedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        }),
        this.prisma.securityIncident.count({ where }),
      ]);

      return {
        incidents: incidents as unknown as SecurityIncident[],
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Failed to get incidents: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Met à jour un incident
   * @param id ID de l'incident
   * @param data Données de mise à jour
   * @param userId ID de l'utilisateur effectuant la mise à jour
   * @returns Incident mis à jour
   */
  async updateIncident(
    id: string,
    data: UpdateIncidentDto,
    userId?: string,
  ): Promise<SecurityIncident> {
    try {
      // Récupérer l'incident actuel
      const currentIncident = await this.prisma.securityIncident.findUnique({
        where: { id },
      });

      if (!currentIncident) {
        throw new Error(`Incident with ID ${id} not found`);
      }

      // Préparer les données de mise à jour
      const updateData: any = { ...data };

      // Si le statut change à RESOLVED ou CLOSED, ajouter la date de résolution
      if (
        (data.status === 'RESOLVED' || data.status === 'CLOSED') &&
        currentIncident.status !== 'RESOLVED' &&
        currentIncident.status !== 'CLOSED'
      ) {
        updateData.resolvedAt = new Date();
      }

      // Mettre à jour l'incident
      const updatedIncident = await this.prisma.securityIncident.update({
        where: { id },
        data: updateData,
      });

      // Ajouter l'activité de mise à jour
      const changes = this.getChanges(currentIncident, updatedIncident);
      await this.addIncidentActivity(id, {
        action: 'UPDATE',
        details: `Incident updated: ${changes.join(', ')}`,
        userId,
      });

      // Si le statut a changé, ajouter une activité spécifique
      if (data.status && data.status !== currentIncident.status) {
        await this.addIncidentActivity(id, {
          action: 'STATUS_CHANGE',
          details: `Status changed from ${currentIncident.status} to ${data.status}`,
          userId,
        });

        // Émettre un événement de changement de statut
        this.eventEmitter.emit('security.incident.status_changed', {
          incidentId: id,
          oldStatus: currentIncident.status,
          newStatus: data.status,
          timestamp: new Date(),
        });

        // Si l'incident est résolu ou fermé, notifier
        if (data.status === 'RESOLVED' || data.status === 'CLOSED') {
          await this.notifyIncidentResolved(updatedIncident);
        }
      }

      // Si l'assignation a changé, ajouter une activité spécifique
      if (data.assignedToId !== undefined && data.assignedToId !== currentIncident.assignedToId) {
        const action = data.assignedToId ? 'ASSIGNED' : 'UNASSIGNED';
        await this.addIncidentActivity(id, {
          action,
          details: data.assignedToId
            ? `Incident assigned to user ${data.assignedToId}`
            : 'Incident unassigned',
          userId,
        });

        // Notifier l'utilisateur assigné
        if (data.assignedToId) {
          await this.notifyIncidentAssigned(updatedIncident, data.assignedToId);
        }
      }

      this.logger.log(`Security incident updated: ${id}`);
      return updatedIncident as unknown as SecurityIncident;
    } catch (error) {
      this.logger.error(`Failed to update incident: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Supprime un incident
   * @param id ID de l'incident
   * @param userId ID de l'utilisateur effectuant la suppression
   * @returns true si la suppression a réussi
   */
  async deleteIncident(id: string, userId?: string): Promise<boolean> {
    try {
      // Vérifier si l'incident existe
      const incident = await this.prisma.securityIncident.findUnique({
        where: { id },
      });

      if (!incident) {
        throw new Error(`Incident with ID ${id} not found`);
      }

      // Supprimer les relations avec les alertes
      await this.prisma.incidentAlertRelation.deleteMany({
        where: { incidentId: id },
      });

      // Supprimer les relations avec les événements
      await this.prisma.incidentEventRelation.deleteMany({
        where: { incidentId: id },
      });

      // Supprimer les activités
      await this.prisma.incidentActivity.deleteMany({
        where: { incidentId: id },
      });

      // Supprimer l'incident
      await this.prisma.securityIncident.delete({
        where: { id },
      });

      // Émettre un événement de suppression d'incident
      this.eventEmitter.emit('security.incident.deleted', {
        incidentId: id,
        title: incident.title,
        userId,
        timestamp: new Date(),
      });

      this.logger.log(`Security incident deleted: ${id}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete incident: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Ajoute une activité à un incident
   * @param incidentId ID de l'incident
   * @param data Données de l'activité
   * @returns Activité créée
   */
  async addIncidentActivity(
    incidentId: string,
    data: {
      action: string;
      details?: string;
      userId?: string;
      metadata?: Record<string, any>;
    },
  ): Promise<IncidentActivity> {
    try {
      const activity = await this.prisma.incidentActivity.create({
        data: {
          incidentId,
          action: data.action,
          details: data.details,
          userId: data.userId,
          metadata: data.metadata || {},
        },
      });

      return activity as unknown as IncidentActivity;
    } catch (error) {
      this.logger.error(`Failed to add incident activity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lie des alertes à un incident
   * @param incidentId ID de l'incident
   * @param alertIds IDs des alertes
   */
  private async linkAlertsToIncident(incidentId: string, alertIds: string[]): Promise<void> {
    try {
      // Créer les relations
      const relations = alertIds.map((alertId) => ({
        incidentId,
        alertId,
      }));

      // Insérer les relations
      await this.prisma.$transaction(
        relations.map((relation) =>
          this.prisma.incidentAlertRelation.create({
            data: relation,
          }),
        ),
      );
    } catch (error) {
      this.logger.error(`Failed to link alerts to incident: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lie des événements à un incident
   * @param incidentId ID de l'incident
   * @param eventIds IDs des événements
   */
  private async linkEventsToIncident(incidentId: string, eventIds: string[]): Promise<void> {
    try {
      // Créer les relations
      const relations = eventIds.map((eventId) => ({
        incidentId,
        eventId,
      }));

      // Insérer les relations
      await this.prisma.$transaction(
        relations.map((relation) =>
          this.prisma.incidentEventRelation.create({
            data: relation,
          }),
        ),
      );
    } catch (error) {
      this.logger.error(`Failed to link events to incident: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Obtient les changements entre deux objets
   * @param oldObj Ancien objet
   * @param newObj Nouvel objet
   * @returns Liste des changements
   */
  private getChanges(oldObj: any, newObj: any): string[] {
    const changes: string[] = [];

    for (const key in newObj) {
      if (
        Object.prototype.hasOwnProperty.call(newObj, key) &&
        JSON.stringify(oldObj[key]) !== JSON.stringify(newObj[key])
      ) {
        changes.push(key);
      }
    }

    return changes;
  }

  /**
   * Notifie les administrateurs de la création d'un incident
   * @param incident Incident créé
   */
  private async notifyIncidentCreated(incident: SecurityIncident): Promise<void> {
    try {
      // Récupérer les administrateurs
      const admins = await this.prisma.user.findMany({
        where: {
          role: {
            has: 'ADMIN',
          },
        },
        select: {
          id: true,
        },
      });

      // Créer des notifications pour chaque administrateur
      for (const admin of admins) {
        await this.securityNotificationService.createNotification({
          userId: admin.id,
          title: `New Security Incident: ${incident.title}`,
          message: `A new security incident has been created with severity ${incident.severity}. Please review it as soon as possible.`,
          severity: this.mapIncidentSeverityToNotificationSeverity(incident.severity),
          type: 'INCIDENT_CREATED',
          actionRequired: true,
          actionUrl: `/security/incidents/${incident.id}`,
          actionText: 'View Incident',
        });
      }
    } catch (error) {
      this.logger.error(`Failed to notify incident created: ${error.message}`, error.stack);
    }
  }

  /**
   * Notifie les administrateurs de la résolution d'un incident
   * @param incident Incident résolu
   */
  private async notifyIncidentResolved(incident: SecurityIncident): Promise<void> {
    try {
      // Récupérer les administrateurs
      const admins = await this.prisma.user.findMany({
        where: {
          role: {
            has: 'ADMIN',
          },
        },
        select: {
          id: true,
        },
      });

      // Créer des notifications pour chaque administrateur
      for (const admin of admins) {
        await this.securityNotificationService.createNotification({
          userId: admin.id,
          title: `Security Incident Resolved: ${incident.title}`,
          message: `The security incident has been marked as ${incident.status}.`,
          severity: 'INFO',
          type: 'INCIDENT_RESOLVED',
          actionRequired: false,
          actionUrl: `/security/incidents/${incident.id}`,
          actionText: 'View Incident',
        });
      }
    } catch (error) {
      this.logger.error(`Failed to notify incident resolved: ${error.message}`, error.stack);
    }
  }

  /**
   * Notifie un utilisateur qu'un incident lui a été assigné
   * @param incident Incident assigné
   * @param userId ID de l'utilisateur assigné
   */
  private async notifyIncidentAssigned(incident: SecurityIncident, userId: string): Promise<void> {
    try {
      await this.securityNotificationService.createNotification({
        userId,
        title: `Security Incident Assigned: ${incident.title}`,
        message: `You have been assigned to handle a security incident with severity ${incident.severity}.`,
        severity: this.mapIncidentSeverityToNotificationSeverity(incident.severity),
        type: 'INCIDENT_ASSIGNED',
        actionRequired: true,
        actionUrl: `/security/incidents/${incident.id}`,
        actionText: 'View Incident',
      });
    } catch (error) {
      this.logger.error(`Failed to notify incident assigned: ${error.message}`, error.stack);
    }
  }

  /**
   * Mappe la sévérité d'un incident à la sévérité d'une notification
   * @param incidentSeverity Sévérité de l'incident
   * @returns Sévérité de la notification
   */
  private mapIncidentSeverityToNotificationSeverity(
    incidentSeverity: string,
  ): 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL' {
    const mapping: Record<string, 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'> = {
      LOW: 'INFO',
      MEDIUM: 'WARNING',
      HIGH: 'ERROR',
      CRITICAL: 'CRITICAL',
    };

    return mapping[incidentSeverity] || 'WARNING';
  }
}
