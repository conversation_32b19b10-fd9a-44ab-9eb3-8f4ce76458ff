import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EndToEndEncryptionService, E2EEncryptedData } from './end-to-end-encryption.service';
import { KeyManagementService } from './key-management.service';
import { CertificateRotationService, CertificateValidationResult } from './certificate-rotation.service';
import { promisify } from 'util';
import * as child_process from 'child_process';

/**
 * Interface pour les options de configuration de la communication entre microservices
 */
export interface MicroserviceSecurityOptions {
  enabled: boolean;
  mtlsEnabled: boolean;
  e2eEncryptionEnabled: boolean;
  certPath: string;
  keyPath: string;
  caPath: string;
  rejectUnauthorized: boolean;
  timeout: number;
  autoKeyRotation?: boolean;
  keyRotationInterval?: number; // en millisecondes
  certificateValidationInterval?: number; // en millisecondes
  crlPath?: string; // Chemin vers la liste de révocation de certificats
}

/**
 * Service de sécurité pour la communication entre microservices
 * Ce service fournit des méthodes pour sécuriser les communications entre microservices
 * en utilisant mTLS (Mutual TLS) et le chiffrement de bout en bout
 */
@Injectable()
export class MicroserviceSecurityService implements OnModuleInit {
  private readonly logger = new Logger(MicroserviceSecurityService.name);
  private readonly options: MicroserviceSecurityOptions;
  private _httpsAgent: https.Agent | null = null;

  // Getter pour httpsAgent
  private get httpsAgent(): https.Agent | null {
    return this._httpsAgent;
  }

  // Setter pour httpsAgent
  private set httpsAgent(agent: https.Agent | null) {
    this._httpsAgent = agent;
  }
  private readonly axiosInstances: Map<string, AxiosInstance> = new Map();
  private readonly serviceKeyPairs: Map<string, { publicKey: string; privateKey: string; version: number; rotatedAt?: Date }> = new Map();
  private readonly exec = promisify(child_process.exec);
  private readonly trustedFingerprints: Set<string> = new Set();
  private keyRotationInterval: NodeJS.Timeout | null = null;
  private certificateValidationInterval: NodeJS.Timeout | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly e2eEncryptionService: EndToEndEncryptionService,
    private readonly keyManagementService: KeyManagementService,
    private readonly certificateRotationService: CertificateRotationService,
  ) {
    this.options = {
      enabled: this.configService.get<boolean>('security.microservice.enabled', true),
      mtlsEnabled: this.configService.get<boolean>('ZERO_TRUST_MTLS_ENABLED', false),
      e2eEncryptionEnabled: this.configService.get<boolean>('E2E_ENCRYPTION_ENABLED', true),
      certPath: this.configService.get<string>('MTLS_CERT_PATH', './certs/client.crt'),
      keyPath: this.configService.get<string>('MTLS_KEY_PATH', './certs/client.key'),
      caPath: this.configService.get<string>('MTLS_CA_PATH', './certs/ca.crt'),
      crlPath: this.configService.get<string>('MTLS_CRL_PATH', './certs/ca.crl'),
      rejectUnauthorized: this.configService.get<boolean>('MTLS_REJECT_UNAUTHORIZED', true),
      timeout: this.configService.get<number>('MICROSERVICE_REQUEST_TIMEOUT', 30000),
      autoKeyRotation: this.configService.get<boolean>('AUTO_KEY_ROTATION', true),
      keyRotationInterval: this.configService.get<number>('KEY_ROTATION_INTERVAL', 7 * 24 * 60 * 60 * 1000), // 7 jours par défaut
      certificateValidationInterval: this.configService.get<number>('CERT_VALIDATION_INTERVAL', 24 * 60 * 60 * 1000), // 1 jour par défaut
    };

    if (this.options.enabled && this.options.mtlsEnabled) {
      try {
        this.httpsAgent = this.createHttpsAgent();
        this.logger.log('mTLS HTTPS agent created successfully');
      } catch (error) {
        this.logger.error('Failed to create mTLS HTTPS agent:', error);
      }
    }

    if (this.options.enabled && this.options.e2eEncryptionEnabled) {
      this.initializeServiceKeyPairs();
    }
  }

  /**
   * Initialisation du service lors du démarrage de l'application
   */
  async onModuleInit() {
    try {
      // Initialiser les empreintes de certificats de confiance
      await this.initializeTrustedFingerprints();

      // Planifier la validation périodique des certificats
      this.scheduleCertificateValidation();

      // Planifier la rotation automatique des clés
      if (this.options.autoKeyRotation) {
        this.scheduleKeyRotation();
      }

      this.logger.log('MicroserviceSecurityService initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize MicroserviceSecurityService', error);
    }
  }

  /**
   * Crée un agent HTTPS avec mTLS
   * @returns Agent HTTPS configuré avec mTLS
   */
  private createHttpsAgent(): https.Agent {
    try {
      // Vérifier si les fichiers de certificat existent
      const certPath = path.resolve(process.cwd(), this.options.certPath);
      const keyPath = path.resolve(process.cwd(), this.options.keyPath);
      const caPath = path.resolve(process.cwd(), this.options.caPath);

      if (!fs.existsSync(certPath)) {
        throw new Error(`Certificate file not found: ${certPath}`);
      }

      if (!fs.existsSync(keyPath)) {
        throw new Error(`Key file not found: ${keyPath}`);
      }

      if (!fs.existsSync(caPath)) {
        throw new Error(`CA certificate file not found: ${caPath}`);
      }

      // Créer l'agent HTTPS avec mTLS
      return new https.Agent({
        cert: fs.readFileSync(certPath),
        key: fs.readFileSync(keyPath),
        ca: fs.readFileSync(caPath),
        rejectUnauthorized: this.options.rejectUnauthorized,
        minVersion: 'TLSv1.3',
      });
    } catch (error) {
      this.logger.error('Failed to create HTTPS agent:', error);
      throw error;
    }
  }

  /**
   * Initialise les paires de clés pour les services
   */
  private async initializeServiceKeyPairs(): Promise<void> {
    try {
      // Générer une paire de clés pour ce service
      const keyPair = await this.e2eEncryptionService.generateUserKeyPair();
      this.serviceKeyPairs.set('self', {
        ...keyPair,
        version: 1,
        rotatedAt: new Date()
      });
      this.logger.log('Service key pair generated successfully');

      // Dans une implémentation réelle, on récupérerait les clés publiques des autres services
      // depuis un service de découverte ou un registre de clés
    } catch (error) {
      this.logger.error('Failed to initialize service key pairs:', error);
    }
  }

  /**
   * Initialise les empreintes de certificats de confiance
   */
  private async initializeTrustedFingerprints(): Promise<void> {
    try {
      // Vérifier si le fichier CA existe
      const caPath = path.resolve(process.cwd(), this.options.caPath);
      if (!fs.existsSync(caPath)) {
        this.logger.warn(`CA certificate file not found: ${caPath}`);
        return;
      }

      // Calculer l'empreinte du certificat CA
      const caFingerprint = await this.calculateCertificateFingerprint(caPath);
      this.trustedFingerprints.add(caFingerprint);

      // Dans une implémentation réelle, on chargerait également les empreintes
      // des certificats de confiance depuis une source sécurisée

      this.logger.log(`Initialized ${this.trustedFingerprints.size} trusted certificate fingerprints`);
    } catch (error) {
      this.logger.error('Failed to initialize trusted fingerprints:', error);
    }
  }

  /**
   * Calcule l'empreinte d'un certificat
   * @param certPath Chemin du certificat
   * @returns Empreinte du certificat (SHA-256)
   */
  private async calculateCertificateFingerprint(certPath: string): Promise<string> {
    try {
      const { stdout } = await this.exec(
        `openssl x509 -in "${certPath}" -noout -fingerprint -sha256`
      );

      // Extraire l'empreinte du résultat (format: SHA256 Fingerprint=XX:XX:XX...)
      const fingerprintMatch = stdout.match(/Fingerprint=([0-9A-F:]+)/);
      if (!fingerprintMatch) {
        throw new Error('Failed to extract certificate fingerprint');
      }

      // Normaliser l'empreinte (supprimer les :)
      return fingerprintMatch[1].replace(/:/g, '').toLowerCase();
    } catch (error) {
      this.logger.error(`Failed to calculate certificate fingerprint: ${error.message}`);
      throw error;
    }
  }

  /**
   * Obtient une instance Axios configurée pour un service spécifique
   * @param serviceUrl URL du service
   * @returns Instance Axios configurée
   */
  private getAxiosInstance(serviceUrl: string): AxiosInstance {
    // Vérifier si une instance existe déjà pour ce service
    if (this.axiosInstances.has(serviceUrl)) {
      return this.axiosInstances.get(serviceUrl)!;
    }

    // Créer une nouvelle instance Axios
    const config: AxiosRequestConfig = {
      baseURL: serviceUrl,
      timeout: this.options.timeout,
    };

    // Ajouter l'agent HTTPS si mTLS est activé
    if (this.options.mtlsEnabled && this.httpsAgent) {
      config.httpsAgent = this.httpsAgent;
    }

    const instance = axios.create(config);

    // Ajouter des intercepteurs pour le chiffrement/déchiffrement si E2E est activé
    if (this.options.e2eEncryptionEnabled) {
      // Intercepteur de requête pour chiffrer les données
      instance.interceptors.request.use(async (config) => {
        if (config.data && typeof config.data === 'object') {
          const targetServicePublicKey = this.getServicePublicKey(serviceUrl);
          if (targetServicePublicKey) {
            const encryptedData = await this.encryptPayload(config.data, targetServicePublicKey);
            config.data = { encryptedPayload: encryptedData };
          }
        }
        return config;
      });

      // Intercepteur de réponse pour déchiffrer les données
      instance.interceptors.response.use(async (response) => {
        if (response.data && response.data.encryptedPayload) {
          const selfKeyPair = this.serviceKeyPairs.get('self');
          if (selfKeyPair) {
            response.data = await this.decryptPayload(
              response.data.encryptedPayload,
              selfKeyPair.privateKey
            );
          }
        }
        return response;
      });
    }

    // Stocker l'instance pour une utilisation future
    this.axiosInstances.set(serviceUrl, instance);
    return instance;
  }

  /**
   * Obtient la clé publique d'un service
   * @param serviceUrl URL du service
   * @returns Clé publique du service
   */
  private getServicePublicKey(serviceUrl: string): string | null {
    // Dans une implémentation réelle, on récupérerait la clé publique du service
    // depuis un service de découverte ou un registre de clés
    // Ici, on simule avec une clé fixe pour la démonstration
    return 'simulated-public-key';
  }

  /**
   * Chiffre un payload pour un service cible
   * @param payload Payload à chiffrer
   * @param targetPublicKey Clé publique du service cible
   * @returns Payload chiffré
   */
  private async encryptPayload(payload: any, targetPublicKey: string): Promise<string> {
    try {
      const encryptedData = await this.e2eEncryptionService.encrypt(
        JSON.stringify(payload),
        targetPublicKey
      );
      return JSON.stringify(encryptedData);
    } catch (error) {
      this.logger.error('Failed to encrypt payload:', error);
      throw error;
    }
  }

  /**
   * Déchiffre un payload avec la clé privée du service
   * @param encryptedPayload Payload chiffré
   * @param privateKey Clé privée du service
   * @returns Payload déchiffré
   */
  private async decryptPayload(encryptedPayload: string, privateKey: string): Promise<any> {
    try {
      const encryptedData = JSON.parse(encryptedPayload) as E2EEncryptedData;
      const decryptedData = await this.e2eEncryptionService.decrypt(encryptedData, privateKey);
      return JSON.parse(decryptedData);
    } catch (error) {
      this.logger.error('Failed to decrypt payload:', error);
      throw error;
    }
  }

  /**
   * Envoie une requête sécurisée à un microservice
   * @param serviceUrl URL du service
   * @param method Méthode HTTP
   * @param endpoint Endpoint de l'API
   * @param data Données à envoyer
   * @returns Réponse du service
   */
  async sendRequest<T = any>(
    serviceUrl: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    try {
      if (!this.options.enabled) {
        throw new Error('Microservice security is disabled');
      }

      const instance = this.getAxiosInstance(serviceUrl);
      const response = await instance.request<T>({
        method,
        url: endpoint,
        data,
        headers,
      });

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send request to ${serviceUrl}${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Envoie une requête GET sécurisée à un microservice
   * @param serviceUrl URL du service
   * @param endpoint Endpoint de l'API
   * @param params Paramètres de requête
   * @returns Réponse du service
   */
  async get<T = any>(
    serviceUrl: string,
    endpoint: string,
    params?: Record<string, any>,
    headers?: Record<string, string>
  ): Promise<T> {
    const instance = this.getAxiosInstance(serviceUrl);
    const response = await instance.get<T>(endpoint, { params, headers });
    return response.data;
  }

  /**
   * Envoie une requête POST sécurisée à un microservice
   * @param serviceUrl URL du service
   * @param endpoint Endpoint de l'API
   * @param data Données à envoyer
   * @returns Réponse du service
   */
  async post<T = any>(
    serviceUrl: string,
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    const instance = this.getAxiosInstance(serviceUrl);
    const response = await instance.post<T>(endpoint, data, { headers });
    return response.data;
  }

  /**
   * Envoie une requête PUT sécurisée à un microservice
   * @param serviceUrl URL du service
   * @param endpoint Endpoint de l'API
   * @param data Données à envoyer
   * @returns Réponse du service
   */
  async put<T = any>(
    serviceUrl: string,
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    const instance = this.getAxiosInstance(serviceUrl);
    const response = await instance.put<T>(endpoint, data, { headers });
    return response.data;
  }

  /**
   * Envoie une requête DELETE sécurisée à un microservice
   * @param serviceUrl URL du service
   * @param endpoint Endpoint de l'API
   * @returns Réponse du service
   */
  async delete<T = any>(
    serviceUrl: string,
    endpoint: string,
    headers?: Record<string, string>
  ): Promise<T> {
    const instance = this.getAxiosInstance(serviceUrl);
    const response = await instance.delete<T>(endpoint, { headers });
    return response.data;
  }

  /**
   * Enregistre la clé publique d'un service
   * @param serviceId Identifiant du service
   * @param publicKey Clé publique du service
   * @param version Version de la clé
   */
  registerServicePublicKey(serviceId: string, publicKey: string, version: number = 1): void {
    this.serviceKeyPairs.set(serviceId, {
      publicKey,
      privateKey: '', // Pas de clé privée pour les services externes
      version,
      rotatedAt: new Date()
    });
    this.logger.log(`Registered public key for service: ${serviceId} (version ${version})`);
  }

  /**
   * Vérifie si un certificat est valide et de confiance
   * @param certPath Chemin du certificat à vérifier
   * @returns Résultat de la validation
   */
  async validateCertificate(certPath: string): Promise<CertificateValidationResult> {
    try {
      // Utiliser le service de rotation des certificats pour la validation de base
      const validationResult = await this.certificateRotationService.validateCertificate(certPath);

      // Si la validation de base échoue, retourner le résultat
      if (!validationResult.valid) {
        return validationResult;
      }

      // Vérifications supplémentaires

      // 1. Vérifier si le certificat est révoqué
      const isRevoked = await this.checkCertificateRevocation(certPath);
      if (isRevoked) {
        return {
          valid: false,
          details: {
            ...validationResult.details,
            error: 'Certificate has been revoked'
          }
        };
      }

      // 2. Vérifier l'empreinte du certificat
      const fingerprint = await this.calculateCertificateFingerprint(certPath);
      const isTrusted = this.trustedFingerprints.has(fingerprint);

      if (!isTrusted) {
        return {
          valid: false,
          details: {
            ...validationResult.details,
            error: 'Certificate fingerprint is not trusted'
          }
        };
      }

      // 3. Vérifier la chaîne de confiance complète
      const chainValid = await this.verifyTrustChain(certPath);
      if (!chainValid) {
        return {
          valid: false,
          details: {
            ...validationResult.details,
            error: 'Certificate trust chain validation failed'
          }
        };
      }

      return validationResult;
    } catch (error) {
      this.logger.error(`Certificate validation failed: ${error.message}`);
      return {
        valid: false,
        details: {
          error: `Validation error: ${error.message}`
        }
      };
    }
  }

  /**
   * Vérifie si un certificat est révoqué
   * @param certPath Chemin du certificat à vérifier
   * @returns true si le certificat est révoqué
   */
  private async checkCertificateRevocation(certPath: string): Promise<boolean> {
    try {
      // Vérifier si la liste de révocation existe
      const crlPath = path.resolve(process.cwd(), this.options.crlPath);
      if (!fs.existsSync(crlPath)) {
        this.logger.warn(`CRL file not found: ${crlPath}`);
        return false; // Considérer que le certificat n'est pas révoqué si la CRL n'existe pas
      }

      // Extraire le numéro de série du certificat
      const { stdout: certInfo } = await this.exec(
        `openssl x509 -in "${certPath}" -noout -serial`
      );

      const serialMatch = certInfo.match(/serial=([0-9A-Fa-f]+)/);
      if (!serialMatch) {
        throw new Error('Failed to extract certificate serial number');
      }

      const serialNumber = serialMatch[1];

      // Vérifier si le numéro de série est dans la CRL
      const { stdout: crlInfo } = await this.exec(
        `openssl crl -in "${crlPath}" -noout -text`
      );

      // Rechercher le numéro de série dans la CRL
      return crlInfo.includes(serialNumber);
    } catch (error) {
      this.logger.error(`Failed to check certificate revocation: ${error.message}`);
      return false; // En cas d'erreur, considérer que le certificat n'est pas révoqué
    }
  }

  /**
   * Vérifie la chaîne de confiance d'un certificat
   * @param certPath Chemin du certificat à vérifier
   * @returns true si la chaîne de confiance est valide
   */
  private async verifyTrustChain(certPath: string): Promise<boolean> {
    try {
      const caPath = path.resolve(process.cwd(), this.options.caPath);

      // Vérifier la chaîne de confiance avec OpenSSL
      await this.exec(`openssl verify -CAfile "${caPath}" "${certPath}"`);
      return true;
    } catch (error) {
      this.logger.error(`Trust chain verification failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Planifie la validation périodique des certificats
   */
  private scheduleCertificateValidation(): void {
    // Annuler l'intervalle existant s'il y en a un
    if (this.certificateValidationInterval) {
      clearInterval(this.certificateValidationInterval);
    }

    // Vérifier les certificats périodiquement
    this.certificateValidationInterval = setInterval(async () => {
      try {
        if (this.options.mtlsEnabled) {
          const certPath = path.resolve(process.cwd(), this.options.certPath);
          const validationResult = await this.validateCertificate(certPath);

          if (!validationResult.valid) {
            this.logger.warn(`Certificate validation failed: ${validationResult.details.error}`);

            // Demander une rotation du certificat si nécessaire
            await this.certificateRotationService.rotateCertificates();

            // Recréer l'agent HTTPS avec les nouveaux certificats
            this.httpsAgent = this.createHttpsAgent();

            // Vider le cache des instances Axios pour forcer leur recréation
            this.axiosInstances.clear();
          } else {
            this.logger.debug(`Certificate validation successful. Expires in ${validationResult.details.expiresIn} days.`);
          }
        }
      } catch (error) {
        this.logger.error('Error during scheduled certificate validation:', error);
      }
    }, this.options.certificateValidationInterval);

    this.logger.log('Certificate validation scheduler started');
  }

  /**
   * Planifie la rotation automatique des clés
   */
  private scheduleKeyRotation(): void {
    // Annuler l'intervalle existant s'il y en a un
    if (this.keyRotationInterval) {
      clearInterval(this.keyRotationInterval);
    }

    // Effectuer la rotation des clés périodiquement
    this.keyRotationInterval = setInterval(async () => {
      try {
        await this.rotateServiceKeys();
      } catch (error) {
        this.logger.error('Error during scheduled key rotation:', error);
      }
    }, this.options.keyRotationInterval);

    this.logger.log('Key rotation scheduler started');
  }

  /**
   * Effectue la rotation des clés de service
   */
  @Cron(CronExpression.EVERY_WEEK)
  async rotateServiceKeys(): Promise<void> {
    try {
      if (!this.options.autoKeyRotation) {
        return;
      }

      this.logger.log('Starting service key rotation');

      // Générer une nouvelle paire de clés pour ce service
      const keyPair = await this.e2eEncryptionService.generateUserKeyPair();

      // Récupérer l'ancienne paire de clés
      const oldKeyPair = this.serviceKeyPairs.get('self');

      if (!oldKeyPair) {
        throw new Error('No existing key pair found for rotation');
      }

      // Mettre à jour la paire de clés avec la nouvelle version
      this.serviceKeyPairs.set('self', {
        ...keyPair,
        version: oldKeyPair.version + 1,
        rotatedAt: new Date()
      });

      // Conserver l'ancienne clé pendant une période de transition
      this.serviceKeyPairs.set('self-previous', {
        ...oldKeyPair,
        rotatedAt: new Date()
      });

      this.logger.log(`Service key rotated successfully. New version: ${oldKeyPair.version + 1}`);

      // Journaliser l'événement de rotation
      this.logSecurityEvent('key-rotation', {
        keyType: 'service',
        serviceId: 'self',
        oldVersion: oldKeyPair.version,
        newVersion: oldKeyPair.version + 1
      });
    } catch (error) {
      this.logger.error('Failed to rotate service keys:', error);
      throw error;
    }
  }

  /**
   * Journalise un événement de sécurité
   * @param eventType Type d'événement
   * @param details Détails de l'événement
   */
  private logSecurityEvent(eventType: string, details: any): void {
    const event = {
      timestamp: new Date(),
      type: eventType,
      service: 'microservice-security',
      details
    };

    this.logger.log(`Security event: ${eventType}`, event);

    // Dans une implémentation réelle, on enverrait cet événement à un service de journalisation sécurisé
  }

  /**
   * Obtient la clé publique de ce service
   * @returns Clé publique de ce service et sa version
   */
  getPublicKey(): { publicKey: string; version: number } | null {
    const selfKeyPair = this.serviceKeyPairs.get('self');
    return selfKeyPair ? { publicKey: selfKeyPair.publicKey, version: selfKeyPair.version } : null;
  }

  /**
   * Force la rotation des clés de service
   * @returns Nouvelle version de la clé
   */
  async forceKeyRotation(): Promise<number> {
    await this.rotateServiceKeys();
    const selfKeyPair = this.serviceKeyPairs.get('self');
    return selfKeyPair ? selfKeyPair.version : 0;
  }

  /**
   * Vérifie l'état de santé du service de sécurité des microservices
   * @returns État de santé du service
   */
  async healthCheck(): Promise<{
    status: 'ok' | 'warning' | 'error';
    details: {
      mtls: boolean;
      e2eEncryption: boolean;
      certificateValid: boolean;
      certificateExpiresIn?: number;
      keyRotation: boolean;
      lastKeyRotation?: Date;
    };
  }> {
    try {
      // Vérifier l'état des certificats
      let certificateValid = false;
      let certificateExpiresIn = undefined;

      if (this.options.mtlsEnabled) {
        const certPath = path.resolve(process.cwd(), this.options.certPath);
        const validationResult = await this.validateCertificate(certPath);
        certificateValid = validationResult.valid;
        certificateExpiresIn = validationResult.details.expiresIn;
      }

      // Vérifier l'état des clés
      const selfKeyPair = this.serviceKeyPairs.get('self');
      const lastKeyRotation = selfKeyPair?.rotatedAt;

      // Déterminer le statut global
      let status: 'ok' | 'warning' | 'error' = 'ok';

      if (!certificateValid && this.options.mtlsEnabled) {
        status = 'error';
      } else if (certificateExpiresIn && certificateExpiresIn < 7 && this.options.mtlsEnabled) {
        status = 'warning';
      }

      return {
        status,
        details: {
          mtls: this.options.mtlsEnabled,
          e2eEncryption: this.options.e2eEncryptionEnabled,
          certificateValid,
          certificateExpiresIn,
          keyRotation: this.options.autoKeyRotation,
          lastKeyRotation
        }
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'error',
        details: {
          mtls: this.options.mtlsEnabled,
          e2eEncryption: this.options.e2eEncryptionEnabled,
          certificateValid: false,
          keyRotation: this.options.autoKeyRotation
        }
      };
    }
  }
}
