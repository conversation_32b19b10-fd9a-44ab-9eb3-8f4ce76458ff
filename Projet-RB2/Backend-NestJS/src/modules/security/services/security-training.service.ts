import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CacheService } from '../../../modules/cache/cache.service';

interface SecurityTrainingSimulation {
  id: string;
  type: 'phishing' | 'social_engineering' | 'password_security' | 'data_handling';
  difficulty: 'basic' | 'intermediate' | 'advanced';
  scenario: string;
  correctResponse: string;
  hints: string[];
  resources: string[];
}

interface SimulationResult {
  userId: string;
  simulationId: string;
  success: boolean;
  timeToComplete: number;
  mistakes: string[];
  feedback: string;
  score: number;
}

@Injectable()
export class SecurityTrainingService {
  private readonly logger = new Logger(SecurityTrainingService.name);
  private readonly CACHE_TTL = 3600; // 1 hour

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly cacheService: CacheService,
  ) {}

  async createSecuritySimulation(data: Partial<SecurityTrainingSimulation>): Promise<SecurityTrainingSimulation> {
    try {
      const simulation = await this.prisma.securitySimulation.create({
        data: {
          type: data.type,
          difficulty: data.difficulty || 'basic',
          scenario: data.scenario,
          correctResponse: data.correctResponse,
          hints: data.hints || [],
          resources: data.resources || [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      await this.cacheService.del('security:simulations:list');
      this.eventEmitter.emit('security.simulation.created', simulation);

      return simulation;
    } catch (error) {
      this.logger.error(`Error creating security simulation: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSimulations(type?: string, difficulty?: string): Promise<SecurityTrainingSimulation[]> {
    const cacheKey = `security:simulations:${type || 'all'}:${difficulty || 'all'}`;

    try {
      const cached = await this.cacheService.get<SecurityTrainingSimulation[]>(cacheKey);
      if (cached) {
        return cached;
      }

      const where = {
        ...(type && { type }),
        ...(difficulty && { difficulty }),
      };

      const simulations = await this.prisma.securitySimulation.findMany({ where });
      await this.cacheService.set(cacheKey, simulations, this.CACHE_TTL);

      return simulations;
    } catch (error) {
      this.logger.error(`Error fetching simulations: ${error.message}`, error.stack);
      throw error;
    }
  }

  async startSimulation(userId: string, simulationId: string): Promise<SimulationResult> {
    try {
      const simulation = await this.prisma.securitySimulation.findUnique({
        where: { id: simulationId },
      });

      if (!simulation) {
        throw new Error(`Simulation ${simulationId} not found`);
      }

      const attempt = await this.prisma.simulationAttempt.create({
        data: {
          userId,
          simulationId,
          startedAt: new Date(),
          status: 'IN_PROGRESS',
        },
      });

      this.eventEmitter.emit('security.simulation.started', {
        userId,
        simulationId,
        attemptId: attempt.id,
      });

      return {
        userId,
        simulationId,
        success: false,
        timeToComplete: 0,
        mistakes: [],
        feedback: 'Simulation in progress',
        score: 0,
      };
    } catch (error) {
      this.logger.error(`Error starting simulation: ${error.message}`, error.stack);
      throw error;
    }
  }

  async completeSimulation(
    userId: string,
    simulationId: string,
    response: string,
  ): Promise<SimulationResult> {
    try {
      const [simulation, attempt] = await Promise.all([
        this.prisma.securitySimulation.findUnique({
          where: { id: simulationId },
        }),
        this.prisma.simulationAttempt.findFirst({
          where: {
            userId,
            simulationId,
            status: 'IN_PROGRESS',
          },
        }),
      ]);

      if (!attempt) {
        throw new Error('No active simulation attempt found');
      }

      const timeToComplete = Date.now() - attempt.startedAt.getTime();
      const success = response === simulation.correctResponse;
      const score = this.calculateScore(success, timeToComplete, simulation.difficulty);

      const result: SimulationResult = {
        userId,
        simulationId,
        success,
        timeToComplete,
        mistakes: success ? [] : ['incorrect_response'],
        feedback: this.generateFeedback(simulation, success),
        score,
      };

      await this.prisma.simulationAttempt.update({
        where: { id: attempt.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          success,
          timeToComplete,
          score,
        },
      });

      await this.updateUserSecurityScore(userId, score);
      this.eventEmitter.emit('security.simulation.completed', result);

      return result;
    } catch (error) {
      this.logger.error(`Error completing simulation: ${error.message}`, error.stack);
      throw error;
    }
  }

  private calculateScore(success: boolean, timeToComplete: number, difficulty: string): number {
    if (!success) return 0;

    const baseScore = {
      basic: 100,
      intermediate: 200,
      advanced: 300,
    }[difficulty] || 100;

    const timeBonus = Math.max(0, 50 - Math.floor(timeToComplete / 1000)); // Bonus based on completion time
    return baseScore + timeBonus;
  }

  private async updateUserSecurityScore(userId: string, simulationScore: number): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { securityScore: true },
      });

      const newScore = Math.round((user.securityScore + simulationScore) / 2);

      await this.prisma.user.update({
        where: { id: userId },
        data: { securityScore: newScore },
      });

      await this.cacheService.del(`user:${userId}:security-score`);
    } catch (error) {
      this.logger.error(`Error updating user security score: ${error.message}`, error.stack);
    }
  }

  private generateFeedback(simulation: SecurityTrainingSimulation, success: boolean): string {
    if (success) {
      return `Excellent! Vous avez correctement géré ce scénario de ${simulation.type}. Continuez à appliquer ces bonnes pratiques dans votre travail quotidien.`;
    }

    return `La réponse correcte aurait été : ${simulation.correctResponse}. Consultez les ressources fournies pour en apprendre davantage sur la prévention des attaques de type ${simulation.type}.`;
  }

  async getUserSecurityStats(userId: string): Promise<any> {
    const cacheKey = `user:${userId}:security-stats`;

    try {
      const cached = await this.cacheService.get<any>(cacheKey);
      if (cached) {
        return cached;
      }

      const attempts = await this.prisma.simulationAttempt.findMany({
        where: { userId },
        include: { simulation: true },
      });

      const stats = {
        totalAttempts: attempts.length,
        successfulAttempts: attempts.filter((a: any) => a.success).length,
        averageScore: this.calculateAverageScore(attempts),
        bySimulationType: this.groupBySimulationType(attempts),
        lastCompletedAt: this.getLastCompletionDate(attempts),
      };

      await this.cacheService.set(cacheKey, stats, this.CACHE_TTL);
      return stats;
    } catch (error) {
      this.logger.error(`Error retrieving user security stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  private calculateAverageScore(attempts: any[]): number {
    const completedAttempts = attempts.filter(a => a.score !== null);
    if (completedAttempts.length === 0) return 0;

    const totalScore = completedAttempts.reduce((sum, attempt) => sum + attempt.score, 0);
    return Math.round(totalScore / completedAttempts.length);
  }

  private groupBySimulationType(attempts: any[]): Record<string, any> {
    return attempts.reduce((acc, attempt) => {
      const type = attempt.simulation.type;
      if (!acc[type]) {
        acc[type] = {
          total: 0,
          successful: 0,
          averageScore: 0,
        };
      }

      acc[type].total++;
      if (attempt.success) acc[type].successful++;
      if (attempt.score) {
        acc[type].averageScore = Math.round(
          (acc[type].averageScore * (acc[type].total - 1) + attempt.score) / acc[type].total
        );
      }

      return acc;
    }, {});
  }

  private getLastCompletionDate(attempts: any[]): Date | null {
    const completedAttempts = attempts.filter(a => a.completedAt);
    if (completedAttempts.length === 0) return null;

    return new Date(Math.max(...completedAttempts.map(a => a.completedAt.getTime())));
  }
}