import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TokenizationService } from './tokenization.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { KeyManagementService } from './key-management.service';
import { TokenFormat, TokenizationType, TokenStatus } from '../interfaces/tokenization.interfaces';

describe('TokenizationService', () => {
  let service: TokenizationService;
  let prismaService: PrismaService;
  let keyManagementService: KeyManagementService;
  let configService: ConfigService;
  let eventEmitter: EventEmitter2;

  const mockPrismaService = {
    onModuleInit: jest.fn(),
    token: {
      create: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
      findMany: jest.fn(),
      updateMany: jest.fn(),
    },
  };

  const mockKeyManagementService = {
    getActiveKey: jest.fn(),
    getKeyById: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key, defaultValue) => {
      const config = {
        'security.tokenization.enabled': true,
        'security.tokenization.defaultExpirationTime': 86400,
        'security.tokenization.defaultTokenFormat': 'UUID',
        'security.tokenization.preserveFormatByDefault': false,
        'security.vault.enabled': false,
        'security.tokenization.cleanupInterval': 3600,
      };
      return config[key] !== undefined ? config[key] : defaultValue;
    }),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TokenizationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: KeyManagementService,
          useValue: mockKeyManagementService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    service = module.get<TokenizationService>(TokenizationService);
    prismaService = module.get<PrismaService>(PrismaService);
    keyManagementService = module.get<KeyManagementService>(KeyManagementService);
    configService = module.get<ConfigService>(ConfigService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);

    // Mock key management service responses
    mockKeyManagementService.getActiveKey.mockResolvedValue({
      key: Buffer.from('0123456789abcdef0123456789abcdef'),
      metadata: { id: 'key-1', version: 1 },
    });

    mockKeyManagementService.getKeyById.mockResolvedValue({
      key: Buffer.from('0123456789abcdef0123456789abcdef'),
    });

    // Initialize the service
    await service.onModuleInit();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('tokenize', () => {
    it('should tokenize data with default options', async () => {
      // Mock token creation
      mockPrismaService.token.create.mockResolvedValue({
        id: 'token-id-1',
        value: 'token-value-1',
        type: TokenizationType.CUSTOM,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400 * 1000),
        status: 'ACTIVE',
        metadata: {},
      });

      const result = await service.tokenize('sensitive-data');

      expect(result).toBeDefined();
      expect(result.token).toBeDefined();
      expect(result.tokenId).toBeDefined();
      expect(mockPrismaService.token.create).toHaveBeenCalled();
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'tokenization.event',
        expect.objectContaining({
          type: 'created',
        }),
      );
    });

    it('should tokenize data with custom options', async () => {
      // Mock token creation
      mockPrismaService.token.create.mockResolvedValue({
        id: 'token-id-2',
        value: 'token-value-2',
        type: TokenizationType.PAYMENT_CARD,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 3600 * 1000),
        status: 'ACTIVE',
        metadata: { userId: '123' },
      });

      const result = await service.tokenize('4111-1111-1111-1111', {
        type: TokenizationType.PAYMENT_CARD,
        expiresIn: 3600,
        format: TokenFormat.PRESERVING,
        preserveFormat: true,
        metadata: { userId: '123' },
      });

      expect(result).toBeDefined();
      expect(result.token).toBeDefined();
      expect(result.tokenId).toBeDefined();
      expect(mockPrismaService.token.create).toHaveBeenCalled();
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'tokenization.event',
        expect.objectContaining({
          type: 'created',
        }),
      );
    });

    it('should throw an error if tokenization is disabled', async () => {
      // Mock tokenization disabled
      jest.spyOn(service, 'isEnabled').mockReturnValue(false);

      await expect(service.tokenize('sensitive-data')).rejects.toThrow(
        'Tokenization service is not enabled or initialized',
      );
    });
  });

  describe('detokenize', () => {
    it('should detokenize a token from cache', async () => {
      // Setup cache with a token
      const token = {
        id: 'token-id-3',
        value: 'token-value-3',
        type: TokenizationType.CUSTOM,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400 * 1000),
        status: TokenStatus.ACTIVE,
        metadata: {},
      };

      // @ts-ignore - accessing private property for testing
      service.tokenCache.set('token-value-3', {
        originalData: 'sensitive-data',
        token,
      });

      const result = await service.detokenize('token-value-3');

      expect(result).toBe('sensitive-data');
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'tokenization.event',
        expect.objectContaining({
          type: 'used',
        }),
      );
    });

    it('should detokenize a token from database', async () => {
      // Mock token retrieval from database
      mockPrismaService.token.findFirst.mockResolvedValue({
        id: 'token-id-4',
        value: 'token-value-4',
        type: TokenizationType.CUSTOM,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400 * 1000),
        status: TokenStatus.ACTIVE,
        metadata: {},
      });

      mockPrismaService.token.findUnique.mockResolvedValue({
        originalData: JSON.stringify({
          data: 'encryptedData',
          iv: 'base64iv',
          authTag: 'base64authTag',
          keyId: 'key-1',
          algorithm: 'aes-256-gcm',
          version: 1,
        }),
      });

      const result = await service.detokenize('token-value-4');

      expect(result).toBeDefined();
      expect(mockPrismaService.token.findFirst).toHaveBeenCalled();
      expect(mockPrismaService.token.findUnique).toHaveBeenCalled();
      expect(mockKeyManagementService.getKeyById).toHaveBeenCalled();
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'tokenization.event',
        expect.objectContaining({
          type: 'used',
        }),
      );
    });

    it('should throw an error if token is not found', async () => {
      mockPrismaService.token.findFirst.mockResolvedValue(null);

      await expect(service.detokenize('non-existent-token')).rejects.toThrow('Token not found');
    });

    it('should throw an error if token is expired', async () => {
      mockPrismaService.token.findFirst.mockResolvedValue({
        id: 'token-id-5',
        value: 'token-value-5',
        type: TokenizationType.CUSTOM,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() - 86400 * 1000), // Expired
        status: TokenStatus.ACTIVE,
        metadata: {},
      });

      await expect(service.detokenize('token-value-5')).rejects.toThrow('Token is expired');
      expect(mockPrismaService.token.update).toHaveBeenCalledWith({
        where: { id: 'token-id-5' },
        data: { status: TokenStatus.EXPIRED },
      });
    });
  });

  describe('revokeToken', () => {
    it('should revoke a token', async () => {
      mockPrismaService.token.findFirst.mockResolvedValue({
        id: 'token-id-6',
        value: 'token-value-6',
        type: TokenizationType.CUSTOM,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 86400 * 1000),
        status: TokenStatus.ACTIVE,
        metadata: {},
      });

      await service.revokeToken('token-value-6');

      expect(mockPrismaService.token.update).toHaveBeenCalledWith({
        where: { id: 'token-id-6' },
        data: { status: TokenStatus.REVOKED },
      });
      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'tokenization.event',
        expect.objectContaining({
          type: 'revoked',
        }),
      );
    });

    it('should throw an error if token is not found', async () => {
      mockPrismaService.token.findFirst.mockResolvedValue(null);

      await expect(service.revokeToken('non-existent-token')).rejects.toThrow('Token not found');
    });
  });

  describe('getTokenizationStats', () => {
    it('should return tokenization statistics', async () => {
      mockPrismaService.token.count.mockImplementation((params) => {
        if (!params) return 100;
        if (params.where.status === TokenStatus.ACTIVE) return 80;
        if (params.where.status === TokenStatus.EXPIRED) return 15;
        if (params.where.status === TokenStatus.REVOKED) return 5;
        if (params.where.type === 'PAYMENT_CARD') return 50;
        if (params.where.type === 'BANK_ACCOUNT') return 20;
        if (params.where.type === 'PERSONAL_DATA') return 15;
        if (params.where.type === 'HEALTH_DATA') return 10;
        if (params.where.type === 'CREDENTIALS') return 3;
        if (params.where.type === 'CUSTOM') return 2;
        return 0;
      });

      const stats = await service.getTokenizationStats();

      expect(stats).toBeDefined();
      expect(stats.totalTokens).toBe(100);
      expect(stats.activeTokens).toBe(80);
      expect(stats.expiredTokens).toBe(15);
      expect(stats.revokedTokens).toBe(5);
      expect(stats.tokensByType).toBeDefined();
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should cleanup expired tokens', async () => {
      mockPrismaService.token.findMany.mockResolvedValue([
        {
          id: 'token-id-7',
          value: 'token-value-7',
          type: TokenizationType.CUSTOM,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() - 86400 * 1000), // Expired
          status: TokenStatus.ACTIVE,
          metadata: {},
        },
        {
          id: 'token-id-8',
          value: 'token-value-8',
          type: TokenizationType.CUSTOM,
          createdAt: new Date(),
          expiresAt: new Date(Date.now() - 86400 * 1000), // Expired
          status: TokenStatus.ACTIVE,
          metadata: {},
        },
      ]);

      await service.cleanupExpiredTokens();

      expect(mockPrismaService.token.updateMany).toHaveBeenCalledWith({
        where: {
          id: { in: ['token-id-7', 'token-id-8'] },
        },
        data: {
          status: TokenStatus.EXPIRED,
        },
      });
      expect(mockEventEmitter.emit).toHaveBeenCalledTimes(2);
    });
  });
});
