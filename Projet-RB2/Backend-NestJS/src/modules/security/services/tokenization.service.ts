import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../../../prisma/prisma.service';
import { KeyManagementService } from './key-management.service';
import * as crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import {
  Token,
  TokenFormat,
  TokenizationEvent,
  TokenizationOptions,
  TokenizationResult,
  TokenizationStats,
  TokenizationType,
  TokenStatus,
} from '../interfaces/tokenization.interfaces';

/**
 * Options de configuration du service de tokenisation
 */
interface TokenizationServiceConfig {
  enabled: boolean;
  defaultExpirationTime: number; // en secondes
  defaultTokenFormat: TokenFormat;
  preserveFormatByDefault: boolean;
  vaultEnabled: boolean;
  cleanupInterval: number; // en secondes
}

/**
 * Service de tokenisation des données sensibles
 * Ce service permet de remplacer des données sensibles par des tokens
 * pour réduire les risques liés au stockage de données sensibles
 */
@Injectable()
export class TokenizationService implements OnModuleInit {
  private readonly logger = new Logger(TokenizationService.name);
  private readonly config: TokenizationServiceConfig;
  private readonly tokenCache: Map<string, { originalData: string; token: Token }> = new Map();
  private initialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly keyManagementService: KeyManagementService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.config = {
      enabled: this.configService.get<boolean>('security.tokenization.enabled', true),
      defaultExpirationTime: this.configService.get<number>(
        'security.tokenization.defaultExpirationTime',
        86400 * 30, // 30 jours par défaut
      ),
      defaultTokenFormat: this.configService.get<TokenFormat>(
        'security.tokenization.defaultTokenFormat',
        TokenFormat.UUID,
      ),
      preserveFormatByDefault: this.configService.get<boolean>(
        'security.tokenization.preserveFormatByDefault',
        false,
      ),
      vaultEnabled: this.configService.get<boolean>('security.vault.enabled', false),
      cleanupInterval: this.configService.get<number>(
        'security.tokenization.cleanupInterval',
        3600, // 1 heure par défaut
      ),
    };
  }

  /**
   * Initialise le service de tokenisation
   */
  async onModuleInit() {
    if (!this.config.enabled) {
      this.logger.log('Tokenization service is disabled');
      return;
    }

    try {
      this.logger.log('Initializing tokenization service...');

      // Vérifier si la table des tokens existe
      await this.prisma.onModuleInit();

      // Charger les tokens actifs en cache pour des performances optimales
      await this.loadActiveTokensToCache();

      this.initialized = true;
      this.logger.log('Tokenization service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize tokenization service:', error);
      throw error;
    }
  }

  /**
   * Vérifie si le service de tokenisation est activé et initialisé
   */
  isEnabled(): boolean {
    return this.config.enabled && this.initialized;
  }

  /**
   * Tokenise une donnée sensible
   * @param data Donnée sensible à tokeniser
   * @param options Options de tokenisation
   * @returns Résultat de la tokenisation
   */
  async tokenize(data: string, options?: Partial<TokenizationOptions>): Promise<TokenizationResult> {
    if (!this.isEnabled()) {
      throw new Error('Tokenization service is not enabled or initialized');
    }

    if (!data) {
      throw new Error('Data to tokenize cannot be empty');
    }

    try {
      // Fusionner les options avec les valeurs par défaut
      const tokenOptions: TokenizationOptions = {
        type: options?.type || TokenizationType.CUSTOM,
        expiresIn: options?.expiresIn || this.config.defaultExpirationTime,
        detokenizable: options?.detokenizable !== undefined ? options.detokenizable : true,
        format: options?.format || this.config.defaultTokenFormat,
        preserveFormat: options?.preserveFormat !== undefined
          ? options.preserveFormat
          : this.config.preserveFormatByDefault,
        metadata: options?.metadata || {},
      };

      // Générer un identifiant unique pour le token
      const tokenId = uuidv4();

      // Générer la valeur du token selon le format demandé
      const tokenValue = await this.generateTokenValue(data, tokenOptions);

      // Calculer la date d'expiration
      const expiresAt = tokenOptions.expiresIn
        ? new Date(Date.now() + tokenOptions.expiresIn * 1000)
        : undefined;

      // Créer l'objet token
      const token: Token = {
        id: tokenId,
        value: tokenValue,
        type: tokenOptions.type,
        createdAt: new Date(),
        expiresAt,
        status: TokenStatus.ACTIVE,
        metadata: tokenOptions.metadata,
      };

      // Si le token est détokenisable, chiffrer et stocker la donnée originale
      if (tokenOptions.detokenizable) {
        // Chiffrer la donnée originale
        const encryptedData = await this.encryptOriginalData(data, tokenOptions.type);

        // Stocker le token et la donnée chiffrée
        await this.storeToken(token, encryptedData);

        // Ajouter au cache pour des performances optimales
        this.tokenCache.set(tokenValue, { originalData: data, token });
      } else {
        // Stocker uniquement le token sans la donnée originale
        await this.storeToken(token);
      }

      // Émettre un événement de création de token
      this.emitTokenizationEvent({
        tokenId,
        type: 'created',
        timestamp: new Date(),
        metadata: {
          tokenType: tokenOptions.type,
          expiresAt: token.expiresAt,
        },
      });

      // Retourner le résultat
      return {
        token: tokenValue,
        tokenId,
        expiresAt: token.expiresAt,
        metadata: token.metadata,
      };
    } catch (error) {
      this.logger.error('Failed to tokenize data:', error);
      throw new Error(`Tokenization failed: ${error.message}`);
    }
  }

  /**
   * Détokenise une donnée (récupère la donnée originale à partir du token)
   * @param tokenValue Valeur du token
   * @returns Donnée originale
   */
  async detokenize(tokenValue: string): Promise<string> {
    if (!this.isEnabled()) {
      throw new Error('Tokenization service is not enabled or initialized');
    }

    if (!tokenValue) {
      throw new Error('Token value cannot be empty');
    }

    try {
      // Vérifier si le token est dans le cache
      const cachedData = this.tokenCache.get(tokenValue);
      if (cachedData) {
        // Vérifier si le token est expiré ou révoqué
        if (
          cachedData.token.status !== TokenStatus.ACTIVE ||
          (cachedData.token.expiresAt && cachedData.token.expiresAt < new Date())
        ) {
          throw new Error('Token is expired or revoked');
        }

        // Émettre un événement d'utilisation de token
        this.emitTokenizationEvent({
          tokenId: cachedData.token.id,
          type: 'used',
          timestamp: new Date(),
        });

        return cachedData.originalData;
      }

      // Si le token n'est pas dans le cache, le récupérer depuis la base de données
      const token = await this.getTokenByValue(tokenValue);

      if (!token) {
        throw new Error('Token not found');
      }

      // Vérifier si le token est expiré ou révoqué
      if (token.status !== TokenStatus.ACTIVE) {
        throw new Error('Token is revoked');
      }

      if (token.expiresAt && token.expiresAt < new Date()) {
        // Marquer le token comme expiré
        await this.updateTokenStatus(token.id, TokenStatus.EXPIRED);

        // Émettre un événement d'expiration de token
        this.emitTokenizationEvent({
          tokenId: token.id,
          type: 'expired',
          timestamp: new Date(),
        });

        throw new Error('Token is expired');
      }

      // Récupérer la donnée chiffrée
      const encryptedData = await this.getEncryptedData(token.id);

      if (!encryptedData) {
        throw new Error('Original data not found for this token');
      }

      // Déchiffrer la donnée originale
      const originalData = await this.decryptOriginalData(encryptedData, token.type);

      // Ajouter au cache pour des performances optimales
      this.tokenCache.set(tokenValue, { originalData, token });

      // Émettre un événement d'utilisation de token
      this.emitTokenizationEvent({
        tokenId: token.id,
        type: 'used',
        timestamp: new Date(),
      });

      return originalData;
    } catch (error) {
      this.logger.error('Failed to detokenize data:', error);
      throw new Error(`Detokenization failed: ${error.message}`);
    }
  }

  /**
   * Révoque un token (le rend inutilisable)
   * @param tokenValue Valeur du token à révoquer
   */
  async revokeToken(tokenValue: string): Promise<void> {
    if (!this.isEnabled()) {
      throw new Error('Tokenization service is not enabled or initialized');
    }

    try {
      // Récupérer le token
      const token = await this.getTokenByValue(tokenValue);

      if (!token) {
        throw new Error('Token not found');
      }

      // Mettre à jour le statut du token
      await this.updateTokenStatus(token.id, TokenStatus.REVOKED);

      // Supprimer du cache
      this.tokenCache.delete(tokenValue);

      // Émettre un événement de révocation de token
      this.emitTokenizationEvent({
        tokenId: token.id,
        type: 'revoked',
        timestamp: new Date(),
      });

      this.logger.debug(`Token ${token.id} revoked successfully`);
    } catch (error) {
      this.logger.error('Failed to revoke token:', error);
      throw new Error(`Token revocation failed: ${error.message}`);
    }
  }

  /**
   * Récupère les statistiques de tokenisation
   * @returns Statistiques de tokenisation
   */
  async getTokenizationStats(): Promise<TokenizationStats> {
    if (!this.isEnabled()) {
      throw new Error('Tokenization service is not enabled or initialized');
    }

    try {
      // Récupérer les statistiques depuis la base de données
      const totalTokens = await this.prisma.token.count();
      const activeTokens = await this.prisma.token.count({
        where: {
          status: TokenStatus.ACTIVE,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
      });
      const expiredTokens = await this.prisma.token.count({
        where: {
          OR: [
            { status: TokenStatus.EXPIRED },
            {
              status: TokenStatus.ACTIVE,
              expiresAt: { lt: new Date() },
            },
          ],
        },
      });
      const revokedTokens = await this.prisma.token.count({
        where: { status: TokenStatus.REVOKED },
      });

      // Récupérer le nombre de tokens par type
      const tokensByType: Record<TokenizationType, number> = {} as Record<TokenizationType, number>;

      for (const type of Object.values(TokenizationType)) {
        tokensByType[type] = await this.prisma.token.count({
          where: { type },
        });
      }

      return {
        totalTokens,
        activeTokens,
        expiredTokens,
        revokedTokens,
        tokensByType,
      };
    } catch (error) {
      this.logger.error('Failed to get tokenization stats:', error);
      throw new Error(`Failed to get tokenization stats: ${error.message}`);
    }
  }

  /**
   * Nettoie les tokens expirés
   * Cette méthode est exécutée périodiquement
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupExpiredTokens(): Promise<void> {
    if (!this.isEnabled()) {
      return;
    }

    try {
      this.logger.log('Cleaning up expired tokens...');

      // Récupérer les tokens expirés
      const expiredTokens = await this.prisma.token.findMany({
        where: {
          status: TokenStatus.ACTIVE,
          expiresAt: { lt: new Date() },
        },
      });

      // Mettre à jour le statut des tokens expirés
      if (expiredTokens.length > 0) {
        await this.prisma.token.updateMany({
          where: {
            id: { in: expiredTokens.map((token: any) => token.id) },
          },
          data: {
            status: TokenStatus.EXPIRED,
          },
        });

        // Supprimer du cache
        for (const token of expiredTokens) {
          for (const [key, value] of this.tokenCache.entries()) {
            if (value.token.id === token.id) {
              this.tokenCache.delete(key);
              break;
            }
          }

          // Émettre un événement d'expiration de token
          this.emitTokenizationEvent({
            tokenId: token.id,
            type: 'expired',
            timestamp: new Date(),
          });
        }
      }

      this.logger.log(`Cleaned up ${expiredTokens.length} expired tokens`);
    } catch (error) {
      this.logger.error('Failed to clean up expired tokens:', error);
    }
  }

  /**
   * Charge les tokens actifs en cache
   * Cette méthode est appelée lors de l'initialisation du service
   */
  private async loadActiveTokensToCache(): Promise<void> {
    try {
      // Récupérer les tokens actifs
      const activeTokens = await this.prisma.token.findMany({
        where: {
          status: TokenStatus.ACTIVE,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        take: 1000, // Limiter le nombre de tokens chargés en cache
      });

      // Charger les tokens en cache
      if (activeTokens && Array.isArray(activeTokens)) {
        for (const token of activeTokens) {
          // Récupérer la donnée chiffrée
          const encryptedData = await this.getEncryptedData(token.id);

          if (encryptedData) {
            // Déchiffrer la donnée originale
            const originalData = await this.decryptOriginalData(encryptedData, token.type as TokenizationType);

            // Ajouter au cache
            this.tokenCache.set(token.value, {
              originalData,
              token: {
                id: token.id,
                value: token.value,
                type: token.type as TokenizationType,
                createdAt: token.createdAt,
                expiresAt: token.expiresAt,
                status: token.status as TokenStatus,
                metadata: token.metadata as Record<string, any>,
              },
            });
          }
        }
      }

      this.logger.log(`Loaded ${this.tokenCache.size} active tokens into cache`);
    } catch (error) {
      this.logger.error('Failed to load active tokens to cache:', error);
      throw error;
    }
  }

  /**
   * Génère une valeur de token selon le format demandé
   * @param originalData Donnée originale
   * @param options Options de tokenisation
   * @returns Valeur du token
   */
  private async generateTokenValue(originalData: string, options: TokenizationOptions): Promise<string> {
    switch (options.format) {
      case TokenFormat.UUID:
        return uuidv4();

      case TokenFormat.NUMERIC:
        return this.generateNumericToken(16);

      case TokenFormat.ALPHANUMERIC:
        return this.generateAlphanumericToken(24);

      case TokenFormat.PRESERVING:
        return this.generateFormatPreservingToken(originalData, options.type);

      default:
        return uuidv4();
    }
  }

  /**
   * Génère un token numérique
   * @param length Longueur du token
   * @returns Token numérique
   */
  private generateNumericToken(length: number): string {
    let result = '';
    const characters = '0123456789';

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    return result;
  }

  /**
   * Génère un token alphanumérique
   * @param length Longueur du token
   * @returns Token alphanumérique
   */
  private generateAlphanumericToken(length: number): string {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    return result;
  }

  /**
   * Génère un token qui préserve le format de la donnée originale
   * @param originalData Donnée originale
   * @param type Type de donnée
   * @returns Token préservant le format
   */
  private generateFormatPreservingToken(originalData: string, type: TokenizationType): string {
    // Déterminer le format selon le type de donnée
    switch (type) {
      case TokenizationType.PAYMENT_CARD:
        return this.generateFormatPreservingCardToken(originalData);

      case TokenizationType.BANK_ACCOUNT:
        return this.generateFormatPreservingAccountToken(originalData);

      default:
        // Pour les autres types, préserver uniquement la longueur et les caractères spéciaux
        return this.generateGenericFormatPreservingToken(originalData);
    }
  }

  /**
   * Génère un token préservant le format d'une carte de paiement
   * @param cardNumber Numéro de carte
   * @returns Token préservant le format de la carte
   */
  private generateFormatPreservingCardToken(cardNumber: string): string {
    // Conserver les 4 derniers chiffres et remplacer le reste
    const lastFourDigits = cardNumber.slice(-4);
    const prefix = cardNumber.slice(0, -4).replace(/\d/g, 'X');

    return prefix + lastFourDigits;
  }

  /**
   * Génère un token préservant le format d'un compte bancaire
   * @param accountNumber Numéro de compte
   * @returns Token préservant le format du compte
   */
  private generateFormatPreservingAccountToken(accountNumber: string): string {
    // Conserver les 2 derniers chiffres et remplacer le reste
    const lastTwoDigits = accountNumber.slice(-2);
    const prefix = accountNumber.slice(0, -2).replace(/\d/g, 'X');

    return prefix + lastTwoDigits;
  }

  /**
   * Génère un token préservant le format générique d'une donnée
   * @param data Donnée originale
   * @returns Token préservant le format
   */
  private generateGenericFormatPreservingToken(data: string): string {
    let result = '';

    for (let i = 0; i < data.length; i++) {
      const char = data[i];

      if (/[a-zA-Z]/.test(char)) {
        // Remplacer les lettres par des lettres aléatoires du même type
        result += char === char.toUpperCase()
          ? String.fromCharCode(65 + Math.floor(Math.random() * 26))
          : String.fromCharCode(97 + Math.floor(Math.random() * 26));
      } else if (/\d/.test(char)) {
        // Remplacer les chiffres par des chiffres aléatoires
        result += Math.floor(Math.random() * 10).toString();
      } else {
        // Conserver les caractères spéciaux
        result += char;
      }
    }

    return result;
  }

  /**
   * Chiffre la donnée originale
   * @param data Donnée originale
   * @param type Type de donnée
   * @returns Donnée chiffrée
   */
  private async encryptOriginalData(data: string, _type: TokenizationType): Promise<string> {
    try {
      // Récupérer une clé active pour le chiffrement
      const { key, metadata } = await this.keyManagementService.getActiveKey('encryption', 'tokenization');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Chiffrer la donnée
      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
      let encryptedData = cipher.update(data, 'utf8', 'base64');
      encryptedData += cipher.final('base64');

      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag();

      // Créer l'objet de données chiffrées
      const encryptedObject = {
        data: encryptedData,
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        keyId: metadata.id,
        algorithm: 'aes-256-gcm',
        version: metadata.version,
      };

      // Sérialiser l'objet
      return JSON.stringify(encryptedObject);
    } catch (error) {
      this.logger.error('Failed to encrypt original data:', error);
      throw error;
    }
  }

  /**
   * Déchiffre la donnée originale
   * @param encryptedData Donnée chiffrée
   * @param type Type de donnée
   * @returns Donnée originale
   */
  private async decryptOriginalData(encryptedData: string, _type: TokenizationType): Promise<string> {
    try {
      // Désérialiser l'objet
      const encryptedObject = JSON.parse(encryptedData);

      // Récupérer la clé de déchiffrement
      const { key } = await this.keyManagementService.getKeyById(encryptedObject.keyId);

      // Déchiffrer la donnée
      const iv = Buffer.from(encryptedObject.iv, 'base64');
      const authTag = Buffer.from(encryptedObject.authTag, 'base64');

      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      let decryptedData = decipher.update(encryptedObject.data, 'base64', 'utf8');
      decryptedData += decipher.final('utf8');

      return decryptedData;
    } catch (error) {
      this.logger.error('Failed to decrypt original data:', error);
      throw error;
    }
  }

  /**
   * Stocke un token dans la base de données
   * @param token Token à stocker
   * @param encryptedData Donnée chiffrée (optionnel)
   */
  private async storeToken(token: Token, encryptedData?: string): Promise<void> {
    try {
      // Créer le token dans la base de données
      await this.prisma.token.create({
        data: {
          id: token.id,
          value: token.value,
          type: token.type,
          createdAt: token.createdAt,
          expiresAt: token.expiresAt,
          status: token.status,
          metadata: token.metadata,
          originalData: encryptedData,
        },
      });
    } catch (error) {
      this.logger.error('Failed to store token:', error);
      throw error;
    }
  }

  /**
   * Récupère un token par sa valeur
   * @param tokenValue Valeur du token
   * @returns Token
   */
  private async getTokenByValue(tokenValue: string): Promise<any> {
    return this.prisma.token.findFirst({
      where: { value: tokenValue },
    });
  }

  /**
   * Récupère la donnée chiffrée associée à un token
   * @param tokenId Identifiant du token
   * @returns Donnée chiffrée
   */
  private async getEncryptedData(tokenId: string): Promise<string | null> {
    const token = await this.prisma.token.findUnique({
      where: { id: tokenId },
      select: { originalData: true },
    });

    return token?.originalData || null;
  }

  /**
   * Met à jour le statut d'un token
   * @param tokenId Identifiant du token
   * @param status Nouveau statut
   */
  private async updateTokenStatus(tokenId: string, status: TokenStatus): Promise<void> {
    await this.prisma.token.update({
      where: { id: tokenId },
      data: { status },
    });
  }

  /**
   * Émet un événement de tokenisation
   * @param event Événement de tokenisation
   */
  private emitTokenizationEvent(event: TokenizationEvent): void {
    this.eventEmitter.emit('tokenization.event', event);
  }
}
