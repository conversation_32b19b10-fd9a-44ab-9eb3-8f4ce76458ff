import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HomomorphicEncryptionService } from '../services/homomorphic-encryption.service';

describe('HomomorphicEncryptionService', () => {
  let service: HomomorphicEncryptionService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HomomorphicEncryptionService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue: any) => {
              const config = {
                'HOMOMORPHIC_ENCRYPTION_ENABLED': true,
                'HOMOMORPHIC_ENCRYPTION_SCHEME': 'CKKS',
                'HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE': 8192,
                'HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL': 128,
              };
              return config[key] !== undefined ? config[key] : defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<HomomorphicEncryptionService>(HomomorphicEncryptionService);
    configService = module.get<ConfigService>(ConfigService);
    
    // Initialiser le service avant les tests
    await service.initialize();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should encrypt and decrypt data correctly', async () => {
    const testValue = 42;
    const encryptedData = await service.encrypt(testValue);
    
    expect(encryptedData).toBeDefined();
    expect(encryptedData instanceof Buffer).toBeTruthy();
    
    const decryptedValue = await service.decrypt(encryptedData);
    expect(Math.abs(decryptedValue - testValue)).toBeLessThan(0.001);
  });

  it('should encrypt and decrypt batch data correctly', async () => {
    const testValues = [1, 2, 3, 4, 5];
    const encryptedData = await service.encryptBatch(testValues);
    
    expect(encryptedData).toBeDefined();
    expect(encryptedData instanceof Buffer).toBeTruthy();
    
    const decryptedValues = await service.decryptBatch(encryptedData);
    expect(decryptedValues.length).toEqual(testValues.length);
    
    for (let i = 0; i < testValues.length; i++) {
      expect(Math.abs(decryptedValues[i] - testValues[i])).toBeLessThan(0.001);
    }
  });

  it('should perform homomorphic addition correctly', async () => {
    const a = 10;
    const b = 5;
    const expected = a + b;
    
    const encryptedA = await service.encrypt(a);
    const encryptedB = await service.encrypt(b);
    
    const encryptedResult = await service.add(encryptedA, encryptedB);
    const result = await service.decrypt(encryptedResult);
    
    expect(Math.abs(result - expected)).toBeLessThan(0.001);
  });

  it('should perform homomorphic subtraction correctly', async () => {
    const a = 10;
    const b = 5;
    const expected = a - b;
    
    const encryptedA = await service.encrypt(a);
    const encryptedB = await service.encrypt(b);
    
    const encryptedResult = await service.subtract(encryptedA, encryptedB);
    const result = await service.decrypt(encryptedResult);
    
    expect(Math.abs(result - expected)).toBeLessThan(0.001);
  });

  it('should perform homomorphic multiplication correctly', async () => {
    const a = 10;
    const b = 5;
    const expected = a * b;
    
    const encryptedA = await service.encrypt(a);
    const encryptedB = await service.encrypt(b);
    
    const encryptedResult = await service.multiply(encryptedA, encryptedB);
    const result = await service.decrypt(encryptedResult);
    
    expect(Math.abs(result - expected)).toBeLessThan(0.001);
  });

  it('should perform homomorphic division correctly', async () => {
    const a = 10;
    const b = 5;
    const expected = a / b;
    
    const encryptedA = await service.encrypt(a);
    
    const encryptedResult = await service.divide(encryptedA, b);
    const result = await service.decrypt(encryptedResult);
    
    expect(Math.abs(result - expected)).toBeLessThan(0.001);
  });

  it('should calculate average homomorphically', async () => {
    const values = [10, 20, 30, 40, 50];
    const expected = values.reduce((a, b) => a + b, 0) / values.length;
    
    const encryptedValues = await Promise.all(values.map(v => service.encrypt(v)));
    
    const encryptedResult = await service.average(encryptedValues);
    const result = await service.decrypt(encryptedResult);
    
    expect(Math.abs(result - expected)).toBeLessThan(0.001);
  });

  it('should calculate variance homomorphically', async () => {
    const values = [10, 20, 30, 40, 50];
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const expected = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    
    const encryptedValues = await Promise.all(values.map(v => service.encrypt(v)));
    
    const encryptedResult = await service.variance(encryptedValues);
    const result = await service.decrypt(encryptedResult);
    
    // La variance homomorphique peut être moins précise en raison des approximations
    expect(Math.abs(result - expected)).toBeLessThan(0.1);
  });

  it('should rotate keys correctly', async () => {
    const testValue = 42;
    
    // Générer de nouvelles clés
    const newKeys = await service.rotateKeys();
    expect(newKeys).toBeDefined();
    expect(newKeys.publicKey).toBeDefined();
    expect(newKeys.secretKey).toBeDefined();
    expect(newKeys.relinKeys).toBeDefined();
    
    // Vérifier que les nouvelles clés fonctionnent
    const encryptedData = await service.encrypt(testValue);
    const decryptedValue = await service.decrypt(encryptedData);
    expect(Math.abs(decryptedValue - testValue)).toBeLessThan(0.001);
  });

  it('should throw an error when homomorphic encryption is disabled', async () => {
    // Simuler la désactivation du chiffrement
    jest.spyOn(service, 'isEnabled').mockReturnValue(false);
    
    const testValue = 42;
    
    await expect(service.encrypt(testValue)).rejects.toThrow('Homomorphic encryption is not enabled or initialized');
    await expect(service.decrypt(Buffer.from('dummy'))).rejects.toThrow('Homomorphic encryption is not enabled or initialized');
  });
});
