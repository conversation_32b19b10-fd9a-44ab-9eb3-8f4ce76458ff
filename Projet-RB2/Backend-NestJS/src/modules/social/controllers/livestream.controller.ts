import { Controller, Get, Post, Patch, Delete, Body, Param, Query, UseGuards, HttpException, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { User } from '../../users/decorators/user.decorator';
import { LivestreamService } from '../services/livestream.service';

@ApiTags('social-livestream')
@Controller('social/livestream')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class LivestreamController {
  constructor(
    private readonly livestreamService: LivestreamService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all livestreams' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status (scheduled, live, ended, cancelled)' })
  @ApiQuery({ name: 'hostId', required: false, description: 'Filter by host ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit the number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'Returns all livestreams' })
  async getLivestreams(
    @Query() filters: Record<string, any>,
  ) {
    const livestreams = await this.livestreamService.getLivestreams(filters);
    return {
      success: true,
      data: livestreams,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a livestream by ID' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 200, description: 'Returns the livestream' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async getLivestreamById(
    @Param('id') id: string,
  ) {
    const livestream = await this.livestreamService.getLivestreamById(id);
    return {
      success: true,
      data: livestream,
    };
  }

  @Post()
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Create a new livestream' })
  @ApiResponse({ status: 201, description: 'Livestream created successfully' })
  async createLivestream(
    @Body() livestreamData: {
      title: string;
      description: string;
      scheduledStartTime?: string;
      isPrivate?: boolean;
      participants?: string[];
      metadata?: Record<string, any>;
    },
    @User() user: any,
  ) {
    const createdLivestream = await this.livestreamService.createLivestream({
      ...livestreamData,
      hostId: user.id,
    });
    
    return {
      success: true,
      data: createdLivestream,
      message: 'Livestream created successfully',
    };
  }

  @Patch(':id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Update a livestream' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 200, description: 'Livestream updated successfully' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async updateLivestream(
    @Param('id') id: string,
    @Body() updateData: Record<string, any>,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est l'hôte du livestream
    const livestream = await this.livestreamService.getLivestreamById(id);
    if (livestream.hostId !== user.id && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to update this livestream', HttpStatus.FORBIDDEN);
    }

    const updatedLivestream = await this.livestreamService.updateLivestream(id, updateData);
    
    return {
      success: true,
      data: updatedLivestream,
      message: 'Livestream updated successfully',
    };
  }

  @Post(':id/start')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Start a livestream' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 200, description: 'Livestream started successfully' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async startLivestream(
    @Param('id') id: string,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est l'hôte du livestream
    const livestream = await this.livestreamService.getLivestreamById(id);
    if (livestream.hostId !== user.id && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to start this livestream', HttpStatus.FORBIDDEN);
    }

    const result = await this.livestreamService.startLivestream(id);
    
    return {
      success: true,
      data: result,
      message: 'Livestream started successfully',
    };
  }

  @Post(':id/end')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'End a livestream' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 200, description: 'Livestream ended successfully' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async endLivestream(
    @Param('id') id: string,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur est l'hôte du livestream
    const livestream = await this.livestreamService.getLivestreamById(id);
    if (livestream.hostId !== user.id && user.role !== 'ADMIN') {
      throw new HttpException('You are not authorized to end this livestream', HttpStatus.FORBIDDEN);
    }

    const result = await this.livestreamService.endLivestream(id);
    
    return {
      success: true,
      data: result,
      message: 'Livestream ended successfully',
    };
  }

  @Get(':id/messages')
  @ApiOperation({ summary: 'Get messages for a livestream' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 200, description: 'Returns the livestream messages' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async getLivestreamMessages(
    @Param('id') id: string,
  ) {
    const messages = await this.livestreamService.getLivestreamMessages(id);
    
    return {
      success: true,
      data: messages,
    };
  }

  @Post(':id/messages')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Send a message to a livestream' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async sendLivestreamMessage(
    @Param('id') id: string,
    @Body() messageData: {
      content: string;
      type?: 'text' | 'question' | 'reaction';
    },
    @User() user: any,
  ) {
    const message = await this.livestreamService.sendLivestreamMessage(id, {
      userId: user.id,
      userName: `${user.firstName} ${user.lastName}`,
      content: messageData.content,
      type: messageData.type || 'text',
    });
    
    return {
      success: true,
      data: message,
      message: 'Message sent successfully',
    };
  }
}
