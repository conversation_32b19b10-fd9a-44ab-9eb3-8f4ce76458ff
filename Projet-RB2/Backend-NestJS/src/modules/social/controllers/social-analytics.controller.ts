import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { User } from '../../users/decorators/user.decorator';
import { SocialAnalyticsService } from '../services/social-analytics.service';

@ApiTags('social-analytics')
@Controller('social/analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class SocialAnalyticsController {
  constructor(
    private readonly analyticsService: SocialAnalyticsService,
  ) {}

  @Get()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get social analytics' })
  @ApiQuery({ name: 'period', required: false, description: 'Period (day, week, month, year)' })
  @ApiResponse({ status: 200, description: 'Returns social analytics' })
  async getSocialAnalytics(
    @Query('period') period: string = 'month',
  ) {
    const analytics = await this.analyticsService.getSocialAnalytics(period);
    return {
      success: true,
      data: analytics,
    };
  }

  @Get('livestreams/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Get analytics for a specific livestream' })
  @ApiParam({ name: 'id', description: 'Livestream ID' })
  @ApiResponse({ status: 200, description: 'Returns livestream analytics' })
  @ApiResponse({ status: 404, description: 'Livestream not found' })
  async getLivestreamAnalytics(
    @Param('id') id: string,
    @User() user: any,
  ) {
    const analytics = await this.analyticsService.getLivestreamAnalytics(id);
    return {
      success: true,
      data: analytics,
    };
  }

  @Get('blog/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Get analytics for a specific blog post' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiResponse({ status: 200, description: 'Returns blog post analytics' })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async getBlogPostAnalytics(
    @Param('id') id: string,
    @User() user: any,
  ) {
    const analytics = await this.analyticsService.getBlogPostAnalytics(id);
    return {
      success: true,
      data: analytics,
    };
  }

  @Get('users/:id')
  @Roles('USER', 'PARTNER', 'ADMIN')
  @ApiOperation({ summary: 'Get analytics for a specific user' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'Returns user analytics' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserAnalytics(
    @Param('id') id: string,
    @User() user: any,
  ) {
    // Vérifier que l'utilisateur demande ses propres analytics ou est un admin
    if (id !== user.id && user.role !== 'ADMIN') {
      return {
        success: false,
        message: 'You are not authorized to view analytics for this user',
      };
    }

    const analytics = await this.analyticsService.getUserAnalytics(id);
    return {
      success: true,
      data: analytics,
    };
  }

  @Get('popular')
  @ApiOperation({ summary: 'Get popular content' })
  @ApiQuery({ name: 'type', required: false, description: 'Content type (livestream, blog, video)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit the number of results' })
  @ApiQuery({ name: 'period', required: false, description: 'Period (day, week, month, year)' })
  @ApiResponse({ status: 200, description: 'Returns popular content' })
  async getPopularContent(
    @Query('type') type?: string,
    @Query('limit') limit: number = 10,
    @Query('period') period: string = 'month',
  ) {
    const popularContent = await this.analyticsService.getPopularContent(type, limit, period);
    return {
      success: true,
      data: popularContent,
    };
  }

  @Post('events')
  @ApiOperation({ summary: 'Track an analytics event' })
  @ApiResponse({ status: 201, description: 'Event tracked successfully' })
  async trackEvent(
    @Body() eventData: {
      eventType: string;
      entityId: string;
      entityType: 'livestream' | 'blog' | 'video' | 'user';
      metadata?: Record<string, any>;
    },
    @User() user: any,
  ) {
    const result = await this.analyticsService.trackEvent({
      ...eventData,
      userId: user.id,
    });
    
    return {
      success: true,
      data: result,
      message: 'Event tracked successfully',
    };
  }

  @Get('engagement')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Get engagement trends' })
  @ApiQuery({ name: 'period', required: false, description: 'Period (day, week, month, year)' })
  @ApiResponse({ status: 200, description: 'Returns engagement trends' })
  async getEngagementTrends(
    @Query('period') period: string = 'month',
  ) {
    const trends = await this.analyticsService.getEngagementTrends(period);
    return {
      success: true,
      data: trends,
    };
  }
}
