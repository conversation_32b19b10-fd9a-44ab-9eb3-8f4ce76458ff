import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsDateString, IsEnum, IsArray, IsUUID } from 'class-validator';
import { BlogPostStatus } from '../../../prisma/prisma-types';

export class CreateBlogPostDto {
  @ApiProperty({ description: 'Title of the blog post' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Content of the blog post' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiPropertyOptional({ description: 'Tags for the blog post' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Image URL for the blog post' })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Publish date of the blog post' })
  @IsDateString()
  @IsOptional()
  publishDate?: string;

  @ApiPropertyOptional({ description: 'Status of the blog post', enum: BlogPostStatus, default: BlogPostStatus.DRAFT })
  @IsEnum(BlogPostStatus)
  @IsOptional()
  status?: BlogPostStatus;

  @ApiPropertyOptional({ description: 'Additional metadata for the blog post', type: 'object' })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateBlogPostDto {
  @ApiPropertyOptional({ description: 'Title of the blog post' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ description: 'Content of the blog post' })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional({ description: 'Tags for the blog post' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Image URL for the blog post' })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Publish date of the blog post' })
  @IsDateString()
  @IsOptional()
  publishDate?: string;

  @ApiPropertyOptional({ description: 'Status of the blog post', enum: BlogPostStatus })
  @IsEnum(BlogPostStatus)
  @IsOptional()
  status?: BlogPostStatus;

  @ApiPropertyOptional({ description: 'Additional metadata for the blog post', type: 'object' })
  @IsOptional()
  metadata?: Record<string, any>;
}

export class BlogCommentDto {
  @ApiProperty({ description: 'Content of the comment' })
  @IsString()
  @IsNotEmpty()
  content: string;
}

export class BlogFilterDto {
  @ApiPropertyOptional({ description: 'Status of the blog post', enum: BlogPostStatus })
  @IsEnum(BlogPostStatus)
  @IsOptional()
  status?: BlogPostStatus;

  @ApiPropertyOptional({ description: 'Author ID of the blog post' })
  @IsUUID()
  @IsOptional()
  authorId?: string;

  @ApiPropertyOptional({ description: 'Tags to filter by (comma-separated)' })
  @IsString()
  @IsOptional()
  tags?: string;

  @ApiPropertyOptional({ description: 'Limit the number of results' })
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({ description: 'Skip a number of results (for pagination)' })
  @IsOptional()
  skip?: number;
}
