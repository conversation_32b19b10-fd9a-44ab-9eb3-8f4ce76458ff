import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class LivestreamService {
  private readonly logger = new Logger(LivestreamService.name);
  private readonly socialPlatformVideoUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.socialPlatformVideoUrl = this.configService.get<string>('microservices.socialPlatformVideo.url', 'http://social-platform-video:3002');
    this.apiKey = this.configService.get<string>('microservices.socialPlatformVideo.apiKey', 'default-api-key');
  }

  /**
   * Récupère tous les livestreams avec filtres optionnels
   * @param filters Filtres optionnels (status, hostId, etc.)
   * @returns Liste des livestreams
   */
  async getLivestreams(filters?: Record<string, any>): Promise<any[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/livestreams`, {
          params: filters,
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching livestreams: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching livestreams',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getLivestreams: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching livestreams', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère un livestream par son ID
   * @param id ID du livestream
   * @returns Détails du livestream
   */
  async getLivestreamById(id: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/livestreams/${id}`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching livestream: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching livestream',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getLivestreamById: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching livestream', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Crée un nouveau livestream
   * @param livestreamData Données du livestream
   * @returns Livestream créé
   */
  async createLivestream(livestreamData: {
    title: string;
    description: string;
    hostId: string;
    scheduledStartTime?: string;
    isPrivate?: boolean;
    participants?: string[];
    metadata?: Record<string, any>;
  }): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/livestreams`, livestreamData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error creating livestream: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error creating livestream',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour la création du livestream
      this.eventEmitter.emit('livestream.created', {
        livestreamId: data.id,
        hostId: livestreamData.hostId,
        title: livestreamData.title,
        description: livestreamData.description,
        isPrivate: livestreamData.isPrivate || false,
        participants: livestreamData.participants || [],
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in createLivestream: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error creating livestream', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Met à jour un livestream existant
   * @param id ID du livestream
   * @param updateData Données à mettre à jour
   * @returns Livestream mis à jour
   */
  async updateLivestream(id: string, updateData: Record<string, any>): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.patch(`${this.socialPlatformVideoUrl}/api/livestreams/${id}`, updateData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error updating livestream: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error updating livestream',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in updateLivestream: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error updating livestream', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Démarre un livestream
   * @param id ID du livestream
   * @returns Résultat de l'opération
   */
  async startLivestream(id: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/livestreams/${id}/start`, {}, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error starting livestream: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error starting livestream',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour le démarrage du livestream
      this.eventEmitter.emit('livestream.started', {
        livestreamId: id,
        hostId: data.hostId,
        title: data.title,
        streamUrl: data.streamUrl,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in startLivestream: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error starting livestream', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Termine un livestream
   * @param id ID du livestream
   * @returns Résultat de l'opération
   */
  async endLivestream(id: string): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/livestreams/${id}/end`, {}, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error ending livestream: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error ending livestream',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour la fin du livestream
      this.eventEmitter.emit('livestream.ended', {
        livestreamId: id,
        hostId: data.hostId,
        title: data.title,
        recordingUrl: data.recordingUrl,
        viewerCount: data.viewerCount,
        duration: data.duration,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in endLivestream: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error ending livestream', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Récupère les messages d'un livestream
   * @param id ID du livestream
   * @returns Liste des messages
   */
  async getLivestreamMessages(id: string): Promise<any[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`${this.socialPlatformVideoUrl}/api/livestreams/${id}/messages`, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error fetching livestream messages: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error fetching livestream messages',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      this.logger.error(`Error in getLivestreamMessages: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error fetching livestream messages', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Envoie un message dans un livestream
   * @param id ID du livestream
   * @param messageData Données du message
   * @returns Message envoyé
   */
  async sendLivestreamMessage(id: string, messageData: {
    userId: string;
    userName: string;
    content: string;
    type?: 'text' | 'question' | 'reaction';
  }): Promise<any> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post(`${this.socialPlatformVideoUrl}/api/livestreams/${id}/messages`, messageData, {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }).pipe(
          catchError((error: AxiosError) => {
            this.logger.error(`Error sending livestream message: ${error.message}`, error.stack);
            throw new HttpException(
              error.response?.data || 'Error sending livestream message',
              error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }),
        ),
      );

      // Émettre un événement pour le message de livestream
      this.eventEmitter.emit('livestream.message', {
        livestreamId: id,
        userId: messageData.userId,
        userName: messageData.userName,
        content: messageData.content,
        type: messageData.type,
        timestamp: new Date(),
      });

      return data;
    } catch (error) {
      this.logger.error(`Error in sendLivestreamMessage: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Error sending livestream message', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
