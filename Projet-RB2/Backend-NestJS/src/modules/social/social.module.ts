import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PrismaModule } from '../../prisma/prisma.module';

import { LivestreamService } from './services/livestream.service';
import { BlogService } from './services/blog.service';
import { SocialAnalyticsService } from './services/social-analytics.service';
import { MessagingIntegrationService } from './services/messaging-integration.service';

import { LivestreamController } from './controllers/livestream.controller';
import { BlogController } from './controllers/blog.controller';
import { SocialAnalyticsController } from './controllers/social-analytics.controller';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    HttpModule,
    EventEmitterModule.forRoot(),
  ],
  controllers: [
    LivestreamController,
    BlogController,
    SocialAnalyticsController,
  ],
  providers: [
    LivestreamService,
    BlogService,
    SocialAnalyticsService,
    MessagingIntegrationService,
  ],
  exports: [
    LivestreamService,
    BlogService,
    SocialAnalyticsService,
    MessagingIntegrationService,
  ],
})
export class SocialModule {}
