import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { KeyManagementService } from '../../security/services/key-management.service';
import { ExtendedCipher, ExtendedDecipher } from '../../security/interfaces/extended-crypto.interfaces';

/**
 * Interface pour les fichiers chiffrés
 */
export interface EncryptedFile {
  encryptedData: Buffer;
  iv: Buffer;
  authTag: Buffer;
  encryptedKey: Buffer;
  metadata: {
    originalName: string;
    mimeType: string;
    size: number;
    createdAt: Date;
    algorithm: string;
    keyId: string;
  };
}

/**
 * Service de chiffrement pour le stockage décentralisé
 * Ce service utilise un chiffrement hybride (RSA + AES) pour protéger les fichiers
 */
@Injectable()
export class StorageEncryptionService {
  private readonly logger = new Logger(StorageEncryptionService.name);
  private readonly ALGORITHM = 'aes-256-gcm';
  private readonly RSA_BITS = 4096;

  constructor(
    private readonly configService: ConfigService,
    private readonly keyManagementService: KeyManagementService,
  ) {}

  /**
   * Chiffre un fichier avec le chiffrement hybride
   * @param fileData Données du fichier
   * @param metadata Métadonnées du fichier
   * @param recipientPublicKey Clé publique du destinataire
   * @returns Fichier chiffré
   */
  async encryptFile(
    fileData: Buffer,
    metadata: {
      originalName: string;
      mimeType: string;
      size: number;
    },
    recipientPublicKey: string
  ): Promise<EncryptedFile> {
    try {
      // Récupérer une clé active pour le chiffrement de stockage
      const { key, metadata: keyMetadata } = await this.keyManagementService.getActiveKey('encryption', 'storage');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Chiffrer le fichier avec AES
      const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv) as ExtendedCipher;
      const encryptedData = Buffer.concat([cipher.update(fileData), cipher.final()]);
      const authTag = cipher.getAuthTag();

      // Chiffrer la clé AES avec la clé publique RSA du destinataire
      const encryptedKey = crypto.publicEncrypt(
        {
          key: recipientPublicKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256'
        },
        key
      );

      return {
        encryptedData,
        iv,
        authTag,
        encryptedKey,
        metadata: {
          originalName: metadata.originalName,
          mimeType: metadata.mimeType,
          size: metadata.size,
          createdAt: new Date(),
          algorithm: this.ALGORITHM,
          keyId: keyMetadata.id
        }
      };
    } catch (error) {
      this.logger.error('Failed to encrypt file:', error);
      throw error;
    }
  }

  /**
   * Déchiffre un fichier avec le chiffrement hybride
   * @param encryptedFile Fichier chiffré
   * @param privateKey Clé privée du destinataire
   * @returns Données du fichier déchiffré
   */
  async decryptFile(encryptedFile: EncryptedFile, privateKey: string): Promise<Buffer> {
    try {
      // Déchiffrer la clé AES avec la clé privée RSA
      const fileKey = crypto.privateDecrypt(
        {
          key: privateKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256'
        },
        encryptedFile.encryptedKey
      );

      // Déchiffrer le fichier avec AES
      const decipher = crypto.createDecipheriv(
        encryptedFile.metadata.algorithm,
        fileKey,
        encryptedFile.iv
      ) as ExtendedDecipher;

      decipher.setAuthTag(encryptedFile.authTag);

      return Buffer.concat([
        decipher.update(encryptedFile.encryptedData),
        decipher.final()
      ]);
    } catch (error) {
      this.logger.error('Failed to decrypt file:', error);
      throw error;
    }
  }

  /**
   * Génère une paire de clés RSA pour le chiffrement de fichiers
   * @returns Paire de clés RSA
   */
  generateKeyPair(): { publicKey: string; privateKey: string } {
    try {
      return crypto.generateKeyPairSync('rsa', {
        modulusLength: this.RSA_BITS,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });
    } catch (error) {
      this.logger.error('Failed to generate key pair:', error);
      throw error;
    }
  }

  /**
   * Chiffre un fichier pour plusieurs destinataires
   * @param fileData Données du fichier
   * @param metadata Métadonnées du fichier
   * @param recipientPublicKeys Clés publiques des destinataires
   * @returns Fichier chiffré avec clés pour chaque destinataire
   */
  async encryptFileForMultipleRecipients(
    fileData: Buffer,
    metadata: {
      originalName: string;
      mimeType: string;
      size: number;
    },
    recipientPublicKeys: string[]
  ): Promise<{
    encryptedFile: Omit<EncryptedFile, 'encryptedKey'>;
    encryptedKeys: { recipientId: string; encryptedKey: Buffer }[];
  }> {
    try {
      // Récupérer une clé active pour le chiffrement de stockage
      const { key, metadata: keyMetadata } = await this.keyManagementService.getActiveKey('encryption', 'storage');

      // Générer un vecteur d'initialisation aléatoire
      const iv = crypto.randomBytes(12);

      // Chiffrer le fichier avec AES
      const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv) as ExtendedCipher;
      const encryptedData = Buffer.concat([cipher.update(fileData), cipher.final()]);
      const authTag = cipher.getAuthTag();

      // Chiffrer la clé AES avec la clé publique RSA de chaque destinataire
      const encryptedKeys = recipientPublicKeys.map((publicKey, index) => {
        const encryptedKey = crypto.publicEncrypt(
          {
            key: publicKey,
            padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            oaepHash: 'sha256'
          },
          key
        );

        return {
          recipientId: `recipient-${index}`,
          encryptedKey
        };
      });

      return {
        encryptedFile: {
          encryptedData,
          iv,
          authTag,
          metadata: {
            originalName: metadata.originalName,
            mimeType: metadata.mimeType,
            size: metadata.size,
            createdAt: new Date(),
            algorithm: this.ALGORITHM,
            keyId: keyMetadata.id
          }
        },
        encryptedKeys
      };
    } catch (error) {
      this.logger.error('Failed to encrypt file for multiple recipients:', error);
      throw error;
    }
  }

  /**
   * Calcule le hachage d'un fichier pour vérifier son intégrité
   * @param fileData Données du fichier
   * @returns Hachage du fichier
   */
  calculateFileHash(fileData: Buffer): string {
    return crypto.createHash('sha256').update(fileData).digest('hex');
  }

  /**
   * Vérifie l'intégrité d'un fichier en comparant son hachage
   * @param fileData Données du fichier
   * @param expectedHash Hachage attendu
   * @returns true si le fichier est intègre
   */
  verifyFileIntegrity(fileData: Buffer, expectedHash: string): boolean {
    const actualHash = this.calculateFileHash(fileData);
    return actualHash === expectedHash;
  }
}
