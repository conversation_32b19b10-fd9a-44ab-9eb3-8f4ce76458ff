# 👥 Module de Gestion des Utilisateurs

## Vue d'ensemble

Le module Users fournit une gestion complète des profils utilisateurs avec CRUD, rôles, préférences et analytics.

## Fonctionnalités

### ✅ Gestion des Profils
- **CRUD complet**: Création, lecture, mise à jour, suppression
- **Profils détaillés**: Informations personnelles et professionnelles
- **Avatar et médias**: Gestion des images de profil
- **Préférences**: Paramètres personnalisés

### ✅ Système de Rôles
- **Rôles hiérarchiques**: USER, CREATOR, MODERATOR, ADMIN
- **Permissions granulaires**: Contrôle d'accès fin
- **Rôles dynamiques**: Attribution automatique basée sur l'activité

### ✅ Analytics Utilisateur
- **Métriques d'engagement**: Temps passé, actions effectuées
- **Historique d'activité**: Traçabilité des actions
- **Recommandations**: Suggestions personnalisées

## Architecture

```
users/
├── controllers/
│   ├── users.controller.ts         # CRUD utilisateurs
│   ├── profile.controller.ts       # Gestion des profils
│   └── preferences.controller.ts   # Préférences utilisateur
├── services/
│   ├── users.service.ts            # Service principal
│   ├── profile.service.ts          # Service de profil
│   ├── preferences.service.ts      # Service de préférences
│   └── user-analytics.service.ts   # Analytics utilisateur
├── dto/
│   ├── create-user.dto.ts          # DTO de création
│   ├── update-user.dto.ts          # DTO de mise à jour
│   └── user-preferences.dto.ts     # DTO de préférences
└── entities/
    ├── user.entity.ts              # Entité utilisateur
    └── user-preference.entity.ts   # Entité préférences
```

## Utilisation

### Créer un utilisateur
```typescript
POST /users
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "role": "USER"
}
```

### Obtenir un profil
```typescript
GET /users/:id/profile
```

### Mettre à jour les préférences
```typescript
PUT /users/:id/preferences
{
  "notifications": {
    "email": true,
    "push": false
  },
  "privacy": {
    "profileVisible": true
  }
}
```

## Modèle de Données

### Utilisateur
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
  isVerified: boolean;
  profile?: UserProfile;
  preferences?: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}
```

### Profil Utilisateur
```typescript
interface UserProfile {
  bio?: string;
  avatar?: string;
  location?: string;
  website?: string;
  socialLinks?: SocialLinks;
  interests?: string[];
}
```

## Tests

- **Tests unitaires**: 95% de couverture
- **Tests d'intégration**: Validation des workflows
- **Tests de performance**: Optimisation des requêtes

```bash
npm run test src/modules/users
```

## Sécurité

### Protection des données
- ✅ Chiffrement des données sensibles
- ✅ Validation stricte des entrées
- ✅ Contrôle d'accès basé sur les rôles
- ✅ Audit des modifications

### Conformité RGPD
- ✅ Droit à l'oubli
- ✅ Portabilité des données
- ✅ Consentement explicite
- ✅ Minimisation des données
