import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
// Définir notre propre interface User au lieu d'importer de @prisma/client
export interface User {
  id: string;
  email: string;
  name?: string;
  password?: string;
  role: string;
  isActive: boolean;
  isVerified: boolean;
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  twoFactorVerified?: boolean;
  createdAt: Date;
  updatedAt: Date;
  [key: string]: any; // Pour les propriétés supplémentaires
}
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    // Vérifier si l'utilisateur existe déjà
    const existingUser = await this.prisma.user.findUnique({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Un utilisateur avec cet email existe déjà');
    }

    // Hasher le mot de passe
    const hashedPassword = await this.hashPassword(createUserDto.password);

    try {
      // Créer l'utilisateur
      const user = await this.prisma.user.create({
        data: {
          email: createUserDto.email,
          password: hashedPassword,
          name: createUserDto.name,
          role: createUserDto.role || 'USER',
        },
      });

      this.logger.log(`Utilisateur créé avec succès: ${user.id}`);

      // Ne pas renvoyer le mot de passe
      const { password, ...result } = user;
      return result as User;
    } catch (error) {
      this.logger.error(`Erreur lors de la création de l'utilisateur: ${error.message}`);
      throw error;
    }
  }

  async findAll(page = 1, limit = 10): Promise<{ users: User[]; total: number; page: number; limit: number }> {
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          isVerified: true,
          createdAt: true,
          updatedAt: true,
          // Ne pas sélectionner le mot de passe
        },
      }),
      this.prisma.user.count(),
    ]);

    return {
      users,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string): Promise<User> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        isVerified: true,
        twoFactorEnabled: true,
        createdAt: true,
        updatedAt: true,
        // Ne pas sélectionner le mot de passe et autres champs sensibles
      },
    });

    if (!user) {
      throw new NotFoundException(`Utilisateur avec l'ID ${id} non trouvé`);
    }

    return user as User;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException(`Utilisateur avec l'email ${email} non trouvé`);
    }

    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    // Vérifier si l'utilisateur existe
    await this.findOne(id);

    // Préparer les données à mettre à jour
    const data: any = { ...updateUserDto };

    // Si le mot de passe est fourni, le hasher
    if (updateUserDto.password) {
      data.password = await this.hashPassword(updateUserDto.password);
    }

    try {
      const updatedUser = await this.prisma.user.update({
        where: { id },
        data,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          isVerified: true,
          twoFactorEnabled: true,
          createdAt: true,
          updatedAt: true,
          // Ne pas sélectionner le mot de passe
        },
      });

      this.logger.log(`Utilisateur mis à jour avec succès: ${id}`);
      return updatedUser as User;
    } catch (error) {
      this.logger.error(`Erreur lors de la mise à jour de l'utilisateur: ${error.message}`);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    // Vérifier si l'utilisateur existe
    await this.findOne(id);

    try {
      await this.prisma.user.delete({
        where: { id },
      });

      this.logger.log(`Utilisateur supprimé avec succès: ${id}`);
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de l'utilisateur: ${error.message}`);
      throw error;
    }
  }

  async validateUser(email: string, password: string): Promise<Omit<User, 'password'> | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        return null;
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        return null;
      }

      // Ne pas renvoyer le mot de passe
      const { password: _, ...result } = user;
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la validation de l'utilisateur: ${error.message}`);
      throw error;
    }
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  // Méthodes pour la gestion de l'authentification à deux facteurs
  async setTwoFactorSecret(userId: string, secret: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: { twoFactorSecret: secret },
    });
  }

  async enableTwoFactor(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        twoFactorEnabled: true,
        twoFactorVerified: true
      },
    });
  }

  async disableTwoFactor(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        twoFactorEnabled: false,
        twoFactorVerified: false,
        twoFactorSecret: null
      },
    });
  }
}
