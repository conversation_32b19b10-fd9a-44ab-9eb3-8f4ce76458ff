// Définition des types manquants de Prisma

export enum SocialContentType {
  VIDEO = 'VIDEO',
  POST = 'POST',
  LIVESTREAM = 'LIVESTREAM',
  BLOG = 'BLOG',
  ARTICLE = 'ARTICLE',
  AUDIO = 'AUDIO',
  EXERCISE = 'EXERCISE',
  MEDITATION = 'MEDITATION',
  WORKSHOP = 'WORKSHOP'
}

export enum BlogPostStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
  PENDING_REVIEW = 'PENDING_REVIEW'
}

export enum LivestreamStatus {
  SCHEDULED = 'SCHEDULED',
  LIVE = 'LIVE',
  ENDED = 'ENDED',
  CANCELLED = 'CANCELLED',
  PROCESSING = 'PROCESSING',
  ERROR = 'ERROR'
}

export enum CommentType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  REACTION = 'REACTION'
}

export enum PartnerCategory {
  ORGANIZER = 'ORGANIZER',
  TRAVEL_AGENCY = 'TRAVEL_AGENCY',
  CATERING = 'CATERING',
  GUIDE = 'GUIDE',
  TRANSPORT = 'TRANSPORT',
  WELLNESS = 'WELLNESS',
  INSURANCE = 'INSURANCE',
  ACCOMMODATION = 'ACCOMMODATION',
  EQUIPMENT = 'EQUIPMENT',
  OTHER = 'OTHER'
}

export enum PartnerType {
  PREMIUM_CERTIFIED = 'PREMIUM_CERTIFIED',
  CERTIFIED = 'CERTIFIED',
  STANDARD = 'STANDARD'
}

export enum PartnerStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  INACTIVE = 'INACTIVE',
  REJECTED = 'REJECTED'
}

export enum ContentType {
  ARTICLE = 'ARTICLE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  EXERCISE = 'EXERCISE',
  MEDITATION = 'MEDITATION',
  WORKSHOP = 'WORKSHOP',
  POST = 'POST',
  LIVESTREAM = 'LIVESTREAM'
}

export enum MonetizationType {
  PREMIUM = 'PREMIUM',
  SUBSCRIPTION = 'SUBSCRIPTION',
  ONE_TIME = 'ONE_TIME',
  DONATION = 'DONATION'
}

export enum RecommendationType {
  VIDEO = 'VIDEO',
  POST = 'POST',
  LIVESTREAM = 'LIVESTREAM',
  BLOG = 'BLOG',
  ARTICLE = 'ARTICLE',
  AUDIO = 'AUDIO',
  EXERCISE = 'EXERCISE',
  MEDITATION = 'MEDITATION',
  WORKSHOP = 'WORKSHOP',
  RETREAT = 'RETREAT',
  PARTNER = 'PARTNER',
  USER = 'USER'
}

// Interfaces pour les entités principales
export interface Partner {
  id: string;
  userId: string;
  companyName: string;
  type: PartnerType;
  category: PartnerCategory;
  description: string;
  logo?: string;
  website?: string;
  specializations: string[];
  languages: string[];
  status: PartnerStatus;
  createdAt: Date;
  updatedAt: Date;
  // Propriétés supplémentaires
  coverageAreas?: {
    countries: string[];
    cities?: string[];
    regions?: string[];
  };
  reviews?: {
    id: string;
    rating: number;
    comment?: string;
    userId: string;
    createdAt: Date;
  }[];
  completedServices?: number;
}

export interface Retreat {
  id: string;
  organizerId: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location: string;
  capacity: number;
  price: number;
  images: string[];
  createdAt: Date;
  updatedAt: Date;
  // Propriétés supplémentaires
  categories?: string[];
  reviews?: {
    id: string;
    rating: number;
    comment?: string;
    userId: string;
    createdAt: Date;
  }[];
  status?: string;
  amenities?: string[];
  activities?: string[];
  includedServices?: string[];
  maxParticipants?: number;
  minParticipants?: number;
  currency?: string;
}
