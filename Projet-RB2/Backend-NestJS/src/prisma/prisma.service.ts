import { Injectable, OnModuleInit, OnModuleDestroy, INestApplication, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// Classe de base pour simuler PrismaClient
class MockPrismaClient {
  protected logger = new Logger('MockPrismaClient');

  // Simuler les modèles Prisma avec des proxies
  [key: string]: any;

  constructor() {
    // Créer des proxies pour simuler les modèles Prisma
    return new Proxy(this, {
      get: (target, prop) => {
        if (prop in target) {
          return target[prop as string];
        }

        // Créer un proxy pour le modèle demandé
        return new Proxy({}, {
          get: (_, method) => {
            return (...args: any[]) => {
              this.logger.log(`Mock ${String(prop)}.${String(method)}() called with args: ${JSON.stringify(args)}`);

              // Simuler les méthodes courantes
              switch (method) {
                case 'findUnique':
                case 'findFirst':
                  return Promise.resolve(null);
                case 'findMany':
                  return Promise.resolve([]);
                case 'create':
                  return Promise.resolve({ id: 'mock-id', ...args[0]?.data });
                case 'update':
                  return Promise.resolve({ id: 'mock-id', ...args[0]?.data });
                case 'delete':
                  return Promise.resolve({ id: 'mock-id' });
                case 'deleteMany':
                  return Promise.resolve({ count: 0 });
                case 'count':
                  return Promise.resolve(0);
                default:
                  return Promise.resolve(null);
              }
            };
          }
        });
      }
    });
  }

  async $connect() {
    this.logger.log('Mock database connected');
    return Promise.resolve();
  }

  async $disconnect() {
    this.logger.log('Mock database disconnected');
    return Promise.resolve();
  }

  $on(event: string, callback: () => void) {
    this.logger.log(`Event ${event} registered`);
  }
}

@Injectable()
export class PrismaService extends MockPrismaClient implements OnModuleInit, OnModuleDestroy {

  constructor(private configService: ConfigService) {
    super();
    this.logger.log('PrismaService initialized with mock implementation');
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  async enableShutdownHooks(app: INestApplication) {
    this.$on('beforeExit', async () => {
      await app.close();
    });
  }

  async cleanDatabase() {
    if (this.configService.get<string>('app.nodeEnv') === 'test') {
      // Suppression des données dans l'ordre inverse des dépendances
      const models = Reflect.ownKeys(this).filter(
        (key) => typeof key === 'string' && key[0] !== '_' && key[0] !== '$' && key !== 'constructor',
      ) as string[];

      return Promise.all(
        models.map((modelKey) => {
          return this[modelKey as string].deleteMany();
        }),
      );
    }
  }
}
