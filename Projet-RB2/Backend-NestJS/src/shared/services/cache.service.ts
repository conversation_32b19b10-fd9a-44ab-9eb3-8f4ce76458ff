import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as NodeCache from 'node-cache';
import * as Redis from 'ioredis';

/**
 * Service de cache qui supporte à la fois le cache en mémoire (NodeCache) et Redis
 */
@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly cache: NodeCache;
  private readonly redis: Redis.Redis | null = null;
  private readonly useRedis: boolean;
  private readonly defaultTTL: number;

  constructor(private readonly configService: ConfigService) {
    this.useRedis = this.configService.get<boolean>('CACHE_USE_REDIS', false);
    this.defaultTTL = this.configService.get<number>('CACHE_DEFAULT_TTL', 3600); // 1 heure par défaut

    // Initialiser le cache en mémoire
    this.cache = new NodeCache({
      stdTTL: this.defaultTTL,
      checkperiod: 120, // Vérifier les expirations toutes les 2 minutes
      useClones: false, // Ne pas cloner les objets pour améliorer les performances
    });

    // Initialiser Redis si activé
    if (this.useRedis) {
      try {
        const redisHost = this.configService.get<string>('REDIS_HOST', 'localhost');
        const redisPort = this.configService.get<number>('REDIS_PORT', 6379);
        const redisPassword = this.configService.get<string>('REDIS_PASSWORD', '');
        const redisDb = this.configService.get<number>('REDIS_DB', 0);

        this.redis = new Redis({
          host: redisHost,
          port: redisPort,
          password: redisPassword || undefined,
          db: redisDb,
          retryStrategy: (times) => {
            const delay = Math.min(times * 50, 2000);
            return delay;
          },
        });

        this.redis.on('error', (err) => {
          this.logger.error(`Erreur Redis: ${err.message}`);
        });

        this.redis.on('connect', () => {
          this.logger.log(`Connecté à Redis: ${redisHost}:${redisPort}`);
        });
      } catch (error) {
        this.logger.error(`Erreur lors de l'initialisation de Redis: ${error.message}`);
        this.logger.warn('Utilisation du cache en mémoire comme fallback');
      }
    }
  }

  /**
   * Définit une valeur dans le cache
   * @param key Clé du cache
   * @param value Valeur à stocker
   * @param ttl Durée de vie en secondes (optionnel)
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      if (this.useRedis && this.redis) {
        // Stocker dans Redis
        const serializedValue = JSON.stringify(value);
        if (ttl) {
          await this.redis.set(key, serializedValue, 'EX', ttl);
        } else {
          await this.redis.set(key, serializedValue);
        }
      } else {
        // Stocker dans le cache en mémoire
        this.cache.set(key, value, ttl);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la définition de la valeur dans le cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        this.cache.set(key, value, ttl);
      }
    }
  }

  /**
   * Récupère une valeur du cache
   * @param key Clé du cache
   * @returns Valeur stockée ou null si non trouvée
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      if (this.useRedis && this.redis) {
        // Récupérer depuis Redis
        const value = await this.redis.get(key);
        if (!value) {
          return null;
        }
        return JSON.parse(value) as T;
      } else {
        // Récupérer depuis le cache en mémoire
        return this.cache.get<T>(key) || null;
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de la valeur du cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        return this.cache.get<T>(key) || null;
      }
      return null;
    }
  }

  /**
   * Supprime une valeur du cache
   * @param key Clé du cache
   * @returns true si la valeur a été supprimée, false sinon
   */
  async delete(key: string): Promise<boolean> {
    try {
      if (this.useRedis && this.redis) {
        // Supprimer de Redis
        const result = await this.redis.del(key);
        return result > 0;
      } else {
        // Supprimer du cache en mémoire
        return this.cache.del(key) > 0;
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de la valeur du cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        return this.cache.del(key) > 0;
      }
      return false;
    }
  }

  /**
   * Supprime toutes les valeurs du cache
   */
  async clear(): Promise<void> {
    try {
      if (this.useRedis && this.redis) {
        // Vider Redis
        await this.redis.flushdb();
      }
      // Vider le cache en mémoire
      this.cache.flushAll();
    } catch (error) {
      this.logger.error(`Erreur lors de la suppression de toutes les valeurs du cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      this.cache.flushAll();
    }
  }

  /**
   * Récupère plusieurs valeurs du cache
   * @param keys Clés du cache
   * @returns Map des valeurs trouvées
   */
  async mget<T>(keys: string[]): Promise<Map<string, T>> {
    try {
      const result = new Map<string, T>();

      if (this.useRedis && this.redis) {
        // Récupérer depuis Redis
        const values = await this.redis.mget(...keys);
        keys.forEach((key, index) => {
          const value = values[index];
          if (value) {
            result.set(key, JSON.parse(value) as T);
          }
        });
      } else {
        // Récupérer depuis le cache en mémoire
        const values = this.cache.mget<T>(keys);
        Object.entries(values).forEach(([key, value]) => {
          result.set(key, value);
        });
      }

      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de plusieurs valeurs du cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        const values = this.cache.mget<T>(keys);
        const result = new Map<string, T>();
        Object.entries(values).forEach(([key, value]) => {
          result.set(key, value);
        });
        return result;
      }
      return new Map<string, T>();
    }
  }

  /**
   * Définit plusieurs valeurs dans le cache
   * @param entries Map des clés et valeurs à stocker
   * @param ttl Durée de vie en secondes (optionnel)
   */
  async mset<T>(entries: Map<string, T>, ttl?: number): Promise<void> {
    try {
      if (this.useRedis && this.redis) {
        // Stocker dans Redis
        const pipeline = this.redis.pipeline();
        
        entries.forEach((value, key) => {
          const serializedValue = JSON.stringify(value);
          if (ttl) {
            pipeline.set(key, serializedValue, 'EX', ttl);
          } else {
            pipeline.set(key, serializedValue);
          }
        });
        
        await pipeline.exec();
      } else {
        // Stocker dans le cache en mémoire
        const entriesObj: Record<string, T> = {};
        entries.forEach((value, key) => {
          entriesObj[key] = value;
        });
        
        this.cache.mset(Object.entries(entriesObj).map(([key, value]) => ({ key, val: value, ttl })));
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la définition de plusieurs valeurs dans le cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        const entriesObj: Record<string, T> = {};
        entries.forEach((value, key) => {
          entriesObj[key] = value;
        });
        
        this.cache.mset(Object.entries(entriesObj).map(([key, value]) => ({ key, val: value, ttl })));
      }
    }
  }

  /**
   * Récupère toutes les clés correspondant à un motif
   * @param pattern Motif de recherche (ex: "user:*")
   * @returns Liste des clés correspondantes
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      if (this.useRedis && this.redis) {
        // Récupérer depuis Redis
        return await this.redis.keys(pattern);
      } else {
        // Récupérer depuis le cache en mémoire
        const allKeys = this.cache.keys();
        const regex = new RegExp(pattern.replace('*', '.*'));
        return allKeys.filter(key => regex.test(key));
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des clés: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        const allKeys = this.cache.keys();
        const regex = new RegExp(pattern.replace('*', '.*'));
        return allKeys.filter(key => regex.test(key));
      }
      return [];
    }
  }

  /**
   * Vérifie si une clé existe dans le cache
   * @param key Clé du cache
   * @returns true si la clé existe, false sinon
   */
  async has(key: string): Promise<boolean> {
    try {
      if (this.useRedis && this.redis) {
        // Vérifier dans Redis
        return (await this.redis.exists(key)) > 0;
      } else {
        // Vérifier dans le cache en mémoire
        return this.cache.has(key);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la vérification de l'existence de la clé: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        return this.cache.has(key);
      }
      return false;
    }
  }

  /**
   * Définit une valeur dans le cache uniquement si la clé n'existe pas déjà
   * @param key Clé du cache
   * @param value Valeur à stocker
   * @param ttl Durée de vie en secondes (optionnel)
   * @returns true si la valeur a été définie, false si la clé existait déjà
   */
  async setIfNotExists<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      if (this.useRedis && this.redis) {
        // Stocker dans Redis avec NX (only if not exists)
        const serializedValue = JSON.stringify(value);
        const result = ttl
          ? await this.redis.set(key, serializedValue, 'EX', ttl, 'NX')
          : await this.redis.set(key, serializedValue, 'NX');
        
        return result === 'OK';
      } else {
        // Stocker dans le cache en mémoire
        return this.cache.set(key, value, ttl);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de la définition conditionnelle de la valeur dans le cache: ${error.message}`);
      // Fallback au cache en mémoire en cas d'erreur Redis
      if (this.useRedis && this.redis) {
        return this.cache.set(key, value, ttl);
      }
      return false;
    }
  }
}
