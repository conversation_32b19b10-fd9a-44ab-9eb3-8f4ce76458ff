import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { ContentType } from '@prisma/client';
import { JwtAuthGuard } from '../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../src/modules/auth/guards/roles.guard';

describe('AnalyticsController (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtToken: string;

  // Mock des guards pour les tests
  const mockJwtAuthGuard = { canActivate: jest.fn(() => true) };
  const mockRolesGuard = { canActivate: jest.fn(() => true) };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    
    prismaService = app.get<PrismaService>(PrismaService);
    
    // Nettoyer la base de données avant les tests
    await prismaService.engagementMetric.deleteMany({});
    await prismaService.audienceMetric.deleteMany({});
    await prismaService.revenueMetric.deleteMany({});
    await prismaService.dashboard.deleteMany({});
    
    // Créer des données de test
    const creatorId = '123e4567-e89b-12d3-a456-426614174000';
    const contentId = '123e4567-e89b-12d3-a456-426614174001';
    
    // Créer des métriques d'engagement
    await prismaService.engagementMetric.create({
      data: {
        creatorId,
        contentId,
        contentType: ContentType.ARTICLE,
        views: 100,
        likes: 50,
        comments: 10,
        shares: 5,
        bookmarks: 20,
        clickThroughs: 30,
        date: new Date(),
      },
    });
    
    // Créer des métriques d'audience
    await prismaService.audienceMetric.create({
      data: {
        creatorId,
        totalFollowers: 1000,
        newFollowers: 50,
        lostFollowers: 10,
        activeFollowers: 500,
        demographics: {
          age: {
            '18-24': 20,
            '25-34': 35,
            '35-44': 25,
            '45-54': 15,
            '55+': 5,
          },
          gender: {
            male: 40,
            female: 58,
            other: 2,
          },
        },
        date: new Date(),
      },
    });
    
    // Créer des métriques de revenus
    await prismaService.revenueMetric.create({
      data: {
        creatorId,
        contentId,
        amount: 100.50,
        currency: 'EUR',
        source: 'SUBSCRIPTION',
        date: new Date(),
      },
    });
    
    // Simuler un token JWT pour les tests
    jwtToken = 'test-jwt-token';
    
    await app.init();
  });

  afterAll(async () => {
    // Nettoyer la base de données après les tests
    await prismaService.engagementMetric.deleteMany({});
    await prismaService.audienceMetric.deleteMany({});
    await prismaService.revenueMetric.deleteMany({});
    await prismaService.dashboard.deleteMany({});
    
    await app.close();
  });

  describe('/analytics/metrics/:creatorId (GET)', () => {
    it('should get creator metrics', () => {
      const creatorId = '123e4567-e89b-12d3-a456-426614174000';
      
      return request(app.getHttpServer())
        .get(`/analytics/metrics/${creatorId}`)
        .query({
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          contentType: ContentType.ARTICLE,
        })
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('engagement');
          expect(res.body).toHaveProperty('audience');
          expect(res.body).toHaveProperty('revenue');
          expect(res.body).toHaveProperty('topContent');
          expect(res.body).toHaveProperty('period');
        });
    });
  });

  describe('/analytics/metrics/:creatorId/content/:contentId (GET)', () => {
    it('should get content metrics', () => {
      const creatorId = '123e4567-e89b-12d3-a456-426614174000';
      const contentId = '123e4567-e89b-12d3-a456-426614174001';
      
      return request(app.getHttpServer())
        .get(`/analytics/metrics/${creatorId}/content/${contentId}`)
        .query({
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        })
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('engagement');
          expect(res.body).toHaveProperty('revenue');
          expect(res.body).toHaveProperty('period');
        });
    });
  });

  describe('/analytics/metrics/:creatorId/summary (GET)', () => {
    it('should get creator summary', () => {
      const creatorId = '123e4567-e89b-12d3-a456-426614174000';
      
      return request(app.getHttpServer())
        .get(`/analytics/metrics/${creatorId}/summary`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('engagement');
          expect(res.body).toHaveProperty('audience');
          expect(res.body).toHaveProperty('revenue');
          expect(res.body).toHaveProperty('topContent');
        });
    });
  });

  describe('/analytics/forecasting/:creatorId (GET)', () => {
    it('should get creator forecasts', () => {
      const creatorId = '123e4567-e89b-12d3-a456-426614174000';
      
      return request(app.getHttpServer())
        .get(`/analytics/forecasting/${creatorId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('engagement');
          expect(res.body).toHaveProperty('audience');
          expect(res.body).toHaveProperty('revenue');
        });
    });
  });

  describe('/analytics/benchmarks/:creatorId (GET)', () => {
    it('should get creator benchmarks', () => {
      const creatorId = '123e4567-e89b-12d3-a456-426614174000';
      
      return request(app.getHttpServer())
        .get(`/analytics/benchmarks/${creatorId}`)
        .query({ category: 'wellness' })
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('engagement');
          expect(res.body).toHaveProperty('audience');
          expect(res.body).toHaveProperty('revenue');
          expect(res.body).toHaveProperty('category');
        });
    });
  });

  describe('/analytics/dashboards/:creatorId (POST)', () => {
    it('should create a dashboard', () => {
      const creatorId = '123e4567-e89b-12d3-a456-426614174000';
      const dashboardData = {
        name: 'Test Dashboard',
        description: 'Test dashboard description',
        layout: { columns: 2 },
        isDefault: false,
      };
      
      return request(app.getHttpServer())
        .post(`/analytics/dashboards/${creatorId}`)
        .send(dashboardData)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.name).toBe(dashboardData.name);
          expect(res.body.description).toBe(dashboardData.description);
          expect(res.body.creatorId).toBe(creatorId);
        });
    });
  });
});
