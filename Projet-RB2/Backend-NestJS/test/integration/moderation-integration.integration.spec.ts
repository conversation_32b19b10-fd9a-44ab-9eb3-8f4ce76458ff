import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { JwtAuthGuard } from '../../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/modules/auth/guards/roles.guard';
import { RecommendationType } from '../../src/modules/recommendation/enums/recommendation-type.enum';
import { HttpService } from '@nestjs/axios';
import { of } from 'rxjs';

// Mock pour le JwtAuthGuard
const mockJwtAuthGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour le RolesGuard
const mockRolesGuard = {
  canActivate: jest.fn().mockImplementation(() => true),
};

// Mock pour l'utilisateur authentifié
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  role: 'USER',
};

// Mock pour le service HTTP
const mockHttpService = {
  post: jest.fn().mockImplementation((url, data, config) => {
    if (url.includes('/api/moderation/check')) {
      return of({
        data: {
          success: true,
          allowed: true,
          status: 'APPROVED',
        },
      });
    } else if (url.includes('/api/moderation/report')) {
      return of({
        data: {
          success: true,
          message: 'Report received',
          reportId: 'test-report-id',
        },
      });
    } else if (url.includes('/api/moderation/status')) {
      return of({
        data: {
          success: true,
          statuses: {
            'test-retreat-id': 'APPROVED',
            'test-course-id': 'PENDING',
            'test-video-id': 'REJECTED',
          },
        },
      });
    }
    return of({ data: {} });
  }),
};

describe('Moderation Integration Tests', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .overrideProvider(HttpService)
      .useValue(mockHttpService)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());

    // Mock pour la requête utilisateur
    app.use((req, res, next) => {
      req.user = mockUser;
      next();
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /recommendations/moderation/check/:type/:id', () => {
    it('should check if a recommendation is allowed', async () => {
      const response = await request(app.getHttpServer())
        .post(`/recommendations/moderation/check/${RecommendationType.RETREAT}/test-retreat-id`)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.allowed).toBe(true);
      expect(response.body.status).toBe('APPROVED');
    });
  });

  describe('POST /recommendations/moderation/report/:type/:id', () => {
    it('should report a recommendation', async () => {
      const reportData = {
        reason: 'Inappropriate content',
      };

      const response = await request(app.getHttpServer())
        .post(`/recommendations/moderation/report/${RecommendationType.RETREAT}/test-retreat-id`)
        .send(reportData)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Report received');
      expect(response.body.reportId).toBe('test-report-id');
    });

    it('should return 400 if reason is missing', async () => {
      await request(app.getHttpServer())
        .post(`/recommendations/moderation/report/${RecommendationType.RETREAT}/test-retreat-id`)
        .send({})
        .expect(400);
    });
  });

  describe('GET /recommendations/moderation/status/:type/:id', () => {
    it('should get the moderation status of a recommendation', async () => {
      const response = await request(app.getHttpServer())
        .get(`/recommendations/moderation/status/${RecommendationType.RETREAT}/test-retreat-id`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('APPROVED');
    });
  });

  describe('POST /recommendations/moderation/filter', () => {
    it('should filter recommendations based on moderation status', async () => {
      const recommendations = [
        { id: 'test-retreat-id', title: 'Test Retreat' },
        { id: 'test-course-id', title: 'Test Course' },
        { id: 'test-video-id', title: 'Test Video' },
      ];

      const response = await request(app.getHttpServer())
        .post(`/recommendations/moderation/filter`)
        .send({
          recommendations,
          type: RecommendationType.RETREAT,
        })
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data).toBeInstanceOf(Array);
      
      // Should filter out rejected recommendations
      expect(response.body.data.length).toBe(2);
      expect(response.body.data.find(r => r.id === 'test-video-id')).toBeUndefined();
      expect(response.body.data.find(r => r.id === 'test-retreat-id')).toBeDefined();
      expect(response.body.data.find(r => r.id === 'test-course-id')).toBeDefined();
    });

    it('should return 400 if recommendations or type is missing', async () => {
      await request(app.getHttpServer())
        .post(`/recommendations/moderation/filter`)
        .send({
          // Missing recommendations
          type: RecommendationType.RETREAT,
        })
        .expect(400);

      await request(app.getHttpServer())
        .post(`/recommendations/moderation/filter`)
        .send({
          recommendations: [{ id: 'test-retreat-id', title: 'Test Retreat' }],
          // Missing type
        })
        .expect(400);
    });
  });
});
