import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

describe('LearningModule (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let adminToken: string;
  let userToken: string;
  let courseId: string;
  let lessonId: string;
  let enrollmentId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prismaService = app.get<PrismaService>(PrismaService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);

    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      }),
    );

    await app.init();

    // Nettoyer la base de données avant les tests
    await prismaService.enrollment.deleteMany();
    await prismaService.lesson.deleteMany();
    await prismaService.course.deleteMany();
    await prismaService.user.deleteMany();

    // Créer un utilisateur admin et un utilisateur normal pour les tests
    const adminUser = await prismaService.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // secret42
        name: 'Admin',
        role: 'ADMIN',
        isActive: true,
        isVerified: true,
      },
    });

    const normalUser = await prismaService.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // secret42
        name: 'User',
        role: 'USER',
        isActive: true,
        isVerified: true,
      },
    });

    // Générer des tokens JWT pour les tests
    adminToken = jwtService.sign(
      { sub: adminUser.id, email: adminUser.email, role: adminUser.role },
      {
        secret: configService.get<string>('jwt.secret'),
        expiresIn: '1h',
      },
    );

    userToken = jwtService.sign(
      { sub: normalUser.id, email: normalUser.email, role: normalUser.role },
      {
        secret: configService.get<string>('jwt.secret'),
        expiresIn: '1h',
      },
    );
  });

  afterAll(async () => {
    // Nettoyer la base de données après les tests
    await prismaService.enrollment.deleteMany();
    await prismaService.lesson.deleteMany();
    await prismaService.course.deleteMany();
    await prismaService.user.deleteMany();
    await app.close();
  });

  describe('Courses', () => {
    it('should create a course (POST /courses)', async () => {
      const createCourseDto = {
        title: 'Test Course',
        description: 'Test Description',
        level: 'BEGINNER',
        category: 'Test Category',
      };

      const response = await request(app.getHttpServer())
        .post('/courses')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createCourseDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.title).toBe(createCourseDto.title);
      expect(response.body.description).toBe(createCourseDto.description);
      expect(response.body.level).toBe(createCourseDto.level);
      expect(response.body.category).toBe(createCourseDto.category);

      courseId = response.body.id;
    });

    it('should get all courses (GET /courses)', async () => {
      const response = await request(app.getHttpServer())
        .get('/courses')
        .expect(200);

      expect(response.body).toHaveProperty('courses');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body.courses).toBeInstanceOf(Array);
      expect(response.body.courses.length).toBeGreaterThan(0);
    });

    it('should get a course by id (GET /courses/:id)', async () => {
      const response = await request(app.getHttpServer())
        .get(`/courses/${courseId}`)
        .expect(200);

      expect(response.body).toHaveProperty('id', courseId);
      expect(response.body).toHaveProperty('title');
      expect(response.body).toHaveProperty('description');
      expect(response.body).toHaveProperty('level');
      expect(response.body).toHaveProperty('category');
      expect(response.body).toHaveProperty('lessons');
    });

    it('should update a course (PATCH /courses/:id)', async () => {
      const updateCourseDto = {
        title: 'Updated Course',
        description: 'Updated Description',
      };

      const response = await request(app.getHttpServer())
        .patch(`/courses/${courseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateCourseDto)
        .expect(200);

      expect(response.body).toHaveProperty('id', courseId);
      expect(response.body.title).toBe(updateCourseDto.title);
      expect(response.body.description).toBe(updateCourseDto.description);
    });
  });

  describe('Lessons', () => {
    it('should create a lesson (POST /courses/:courseId/lessons)', async () => {
      const createLessonDto = {
        title: 'Test Lesson',
        content: 'Test Content',
        order: 1,
      };

      const response = await request(app.getHttpServer())
        .post(`/courses/${courseId}/lessons`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createLessonDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('courseId', courseId);
      expect(response.body.title).toBe(createLessonDto.title);
      expect(response.body.content).toBe(createLessonDto.content);
      expect(response.body.order).toBe(createLessonDto.order);

      lessonId = response.body.id;
    });

    it('should get all lessons for a course (GET /courses/:courseId/lessons)', async () => {
      const response = await request(app.getHttpServer())
        .get(`/courses/${courseId}/lessons`)
        .expect(200);

      expect(response.body).toHaveProperty('lessons');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body.lessons).toBeInstanceOf(Array);
      expect(response.body.lessons.length).toBeGreaterThan(0);
    });

    it('should get a lesson by id (GET /courses/:courseId/lessons/:id)', async () => {
      const response = await request(app.getHttpServer())
        .get(`/courses/${courseId}/lessons/${lessonId}`)
        .expect(200);

      expect(response.body).toHaveProperty('id', lessonId);
      expect(response.body).toHaveProperty('courseId', courseId);
      expect(response.body).toHaveProperty('title');
      expect(response.body).toHaveProperty('content');
      expect(response.body).toHaveProperty('order');
    });

    it('should update a lesson (PATCH /courses/:courseId/lessons/:id)', async () => {
      const updateLessonDto = {
        title: 'Updated Lesson',
        content: 'Updated Content',
      };

      const response = await request(app.getHttpServer())
        .patch(`/courses/${courseId}/lessons/${lessonId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateLessonDto)
        .expect(200);

      expect(response.body).toHaveProperty('id', lessonId);
      expect(response.body.title).toBe(updateLessonDto.title);
      expect(response.body.content).toBe(updateLessonDto.content);
    });
  });

  describe('Enrollments', () => {
    it('should enroll a user in a course (POST /enrollments/enroll-me/:courseId)', async () => {
      const response = await request(app.getHttpServer())
        .post(`/enrollments/enroll-me/${courseId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('courseId', courseId);
      expect(response.body).toHaveProperty('progress', 0);

      enrollmentId = response.body.id;
    });

    it('should get user enrollments (GET /enrollments/my-enrollments)', async () => {
      const response = await request(app.getHttpServer())
        .get('/enrollments/my-enrollments')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('enrollments');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body.enrollments).toBeInstanceOf(Array);
      expect(response.body.enrollments.length).toBeGreaterThan(0);
    });

    it('should update enrollment progress (PATCH /enrollments/:id/progress)', async () => {
      const updateProgressDto = {
        progress: 50,
      };

      const response = await request(app.getHttpServer())
        .patch(`/enrollments/${enrollmentId}/progress`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateProgressDto)
        .expect(200);

      expect(response.body).toHaveProperty('id', enrollmentId);
      expect(response.body).toHaveProperty('progress', updateProgressDto.progress);
    });
  });

  describe('Cleanup', () => {
    it('should delete an enrollment (DELETE /enrollments/:id)', async () => {
      await request(app.getHttpServer())
        .delete(`/enrollments/${enrollmentId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(204);
    });

    it('should delete a lesson (DELETE /courses/:courseId/lessons/:id)', async () => {
      await request(app.getHttpServer())
        .delete(`/courses/${courseId}/lessons/${lessonId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(204);
    });

    it('should delete a course (DELETE /courses/:id)', async () => {
      await request(app.getHttpServer())
        .delete(`/courses/${courseId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(204);
    });
  });
});
