import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PartnerCategory, PartnerType } from '../src/prisma/prisma-types';

describe('Matching Module (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtService: JwtService;
  let configService: ConfigService;
  let adminToken: string;
  let partnerToken: string;
  let userToken: string;
  let partnerId: string;
  let retreatId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

    prismaService = app.get<PrismaService>(PrismaService);
    jwtService = app.get<JwtService>(JwtService);
    configService = app.get<ConfigService>(ConfigService);

    await app.init();

    // Créer des utilisateurs de test
    await setupTestUsers();

    // Créer des données de test
    await setupTestData();

    // Générer des tokens JWT pour les tests
    adminToken = generateToken('admin-user-id', 'ADMIN');
    partnerToken = generateToken('partner-user-id', 'PARTNER');
    userToken = generateToken('regular-user-id', 'USER');
  });

  afterAll(async () => {
    // Nettoyer les données de test
    await cleanupTestData();
    await app.close();
  });

  // Fonction pour générer un token JWT
  function generateToken(userId: string, role: string): string {
    const payload = { sub: userId, email: `${role.toLowerCase()}@example.com`, role };
    return jwtService.sign(payload, {
      secret: configService.get<string>('JWT_SECRET'),
      expiresIn: '1h',
    });
  }

  // Fonction pour configurer les utilisateurs de test
  async function setupTestUsers() {
    // Créer un utilisateur administrateur
    await prismaService.user.upsert({
      where: { id: 'admin-user-id' },
      update: {},
      create: {
        id: 'admin-user-id',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        password: 'hashed-password',
        role: 'ADMIN',
      },
    });

    // Créer un utilisateur partenaire
    await prismaService.user.upsert({
      where: { id: 'partner-user-id' },
      update: {},
      create: {
        id: 'partner-user-id',
        email: '<EMAIL>',
        firstName: 'Partner',
        lastName: 'User',
        password: 'hashed-password',
        role: 'PARTNER',
      },
    });

    // Créer un utilisateur régulier
    await prismaService.user.upsert({
      where: { id: 'regular-user-id' },
      update: {},
      create: {
        id: 'regular-user-id',
        email: '<EMAIL>',
        firstName: 'Regular',
        lastName: 'User',
        password: 'hashed-password',
        role: 'USER',
      },
    });
  }

  // Fonction pour configurer les données de test
  async function setupTestData() {
    // Créer un partenaire de test
    const partner = await prismaService.partner.upsert({
      where: { userId: 'partner-user-id' },
      update: {},
      create: {
        userId: 'partner-user-id',
        companyName: 'Test Partner Company',
        description: 'A test partner for e2e tests',
        category: PartnerCategory.WELLNESS,
        type: PartnerType.CERTIFIED,
        specializations: ['Yoga', 'Meditation'],
        languages: ['English', 'French'],
        coverageAreas: {
          countries: ['France'],
          regions: ['Île-de-France'],
        },
        status: 'ACTIVE',
      },
    });

    partnerId = partner.id;

    // Créer une retraite de test
    const retreat = await prismaService.retreat.create({
      data: {
        title: 'Test Retreat',
        description: 'A test retreat for e2e tests',
        location: 'Paris, France',
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours dans le futur
        endDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 37 jours dans le futur
        capacity: 20,
        price: 1000,
        status: 'PUBLISHED',
        organizerId: 'regular-user-id',
        categories: {
          create: [
            { name: 'Yoga' },
            { name: 'Meditation' },
          ],
        },
      },
    });

    retreatId = retreat.id;
  }

  // Fonction pour nettoyer les données de test
  async function cleanupTestData() {
    // Supprimer les données de test dans l'ordre inverse de leur création
    await prismaService.retreat.deleteMany({
      where: { organizerId: 'regular-user-id' },
    });

    await prismaService.partner.deleteMany({
      where: { userId: 'partner-user-id' },
    });

    await prismaService.user.deleteMany({
      where: {
        id: {
          in: ['admin-user-id', 'partner-user-id', 'regular-user-id'],
        },
      },
    });
  }

  describe('/matching/partners (POST)', () => {
    it('should return matching partners for criteria', () => {
      return request(app.getHttpServer())
        .post('/matching/partners')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          categories: [PartnerCategory.WELLNESS],
          specializations: ['Yoga'],
          languages: ['French'],
        })
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('results');
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('executionTimeMs');
          expect(Array.isArray(res.body.results)).toBe(true);
        });
    });

    it('should return 401 if not authenticated', () => {
      return request(app.getHttpServer())
        .post('/matching/partners')
        .send({
          categories: [PartnerCategory.WELLNESS],
        })
        .expect(401);
    });
  });

  describe('/matching/retreats (POST)', () => {
    it('should return matching retreats for criteria', () => {
      return request(app.getHttpServer())
        .post('/matching/retreats')
        .set('Authorization', `Bearer ${partnerToken}`)
        .send({
          dateRange: {
            start: new Date().toISOString().split('T')[0],
            end: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 60 jours
          },
          location: {
            country: 'France',
          },
        })
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('results');
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('executionTimeMs');
          expect(Array.isArray(res.body.results)).toBe(true);
        });
    });

    it('should return 401 if not authenticated', () => {
      return request(app.getHttpServer())
        .post('/matching/retreats')
        .send({
          dateRange: {
            start: new Date().toISOString().split('T')[0],
            end: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          },
        })
        .expect(401);
    });
  });

  describe('/matching/partners/retreat/:retreatId (GET)', () => {
    it('should return matching partners for a retreat', () => {
      return request(app.getHttpServer())
        .get(`/matching/partners/retreat/${retreatId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('results');
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('executionTimeMs');
          expect(Array.isArray(res.body.results)).toBe(true);
        });
    });

    it('should return 404 if retreat not found', () => {
      return request(app.getHttpServer())
        .get('/matching/partners/retreat/non-existent-id')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);
    });
  });

  describe('/matching/retreats/partner/:partnerId (GET)', () => {
    it('should return matching retreats for a partner', () => {
      return request(app.getHttpServer())
        .get(`/matching/retreats/partner/${partnerId}`)
        .set('Authorization', `Bearer ${partnerToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('results');
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('executionTimeMs');
          expect(Array.isArray(res.body.results)).toBe(true);
        });
    });

    it('should return 404 if partner not found', () => {
      return request(app.getHttpServer())
        .get('/matching/retreats/partner/non-existent-id')
        .set('Authorization', `Bearer ${partnerToken}`)
        .expect(404);
    });
  });

  describe('/matching/score/:partnerId/:retreatId (GET)', () => {
    it('should return matching score between partner and retreat', () => {
      return request(app.getHttpServer())
        .get(`/matching/score/${partnerId}/${retreatId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('score');
          expect(res.body).toHaveProperty('compatibilityFactors');
          expect(typeof res.body.score).toBe('number');
        });
    });
  });

  describe('/matching/analytics/partner/:partnerId (GET)', () => {
    it('should return matching analytics for a partner', () => {
      return request(app.getHttpServer())
        .get(`/matching/analytics/partner/${partnerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('views');
          expect(res.body).toHaveProperty('contacts');
          expect(res.body).toHaveProperty('conversions');
          expect(res.body).toHaveProperty('conversionRate');
          expect(res.body).toHaveProperty('averageScore');
        });
    });

    it('should return 403 if not admin or partner owner', () => {
      return request(app.getHttpServer())
        .get(`/matching/analytics/partner/${partnerId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });
  });

  describe('/matching/recommendations/partner/:partnerId (GET)', () => {
    it('should return recommendations for a partner', () => {
      return request(app.getHttpServer())
        .get(`/matching/recommendations/partner/${partnerId}`)
        .set('Authorization', `Bearer ${partnerToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should return 403 if not admin or partner owner', () => {
      return request(app.getHttpServer())
        .get(`/matching/recommendations/partner/${partnerId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });
  });
});
