import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { ContentType } from '@prisma/client';
import { JwtAuthGuard } from '../src/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../src/modules/auth/guards/roles.guard';

describe('ModerationController (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let jwtToken: string;

  // Mock des guards pour les tests
  const mockJwtAuthGuard = { canActivate: jest.fn(() => true) };
  const mockRolesGuard = { canActivate: jest.fn(() => true) };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(RolesGuard)
      .useValue(mockRolesGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    
    prismaService = app.get<PrismaService>(PrismaService);
    
    // Nettoyer la base de données avant les tests
    await prismaService.report.deleteMany({});
    await prismaService.textModerationRule.deleteMany({});
    await prismaService.imageModerationRule.deleteMany({});
    
    // Créer des règles de modération pour les tests
    await prismaService.textModerationRule.create({
      data: {
        name: 'Test Rule',
        pattern: 'inappropriate',
        severity: 'MEDIUM',
        isActive: true,
      },
    });
    
    await prismaService.imageModerationRule.create({
      data: {
        name: 'Test Image Rule',
        category: 'nudity',
        threshold: 0.7,
        severity: 'HIGH',
        isActive: true,
      },
    });
    
    // Simuler un token JWT pour les tests
    jwtToken = 'test-jwt-token';
    
    await app.init();
  });

  afterAll(async () => {
    // Nettoyer la base de données après les tests
    await prismaService.report.deleteMany({});
    await prismaService.textModerationRule.deleteMany({});
    await prismaService.imageModerationRule.deleteMany({});
    
    await app.close();
  });

  describe('/moderation/content (POST)', () => {
    it('should moderate text content', () => {
      return request(app.getHttpServer())
        .post('/moderation/content')
        .query({ contentType: ContentType.TEXT })
        .send({ text: 'This is a test with inappropriate content' })
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('isInappropriate');
          expect(res.body).toHaveProperty('severity');
          expect(res.body).toHaveProperty('matchedRules');
          expect(res.body).toHaveProperty('confidence');
        });
    });

    it('should moderate image content', () => {
      return request(app.getHttpServer())
        .post('/moderation/content')
        .query({ contentType: ContentType.IMAGE })
        .send({ imageUrl: 'https://example.com/test-image.jpg' })
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('isInappropriate');
          expect(res.body).toHaveProperty('severity');
          expect(res.body).toHaveProperty('matchedRules');
          expect(res.body).toHaveProperty('categories');
          expect(res.body).toHaveProperty('confidence');
        });
    });
  });

  describe('/moderation/reports (GET)', () => {
    it('should get reports with filters', () => {
      return request(app.getHttpServer())
        .get('/moderation/reports')
        .query({ status: 'PENDING' })
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('reports');
          expect(res.body).toHaveProperty('total');
          expect(Array.isArray(res.body.reports)).toBe(true);
        });
    });
  });

  describe('/moderation/reports (POST)', () => {
    it('should create a new report', () => {
      const reportData = {
        contentType: ContentType.TEXT,
        contentId: '123',
        reporterId: '456',
        reason: 'Inappropriate content',
        description: 'This content contains inappropriate language',
      };

      return request(app.getHttpServer())
        .post('/moderation/reports')
        .send(reportData)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.contentType).toBe(reportData.contentType);
          expect(res.body.contentId).toBe(reportData.contentId);
          expect(res.body.reporterId).toBe(reportData.reporterId);
          expect(res.body.reason).toBe(reportData.reason);
          expect(res.body.description).toBe(reportData.description);
          expect(res.body.status).toBe('PENDING');
        });
    });
  });

  describe('/moderation/stats (GET)', () => {
    it('should get moderation statistics', () => {
      return request(app.getHttpServer())
        .get('/moderation/stats')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('pending');
          expect(res.body).toHaveProperty('inReview');
          expect(res.body).toHaveProperty('approved');
          expect(res.body).toHaveProperty('rejected');
          expect(res.body).toHaveProperty('escalated');
        });
    });
  });
});
