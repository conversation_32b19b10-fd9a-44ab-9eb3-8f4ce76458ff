import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import * as request from 'supertest';

// Configuration globale pour les tests E2E
let app: INestApplication;
let prisma: PrismaService;

beforeAll(async () => {
  // Configuration des variables d'environnement pour les tests E2E
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/retreat_and_be_e2e_test';
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-e2e-testing';
  process.env.REDIS_URL = 'redis://localhost:6379/2';
  process.env.PORT = '3001'; // Port différent pour éviter les conflits

  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [AppModule],
  }).compile();

  app = moduleFixture.createNestApplication();
  prisma = app.get<PrismaService>(PrismaService);

  await app.init();
});

// Augmenter le timeout pour les tests e2e
jest.setTimeout(60000);

// Supprimer les logs pendant les tests (garder les erreurs)
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: console.error, // Garder les erreurs pour le debugging
};

beforeEach(async () => {
  // Nettoyer la base de données avant chaque test
  if (prisma) {
    await cleanDatabase();
  }
});

afterAll(async () => {
  // Nettoyer et fermer les connexions
  if (prisma) {
    await cleanDatabase();
    await prisma.$disconnect();
  }
  if (app) {
    await app.close();
  }
});

// Helper pour nettoyer la base de données
async function cleanDatabase() {
  try {
    // Supprimer les données dans l'ordre pour respecter les contraintes FK
    await prisma.booking.deleteMany();
    await prisma.retreat.deleteMany();
    await prisma.user.deleteMany();
  } catch (error) {
    console.error('Erreur lors du nettoyage de la base de données:', error);
  }
}

// Nettoyer les mocks après chaque test
afterEach(() => {
  jest.clearAllMocks();
});

// Helper pour créer des données de test
export const createTestUser = async () => {
  if (!prisma) return null;
  return await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Test User',
      password: '$2b$10$hashedPasswordForTesting',
      role: 'USER',
    },
  });
};

// Export de l'app pour utilisation dans les tests
export { app, prisma };
