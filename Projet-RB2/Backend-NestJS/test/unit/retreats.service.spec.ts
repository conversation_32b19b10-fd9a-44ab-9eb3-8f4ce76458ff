import { Test, TestingModule } from '@nestjs/testing';
import { RetreatsService } from '../../src/modules/retreats/retreats.service';
import { PrismaService } from '../../src/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { Status, UserRole } from '../../src/modules/auth/decorators/roles.decorator';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';

describe('RetreatsService', () => {
  let service: RetreatsService;
  let prismaService: DeepMockProxy<PrismaService>;
  let configService: DeepMockProxy<ConfigService>;
  let eventEmitter: DeepMockProxy<EventEmitter2>;

  beforeEach(async () => {
    // Create mock instances
    prismaService = mockDeep<PrismaService>();
    configService = mockDeep<ConfigService>();
    eventEmitter = mockDeep<EventEmitter2>();

    // Configure mocks
    configService.get.mockImplementation((key: string) => {
      if (key === 'UPLOAD_DIR') return './uploads';
      return undefined;
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RetreatsService,
        { provide: PrismaService, useValue: prismaService },
        { provide: ConfigService, useValue: configService },
        { provide: EventEmitter2, useValue: eventEmitter },
      ],
    }).compile();

    service = module.get<RetreatsService>(RetreatsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a retreat successfully', async () => {
      // Arrange
      const userId = 'user-id';
      const createRetreatDto = {
        title: 'Test Retreat',
        description: 'A test retreat',
        location: 'Test Location',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        price: 1000,
        capacity: 20,
        status: Status.DRAFT,
        categories: ['yoga', 'meditation'],
        amenities: ['wifi', 'pool'],
        images: [],
      };

      const user = {
        id: userId,
        role: UserRole.HOST,
      };

      const createdRetreat = {
        id: 'retreat-id',
        ...createRetreatDto,
        hostId: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the prisma calls
      prismaService.user.findUnique.mockResolvedValue(user as any);
      prismaService.retreat.create.mockResolvedValue(createdRetreat as any);

      // Act
      const result = await service.create(createRetreatDto, userId);

      // Assert
      expect(result).toEqual(createdRetreat);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { id: true, role: true },
      });
      expect(prismaService.retreat.create).toHaveBeenCalledWith({
        data: {
          title: createRetreatDto.title,
          description: createRetreatDto.description,
          location: createRetreatDto.location,
          startDate: expect.any(Date),
          endDate: expect.any(Date),
          price: createRetreatDto.price,
          capacity: createRetreatDto.capacity,
          status: createRetreatDto.status,
          categories: createRetreatDto.categories,
          amenities: createRetreatDto.amenities,
          images: createRetreatDto.images,
          host: {
            connect: { id: userId },
          },
        },
      });
    });

    it('should throw NotFoundException if user not found', async () => {
      // Arrange
      const userId = 'non-existent-user-id';
      const createRetreatDto = {
        title: 'Test Retreat',
        description: 'A test retreat',
        location: 'Test Location',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        price: 1000,
        capacity: 20,
      };

      // Mock the prisma call to return null (user not found)
      prismaService.user.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(service.create(createRetreatDto as any, userId)).rejects.toThrow(NotFoundException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { id: true, role: true },
      });
    });

    it('should throw ForbiddenException if user role is not allowed', async () => {
      // Arrange
      const userId = 'user-id';
      const createRetreatDto = {
        title: 'Test Retreat',
        description: 'A test retreat',
        location: 'Test Location',
        startDate: '2023-12-01',
        endDate: '2023-12-10',
        price: 1000,
        capacity: 20,
      };

      const user = {
        id: userId,
        role: UserRole.USER, // Regular user role, not allowed to create retreats
      };

      // Mock the prisma call
      prismaService.user.findUnique.mockResolvedValue(user as any);

      // Act & Assert
      await expect(service.create(createRetreatDto as any, userId)).rejects.toThrow(ForbiddenException);
      expect(prismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { id: true, role: true },
      });
    });
  });

  describe('findAll', () => {
    it('should return paginated retreats', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        search: 'yoga',
        category: 'wellness',
      };

      const retreats = [
        {
          id: 'retreat-1',
          title: 'Yoga Retreat',
          description: 'A yoga retreat',
          status: Status.PUBLISHED,
          host: { id: 'host-1', firstName: 'John', lastName: 'Doe' },
        },
        {
          id: 'retreat-2',
          title: 'Meditation Retreat',
          description: 'A meditation retreat',
          status: Status.PUBLISHED,
          host: { id: 'host-2', firstName: 'Jane', lastName: 'Smith' },
        },
      ];

      const total = 2;

      // Mock the prisma calls
      prismaService.retreat.findMany.mockResolvedValue(retreats as any);
      prismaService.retreat.count.mockResolvedValue(total);

      // Act
      const result = await service.findAll(params);

      // Assert
      expect(result).toEqual({
        data: retreats,
        total,
        page: params.page,
        limit: params.limit,
      });
      expect(prismaService.retreat.findMany).toHaveBeenCalled();
      expect(prismaService.retreat.count).toHaveBeenCalled();
    });
  });

  // Add more test cases for other methods as needed
});
