# Guide de contribution - Système de recommandation Retreat And Be

Merci de votre intérêt pour contribuer au système de recommandation de Retreat And Be ! Ce document vous guidera à travers le processus de contribution et vous aidera à comprendre comment participer efficacement au projet.

## Table des matières

1. [Code de conduite](#code-de-conduite)
2. [Comment puis-je contribuer ?](#comment-puis-je-contribuer)
3. [Configuration de l'environnement de développement](#configuration-de-lenvironnement-de-développement)
4. [Structure du projet](#structure-du-projet)
5. [Workflow de développement](#workflow-de-développement)
6. [Standards de code](#standards-de-code)
7. [Tests](#tests)
8. [Documentation](#documentation)
9. [Soumission d'une pull request](#soumission-dune-pull-request)
10. [Processus de revue](#processus-de-revue)
11. [Ressources supplémentaires](#ressources-supplémentaires)

## Code de conduite

Ce projet adhère à un code de conduite qui garantit un environnement ouvert et accueillant pour tous les contributeurs. En participant, vous vous engagez à respecter ce code. Veuillez lire le [Code de conduite](CODE_OF_CONDUCT.md) avant de contribuer.

## Comment puis-je contribuer ?

Il existe plusieurs façons de contribuer au système de recommandation de Retreat And Be :

### Signaler des bugs

Si vous trouvez un bug, veuillez créer une issue en utilisant le modèle de bug. Assurez-vous d'inclure :

- Un titre clair et descriptif
- Une description détaillée du problème
- Les étapes pour reproduire le bug
- Le comportement attendu et le comportement observé
- Des captures d'écran si applicable
- Votre environnement (navigateur, système d'exploitation, etc.)

### Suggérer des améliorations

Si vous avez une idée pour améliorer le système de recommandation, veuillez créer une issue en utilisant le modèle de fonctionnalité. Assurez-vous d'inclure :

- Un titre clair et descriptif
- Une description détaillée de l'amélioration proposée
- Les avantages de cette amélioration
- Des exemples d'utilisation si applicable

### Contribuer du code

Si vous souhaitez contribuer du code, veuillez suivre ces étapes :

1. Recherchez une issue existante ou créez-en une nouvelle
2. Discutez de votre approche dans l'issue
3. Forkez le dépôt
4. Créez une branche pour votre contribution
5. Développez votre fonctionnalité ou correction
6. Ajoutez des tests
7. Mettez à jour la documentation
8. Soumettez une pull request

## Configuration de l'environnement de développement

### Prérequis

- Node.js 14+
- npm 6+
- PostgreSQL 12+
- Docker et Docker Compose (pour l'infrastructure ML et le monitoring)

### Installation

1. Clonez le dépôt :
   ```bash
   git clone https://github.com/retreat-and-be/recommendation-system.git
   cd recommendation-system
   ```

2. Installez les dépendances :
   ```bash
   # Backend
   cd Backend-NestJS
   npm install

   # Frontend
   cd ../Front-Audrey-V1-Main-main
   npm install
   ```

3. Configurez l'environnement :
   ```bash
   # Backend
   cd Backend-NestJS
   cp .env.example .env
   # Modifiez le fichier .env avec vos paramètres

   # Frontend
   cd ../Front-Audrey-V1-Main-main
   cp .env.example .env
   # Modifiez le fichier .env avec vos paramètres
   ```

4. Démarrez la base de données :
   ```bash
   # Si vous utilisez Docker
   docker-compose up -d postgres
   ```

5. Exécutez les migrations :
   ```bash
   cd Backend-NestJS
   npx prisma migrate dev
   ```

6. Démarrez les services :
   ```bash
   # Backend
   cd Backend-NestJS
   npm run start:dev

   # Frontend
   cd ../Front-Audrey-V1-Main-main
   npm start
   ```

## Structure du projet

Le projet est organisé comme suit :

```
Projet-RB2/
├── Backend-NestJS/              # Backend du système de recommandation
│   ├── src/                     # Code source
│   │   ├── modules/             # Modules NestJS
│   │   │   ├── recommendation/  # Module de recommandation
│   │   │   ├── explanation/     # Module d'explication
│   │   │   ├── feedback/        # Module de feedback
│   │   │   ├── moderation/      # Module de modération
│   │   │   └── analytics/       # Module d'analytics
│   │   ├── common/              # Code partagé
│   │   └── main.ts              # Point d'entrée
│   ├── prisma/                  # Schéma Prisma et migrations
│   ├── test/                    # Tests
│   ├── scripts/                 # Scripts utilitaires
│   ├── docs/                    # Documentation
│   └── monitoring/              # Configuration de monitoring
├── Front-Audrey-V1-Main-main/   # Frontend de la plateforme
│   ├── src/                     # Code source
│   │   ├── components/          # Composants React
│   │   ├── pages/               # Pages React
│   │   ├── services/            # Services API
│   │   └── utils/               # Utilitaires
│   └── public/                  # Fichiers statiques
└── docs/                        # Documentation générale
```

## Workflow de développement

Nous suivons un workflow basé sur les branches pour le développement :

1. **Branches principales** :
   - `main` : Code en production
   - `develop` : Code en développement

2. **Branches de fonctionnalités** :
   - `feature/nom-de-la-fonctionnalité` : Pour les nouvelles fonctionnalités
   - `bugfix/nom-du-bug` : Pour les corrections de bugs
   - `docs/nom-de-la-documentation` : Pour les mises à jour de documentation
   - `refactor/nom-du-refactoring` : Pour les refactorisations

3. **Workflow** :
   - Créez une branche à partir de `develop`
   - Développez votre fonctionnalité ou correction
   - Soumettez une pull request vers `develop`
   - Après revue et approbation, votre code sera fusionné dans `develop`
   - Périodiquement, `develop` est fusionné dans `main` pour les releases

## Standards de code

Nous utilisons les standards de code suivants :

### TypeScript

- Nous suivons les règles ESLint configurées dans le projet
- Utilisez des types explicites plutôt que `any`
- Préférez les fonctions fléchées pour les fonctions anonymes
- Utilisez async/await plutôt que les promesses chaînées

### NestJS

- Suivez l'architecture modulaire de NestJS
- Utilisez l'injection de dépendances
- Créez des services pour la logique métier
- Utilisez des DTOs pour la validation des données

### React

- Utilisez des composants fonctionnels avec des hooks
- Suivez le pattern de présentation/conteneur
- Utilisez des props typées
- Évitez les effets de bord dans les composants

### Tests

- Écrivez des tests unitaires pour chaque fonctionnalité
- Écrivez des tests d'intégration pour les flux importants
- Maintenez une couverture de tests d'au moins 80%

## Tests

Nous utilisons Jest pour les tests unitaires et d'intégration.

### Exécution des tests

```bash
# Tests unitaires
npm run test

# Tests avec couverture
npm run test:cov

# Tests e2e
npm run test:e2e

# Tests de performance
./scripts/run-performance-tests.sh
```

### Écriture des tests

- Chaque module doit avoir des tests unitaires
- Les services doivent être testés de manière isolée avec des mocks
- Les contrôleurs doivent être testés avec des requêtes simulées
- Les tests d'intégration doivent couvrir les flux complets

## Documentation

La documentation est essentielle pour maintenir le projet accessible à tous les contributeurs.

### Documentation du code

- Utilisez JSDoc pour documenter les fonctions et les classes
- Documentez les paramètres, les valeurs de retour et les exceptions
- Expliquez le "pourquoi" plutôt que le "quoi"

### Documentation du projet

- Mettez à jour le README.md avec les nouvelles fonctionnalités
- Documentez les API dans le dossier `docs/`
- Créez des guides d'utilisation pour les nouvelles fonctionnalités

## Soumission d'une pull request

Lorsque vous soumettez une pull request, veuillez suivre ces étapes :

1. Assurez-vous que votre code respecte les standards du projet
2. Exécutez les tests et assurez-vous qu'ils passent
3. Mettez à jour la documentation si nécessaire
4. Remplissez le modèle de pull request avec toutes les informations requises
5. Référencez l'issue que votre pull request résout
6. Demandez une revue à au moins un membre de l'équipe

## Processus de revue

Le processus de revue de code est conçu pour maintenir la qualité du code et partager les connaissances :

1. Au moins un membre de l'équipe doit approuver votre pull request
2. Les commentaires de revue doivent être adressés avant la fusion
3. Les tests automatisés doivent passer
4. Le code doit respecter les standards du projet
5. La documentation doit être à jour

## Ressources supplémentaires

- [Documentation NestJS](https://docs.nestjs.com/)
- [Documentation React](https://reactjs.org/docs/getting-started.html)
- [Documentation Prisma](https://www.prisma.io/docs/)
- [Documentation du système de recommandation](RECOMMENDATION-SYSTEM.md)
- [Roadmap du système de recommandation](recommendation_roadmap.md)

---

Merci de contribuer au système de recommandation de Retreat And Be ! Votre aide est précieuse pour améliorer l'expérience de nos utilisateurs.
