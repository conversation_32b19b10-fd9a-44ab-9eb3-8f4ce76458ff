module.exports = {
  semi: true, // Ajoute des points-virgules à la fin des instructions
  trailingComma: 'es5', // Ajoute des virgules finales là où c'est valide en ES5 (objets, tableaux, etc.)
  singleQuote: true, // Utilise des guillemets simples au lieu de guillemets doubles
  printWidth: 100, // Spécifie la largeur de ligne que Prettier essaiera de respecter
  tabWidth: 2, // Spécifie le nombre d'espaces par niveau d'indentation
  useTabs: false, // Indente avec des espaces au lieu de tabulations
  jsxSingleQuote: true, // Utilise des guillemets simples dans le JSX
  bracketSpacing: true, // Ajoute des espaces à l'intérieur des accolades dans les littéraux d'objets ({ foo: bar })
  arrowParens: 'always', // Inclut toujours des parenthèses autour des paramètres de fonction fléchée (ex: (x) => x)
}; 