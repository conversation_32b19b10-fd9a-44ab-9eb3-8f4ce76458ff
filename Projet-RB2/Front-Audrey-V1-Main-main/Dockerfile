# Stage 1: Build the application
FROM node:18-alpine as build

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy all files
COPY . .

# Set environment variables for the build
ARG REACT_APP_API_URL
ENV REACT_APP_API_URL=${REACT_APP_API_URL:-https://api.retreat-and-be.com}

ARG REACT_APP_SECURITY_URL
ENV REACT_APP_SECURITY_URL=${REACT_APP_SECURITY_URL:-https://api.retreat-and-be.com/security}

ARG REACT_APP_AGENT_IA_URL
ENV REACT_APP_AGENT_IA_URL=${REACT_APP_AGENT_IA_URL:-https://api.retreat-and-be.com/agent-ia}

ARG REACT_APP_SOCIAL_URL
ENV REACT_APP_SOCIAL_URL=${REACT_APP_SOCIAL_URL:-https://api.retreat-and-be.com/social}

ARG REACT_APP_FINANCIAL_URL
ENV REACT_APP_FINANCIAL_URL=${REACT_APP_FINANCIAL_URL:-https://api.retreat-and-be.com/financial}

# Build the application
RUN npm run build

# Stage 2: Serve the application with Nginx
FROM nginx:alpine

# Copy the build output
COPY --from=build /app/build /usr/share/nginx/html

# Copy custom nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget -q --spider http://localhost/ || exit 1

# Expose port 80
EXPOSE 80

# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]
