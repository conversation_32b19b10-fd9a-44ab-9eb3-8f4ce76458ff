# Chatbot Retreat And Be

Ce document décrit l'implémentation du chatbot Retreat And Be, qui intègre le frontend Front-Audrey-V1-Main-main avec le microservice superagent.

## Fonctionnalités

Le chatbot Retreat And Be offre les fonctionnalités suivantes :

- **Authentification** : Intégration avec le système d'authentification de l'application
- **Historique des conversations** : Stockage local des conversations pour une reprise ultérieure
- **Types de messages riches** : Support pour différents types de messages (texte, boutons, images, etc.)
- **Analytiques** : Suivi des interactions utilisateur pour améliorer l'expérience
- **Entrée vocale** : Reconnaissance vocale pour une interaction mains libres

## Architecture

L'architecture du chatbot est composée des éléments suivants :

### Services

- `chatbotService.ts` : Service pour communiquer avec l'API du chatbot
- `chatbotStorageService.ts` : Service pour gérer le stockage local des conversations
- `chatbotAnalyticsService.ts` : Service pour suivre les interactions utilisateur

### Hooks

- `useChatbot.ts` : Hook React pour gérer l'état du chatbot et exposer les méthodes d'interaction

### Composants

- `ChatBot.tsx` : Composant principal qui affiche le bouton flottant
- `ChatWindow.tsx` : Fenêtre de chat avec historique des messages et formulaire d'envoi
- `MessageContent.tsx` : Composant pour afficher différents types de messages
- `ConversationList.tsx` : Liste des conversations enregistrées

## Installation

### Prérequis

- Node.js 14+
- React 18+
- Microservice superagent en cours d'exécution

### Configuration

1. Copiez le fichier `.env.example` vers `.env` :

```bash
cp .env.example .env
```

2. Configurez les variables d'environnement :

```
REACT_APP_SUPERAGENT_URL=http://localhost:8001
REACT_APP_ENABLE_ANALYTICS=true
```

## Utilisation

### Intégration dans une page

Le chatbot est déjà intégré dans l'application via le composant `ChatBot` qui est importé dans le composant racine de l'application.

### Personnalisation

Vous pouvez personnaliser l'apparence et le comportement du chatbot en modifiant les composants correspondants.

## Types de messages

Le chatbot prend en charge les types de messages suivants :

- **Texte** : Messages textuels simples
- **Boutons** : Messages avec des boutons cliquables
- **Images** : Messages contenant des images
- **Réponses rapides** : Suggestions de réponses rapides
- **Cartes** : Messages formatés sous forme de cartes
- **Carrousel** : Séries de cartes défilantes
- **Audio** : Messages audio
- **Vidéo** : Messages vidéo
- **Fichiers** : Messages contenant des fichiers à télécharger
- **Localisation** : Messages contenant des informations de localisation

## Stockage local

Les conversations sont stockées localement dans le navigateur de l'utilisateur à l'aide de `localStorage`. Cela permet de :

- Reprendre les conversations interrompues
- Conserver l'historique des conversations
- Fonctionner hors ligne (partiellement)

## Analytiques

Le service d'analytiques suit les événements suivants :

- Messages envoyés
- Messages reçus
- Conversations démarrées
- Conversations terminées
- Boutons cliqués
- Erreurs survenues
- Feedback donné

## Reconnaissance vocale

La reconnaissance vocale utilise l'API Web Speech pour permettre aux utilisateurs d'envoyer des messages vocaux. Cette fonctionnalité :

- Est disponible uniquement sur les navigateurs compatibles
- Prend en charge le français par défaut
- Convertit automatiquement la parole en texte

## Sécurité

Le chatbot implémente les mesures de sécurité suivantes :

- Authentification par token JWT
- Rafraîchissement automatique des tokens expirés
- Gestion des erreurs d'authentification

## Dépannage

### Le chatbot ne se connecte pas

- Vérifiez que le microservice superagent est en cours d'exécution
- Vérifiez que l'URL du microservice est correctement configurée
- Vérifiez que l'utilisateur est correctement authentifié

### Les messages ne sont pas envoyés

- Vérifiez la console du navigateur pour détecter d'éventuelles erreurs
- Vérifiez que l'utilisateur est authentifié si l'authentification est requise
- Vérifiez les logs du microservice pour détecter d'éventuelles erreurs de traitement des messages

### La reconnaissance vocale ne fonctionne pas

- Vérifiez que votre navigateur prend en charge l'API Web Speech
- Vérifiez que vous avez accordé les autorisations d'accès au microphone
- Essayez de rafraîchir la page

## Développement futur

Voici quelques idées pour améliorer le chatbot à l'avenir :

- Synchronisation des conversations avec le serveur
- Support pour plus de types de messages
- Amélioration de l'interface utilisateur
- Intégration avec d'autres services de l'application
- Support multilingue
