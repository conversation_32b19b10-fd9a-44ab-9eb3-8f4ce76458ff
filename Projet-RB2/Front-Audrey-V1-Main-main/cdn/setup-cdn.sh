#!/bin/bash

# Script de configuration du CDN pour Front-Audrey-V1-Main-main
# Ce script configure CloudFront pour le frontend

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si AWS CLI est installé
  if ! command -v aws &> /dev/null; then
    error "AWS CLI n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si jq est installé
  if ! command -v jq &> /dev/null; then
    error "jq n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si le profil AWS est configuré
  if ! aws configure list &> /dev/null; then
    error "Profil AWS non configuré. Veuillez configurer AWS CLI avant de continuer."
    exit 1
  fi
  
  success "Tous les prérequis sont installés."
}

# Configurer le certificat ACM
configure_certificate() {
  log "Configuration du certificat ACM..."
  
  # Demander le domaine
  read -p "Entrez le domaine pour le certificat (par défaut: app.retreat-and-be.com): " DOMAIN
  DOMAIN=${DOMAIN:-app.retreat-and-be.com}
  
  # Vérifier si le certificat existe déjà
  CERTIFICATE_ARN=$(aws acm list-certificates --region us-east-1 | jq -r --arg DOMAIN "$DOMAIN" '.CertificateSummaryList[] | select(.DomainName==$DOMAIN) | .CertificateArn')
  
  if [ -n "$CERTIFICATE_ARN" ]; then
    log "Certificat existant trouvé: $CERTIFICATE_ARN"
  else
    log "Demande d'un nouveau certificat pour $DOMAIN..."
    
    # Demander un nouveau certificat
    CERTIFICATE_ARN=$(aws acm request-certificate --domain-name $DOMAIN --validation-method DNS --region us-east-1 | jq -r '.CertificateArn')
    
    if [ -z "$CERTIFICATE_ARN" ]; then
      error "Erreur lors de la demande du certificat."
      exit 1
    fi
    
    log "Certificat demandé: $CERTIFICATE_ARN"
    log "Veuillez valider le certificat en ajoutant les enregistrements DNS requis."
    
    # Attendre la validation du certificat
    log "Attente de la validation du certificat..."
    aws acm wait certificate-validated --certificate-arn $CERTIFICATE_ARN --region us-east-1
    
    if [ $? -ne 0 ]; then
      error "Erreur lors de la validation du certificat."
      exit 1
    fi
  fi
  
  success "Certificat configuré avec succès: $CERTIFICATE_ARN"
  
  # Mettre à jour le fichier de configuration CloudFront
  log "Mise à jour du fichier de configuration CloudFront..."
  sed -i "s|arn:aws:acm:us-east-1:ACCOUNT_ID:certificate/CERTIFICATE_ID|$CERTIFICATE_ARN|g" cloudfront-distribution.json
  
  success "Fichier de configuration CloudFront mis à jour avec succès."
}

# Créer la distribution CloudFront
create_cloudfront_distribution() {
  log "Création de la distribution CloudFront..."
  
  # Demander le domaine d'origine
  read -p "Entrez le domaine d'origine (par défaut: app.retreat-and-be.com): " ORIGIN_DOMAIN
  ORIGIN_DOMAIN=${ORIGIN_DOMAIN:-app.retreat-and-be.com}
  
  # Mettre à jour le domaine d'origine dans le fichier de configuration
  sed -i "s|\"DomainName\": \"app.retreat-and-be.com\"|\"DomainName\": \"$ORIGIN_DOMAIN\"|g" cloudfront-distribution.json
  
  # Créer la distribution CloudFront
  DISTRIBUTION_ID=$(aws cloudfront create-distribution --distribution-config file://cloudfront-distribution.json | jq -r '.Distribution.Id')
  
  if [ -z "$DISTRIBUTION_ID" ]; then
    error "Erreur lors de la création de la distribution CloudFront."
    exit 1
  fi
  
  log "Distribution CloudFront créée: $DISTRIBUTION_ID"
  
  # Attendre que la distribution soit déployée
  log "Attente du déploiement de la distribution CloudFront..."
  aws cloudfront wait distribution-deployed --id $DISTRIBUTION_ID
  
  if [ $? -ne 0 ]; then
    warn "Délai d'attente dépassé pour le déploiement de la distribution CloudFront."
    log "Le déploiement peut prendre jusqu'à 15 minutes. Veuillez vérifier l'état de la distribution ultérieurement."
  fi
  
  # Obtenir le domaine CloudFront
  CLOUDFRONT_DOMAIN=$(aws cloudfront get-distribution --id $DISTRIBUTION_ID | jq -r '.Distribution.DomainName')
  
  success "Distribution CloudFront créée avec succès: $CLOUDFRONT_DOMAIN"
  log "ID de la distribution: $DISTRIBUTION_ID"
}

# Configurer Route 53
configure_route53() {
  log "Configuration de Route 53..."
  
  # Demander le domaine
  read -p "Entrez le domaine pour Route 53 (par défaut: cdn.retreat-and-be.com): " DOMAIN
  DOMAIN=${DOMAIN:-cdn.retreat-and-be.com}
  
  # Demander l'ID de la zone hébergée
  read -p "Entrez l'ID de la zone hébergée Route 53: " HOSTED_ZONE_ID
  
  if [ -z "$HOSTED_ZONE_ID" ]; then
    error "ID de la zone hébergée non spécifié."
    exit 1
  fi
  
  # Obtenir le domaine CloudFront
  read -p "Entrez le domaine CloudFront: " CLOUDFRONT_DOMAIN
  
  if [ -z "$CLOUDFRONT_DOMAIN" ]; then
    error "Domaine CloudFront non spécifié."
    exit 1
  fi
  
  # Créer l'enregistrement CNAME
  aws route53 change-resource-record-sets --hosted-zone-id $HOSTED_ZONE_ID --change-batch '{
    "Changes": [
      {
        "Action": "UPSERT",
        "ResourceRecordSet": {
          "Name": "'$DOMAIN'",
          "Type": "CNAME",
          "TTL": 300,
          "ResourceRecords": [
            {
              "Value": "'$CLOUDFRONT_DOMAIN'"
            }
          ]
        }
      }
    ]
  }'
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la création de l'enregistrement CNAME dans Route 53."
    exit 1
  fi
  
  success "Enregistrement CNAME créé avec succès dans Route 53."
  log "Domaine CDN: $DOMAIN"
}

# Mettre à jour la configuration Nginx
update_nginx_config() {
  log "Mise à jour de la configuration Nginx..."
  
  # Demander le domaine CDN
  read -p "Entrez le domaine CDN (par défaut: cdn.retreat-and-be.com): " CDN_DOMAIN
  CDN_DOMAIN=${CDN_DOMAIN:-cdn.retreat-and-be.com}
  
  # Créer un fichier de configuration Nginx pour le CDN
  cat > ../nginx-cdn.conf << EOF
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Enable gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_comp_level 6;
    gzip_min_length 1000;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://$CDN_DOMAIN; font-src 'self' data:; connect-src 'self' https://api.retreat-and-be.com;";

    # CDN configuration
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg)$ {
        expires 1y;
        add_header Cache-Control "public, no-transform, immutable";
        try_files \$uri @cdn;
    }

    location @cdn {
        return 301 https://$CDN_DOMAIN\$request_uri;
    }

    # Handle SPA routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
    }
}
EOF
  
  success "Configuration Nginx mise à jour avec succès."
  log "Fichier de configuration: nginx-cdn.conf"
}

# Menu principal
main() {
  echo "================================================"
  echo "  Configuration du CDN pour Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Configurer le certificat ACM
  configure_certificate
  
  # Créer la distribution CloudFront
  create_cloudfront_distribution
  
  # Configurer Route 53
  configure_route53
  
  # Mettre à jour la configuration Nginx
  update_nginx_config
  
  success "Configuration du CDN terminée avec succès!"
}

# Exécuter le script
main
