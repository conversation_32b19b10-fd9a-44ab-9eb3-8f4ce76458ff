/**
 * Tests E2E - Authentification Login - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests complets du processus de connexion utilisateur
 * avec validation des cas d'erreur et de succès.
 */

describe('Authentication - Login Flow', () => {
  beforeEach(() => {
    // Intercepter les appels API d'authentification
    cy.intercept('POST', '/api/auth/login').as('loginRequest');
    cy.intercept('GET', '/api/user/profile').as('userProfile');
    
    cy.visit('/auth/login');
  });

  describe('Page Login - UI/UX', () => {
    it('should display login form correctly', () => {
      // Vérifier la présence du logo
      cy.get('[data-cy="logo"]').should('be.visible');
      
      // Vérifier le titre
      cy.get('h2').should('contain.text', 'Connexion');
      
      // Vérifier les champs du formulaire
      cy.get('[data-cy="email-input"]').should('be.visible').and('have.attr', 'type', 'email');
      cy.get('[data-cy="password-input"]').should('be.visible').and('have.attr', 'type', 'password');
      cy.get('[data-cy="login-button"]').should('be.visible').and('contain.text', 'Se connecter');
      
      // Vérifier les liens
      cy.get('[data-cy="forgot-password-link"]').should('be.visible');
      cy.get('[data-cy="register-link"]').should('be.visible');
    });

    it('should have proper form validation', () => {
      // Tenter de soumettre sans données
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier les messages d'erreur de validation
      cy.get('[data-cy="email-error"]').should('contain.text', 'Email requis');
      cy.get('[data-cy="password-error"]').should('contain.text', 'Mot de passe requis');
      
      // Tester avec un email invalide
      cy.get('[data-cy="email-input"]').type('invalid-email');
      cy.get('[data-cy="login-button"]').click();
      cy.get('[data-cy="email-error"]').should('contain.text', 'Email invalide');
      
      // Tester avec un mot de passe trop court
      cy.get('[data-cy="email-input"]').clear().type('<EMAIL>');
      cy.get('[data-cy="password-input"]').type('123');
      cy.get('[data-cy="login-button"]').click();
      cy.get('[data-cy="password-error"]').should('contain.text', 'Mot de passe trop court');
    });

    it('should be accessible', () => {
      // Tests d'accessibilité
      cy.checkA11y();
      
      // Navigation au clavier
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-cy', 'email-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-cy', 'password-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-cy', 'login-button');
    });
  });

  describe('Login Success Flow', () => {
    it('should login successfully with valid credentials', () => {
      // Mocker une réponse de succès
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 200,
        body: {
          success: true,
          token: 'mock-jwt-token',
          user: {
            id: '1',
            name: 'Test User',
            email: '<EMAIL>',
            role: 'user'
          }
        }
      }).as('successLogin');

      // Remplir le formulaire
      cy.get('[data-cy="email-input"]').type(Cypress.env('testUser.email'));
      cy.get('[data-cy="password-input"]').type(Cypress.env('testUser.password'));
      
      // Soumettre
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier l'appel API
      cy.wait('@successLogin').then((interception) => {
        expect(interception.request.body).to.deep.include({
          email: Cypress.env('testUser.email'),
          password: Cypress.env('testUser.password')
        });
      });
      
      // Vérifier la redirection
      cy.url().should('include', '/app/dashboard');
      
      // Vérifier que l'utilisateur est connecté
      cy.get('[data-cy="user-menu"]').should('contain', 'Test User');
      
      // Vérifier le toast de succès
      cy.checkToast('Connexion réussie', 'success');
    });

    it('should remember user session', () => {
      // Se connecter
      cy.login();
      
      // Recharger la page
      cy.reload();
      
      // Vérifier que l'utilisateur est toujours connecté
      cy.url().should('include', '/app/dashboard');
      cy.get('[data-cy="user-menu"]').should('be.visible');
    });

    it('should redirect to intended page after login', () => {
      // Tenter d'accéder à une page protégée
      cy.visit('/app/retreats');
      
      // Devrait être redirigé vers login
      cy.url().should('include', '/auth/login');
      
      // Se connecter
      cy.login();
      
      // Devrait être redirigé vers la page initialement demandée
      cy.url().should('include', '/app/retreats');
    });
  });

  describe('Login Error Handling', () => {
    it('should handle invalid credentials', () => {
      // Mocker une réponse d'erreur
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 401,
        body: {
          success: false,
          message: 'Identifiants invalides'
        }
      }).as('failedLogin');

      // Remplir avec de mauvais identifiants
      cy.get('[data-cy="email-input"]').type('<EMAIL>');
      cy.get('[data-cy="password-input"]').type('wrongpassword');
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier l'appel API
      cy.wait('@failedLogin');
      
      // Vérifier que l'utilisateur reste sur la page de login
      cy.url().should('include', '/auth/login');
      
      // Vérifier le message d'erreur
      cy.get('[data-cy="error-message"]').should('contain.text', 'Identifiants invalides');
      
      // Vérifier le toast d'erreur
      cy.checkToast('Identifiants invalides', 'error');
    });

    it('should handle server errors', () => {
      // Mocker une erreur serveur
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 500,
        body: {
          success: false,
          message: 'Erreur serveur'
        }
      }).as('serverError');

      cy.get('[data-cy="email-input"]').type(Cypress.env('testUser.email'));
      cy.get('[data-cy="password-input"]').type(Cypress.env('testUser.password'));
      cy.get('[data-cy="login-button"]').click();
      
      cy.wait('@serverError');
      
      // Vérifier le message d'erreur générique
      cy.get('[data-cy="error-message"]').should('contain.text', 'Une erreur est survenue');
      cy.checkToast('Une erreur est survenue', 'error');
    });

    it('should handle network errors', () => {
      // Simuler une erreur réseau
      cy.simulateNetworkError();

      cy.get('[data-cy="email-input"]').type(Cypress.env('testUser.email'));
      cy.get('[data-cy="password-input"]').type(Cypress.env('testUser.password'));
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier le message d'erreur réseau
      cy.get('[data-cy="error-message"]').should('contain.text', 'Problème de connexion');
      cy.checkToast('Problème de connexion', 'error');
    });

    it('should handle account locked', () => {
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 423,
        body: {
          success: false,
          message: 'Compte temporairement verrouillé'
        }
      }).as('lockedAccount');

      cy.get('[data-cy="email-input"]').type(Cypress.env('testUser.email'));
      cy.get('[data-cy="password-input"]').type('wrongpassword');
      cy.get('[data-cy="login-button"]').click();
      
      cy.wait('@lockedAccount');
      
      cy.get('[data-cy="error-message"]').should('contain.text', 'Compte temporairement verrouillé');
      cy.checkToast('Compte temporairement verrouillé', 'warning');
    });
  });

  describe('Login Loading States', () => {
    it('should show loading state during login', () => {
      // Mocker une réponse lente
      cy.intercept('POST', '/api/auth/login', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({
            statusCode: 200,
            body: { success: true, token: 'token', user: {} }
          });
        });
      }).as('slowLogin');

      cy.get('[data-cy="email-input"]').type(Cypress.env('testUser.email'));
      cy.get('[data-cy="password-input"]').type(Cypress.env('testUser.password'));
      cy.get('[data-cy="login-button"]').click();
      
      // Vérifier l'état de chargement
      cy.get('[data-cy="login-button"]').should('be.disabled');
      cy.get('[data-cy="login-button"]').should('contain.text', 'Connexion...');
      cy.get('[data-cy="loading-spinner"]').should('be.visible');
      
      // Attendre la fin du chargement
      cy.wait('@slowLogin');
      cy.url().should('include', '/app/dashboard');
    });
  });

  describe('Navigation Links', () => {
    it('should navigate to forgot password', () => {
      cy.get('[data-cy="forgot-password-link"]').click();
      cy.url().should('include', '/auth/forgot-password');
    });

    it('should navigate to register', () => {
      cy.get('[data-cy="register-link"]').click();
      cy.url().should('include', '/auth/register');
    });

    it('should navigate to home page', () => {
      cy.get('[data-cy="logo"]').click();
      cy.url().should('eq', Cypress.config().baseUrl + '/');
    });
  });

  describe('Responsive Design', () => {
    it('should work on mobile devices', () => {
      cy.viewport('iphone-x');
      
      // Vérifier que le formulaire est toujours utilisable
      cy.get('[data-cy="email-input"]').should('be.visible');
      cy.get('[data-cy="password-input"]').should('be.visible');
      cy.get('[data-cy="login-button"]').should('be.visible');
      
      // Tester la connexion sur mobile
      cy.login();
      cy.url().should('include', '/app/dashboard');
    });

    it('should work on tablet devices', () => {
      cy.viewport('ipad-2');
      
      cy.get('[data-cy="email-input"]').should('be.visible');
      cy.get('[data-cy="password-input"]').should('be.visible');
      cy.get('[data-cy="login-button"]').should('be.visible');
    });
  });

  describe('Security Features', () => {
    it('should not expose sensitive data in DOM', () => {
      cy.get('[data-cy="password-input"]').type('secretpassword');
      
      // Vérifier que le mot de passe n'est pas visible dans le DOM
      cy.get('[data-cy="password-input"]').should('have.attr', 'type', 'password');
      cy.get('body').should('not.contain.text', 'secretpassword');
    });

    it('should clear form on navigation away', () => {
      cy.get('[data-cy="email-input"]').type('<EMAIL>');
      cy.get('[data-cy="password-input"]').type('password123');
      
      // Naviguer vers une autre page
      cy.get('[data-cy="register-link"]').click();
      
      // Revenir à la page de login
      cy.visit('/auth/login');
      
      // Vérifier que les champs sont vides
      cy.get('[data-cy="email-input"]').should('have.value', '');
      cy.get('[data-cy="password-input"]').should('have.value', '');
    });
  });
});
