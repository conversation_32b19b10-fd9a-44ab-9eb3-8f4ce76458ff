/**
 * Tests E2E - Contact Professionnels - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Tests complets du processus de contact et interaction
 * avec les professionnels du bien-être.
 */

describe('Professional Contact Flow', () => {
  beforeEach(() => {
    // Intercepter les appels API
    cy.intercept('GET', '/api/professionals*').as('getProfessionals');
    cy.intercept('GET', '/api/professionals/*').as('getProfessional');
    cy.intercept('POST', '/api/messages').as('sendMessage');
    cy.intercept('POST', '/api/appointments').as('bookAppointment');
    
    // Se connecter avant chaque test
    cy.login();
  });

  describe('Professional Discovery', () => {
    it('should display professionals on the main page', () => {
      cy.visitProfessionals();
      
      // Vérifier que la page se charge
      cy.wait('@getProfessionals');
      cy.get('[data-cy="professional-list"]').should('be.visible');
      
      // Vérifier qu'au moins un professionnel est affiché
      cy.get('[data-cy^="professional-card-"]').should('have.length.at.least', 1);
      
      // Vérifier les informations essentielles sur chaque carte
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="professional-name"]').should('be.visible');
        cy.get('[data-cy="professional-title"]').should('be.visible');
        cy.get('[data-cy="professional-specialties"]').should('be.visible');
        cy.get('[data-cy="professional-rating"]').should('be.visible');
        cy.get('[data-cy="professional-price"]').should('be.visible');
        cy.get('[data-cy="contact-button"]').should('be.visible');
        cy.get('[data-cy="view-profile-button"]').should('be.visible');
      });
    });

    it('should search professionals by specialty', () => {
      cy.visitProfessionals();
      
      // Rechercher "yoga"
      cy.get('[data-cy="search-input"]').type('yoga');
      cy.get('[data-cy="search-button"]').click();
      cy.wait('@getProfessionals');
      
      // Vérifier que les résultats contiennent la spécialité
      cy.get('[data-cy="search-results"]').should('be.visible');
      cy.get('[data-cy^="professional-card-"]').each(($card) => {
        cy.wrap($card).find('[data-cy="professional-specialties"]').should('contain.text', 'Yoga');
      });
    });

    it('should filter professionals by availability', () => {
      cy.visitProfessionals();
      
      // Ouvrir les filtres
      cy.get('[data-cy="filters-toggle"]').click();
      cy.get('[data-cy="filters-panel"]').should('be.visible');
      
      // Filtrer par disponibilité
      cy.get('[data-cy="available-only-checkbox"]').check();
      cy.get('[data-cy="apply-filters"]').click();
      
      cy.wait('@getProfessionals');
      
      // Vérifier que tous les résultats sont disponibles
      cy.get('[data-cy^="professional-card-"]').each(($card) => {
        cy.wrap($card).find('[data-cy="availability-status"]').should('contain.text', 'Disponible');
      });
    });

    it('should filter professionals by price range', () => {
      cy.visitProfessionals();
      
      cy.get('[data-cy="filters-toggle"]').click();
      
      // Définir une fourchette de prix
      cy.get('[data-cy="price-min"]').clear().type('50');
      cy.get('[data-cy="price-max"]').clear().type('100');
      cy.get('[data-cy="apply-filters"]').click();
      
      cy.wait('@getProfessionals');
      
      // Vérifier que tous les résultats sont dans la fourchette
      cy.get('[data-cy^="professional-card-"]').each(($card) => {
        cy.wrap($card).find('[data-cy="professional-price"]').then(($price) => {
          const price = parseInt($price.text().replace(/[^\d]/g, ''));
          expect(price).to.be.within(50, 100);
        });
      });
    });

    it('should sort professionals by rating', () => {
      cy.visitProfessionals();
      
      // Trier par note décroissante
      cy.get('[data-cy="sort-select"]').select('rating-desc');
      cy.wait('@getProfessionals');
      
      // Vérifier l'ordre des notes
      cy.get('[data-cy^="professional-card-"]').then(($cards) => {
        const ratings = [];
        $cards.each((index, card) => {
          const ratingText = Cypress.$(card).find('[data-cy="professional-rating"]').text();
          const rating = parseFloat(ratingText.replace(/[^\d.]/g, ''));
          ratings.push(rating);
        });
        
        // Vérifier que les notes sont en ordre décroissant
        for (let i = 1; i < ratings.length; i++) {
          expect(ratings[i]).to.be.at.most(ratings[i - 1]);
        }
      });
    });
  });

  describe('Professional Profile', () => {
    it('should display professional details when clicked', () => {
      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      // Cliquer sur le premier professionnel
      cy.get('[data-cy^="professional-card-"]').first().click();
      cy.wait('@getProfessional');
      
      // Vérifier que la page de profil s'affiche
      cy.url().should('include', '/professionals/');
      cy.get('[data-cy="professional-profile"]').should('be.visible');
      
      // Vérifier les informations détaillées
      cy.get('[data-cy="professional-name"]').should('be.visible');
      cy.get('[data-cy="professional-bio"]').should('be.visible');
      cy.get('[data-cy="professional-experience"]').should('be.visible');
      cy.get('[data-cy="professional-certifications"]').should('be.visible');
      cy.get('[data-cy="professional-reviews"]').should('be.visible');
      cy.get('[data-cy="contact-professional-button"]').should('be.visible');
      cy.get('[data-cy="book-session-button"]').should('be.visible');
    });

    it('should show professional availability calendar', () => {
      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      cy.get('[data-cy^="professional-card-"]').first().click();
      cy.wait('@getProfessional');
      
      // Vérifier le calendrier de disponibilité
      cy.get('[data-cy="availability-calendar"]').should('be.visible');
      cy.get('[data-cy="available-slots"]').should('have.length.at.least', 1);
      
      // Sélectionner un créneau disponible
      cy.get('[data-cy="available-slots"]').first().click();
      cy.get('[data-cy="selected-slot"]').should('be.visible');
      cy.get('[data-cy="book-session-button"]').should('not.be.disabled');
    });

    it('should display professional reviews and ratings', () => {
      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      cy.get('[data-cy^="professional-card-"]').first().click();
      cy.wait('@getProfessional');
      
      // Vérifier la section des avis
      cy.get('[data-cy="reviews-section"]').should('be.visible');
      cy.get('[data-cy="overall-rating"]').should('be.visible');
      cy.get('[data-cy="review-count"]').should('be.visible');
      cy.get('[data-cy="review-list"]').should('be.visible');
      
      // Vérifier qu'au moins un avis est affiché
      cy.get('[data-cy^="review-"]').should('have.length.at.least', 1);
      
      // Vérifier les détails d'un avis
      cy.get('[data-cy^="review-"]').first().within(() => {
        cy.get('[data-cy="reviewer-name"]').should('be.visible');
        cy.get('[data-cy="review-rating"]').should('be.visible');
        cy.get('[data-cy="review-text"]').should('be.visible');
        cy.get('[data-cy="review-date"]').should('be.visible');
      });
    });
  });

  describe('Contact Professional', () => {
    it('should send a message to a professional', () => {
      // Mocker la réponse d'envoi de message
      cy.intercept('POST', '/api/messages', {
        statusCode: 200,
        body: {
          success: true,
          messageId: 'msg-123',
        }
      }).as('sendMessageSuccess');

      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      // Contacter le premier professionnel
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="contact-button"]').click();
      });
      
      // Vérifier l'ouverture du modal de contact
      cy.get('[data-cy="contact-modal"]').should('be.visible');
      
      // Remplir le formulaire de contact
      cy.get('[data-cy="subject-select"]').select('Demande de rendez-vous');
      cy.get('[data-cy="message-textarea"]').type(
        'Bonjour, je souhaiterais prendre rendez-vous pour une séance de yoga. Quelles sont vos disponibilités cette semaine ?'
      );
      
      // Envoyer le message
      cy.get('[data-cy="send-message-button"]').click();
      
      // Vérifier l'appel API
      cy.wait('@sendMessageSuccess').then((interception) => {
        expect(interception.request.body).to.deep.include({
          subject: 'Demande de rendez-vous',
          message: 'Bonjour, je souhaiterais prendre rendez-vous pour une séance de yoga. Quelles sont vos disponibilités cette semaine ?'
        });
      });
      
      // Vérifier la confirmation
      cy.checkToast('Message envoyé avec succès', 'success');
      cy.get('[data-cy="contact-modal"]').should('not.exist');
    });

    it('should handle contact form validation', () => {
      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="contact-button"]').click();
      });
      
      // Essayer d'envoyer sans remplir les champs
      cy.get('[data-cy="send-message-button"]').click();
      
      // Vérifier les messages d'erreur
      cy.get('[data-cy="subject-error"]').should('contain.text', 'Sujet requis');
      cy.get('[data-cy="message-error"]').should('contain.text', 'Message requis');
      
      // Tester avec un message trop court
      cy.get('[data-cy="subject-select"]').select('Question générale');
      cy.get('[data-cy="message-textarea"]').type('Salut');
      cy.get('[data-cy="send-message-button"]').click();
      
      cy.get('[data-cy="message-error"]').should('contain.text', 'Message trop court');
    });

    it('should handle unavailable professional contact', () => {
      // Mocker un professionnel non disponible
      cy.intercept('POST', '/api/messages', {
        statusCode: 409,
        body: {
          success: false,
          message: 'Ce professionnel n\'accepte plus de nouveaux clients actuellement'
        }
      }).as('unavailableProfessional');

      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="contact-button"]').click();
      });
      
      cy.get('[data-cy="subject-select"]').select('Demande de rendez-vous');
      cy.get('[data-cy="message-textarea"]').type('Message de test');
      cy.get('[data-cy="send-message-button"]').click();
      
      cy.wait('@unavailableProfessional');
      
      // Vérifier le message d'erreur
      cy.get('[data-cy="error-message"]').should('contain.text', 'Ce professionnel n\'accepte plus de nouveaux clients');
      cy.checkToast('Professionnel non disponible', 'warning');
    });
  });

  describe('Book Session', () => {
    it('should book a session with a professional', () => {
      // Mocker la réponse de réservation
      cy.intercept('POST', '/api/appointments', {
        statusCode: 200,
        body: {
          success: true,
          appointmentId: 'apt-123',
          professionalId: 'prof-1',
          date: '2025-06-15',
          time: '10:00'
        }
      }).as('bookSessionSuccess');

      cy.visit('/app/professionals/prof-1');
      cy.wait('@getProfessional');
      
      // Sélectionner un créneau
      cy.get('[data-cy="slot-2025-06-15-10:00"]').click();
      cy.get('[data-cy="book-session-button"]').click();
      
      // Vérifier l'ouverture du modal de réservation
      cy.get('[data-cy="booking-modal"]').should('be.visible');
      
      // Confirmer les détails
      cy.get('[data-cy="session-type-select"]').select('Séance individuelle');
      cy.get('[data-cy="special-requests"]').type('Première séance, niveau débutant');
      
      // Confirmer la réservation
      cy.get('[data-cy="confirm-booking"]').click();
      
      // Vérifier l'appel API
      cy.wait('@bookSessionSuccess').then((interception) => {
        expect(interception.request.body).to.deep.include({
          professionalId: 'prof-1',
          date: '2025-06-15',
          time: '10:00',
          sessionType: 'Séance individuelle'
        });
      });
      
      // Vérifier la confirmation
      cy.checkToast('Séance réservée avec succès', 'success');
      cy.url().should('include', '/appointment-confirmation');
    });

    it('should handle session booking conflicts', () => {
      // Mocker un conflit de réservation
      cy.intercept('POST', '/api/appointments', {
        statusCode: 409,
        body: {
          success: false,
          message: 'Ce créneau n\'est plus disponible'
        }
      }).as('bookingConflict');

      cy.visit('/app/professionals/prof-1');
      cy.wait('@getProfessional');
      
      cy.get('[data-cy="slot-2025-06-15-10:00"]').click();
      cy.get('[data-cy="book-session-button"]').click();
      
      cy.get('[data-cy="session-type-select"]').select('Séance individuelle');
      cy.get('[data-cy="confirm-booking"]').click();
      
      cy.wait('@bookingConflict');
      
      // Vérifier le message d'erreur
      cy.get('[data-cy="error-message"]').should('contain.text', 'Ce créneau n\'est plus disponible');
      cy.checkToast('Créneau non disponible', 'error');
      
      // Vérifier que l'utilisateur peut sélectionner un autre créneau
      cy.get('[data-cy="select-another-slot"]').should('be.visible');
    });
  });

  describe('Professional Favorites', () => {
    it('should add professional to favorites', () => {
      cy.intercept('POST', '/api/user/favorites/professionals', {
        statusCode: 200,
        body: { success: true }
      }).as('addToFavorites');

      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      // Ajouter aux favoris depuis la liste
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="favorite-button"]').click();
      });
      
      cy.wait('@addToFavorites');
      
      // Vérifier le toast de confirmation
      cy.checkToast('Professionnel ajouté aux favoris', 'success');
      
      // Vérifier que l'icône change
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="favorite-button"]').should('have.class', 'favorited');
      });
    });

    it('should remove professional from favorites', () => {
      cy.intercept('DELETE', '/api/user/favorites/professionals/*', {
        statusCode: 200,
        body: { success: true }
      }).as('removeFromFavorites');

      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      // Supposer que le professionnel est déjà en favori
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="favorite-button"].favorited').click();
      });
      
      cy.wait('@removeFromFavorites');
      
      // Vérifier le toast de confirmation
      cy.checkToast('Professionnel retiré des favoris', 'success');
      
      // Vérifier que l'icône change
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="favorite-button"]').should('not.have.class', 'favorited');
      });
    });
  });

  describe('Professional Reviews', () => {
    it('should submit a review for a professional', () => {
      cy.intercept('POST', '/api/reviews', {
        statusCode: 200,
        body: {
          success: true,
          reviewId: 'review-123'
        }
      }).as('submitReview');

      cy.visit('/app/professionals/prof-1');
      cy.wait('@getProfessional');
      
      // Ouvrir le formulaire d'avis
      cy.get('[data-cy="write-review-button"]').click();
      cy.get('[data-cy="review-modal"]').should('be.visible');
      
      // Remplir l'avis
      cy.get('[data-cy="rating-stars"]').within(() => {
        cy.get('[data-cy="star-5"]').click(); // 5 étoiles
      });
      
      cy.get('[data-cy="review-title"]').type('Excellente expérience');
      cy.get('[data-cy="review-text"]').type(
        'Séance très enrichissante avec Marie. Elle a su s\'adapter à mon niveau et m\'a donné d\'excellents conseils. Je recommande vivement !'
      );
      
      // Soumettre l'avis
      cy.get('[data-cy="submit-review"]').click();
      
      cy.wait('@submitReview');
      
      // Vérifier la confirmation
      cy.checkToast('Avis publié avec succès', 'success');
      cy.get('[data-cy="review-modal"]').should('not.exist');
    });
  });

  describe('Responsive Professional Flow', () => {
    it('should work on mobile devices', () => {
      cy.viewport('iphone-x');
      
      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      // Vérifier que les cartes s'affichent correctement sur mobile
      cy.get('[data-cy^="professional-card-"]').should('be.visible');
      
      // Tester le processus de contact sur mobile
      cy.get('[data-cy^="professional-card-"]').first().within(() => {
        cy.get('[data-cy="contact-button"]').should('be.visible').click();
      });
      
      cy.get('[data-cy="contact-modal"]').should('be.visible');
    });

    it('should work on tablet devices', () => {
      cy.viewport('ipad-2');
      
      cy.visitProfessionals();
      cy.wait('@getProfessionals');
      
      // Vérifier l'affichage en grille sur tablette
      cy.get('[data-cy="professional-list"]').should('be.visible');
      cy.get('[data-cy^="professional-card-"]').should('have.length.at.least', 2);
    });
  });
});
