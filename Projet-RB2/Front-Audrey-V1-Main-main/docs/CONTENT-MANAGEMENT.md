# Documentation du Système de Gestion de Contenu

Ce document détaille le système de gestion de contenu intégré dans le frontend de Retreat And Be.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Fonctionnalités](#fonctionnalités)
   - [Gestion de contenu](#gestion-de-contenu)
   - [Téléchargement de contenu](#téléchargement-de-contenu)
   - [Analyses de contenu](#analyses-de-contenu)
   - [Recherche de contenu](#recherche-de-contenu)
4. [Intégration avec les services](#intégration-avec-les-services)
5. [Modèles de données](#modèles-de-données)
6. [Guide d'utilisation](#guide-dutilisation)
7. [Considérations techniques](#considérations-techniques)
8. [Évolutions futures](#évolutions-futures)

## Vue d'ensemble

Le système de gestion de contenu (CMS) de Retreat And Be permet aux partenaires et administrateurs de gérer efficacement le contenu de la plateforme. Il offre des fonctionnalités complètes pour la création, l'édition, la publication, l'archivage et la suppression de contenu, ainsi que des outils d'analyse et de recherche avancés.

## Architecture

Le CMS est construit selon une architecture modulaire qui s'intègre au reste de l'application frontend :

```
src/
├── components/
│   ├── content/
│   │   ├── ContentManagement.tsx       # Composant principal de gestion
│   │   ├── ContentAnalytics.tsx        # Composant d'analyse
│   │   ├── ContentSearch.tsx           # Composant de recherche
│   │   ├── EditPostDialog.tsx          # Dialogue d'édition
│   │   ├── UploadContentDialog.tsx     # Dialogue de téléchargement
│   │   ├── PostList.tsx                # Liste de contenu
│   │   └── ...
├── pages/
│   ├── ContentManagementPage.tsx       # Page de gestion
│   ├── ContentAnalyticsPage.tsx        # Page d'analyse
│   ├── ContentSearchPage.tsx           # Page de recherche
│   └── ...
├── services/
│   ├── api/
│   │   ├── socialVideoService.ts       # Service pour les vidéos
│   │   ├── socialService.ts            # Service pour le contenu social
│   │   ├── socialAnalyticsService.ts   # Service pour les analyses
│   │   └── ...
└── ...
```

## Fonctionnalités

### Gestion de contenu

La gestion de contenu permet aux utilisateurs de :

- **Visualiser le contenu** par statut (publié, archivé, supprimé)
- **Sélectionner plusieurs contenus** pour des actions par lots
- **Archiver du contenu** pour le retirer temporairement
- **Restaurer du contenu** archivé ou supprimé
- **Supprimer définitivement** du contenu
- **Éditer les métadonnées** comme le titre, la description et les tags
- **Gérer les paramètres de confidentialité** (public, amis, privé)

#### Statuts de contenu

- **Publié** : Contenu visible selon ses paramètres de confidentialité
- **Archivé** : Contenu temporairement retiré mais récupérable
- **Supprimé** : Contenu dans la corbeille, récupérable pendant une période limitée

### Téléchargement de contenu

Le système permet de télécharger du contenu avec :

- **Interface de glisser-déposer** pour une expérience utilisateur intuitive
- **Prévisualisation** des images et génération de vignettes pour les vidéos
- **Formulaire de métadonnées** pour ajouter titre, description et tags
- **Sélection de confidentialité** pour contrôler la visibilité
- **Barre de progression** pour suivre l'avancement du téléchargement
- **Validation des fichiers** pour assurer la conformité (taille, format)

### Analyses de contenu

Le module d'analyse offre :

- **Statistiques globales** (vues, likes, commentaires, partages)
- **Graphiques d'engagement** au fil du temps
- **Répartition du contenu** par statut
- **Classement du contenu** le plus performant
- **Filtrage par période** (semaine, mois, année)
- **Exportation des données** pour une analyse plus approfondie

### Recherche de contenu

La recherche avancée permet de :

- **Rechercher par mots-clés** dans les titres, descriptions et tags
- **Filtrer par type de contenu** (vidéo, image, texte)
- **Filtrer par date** (aujourd'hui, cette semaine, ce mois, cette année)
- **Filtrer par statut** (publié, archivé, supprimé)
- **Sélectionner par tags** pour un filtrage précis
- **Trier les résultats** par pertinence ou date
- **Prévisualiser les résultats** avec des vignettes et métadonnées

## Intégration avec les services

Le CMS s'intègre avec plusieurs services :

- **Services d'authentification** pour la gestion des permissions
- **Services de stockage** pour les fichiers multimédias
- **Services d'analyse** pour le suivi des performances
- **Services sociaux** pour l'engagement des utilisateurs

### Points d'intégration API

Le CMS communique avec le backend via les services suivants :

- `socialVideoService` : Gestion des vidéos et contenus multimédias
- `socialService` : Gestion du contenu social
- `socialAnalyticsService` : Analyses et statistiques

## Modèles de données

### Post

```typescript
interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
}
```

### ContentAnalyticsData

```typescript
interface ContentAnalyticsData {
  viewsByDay: {
    date: string;
    views: number;
  }[];
  engagementByType: {
    type: string;
    count: number;
  }[];
  contentByStatus: {
    status: string;
    count: number;
  }[];
  topContent: {
    id: string;
    title: string;
    views: number;
    engagement: number;
  }[];
  totalStats: {
    totalViews: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalContent: number;
  };
}
```

## Guide d'utilisation

### Accès au CMS

Les fonctionnalités de gestion de contenu sont accessibles via :

- `/content-management` - Gestion principale du contenu
- `/content-analytics` - Analyses et statistiques
- `/content-search` - Recherche avancée

### Gestion des permissions

L'accès au CMS est limité aux utilisateurs ayant les rôles suivants :
- Administrateurs
- Partenaires

### Flux de travail typiques

#### Publication de contenu

1. Accéder à la page de gestion de contenu
2. Cliquer sur "Ajouter du contenu"
3. Télécharger le fichier et remplir les métadonnées
4. Sélectionner les paramètres de confidentialité
5. Cliquer sur "Télécharger"

#### Analyse de performance

1. Accéder à la page d'analyses
2. Sélectionner la période souhaitée
3. Consulter les statistiques et graphiques
4. Identifier le contenu le plus performant
5. Exporter les données si nécessaire

## Considérations techniques

### Performance

- Les listes de contenu utilisent la pagination pour optimiser le chargement
- Les images sont redimensionnées côté serveur pour réduire la bande passante
- Les analyses utilisent des données agrégées pour améliorer les performances

### Sécurité

- Validation des fichiers téléchargés pour prévenir les attaques
- Contrôle d'accès basé sur les rôles pour protéger le contenu
- Sanitisation des entrées utilisateur pour prévenir les injections

### Accessibilité

- Interface conforme aux normes WCAG 2.1
- Support du clavier pour toutes les fonctionnalités
- Textes alternatifs pour les images et contrôles

## Évolutions futures

Fonctionnalités prévues pour les prochaines versions :

- **Planification de contenu** : Programmer la publication à des dates spécifiques
- **Catégorisation avancée** : Système de catégories hiérarchiques
- **Modération de contenu** : Outils pour modérer le contenu généré par les utilisateurs
- **Intégration avec les médias sociaux** : Partage direct sur les plateformes sociales
- **Analyses avancées** : Démographie des utilisateurs et tendances d'engagement
- **Éditeur de contenu riche** : Interface WYSIWYG pour la création de contenu
- **Gestion des droits d'auteur** : Outils pour gérer les licences et attributions
