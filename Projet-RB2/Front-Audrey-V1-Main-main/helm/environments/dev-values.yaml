# Valeurs pour l'environnement de développement
replicaCount: 1

image:
  repository: registry.retreat-and-be.com/retreat-and-be/audrey-frontend
  tag: dev
  pullPolicy: Always

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: nginx
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: letsencrypt-staging
  hosts:
    - host: dev.app.retreat-and-be.com
      paths:
        - path: /(.*)
          pathType: Prefix
  tls:
    - secretName: audrey-frontend-dev-tls
      hosts:
        - dev.app.retreat-and-be.com

resources:
  limits:
    cpu: 200m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

autoscaling:
  enabled: false

env:
  NODE_ENV: development
  API_URL: https://dev-api.retreat-and-be.com
  SECURITY_URL: https://dev-api.retreat-and-be.com/security
  AGENT_IA_URL: https://dev-api.retreat-and-be.com/agent-ia
  SOCIAL_URL: https://dev-api.retreat-and-be.com/social
  FINANCIAL_URL: https://dev-api.retreat-and-be.com/financial
  ENABLE_LOGS: "true"
  ENABLE_DEBUG: "true"

# Configuration pour le développement
development:
  enableHotReload: true
  enableSourceMaps: true
  enableVerboseLogs: true
