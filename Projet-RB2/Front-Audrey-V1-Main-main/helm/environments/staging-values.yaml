# Valeurs pour l'environnement de staging
replicaCount: 2

image:
  repository: registry.retreat-and-be.com/retreat-and-be/audrey-frontend
  tag: staging
  pullPolicy: Always

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: nginx
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: letsencrypt-staging
  hosts:
    - host: staging.app.retreat-and-be.com
      paths:
        - path: /(.*)
          pathType: Prefix
  tls:
    - secretName: audrey-frontend-staging-tls
      hosts:
        - staging.app.retreat-and-be.com

resources:
  limits:
    cpu: 300m
    memory: 384Mi
  requests:
    cpu: 150m
    memory: 192Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 70

env:
  NODE_ENV: staging
  API_URL: https://staging-api.retreat-and-be.com
  SECURITY_URL: https://staging-api.retreat-and-be.com/security
  AGENT_IA_URL: https://staging-api.retreat-and-be.com/agent-ia
  SOCIAL_URL: https://staging-api.retreat-and-be.com/social
  FINANCIAL_URL: https://staging-api.retreat-and-be.com/financial
  ENABLE_LOGS: "true"
  ENABLE_DEBUG: "false"

# Configuration pour le staging
staging:
  enableCaching: true
  cacheTTL: 3600
  enableRateLimiting: true
  rateLimit: 100
