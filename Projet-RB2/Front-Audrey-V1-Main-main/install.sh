#!/bin/bash

# Script d'installation complet pour Front-Audrey-V1-Main-main
# Ce script automatise l'ensemble du processus d'installation et de déploiement

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si Docker est installé
  if ! command -v docker &> /dev/null; then
    error "Docker n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si Helm est installé
  if ! command -v helm &> /dev/null; then
    error "Helm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  }
  
  success "Tous les prérequis sont installés."
}

# Configurer les variables d'environnement
setup_environment() {
  log "Configuration des variables d'environnement..."
  
  # Demander le registre Docker
  read -p "Entrez l'URL du registre Docker (par défaut: registry.retreat-and-be.com): " DOCKER_REGISTRY
  DOCKER_REGISTRY=${DOCKER_REGISTRY:-registry.retreat-and-be.com}
  
  # Demander le namespace Kubernetes
  read -p "Entrez le namespace Kubernetes (par défaut: retreat-and-be): " NAMESPACE
  NAMESPACE=${NAMESPACE:-retreat-and-be}
  
  # Demander le tag de l'image
  read -p "Entrez le tag de l'image (par défaut: latest): " IMAGE_TAG
  IMAGE_TAG=${IMAGE_TAG:-latest}
  
  # Demander l'URL de l'API
  read -p "Entrez l'URL de l'API (par défaut: https://api.retreat-and-be.com): " API_URL
  API_URL=${API_URL:-https://api.retreat-and-be.com}
  
  # Demander le nom de domaine
  read -p "Entrez le nom de domaine pour l'application (par défaut: app.retreat-and-be.com): " DOMAIN
  DOMAIN=${DOMAIN:-app.retreat-and-be.com}
  
  success "Variables d'environnement configurées."
}

# Construire l'image Docker
build_docker_image() {
  log "Construction de l'image Docker..."
  
  # Construire l'image Docker
  docker build -t ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG} .
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la construction de l'image Docker."
    exit 1
  fi
  
  success "Image Docker construite avec succès."
}

# Pousser l'image vers le registre
push_docker_image() {
  log "Envoi de l'image vers le registre Docker..."
  
  # Se connecter au registre Docker
  echo "Connexion au registre Docker..."
  read -p "Nom d'utilisateur pour le registre Docker: " DOCKER_USERNAME
  read -sp "Mot de passe pour le registre Docker: " DOCKER_PASSWORD
  echo
  
  echo "$DOCKER_PASSWORD" | docker login ${DOCKER_REGISTRY} -u ${DOCKER_USERNAME} --password-stdin
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la connexion au registre Docker."
    exit 1
  fi
  
  # Pousser l'image
  docker push ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'envoi de l'image vers le registre Docker."
    exit 1
  fi
  
  success "Image Docker envoyée avec succès."
}

# Déployer sur Kubernetes avec Helm
deploy_with_helm() {
  log "Déploiement sur Kubernetes avec Helm..."
  
  # Vérifier si le namespace existe, sinon le créer
  if ! kubectl get namespace ${NAMESPACE} &> /dev/null; then
    log "Création du namespace ${NAMESPACE}..."
    kubectl create namespace ${NAMESPACE}
  fi
  
  # Mettre à jour les valeurs Helm
  log "Mise à jour des valeurs Helm..."
  cat > helm/values-custom.yaml << EOF
image:
  repository: ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend
  tag: ${IMAGE_TAG}

ingress:
  hosts:
    - host: ${DOMAIN}
      paths:
        - path: /(.*)
          pathType: Prefix

env:
  API_URL: ${API_URL}
EOF
  
  # Déployer avec Helm
  helm upgrade --install audrey-frontend ./helm -f helm/values-custom.yaml -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors du déploiement avec Helm."
    exit 1
  fi
  
  success "Déploiement avec Helm réussi."
}

# Vérifier le déploiement
verify_deployment() {
  log "Vérification du déploiement..."
  
  # Attendre que le déploiement soit prêt
  kubectl rollout status deployment/audrey-frontend -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la vérification du déploiement."
    exit 1
  fi
  
  # Afficher les informations du service
  kubectl get service audrey-frontend -n ${NAMESPACE}
  
  # Afficher les informations de l'ingress
  kubectl get ingress -n ${NAMESPACE}
  
  success "Déploiement vérifié avec succès."
  log "L'application est accessible à l'adresse: https://${DOMAIN}"
}

# Configurer la surveillance
setup_monitoring() {
  log "Configuration de la surveillance..."
  
  # Appliquer la configuration ServiceMonitor
  kubectl apply -f kubernetes/servicemonitor.yaml -n ${NAMESPACE}
  
  # Importer le dashboard Grafana
  log "Pour importer le dashboard Grafana, accédez à votre instance Grafana et importez le fichier monitoring/grafana-dashboard.json"
  
  success "Configuration de la surveillance terminée."
}

# Configurer la journalisation centralisée
setup_logging() {
  log "Configuration de la journalisation centralisée..."
  
  # Appliquer la configuration Fluentd
  kubectl apply -f monitoring/fluentd-config.yaml -n ${NAMESPACE}
  
  success "Configuration de la journalisation centralisée terminée."
}

# Configurer les sauvegardes
setup_backups() {
  log "Configuration des sauvegardes..."
  
  # Vérifier si Velero est installé
  if ! kubectl get namespace velero &> /dev/null; then
    warn "Le namespace Velero n'existe pas. Veuillez installer Velero avant de configurer les sauvegardes."
  else
    # Appliquer la configuration de sauvegarde
    kubectl apply -f kubernetes/backup-schedule.yaml
    
    success "Configuration des sauvegardes terminée."
  fi
}

# Menu principal
main() {
  echo "================================================"
  echo "  Installation de Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Configurer les variables d'environnement
  setup_environment
  
  # Demander confirmation
  echo
  echo "Résumé de la configuration:"
  echo "- Registre Docker: ${DOCKER_REGISTRY}"
  echo "- Namespace Kubernetes: ${NAMESPACE}"
  echo "- Tag de l'image: ${IMAGE_TAG}"
  echo "- URL de l'API: ${API_URL}"
  echo "- Nom de domaine: ${DOMAIN}"
  echo
  
  read -p "Voulez-vous continuer avec cette configuration? (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Installation annulée."
    exit 0
  fi
  
  # Construire l'image Docker
  build_docker_image
  
  # Pousser l'image vers le registre
  push_docker_image
  
  # Déployer sur Kubernetes avec Helm
  deploy_with_helm
  
  # Vérifier le déploiement
  verify_deployment
  
  # Configurer la surveillance
  setup_monitoring
  
  # Configurer la journalisation centralisée
  setup_logging
  
  # Configurer les sauvegardes
  setup_backups
  
  success "Installation terminée avec succès!"
  log "L'application est accessible à l'adresse: https://${DOMAIN}"
}

# Exécuter le script
main
