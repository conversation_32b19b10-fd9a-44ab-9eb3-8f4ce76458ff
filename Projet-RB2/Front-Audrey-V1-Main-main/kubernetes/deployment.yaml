apiVersion: apps/v1
kind: Deployment
metadata:
  name: audrey-frontend
  labels:
    app: audrey-frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: audrey-frontend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: audrey-frontend
    spec:
      containers:
      - name: audrey-frontend
        image: ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 300m
            memory: 256Mi
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 15
          periodSeconds: 15
        env:
        - name: NODE_ENV
          value: "production"
        - name: REACT_APP_API_URL
          valueFrom:
            configMapKeyRef:
              name: audrey-frontend-config
              key: REACT_APP_API_URL
        - name: REACT_APP_SECURITY_URL
          valueFrom:
            configMapKeyRef:
              name: audrey-frontend-config
              key: REACT_APP_SECURITY_URL
        - name: REACT_APP_AGENT_IA_URL
          valueFrom:
            configMapKeyRef:
              name: audrey-frontend-config
              key: REACT_APP_AGENT_IA_URL
        - name: REACT_APP_SOCIAL_URL
          valueFrom:
            configMapKeyRef:
              name: audrey-frontend-config
              key: REACT_APP_SOCIAL_URL
        - name: REACT_APP_FINANCIAL_URL
          valueFrom:
            configMapKeyRef:
              name: audrey-frontend-config
              key: REACT_APP_FINANCIAL_URL
      imagePullSecrets:
      - name: regcred
