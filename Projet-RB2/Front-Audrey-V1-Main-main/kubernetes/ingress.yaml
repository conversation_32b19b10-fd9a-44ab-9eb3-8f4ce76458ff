apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: audrey-frontend-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - app.retreat-and-be.com
    secretName: audrey-frontend-tls
  rules:
  - host: app.retreat-and-be.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: audrey-frontend
            port:
              number: 80
