apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: retreat-and-be
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/audrey-frontend-*.log
      pos_file /var/log/fluentd-audrey-frontend.log.pos
      tag kubernetes.audrey-frontend
      read_from_head true
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </parse>
    </source>

    <filter kubernetes.audrey-frontend>
      @type kubernetes_metadata
      kubernetes_url https://kubernetes.default.svc
      bearer_token_file /var/run/secrets/kubernetes.io/serviceaccount/token
      ca_file /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    </filter>

    <match kubernetes.audrey-frontend>
      @type elasticsearch
      host elasticsearch-master
      port 9200
      logstash_format true
      logstash_prefix audrey-frontend
      <buffer>
        @type file
        path /var/log/fluentd-buffers/kubernetes.audrey-frontend
        flush_mode interval
        retry_type exponential_backoff
        flush_thread_count 2
        flush_interval 5s
        retry_forever
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 8
        overflow_action block
      </buffer>
    </match>
