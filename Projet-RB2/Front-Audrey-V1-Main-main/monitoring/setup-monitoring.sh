#!/bin/bash

# Script de configuration du monitoring pour Front-Audrey-V1-Main-main
# Ce script configure Prometheus, Grafana et Alertmanager pour le frontend

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si Helm est installé
  if ! command -v helm &> /dev/null; then
    error "Helm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  success "Tous les prérequis sont installés."
}

# Installer Prometheus Operator
install_prometheus_operator() {
  log "Installation de Prometheus Operator..."
  
  # Vérifier si le namespace monitoring existe
  if ! kubectl get namespace monitoring &> /dev/null; then
    log "Création du namespace monitoring..."
    kubectl create namespace monitoring
  fi
  
  # Ajouter le repo Helm de Prometheus
  log "Ajout du repo Helm de Prometheus..."
  helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
  helm repo update
  
  # Installer Prometheus Operator
  log "Installation de Prometheus Operator..."
  helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
    --namespace monitoring \
    --set prometheus.prometheusSpec.serviceMonitorSelectorNilUsesHelmValues=false \
    --set prometheus.prometheusSpec.podMonitorSelectorNilUsesHelmValues=false
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'installation de Prometheus Operator."
    exit 1
  fi
  
  success "Prometheus Operator installé avec succès."
}

# Installer Blackbox Exporter
install_blackbox_exporter() {
  log "Installation de Blackbox Exporter..."
  
  # Appliquer la configuration de Blackbox Exporter
  kubectl apply -f blackbox-exporter.yaml
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'installation de Blackbox Exporter."
    exit 1
  fi
  
  success "Blackbox Exporter installé avec succès."
}

# Configurer les règles d'alerte Prometheus
configure_prometheus_rules() {
  log "Configuration des règles d'alerte Prometheus..."
  
  # Appliquer les règles d'alerte Prometheus
  kubectl apply -f prometheus-rules.yaml
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la configuration des règles d'alerte Prometheus."
    exit 1
  fi
  
  success "Règles d'alerte Prometheus configurées avec succès."
}

# Configurer Grafana
configure_grafana() {
  log "Configuration de Grafana..."
  
  # Obtenir le mot de passe admin de Grafana
  GRAFANA_PASSWORD=$(kubectl get secret -n monitoring prometheus-grafana -o jsonpath="{.data.admin-password}" | base64 --decode)
  
  if [ -z "$GRAFANA_PASSWORD" ]; then
    error "Impossible d'obtenir le mot de passe admin de Grafana."
    exit 1
  fi
  
  log "Mot de passe admin de Grafana: $GRAFANA_PASSWORD"
  
  # Obtenir l'URL de Grafana
  GRAFANA_URL=$(kubectl get svc -n monitoring prometheus-grafana -o jsonpath="{.status.loadBalancer.ingress[0].ip}")
  
  if [ -z "$GRAFANA_URL" ]; then
    GRAFANA_URL=$(kubectl get svc -n monitoring prometheus-grafana -o jsonpath="{.status.loadBalancer.ingress[0].hostname}")
  fi
  
  if [ -z "$GRAFANA_URL" ]; then
    warn "Impossible d'obtenir l'URL de Grafana. Utilisation de port-forward..."
    log "Exécutez la commande suivante pour accéder à Grafana:"
    log "kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring"
    GRAFANA_URL="localhost:3000"
  else
    GRAFANA_URL="http://$GRAFANA_URL"
  fi
  
  log "URL de Grafana: $GRAFANA_URL"
  
  # Importer le dashboard Grafana
  log "Pour importer le dashboard Grafana:"
  log "1. Accédez à Grafana à l'URL: $GRAFANA_URL"
  log "2. Connectez-vous avec les identifiants:"
  log "   - Nom d'utilisateur: admin"
  log "   - Mot de passe: $GRAFANA_PASSWORD"
  log "3. Allez dans 'Create' > 'Import'"
  log "4. Importez le fichier 'grafana-dashboard.json'"
  
  success "Configuration de Grafana terminée."
}

# Configurer Alertmanager
configure_alertmanager() {
  log "Configuration d'Alertmanager..."
  
  # Demander l'URL du webhook Slack
  read -p "Entrez l'URL du webhook Slack (laissez vide pour ignorer): " SLACK_WEBHOOK_URL
  
  if [ -z "$SLACK_WEBHOOK_URL" ]; then
    log "Configuration de Slack ignorée."
  else
    # Créer la configuration Alertmanager
    cat > alertmanager-config.yaml << EOF
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-prometheus-kube-prometheus-alertmanager
  namespace: monitoring
stringData:
  alertmanager.yaml: |
    global:
      resolve_timeout: 5m
    route:
      group_by: ['job', 'alertname', 'severity']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 12h
      receiver: 'slack-notifications'
      routes:
      - match:
          alertname: Watchdog
        receiver: 'null'
      - match:
          severity: critical
        receiver: 'slack-notifications'
        continue: true
    receivers:
    - name: 'null'
    - name: 'slack-notifications'
      slack_configs:
      - api_url: '$SLACK_WEBHOOK_URL'
        channel: '#alerts'
        send_resolved: true
        title: '[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .CommonLabels.alertname }}'
        text: >-
          {{ range .Alerts }}
            *Alert:* {{ .Annotations.summary }}
            *Description:* {{ .Annotations.description }}
            *Severity:* {{ .Labels.severity }}
            *Details:*
            {{ range .Labels.SortedPairs }} • *{{ .Name }}:* {{ .Value }}
            {{ end }}
          {{ end }}
EOF
    
    # Appliquer la configuration Alertmanager
    kubectl apply -f alertmanager-config.yaml
    
    if [ $? -ne 0 ]; then
      error "Erreur lors de la configuration d'Alertmanager."
      exit 1
    fi
    
    # Redémarrer Alertmanager
    kubectl rollout restart statefulset/prometheus-prometheus-kube-prometheus-alertmanager -n monitoring
    
    success "Alertmanager configuré avec succès."
  fi
}

# Menu principal
main() {
  echo "================================================"
  echo "  Configuration du monitoring pour Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Installer Prometheus Operator
  install_prometheus_operator
  
  # Installer Blackbox Exporter
  install_blackbox_exporter
  
  # Configurer les règles d'alerte Prometheus
  configure_prometheus_rules
  
  # Configurer Grafana
  configure_grafana
  
  # Configurer Alertmanager
  configure_alertmanager
  
  success "Configuration du monitoring terminée avec succès!"
}

# Exécuter le script
main
