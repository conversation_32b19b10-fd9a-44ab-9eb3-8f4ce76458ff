{"multiCriteria": {"pageTitle": "Multi-Criteria Recommendations", "pageDescription": "Customize your recommendations based on multiple criteria and find retreats that perfectly match your needs.", "sectionTitle": "Personalized Recommendations", "sectionDescription": "Recommendations optimized according to your preferences and multiple criteria.", "criteriaSelector": "Criteria Selection", "selectedCriteria": "Selected criteria", "noCriteriaSelected": "No criteria selected", "availableCriteria": "Available criteria", "weight": "Weight", "direction": "Direction", "maximize": "Maximize", "minimize": "Minimize", "removeCriterion": "Remove this criterion", "optimizationMethod": "Optimization Method", "aboutSelectedMethod": "About the selected method", "recommendations": "Recommendations", "optimizedUsing": "Optimized using", "noRecommendations": "No recommendations found", "globalScore": "Global score", "paretoOptimal": "Pareto optimal", "viewDetails": "View details", "seeMore": "See more", "loadError": "Error loading recommendations", "loadCriteriaError": "Error loading criteria", "preferencesChangedWarning": "Your preferences have changed. Apply changes to update recommendations.", "applyChanges": "Apply changes", "savePreferencesDescription": "Save your preferences to find them on your next visit.", "savePreferences": "Save my preferences", "preferencesSaved": "Your preferences have been successfully saved", "savePreferencesError": "Error saving preferences", "methods": {"weighted_sum": "Weighted Sum", "pareto": "Pareto Optimization", "lexicographic": "Lexicographic Method", "topsis": "TOPSIS Method"}, "methodDescriptions": {"weightedSum": "Combines scores from each criterion based on their weights", "pareto": "Identifies solutions where no criterion can be improved without degrading another", "lexicographic": "Compares alternatives according to the order of importance of criteria", "topsis": "Identifies solutions closest to the ideal and furthest from the anti-ideal"}, "methodDetails": {"weightedSum": {"p1": "The weighted sum method is the simplest and most intuitive multi-criteria optimization method.", "p2": "It consists of multiplying each normalized score by the weight of the corresponding criterion, then adding these weighted scores:", "p3": "This method is easy to understand and implement, but it assumes that criteria are independent and preferences are linear."}, "pareto": {"p1": "Pareto optimization identifies non-dominated solutions, i.e., those for which it is impossible to improve one criterion without degrading at least one other criterion.", "p2": "A solution A dominates a solution B if A is at least as good as B on all criteria, and strictly better on at least one criterion.", "p3": "This method is particularly useful when criteria are in conflict and there is no unique optimal solution."}, "lexicographic": {"p1": "The lexicographic method sorts criteria by order of importance and compares alternatives according to the most important criterion.", "p2": "In case of a tie on the most important criterion, we move to the next criterion, and so on.", "p3": "This method is suitable when there is a clear hierarchy between criteria, but it may ignore trade-offs between criteria."}, "topsis": {"p1": "TOPSIS (Technique for Order of Preference by Similarity to Ideal Solution) identifies the ideal and anti-ideal solution, then ranks alternatives according to their relative proximity to the ideal solution.", "p2": "The ideal solution is the one that maximizes all criteria to be maximized and minimizes all criteria to be minimized.", "p3": "This method takes into account all criteria simultaneously and allows finding a good compromise between conflicting criteria."}}}}