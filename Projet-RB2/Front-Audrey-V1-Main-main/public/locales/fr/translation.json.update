{"multiCriteria": {"pageTitle": "Recommandations Multi-critères", "pageDescription": "Personnalisez vos recommandations en fonction de plusieurs critères et trouvez les retraites qui correspondent parfaitement à vos besoins.", "sectionTitle": "Recommandations Personnalisées", "sectionDescription": "Des recommandations optimisées selon vos préférences et plusieurs critères.", "criteriaSelector": "Sélection des Critères", "selectedCriteria": "Critères sélectionnés", "noCriteriaSelected": "Aucun critère sélectionné", "availableCriteria": "Critères disponibles", "weight": "Poids", "direction": "Direction", "maximize": "Maximiser", "minimize": "Minimiser", "removeCriterion": "Supprimer ce critère", "optimizationMethod": "Méthode d'Optimisation", "aboutSelectedMethod": "À propos de la méthode sélectionnée", "recommendations": "Recommandations", "optimizedUsing": "Optimisé avec", "noRecommendations": "Aucune recommandation trouvée", "globalScore": "Score global", "paretoOptimal": "Pareto optimal", "viewDetails": "Voir détails", "seeMore": "Voir plus", "loadError": "<PERSON><PERSON>ur lors du chargement des recommandations", "loadCriteriaError": "Erreur lors du chargement des critères", "preferencesChangedWarning": "Vos préférences ont changé. Appliquez les changements pour mettre à jour les recommandations.", "applyChanges": "Appliquer les changements", "savePreferencesDescription": "Enregistrez vos préférences pour les retrouver lors de votre prochaine visite.", "savePreferences": "Enregistrer mes préférences", "preferencesSaved": "Vos préférences ont été enregistrées avec succès", "savePreferencesError": "Erreur lors de l'enregistrement des préférences", "methods": {"weighted_sum": "Somme pondérée", "pareto": "Optimisation de Pareto", "lexicographic": "Méthode lexicographique", "topsis": "Méthode TOPSIS"}, "methodDescriptions": {"weightedSum": "Combine les scores de chaque critère en fonction de leur poids", "pareto": "Identifie les solutions où aucun critère ne peut être amélioré sans en dégrader un autre", "lexicographic": "Compare les alternatives selon l'ordre d'importance des critères", "topsis": "Identifie les solutions les plus proches de l'idéal et les plus éloignées de l'anti-idéal"}, "methodDetails": {"weightedSum": {"p1": "La méthode de la somme pondérée est la plus simple et la plus intuitive des méthodes d'optimisation multi-critères.", "p2": "Elle consiste à multiplier chaque score normalisé par le poids du critère correspondant, puis à additionner ces scores pondérés :", "p3": "Cette méthode est facile à comprendre et à mettre en œuvre, mais elle suppose que les critères sont indépendants et que les préférences sont linéaires."}, "pareto": {"p1": "L'optimisation de Pareto identifie les solutions non-dominées, c'est-à-dire celles pour lesquelles il est impossible d'améliorer un critère sans dégrader au moins un autre critère.", "p2": "Une solution A domine une solution B si A est au moins aussi bonne que B sur tous les critères, et strictement meilleure sur au moins un critère.", "p3": "Cette méthode est particulièrement utile lorsque les critères sont en conflit et qu'il n'existe pas de solution optimale unique."}, "lexicographic": {"p1": "La méthode lexicographique trie les critères par ordre d'importance et compare les alternatives selon le critère le plus important.", "p2": "En cas d'égalité sur le critère le plus important, on passe au critère suivant, et ainsi de suite.", "p3": "Cette méthode est adaptée lorsqu'il existe une hiérarchie claire entre les critères, mais elle peut ignorer les compromis entre critères."}, "topsis": {"p1": "TOPSIS (Technique for Order of Preference by Similarity to Ideal Solution) identifie la solution idéale et anti-idéale, puis classe les alternatives selon leur proximité relative à la solution idéale.", "p2": "La solution idéale est celle qui maximise tous les critères à maximiser et minimise tous les critères à minimiser.", "p3": "Cette méthode prend en compte tous les critères simultanément et permet de trouver un bon compromis entre des critères conflictuels."}}}}