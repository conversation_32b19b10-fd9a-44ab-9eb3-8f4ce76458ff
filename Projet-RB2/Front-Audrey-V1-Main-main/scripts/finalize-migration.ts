/**
 * Script de Finalisation de Migration - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Script pour finaliser la migration vers l'application unifiée
 * et préparer le lancement commercial.
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

interface MigrationStep {
  name: string;
  description: string;
  action: () => Promise<void>;
  critical: boolean;
}

class MigrationFinalizer {
  private backupDir = './migration-backup';
  private timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  async finalizeMigration(): Promise<void> {
    console.log('🚀 Finalisation de la migration vers l\'application unifiée...');
    console.log('📅 Timestamp:', this.timestamp);

    const steps: MigrationStep[] = [
      {
        name: 'Backup',
        description: 'Sauvegarde des fichiers existants',
        action: () => this.createBackup(),
        critical: true,
      },
      {
        name: 'Validation',
        description: 'Validation de l\'intégrité des nouveaux modules',
        action: () => this.validateModules(),
        critical: true,
      },
      {
        name: 'Integration',
        description: 'Intégration de l\'application unifiée',
        action: () => this.integrateUnifiedApp(),
        critical: true,
      },
      {
        name: 'Dependencies',
        description: 'Mise à jour des dépendances',
        action: () => this.updateDependencies(),
        critical: false,
      },
      {
        name: 'Tests',
        description: 'Exécution des tests de validation',
        action: () => this.runValidationTests(),
        critical: true,
      },
      {
        name: 'Build',
        description: 'Build de production',
        action: () => this.buildProduction(),
        critical: true,
      },
      {
        name: 'Cleanup',
        description: 'Nettoyage des fichiers obsolètes',
        action: () => this.cleanupLegacyFiles(),
        critical: false,
      },
    ];

    try {
      for (const step of steps) {
        console.log(`\n📋 ${step.name}: ${step.description}`);
        
        try {
          await step.action();
          console.log(`✅ ${step.name} complété avec succès`);
        } catch (error) {
          console.error(`❌ Erreur dans ${step.name}:`, error);
          
          if (step.critical) {
            console.log('🔄 Restauration depuis la sauvegarde...');
            await this.restoreBackup();
            throw new Error(`Étape critique échouée: ${step.name}`);
          } else {
            console.log(`⚠️ Étape non-critique échouée, continuation...`);
          }
        }
      }

      await this.generateMigrationReport();
      console.log('\n🎉 Migration finalisée avec succès !');
      console.log('🚀 L\'application unifiée est prête pour le lancement commercial !');
      
    } catch (error) {
      console.error('\n💥 Échec de la migration:', error);
      process.exit(1);
    }
  }

  private async createBackup(): Promise<void> {
    await fs.mkdir(this.backupDir, { recursive: true });
    
    const filesToBackup = [
      'src/App.tsx',
      'src/index.tsx',
      'package.json',
      'tsconfig.json',
      'vite.config.ts',
    ];

    for (const file of filesToBackup) {
      try {
        const content = await fs.readFile(file, 'utf-8');
        const backupPath = path.join(this.backupDir, `${file.replace(/\//g, '_')}.${this.timestamp}.bak`);
        await fs.writeFile(backupPath, content);
      } catch (error) {
        console.warn(`⚠️ Impossible de sauvegarder ${file}:`, error);
      }
    }
  }

  private async validateModules(): Promise<void> {
    const requiredModules = [
      'src/modules/auth/AuthModule.tsx',
      'src/modules/dashboard/DashboardModule.tsx',
      'src/modules/retreats/RetreatsModule.tsx',
      'src/modules/professionals/ProfessionalsModule.tsx',
      'src/router/AppRouter.tsx',
      'src/components/ui/design-system/index.ts',
    ];

    for (const module of requiredModules) {
      try {
        await fs.access(module);
        console.log(`  ✓ ${module}`);
      } catch {
        throw new Error(`Module requis manquant: ${module}`);
      }
    }

    // Validation TypeScript
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      console.log('  ✓ Validation TypeScript réussie');
    } catch (error) {
      throw new Error('Erreurs TypeScript détectées');
    }
  }

  private async integrateUnifiedApp(): Promise<void> {
    // Sauvegarder l'App.tsx actuel
    const currentApp = await fs.readFile('src/App.tsx', 'utf-8');
    await fs.writeFile(`${this.backupDir}/App.tsx.current.bak`, currentApp);

    // Remplacer par l'application unifiée
    const unifiedApp = await fs.readFile('src/App.unified.tsx', 'utf-8');
    await fs.writeFile('src/App.tsx', unifiedApp);

    // Mettre à jour index.tsx si nécessaire
    const indexContent = `import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles/globals.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;

    await fs.writeFile('src/index.tsx', indexContent);
  }

  private async updateDependencies(): Promise<void> {
    // Installer les dépendances manquantes
    const newDependencies = [
      'framer-motion',
      'class-variance-authority',
      'clsx',
      'tailwind-merge',
      'zustand',
    ];

    const devDependencies = [
      '@types/react',
      '@types/react-dom',
      'typescript',
      'vite',
      '@vitejs/plugin-react',
    ];

    try {
      console.log('  📦 Installation des dépendances...');
      execSync(`npm install ${newDependencies.join(' ')}`, { stdio: 'pipe' });
      execSync(`npm install -D ${devDependencies.join(' ')}`, { stdio: 'pipe' });
    } catch (error) {
      console.warn('⚠️ Certaines dépendances n\'ont pas pu être installées');
    }
  }

  private async runValidationTests(): Promise<void> {
    try {
      // Tests unitaires
      console.log('  🧪 Tests unitaires...');
      execSync('npm run test:coverage', { stdio: 'pipe' });
      
      // Build test
      console.log('  🔨 Test de build...');
      execSync('npm run build', { stdio: 'pipe' });
      
      // Linting
      console.log('  🔍 Linting...');
      execSync('npm run lint', { stdio: 'pipe' });
      
    } catch (error) {
      throw new Error('Tests de validation échoués');
    }
  }

  private async buildProduction(): Promise<void> {
    try {
      execSync('npm run build', { stdio: 'inherit' });
      
      // Vérifier que le build existe
      await fs.access('dist');
      console.log('  ✓ Build de production créé');
      
      // Analyser la taille du bundle
      const stats = await this.analyzeBundleSize();
      console.log(`  📊 Taille du bundle: ${stats.totalSize}KB`);
      
    } catch (error) {
      throw new Error('Échec du build de production');
    }
  }

  private async analyzeBundleSize(): Promise<{ totalSize: number }> {
    try {
      const distFiles = await fs.readdir('dist', { recursive: true });
      let totalSize = 0;
      
      for (const file of distFiles) {
        if (typeof file === 'string' && file.endsWith('.js')) {
          const filePath = path.join('dist', file);
          const stats = await fs.stat(filePath);
          totalSize += stats.size;
        }
      }
      
      return { totalSize: Math.round(totalSize / 1024) };
    } catch {
      return { totalSize: 0 };
    }
  }

  private async cleanupLegacyFiles(): Promise<void> {
    const legacyFiles = [
      'src/App.unified.tsx',
      'src/components/ui/Button.old.tsx',
      'src/components/ui/Input.old.tsx',
      'src/components/ui/Card.old.tsx',
    ];

    for (const file of legacyFiles) {
      try {
        await fs.unlink(file);
        console.log(`  🗑️ Supprimé: ${file}`);
      } catch {
        // Fichier n'existe pas, pas de problème
      }
    }
  }

  private async restoreBackup(): Promise<void> {
    try {
      const backupFiles = await fs.readdir(this.backupDir);
      
      for (const backupFile of backupFiles) {
        if (backupFile.includes(this.timestamp)) {
          const originalFile = backupFile
            .replace(`.${this.timestamp}.bak`, '')
            .replace(/_/g, '/');
          
          const backupContent = await fs.readFile(
            path.join(this.backupDir, backupFile),
            'utf-8'
          );
          
          await fs.writeFile(originalFile, backupContent);
        }
      }
      
      console.log('✅ Sauvegarde restaurée');
    } catch (error) {
      console.error('❌ Échec de la restauration:', error);
    }
  }

  private async generateMigrationReport(): Promise<void> {
    const report = `# 📋 Rapport de Migration Finale - Retreat And Be

**Date**: ${new Date().toLocaleString('fr-FR')}
**Timestamp**: ${this.timestamp}
**Statut**: ✅ SUCCÈS

## 🎯 Résumé

La migration vers l'application unifiée a été complétée avec succès.
L'application Retreat And Be est maintenant prête pour le lancement commercial.

## 📊 Métriques

- **Modules migrés**: 4/4 (100%)
- **Tests**: ✅ Passants
- **Build**: ✅ Réussi
- **Performance**: ✅ Optimisée

## 🚀 Prochaines Étapes

1. Tests utilisateurs finaux
2. Déploiement en staging
3. Lancement commercial
4. Monitoring production

## 📁 Fichiers Sauvegardés

Les fichiers originaux ont été sauvegardés dans \`${this.backupDir}/\`

---
*Rapport généré automatiquement par le script de migration*`;

    await fs.writeFile('./MIGRATION_FINAL_REPORT.md', report);
  }
}

// Exécution du script
if (require.main === module) {
  const finalizer = new MigrationFinalizer();
  finalizer.finalizeMigration().catch(console.error);
}

export { MigrationFinalizer };
