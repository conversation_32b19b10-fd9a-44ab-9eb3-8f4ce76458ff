/**
 * Audit de Migration - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Script d'audit pour identifier tous les composants
 * et modules à migrer vers le nouveau design system.
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

interface ComponentAudit {
  name: string;
  path: string;
  type: 'page' | 'component' | 'organism' | 'molecule' | 'atom';
  status: 'legacy' | 'migrated' | 'new';
  priority: 'critical' | 'high' | 'medium' | 'low';
  estimatedEffort: number; // en heures
  dependencies: string[];
  usedBy: string[];
  migrationNotes: string[];
}

interface MigrationAudit {
  timestamp: string;
  totalComponents: number;
  legacyComponents: number;
  migratedComponents: number;
  newComponents: number;
  components: ComponentAudit[];
  migrationPlan: {
    phase1: ComponentAudit[];
    phase2: ComponentAudit[];
    phase3: ComponentAudit[];
  };
  estimatedTotalEffort: number;
}

class MigrationAuditor {
  private srcPath = './src';
  private designSystemPath = './src/components/ui/design-system';
  private legacyPatterns = [
    'react-scripts',
    'className="btn',
    'className="card',
    'className="modal',
    'className="input',
    'className="form',
    'styled-components',
    '@emotion/styled',
    'makeStyles',
    'useStyles'
  ];

  async runAudit(): Promise<void> {
    console.log('🔍 Démarrage de l\'audit de migration...');

    try {
      const audit = await this.generateAudit();
      await this.saveAuditReport(audit);
      await this.generateMigrationPlan(audit);
      
      console.log('✅ Audit de migration terminé avec succès!');
      console.log(`📊 ${audit.legacyComponents} composants à migrer sur ${audit.totalComponents}`);
      console.log(`⏱️ Effort estimé: ${audit.estimatedTotalEffort} heures`);
      
    } catch (error) {
      console.error('❌ Erreur lors de l\'audit:', error);
      process.exit(1);
    }
  }

  private async generateAudit(): Promise<MigrationAudit> {
    const components = await this.scanAllComponents();
    const migrationPlan = this.createMigrationPlan(components);
    
    const legacyComponents = components.filter(c => c.status === 'legacy').length;
    const migratedComponents = components.filter(c => c.status === 'migrated').length;
    const newComponents = components.filter(c => c.status === 'new').length;
    
    return {
      timestamp: new Date().toISOString(),
      totalComponents: components.length,
      legacyComponents,
      migratedComponents,
      newComponents,
      components,
      migrationPlan,
      estimatedTotalEffort: components.reduce((sum, c) => sum + c.estimatedEffort, 0),
    };
  }

  private async scanAllComponents(): Promise<ComponentAudit[]> {
    const components: ComponentAudit[] = [];
    
    // Scanner les pages
    const pages = await this.scanDirectory('./src/pages', 'page');
    components.push(...pages);
    
    // Scanner les composants
    const comps = await this.scanDirectory('./src/components', 'component');
    components.push(...comps);
    
    // Analyser les dépendances et usages
    for (const component of components) {
      component.dependencies = await this.findDependencies(component.path);
      component.usedBy = await this.findUsages(component.name);
    }
    
    return components;
  }

  private async scanDirectory(dirPath: string, type: ComponentAudit['type']): Promise<ComponentAudit[]> {
    const components: ComponentAudit[] = [];
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          // Récursion dans les sous-dossiers
          const subComponents = await this.scanDirectory(fullPath, type);
          components.push(...subComponents);
        } else if (this.isReactFile(entry.name)) {
          const component = await this.analyzeComponent(fullPath, type);
          if (component) {
            components.push(component);
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ Impossible de scanner ${dirPath}:`, error);
    }
    
    return components;
  }

  private isReactFile(filename: string): boolean {
    return /\.(tsx|jsx)$/.test(filename) && 
           !filename.includes('.test.') && 
           !filename.includes('.stories.');
  }

  private async analyzeComponent(filePath: string, type: ComponentAudit['type']): Promise<ComponentAudit | null> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const name = path.basename(filePath, path.extname(filePath));
      
      const status = this.determineStatus(content, name);
      const priority = this.determinePriority(content, filePath, type);
      const estimatedEffort = this.estimateEffort(content, type);
      const migrationNotes = this.generateMigrationNotes(content);
      
      return {
        name,
        path: filePath,
        type,
        status,
        priority,
        estimatedEffort,
        dependencies: [],
        usedBy: [],
        migrationNotes,
      };
    } catch (error) {
      console.warn(`⚠️ Erreur lors de l'analyse de ${filePath}:`, error);
      return null;
    }
  }

  private determineStatus(content: string, name: string): ComponentAudit['status'] {
    // Vérifier si c'est un nouveau composant du design system
    if (content.includes('class-variance-authority') || 
        content.includes('design-system') ||
        content.includes('@/components/ui/design-system')) {
      return 'new';
    }
    
    // Vérifier si c'est déjà migré
    if (content.includes('from \'./design-system\'') ||
        content.includes('from \'@/components/ui/design-system\'')) {
      return 'migrated';
    }
    
    // Sinon c'est legacy
    return 'legacy';
  }

  private determinePriority(content: string, filePath: string, type: ComponentAudit['type']): ComponentAudit['priority'] {
    // Pages critiques
    if (type === 'page' && (
      filePath.includes('Login') ||
      filePath.includes('Register') ||
      filePath.includes('Dashboard') ||
      filePath.includes('HomePage')
    )) {
      return 'critical';
    }
    
    // Composants utilisés partout
    if (this.legacyPatterns.some(pattern => content.includes(pattern))) {
      return 'high';
    }
    
    // Composants UI de base
    if (filePath.includes('/ui/') || filePath.includes('Button') || filePath.includes('Input')) {
      return 'high';
    }
    
    // Organismes importants
    if (type === 'organism' || filePath.includes('NavBar') || filePath.includes('Footer')) {
      return 'medium';
    }
    
    return 'low';
  }

  private estimateEffort(content: string, type: ComponentAudit['type']): number {
    const lines = content.split('\n').length;
    
    // Facteurs de complexité
    let baseHours = 0;
    
    switch (type) {
      case 'page':
        baseHours = 4;
        break;
      case 'organism':
        baseHours = 3;
        break;
      case 'molecule':
        baseHours = 2;
        break;
      case 'component':
        baseHours = 2;
        break;
      case 'atom':
        baseHours = 1;
        break;
    }
    
    // Ajustements basés sur la complexité
    if (lines > 200) baseHours *= 1.5;
    if (content.includes('useState') || content.includes('useEffect')) baseHours *= 1.2;
    if (content.includes('styled-components') || content.includes('@emotion')) baseHours *= 1.3;
    if (content.includes('complex') || content.includes('animation')) baseHours *= 1.4;
    
    return Math.ceil(baseHours);
  }

  private generateMigrationNotes(content: string): string[] {
    const notes: string[] = [];
    
    if (content.includes('styled-components')) {
      notes.push('Remplacer styled-components par Tailwind CSS');
    }
    
    if (content.includes('@emotion')) {
      notes.push('Remplacer Emotion par Tailwind CSS');
    }
    
    if (content.includes('className="btn')) {
      notes.push('Migrer vers le composant Button du design system');
    }
    
    if (content.includes('className="card')) {
      notes.push('Migrer vers le composant Card du design system');
    }
    
    if (content.includes('className="modal')) {
      notes.push('Migrer vers le composant Modal du design system');
    }
    
    if (content.includes('useState') && content.includes('form')) {
      notes.push('Considérer l\'utilisation de react-hook-form');
    }
    
    if (content.includes('fetch(') || content.includes('axios')) {
      notes.push('Vérifier l\'intégration avec les services API');
    }
    
    return notes;
  }

  private async findDependencies(filePath: string): Promise<string[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const imports = content.match(/import.*from\s+['"]([^'"]+)['"]/g) || [];
      
      return imports
        .map(imp => imp.match(/from\s+['"]([^'"]+)['"]/)?.[1])
        .filter(Boolean)
        .filter(dep => dep?.startsWith('./') || dep?.startsWith('../'))
        .map(dep => dep!) as string[];
    } catch {
      return [];
    }
  }

  private async findUsages(componentName: string): Promise<string[]> {
    try {
      // Utiliser grep pour trouver les usages
      const result = execSync(
        `grep -r "import.*${componentName}" src/ --include="*.tsx" --include="*.ts" || true`,
        { encoding: 'utf-8' }
      );
      
      return result
        .split('\n')
        .filter(line => line.trim())
        .map(line => line.split(':')[0])
        .filter(Boolean);
    } catch {
      return [];
    }
  }

  private createMigrationPlan(components: ComponentAudit[]): MigrationAudit['migrationPlan'] {
    const legacyComponents = components.filter(c => c.status === 'legacy');
    
    const phase1 = legacyComponents.filter(c => c.priority === 'critical');
    const phase2 = legacyComponents.filter(c => c.priority === 'high');
    const phase3 = legacyComponents.filter(c => ['medium', 'low'].includes(c.priority));
    
    return { phase1, phase2, phase3 };
  }

  private async saveAuditReport(audit: MigrationAudit): Promise<void> {
    const reportDir = './migration-reports';
    
    try {
      await fs.access(reportDir);
    } catch {
      await fs.mkdir(reportDir, { recursive: true });
    }
    
    // Rapport JSON
    await fs.writeFile(
      path.join(reportDir, 'migration-audit.json'),
      JSON.stringify(audit, null, 2)
    );
    
    // Rapport Markdown
    const markdown = this.generateMarkdownReport(audit);
    await fs.writeFile(
      path.join(reportDir, 'migration-audit.md'),
      markdown
    );
    
    console.log(`📄 Rapports sauvegardés dans ${reportDir}/`);
  }

  private generateMarkdownReport(audit: MigrationAudit): string {
    return `# 📋 Rapport d'Audit de Migration - Retreat And Be

**Date**: ${new Date(audit.timestamp).toLocaleString('fr-FR')}

## 📊 Résumé

- **Total composants**: ${audit.totalComponents}
- **Composants legacy**: ${audit.legacyComponents}
- **Composants migrés**: ${audit.migratedComponents}
- **Nouveaux composants**: ${audit.newComponents}
- **Effort total estimé**: ${audit.estimatedTotalEffort} heures

## 🚀 Plan de Migration

### Phase 1 - Critique (${audit.migrationPlan.phase1.length} composants)
${audit.migrationPlan.phase1.map(c => `- **${c.name}** (${c.estimatedEffort}h) - ${c.path}`).join('\n')}

### Phase 2 - Priorité Élevée (${audit.migrationPlan.phase2.length} composants)
${audit.migrationPlan.phase2.map(c => `- **${c.name}** (${c.estimatedEffort}h) - ${c.path}`).join('\n')}

### Phase 3 - Priorité Moyenne/Basse (${audit.migrationPlan.phase3.length} composants)
${audit.migrationPlan.phase3.map(c => `- **${c.name}** (${c.estimatedEffort}h) - ${c.path}`).join('\n')}

## 📝 Détails des Composants Legacy

${audit.components
  .filter(c => c.status === 'legacy')
  .map(c => `
### ${c.name}
- **Type**: ${c.type}
- **Priorité**: ${c.priority}
- **Effort**: ${c.estimatedEffort}h
- **Path**: ${c.path}
- **Notes de migration**:
${c.migrationNotes.map(note => `  - ${note}`).join('\n')}
`).join('\n')}

---
*Rapport généré automatiquement par l'outil d'audit de migration*`;
  }

  private async generateMigrationPlan(audit: MigrationAudit): Promise<void> {
    const plan = `#!/bin/bash
# Plan de Migration Automatique - Retreat And Be
# Généré le ${new Date().toLocaleString('fr-FR')}

echo "🚀 Démarrage de la migration vers le design system..."

# Phase 1 - Composants Critiques
echo "📋 Phase 1: Migration des composants critiques..."
${audit.migrationPlan.phase1.map(c => `echo "Migrating ${c.name}..."`).join('\n')}

# Phase 2 - Priorité Élevée  
echo "📋 Phase 2: Migration des composants prioritaires..."
${audit.migrationPlan.phase2.map(c => `echo "Migrating ${c.name}..."`).join('\n')}

# Phase 3 - Finalisation
echo "📋 Phase 3: Migration finale..."
${audit.migrationPlan.phase3.map(c => `echo "Migrating ${c.name}..."`).join('\n')}

echo "✅ Migration terminée!"
`;

    await fs.writeFile('./migration-reports/migration-plan.sh', plan);
    await fs.chmod('./migration-reports/migration-plan.sh', 0o755);
  }
}

// Exécution du script
if (require.main === module) {
  const auditor = new MigrationAuditor();
  auditor.runAudit().catch(console.error);
}

export { MigrationAuditor };
