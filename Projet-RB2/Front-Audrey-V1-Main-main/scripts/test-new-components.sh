#!/bin/bash

# Script pour tester les nouveaux composants

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Début des tests des nouveaux composants...${NC}"

# Vérifier si les dossiers de composants existent
if [ ! -d "./src/components/moderation" ] || [ ! -d "./src/components/analytics" ]; then
    echo -e "${RED}Erreur: Les dossiers de composants de modération ou d'analyse n'existent pas.${NC}"
    exit 1
fi

# Installer les dépendances nécessaires pour les tests
echo -e "${YELLOW}Installation des dépendances pour les tests...${NC}"
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-mock-extended
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'installation des dépendances pour les tests.${NC}"
    exit 1
fi
echo -e "${GREEN}Dépendances installées avec succès.${NC}"

# Exécuter les tests pour les composants de modération
echo -e "${YELLOW}Exécution des tests pour les composants de modération...${NC}"
npm test -- --testPathPattern=src/components/moderation
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests pour les composants de modération.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests pour les composants de modération exécutés avec succès.${NC}"

# Exécuter les tests pour les composants d'analyse
echo -e "${YELLOW}Exécution des tests pour les composants d'analyse...${NC}"
npm test -- --testPathPattern=src/components/analytics
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests pour les composants d'analyse.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests pour les composants d'analyse exécutés avec succès.${NC}"

# Exécuter les tests pour les composants UI
echo -e "${YELLOW}Exécution des tests pour les composants UI...${NC}"
npm test -- --testPathPattern=src/components/ui
if [ $? -ne 0 ]; then
    echo -e "${RED}Erreur lors de l'exécution des tests pour les composants UI.${NC}"
    exit 1
fi
echo -e "${GREEN}Tests pour les composants UI exécutés avec succès.${NC}"

echo -e "${GREEN}Tous les tests ont été exécutés avec succès.${NC}"
