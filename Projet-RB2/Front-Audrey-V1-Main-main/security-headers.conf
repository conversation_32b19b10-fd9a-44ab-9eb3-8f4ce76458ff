# En-têtes de sécurité pour l'application Frontend (Front-Audrey-V1-Main-main)
# À inclure dans la configuration du serveur web (Nginx, Apache, etc.)

# Content-Security-Policy (CSP)
# Configuration en production après validation des règles (actuellement en mode report-only dans le meta tag)
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://api.mapbox.com https://events.mapbox.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://api.mapbox.com https://fonts.googleapis.com; img-src 'self' data: blob: https://*.tile.openstreetmap.org https://api.mapbox.com https://* https://www.google-analytics.com; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' https://api.mapbox.com https://events.mapbox.com https://api.retreat-and-be.com https://www.google-analytics.com; frame-src 'self'; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; report-to csp-endpoint;" always;

# Report-To Header (pour les rapports de violation CSP modernes)
add_header Report-To '{"group":"csp-endpoint","max_age":10886400,"endpoints":[{"url":"https://api.retreat-and-be.com/security/csp-report"}]}' always;

# HTTP Strict Transport Security (HSTS)
# Force le navigateur à utiliser HTTPS pour toutes les futures requêtes 
# max-age=31536000 équivaut à 1 an (en secondes)
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# X-Frame-Options
# Empêche le site d'être affiché dans une iframe (protection contre le clickjacking)
add_header X-Frame-Options "DENY" always;

# X-Content-Type-Options
# Empêche le navigateur de faire du MIME-sniffing (détection automatique du type de contenu)
add_header X-Content-Type-Options "nosniff" always;

# Referrer-Policy
# Contrôle les informations envoyées via l'en-tête Referer
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Permissions-Policy (remplace Feature-Policy)
# Contrôle quelles fonctionnalités et API peuvent être utilisées
add_header Permissions-Policy "camera=(), microphone=(), geolocation=(self), interest-cohort=(), payment=(), usb=(), screen-wake-lock=(), ambient-light-sensor=(), accelerometer=(), gyroscope=()" always;

# X-XSS-Protection
# Protection supplémentaire contre les attaques XSS pour les anciens navigateurs
# Note: Les navigateurs modernes utilisent CSP à la place, mais cela ne fait pas de mal
add_header X-XSS-Protection "1; mode=block" always;

# Cache-Control
# Empêche la mise en cache des documents HTML pour réduire le risque de fuite d'informations sensibles
# Ajuster selon les besoins pour les fichiers statiques (CSS, JS, images)
add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0" always;

# Clear-Site-Data (à utiliser pour les pages de déconnexion)
# Efface toutes les données du site lors de la déconnexion
# Décommentez et ajoutez cette directive aux endpoints de déconnexion
# add_header Clear-Site-Data "\"cache\", \"cookies\", \"storage\"" always; 