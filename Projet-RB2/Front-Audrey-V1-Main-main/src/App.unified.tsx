/**
 * Application Unifiée - Retreat And Be
 * Date de création: 25 mai 2025
 * 
 * Point d'entrée principal de l'application unifiée
 * intégrant tous les modules avec le design system.
 */

import React from 'react';
import { HelmetProvider } from 'react-helmet-async';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import AppRouter from './router/AppRouter';
import { GlobalStoreProvider } from './store/globalStore';
import './styles/globals.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <HelmetProvider>
        <GlobalStoreProvider>
          <div className="App min-h-screen bg-neutral-50">
            <AppRouter />
          </div>
          {process.env.NODE_ENV === 'development' && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </GlobalStoreProvider>
      </HelmetProvider>
    </QueryClientProvider>
  );
}

export default App;
