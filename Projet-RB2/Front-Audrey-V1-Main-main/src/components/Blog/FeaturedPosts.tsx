import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const FeaturedPosts: React.FC = () => {
  return (
    <section className='mb-12'>
      <h2 className='text-3xl font-bold text-gray-900 mb-6'>Articles à la une</h2>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {featuredPosts.map((post) => (
          <motion.article
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='bg-white rounded-lg shadow-sm overflow-hidden'
          >
            <Link to={post.slug}>
              <img src={post.image} alt={post.title} className='w-full h-48 object-cover' />
              <div className='p-6'>
                <div className='flex items-center mb-2'>
                  <span className='text-sm text-retreat-green'>{post.category}</span>
                  <span className='mx-2'>•</span>
                  <span className='text-sm text-gray-500'>{post.date}</span>
                </div>
                <h3 className='text-xl font-semibold text-gray-900 mb-2'>{post.title}</h3>
                <p className='text-gray-600'>{post.excerpt}</p>
              </div>
            </Link>
          </motion.article>
        ))}
      </div>
    </section>
  );
};

const featuredPosts = [
  {
    id: 1,
    title: 'Les bienfaits de la méditation en pleine conscience',
    slug: '/blog/meditation-pleine-conscience',
    excerpt:
      'Découvrez comment la méditation peut transformer votre quotidien et améliorer votre bien-être mental.',
    image: '/images/blog/meditation.jpg',
    category: 'Bien-être',
    date: '12 Mars 2024',
  },
  // Ajoutez d'autres articles à la une
];

export default FeaturedPosts;
