import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MagnifyingGlassIcon, MicrophoneIcon } from '@heroicons/react/24/outline';

const SearchBar: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);

  const handleVoiceSearch = () => {
    setIsRecording(!isRecording);
    // TODO: Implémenter la logique de recherche vocale
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className='relative w-full max-w-2xl'
    >
      <div className='relative flex items-center'>
        <input
          type='text'
          placeholder='Rechercher une retraite...'
          className='w-full px-6 py-4 text-lg rounded-full border-2 border-mint-200 focus:border-mint-400 focus:outline-none focus:ring-2 focus:ring-mint-200 transition-all duration-300 shadow-md'
        />
        <div className='absolute right-4 flex items-center gap-2'>
          <button
            onClick={handleVoiceSearch}
            className={`p-2 rounded-full transition-all duration-300 ${
              isRecording
                ? 'bg-red-100 text-red-600 animate-pulse'
                : 'hover:bg-mint-100 text-retreat-green'
            }`}
            title='Recherche vocale'
          >
            <MicrophoneIcon className='h-5 w-5' />
          </button>
          <button className='p-2 rounded-full hover:bg-mint-100 text-retreat-green transition-colors duration-300'>
            <MagnifyingGlassIcon className='h-5 w-5' />
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default SearchBar;
