import React from 'react';
import { useTranslationContext } from '../../providers/TranslationProvider';
import { SupportedLanguage } from '../../services/i18n/i18nService';
import './LanguageSwitcher.css';

// Interface pour les props du composant
interface LanguageSwitcherProps {
  className?: string;
  variant?: 'dropdown' | 'buttons' | 'select';
}

/**
 * Composant pour changer la langue
 */
const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className = '',
  variant = 'dropdown',
}) => {
  const { currentLanguage, changeLanguage, isReady } = useTranslationContext();

  // Langues disponibles avec leurs libellés
  const languages: Record<SupportedLanguage, string> = {
    fr: 'Français',
    en: 'English',
    es: 'Español',
    de: 'Deutsch',
  };

  // Si le service n'est pas initialisé, ne rien afficher
  if (!isReady) {
    return null;
  }

  // Rendu pour la variante dropdown
  if (variant === 'dropdown') {
    return (
      <div className={`language-switcher language-switcher--dropdown ${className}`}>
        <select
          value={currentLanguage}
          onChange={(e) => changeLanguage(e.target.value as SupportedLanguage)}
          className='language-switcher__select'
        >
          {Object.entries(languages).map(([code, label]) => (
            <option key={code} value={code}>
              {label}
            </option>
          ))}
        </select>
      </div>
    );
  }

  // Rendu pour la variante buttons
  if (variant === 'buttons') {
    return (
      <div className={`language-switcher language-switcher--buttons ${className}`}>
        {Object.entries(languages).map(([code, label]) => (
          <button
            key={code}
            onClick={() => changeLanguage(code as SupportedLanguage)}
            className={`language-switcher__button ${
              currentLanguage === code ? 'language-switcher__button--active' : ''
            }`}
          >
            {code.toUpperCase()}
          </button>
        ))}
      </div>
    );
  }

  // Rendu pour la variante select (par défaut)
  return (
    <div className={`language-switcher language-switcher--select ${className}`}>
      <div className='language-switcher__current'>{languages[currentLanguage]}</div>
      <div className='language-switcher__options'>
        {Object.entries(languages).map(([code, label]) => (
          <div
            key={code}
            onClick={() => changeLanguage(code as SupportedLanguage)}
            className={`language-switcher__option ${
              currentLanguage === code ? 'language-switcher__option--active' : ''
            }`}
          >
            {label}
          </div>
        ))}
      </div>
    </div>
  );
};

export default LanguageSwitcher;
