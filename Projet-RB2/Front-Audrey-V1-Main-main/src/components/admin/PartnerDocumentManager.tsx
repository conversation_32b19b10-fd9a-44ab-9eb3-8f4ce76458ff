import React, { useState, useEffect } from 'react';
import { partnerService, PartnerDocument } from '../../services/api/partnerService';
import { fileService } from '../../services/api/fileService';
import { toast } from 'react-toastify';

// Document types mapping
const DOCUMENT_TYPES: Record<string, string> = {
  'IDENTITY': 'Pièce d\'identité',
  'BUSINESS_REGISTRATION': 'Immatriculation d\'entreprise',
  'INSURANCE': 'Attestation d\'assurance',
  'CERTIFICATION': 'Certification professionnelle',
  'TAX_DOCUMENT': 'Document fiscal',
  'BANK_DETAILS': 'Coordonnées bancaires',
  'OTHER': 'Autre document',
};

// Document status options
const DOCUMENT_STATUS_OPTIONS = [
  { value: 'PENDING', label: 'En attente de vérification', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'VERIFIED', label: 'Vérifié', color: 'bg-green-100 text-green-800' },
  { value: 'REJECTED', label: 'Rejeté', color: 'bg-red-100 text-red-800' },
  { value: 'EXPIRED', label: 'Expiré', color: 'bg-gray-100 text-gray-800' },
];

interface PartnerDocumentManagerProps {
  partnerId: string;
  onStatusChange?: () => void;
}

const PartnerDocumentManager: React.FC<PartnerDocumentManagerProps> = ({ 
  partnerId, 
  onStatusChange 
}) => {
  const [documents, setDocuments] = useState<PartnerDocument[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<PartnerDocument | null>(null);
  const [documentStatus, setDocumentStatus] = useState<string>('PENDING');
  const [verificationNotes, setVerificationNotes] = useState<string>('');
  const [securityScanResult, setSecurityScanResult] = useState<any>(null);

  // Load documents
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const data = await partnerService.getDocuments(partnerId);
        setDocuments(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching partner documents:', err);
        setError('Impossible de récupérer les documents du partenaire');
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [partnerId]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Handle document selection
  const handleSelectDocument = async (document: PartnerDocument) => {
    setSelectedDocument(document);
    setDocumentStatus(document.status || 'PENDING');
    setVerificationNotes(document.verificationNotes || '');
    
    // Simulate security scan
    setSecurityScanResult({
      scanning: true,
      result: null,
    });
    
    try {
      // In a real implementation, this would call a security scan API
      // For now, we'll simulate a scan with a timeout
      setTimeout(() => {
        setSecurityScanResult({
          scanning: false,
          result: {
            clean: Math.random() > 0.1, // 90% chance of being clean
            integrityVerified: Math.random() > 0.05, // 95% chance of integrity being verified
            scanDate: new Date().toISOString(),
            threatDetails: Math.random() > 0.9 ? ['Suspicious content detected'] : [],
          }
        });
      }, 1500);
    } catch (err) {
      console.error('Error scanning document:', err);
      setSecurityScanResult({
        scanning: false,
        result: {
          error: 'Échec de l\'analyse de sécurité',
        }
      });
    }
  };

  // Handle document status update
  const handleUpdateStatus = async () => {
    if (!selectedDocument) return;
    
    try {
      // In a real implementation, this would call an API to update the document status
      // For now, we'll just update the local state
      const updatedDocument = {
        ...selectedDocument,
        status: documentStatus,
        verificationNotes,
        verifiedAt: new Date().toISOString(),
      };
      
      // Update the document in the list
      const updatedDocuments = documents.map(doc => 
        doc.id === selectedDocument.id ? updatedDocument : doc
      );
      
      setDocuments(updatedDocuments);
      setSelectedDocument(updatedDocument);
      
      // Notify parent component if status changed
      if (onStatusChange) {
        onStatusChange();
      }
      
      toast.success('Statut du document mis à jour avec succès');
    } catch (err) {
      console.error('Error updating document status:', err);
      toast.error('Erreur lors de la mise à jour du statut du document');
    }
  };

  // Handle document download
  const handleDownloadDocument = async () => {
    if (!selectedDocument) return;
    
    try {
      // In a real implementation, this would download the file
      // For now, we'll just show a toast
      toast.info('Téléchargement du document...');
    } catch (err) {
      console.error('Error downloading document:', err);
      toast.error('Erreur lors du téléchargement du document');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Document List */}
        <div className="p-4 border-r border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Documents du partenaire</h3>
          
          {documents.length === 0 ? (
            <p className="text-gray-500">Aucun document soumis</p>
          ) : (
            <ul className="space-y-2">
              {documents.map((document) => (
                <li 
                  key={document.id}
                  className={`p-3 rounded-md cursor-pointer hover:bg-gray-50 ${
                    selectedDocument?.id === document.id ? 'bg-blue-50 border border-blue-200' : ''
                  }`}
                  onClick={() => handleSelectDocument(document)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{DOCUMENT_TYPES[document.type] || document.type}</p>
                      <p className="text-sm text-gray-500">{document.description}</p>
                      <p className="text-xs text-gray-400">Ajouté le {formatDate(document.createdAt)}</p>
                    </div>
                    {document.status && (
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        DOCUMENT_STATUS_OPTIONS.find(opt => opt.value === document.status)?.color || 'bg-gray-100'
                      }`}>
                        {DOCUMENT_STATUS_OPTIONS.find(opt => opt.value === document.status)?.label || document.status}
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
        
        {/* Document Details */}
        <div className="p-4 md:col-span-2">
          {selectedDocument ? (
            <div>
              <h3 className="text-lg font-semibold mb-4">Détails du document</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <p className="font-medium">{DOCUMENT_TYPES[selectedDocument.type] || selectedDocument.type}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Description</p>
                  <p className="font-medium">{selectedDocument.description}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Date d'ajout</p>
                  <p className="font-medium">{formatDate(selectedDocument.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">ID du fichier</p>
                  <p className="font-medium">{selectedDocument.fileId}</p>
                </div>
              </div>
              
              {/* Security Scan Results */}
              <div className="mb-6">
                <h4 className="font-medium mb-2">Analyse de sécurité</h4>
                
                {securityScanResult?.scanning ? (
                  <div className="flex items-center space-x-2 text-yellow-600">
                    <div className="animate-spin h-4 w-4 border-2 border-yellow-600 rounded-full border-t-transparent"></div>
                    <span>Analyse en cours...</span>
                  </div>
                ) : securityScanResult?.result ? (
                  <div className={`p-3 rounded-md ${
                    securityScanResult.result.clean && securityScanResult.result.integrityVerified
                      ? 'bg-green-50'
                      : 'bg-red-50'
                  }`}>
                    <div className="flex items-start space-x-2">
                      <div className={`mt-1 h-4 w-4 rounded-full ${
                        securityScanResult.result.clean && securityScanResult.result.integrityVerified
                          ? 'bg-green-500'
                          : 'bg-red-500'
                      }`}></div>
                      <div>
                        <p className={`font-medium ${
                          securityScanResult.result.clean && securityScanResult.result.integrityVerified
                            ? 'text-green-800'
                            : 'text-red-800'
                        }`}>
                          {securityScanResult.result.clean && securityScanResult.result.integrityVerified
                            ? 'Document sécurisé'
                            : 'Problème de sécurité détecté'}
                        </p>
                        <ul className="mt-1 text-sm">
                          <li className={securityScanResult.result.clean ? 'text-green-700' : 'text-red-700'}>
                            Malware: {securityScanResult.result.clean ? 'Non détecté' : 'Détecté'}
                          </li>
                          <li className={securityScanResult.result.integrityVerified ? 'text-green-700' : 'text-red-700'}>
                            Intégrité: {securityScanResult.result.integrityVerified ? 'Vérifiée' : 'Compromise'}
                          </li>
                          {securityScanResult.result.threatDetails?.length > 0 && (
                            <li className="text-red-700">
                              Détails: {securityScanResult.result.threatDetails.join(', ')}
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">Cliquez sur "Analyser" pour vérifier la sécurité du document</p>
                )}
              </div>
              
              {/* Verification Form */}
              <div className="mb-6">
                <h4 className="font-medium mb-2">Vérification du document</h4>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Statut
                    </label>
                    <select
                      value={documentStatus}
                      onChange={(e) => setDocumentStatus(e.target.value)}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    >
                      {DOCUMENT_STATUS_OPTIONS.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes de vérification
                    </label>
                    <textarea
                      value={verificationNotes}
                      onChange={(e) => setVerificationNotes(e.target.value)}
                      rows={3}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                      placeholder="Ajoutez des notes concernant la vérification du document..."
                    />
                  </div>
                </div>
              </div>
              
              {/* Actions */}
              <div className="flex justify-between">
                <button
                  onClick={handleDownloadDocument}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Télécharger
                </button>
                
                <button
                  onClick={handleUpdateStatus}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  Mettre à jour le statut
                </button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <p>Sélectionnez un document pour voir les détails</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PartnerDocumentManager;
