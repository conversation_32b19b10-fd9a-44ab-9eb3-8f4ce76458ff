/**
 * Configuration des Agents
 * Interface de paramétrage et gestion des agents
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Grid,
  Badge,
  Input,
  Select,
  Switch,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from '../ui/design-system';
import { useToast } from '../ui/design-system/Toast';
import { agentsService } from '../../services/api/agentsService';

interface AgentStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  uptime: number;
  lastHeartbeat: Date;
  activeJobs: number;
  totalJobs: number;
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  version: string;
  endpoint: string;
}

interface AgentConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  autoRestart: boolean;
  maxJobs: number;
  timeout: number;
  retryAttempts: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  resources: {
    cpu: number;
    memory: number;
    disk: number;
  };
  environment: Record<string, string>;
  dependencies: string[];
  healthCheck: {
    enabled: boolean;
    interval: number;
    timeout: number;
    retries: number;
  };
  scaling: {
    enabled: boolean;
    minInstances: number;
    maxInstances: number;
    targetCpu: number;
    targetMemory: number;
  };
}

interface AgentConfigurationProps {
  agents: AgentStatus[];
  onUpdate: () => void;
}

export const AgentConfiguration: React.FC<AgentConfigurationProps> = ({
  agents,
  onUpdate
}) => {
  const { toast } = useToast();
  
  // États
  const [configs, setConfigs] = useState<AgentConfig[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<AgentConfig | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Charger les configurations
  const loadConfigs = async () => {
    try {
      setIsLoading(true);
      const response = await agentsService.getAgentConfigs();
      setConfigs(response.data);
    } catch (error) {
      console.error('Erreur lors du chargement des configurations:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les configurations',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // Sauvegarder une configuration
  const handleSaveConfig = async () => {
    if (!editingConfig) return;
    
    try {
      await agentsService.updateAgentConfig(editingConfig.id, editingConfig);
      toast({
        title: 'Succès',
        description: 'Configuration sauvegardée',
        variant: 'default'
      });
      setShowEditModal(false);
      setEditingConfig(null);
      setHasChanges(false);
      loadConfigs();
      onUpdate();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de sauvegarder la configuration',
        variant: 'destructive'
      });
    }
  };

  // Redémarrer un agent
  const handleRestartAgent = async (agentId: string) => {
    try {
      await agentsService.controlAgent(agentId, 'restart');
      toast({
        title: 'Succès',
        description: 'Agent redémarré',
        variant: 'default'
      });
      onUpdate();
    } catch (error) {
      console.error('Erreur lors du redémarrage:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de redémarrer l\'agent',
        variant: 'destructive'
      });
    }
  };

  // Ouvrir l'éditeur de configuration
  const openConfigEditor = (agentId: string) => {
    const config = configs.find(c => c.id === agentId);
    if (config) {
      setEditingConfig({ ...config });
      setShowEditModal(true);
      setHasChanges(false);
    }
  };

  // Mettre à jour la configuration en cours d'édition
  const updateEditingConfig = (updates: Partial<AgentConfig>) => {
    if (editingConfig) {
      setEditingConfig({ ...editingConfig, ...updates });
      setHasChanges(true);
    }
  };

  // Obtenir la couleur du statut
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'success';
      case 'offline': return 'secondary';
      case 'error': return 'destructive';
      case 'maintenance': return 'warning';
      default: return 'secondary';
    }
  };

  // Obtenir l'icône de l'agent
  const getAgentIcon = (name: string) => {
    const lowerName = name.toLowerCase();
    if (lowerName.includes('performance')) return '⚡';
    if (lowerName.includes('security')) return '🔒';
    if (lowerName.includes('qa')) return '✅';
    if (lowerName.includes('devops')) return '🔧';
    if (lowerName.includes('ui') || lowerName.includes('ux')) return '🎨';
    if (lowerName.includes('frontend')) return '🌐';
    return '🤖';
  };

  const selectedAgentData = agents.find(a => a.id === selectedAgent);
  const selectedConfig = configs.find(c => c.id === selectedAgent);

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Configuration des Agents</h2>
          <p className="text-gray-600">Paramétrage et gestion des agents du système</p>
        </div>
        
        <div className="flex space-x-3">
          <Button onClick={loadConfigs} variant="outline" leftIcon="🔄">
            Actualiser
          </Button>
        </div>
      </div>

      {/* Vue d'ensemble */}
      <Grid cols={4} gap="md">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-green-600">
              {configs.filter(c => c.enabled).length}
            </div>
            <p className="text-sm text-gray-500">Agents activés</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-blue-600">
              {configs.filter(c => c.autoRestart).length}
            </div>
            <p className="text-sm text-gray-500">Auto-restart activé</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-purple-600">
              {configs.filter(c => c.scaling.enabled).length}
            </div>
            <p className="text-sm text-gray-500">Scaling activé</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold text-orange-600">
              {configs.reduce((sum, c) => sum + c.maxJobs, 0)}
            </div>
            <p className="text-sm text-gray-500">Jobs max total</p>
          </CardContent>
        </Card>
      </Grid>

      {/* Liste des agents et configuration */}
      <Grid cols={selectedAgent ? 2 : 1} gap="lg">
        <Card>
          <CardHeader>
            <CardTitle>Agents ({agents.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {agents.map((agent) => {
                const config = configs.find(c => c.id === agent.id);
                
                return (
                  <motion.div
                    key={agent.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedAgent === agent.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedAgent(agent.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getAgentIcon(agent.name)}</span>
                        <div>
                          <h3 className="font-medium">{agent.name}</h3>
                          <p className="text-sm text-gray-500">{agent.version}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge variant={getStatusColor(agent.status)}>
                          {agent.status}
                        </Badge>
                        {config && (
                          <Badge variant={config.enabled ? 'success' : 'secondary'}>
                            {config.enabled ? 'Activé' : 'Désactivé'}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="text-gray-500">Jobs:</span>
                        <span className="ml-1">{agent.activeJobs}/{config?.maxJobs || 'N/A'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">CPU:</span>
                        <span className="ml-1">{agent.cpuUsage}%</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Mémoire:</span>
                        <span className="ml-1">{agent.memoryUsage}%</span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-3">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          openConfigEditor(agent.id);
                        }}
                        leftIcon="⚙️"
                      >
                        Configurer
                      </Button>
                      
                      {agent.status === 'online' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRestartAgent(agent.id);
                          }}
                          leftIcon="🔄"
                        >
                          Redémarrer
                        </Button>
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Détails de l'agent sélectionné */}
        {selectedAgent && selectedAgentData && selectedConfig && (
          <Card>
            <CardHeader>
              <CardTitle>Configuration de {selectedAgentData.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Paramètres généraux</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Statut:</span>
                      <Badge variant={getStatusColor(selectedAgentData.status)} className="ml-2">
                        {selectedAgentData.status}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-gray-500">Activé:</span>
                      <Badge variant={selectedConfig.enabled ? 'success' : 'secondary'} className="ml-2">
                        {selectedConfig.enabled ? 'Oui' : 'Non'}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-gray-500">Auto-restart:</span>
                      <span className="ml-2">{selectedConfig.autoRestart ? 'Oui' : 'Non'}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Jobs max:</span>
                      <span className="ml-2">{selectedConfig.maxJobs}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Timeout:</span>
                      <span className="ml-2">{selectedConfig.timeout}s</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Log level:</span>
                      <span className="ml-2">{selectedConfig.logLevel}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium mb-2">Ressources</h3>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">CPU:</span>
                      <span className="ml-2">{selectedConfig.resources.cpu} cores</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Mémoire:</span>
                      <span className="ml-2">{selectedConfig.resources.memory} MB</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Disque:</span>
                      <span className="ml-2">{selectedConfig.resources.disk} GB</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium mb-2">Health Check</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Activé:</span>
                      <span className="ml-2">{selectedConfig.healthCheck.enabled ? 'Oui' : 'Non'}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Intervalle:</span>
                      <span className="ml-2">{selectedConfig.healthCheck.interval}s</span>
                    </div>
                  </div>
                </div>
                
                {selectedConfig.scaling.enabled && (
                  <div>
                    <h3 className="font-medium mb-2">Scaling</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Min instances:</span>
                        <span className="ml-2">{selectedConfig.scaling.minInstances}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Max instances:</span>
                        <span className="ml-2">{selectedConfig.scaling.maxInstances}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Target CPU:</span>
                        <span className="ml-2">{selectedConfig.scaling.targetCpu}%</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Target Memory:</span>
                        <span className="ml-2">{selectedConfig.scaling.targetMemory}%</span>
                      </div>
                    </div>
                  </div>
                )}
                
                <div>
                  <h3 className="font-medium mb-2">Dépendances</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedConfig.dependencies.map((dep, index) => (
                      <Badge key={index} variant="outline">
                        {dep}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </Grid>

      {/* Modal d'édition de configuration */}
      <Modal open={showEditModal} onOpenChange={setShowEditModal}>
        <ModalContent className="max-w-4xl">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">
              Configuration de {editingConfig?.name}
            </h2>
            
            {editingConfig && (
              <div className="space-y-6">
                {/* Paramètres généraux */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Paramètres généraux</h3>
                  <Grid cols={2} gap="md">
                    <div>
                      <label className="block text-sm font-medium mb-1">Nom</label>
                      <Input
                        value={editingConfig.name}
                        onChange={(e) => updateEditingConfig({ name: e.target.value })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Description</label>
                      <Input
                        value={editingConfig.description}
                        onChange={(e) => updateEditingConfig({ description: e.target.value })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Jobs maximum</label>
                      <Input
                        type="number"
                        value={editingConfig.maxJobs}
                        onChange={(e) => updateEditingConfig({ maxJobs: Number(e.target.value) })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Timeout (secondes)</label>
                      <Input
                        type="number"
                        value={editingConfig.timeout}
                        onChange={(e) => updateEditingConfig({ timeout: Number(e.target.value) })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Tentatives de retry</label>
                      <Input
                        type="number"
                        value={editingConfig.retryAttempts}
                        onChange={(e) => updateEditingConfig({ retryAttempts: Number(e.target.value) })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Niveau de log</label>
                      <Select
                        value={editingConfig.logLevel}
                        onValueChange={(value) => updateEditingConfig({ logLevel: value as any })}
                      >
                        <option value="debug">Debug</option>
                        <option value="info">Info</option>
                        <option value="warn">Warning</option>
                        <option value="error">Error</option>
                      </Select>
                    </div>
                  </Grid>
                  
                  <div className="flex items-center space-x-6 mt-4">
                    <label className="flex items-center space-x-2">
                      <Switch
                        checked={editingConfig.enabled}
                        onCheckedChange={(checked) => updateEditingConfig({ enabled: checked })}
                      />
                      <span className="text-sm">Agent activé</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <Switch
                        checked={editingConfig.autoRestart}
                        onCheckedChange={(checked) => updateEditingConfig({ autoRestart: checked })}
                      />
                      <span className="text-sm">Redémarrage automatique</span>
                    </label>
                  </div>
                </div>
                
                {/* Ressources */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Ressources</h3>
                  <Grid cols={3} gap="md">
                    <div>
                      <label className="block text-sm font-medium mb-1">CPU (cores)</label>
                      <Input
                        type="number"
                        step="0.1"
                        value={editingConfig.resources.cpu}
                        onChange={(e) => updateEditingConfig({
                          resources: { ...editingConfig.resources, cpu: Number(e.target.value) }
                        })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Mémoire (MB)</label>
                      <Input
                        type="number"
                        value={editingConfig.resources.memory}
                        onChange={(e) => updateEditingConfig({
                          resources: { ...editingConfig.resources, memory: Number(e.target.value) }
                        })}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium mb-1">Disque (GB)</label>
                      <Input
                        type="number"
                        value={editingConfig.resources.disk}
                        onChange={(e) => updateEditingConfig({
                          resources: { ...editingConfig.resources, disk: Number(e.target.value) }
                        })}
                      />
                    </div>
                  </Grid>
                </div>
                
                {/* Health Check */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Health Check</h3>
                  <div className="flex items-center space-x-2 mb-3">
                    <Switch
                      checked={editingConfig.healthCheck.enabled}
                      onCheckedChange={(checked) => updateEditingConfig({
                        healthCheck: { ...editingConfig.healthCheck, enabled: checked }
                      })}
                    />
                    <span className="text-sm">Health check activé</span>
                  </div>
                  
                  {editingConfig.healthCheck.enabled && (
                    <Grid cols={3} gap="md">
                      <div>
                        <label className="block text-sm font-medium mb-1">Intervalle (s)</label>
                        <Input
                          type="number"
                          value={editingConfig.healthCheck.interval}
                          onChange={(e) => updateEditingConfig({
                            healthCheck: { ...editingConfig.healthCheck, interval: Number(e.target.value) }
                          })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Timeout (s)</label>
                        <Input
                          type="number"
                          value={editingConfig.healthCheck.timeout}
                          onChange={(e) => updateEditingConfig({
                            healthCheck: { ...editingConfig.healthCheck, timeout: Number(e.target.value) }
                          })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Tentatives</label>
                        <Input
                          type="number"
                          value={editingConfig.healthCheck.retries}
                          onChange={(e) => updateEditingConfig({
                            healthCheck: { ...editingConfig.healthCheck, retries: Number(e.target.value) }
                          })}
                        />
                      </div>
                    </Grid>
                  )}
                </div>
                
                {/* Scaling */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Auto-scaling</h3>
                  <div className="flex items-center space-x-2 mb-3">
                    <Switch
                      checked={editingConfig.scaling.enabled}
                      onCheckedChange={(checked) => updateEditingConfig({
                        scaling: { ...editingConfig.scaling, enabled: checked }
                      })}
                    />
                    <span className="text-sm">Auto-scaling activé</span>
                  </div>
                  
                  {editingConfig.scaling.enabled && (
                    <Grid cols={2} gap="md">
                      <div>
                        <label className="block text-sm font-medium mb-1">Instances min</label>
                        <Input
                          type="number"
                          value={editingConfig.scaling.minInstances}
                          onChange={(e) => updateEditingConfig({
                            scaling: { ...editingConfig.scaling, minInstances: Number(e.target.value) }
                          })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Instances max</label>
                        <Input
                          type="number"
                          value={editingConfig.scaling.maxInstances}
                          onChange={(e) => updateEditingConfig({
                            scaling: { ...editingConfig.scaling, maxInstances: Number(e.target.value) }
                          })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">CPU cible (%)</label>
                        <Input
                          type="number"
                          value={editingConfig.scaling.targetCpu}
                          onChange={(e) => updateEditingConfig({
                            scaling: { ...editingConfig.scaling, targetCpu: Number(e.target.value) }
                          })}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">Mémoire cible (%)</label>
                        <Input
                          type="number"
                          value={editingConfig.scaling.targetMemory}
                          onChange={(e) => updateEditingConfig({
                            scaling: { ...editingConfig.scaling, targetMemory: Number(e.target.value) }
                          })}
                        />
                      </div>
                    </Grid>
                  )}
                </div>
              </div>
            )}
          </div>
          
          <ModalFooter>
            <Button variant="outline" onClick={() => setShowEditModal(false)}>
              Annuler
            </Button>
            <Button onClick={handleSaveConfig} disabled={!hasChanges}>
              Sauvegarder
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
