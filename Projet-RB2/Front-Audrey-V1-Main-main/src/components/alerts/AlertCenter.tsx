import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { alertService, Alert } from '../../services/api/alertService';

interface AlertCenterProps {
  onClose: () => void;
}

/**
 * Centre d'alertes pour afficher et gérer les alertes
 */
const AlertCenter: React.FC<AlertCenterProps> = ({ onClose }) => {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');
  const alertCenterRef = useRef<HTMLDivElement>(null);
  
  // Charger les alertes
  useEffect(() => {
    const loadAlerts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const alertsData = await alertService.getAlerts(
          filter === 'unread' ? false : undefined
        );
        
        setAlerts(alertsData);
      } catch (err: any) {
        console.error('Erreur lors du chargement des alertes:', err);
        setError(err.message || 'Erreur lors du chargement des alertes');
      } finally {
        setLoading(false);
      }
    };
    
    loadAlerts();
  }, [filter]);
  
  // Gérer les clics en dehors du centre d'alertes
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (alertCenterRef.current && !alertCenterRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);
  
  // Marquer une alerte comme lue
  const handleMarkAsRead = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    try {
      await alertService.markAsRead(id);
      
      // Mettre à jour l'état local
      setAlerts(alerts.map(alert => 
        alert.id === id ? { ...alert, read: true } : alert
      ));
    } catch (err) {
      console.error(`Erreur lors du marquage de l'alerte ${id} comme lue:`, err);
    }
  };
  
  // Marquer toutes les alertes comme lues
  const handleMarkAllAsRead = async () => {
    try {
      await alertService.markAllAsRead();
      
      // Mettre à jour l'état local
      setAlerts(alerts.map(alert => ({ ...alert, read: true })));
    } catch (err) {
      console.error('Erreur lors du marquage de toutes les alertes comme lues:', err);
    }
  };
  
  // Supprimer une alerte
  const handleDeleteAlert = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    try {
      await alertService.deleteAlert(id);
      
      // Mettre à jour l'état local
      setAlerts(alerts.filter(alert => alert.id !== id));
    } catch (err) {
      console.error(`Erreur lors de la suppression de l'alerte ${id}:`, err);
    }
  };
  
  // Formater une date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);
    
    if (diffMins < 60) {
      return `Il y a ${diffMins} minute${diffMins > 1 ? 's' : ''}`;
    } else if (diffHours < 24) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    } else if (diffDays < 7) {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    } else {
      return date.toLocaleDateString('fr-FR');
    }
  };
  
  // Obtenir la couleur de l'alerte en fonction du type
  const getAlertColor = (type: string): string => {
    switch (type) {
      case 'success':
        return 'bg-green-100 border-green-500 text-green-800';
      case 'warning':
        return 'bg-yellow-100 border-yellow-500 text-yellow-800';
      case 'error':
        return 'bg-red-100 border-red-500 text-red-800';
      default:
        return 'bg-blue-100 border-blue-500 text-blue-800';
    }
  };
  
  // Obtenir l'icône de l'alerte en fonction du type
  const getAlertIcon = (type: string): JSX.Element => {
    switch (type) {
      case 'success':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };
  
  return (
    <div 
      ref={alertCenterRef}
      className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-50"
      style={{ maxHeight: '80vh' }}
    >
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Alertes</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div className="flex mt-2">
          <button
            className={`px-3 py-1 text-xs rounded-md mr-2 ${
              filter === 'all' ? 'bg-retreat-green text-white' : 'bg-gray-100 text-gray-700'
            }`}
            onClick={() => setFilter('all')}
          >
            Toutes
          </button>
          <button
            className={`px-3 py-1 text-xs rounded-md ${
              filter === 'unread' ? 'bg-retreat-green text-white' : 'bg-gray-100 text-gray-700'
            }`}
            onClick={() => setFilter('unread')}
          >
            Non lues
          </button>
          
          <div className="flex-1"></div>
          
          <button
            className="px-3 py-1 text-xs text-gray-700 hover:text-retreat-green"
            onClick={handleMarkAllAsRead}
          >
            Tout marquer comme lu
          </button>
        </div>
      </div>
      
      <div className="overflow-y-auto" style={{ maxHeight: 'calc(80vh - 80px)' }}>
        {loading ? (
          <div className="flex justify-center items-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-retreat-green"></div>
          </div>
        ) : error ? (
          <div className="p-4 text-red-500 text-center">
            {error}
          </div>
        ) : alerts.length === 0 ? (
          <div className="p-4 text-gray-500 text-center">
            Aucune alerte
          </div>
        ) : (
          <div>
            {alerts.map((alert) => (
              <div 
                key={alert.id}
                className={`p-4 border-l-4 ${getAlertColor(alert.type)} ${!alert.read ? 'bg-opacity-50' : ''} hover:bg-gray-50 transition-colors`}
              >
                <div className="flex">
                  <div className="flex-shrink-0">
                    {getAlertIcon(alert.type)}
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">
                        {alert.title}
                      </p>
                      <div className="ml-2 flex-shrink-0 flex">
                        {!alert.read && (
                          <button
                            onClick={(e) => handleMarkAsRead(alert.id, e)}
                            className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                          >
                            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                        )}
                        <button
                          onClick={(e) => handleDeleteAlert(alert.id, e)}
                          className="ml-2 bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                        >
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <p className="mt-1 text-sm text-gray-600">
                      {alert.message}
                    </p>
                    <div className="mt-2 text-xs text-gray-500 flex justify-between items-center">
                      <span>{formatDate(alert.timestamp)}</span>
                      <span className="capitalize">{alert.source}</span>
                    </div>
                    {alert.actionRequired && alert.actionLink && (
                      <div className="mt-2">
                        <Link
                          to={alert.actionLink}
                          className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-retreat-green bg-retreat-green-50 hover:bg-retreat-green-100"
                        >
                          {alert.actionText || 'Voir les détails'}
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertCenter;
