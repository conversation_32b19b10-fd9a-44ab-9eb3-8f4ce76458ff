import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { analyticsService } from '../../services/api/analyticsService';
import { EngagementMetrics } from './EngagementMetrics';
import { AudienceMetrics } from './AudienceMetrics';
import { RevenueMetrics } from './RevenueMetrics';
import { ContentPerformance } from './ContentPerformance';
import { CustomDashboard } from './CustomDashboard';
import { ForecastingMetrics } from './ForecastingMetrics';
import { BenchmarkMetrics } from './BenchmarkMetrics';
import { ExportDataButton } from './ExportDataButton';
import { FadeIn } from '../ui/FadeIn';
import { AnimatedCounter } from '../ui/AnimatedCounter';
import { SkeletonChart, SkeletonText } from '../ui/Skeleton';

export interface AnalyticsDashboardProps {
  creatorId: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ creatorId }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'overview' | 'engagement' | 'audience' | 'revenue' | 'content' | 'custom' | 'forecasting' | 'benchmarks'>('overview');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [metrics, setMetrics] = useState<any>(null);
  const [forecast, setForecast] = useState<any>(null);
  const [benchmark, setBenchmark] = useState<any>(null);
  const [benchmarkCategory, setBenchmarkCategory] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ startDate?: string; endDate?: string }>({});
  const [contentType, setContentType] = useState<string>('');
  const [categories, setCategories] = useState<string[]>(['all']);

  useEffect(() => {
    fetchMetrics();
  }, [creatorId, dateRange, contentType]);

  useEffect(() => {
    if (activeTab === 'forecasting') {
      fetchForecasts();
    }
  }, [creatorId, activeTab]);

  useEffect(() => {
    if (activeTab === 'benchmarks') {
      fetchBenchmarks();
    }
  }, [creatorId, activeTab, benchmarkCategory]);

  const fetchMetrics = async () => {
    try {
      setIsLoading(true);

      const filters = {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        contentType: contentType || undefined,
      };

      const response = await analyticsService.getCreatorMetrics(creatorId, filters);
      setMetrics(response);
    } catch (error) {
      console.error('Error fetching creator metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchForecasts = async () => {
    try {
      setIsLoading(true);

      const response = await analyticsService.getCreatorForecasts(creatorId);
      setForecast(response);
    } catch (error) {
      console.error('Error fetching creator forecasts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBenchmarks = async () => {
    try {
      setIsLoading(true);

      // Récupérer les catégories disponibles si ce n'est pas déjà fait
      if (categories.length === 1) {
        try {
          // Cette API est fictive, mais elle pourrait être implémentée pour récupérer les catégories
          const categoriesResponse = await fetch('/api/analytics/categories');
          const categoriesData = await categoriesResponse.json();
          setCategories(['all', ...categoriesData]);
        } catch (error) {
          console.error('Error fetching categories:', error);
          // Catégories par défaut en cas d'erreur
          setCategories(['all', 'wellness', 'fitness', 'nutrition', 'mindfulness', 'yoga']);
        }
      }

      const response = await analyticsService.getCreatorBenchmarks(creatorId, benchmarkCategory);
      setBenchmark(response);
    } catch (error) {
      console.error('Error fetching creator benchmarks:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDateRangeChange = (range: { startDate?: string; endDate?: string }) => {
    setDateRange(range);
  };

  const handleContentTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setContentType(e.target.value);
  };

  const handleBenchmarkCategoryChange = (category: string) => {
    setBenchmarkCategory(category);
  };

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <SkeletonText lines={1} lineHeight="2rem" className="w-64 mb-2" />
              <SkeletonText lines={1} lineHeight="1rem" className="w-48" />
            </div>
            <div className="flex space-x-2 mt-4 md:mt-0">
              <SkeletonText lines={1} lineHeight="2rem" className="w-32" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="flex border-b">
              <SkeletonText lines={1} lineHeight="1rem" className="w-24 mx-6 my-3" />
              <SkeletonText lines={1} lineHeight="1rem" className="w-24 mx-6 my-3" />
              <SkeletonText lines={1} lineHeight="1rem" className="w-24 mx-6 my-3" />
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <SkeletonChart height="12rem" />
                <SkeletonChart height="12rem" />
                <SkeletonChart height="12rem" />
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (activeTab === 'forecasting' && !forecast) {
      return (
        <div className="text-center py-12 text-gray-500">
          {t('analytics.noData')}
        </div>
      );
    }

    if (activeTab === 'benchmarks' && !benchmark) {
      return (
        <div className="text-center py-12 text-gray-500">
          {t('analytics.noData')}
        </div>
      );
    }

    if (!metrics && !['forecasting', 'benchmarks'].includes(activeTab)) {
      return (
        <div className="text-center py-12 text-gray-500">
          {t('analytics.noData')}
        </div>
      );
    }

    switch (activeTab) {
      case 'overview':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <FadeIn className="lg:col-span-3" delay={100}>
              <h3 className="text-lg font-medium mb-4">{t('analytics.overview.title')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FadeIn className="bg-white rounded-lg shadow-md p-4" delay={200} direction="up">
                  <h4 className="text-sm font-medium text-gray-500">{t('analytics.overview.totalViews')}</h4>
                  <p className="text-2xl font-bold mt-2">
                    <AnimatedCounter value={metrics.engagement.summary.totalViews} />
                  </p>
                </FadeIn>
                <FadeIn className="bg-white rounded-lg shadow-md p-4" delay={300} direction="up">
                  <h4 className="text-sm font-medium text-gray-500">{t('analytics.overview.totalFollowers')}</h4>
                  <p className="text-2xl font-bold mt-2">
                    <AnimatedCounter value={metrics.audience.totalFollowers} />
                  </p>
                </FadeIn>
                <FadeIn className="bg-white rounded-lg shadow-md p-4" delay={400} direction="up">
                  <h4 className="text-sm font-medium text-gray-500">{t('analytics.overview.totalRevenue')}</h4>
                  <p className="text-2xl font-bold mt-2">
                    <AnimatedCounter
                      value={metrics.revenue.totalRevenue}
                      formatValue={(val) => `${val.toLocaleString()} €`}
                    />
                  </p>
                </FadeIn>
              </div>
            </FadeIn>

            <FadeIn className="bg-white rounded-lg shadow-md p-6" delay={500} direction="left">
              <h3 className="text-lg font-medium mb-4">{t('analytics.overview.engagementSummary')}</h3>
              <EngagementMetrics
                metrics={metrics.engagement}
                compact={true}
              />
            </FadeIn>

            <FadeIn className="bg-white rounded-lg shadow-md p-6" delay={600} direction="up">
              <h3 className="text-lg font-medium mb-4">{t('analytics.overview.audienceSummary')}</h3>
              <AudienceMetrics
                metrics={metrics.audience}
                compact={true}
              />
            </FadeIn>

            <FadeIn className="bg-white rounded-lg shadow-md p-6" delay={700} direction="right">
              <h3 className="text-lg font-medium mb-4">{t('analytics.overview.revenueSummary')}</h3>
              <RevenueMetrics
                metrics={metrics.revenue}
                compact={true}
              />
            </FadeIn>

            <FadeIn className="lg:col-span-3" delay={800} direction="up">
              <h3 className="text-lg font-medium mb-4">{t('analytics.overview.topContent')}</h3>
              <ContentPerformance
                content={metrics.topContent}
                compact={true}
              />
            </FadeIn>
          </div>
        );
      case 'engagement':
        return (
          <EngagementMetrics
            metrics={metrics.engagement}
            compact={false}
          />
        );
      case 'audience':
        return (
          <AudienceMetrics
            metrics={metrics.audience}
            compact={false}
          />
        );
      case 'revenue':
        return (
          <RevenueMetrics
            metrics={metrics.revenue}
            compact={false}
          />
        );
      case 'content':
        return (
          <ContentPerformance
            content={metrics.topContent}
            compact={false}
            creatorId={creatorId}
          />
        );
      case 'custom':
        return (
          <CustomDashboard
            creatorId={creatorId}
          />
        );
      case 'forecasting':
        return (
          <ForecastingMetrics
            forecast={forecast}
          />
        );
      case 'benchmarks':
        return (
          <BenchmarkMetrics
            benchmark={benchmark}
            categories={categories}
            onCategoryChange={handleBenchmarkCategoryChange}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-3xl font-bold">{t('analytics.dashboard.title')}</h1>

        <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4 mt-4 md:mt-0">
          <div>
            <select
              value={contentType}
              onChange={handleContentTypeChange}
              className="border rounded-md px-3 py-2 text-sm"
            >
              <option value="">{t('analytics.filters.allContentTypes')}</option>
              <option value="RETREAT">{t('analytics.filters.retreat')}</option>
              <option value="POST">{t('analytics.filters.post')}</option>
              <option value="VIDEO">{t('analytics.filters.video')}</option>
              <option value="ARTICLE">{t('analytics.filters.article')}</option>
            </select>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => handleDateRangeChange({ startDate: undefined, endDate: undefined })}
              className={`px-3 py-2 text-sm rounded-md ${
                !dateRange.startDate && !dateRange.endDate
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('analytics.filters.allTime')}
            </button>

            <button
              onClick={() => {
                const date = new Date();
                date.setDate(date.getDate() - 30);
                handleDateRangeChange({
                  startDate: date.toISOString().split('T')[0],
                  endDate: new Date().toISOString().split('T')[0],
                });
              }}
              className={`px-3 py-2 text-sm rounded-md ${
                dateRange.startDate && new Date(dateRange.startDate).getDate() === new Date().getDate() - 30
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('analytics.filters.last30Days')}
            </button>

            <button
              onClick={() => {
                const date = new Date();
                date.setDate(date.getDate() - 7);
                handleDateRangeChange({
                  startDate: date.toISOString().split('T')[0],
                  endDate: new Date().toISOString().split('T')[0],
                });
              }}
              className={`px-3 py-2 text-sm rounded-md ${
                dateRange.startDate && new Date(dateRange.startDate).getDate() === new Date().getDate() - 7
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {t('analytics.filters.last7Days')}
            </button>
          </div>

          {metrics && (
            <ExportDataButton
              data={{
                overview: {
                  engagement: metrics.engagement.summary,
                  audience: {
                    totalFollowers: metrics.audience.totalFollowers,
                    newFollowers: metrics.audience.newFollowers,
                    lostFollowers: metrics.audience.lostFollowers,
                    activeFollowers: metrics.audience.activeFollowers,
                  },
                  revenue: {
                    totalRevenue: metrics.revenue.totalRevenue,
                    bySources: metrics.revenue.bySources,
                  },
                },
                period: metrics.period,
              }}
              filename={`analytics_${creatorId}_${new Date().toISOString().split('T')[0]}`}
              title="Analytics Dashboard"
            />
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="flex flex-wrap border-b">
          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'overview'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            {t('analytics.dashboard.tabs.overview')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'engagement'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('engagement')}
          >
            {t('analytics.dashboard.tabs.engagement')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'audience'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('audience')}
          >
            {t('analytics.dashboard.tabs.audience')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'revenue'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('revenue')}
          >
            {t('analytics.dashboard.tabs.revenue')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'content'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('content')}
          >
            {t('analytics.dashboard.tabs.content')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'custom'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('custom')}
          >
            {t('analytics.dashboard.tabs.custom')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'forecasting'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('forecasting')}
          >
            {t('analytics.forecasting.title')}
          </button>

          <button
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'benchmarks'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('benchmarks')}
          >
            {t('analytics.benchmarks.title')}
          </button>
        </div>

        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
