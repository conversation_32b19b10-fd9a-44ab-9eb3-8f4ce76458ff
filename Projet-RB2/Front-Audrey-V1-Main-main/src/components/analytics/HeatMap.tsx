import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';

interface HeatMapProps {
  data: {
    x: string;
    y: string;
    value: number;
  }[];
  xLabels?: string[];
  yLabels?: string[];
  colorRange?: string[];
  height?: number;
  width?: number;
  cellSize?: number;
  showValues?: boolean;
  valueFormatter?: (value: number) => string;
  title?: string;
  subtitle?: string;
}

export const HeatMap: React.FC<HeatMapProps> = ({
  data,
  xLabels,
  yLabels,
  colorRange = ['#f7fbff', '#08519c'],
  height = 400,
  width = 800,
  cellSize = 40,
  showValues = true,
  valueFormatter = (value) => value.toString(),
  title,
  subtitle,
}) => {
  const { t } = useTranslation();
  
  // Extract unique x and y values if not provided
  const uniqueX = xLabels || [...new Set(data.map(item => item.x))].sort();
  const uniqueY = yLabels || [...new Set(data.map(item => item.y))].sort();
  
  // Calculate min and max values for color scaling
  const minValue = Math.min(...data.map(item => item.value));
  const maxValue = Math.max(...data.map(item => item.value));
  
  // Calculate dimensions
  const calculatedWidth = Math.max(width, (uniqueX.length + 1) * cellSize);
  const calculatedHeight = Math.max(height, (uniqueY.length + 1) * cellSize);
  
  // Function to get color based on value
  const getColor = (value: number) => {
    if (minValue === maxValue) return colorRange[0];
    
    const normalizedValue = (value - minValue) / (maxValue - minValue);
    
    if (colorRange.length === 2) {
      // Linear interpolation between two colors
      return interpolateColor(colorRange[0], colorRange[1], normalizedValue);
    } else {
      // Use color range as a gradient
      const index = Math.min(
        Math.floor(normalizedValue * (colorRange.length - 1)),
        colorRange.length - 2
      );
      const t = (normalizedValue * (colorRange.length - 1)) - index;
      return interpolateColor(colorRange[index], colorRange[index + 1], t);
    }
  };
  
  // Function to interpolate between two colors
  const interpolateColor = (color1: string, color2: string, t: number) => {
    // Parse hex colors
    const r1 = parseInt(color1.slice(1, 3), 16);
    const g1 = parseInt(color1.slice(3, 5), 16);
    const b1 = parseInt(color1.slice(5, 7), 16);
    
    const r2 = parseInt(color2.slice(1, 3), 16);
    const g2 = parseInt(color2.slice(3, 5), 16);
    const b2 = parseInt(color2.slice(5, 7), 16);
    
    // Interpolate
    const r = Math.round(r1 + (r2 - r1) * t);
    const g = Math.round(g1 + (g2 - g1) * t);
    const b = Math.round(b1 + (b2 - b1) * t);
    
    // Convert back to hex
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };
  
  // Function to get cell value
  const getCellValue = (x: string, y: string) => {
    const cell = data.find(item => item.x === x && item.y === y);
    return cell ? cell.value : null;
  };
  
  // Function to get text color based on background color
  const getTextColor = (backgroundColor: string) => {
    // Convert hex to RGB
    const r = parseInt(backgroundColor.slice(1, 3), 16);
    const g = parseInt(backgroundColor.slice(3, 5), 16);
    const b = parseInt(backgroundColor.slice(5, 7), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return white for dark backgrounds, black for light backgrounds
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <p className="text-gray-500">{t('common.noData')}</p>
      </div>
    );
  }

  return (
    <div className="overflow-auto">
      {(title || subtitle) && (
        <div className="mb-4">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        </div>
      )}
      
      <div style={{ width: calculatedWidth, height: calculatedHeight }}>
        <svg width={calculatedWidth} height={calculatedHeight}>
          {/* X-axis labels */}
          {uniqueX.map((x, i) => (
            <text
              key={`x-${i}`}
              x={(i + 1) * cellSize + cellSize / 2}
              y={cellSize / 2}
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="12"
              fontWeight="bold"
            >
              {x}
            </text>
          ))}
          
          {/* Y-axis labels */}
          {uniqueY.map((y, i) => (
            <text
              key={`y-${i}`}
              x={cellSize / 2}
              y={(i + 1) * cellSize + cellSize / 2}
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="12"
              fontWeight="bold"
            >
              {y}
            </text>
          ))}
          
          {/* Cells */}
          {uniqueY.map((y, yIndex) => (
            uniqueX.map((x, xIndex) => {
              const value = getCellValue(x, y);
              
              if (value === null) return null;
              
              const cellColor = getColor(value);
              const textColor = getTextColor(cellColor);
              
              return (
                <g key={`cell-${xIndex}-${yIndex}`}>
                  <rect
                    x={(xIndex + 1) * cellSize}
                    y={(yIndex + 1) * cellSize}
                    width={cellSize}
                    height={cellSize}
                    fill={cellColor}
                    stroke="#ffffff"
                    strokeWidth="1"
                  />
                  {showValues && (
                    <text
                      x={(xIndex + 1) * cellSize + cellSize / 2}
                      y={(yIndex + 1) * cellSize + cellSize / 2}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fontSize="10"
                      fill={textColor}
                    >
                      {valueFormatter(value)}
                    </text>
                  )}
                </g>
              );
            })
          ))}
        </svg>
      </div>
      
      {/* Legend */}
      <div className="mt-4 flex items-center justify-center">
        <div className="flex items-center">
          <div className="mr-2 text-xs">{valueFormatter(minValue)}</div>
          <div
            className="w-40 h-4"
            style={{
              background: `linear-gradient(to right, ${colorRange.join(', ')})`,
            }}
          />
          <div className="ml-2 text-xs">{valueFormatter(maxValue)}</div>
        </div>
      </div>
    </div>
  );
};

export default HeatMap;
