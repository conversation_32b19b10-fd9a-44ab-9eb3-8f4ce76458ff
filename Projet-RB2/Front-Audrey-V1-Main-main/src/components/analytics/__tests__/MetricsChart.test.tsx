import React from 'react';
import { render } from '@testing-library/react';
import { Metrics<PERSON>hart } from '../MetricsChart';

// Mock de recharts
jest.mock('recharts', () => {
  const OriginalModule = jest.requireActual('recharts');
  
  return {
    ...OriginalModule,
    ResponsiveContainer: ({ children, width, height }) => (
      <div data-testid="responsive-container" style={{ width, height }}>
        {children}
      </div>
    ),
    LineChart: ({ children, data, margin }) => (
      <div data-testid="line-chart" data-data={JSON.stringify(data)} data-margin={JSON.stringify(margin)}>
        {children}
      </div>
    ),
    BarChart: ({ children, data, margin }) => (
      <div data-testid="bar-chart" data-data={JSON.stringify(data)} data-margin={JSON.stringify(margin)}>
        {children}
      </div>
    ),
    PieChart: ({ children }) => (
      <div data-testid="pie-chart">
        {children}
      </div>
    ),
    Line: ({ dataKey, stroke, type, activeDot }) => (
      <div data-testid="line" data-key={dataKey} data-stroke={stroke} data-type={type}>
        Line Component
      </div>
    ),
    Bar: ({ dataKey, fill, stackId }) => (
      <div data-testid="bar" data-key={dataKey} data-fill={fill} data-stack-id={stackId}>
        Bar Component
      </div>
    ),
    Pie: ({ data, cx, cy, labelLine, outerRadius, fill, dataKey, label }) => (
      <div data-testid="pie" data-data={JSON.stringify(data)} data-key={dataKey}>
        Pie Component
      </div>
    ),
    Cell: ({ key, fill }) => (
      <div data-testid="cell" data-key={key} data-fill={fill}>
        Cell Component
      </div>
    ),
    CartesianGrid: ({ strokeDasharray }) => (
      <div data-testid="cartesian-grid" data-stroke-dasharray={strokeDasharray}>
        CartesianGrid Component
      </div>
    ),
    XAxis: ({ dataKey }) => (
      <div data-testid="x-axis" data-key={dataKey}>
        XAxis Component
      </div>
    ),
    YAxis: () => (
      <div data-testid="y-axis">
        YAxis Component
      </div>
    ),
    Tooltip: () => (
      <div data-testid="tooltip">
        Tooltip Component
      </div>
    ),
    Legend: () => (
      <div data-testid="legend">
        Legend Component
      </div>
    ),
  };
});

describe('MetricsChart', () => {
  const mockData = [
    { date: '2023-01-01', views: 100, likes: 50, comments: 10 },
    { date: '2023-01-02', views: 150, likes: 75, comments: 15 },
    { date: '2023-01-03', views: 200, likes: 100, comments: 20 },
  ];
  
  it('renders a line chart by default', () => {
    const { getByTestId, getAllByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views', 'likes']}
        xAxisDataKey="date"
        height={300}
      />
    );
    
    // Vérifier que le conteneur responsive est rendu
    expect(getByTestId('responsive-container')).toBeInTheDocument();
    
    // Vérifier que le graphique linéaire est rendu
    expect(getByTestId('line-chart')).toBeInTheDocument();
    
    // Vérifier que les lignes sont rendues pour chaque clé de données
    const lines = getAllByTestId('line');
    expect(lines).toHaveLength(2);
    expect(lines[0]).toHaveAttribute('data-key', 'views');
    expect(lines[1]).toHaveAttribute('data-key', 'likes');
    
    // Vérifier que les composants d'axes sont rendus
    expect(getByTestId('x-axis')).toHaveAttribute('data-key', 'date');
    expect(getByTestId('y-axis')).toBeInTheDocument();
    
    // Vérifier que les composants de grille, info-bulle et légende sont rendus
    expect(getByTestId('cartesian-grid')).toBeInTheDocument();
    expect(getByTestId('tooltip')).toBeInTheDocument();
    expect(getByTestId('legend')).toBeInTheDocument();
  });
  
  it('renders a bar chart when type is bar', () => {
    const { getByTestId, getAllByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views', 'likes']}
        xAxisDataKey="date"
        height={300}
        type="bar"
      />
    );
    
    // Vérifier que le graphique à barres est rendu
    expect(getByTestId('bar-chart')).toBeInTheDocument();
    
    // Vérifier que les barres sont rendues pour chaque clé de données
    const bars = getAllByTestId('bar');
    expect(bars).toHaveLength(2);
    expect(bars[0]).toHaveAttribute('data-key', 'views');
    expect(bars[1]).toHaveAttribute('data-key', 'likes');
  });
  
  it('renders a stacked bar chart when type is bar and stacked is true', () => {
    const { getByTestId, getAllByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views', 'likes']}
        xAxisDataKey="date"
        height={300}
        type="bar"
        stacked={true}
      />
    );
    
    // Vérifier que le graphique à barres est rendu
    expect(getByTestId('bar-chart')).toBeInTheDocument();
    
    // Vérifier que les barres sont rendues pour chaque clé de données avec le même stackId
    const bars = getAllByTestId('bar');
    expect(bars).toHaveLength(2);
    expect(bars[0]).toHaveAttribute('data-stack-id', 'stack');
    expect(bars[1]).toHaveAttribute('data-stack-id', 'stack');
  });
  
  it('renders a pie chart when type is pie', () => {
    const { getByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views', 'likes']}
        xAxisDataKey="date"
        height={300}
        type="pie"
      />
    );
    
    // Vérifier que le graphique circulaire est rendu
    expect(getByTestId('pie-chart')).toBeInTheDocument();
    
    // Vérifier que le composant Pie est rendu
    expect(getByTestId('pie')).toBeInTheDocument();
  });
  
  it('formats date strings in the data', () => {
    const { getByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views']}
        xAxisDataKey="date"
        height={300}
      />
    );
    
    // Vérifier que les dates sont formatées dans les données
    const lineChart = getByTestId('line-chart');
    const chartData = JSON.parse(lineChart.getAttribute('data-data') || '[]');
    
    // Vérifier que les dates sont formatées
    expect(chartData[0].date).not.toBe('2023-01-01');
    expect(typeof chartData[0].date).toBe('string');
  });
  
  it('uses custom colors when provided', () => {
    const customColors = ['#FF0000', '#00FF00'];
    
    const { getAllByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views', 'likes']}
        xAxisDataKey="date"
        height={300}
        colors={customColors}
      />
    );
    
    // Vérifier que les couleurs personnalisées sont utilisées
    const lines = getAllByTestId('line');
    expect(lines[0]).toHaveAttribute('data-stroke', customColors[0]);
    expect(lines[1]).toHaveAttribute('data-stroke', customColors[1]);
  });
  
  it('uses custom height when provided', () => {
    const customHeight = 500;
    
    const { getByTestId } = render(
      <MetricsChart
        data={mockData}
        dataKeys={['views']}
        xAxisDataKey="date"
        height={customHeight}
      />
    );
    
    // Vérifier que la hauteur personnalisée est utilisée
    const container = getByTestId('responsive-container');
    expect(container.style.height).toBe(`${customHeight}px`);
  });
});
