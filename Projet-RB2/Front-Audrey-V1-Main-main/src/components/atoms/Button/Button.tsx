import React from 'react';
import Icon from '../Icon/Icon';
import type { ComponentProps, ElementType } from 'react';

type ButtonProps<T extends ElementType = 'button'> = {
  as?: T;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ComponentType<ComponentProps<'svg'>>;
  iconPosition?: 'left' | 'right';
  isLoading?: boolean;
  fullWidth?: boolean;
} & Omit<ComponentProps<T>, 'as'>;

const Button = <T extends ElementType = 'button'>({
  children,
  variant = 'primary',
  size = 'md',
  icon: IconComponent,
  iconPosition = 'left',
  isLoading = false,
  fullWidth = false,
  className = '',
  disabled,
  as,
  ...props
}: ButtonProps<T>) => {
  const baseStyles =
    'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variants = {
    primary: 'bg-retreat-green text-white hover:bg-retreat-green-dark focus:ring-retreat-green',
    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500',
    outline:
      'border-2 border-retreat-green text-retreat-green hover:bg-retreat-green hover:text-white focus:ring-retreat-green',
    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500',
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const widthClass = fullWidth ? 'w-full' : '';
  const disabledClass = disabled || isLoading ? 'opacity-50 cursor-not-allowed' : '';

  const Component = as || 'button';

  return (
    <Component
      className={`${baseStyles} ${variants[variant]} ${sizes[size]} ${widthClass} ${disabledClass} ${className}`}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <Icon
          icon={
            IconComponent ||
            (() => (
              <svg className='animate-spin h-5 w-5' viewBox='0 0 24 24'>
                <circle
                  className='opacity-25'
                  cx='12'
                  cy='12'
                  r='10'
                  stroke='currentColor'
                  strokeWidth='4'
                  fill='none'
                />
                <path
                  className='opacity-75'
                  fill='currentColor'
                  d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                />
              </svg>
            ))
          }
          size='sm'
          className='mr-2'
        />
      ) : (
        <>
          {IconComponent && iconPosition === 'left' && (
            <Icon icon={IconComponent} size='sm' className='mr-2' />
          )}
          {children}
          {IconComponent && iconPosition === 'right' && (
            <Icon icon={IconComponent} size='sm' className='ml-2' />
          )}
        </>
      )}
    </Component>
  );
};

export default Button;
