import React, { useState } from 'react';
import { HeartIcon } from '@heroicons/react/24/solid';

interface CardProps {
  header?: React.ReactNode;
  images?: {
    src: string;
    alt: string;
  }[];
  title: string;
  subtitle?: string;
  description?: string;
  features?: string[];
  children?: React.ReactNode;
  className?: string;
  onFavoriteClick?: (isFavorite: boolean) => void;
}

const Card: React.FC<CardProps> = ({
  header,
  images,
  title,
  subtitle,
  description,
  features,
  children,
  className = '',
  onFavoriteClick,
}) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsFavorite(!isFavorite);
    onFavoriteClick?.(!isFavorite);
  };

  const handleImageChange = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <div
      className={`
        h-full flex flex-col
        bg-white rounded-lg overflow-hidden
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-1px_rgba(0,0,0,0.06)]
        hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)]
        border border-transparent hover:border-retreat-green/20
        transform hover:-translate-y-1
        transition-all duration-300 ease-in-out
        ${className}
      `}
    >
      {header && <div className='card-header'>{header}</div>}

      {images && images.length > 0 && (
        <div className='relative'>
          <div className='relative h-72'>
            <img
              src={images[currentImageIndex].src}
              alt={images[currentImageIndex].alt}
              className='w-full h-full object-cover'
            />
          </div>

          {/* Navigation dots */}
          <div className='absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2'>
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => handleImageChange(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 ${currentImageIndex === index ? 'bg-white' : 'bg-white/50 hover:bg-white/70'}`}
                aria-label={`Image ${index + 1}`}
              />
            ))}
          </div>

          {/* Favorite button */}
          <button
            onClick={handleFavoriteClick}
            className='absolute top-4 right-4 p-2 rounded-full bg-white/80 hover:bg-white transition-colors'
            aria-label={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}
          >
            <HeartIcon className={`w-6 h-6 ${isFavorite ? 'text-red-500' : 'text-gray-400'}`} />
          </button>
        </div>
      )}

      <div className='p-6 flex flex-col flex-1'>
        <div className='flex-1'>
          <h3 className='text-2xl font-semibold mb-2'>{title}</h3>
          {subtitle && <p className='text-gray-600 mb-4'>{subtitle}</p>}

          <div className='space-y-3'>
            {description && <p className='text-gray-700'>{description}</p>}

            {features && features.length > 0 && (
              <ul className='space-y-2'>
                {features.map((feature, index) => (
                  <li key={index} className='flex items-center text-gray-600'>
                    <span className='w-4 h-4 mr-2 rounded-full bg-retreat-green/20 flex items-center justify-center'>
                      ✓
                    </span>
                    {feature}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>

        {children && <div className='mt-4'>{children}</div>}
      </div>
    </div>
  );
};

export default Card;
