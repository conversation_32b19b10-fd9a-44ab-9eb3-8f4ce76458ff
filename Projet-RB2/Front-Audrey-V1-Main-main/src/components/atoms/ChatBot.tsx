import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import ChatWindow from '../molecules/ChatWindow';

const ChatBot: React.FC = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <motion.button
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        onClick={() => setIsOpen(true)}
        className='fixed bottom-4 right-4 p-3 bg-retreat-green text-black rounded-full shadow-lg hover:bg-opacity-90 transition-colors duration-300 z-50'
        aria-label="Chat avec l'assistant"
      >
        <motion.div
          animate={{
            scale: isHovered ? 1.1 : 1,
            rotate: isHovered ? 5 : 0,
          }}
          transition={{ duration: 0.2 }}
        >
          <ChatBubbleLeftRightIcon className='h-6 w-6' />
        </motion.div>
      </motion.button>

      <ChatWindow isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
};

export default ChatBot;
