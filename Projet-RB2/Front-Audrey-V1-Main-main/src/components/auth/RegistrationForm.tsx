import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAuth } from '../../contexts/AuthContext';
import { RegistrationData } from '../../services/api/authService';
import { useNavigate } from 'react-router-dom';

const registrationSchema = z.object({
  username: z.string().min(3, { message: 'Le nom d\'utilisateur doit contenir au moins 3 caractères' }),
  email: z.string().email({ message: 'Adresse e-mail invalide' }),
  password: z.string().min(6, { message: 'Le mot de passe doit contenir au moins 6 caractères' }),
  confirmPassword: z.string(),
  firstName: z.string().min(1, { message: 'Prénom requis' }).optional(),
  lastName: z.string().min(1, { message: 'Nom de famille requis' }).optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Les mots de passe ne correspondent pas',
  path: ['confirmPassword'], // path of error
});

type RegistrationFormInputs = z.infer<typeof registrationSchema>;

export const RegistrationForm: React.FC = () => {
  const {
    register: formRegister, // alias to avoid conflict with auth.register
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<RegistrationFormInputs>({
    resolver: zodResolver(registrationSchema),
  });

  const { register: authRegister, error: authError, clearError } = useAuth();
  const navigate = useNavigate();

  const onSubmit = async (data: RegistrationFormInputs) => {
    clearError();
    try {
      // Exclude confirmPassword from the data sent to the backend
      const { confirmPassword, ...registrationPayload } = data;
      await authRegister(registrationPayload as RegistrationData);
      // Navigate to login or a "please verify your email" page
      // Depending on whether registration auto-logs in or requires verification
      navigate('/login'); 
      // Or show a success message from AuthContext if registration doesn't auto-login
    } catch (err) {
      console.error('Registration failed:', err);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className='space-y-6 w-full max-w-md p-8 bg-white shadow-xl rounded-lg'>
      <h2 className='text-3xl font-bold text-center text-gray-800'>Créer un compte</h2>
      
      {authError && (
        <div role='alert' className='p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg'>
          {authError}
        </div>
      )}

      <div>
        <label htmlFor='username' className='block text-sm font-medium text-gray-700 mb-1'>Nom d'utilisateur</label>
        <input id='username' type='text' {...formRegister('username')} className={`mt-1 block w-full px-4 py-2 border ${errors.username ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`} />
        {errors.username && <p role='alert' className='mt-2 text-sm text-red-600'>{errors.username.message}</p>}
      </div>

      <div>
        <label htmlFor='email' className='block text-sm font-medium text-gray-700 mb-1'>Adresse E-mail</label>
        <input id='email' type='email' {...formRegister('email')} className={`mt-1 block w-full px-4 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`} />
        {errors.email && <p role='alert' className='mt-2 text-sm text-red-600'>{errors.email.message}</p>}
      </div>

      <div>
        <label htmlFor='password' className='block text-sm font-medium text-gray-700 mb-1'>Mot de passe</label>
        <input id='password' type='password' {...formRegister('password')} className={`mt-1 block w-full px-4 py-2 border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`} />
        {errors.password && <p role='alert' className='mt-2 text-sm text-red-600'>{errors.password.message}</p>}
      </div>

      <div>
        <label htmlFor='confirmPassword' className='block text-sm font-medium text-gray-700 mb-1'>Confirmer le mot de passe</label>
        <input id='confirmPassword' type='password' {...formRegister('confirmPassword')} className={`mt-1 block w-full px-4 py-2 border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`} />
        {errors.confirmPassword && <p role='alert' className='mt-2 text-sm text-red-600'>{errors.confirmPassword.message}</p>}
      </div>
      
      <div>
        <label htmlFor='firstName' className='block text-sm font-medium text-gray-700 mb-1'>Prénom (Optionnel)</label>
        <input id='firstName' type='text' {...formRegister('firstName')} className={`mt-1 block w-full px-4 py-2 border ${errors.firstName ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`} />
        {errors.firstName && <p role='alert' className='mt-2 text-sm text-red-600'>{errors.firstName.message}</p>}
      </div>

      <div>
        <label htmlFor='lastName' className='block text-sm font-medium text-gray-700 mb-1'>Nom (Optionnel)</label>
        <input id='lastName' type='text' {...formRegister('lastName')} className={`mt-1 block w-full px-4 py-2 border ${errors.lastName ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`} />
        {errors.lastName && <p role='alert' className='mt-2 text-sm text-red-600'>{errors.lastName.message}</p>}
      </div>

      <div>
        <button type='submit' disabled={isSubmitting} className='w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50'>
          {isSubmitting ? 'Création du compte...' : 'S\'inscrire'}
        </button>
      </div>
    </form>
  );
}; 