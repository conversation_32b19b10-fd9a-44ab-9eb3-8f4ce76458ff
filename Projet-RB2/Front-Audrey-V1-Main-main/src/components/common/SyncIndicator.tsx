import React from 'react';
import { useSync } from '../../hooks/useSync';
import { SyncEntityType } from '../../services/syncService';

interface SyncIndicatorProps {
  showProgress?: boolean;
  showLastSync?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Composant pour afficher l'état de synchronisation et permettre de déclencher une synchronisation
 */
const SyncIndicator: React.FC<SyncIndicatorProps> = ({
  showProgress = true,
  showLastSync = true,
  className = '',
  size = 'md',
}) => {
  const { syncAll, isSyncing, progress, lastSync } = useSync();

  // Obtenir la date de dernière synchronisation globale (la plus récente)
  const getLastGlobalSync = (): Date | null => {
    const dates = Object.values(lastSync).filter((date) => date !== null) as Date[];
    if (dates.length === 0) return null;
    return new Date(Math.max(...dates.map((date) => date.getTime())));
  };

  // Formater la date de dernière synchronisation
  const formatLastSync = (date: Date | null): string => {
    if (!date) return 'Jamais';

    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // Moins d'une minute
    if (diff < 60 * 1000) {
      return "À l'instant";
    }

    // Moins d'une heure
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `Il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
    }

    // Moins d'un jour
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
    }

    // Plus d'un jour
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
  };

  // Obtenir les classes CSS en fonction de la taille
  const getSizeClasses = (): string => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'md':
        return 'text-sm';
      case 'lg':
        return 'text-base';
      default:
        return 'text-sm';
    }
  };

  // Obtenir les classes CSS pour l'icône en fonction de la taille
  const getIconSizeClasses = (): string => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'md':
        return 'h-5 w-5';
      case 'lg':
        return 'h-6 w-6';
      default:
        return 'h-5 w-5';
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      {/* Bouton de synchronisation */}
      <button
        onClick={() => syncAll(true)}
        disabled={isSyncing}
        className={`flex items-center justify-center ${getSizeClasses()} text-gray-600 hover:text-gray-900 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed`}
        title='Synchroniser les données'
      >
        <svg
          className={`${getIconSizeClasses()} ${isSyncing ? 'animate-spin' : ''}`}
          xmlns='http://www.w3.org/2000/svg'
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
          />
        </svg>
        <span className='sr-only'>Synchroniser</span>
      </button>

      {/* Barre de progression */}
      {showProgress && isSyncing && (
        <div className='ml-2 w-24 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700'>
          <div className='bg-green-600 h-2.5 rounded-full' style={{ width: `${progress}%` }}></div>
        </div>
      )}

      {/* Dernière synchronisation */}
      {showLastSync && !isSyncing && (
        <span className={`ml-2 text-gray-500 ${getSizeClasses()}`}>
          Dernière synchronisation : {formatLastSync(getLastGlobalSync())}
        </span>
      )}
    </div>
  );
};

export default SyncIndicator;
