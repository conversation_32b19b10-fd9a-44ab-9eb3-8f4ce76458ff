'use client'; // Ensure this is at the top

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { fileService, FileUploadResponse } from '../../services/api/fileService';
import { AxiosProgressEvent } from 'axios'; // Import AxiosProgressEvent
// TODO: Setup ShadCN/UI and uncomment these imports, then configure path alias in tsconfig.json
// import { Progress } from '@/components/ui/progress';
// import { Button } from '@/components/ui/button';
// import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, XCircle, UploadCloud } from 'lucide-react';

interface VideoUploadFormProps {
  onUploadSuccess: (response: FileUploadResponse) => void;
  // onUploadError: (error: Error) => void; 
}

export const VideoUploadForm: React.FC<VideoUploadFormProps> = ({ onUploadSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadResponse, setUploadResponse] = useState<FileUploadResponse | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      const videoFile = acceptedFiles[0];
      if (videoFile.type.startsWith('video/')) {
        setFile(videoFile);
        setError(null);
        setUploadResponse(null);
        setUploadProgress(0);
      } else {
        setError('Invalid file type. Please upload a video.');
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/mp4': ['.mp4'],
      'video/quicktime': ['.mov'],
      'video/webm': ['.webm'],
      'video/x-msvideo': ['.avi'],
      'video/x-matroska': ['.mkv']
      // Add other video types as needed
    }, 
    multiple: false,
  });

  const handleUploadProgress = (progressEvent: AxiosProgressEvent) => {
    if (progressEvent.total) {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      setUploadProgress(percentCompleted);
    } else {
      // Fallback if total size is not available (e.g. for chunked transfer)
      // This part might need adjustment based on how server handles progress without total
      // For now, we can set a small progress or an indeterminate state if UI supports it.
      // setUploadProgress(5); // Example: small fixed progress
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setError('Please select a video file to upload.');
      return;
    }

    setIsUploading(true);
    setError(null);
    setUploadProgress(0); 

    try {
      const response = await fileService.uploadVideoFile(file, handleUploadProgress);
      
      setUploadProgress(100); // Ensure progress is 100% on successful completion
      setUploadResponse(response);
      onUploadSuccess(response);
      setFile(null); 
    } catch (err: any) {
      setError(err.message || 'Video upload failed. Please try again.');
      setUploadProgress(0); // Reset progress on error
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="w-full max-w-lg p-6 space-y-6 bg-white dark:bg-gray-800 shadow-xl rounded-lg">
      <div
        {...getRootProps()}
        className={`p-10 border-2 border-dashed rounded-lg text-center cursor-pointer 
                    ${isDragActive ? 'border-indigo-600 bg-indigo-50 dark:bg-indigo-900/50' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'}
                    transition-colors duration-200 ease-in-out`}
      >
        <input {...getInputProps()} />
        <UploadCloud className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
        {isDragActive ? (
          <p className="text-indigo-600 dark:text-indigo-400">Relâchez pour déposer la vidéo</p>
        ) : (
          <p className="text-gray-500 dark:text-gray-400">Glissez-déposez une vidéo ici, ou cliquez pour sélectionner</p>
        )}
        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">Supporte les formats MP4, MOV, WebM, etc.</p>
      </div>

      {file && (
        <div className="mt-4 p-4 border border-gray-200 dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-900/50">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Fichier sélectionné: {file.name}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">Taille: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
          {isUploading && (
            // <Progress value={uploadProgress} className="w-full mt-2 h-2" />
            <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
              <div className="bg-indigo-600 h-2.5 rounded-full" style={{ width: `${uploadProgress}%` }}></div>
            </div>
          )}
        </div>
      )}

      {error && (
        // <Alert variant="destructive" className="mt-4">
        //   <XCircle className="h-5 w-5" />
        //   <AlertTitle>Erreur</AlertTitle>
        //   <AlertDescription>{error}</AlertDescription>
        // </Alert>
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md" role="alert">
          <strong className="font-bold">Erreur: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {uploadResponse && (
        // <Alert variant="success" className="mt-4">
        //   <CheckCircle className="h-5 w-5" />
        //   <AlertTitle>Succès</AlertTitle>
        //   <AlertDescription>
        //     Vidéo "{uploadResponse.name}" téléversée avec succès!
        //   </AlertDescription>
        // </Alert>
        <div className="mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md" role="alert">
          <strong className="font-bold">Succès: </strong>
          <span className="block sm:inline">Vidéo "{uploadResponse.name}" téléversée avec succès!</span>
        </div>
      )}

      {file && !isUploading && !uploadResponse && (
        // <Button onClick={handleUpload} className="w-full mt-6" disabled={isUploading}>
        //   {isUploading ? 'Téléversement en cours...' : 'Téléverser la vidéo'}
        // </Button>
        <button 
          onClick={handleUpload} 
          className="w-full mt-6 px-4 py-2 bg-indigo-600 text-white font-semibold rounded-md hover:bg-indigo-700 disabled:opacity-50" 
          disabled={isUploading}
        >
          {isUploading ? 'Téléversement en cours...' : 'Téléverser la vidéo'}
        </button>
      )}
    </div>
  );
}; 