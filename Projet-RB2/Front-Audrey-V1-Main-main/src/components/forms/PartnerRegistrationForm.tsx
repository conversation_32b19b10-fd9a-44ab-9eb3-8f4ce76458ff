import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Select from 'react-select';
import { partnerService } from '../../services/api/partnerService';
import { fileService } from '../../services/api/fileService';

// Types de partenaires
const PARTNER_TYPES = [
  { value: 'PREMIUM_CERTIFIED', label: 'Premium Certifié' },
  { value: 'CERTIFIED', label: 'Certifié' },
  { value: 'STANDARD', label: 'Standard' },
];

// Catégories de partenaires
const PARTNER_CATEGORIES = [
  { value: 'ORGANIZER', label: 'Organisateur' },
  { value: 'TRAVEL_AGENCY', label: 'Agence de voyage' },
  { value: 'CATERING', label: 'Restauration' },
  { value: 'GUIDE', label: 'Guide' },
  { value: 'TRANSPORT', label: 'Transport' },
  { value: 'WELLNESS', label: 'Bien-être' },
  { value: 'INSURANCE', label: 'Assurance' },
  { value: 'ACCOMMODATION', label: 'Hébergement' },
  { value: 'EQUIPMENT', label: 'Équipement' },
  { value: 'OTHER', label: 'Autre' },
];

// Spécialisations
const SPECIALIZATIONS = [
  { value: 'Yoga', label: 'Yoga' },
  { value: 'Méditation', label: 'Méditation' },
  { value: 'Nutrition', label: 'Nutrition' },
  { value: 'Fitness', label: 'Fitness' },
  { value: 'Bien-être', label: 'Bien-être' },
  { value: 'Développement personnel', label: 'Développement personnel' },
  { value: 'Pleine conscience', label: 'Pleine conscience' },
  { value: 'Art-thérapie', label: 'Art-thérapie' },
  { value: 'Randonnée', label: 'Randonnée' },
  { value: 'Cuisine saine', label: 'Cuisine saine' },
];

// Langues
const LANGUAGES = [
  { value: 'Français', label: 'Français' },
  { value: 'Anglais', label: 'Anglais' },
  { value: 'Espagnol', label: 'Espagnol' },
  { value: 'Allemand', label: 'Allemand' },
  { value: 'Italien', label: 'Italien' },
  { value: 'Portugais', label: 'Portugais' },
  { value: 'Néerlandais', label: 'Néerlandais' },
  { value: 'Russe', label: 'Russe' },
  { value: 'Chinois', label: 'Chinois' },
  { value: 'Japonais', label: 'Japonais' },
];

// Pays
const COUNTRIES = [
  { value: 'France', label: 'France' },
  { value: 'Espagne', label: 'Espagne' },
  { value: 'Italie', label: 'Italie' },
  { value: 'Portugal', label: 'Portugal' },
  { value: 'Grèce', label: 'Grèce' },
  { value: 'Suisse', label: 'Suisse' },
  { value: 'Belgique', label: 'Belgique' },
  { value: 'Allemagne', label: 'Allemagne' },
  { value: 'Royaume-Uni', label: 'Royaume-Uni' },
  { value: 'États-Unis', label: 'États-Unis' },
  { value: 'Canada', label: 'Canada' },
  { value: 'Maroc', label: 'Maroc' },
  { value: 'Thaïlande', label: 'Thaïlande' },
  { value: 'Indonésie', label: 'Indonésie' },
  { value: 'Inde', label: 'Inde' },
];

interface PartnerRegistrationFormProps {
  onSuccess?: (partnerId: string) => void;
}

interface RegistrationFormData {
  companyName: string;
  type: any;
  category: any;
  description: string;
  website?: string;
  specializations: any[];
  languages: any[];
  coverageAreas: {
    countries: any[];
    regions?: string[];
  };
  contactInfo: {
    name: string;
    email: string;
    phone: string;
  };
  documents: File[];
}

const PartnerRegistrationForm: React.FC<PartnerRegistrationFormProps> = ({ onSuccess }) => {
  const navigate = useNavigate();
  const { control, handleSubmit, formState: { errors }, reset } = useForm<RegistrationFormData>({
    defaultValues: {
      companyName: '',
      type: null,
      category: null,
      description: '',
      website: '',
      specializations: [],
      languages: [],
      coverageAreas: {
        countries: [],
        regions: [],
      },
      contactInfo: {
        name: '',
        email: '',
        phone: '',
      },
      documents: [],
    },
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setSelectedFiles(filesArray);
    }
  };

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const onSubmit = async (data: RegistrationFormData) => {
    try {
      setIsSubmitting(true);

      // Convertir les valeurs des sélecteurs en tableaux de chaînes
      const formattedData = {
        ...data,
        type: data.type.value,
        category: data.category.value,
        specializations: data.specializations.map((spec) => spec.value),
        languages: data.languages.map((lang) => lang.value),
        coverageAreas: {
          countries: data.coverageAreas.countries.map((country) => country.value),
          regions: data.coverageAreas.regions,
        },
      };

      // Enregistrer le partenaire
      const partnerResponse = await partnerService.registerPartner(formattedData);
      const partnerId = partnerResponse.id;

      // Télécharger les documents
      if (selectedFiles.length > 0) {
        const uploadPromises = selectedFiles.map(async (file) => {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('partnerId', partnerId);
          formData.append('type', 'IDENTITY'); // Type par défaut, à améliorer

          return fileService.uploadFile(formData);
        });

        await Promise.all(uploadPromises);
      }

      toast.success('Votre demande de partenariat a été soumise avec succès !');
      reset();
      setSelectedFiles([]);

      // Rediriger ou appeler le callback de succès
      if (onSuccess) {
        onSuccess(partnerId);
      } else {
        navigate(`/partner-status/${partnerId}`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'inscription du partenaire:', error);
      toast.error('Une erreur est survenue lors de l\'inscription. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-semibold mb-6">Inscription Partenaire</h2>

      <div className="mb-6">
        <div className="flex justify-between items-center">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <div
              key={index}
              className={`w-full h-2 rounded-full ${
                index + 1 <= currentStep ? 'bg-retreat-green' : 'bg-gray-200'
              }`}
            ></div>
          ))}
        </div>
        <div className="flex justify-between mt-2">
          <span className="text-sm text-gray-500">Informations de base</span>
          <span className="text-sm text-gray-500">Spécialités et couverture</span>
          <span className="text-sm text-gray-500">Documents et finalisation</span>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {currentStep === 1 && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom de l'entreprise *
              </label>
              <Controller
                name="companyName"
                control={control}
                rules={{ required: 'Ce champ est requis' }}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    className={`w-full rounded-md ${
                      errors.companyName ? 'border-red-500' : 'border-gray-300'
                    } shadow-sm focus:border-retreat-green focus:ring-retreat-green`}
                    placeholder="Nom de votre entreprise"
                  />
                )}
              />
              {errors.companyName && (
                <p className="mt-1 text-sm text-red-600">{errors.companyName.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type de partenaire *
                </label>
                <Controller
                  name="type"
                  control={control}
                  rules={{ required: 'Ce champ est requis' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={PARTNER_TYPES}
                      placeholder="Sélectionnez un type"
                      className={`basic-select ${errors.type ? 'border-red-500' : ''}`}
                      classNamePrefix="select"
                    />
                  )}
                />
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Catégorie *
                </label>
                <Controller
                  name="category"
                  control={control}
                  rules={{ required: 'Ce champ est requis' }}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={PARTNER_CATEGORIES}
                      placeholder="Sélectionnez une catégorie"
                      className={`basic-select ${errors.category ? 'border-red-500' : ''}`}
                      classNamePrefix="select"
                    />
                  )}
                />
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <Controller
                name="description"
                control={control}
                rules={{ required: 'Ce champ est requis' }}
                render={({ field }) => (
                  <textarea
                    {...field}
                    rows={4}
                    className={`w-full rounded-md ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    } shadow-sm focus:border-retreat-green focus:ring-retreat-green`}
                    placeholder="Décrivez votre entreprise et vos services"
                  />
                )}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Site web
              </label>
              <Controller
                name="website"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="url"
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    placeholder="https://www.votresite.com"
                  />
                )}
              />
            </div>
          </div>
        )}

        {currentStep === 2 && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Spécialisations *
              </label>
              <Controller
                name="specializations"
                control={control}
                rules={{ required: 'Ce champ est requis' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    isMulti
                    options={SPECIALIZATIONS}
                    placeholder="Sélectionnez vos spécialisations"
                    className={`basic-multi-select ${errors.specializations ? 'border-red-500' : ''}`}
                    classNamePrefix="select"
                  />
                )}
              />
              {errors.specializations && (
                <p className="mt-1 text-sm text-red-600">{errors.specializations.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Langues parlées *
              </label>
              <Controller
                name="languages"
                control={control}
                rules={{ required: 'Ce champ est requis' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    isMulti
                    options={LANGUAGES}
                    placeholder="Sélectionnez les langues parlées"
                    className={`basic-multi-select ${errors.languages ? 'border-red-500' : ''}`}
                    classNamePrefix="select"
                  />
                )}
              />
              {errors.languages && (
                <p className="mt-1 text-sm text-red-600">{errors.languages.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pays de couverture *
              </label>
              <Controller
                name="coverageAreas.countries"
                control={control}
                rules={{ required: 'Ce champ est requis' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    isMulti
                    options={COUNTRIES}
                    placeholder="Sélectionnez les pays où vous opérez"
                    className="basic-multi-select"
                    classNamePrefix="select"
                  />
                )}
              />
              {errors.coverageAreas?.countries && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.coverageAreas.countries.message}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Régions spécifiques (optionnel)
              </label>
              <Controller
                name="coverageAreas.regions"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    placeholder="Ex: Provence, Toscane, Andalousie (séparées par des virgules)"
                  />
                )}
              />
            </div>
          </div>
        )}

        {currentStep === 3 && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Informations de contact
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Controller
                    name="contactInfo.name"
                    control={control}
                    rules={{ required: 'Ce champ est requis' }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        className={`w-full rounded-md ${
                          errors.contactInfo?.name ? 'border-red-500' : 'border-gray-300'
                        } shadow-sm focus:border-retreat-green focus:ring-retreat-green`}
                        placeholder="Nom du contact"
                      />
                    )}
                  />
                  {errors.contactInfo?.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.contactInfo.name.message}</p>
                  )}
                </div>
                <div>
                  <Controller
                    name="contactInfo.email"
                    control={control}
                    rules={{
                      required: 'Ce champ est requis',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Adresse email invalide',
                      },
                    }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="email"
                        className={`w-full rounded-md ${
                          errors.contactInfo?.email ? 'border-red-500' : 'border-gray-300'
                        } shadow-sm focus:border-retreat-green focus:ring-retreat-green`}
                        placeholder="Email"
                      />
                    )}
                  />
                  {errors.contactInfo?.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.contactInfo.email.message}</p>
                  )}
                </div>
                <div>
                  <Controller
                    name="contactInfo.phone"
                    control={control}
                    rules={{ required: 'Ce champ est requis' }}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="tel"
                        className={`w-full rounded-md ${
                          errors.contactInfo?.phone ? 'border-red-500' : 'border-gray-300'
                        } shadow-sm focus:border-retreat-green focus:ring-retreat-green`}
                        placeholder="Téléphone"
                      />
                    )}
                  />
                  {errors.contactInfo?.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.contactInfo.phone.message}</p>
                  )}
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Documents (pièce d'identité, certificats, etc.)
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-retreat-green hover:text-retreat-green-dark focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-retreat-green"
                    >
                      <span>Télécharger des fichiers</span>
                      <input
                        id="file-upload"
                        name="file-upload"
                        type="file"
                        className="sr-only"
                        multiple
                        onChange={handleFileChange}
                      />
                    </label>
                    <p className="pl-1">ou glisser-déposer</p>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, PDF jusqu'à 10MB</p>
                </div>
              </div>
              {selectedFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-gray-600">
                    {selectedFiles.length} fichier(s) sélectionné(s)
                  </p>
                  <ul className="mt-1 text-xs text-gray-500">
                    {selectedFiles.map((file, index) => (
                      <li key={index}>{file.name}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="mt-4">
              <div className="relative flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="terms"
                    name="terms"
                    type="checkbox"
                    className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
                    required
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="terms" className="font-medium text-gray-700">
                    J'accepte les conditions générales
                  </label>
                  <p className="text-gray-500">
                    En soumettant ce formulaire, vous acceptez nos{' '}
                    <a href="/terms" className="text-retreat-green hover:underline">
                      conditions d'utilisation
                    </a>{' '}
                    et notre{' '}
                    <a href="/privacy" className="text-retreat-green hover:underline">
                      politique de confidentialité
                    </a>
                    .
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between mt-8">
          {currentStep > 1 ? (
            <button
              type="button"
              onClick={prevStep}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              Précédent
            </button>
          ) : (
            <div></div>
          )}

          {currentStep < totalSteps ? (
            <button
              type="button"
              onClick={nextStep}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            >
              Suivant
            </button>
          ) : (
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Soumission en cours...' : 'Soumettre la demande'}
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default PartnerRegistrationForm;
