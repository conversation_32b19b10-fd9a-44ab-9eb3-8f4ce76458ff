import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { socialGroupRecommendationService, GroupAggregationStrategy } from '../../services/api/socialGroupRecommendationService';
import { useAuthContext } from '../../hooks/useAuthContext';
import { toast } from 'react-toastify';
import { t } from '../../services/i18n/i18nService';
import RetreatCard from '../retreats/RetreatCard';
import LoadingSpinner from '../common/LoadingSpinner';

/**
 * Interface pour les propriétés du composant
 */
interface GroupRecommendationSectionProps {
  /** IDs des utilisateurs du groupe */
  userIds: string[];
  
  /** Nombre de recommandations à afficher */
  count?: number;
  
  /** Titre de la section */
  title?: string;
  
  /** Description de la section */
  description?: string;
  
  /** Stratégie d'agrégation */
  aggregationStrategy?: GroupAggregationStrategy;
  
  /** Poids des utilisateurs */
  userWeights?: Record<string, number>;
  
  /** Classe CSS supplémentaire */
  className?: string;
  
  /** Fonction appelée lors de la création d'un plan collaboratif */
  onCreatePlan?: (recommendations: any[]) => void;
}

/**
 * Composant pour afficher les recommandations de groupe
 */
const GroupRecommendationSection: React.FC<GroupRecommendationSectionProps> = ({
  userIds,
  count = 3,
  title = t('group.title'),
  description = t('group.description'),
  aggregationStrategy = GroupAggregationStrategy.AVERAGE,
  userWeights,
  className = '',
  onCreatePlan,
}) => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  // Charger les recommandations de groupe
  useEffect(() => {
    const loadRecommendations = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Vérifier que le groupe contient au moins 2 utilisateurs
        if (userIds.length < 2) {
          setError(t('group.notEnoughUsers'));
          setLoading(false);
          return;
        }
        
        const groupRecommendations = await socialGroupRecommendationService.getGroupRecommendations({
          userIds,
          aggregationStrategy,
          userWeights,
          maxRecommendations: count,
        });
        
        setRecommendations(groupRecommendations);
      } catch (error) {
        console.error('Erreur lors du chargement des recommandations de groupe:', error);
        setError(t('group.loadError'));
      } finally {
        setLoading(false);
      }
    };
    
    if (user && userIds.length > 0) {
      loadRecommendations();
    }
  }, [user, userIds, count, aggregationStrategy, userWeights]);
  
  // Gérer le clic sur "Voir plus"
  const handleSeeMore = () => {
    navigate('/group', { state: { userIds, aggregationStrategy, userWeights } });
  };
  
  // Gérer le clic sur "Créer un plan"
  const handleCreatePlan = () => {
    if (onCreatePlan) {
      onCreatePlan(recommendations);
    } else {
      navigate('/collaborative-plan/new', { state: { userIds, recommendations, aggregationStrategy, userWeights } });
    }
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }
  
  // Afficher un message d'erreur
  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Afficher les recommandations
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600">{description}</p>
        
        <div className="mt-4 flex items-center text-sm text-gray-500">
          <svg className="h-5 w-5 text-gray-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
          </svg>
          <span>
            {t('group.membersCount', { count: userIds.length })} • 
            {t('group.strategyLabel')} {socialGroupRecommendationService.getAggregationStrategyName(aggregationStrategy)}
          </span>
        </div>
      </div>
      
      {recommendations.length === 0 ? (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('group.noRecommendations')}</h3>
          <p className="text-gray-500 mb-6">{t('group.tryDifferentStrategy')}</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {recommendations.map((recommendation) => (
              <RetreatCard
                key={recommendation.id}
                retreat={recommendation}
                isGroup={true}
                groupScore={recommendation.groupScore}
                userScores={recommendation.userScores}
              />
            ))}
          </div>
          
          <div className="flex justify-center space-x-4">
            <button
              onClick={handleSeeMore}
              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
            >
              {t('group.seeMore')}
            </button>
            
            <button
              onClick={handleCreatePlan}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              {t('group.createPlan')}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default GroupRecommendationSection;
