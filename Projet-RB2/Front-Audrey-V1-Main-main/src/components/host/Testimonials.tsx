import React from 'react';
import { motion } from 'framer-motion';
import { StarIcon } from '@heroicons/react/24/solid';

interface Testimonial {
  name: string;
  location: string;
  rating: number;
  text: string;
  image: string;
}

const testimonials: Testimonial[] = [
  {
    name: '<PERSON>',
    location: 'Provence, France',
    rating: 5,
    text: "Devenir hôte sur Retreat & Be a transformé mon espace en une source de revenus stable. La communauté est incroyable et les retraites que j'accueille sont toujours enrichissantes.",
    image: '/images/hosts/sophie.jpg',
  },
  {
    name: '<PERSON>',
    location: 'Bretagne, France',
    rating: 5,
    text: "La plateforme m'a permis de valoriser mon espace et de rencontrer des praticiens passionnés. Le support est excellent et les outils de gestion sont très pratiques.",
    image: '/images/hosts/marc.jpg',
  },
  {
    name: '<PERSON>',
    location: 'Alpes, France',
    rating: 5,
    text: "J'ai pu développer mon activité de manière significative grâce à Retreat & Be. Les retraites que j'accueille sont de qualité et les retours des participants sont très positifs.",
    image: '/images/hosts/emma.jpg',
  },
];

const Testimonials: React.FC = () => {
  return (
    <div className='bg-white rounded-2xl shadow-lg p-8'>
      <h2 className='text-2xl font-bold text-gray-900 mb-6'>Témoignages de nos hôtes</h2>
      <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
        {testimonials.map((testimonial, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className='bg-gray-50 rounded-lg p-6'
          >
            <div className='flex items-center mb-4'>
              <img
                src={testimonial.image}
                alt={testimonial.name}
                className='w-12 h-12 rounded-full object-cover mr-4'
              />
              <div>
                <h3 className='font-semibold text-gray-900'>{testimonial.name}</h3>
                <p className='text-sm text-gray-600'>{testimonial.location}</p>
              </div>
            </div>
            <div className='flex mb-4'>
              {[...Array(testimonial.rating)].map((_, i) => (
                <StarIcon key={i} className='w-5 h-5 text-yellow-400' />
              ))}
            </div>
            <p className='text-gray-600 italic'>"{testimonial.text}"</p>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Testimonials;
