import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { matchingMessagingService } from '../../services/api/matchingMessagingService';
import { matchingAnalyticsService } from '../../services/api/matchingAnalyticsService';
import { MatchingResult } from '../../services/api/matchingService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface MatchingContactFormProps {
  matchingResult: MatchingResult;
  userRole: string;
  onSuccess?: (conversationId: string) => void;
}

const MatchingContactForm: React.FC<MatchingContactFormProps> = ({
  matchingResult,
  userRole,
  onSuccess,
}) => {
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Déterminer le type de message par défaut en fonction du rôle de l'utilisateur
  const getDefaultMessage = () => {
    if (userRole === 'PARTNER') {
      return `Bonjour,

Je suis intéressé(e) par votre retraite "${matchingResult.retreat?.title}". Notre score de compatibilité est de ${matchingResult.score}%, ce qui indique que mes services correspondent parfaitement à vos besoins.

Je serais ravi(e) d'en discuter plus en détail. Pourriez-vous me donner plus d'informations sur vos attentes spécifiques ?

Cordialement,`;
    } else {
      return `Bonjour,

Je suis l'organisateur de la retraite "${matchingResult.retreat?.title}" et je suis intéressé(e) par vos services. Notre score de compatibilité est de ${matchingResult.score}%, ce qui indique que vous correspondez parfaitement à nos besoins.

Je serais ravi(e) d'en discuter plus en détail. Pourriez-vous me donner plus d'informations sur vos services et votre disponibilité pour notre retraite qui se déroulera du ${new Date(matchingResult.retreat?.startDate || '').toLocaleDateString('fr-FR')} au ${new Date(matchingResult.retreat?.endDate || '').toLocaleDateString('fr-FR')} ?

Cordialement,`;
    }
  };

  // Initialiser le message par défaut
  React.useEffect(() => {
    setMessage(getDefaultMessage());
  }, [matchingResult, userRole]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) {
      setError('Veuillez saisir un message');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Envoyer le message
      const result = await matchingMessagingService.contactFromMatching(
        matchingResult.partnerId,
        matchingResult.retreatId,
        message,
      );
      
      // Enregistrer l'événement d'analyse
      await matchingAnalyticsService.recordMatchingContact(
        matchingResult,
        'message',
        { messageLength: message.length },
      );
      
      toast.success('Message envoyé avec succès');
      
      // Appeler le callback de succès si fourni
      if (onSuccess && result.conversationId) {
        onSuccess(result.conversationId);
      } else {
        // Rediriger vers la conversation
        navigate(`/messaging/conversation/${result.conversationId}`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      setError('Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer.');
      toast.error('Erreur lors de l\'envoi du message');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Contacter {userRole === 'PARTNER' ? 'l\'organisateur' : 'le partenaire'}
      </h3>
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            Message
          </label>
          <textarea
            id="message"
            name="message"
            rows={6}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green sm:text-sm"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            required
          />
        </div>
        
        {error && (
          <div className="mb-4 text-sm text-red-600">
            {error}
          </div>
        )}
        
        <div className="flex justify-end">
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="small" color="white" />
                <span className="ml-2">Envoi en cours...</span>
              </>
            ) : (
              'Envoyer le message'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MatchingContactForm;
