import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { MatchingCriteria } from '../../services/api/matchingService';
import { toast } from 'react-toastify';
import Select from 'react-select';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

// Types de partenaires
const PARTNER_TYPES = [
  { value: 'PREMIUM_CERTIFIED', label: 'Premium Certifié' },
  { value: 'CERTIFIED', label: 'Certifié' },
  { value: 'STANDARD', label: 'Standard' },
];

// Catégories de partenaires
const PARTNER_CATEGORIES = [
  { value: 'ORGANIZER', label: 'Organisateur' },
  { value: 'TRAVEL_AGENCY', label: 'Agence de voyage' },
  { value: 'CATERING', label: 'Restauration' },
  { value: 'GUIDE', label: 'Guide' },
  { value: 'TRANSPORT', label: 'Transport' },
  { value: 'WELLNESS', label: 'Bien-être' },
  { value: 'INSURANCE', label: 'Assurance' },
  { value: 'ACCOMMODATION', label: 'Hébergement' },
  { value: 'EQUIPMENT', label: 'Équipement' },
  { value: 'OTHER', label: 'Autre' },
];

// Spécialisations
const SPECIALIZATIONS = [
  { value: 'Yoga', label: 'Yoga' },
  { value: 'Méditation', label: 'Méditation' },
  { value: 'Nutrition', label: 'Nutrition' },
  { value: 'Fitness', label: 'Fitness' },
  { value: 'Bien-être', label: 'Bien-être' },
  { value: 'Développement personnel', label: 'Développement personnel' },
  { value: 'Pleine conscience', label: 'Pleine conscience' },
  { value: 'Art-thérapie', label: 'Art-thérapie' },
  { value: 'Randonnée', label: 'Randonnée' },
  { value: 'Cuisine saine', label: 'Cuisine saine' },
];

// Langues
const LANGUAGES = [
  { value: 'Français', label: 'Français' },
  { value: 'Anglais', label: 'Anglais' },
  { value: 'Espagnol', label: 'Espagnol' },
  { value: 'Allemand', label: 'Allemand' },
  { value: 'Italien', label: 'Italien' },
  { value: 'Portugais', label: 'Portugais' },
  { value: 'Néerlandais', label: 'Néerlandais' },
  { value: 'Russe', label: 'Russe' },
  { value: 'Chinois', label: 'Chinois' },
  { value: 'Japonais', label: 'Japonais' },
];

// Pays
const COUNTRIES = [
  { value: 'France', label: 'France' },
  { value: 'Espagne', label: 'Espagne' },
  { value: 'Italie', label: 'Italie' },
  { value: 'Portugal', label: 'Portugal' },
  { value: 'Grèce', label: 'Grèce' },
  { value: 'Suisse', label: 'Suisse' },
  { value: 'Belgique', label: 'Belgique' },
  { value: 'Allemagne', label: 'Allemagne' },
  { value: 'Royaume-Uni', label: 'Royaume-Uni' },
  { value: 'États-Unis', label: 'États-Unis' },
  { value: 'Canada', label: 'Canada' },
  { value: 'Maroc', label: 'Maroc' },
  { value: 'Thaïlande', label: 'Thaïlande' },
  { value: 'Indonésie', label: 'Indonésie' },
  { value: 'Inde', label: 'Inde' },
];

interface PartnerSearchFormProps {
  onSearch: (criteria: MatchingCriteria) => void;
  retreatId?: string;
  initialCriteria?: Partial<MatchingCriteria>;
  isLoading?: boolean;
}

const PartnerSearchForm: React.FC<PartnerSearchFormProps> = ({
  onSearch,
  retreatId,
  initialCriteria,
  isLoading = false,
}) => {
  const { control, handleSubmit, reset, setValue, watch } = useForm<MatchingCriteria>({
    defaultValues: {
      retreatId: retreatId || '',
      categories: [],
      types: [],
      specializations: [],
      languages: [],
      maxBudget: undefined,
      minExperience: undefined,
      minRating: undefined,
      certifiedOnly: false,
      limit: 10,
      dateRange: undefined,
      location: undefined,
    },
  });

  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Initialiser le formulaire avec les critères initiaux
  useEffect(() => {
    if (initialCriteria) {
      Object.entries(initialCriteria).forEach(([key, value]) => {
        setValue(key as keyof MatchingCriteria, value);
      });

      if (initialCriteria.dateRange) {
        setStartDate(new Date(initialCriteria.dateRange.start));
        setEndDate(new Date(initialCriteria.dateRange.end));
      }
    }
  }, [initialCriteria, setValue]);

  // Mettre à jour les dates dans le formulaire
  useEffect(() => {
    if (startDate && endDate) {
      setValue('dateRange', {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0],
      });
    } else {
      setValue('dateRange', undefined);
    }
  }, [startDate, endDate, setValue]);

  const onSubmit = (data: MatchingCriteria) => {
    try {
      // Ajouter l'ID de la retraite si fourni
      if (retreatId) {
        data.retreatId = retreatId;
      }

      // Convertir les valeurs des sélecteurs en tableaux de chaînes
      if (data.categories) {
        data.categories = data.categories.map((cat: any) => cat.value);
      }
      if (data.types) {
        data.types = data.types.map((type: any) => type.value);
      }
      if (data.specializations) {
        data.specializations = data.specializations.map((spec: any) => spec.value);
      }
      if (data.languages) {
        data.languages = data.languages.map((lang: any) => lang.value);
      }

      // Convertir la localisation
      if (data.location && data.location.country) {
        data.location.country = (data.location.country as any).value;
      }

      onSearch(data);
    } catch (error) {
      console.error('Erreur lors de la soumission du formulaire:', error);
      toast.error('Une erreur est survenue lors de la recherche. Veuillez réessayer.');
    }
  };

  const handleReset = () => {
    reset();
    setStartDate(null);
    setEndDate(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-4">Recherche de partenaires professionnels</h2>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Catégories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Catégories de partenaires
            </label>
            <Controller
              name="categories"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  isMulti
                  options={PARTNER_CATEGORIES}
                  placeholder="Sélectionnez des catégories"
                  className="basic-multi-select"
                  classNamePrefix="select"
                />
              )}
            />
          </div>

          {/* Types */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Types de partenaires
            </label>
            <Controller
              name="types"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  isMulti
                  options={PARTNER_TYPES}
                  placeholder="Sélectionnez des types"
                  className="basic-multi-select"
                  classNamePrefix="select"
                />
              )}
            />
          </div>

          {/* Spécialisations */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Spécialisations
            </label>
            <Controller
              name="specializations"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  isMulti
                  options={SPECIALIZATIONS}
                  placeholder="Sélectionnez des spécialisations"
                  className="basic-multi-select"
                  classNamePrefix="select"
                />
              )}
            />
          </div>

          {/* Langues */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Langues parlées
            </label>
            <Controller
              name="languages"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  isMulti
                  options={LANGUAGES}
                  placeholder="Sélectionnez des langues"
                  className="basic-multi-select"
                  classNamePrefix="select"
                />
              )}
            />
          </div>

          {/* Dates */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Période de disponibilité
            </label>
            <div className="flex space-x-2">
              <DatePicker
                selected={startDate}
                onChange={(date) => setStartDate(date)}
                selectsStart
                startDate={startDate}
                endDate={endDate}
                placeholderText="Date de début"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
              />
              <DatePicker
                selected={endDate}
                onChange={(date) => setEndDate(date)}
                selectsEnd
                startDate={startDate}
                endDate={endDate}
                minDate={startDate}
                placeholderText="Date de fin"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
              />
            </div>
          </div>

          {/* Pays */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Pays
            </label>
            <Controller
              name="location.country"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  options={COUNTRIES}
                  placeholder="Sélectionnez un pays"
                  className="basic-select"
                  classNamePrefix="select"
                  isClearable
                />
              )}
            />
          </div>
        </div>

        {/* Options avancées */}
        <div className="mt-4">
          <button
            type="button"
            className="text-retreat-green hover:text-retreat-green-dark"
            onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
          >
            {showAdvancedOptions ? 'Masquer les options avancées' : 'Afficher les options avancées'}
          </button>
        </div>

        {showAdvancedOptions && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            {/* Budget maximum */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Budget maximum (€/jour)
              </label>
              <Controller
                name="maxBudget"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    min="0"
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    placeholder="Budget maximum"
                  />
                )}
              />
            </div>

            {/* Expérience minimum */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expérience minimum (années)
              </label>
              <Controller
                name="minExperience"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    min="0"
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    placeholder="Expérience minimum"
                  />
                )}
              />
            </div>

            {/* Note minimum */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Note minimum (sur 5)
              </label>
              <Controller
                name="minRating"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    placeholder="Note minimum"
                  />
                )}
              />
            </div>

            {/* Partenaires certifiés uniquement */}
            <div className="flex items-center">
              <Controller
                name="certifiedOnly"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="checkbox"
                    className="h-4 w-4 text-retreat-green focus:ring-retreat-green border-gray-300 rounded"
                    checked={field.value}
                  />
                )}
              />
              <label className="ml-2 block text-sm text-gray-700">
                Partenaires certifiés uniquement
              </label>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-4 mt-6">
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            Réinitialiser
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            {isLoading ? 'Recherche en cours...' : 'Rechercher'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PartnerSearchForm;
