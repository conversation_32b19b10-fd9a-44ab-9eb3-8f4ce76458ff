import React, { useState } from 'react';
import { useMessagingContext } from '../../contexts/MessagingContext';
import { ParticipantLike } from '../../services/api/messagingService';

// Remove or comment out the local Participant type if ParticipantLike is sufficient
// interface Participant {
//   id: string;
//   firstName?: string;
//   lastName?: string;
//   image?: string;
// }

interface ConversationHeaderProps {
  // conversationId: string; // Removed unused prop
  onBack: () => void;
}

const ConversationHeader: React.FC<ConversationHeaderProps> = ({
  // conversationId, // Removed unused prop
  onBack,
}) => {
  const { currentConversation } = useMessagingContext();
  const [showOptions, setShowOptions] = useState(false);

  if (!currentConversation) return null;

  const getConversationTitle = () => {
    if (currentConversation.title) {
      return currentConversation.title;
    }

    // For direct conversations, use the other participant's name
    if (currentConversation.type === 'DIRECT' && currentConversation.participants.length === 2) {
      const otherParticipant = currentConversation.participants.find(
        (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
      );

      if (otherParticipant) {
        return `${otherParticipant.firstName} ${otherParticipant.lastName}`;
      }
    }

    return 'Conversation';
  };

  const getParticipantStatus = () => {
    if (currentConversation.type !== 'DIRECT' || currentConversation.participants.length !== 2) {
      return `${currentConversation.participants.length} participants`;
    }

    const otherParticipant = currentConversation.participants.find(
      (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
    );

    if (!otherParticipant) return '';

    // TODO: Implement online status
    return 'En ligne';
  };

  const getParticipantImage = () => {
    if (currentConversation.type !== 'DIRECT' || currentConversation.participants.length !== 2) {
      return undefined;
    }

    const otherParticipant = currentConversation.participants.find(
      (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
    );

    return otherParticipant?.image || undefined;
  };

  const getParticipantInitials = () => {
    if (currentConversation.type !== 'DIRECT' || currentConversation.participants.length !== 2) {
      return 'G';
    }

    const otherParticipant = currentConversation.participants.find(
      (p: ParticipantLike) => p.id !== localStorage.getItem('userId')
    );

    if (!otherParticipant) return '?';

    return `${otherParticipant.firstName?.[0] || ''}${otherParticipant.lastName?.[0] || ''}`;
  };

  return (
    <div className='flex items-center p-4 border-b border-gray-200'>
      <button onClick={onBack} className='md:hidden p-2 mr-2 text-gray-500 hover:text-gray-700'>
        <svg
          className='w-5 h-5'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          xmlns='http://www.w3.org/2000/svg'
        >
          <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
        </svg>
      </button>

      {getParticipantImage() ? (
        <img src={getParticipantImage()} alt='Avatar' className='w-10 h-10 rounded-full mr-3' />
      ) : (
        <div className='w-10 h-10 rounded-full bg-green-500 text-white flex items-center justify-center mr-3'>
          {getParticipantInitials()}
        </div>
      )}

      <div className='flex-1'>
        <h2 className='text-lg font-semibold'>{getConversationTitle()}</h2>
        <p className='text-sm text-gray-500'>{getParticipantStatus()}</p>
      </div>

      <div className='relative'>
        <button
          onClick={() => setShowOptions(!showOptions)}
          className='p-2 text-gray-500 hover:text-gray-700'
        >
          <svg
            className='w-6 h-6'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'
            />
          </svg>
        </button>

        {showOptions && (
          <div className='absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10'>
            <div className='py-1'>
              {currentConversation.type === 'GROUP' && (
                <button className='w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'>
                  Ajouter un participant
                </button>
              )}
              <button className='w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100'>
                Rechercher dans la conversation
              </button>
              <button className='w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100'>
                Quitter la conversation
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationHeader;
