import React, { useState, useEffect } from 'react';
import { useMessagingContext } from '../../contexts/MessagingContext';
import { CreateConversationDto } from '../../services/api/messagingService';

// Define AppUser interface based on mockUsers and usage
interface AppUser {
  id: string;
  firstName: string;
  lastName: string;
  image: string | null;
}

interface NewConversationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConversationCreated: (conversationId: string) => void;
}

const NewConversationModal: React.FC<NewConversationModalProps> = ({
  isOpen,
  onClose,
  onConversationCreated,
}) => {
  const [title, setTitle] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<AppUser[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<AppUser[]>([]);
  const [isGroup, setIsGroup] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { createConversation } = useMessagingContext();

  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setTitle('');
      setSelectedUsers([]);
      setSearchQuery('');
      setSearchResults([]);
      setIsGroup(false);
    }
  }, [isOpen]);

  useEffect(() => {
    // Mock search results - in a real app, this would be an API call
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);

    // Simulate API call delay
    const timeoutId = setTimeout(() => {
      // Mock users
      const mockUsers: AppUser[] = [
        { id: '1', firstName: 'Jean', lastName: 'Dupont', image: null },
        { id: '2', firstName: 'Marie', lastName: 'Martin', image: null },
        { id: '3', firstName: 'Pierre', lastName: 'Bernard', image: null },
        { id: '4', firstName: 'Sophie', lastName: 'Petit', image: null },
        { id: '5', firstName: 'Thomas', lastName: 'Robert', image: null },
      ];

      const filteredUsers = mockUsers.filter((user) =>
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase())
      );

      setSearchResults(filteredUsers);
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handleSelectUser = (user: AppUser) => {
    // Check if user is already selected
    if (selectedUsers.some((u) => u.id === user.id)) {
      return;
    }

    setSelectedUsers([...selectedUsers, user]);
    setSearchQuery('');
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(selectedUsers.filter((user) => user.id !== userId));
  };

  const handleCreateConversation = async () => {
    if (selectedUsers.length === 0) return;

    const conversationData: CreateConversationDto = {
      title: isGroup ? title : undefined,
      type: isGroup ? 'GROUP' : 'DIRECT',
      participantIds: selectedUsers.map((user) => user.id),
    };

    const conversation = await createConversation(conversationData);

    if (conversation) {
      onConversationCreated(conversation.id);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg w-full max-w-md mx-4'>
        <div className='p-4 border-b border-gray-200'>
          <div className='flex justify-between items-center'>
            <h2 className='text-lg font-semibold'>Nouvelle conversation</h2>
            <button onClick={onClose} className='text-gray-500 hover:text-gray-700'>
              <svg
                className='w-6 h-6'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M6 18L18 6M6 6l12 12'
                />
              </svg>
            </button>
          </div>
        </div>

        <div className='p-4'>
          <div className='mb-4'>
            <label htmlFor='isGroupCheckbox' className='flex items-center'>
              <input
                id='isGroupCheckbox'
                type='checkbox'
                checked={isGroup}
                onChange={() => setIsGroup(!isGroup)}
                className='mr-2'
              />
              <span>Conversation de groupe</span>
            </label>
          </div>

          {isGroup && (
            <div className='mb-4'>
              <label
                htmlFor='groupNameInput'
                className='block text-sm font-medium text-gray-700 mb-1'
              >
                Nom du groupe
              </label>
              <input
                id='groupNameInput'
                type='text'
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder='Entrez le nom du groupe'
                className='w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
              />
            </div>
          )}

          <div className='mb-4'>
            <label
              htmlFor='userSearchInput'
              className='block text-sm font-medium text-gray-700 mb-1'
            >
              {isGroup ? 'Ajouter des participants' : 'Sélectionner un contact'}
            </label>
            <input
              id='userSearchInput'
              type='text'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder='Rechercher un utilisateur...'
              className='w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
            />
          </div>

          {selectedUsers.length > 0 && (
            <div className='mb-4'>
              <div className='flex flex-wrap gap-2'>
                {selectedUsers.map((user) => (
                  <div
                    key={user.id}
                    className='flex items-center bg-gray-100 rounded-full px-3 py-1'
                  >
                    <span className='text-sm'>
                      {user.firstName} {user.lastName}
                    </span>
                    <button
                      onClick={() => handleRemoveUser(user.id)}
                      className='ml-2 text-gray-500 hover:text-gray-700'
                    >
                      <svg
                        className='w-4 h-4'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                        xmlns='http://www.w3.org/2000/svg'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M6 18L18 6M6 6l12 12'
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {searchQuery.trim() !== '' && (
            <div className='mb-4 max-h-60 overflow-y-auto'>
              {isLoading ? (
                <div className='flex justify-center py-4'>
                  <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-green-500'></div>
                </div>
              ) : searchResults.length === 0 ? (
                <p className='text-center text-gray-500 py-4'>Aucun utilisateur trouvé</p>
              ) : (
                <div className='divide-y divide-gray-200'>
                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      role='button'
                      tabIndex={0}
                      className='py-2 cursor-pointer hover:bg-gray-50'
                      onClick={() => handleSelectUser(user)}
                      onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          handleSelectUser(user);
                        }
                      }}
                    >
                      <div className='flex items-center'>
                        {user.image ? (
                          <img
                            src={user.image}
                            alt={`${user.firstName} ${user.lastName}`}
                            className='w-10 h-10 rounded-full mr-3'
                          />
                        ) : (
                          <div className='w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-3'>
                            {user.firstName[0]}
                            {user.lastName[0]}
                          </div>
                        )}
                        <div>
                          <p className='font-medium'>
                            {user.firstName} {user.lastName}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <div className='p-4 border-t border-gray-200 flex justify-end'>
          <button
            onClick={onClose}
            className='px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md mr-2'
          >
            Annuler
          </button>
          <button
            onClick={handleCreateConversation}
            disabled={selectedUsers.length === 0 || (isGroup && title.trim() === '')}
            className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
              selectedUsers.length === 0 || (isGroup && title.trim() === '')
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-green-500 hover:bg-green-600'
            }`}
          >
            Créer
          </button>
        </div>
      </div>
    </div>
  );
};

export default NewConversationModal;
