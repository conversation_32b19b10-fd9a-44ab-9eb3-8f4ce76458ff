import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { moderationService } from '../../services/api/moderationService';
import { aiModerationService, ModerationResult } from '../../services/api/aiModerationService';
import { FadeIn } from '../ui/FadeIn';

export interface ContentModerationFormProps {
  onModerationComplete?: (result: ModerationResult) => void;
}

export const ContentModerationForm: React.FC<ContentModerationFormProps> = ({ onModerationComplete }) => {
  const { t } = useTranslation();
  const [contentType, setContentType] = useState<string>('TEXT');
  const [text, setText] = useState<string>('');
  const [imageUrl, setImageUrl] = useState<string>('');
  const [base64Image, setBase64Image] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ModerationResult | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (contentType === 'TEXT' && !text.trim()) {
      setError(t('moderation.contentForm.errors.textRequired'));
      return;
    }

    if ((contentType === 'IMAGE' || contentType === 'VIDEO') && !imageUrl && !base64Image) {
      setError(t('moderation.contentForm.errors.imageRequired'));
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      let content: any = {};

      if (contentType === 'TEXT' || contentType === 'COMMENT' || contentType === 'ARTICLE') {
        content = { text };
      } else if (contentType === 'IMAGE' || contentType === 'VIDEO') {
        content = { imageUrl, base64Image };
      }

      // Utiliser le service de modération IA pour une analyse plus avancée
      let moderationResult;

      try {
        // Essayer d'abord avec le service IA
        moderationResult = await aiModerationService.moderateContent(content, contentType);
      } catch (aiError) {
        console.warn('AI moderation failed, falling back to standard moderation:', aiError);
        // Fallback sur le service standard en cas d'échec
        moderationResult = await moderationService.moderateContent(content, contentType);
      }

      setResult(moderationResult);

      if (onModerationComplete) {
        onModerationComplete(moderationResult);
      }

    } catch (error) {
      console.error('Error moderating content:', error);
      setError(t('moderation.contentForm.errors.moderationFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError(t('moderation.contentForm.errors.fileTooLarge'));
      return;
    }

    const reader = new FileReader();

    reader.onload = (event) => {
      const base64 = event.target?.result as string;
      setBase64Image(base64);
      setImageUrl('');
    };

    reader.onerror = () => {
      setError(t('moderation.contentForm.errors.fileReadError'));
    };

    reader.readAsDataURL(file);
  };

  const resetForm = () => {
    setContentType('TEXT');
    setText('');
    setImageUrl('');
    setBase64Image('');
    setResult(null);
    setError(null);
  };

  const getSeverityClass = (severity: string | null) => {
    if (!severity) return 'bg-gray-100 text-gray-800';

    switch (severity) {
      case 'LOW':
        return 'bg-yellow-100 text-yellow-800';
      case 'MEDIUM':
        return 'bg-orange-100 text-orange-800';
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'CRITICAL':
        return 'bg-red-600 text-white';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="px-6 py-4 border-b">
        <h2 className="text-xl font-semibold">{t('moderation.contentForm.title')}</h2>
      </div>

      <div className="p-6">
        {error && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {result ? (
          <FadeIn className="mb-6">
            <h3 className="text-lg font-medium mb-4">{t('moderation.contentForm.result.title')}</h3>

            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center mb-4">
                <span className="font-medium mr-2">{t('moderation.contentForm.result.appropriate')}:</span>
                {result.isInappropriate ? (
                  <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-semibold">
                    {t('moderation.contentForm.result.inappropriate')}
                  </span>
                ) : (
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-semibold">
                    {t('moderation.contentForm.result.appropriate')}
                  </span>
                )}
              </div>

              {result.severity && (
                <div className="flex items-center mb-4">
                  <span className="font-medium mr-2">{t('moderation.contentForm.result.severity')}:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getSeverityClass(result.severity)}`}>
                    {result.severity}
                  </span>
                </div>
              )}

              <div className="mb-4">
                <span className="font-medium mr-2">{t('moderation.contentForm.result.confidence')}:</span>
                <span>{(result.confidence * 100).toFixed(2)}%</span>
              </div>

              {result.matchedRules && result.matchedRules.length > 0 && (
                <div>
                  <span className="font-medium block mb-2">{t('moderation.contentForm.result.matchedRules')}:</span>
                  <ul className="list-disc pl-5 space-y-1">
                    {result.matchedRules.map((rule, index) => (
                      <li key={index} className="text-sm">
                        {rule.name}
                        <span className={`ml-2 px-2 py-0.5 rounded-full text-xs font-semibold ${getSeverityClass(rule.severity)}`}>
                          {rule.severity}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {result.categories && (
                <div className="mt-4">
                  <span className="font-medium block mb-2">{t('moderation.contentForm.result.categories')}:</span>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(result.categories).map(([category, score]) => (
                      <div key={category} className="flex justify-between items-center">
                        <span className="text-sm">{category}:</span>
                        <span className="text-sm font-medium">{(typeof score === 'number' ? (score * 100).toFixed(2) : score)}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {result.explanation && (
                <div className="mt-4 p-3 bg-blue-50 text-blue-800 rounded-md">
                  <span className="font-medium block mb-1">Explication :</span>
                  <p className="text-sm">{result.explanation}</p>
                </div>
              )}
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={resetForm}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {t('moderation.contentForm.newCheck')}
              </button>
            </div>
          </FadeIn>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('moderation.contentForm.contentType')}
              </label>
              <select
                value={contentType}
                onChange={(e) => setContentType(e.target.value)}
                className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                disabled={isLoading}
              >
                <option value="TEXT">{t('moderation.contentForm.types.text')}</option>
                <option value="COMMENT">{t('moderation.contentForm.types.comment')}</option>
                <option value="ARTICLE">{t('moderation.contentForm.types.article')}</option>
                <option value="IMAGE">{t('moderation.contentForm.types.image')}</option>
                <option value="VIDEO">{t('moderation.contentForm.types.video')}</option>
              </select>
            </div>

            {(contentType === 'TEXT' || contentType === 'COMMENT' || contentType === 'ARTICLE') && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('moderation.contentForm.text')}
                </label>
                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  rows={5}
                  className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder={t('moderation.contentForm.textPlaceholder')}
                  disabled={isLoading}
                ></textarea>
              </div>
            )}

            {(contentType === 'IMAGE' || contentType === 'VIDEO') && (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('moderation.contentForm.imageUrl')}
                  </label>
                  <input
                    type="text"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder={t('moderation.contentForm.imageUrlPlaceholder')}
                    disabled={isLoading || !!base64Image}
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('moderation.contentForm.uploadImage')}
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    disabled={isLoading || !!imageUrl}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    {t('moderation.contentForm.maxFileSize')}
                  </p>
                </div>

                {base64Image && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('moderation.contentForm.preview')}
                    </label>
                    <div className="mt-1 flex justify-center">
                      <img
                        src={base64Image}
                        alt="Preview"
                        className="max-h-48 rounded-md"
                      />
                    </div>
                  </div>
                )}
              </>
            )}

            <div className="flex justify-end">
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('common.processing')}
                  </span>
                ) : (
                  t('moderation.contentForm.checkContent')
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ContentModerationForm;
