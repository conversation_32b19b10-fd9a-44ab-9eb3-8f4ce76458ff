import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ContentModerationForm } from '../ContentModerationForm';
import { moderationService } from '../../../services/api/moderationService';

// Mock des dépendances
jest.mock('../../../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Retourne simplement la clé pour faciliter les tests
  }),
}));

jest.mock('../../../services/api/moderationService');

describe('ContentModerationForm', () => {
  const mockOnModerationComplete = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    (moderationService.moderateContent as jest.Mock).mockResolvedValue({
      isInappropriate: false,
      severity: null,
      matchedRules: [],
      confidence: 0.1,
    });
  });

  it('renders the form correctly', () => {
    render(<ContentModerationForm />);
    
    // Vérifier que le titre est affiché
    expect(screen.getByText('moderation.contentForm.title')).toBeInTheDocument();
    
    // Vérifier que le sélecteur de type de contenu est affiché
    expect(screen.getByLabelText('moderation.contentForm.contentType')).toBeInTheDocument();
    
    // Vérifier que le champ de texte est affiché (par défaut, le type est TEXT)
    expect(screen.getByLabelText('moderation.contentForm.text')).toBeInTheDocument();
    
    // Vérifier que le bouton de soumission est affiché
    expect(screen.getByText('moderation.contentForm.checkContent')).toBeInTheDocument();
  });

  it('shows image fields when image content type is selected', () => {
    render(<ContentModerationForm />);
    
    // Sélectionner le type de contenu IMAGE
    fireEvent.change(screen.getByLabelText('moderation.contentForm.contentType'), {
      target: { value: 'IMAGE' },
    });
    
    // Vérifier que les champs d'image sont affichés
    expect(screen.getByLabelText('moderation.contentForm.imageUrl')).toBeInTheDocument();
    expect(screen.getByLabelText('moderation.contentForm.uploadImage')).toBeInTheDocument();
    
    // Vérifier que le champ de texte n'est plus affiché
    expect(screen.queryByLabelText('moderation.contentForm.text')).not.toBeInTheDocument();
  });

  it('validates form before submission', async () => {
    render(<ContentModerationForm />);
    
    // Soumettre le formulaire sans remplir le champ de texte
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier qu'un message d'erreur est affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.errors.textRequired')).toBeInTheDocument();
    });
    
    // Vérifier que le service n'a pas été appelé
    expect(moderationService.moderateContent).not.toHaveBeenCalled();
  });

  it('submits the form with text content', async () => {
    render(<ContentModerationForm onModerationComplete={mockOnModerationComplete} />);
    
    // Remplir le champ de texte
    fireEvent.change(screen.getByLabelText('moderation.contentForm.text'), {
      target: { value: 'Test content' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier que le service a été appelé avec les bons paramètres
    await waitFor(() => {
      expect(moderationService.moderateContent).toHaveBeenCalledWith(
        { text: 'Test content' },
        'TEXT'
      );
    });
    
    // Vérifier que le callback a été appelé
    expect(mockOnModerationComplete).toHaveBeenCalled();
  });

  it('submits the form with image URL', async () => {
    render(<ContentModerationForm />);
    
    // Sélectionner le type de contenu IMAGE
    fireEvent.change(screen.getByLabelText('moderation.contentForm.contentType'), {
      target: { value: 'IMAGE' },
    });
    
    // Remplir le champ d'URL d'image
    fireEvent.change(screen.getByLabelText('moderation.contentForm.imageUrl'), {
      target: { value: 'https://example.com/image.jpg' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier que le service a été appelé avec les bons paramètres
    await waitFor(() => {
      expect(moderationService.moderateContent).toHaveBeenCalledWith(
        { imageUrl: 'https://example.com/image.jpg', base64Image: '' },
        'IMAGE'
      );
    });
  });

  it('displays moderation result after submission', async () => {
    // Configurer le mock pour retourner un résultat avec un contenu inapproprié
    (moderationService.moderateContent as jest.Mock).mockResolvedValue({
      isInappropriate: true,
      severity: 'MEDIUM',
      matchedRules: [
        { id: '1', name: 'Test Rule', severity: 'MEDIUM' }
      ],
      confidence: 0.85,
    });
    
    render(<ContentModerationForm />);
    
    // Remplir le champ de texte
    fireEvent.change(screen.getByLabelText('moderation.contentForm.text'), {
      target: { value: 'Test inappropriate content' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Vérifier que le résultat est affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.result.title')).toBeInTheDocument();
      expect(screen.getByText('moderation.contentForm.result.inappropriate')).toBeInTheDocument();
      expect(screen.getByText('MEDIUM')).toBeInTheDocument();
      expect(screen.getByText('Test Rule')).toBeInTheDocument();
    });
    
    // Vérifier que le bouton pour une nouvelle vérification est affiché
    expect(screen.getByText('moderation.contentForm.newCheck')).toBeInTheDocument();
  });

  it('resets the form when clicking on new check button', async () => {
    // Configurer le mock pour retourner un résultat
    (moderationService.moderateContent as jest.Mock).mockResolvedValue({
      isInappropriate: false,
      severity: null,
      matchedRules: [],
      confidence: 0.1,
    });
    
    render(<ContentModerationForm />);
    
    // Remplir le champ de texte
    fireEvent.change(screen.getByLabelText('moderation.contentForm.text'), {
      target: { value: 'Test content' },
    });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('moderation.contentForm.checkContent'));
    
    // Attendre que le résultat soit affiché
    await waitFor(() => {
      expect(screen.getByText('moderation.contentForm.result.title')).toBeInTheDocument();
    });
    
    // Cliquer sur le bouton pour une nouvelle vérification
    fireEvent.click(screen.getByText('moderation.contentForm.newCheck'));
    
    // Vérifier que le formulaire est réinitialisé
    await waitFor(() => {
      expect(screen.getByLabelText('moderation.contentForm.text')).toBeInTheDocument();
      expect(screen.getByText('moderation.contentForm.checkContent')).toBeInTheDocument();
    });
  });
});
