import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ModerationDashboard } from '../ModerationDashboard';
import { moderationService } from '../../../services/api/moderationService';

// Mock des dépendances
jest.mock('../../../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Retourne simplement la clé pour faciliter les tests
  }),
}));

jest.mock('../../../services/api/moderationService');
jest.mock('../ReportList', () => ({
  ReportList: () => <div data-testid="report-list">Report List Component</div>,
}));
jest.mock('../ModerationRules', () => ({
  ModerationRules: () => <div data-testid="moderation-rules">Moderation Rules Component</div>,
}));
jest.mock('../ModerationStats', () => ({
  ModerationStats: () => <div data-testid="moderation-stats">Moderation Stats Component</div>,
}));

describe('ModerationDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (moderationService.getStats as jest.Mock).mockResolvedValue({
      total: 100,
      pending: 20,
      inReview: 10,
      approved: 40,
      rejected: 25,
      escalated: 5,
    });
  });

  it('renders the dashboard with reports tab active by default', async () => {
    render(<ModerationDashboard userRole="admin" />);
    
    // Vérifier que le titre est affiché
    expect(screen.getByText('moderation.dashboard.title')).toBeInTheDocument();
    
    // Vérifier que l'onglet Reports est actif par défaut
    expect(screen.getByText('moderation.dashboard.tabs.reports')).toHaveClass('text-blue-600');
    
    // Vérifier que le composant ReportList est affiché
    await waitFor(() => {
      expect(screen.getByTestId('report-list')).toBeInTheDocument();
    });
  });

  it('switches to rules tab when clicked', async () => {
    render(<ModerationDashboard userRole="admin" />);
    
    // Cliquer sur l'onglet Rules
    fireEvent.click(screen.getByText('moderation.dashboard.tabs.rules'));
    
    // Vérifier que l'onglet Rules est actif
    expect(screen.getByText('moderation.dashboard.tabs.rules')).toHaveClass('text-blue-600');
    
    // Vérifier que le composant ModerationRules est affiché
    await waitFor(() => {
      expect(screen.getByTestId('moderation-rules')).toBeInTheDocument();
    });
  });

  it('switches to stats tab when clicked', async () => {
    render(<ModerationDashboard userRole="admin" />);
    
    // Cliquer sur l'onglet Stats
    fireEvent.click(screen.getByText('moderation.dashboard.tabs.stats'));
    
    // Vérifier que l'onglet Stats est actif
    expect(screen.getByText('moderation.dashboard.tabs.stats')).toHaveClass('text-blue-600');
    
    // Vérifier que le composant ModerationStats est affiché
    await waitFor(() => {
      expect(screen.getByTestId('moderation-stats')).toBeInTheDocument();
    });
  });

  it('does not show rules tab for regular users', () => {
    render(<ModerationDashboard userRole="user" />);
    
    // Vérifier que l'onglet Rules n'est pas affiché
    expect(screen.queryByText('moderation.dashboard.tabs.rules')).not.toBeInTheDocument();
  });

  it('does not show stats tab for moderators', () => {
    render(<ModerationDashboard userRole="moderator" />);
    
    // Vérifier que l'onglet Stats n'est pas affiché
    expect(screen.queryByText('moderation.dashboard.tabs.stats')).not.toBeInTheDocument();
  });

  it('fetches stats on component mount', async () => {
    render(<ModerationDashboard userRole="admin" />);
    
    // Vérifier que la méthode getStats a été appelée
    await waitFor(() => {
      expect(moderationService.getStats).toHaveBeenCalled();
    });
  });
});
