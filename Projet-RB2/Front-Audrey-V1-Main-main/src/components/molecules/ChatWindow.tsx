import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  PaperAirplaneIcon,
  MicrophoneIcon,
  StopIcon,
  ArrowPathIcon,
  Cog6ToothIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { useChatbot, ChatMessage } from '../../hooks/useChatbot';
import MessageContent from './chatbot/MessageContent';
// Importation directe avec le chemin absolu
import ConversationList from './chatbot/ConversationList';

interface ChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ isOpen, onClose }) => {
  const [inputMessage, setInputMessage] = useState('');
  const [showConversations, setShowConversations] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const {
    messages,
    conversations,
    currentConversation,
    isLoading,
    error,
    isAvailable,
    isSpeechRecognitionAvailable,
    isListening,
    sendMessage,
    sendVoiceMessage,
    stopVoiceMessage,
    clearMessages,
    startNewConversation,
    loadConversation,
    deleteConversation,
    handleButtonClick,
    giveFeedback,
  } = useChatbot();

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Focus input when chat window opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading) return;

    const messageToSend = inputMessage;
    setInputMessage('');
    await sendMessage(messageToSend);
  };

  // Toggle voice input
  const handleVoiceInput = () => {
    if (isListening) {
      stopVoiceMessage();
    } else {
      sendVoiceMessage();
    }
  };

  // Start a new conversation
  const handleNewConversation = () => {
    startNewConversation();
    setShowConversations(false);
  };

  // Render a single message
  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';

    return (
      <div key={message.id} className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
        <div
          className={`max-w-[80%] p-3 rounded-lg ${
            isUser
              ? 'bg-retreat-green text-black rounded-tr-none'
              : 'bg-mint-50 text-black rounded-tl-none'
          }`}
        >
          <MessageContent
            message={message}
            onButtonClick={handleButtonClick}
            onFeedback={giveFeedback}
          />
        </div>
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 20, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className='fixed bottom-24 right-4 w-96 h-[500px] bg-white rounded-lg shadow-xl flex flex-col z-50'
        >
          {/* Header */}
          <div className='p-4 border-b border-mint-100 flex justify-between items-center'>
            <div className='flex items-center'>
              <h3 className='text-lg font-semibold text-black'>
                {showConversations
                  ? 'Historique des conversations'
                  : showSettings
                    ? 'Paramètres'
                    : currentConversation?.title || "Chat avec l'assistant"}
              </h3>

              {/* Back button when showing conversations or settings */}
              {(showConversations || showSettings) && (
                <button
                  onClick={() => {
                    setShowConversations(false);
                    setShowSettings(false);
                  }}
                  className='ml-2 p-1 hover:bg-mint-50 rounded-full transition-colors'
                  aria-label='Retour'
                >
                  <ArrowPathIcon className='h-4 w-4 text-green-dark' />
                </button>
              )}
            </div>

            <div className='flex items-center space-x-2'>
              {/* Conversations button */}
              <button
                onClick={() => {
                  setShowConversations(!showConversations);
                  setShowSettings(false);
                }}
                className={`p-1 rounded-full transition-colors ${
                  showConversations ? 'bg-mint-50' : 'hover:bg-mint-50'
                }`}
                aria-label='Historique des conversations'
                title='Historique des conversations'
              >
                <ChatBubbleLeftRightIcon className='h-5 w-5 text-green-dark' />
              </button>

              {/* Settings button */}
              <button
                onClick={() => {
                  setShowSettings(!showSettings);
                  setShowConversations(false);
                }}
                className={`p-1 rounded-full transition-colors ${
                  showSettings ? 'bg-mint-50' : 'hover:bg-mint-50'
                }`}
                aria-label='Paramètres'
                title='Paramètres'
              >
                <Cog6ToothIcon className='h-5 w-5 text-green-dark' />
              </button>

              {/* Close button */}
              <button
                onClick={onClose}
                className='p-1 hover:bg-mint-50 rounded-full transition-colors'
                aria-label='Fermer le chat'
                title='Fermer'
              >
                <XMarkIcon className='h-5 w-5 text-green-dark' />
              </button>
            </div>
          </div>

          {/* Conversation list */}
          {showConversations && (
            <div className='flex-1 overflow-y-auto p-4'>
              <ConversationList
                conversations={conversations}
                currentConversationId={currentConversation?.id}
                onSelectConversation={(id: string) => {
                  loadConversation(id);
                  setShowConversations(false);
                }}
                onDeleteConversation={deleteConversation}
                onNewConversation={handleNewConversation}
              />
            </div>
          )}

          {/* Settings */}
          {showSettings && (
            <div className='flex-1 overflow-y-auto p-4'>
              <div className='space-y-4'>
                <div>
                  <h4 className='font-medium text-black mb-2'>Actions</h4>
                  <div className='space-y-2'>
                    <button
                      onClick={handleNewConversation}
                      className='w-full px-4 py-2 bg-retreat-green text-black rounded-md hover:bg-opacity-90 transition-colors'
                    >
                      Nouvelle conversation
                    </button>
                    <button
                      onClick={clearMessages}
                      className='w-full px-4 py-2 bg-gray-200 text-black rounded-md hover:bg-gray-300 transition-colors'
                    >
                      Effacer cette conversation
                    </button>
                  </div>
                </div>

                <div>
                  <h4 className='font-medium text-black mb-2'>À propos</h4>
                  <p className='text-sm text-gray-600'>
                    Assistant Retreat And Be v1.0.0
                    <br />
                    Propulsé par l'IA de Retreat And Be
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Messages */}
          {!showConversations && !showSettings && (
            <div className='flex-1 overflow-y-auto p-4 space-y-2'>
              {/* Error message if service is unavailable */}
              {!isAvailable && (
                <div className='bg-red-100 text-red-800 p-3 rounded-lg mb-4'>
                  Le service de chatbot est actuellement indisponible. Veuillez réessayer plus tard.
                </div>
              )}

              {/* Error message from API */}
              {error && <div className='bg-red-100 text-red-800 p-3 rounded-lg mb-4'>{error}</div>}

              {/* Chat messages */}
              {messages.map(renderMessage)}

              {/* Loading indicator */}
              {isLoading && (
                <div className='flex justify-start mb-4'>
                  <div className='bg-mint-50 p-3 rounded-lg flex items-center space-x-2'>
                    <div
                      className='w-2 h-2 bg-green-500 rounded-full animate-bounce'
                      style={{ animationDelay: '0ms' }}
                    ></div>
                    <div
                      className='w-2 h-2 bg-green-500 rounded-full animate-bounce'
                      style={{ animationDelay: '150ms' }}
                    ></div>
                    <div
                      className='w-2 h-2 bg-green-500 rounded-full animate-bounce'
                      style={{ animationDelay: '300ms' }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Invisible element to scroll to */}
              <div ref={messagesEndRef} />
            </div>
          )}

          {/* Input */}
          {!showConversations && !showSettings && (
            <form onSubmit={handleSubmit} className='p-4 border-t border-mint-100'>
              <div className='flex gap-2 items-center'>
                {/* Voice input button */}
                {isSpeechRecognitionAvailable && (
                  <button
                    type='button'
                    onClick={handleVoiceInput}
                    disabled={!isAvailable}
                    className={`p-2 rounded-full transition-colors ${
                      isListening
                        ? 'bg-red-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                    aria-label={
                      isListening ? 'Arrêter l&apos;enregistrement' : 'Enregistrer un message vocal'
                    }
                    title={
                      isListening ? 'Arrêter l&apos;enregistrement' : 'Enregistrer un message vocal'
                    }
                  >
                    {isListening ? (
                      <StopIcon className='h-5 w-5' />
                    ) : (
                      <MicrophoneIcon className='h-5 w-5' />
                    )}
                  </button>
                )}

                {/* Text input */}
                <input
                  ref={inputRef}
                  type='text'
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder={isListening ? 'Parlez maintenant...' : 'Écrivez votre message...'}
                  disabled={isLoading || !isAvailable}
                  className='flex-1 px-4 py-2 border border-mint-200 rounded-full focus:outline-none focus:border-retreat-green transition-colors text-black disabled:bg-gray-100 disabled:text-gray-400'
                />

                {/* Send button */}
                <button
                  type='submit'
                  disabled={isLoading || !isAvailable || (!inputMessage.trim() && !isListening)}
                  className='p-2 bg-retreat-green text-black rounded-full hover:bg-opacity-90 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed'
                  aria-label='Envoyer'
                  title='Envoyer'
                >
                  <PaperAirplaneIcon className='h-5 w-5' />
                </button>
              </div>

              {/* Listening indicator */}
              {isListening && (
                <div className='mt-2 text-xs text-center text-gray-500 animate-pulse'>
                  Écoute en cours... Cliquez sur le microphone pour arrêter.
                </div>
              )}
            </form>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ChatWindow;
