import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
// import { Dialog } from '@headlessui/react';
import {
  // UserIcon,
  // CalendarIcon,
  UserGroupIcon,
  FireIcon,
  // PhotoIcon
} from '@heroicons/react/24/outline';
import { StarIcon, HeartIcon /* MapPinIcon */ } from '@heroicons/react/24/solid';
// import Map, { Marker } from 'react-map-gl';
// import 'mapbox-gl/dist/mapbox-gl.css';

// Configuration de la carte avec vérification du token
/* const MapComponent = () => {
  const [mapToken, setMapToken] = useState<string | undefined>(undefined);

  useEffect(() => {
    setMapToken(process.env.REACT_APP_MAPBOX_TOKEN);
  }, []);

  if (!mapToken) {
    return <div className="h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Chargement de la carte...</p>
    </div>
  }

   return (
    <div className="h-[300px] rounded-lg overflow-hidden">
      <Map
        mapboxAccessToken={mapToken}
        initialViewState={{
          longitude: 5.284,
          latitude: 43.853,
          zoom: 12
        }}
        style={{ width: '100%', height: '100%' }}
        mapStyle="mapbox://styles/mapbox/streets-v11"
      />
    </div>
  );
};

  interface ImageGalleryProps {
    images: { url: string; alt: string }[];
    onClose: () => void;
    isOpen: boolean;
  }

const ImageGallery: React.FC<ImageGalleryProps> = ({ images, isOpen, onClose }) => (
  <Dialog open={isOpen} onClose={onClose} className="relative z-50">
    <div className="fixed inset-0 bg-black/90" aria-hidden="true" />
    <div className="fixed inset-0 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl p-4 max-w-4xl w-full">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <img
              key={index}
              src={image.url}
              alt={image.alt}
              className="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
            />
          ))}
        </div>
        <button
          onClick={onClose}
          className="mt-4 px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300"
        >
          Fermer
        </button>
      </div>
    </div>
  </Dialog>
);

  interface DetailModalProps {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    content: React.ReactNode;
  }

  const DetailModal: React.FC<DetailModalProps> = ({ isOpen, onClose, title, content }) => (
    <AnimatePresence>
      {isOpen && (
        <Dialog open={isOpen} onClose={onClose} className="relative z-50">
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-white rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            >
              <Dialog.Title className="text-2xl font-semibold mb-4">{title}</Dialog.Title>
              {content}
              <button
                onClick={onClose}
                className="mt-4 px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300"
              >
                Fermer
              </button>
            </motion.div>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
); 
*/

const FeaturedRetreat: React.FC = () => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  // const [isGalleryOpen, setIsGalleryOpen] = useState(false); // Supprimé car ajout auto non désiré et non utilisé
  // const [activeModal, setActiveModal] = useState<string | null>(null); // Supprimé car ajout auto non désiré et non utilisé
  // const [expandedContent, setExpandedContent] = useState(false); // Non utilisé

  // Données de la retraite
  const retreatData = {
    totalSpots: 24,
    availableSpots: 8,
    popularity: {
      views: 1250,
      bookingsLastMonth: 16,
      trend: 'up',
    },
    images: [
      { url: '/images/retreat-gallery-1.jpg', alt: 'Vue de la piscine' },
      { url: '/images/retreat-gallery-2.jpg', alt: 'Salle de yoga' },
      { url: '/images/retreat-gallery-3.jpg', alt: 'Chambre double' },
      { url: '/images/retreat-gallery-4.jpg', alt: 'Restaurant' },
    ],
    location: {
      latitude: 43.853,
      longitude: 5.284,
      address: 'Route de Gordes, 84220 Goult',
    },
    availability: [
      { startDate: '2024-06-15', endDate: '2024-06-22', spots: 8 },
      { startDate: '2024-07-13', endDate: '2024-07-20', spots: 12 },
      { startDate: '2024-08-10', endDate: '2024-08-17', spots: 24 },
    ],
  };

  // La constante additionalModalContent est supprimée ci-dessous

  const modalContent = {
    retreat: {
      title: 'La Retraite',
      content: (
        <div className='space-y-4'>
          <h3 className='text-xl font-semibold'>Domaine de la Provence Zen</h3>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <h4 className='font-medium'>Type d&apos;hébergement</h4>
              <p>Bastide provençale rénovée</p>
            </div>
            <div>
              <h4 className='font-medium'>Capacité</h4>
              <p>12 chambres - 24 personnes</p>
            </div>
            <div>
              <h4 className='font-medium'>Équipements</h4>
              <ul className='list-disc list-inside'>
                <li>Piscine naturelle</li>
                <li>Salle de yoga</li>
                <li>Spa et sauna</li>
                <li>Jardin méditatif</li>
              </ul>
            </div>
            <div>
              <h4 className='font-medium'>Services inclus</h4>
              <ul className='list-disc list-inside'>
                <li>Pension complète bio</li>
                <li>Ménage quotidien</li>
                <li>Wifi gratuit</li>
                <li>Navette aéroport</li>
              </ul>
            </div>
          </div>
        </div>
      ),
    },
    professional: {
      title: 'Le professionnel',
      content: (
        <div className='space-y-4'>
          <div className='flex items-center space-x-4'>
            <img
              src='/images/avatars/optimized/claireAvatar-400.jpg'
              alt='Claire'
              className='w-24 h-24 rounded-full object-cover border-2 border-retreat-green'
            />
            <div>
              <h3 className='text-xl font-semibold'>Claire</h3>
              <p className='text-gray-600'>Professeure de yoga et méditation</p>
              <p className='text-sm'>15 ans d&apos;expérience</p>
            </div>
          </div>
          <div className='space-y-4'>
            <div>
              <h4 className='font-medium text-lg text-retreat-green'>Diplômes et Certifications</h4>
              <ul className='mt-2 space-y-2 list-disc list-inside text-gray-700'>
                <li>Diplôme de Professeur de Yoga (500h) - École Sivananda, Inde</li>
                <li>Certification Vinyasa Flow Yoga - Power Yoga Institute</li>
                <li>Certification Yin Yoga - Sarah Powers Institute</li>
                <li>Formation en Yoga Thérapie - Institut de Yogathérapie</li>
                <li>Spécialisation en Méditation Pleine Conscience (MBSR)</li>
                <li>
                  Approche pédagogique : Claire privilégie une approche holistique, bienveillante et
                  adaptée à chacun. Ses cours allient dynamisme et relaxation profonde, avec un
                  accent particulier sur l&apos;alignement et la conscience du souffle. Elle intègre
                  également des éléments de philosophie du yoga et des techniques de méditation pour
                  une expérience complète.
                </li>
              </ul>
            </div>

            <div>
              <h4 className='font-medium text-lg text-retreat-green'>Formations Spécialisées</h4>
              <ul className='mt-2 space-y-2 list-disc list-inside text-gray-700'>
                <li>Formation en Yoga Thérapeutique - Krishnamacharya Yoga Mandiram</li>
                <li>Anatomie et Ajustements - avec Leslie Kaminoff</li>
                <li>Yoga Prénatal et Postnatal - Birthlight</li>
                <li>Pranayama avancé - École de Bihar, Inde</li>
              </ul>
            </div>

            <div>
              <h4 className='font-medium text-lg text-retreat-green'>Approche</h4>
              <p className='mt-2 text-gray-700'>
                Mon enseignement combine tradition et modernité, s&apos;adaptant aux besoins
                individuels de chaque pratiquant. Je mets l&apos;accent sur l&apos;alignement, la
                respiration et la présence consciente pour une pratique sûre et transformative.
              </p>
            </div>

            <div>
              <h4 className='font-medium text-lg text-retreat-green'>Contact</h4>
              <p className='mt-2 text-gray-700'><EMAIL> | +33 6 12 34 56 78</p>
            </div>
          </div>
        </div>
      ),
    },
    reviews: {
      title: 'Avis des participants',
      content: (
        <div className='space-y-6'>
          {[
            {
              name: 'Marie L.',
              date: 'Août 2023',
              rating: 5,
              retreat: 'Retraite Yoga & Méditation - 7 jours',
              comment:
                'Une expérience transformative qui a dépassé toutes mes attentes. Claire est une enseignante exceptionnelle qui sait parfaitement adapter les pratiques à chaque niveau. La combinaison de yoga, méditation et ateliers de développement personnel était parfaitement équilibrée. Les repas bio étaient délicieux et le cadre provençal absolument magique. Je repars ressourcée et avec de nouveaux outils pour ma pratique quotidienne.',
              highlights: [
                'Enseignement personnalisé',
                'Cuisine bio excellente',
                'Cadre exceptionnel',
              ],
            },
            {
              name: 'Pierre D.',
              date: 'Juillet 2023',
              rating: 5,
              retreat: 'Retraite Yoga & Méditation - 7 jours',
              comment:
                "En tant que débutant, j'appréhendais un peu cette première retraite. Claire a su me mettre à l'aise dès le premier jour avec sa bienveillance et son professionnalisme. Le programme est très bien pensé avec une progression naturelle sur la semaine. Les sessions de yoga du matin et les méditations du soir étaient particulièrement puissantes. Le lieu est sublime et propice au ressourcement.",
              highlights: ['Parfait pour débutants', 'Programme progressif', 'Ambiance apaisante'],
            },
            {
              name: 'Sophie M.',
              date: 'Juin 2023',
              rating: 5,
              retreat: 'Retraite Yoga & Méditation - 7 jours',
              comment:
                "Ma troisième retraite avec Claire et toujours aussi enchantée. Sa capacité à créer un espace sécurisant où chacun peut évoluer à son rythme est remarquable. Cette année, j'ai particulièrement apprécié les ateliers sur la respiration et les séances de yoga restauratif. Le petit groupe permet une vraie attention individuelle. Les chambres sont confortables et la piscine est un vrai plus après les pratiques.",
              highlights: [
                'Attention personnalisée',
                'Hébergement confortable',
                'Pratiques variées',
              ],
            },
            {
              name: 'Thomas B.',
              date: 'Mai 2023',
              rating: 5,
              retreat: 'Retraite Yoga & Méditation - 7 jours',
              comment:
                "Une semaine intense et enrichissante. Claire possède une connaissance approfondie du yoga et de la méditation qu'elle transmet avec passion. Les matinées dynamiques et les après-midis plus contemplatives offrent un bon équilibre. J'ai particulièrement apprécié les discussions philosophiques pendant les repas et l'atmosphère conviviale du groupe. La région est magnifique et les balades méditatives dans la nature sont un vrai plus.",
              highlights: ['Expert en yoga', 'Bel équilibre du programme', 'Cadre naturel'],
            },
          ].map((review, index) => (
            <div key={index} className='border-b pb-6'>
              <div className='flex items-center justify-between mb-2'>
                <div>
                  <span className='font-medium text-lg'>{review.name}</span>
                  <span className='text-gray-500 text-sm ml-2'>{review.date}</span>
                </div>
                <div className='flex'>
                  {[...Array(review.rating)].map((_, i) => (
                    <StarIcon key={i} className='h-5 w-5 text-yellow-400' />
                  ))}
                </div>
              </div>
              <p className='text-sm text-retreat-green font-medium mb-2'>{review.retreat}</p>
              <p className='text-gray-700 mb-3'>{review.comment}</p>
              <div className='flex flex-wrap gap-2'>
                {review.highlights.map((highlight, i) => (
                  <span
                    key={i}
                    className='bg-retreat-green/10 text-retreat-green text-sm px-3 py-1 rounded-full'
                  >
                    {highlight}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      ),
    },
    destination: {
      title: 'La destination',
      content: (
        <div className='space-y-4'>
          <div className='flex items-center space-x-4 mb-4'>
            <img
              src='/images/Firefly Génère des photos hyper réalistes de paysage de voyage à Bali 98158.jpg'
              alt='Vue du Luberon'
              className='w-24 h-24 rounded-full object-cover border-2 border-retreat-green'
            />
            <div>
              <h3 className='text-xl font-semibold'>Luberon, Provence</h3>
              <p className='text-gray-600'>Sud de la France</p>
            </div>
          </div>

          <div className='aspect-w-16 aspect-h-9'>
            <img
              src='/images/provence-landscape.jpg'
              alt='Paysage de Provence'
              className='rounded-lg object-cover'
            />
          </div>

          <div className='grid grid-cols-2 gap-4'>
            <div>
              <h4 className='font-medium'>Activités culturelles</h4>
              <ul className='list-disc list-inside'>
                <li>Villages perchés</li>
                <li>Marchés provençaux</li>
                <li>Routes des vins</li>
                <li>Sites historiques</li>
              </ul>
            </div>
            <div>
              <h4 className='font-medium'>Activités nature</h4>
              <ul className='list-disc list-inside'>
                <li>Randonnées</li>
                <li>Vélo</li>
                <li>Lavandes</li>
                <li>Baignade</li>
              </ul>
            </div>
          </div>
          <div>
            <h4 className='font-medium'>Le domaine</h4>
            <p>
              Niché au cœur du Parc Naturel Régional du Luberon, notre domaine offre une vue
              imprenable sur les vallées et les villages perchés. À 45 minutes de l&apos;aéroport de
              Marseille.
            </p>
          </div>
        </div>
      ),
    },
  };

  // Ajoute les indicateurs de popularité sous le titre
  const PopularityIndicators = () => (
    <div className='flex items-center space-x-4 text-sm text-gray-600'>
      <div className='flex items-center'>
        <FireIcon className='w-5 h-5 text-orange-500 mr-1' />
        <span>{retreatData.popularity.views} vues ce mois</span>
      </div>
      <div className='flex items-center'>
        <UserGroupIcon className='w-5 h-5 text-retreat-green mr-1' />
        <span>{retreatData.popularity.bookingsLastMonth} réservations récentes</span>
      </div>
      <div className='px-2 py-1 bg-retreat-green/10 text-retreat-green rounded-full'>
        Tendance {retreatData.popularity.trend === 'up' ? '↑' : '↓'}
      </div>
    </div>
  );

  const handleImageClick = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <div className='w-full bg-white rounded-xl shadow-lg overflow-hidden'>
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
        {/* Image Section */}
        <div className='relative'>
          <img
            src={retreatData.images[currentImageIndex].url}
            alt={retreatData.images[currentImageIndex].alt}
            className='w-full h-full object-cover'
          />
          <button
            onClick={() => setIsFavorite(!isFavorite)}
            className='absolute top-4 right-4 p-2 rounded-full bg-white/80 hover:bg-white transition-colors'
          >
            <HeartIcon className={`w-6 h-6 ${isFavorite ? 'text-red-500' : 'text-gray-400'}`} />
          </button>

          {/* Points de navigation */}
          <div className='absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2'>
            {retreatData.images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  currentImageIndex === index ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Content Section */}
        <div className='p-6 space-y-6'>
          <div className='text-center'>
            <h2 className='text-3xl font-bold text-retreat-green'>Retraite Yoga & Méditation</h2>
            <p className='text-xl text-gray-600 mt-2'>Provence, France</p>
            <PopularityIndicators />
          </div>

          <p className='text-gray-700'>
            Immergez-vous dans une expérience transformative de 7 jours alliant yoga, méditation et
            découverte de soi. Dans un cadre exceptionnel au cœur de la Provence, retrouvez votre
            équilibre intérieur guidé par des experts passionnés.
          </p>

          {/* Thumbnail Gallery */}
          <div className='grid grid-cols-2 gap-4'>
            {retreatData.images.map((image, index) => (
              <button
                key={index}
                onClick={() => handleImageClick(index)}
                className={`relative overflow-hidden rounded-lg ${
                  currentImageIndex === index ? 'ring-2 ring-retreat-green' : ''
                }`}
              >
                <img src={image.url} alt={image.alt} className='w-full h-40 object-cover' />
              </button>
            ))}
          </div>

          {/* Available Spots */}
          <div className='bg-retreat-green/10 p-4 rounded-lg'>
            <p className='text-retreat-green font-medium'>
              {retreatData.availableSpots} places disponibles sur {retreatData.totalSpots}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation and Expanded Content */}
      <div className='border-t p-6'>
        <div className='flex flex-col space-y-6'>
          {/* Navigation Buttons */}
          <div className='flex justify-center space-x-4'>
            {Object.entries(modalContent).map(([key, { title }]) => (
              <button
                key={key}
                onClick={() => setActiveSection(activeSection === key ? null : key)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  activeSection === key
                    ? 'bg-retreat-green text-white'
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {title}
              </button>
            ))}
          </div>

          {/* Expanded Content */}
          <AnimatePresence>
            {activeSection && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className='overflow-hidden'
              >
                <div className='bg-gray-50 p-6 rounded-lg'>
                  {modalContent[activeSection as keyof typeof modalContent].content}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Booking Section */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center'>
              <StarIcon className='h-5 w-5 text-yellow-400' />
              <span className='ml-1 text-gray-600'>4.9 (128 avis)</span>
            </div>
            <span className='text-2xl font-bold text-retreat-green'>299€</span>
          </div>

          <button className='px-8 bg-retreat-green text-white py-3 rounded-lg hover:bg-opacity-90 transition-colors duration-300 font-medium mx-auto block'>
            Réserver
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeaturedRetreat;
