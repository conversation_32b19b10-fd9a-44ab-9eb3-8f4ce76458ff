import React from 'react';
import { motion } from 'framer-motion';
import Icon from '../../atoms/Icon/Icon';
import type { ComponentProps } from 'react';

interface SearchCategoryProps {
  icon: React.ComponentType<ComponentProps<'svg'>>;
  label: string;
  href: string;
  className?: string;
}

const SearchCategory: React.FC<SearchCategoryProps> = ({ icon, label, href, className = '' }) => {
  return (
    <motion.a
      href={href}
      className={`flex flex-col items-center min-w-fit group ${className}`}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      <div className='p-2 rounded-full bg-gray-50 group-hover:bg-gray-100 transition-colors duration-200'>
        <Icon icon={icon} size='sm' color='text-retreat-green' />
      </div>
      <span className='mt-0.5 text-xs text-gray-600 group-hover:text-retreat-green transition-colors duration-200 whitespace-nowrap'>
        {label}
      </span>
    </motion.a>
  );
};

export default SearchCategory;
