import React from 'react';
import { useTranslationContext } from '../../../providers/TranslationProvider';

interface TranslationExampleProps {
  className?: string;
}

/**
 * Composant d'exemple pour démontrer l'utilisation des traductions
 */
const TranslationExample: React.FC<TranslationExampleProps> = ({ className = '' }) => {
  const { t, currentLanguage, formatDate, formatNumber, formatCurrency } = useTranslationContext();

  const today = new Date();
  const number = 1234.56;
  const price = 99.99;

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <h2 className='text-2xl font-bold mb-4'>{t('landing.hero.title1')}</h2>
      <p className='mb-4'>{t('landing.hero.description')}</p>

      <div className='border-t border-gray-200 pt-4 mt-4'>
        <h3 className='text-lg font-semibold mb-2'>Exemples de formatage</h3>

        <div className='space-y-2'>
          <p>
            <span className='font-medium'>Langue actuelle :</span>{' '}
            {currentLanguage === 'fr' ? 'Français' : 'English'}
          </p>

          <p>
            <span className='font-medium'>Date :</span> {formatDate(today, { dateStyle: 'full' })}
          </p>

          <p>
            <span className='font-medium'>Nombre :</span> {formatNumber(number)}
          </p>

          <p>
            <span className='font-medium'>Prix :</span> {formatCurrency(price, 'EUR')}
          </p>
        </div>
      </div>

      <div className='border-t border-gray-200 pt-4 mt-4'>
        <h3 className='text-lg font-semibold mb-2'>Exemples de traductions</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div>
            <h4 className='font-medium'>Navigation</h4>
            <ul className='list-disc list-inside space-y-1'>
              <li>{t('navigation.home')}</li>
              <li>{t('navigation.retreats')}</li>
              <li>{t('navigation.professionals')}</li>
              <li>{t('navigation.education')}</li>
            </ul>
          </div>

          <div>
            <h4 className='font-medium'>Authentification</h4>
            <ul className='list-disc list-inside space-y-1'>
              <li>{t('auth.signIn')}</li>
              <li>{t('auth.signUp')}</li>
              <li>{t('auth.forgotPassword')}</li>
              <li>{t('auth.resetPassword')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TranslationExample;
