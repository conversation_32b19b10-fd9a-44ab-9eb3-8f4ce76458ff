import React from 'react';
import { Conversation } from '../../../hooks/useChatbot';
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline';

interface ConversationListProps {
  conversations: Conversation[];
  currentConversationId?: string;
  onSelectConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  onNewConversation: () => void;
}

/**
 * Component to render a list of conversations
 */
const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  currentConversationId,
  onSelectConversation,
  onDeleteConversation,
  onNewConversation,
}) => {
  // Format date to a readable string
  const formatDate = (date: Date): string => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const conversationDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (conversationDate.getTime() === today.getTime()) {
      return "Aujourd'hui";
    } else if (conversationDate.getTime() === yesterday.getTime()) {
      return 'Hier';
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
    }
  };

  // Get the first message content for preview
  const getPreviewText = (conversation: Conversation): string => {
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    if (!lastMessage) return '';

    // Truncate message if it's too long
    const maxLength = 60;
    const content = lastMessage.content;

    if (content.length <= maxLength) {
      return content;
    }

    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className='space-y-4'>
      {/* New conversation button */}
      <button
        onClick={onNewConversation}
        className='w-full flex items-center justify-center gap-2 px-4 py-3 bg-retreat-green text-black rounded-lg hover:bg-opacity-90 transition-colors'
      >
        <PlusIcon className='h-5 w-5' />
        <span>Nouvelle conversation</span>
      </button>

      {/* Conversations list */}
      <div className='space-y-2'>
        {conversations.length === 0 ? (
          <div className='text-center text-gray-500 py-4'>Aucune conversation</div>
        ) : (
          conversations.map((conversation) => (
            <div
              key={conversation.id}
              role='button'
              tabIndex={0}
              className={`p-3 rounded-lg cursor-pointer transition-colors flex justify-between items-start ${
                conversation.id === currentConversationId ? 'bg-mint-50' : 'hover:bg-gray-100'
              }`}
              onClick={() => onSelectConversation(conversation.id)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  onSelectConversation(conversation.id);
                }
              }}
            >
              <div className='flex-1 min-w-0'>
                <div className='flex justify-between items-center mb-1'>
                  <h4 className='font-medium text-black truncate'>{conversation.title}</h4>
                  <span className='text-xs text-gray-500 ml-2 whitespace-nowrap'>
                    {formatDate(conversation.updatedAt)}
                  </span>
                </div>
                <p className='text-sm text-gray-600 truncate'>{getPreviewText(conversation)}</p>
              </div>

              {/* Delete button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteConversation(conversation.id);
                }}
                className='ml-2 p-1 text-gray-400 hover:text-red-500 rounded-full hover:bg-gray-200 transition-colors'
                aria-label='Supprimer la conversation'
                title='Supprimer la conversation'
              >
                <TrashIcon className='h-4 w-4' />
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ConversationList;
