import React, { useState } from 'react';
import { ChatMessage } from '../../../../hooks/useChatbot';
import MessageFeedback from '../MessageFeedback';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface ImageMessageProps {
  message: ChatMessage;
  onFeedback?: (messageId: string, isPositive: boolean) => void;
}

/**
 * Component to render an image message
 */
const ImageMessage: React.FC<ImageMessageProps> = ({ message, onFeedback }) => {
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);

  // Get image URL from metadata
  const imageUrl = typeof message.metadata?.imageUrl === 'string' ? message.metadata.imageUrl : '';
  const altText =
    typeof message.metadata?.altText === 'string'
      ? message.metadata.altText
      : 'Image from assistant';

  const openLightbox = () => {
    setIsLightboxOpen(true);
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  return (
    <div className='flex flex-col'>
      {/* Message content */}
      {message.content && <p className='whitespace-pre-wrap mb-2'>{message.content}</p>}

      {/* Image */}
      {imageUrl && (
        <div className='mt-2 mb-2'>
          <button
            onClick={openLightbox}
            className='block w-full text-left p-0 border-0 bg-transparent cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-retreat-green rounded-lg'
            aria-label={`View image: ${altText}`}
            type='button'
          >
            <img
              src={imageUrl}
              alt={altText}
              className='max-w-full rounded-lg hover:opacity-90 transition-opacity max-h-60 object-contain'
            />
          </button>
        </div>
      )}

      {/* Lightbox */}
      {isLightboxOpen && (
        <div
          className='fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50'
          onClick={closeLightbox}
          role='button'
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ' || e.key === 'Escape') {
              closeLightbox();
            }
          }}
          aria-label='Close image lightbox'
        >
          <div
            className='relative max-w-4xl max-h-screen p-4'
            onClick={(e) => e.stopPropagation()}
            role='dialog'
            aria-modal='true'
          >
            <button
              className='absolute top-2 right-2 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors'
              onClick={closeLightbox}
            >
              <XMarkIcon className='h-6 w-6' />
            </button>
            <img src={imageUrl} alt={altText} className='max-w-full max-h-[90vh] object-contain' />
          </div>
        </div>
      )}

      {/* Timestamp */}
      <div className='text-xs opacity-70 mt-1 text-right'>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>

      {/* Feedback buttons */}
      {message.role === 'assistant' && onFeedback && (
        <MessageFeedback messageId={message.id} onFeedback={onFeedback} />
      )}
    </div>
  );
};

export default ImageMessage;
