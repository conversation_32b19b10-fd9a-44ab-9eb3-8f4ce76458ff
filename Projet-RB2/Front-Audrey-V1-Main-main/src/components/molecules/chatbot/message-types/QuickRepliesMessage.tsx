import React from 'react';
import { ChatMessage } from '../../../../hooks/useChatbot';
import { Button } from '../../../../services/api/chatbotService';
import MessageFeedback from '../MessageFeedback';

interface QuickRepliesMessageProps {
  message: ChatMessage;
  onButtonClick?: (button: Button) => void;
  onFeedback?: (messageId: string, isPositive: boolean) => void;
}

/**
 * Component to render a message with quick replies
 */
const QuickRepliesMessage: React.FC<QuickRepliesMessageProps> = ({
  message,
  onButtonClick,
  onFeedback,
}) => {
  const handleButtonClick = (button: Button) => {
    if (onButtonClick) {
      onButtonClick(button);
    }
  };

  return (
    <div className='flex flex-col'>
      {/* Message content */}
      <p className='whitespace-pre-wrap mb-2'>{message.content}</p>

      {/* Quick replies */}
      <div className='flex flex-wrap gap-2 mt-2'>
        {message.buttons?.map((button, index) => (
          <button
            key={index}
            onClick={() => handleButtonClick(button)}
            className='px-3 py-1 bg-mint-50 text-black rounded-full text-sm hover:bg-mint-100 transition-colors'
          >
            {button.text}
          </button>
        ))}
      </div>

      {/* Timestamp */}
      <div className='text-xs opacity-70 mt-2 text-right'>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </div>

      {/* Feedback buttons */}
      {message.role === 'assistant' && onFeedback && (
        <MessageFeedback messageId={message.id} onFeedback={onFeedback} />
      )}
    </div>
  );
};

export default QuickRepliesMessage;
