import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuthContext } from '../../../hooks/useAuthContext';
import { partnerService } from '../../../services/api/partnerService';

interface MenuItem {
  label: string;
  href: string;
  subItems?: MenuItem[];
  requiredRole?: string;
}

const Navbar: React.FC = () => {
  const { user, logout } = useAuthContext();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [isPartner, setIsPartner] = useState<boolean>(false);

  // Vérifier si l'utilisateur est un partenaire
  useEffect(() => {
    const checkPartnerStatus = async () => {
      if (user) {
        try {
          const partner = await partnerService.getPartnerByUserId(user.id);
          setIsPartner(!!partner);
        } catch (error) {
          console.error('Erreur lors de la vérification du statut de partenaire:', error);
          setIsPartner(false);
        }
      } else {
        setIsPartner(false);
      }
    };

    checkPartnerStatus();
  }, [user]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const menuItems: MenuItem[] = [
    { label: 'Accueil', href: '/' },
    { label: 'Retraites', href: '/retreats' },
    { label: 'Découvrir', href: '/discover' },
    { label: 'Blog', href: '/blog' },
    { label: 'À propos', href: '/about' },
    { label: 'Aide', href: '/aide' },
  ];

  const userMenuItems: MenuItem[] = user
    ? [
        { label: 'Mon compte', href: '/compte' },
        { label: 'Mes réservations', href: '/bookings' },
        { label: 'Messagerie', href: '/messaging' },
        { label: 'Préférences d\'explication', href: '/explanation-preferences' },
        ...(user.role === 'ADMIN' ? [{ label: 'Administration', href: '/admin/partners' }] : []),
        ...(isPartner ? [
          { label: 'Tableau de bord partenaire', href: '/partner/dashboard' },
          { label: 'Gestion de contenu', href: '/content-management' }
        ] : []),
        { label: 'Déconnexion', href: '/logout' },
      ]
    : [
        { label: 'Connexion', href: '/login' },
        { label: 'Inscription', href: '/register' },
      ];

  const handleLogout = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (e.currentTarget.getAttribute('href') === '/logout') {
      e.preventDefault();
      logout();
    }
  };

  return (
    <nav className="bg-white shadow-md fixed top-0 left-0 right-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="text-2xl font-bold text-retreat-green">
                Retreat And Be
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {menuItems.map((item) => (
                <Link
                  key={item.label}
                  to={item.href}
                  className={`inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    location.pathname === item.href
                      ? 'border-retreat-green text-gray-900'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            {!user && (
              <Link
                to="/become-partner"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
              >
                Devenir partenaire
              </Link>
            )}
            <div className="ml-3 relative">
              <div>
                <button
                  type="button"
                  className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
                  id="user-menu-button"
                  aria-expanded={activeSubmenu === 'user'}
                  aria-haspopup="true"
                  onClick={() => setActiveSubmenu(activeSubmenu === 'user' ? null : 'user')}
                >
                  <span className="sr-only">Ouvrir le menu utilisateur</span>
                  <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                    {user ? user.firstName?.charAt(0) || user.email.charAt(0) : 'G'}
                  </div>
                </button>
              </div>

              {activeSubmenu === 'user' && (
                <div
                  className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="user-menu-button"
                  tabIndex={-1}
                >
                  {userMenuItems.map((item) => (
                    <Link
                      key={item.label}
                      to={item.href}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                      tabIndex={-1}
                      onClick={handleLogout}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
          <div className="-mr-2 flex items-center sm:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-retreat-green"
              aria-expanded={isMenuOpen}
              onClick={toggleMenu}
            >
              <span className="sr-only">Ouvrir le menu principal</span>
              <svg
                className={`${isMenuOpen ? 'hidden' : 'block'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
              <svg
                className={`${isMenuOpen ? 'block' : 'hidden'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`${isMenuOpen ? 'block' : 'hidden'} sm:hidden`}>
        <div className="pt-2 pb-3 space-y-1">
          {menuItems.map((item) => (
            <Link
              key={item.label}
              to={item.href}
              className={`block pl-3 pr-4 py-2 border-l-4 text-base font-medium ${
                location.pathname === item.href
                  ? 'border-retreat-green text-retreat-green bg-retreat-green-50'
                  : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'
              }`}
            >
              {item.label}
            </Link>
          ))}
        </div>
        <div className="pt-4 pb-3 border-t border-gray-200">
          <div className="flex items-center px-4">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                {user ? user.firstName?.charAt(0) || user.email.charAt(0) : 'G'}
              </div>
            </div>
            <div className="ml-3">
              <div className="text-base font-medium text-gray-800">
                {user ? `${user.firstName || ''} ${user.lastName || ''}` : 'Invité'}
              </div>
              <div className="text-sm font-medium text-gray-500">{user?.email || ''}</div>
            </div>
          </div>
          <div className="mt-3 space-y-1">
            {userMenuItems.map((item) => (
              <Link
                key={item.label}
                to={item.href}
                className="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                onClick={handleLogout}
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
