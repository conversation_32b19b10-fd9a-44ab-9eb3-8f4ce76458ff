import React from 'react';
import RetreatCard from '../../molecules/RetreatCard/RetreatCard';

interface Retreat {
  id: string;
  title: string;
  image: string;
  location: string;
  price: number;
  duration: string;
  maxParticipants: number;
  rating: number;
  tags: string[];
  host: {
    name: string;
    avatar: string;
  };
}

interface RetreatGridProps {
  retreats: Retreat[];
  className?: string;
  loading?: boolean;
  error?: string;
}

const RetreatGrid: React.FC<RetreatGridProps> = ({
  retreats,
  className = '',
  loading = false,
  error,
}) => {
  if (loading) {
    return (
      <div
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}
      >
        {[...Array(8)].map((_, index) => (
          <div key={index} className='bg-white rounded-lg shadow-sm overflow-hidden animate-pulse'>
            <div className='aspect-[4/3] bg-gray-200' />
            <div className='p-4 space-y-3'>
              <div className='h-4 bg-gray-200 rounded w-3/4' />
              <div className='h-4 bg-gray-200 rounded w-1/2' />
              <div className='h-4 bg-gray-200 rounded w-2/3' />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className='text-red-600'>{error}</p>
      </div>
    );
  }

  if (retreats.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className='text-gray-600'>Aucune retraite trouvée</p>
      </div>
    );
  }

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}
    >
      {retreats.map((retreat) => (
        <RetreatCard key={retreat.id} {...retreat} />
      ))}
    </div>
  );
};

export default RetreatGrid;
