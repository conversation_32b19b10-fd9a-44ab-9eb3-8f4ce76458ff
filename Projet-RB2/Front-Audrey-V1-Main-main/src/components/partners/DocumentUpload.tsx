import React, { useState } from 'react';
import { fileService } from '../../services/api/fileService';
import { partnerService, PartnerDocumentData } from '../../services/api/partnerService';
import { toast } from 'react-toastify';

// Document types from the backend enum
const DOCUMENT_TYPES = [
  { value: 'IDENTITY', label: 'Pièce d\'identité' },
  { value: 'BUSINESS_REGISTRATION', label: 'Immatriculation d\'entreprise' },
  { value: 'INSURANCE', label: 'Attestation d\'assurance' },
  { value: 'CERTIFICATION', label: 'Certification professionnelle' },
  { value: 'TAX_DOCUMENT', label: 'Document fiscal' },
  { value: 'BANK_DETAILS', label: 'Coordonnées bancaires' },
  { value: 'OTHER', label: 'Autre document' },
];

interface DocumentUploadProps {
  partnerId: string;
  onDocumentAdded: () => void;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({ partnerId, onDocumentAdded }) => {
  const [file, setFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState<string>('BUSINESS_REGISTRATION');
  const [description, setDescription] = useState<string>('');
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!file) {
      toast.error('Veuillez sélectionner un fichier');
      return;
    }
    
    if (!description) {
      toast.error('Veuillez ajouter une description');
      return;
    }
    
    setIsUploading(true);
    setUploadProgress(10);
    
    try {
      // First, upload the file
      const uploadedFile = await fileService.uploadFile(file, 'partner-documents');
      setUploadProgress(50);
      
      // Then, create the document record
      const documentData: PartnerDocumentData = {
        type: documentType,
        description,
        fileId: uploadedFile.id,
      };
      
      await partnerService.addDocument(partnerId, documentData);
      setUploadProgress(100);
      
      // Reset form
      setFile(null);
      setDescription('');
      
      // Notify parent component
      onDocumentAdded();
      
      toast.success('Document ajouté avec succès');
    } catch (error) {
      console.error('Error uploading document:', error);
      toast.error('Une erreur est survenue lors de l\'ajout du document');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold mb-4">Ajouter un document</h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Type de document *
          </label>
          <select
            value={documentType}
            onChange={(e) => setDocumentType(e.target.value)}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
            required
          >
            {DOCUMENT_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description *
          </label>
          <input
            type="text"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
            placeholder="Ex: Extrait Kbis de mon entreprise"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Fichier *
          </label>
          <input
            type="file"
            onChange={handleFileChange}
            className="w-full text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-retreat-green file:text-white hover:file:bg-retreat-green-dark"
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            Formats acceptés: PDF, JPG, PNG. Taille max: 5MB
          </p>
        </div>
        
        {isUploading && (
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-retreat-green h-2.5 rounded-full"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        )}
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isUploading || !file}
            className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:opacity-50"
          >
            {isUploading ? 'Envoi en cours...' : 'Ajouter le document'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DocumentUpload;
