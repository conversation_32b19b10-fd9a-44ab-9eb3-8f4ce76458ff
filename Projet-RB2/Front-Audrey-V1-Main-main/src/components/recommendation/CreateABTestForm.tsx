import React, { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { abTestingService, CreateABTestRequest } from '../../services/api/abTestingService';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { TextArea } from '../ui/TextArea';
import { DatePicker } from '../ui/DatePicker';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';

interface Strategy {
  name: string;
  weight: number;
}

interface CreateABTestFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export const CreateABTestForm: React.FC<CreateABTestFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [strategies, setStrategies] = useState<Strategy[]>([
    { name: 'CONTENT_BASED', weight: 50 },
    { name: 'COLLABORATIVE_FILTERING', weight: 50 },
  ]);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleAddStrategy = () => {
    setStrategies([...strategies, { name: '', weight: 0 }]);
  };

  const handleRemoveStrategy = (index: number) => {
    const newStrategies = [...strategies];
    newStrategies.splice(index, 1);
    setStrategies(newStrategies);
  };

  const handleStrategyNameChange = (index: number, value: string) => {
    const newStrategies = [...strategies];
    newStrategies[index].name = value;
    setStrategies(newStrategies);
  };

  const handleStrategyWeightChange = (index: number, value: string) => {
    const newStrategies = [...strategies];
    newStrategies[index].weight = parseInt(value, 10) || 0;
    setStrategies(newStrategies);
  };

  const normalizeWeights = () => {
    const totalWeight = strategies.reduce((sum, strategy) => sum + strategy.weight, 0);
    
    if (totalWeight === 0) {
      // Si tous les poids sont à 0, distribuer équitablement
      const equalWeight = 100 / strategies.length;
      return strategies.map(strategy => ({
        ...strategy,
        weight: equalWeight,
      }));
    }
    
    // Normaliser les poids pour qu'ils totalisent 100%
    return strategies.map(strategy => ({
      ...strategy,
      weight: (strategy.weight / totalWeight) * 100,
    }));
  };

  const validateForm = () => {
    if (!name.trim()) {
      setError(t('abTesting.form.errors.nameRequired'));
      return false;
    }
    
    if (!description.trim()) {
      setError(t('abTesting.form.errors.descriptionRequired'));
      return false;
    }
    
    if (strategies.length < 2) {
      setError(t('abTesting.form.errors.minStrategies'));
      return false;
    }
    
    if (strategies.some(strategy => !strategy.name.trim())) {
      setError(t('abTesting.form.errors.strategyNameRequired'));
      return false;
    }
    
    if (endDate && startDate && endDate < startDate) {
      setError(t('abTesting.form.errors.endDateBeforeStartDate'));
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const normalizedStrategies = normalizeWeights();
      
      const request: CreateABTestRequest = {
        name,
        description,
        strategies: normalizedStrategies.map(s => s.name),
        weights: normalizedStrategies.map(s => s.weight / 100), // Convertir en pourcentage décimal
        startDate: startDate.toISOString(),
        endDate: endDate ? endDate.toISOString() : undefined,
      };
      
      await abTestingService.createTest(request);
      
      setLoading(false);
      onSuccess();
    } catch (error) {
      console.error('Error creating A/B test:', error);
      setError(t('abTesting.form.errors.createFailed'));
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="error" title={t('common.error')} message={error} />
      )}
      
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
          {t('abTesting.form.name')} *
        </label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder={t('abTesting.form.namePlaceholder')}
          required
        />
      </div>
      
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          {t('abTesting.form.description')} *
        </label>
        <TextArea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder={t('abTesting.form.descriptionPlaceholder')}
          rows={3}
          required
        />
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700">
            {t('abTesting.form.strategies')} *
          </label>
          <Button
            type="button"
            onClick={handleAddStrategy}
            variant="secondary"
            size="sm"
          >
            {t('abTesting.form.addStrategy')}
          </Button>
        </div>
        
        <div className="space-y-3">
          {strategies.map((strategy, index) => (
            <div key={index} className="flex space-x-2">
              <div className="flex-grow">
                <Input
                  value={strategy.name}
                  onChange={(e) => handleStrategyNameChange(index, e.target.value)}
                  placeholder={t('abTesting.form.strategyNamePlaceholder')}
                  required
                />
              </div>
              <div className="w-24">
                <Input
                  type="number"
                  value={strategy.weight.toString()}
                  onChange={(e) => handleStrategyWeightChange(index, e.target.value)}
                  placeholder={t('abTesting.form.weightPlaceholder')}
                  min="0"
                  max="100"
                  required
                />
              </div>
              {strategies.length > 2 && (
                <Button
                  type="button"
                  onClick={() => handleRemoveStrategy(index)}
                  variant="danger"
                  size="sm"
                >
                  {t('common.remove')}
                </Button>
              )}
            </div>
          ))}
        </div>
        
        <p className="mt-2 text-sm text-gray-500">
          {t('abTesting.form.strategyHelp')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
            {t('abTesting.form.startDate')} *
          </label>
          <DatePicker
            id="startDate"
            selected={startDate}
            onChange={(date) => setStartDate(date as Date)}
            required
          />
        </div>
        
        <div>
          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
            {t('abTesting.form.endDate')}
          </label>
          <DatePicker
            id="endDate"
            selected={endDate}
            onChange={(date) => setEndDate(date as Date)}
            minDate={startDate}
            placeholderText={t('abTesting.form.endDatePlaceholder')}
          />
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          onClick={onCancel}
          variant="secondary"
          disabled={loading}
        >
          {t('common.cancel')}
        </Button>
        
        <Button
          type="submit"
          variant="primary"
          disabled={loading}
        >
          {loading ? <Spinner size="sm" /> : t('abTesting.form.create')}
        </Button>
      </div>
    </form>
  );
};

export default CreateABTestForm;
