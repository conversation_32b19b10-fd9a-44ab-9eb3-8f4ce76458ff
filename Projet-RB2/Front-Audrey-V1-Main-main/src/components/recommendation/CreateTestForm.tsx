import React, { useState, useEffect } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { recommendationTestingService } from '../../services/api/recommendationTestingService';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { TextArea } from '../ui/TextArea';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';
import { MultiSelect } from '../ui/MultiSelect';

interface CreateTestFormProps {
  onSubmit: (test: any) => void;
  onCancel: () => void;
}

export const CreateTestForm: React.FC<CreateTestFormProps> = ({
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [strategies, setStrategies] = useState<string[]>([]);
  const [userSegments, setUserSegments] = useState<string[]>([]);
  const [metrics, setMetrics] = useState<string[]>([]);
  const [sampleSize, setSampleSize] = useState<number>(100);
  const [testDuration, setTestDuration] = useState<number>(7);
  const [availableStrategies, setAvailableStrategies] = useState<string[]>([]);
  const [availableSegments, setAvailableSegments] = useState<string[]>([]);
  const [availableMetrics, setAvailableMetrics] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAvailableOptions();
  }, []);

  const fetchAvailableOptions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [strategiesData, segmentsData, metricsData] = await Promise.all([
        recommendationTestingService.getAvailableStrategies(),
        recommendationTestingService.getAvailableSegments(),
        recommendationTestingService.getAvailableMetrics(),
      ]);
      
      setAvailableStrategies(strategiesData);
      setAvailableSegments(segmentsData);
      setAvailableMetrics(metricsData);
      
      // Définir des valeurs par défaut
      setStrategies(strategiesData.slice(0, 2));
      setUserSegments(['ALL_USERS']);
      setMetrics(metricsData.slice(0, 3));
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching available options:', error);
      setError(t('testing.form.errors.fetchOptionsFailed'));
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    const test = {
      name,
      description,
      strategies,
      userSegments,
      metrics,
      sampleSize,
      testDuration,
    };
    
    onSubmit(test);
  };

  const validateForm = () => {
    if (!name.trim()) {
      setError(t('testing.form.errors.nameRequired'));
      return false;
    }
    
    if (!description.trim()) {
      setError(t('testing.form.errors.descriptionRequired'));
      return false;
    }
    
    if (strategies.length === 0) {
      setError(t('testing.form.errors.strategiesRequired'));
      return false;
    }
    
    if (userSegments.length === 0) {
      setError(t('testing.form.errors.segmentsRequired'));
      return false;
    }
    
    if (metrics.length === 0) {
      setError(t('testing.form.errors.metricsRequired'));
      return false;
    }
    
    if (sampleSize <= 0) {
      setError(t('testing.form.errors.invalidSampleSize'));
      return false;
    }
    
    if (testDuration <= 0) {
      setError(t('testing.form.errors.invalidTestDuration'));
      return false;
    }
    
    return true;
  };

  if (loading) {
    return (
      <div className="p-4 flex justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="p-4 space-y-4">
      {error && (
        <Alert variant="error" title={t('common.error')} message={error} />
      )}
      
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
          {t('testing.form.name')} *
        </label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder={t('testing.form.namePlaceholder')}
          required
        />
      </div>
      
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          {t('testing.form.description')} *
        </label>
        <TextArea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder={t('testing.form.descriptionPlaceholder')}
          rows={3}
          required
        />
      </div>
      
      <div>
        <label htmlFor="strategies" className="block text-sm font-medium text-gray-700 mb-1">
          {t('testing.form.strategies')} *
        </label>
        <MultiSelect
          options={availableStrategies.map(strategy => ({
            value: strategy,
            label: t(`recommendation.strategies.${strategy.toLowerCase()}`, strategy),
          }))}
          value={strategies.map(strategy => ({
            value: strategy,
            label: t(`recommendation.strategies.${strategy.toLowerCase()}`, strategy),
          }))}
          onChange={(selected) => setStrategies(selected.map(option => option.value))}
          placeholder={t('testing.form.strategiesPlaceholder')}
        />
        <p className="mt-1 text-sm text-gray-500">
          {t('testing.form.strategiesHelp')}
        </p>
      </div>
      
      <div>
        <label htmlFor="userSegments" className="block text-sm font-medium text-gray-700 mb-1">
          {t('testing.form.userSegments')} *
        </label>
        <MultiSelect
          options={availableSegments.map(segment => ({
            value: segment,
            label: t(`testing.segments.${segment.toLowerCase()}`, segment),
          }))}
          value={userSegments.map(segment => ({
            value: segment,
            label: t(`testing.segments.${segment.toLowerCase()}`, segment),
          }))}
          onChange={(selected) => setUserSegments(selected.map(option => option.value))}
          placeholder={t('testing.form.userSegmentsPlaceholder')}
        />
        <p className="mt-1 text-sm text-gray-500">
          {t('testing.form.userSegmentsHelp')}
        </p>
      </div>
      
      <div>
        <label htmlFor="metrics" className="block text-sm font-medium text-gray-700 mb-1">
          {t('testing.form.metrics')} *
        </label>
        <MultiSelect
          options={availableMetrics.map(metric => ({
            value: metric,
            label: t(`testing.metrics.${metric.toLowerCase()}`, metric),
          }))}
          value={metrics.map(metric => ({
            value: metric,
            label: t(`testing.metrics.${metric.toLowerCase()}`, metric),
          }))}
          onChange={(selected) => setMetrics(selected.map(option => option.value))}
          placeholder={t('testing.form.metricsPlaceholder')}
        />
        <p className="mt-1 text-sm text-gray-500">
          {t('testing.form.metricsHelp')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="sampleSize" className="block text-sm font-medium text-gray-700 mb-1">
            {t('testing.form.sampleSize')} *
          </label>
          <Input
            id="sampleSize"
            type="number"
            value={sampleSize.toString()}
            onChange={(e) => setSampleSize(parseInt(e.target.value) || 0)}
            min="1"
            required
          />
          <p className="mt-1 text-sm text-gray-500">
            {t('testing.form.sampleSizeHelp')}
          </p>
        </div>
        
        <div>
          <label htmlFor="testDuration" className="block text-sm font-medium text-gray-700 mb-1">
            {t('testing.form.testDuration')} *
          </label>
          <Input
            id="testDuration"
            type="number"
            value={testDuration.toString()}
            onChange={(e) => setTestDuration(parseInt(e.target.value) || 0)}
            min="1"
            required
          />
          <p className="mt-1 text-sm text-gray-500">
            {t('testing.form.testDurationHelp')}
          </p>
        </div>
      </div>
      
      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          variant="primary"
        >
          {t('testing.form.create')}
        </Button>
      </div>
    </form>
  );
};

export default CreateTestForm;
