import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { useAuthContext } from '../../hooks/useAuthContext';
import { recommendationAnalyticsService } from '../../services/api/recommendationAnalyticsService';
import { FadeIn } from '../ui/FadeIn';
import { Tabs, TabList, Tab, TabPanel } from '../ui/Tabs';
import { Button } from '../ui/Button';
import { Spinner } from '../ui/Spinner';
import { Alert } from '../ui/Alert';
import { DateRangePicker } from '../ui/DateRangePicker';
import { Select } from '../ui/Select';
import { Card } from '../ui/Card';
import { MetricsChart } from '../analytics/MetricsChart';
import { MetricsTable } from '../analytics/MetricsTable';
import { ExportDataButton } from '../analytics/ExportDataButton';
import { HeatMap } from '../analytics/HeatMap';
import { SankeyDiagram } from '../analytics/SankeyDiagram';

interface RecommendationMetrics {
  totalRecommendations: number;
  totalImpressions: number;
  totalClicks: number;
  totalConversions: number;
  clickThroughRate: number;
  conversionRate: number;
  averageRelevanceScore: number;
  averageSatisfactionScore: number;
}

interface StrategyPerformance {
  strategy: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
  relevanceScore: number;
  satisfactionScore: number;
}

interface CategoryPerformance {
  category: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
}

interface UserSegmentPerformance {
  segment: string;
  userCount: number;
  impressionsPerUser: number;
  clicksPerUser: number;
  conversionsPerUser: number;
  clickThroughRate: number;
  conversionRate: number;
}

interface TimeSeriesData {
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  clickThroughRate: number;
  conversionRate: number;
}

interface RecommendationFlow {
  source: string;
  target: string;
  value: number;
}

interface RecommendationAnalyticsDashboardProps {
  userId?: string;
  isAdmin?: boolean;
}

export const RecommendationAnalyticsDashboard: React.FC<RecommendationAnalyticsDashboardProps> = ({
  userId,
  isAdmin = false,
}) => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    new Date(),
  ]);
  const [granularity, setGranularity] = useState<'day' | 'week' | 'month'>('day');
  const [selectedStrategy, setSelectedStrategy] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSegment, setSelectedSegment] = useState<string>('all');
  const [metrics, setMetrics] = useState<RecommendationMetrics | null>(null);
  const [strategyPerformance, setStrategyPerformance] = useState<StrategyPerformance[]>([]);
  const [categoryPerformance, setCategoryPerformance] = useState<CategoryPerformance[]>([]);
  const [userSegmentPerformance, setUserSegmentPerformance] = useState<UserSegmentPerformance[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [recommendationFlow, setRecommendationFlow] = useState<RecommendationFlow[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [availableStrategies, setAvailableStrategies] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableSegments, setAvailableSegments] = useState<string[]>([]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange, granularity, selectedStrategy, selectedCategory, selectedSegment]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const targetUserId = userId || (isAdmin ? undefined : user?.id);
      
      // Fetch overview metrics
      const metricsData = await recommendationAnalyticsService.getRecommendationMetrics({
        userId: targetUserId,
        startDate: dateRange[0].toISOString(),
        endDate: dateRange[1].toISOString(),
        strategy: selectedStrategy !== 'all' ? selectedStrategy : undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        segment: selectedSegment !== 'all' ? selectedSegment : undefined,
      });
      setMetrics(metricsData);
      
      // Fetch strategy performance
      const strategyData = await recommendationAnalyticsService.getStrategyPerformance({
        userId: targetUserId,
        startDate: dateRange[0].toISOString(),
        endDate: dateRange[1].toISOString(),
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        segment: selectedSegment !== 'all' ? selectedSegment : undefined,
      });
      setStrategyPerformance(strategyData);
      
      // Fetch category performance
      const categoryData = await recommendationAnalyticsService.getCategoryPerformance({
        userId: targetUserId,
        startDate: dateRange[0].toISOString(),
        endDate: dateRange[1].toISOString(),
        strategy: selectedStrategy !== 'all' ? selectedStrategy : undefined,
        segment: selectedSegment !== 'all' ? selectedSegment : undefined,
      });
      setCategoryPerformance(categoryData);
      
      // Fetch user segment performance
      if (isAdmin) {
        const segmentData = await recommendationAnalyticsService.getUserSegmentPerformance({
          startDate: dateRange[0].toISOString(),
          endDate: dateRange[1].toISOString(),
          strategy: selectedStrategy !== 'all' ? selectedStrategy : undefined,
          category: selectedCategory !== 'all' ? selectedCategory : undefined,
        });
        setUserSegmentPerformance(segmentData);
      }
      
      // Fetch time series data
      const timeSeriesData = await recommendationAnalyticsService.getTimeSeriesData({
        userId: targetUserId,
        startDate: dateRange[0].toISOString(),
        endDate: dateRange[1].toISOString(),
        granularity,
        strategy: selectedStrategy !== 'all' ? selectedStrategy : undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        segment: selectedSegment !== 'all' ? selectedSegment : undefined,
      });
      setTimeSeriesData(timeSeriesData);
      
      // Fetch recommendation flow data
      const flowData = await recommendationAnalyticsService.getRecommendationFlow({
        userId: targetUserId,
        startDate: dateRange[0].toISOString(),
        endDate: dateRange[1].toISOString(),
        strategy: selectedStrategy !== 'all' ? selectedStrategy : undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        segment: selectedSegment !== 'all' ? selectedSegment : undefined,
      });
      setRecommendationFlow(flowData);
      
      // Fetch available filters
      const filtersData = await recommendationAnalyticsService.getAvailableFilters({
        userId: targetUserId,
        startDate: dateRange[0].toISOString(),
        endDate: dateRange[1].toISOString(),
      });
      setAvailableStrategies(filtersData.strategies);
      setAvailableCategories(filtersData.categories);
      setAvailableSegments(filtersData.segments);
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      setError(t('analytics.errors.fetchFailed'));
      setLoading(false);
    }
  };

  const handleDateRangeChange = (range: [Date, Date]) => {
    setDateRange(range);
  };

  const handleGranularityChange = (value: string) => {
    setGranularity(value as 'day' | 'week' | 'month');
  };

  const handleStrategyChange = (value: string) => {
    setSelectedStrategy(value);
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
  };

  const handleSegmentChange = (value: string) => {
    setSelectedSegment(value);
  };

  const handleRefresh = () => {
    fetchAnalyticsData();
  };

  const exportData = useMemo(() => {
    return {
      metrics,
      strategyPerformance,
      categoryPerformance,
      userSegmentPerformance,
      timeSeriesData,
      filters: {
        dateRange,
        granularity,
        strategy: selectedStrategy,
        category: selectedCategory,
        segment: selectedSegment,
      },
    };
  }, [
    metrics,
    strategyPerformance,
    categoryPerformance,
    userSegmentPerformance,
    timeSeriesData,
    dateRange,
    granularity,
    selectedStrategy,
    selectedCategory,
    selectedSegment,
  ]);

  if (loading && !metrics) {
    return (
      <div className="flex justify-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <FadeIn>
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">{t('analytics.recommendation.title')}</h2>
        
        <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
          <DateRangePicker
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={handleDateRangeChange}
            className="w-64"
          />
          
          <Select
            value={granularity}
            onChange={handleGranularityChange}
            options={[
              { value: 'day', label: t('analytics.granularity.day') },
              { value: 'week', label: t('analytics.granularity.week') },
              { value: 'month', label: t('analytics.granularity.month') },
            ]}
            className="w-32"
          />
          
          <Button
            variant="secondary"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? <Spinner size="sm" /> : t('common.refresh')}
          </Button>
          
          <ExportDataButton
            data={exportData}
            filename={`recommendation_analytics_${new Date().toISOString().split('T')[0]}`}
          />
        </div>
      </div>
      
      {error && (
        <Alert variant="error" title={t('common.error')} message={error} className="mb-4" />
      )}
      
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('analytics.filters.strategy')}
            </label>
            <Select
              value={selectedStrategy}
              onChange={handleStrategyChange}
              options={[
                { value: 'all', label: t('analytics.filters.allStrategies') },
                ...availableStrategies.map(strategy => ({
                  value: strategy,
                  label: t(`recommendation.strategies.${strategy.toLowerCase()}`, strategy),
                })),
              ]}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('analytics.filters.category')}
            </label>
            <Select
              value={selectedCategory}
              onChange={handleCategoryChange}
              options={[
                { value: 'all', label: t('analytics.filters.allCategories') },
                ...availableCategories.map(category => ({
                  value: category,
                  label: category,
                })),
              ]}
              className="w-full"
            />
          </div>
          
          {isAdmin && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('analytics.filters.segment')}
              </label>
              <Select
                value={selectedSegment}
                onChange={handleSegmentChange}
                options={[
                  { value: 'all', label: t('analytics.filters.allSegments') },
                  ...availableSegments.map(segment => ({
                    value: segment,
                    label: t(`analytics.segments.${segment.toLowerCase()}`, segment),
                  })),
                ]}
                className="w-full"
              />
            </div>
          )}
        </div>
      </div>
      
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-500">{t('analytics.metrics.totalRecommendations')}</h3>
              <p className="mt-1 text-3xl font-semibold">{metrics.totalRecommendations.toLocaleString()}</p>
            </div>
          </Card>
          
          <Card>
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-500">{t('analytics.metrics.clickThroughRate')}</h3>
              <p className="mt-1 text-3xl font-semibold">{(metrics.clickThroughRate * 100).toFixed(2)}%</p>
              <p className="text-sm text-gray-500">{metrics.totalClicks.toLocaleString()} {t('analytics.metrics.clicks')}</p>
            </div>
          </Card>
          
          <Card>
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-500">{t('analytics.metrics.conversionRate')}</h3>
              <p className="mt-1 text-3xl font-semibold">{(metrics.conversionRate * 100).toFixed(2)}%</p>
              <p className="text-sm text-gray-500">{metrics.totalConversions.toLocaleString()} {t('analytics.metrics.conversions')}</p>
            </div>
          </Card>
          
          <Card>
            <div className="p-4">
              <h3 className="text-sm font-medium text-gray-500">{t('analytics.metrics.averageRelevance')}</h3>
              <p className="mt-1 text-3xl font-semibold">{(metrics.averageRelevanceScore * 100).toFixed(2)}%</p>
              <p className="text-sm text-gray-500">{(metrics.averageSatisfactionScore * 100).toFixed(2)}% {t('analytics.metrics.satisfaction')}</p>
            </div>
          </Card>
        </div>
      )}
      
      <Tabs>
        <TabList>
          <Tab>{t('analytics.tabs.overview')}</Tab>
          <Tab>{t('analytics.tabs.strategies')}</Tab>
          <Tab>{t('analytics.tabs.categories')}</Tab>
          {isAdmin && <Tab>{t('analytics.tabs.segments')}</Tab>}
          <Tab>{t('analytics.tabs.flow')}</Tab>
        </TabList>
        
        <TabPanel>
          {timeSeriesData.length > 0 ? (
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <h3 className="text-lg font-medium mb-4">{t('analytics.charts.performanceOverTime')}</h3>
              <div className="h-80">
                <MetricsChart
                  data={timeSeriesData}
                  dataKeys={['impressions', 'clicks', 'conversions']}
                  xAxisDataKey="date"
                  height={300}
                />
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-500">{t('analytics.noData')}</p>
            </div>
          )}
          
          {timeSeriesData.length > 0 && (
            <div className="bg-white rounded-lg shadow-md p-4">
              <h3 className="text-lg font-medium mb-4">{t('analytics.charts.ratesOverTime')}</h3>
              <div className="h-80">
                <MetricsChart
                  data={timeSeriesData}
                  dataKeys={['clickThroughRate', 'conversionRate']}
                  xAxisDataKey="date"
                  height={300}
                  type="line"
                  yAxisFormatter={(value) => `${(value * 100).toFixed(2)}%`}
                />
              </div>
            </div>
          )}
        </TabPanel>
        
        <TabPanel>
          {strategyPerformance.length > 0 ? (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-4">
                <h3 className="text-lg font-medium mb-4">{t('analytics.tables.strategyPerformance')}</h3>
              </div>
              
              <MetricsTable
                data={strategyPerformance}
                columns={[
                  {
                    id: 'strategy',
                    header: t('analytics.columns.strategy'),
                    cell: (row) => t(`recommendation.strategies.${row.strategy.toLowerCase()}`, row.strategy),
                  },
                  {
                    id: 'impressions',
                    header: t('analytics.columns.impressions'),
                    cell: (row) => row.impressions.toLocaleString(),
                  },
                  {
                    id: 'clicks',
                    header: t('analytics.columns.clicks'),
                    cell: (row) => row.clicks.toLocaleString(),
                  },
                  {
                    id: 'clickThroughRate',
                    header: t('analytics.columns.ctr'),
                    cell: (row) => `${(row.clickThroughRate * 100).toFixed(2)}%`,
                  },
                  {
                    id: 'conversions',
                    header: t('analytics.columns.conversions'),
                    cell: (row) => row.conversions.toLocaleString(),
                  },
                  {
                    id: 'conversionRate',
                    header: t('analytics.columns.cvr'),
                    cell: (row) => `${(row.conversionRate * 100).toFixed(2)}%`,
                  },
                  {
                    id: 'relevanceScore',
                    header: t('analytics.columns.relevance'),
                    cell: (row) => `${(row.relevanceScore * 100).toFixed(2)}%`,
                  },
                ]}
              />
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-500">{t('analytics.noData')}</p>
            </div>
          )}
        </TabPanel>
        
        <TabPanel>
          {categoryPerformance.length > 0 ? (
            <>
              <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                <h3 className="text-lg font-medium mb-4">{t('analytics.charts.categoryPerformance')}</h3>
                <div className="h-80">
                  <MetricsChart
                    data={categoryPerformance}
                    dataKeys={['clicks', 'conversions']}
                    xAxisDataKey="category"
                    height={300}
                    type="bar"
                  />
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-4">
                  <h3 className="text-lg font-medium mb-4">{t('analytics.tables.categoryPerformance')}</h3>
                </div>
                
                <MetricsTable
                  data={categoryPerformance}
                  columns={[
                    {
                      id: 'category',
                      header: t('analytics.columns.category'),
                      cell: (row) => row.category,
                    },
                    {
                      id: 'impressions',
                      header: t('analytics.columns.impressions'),
                      cell: (row) => row.impressions.toLocaleString(),
                    },
                    {
                      id: 'clicks',
                      header: t('analytics.columns.clicks'),
                      cell: (row) => row.clicks.toLocaleString(),
                    },
                    {
                      id: 'clickThroughRate',
                      header: t('analytics.columns.ctr'),
                      cell: (row) => `${(row.clickThroughRate * 100).toFixed(2)}%`,
                    },
                    {
                      id: 'conversions',
                      header: t('analytics.columns.conversions'),
                      cell: (row) => row.conversions.toLocaleString(),
                    },
                    {
                      id: 'conversionRate',
                      header: t('analytics.columns.cvr'),
                      cell: (row) => `${(row.conversionRate * 100).toFixed(2)}%`,
                    },
                  ]}
                />
              </div>
            </>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-500">{t('analytics.noData')}</p>
            </div>
          )}
        </TabPanel>
        
        {isAdmin && (
          <TabPanel>
            {userSegmentPerformance.length > 0 ? (
              <>
                <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                  <h3 className="text-lg font-medium mb-4">{t('analytics.charts.segmentPerformance')}</h3>
                  <div className="h-80">
                    <MetricsChart
                      data={userSegmentPerformance}
                      dataKeys={['clicksPerUser', 'conversionsPerUser']}
                      xAxisDataKey="segment"
                      height={300}
                      type="bar"
                    />
                  </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-4">
                    <h3 className="text-lg font-medium mb-4">{t('analytics.tables.segmentPerformance')}</h3>
                  </div>
                  
                  <MetricsTable
                    data={userSegmentPerformance}
                    columns={[
                      {
                        id: 'segment',
                        header: t('analytics.columns.segment'),
                        cell: (row) => t(`analytics.segments.${row.segment.toLowerCase()}`, row.segment),
                      },
                      {
                        id: 'userCount',
                        header: t('analytics.columns.users'),
                        cell: (row) => row.userCount.toLocaleString(),
                      },
                      {
                        id: 'impressionsPerUser',
                        header: t('analytics.columns.impressionsPerUser'),
                        cell: (row) => row.impressionsPerUser.toFixed(2),
                      },
                      {
                        id: 'clicksPerUser',
                        header: t('analytics.columns.clicksPerUser'),
                        cell: (row) => row.clicksPerUser.toFixed(2),
                      },
                      {
                        id: 'clickThroughRate',
                        header: t('analytics.columns.ctr'),
                        cell: (row) => `${(row.clickThroughRate * 100).toFixed(2)}%`,
                      },
                      {
                        id: 'conversionsPerUser',
                        header: t('analytics.columns.conversionsPerUser'),
                        cell: (row) => row.conversionsPerUser.toFixed(2),
                      },
                      {
                        id: 'conversionRate',
                        header: t('analytics.columns.cvr'),
                        cell: (row) => `${(row.conversionRate * 100).toFixed(2)}%`,
                      },
                    ]}
                  />
                </div>
              </>
            ) : (
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <p className="text-gray-500">{t('analytics.noData')}</p>
              </div>
            )}
          </TabPanel>
        )}
        
        <TabPanel>
          {recommendationFlow.length > 0 ? (
            <div className="bg-white rounded-lg shadow-md p-4">
              <h3 className="text-lg font-medium mb-4">{t('analytics.charts.recommendationFlow')}</h3>
              <div className="h-96">
                <SankeyDiagram
                  data={recommendationFlow}
                  height={350}
                />
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-500">{t('analytics.noData')}</p>
            </div>
          )}
        </TabPanel>
      </Tabs>
    </FadeIn>
  );
};

export default RecommendationAnalyticsDashboard;
