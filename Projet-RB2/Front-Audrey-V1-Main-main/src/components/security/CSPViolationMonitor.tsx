import React, { useState, useEffect, KeyboardEvent } from 'react';
import { configureCSPReporting } from '../../api/cspReport';

interface CSPViolation {
  id: string;
  timestamp: Date;
  directive: string;
  blockedURI: string;
  sourceFile?: string;
  lineNumber?: number;
  columnNumber?: number;
}

interface CSPReport {
  'csp-report': {
    'violated-directive'?: string;
    'effective-directive'?: string;
    'blocked-uri'?: string;
    'source-file'?: string;
    'line-number'?: number;
    'column-number'?: number;
    [key: string]: unknown;
  };
}

/**
 * Composant pour afficher les violations CSP en mode développement.
 * Ce composant ne s'affiche pas en production et est utile pour déboguer
 * les problèmes de Content Security Policy.
 */
const CSPViolationMonitor: React.FC = () => {
  const [violations, setViolations] = useState<CSPViolation[]>([]);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  useEffect(() => {
    // Ne s'exécute qu'en développement
    if (process.env.NODE_ENV !== 'development') return;

    // Type pour le handler CSP
    type CSPViolationHandler = (report: CSPReport) => void;

    // Configurer le handler CSP pour collecter les violations
    const windowWithHandler = window as Window & { __cspViolationHandler?: CSPViolationHandler };
    const originalHandleViolation = windowWithHandler.__cspViolationHandler;

    // Fonction pour générer un ID unique
    const generateId = () => Math.random().toString(36).substring(2, 9);

    // Intercepter et stocker les violations CSP
    windowWithHandler.__cspViolationHandler = (report: CSPReport) => {
      // Appeler le handler original s'il existe
      if (originalHandleViolation) {
        originalHandleViolation(report);
      }

      const cspReport = report['csp-report'] || {};

      const violation: CSPViolation = {
        id: generateId(),
        timestamp: new Date(),
        directive: cspReport['violated-directive'] || cspReport['effective-directive'] || 'unknown',
        blockedURI: cspReport['blocked-uri'] || 'unknown',
        sourceFile: cspReport['source-file'],
        lineNumber: cspReport['line-number'],
        columnNumber: cspReport['column-number'],
      };

      setViolations((prev) => [violation, ...prev].slice(0, 50)); // Limiter à 50 violations
    };

    // Configurer la journalisation CSP pour utiliser notre handler
    configureCSPReporting({
      logInDevelopment: true,
      endpoint: '/csp-report-endpoint',
    });

    // Nettoyage
    return () => {
      windowWithHandler.__cspViolationHandler = originalHandleViolation;
    };
  }, []);

  // Gestionnaire de clic
  const handleClick = () => {
    setIsVisible(!isVisible);
  };

  // Gestionnaire de touche pour l'accessibilité
  const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  // Gestionnaire de clic pour le bouton Clear
  const handleClearClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setViolations([]);
  };

  // Ne rien afficher en production ou s'il n'y a pas de violations
  if (process.env.NODE_ENV !== 'development' || violations.length === 0) {
    return null;
  }

  return (
    <div
      role='button'
      tabIndex={0}
      aria-label='Afficher les violations CSP'
      aria-expanded={isVisible}
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 9999,
        backgroundColor: 'rgba(220, 53, 69, 0.9)',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '5px',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
        cursor: 'pointer',
        fontSize: '14px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        maxWidth: isVisible ? '600px' : '200px',
        maxHeight: isVisible ? '300px' : '30px',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
      }}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
    >
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span role='img' aria-label='warning' style={{ marginRight: '8px' }}>
          ⚠️
        </span>
        <div>
          {isVisible ? (
            <>
              <h4 style={{ margin: '0 0 10px 0' }}>CSP Violations ({violations.length})</h4>
              <div style={{ maxHeight: '250px', overflowY: 'auto' }}>
                {violations.map((v) => (
                  <div
                    key={v.id}
                    style={{
                      marginBottom: '10px',
                      borderBottom: '1px solid rgba(255,255,255,0.3)',
                      paddingBottom: '8px',
                    }}
                  >
                    <div>
                      <strong>Time:</strong> {v.timestamp.toLocaleTimeString()}
                    </div>
                    <div>
                      <strong>Directive:</strong> {v.directive}
                    </div>
                    <div>
                      <strong>Blocked URI:</strong> {v.blockedURI}
                    </div>
                    {v.sourceFile && (
                      <div>
                        <strong>Source:</strong> {v.sourceFile}:{v.lineNumber}:{v.columnNumber}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </>
          ) : (
            <span>CSP Violations: {violations.length}</span>
          )}
        </div>
      </div>
      {isVisible && (
        <button
          style={{
            background: 'rgba(255,255,255,0.3)',
            border: 'none',
            color: 'white',
            borderRadius: '4px',
            padding: '4px 8px',
            marginLeft: '8px',
            cursor: 'pointer',
          }}
          onClick={handleClearClick}
          aria-label='Effacer les violations CSP'
        >
          Clear
        </button>
      )}
    </div>
  );
};

export default CSPViolationMonitor;
