import React, { useState } from 'react';
import { useSecurity } from '../../contexts/SecurityContext';

// Composant pour afficher les métriques de sécurité
const SecurityMetricsCard: React.FC = () => {
  const { metrics, refreshMetrics, isLoading } = useSecurity();
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month'>('day');

  const handleTimeframeChange = (newTimeframe: 'day' | 'week' | 'month') => {
    setTimeframe(newTimeframe);
    refreshMetrics(newTimeframe);
  };

  if (!metrics) {
    return (
      <div className='bg-white shadow rounded-lg p-4 animate-pulse'>
        <div className='h-8 bg-gray-200 rounded w-1/3 mb-4'></div>
        <div className='h-24 bg-gray-200 rounded mb-4'></div>
        <div className='h-8 bg-gray-200 rounded w-1/4'></div>
      </div>
    );
  }

  return (
    <div className='bg-white shadow rounded-lg p-4'>
      <div className='flex justify-between items-center mb-4'>
        <h2 className='text-xl font-semibold text-gray-800'>Métriques de sécurité</h2>
        <div className='flex space-x-2'>
          <button
            onClick={() => handleTimeframeChange('day')}
            className={`px-3 py-1 rounded text-sm ${
              timeframe === 'day'
                ? 'bg-green-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            disabled={isLoading}
          >
            Jour
          </button>
          <button
            onClick={() => handleTimeframeChange('week')}
            className={`px-3 py-1 rounded text-sm ${
              timeframe === 'week'
                ? 'bg-green-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            disabled={isLoading}
          >
            Semaine
          </button>
          <button
            onClick={() => handleTimeframeChange('month')}
            className={`px-3 py-1 rounded text-sm ${
              timeframe === 'month'
                ? 'bg-green-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            disabled={isLoading}
          >
            Mois
          </button>
        </div>
      </div>

      <div className='grid grid-cols-2 gap-4 mb-4'>
        <div className='bg-gray-50 p-3 rounded-lg'>
          <div className='text-sm text-gray-500'>Événements totaux</div>
          <div className='text-2xl font-bold text-gray-800'>{metrics.totalEvents}</div>
        </div>
        <div className='bg-gray-50 p-3 rounded-lg'>
          <div className='text-sm text-gray-500'>Alertes actives</div>
          <div className='text-2xl font-bold text-gray-800'>{metrics.totalAlerts}</div>
        </div>
      </div>

      <div className='mb-4'>
        <h3 className='text-sm font-medium text-gray-700 mb-2'>Événements par sévérité</h3>
        <div className='flex h-4 rounded-full overflow-hidden bg-gray-200'>
          {metrics.eventsBySeverity.critical > 0 && (
            <div
              className='bg-red-600'
              style={{
                width: `${(metrics.eventsBySeverity.critical / (metrics.totalEvents || 1)) * 100}%`,
              }}
              title={`Critique: ${metrics.eventsBySeverity.critical}`}
            ></div>
          )}
          {metrics.eventsBySeverity.error > 0 && (
            <div
              className='bg-orange-500'
              style={{
                width: `${(metrics.eventsBySeverity.error / (metrics.totalEvents || 1)) * 100}%`,
              }}
              title={`Erreur: ${metrics.eventsBySeverity.error}`}
            ></div>
          )}
          {metrics.eventsBySeverity.warning > 0 && (
            <div
              className='bg-yellow-400'
              style={{
                width: `${(metrics.eventsBySeverity.warning / (metrics.totalEvents || 1)) * 100}%`,
              }}
              title={`Avertissement: ${metrics.eventsBySeverity.warning}`}
            ></div>
          )}
          {metrics.eventsBySeverity.info > 0 && (
            <div
              className='bg-blue-400'
              style={{
                width: `${(metrics.eventsBySeverity.info / (metrics.totalEvents || 1)) * 100}%`,
              }}
              title={`Info: ${metrics.eventsBySeverity.info}`}
            ></div>
          )}
        </div>
        <div className='flex justify-between text-xs text-gray-500 mt-1'>
          <div>Info: {metrics.eventsBySeverity.info}</div>
          <div>Avertissement: {metrics.eventsBySeverity.warning}</div>
          <div>Erreur: {metrics.eventsBySeverity.error}</div>
          <div>Critique: {metrics.eventsBySeverity.critical}</div>
        </div>
      </div>

      <div>
        <h3 className='text-sm font-medium text-gray-700 mb-2'>
          Sources principales d&apos;événements
        </h3>
        <div className='space-y-2'>
          {metrics.topEventSources.map((source, index) => (
            <div key={index} className='flex items-center'>
              <div className='w-1/3 text-sm text-gray-600'>{source.source}</div>
              <div className='w-2/3'>
                <div className='relative h-2 bg-gray-200 rounded-full'>
                  <div
                    className='absolute top-0 left-0 h-2 bg-green-500 rounded-full'
                    style={{
                      width: `${(source.count / (metrics.topEventSources[0]?.count || 1)) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>
              <div className='ml-2 text-sm text-gray-600'>{source.count}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Composant pour afficher les alertes de sécurité
const SecurityAlertsCard: React.FC = () => {
  const { alerts, updateAlertStatus, refreshAlerts, isLoading } = useSecurity();

  const handleUpdateStatus = async (
    alertId: string,
    status: 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED'
  ) => {
    await updateAlertStatus(alertId, status);
  };

  const getSeverityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='bg-white shadow rounded-lg p-4'>
      <div className='flex justify-between items-center mb-4'>
        <h2 className='text-xl font-semibold text-gray-800'>Alertes de sécurité</h2>
        <button
          onClick={() => refreshAlerts()}
          className='px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm'
          disabled={isLoading}
        >
          Rafraîchir
        </button>
      </div>

      {alerts.length === 0 ? (
        <div className='text-center py-4 text-gray-500'>Aucune alerte de sécurité active</div>
      ) : (
        <div className='space-y-3'>
          {alerts.map((alert) => (
            <div key={alert.id} className='border rounded-lg p-3'>
              <div className='flex justify-between items-start'>
                <div>
                  <span
                    className={`inline-block px-2 py-1 rounded text-xs font-medium ${getSeverityColor(
                      alert.priority
                    )}`}
                  >
                    {alert.priority}
                  </span>
                  <h3 className='text-md font-medium mt-1'>{alert.type}</h3>
                  <p className='text-sm text-gray-600 mt-1'>
                    {new Date(alert.createdAt).toLocaleString()}
                  </p>
                </div>
                <div className='flex space-x-2'>
                  <button
                    onClick={() => handleUpdateStatus(alert.id, 'ACKNOWLEDGED')}
                    className='px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200'
                    disabled={isLoading}
                  >
                    Reconnaître
                  </button>
                  <button
                    onClick={() => handleUpdateStatus(alert.id, 'RESOLVED')}
                    className='px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200'
                    disabled={isLoading}
                  >
                    Résoudre
                  </button>
                </div>
              </div>
              <div className='mt-2 text-sm'>
                <pre className='whitespace-pre-wrap bg-gray-50 p-2 rounded'>
                  {JSON.stringify(alert.details, null, 2)}
                </pre>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Composant principal du tableau de bord de sécurité
const SecurityDashboard: React.FC = () => {
  const { error, clearError } = useSecurity();

  return (
    <div className='container mx-auto px-4 py-8'>
      <h1 className='text-2xl font-bold text-gray-900 mb-6'>Tableau de bord de sécurité</h1>

      {error && (
        <div className='bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6'>
          <div className='flex'>
            <div className='flex-shrink-0'>
              <svg
                className='h-5 w-5 text-red-500'
                xmlns='http://www.w3.org/2000/svg'
                viewBox='0 0 20 20'
                fill='currentColor'
              >
                <path
                  fillRule='evenodd'
                  d='M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z'
                  clipRule='evenodd'
                />
              </svg>
            </div>
            <div className='ml-3'>
              <p className='text-sm'>{error}</p>
            </div>
            <div className='ml-auto'>
              <button
                onClick={clearError}
                className='text-red-700 hover:text-red-900 focus:outline-none'
              >
                <svg
                  className='h-5 w-5'
                  xmlns='http://www.w3.org/2000/svg'
                  viewBox='0 0 20 20'
                  fill='currentColor'
                >
                  <path
                    fillRule='evenodd'
                    d='M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z'
                    clipRule='evenodd'
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <SecurityMetricsCard />
        <SecurityAlertsCard />
      </div>
    </div>
  );
};

export default SecurityDashboard;
