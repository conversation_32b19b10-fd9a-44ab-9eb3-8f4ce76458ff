import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { livestreamService } from '../../services/api/livestreamService';
import { socialAnalyticsService } from '../../services/api/socialAnalyticsService';
import LoadingSpinner from '../atoms/LoadingSpinner/LoadingSpinner';

interface LivestreamListProps {
  status?: string;
  hostId?: string;
  limit?: number;
  showCreateButton?: boolean;
  onCreateClick?: () => void;
}

const LivestreamList: React.FC<LivestreamListProps> = ({
  status,
  hostId,
  limit = 6,
  showCreateButton = false,
  onCreateClick,
}) => {
  const [livestreams, setLivestreams] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLivestreams = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const filters: Record<string, any> = { limit };
        if (status) filters.status = status;
        if (hostId) filters.hostId = hostId;
        
        const data = await livestreamService.getLivestreams(filters);
        setLivestreams(data);
      } catch (error) {
        console.error('Error fetching livestreams:', error);
        setError('Une erreur est survenue lors du chargement des livestreams.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLivestreams();
  }, [status, hostId, limit]);

  const handleLivestreamClick = (livestreamId: string) => {
    // Enregistrer l'événement de vue
    socialAnalyticsService.trackView(livestreamId, 'livestream');
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'Programmé';
      case 'live':
        return 'En direct';
      case 'ended':
        return 'Terminé';
      case 'cancelled':
        return 'Annulé';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'live':
        return 'bg-red-100 text-red-800';
      case 'ended':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'PPP à HH:mm', { locale: fr });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-600 p-4">
        <p>{error}</p>
        <button
          className="mt-2 text-retreat-green hover:text-retreat-green-dark"
          onClick={() => window.location.reload()}
        >
          Réessayer
        </button>
      </div>
    );
  }

  if (livestreams.length === 0) {
    return (
      <div className="text-center p-6">
        <p className="text-gray-500">Aucun livestream disponible.</p>
        {showCreateButton && onCreateClick && (
          <button
            onClick={onCreateClick}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Créer un livestream
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          {status === 'live' ? 'En direct maintenant' : 
           status === 'scheduled' ? 'Livestreams à venir' : 
           'Livestreams'}
        </h2>
        {showCreateButton && onCreateClick && (
          <button
            onClick={onCreateClick}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-retreat-green hover:bg-retreat-green-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Créer un livestream
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {livestreams.map((livestream) => (
          <Link
            key={livestream.id}
            to={`/livestream/${livestream.id}`}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            onClick={() => handleLivestreamClick(livestream.id)}
          >
            <div className="relative">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                {livestream.thumbnailUrl ? (
                  <img
                    src={livestream.thumbnailUrl}
                    alt={livestream.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <svg className="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                )}
              </div>
              <div className="absolute top-2 right-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(livestream.status)}`}>
                  {getStatusLabel(livestream.status)}
                </span>
              </div>
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-medium text-gray-900 truncate">{livestream.title}</h3>
              
              <div className="mt-2 flex items-center text-sm text-gray-500">
                <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>{livestream.hostName}</span>
              </div>
              
              <div className="mt-1 flex items-center text-sm text-gray-500">
                <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>{formatDate(livestream.scheduledStartTime || livestream.createdAt)}</span>
              </div>
              
              {livestream.viewerCount !== undefined && (
                <div className="mt-1 flex items-center text-sm text-gray-500">
                  <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <span>{livestream.viewerCount} spectateurs</span>
                </div>
              )}
            </div>
          </Link>
        ))}
      </div>
      
      {livestreams.length > 0 && (
        <div className="text-center mt-6">
          <Link
            to={`/livestreams${status ? `?status=${status}` : ''}`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-retreat-green"
          >
            Voir tous les livestreams
            <svg className="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      )}
    </div>
  );
};

export default LivestreamList;
