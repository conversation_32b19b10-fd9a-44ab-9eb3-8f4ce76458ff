import { MapPinIcon } from '@heroicons/react/24/outline';
import { CalendarIcon } from '@heroicons/react/24/outline';
import { UserGroupIcon } from '@heroicons/react/24/outline';

const Hero = () => {
  return (
    <section className='pt-24 pb-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-7xl mx-auto'>
        <h2 className='text-4xl font-bold text-center mb-8'>
          Trouvez votre retraite bien-être idéale
        </h2>
        <div className='bg-white rounded-full shadow-lg p-4 flex items-center space-x-4'>
          <div className='flex-1 flex items-center space-x-2 border-r px-4'>
            <MapPinIcon className='h-6 w-6 text-gray-400' />
            <input
              type='text'
              placeholder='Où souhaitez-vous partir ?'
              className='w-full outline-none'
            />
          </div>
          <div className='flex-1 flex items-center space-x-2 border-r px-4'>
            <CalendarIcon className='h-6 w-6 text-gray-400' />
            <input type='text' placeholder='Quand partez-vous ?' className='w-full outline-none' />
          </div>
          <div className='flex-1 flex items-center space-x-2 px-4'>
            <UserGroupIcon className='h-6 w-6 text-gray-400' />
            <input
              type='text'
              placeholder='Combien de personnes ?'
              className='w-full outline-none'
            />
          </div>
          <button className='bg-primary text-white px-6 py-3 rounded-full hover:bg-opacity-90'>
            Rechercher
          </button>
        </div>
      </div>
    </section>
  );
};
export default Hero;
