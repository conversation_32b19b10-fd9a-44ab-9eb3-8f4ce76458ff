import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  ExclamationCircleIcon,
  BellIcon,
} from '@heroicons/react/24/outline';

export type NotificationType = 'success' | 'error' | 'info' | 'warning' | 'default';

interface NotificationProps {
  type: NotificationType;
  message: string;
  onClose: () => void;
  duration?: number;
  title?: string;
}

const Notification: React.FC<NotificationProps> = ({
  type,
  message,
  onClose,
  duration = 5000,
  title,
}) => {
  React.useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className='w-6 h-6 text-green-500' />;
      case 'error':
        return <XCircleIcon className='w-6 h-6 text-red-500' />;
      case 'info':
        return <InformationCircleIcon className='w-6 h-6 text-blue-500' />;
      case 'warning':
        return <ExclamationCircleIcon className='w-6 h-6 text-yellow-500' />;
      default:
        return <BellIcon className='w-6 h-6 text-gray-500' />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getTextColor = () => {
    switch (type) {
      case 'success':
        return 'text-green-800';
      case 'error':
        return 'text-red-800';
      case 'info':
        return 'text-blue-800';
      case 'warning':
        return 'text-yellow-800';
      default:
        return 'text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`fixed top-4 right-4 p-4 rounded-lg shadow-lg border ${getBackgroundColor()} max-w-md`}
    >
      <div className='flex items-start gap-3'>
        <div className='flex-shrink-0'>{getIcon()}</div>
        <div className='flex-1'>
          {title && <h3 className={`text-sm font-medium ${getTextColor()} mb-1`}>{title}</h3>}
          <p className={`text-sm ${getTextColor()}`}>{message}</p>
        </div>
        <button
          onClick={onClose}
          className='flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors'
        >
          <XCircleIcon className='w-5 h-5' />
        </button>
      </div>
    </motion.div>
  );
};

export default Notification;
