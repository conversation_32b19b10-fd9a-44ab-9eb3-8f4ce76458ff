/**
 * Composant Card Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Composant de carte standardisé pour l'affichage de contenu
 * avec différentes variantes et états.
 */

import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../../utils/cn';

const cardVariants = cva(
  'rounded-lg border bg-white text-neutral-950 shadow-soft transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border-neutral-200',
        outlined: 'border-neutral-300 shadow-none',
        elevated: 'border-neutral-200 shadow-medium',
        interactive: 'border-neutral-200 hover:shadow-medium hover:border-neutral-300 cursor-pointer',
        success: 'border-success-200 bg-success-50',
        warning: 'border-warning-200 bg-warning-50',
        error: 'border-error-200 bg-error-50',
        info: 'border-info-200 bg-info-50',
      },
      size: {
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  children: React.ReactNode;
}

export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant, size }), className)}
      {...props}
    >
      {children}
    </div>
  )
);

Card.displayName = 'Card';

// Composants de structure de carte
export const CardHeader = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 pb-4', className)}
    {...props}
  />
));

CardHeader.displayName = 'CardHeader';

export const CardTitle = forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn('text-lg font-semibold leading-none tracking-tight', className)}
    {...props}
  >
    {children}
  </h3>
));

CardTitle.displayName = 'CardTitle';

export const CardDescription = forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-sm text-neutral-600', className)}
    {...props}
  />
));

CardDescription.displayName = 'CardDescription';

export const CardContent = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('pt-0', className)} {...props} />
));

CardContent.displayName = 'CardContent';

export const CardFooter = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center pt-4', className)}
    {...props}
  />
));

CardFooter.displayName = 'CardFooter';

// Cartes spécialisées pour Retreat And Be
export interface RetreatCardProps {
  retreat: {
    id: string;
    title: string;
    description: string;
    image: string;
    price: number;
    duration: string;
    location: string;
    rating: number;
    category: string;
  };
  onBook?: (id: string) => void;
  onFavorite?: (id: string) => void;
  isFavorite?: boolean;
}

export const RetreatCard: React.FC<RetreatCardProps> = ({
  retreat,
  onBook,
  onFavorite,
  isFavorite = false,
}) => {
  return (
    <Card variant="interactive" className="overflow-hidden">
      <div className="relative">
        <img
          src={retreat.image}
          alt={retreat.title}
          className="h-48 w-full object-cover"
        />
        <button
          onClick={() => onFavorite?.(retreat.id)}
          className="absolute top-2 right-2 rounded-full bg-white/80 p-2 backdrop-blur-sm transition-colors hover:bg-white"
        >
          <span className={isFavorite ? 'text-error-500' : 'text-neutral-400'}>
            {isFavorite ? '❤️' : '🤍'}
          </span>
        </button>
        <div className="absolute top-2 left-2">
          <span className="rounded-full bg-primary-100 px-2 py-1 text-xs font-medium text-primary-800">
            {retreat.category}
          </span>
        </div>
      </div>
      
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle className="line-clamp-2">{retreat.title}</CardTitle>
          <div className="flex items-center space-x-1 text-sm text-warning-600">
            <span>⭐</span>
            <span>{retreat.rating}</span>
          </div>
        </div>
        <CardDescription className="line-clamp-2">
          {retreat.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2 text-sm text-neutral-600">
          <div className="flex items-center space-x-2">
            <span>📍</span>
            <span>{retreat.location}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>⏱️</span>
            <span>{retreat.duration}</span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="justify-between">
        <div>
          <span className="text-2xl font-bold text-primary-600">
            {retreat.price}€
          </span>
          <span className="text-sm text-neutral-500 ml-1">/ personne</span>
        </div>
        <button
          onClick={() => onBook?.(retreat.id)}
          className="rounded-md bg-primary-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary-700"
        >
          Réserver
        </button>
      </CardFooter>
    </Card>
  );
};

// Carte de professionnel
export interface ProfessionalCardProps {
  professional: {
    id: string;
    name: string;
    title: string;
    avatar: string;
    specialties: string[];
    rating: number;
    reviewCount: number;
    price: number;
    available: boolean;
  };
  onContact?: (id: string) => void;
  onViewProfile?: (id: string) => void;
}

export const ProfessionalCard: React.FC<ProfessionalCardProps> = ({
  professional,
  onContact,
  onViewProfile,
}) => {
  return (
    <Card variant="interactive">
      <CardHeader>
        <div className="flex items-start space-x-4">
          <img
            src={professional.avatar}
            alt={professional.name}
            className="h-16 w-16 rounded-full object-cover"
          />
          <div className="flex-1">
            <CardTitle className="text-base">{professional.name}</CardTitle>
            <CardDescription>{professional.title}</CardDescription>
            <div className="mt-2 flex items-center space-x-2">
              <div className="flex items-center space-x-1 text-sm text-warning-600">
                <span>⭐</span>
                <span>{professional.rating}</span>
                <span className="text-neutral-500">({professional.reviewCount})</span>
              </div>
              <div className={cn(
                'h-2 w-2 rounded-full',
                professional.available ? 'bg-success-500' : 'bg-neutral-400'
              )} />
              <span className="text-xs text-neutral-500">
                {professional.available ? 'Disponible' : 'Occupé'}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          <div>
            <h4 className="text-sm font-medium text-neutral-700 mb-2">Spécialités</h4>
            <div className="flex flex-wrap gap-1">
              {professional.specialties.map((specialty, index) => (
                <span
                  key={index}
                  className="rounded-full bg-neutral-100 px-2 py-1 text-xs text-neutral-700"
                >
                  {specialty}
                </span>
              ))}
            </div>
          </div>
          <div className="text-lg font-semibold text-primary-600">
            À partir de {professional.price}€/session
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="space-x-2">
        <button
          onClick={() => onViewProfile?.(professional.id)}
          className="flex-1 rounded-md border border-neutral-300 px-3 py-2 text-sm font-medium text-neutral-700 transition-colors hover:bg-neutral-50"
        >
          Voir le profil
        </button>
        <button
          onClick={() => onContact?.(professional.id)}
          className="flex-1 rounded-md bg-primary-600 px-3 py-2 text-sm font-medium text-white transition-colors hover:bg-primary-700"
          disabled={!professional.available}
        >
          Contacter
        </button>
      </CardFooter>
    </Card>
  );
};

// Carte de statistiques
export interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ReactNode;
  description?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  icon,
  description,
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-neutral-600">{title}</p>
            <p className="text-3xl font-bold text-neutral-900">{value}</p>
            {change && (
              <div className="flex items-center space-x-1 mt-1">
                <span className={cn(
                  'text-sm font-medium',
                  change.type === 'increase' ? 'text-success-600' : 'text-error-600'
                )}>
                  {change.type === 'increase' ? '↗' : '↘'} {Math.abs(change.value)}%
                </span>
                {description && (
                  <span className="text-sm text-neutral-500">{description}</span>
                )}
              </div>
            )}
          </div>
          {icon && (
            <div className="text-3xl text-primary-600">
              {icon}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Exemples d'utilisation
export const CardExamples = () => {
  const sampleRetreat = {
    id: '1',
    title: 'Retraite Yoga & Méditation en Provence',
    description: 'Une expérience transformatrice au cœur de la nature provençale.',
    image: '/api/placeholder/400/300',
    price: 450,
    duration: '3 jours',
    location: 'Provence, France',
    rating: 4.8,
    category: 'Yoga',
  };

  const sampleProfessional = {
    id: '1',
    name: 'Marie Dubois',
    title: 'Professeure de Yoga certifiée',
    avatar: '/api/placeholder/100/100',
    specialties: ['Hatha Yoga', 'Méditation', 'Pranayama'],
    rating: 4.9,
    reviewCount: 127,
    price: 75,
    available: true,
  };

  return (
    <div className="space-y-8 p-6">
      <div>
        <h3 className="mb-4 text-lg font-semibold">Variantes de cartes</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card variant="default">
            <CardHeader>
              <CardTitle>Carte par défaut</CardTitle>
              <CardDescription>Description de la carte</CardDescription>
            </CardHeader>
            <CardContent>Contenu de la carte</CardContent>
          </Card>
          
          <Card variant="elevated">
            <CardHeader>
              <CardTitle>Carte élevée</CardTitle>
              <CardDescription>Avec ombre plus prononcée</CardDescription>
            </CardHeader>
            <CardContent>Contenu de la carte</CardContent>
          </Card>
          
          <Card variant="interactive">
            <CardHeader>
              <CardTitle>Carte interactive</CardTitle>
              <CardDescription>Avec effet hover</CardDescription>
            </CardHeader>
            <CardContent>Contenu de la carte</CardContent>
          </Card>
        </div>
      </div>

      <div>
        <h3 className="mb-4 text-lg font-semibold">Carte de retraite</h3>
        <div className="max-w-sm">
          <RetreatCard retreat={sampleRetreat} />
        </div>
      </div>

      <div>
        <h3 className="mb-4 text-lg font-semibold">Carte de professionnel</h3>
        <div className="max-w-sm">
          <ProfessionalCard professional={sampleProfessional} />
        </div>
      </div>

      <div>
        <h3 className="mb-4 text-lg font-semibold">Cartes de statistiques</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatsCard
            title="Utilisateurs actifs"
            value="2,847"
            change={{ value: 12, type: 'increase' }}
            icon="👥"
            description="ce mois"
          />
          <StatsCard
            title="Réservations"
            value="1,234"
            change={{ value: 8, type: 'increase' }}
            icon="📅"
            description="cette semaine"
          />
          <StatsCard
            title="Revenus"
            value="€45,678"
            change={{ value: 3, type: 'decrease' }}
            icon="💰"
            description="ce mois"
          />
        </div>
      </div>
    </div>
  );
};
