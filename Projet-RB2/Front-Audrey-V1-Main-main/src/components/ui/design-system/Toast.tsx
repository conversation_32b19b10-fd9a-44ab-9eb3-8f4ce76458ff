/**
 * Composant Toast/Notification Unifié - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Système de notifications toast avec animations
 * et gestion automatique des états.
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../../utils/cn';

const toastVariants = cva(
  'relative flex items-start space-x-3 rounded-lg border p-4 shadow-medium backdrop-blur-sm transition-all duration-300 ease-in-out',
  {
    variants: {
      variant: {
        default: 'bg-white border-neutral-200 text-neutral-900',
        success: 'bg-success-50 border-success-200 text-success-900',
        error: 'bg-error-50 border-error-200 text-error-900',
        warning: 'bg-warning-50 border-warning-200 text-warning-900',
        info: 'bg-info-50 border-info-200 text-info-900',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export interface Toast {
  id: string;
  title?: string;
  message: string;
  variant?: VariantProps<typeof toastVariants>['variant'];
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  onClose?: () => void;
}

interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => string;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Provider pour les toasts
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = useCallback((toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: Toast = {
      ...toast,
      id,
      duration: toast.duration ?? 5000,
    };

    setToasts(prev => [...prev, newToast]);

    // Auto-remove après la durée spécifiée
    if (newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }

    return id;
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearToasts }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
};

// Hook pour utiliser les toasts
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }

  const { addToast, removeToast, clearToasts } = context;

  // Méthodes de convenance
  const toast = {
    success: (message: string, options?: Partial<Toast>) =>
      addToast({ ...options, message, variant: 'success' }),
    error: (message: string, options?: Partial<Toast>) =>
      addToast({ ...options, message, variant: 'error' }),
    warning: (message: string, options?: Partial<Toast>) =>
      addToast({ ...options, message, variant: 'warning' }),
    info: (message: string, options?: Partial<Toast>) =>
      addToast({ ...options, message, variant: 'info' }),
    default: (message: string, options?: Partial<Toast>) =>
      addToast({ ...options, message, variant: 'default' }),
  };

  return {
    toast,
    removeToast,
    clearToasts,
  };
};

// Composant Toast individuel
const ToastComponent: React.FC<{ toast: Toast; onRemove: (id: string) => void }> = ({
  toast,
  onRemove,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  React.useEffect(() => {
    // Animation d'entrée
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onRemove(toast.id);
      toast.onClose?.();
    }, 300);
  };

  const getIcon = () => {
    switch (toast.variant) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  return (
    <div
      className={cn(
        toastVariants({ variant: toast.variant }),
        'transform transition-all duration-300 ease-in-out',
        isVisible && !isLeaving
          ? 'translate-x-0 opacity-100 scale-100'
          : 'translate-x-full opacity-0 scale-95'
      )}
    >
      <div className="flex-shrink-0 text-lg">
        {getIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        {toast.title && (
          <h4 className="text-sm font-medium mb-1">{toast.title}</h4>
        )}
        <p className="text-sm">{toast.message}</p>
        
        {toast.action && (
          <button
            onClick={toast.action.onClick}
            className="mt-2 text-sm font-medium underline hover:no-underline"
          >
            {toast.action.label}
          </button>
        )}
      </div>
      
      <button
        onClick={handleClose}
        className="flex-shrink-0 text-neutral-400 hover:text-neutral-600 transition-colors"
      >
        <span className="text-lg">✕</span>
      </button>
    </div>
  );
};

// Container pour afficher les toasts
const ToastContainer: React.FC = () => {
  const { toasts, removeToast } = useToast();

  if (toasts.length === 0) return null;

  const container = (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {toasts.map(toast => (
        <ToastComponent
          key={toast.id}
          toast={toast}
          onRemove={removeToast}
        />
      ))}
    </div>
  );

  return createPortal(container, document.body);
};

// Composant Alert statique (pour les alertes dans la page)
export interface AlertProps extends VariantProps<typeof toastVariants> {
  title?: string;
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
}

export const Alert: React.FC<AlertProps> = ({
  variant = 'default',
  title,
  children,
  onClose,
  className,
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  return (
    <div className={cn(toastVariants({ variant }), className)}>
      <div className="flex-shrink-0 text-lg">
        {getIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        {title && (
          <h4 className="text-sm font-medium mb-1">{title}</h4>
        )}
        <div className="text-sm">{children}</div>
      </div>
      
      {onClose && (
        <button
          onClick={onClose}
          className="flex-shrink-0 text-neutral-400 hover:text-neutral-600 transition-colors"
        >
          <span className="text-lg">✕</span>
        </button>
      )}
    </div>
  );
};

// Exemples d'utilisation
export const ToastExamples = () => {
  const { toast } = useToast();

  const showSuccessToast = () => {
    toast.success('Réservation confirmée !', {
      title: 'Succès',
      action: {
        label: 'Voir les détails',
        onClick: () => console.log('Voir les détails'),
      },
    });
  };

  const showErrorToast = () => {
    toast.error('Erreur lors de la réservation', {
      title: 'Erreur',
      duration: 0, // Ne se ferme pas automatiquement
    });
  };

  const showWarningToast = () => {
    toast.warning('Votre session expire dans 5 minutes', {
      title: 'Attention',
      action: {
        label: 'Prolonger',
        onClick: () => console.log('Session prolongée'),
      },
    });
  };

  const showInfoToast = () => {
    toast.info('Nouvelle fonctionnalité disponible !', {
      title: 'Information',
    });
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h3 className="mb-4 text-lg font-semibold">Exemples de toasts</h3>
        <div className="space-x-2">
          <button
            onClick={showSuccessToast}
            className="px-4 py-2 bg-success-600 text-white rounded-md hover:bg-success-700"
          >
            Toast de succès
          </button>
          <button
            onClick={showErrorToast}
            className="px-4 py-2 bg-error-600 text-white rounded-md hover:bg-error-700"
          >
            Toast d'erreur
          </button>
          <button
            onClick={showWarningToast}
            className="px-4 py-2 bg-warning-600 text-white rounded-md hover:bg-warning-700"
          >
            Toast d'avertissement
          </button>
          <button
            onClick={showInfoToast}
            className="px-4 py-2 bg-info-600 text-white rounded-md hover:bg-info-700"
          >
            Toast d'information
          </button>
        </div>
      </div>

      <div>
        <h3 className="mb-4 text-lg font-semibold">Alertes statiques</h3>
        <div className="space-y-4">
          <Alert variant="success" title="Succès">
            Votre profil a été mis à jour avec succès.
          </Alert>
          
          <Alert variant="error" title="Erreur">
            Une erreur s'est produite lors de l'enregistrement.
            <br />
            Veuillez réessayer plus tard.
          </Alert>
          
          <Alert variant="warning" title="Attention">
            Votre abonnement expire dans 3 jours.
          </Alert>
          
          <Alert variant="info" title="Information">
            Une nouvelle version de l'application est disponible.
          </Alert>
        </div>
      </div>
    </div>
  );
};
