import React, { createContext, useState, useEffect, useCallback, ReactNode } from 'react';
import {
  securityService,
  SecurityMetrics,
  SecurityAlert,
  SecurityNotification,
} from '../services/api/securityService';

interface SecurityContextType {
  isLoading: boolean;
  error: string | null;
  metrics: SecurityMetrics | null;
  alerts: SecurityAlert[];
  notifications: SecurityNotification[];
  unreadNotificationsCount: number;
  refreshMetrics: (timeframe?: 'day' | 'week' | 'month') => Promise<void>;
  refreshAlerts: () => Promise<void>;
  updateAlertStatus: (
    alertId: string,
    status: 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED',
    notes?: string
  ) => Promise<void>;
  getSecurityNotifications: (options?: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
    severity?: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  }) => Promise<{
    notifications: SecurityNotification[];
    total: number;
    unreadCount: number;
  }>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
  validateFile: (file: File) => Promise<{ valid: boolean; reason?: string }>;
  encryptData: (data: string, purpose: string) => Promise<string>;
  decryptData: (encryptedData: string, purpose: string) => Promise<string>;
  clearError: () => void;
}

export const SecurityContext = createContext<SecurityContextType>({
  isLoading: false,
  error: null,
  metrics: null,
  alerts: [],
  notifications: [],
  unreadNotificationsCount: 0,
  refreshMetrics: async () => {},
  refreshAlerts: async () => {},
  updateAlertStatus: async () => {},
  getSecurityNotifications: async () => ({ notifications: [], total: 0, unreadCount: 0 }),
  markNotificationAsRead: async () => {},
  markAllNotificationsAsRead: async () => {},
  validateFile: async () => ({ valid: false }),
  encryptData: async () => '',
  decryptData: async () => '',
  clearError: () => {},
});

// Hook pour utiliser le contexte de sécurité
export const useSecurity = () => {
  const context = React.useContext(SecurityContext);
  if (context === undefined) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

interface SecurityProviderProps {
  children: ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [notifications, setNotifications] = useState<SecurityNotification[]>([]);
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState<number>(0);

  // Charger les métriques de sécurité
  const refreshMetrics = useCallback(async (timeframe: 'day' | 'week' | 'month' = 'day') => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await securityService.getSecurityDashboard(timeframe);
      setMetrics(data);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(
          err.message || 'Une erreur est survenue lors du chargement des métriques de sécurité'
        );
      } else {
        setError('Une erreur inconnue est survenue lors du chargement des métriques de sécurité');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Charger les alertes de sécurité
  const refreshAlerts = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await securityService.getSecurityAlerts({ status: 'OPEN' });
      setAlerts(data);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(
          err.message || 'Une erreur est survenue lors du chargement des alertes de sécurité'
        );
      } else {
        setError('Une erreur inconnue est survenue lors du chargement des alertes de sécurité');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Mettre à jour le statut d'une alerte
  const updateAlertStatus = useCallback(
    async (alertId: string, status: 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED', notes?: string) => {
      setIsLoading(true);
      setError(null);
      try {
        await securityService.updateAlertStatus(alertId, status, notes);
        // Rafraîchir la liste des alertes après la mise à jour
        await refreshAlerts();
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(
            err.message || "Une erreur est survenue lors de la mise à jour du statut de l'alerte"
          );
        } else {
          setError("Une erreur inconnue est survenue lors de la mise à jour du statut de l'alerte");
        }
      } finally {
        setIsLoading(false);
      }
    },
    [refreshAlerts]
  );

  // Valider un fichier
  const validateFile = useCallback(async (file: File) => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await securityService.validateFile(file);
      return result;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors de la validation du fichier');
      } else {
        setError('Une erreur inconnue est survenue lors de la validation du fichier');
      }
      return { valid: false, reason: 'Erreur de validation' };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Chiffrer des données
  const encryptData = useCallback(async (data: string, purpose: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await securityService.encryptData(data, purpose);
      return result.encryptedData;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors du chiffrement des données');
      } else {
        setError('Une erreur inconnue est survenue lors du chiffrement des données');
      }
      return '';
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Déchiffrer des données
  const decryptData = useCallback(async (encryptedData: string, purpose: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await securityService.decryptData(encryptedData, purpose);
      return result.data;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Une erreur est survenue lors du déchiffrement des données');
      } else {
        setError('Une erreur inconnue est survenue lors du déchiffrement des données');
      }
      return '';
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Récupérer les notifications de sécurité
  const getSecurityNotifications = useCallback(async (options = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await securityService.getSecurityNotifications(options);
      setNotifications(data.notifications);
      setUnreadNotificationsCount(data.unreadCount);
      return data;
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(
          err.message || 'Une erreur est survenue lors du chargement des notifications de sécurité'
        );
      } else {
        setError(
          'Une erreur inconnue est survenue lors du chargement des notifications de sécurité'
        );
      }
      return { notifications: [], total: 0, unreadCount: 0 };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Marquer une notification comme lue
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await securityService.markNotificationAsRead(notificationId);

      // Mettre à jour l'état local
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId ? { ...notification, read: true } : notification
        )
      );

      // Décrémenter le compteur de notifications non lues
      setUnreadNotificationsCount((prev) => Math.max(0, prev - 1));
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(
          err.message || 'Une erreur est survenue lors du marquage de la notification comme lue'
        );
      } else {
        setError('Une erreur inconnue est survenue lors du marquage de la notification comme lue');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Marquer toutes les notifications comme lues
  const markAllNotificationsAsRead = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      await securityService.markAllNotificationsAsRead();

      // Mettre à jour l'état local
      setNotifications((prev) => prev.map((notification) => ({ ...notification, read: true })));

      // Réinitialiser le compteur de notifications non lues
      setUnreadNotificationsCount(0);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(
          err.message ||
            'Une erreur est survenue lors du marquage de toutes les notifications comme lues'
        );
      } else {
        setError(
          'Une erreur inconnue est survenue lors du marquage de toutes les notifications comme lues'
        );
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effacer l'erreur
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Charger les données initiales
  useEffect(() => {
    refreshMetrics();
    refreshAlerts();
    getSecurityNotifications();
  }, [refreshMetrics, refreshAlerts, getSecurityNotifications]);

  const value = {
    isLoading,
    error,
    metrics,
    alerts,
    notifications,
    unreadNotificationsCount,
    refreshMetrics,
    refreshAlerts,
    updateAlertStatus,
    getSecurityNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    validateFile,
    encryptData,
    decryptData,
    clearError,
  };

  return <SecurityContext.Provider value={value}>{children}</SecurityContext.Provider>;
};
