import { useState, useEffect, useCallback } from 'react';
import { syncService, SyncEntityType } from '../services/syncService';
import { useNotification } from './useNotification';
import { useErrorHandler } from './useErrorHandler';

interface UseSyncReturn {
  syncEntity: (entityType: SyncEntityType, forceRefresh?: boolean) => Promise<void>;
  syncAll: (forceRefresh?: boolean) => Promise<void>;
  lastSync: Record<SyncEntityType, Date | null>;
  isSyncing: boolean;
  progress: number;
  error: any | null;
}

/**
 * Hook pour utiliser le service de synchronisation
 * @returns Méthodes et état pour la synchronisation des données
 */
export const useSync = (): UseSyncReturn => {
  const [lastSync, setLastSync] = useState<Record<SyncEntityType, Date | null>>(
    syncService.getStatus().lastSync
  );
  const [isSyncing, setIsSyncing] = useState<boolean>(syncService.getStatus().inProgress);
  const [progress, setProgress] = useState<number>(syncService.getStatus().progress);
  const [error, setError] = useState<any | null>(syncService.getStatus().error);

  const notification = useNotification();
  const errorHandler = useErrorHandler();

  // Mettre à jour l'état local lorsque le statut de synchronisation change
  useEffect(() => {
    const unsubscribe = syncService.addListener((status) => {
      setLastSync(status.lastSync);
      setIsSyncing(status.inProgress);
      setProgress(status.progress);
      setError(status.error);
    });

    return unsubscribe;
  }, []);

  /**
   * Synchroniser une entité spécifique
   * @param entityType Type d'entité à synchroniser
   * @param forceRefresh Forcer le rafraîchissement des données
   */
  const syncEntity = useCallback(
    async (entityType: SyncEntityType, forceRefresh: boolean = false): Promise<void> => {
      try {
        await syncService.syncEntity(entityType, {
          forceRefresh,
          onProgress: (progress) => {
            // La progression est déjà gérée par le listener
          },
          onComplete: () => {
            notification.success(`Synchronisation de ${getEntityName(entityType)} terminée`);
          },
          onError: (error) => {
            notification.error(`Erreur lors de la synchronisation de ${getEntityName(entityType)}`);
            errorHandler.handleError(error);
          },
        });
      } catch (error) {
        // Les erreurs sont déjà gérées par les callbacks
      }
    },
    [notification, errorHandler]
  );

  /**
   * Synchroniser toutes les entités
   * @param forceRefresh Forcer le rafraîchissement des données
   */
  const syncAll = useCallback(
    async (forceRefresh: boolean = false): Promise<void> => {
      try {
        await syncService.syncAll({
          forceRefresh,
          onProgress: (progress) => {
            // La progression est déjà gérée par le listener
          },
          onComplete: () => {
            notification.success('Synchronisation terminée');
          },
          onError: (error) => {
            notification.error('Erreur lors de la synchronisation');
            errorHandler.handleError(error);
          },
        });
      } catch (error) {
        // Les erreurs sont déjà gérées par les callbacks
      }
    },
    [notification, errorHandler]
  );

  return {
    syncEntity,
    syncAll,
    lastSync,
    isSyncing,
    progress,
    error,
  };
};

/**
 * Obtenir le nom d'une entité en français
 * @param entityType Type d'entité
 * @returns Nom de l'entité en français
 */
function getEntityName(entityType: SyncEntityType): string {
  switch (entityType) {
    case SyncEntityType.USER:
      return 'utilisateurs';
    case SyncEntityType.RETREAT:
      return 'retraites';
    case SyncEntityType.BOOKING:
      return 'réservations';
    case SyncEntityType.PROFILE:
      return 'profils';
    case SyncEntityType.REVIEW:
      return 'avis';
    default:
      return entityType;
  }
}
