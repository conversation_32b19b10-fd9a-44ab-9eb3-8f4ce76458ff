import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { AnalyticsDashboard } from '../components/analytics';
import { useAuthContext } from '../hooks/useAuthContext';
import { useTranslation } from '../hooks/useTranslation';

const AnalyticsDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const { creatorId } = useParams<{ creatorId: string }>();
  const [hasAccess, setHasAccess] = useState<boolean>(false);

  useEffect(() => {
    // Check if user has access to this creator's analytics
    if (user) {
      // User can access their own analytics
      if (user.id === creatorId) {
        setHasAccess(true);
        return;
      }
      
      // <PERSON><PERSON> can access any creator's analytics
      if (user.roles?.includes('admin')) {
        setHasAccess(true);
        return;
      }
      
      // Check if user has been granted access to this creator's analytics
      // This would typically involve checking permissions in the user object
      // or making an API call to check permissions
      const hasPermission = user.permissions?.includes(`analytics:${creatorId}`);
      setHasAccess(!!hasPermission);
    }
  }, [user, creatorId]);

  // Redirect or show access denied if user doesn't have appropriate access
  if (!hasAccess) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <h2 className="text-lg font-medium">{t('common.accessDenied')}</h2>
          <p>{t('analytics.accessDeniedMessage')}</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <AnalyticsDashboard creatorId={creatorId || user?.id || ''} />
    </div>
  );
};

export default AnalyticsDashboardPage;
