import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import SearchBar from '../components/HomePage/SearchBar';
import { UserCircleIcon } from '@heroicons/react/24/outline';
import LazyImage from '../components/ui/LazyImage';
import ScrollToTop from '../components/ui/ScrollToTop';
import ChatBot from '../components/atoms/ChatBot';
import useIsMobile from '../hooks/useIsMobile';
import MultiCriteriaRecommendationSection from '../components/multicriteria/MultiCriteriaRecommendationSection';
import FooterAccueil from '../components/ui/Footer-accueil';

const HomePage: React.FC = () => {
  const isMobile = useIsMobile();

  const fadeInUp = {
    initial: { opacity: 0, y: isMobile ? 0 : -20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: isMobile ? 0.2 : 0.5 },
  };

  const fadeInDown = {
    initial: { opacity: 0, y: isMobile ? 0 : 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: isMobile ? 0.2 : 0.5, delay: isMobile ? 0 : 0.2 },
  };

  const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: isMobile ? 0.2 : 0.5, delay: isMobile ? 0 : 0.4 },
  };

  return (
    <>
      <Helmet>
        <title>Retreat And Be - Trouvez votre retraite bien-être idéale</title>
        <meta
          name='description'
          content='Découvrez et réservez des retraites bien-être personnalisées. Trouvez votre séjour de relaxation et de développement personnel idéal.'
        />
        <meta
          name='keywords'
          content='retraite bien-être, yoga, méditation, développement personnel, relaxation, spa, wellness'
        />
        <meta
          property='og:title'
          content='Retreat And Be - Votre plateforme de retraites bien-être'
        />
        <meta
          property='og:description'
          content='Découvrez et réservez des retraites bien-être personnalisées. Trouvez votre séjour de relaxation et de développement personnel idéal.'
        />
        <meta property='og:image' content='/images/background-wellness.jpg' />
        <meta property='og:url' content='https://retreat-and-be.com' />
        <meta name='twitter:card' content='summary_large_image' />
        <meta
          name='twitter:title'
          content='Retreat And Be - Votre plateforme de retraites bien-être'
        />
        <meta
          name='twitter:description'
          content='Découvrez et réservez des retraites bien-être personnalisées. Trouvez votre séjour de relaxation et de développement personnel idéal.'
        />
        <meta name='twitter:image' content='/images/background-wellness.jpg' />
        <link rel='canonical' href='https://retreat-and-be.com' />
        <meta name='viewport' content='width=device-width, initial-scale=1.0' />
        <meta name='theme-color' content='#38C283' />
        <meta name='robots' content='index, follow' />
        <meta name='language' content='fr' />
      </Helmet>

      <div
        className='min-h-screen flex flex-col items-center justify-between bg-mint-50 relative'
        role='main'
        aria-label="Page d'accueil"
      >
        {/* Background Image */}
        <div className='absolute inset-0 -z-10' aria-hidden='true'>
          <LazyImage
            src='/images/background-wellness.jpg'
            alt=''
            className='w-full h-full opacity-10'
            role='presentation'
          />
        </div>

        <div className='flex-1 flex flex-col items-center justify-center w-full'>
          {/* Logo */}
          <motion.div {...fadeInUp} className='mb-8'>
            <h1 className='text-4xl font-bold text-retreat-green' role='banner'>
              Retreat And Be
            </h1>
          </motion.div>

          {/* Search Bar */}
          <SearchBar />

          {/* Action Buttons */}
          <motion.div
            {...fadeInDown}
            className='mt-8 flex flex-col sm:flex-row gap-4'
            role='navigation'
            aria-label='Actions principales'
          >
            <Link
              to='/client'
              className='px-6 py-3 bg-retreat-green text-black font-medium rounded-full hover:bg-opacity-90 transition-colors duration-300 shadow-md hover:shadow-lg border-2 border-mint-700 hover:border-mint-800'
              role='button'
              aria-label='Trouver sa retraite idéale'
              tabIndex={0}
            >
              Trouver sa retraite idéale
            </Link>
            <Link
              to='/professional'
              className='px-6 py-3 bg-retreat-green text-black font-medium rounded-full hover:bg-opacity-90 transition-colors duration-300 shadow-md hover:shadow-lg border-2 border-mint-700 hover:border-mint-800'
              role='button'
              aria-label="Accéder à l'espace professionnel"
              tabIndex={0}
            >
              Espace professionnel
            </Link>
          </motion.div>

          {/* Account Icon */}
          <motion.div {...fadeIn} className='absolute top-4 right-4'>
            <button
              className='p-2 rounded-full hover:bg-mint-100 transition-colors duration-300'
              aria-label='Accéder à mon compte'
              tabIndex={0}
            >
              <UserCircleIcon className='h-6 w-6 text-retreat-green' aria-hidden='true' />
            </button>
          </motion.div>

          {/* Multi-Criteria Recommendation Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="w-full mt-16"
          >
            <MultiCriteriaRecommendationSection
              count={3}
              className="bg-mint-50"
            />
          </motion.div>
        </div>

        <FooterAccueil />

        {/* Scroll to Top Button */}
        <ScrollToTop />

        {/* Chat Bot Button */}
        <ChatBot />
      </div>
    </>
  );
};

export default HomePage;
