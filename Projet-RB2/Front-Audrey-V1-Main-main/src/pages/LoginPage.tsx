import React from 'react';
import { Helmet } from 'react-helmet-async';
import { LoginForm } from '../components/auth/LoginForm';
import MainLayout from '../components/templates/MainLayout/MainLayout';
import { useAuth } from '../contexts/AuthContext';
import { Navigate, Link } from 'react-router-dom';

const LoginPage: React.FC = () => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <Navigate to='/' replace />;
  }

  return (
    <MainLayout>
      <Helmet>
        <title>Connexion | Retreat and Be</title>
        <meta name='description' content='Connectez-vous à votre compte Retreat and Be.' />
      </Helmet>
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
        <div className='w-full max-w-md'>
          <LoginForm />
          <p className='mt-8 text-center text-sm text-gray-600'>
            Pas encore de compte ?{' '} <Link to='/register' className='font-medium text-indigo-600 hover:text-indigo-500'>Inscrivez-vous ici</Link>
          </p>
        </div>
      </div>
    </MainLayout>
  );
};

export default LoginPage;
