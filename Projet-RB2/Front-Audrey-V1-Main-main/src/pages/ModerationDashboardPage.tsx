import React, { useEffect, useState } from 'react';
import { ModerationDashboard } from '../components/moderation';
import { useAuthContext } from '../hooks/useAuthContext';
import { useTranslation } from '../hooks/useTranslation';

const ModerationDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  const [userRole, setUserRole] = useState<'admin' | 'moderator' | 'user'>('user');

  useEffect(() => {
    // Determine user role based on user data
    if (user) {
      if (user.roles?.includes('admin')) {
        setUserRole('admin');
      } else if (user.roles?.includes('moderator')) {
        setUserRole('moderator');
      } else {
        setUserRole('user');
      }
    }
  }, [user]);

  // Redirect or show access denied if user doesn't have appropriate role
  if (userRole === 'user') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <h2 className="text-lg font-medium">{t('common.accessDenied')}</h2>
          <p>{t('moderation.accessDeniedMessage')}</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <ModerationDashboard userRole={userRole} />
    </div>
  );
};

export default ModerationDashboardPage;
