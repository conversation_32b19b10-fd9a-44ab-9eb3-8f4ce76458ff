import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import StripeWrapper from '../../components/organisms/PaymentForm/StripeWrapper';
import Spinner from '../../components/atoms/Spinner/Spinner';
import Alert from '../../components/common/Alert';
import Button from '../../components/atoms/Button/Button';
import { bookingService } from '../../services/api/bookingService';
import { retreatService } from '../../services/api/retreatService';
import { paymentService } from '../../services/api/paymentService';
import { formatDate } from '../../utils/dateUtils';

const PaymentPage: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();

  const [booking, setBooking] = useState<any>(null);
  const [retreat, setRetreat] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState<boolean>(false);
  const [paymentId, setPaymentId] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!bookingId) {
        setError('Booking ID is missing');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch booking details
        const bookingData = await bookingService.getBookingById(bookingId);
        setBooking(bookingData);

        // Fetch retreat details
        const retreatData = await retreatService.getRetreatById(bookingData.retreatId);
        setRetreat(retreatData);
      } catch (err: any) {
        setError(err.message || 'Failed to load booking details');
        console.error('Error loading booking details:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [bookingId]);

  const handlePaymentSuccess = async (paymentId: string) => {
    setPaymentId(paymentId);
    setPaymentSuccess(true);

    try {
      // Update booking status to CONFIRMED
      await bookingService.updateBookingStatus(bookingId!, 'confirmed');
    } catch (err: any) {
      console.error('Error updating booking status:', err);
      // We don't set an error here because the payment was successful
    }
  };

  const handlePaymentCancel = () => {
    navigate(`/bookings/${bookingId}`);
  };

  const handleViewBooking = () => {
    navigate(`/bookings/${bookingId}`);
  };

  const handleDownloadInvoice = async () => {
    if (!paymentId) return;

    try {
      // Get invoice for the payment
      const invoice = await paymentService.getInvoice(paymentId);

      // Download invoice PDF
      const invoicePdf = await paymentService.downloadInvoice(invoice.id);

      // Create a download link
      const url = window.URL.createObjectURL(new Blob([invoicePdf]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `invoice-${invoice.invoiceNumber}.pdf`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      console.error('Error downloading invoice:', err);
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center min-h-screen'>
        <Spinner size='lg' />
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <Alert type='error' message={error} className='mb-4' />
        <Button onClick={() => navigate('/bookings')}>Back to Bookings</Button>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <motion.div
        className='container mx-auto px-4 py-8'
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className='bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto'>
          <div className='text-center mb-6'>
            <div className='inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4'>
              <svg
                className='w-8 h-8 text-green-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M5 13l4 4L19 7'
                />
              </svg>
            </div>
            <h1 className='text-2xl font-bold text-gray-800'>Payment Successful!</h1>
            <p className='text-gray-600 mt-2'>Your booking has been confirmed.</p>
          </div>

          <div className='border-t border-b border-gray-200 py-4 mb-6'>
            <div className='flex justify-between mb-2'>
              <span className='text-gray-600'>Retreat:</span>
              <span className='font-medium'>{retreat?.title}</span>
            </div>
            <div className='flex justify-between mb-2'>
              <span className='text-gray-600'>Dates:</span>
              <span className='font-medium'>
                {formatDate(booking?.startDate)} - {formatDate(booking?.endDate)}
              </span>
            </div>
            <div className='flex justify-between mb-2'>
              <span className='text-gray-600'>Participants:</span>
              <span className='font-medium'>{booking?.numberOfParticipants}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>Total Amount:</span>
              <span className='font-bold text-retreat-green'>
                {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(
                  booking?.totalPrice
                )}
              </span>
            </div>
          </div>

          <div className='flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4'>
            <Button onClick={handleViewBooking} variant='primary'>
              View Booking Details
            </Button>
            <Button onClick={handleDownloadInvoice} variant='outline'>
              Download Invoice
            </Button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        <h1 className='text-3xl font-bold mb-6'>Complete Your Payment</h1>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {/* Booking Summary */}
          <div className='md:col-span-1'>
            <div className='bg-white rounded-lg shadow-md p-6'>
              <h2 className='text-xl font-semibold mb-4'>Booking Summary</h2>

              {retreat && (
                <div className='mb-4'>
                  <img
                    src={
                      retreat.images && retreat.images.length > 0
                        ? retreat.images[0]
                        : '/images/placeholder-retreat.jpg'
                    }
                    alt={retreat.title}
                    className='w-full h-40 object-cover rounded-md mb-3'
                  />
                  <h3 className='font-medium text-lg'>{retreat.title}</h3>
                  <p className='text-gray-600'>{retreat.location}</p>
                </div>
              )}

              {booking && (
                <div className='space-y-3 border-t pt-4'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Dates:</span>
                    <span>
                      {formatDate(booking.startDate)} - {formatDate(booking.endDate)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Participants:</span>
                    <span>{booking.numberOfParticipants}</span>
                  </div>
                  <div className='flex justify-between font-medium'>
                    <span>Total:</span>
                    <span>
                      {new Intl.NumberFormat('fr-FR', {
                        style: 'currency',
                        currency: 'EUR',
                      }).format(booking.totalPrice)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Payment Form */}
          <div className='md:col-span-2'>
            {booking && (
              <StripeWrapper
                bookingId={bookingId!}
                amount={booking.totalPrice}
                currency='EUR'
                onSuccess={handlePaymentSuccess}
                onCancel={handlePaymentCancel}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentPage;
