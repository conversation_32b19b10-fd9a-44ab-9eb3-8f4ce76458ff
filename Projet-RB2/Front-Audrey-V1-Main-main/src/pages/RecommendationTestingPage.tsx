import React from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useAuthContext } from '../hooks/useAuthContext';
import { PageLayout } from '../layouts/PageLayout';
import { RecommendationTestingDashboard } from '../components/recommendation/RecommendationTestingDashboard';
import { Alert } from '../components/ui/Alert';

const RecommendationTestingPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuthContext();
  
  const isAdmin = user?.roles?.includes('admin');

  if (!user) {
    return (
      <PageLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert
            variant="warning"
            title={t('common.unauthorized')}
            message={t('testing.unauthorized')}
          />
        </div>
      </PageLayout>
    );
  }

  if (!isAdmin) {
    return (
      <PageLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert
            variant="warning"
            title={t('common.forbidden')}
            message={t('testing.adminOnly')}
          />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('testing.pageTitle')}</h1>
          <p className="text-gray-500 mt-2">{t('testing.pageDescription')}</p>
        </div>
        
        <RecommendationTestingDashboard />
      </div>
    </PageLayout>
  );
};

export default RecommendationTestingPage;
