import React, { useState, useEffect } from 'react';
import { Layout, Typography, Row, Col, Spin, Alert, Empty, Select, Space, Divider, Button, Affix, Card } from 'antd';
import { FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import RecommendationCard from '../components/recommendation/RecommendationCard';
import { getRecommendations } from '../services/api/recommendationService';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

/**
 * Page des recommandations
 */
const RecommendationsPage = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    type: 'RETREAT',
    limit: 10,
    detailLevel: 'STANDARD',
    language: 'fr',
  });

  useEffect(() => {
    fetchRecommendations();
  }, [filters.type, filters.limit]);

  /**
   * Récupère les recommandations depuis l'API
   */
  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getRecommendations({
        type: filters.type,
        limit: filters.limit,
      });
      
      if (response && response.success) {
        setRecommendations(response.data);
      } else {
        setError('Impossible de récupérer les recommandations');
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des recommandations:', err);
      setError('Une erreur est survenue lors de la récupération des recommandations');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Gère le changement de type de recommandation
   */
  const handleTypeChange = (value) => {
    setFilters({
      ...filters,
      type: value,
    });
  };

  /**
   * Gère le changement de niveau de détail
   */
  const handleDetailLevelChange = (value) => {
    setFilters({
      ...filters,
      detailLevel: value,
    });
  };

  /**
   * Gère le changement de langue
   */
  const handleLanguageChange = (value) => {
    setFilters({
      ...filters,
      language: value,
    });
  };

  /**
   * Gère la soumission d'un feedback
   */
  const handleFeedbackSubmitted = (recommendationId, type, feedbackType, data) => {
    console.log('Feedback soumis:', { recommendationId, type, feedbackType, data });
    // Vous pourriez mettre à jour l'état local ou rafraîchir les recommandations ici
  };

  return (
    <Layout>
      <Content style={{ padding: '24px', maxWidth: 1200, margin: '0 auto' }}>
        <Title level={2}>Recommandations personnalisées</Title>
        <Text type="secondary">
          Découvrez des retraites, cours et événements adaptés à vos préférences
        </Text>
        
        <Divider />
        
        <Affix offsetTop={10}>
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Space>
                <FilterOutlined />
                <Text strong>Filtres:</Text>
              </Space>
              
              <Select
                value={filters.type}
                onChange={handleTypeChange}
                style={{ width: 150 }}
              >
                <Option value="RETREAT">Retraites</Option>
                <Option value="COURSE">Cours</Option>
                <Option value="VIDEO">Vidéos</Option>
                <Option value="ARTICLE">Articles</Option>
                <Option value="EVENT">Événements</Option>
              </Select>
              
              <Select
                value={filters.detailLevel}
                onChange={handleDetailLevelChange}
                style={{ width: 150 }}
              >
                <Option value="BASIC">Basique</Option>
                <Option value="STANDARD">Standard</Option>
                <Option value="DETAILED">Détaillé</Option>
              </Select>
              
              <Select
                value={filters.language}
                onChange={handleLanguageChange}
                style={{ width: 100 }}
              >
                <Option value="fr">Français</Option>
                <Option value="en">English</Option>
              </Select>
              
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchRecommendations}
                loading={loading}
              >
                Actualiser
              </Button>
            </Space>
          </Card>
        </Affix>
        
        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>Chargement des recommandations...</Text>
            </div>
          </div>
        ) : error ? (
          <Alert
            message="Erreur"
            description={error}
            type="error"
            showIcon
          />
        ) : recommendations.length === 0 ? (
          <Empty
            description="Aucune recommandation trouvée"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <Row gutter={[16, 16]}>
            {recommendations.map((recommendation) => (
              <Col xs={24} sm={24} md={12} lg={8} key={recommendation.id}>
                <RecommendationCard
                  recommendation={recommendation}
                  type={filters.type}
                  onFeedbackSubmitted={handleFeedbackSubmitted}
                  language={filters.language}
                  detailLevel={filters.detailLevel}
                />
              </Col>
            ))}
          </Row>
        )}
      </Content>
    </Layout>
  );
};

export default RecommendationsPage;
