import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../hooks/useAuthContext';
import Header from '../components/organisms/NavBarClient/NavBarClient';
import Footer from '../components/ui/Footer';

const RegisterPage: React.FC = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [formError, setFormError] = useState('');

  const { register, isLoading, error, clearError } = useAuthContext();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    setFormError('');

    // Validation basique
    if (!firstName || !lastName || !email || !password || !confirmPassword) {
      setFormError('Veuillez remplir tous les champs');
      return;
    }

    if (password !== confirmPassword) {
      setFormError('Les mots de passe ne correspondent pas');
      return;
    }

    if (!acceptTerms) {
      setFormError("Vous devez accepter les conditions d'utilisation");
      return;
    }

    try {
      await register({ firstName, lastName, email, password });
      navigate('/');
    } catch (err: unknown) {
      console.error("Erreur d'inscription:", err instanceof Error ? err.message : err);
      // L'erreur est déjà gérée par le contexte d'authentification
    }
  };

  return (
    <>
      <Helmet>
        <title>Inscription | Retreat And Be</title>
        <meta
          name='description'
          content='Créez un compte sur Retreat And Be pour découvrir des retraites bien-être et réserver votre prochaine expérience.'
        />
      </Helmet>

      <Header />

      <main className='min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8'>
        <div className='sm:mx-auto sm:w-full sm:max-w-md'>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
            Créer un compte
          </h2>
          <p className='mt-2 text-center text-sm text-gray-600'>
            Ou{' '}
            <Link to='/login' className='font-medium text-green-600 hover:text-green-500'>
              connectez-vous à votre compte existant
            </Link>
          </p>
        </div>

        <div className='mt-8 sm:mx-auto sm:w-full sm:max-w-md'>
          <div className='bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10'>
            {(error || formError) && (
              <div className='mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded'>
                {error || formError}
              </div>
            )}

            <form className='space-y-6' onSubmit={handleSubmit}>
              <div className='grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2'>
                <div>
                  <label htmlFor='first-name' className='block text-sm font-medium text-gray-700'>
                    Prénom
                  </label>
                  <div className='mt-1'>
                    <input
                      type='text'
                      id='first-name'
                      name='first-name'
                      autoComplete='given-name'
                      required
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm'
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor='last-name' className='block text-sm font-medium text-gray-700'>
                    Nom
                  </label>
                  <div className='mt-1'>
                    <input
                      type='text'
                      id='last-name'
                      name='last-name'
                      autoComplete='family-name'
                      required
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm'
                    />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor='email' className='block text-sm font-medium text-gray-700'>
                  Adresse e-mail
                </label>
                <div className='mt-1'>
                  <input
                    id='email'
                    name='email'
                    type='email'
                    autoComplete='email'
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm'
                  />
                </div>
              </div>

              <div>
                <label htmlFor='password' className='block text-sm font-medium text-gray-700'>
                  Mot de passe
                </label>
                <div className='mt-1'>
                  <input
                    id='password'
                    name='password'
                    type='password'
                    autoComplete='new-password'
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm'
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor='confirm-password'
                  className='block text-sm font-medium text-gray-700'
                >
                  Confirmer le mot de passe
                </label>
                <div className='mt-1'>
                  <input
                    id='confirm-password'
                    name='confirm-password'
                    type='password'
                    autoComplete='new-password'
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm'
                  />
                </div>
              </div>

              <div className='flex items-center'>
                <input
                  id='accept-terms'
                  name='accept-terms'
                  type='checkbox'
                  required
                  checked={acceptTerms}
                  onChange={(e) => setAcceptTerms(e.target.checked)}
                  className='h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded'
                />
                <label htmlFor='accept-terms' className='ml-2 block text-sm text-gray-900'>
                  J&apos;accepte les{' '}
                  <Link
                    to='/conditions-utilisation'
                    className='font-medium text-green-600 hover:text-green-500'
                  >
                    conditions d&apos;utilisation
                  </Link>{' '}
                  et la{' '}
                  <Link
                    to='/politique-confidentialite'
                    className='font-medium text-green-600 hover:text-green-500'
                  >
                    politique de confidentialité
                  </Link>
                </label>
              </div>

              <div>
                <button
                  type='submit'
                  disabled={isLoading}
                  className='w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  {isLoading ? 'Inscription en cours...' : 'S&apos;inscrire'}
                </button>
              </div>
            </form>

            <div className='mt-6'>
              <div className='relative'>
                <div className='absolute inset-0 flex items-center'>
                  <div className='w-full border-t border-gray-300' />
                </div>
                <div className='relative flex justify-center text-sm'>
                  <span className='px-2 bg-white text-gray-500'>Ou s&apos;inscrire avec</span>
                </div>
              </div>

              <div className='mt-6 grid grid-cols-2 gap-3'>
                <div>
                  <button
                    type='button'
                    className='w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'
                  >
                    <span className='sr-only'>S&apos;inscrire avec Google</span>
                    <svg
                      className='w-5 h-5'
                      fill='currentColor'
                      viewBox='0 0 24 24'
                      aria-hidden='true'
                    >
                      <path d='M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z' />
                    </svg>
                  </button>
                </div>

                <div>
                  <button
                    type='button'
                    className='w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50'
                  >
                    <span className='sr-only'>S&apos;inscrire avec Facebook</span>
                    <svg
                      className='w-5 h-5'
                      fill='currentColor'
                      viewBox='0 0 24 24'
                      aria-hidden='true'
                    >
                      <path
                        fillRule='evenodd'
                        d='M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </>
  );
};

export default RegisterPage;
