import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { alertService, AlertConfig } from '../../services/api/alertService';
import { t } from '../../services/i18n/i18nService';

/**
 * Page de configuration des alertes
 */
const AlertConfigPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<AlertConfig>({
    enabled: true,
    performanceThreshold: 0.1,
    anomalyDetectionEnabled: true,
    anomalyThreshold: 0.2,
    notifyByEmail: true,
    notifyInApp: true,
    minPriority: 'medium',
  });
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger la configuration
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const configData = await alertService.getAlertConfig();
        setConfig(configData);
      } catch (err: any) {
        console.error('Erreur lors du chargement de la configuration des alertes:', err);
        setError(err.message || 'Erreur lors du chargement de la configuration des alertes');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && user.role === 'ADMIN') {
      loadConfig();
    }
  }, [user]);
  
  // Gérer le changement de configuration
  const handleConfigChange = (field: keyof AlertConfig, value: any) => {
    setConfig({
      ...config,
      [field]: value,
    });
  };
  
  // Enregistrer la configuration
  const handleSaveConfig = async () => {
    try {
      setSaving(true);
      
      await alertService.updateAlertConfig(config);
      
      toast.success('Configuration des alertes enregistrée avec succès');
    } catch (err: any) {
      console.error('Erreur lors de l\'enregistrement de la configuration des alertes:', err);
      toast.error(err.message || 'Erreur lors de l\'enregistrement de la configuration des alertes');
    } finally {
      setSaving(false);
    }
  };
  
  // Créer une alerte de test
  const handleCreateTestAlert = async () => {
    try {
      await alertService.createAlert({
        type: 'info',
        title: 'Alerte de test',
        message: 'Ceci est une alerte de test pour vérifier la configuration.',
        source: 'admin',
        read: false,
        priority: 'medium',
        actionRequired: false,
      });
      
      toast.success('Alerte de test créée avec succès');
    } catch (err: any) {
      console.error('Erreur lors de la création de l\'alerte de test:', err);
      toast.error(err.message || 'Erreur lors de la création de l\'alerte de test');
    }
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Configuration des Alertes | Retreat And Be</title>
          <meta
            name="description"
            content="Configuration des alertes pour les administrateurs de Retreat And Be."
          />
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Configuration des Alertes | Retreat And Be</title>
        <meta
          name="description"
          content="Configuration des alertes pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Configuration des Alertes</h1>
            <p className="text-gray-600">Configurer les alertes automatiques pour les performances exceptionnelles</p>
          </div>
          
          {/* Afficher un message d'erreur */}
          {error && (
            <div className="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p>{error}</p>
              </div>
            </div>
          )}
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-6">
              <div className="mb-6">
                <label className="inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={config.enabled}
                    onChange={(e) => handleConfigChange('enabled', e.target.checked)}
                  />
                  <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green"></div>
                  <span className="ms-3 text-sm font-medium text-gray-900">Activer les alertes automatiques</span>
                </label>
                <p className="mt-1 text-xs text-gray-500">
                  Recevoir des alertes pour les performances exceptionnelles et les anomalies
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Seuils de détection</h3>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Seuil de performance exceptionnelle: {(config.performanceThreshold * 100).toFixed(0)}%
                    </label>
                    <input
                      type="range"
                      value={config.performanceThreshold * 100}
                      onChange={(e) => handleConfigChange('performanceThreshold', parseInt(e.target.value) / 100)}
                      disabled={!config.enabled}
                      min={5}
                      max={50}
                      step={1}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Amélioration minimale pour déclencher une alerte de performance exceptionnelle
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={config.anomalyDetectionEnabled}
                        onChange={(e) => handleConfigChange('anomalyDetectionEnabled', e.target.checked)}
                        disabled={!config.enabled}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green disabled:opacity-50"></div>
                      <span className="ms-3 text-sm font-medium text-gray-900">Activer la détection d'anomalies</span>
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Détecter automatiquement les anomalies dans les performances
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Seuil d'anomalie: {(config.anomalyThreshold * 100).toFixed(0)}%
                    </label>
                    <input
                      type="range"
                      value={config.anomalyThreshold * 100}
                      onChange={(e) => handleConfigChange('anomalyThreshold', parseInt(e.target.value) / 100)}
                      disabled={!config.enabled || !config.anomalyDetectionEnabled}
                      min={5}
                      max={50}
                      step={1}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Écart minimal par rapport à la moyenne pour considérer une performance comme anormale
                    </p>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Paramètres de notification</h3>
                  
                  <div className="mb-4">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={config.notifyInApp}
                        onChange={(e) => handleConfigChange('notifyInApp', e.target.checked)}
                        disabled={!config.enabled}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green disabled:opacity-50"></div>
                      <span className="ms-3 text-sm font-medium text-gray-900">Notifications dans l'application</span>
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Afficher les alertes dans le centre de notifications de l'application
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={config.notifyByEmail}
                        onChange={(e) => handleConfigChange('notifyByEmail', e.target.checked)}
                        disabled={!config.enabled}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green disabled:opacity-50"></div>
                      <span className="ms-3 text-sm font-medium text-gray-900">Notifications par email</span>
                    </label>
                    <p className="mt-1 text-xs text-gray-500">
                      Envoyer les alertes par email aux administrateurs
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priorité minimale
                    </label>
                    <select
                      value={config.minPriority}
                      onChange={(e) => handleConfigChange('minPriority', e.target.value)}
                      disabled={!config.enabled}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green disabled:bg-gray-100 disabled:text-gray-500"
                    >
                      <option value="low">Basse</option>
                      <option value="medium">Moyenne</option>
                      <option value="high">Haute</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Priorité minimale pour déclencher une notification
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={handleCreateTestAlert}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  disabled={!config.enabled}
                >
                  Créer une alerte de test
                </button>
                <button
                  onClick={handleSaveConfig}
                  disabled={saving}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                >
                  {saving ? 'Enregistrement...' : 'Enregistrer'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AlertConfigPage;
