import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { explanationLearningService, ExplanationLearningConfig, ExplanationLearningMetrics } from '../../services/api/explanationLearningService';
import { t } from '../../services/i18n/i18nService';

/**
 * Page d'administration pour l'intégration des tests A/B et de l'apprentissage continu
 */
const ExplanationLearningPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [config, setConfig] = useState<ExplanationLearningConfig>({
    enabled: true,
    minInteractionsForOptimization: 100,
    minConfidenceLevel: 0.8,
    optimizationInterval: 24,
    learningRate: 0.1,
    templateOptimizationEnabled: true,
    factorWeightOptimizationEnabled: true,
    autoDeployWinners: false,
    autoDeployThreshold: 0.15,
  });
  const [metrics, setMetrics] = useState<ExplanationLearningMetrics>({
    totalOptimizations: 0,
    lastOptimizationDate: null,
    averageImprovement: 0,
    factorWeightUpdates: 0,
    templateUpdates: 0,
    deployedVariants: 0,
    learningEvents: [],
    performanceHistory: [],
  });
  const [saving, setSaving] = useState<boolean>(false);
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger la configuration et les métriques
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Charger la configuration
        const configData = await explanationLearningService.getConfiguration();
        setConfig(configData);
        
        // Charger les métriques
        const metricsData = await explanationLearningService.getMetrics();
        setMetrics(metricsData);
      } catch (err: any) {
        console.error('Erreur lors du chargement des données:', err);
        setError(err.message || 'Erreur lors du chargement des données');
      } finally {
        setLoading(false);
      }
    };
    
    if (user && user.role === 'ADMIN') {
      loadData();
    }
  }, [user]);
  
  // Gérer le changement de configuration
  const handleConfigChange = (field: keyof ExplanationLearningConfig, value: any) => {
    setConfig({
      ...config,
      [field]: value,
    });
  };
  
  // Enregistrer la configuration
  const handleSaveConfig = async () => {
    try {
      setSaving(true);
      
      await explanationLearningService.updateConfiguration(config);
      
      toast.success('Configuration enregistrée avec succès');
    } catch (err: any) {
      console.error('Erreur lors de l\'enregistrement de la configuration:', err);
      toast.error(err.message || 'Erreur lors de l\'enregistrement de la configuration');
    } finally {
      setSaving(false);
    }
  };
  
  // Déclencher une optimisation manuelle
  const handleTriggerOptimization = async () => {
    try {
      setLoading(true);
      
      await explanationLearningService.triggerOptimization();
      
      // Recharger les métriques
      const metricsData = await explanationLearningService.getMetrics();
      setMetrics(metricsData);
      
      toast.success('Optimisation déclenchée avec succès');
    } catch (err: any) {
      console.error('Erreur lors du déclenchement de l\'optimisation:', err);
      toast.error(err.message || 'Erreur lors du déclenchement de l\'optimisation');
    } finally {
      setLoading(false);
    }
  };
  
  // Formater une date
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'Non disponible';
    
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };
  
  // Afficher un indicateur de chargement
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Apprentissage Continu | Retreat And Be</title>
          <meta
            name="description"
            content="Gestion de l'apprentissage continu des explications pour les administrateurs de Retreat And Be."
          />
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Apprentissage Continu | Retreat And Be</title>
        <meta
          name="description"
          content="Gestion de l'apprentissage continu des explications pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Apprentissage Continu des Explications</h1>
              <p className="text-gray-600">Intégration des tests A/B et de l'apprentissage automatique</p>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => window.location.reload()}
                className="p-2 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors"
                aria-label="Rafraîchir"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
              </button>
              <button
                onClick={handleTriggerOptimization}
                disabled={!config.enabled}
                className={`px-4 py-2 rounded-md flex items-center space-x-1 ${
                  config.enabled 
                    ? 'bg-retreat-green text-white hover:bg-retreat-green-dark' 
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                } transition-colors`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                <span>Optimiser maintenant</span>
              </button>
            </div>
          </div>
          
          {/* Afficher un message d'erreur */}
          {error && (
            <div className="mb-6 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <p>{error}</p>
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Configuration */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-retreat-green mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <h2 className="text-xl font-semibold text-gray-900">Configuration</h2>
                </div>
                
                <div className="mb-4">
                  <label className="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={config.enabled}
                      onChange={(e) => handleConfigChange('enabled', e.target.checked)}
                    />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green"></div>
                    <span className="ms-3 text-sm font-medium text-gray-900">Activer l'apprentissage continu</span>
                  </label>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Interactions minimales
                    </label>
                    <input
                      type="number"
                      value={config.minInteractionsForOptimization}
                      onChange={(e) => handleConfigChange('minInteractionsForOptimization', parseInt(e.target.value))}
                      disabled={!config.enabled}
                      min={10}
                      max={1000}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green disabled:bg-gray-100 disabled:text-gray-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Nombre minimum d'interactions pour déclencher une optimisation
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Niveau de confiance minimum: {(config.minConfidenceLevel * 100).toFixed(0)}%
                    </label>
                    <input
                      type="range"
                      value={config.minConfidenceLevel * 100}
                      onChange={(e) => handleConfigChange('minConfidenceLevel', parseInt(e.target.value) / 100)}
                      disabled={!config.enabled}
                      min={50}
                      max={99}
                      step={1}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Niveau de confiance statistique minimum pour appliquer les optimisations
                    </p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Paramètres d'optimisation</h3>
                  
                  <div className="space-y-2">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={config.templateOptimizationEnabled}
                        onChange={(e) => handleConfigChange('templateOptimizationEnabled', e.target.checked)}
                        disabled={!config.enabled}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green disabled:opacity-50"></div>
                      <span className="ms-3 text-sm font-medium text-gray-900">Optimisation des templates</span>
                    </label>
                    
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={config.factorWeightOptimizationEnabled}
                        onChange={(e) => handleConfigChange('factorWeightOptimizationEnabled', e.target.checked)}
                        disabled={!config.enabled}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green disabled:opacity-50"></div>
                      <span className="ms-3 text-sm font-medium text-gray-900">Optimisation des poids des facteurs</span>
                    </label>
                    
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={config.autoDeployWinners}
                        onChange={(e) => handleConfigChange('autoDeployWinners', e.target.checked)}
                        disabled={!config.enabled}
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-retreat-green/20 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-retreat-green disabled:opacity-50"></div>
                      <span className="ms-3 text-sm font-medium text-gray-900">Déploiement automatique des gagnants</span>
                    </label>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <button
                    onClick={handleSaveConfig}
                    disabled={saving}
                    className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors disabled:bg-gray-300 disabled:text-gray-500"
                  >
                    {saving ? 'Enregistrement...' : 'Enregistrer'}
                  </button>
                </div>
              </div>
            </div>
            
            {/* Métriques */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-retreat-green mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <h2 className="text-xl font-semibold text-gray-900">Métriques</h2>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-retreat-green">{metrics.totalOptimizations}</div>
                    <div className="text-sm text-gray-500">Optimisations totales</div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-retreat-green">
                      {metrics.lastOptimizationDate ? formatDate(metrics.lastOptimizationDate).split(' ')[0] : 'Jamais'}
                    </div>
                    <div className="text-sm text-gray-500">Dernière optimisation</div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-retreat-green">{(metrics.averageImprovement * 100).toFixed(1)}%</div>
                    <div className="text-sm text-gray-500">Amélioration moyenne</div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-retreat-green">{metrics.deployedVariants}</div>
                    <div className="text-sm text-gray-500">Variantes déployées</div>
                  </div>
                </div>
                
                {/* Événements d'apprentissage récents */}
                {metrics.learningEvents && metrics.learningEvents.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Événements récents</h3>
                    <div className="space-y-2">
                      {metrics.learningEvents.slice(0, 5).map((event, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-retreat-green mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                            </svg>
                            <div>
                              <div className="text-sm font-medium">{event.description}</div>
                              <div className="text-xs text-gray-500">{formatDate(event.timestamp)}</div>
                            </div>
                          </div>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            +{(event.improvement * 100).toFixed(1)}%
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Pas d'événements */}
                {(!metrics.learningEvents || metrics.learningEvents.length === 0) && (
                  <div className="text-center py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun événement d'apprentissage</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Les événements d'apprentissage apparaîtront ici une fois que des tests A/B seront terminés.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ExplanationLearningPage;
