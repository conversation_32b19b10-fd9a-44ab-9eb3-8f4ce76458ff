import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import { toast } from 'react-toastify';
import { 
  diversityFairnessService,
  DiversityMetrics,
  FairnessMetrics,
} from '../../services/api/diversityFairnessService';
import { t } from '../../services/i18n/i18nService';
import LoadingSpinner from '../../components/common/LoadingSpinner';

/**
 * Page de tableau de bord d'équité pour les administrateurs
 */
const FairnessDashboardPage: React.FC = () => {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string>('');
  const [diversityMetrics, setDiversityMetrics] = useState<DiversityMetrics | null>(null);
  const [fairnessMetrics, setFairnessMetrics] = useState<FairnessMetrics | null>(null);
  const [activeTab, setActiveTab] = useState<'diversity' | 'fairness'>('diversity');
  
  // Vérifier si l'utilisateur est administrateur
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);
  
  // Charger les métriques lorsque l'ID utilisateur change
  useEffect(() => {
    const loadMetrics = async () => {
      if (!userId) {
        return;
      }
      
      try {
        setLoading(true);
        
        // Charger les métriques de diversité
        const diversityData = await diversityFairnessService.getDiversityMetrics(userId);
        setDiversityMetrics(diversityData);
        
        // Charger les métriques d'équité
        const fairnessData = await diversityFairnessService.getFairnessMetrics(userId);
        setFairnessMetrics(fairnessData);
      } catch (error) {
        console.error('Erreur lors du chargement des métriques:', error);
        toast.error('Erreur lors du chargement des métriques');
      } finally {
        setLoading(false);
      }
    };
    
    loadMetrics();
  }, [userId]);
  
  // Gérer la soumission du formulaire
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userId) {
      toast.error('Veuillez saisir un ID utilisateur');
      return;
    }
    
    // Les métriques seront chargées automatiquement grâce à l'effet
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Tableau de Bord d'Équité | Retreat And Be</title>
        <meta
          name="description"
          content="Tableau de bord d'équité pour les administrateurs de Retreat And Be."
        />
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Tableau de Bord d'Équité</h1>
            <p className="text-gray-600">Analysez la diversité et l'équité des recommandations pour les utilisateurs</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div className="p-6">
              <form onSubmit={handleSubmit} className="flex items-end space-x-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ID Utilisateur
                  </label>
                  <input
                    type="text"
                    value={userId}
                    onChange={(e) => setUserId(e.target.value)}
                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                    placeholder="Saisissez l'ID de l'utilisateur..."
                  />
                </div>
                
                <button
                  type="submit"
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  Analyser
                </button>
              </form>
            </div>
          </div>
          
          {userId && (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'diversity'
                          ? 'border-retreat-green text-retreat-green'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('diversity')}
                    >
                      Métriques de Diversité
                    </button>
                    <button
                      className={`py-4 px-1 border-b-2 font-medium text-sm ${
                        activeTab === 'fairness'
                          ? 'border-retreat-green text-retreat-green'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                      onClick={() => setActiveTab('fairness')}
                    >
                      Métriques d'Équité
                    </button>
                  </nav>
                </div>
                
                <div className="mt-6">
                  {loading ? (
                    <div className="flex justify-center items-center h-64">
                      <LoadingSpinner />
                    </div>
                  ) : activeTab === 'diversity' ? (
                    <div>
                      {diversityMetrics ? (
                        <div>
                          <div className="mb-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-3">Scores de Diversité</h2>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Score Global</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(diversityMetrics.overallDiversity)}`}>
                                  {diversityFairnessService.formatPercent(diversityMetrics.overallDiversity)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Catégories</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(diversityMetrics.categoryDiversity)}`}>
                                  {diversityFairnessService.formatPercent(diversityMetrics.categoryDiversity)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Prix</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(diversityMetrics.priceDiversity)}`}>
                                  {diversityFairnessService.formatPercent(diversityMetrics.priceDiversity)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Durées</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(diversityMetrics.durationDiversity)}`}>
                                  {diversityFairnessService.formatPercent(diversityMetrics.durationDiversity)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Localisations</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(diversityMetrics.locationDiversity)}`}>
                                  {diversityFairnessService.formatPercent(diversityMetrics.locationDiversity)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Thèmes</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(diversityMetrics.themeDiversity)}`}>
                                  {diversityFairnessService.formatPercent(diversityMetrics.themeDiversity)}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="mb-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-3">Éléments Uniques</h2>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Catégories</div>
                                <div className="text-xl font-semibold">{diversityMetrics.uniqueCategories}</div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Plages de Prix</div>
                                <div className="text-xl font-semibold">{diversityMetrics.uniquePriceRanges}</div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Plages de Durée</div>
                                <div className="text-xl font-semibold">{diversityMetrics.uniqueDurationRanges}</div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Localisations</div>
                                <div className="text-xl font-semibold">{diversityMetrics.uniqueLocations}</div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Thèmes</div>
                                <div className="text-xl font-semibold">{diversityMetrics.uniqueThemes}</div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex justify-end">
                            <button
                              onClick={() => {
                                if (!userId) return;
                                
                                diversityFairnessService.diversifyRecommendations(userId, {
                                  diversityFactor: 0.7,
                                  includeSurprising: true,
                                }).then(() => {
                                  toast.success('Recommandations diversifiées avec succès');
                                  
                                  // Recharger les métriques
                                  setLoading(true);
                                  diversityFairnessService.getDiversityMetrics(userId)
                                    .then(setDiversityMetrics)
                                    .finally(() => setLoading(false));
                                }).catch((error) => {
                                  console.error('Erreur lors de la diversification des recommandations:', error);
                                  toast.error('Erreur lors de la diversification des recommandations');
                                });
                              }}
                              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                            >
                              Appliquer la Diversification
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <p className="text-gray-500">Aucune donnée de diversité disponible pour cet utilisateur.</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div>
                      {fairnessMetrics ? (
                        <div>
                          <div className="mb-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-3">Scores d'Équité</h2>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Score Global</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(fairnessMetrics.overallFairnessScore)}`}>
                                  {diversityFairnessService.formatPercent(fairnessMetrics.overallFairnessScore)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Catégories</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(fairnessMetrics.categoryRepresentationScore)}`}>
                                  {diversityFairnessService.formatPercent(fairnessMetrics.categoryRepresentationScore)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Prix</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(fairnessMetrics.priceRepresentationScore)}`}>
                                  {diversityFairnessService.formatPercent(fairnessMetrics.priceRepresentationScore)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Localisations</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(fairnessMetrics.locationRepresentationScore)}`}>
                                  {diversityFairnessService.formatPercent(fairnessMetrics.locationRepresentationScore)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Fournisseurs</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(fairnessMetrics.providerRepresentationScore)}`}>
                                  {diversityFairnessService.formatPercent(fairnessMetrics.providerRepresentationScore)}
                                </div>
                              </div>
                              <div className="bg-gray-50 p-4 rounded-md">
                                <div className="text-sm text-gray-500">Thèmes</div>
                                <div className={`text-xl font-semibold ${diversityFairnessService.getScoreColorClass(fairnessMetrics.themeRepresentationScore)}`}>
                                  {diversityFairnessService.formatPercent(fairnessMetrics.themeRepresentationScore)}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {fairnessMetrics.detectedBiases.length > 0 && (
                            <div className="mb-6">
                              <h2 className="text-lg font-medium text-gray-900 mb-3">Biais Détectés</h2>
                              <div className="space-y-2">
                                {fairnessMetrics.detectedBiases.map((bias, index) => (
                                  <div
                                    key={index}
                                    className={`p-3 rounded-md ${
                                      bias.severity === 'high'
                                        ? 'bg-red-50 border-l-4 border-red-500'
                                        : bias.severity === 'medium'
                                        ? 'bg-yellow-50 border-l-4 border-yellow-500'
                                        : 'bg-blue-50 border-l-4 border-blue-500'
                                    }`}
                                  >
                                    <div className="flex">
                                      <div className="flex-shrink-0">
                                        <svg className={`h-5 w-5 ${diversityFairnessService.getBiasSeverityColorClass(bias.severity)}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                        </svg>
                                      </div>
                                      <div className="ml-3">
                                        <p className={`text-sm ${diversityFairnessService.getBiasSeverityColorClass(bias.severity)}`}>
                                          {bias.description}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          <div className="flex justify-end">
                            <button
                              onClick={() => {
                                if (!userId) return;
                                
                                diversityFairnessService.applyFairness(userId, {
                                  fairnessFactor: 0.7,
                                  applyFairnessCorrection: true,
                                }).then(() => {
                                  toast.success('Équité appliquée avec succès');
                                  
                                  // Recharger les métriques
                                  setLoading(true);
                                  diversityFairnessService.getFairnessMetrics(userId)
                                    .then(setFairnessMetrics)
                                    .finally(() => setLoading(false));
                                }).catch((error) => {
                                  console.error('Erreur lors de l\'application de l\'équité:', error);
                                  toast.error('Erreur lors de l\'application de l\'équité');
                                });
                              }}
                              className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                            >
                              Appliquer l'Équité
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <p className="text-gray-500">Aucune donnée d'équité disponible pour cet utilisateur.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default FairnessDashboardPage;
