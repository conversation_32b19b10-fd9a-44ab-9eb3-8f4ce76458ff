import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { partnerService, Partner } from '../../services/api/partnerService';
import { useAuthContext } from '../../hooks/useAuthContext';
import AdminNavBar from '../../components/organisms/AdminNavBar/AdminNavBar';
import PartnerDocumentManager from '../../components/admin/PartnerDocumentManager';
import { toast } from 'react-toastify';

// Partner status options
const PARTNER_STATUS_OPTIONS = [
  { value: 'PENDING', label: 'En attente', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'ACTIVE', label: 'Actif', color: 'bg-green-100 text-green-800' },
  { value: 'SUSPENDED', label: 'Suspendu', color: 'bg-red-100 text-red-800' },
  { value: 'TERMINATED', label: 'Résilié', color: 'bg-gray-100 text-gray-800' },
];

const PartnerManagementPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const [partner, setPartner] = useState<Partner | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [partnerStatus, setPartnerStatus] = useState<string>('');
  const [adminNotes, setAdminNotes] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'details' | 'documents' | 'history'>('details');

  // Load partner data
  useEffect(() => {
    const fetchPartner = async () => {
      if (!id) {
        setError('ID du partenaire non fourni');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await partnerService.getPartnerById(id);
        setPartner(data);
        setPartnerStatus(data.status);
        setAdminNotes(data.adminNotes || '');
        setError(null);
      } catch (err) {
        console.error('Error fetching partner:', err);
        setError('Impossible de récupérer les informations du partenaire');
      } finally {
        setLoading(false);
      }
    };

    fetchPartner();
  }, [id]);

  // Check if user is admin
  useEffect(() => {
    if (user && user.role !== 'ADMIN') {
      toast.error('Accès non autorisé');
      navigate('/');
    }
  }, [user, navigate]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Handle partner status update
  const handleUpdateStatus = async () => {
    if (!partner) return;
    
    try {
      const updatedPartner = await partnerService.updateStatus(partner.id, partnerStatus);
      setPartner(updatedPartner);
      toast.success('Statut du partenaire mis à jour avec succès');
    } catch (err) {
      console.error('Error updating partner status:', err);
      toast.error('Erreur lors de la mise à jour du statut du partenaire');
    }
  };

  // Handle document status change
  const handleDocumentStatusChange = () => {
    // Refresh partner data when document status changes
    if (id) {
      partnerService.getPartnerById(id)
        .then(data => {
          setPartner(data);
        })
        .catch(err => {
          console.error('Error refreshing partner data:', err);
        });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-retreat-green"></div>
      </div>
    );
  }

  if (error || !partner) {
    return (
      <div className="min-h-screen bg-gray-100">
        <Helmet>
          <title>Erreur | Administration Partenaire</title>
        </Helmet>
        
        <AdminNavBar />
        
        <main className="pt-24 pb-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center py-8">
                <div className="text-red-500 text-lg mb-4">{error || 'Partenaire non trouvé'}</div>
                <Link
                  to="/admin/partners"
                  className="inline-block px-6 py-3 bg-retreat-green text-white rounded-lg hover:bg-retreat-green-dark transition-colors"
                >
                  Retour à la liste des partenaires
                </Link>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <Helmet>
        <title>Gestion Partenaire | Administration</title>
      </Helmet>
      
      <AdminNavBar />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Gestion du partenaire</h1>
              <p className="text-gray-600">{partner.companyName}</p>
            </div>
            
            <Link
              to="/admin/partners"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Retour à la liste
            </Link>
          </div>
          
          {/* Partner Status Card */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h2 className="text-xl font-semibold">{partner.companyName}</h2>
                <p className="text-gray-600">ID: {partner.id}</p>
                <p className="text-gray-600">Créé le: {formatDate(partner.createdAt)}</p>
              </div>
              
              <div className="mt-4 md:mt-0 flex items-center space-x-4">
                <div>
                  <select
                    value={partnerStatus}
                    onChange={(e) => setPartnerStatus(e.target.value)}
                    className="rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                  >
                    {PARTNER_STATUS_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <button
                  onClick={handleUpdateStatus}
                  className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                >
                  Mettre à jour
                </button>
              </div>
            </div>
          </div>
          
          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-md mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex">
                <button
                  onClick={() => setActiveTab('details')}
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'details'
                      ? 'border-b-2 border-retreat-green text-retreat-green'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Détails
                </button>
                <button
                  onClick={() => setActiveTab('documents')}
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'documents'
                      ? 'border-b-2 border-retreat-green text-retreat-green'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Documents
                </button>
                <button
                  onClick={() => setActiveTab('history')}
                  className={`py-4 px-6 font-medium text-sm ${
                    activeTab === 'history'
                      ? 'border-b-2 border-retreat-green text-retreat-green'
                      : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Historique
                </button>
              </nav>
            </div>
            
            <div className="p-6">
              {/* Details Tab */}
              {activeTab === 'details' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Informations de l'entreprise</h3>
                      
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-gray-500">Nom de l'entreprise</p>
                          <p className="font-medium">{partner.companyName}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Type</p>
                          <p className="font-medium">{partner.type}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Catégorie</p>
                          <p className="font-medium">{partner.category}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Site web</p>
                          <p className="font-medium">
                            {partner.website ? (
                              <a 
                                href={partner.website} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                {partner.website}
                              </a>
                            ) : (
                              'Non renseigné'
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Informations de contact</h3>
                      
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-gray-500">Utilisateur</p>
                          <p className="font-medium">
                            {partner.user ? (
                              <>
                                {partner.user.firstName} {partner.user.lastName} ({partner.user.email})
                              </>
                            ) : (
                              'Utilisateur non trouvé'
                            )}
                          </p>
                        </div>
                        {partner.contactInfo && (
                          <>
                            <div>
                              <p className="text-sm text-gray-500">Contact principal</p>
                              <p className="font-medium">{partner.contactInfo.name}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Email</p>
                              <p className="font-medium">{partner.contactInfo.email}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Téléphone</p>
                              <p className="font-medium">{partner.contactInfo.phone}</p>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Notes administratives</h3>
                    <textarea
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      rows={4}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-retreat-green focus:ring-retreat-green"
                      placeholder="Ajoutez des notes administratives concernant ce partenaire..."
                    />
                    <div className="mt-2 flex justify-end">
                      <button
                        className="px-4 py-2 bg-retreat-green text-white rounded-md hover:bg-retreat-green-dark transition-colors"
                      >
                        Enregistrer les notes
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Documents Tab */}
              {activeTab === 'documents' && (
                <PartnerDocumentManager 
                  partnerId={partner.id} 
                  onStatusChange={handleDocumentStatusChange}
                />
              )}
              
              {/* History Tab */}
              {activeTab === 'history' && (
                <div>
                  <h3 className="text-lg font-semibold mb-4">Historique des modifications</h3>
                  
                  <div className="bg-gray-50 p-4 rounded-md">
                    <p className="text-gray-500 text-center">Fonctionnalité à venir</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default PartnerManagementPage;
