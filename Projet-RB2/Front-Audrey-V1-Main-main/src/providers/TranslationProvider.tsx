import React, { createContext, useContext, ReactNode } from 'react';
import { SupportedLanguage } from '../services/i18n/i18nService';
import useTranslation from '../hooks/useTranslation';

// Interface pour le contexte de traduction
interface TranslationContextType {
  t: (key: string, params?: Record<string, string | number>) => string;
  isReady: boolean;
  currentLanguage: SupportedLanguage;
  changeLanguage: (language: SupportedLanguage) => void;
  formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => string;
  formatNumber: (number: number, options?: Intl.NumberFormatOptions) => string;
  formatCurrency: (amount: number, currency: string, options?: Intl.NumberFormatOptions) => string;
  formatRelativeTime: (value: number, unit: Intl.RelativeTimeFormatUnit) => string;
}

// Création du contexte
const TranslationContext = createContext<TranslationContextType | null>(null);

// Props pour le provider
interface TranslationProviderProps {
  children: ReactNode;
}

/**
 * Provider pour les traductions
 * Fournit les fonctions de traduction à tous les composants enfants
 */
export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {
  const translation = useTranslation();

  return <TranslationContext.Provider value={translation}>{children}</TranslationContext.Provider>;
};

/**
 * Hook pour utiliser les traductions dans les composants
 * @returns Fonctions et données pour l'internationalisation
 * @throws Error si utilisé en dehors d'un TranslationProvider
 */
export const useTranslationContext = (): TranslationContextType => {
  const context = useContext(TranslationContext);

  if (!context) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }

  return context;
};

export default TranslationProvider;
