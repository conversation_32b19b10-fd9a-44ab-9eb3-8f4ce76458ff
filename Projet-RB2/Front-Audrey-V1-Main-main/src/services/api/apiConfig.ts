/**
 * API Configuration
 * This file contains the configuration for the API client
 */

// Base API URL from environment variables
export const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Microservice URLs
export const SECURITY_URL = process.env.REACT_APP_SECURITY_URL || 'http://localhost:3001/api';
export const AGENT_IA_URL = process.env.REACT_APP_AGENT_IA_URL || 'http://localhost:3002/api';
export const SOCIAL_URL = process.env.REACT_APP_SOCIAL_URL || 'http://localhost:3003/api';
export const FINANCIAL_URL = process.env.REACT_APP_FINANCIAL_URL || 'http://localhost:3004/api';

// API Endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
    REQUEST_VERIFICATION_EMAIL: '/auth/request-verification-email',
    CHANGE_PASSWORD: '/auth/change-password',
    TWO_FACTOR: '/auth/two-factor',
  },

  // User endpoints
  USER: {
    PROFILE: '/users/profile',
    DETAILED_PROFILE: '/users/profile/detailed',
    UPDATE_PROFILE: '/users/profile',
    PREFERENCES: '/users/preferences',
    NOTIFICATIONS: '/users/notifications',
    ACTIVITY: '/users/activity',
  },

  // Retreat endpoints
  RETREAT: {
    BASE: '/retreats',
    FEATURED: '/retreats/featured',
    UPCOMING: '/retreats/upcoming',
    SEARCH: '/retreats/search',
    CATEGORIES: '/retreats/categories',
    REVIEWS: (id: string) => `/retreats/${id}/reviews`,
    BOOKING: (id: string) => `/retreats/${id}/booking`,
  },

  // Booking endpoints
  BOOKING: {
    BASE: '/bookings',
    USER_BOOKINGS: '/bookings/user',
    HOST_BOOKINGS: '/bookings/host',
    CANCEL: (id: string) => `/bookings/${id}/cancel`,
    CONFIRM: (id: string) => `/bookings/${id}/confirm`,
  },

  // Course endpoints
  COURSE: {
    BASE: '/courses',
    FEATURED: '/courses/featured',
    CATEGORIES: '/courses/categories',
    LESSONS: (id: string) => `/courses/${id}/lessons`,
    ENROLLMENTS: '/courses/enrollments',
  },

  // Journal endpoints
  JOURNAL: {
    BASE: '/journals',
    ENTRIES: (id: string) => `/journals/${id}/entries`,
  },

  // Partner endpoints
  PARTNERS: {
    BASE: '/partners',
    REGISTER: '/partners',
    STATUS: (id: string) => `/partners/${id}/status`,
    DOCUMENTS: (id: string) => `/partners/${id}/documents`,
    VERIFICATIONS: (id: string) => `/partners/${id}/verifications`,
    SERVICES: (id: string) => `/partners/${id}/services`,
  },

  // File endpoints
  FILES: {
    BASE: '/files',
    UPLOAD: '/files/upload',
    UPLOAD_MULTIPLE: '/files/upload/multiple',
  },

  // Security endpoints
  SECURITY: {
    ENCRYPT: '/security/encrypt',
    DECRYPT: '/security/decrypt',
    VALIDATE_FILE: '/security/validate-file',
    CERTIFICATES: '/security/certificates',
  },

  // Matching endpoints
  MATCHING: {
    PARTNERS: '/matching/partners',
    RETREATS: '/matching/retreats',
    PARTNERS_FOR_RETREAT: (retreatId: string) => `/matching/partners/retreat/${retreatId}`,
    RETREATS_FOR_PARTNER: (partnerId: string) => `/matching/retreats/partner/${partnerId}`,
    SCORE: (partnerId: string, retreatId: string) => `/matching/score/${partnerId}/${retreatId}`,
  },

  // Social endpoints
  SOCIAL: {
    FEED: '/social/feed',
    POSTS: '/social/posts',
    COMMENTS: '/social/comments',
    LIKES: '/social/likes',
    SHARES: '/social/shares',
    FOLLOW: '/social/follow',
    UNFOLLOW: '/social/unfollow',
  },

  // Financial endpoints
  FINANCIAL: {
    // Payment endpoints
    PAYMENTS: `${FINANCIAL_URL}/payments`,
    PAYMENT_INTENT: `${FINANCIAL_URL}/payments/intent`,
    CONFIRM_PAYMENT: `${FINANCIAL_URL}/payments/confirm`,
    PAYMENT_METHODS: `${FINANCIAL_URL}/payments/methods`,
    PAYMENT_HISTORY: `${FINANCIAL_URL}/payments/history`,
    AVAILABLE_PAYMENT_METHODS: `${FINANCIAL_URL}/payments/available-methods`,

    // Refund endpoints
    REFUNDS: `${FINANCIAL_URL}/refunds`,

    // Invoice endpoints
    INVOICES: `${FINANCIAL_URL}/invoices`,

    // Subscription endpoints
    SUBSCRIPTIONS: `${FINANCIAL_URL}/subscriptions`,
    SUBSCRIPTION_PLANS: `${FINANCIAL_URL}/subscriptions/plans`,

    // Promo code endpoints
    PROMO_CODES: `${FINANCIAL_URL}/promo-codes`,

    // Commission endpoints
    COMMISSIONS: `${FINANCIAL_URL}/commissions`,

    // Currency endpoints
    CURRENCIES: `${FINANCIAL_URL}/currencies`,
    EXCHANGE_RATES: `${FINANCIAL_URL}/currencies/exchange-rates`,
  },
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'You do not have permission to access this resource.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Validation error. Please check your input.',
  DEFAULT: 'An error occurred. Please try again.',
};
