import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';
import { User } from './userService'; // Assuming User type is available from userService

// Interfaces for Login
export interface LoginCredentials {
  email?: string;
  username?: string; // Support login with email or username
  password?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Interfaces for Registration
export interface RegistrationData {
  username?: string;
  email?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  // Add other fields as necessary for your registration form
}

// The response from registration might be similar to AuthResponse or just a success message/user object
export interface RegistrationResponse {
  user: User;
  token: string; // Optional: some registration flows log the user in directly
  refreshToken: string; // Optional
  message?: string; // e.g., "Registration successful, please verify your email."
}

// Interfaces for Password Reset
export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

// Assuming the backend sends a simple message on successful password reset
export interface PasswordResetResponse {
  message: string;
}

// Interfaces for Email Verification
export interface RequestVerificationEmailRequest {
  email: string;
}

// Assuming token is sent as a path parameter for verifyEmail, so no specific request body interface needed for it.
// Backend might return a success message or updated user object.
export interface EmailVerificationResponse {
  message: string;
  user?: User; // Optional: if backend returns updated user
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      if (!credentials.email && !credentials.username) {
        throw new Error('Email or username is required for login.');
      }
      if (!credentials.password) {
        throw new Error('Password is required for login.');
      }
      const response = await apiClient.post<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);
      return response;
    } catch (error) {
      console.error('Error during login:', error);
      throw error; // Rethrow to be handled by the caller (e.g., in UI)
    }
  }

  async register(userData: RegistrationData): Promise<RegistrationResponse> {
    try {
      // Basic validation, more comprehensive validation should be done with a schema library (e.g., Zod)
      if (!userData.email || !userData.password || !userData.username) {
        throw new Error('Username, email, and password are required for registration.');
      }
      const response = await apiClient.post<RegistrationResponse>(API_ENDPOINTS.AUTH.REGISTER, userData);
      return response;
    } catch (error) {
      console.error('Error during registration:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      // Optional: Call a backend endpoint to invalidate the session/token if available
      // await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT, {});
      // For now, primarily client-side, as AuthContext will handle clearing local storage
      console.log('Logout successful on client-side.');
    } catch (error) {
      console.error('Error during server-side logout:', error);
      // Don't necessarily throw, as client-side logout (clearing tokens) is the most critical part.
    }
  }

  async requestPasswordReset(email: string): Promise<PasswordResetResponse> {
    try {
      const response = await apiClient.post<PasswordResetResponse>(
        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        { email } as ForgotPasswordRequest
      );
      return response;
    } catch (error) {
      console.error('Error requesting password reset:', error);
      throw error;
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<PasswordResetResponse> {
    try {
      const response = await apiClient.post<PasswordResetResponse>(
        API_ENDPOINTS.AUTH.RESET_PASSWORD, // Assuming this endpoint expects token in URL or body
        { token, newPassword } as ResetPasswordRequest 
        // If token needs to be in URL, adjust API_ENDPOINTS.AUTH.RESET_PASSWORD or apiClient call accordingly
        // e.g., `${API_ENDPOINTS.AUTH.RESET_PASSWORD}/${token}` if token is a path param
      );
      return response;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  }

  async verifyEmail(token: string): Promise<EmailVerificationResponse> {
    try {
      // Token is typically part of the URL path, e.g., /auth/verify-email/:token
      // The API endpoint should be configured to extract the token from the path.
      const response = await apiClient.get<EmailVerificationResponse>(
        // Ensure API_ENDPOINTS.AUTH.VERIFY_EMAIL is defined in your apiConfig.ts
        // It might look like: VERIFY_EMAIL: '/auth/verify-email' (without the token part here)
        `${API_ENDPOINTS.AUTH.VERIFY_EMAIL}/${token}`
      );
      return response;
    } catch (error) {
      console.error('Error verifying email:', error);
      throw error;
    }
  }

  async requestVerificationEmail(email: string): Promise<EmailVerificationResponse> {
    try {
      // Ensure API_ENDPOINTS.AUTH.REQUEST_VERIFICATION_EMAIL is defined
      const response = await apiClient.post<EmailVerificationResponse>(
        API_ENDPOINTS.AUTH.REQUEST_VERIFICATION_EMAIL,
        { email } as RequestVerificationEmailRequest
      );
      return response;
    } catch (error) {
      console.error('Error requesting verification email:', error);
      throw error;
    }
  }

  // Placeholder for future methods from README
  // async changePassword(passwords: ChangePasswordData): Promise<void> { ... }
  // async manageTwoFactor(data: TwoFactorData): Promise<TwoFactorResponse> { ... }
}

export const authService = new AuthService();
