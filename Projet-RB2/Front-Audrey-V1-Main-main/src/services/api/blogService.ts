import { apiClient as api } from './apiClient';

// Define interfaces for Blog objects
export interface BlogPost {
  id: string;
  title: string;
  content: string;
  authorId: string;
  tags: string[];
  imageUrl?: string;
  publishDate?: string; // Should be Date type in a real app, but keeping string for simplicity based on current use
  status: 'draft' | 'published' | 'archived';
  metadata?: Record<string, any>;
  createdAt: string; // Or Date
  updatedAt: string; // Or Date
  comments?: BlogComment[];
  likes?: number; // Or an array of user IDs who liked
}

export interface BlogComment {
  id: string;
  postId: string;
  userId: string;
  content: string;
  createdAt: string; // Or Date
}

interface BlogFilters {
  tags?: string | string[];
  authorId?: string;
  status?: 'draft' | 'published' | 'archived';
  limit?: number;
  offset?: number;
}

interface CreateBlogPostParams {
  title: string;
  content: string;
  tags?: string[];
  imageUrl?: string;
  publishDate?: string;
  status?: 'draft' | 'published' | 'archived';
  metadata?: Record<string, any>;
}

// Add an interface for update parameters for clarity
export interface UpdateBlogPostParams {
  title?: string;
  content?: string;
  tags?: string[];
  imageUrl?: string;
  publishDate?: string;
  status?: 'draft' | 'published' | 'archived';
  metadata?: Record<string, any>;
}

class BlogService {
  /**
   * Récupère tous les articles de blog avec filtres optionnels
   * @param filters Filtres optionnels
   * @returns Liste des articles de blog
   */
  async getBlogPosts(filters?: BlogFilters): Promise<BlogPost[]> {
    try {
      // Convertir les tags en chaîne si c'est un tableau
      if (filters?.tags && Array.isArray(filters.tags)) {
        filters.tags = filters.tags.join(',');
      }

      const response = await api.get<BlogPost[]>('/social/blog', { params: filters });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      return [];
    }
  }

  /**
   * Récupère un article de blog par son ID
   * @param id ID de l'article de blog
   * @returns Détails de l'article de blog
   */
  async getBlogPostById(id: string): Promise<BlogPost | null> {
    try {
      const response = await api.get<BlogPost>(`/social/blog/${id}`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching blog post:', error);
      // Consider how to handle not found (e.g., return null or let error propagate)
      // For now, let's assume a 404 might throw and be caught here.
      // If the API returns a specific structure for not found (e.g. empty object), adjust accordingly.
      return null; 
    }
  }

  /**
   * Crée un nouvel article de blog
   * @param params Paramètres de l'article de blog
   * @returns Article de blog créé
   */
  async createBlogPost(params: CreateBlogPostParams): Promise<BlogPost> {
    try {
      const response = await api.post<BlogPost>('/social/blog', params);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error creating blog post:', error);
      throw error;
    }
  }

  /**
   * Met à jour un article de blog existant
   * @param id ID de l'article de blog
   * @param updateData Données à mettre à jour
   * @returns Article de blog mis à jour
   */
  async updateBlogPost(id: string, updateData: UpdateBlogPostParams): Promise<BlogPost> {
    try {
      const response = await api.patch<BlogPost>(`/social/blog/${id}`, updateData);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error updating blog post:', error);
      throw error;
    }
  }

  /**
   * Supprime un article de blog
   * @param id ID de l'article de blog
   * @returns Résultat de l'opération
   */
  async deleteBlogPost(id: string): Promise<void> { // Typically delete might not return the object, or a success status
    try {
      await api.delete<void>(`/social/blog/${id}`); // Assuming API returns 204 No Content or similar
      // return response; // No data to return if it's void
    } catch (error) {
      console.error('Error deleting blog post:', error);
      throw error;
    }
  }

  /**
   * Récupère les commentaires d'un article de blog
   * @param id ID de l'article de blog
   * @returns Liste des commentaires
   */
  async getBlogPostComments(id: string): Promise<BlogComment[]> {
    try {
      const response = await api.get<BlogComment[]>(`/social/blog/${id}/comments`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching blog post comments:', error);
      return [];
    }
  }

  /**
   * Ajoute un commentaire à un article de blog
   * @param id ID de l'article de blog
   * @param content Contenu du commentaire
   * @returns Commentaire ajouté
   */
  async addBlogPostComment(id: string, content: string, userId: string): Promise<BlogComment> { // Added userId as it's common for creating comments
    try {
      const response = await api.post<BlogComment>(`/social/blog/${id}/comments`, { content, userId }); // Assuming API needs userId
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error adding blog post comment:', error);
      throw error;
    }
  }

  /**
   * Ajoute un like à un article de blog
   * @param id ID de l'article de blog
   * @returns Résultat de l'opération
   */
  async likeBlogPost(id: string): Promise<{ success: boolean; likes: number }> { // Assuming a more specific response
    try {
      const response = await api.post<{ success: boolean; likes: number }>(`/social/blog/${id}/like`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error liking blog post:', error);
      throw error;
    }
  }

  /**
   * Retire un like à un article de blog
   * @param id ID de l'article de blog
   * @returns Résultat de l'opération
   */
  async unlikeBlogPost(id: string): Promise<{ success: boolean; likes: number }> { // Assuming a more specific response
    try {
      const response = await api.post<{ success: boolean; likes: number }>(`/social/blog/${id}/unlike`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error unliking blog post:', error);
      throw error;
    }
  }

  /**
   * Récupère les articles de blog publiés
   * @returns Liste des articles de blog publiés
   */
  async getPublishedBlogPosts(): Promise<BlogPost[]> {
    try {
      const response = await api.get<BlogPost[]>('/social/blog', { params: { status: 'published' } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching published blog posts:', error);
      return [];
    }
  }

  /**
   * Récupère les articles de blog d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Liste des articles de blog de l'utilisateur
   */
  async getUserBlogPosts(userId: string): Promise<BlogPost[]> {
    try {
      const response = await api.get<BlogPost[]>('/social/blog', { params: { authorId: userId } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching user blog posts:', error);
      return [];
    }
  }

  /**
   * Récupère les articles de blog par tag
   * @param tag Tag à rechercher
   * @returns Liste des articles de blog avec le tag spécifié
   */
  async getBlogPostsByTag(tag: string): Promise<BlogPost[]> {
    try {
      const response = await api.get<BlogPost[]>('/social/blog', { params: { tags: tag } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching blog posts by tag:', error);
      return [];
    }
  }
}

export const blogService = new BlogService();
