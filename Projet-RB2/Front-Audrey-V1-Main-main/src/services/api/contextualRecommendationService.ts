import { apiClient } from './apiClient';

/**
 * Interface pour le contexte utilisateur
 */
export interface UserContext {
  /** ID de l'utilisateur */
  userId: string;
  
  /** Localisation */
  location?: {
    /** Pays */
    country: string;
    
    /** Ville */
    city: string;
    
    /** Coordonnées */
    coordinates: {
      /** Latitude */
      latitude: number;
      
      /** Longitude */
      longitude: number;
    };
  };
  
  /** Données météorologiques */
  weather?: {
    /** Température en degrés Celsius */
    temperature: number;
    
    /** Condition météorologique (ensoleillé, nuageux, pluvieux, etc.) */
    condition: string;
    
    /** Humidité en pourcentage */
    humidity: number;
    
    /** Vitesse du vent en km/h */
    windSpeed: number;
    
    /** Précipitations en mm */
    precipitation: number;
    
    /** Indice UV */
    uvIndex: number;
    
    /** Heure de lever du soleil */
    sunrise: string;
    
    /** Heure de coucher du soleil */
    sunset: string;
  };
  
  /** Événements locaux */
  localEvents?: Array<{
    /** ID de l'événement */
    id: string;
    
    /** Nom de l'événement */
    name: string;
    
    /** Description de l'événement */
    description: string;
    
    /** Date de début */
    startDate: string;
    
    /** Date de fin */
    endDate: string;
    
    /** Lieu */
    location: string;
    
    /** Catégorie */
    category: string;
    
    /** URL de l'image */
    imageUrl?: string;
    
    /** URL de l'événement */
    eventUrl?: string;
  }>;
  
  /** Données de saison */
  seasonData?: {
    /** Saison actuelle */
    currentSeason: 'spring' | 'summer' | 'autumn' | 'winter';
    
    /** Saison prochaine */
    nextSeason: 'spring' | 'summer' | 'autumn' | 'winter';
    
    /** Date de début de la saison actuelle */
    currentSeasonStartDate: string;
    
    /** Date de fin de la saison actuelle */
    currentSeasonEndDate: string;
    
    /** Jours restants dans la saison actuelle */
    daysRemainingInCurrentSeason: number;
  };
  
  /** Données culturelles */
  culturalData?: {
    /** Jours fériés */
    holidays: Array<{
      name: string;
      date: string;
      description: string;
      country: string;
    }>;
    
    /** Événements culturels */
    culturalEvents: Array<{
      name: string;
      startDate: string;
      endDate: string;
      description: string;
      country: string;
    }>;
    
    /** Festivals */
    festivals: Array<{
      name: string;
      startDate: string;
      endDate: string;
      description: string;
      location: string;
    }>;
  };
  
  /** Appareil */
  device?: {
    /** Type d'appareil */
    type: 'mobile' | 'tablet' | 'desktop';
    
    /** Système d'exploitation */
    os: string;
    
    /** Navigateur */
    browser: string;
  };
  
  /** Heure locale */
  localTime?: {
    /** Heure */
    hour: number;
    
    /** Minute */
    minute: number;
    
    /** Jour de la semaine */
    dayOfWeek: number;
    
    /** Période de la journée */
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  };
  
  /** Dernière mise à jour */
  lastUpdated: string;
}

/**
 * Interface pour les options de recommandation contextuelle
 */
export interface ContextualRecommendationOptions {
  /** Facteur d'influence du contexte (0-1) */
  contextFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Types de contexte à prendre en compte */
  contextTypes?: Array<'location' | 'weather' | 'season' | 'time' | 'events' | 'cultural'>;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les options de recommandation saisonnière
 */
export interface SeasonalRecommendationOptions {
  /** Facteur d'influence de la saison (0-1) */
  seasonFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Inclure les recommandations pour la saison suivante */
  includeNextSeason?: boolean;
  
  /** Facteur d'influence de la saison suivante (0-1) */
  nextSeasonFactor?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Service pour les recommandations contextuelles et saisonnières
 */
class ContextualRecommendationService {
  /**
   * Récupère le contexte de l'utilisateur courant
   * @param forceRefresh Forcer le rafraîchissement du contexte
   * @returns Contexte utilisateur
   */
  async getMyContext(forceRefresh: boolean = false): Promise<UserContext> {
    try {
      const params: Record<string, any> = {};
      
      if (forceRefresh) {
        params.forceRefresh = true;
      }
      
      const response = await apiClient.get('/recommendation/contextual/my-context', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du contexte utilisateur:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations contextuelles pour l'utilisateur courant
   * @param options Options de recommandation
   * @returns Recommandations contextuelles
   */
  async getContextualRecommendations(options?: ContextualRecommendationOptions): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (options?.contextFactor !== undefined) {
        params.contextFactor = options.contextFactor;
      }
      
      if (options?.maxRecommendations !== undefined) {
        params.maxRecommendations = options.maxRecommendations;
      }
      
      const response = await apiClient.get('/recommendation/contextual/my-recommendations', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations contextuelles:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations saisonnières pour l'utilisateur courant
   * @param options Options de recommandation
   * @returns Recommandations saisonnières
   */
  async getSeasonalRecommendations(options?: SeasonalRecommendationOptions): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (options?.seasonFactor !== undefined) {
        params.seasonFactor = options.seasonFactor;
      }
      
      if (options?.includeNextSeason !== undefined) {
        params.includeNextSeason = options.includeNextSeason;
      }
      
      if (options?.maxRecommendations !== undefined) {
        params.maxRecommendations = options.maxRecommendations;
      }
      
      const response = await apiClient.get('/recommendation/contextual/my-seasonal', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations saisonnières:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations pour la saison actuelle
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations pour la saison actuelle
   */
  async getCurrentSeasonRecommendations(maxRecommendations?: number): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (maxRecommendations !== undefined) {
        params.maxRecommendations = maxRecommendations;
      }
      
      const response = await apiClient.get('/recommendation/contextual/current-season', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations pour la saison actuelle:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations pour la saison suivante
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations pour la saison suivante
   */
  async getNextSeasonRecommendations(maxRecommendations?: number): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (maxRecommendations !== undefined) {
        params.maxRecommendations = maxRecommendations;
      }
      
      const response = await apiClient.get('/recommendation/contextual/next-season', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations pour la saison suivante:', error);
      throw error;
    }
  }
  
  /**
   * Traduit le nom d'une saison
   * @param season Saison
   * @returns Nom traduit
   */
  getSeasonName(season: 'spring' | 'summer' | 'autumn' | 'winter'): string {
    switch (season) {
      case 'spring':
        return 'Printemps';
      case 'summer':
        return 'Été';
      case 'autumn':
        return 'Automne';
      case 'winter':
        return 'Hiver';
      default:
        return '';
    }
  }
  
  /**
   * Récupère l'icône d'une saison
   * @param season Saison
   * @returns Nom de l'icône
   */
  getSeasonIcon(season: 'spring' | 'summer' | 'autumn' | 'winter'): string {
    switch (season) {
      case 'spring':
        return 'flower';
      case 'summer':
        return 'sun';
      case 'autumn':
        return 'leaf';
      case 'winter':
        return 'snowflake';
      default:
        return '';
    }
  }
  
  /**
   * Récupère la couleur d'une saison
   * @param season Saison
   * @returns Classe CSS de couleur
   */
  getSeasonColorClass(season: 'spring' | 'summer' | 'autumn' | 'winter'): string {
    switch (season) {
      case 'spring':
        return 'text-green-500';
      case 'summer':
        return 'text-yellow-500';
      case 'autumn':
        return 'text-orange-500';
      case 'winter':
        return 'text-blue-500';
      default:
        return '';
    }
  }
  
  /**
   * Récupère la classe CSS de fond d'une saison
   * @param season Saison
   * @returns Classe CSS de fond
   */
  getSeasonBackgroundClass(season: 'spring' | 'summer' | 'autumn' | 'winter'): string {
    switch (season) {
      case 'spring':
        return 'bg-green-100';
      case 'summer':
        return 'bg-yellow-100';
      case 'autumn':
        return 'bg-orange-100';
      case 'winter':
        return 'bg-blue-100';
      default:
        return '';
    }
  }
}

export const contextualRecommendationService = new ContextualRecommendationService();
