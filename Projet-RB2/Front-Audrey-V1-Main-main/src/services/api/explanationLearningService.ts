import { apiClient } from './apiClient';

/**
 * Interface pour la configuration de l'intégration des tests A/B et de l'apprentissage continu
 */
export interface ExplanationLearningConfig {
  /** Activer/désactiver l'intégration */
  enabled: boolean;
  
  /** Nombre minimum d'interactions pour déclencher une optimisation */
  minInteractionsForOptimization: number;
  
  /** Niveau de confiance minimum pour appliquer les optimisations */
  minConfidenceLevel: number;
  
  /** Intervalle d'optimisation (en heures) */
  optimizationInterval: number;
  
  /** Taux d'apprentissage pour les mises à jour des poids des facteurs */
  learningRate: number;
  
  /** Activer/désactiver l'optimisation des templates d'explication */
  templateOptimizationEnabled: boolean;
  
  /** Activer/désactiver l'optimisation des poids des facteurs */
  factorWeightOptimizationEnabled: boolean;
  
  /** Activer/désactiver le déploiement automatique des variantes gagnantes */
  autoDeployWinners: boolean;
  
  /** Seuil d'amélioration pour le déploiement automatique */
  autoDeployThreshold: number;
}

/**
 * Interface pour les métriques de l'intégration des tests A/B et de l'apprentissage continu
 */
export interface ExplanationLearningMetrics {
  /** Nombre total d'optimisations */
  totalOptimizations: number;
  
  /** Date de la dernière optimisation */
  lastOptimizationDate: string | null;
  
  /** Amélioration moyenne */
  averageImprovement: number;
  
  /** Nombre de mises à jour des poids des facteurs */
  factorWeightUpdates: number;
  
  /** Nombre de mises à jour des templates */
  templateUpdates: number;
  
  /** Nombre de variantes déployées */
  deployedVariants: number;
  
  /** Événements d'apprentissage récents */
  learningEvents: Array<{
    timestamp: string;
    description: string;
    improvement: number;
    source: string;
  }>;
  
  /** Historique des performances */
  performanceHistory: Array<{
    date: string;
    interactionRate: number;
    conversionRate: number;
  }>;
}

/**
 * Service pour l'intégration des tests A/B et de l'apprentissage continu
 */
class ExplanationLearningService {
  /**
   * Récupère la configuration actuelle
   * @returns Configuration
   */
  async getConfiguration(): Promise<ExplanationLearningConfig> {
    try {
      const response = await apiClient.get('/explanation-learning/config');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de la configuration:', error);
      throw error;
    }
  }
  
  /**
   * Met à jour la configuration
   * @param config Nouvelle configuration
   * @returns Configuration mise à jour
   */
  async updateConfiguration(config: ExplanationLearningConfig): Promise<ExplanationLearningConfig> {
    try {
      const response = await apiClient.put('/explanation-learning/config', config);
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la configuration:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les métriques
   * @returns Métriques
   */
  async getMetrics(): Promise<ExplanationLearningMetrics> {
    try {
      const response = await apiClient.get('/explanation-learning/metrics');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques:', error);
      throw error;
    }
  }
  
  /**
   * Déclenche une optimisation manuelle
   * @returns Résultat de l'optimisation
   */
  async triggerOptimization(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post('/explanation-learning/optimize');
      return response;
    } catch (error) {
      console.error('Erreur lors du déclenchement de l\'optimisation:', error);
      throw error;
    }
  }
  
  /**
   * Récupère l'historique des optimisations
   * @param startDate Date de début (optionnelle)
   * @param endDate Date de fin (optionnelle)
   * @returns Historique des optimisations
   */
  async getOptimizationHistory(
    startDate?: string,
    endDate?: string,
  ): Promise<Array<{
    timestamp: string;
    testId: string;
    testName: string;
    winnerVariantId: string;
    winnerVariantName: string;
    improvement: number;
    confidenceLevel: number;
    appliedChanges: string[];
  }>> {
    try {
      const params: any = {};
      if (startDate) params.startDate = startDate;
      if (endDate) params.endDate = endDate;
      
      const response = await apiClient.get('/explanation-learning/history', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des optimisations:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les facteurs optimisés
   * @returns Facteurs optimisés
   */
  async getOptimizedFactors(): Promise<Array<{
    type: string;
    name: string;
    originalWeight: number;
    optimizedWeight: number;
    lastUpdated: string;
  }>> {
    try {
      const response = await apiClient.get('/explanation-learning/factors');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des facteurs optimisés:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les templates optimisés
   * @returns Templates optimisés
   */
  async getOptimizedTemplates(): Promise<Array<{
    id: string;
    factorType: string;
    originalTemplate: string;
    optimizedTemplate: string;
    lastUpdated: string;
  }>> {
    try {
      const response = await apiClient.get('/explanation-learning/templates');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des templates optimisés:', error);
      throw error;
    }
  }
  
  /**
   * Réinitialise les optimisations
   * @returns Résultat de la réinitialisation
   */
  async resetOptimizations(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post('/explanation-learning/reset');
      return response;
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des optimisations:', error);
      throw error;
    }
  }
}

export const explanationLearningService = new ExplanationLearningService();
