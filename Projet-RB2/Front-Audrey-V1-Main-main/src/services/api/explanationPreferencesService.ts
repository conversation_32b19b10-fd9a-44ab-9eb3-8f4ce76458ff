import { apiClient } from './apiClient';

/**
 * Énumération des styles d'explication
 */
export enum ExplanationStyle {
  /** Style technique avec des détails précis */
  TECHNICAL = 'TECHNICAL',
  
  /** Style conversationnel et amical */
  CONVERSATIONAL = 'CONVERSATIONAL',
  
  /** Style concis et direct */
  CONCISE = 'CONCISE',
  
  /** Style narratif et descriptif */
  NARRATIVE = 'NARRATIVE',
  
  /** Style éducatif avec des informations supplémentaires */
  EDUCATIONAL = 'EDUCATIONAL',
}

/**
 * Énumération des niveaux de détail
 */
export enum DetailLevel {
  /** Très peu de détails, juste l'essentiel */
  MINIMAL = 'MINIMAL',
  
  /** Niveau de détail modéré */
  MODERATE = 'MODERATE',
  
  /** Niveau de détail élevé */
  DETAILED = 'DETAILED',
  
  /** Niveau de détail très élevé avec des informations supplémentaires */
  COMPREHENSIVE = 'COMPREHENSIVE',
}

/**
 * Énumération des formats d'explication
 */
export enum ExplanationFormat {
  /** Texte simple */
  TEXT = 'TEXT',
  
  /** Représentation visuelle (graphiques, icônes) */
  VISUAL = 'VISUAL',
  
  /** Combinaison de texte et d'éléments visuels */
  MIXED = 'MIXED',
  
  /** Format interactif permettant d'explorer les détails */
  INTERACTIVE = 'INTERACTIVE',
}

/**
 * Interface pour les préférences culturelles
 */
export interface CulturalPreferences {
  /** Région culturelle (ex: 'western', 'eastern', 'african', etc.) */
  region?: string;
  
  /** Préférences de style de communication */
  communicationStyle?: 'direct' | 'indirect' | 'contextual';
  
  /** Préférences pour les exemples et métaphores */
  examplePreferences?: string[];
  
  /** Sensibilité aux références culturelles */
  culturalSensitivity?: 'low' | 'medium' | 'high';
}

/**
 * Interface pour les préférences d'accessibilité
 */
export interface AccessibilityPreferences {
  /** Préférence pour un texte à contraste élevé */
  highContrast?: boolean;
  
  /** Préférence pour un texte plus grand */
  largeText?: boolean;
  
  /** Préférence pour des descriptions alternatives des éléments visuels */
  screenReaderOptimized?: boolean;
  
  /** Préférence pour éviter les animations */
  reduceMotion?: boolean;
  
  /** Préférence pour un langage simplifié */
  simplifiedLanguage?: boolean;
}

/**
 * Interface pour les préférences d'explication
 */
export interface ExplanationPreferences {
  /** ID de l'utilisateur */
  userId: string;
  
  /** Style d'explication préféré */
  preferredStyle: ExplanationStyle;
  
  /** Niveau de détail préféré */
  detailLevel: DetailLevel;
  
  /** Format d'explication préféré */
  preferredFormat: ExplanationFormat[];
  
  /** Facteurs à mettre en évidence */
  highlightedFactors: string[];
  
  /** Facteurs à masquer */
  hiddenFactors: string[];
  
  /** Langue préférée pour les explications */
  language: string;
  
  /** Préférences culturelles */
  culturalPreferences: CulturalPreferences;
  
  /** Préférences d'accessibilité */
  accessibilityPreferences: AccessibilityPreferences;
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
  
  /** Date de création */
  createdAt: string;
  
  /** Date de mise à jour */
  updatedAt: string;
}

/**
 * Interface pour la mise à jour des préférences d'explication
 */
export interface UpdateExplanationPreferencesRequest {
  /** Style d'explication préféré */
  preferredStyle?: ExplanationStyle;
  
  /** Niveau de détail préféré */
  detailLevel?: DetailLevel;
  
  /** Format d'explication préféré */
  preferredFormat?: ExplanationFormat[];
  
  /** Facteurs à mettre en évidence */
  highlightedFactors?: string[];
  
  /** Facteurs à masquer */
  hiddenFactors?: string[];
  
  /** Langue préférée pour les explications */
  language?: string;
  
  /** Préférences culturelles */
  culturalPreferences?: CulturalPreferences;
  
  /** Préférences d'accessibilité */
  accessibilityPreferences?: AccessibilityPreferences;
  
  /** Métadonnées supplémentaires */
  metadata?: Record<string, any>;
}

/**
 * Service pour gérer les préférences d'explication
 */
class ExplanationPreferencesService {
  /**
   * Récupère les préférences d'explication de l'utilisateur connecté
   * @returns Préférences d'explication
   */
  async getUserPreferences(): Promise<ExplanationPreferences> {
    try {
      const response = await apiClient.get('/recommendation/explanation-preferences');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des préférences d\'explication:', error);
      throw error;
    }
  }
  
  /**
   * Met à jour les préférences d'explication de l'utilisateur connecté
   * @param preferences Nouvelles préférences
   * @returns Préférences d'explication mises à jour
   */
  async updateUserPreferences(preferences: UpdateExplanationPreferencesRequest): Promise<ExplanationPreferences> {
    try {
      const response = await apiClient.put('/recommendation/explanation-preferences', preferences);
      return response;
    } catch (error) {
      console.error('Erreur lors de la mise à jour des préférences d\'explication:', error);
      throw error;
    }
  }
  
  /**
   * Réinitialise les préférences d'explication de l'utilisateur connecté
   * @returns Préférences d'explication par défaut
   */
  async resetUserPreferences(): Promise<ExplanationPreferences> {
    try {
      const response = await apiClient.delete('/recommendation/explanation-preferences');
      return this.getUserPreferences();
    } catch (error) {
      console.error('Erreur lors de la réinitialisation des préférences d\'explication:', error);
      throw error;
    }
  }
}

export const explanationPreferencesService = new ExplanationPreferencesService();
