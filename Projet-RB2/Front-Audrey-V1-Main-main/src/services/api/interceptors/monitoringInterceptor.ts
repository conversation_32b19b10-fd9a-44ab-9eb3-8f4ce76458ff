import { AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';

// Interface pour les métriques d'API
interface ApiMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  requestDurations: Record<string, number[]>;
  averageRequestDuration: Record<string, number>;
  errorRates: Record<string, number>;
}

// Initialisation des métriques
const metrics: ApiMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  requestDurations: {},
  averageRequestDuration: {},
  errorRates: {},
};

// Fonction pour calculer la moyenne d'un tableau de nombres
const calculateAverage = (arr: number[]): number => {
  if (arr.length === 0) return 0;
  const sum = arr.reduce((a, b) => a + b, 0);
  return sum / arr.length;
};

// Fonction pour mettre à jour les métriques d'erreur
const updateErrorRates = (): void => {
  Object.keys(metrics.requestDurations).forEach((endpoint) => {
    const totalEndpointRequests = metrics.requestDurations[endpoint].length;
    const failedEndpointRequests = metrics.failedRequests;
    metrics.errorRates[endpoint] =
      totalEndpointRequests > 0 ? failedEndpointRequests / totalEndpointRequests : 0;
  });
};

// Fonction pour mettre à jour les durées moyennes
const updateAverageDurations = (): void => {
  Object.keys(metrics.requestDurations).forEach((endpoint) => {
    metrics.averageRequestDuration[endpoint] = calculateAverage(metrics.requestDurations[endpoint]);
  });
};

// Fonction pour normaliser l'URL de l'endpoint
const normalizeEndpoint = (url: string): string => {
  // Supprimer les paramètres de requête
  const baseUrl = url.split('?')[0];

  // Remplacer les IDs dynamiques par un placeholder
  return baseUrl.replace(/\/[0-9a-f]{8,}(?:-[0-9a-f]{4,}){3,}-[0-9a-f]{12,}/gi, '/:id');
};

// Interface pour les requêtes avec métadonnées
interface InternalAxiosRequestConfigWithMetadata extends InternalAxiosRequestConfig {
  metadata: { startTime: number };
}

// Intercepteur de requête
export const requestInterceptor = (
  config: InternalAxiosRequestConfig
): InternalAxiosRequestConfig => {
  // Ajouter un timestamp à la requête pour mesurer la durée
  (config as InternalAxiosRequestConfigWithMetadata).metadata = { startTime: new Date().getTime() };

  // Incrémenter le compteur de requêtes
  metrics.totalRequests++;

  return config;
};

// Intercepteur de réponse
export const responseInterceptor = (response: AxiosResponse): AxiosResponse => {
  const config = response.config as InternalAxiosRequestConfigWithMetadata;

  // Calculer la durée de la requête
  const endTime = new Date().getTime();
  const duration = endTime - config.metadata.startTime;

  // Normaliser l'endpoint
  const endpoint = normalizeEndpoint(response.config.url || '');

  // Mettre à jour les métriques
  metrics.successfulRequests++;

  // Initialiser le tableau de durées pour cet endpoint s'il n'existe pas
  if (!metrics.requestDurations[endpoint]) {
    metrics.requestDurations[endpoint] = [];
  }

  // Ajouter la durée de cette requête
  metrics.requestDurations[endpoint].push(duration);

  // Mettre à jour les moyennes et les taux d'erreur
  updateAverageDurations();
  updateErrorRates();

  // Envoyer les métriques à un service de monitoring (si configuré)
  sendMetricsToMonitoring();

  return response;
};

// Intercepteur d'erreur
export const errorInterceptor = (error: AxiosError): Promise<never> => {
  // Incrémenter le compteur d'erreurs
  metrics.failedRequests++;

  // Si l'erreur a une configuration, calculer la durée
  if (error.config && (error.config as InternalAxiosRequestConfigWithMetadata).metadata) {
    const configWithMetadata = error.config as InternalAxiosRequestConfigWithMetadata;
    const endTime = new Date().getTime();
    const duration = endTime - configWithMetadata.metadata.startTime;

    // Normaliser l'endpoint
    const endpoint = normalizeEndpoint(configWithMetadata.url || '');

    // Initialiser le tableau de durées pour cet endpoint s'il n'existe pas
    if (!metrics.requestDurations[endpoint]) {
      metrics.requestDurations[endpoint] = [];
    }

    // Ajouter la durée de cette requête
    metrics.requestDurations[endpoint].push(duration);

    // Mettre à jour les moyennes et les taux d'erreur
    updateAverageDurations();
    updateErrorRates();
  }

  // Envoyer les métriques à un service de monitoring (si configuré)
  sendMetricsToMonitoring();

  // Enregistrer l'erreur dans un service de logging (si configuré)
  logError(error);

  return Promise.reject(error);
};

// Fonction pour envoyer les métriques à un service de monitoring
const sendMetricsToMonitoring = (): void => {
  // Cette fonction pourrait envoyer les métriques à un service comme Datadog, New Relic, etc.
  // Pour l'instant, nous les affichons simplement dans la console en développement
  if (process.env.NODE_ENV === 'development') {
    console.debug('API Metrics:', {
      totalRequests: metrics.totalRequests,
      successRate:
        metrics.totalRequests > 0 ? (metrics.successfulRequests / metrics.totalRequests) * 100 : 0,
      averageDurations: metrics.averageRequestDuration,
      errorRates: metrics.errorRates,
    });
  }
};

// Fonction pour enregistrer les erreurs
const logError = (error: AxiosError): void => {
  // Cette fonction pourrait envoyer les erreurs à un service comme Sentry, LogRocket, etc.
  // Pour l'instant, nous les affichons simplement dans la console
  console.error('API Error:', {
    url: error.config?.url,
    method: error.config?.method,
    status: error.response?.status,
    statusText: error.response?.statusText,
    data: error.response?.data as unknown,
  });
};

// Fonction pour obtenir les métriques actuelles
export const getApiMetrics = (): ApiMetrics => {
  return { ...metrics };
};

// Fonction pour réinitialiser les métriques
export const resetApiMetrics = (): void => {
  metrics.totalRequests = 0;
  metrics.successfulRequests = 0;
  metrics.failedRequests = 0;
  metrics.requestDurations = {};
  metrics.averageRequestDuration = {};
  metrics.errorRates = {};
};
