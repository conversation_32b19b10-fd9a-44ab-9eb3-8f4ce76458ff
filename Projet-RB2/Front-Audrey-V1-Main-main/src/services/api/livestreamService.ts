import { apiClient as api } from './apiClient';

// Define interfaces for Livestream objects
export interface Livestream {
  id: string;
  title: string;
  description: string;
  hostId: string;
  scheduledStartTime?: string; // Or Date
  actualStartTime?: string; // Or Date
  actualEndTime?: string; // Or Date
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  isPrivate?: boolean;
  participants?: string[]; // Array of user IDs
  viewerCount?: number;
  metadata?: Record<string, any>;
  streamUrl?: string; // URL to view the stream
  recordingUrl?: string; // URL of the recorded stream, if available
}

interface LivestreamFilters {
  status?: string;
  hostId?: string;
  limit?: number;
  offset?: number;
}

interface CreateLivestreamParams {
  title: string;
  description: string;
  scheduledStartTime?: string; // Or Date
  isPrivate?: boolean;
  participants?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateLivestreamParams {
  title?: string;
  description?: string;
  scheduledStartTime?: string;
  isPrivate?: boolean;
  participants?: string[];
  status?: 'scheduled' | 'live' | 'ended' | 'cancelled';
  metadata?: Record<string, any>;
}

interface LivestreamMessage {
  id?: string; // Usually assigned by the backend
  userId: string; // Who sent the message
  content: string;
  type?: 'text' | 'question' | 'reaction';
  timestamp?: string; // Or Date
}

interface SendLivestreamMessageParams {
    content: string;
    type?: 'text' | 'question' | 'reaction';
    // userId will likely be inferred from auth token on backend
}

class LivestreamService {
  /**
   * Récupère tous les livestreams avec filtres optionnels
   * @param filters Filtres optionnels
   * @returns Liste des livestreams
   */
  async getLivestreams(filters?: LivestreamFilters): Promise<Livestream[]> {
    try {
      const response = await api.get<Livestream[]>('/social/livestream', { params: filters });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching livestreams:', error);
      return [];
    }
  }

  /**
   * Récupère un livestream par son ID
   * @param id ID du livestream
   * @returns Détails du livestream
   */
  async getLivestreamById(id: string): Promise<Livestream | null> {
    try {
      const response = await api.get<Livestream>(`/social/livestream/${id}`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching livestream:', error);
      return null;
    }
  }

  /**
   * Crée un nouveau livestream
   * @param params Paramètres du livestream
   * @returns Livestream créé
   */
  async createLivestream(params: CreateLivestreamParams): Promise<Livestream> {
    try {
      const response = await api.post<Livestream>('/social/livestream', params);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error creating livestream:', error);
      throw error;
    }
  }

  /**
   * Met à jour un livestream existant
   * @param id ID du livestream
   * @param updateData Données à mettre à jour
   * @returns Livestream mis à jour
   */
  async updateLivestream(id: string, updateData: UpdateLivestreamParams): Promise<Livestream> {
    try {
      const response = await api.patch<Livestream>(`/social/livestream/${id}`, updateData);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error updating livestream:', error);
      throw error;
    }
  }

  /**
   * Démarre un livestream
   * @param id ID du livestream
   * @returns Résultat de l'opération
   */
  async startLivestream(id: string): Promise<{ success: boolean; livestream?: Livestream }> { // Assuming a more specific response
    try {
      const response = await api.post<{ success: boolean; livestream?: Livestream }>(`/social/livestream/${id}/start`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error starting livestream:', error);
      throw error;
    }
  }

  /**
   * Termine un livestream
   * @param id ID du livestream
   * @returns Résultat de l'opération
   */
  async endLivestream(id: string): Promise<{ success: boolean; livestream?: Livestream }> { // Assuming a more specific response
    try {
      const response = await api.post<{ success: boolean; livestream?: Livestream }>(`/social/livestream/${id}/end`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error ending livestream:', error);
      throw error;
    }
  }

  /**
   * Récupère les messages d'un livestream
   * @param id ID du livestream
   * @returns Liste des messages
   */
  async getLivestreamMessages(id: string): Promise<LivestreamMessage[]> {
    try {
      const response = await api.get<LivestreamMessage[]>(`/social/livestream/${id}/messages`);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching livestream messages:', error);
      return [];
    }
  }

  /**
   * Envoie un message dans un livestream
   * @param id ID du livestream
   * @param message Message à envoyer
   * @returns Message envoyé
   */
  async sendLivestreamMessage(id: string, message: SendLivestreamMessageParams): Promise<LivestreamMessage> {
    try {
      const response = await api.post<LivestreamMessage>(`/social/livestream/${id}/messages`, message);
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error sending livestream message:', error);
      throw error;
    }
  }

  /**
   * Récupère les livestreams en cours
   * @returns Liste des livestreams en cours
   */
  async getLiveLivestreams(): Promise<Livestream[]> {
    try {
      const response = await api.get<Livestream[]>('/social/livestream', { params: { status: 'live' } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching live livestreams:', error);
      return [];
    }
  }

  /**
   * Récupère les livestreams programmés
   * @returns Liste des livestreams programmés
   */
  async getScheduledLivestreams(): Promise<Livestream[]> {
    try {
      const response = await api.get<Livestream[]>('/social/livestream', { params: { status: 'scheduled' } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching scheduled livestreams:', error);
      return [];
    }
  }

  /**
   * Récupère les livestreams d'un utilisateur
   * @param userId ID de l'utilisateur
   * @returns Liste des livestreams de l'utilisateur
   */
  async getUserLivestreams(userId: string): Promise<Livestream[]> {
    try {
      const response = await api.get<Livestream[]>('/social/livestream', { params: { hostId: userId } });
      return response; // apiClient returns response.data directly
    } catch (error) {
      console.error('Error fetching user livestreams:', error);
      return [];
    }
  }
}

export const livestreamService = new LivestreamService();
