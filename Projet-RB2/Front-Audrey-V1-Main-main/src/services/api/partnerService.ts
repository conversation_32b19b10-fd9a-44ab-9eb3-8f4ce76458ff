import { apiClient } from './apiClient';
import { API_ENDPOINTS } from './apiConfig';

export interface PartnerRegistrationData {
  userId: string;
  companyName: string;
  type: 'PREMIUM_CERTIFIED' | 'CERTIFIED' | 'STANDARD';
  category: 'ORGANIZER' | 'TRAVEL_AGENCY' | 'CATERING' | 'GUIDE' | 'TRANSPORT' | 'WELLNESS' | 'INSURANCE' | 'ACCOMMODATION' | 'EQUIPMENT' | 'OTHER';
  description: string;
  logo?: string;
  website?: string;
  specializations: string[];
  languages: string[];
  contactInfo: {
    name: string;
    email: string;
    phone: string;
    position?: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  coverageAreas?: Record<string, any>;
  insurance?: Record<string, any>;
}

export interface Partner {
  id: string;
  userId: string;
  companyName: string;
  type: string;
  category: string;
  description: string;
  status: string;
  logo?: string;
  website?: string;
  specializations: string[];
  languages: string[];
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    image?: string;
  };
  documents?: PartnerDocument[];
}

export interface PartnerDocument {
  id: string;
  partnerId: string;
  type: string;
  description: string;
  fileId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface PartnerDocumentData {
  type: string;
  description: string;
  fileId: string;
  metadata?: Record<string, any>;
}

/**
 * Service for partner-related operations
 */
export const partnerService = {
  /**
   * Register as a partner
   * @param data Partner registration data
   * @returns The created partner
   */
  async register(data: PartnerRegistrationData): Promise<Partner> {
    return apiClient.post<Partner>(API_ENDPOINTS.PARTNERS.REGISTER, data);
  },

  /**
   * Get partner by ID
   * @param id Partner ID
   * @returns Partner details
   */
  async getPartnerById(id: string): Promise<Partner> {
    return apiClient.get<Partner>(`${API_ENDPOINTS.PARTNERS.BASE}/${id}`);
  },

  /**
   * Get all partners (admin only)
   * @param status Optional status filter
   * @returns List of partners
   */
  async getAllPartners(status?: string): Promise<Partner[]> {
    const params = status ? { status } : undefined;
    return apiClient.get<Partner[]>(API_ENDPOINTS.PARTNERS.BASE, { params });
  },

  /**
   * Update partner status (admin only)
   * @param id Partner ID
   * @param status New status
   * @returns Updated partner
   */
  async updateStatus(id: string, status: string): Promise<Partner> {
    return apiClient.patch<Partner>(`${API_ENDPOINTS.PARTNERS.BASE}/${id}/status`, { status });
  },

  /**
   * Add a document to a partner
   * @param partnerId Partner ID
   * @param documentData Document data
   * @returns The created document
   */
  async addDocument(partnerId: string, documentData: PartnerDocumentData): Promise<PartnerDocument> {
    return apiClient.post<PartnerDocument>(
      API_ENDPOINTS.PARTNERS.DOCUMENTS(partnerId),
      documentData
    );
  },

  /**
   * Get all documents for a partner
   * @param partnerId Partner ID
   * @returns List of partner documents
   */
  async getDocuments(partnerId: string): Promise<PartnerDocument[]> {
    return apiClient.get<PartnerDocument[]>(API_ENDPOINTS.PARTNERS.DOCUMENTS(partnerId));
  },

  /**
   * Delete a document
   * @param documentId Document ID
   * @returns The deleted document
   */
  async deleteDocument(documentId: string): Promise<PartnerDocument> {
    return apiClient.delete<PartnerDocument>(`${API_ENDPOINTS.PARTNERS.BASE}/documents/${documentId}`);
  },
}
