import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { getAuthToken } from '../../utils/auth';

/**
 * Interface pour les événements de sécurité
 */
export interface SecurityEvent {
  id: string;
  type: string;
  source: string;
  severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  details: Record<string, unknown>;
  timestamp: string;
  userId?: string;
}

/**
 * Interface pour les alertes de sécurité
 */
export interface SecurityAlert {
  id: string;
  type: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  details: Record<string, unknown>;
  status: 'OPEN' | 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED';
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
}

/**
 * Interface pour les notifications de sécurité
 */
export interface SecurityNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  severity: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
  type: string;
  read: boolean;
  createdAt: string;
  readAt?: string;
  actionRequired: boolean;
  actionUrl?: string;
  actionText?: string;
  eventId?: string;
}

/**
 * Interface pour les métriques de sécurité
 */
export interface SecurityMetrics {
  totalEvents: number;
  totalAlerts: number;
  topEventSources: Array<{ source: string; count: number }>;
  eventsBySeverity: {
    info: number;
    warning: number;
    error: number;
    critical: number;
  };
  timeframe: 'day' | 'week' | 'month';
}

/**
 * Interface pour les options de validation de fichier
 */
export interface FileValidationOptions {
  scanForMalware?: boolean;
  validateContent?: boolean;
  validateSize?: boolean;
  validateType?: boolean;
}

/**
 * Service pour interagir avec le microservice de sécurité
 */
class SecurityService {
  private readonly apiClient: AxiosInstance;
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';
    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Intercepteur pour ajouter le token d'authentification
    this.apiClient.interceptors.request.use((config) => {
      const token = getAuthToken();
      if (token) {
        config.headers = config.headers || {};
        config.headers['Authorization'] = `Bearer ${token}`;
      }
      return config;
    });
  }

  /**
   * Récupère les événements de sécurité avec des filtres optionnels
   */
  public async getSecurityEvents(
    filters?: Record<string, string | number | boolean | undefined>,
    config?: AxiosRequestConfig
  ): Promise<SecurityEvent[]> {
    const response = await this.apiClient.get('/security/events', {
      params: filters,
      ...config,
    });
    return response.data;
  }

  /**
   * Récupère les données du tableau de bord de surveillance de sécurité
   */
  public async getSecurityDashboard(
    timeframe: 'day' | 'week' | 'month' = 'day',
    config?: AxiosRequestConfig
  ): Promise<SecurityMetrics> {
    const response = await this.apiClient.get('/security/dashboard', {
      params: { timeframe },
      ...config,
    });
    return response.data;
  }

  /**
   * Récupère les alertes de sécurité avec des filtres optionnels
   */
  public async getSecurityAlerts(
    filters?: Record<string, string | number | boolean | undefined>,
    config?: AxiosRequestConfig
  ): Promise<SecurityAlert[]> {
    const response = await this.apiClient.get('/security/alerts', {
      params: filters,
      ...config,
    });
    return response.data;
  }

  /**
   * Met à jour le statut d'une alerte de sécurité
   */
  public async updateAlertStatus(
    alertId: string,
    status: 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED',
    notes?: string,
    config?: AxiosRequestConfig
  ): Promise<SecurityAlert> {
    const response = await this.apiClient.patch(
      `/security/alerts/${alertId}/status`,
      {
        status,
        notes,
      },
      config
    );
    return response.data;
  }

  /**
   * Valide un fichier avant l'upload
   */
  public async validateFile(
    file: File,
    options?: FileValidationOptions,
    config?: AxiosRequestConfig
  ): Promise<{ valid: boolean; reason?: string }> {
    const formData = new FormData();
    formData.append('file', file);

    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    const response = await this.apiClient.post('/security/validate-file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    });

    return response.data;
  }

  /**
   * Chiffre des données sensibles
   */
  public async encryptData(
    data: string,
    purpose: string,
    config?: AxiosRequestConfig
  ): Promise<{ encryptedData: string }> {
    const response = await this.apiClient.post(
      '/security/encrypt',
      {
        data,
        purpose,
      },
      config
    );
    return response.data;
  }

  /**
   * Déchiffre des données sensibles
   */
  public async decryptData(
    encryptedData: string,
    purpose: string,
    config?: AxiosRequestConfig
  ): Promise<{ data: string }> {
    const response = await this.apiClient.post(
      '/security/decrypt',
      {
        encryptedData,
        purpose,
      },
      config
    );
    return response.data;
  }

  /**
   * Récupère les notifications de sécurité
   */
  public async getSecurityNotifications(
    options: {
      page?: number;
      limit?: number;
      unreadOnly?: boolean;
      severity?: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';
    } = {},
    config?: AxiosRequestConfig
  ): Promise<{ notifications: SecurityNotification[]; total: number; unreadCount: number }> {
    const response = await this.apiClient.get('/security/notifications', {
      params: options,
      ...config,
    });
    return response.data;
  }

  /**
   * Récupère le nombre de notifications non lues
   */
  public async getUnreadNotificationsCount(
    config?: AxiosRequestConfig
  ): Promise<{ unreadCount: number }> {
    const response = await this.apiClient.get('/security/notifications/unread-count', config);
    return response.data;
  }

  /**
   * Marque une notification comme lue
   */
  public async markNotificationAsRead(
    notificationId: string,
    config?: AxiosRequestConfig
  ): Promise<SecurityNotification> {
    const response = await this.apiClient.post(
      `/security/notifications/${notificationId}/read`,
      {},
      config
    );
    return response.data;
  }

  /**
   * Marque toutes les notifications comme lues
   */
  public async markAllNotificationsAsRead(config?: AxiosRequestConfig): Promise<{ count: number }> {
    const response = await this.apiClient.post('/security/notifications/mark-all-read', {}, config);
    return response.data;
  }

  /**
   * Vérifie l'état de santé du service de sécurité
   */
  public async checkHealth(
    config?: AxiosRequestConfig
  ): Promise<{ status: string; details: Record<string, unknown> }> {
    const response = await this.apiClient.get('/security/health', config);
    return response.data;
  }
}

// Exporter une instance singleton du service
export const securityService = new SecurityService();
export default securityService;
