import { apiClient } from './apiClient';

/**
 * Stratégies d'agrégation de groupe
 */
export enum GroupAggregationStrategy {
  /** Moyenne des scores */
  AVERAGE = 'average',
  
  /** Minimum des scores (satisfaction de tous) */
  LEAST_MISERY = 'least_misery',
  
  /** Maximum des scores (satisfaction d'au moins un) */
  MOST_PLEASURE = 'most_pleasure',
  
  /** Moyenne sans les extrêmes */
  AVERAGE_WITHOUT_MISERY = 'average_without_misery',
  
  /** Médiane des scores */
  MEDIAN = 'median',
  
  /** Multiplicatif (produit des scores) */
  MULTIPLICATIVE = 'multiplicative',
  
  /** Basé sur la variance (favorise le consensus) */
  CONSENSUS = 'consensus',
  
  /** Pondéré par utilisateur */
  WEIGHTED = 'weighted',
}

/**
 * Statut d'un plan collaboratif
 */
export enum CollaborativePlanStatus {
  /** En cours de création */
  DRAFT = 'draft',
  
  /** En attente de votes */
  VOTING = 'voting',
  
  /** Finalisé */
  FINALIZED = 'finalized',
  
  /** Annulé */
  CANCELLED = 'cancelled',
}

/**
 * Type de vote
 */
export enum VoteType {
  /** Pour */
  UPVOTE = 'upvote',
  
  /** Contre */
  DOWNVOTE = 'downvote',
  
  /** Neutre */
  NEUTRAL = 'neutral',
}

/**
 * Interface pour les options de recommandation de groupe
 */
export interface GroupRecommendationOptions {
  /** IDs des utilisateurs du groupe */
  userIds: string[];
  
  /** Stratégie d'agrégation */
  aggregationStrategy?: GroupAggregationStrategy;
  
  /** Poids des utilisateurs (pour la stratégie pondérée) */
  userWeights?: Record<string, number>;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Seuil de satisfaction minimum */
  minSatisfactionThreshold?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les options de recommandation sociale
 */
export interface SocialRecommendationOptions {
  /** Facteur d'influence sociale (0-1) */
  socialFactor?: number;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Types de relations à considérer */
  relationTypes?: Array<'friend' | 'follower' | 'following' | 'colleague' | 'family'>;
  
  /** Profondeur du réseau social (1-3) */
  networkDepth?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les options de création de plan collaboratif
 */
export interface CreateCollaborativePlanOptions {
  /** Titre du plan */
  title: string;
  
  /** Description du plan */
  description?: string;
  
  /** IDs des participants */
  participantIds: string[];
  
  /** Date de début */
  startDate?: Date;
  
  /** Date de fin */
  endDate?: Date;
  
  /** Stratégie d'agrégation */
  aggregationStrategy?: GroupAggregationStrategy;
  
  /** Poids des utilisateurs */
  userWeights?: Record<string, number>;
  
  /** Nombre maximum de recommandations */
  maxRecommendations?: number;
  
  /** Filtres supplémentaires */
  filters?: Record<string, any>;
}

/**
 * Interface pour les options de vote
 */
export interface VoteOptions {
  /** ID de la recommandation */
  recommendationId: string;
  
  /** Type de vote */
  voteType: VoteType;
  
  /** Commentaire */
  comment?: string;
}

/**
 * Interface pour les options de finalisation de plan
 */
export interface FinalizePlanOptions {
  /** IDs des recommandations sélectionnées */
  selectedRecommendationIds: string[];
  
  /** Commentaire */
  comment?: string;
}

/**
 * Service pour les recommandations sociales et de groupe
 */
class SocialGroupRecommendationService {
  /**
   * Récupère des recommandations de groupe
   * @param options Options de recommandation de groupe
   * @returns Recommandations de groupe
   */
  async getGroupRecommendations(options: GroupRecommendationOptions): Promise<any[]> {
    try {
      const response = await apiClient.post('/recommendation/social-group/group', options);
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations de groupe:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations sociales
   * @param options Options de recommandation sociale
   * @returns Recommandations sociales
   */
  async getSocialRecommendations(options?: SocialRecommendationOptions): Promise<any[]> {
    try {
      const response = await apiClient.post('/recommendation/social-group/social', options || {});
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations sociales:', error);
      throw error;
    }
  }
  
  /**
   * Récupère des recommandations sociales (GET)
   * @param socialFactor Facteur d'influence sociale
   * @param maxRecommendations Nombre maximum de recommandations
   * @returns Recommandations sociales
   */
  async getMySocialRecommendations(socialFactor?: number, maxRecommendations?: number): Promise<any[]> {
    try {
      const params: Record<string, any> = {};
      
      if (socialFactor !== undefined) {
        params.socialFactor = socialFactor;
      }
      
      if (maxRecommendations !== undefined) {
        params.maxRecommendations = maxRecommendations;
      }
      
      const response = await apiClient.get('/recommendation/social-group/my-social', { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des recommandations sociales:', error);
      throw error;
    }
  }
  
  /**
   * Crée un plan collaboratif
   * @param options Options de création de plan collaboratif
   * @returns Plan collaboratif
   */
  async createCollaborativePlan(options: CreateCollaborativePlanOptions): Promise<any> {
    try {
      const response = await apiClient.post('/recommendation/social-group/collaborative-plan', options);
      return response;
    } catch (error) {
      console.error('Erreur lors de la création du plan collaboratif:', error);
      throw error;
    }
  }
  
  /**
   * Récupère un plan collaboratif
   * @param planId ID du plan
   * @returns Plan collaboratif
   */
  async getCollaborativePlan(planId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/recommendation/social-group/collaborative-plan/${planId}`);
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération du plan collaboratif:', error);
      throw error;
    }
  }
  
  /**
   * Récupère les plans collaboratifs de l'utilisateur courant
   * @returns Plans collaboratifs
   */
  async getMyCollaborativePlans(): Promise<any[]> {
    try {
      const response = await apiClient.get('/recommendation/social-group/my-collaborative-plans');
      return response;
    } catch (error) {
      console.error('Erreur lors de la récupération des plans collaboratifs:', error);
      throw error;
    }
  }
  
  /**
   * Vote pour une recommandation dans un plan collaboratif
   * @param planId ID du plan
   * @param options Options de vote
   * @returns Vote
   */
  async voteForRecommendation(planId: string, options: VoteOptions): Promise<any> {
    try {
      const response = await apiClient.post(`/recommendation/social-group/collaborative-plan/${planId}/vote`, options);
      return response;
    } catch (error) {
      console.error('Erreur lors du vote pour une recommandation:', error);
      throw error;
    }
  }
  
  /**
   * Finalise un plan collaboratif
   * @param planId ID du plan
   * @param options Options de finalisation
   * @returns Plan finalisé
   */
  async finalizePlan(planId: string, options: FinalizePlanOptions): Promise<any> {
    try {
      const response = await apiClient.post(`/recommendation/social-group/collaborative-plan/${planId}/finalize`, options);
      return response;
    } catch (error) {
      console.error('Erreur lors de la finalisation du plan collaboratif:', error);
      throw error;
    }
  }
  
  /**
   * Annule un plan collaboratif
   * @param planId ID du plan
   * @param reason Raison de l'annulation
   * @returns Plan annulé
   */
  async cancelPlan(planId: string, reason?: string): Promise<any> {
    try {
      const params: Record<string, any> = {};
      
      if (reason) {
        params.reason = reason;
      }
      
      const response = await apiClient.post(`/recommendation/social-group/collaborative-plan/${planId}/cancel`, {}, { params });
      return response;
    } catch (error) {
      console.error('Erreur lors de l\'annulation du plan collaboratif:', error);
      throw error;
    }
  }
  
  /**
   * Traduit le nom d'une stratégie d'agrégation
   * @param strategy Stratégie d'agrégation
   * @returns Nom traduit
   */
  getAggregationStrategyName(strategy: GroupAggregationStrategy): string {
    switch (strategy) {
      case GroupAggregationStrategy.AVERAGE:
        return 'Moyenne';
      case GroupAggregationStrategy.LEAST_MISERY:
        return 'Satisfaction minimale';
      case GroupAggregationStrategy.MOST_PLEASURE:
        return 'Satisfaction maximale';
      case GroupAggregationStrategy.AVERAGE_WITHOUT_MISERY:
        return 'Moyenne sans extrêmes';
      case GroupAggregationStrategy.MEDIAN:
        return 'Médiane';
      case GroupAggregationStrategy.MULTIPLICATIVE:
        return 'Multiplicatif';
      case GroupAggregationStrategy.CONSENSUS:
        return 'Consensus';
      case GroupAggregationStrategy.WEIGHTED:
        return 'Pondéré';
      default:
        return '';
    }
  }
  
  /**
   * Traduit le nom d'un statut de plan
   * @param status Statut du plan
   * @returns Nom traduit
   */
  getPlanStatusName(status: CollaborativePlanStatus): string {
    switch (status) {
      case CollaborativePlanStatus.DRAFT:
        return 'Brouillon';
      case CollaborativePlanStatus.VOTING:
        return 'En attente de votes';
      case CollaborativePlanStatus.FINALIZED:
        return 'Finalisé';
      case CollaborativePlanStatus.CANCELLED:
        return 'Annulé';
      default:
        return '';
    }
  }
  
  /**
   * Récupère la classe CSS de couleur d'un statut de plan
   * @param status Statut du plan
   * @returns Classe CSS de couleur
   */
  getPlanStatusColorClass(status: CollaborativePlanStatus): string {
    switch (status) {
      case CollaborativePlanStatus.DRAFT:
        return 'text-gray-500';
      case CollaborativePlanStatus.VOTING:
        return 'text-blue-500';
      case CollaborativePlanStatus.FINALIZED:
        return 'text-green-500';
      case CollaborativePlanStatus.CANCELLED:
        return 'text-red-500';
      default:
        return '';
    }
  }
  
  /**
   * Récupère la classe CSS de fond d'un statut de plan
   * @param status Statut du plan
   * @returns Classe CSS de fond
   */
  getPlanStatusBackgroundClass(status: CollaborativePlanStatus): string {
    switch (status) {
      case CollaborativePlanStatus.DRAFT:
        return 'bg-gray-100';
      case CollaborativePlanStatus.VOTING:
        return 'bg-blue-100';
      case CollaborativePlanStatus.FINALIZED:
        return 'bg-green-100';
      case CollaborativePlanStatus.CANCELLED:
        return 'bg-red-100';
      default:
        return '';
    }
  }
}

export const socialGroupRecommendationService = new SocialGroupRecommendationService();
