// Mock service for social functionality
// In a real implementation, this would make API calls to the backend

import { generateMockPosts } from '../../utils/mockData';

interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
}

interface Comment {
  id: string;
  postId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  createdAt: string;
}

// Mock data storage
let mockPosts = generateMockPosts(20);
let mockComments: Comment[] = [];

class SocialService {
  // Get social posts with optional filters
  async getPosts(filters?: Record<string, any>): Promise<Post[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredPosts = [...mockPosts];

    // Apply filters if provided
    if (filters) {
      if (filters.status) {
        filteredPosts = filteredPosts.filter(post => post.status === filters.status);
      }
      if (filters.userId) {
        filteredPosts = filteredPosts.filter(post => post.userId === filters.userId);
      }
      if (filters.tags && filters.tags.length > 0) {
        filteredPosts = filteredPosts.filter(post => 
          filters.tags.some((tag: string) => post.tags.includes(tag))
        );
      }
    }

    return filteredPosts;
  }

  // Get a single post by ID
  async getPost(id: string): Promise<Post | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const post = mockPosts.find(post => post.id === id);
    return post || null;
  }

  // Create a new post
  async createPost(data: {
    title: string;
    description: string;
    thumbnailUrl: string;
    tags?: string[];
    privacy?: 'public' | 'friends' | 'private';
  }): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));

    const newPost: Post = {
      id: `post-${Date.now()}`,
      title: data.title,
      description: data.description,
      thumbnailUrl: data.thumbnailUrl,
      createdAt: new Date().toISOString(),
      userId: 'current-user-id',
      userName: 'Current User',
      userAvatar: 'https://via.placeholder.com/40',
      likes: 0,
      comments: 0,
      views: 0,
      status: 'published',
      tags: data.tags || [],
      privacy: data.privacy || 'public',
    };

    // Add the new post to the array
    mockPosts = [newPost, ...mockPosts];

    return newPost;
  }

  // Update a post
  async updatePost(id: string, data: Partial<Post>): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Update the post
    mockPosts[index] = {
      ...mockPosts[index],
      ...data,
      // Don't allow changing these fields
      id: mockPosts[index].id,
      userId: mockPosts[index].userId,
      userName: mockPosts[index].userName,
      userAvatar: mockPosts[index].userAvatar,
      createdAt: mockPosts[index].createdAt,
    };

    return mockPosts[index];
  }

  // Update post status (published, archived, deleted)
  async updatePostStatus(id: string, status: 'published' | 'archived' | 'deleted'): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Update the post status
    mockPosts[index] = {
      ...mockPosts[index],
      status,
    };

    return mockPosts[index];
  }

  // Delete a post permanently
  async deletePost(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Remove the post from the array
    mockPosts = mockPosts.filter(post => post.id !== id);
  }

  // Like a post
  async likePost(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Increment likes
    mockPosts[index].likes += 1;
  }

  // Unlike a post
  async unlikePost(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Decrement likes (but not below 0)
    mockPosts[index].likes = Math.max(0, mockPosts[index].likes - 1);
  }

  // Get comments for a post
  async getComments(postId: string): Promise<Comment[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));

    return mockComments.filter(comment => comment.postId === postId);
  }

  // Add a comment to a post
  async addComment(postId: string, content: string): Promise<Comment> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const post = mockPosts.find(post => post.id === postId);
    if (!post) {
      throw new Error('Post not found');
    }

    // Create new comment
    const newComment: Comment = {
      id: `comment-${Date.now()}`,
      postId,
      userId: 'current-user-id',
      userName: 'Current User',
      userAvatar: 'https://via.placeholder.com/40',
      content,
      createdAt: new Date().toISOString(),
    };

    // Add comment to array
    mockComments.push(newComment);

    // Increment comment count on post
    post.comments += 1;

    return newComment;
  }
}

export const socialService = new SocialService();
