// Mock service for social video functionality
// In a real implementation, this would make API calls to the backend

import { generateMockPosts } from '../../utils/mockData';

export interface Post {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl?: string;
  createdAt: string;
  userId: string;
  userName: string;
  userAvatar: string;
  likes: number;
  comments: number;
  views: number;
  status: 'published' | 'archived' | 'deleted';
  tags: string[];
  privacy: 'public' | 'friends' | 'private';
  scheduledDate?: string;
  scheduledStatus?: 'scheduled' | 'published' | 'failed';
}

// Mock data storage
let mockPosts = generateMockPosts(30);

class SocialVideoService {
  // Get video posts with optional filters
  async getVideoPosts(filters?: Record<string, any>): Promise<Post[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredPosts = [...mockPosts];

    // Apply filters if provided
    if (filters) {
      if (filters.status) {
        filteredPosts = filteredPosts.filter(post => post.status === filters.status);
      }
      if (filters.userId) {
        filteredPosts = filteredPosts.filter(post => post.userId === filters.userId);
      }
      if (filters.tags && filters.tags.length > 0) {
        filteredPosts = filteredPosts.filter(post =>
          filters.tags.some((tag: string) => post.tags.includes(tag))
        );
      }
    }

    return filteredPosts;
  }

  // Get a single video post by ID
  async getVideoPost(id: string): Promise<Post | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    const post = mockPosts.find(post => post.id === id);
    return post || null;
  }

  // Update a video post
  async updateVideo(id: string, data: Partial<Post>): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Update the post
    mockPosts[index] = {
      ...mockPosts[index],
      ...data,
      // Don't allow changing these fields
      id: mockPosts[index].id,
      userId: mockPosts[index].userId,
      userName: mockPosts[index].userName,
      userAvatar: mockPosts[index].userAvatar,
      createdAt: mockPosts[index].createdAt,
    };

    return mockPosts[index];
  }

  // Update video status (published, archived, deleted)
  async updateVideoStatus(id: string, status: 'published' | 'archived' | 'deleted'): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Update the post status
    mockPosts[index] = {
      ...mockPosts[index],
      status,
    };

    return mockPosts[index];
  }

  // Delete a video post permanently
  async deleteVideo(id: string): Promise<void> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Remove the post from the array
    mockPosts = mockPosts.filter(post => post.id !== id);
  }

  // Upload a new video
  async uploadVideo(data: {
    title: string;
    description: string;
    file: File;
    tags?: string[];
    privacy?: 'public' | 'friends' | 'private';
  }): Promise<Post> {
    // Simulate API delay and upload progress
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Create a new post
    const newPost: Post = {
      id: `post-${Date.now()}`,
      title: data.title,
      description: data.description,
      thumbnailUrl: 'https://via.placeholder.com/640x360',
      videoUrl: 'https://example.com/video.mp4',
      createdAt: new Date().toISOString(),
      userId: 'current-user-id',
      userName: 'Current User',
      userAvatar: 'https://via.placeholder.com/40',
      likes: 0,
      comments: 0,
      views: 0,
      status: 'published',
      tags: data.tags || [],
      privacy: data.privacy || 'public',
    };

    // Add the new post to the array
    mockPosts = [newPost, ...mockPosts];

    return newPost;
  }

  // Schedule a video for future publication
  async scheduleVideo(id: string, scheduledDate: string): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Update the post with scheduled information
    mockPosts[index] = {
      ...mockPosts[index],
      scheduledDate,
      scheduledStatus: 'scheduled',
      status: 'archived', // Archive the post until publication date
    };

    return mockPosts[index];
  }

  // Get scheduled videos
  async getScheduledVideos(userId?: string): Promise<Post[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredPosts = mockPosts.filter(post => post.scheduledStatus === 'scheduled');

    // Filter by user ID if provided
    if (userId) {
      filteredPosts = filteredPosts.filter(post => post.userId === userId);
    }

    // Sort by scheduled date (earliest first)
    filteredPosts.sort((a, b) => {
      if (!a.scheduledDate || !b.scheduledDate) return 0;
      return new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime();
    });

    return filteredPosts;
  }

  // Cancel scheduled publication
  async cancelScheduledVideo(id: string): Promise<Post> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));

    const index = mockPosts.findIndex(post => post.id === id);
    if (index === -1) {
      throw new Error('Post not found');
    }

    // Remove scheduling information
    mockPosts[index] = {
      ...mockPosts[index],
      scheduledDate: undefined,
      scheduledStatus: undefined,
      status: 'archived', // Keep it archived
    };

    return mockPosts[index];
  }

  // Search for video posts
  async searchVideoPosts(params: {
    query?: string;
    contentType?: string;
    dateRange?: string;
    status?: string;
    tags?: string[];
    userId?: string;
  }): Promise<Post[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));

    let filteredPosts = [...mockPosts];

    // Filter by user ID if provided
    if (params.userId) {
      filteredPosts = filteredPosts.filter(post => post.userId === params.userId);
    }

    // Filter by status if provided and not 'all'
    if (params.status && params.status !== 'all') {
      filteredPosts = filteredPosts.filter(post => post.status === params.status);
    }

    // Filter by tags if provided
    if (params.tags && params.tags.length > 0) {
      filteredPosts = filteredPosts.filter(post =>
        params.tags!.some(tag => post.tags.includes(tag))
      );
    }

    // Filter by search query if provided
    if (params.query && params.query.trim() !== '') {
      const query = params.query.toLowerCase().trim();
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(query) ||
        post.description.toLowerCase().includes(query) ||
        post.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by date range if provided
    if (params.dateRange && params.dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (params.dateRange) {
        case 'today':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          startDate = new Date(now);
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          startDate = new Date(now);
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          startDate = new Date(0); // Beginning of time
      }

      filteredPosts = filteredPosts.filter(post =>
        new Date(post.createdAt) >= startDate
      );
    }

    // Sort by relevance (if query provided) or by date (most recent first)
    if (params.query && params.query.trim() !== '') {
      const query = params.query.toLowerCase().trim();

      // Simple relevance scoring
      filteredPosts.sort((a, b) => {
        const aTitle = a.title.toLowerCase();
        const bTitle = b.title.toLowerCase();

        // Exact title match gets highest priority
        if (aTitle === query && bTitle !== query) return -1;
        if (bTitle === query && aTitle !== query) return 1;

        // Title starts with query gets next priority
        if (aTitle.startsWith(query) && !bTitle.startsWith(query)) return -1;
        if (bTitle.startsWith(query) && !aTitle.startsWith(query)) return 1;

        // Title contains query gets next priority
        if (aTitle.includes(query) && !bTitle.includes(query)) return -1;
        if (bTitle.includes(query) && !aTitle.includes(query)) return 1;

        // Fall back to recency
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
    } else {
      // Sort by date (most recent first)
      filteredPosts.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    }

    return filteredPosts;
  }
}

export const socialVideoService = new SocialVideoService();
