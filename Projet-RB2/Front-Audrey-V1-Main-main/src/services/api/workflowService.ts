/**
 * Service API pour la gestion des workflows
 * Communication avec le système d'orchestration des workflows
 */

import { apiClient } from './apiClient';

export interface Workflow {
  id: string;
  name: string;
  description: string;
  status: 'running' | 'completed' | 'failed' | 'paused' | 'pending';
  progress: number;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  steps: WorkflowStep[];
  agents: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  creator: string;
}

export interface WorkflowStep {
  id: string;
  name: string;
  agent: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  input?: any;
  output?: any;
  error?: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  steps: Omit<WorkflowStep, 'id' | 'status' | 'startTime' | 'endTime' | 'duration'>[];
  estimatedDuration: number;
}

export interface CreateWorkflowRequest {
  name: string;
  description: string;
  type: string;
  template: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  parameters: any;
}

class WorkflowService {
  private readonly baseUrl = '/api/workflows';

  /**
   * Obtenir tous les workflows
   */
  async getWorkflows(): Promise<{ data: Workflow[] }> {
    try {
      const response = await apiClient.get(this.baseUrl);
      return {
        data: response.data.map((workflow: any) => ({
          ...workflow,
          startTime: new Date(workflow.startTime),
          endTime: workflow.endTime ? new Date(workflow.endTime) : undefined,
          steps: workflow.steps.map((step: any) => ({
            ...step,
            startTime: step.startTime ? new Date(step.startTime) : undefined,
            endTime: step.endTime ? new Date(step.endTime) : undefined
          }))
        }))
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des workflows:', error);
      // Données de simulation
      return {
        data: [
          {
            id: 'workflow-1',
            name: 'Optimisation E-commerce',
            description: 'Optimisation complète d\'une application e-commerce',
            status: 'running',
            progress: 65,
            startTime: new Date(Date.now() - 3600000),
            steps: [
              {
                id: 'step-1',
                name: 'Analyse UI/UX',
                agent: 'agent-uiux',
                status: 'completed',
                startTime: new Date(Date.now() - 3600000),
                endTime: new Date(Date.now() - 3000000),
                duration: 600
              },
              {
                id: 'step-2',
                name: 'Benchmark Performance',
                agent: 'agent-performance',
                status: 'running',
                startTime: new Date(Date.now() - 3000000)
              },
              {
                id: 'step-3',
                name: 'Audit Sécurité',
                agent: 'agent-security',
                status: 'pending'
              }
            ],
            agents: ['agent-uiux', 'agent-performance', 'agent-security'],
            priority: 'high',
            creator: 'admin'
          },
          {
            id: 'workflow-2',
            name: 'Tests Automatisés',
            description: 'Suite complète de tests automatisés',
            status: 'completed',
            progress: 100,
            startTime: new Date(Date.now() - 7200000),
            endTime: new Date(Date.now() - 1800000),
            duration: 5400,
            steps: [
              {
                id: 'step-4',
                name: 'Tests Unitaires',
                agent: 'agent-qa',
                status: 'completed',
                startTime: new Date(Date.now() - 7200000),
                endTime: new Date(Date.now() - 5400000),
                duration: 1800
              },
              {
                id: 'step-5',
                name: 'Tests E2E',
                agent: 'agent-qa',
                status: 'completed',
                startTime: new Date(Date.now() - 5400000),
                endTime: new Date(Date.now() - 1800000),
                duration: 3600
              }
            ],
            agents: ['agent-qa'],
            priority: 'medium',
            creator: 'developer'
          }
        ]
      };
    }
  }

  /**
   * Obtenir les templates de workflow
   */
  async getTemplates(): Promise<{ data: WorkflowTemplate[] }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/templates`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des templates:', error);
      // Données de simulation
      return {
        data: [
          {
            id: 'template-optimization',
            name: 'Optimisation Complète',
            description: 'Workflow d\'optimisation complète d\'une application',
            category: 'Performance',
            estimatedDuration: 3600,
            steps: [
              {
                name: 'Analyse UI/UX',
                agent: 'agent-uiux',
                input: { type: 'user_research' }
              },
              {
                name: 'Benchmark Performance',
                agent: 'agent-performance',
                input: { type: 'full_benchmark' }
              },
              {
                name: 'Audit Sécurité',
                agent: 'agent-security',
                input: { type: 'security_scan' }
              },
              {
                name: 'Tests QA',
                agent: 'agent-qa',
                input: { type: 'comprehensive_test' }
              }
            ]
          },
          {
            id: 'template-security',
            name: 'Audit Sécurité',
            description: 'Audit de sécurité complet',
            category: 'Sécurité',
            estimatedDuration: 1800,
            steps: [
              {
                name: 'Scan Vulnérabilités',
                agent: 'agent-security',
                input: { type: 'vulnerability_scan' }
              },
              {
                name: 'Analyse Code',
                agent: 'agent-security',
                input: { type: 'code_analysis' }
              }
            ]
          }
        ]
      };
    }
  }

  /**
   * Créer un nouveau workflow
   */
  async createWorkflow(request: CreateWorkflowRequest): Promise<{ data: Workflow }> {
    try {
      const response = await apiClient.post(this.baseUrl, request);
      return {
        data: {
          ...response.data,
          startTime: new Date(response.data.startTime),
          endTime: response.data.endTime ? new Date(response.data.endTime) : undefined
        }
      };
    } catch (error) {
      console.error('Erreur lors de la création du workflow:', error);
      throw error;
    }
  }

  /**
   * Contrôler un workflow (pause, resume, cancel, restart)
   */
  async controlWorkflow(workflowId: string, action: 'pause' | 'resume' | 'cancel' | 'restart'): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/${workflowId}/control`, { action });
    } catch (error) {
      console.error(`Erreur lors du contrôle du workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les détails d'un workflow
   */
  async getWorkflowDetails(workflowId: string): Promise<{ data: Workflow }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${workflowId}`);
      return {
        data: {
          ...response.data,
          startTime: new Date(response.data.startTime),
          endTime: response.data.endTime ? new Date(response.data.endTime) : undefined,
          steps: response.data.steps.map((step: any) => ({
            ...step,
            startTime: step.startTime ? new Date(step.startTime) : undefined,
            endTime: step.endTime ? new Date(step.endTime) : undefined
          }))
        }
      };
    } catch (error) {
      console.error(`Erreur lors de la récupération du workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Supprimer un workflow
   */
  async deleteWorkflow(workflowId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${workflowId}`);
    } catch (error) {
      console.error(`Erreur lors de la suppression du workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir l'historique des workflows
   */
  async getWorkflowHistory(options?: {
    status?: string;
    agent?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ data: Workflow[]; total: number }> {
    try {
      const params = new URLSearchParams();
      if (options?.status) params.append('status', options.status);
      if (options?.agent) params.append('agent', options.agent);
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());

      const response = await apiClient.get(`${this.baseUrl}/history?${params}`);
      return {
        data: response.data.workflows.map((workflow: any) => ({
          ...workflow,
          startTime: new Date(workflow.startTime),
          endTime: workflow.endTime ? new Date(workflow.endTime) : undefined
        })),
        total: response.data.total
      };
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return { data: [], total: 0 };
    }
  }

  /**
   * Obtenir les statistiques des workflows
   */
  async getWorkflowStats(period: string = '24h'): Promise<{ data: any }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats?period=${period}`);
      return { data: response.data };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      return {
        data: {
          total: 0,
          completed: 0,
          failed: 0,
          running: 0,
          averageDuration: 0,
          successRate: 0
        }
      };
    }
  }

  /**
   * Cloner un workflow existant
   */
  async cloneWorkflow(workflowId: string, newName?: string): Promise<{ data: Workflow }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${workflowId}/clone`, {
        name: newName
      });
      return {
        data: {
          ...response.data,
          startTime: new Date(response.data.startTime),
          endTime: response.data.endTime ? new Date(response.data.endTime) : undefined
        }
      };
    } catch (error) {
      console.error(`Erreur lors du clonage du workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Programmer un workflow
   */
  async scheduleWorkflow(workflowId: string, schedule: {
    type: 'once' | 'recurring';
    datetime?: Date;
    cron?: string;
  }): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/${workflowId}/schedule`, {
        ...schedule,
        datetime: schedule.datetime?.toISOString()
      });
    } catch (error) {
      console.error(`Erreur lors de la programmation du workflow ${workflowId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les logs d'un workflow
   */
  async getWorkflowLogs(workflowId: string, stepId?: string): Promise<{ data: any[] }> {
    try {
      const url = stepId 
        ? `${this.baseUrl}/${workflowId}/steps/${stepId}/logs`
        : `${this.baseUrl}/${workflowId}/logs`;
      
      const response = await apiClient.get(url);
      return { data: response.data };
    } catch (error) {
      console.error(`Erreur lors de la récupération des logs du workflow ${workflowId}:`, error);
      return { data: [] };
    }
  }
}

export const workflowService = new WorkflowService();
