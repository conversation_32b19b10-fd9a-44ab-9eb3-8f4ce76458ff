import { v4 as uuidv4 } from 'uuid';
import { ChatMessage, MessageType } from './api/chatbotService';

// Storage keys
const STORAGE_KEY_CONVERSATIONS = 'rb_chatbot_conversations';
const STORAGE_KEY_CURRENT_CONVERSATION = 'rb_chatbot_current_conversation';
const MAX_STORED_CONVERSATIONS = 10;

// Conversation interface
export interface StoredConversation {
  id: string;
  title: string;
  messages: StoredMessage[];
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, unknown>;
  preferences?: Record<string, unknown>;
}

// Message interface for storage
export interface StoredMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  messageType?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Convert ChatMessage to StoredMessage
 */
const toStoredMessage = (message: ChatMessage): StoredMessage => {
  return {
    id: uuidv4(),
    role: message.role,
    content: message.content,
    timestamp: (message.timestamp || new Date()).toISOString(),
    messageType: message.messageType?.toString() || MessageType.TEXT.toString(),
    metadata: message.metadata,
  };
};

/**
 * Convert StoredMessage to ChatMessage
 */
const toChatMessage = (message: StoredMessage): ChatMessage => {
  return {
    role: message.role,
    content: message.content,
    timestamp: new Date(message.timestamp),
    messageType: message.messageType as MessageType,
    metadata: message.metadata,
  };
};

/**
 * Get all stored conversations
 */
export const getStoredConversations = (): StoredConversation[] => {
  try {
    const storedData = localStorage.getItem(STORAGE_KEY_CONVERSATIONS);
    if (!storedData) return [];

    return JSON.parse(storedData);
  } catch (error) {
    console.error('Error retrieving stored conversations:', error);
    return [];
  }
};

/**
 * Get a specific conversation by ID
 */
export const getStoredConversation = (conversationId: string): StoredConversation | null => {
  const conversations = getStoredConversations();
  return conversations.find((conv) => conv.id === conversationId) || null;
};

/**
 * Get the current conversation ID
 */
export const getCurrentConversationId = (): string | null => {
  try {
    return localStorage.getItem(STORAGE_KEY_CURRENT_CONVERSATION);
  } catch (error) {
    console.error('Error retrieving current conversation ID:', error);
    return null;
  }
};

/**
 * Set the current conversation ID
 */
export const setCurrentConversationId = (conversationId: string | null): void => {
  try {
    if (conversationId) {
      localStorage.setItem(STORAGE_KEY_CURRENT_CONVERSATION, conversationId);
    } else {
      localStorage.removeItem(STORAGE_KEY_CURRENT_CONVERSATION);
    }
  } catch (error) {
    console.error('Error setting current conversation ID:', error);
  }
};

/**
 * Create a new conversation
 */
export const createConversation = (
  title: string = 'Nouvelle conversation',
  initialMessages: ChatMessage[] = [],
  metadata?: Record<string, unknown>
): StoredConversation => {
  const now = new Date().toISOString();
  const newConversation: StoredConversation = {
    id: uuidv4(),
    title,
    messages: initialMessages.map(toStoredMessage),
    createdAt: now,
    updatedAt: now,
    metadata,
    preferences: {},
  };

  // Add to stored conversations
  const conversations = getStoredConversations();

  // Limit the number of stored conversations
  if (conversations.length >= MAX_STORED_CONVERSATIONS) {
    conversations.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
    conversations.pop(); // Remove the oldest conversation
  }

  conversations.unshift(newConversation); // Add new conversation at the beginning

  try {
    localStorage.setItem(STORAGE_KEY_CONVERSATIONS, JSON.stringify(conversations));
    setCurrentConversationId(newConversation.id);
  } catch (error) {
    console.error('Error storing new conversation:', error);
  }

  return newConversation;
};

/**
 * Add a message to a conversation
 */
export const addMessageToConversation = (
  conversationId: string,
  message: ChatMessage
): StoredConversation | null => {
  const conversations = getStoredConversations();
  const conversationIndex = conversations.findIndex((conv) => conv.id === conversationId);

  if (conversationIndex === -1) return null;

  const conversation = conversations[conversationIndex];
  const storedMessage = toStoredMessage(message);

  conversation.messages.push(storedMessage);
  conversation.updatedAt = new Date().toISOString();

  try {
    localStorage.setItem(STORAGE_KEY_CONVERSATIONS, JSON.stringify(conversations));
  } catch (error) {
    console.error('Error storing updated conversation:', error);
  }

  return conversation;
};

/**
 * Delete a conversation
 */
export const deleteConversation = (conversationId: string): boolean => {
  const conversations = getStoredConversations();
  const filteredConversations = conversations.filter((conv) => conv.id !== conversationId);

  if (filteredConversations.length === conversations.length) {
    return false; // Conversation not found
  }

  try {
    localStorage.setItem(STORAGE_KEY_CONVERSATIONS, JSON.stringify(filteredConversations));

    // If the deleted conversation was the current one, clear the current conversation
    const currentId = getCurrentConversationId();
    if (currentId === conversationId) {
      setCurrentConversationId(null);
    }

    return true;
  } catch (error) {
    console.error('Error deleting conversation:', error);
    return false;
  }
};

/**
 * Clear all stored conversations
 */
export const clearAllConversations = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEY_CURRENT_CONVERSATION);
  } catch (error) {
    console.error('Error clearing conversations:', error);
  }
};

/**
 * Get messages from a conversation as ChatMessage[]
 */
export const getConversationMessages = (conversationId: string): ChatMessage[] => {
  const conversation = getStoredConversation(conversationId);
  if (!conversation) return [];

  return conversation.messages.map(toChatMessage);
};

/**
 * Update conversation title
 */
export const updateConversationTitle = (
  conversationId: string,
  title: string
): StoredConversation | null => {
  const conversations = getStoredConversations();
  const conversationIndex = conversations.findIndex((conv) => conv.id === conversationId);

  if (conversationIndex === -1) return null;

  conversations[conversationIndex].title = title;
  conversations[conversationIndex].updatedAt = new Date().toISOString();

  try {
    localStorage.setItem(STORAGE_KEY_CONVERSATIONS, JSON.stringify(conversations));
  } catch (error) {
    console.error('Error updating conversation title:', error);
  }

  return conversations[conversationIndex];
};
