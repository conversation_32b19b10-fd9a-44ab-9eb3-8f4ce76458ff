import { AxiosError } from 'axios';

// Types d'erreurs
export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

// Interface pour les erreurs formatées
export interface FormattedError {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: any;
  originalError?: any;
}

/**
 * Service de gestion des erreurs
 * Fournit des méthodes pour formater et gérer les erreurs de manière cohérente
 */
class ErrorService {
  /**
   * Formater une erreur Axios
   * @param error Erreur Axios
   * @returns Erreur formatée
   */
  formatAxiosError(error: AxiosError): FormattedError {
    // Erreur réseau (pas de réponse du serveur)
    if (error.code === 'ECONNABORTED' || !error.response) {
      return {
        type: ErrorType.NETWORK,
        message:
          'Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.',
        code: error.code,
        originalError: error,
      };
    }

    // Erreur avec une réponse du serveur
    const { status, data } = error.response;

    // Extraire le message d'erreur de la réponse si disponible
    const errorMessage = this.extractErrorMessage(data);

    // Formater l'erreur en fonction du code de statut
    switch (status) {
      case 400:
        return {
          type: ErrorType.VALIDATION,
          message: errorMessage || 'Les données fournies sont invalides.',
          code: status,
          details: data,
          originalError: error,
        };

      case 401:
        return {
          type: ErrorType.AUTHENTICATION,
          message: errorMessage || 'Vous devez être connecté pour accéder à cette ressource.',
          code: status,
          originalError: error,
        };

      case 403:
        return {
          type: ErrorType.AUTHORIZATION,
          message:
            errorMessage ||
            "Vous n'avez pas les droits nécessaires pour accéder à cette ressource.",
          code: status,
          originalError: error,
        };

      case 404:
        return {
          type: ErrorType.NOT_FOUND,
          message: errorMessage || "La ressource demandée n'existe pas.",
          code: status,
          originalError: error,
        };

      case 500:
      case 502:
      case 503:
      case 504:
        return {
          type: ErrorType.SERVER,
          message:
            errorMessage ||
            'Une erreur est survenue sur le serveur. Veuillez réessayer ultérieurement.',
          code: status,
          originalError: error,
        };

      default:
        return {
          type: ErrorType.API,
          message:
            errorMessage || 'Une erreur est survenue lors de la communication avec le serveur.',
          code: status,
          details: data,
          originalError: error,
        };
    }
  }

  /**
   * Extraire le message d'erreur de la réponse du serveur
   * @param data Données de la réponse
   * @returns Message d'erreur ou null si non trouvé
   */
  private extractErrorMessage(data: any): string | null {
    if (!data) return null;

    // Format NestJS
    if (data.message) {
      return Array.isArray(data.message) ? data.message.join(', ') : data.message;
    }

    // Format avec un champ 'error'
    if (data.error && typeof data.error === 'string') {
      return data.error;
    }

    // Format avec un champ 'errors' contenant un tableau
    if (data.errors && Array.isArray(data.errors)) {
      return data.errors.map((e: any) => e.message || e).join(', ');
    }

    return null;
  }

  /**
   * Formater une erreur générique
   * @param error Erreur générique
   * @returns Erreur formatée
   */
  formatError(error: any): FormattedError {
    // Si c'est une erreur Axios, utiliser le formateur spécifique
    if (error.isAxiosError) {
      return this.formatAxiosError(error);
    }

    // Erreur avec un message
    if (error.message) {
      return {
        type: ErrorType.UNKNOWN,
        message: error.message,
        originalError: error,
      };
    }

    // Erreur sans message
    return {
      type: ErrorType.UNKNOWN,
      message: 'Une erreur inconnue est survenue.',
      originalError: error,
    };
  }

  /**
   * Obtenir un message d'erreur convivial pour l'utilisateur
   * @param error Erreur formatée ou brute
   * @returns Message d'erreur convivial
   */
  getUserFriendlyMessage(error: FormattedError | any): string {
    // Si l'erreur n'est pas déjà formatée, la formater
    const formattedError = 'type' in error ? error : this.formatError(error);

    // Retourner le message formaté
    return formattedError.message;
  }

  /**
   * Journaliser une erreur
   * @param error Erreur à journaliser
   */
  logError(error: any): void {
    // Formater l'erreur si nécessaire
    const formattedError = 'type' in error ? error : this.formatError(error);

    // Journaliser l'erreur dans la console
    console.error('Error:', formattedError);

    // Ici, on pourrait également envoyer l'erreur à un service de monitoring comme Sentry
    // if (process.env.NODE_ENV === 'production') {
    //   Sentry.captureException(formattedError.originalError || formattedError);
    // }
  }
}

// Exporter une instance unique du service d'erreur
export const errorService = new ErrorService();
