# Internationalisation (i18n) dans Retreat And Be

Ce document explique comment utiliser le système d'internationalisation dans l'application Retreat And Be.

## Structure des fichiers

```
Front-Audrey-V1-Main-main/
├── public/
│   └── locales/
│       ├── fr/
│       │   ├── common.json
│       │   └── translation.json
│       └── en/
│           ├── common.json
│           └── translation.json
├── src/
│   ├── services/
│   │   └── i18n/
│   │       ├── i18nService.ts
│   │       └── README.md (ce fichier)
│   ├── hooks/
│   │   └── useTranslation.ts
│   ├── providers/
│   │   └── TranslationProvider.tsx
│   └── components/
│       └── LanguageSwitcher/
│           ├── LanguageSwitcher.tsx
│           ├── LanguageSwitcher.css
│           └── index.ts
```

## Fichiers de traduction

Les fichiers de traduction sont situés dans le dossier `public/locales/`. Ils sont organisés par langue et par namespace.

- `common.json` : Contient les traductions communes à toute l'application (boutons, messages d'erreur, etc.)
- `translation.json` : Contient les traductions spécifiques à certaines pages ou fonctionnalités

## Utilisation dans les composants React

### 1. Envelopper l'application avec le TranslationProvider

Dans votre fichier principal (par exemple, `App.tsx`), enveloppez votre application avec le `TranslationProvider` :

```tsx
import React from 'react';
import TranslationProvider from './providers/TranslationProvider';

const App: React.FC = () => {
  return <TranslationProvider>{/* Votre application */}</TranslationProvider>;
};

export default App;
```

### 2. Utiliser les traductions dans un composant

Utilisez le hook `useTranslationContext` pour accéder aux fonctions de traduction :

```tsx
import React from 'react';
import { useTranslationContext } from '../../providers/TranslationProvider';

const MyComponent: React.FC = () => {
  const { t, formatDate, formatCurrency } = useTranslationContext();

  const today = new Date();
  const price = 99.99;

  return (
    <div>
      <h1>{t('landing.hero.title1')}</h1>
      <p>{t('landing.hero.description')}</p>
      <p>
        {t('common.date')}: {formatDate(today, { dateStyle: 'full' })}
      </p>
      <p>
        {t('common.price')}: {formatCurrency(price, 'EUR')}
      </p>
    </div>
  );
};

export default MyComponent;
```

### 3. Changer la langue

Utilisez le composant `LanguageSwitcher` pour permettre à l'utilisateur de changer la langue :

```tsx
import React from 'react';
import LanguageSwitcher from '../../components/LanguageSwitcher';

const Header: React.FC = () => {
  return (
    <header>
      <nav>
        {/* Autres éléments de navigation */}
        <LanguageSwitcher variant='dropdown' />
      </nav>
    </header>
  );
};

export default Header;
```

## Ajouter une nouvelle langue

1. Créez un nouveau dossier dans `public/locales/` avec le code de la langue (par exemple, `es` pour l'espagnol)
2. Copiez les fichiers `common.json` et `translation.json` d'une langue existante
3. Traduisez les valeurs dans ces fichiers
4. Ajoutez la langue dans la méthode `loadAllTranslations` du fichier `i18nService.ts`

## Ajouter de nouvelles traductions

1. Identifiez le fichier approprié (`common.json` ou `translation.json`)
2. Ajoutez votre nouvelle clé et sa traduction dans chaque langue
3. Utilisez la clé dans votre composant avec la fonction `t`

## Bonnes pratiques

- Utilisez des clés hiérarchiques pour organiser vos traductions (par exemple, `landing.hero.title`)
- Évitez les traductions trop longues ou contenant du HTML
- Pour les textes avec variables, utilisez des paramètres : `t('greeting', { name: 'John' })` avec une traduction comme `"Bonjour, {name} !"`
- Testez vos traductions dans toutes les langues supportées

## Intégration avec le backend

Le backend (NestJS) utilise également un système d'internationalisation. Les messages d'erreur et autres textes provenant du backend sont traduits selon la langue préférée de l'utilisateur, qui est envoyée dans les en-têtes des requêtes HTTP.

## Ressources

- [Documentation i18next](https://www.i18next.com/)
- [Documentation react-i18next](https://react.i18next.com/)
- [Documentation Intl](https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Global_Objects/Intl)
