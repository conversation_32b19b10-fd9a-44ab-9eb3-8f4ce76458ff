import { apiClient } from './api/apiClient';
import { apiCache } from './api/interceptors/cacheInterceptor';

// Types d'entités synchronisables
export enum SyncEntityType {
  USER = 'user',
  RETREAT = 'retreat',
  BOOKING = 'booking',
  PROFILE = 'profile',
  REVIEW = 'review',
}

// Interface pour les options de synchronisation
interface SyncOptions {
  forceRefresh?: boolean;
  background?: boolean;
  onProgress?: (progress: number) => void;
  onComplete?: () => void;
  onError?: (error: any) => void;
}

// Interface pour le statut de synchronisation
interface SyncStatus {
  lastSync: Record<SyncEntityType, Date | null>;
  inProgress: boolean;
  progress: number;
  error: any | null;
}

/**
 * Service de synchronisation des données
 * Permet de synchroniser les données entre le frontend et le backend
 */
class SyncService {
  private status: SyncStatus = {
    lastSync: {
      [SyncEntityType.USER]: null,
      [SyncEntityType.RETREAT]: null,
      [SyncEntityType.BOOKING]: null,
      [SyncEntityType.PROFILE]: null,
      [SyncEntityType.REVIEW]: null,
    },
    inProgress: false,
    progress: 0,
    error: null,
  };

  private listeners: Array<(status: SyncStatus) => void> = [];

  /**
   * Ajouter un écouteur pour les changements de statut
   * @param listener Fonction à appeler lors d'un changement de statut
   * @returns Fonction pour supprimer l'écouteur
   */
  public addListener(listener: (status: SyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  /**
   * Notifier les écouteurs d'un changement de statut
   */
  private notifyListeners(): void {
    const status = { ...this.status };
    this.listeners.forEach((listener) => listener(status));
  }

  /**
   * Mettre à jour le statut de synchronisation
   * @param updates Mises à jour à appliquer au statut
   */
  private updateStatus(updates: Partial<SyncStatus>): void {
    this.status = { ...this.status, ...updates };
    this.notifyListeners();
  }

  /**
   * Obtenir le statut de synchronisation actuel
   * @returns Statut de synchronisation
   */
  public getStatus(): SyncStatus {
    return { ...this.status };
  }

  /**
   * Synchroniser une entité spécifique
   * @param entityType Type d'entité à synchroniser
   * @param options Options de synchronisation
   * @returns Promesse résolue lorsque la synchronisation est terminée
   */
  public async syncEntity(entityType: SyncEntityType, options: SyncOptions = {}): Promise<void> {
    const { forceRefresh = false, background = false, onProgress, onComplete, onError } = options;

    // Si une synchronisation est déjà en cours et que ce n'est pas une synchronisation en arrière-plan, annuler
    if (this.status.inProgress && !background) {
      throw new Error('Une synchronisation est déjà en cours');
    }

    // Mettre à jour le statut
    this.updateStatus({
      inProgress: true,
      progress: 0,
      error: null,
    });

    try {
      // Si forceRefresh est activé, invalider le cache pour cette entité
      if (forceRefresh) {
        this.invalidateCache(entityType);
      }

      // Effectuer la synchronisation en fonction du type d'entité
      switch (entityType) {
        case SyncEntityType.USER:
          await this.syncUsers(onProgress);
          break;
        case SyncEntityType.RETREAT:
          await this.syncRetreats(onProgress);
          break;
        case SyncEntityType.BOOKING:
          await this.syncBookings(onProgress);
          break;
        case SyncEntityType.PROFILE:
          await this.syncProfiles(onProgress);
          break;
        case SyncEntityType.REVIEW:
          await this.syncReviews(onProgress);
          break;
        default:
          throw new Error(`Type d'entité non pris en charge: ${entityType}`);
      }

      // Mettre à jour la date de dernière synchronisation
      const lastSync = { ...this.status.lastSync };
      lastSync[entityType] = new Date();

      // Mettre à jour le statut
      this.updateStatus({
        lastSync,
        inProgress: false,
        progress: 100,
      });

      // Appeler le callback onComplete si fourni
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      // Mettre à jour le statut en cas d'erreur
      this.updateStatus({
        inProgress: false,
        error,
      });

      // Appeler le callback onError si fourni
      if (onError) {
        onError(error);
      }

      // Propager l'erreur
      throw error;
    }
  }

  /**
   * Synchroniser toutes les entités
   * @param options Options de synchronisation
   * @returns Promesse résolue lorsque la synchronisation est terminée
   */
  public async syncAll(options: SyncOptions = {}): Promise<void> {
    const { forceRefresh = false, background = false, onProgress, onComplete, onError } = options;

    // Si une synchronisation est déjà en cours et que ce n'est pas une synchronisation en arrière-plan, annuler
    if (this.status.inProgress && !background) {
      throw new Error('Une synchronisation est déjà en cours');
    }

    // Mettre à jour le statut
    this.updateStatus({
      inProgress: true,
      progress: 0,
      error: null,
    });

    try {
      // Si forceRefresh est activé, invalider tout le cache
      if (forceRefresh) {
        this.invalidateAllCache();
      }

      // Synchroniser chaque type d'entité
      const entityTypes = Object.values(SyncEntityType);
      const totalEntities = entityTypes.length;

      for (let i = 0; i < totalEntities; i++) {
        const entityType = entityTypes[i];
        const entityProgress = (i / totalEntities) * 100;

        // Mettre à jour le statut
        this.updateStatus({
          progress: entityProgress,
        });

        // Appeler le callback onProgress si fourni
        if (onProgress) {
          onProgress(entityProgress);
        }

        // Synchroniser l'entité
        await this.syncEntity(entityType, {
          forceRefresh: false, // Déjà invalidé si nécessaire
          background: true, // Éviter les vérifications redondantes
        });
      }

      // Mettre à jour le statut
      this.updateStatus({
        inProgress: false,
        progress: 100,
      });

      // Appeler le callback onComplete si fourni
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      // Mettre à jour le statut en cas d'erreur
      this.updateStatus({
        inProgress: false,
        error,
      });

      // Appeler le callback onError si fourni
      if (onError) {
        onError(error);
      }

      // Propager l'erreur
      throw error;
    }
  }

  /**
   * Invalider le cache pour une entité spécifique
   * @param entityType Type d'entité
   */
  private invalidateCache(entityType: SyncEntityType): void {
    // Créer un modèle d'URL pour l'entité
    const urlPattern = new RegExp(`/${entityType}s?/`);

    // Invalider le cache pour ce modèle
    apiCache.invalidatePattern(urlPattern);
  }

  /**
   * Invalider tout le cache
   */
  private invalidateAllCache(): void {
    apiCache.clear();
  }

  /**
   * Synchroniser les utilisateurs
   * @param onProgress Callback pour la progression
   */
  private async syncUsers(onProgress?: (progress: number) => void): Promise<void> {
    // Simuler une progression
    if (onProgress) {
      onProgress(0);
      setTimeout(() => onProgress(50), 500);
      setTimeout(() => onProgress(100), 1000);
    }

    // Récupérer les données utilisateur
    await apiClient.get('/users/profile');
  }

  /**
   * Synchroniser les retraites
   * @param onProgress Callback pour la progression
   */
  private async syncRetreats(onProgress?: (progress: number) => void): Promise<void> {
    // Simuler une progression
    if (onProgress) {
      onProgress(0);
      setTimeout(() => onProgress(50), 500);
      setTimeout(() => onProgress(100), 1000);
    }

    // Récupérer les retraites
    await apiClient.get('/retreats');
  }

  /**
   * Synchroniser les réservations
   * @param onProgress Callback pour la progression
   */
  private async syncBookings(onProgress?: (progress: number) => void): Promise<void> {
    // Simuler une progression
    if (onProgress) {
      onProgress(0);
      setTimeout(() => onProgress(50), 500);
      setTimeout(() => onProgress(100), 1000);
    }

    // Récupérer les réservations
    await apiClient.get('/bookings');
  }

  /**
   * Synchroniser les profils
   * @param onProgress Callback pour la progression
   */
  private async syncProfiles(onProgress?: (progress: number) => void): Promise<void> {
    // Simuler une progression
    if (onProgress) {
      onProgress(0);
      setTimeout(() => onProgress(50), 500);
      setTimeout(() => onProgress(100), 1000);
    }

    // Récupérer le profil détaillé
    await apiClient.get('/users/profile/detailed');
  }

  /**
   * Synchroniser les avis
   * @param onProgress Callback pour la progression
   */
  private async syncReviews(onProgress?: (progress: number) => void): Promise<void> {
    // Simuler une progression
    if (onProgress) {
      onProgress(0);
      setTimeout(() => onProgress(50), 500);
      setTimeout(() => onProgress(100), 1000);
    }

    // Récupérer les avis
    await apiClient.get('/reviews');
  }
}

// Exporter une instance unique du service de synchronisation
export const syncService = new SyncService();
