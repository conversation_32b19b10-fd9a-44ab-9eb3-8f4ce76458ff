/**
 * Mock Server MSW - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Serveur de mocks pour les tests avec MSW (Mock Service Worker)
 * pour simuler les appels API.
 */

import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { createMockUser, createMockRetreat, createMockProfessional } from '../setup';

// Données de test
const mockUsers = [
  createMockUser({ id: '1', email: '<EMAIL>' }),
  createMockUser({ id: '2', email: '<EMAIL>', role: 'admin' }),
  createMockUser({ id: '3', email: '<EMAIL>', role: 'creator' }),
];

const mockRetreats = [
  createMockRetreat({ id: 'retreat-1', title: 'Yoga Retreat Provence' }),
  createMockRetreat({ id: 'retreat-2', title: 'Meditation Mountain Retreat', price: 450 }),
  createMockRetreat({ id: 'retreat-3', title: 'Digital Detox Brittany', price: 320 }),
];

const mockProfessionals = [
  createMockProfessional({ id: 'prof-1', name: '<PERSON>' }),
  createMockProfessional({ id: 'prof-2', name: 'Jean <PERSON>', available: false }),
];

const mockBookings = [
  {
    id: 'booking-1',
    userId: '1',
    retreatId: 'retreat-1',
    date: '2025-06-15',
    status: 'confirmed',
    totalAmount: 299,
    personalInfo: {
      firstName: 'Test',
      lastName: 'User',
      phone: '0123456789',
    },
  },
];

// Handlers pour les différentes routes API
export const handlers = [
  // Authentification
  rest.post('/api/auth/login', (req, res, ctx) => {
    const { email, password } = req.body as { email: string; password: string };
    
    // Simuler différents cas d'authentification
    if (email === '<EMAIL>' && password === 'TestPassword123!') {
      const user = mockUsers.find(u => u.email === email);
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          token: 'mock-jwt-token',
          user,
        })
      );
    }
    
    if (email === '<EMAIL>') {
      return res(
        ctx.status(423),
        ctx.json({
          success: false,
          message: 'Compte temporairement verrouillé',
        })
      );
    }
    
    if (email === '<EMAIL>') {
      return res(
        ctx.status(500),
        ctx.json({
          success: false,
          message: 'Erreur serveur',
        })
      );
    }
    
    return res(
      ctx.status(401),
      ctx.json({
        success: false,
        message: 'Identifiants invalides',
      })
    );
  }),

  rest.post('/api/auth/register', (req, res, ctx) => {
    const { email, password, name } = req.body as { email: string; password: string; name: string };
    
    // Vérifier si l'email existe déjà
    if (mockUsers.some(u => u.email === email)) {
      return res(
        ctx.status(409),
        ctx.json({
          success: false,
          message: 'Email déjà utilisé',
        })
      );
    }
    
    const newUser = createMockUser({
      id: Date.now().toString(),
      email,
      name,
    });
    
    mockUsers.push(newUser);
    
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        token: 'mock-jwt-token',
        user: newUser,
      })
    );
  }),

  rest.post('/api/auth/forgot-password', (req, res, ctx) => {
    const { email } = req.body as { email: string };
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'Email de récupération envoyé',
      })
    );
  }),

  // Profil utilisateur
  rest.get('/api/user/profile', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(
        ctx.status(401),
        ctx.json({ success: false, message: 'Non autorisé' })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        user: mockUsers[0],
      })
    );
  }),

  // Retraites
  rest.get('/api/retreats', (req, res, ctx) => {
    const url = new URL(req.url);
    const search = url.searchParams.get('search');
    const minPrice = url.searchParams.get('minPrice');
    const maxPrice = url.searchParams.get('maxPrice');
    const location = url.searchParams.get('location');
    const sort = url.searchParams.get('sort');
    
    let filteredRetreats = [...mockRetreats];
    
    // Filtrage par recherche
    if (search) {
      filteredRetreats = filteredRetreats.filter(retreat =>
        retreat.title.toLowerCase().includes(search.toLowerCase()) ||
        retreat.description.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Filtrage par prix
    if (minPrice) {
      filteredRetreats = filteredRetreats.filter(retreat => retreat.price >= parseInt(minPrice));
    }
    if (maxPrice) {
      filteredRetreats = filteredRetreats.filter(retreat => retreat.price <= parseInt(maxPrice));
    }
    
    // Filtrage par localisation
    if (location) {
      filteredRetreats = filteredRetreats.filter(retreat =>
        retreat.location.includes(location)
      );
    }
    
    // Tri
    if (sort === 'price-asc') {
      filteredRetreats.sort((a, b) => a.price - b.price);
    } else if (sort === 'price-desc') {
      filteredRetreats.sort((a, b) => b.price - a.price);
    } else if (sort === 'rating') {
      filteredRetreats.sort((a, b) => b.rating - a.rating);
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: filteredRetreats,
        total: filteredRetreats.length,
      })
    );
  }),

  rest.get('/api/retreats/:id', (req, res, ctx) => {
    const { id } = req.params;
    const retreat = mockRetreats.find(r => r.id === id);
    
    if (!retreat) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: 'Retraite non trouvée',
        })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: retreat,
      })
    );
  }),

  // Professionnels
  rest.get('/api/professionals', (req, res, ctx) => {
    const url = new URL(req.url);
    const search = url.searchParams.get('search');
    const specialty = url.searchParams.get('specialty');
    const available = url.searchParams.get('available');
    
    let filteredProfessionals = [...mockProfessionals];
    
    if (search) {
      filteredProfessionals = filteredProfessionals.filter(prof =>
        prof.name.toLowerCase().includes(search.toLowerCase()) ||
        prof.title.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    if (specialty) {
      filteredProfessionals = filteredProfessionals.filter(prof =>
        prof.specialties.some(s => s.toLowerCase().includes(specialty.toLowerCase()))
      );
    }
    
    if (available === 'true') {
      filteredProfessionals = filteredProfessionals.filter(prof => prof.available);
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: filteredProfessionals,
        total: filteredProfessionals.length,
      })
    );
  }),

  rest.get('/api/professionals/:id', (req, res, ctx) => {
    const { id } = req.params;
    const professional = mockProfessionals.find(p => p.id === id);
    
    if (!professional) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: 'Professionnel non trouvé',
        })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: professional,
      })
    );
  }),

  // Réservations
  rest.post('/api/bookings', (req, res, ctx) => {
    const bookingData = req.body as any;
    
    // Simuler une retraite non disponible
    if (bookingData.retreatId === 'unavailable-retreat') {
      return res(
        ctx.status(409),
        ctx.json({
          success: false,
          message: 'Cette date n\'est plus disponible',
        })
      );
    }
    
    const newBooking = {
      id: `booking-${Date.now()}`,
      ...bookingData,
      status: 'pending_payment',
      createdAt: new Date().toISOString(),
    };
    
    mockBookings.push(newBooking);
    
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: newBooking,
      })
    );
  }),

  rest.get('/api/user/bookings', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: mockBookings,
      })
    );
  }),

  rest.put('/api/bookings/:id/cancel', (req, res, ctx) => {
    const { id } = req.params;
    const booking = mockBookings.find(b => b.id === id);
    
    if (!booking) {
      return res(
        ctx.status(404),
        ctx.json({
          success: false,
          message: 'Réservation non trouvée',
        })
      );
    }
    
    booking.status = 'cancelled';
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: booking,
      })
    );
  }),

  // Paiements
  rest.post('/api/payments/process', (req, res, ctx) => {
    const paymentData = req.body as any;
    
    // Simuler une carte refusée
    if (paymentData.cardNumber === '****************') {
      return res(
        ctx.status(402),
        ctx.json({
          success: false,
          message: 'Carte refusée',
        })
      );
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        paymentId: `payment-${Date.now()}`,
        bookingId: paymentData.bookingId,
      })
    );
  }),

  // Health check
  rest.get('/api/health', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ status: 'ok' })
    );
  }),
];

// Créer le serveur
export const server = setupServer(...handlers);
