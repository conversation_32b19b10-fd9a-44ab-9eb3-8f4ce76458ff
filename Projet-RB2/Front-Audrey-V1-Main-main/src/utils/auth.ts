/**
 * Utilitaires pour la gestion de l'authentification
 */

import { jwtDecode, JwtPayload } from 'jwt-decode';
import { User } from '../services/api/userService'; // Import User type

/**
 * Récupère le token d'authentification depuis le localStorage
 * @returns Le token d'authentification ou null s'il n'existe pas
 */
export const getAuthToken = (): string | null => {
  try {
    const token = localStorage.getItem('token');
    return token;
  } catch (error) {
    console.error('Error accessing localStorage for auth token:', error);
    return null;
  }
};

/**
 * Enregistre le token d'authentification dans le localStorage
 * @param token Le token d'authentification
 */
export const setAuthToken = (token: string): void => {
  localStorage.setItem('token', token);
};

/**
 * Supprime le token d'authentification du localStorage
 */
export const removeAuthToken = (): void => {
  try {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken'); // Also remove refresh token if it exists
  } catch (error) {
    console.error('Error accessing localStorage for token removal:', error);
  }
};

/**
 * Récupère le token de rafraîchissement depuis le localStorage
 * @returns Le token de rafraîchissement ou null s'il n'existe pas
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem('refreshToken');
};

/**
 * Enregistre le token de rafraîchissement dans le localStorage
 * @param token Le token de rafraîchissement
 */
export const setRefreshToken = (token: string): void => {
  localStorage.setItem('refreshToken', token);
};

/**
 * Supprime le token de rafraîchissement du localStorage
 */
export const removeRefreshToken = (): void => {
  localStorage.removeItem('refreshToken');
};

/**
 * Vérifie si le token JWT est valide (non expiré).
 * @param token Le token JWT.
 * @returns True si le token est valide, false sinon.
 */
export const isTokenValid = (token: string | null): boolean => {
  if (!token) {
    return false;
  }
  try {
    const decoded = jwtDecode<DecodedJwtPayload>(token);
    const currentTime = Date.now() / 1000;
    return decoded.exp ? decoded.exp > currentTime : false;
  } catch (error) {
    console.error('Invalid token:', error);
    return false;
  }
};

/**
 * Vérifie si l'utilisateur est authentifié (token valide existant).
 */
export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  return isTokenValid(token);
};

/**
 * Interface pour le payload d'un token JWT décodé (basique)
 */
interface DecodedJwtPayload extends JwtPayload {
  exp?: number;
  iat?: number;
  sub?: string;
  roles?: string[] | string; // Roles from token can be string or string array
  userId?: string;
  [key: string]: any;
}

/**
 * Récupère les informations de l'utilisateur depuis le token JWT
 * @returns Les informations de l'utilisateur ou null si le token n'existe pas ou est invalide
 */
export const getUserFromToken = (): DecodedJwtPayload | null => {
  const token = getAuthToken();
  if (!token) return null;

  try {
    // Using jwtDecode for safer parsing than atob/JSON.parse
    return jwtDecode<DecodedJwtPayload>(token);
  } catch (error) {
    console.error('Erreur lors de la récupération des informations utilisateur depuis le token:', error);
    return null;
  }
};

/**
 * Vérifie si l'utilisateur (basé sur le User object) a un rôle spécifique.
 * @param user L'objet utilisateur.
 * @param roleName Le nom du rôle à vérifier.
 * @returns True si l'utilisateur a le rôle, false sinon.
 */
export const hasRole = (user: User | null | undefined, roleName: string): boolean => {
  if (!user || !user.role) {
    return false;
  }
  return user.role.toLowerCase() === roleName.toLowerCase();
};

/**
 * Vérifie si l'utilisateur (basé sur le token actuel) a au moins un des rôles spécifiés.
 * @param rolesToCheck Les rôles à vérifier.
 * @returns true si l'utilisateur a au moins un des rôles spécifiés, false sinon.
 */
export const hasAnyRoleFromToken = (rolesToCheck: string[]): boolean => {
  const tokenPayload = getUserFromToken();
  if (!tokenPayload || !tokenPayload.roles) return false;

  const userRoles = tokenPayload.roles;
  const rolesToCheckLower = rolesToCheck.map(r => r.toLowerCase());

  if (typeof userRoles === 'string') {
    return rolesToCheckLower.includes(userRoles.toLowerCase());
  }
  if (Array.isArray(userRoles)) {
    return userRoles.some((userRole) => rolesToCheckLower.includes(userRole.toLowerCase()));
  }
  return false;
};

/**
 * Vérifie si l'utilisateur (basé sur le token actuel) a tous les rôles spécifiés.
 * @param rolesToCheck Les rôles à vérifier.
 * @returns true si l'utilisateur a tous les rôles spécifiés, false sinon.
 */
export const hasAllRolesFromToken = (rolesToCheck: string[]): boolean => {
  const tokenPayload = getUserFromToken();
  if (!tokenPayload || !tokenPayload.roles || rolesToCheck.length === 0) return false;

  const userRoles = tokenPayload.roles;
  const rolesToCheckLower = rolesToCheck.map(r => r.toLowerCase());

  if (typeof userRoles === 'string') {
    // If user has one role (string), they can only have all roles if rolesToCheck contains just that one role.
    return rolesToCheckLower.length === 1 && rolesToCheckLower[0] === userRoles.toLowerCase();
  }
  if (Array.isArray(userRoles)) {
    const userRolesLower = userRoles.map(ur => ur.toLowerCase());
    return rolesToCheckLower.every((roleToCheck) => userRolesLower.includes(roleToCheck));
  }
  return false;
};

/**
 * Vérifie si l'utilisateur (basé sur le User object) a au moins un des rôles spécifiés dans la liste.
 * (Équivalent à "appartient à au moins un de ces groupes")
 * @param user L'objet utilisateur.
 * @param rolesToCheck La liste des rôles à vérifier.
 * @returns true si le rôle de l'utilisateur est dans la liste, false sinon.
 */
export const hasRequiredRole = (
  user: User | null | undefined,
  rolesToCheck: string[],
): boolean => {
  if (!user || !user.role || rolesToCheck.length === 0) return false;
  const userRoleLower = user.role.toLowerCase();
  return rolesToCheck.some(r => r.toLowerCase() === userRoleLower);
};


// The concept of hasAllRequiredRoles for a user with a single role string (user.role)
// means the rolesToCheck array must contain exactly that one role and nothing else.
// This function might be overly specific or misnamed for its new behavior.
// Consider if this is truly needed or if hasRole/hasRequiredRole covers the use cases.
/**
 * Vérifie si le rôle unique de l'utilisateur (basé sur le User object) correspond à l'unique rôle spécifié.
 * @param user L'objet utilisateur.
 * @param rolesToCheck Doit être un tableau avec un seul nom de rôle.
 * @returns true si user.role correspond à l'unique rôle dans rolesToCheck.
 */
export const hasSpecificRoleFromArray = (
  user: User | null | undefined,
  rolesToCheck: string[], // Expecting a single role in this array for the logic to make sense
): boolean => {
  if (!user || !user.role || rolesToCheck.length !== 1) return false;
  return user.role.toLowerCase() === rolesToCheck[0].toLowerCase();
};

/**
 * Vérifie si l'utilisateur est un partenaire.
 * @param user L'objet utilisateur.
 * @returns True si l'utilisateur est un partenaire, false sinon.
 */
export const isPartner = (user: User | null | undefined): boolean => {
  return hasRole(user, 'partner');
};

/**
 * Vérifie si l'utilisateur est un administrateur.
 * @param user L'objet utilisateur.
 * @returns True si l'utilisateur est un administrateur, false sinon.
 */
export const isAdmin = (user: User | null | undefined): boolean => {
  return hasRole(user, 'admin');
};

/**
 * Vérifie si l'utilisateur est un hôte (host).
 * @param user L'objet utilisateur.
 * @returns True si l'utilisateur est un hôte, false sinon.
 */
export const isHost = (user: User | null | undefined): boolean => {
  return hasRole(user, 'host') || hasRole(user, 'partner');
};

/**
 * Décode un token JWT.
 * @param token Le token JWT.
 * @returns Le payload décodé ou null en cas d'erreur.
 */
export const decodeToken = (token: string | null): DecodedJwtPayload | null => {
  if (!token) {
    return null;
  }
  try {
    return jwtDecode<DecodedJwtPayload>(token);
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};
