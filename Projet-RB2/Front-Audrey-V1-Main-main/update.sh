#!/bin/bash

# Script de mise à jour pour Front-Audrey-V1-Main-main
# Ce script automatise le processus de mise à jour de l'application

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
  log "Vérification des prérequis..."
  
  # Vérifier si Docker est installé
  if ! command -v docker &> /dev/null; then
    error "Docker n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si kubectl est installé
  if ! command -v kubectl &> /dev/null; then
    error "kubectl n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  # Vérifier si Helm est installé
  if ! command -v helm &> /dev/null; then
    error "Helm n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
  fi
  
  success "Tous les prérequis sont installés."
}

# Configurer les variables d'environnement
setup_environment() {
  log "Configuration des variables d'environnement..."
  
  # Demander le registre Docker
  read -p "Entrez l'URL du registre Docker (par défaut: registry.retreat-and-be.com): " DOCKER_REGISTRY
  DOCKER_REGISTRY=${DOCKER_REGISTRY:-registry.retreat-and-be.com}
  
  # Demander le namespace Kubernetes
  read -p "Entrez le namespace Kubernetes (par défaut: retreat-and-be): " NAMESPACE
  NAMESPACE=${NAMESPACE:-retreat-and-be}
  
  # Demander le tag de l'image
  read -p "Entrez le nouveau tag de l'image: " IMAGE_TAG
  if [ -z "$IMAGE_TAG" ]; then
    error "Le tag de l'image est requis."
    exit 1
  fi
  
  success "Variables d'environnement configurées."
}

# Construire l'image Docker
build_docker_image() {
  log "Construction de l'image Docker..."
  
  # Construire l'image Docker
  docker build -t ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG} .
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la construction de l'image Docker."
    exit 1
  fi
  
  success "Image Docker construite avec succès."
}

# Pousser l'image vers le registre
push_docker_image() {
  log "Envoi de l'image vers le registre Docker..."
  
  # Se connecter au registre Docker
  echo "Connexion au registre Docker..."
  read -p "Nom d'utilisateur pour le registre Docker: " DOCKER_USERNAME
  read -sp "Mot de passe pour le registre Docker: " DOCKER_PASSWORD
  echo
  
  echo "$DOCKER_PASSWORD" | docker login ${DOCKER_REGISTRY} -u ${DOCKER_USERNAME} --password-stdin
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la connexion au registre Docker."
    exit 1
  fi
  
  # Pousser l'image
  docker push ${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'envoi de l'image vers le registre Docker."
    exit 1
  fi
  
  success "Image Docker envoyée avec succès."
}

# Mettre à jour le déploiement Kubernetes
update_deployment() {
  log "Mise à jour du déploiement Kubernetes..."
  
  # Mettre à jour l'image avec kubectl
  kubectl set image deployment/audrey-frontend audrey-frontend=${DOCKER_REGISTRY}/retreat-and-be/audrey-frontend:${IMAGE_TAG} -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la mise à jour du déploiement."
    exit 1
  fi
  
  success "Déploiement mis à jour avec succès."
}

# Vérifier la mise à jour
verify_update() {
  log "Vérification de la mise à jour..."
  
  # Attendre que le déploiement soit prêt
  kubectl rollout status deployment/audrey-frontend -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de la vérification de la mise à jour."
    exit 1
  fi
  
  # Afficher les informations du déploiement
  kubectl get deployment audrey-frontend -n ${NAMESPACE}
  
  success "Mise à jour vérifiée avec succès."
}

# Fonction pour annuler la mise à jour en cas d'échec
rollback() {
  warn "Annulation de la mise à jour..."
  
  # Annuler le déploiement
  kubectl rollout undo deployment/audrey-frontend -n ${NAMESPACE}
  
  if [ $? -ne 0 ]; then
    error "Erreur lors de l'annulation de la mise à jour."
    exit 1
  fi
  
  # Attendre que le déploiement soit prêt
  kubectl rollout status deployment/audrey-frontend -n ${NAMESPACE}
  
  success "Mise à jour annulée avec succès."
}

# Menu principal
main() {
  echo "================================================"
  echo "  Mise à jour de Front-Audrey-V1-Main-main"
  echo "================================================"
  
  # Vérifier les prérequis
  check_prerequisites
  
  # Configurer les variables d'environnement
  setup_environment
  
  # Demander confirmation
  echo
  echo "Résumé de la configuration:"
  echo "- Registre Docker: ${DOCKER_REGISTRY}"
  echo "- Namespace Kubernetes: ${NAMESPACE}"
  echo "- Nouveau tag de l'image: ${IMAGE_TAG}"
  echo
  
  read -p "Voulez-vous continuer avec cette configuration? (o/n): " CONFIRM
  if [[ $CONFIRM != "o" && $CONFIRM != "O" ]]; then
    log "Mise à jour annulée."
    exit 0
  fi
  
  # Construire l'image Docker
  build_docker_image
  
  # Pousser l'image vers le registre
  push_docker_image
  
  # Mettre à jour le déploiement Kubernetes
  update_deployment
  
  # Vérifier la mise à jour
  if ! verify_update; then
    warn "La mise à jour a échoué. Voulez-vous annuler la mise à jour? (o/n): "
    read ROLLBACK
    if [[ $ROLLBACK == "o" || $ROLLBACK == "O" ]]; then
      rollback
      exit 1
    fi
  fi
  
  success "Mise à jour terminée avec succès!"
}

# Exécuter le script
main
