/**
 * Configuration Vitest - Retreat And Be
 * Date de création: 24 mai 2025
 * 
 * Configuration pour les tests unitaires et d'intégration
 * avec couverture de code et environnement optimisé.
 */

import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  
  test: {
    // Environnement de test
    environment: 'jsdom',
    
    // Fichiers de setup
    setupFiles: ['./src/test/setup.ts'],
    
    // Patterns des fichiers de test
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/__tests__/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // Exclusions
    exclude: [
      'node_modules',
      'dist',
      '.idea',
      '.git',
      '.cache',
      'cypress',
      'src/test/mocks',
      'src/**/*.stories.{js,ts,jsx,tsx}'
    ],
    
    // Configuration des globals
    globals: true,
    
    // Timeout des tests
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Configuration de la couverture
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      
      // Seuils de couverture
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      },
      
      // Inclusions pour la couverture
      include: [
        'src/**/*.{js,ts,jsx,tsx}',
        '!src/**/*.d.ts',
        '!src/**/*.stories.{js,ts,jsx,tsx}',
        '!src/**/*.test.{js,ts,jsx,tsx}',
        '!src/**/*.spec.{js,ts,jsx,tsx}',
        '!src/test/**/*',
        '!src/main.tsx',
        '!src/vite-env.d.ts'
      ],
      
      // Exclusions pour la couverture
      exclude: [
        'node_modules/',
        'src/test/',
        'src/**/*.d.ts',
        'src/**/*.stories.*',
        'src/**/*.test.*',
        'src/**/*.spec.*',
        'cypress/',
        'dist/',
        '.storybook/'
      ]
    },
    
    // Configuration des reporters
    reporters: [
      'default',
      'json',
      'html'
    ],
    
    // Configuration de l'output
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    },
    
    // Configuration du watch mode
    watch: false,
    
    // Configuration des mocks
    mockReset: true,
    clearMocks: true,
    restoreMocks: true,
    
    // Configuration de l'isolation
    isolate: true,
    
    // Configuration du pool
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 1,
        maxThreads: 4
      }
    },
    
    // Configuration des retry
    retry: 2,
    
    // Configuration du bail
    bail: 0,
    
    // Configuration des snapshots
    resolveSnapshotPath: (testPath, snapExtension) => {
      return path.join(
        path.dirname(testPath),
        '__snapshots__',
        path.basename(testPath) + snapExtension
      );
    }
  },
  
  // Configuration des alias pour les imports
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/store': path.resolve(__dirname, './src/store'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/test': path.resolve(__dirname, './src/test')
    }
  },
  
  // Configuration pour les dépendances externes
  define: {
    'import.meta.vitest': 'undefined'
  }
});
