import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Tabs, Form, Input, Alert, Space, Tag, Divider, Steps, Result } from 'antd';
import { useAuth } from '../../hooks/useAuth';
import { useMFA } from '../../hooks/useMFA';
import { MFAService } from '../../services/auth/mfa.service';
import { QrCode } from './QrCode';
import { 
  LockOutlined, 
  SafetyOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  KeyOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Step } = Steps;

/**
 * Test component for MFA functionality
 * This component is used to test the MFA hooks and services
 */
const MFATestComponent: React.FC = () => {
  const { isAuthenticated, user, token } = useAuth();
  const { 
    setupMFA, 
    enableMFA, 
    verifyMFA, 
    disableMFA, 
    setupState, 
    verifyState, 
    resetMFAStates,
    isMfaEnabled 
  } = useMFA();
  
  const mfaService = MFAService.getInstance();
  const [mockState, setMockState] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('status');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    if (mfaService) {
      refreshMockState();
    }
    
    // Reset states when tabs change
    return () => {
      resetMFAStates();
      setVerificationCode('');
      setError(null);
      setSuccessMessage(null);
    };
  }, [activeTab]);

  const refreshMockState = () => {
    setMockState(mfaService.getMockState());
  };

  const handleSetup = async () => {
    if (!isAuthenticated || !token) {
      setError('You must be logged in to set up MFA.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const success = await setupMFA();
      if (success) {
        setSuccessMessage('QR code generated successfully. Please scan it with your authenticator app.');
        setCurrentStep(1);
      } else if (setupState.error) {
        setError(setupState.error);
      }
    } catch (err) {
      setError('An error occurred during MFA setup');
      console.error('MFA setup error:', err);
    } finally {
      setLoading(false);
      refreshMockState();
    }
  };

  const handleEnable = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const success = await enableMFA(verificationCode);
      if (success) {
        setSuccessMessage('MFA has been successfully enabled!');
        setCurrentStep(2);
      } else if (verifyState.error) {
        setError(verifyState.error);
      }
    } catch (err) {
      setError('Failed to enable MFA');
      console.error('MFA enable error:', err);
    } finally {
      setLoading(false);
      refreshMockState();
    }
  };

  const handleVerify = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const success = await verifyMFA(verificationCode);
      if (success) {
        setSuccessMessage('MFA verification successful!');
      } else if (verifyState.error) {
        setError(verifyState.error);
      }
    } catch (err) {
      setError('Failed to verify MFA code');
      console.error('MFA verification error:', err);
    } finally {
      setLoading(false);
      refreshMockState();
    }
  };

  const handleDisable = async () => {
    if (!verificationCode) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const success = await disableMFA(verificationCode);
      if (success) {
        setSuccessMessage('MFA has been successfully disabled!');
      } else if (verifyState.error) {
        setError(verifyState.error);
      }
    } catch (err) {
      setError('Failed to disable MFA');
      console.error('MFA disable error:', err);
    } finally {
      setLoading(false);
      refreshMockState();
    }
  };

  const renderValidTestCodes = () => (
    <>
      <Divider>Valid Test Codes</Divider>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text strong>You can use any of these codes for testing:</Text>
        <div>
          {mockState?.validTestCodes?.map((code: string) => (
            <Tag key={code} color="blue" style={{ marginBottom: 8 }}>{code}</Tag>
          ))}
        </div>
      </Space>
    </>
  );

  const renderStatus = () => (
    <Card>
      <Title level={4}>MFA Status</Title>
      <Paragraph>Current status of MFA for the logged-in user:</Paragraph>

      {user ? (
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8 }}>
            <Text strong>User: </Text>
            <Text>{user.email}</Text>
          </div>
          <div style={{ marginBottom: 8 }}>
            <Text strong>MFA Enabled: </Text>
            {user.twoFactorEnabled ? (
              <Tag color="green" icon={<CheckCircleOutlined />}>Enabled</Tag>
            ) : (
              <Tag color="red" icon={<CloseCircleOutlined />}>Disabled</Tag>
            )}
          </div>
          <div style={{ marginBottom: 8 }}>
            <Text strong>MFA Verified in Current Session: </Text>
            {mockState?.mfaVerified ? (
              <Tag color="green" icon={<CheckCircleOutlined />}>Verified</Tag>
            ) : (
              <Tag color="orange" icon={<CloseCircleOutlined />}>Not Verified</Tag>
            )}
          </div>
        </div>
      ) : (
        <Alert message="Not logged in" type="warning" showIcon />
      )}

      {renderValidTestCodes()}
    </Card>
  );

  const renderSetup = () => (
    <Card>
      <Title level={4}>Set Up MFA</Title>
      <Paragraph>Follow these steps to set up MFA for your account:</Paragraph>

      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="Generate" description="Create secret key" />
        <Step title="Scan" description="Scan QR code" />
        <Step title="Verify" description="Verify setup" />
      </Steps>

      {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
      {successMessage && <Alert message={successMessage} type="success" style={{ marginBottom: 16 }} />}

      {currentStep === 0 && (
        <Button 
          type="primary" 
          onClick={handleSetup} 
          loading={loading}
          icon={<SafetyOutlined />}
          disabled={user?.twoFactorEnabled}
        >
          Generate QR Code
        </Button>
      )}

      {currentStep >= 1 && (
        <div style={{ marginTop: 16 }}>
          <Title level={5}>Scan this QR code with your authenticator app:</Title>
          <div style={{ textAlign: 'center', margin: '16px 0' }}>
            {setupState.qrCode && <QrCode url={setupState.qrCode} />}
          </div>
          <div style={{ marginBottom: 16 }}>
            <Text strong>Secret key (if you can't scan the QR code):</Text>
            <br />
            <Text code copyable>{setupState.secret}</Text>
          </div>

          <Form layout="vertical">
            <Form.Item 
              label="Verification Code" 
              required 
              help="Enter the 6-digit code from your authenticator app"
            >
              <Input
                prefix={<KeyOutlined />}
                value={verificationCode}
                onChange={e => setVerificationCode(e.target.value)}
                placeholder="6-digit code"
                maxLength={6}
              />
            </Form.Item>
            <Form.Item>
              <Button 
                type="primary" 
                onClick={handleEnable} 
                loading={loading}
                disabled={user?.twoFactorEnabled}
              >
                Verify and Enable MFA
              </Button>
            </Form.Item>
          </Form>
        </div>
      )}

      {currentStep === 2 && (
        <Result
          status="success"
          title="MFA Successfully Enabled!"
          subTitle="Your account is now more secure with two-factor authentication."
        />
      )}

      {renderValidTestCodes()}
    </Card>
  );

  const renderVerify = () => (
    <Card>
      <Title level={4}>Verify MFA Code</Title>
      <Paragraph>
        Test MFA verification using a code from your authenticator app:
      </Paragraph>

      {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
      {successMessage && <Alert message={successMessage} type="success" style={{ marginBottom: 16 }} />}

      <Form layout="vertical">
        <Form.Item 
          label="Verification Code" 
          required
          help="Enter the 6-digit code from your authenticator app"
        >
          <Input
            prefix={<KeyOutlined />}
            value={verificationCode}
            onChange={e => setVerificationCode(e.target.value)}
            placeholder="6-digit code"
            maxLength={6}
          />
        </Form.Item>
        <Form.Item>
          <Button 
            type="primary" 
            onClick={handleVerify} 
            loading={loading}
            disabled={!user?.twoFactorEnabled}
            icon={<LockOutlined />}
          >
            Verify MFA Code
          </Button>
        </Form.Item>
      </Form>

      {renderValidTestCodes()}
    </Card>
  );

  const renderDisable = () => (
    <Card>
      <Title level={4}>Disable MFA</Title>
      <Paragraph>
        Test disabling MFA for the current user:
      </Paragraph>

      {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
      {successMessage && <Alert message={successMessage} type="success" style={{ marginBottom: 16 }} />}

      <Form layout="vertical">
        <Form.Item 
          label="Verification Code" 
          required
          help="Enter the 6-digit code from your authenticator app"
        >
          <Input
            prefix={<KeyOutlined />}
            value={verificationCode}
            onChange={e => setVerificationCode(e.target.value)}
            placeholder="6-digit code"
            maxLength={6}
          />
        </Form.Item>
        <Form.Item>
          <Button 
            type="primary" 
            danger
            onClick={handleDisable} 
            loading={loading}
            disabled={!user?.twoFactorEnabled}
            icon={<CloseCircleOutlined />}
          >
            Disable MFA
          </Button>
        </Form.Item>
      </Form>

      {renderValidTestCodes()}
    </Card>
  );

  return (
    <Card style={{ maxWidth: 800, margin: '20px auto' }}>
      <Title level={3}>MFA Test Component</Title>
      <Paragraph>
        This component allows you to test all MFA functionalities in a single place.
      </Paragraph>

      {!isAuthenticated ? (
        <Alert 
          message="Authentication Required" 
          description="You need to be logged in to test MFA features. Please log in from the test page."
          type="warning" 
          showIcon 
        />
      ) : (
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          destroyInactiveTabPane
        >
          <TabPane tab="MFA Status" key="status">
            {renderStatus()}
          </TabPane>
          <TabPane tab="Setup MFA" key="setup">
            {renderSetup()}
          </TabPane>
          <TabPane tab="Verify MFA" key="verify">
            {renderVerify()}
          </TabPane>
          <TabPane tab="Disable MFA" key="disable">
            {renderDisable()}
          </TabPane>
        </Tabs>
      )}
    </Card>
  );
};

export default MFATestComponent; 