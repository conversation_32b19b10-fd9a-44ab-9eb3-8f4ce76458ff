import { useState, useContext, useCallback } from 'react';
import { AuthContext } from '../context/AuthContext';
import { MFAService } from '../services/auth/mfa.service';
import { useNavigate } from 'react-router-dom';

interface MFASetupState {
  qrCode: string;
  secret: string;
  isLoading: boolean;
  isSuccess: boolean;
  error: string | null;
}

interface MFAVerifyState {
  isLoading: boolean;
  isSuccess: boolean;
  error: string | null;
}

export const useMFA = () => {
  const { token, user, setMfaVerified, setUser } = useContext(AuthContext);
  const navigate = useNavigate();
  const mfaService = MFAService.getInstance();

  // MFA Setup State
  const [setupState, setSetupState] = useState<MFASetupState>({
    qrCode: '',
    secret: '',
    isLoading: false,
    isSuccess: false,
    error: null,
  });

  // MFA Verification State
  const [verifyState, setVerifyState] = useState<MFAVerifyState>({
    isLoading: false,
    isSuccess: false,
    error: null,
  });

  // Initialize MFA setup
  const setupMFA = useCallback(async () => {
    if (!token) {
      setSetupState(prev => ({ ...prev, error: 'Authentication required' }));
      return false;
    }

    try {
      setSetupState(prev => ({ ...prev, isLoading: true, error: null }));
      mfaService.setToken(token);
      
      const response = await mfaService.setup();
      
      if (response.success) {
        setSetupState({
          qrCode: response.qrCodeUrl,
          secret: response.secret,
          isLoading: false,
          isSuccess: true,
          error: null,
        });
        return true;
      } else {
        setSetupState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to generate MFA setup',
        }));
        return false;
      }
    } catch (error: any) {
      console.error('MFA setup error:', error);
      setSetupState(prev => ({
        ...prev,
        isLoading: false,
        error: error.response?.data?.message || 'An error occurred during MFA setup',
      }));
      return false;
    }
  }, [token, mfaService]);

  // Enable MFA with verification code
  const enableMFA = useCallback(async (code: string) => {
    if (!token) {
      setVerifyState(prev => ({ ...prev, error: 'Authentication required' }));
      return false;
    }

    try {
      setVerifyState(prev => ({ ...prev, isLoading: true, error: null }));
      mfaService.setToken(token);
      
      const response = await mfaService.enable(code);
      
      if (response.success) {
        setVerifyState({
          isLoading: false,
          isSuccess: true,
          error: null,
        });
        
        // Update user in context to reflect 2FA is now enabled
        if (user) {
          const updatedUser = {
            ...user,
            twoFactorEnabled: true
          };
          setUser(updatedUser);
        }
        
        return true;
      } else {
        setVerifyState({
          isLoading: false,
          isSuccess: false,
          error: response.message || 'Failed to enable MFA',
        });
        return false;
      }
    } catch (error: any) {
      console.error('MFA enable error:', error);
      setVerifyState({
        isLoading: false,
        isSuccess: false,
        error: error.response?.data?.message || 'An error occurred while enabling MFA',
      });
      return false;
    }
  }, [token, mfaService, user, setUser]);

  // Verify MFA code during login
  const verifyMFA = useCallback(async (code: string) => {
    if (!token) {
      setVerifyState(prev => ({ ...prev, error: 'Authentication required' }));
      return false;
    }

    try {
      setVerifyState(prev => ({ ...prev, isLoading: true, error: null }));
      mfaService.setToken(token);
      
      const response = await mfaService.verify2FA(code);
      
      if (response.success) {
        setVerifyState({
          isLoading: false,
          isSuccess: true,
          error: null,
        });
        setMfaVerified(true);
        return true;
      } else {
        setVerifyState({
          isLoading: false,
          isSuccess: false,
          error: response.message || 'Invalid verification code',
        });
        return false;
      }
    } catch (error: any) {
      console.error('MFA verification error:', error);
      setVerifyState({
        isLoading: false,
        isSuccess: false,
        error: error.response?.data?.message || 'An error occurred during verification',
      });
      return false;
    }
  }, [token, mfaService, setMfaVerified]);

  // Verify MFA and navigate on success
  const verifyMFAAndNavigate = useCallback(async (code: string, redirectPath: string = '/dashboard') => {
    const success = await verifyMFA(code);
    if (success) {
      navigate(redirectPath, { replace: true });
      return true;
    }
    return false;
  }, [verifyMFA, navigate]);

  // Disable MFA
  const disableMFA = useCallback(async (code: string) => {
    if (!token) {
      setVerifyState(prev => ({ ...prev, error: 'Authentication required' }));
      return false;
    }

    try {
      setVerifyState(prev => ({ ...prev, isLoading: true, error: null }));
      mfaService.setToken(token);
      
      const response = await mfaService.disable(code);
      
      if (response.success) {
        setVerifyState({
          isLoading: false,
          isSuccess: true,
          error: null,
        });
        
        // Update user in context to reflect 2FA is now disabled
        if (user) {
          const updatedUser = {
            ...user,
            twoFactorEnabled: false
          };
          setUser(updatedUser);
        }
        
        return true;
      } else {
        setVerifyState({
          isLoading: false,
          isSuccess: false,
          error: response.message || 'Failed to disable MFA',
        });
        return false;
      }
    } catch (error: any) {
      console.error('MFA disable error:', error);
      setVerifyState({
        isLoading: false,
        isSuccess: false,
        error: error.response?.data?.message || 'An error occurred while disabling MFA',
      });
      return false;
    }
  }, [token, mfaService, user, setUser]);

  // Reset MFA states
  const resetMFAStates = useCallback(() => {
    setSetupState({
      qrCode: '',
      secret: '',
      isLoading: false,
      isSuccess: false,
      error: null,
    });
    
    setVerifyState({
      isLoading: false,
      isSuccess: false,
      error: null,
    });
  }, []);

  return {
    setupState,
    verifyState,
    setupMFA,
    enableMFA,
    verifyMFA,
    verifyMFAAndNavigate,
    disableMFA,
    resetMFAStates,
    isMfaEnabled: user?.twoFactorEnabled || false,
  };
}; 