import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout, Menu, Button, Typography, Space } from 'antd';
import { useAuth } from '../hooks/useAuth';
import { Link } from 'react-router-dom';
import { 
  UserOutlined, 
  SafetyOutlined, 
  SettingOutlined, 
  DashboardOutlined,
  LogoutOutlined
} from '@ant-design/icons';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

const AppLayout: React.FC = () => {
  const { user, logout } = useAuth();

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: '#fff',
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.09)'
      }}>
        <div>
          <Title level={4} style={{ margin: 0 }}>
            MFA Implementation Demo
          </Title>
        </div>
        
        <Menu
          mode="horizontal"
          style={{ flex: 1, justifyContent: 'center' }}
          selectedKeys={[window.location.pathname]}
        >
          <Menu.Item key="/dashboard" icon={<DashboardOutlined />}>
            <Link to="/dashboard">Dashboard</Link>
          </Menu.Item>
          <Menu.Item key="/setup-mfa" icon={<SafetyOutlined />}>
            <Link to="/setup-mfa">Setup MFA</Link>
          </Menu.Item>
          <Menu.Item key="/settings" icon={<SettingOutlined />}>
            <Link to="/settings">Settings</Link>
          </Menu.Item>
          <Menu.Item key="/mfa-test" icon={<SafetyOutlined />}>
            <Link to="/mfa-test">MFA Test</Link>
          </Menu.Item>
        </Menu>
        
        <Space>
          <span>
            <UserOutlined /> {user?.email}
          </span>
          <Button 
            icon={<LogoutOutlined />} 
            onClick={logout}
            danger
          >
            Logout
          </Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <div style={{ 
          background: '#fff', 
          padding: '24px', 
          minHeight: 'calc(100vh - 200px)',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.09)'
        }}>
          <Outlet />
        </div>
      </Content>
      
      <Footer style={{ textAlign: 'center' }}>
        MFA Implementation Demo ©{new Date().getFullYear()} Created with React & Ant Design
      </Footer>
    </Layout>
  );
};

export default AppLayout; 