import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import Router from './router';
import './index.css';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <ConfigProvider theme={{ 
      token: { 
        colorPrimary: '#1677ff',
      },
    }}>
      <Router />
    </ConfigProvider>
  </React.StrictMode>,
); 