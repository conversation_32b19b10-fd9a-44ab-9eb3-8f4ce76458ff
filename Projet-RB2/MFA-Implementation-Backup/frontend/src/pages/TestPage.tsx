import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, List, Space, Divider, Input, Form, Alert, Tag } from 'antd';
import { Link } from 'react-router-dom';
import { 
  SafetyOutlined, 
  LockOutlined, 
  UserOutlined, 
  ExperimentOutlined,
  LoginOutlined,
  SettingOutlined,
  PlusCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../hooks/useAuth';
import { MFAService } from '../services/auth/mfa.service';

const { Title, Paragraph, Text } = Typography;

interface TestFeature {
  id: string;
  title: string;
  description: string;
  path: string;
  icon: React.ReactNode;
}

const TestPage: React.FC = () => {
  const { isAuthenticated, user, login, logout } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [mockUser, setMockUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mockState, setMockState] = useState<any>(null);
  const [testCode, setTestCode] = useState('123456');
  
  const mfaService = MFAService.getInstance();

  useEffect(() => {
    if (isAuthenticated) {
      refreshMockState();
    }
  }, [isAuthenticated]);
  
  const refreshMockState = () => {
    if (mfaService) {
      setMockState(mfaService.getMockState());
    }
  };
  
  const resetMockState = () => {
    if (mfaService) {
      const newState = mfaService.resetMockState();
      setMockState(newState);
      setError(null);
    }
  };
  
  const addValidTestCode = () => {
    if (!testCode || testCode.length !== 6 || !/^\d+$/.test(testCode)) {
      setError('Test code must be a 6-digit number');
      return;
    }
    
    mfaService.addValidTestCode(testCode);
    refreshMockState();
    setError(null);
    setTestCode('');
  };

  const features: TestFeature[] = [
    {
      id: 'setup-mfa',
      title: 'Set Up MFA',
      description: 'Test the MFA setup flow including QR code generation',
      path: '/setup-mfa',
      icon: <SafetyOutlined />
    },
    {
      id: 'verify-mfa',
      title: 'Verify MFA',
      description: 'Test the MFA verification flow',
      path: '/verify-mfa',
      icon: <LockOutlined />
    },
    {
      id: 'security-settings',
      title: 'Security Settings',
      description: 'Test the security settings page including MFA management',
      path: '/settings',
      icon: <UserOutlined />
    },
    {
      id: 'mfa-test',
      title: 'MFA Test Component',
      description: 'Direct testing of MFA hooks and service methods',
      path: '/mfa-test',
      icon: <ExperimentOutlined />
    }
  ];

  const handleMockLogin = () => {
    setLoading(true);
    setError(null);
    
    // Create a mock user with the provided email
    try {
      const mockUser = {
        id: '123456',
        email: email,
        name: 'Test User',
        role: 'USER',
        twoFactorEnabled: false,
        isVerified: true
      };
      
      // Generate a mock token
      const mockToken = 'mock_token_' + Date.now();
      
      // Set the mock user in the MFA service
      mfaService.setMockUser(mockUser);
      
      // Use the auth context login method
      login(mockToken, mockUser);
      
      setMockUser(mockUser);
      refreshMockState();
    } catch (err) {
      setError('Failed to create mock user session');
      console.error('Mock login error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleMockLogout = () => {
    logout();
    setMockUser(null);
    refreshMockState();
  };

  return (
    <Card style={{ maxWidth: 800, margin: '24px auto' }}>
      <Title level={2}>MFA Testing Page</Title>
      <Paragraph>
        This page contains links to test various aspects of the MFA implementation.
      </Paragraph>

      <Card type="inner" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>Try our improved testing experience:</Text>
          <Link to="/test-login">
            <Button type="primary" icon={<LoginOutlined />} size="large" block>
              Go to Enhanced MFA Testing Page
            </Button>
          </Link>
          <Paragraph type="secondary" style={{ fontSize: 12 }}>
            The enhanced testing page provides a more comprehensive testing experience with a dedicated login form.
          </Paragraph>
        </Space>
      </Card>

      {!isAuthenticated ? (
        <Card type="inner" style={{ marginBottom: 16 }}>
          <Title level={4}>Mock Login</Title>
          <Paragraph>
            Use this form to create a test user session for MFA testing.
          </Paragraph>
          
          {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
          
          <Form layout="vertical">
            <Form.Item label="Email">
              <Input 
                value={email} 
                onChange={(e) => setEmail(e.target.value)} 
                placeholder="Enter email for test user"
                prefix={<UserOutlined />}
              />
            </Form.Item>
            <Form.Item>
              <Button 
                type="primary" 
                icon={<LoginOutlined />} 
                onClick={handleMockLogin}
                loading={loading}
              >
                Create Test Session
              </Button>
            </Form.Item>
          </Form>
        </Card>
      ) : (
        <>
          <Card type="inner" style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Title level={4}>Current User</Title>
                <Paragraph>
                  <strong>Email:</strong> {user?.email}<br />
                  <strong>2FA Enabled:</strong> {user?.twoFactorEnabled ? 'Yes' : 'No'}
                </Paragraph>
              </div>
              <Button danger onClick={handleMockLogout}>
                End Test Session
              </Button>
            </div>
          </Card>
          
          <Card type="inner" style={{ marginBottom: 16 }}>
            <Title level={4}>
              <SettingOutlined /> MFA Test Settings
            </Title>
            
            {error && <Alert message={error} type="error" style={{ marginBottom: 16 }} />}
            
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>Valid Test Codes</Title>
              <Paragraph>
                For testing purposes, you can only use these codes for verification:
              </Paragraph>
              <div style={{ marginBottom: 8 }}>
                {mockState?.validTestCodes?.map((code: string) => (
                  <Tag key={code} color="blue" style={{ marginBottom: 8 }}>{code}</Tag>
                ))}
              </div>
              <Space.Compact style={{ width: '100%', marginBottom: 16 }}>
                <Input
                  placeholder="Add new 6-digit code"
                  value={testCode}
                  onChange={(e) => setTestCode(e.target.value)}
                  maxLength={6}
                />
                <Button 
                  type="primary"
                  icon={<PlusCircleOutlined />}
                  onClick={addValidTestCode}
                >
                  Add Code
                </Button>
              </Space.Compact>
            </div>
            
            <div>
              <Title level={5}>Current MFA State</Title>
              {mockState ? (
                <ul>
                  <li><Text strong>2FA Enabled:</Text> {mockState.twoFactorEnabled ? 'Yes' : 'No'}</li>
                  <li><Text strong>MFA Verified:</Text> {mockState.mfaVerified ? 'Yes' : 'No'}</li>
                  <li><Text strong>Secret:</Text> {mockState.secret || 'Not set'}</li>
                </ul>
              ) : (
                <Text>Mock state not available</Text>
              )}
              <Button 
                danger 
                onClick={resetMockState}
                style={{ marginTop: 8 }}
              >
                Reset MFA State
              </Button>
            </div>
          </Card>

          <Divider>Test Features</Divider>

          <List
            itemLayout="horizontal"
            dataSource={features}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  avatar={item.icon}
                  title={<Link to={item.path}>{item.title}</Link>}
                  description={item.description}
                />
                <Link to={item.path}>
                  <Button type="primary">Test</Button>
                </Link>
              </List.Item>
            )}
          />
        </>
      )}
    </Card>
  );
};

export default TestPage; 