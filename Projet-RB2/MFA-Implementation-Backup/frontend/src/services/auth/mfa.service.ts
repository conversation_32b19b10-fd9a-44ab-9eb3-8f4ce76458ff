import axios from 'axios';
import { API_BASE_URL } from '../../config';

interface MFASetupResponse {
  qrCodeUrl: string;
  secret: string;
  success: boolean;
}

interface MFAStatusResponse {
  requiresMfa: boolean;
  success: boolean;
}

/**
 * Service to handle two-factor authentication operations
 */
export class MFAService {
  private static instance: MFAService;
  private token: string | null = null;
  private baseUrl: string;
  private mockMode: boolean = true; // For testing purposes
  
  // Mock state for testing
  private mockTwoFactorEnabled: boolean = false;
  private mockSecret: string = '';
  private mockMfaVerified: boolean = false;
  
  // For testing verifications
  private validTestCodes: string[] = ['123456', '000000', '111111'];
  private mockUser: any = null;

  private constructor() {
    this.baseUrl = `${API_BASE_URL}/api/mfa`;
  }

  /**
   * Get a singleton instance of the MFAService
   */
  public static getInstance(): MFAService {
    if (!MFAService.instance) {
      MFAService.instance = new MFAService();
    }
    return MFAService.instance;
  }

  /**
   * Enable or disable mock mode for testing
   */
  public setMockMode(enabled: boolean): void {
    this.mockMode = enabled;
  }

  /**
   * Set the mock user for testing
   */
  public setMockUser(user: any): void {
    this.mockUser = user;
    
    // If user has 2FA already enabled, update mock state
    if (user && user.twoFactorEnabled) {
      this.mockTwoFactorEnabled = true;
    }
  }

  /**
   * Set the authentication token for API calls
   */
  public setToken(token: string): void {
    this.token = token;
  }

  /**
   * Add a valid test code for verification
   * This allows testing specific codes during development
   */
  public addValidTestCode(code: string): void {
    if (code && code.length === 6 && /^\d+$/.test(code)) {
      this.validTestCodes.push(code);
    }
  }

  /**
   * Get headers for authenticated requests
   */
  private getHeaders() {
    return {
      headers: {
        'Content-Type': 'application/json',
        Authorization: this.token ? `Bearer ${this.token}` : '',
      },
    };
  }

  /**
   * For testing: get current mock state
   */
  public getMockState() {
    return {
      twoFactorEnabled: this.mockTwoFactorEnabled,
      mfaVerified: this.mockMfaVerified,
      secret: this.mockSecret,
      validTestCodes: this.validTestCodes,
      user: this.mockUser
    };
  }

  /**
   * For testing: reset mock state
   */
  public resetMockState() {
    this.mockTwoFactorEnabled = false;
    this.mockSecret = '';
    this.mockMfaVerified = false;
    // Don't reset valid test codes to maintain them across resets
    return this.getMockState();
  }

  /**
   * Initialize 2FA setup by generating a QR code
   */
  public async setup(): Promise<MFASetupResponse> {
    if (this.mockMode) {
      return this.mockSetup();
    }

    try {
      const response = await axios.get(
        `${this.baseUrl}/setup`,
        this.getHeaders()
      );
      return response.data;
    } catch (error) {
      console.error('Error setting up MFA:', error);
      throw error;
    }
  }

  /**
   * Enable 2FA with the provided verification code
   */
  public async enable(code: string): Promise<{ success: boolean; message: string }> {
    if (this.mockMode) {
      return this.mockEnable(code);
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/enable`,
        { code },
        this.getHeaders()
      );
      return response.data;
    } catch (error) {
      console.error('Error enabling MFA:', error);
      throw error;
    }
  }

  /**
   * Verify a 2FA code
   */
  public async verify2FA(code: string): Promise<{ success: boolean; message: string }> {
    if (this.mockMode) {
      return this.mockVerify(code);
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/verify`,
        { code },
        this.getHeaders()
      );
      return response.data;
    } catch (error) {
      console.error('Error verifying MFA code:', error);
      throw error;
    }
  }

  /**
   * Disable 2FA for the current user
   */
  public async disable(code: string): Promise<{ success: boolean; message: string }> {
    if (this.mockMode) {
      return this.mockDisable(code);
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/disable`,
        { code },
        this.getHeaders()
      );
      return response.data;
    } catch (error) {
      console.error('Error disabling MFA:', error);
      throw error;
    }
  }

  /**
   * Check if the current session requires 2FA verification
   */
  public async checkMfaStatus(): Promise<MFAStatusResponse> {
    if (this.mockMode) {
      return this.mockCheckStatus();
    }

    try {
      const response = await axios.get(
        `${this.baseUrl}/check-status`,
        this.getHeaders()
      );
      return response.data;
    } catch (error) {
      console.error('Error checking MFA status:', error);
      throw error;
    }
  }

  // Mock implementations for testing
  private mockSetup(): Promise<MFASetupResponse> {
    return new Promise((resolve, reject) => {
      // Ensure we have a token before proceeding
      if (!this.token) {
        reject({
          response: {
            data: {
              message: 'Authentication token missing'
            }
          }
        });
        return;
      }
      
      setTimeout(() => {
        // Generate a consistent secret for this session
        this.mockSecret = 'JBSWY3DPEHPK3PXP';
        
        resolve({
          qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/TestApp:' + 
            (this.mockUser?.email || '<EMAIL>') + 
            '?secret=JBSWY3DPEHPK3PXP&issuer=TestApp',
          secret: this.mockSecret,
          success: true
        });
      }, 1000);
    });
  }

  private mockEnable(code: string): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      // Ensure we have a token before proceeding
      if (!this.token) {
        reject({
          response: {
            data: {
              message: 'Authentication token missing'
            }
          }
        });
        return;
      }
      
      // Ensure we have a secret key (setup has been called)
      if (!this.mockSecret) {
        reject({
          response: {
            data: {
              message: 'MFA setup not completed. Please set up MFA first.'
            }
          }
        });
        return;
      }
      
      setTimeout(() => {
        // For testing, verify if code is one of our valid test codes
        if (code && this.isValidCode(code)) {
          // Update the mock state
          this.mockTwoFactorEnabled = true;
          this.mockMfaVerified = true;
          
          // Update the mock user if available
          if (this.mockUser) {
            this.mockUser.twoFactorEnabled = true;
          }
          
          resolve({
            success: true,
            message: 'MFA has been enabled successfully.'
          });
        } else {
          reject({
            response: {
              data: {
                message: 'Invalid verification code. Please try again.'
              }
            }
          });
        }
      }, 1000);
    });
  }

  private mockVerify(code: string): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      // Ensure we have a token before proceeding
      if (!this.token) {
        reject({
          response: {
            data: {
              message: 'Authentication token missing'
            }
          }
        });
        return;
      }
      
      setTimeout(() => {
        // Check if 2FA is enabled first
        if (!this.mockTwoFactorEnabled) {
          reject({
            response: {
              data: {
                message: 'Two-factor authentication is not enabled for this account.'
              }
            }
          });
          return;
        }
        
        // For testing, verify if code is one of our valid test codes
        if (code && this.isValidCode(code)) {
          // Update the verification status
          this.mockMfaVerified = true;
          
          resolve({
            success: true,
            message: 'MFA verification successful.'
          });
        } else {
          reject({
            response: {
              data: {
                message: 'Invalid verification code. Please try again.'
              }
            }
          });
        }
      }, 1000);
    });
  }

  private mockDisable(code: string): Promise<{ success: boolean; message: string }> {
    return new Promise((resolve, reject) => {
      // Ensure we have a token before proceeding
      if (!this.token) {
        reject({
          response: {
            data: {
              message: 'Authentication token missing'
            }
          }
        });
        return;
      }
      
      setTimeout(() => {
        // Check if 2FA is enabled first
        if (!this.mockTwoFactorEnabled) {
          reject({
            response: {
              data: {
                message: 'Two-factor authentication is not enabled for this account.'
              }
            }
          });
          return;
        }
        
        // For testing, verify if code is one of our valid test codes
        if (code && this.isValidCode(code)) {
          // Update the mock state
          this.mockTwoFactorEnabled = false;
          this.mockMfaVerified = false;
          this.mockSecret = '';
          
          // Update the mock user if available
          if (this.mockUser) {
            this.mockUser.twoFactorEnabled = false;
          }
          
          resolve({
            success: true,
            message: 'MFA has been disabled successfully.'
          });
        } else {
          reject({
            response: {
              data: {
                message: 'Invalid verification code. Please try again.'
              }
            }
          });
        }
      }, 1000);
    });
  }

  private mockCheckStatus(): Promise<MFAStatusResponse> {
    return new Promise((resolve, reject) => {
      // Ensure we have a token before proceeding
      if (!this.token) {
        reject({
          response: {
            data: {
              message: 'Authentication token missing'
            }
          }
        });
        return;
      }
      
      setTimeout(() => {
        resolve({
          requiresMfa: this.mockTwoFactorEnabled && !this.mockMfaVerified,
          success: true
        });
      }, 500);
    });
  }
  
  /**
   * Helper method to validate codes against our mock valid codes
   */
  private isValidCode(code: string): boolean {
    if (!code || code.length !== 6 || !/^\d+$/.test(code)) {
      return false;
    }
    
    // Check if it's one of our valid test codes
    return this.validTestCodes.includes(code);
  }
} 