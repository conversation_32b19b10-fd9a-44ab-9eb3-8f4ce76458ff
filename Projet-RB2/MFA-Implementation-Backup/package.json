{"name": "rb2-mfa-implementation", "version": "1.0.0", "description": "Two-factor authentication implementation with comprehensive testing tools", "main": "index.js", "scripts": {"start": "vite", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest"}, "repository": {"type": "git", "url": "git+https://github.com/lucnerdinaszape/rb2-mfa-implementation.git"}, "keywords": ["2fa", "mfa", "authentication", "security", "totp", "react"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/lucnerdinaszape/rb2-mfa-implementation/issues"}, "homepage": "https://github.com/lucnerdinaszape/rb2-mfa-implementation#readme", "dependencies": {"@ant-design/icons": "^5.0.0", "antd": "^5.0.0", "axios": "^1.3.0", "otplib": "^12.0.1", "qrcode": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@types/node": "^18.11.0", "@types/qrcode": "^1.5.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^3.0.0", "typescript": "^4.9.0", "vite": "^4.0.0", "vitest": "^0.28.0"}}