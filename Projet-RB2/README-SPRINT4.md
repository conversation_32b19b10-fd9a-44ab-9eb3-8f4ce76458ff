# Sprint 4 - Système de Recommandation Amélioré

Ce document présente les fonctionnalités implémentées dans le Sprint 4 du système de recommandation de Retreat And Be.

## Table des matières

1. [Présentation du Sprint 4](#présentation-du-sprint-4)
2. [Fonctionnalités implémentées](#fonctionnalités-implémentées)
3. [Architecture](#architecture)
4. [Installation et configuration](#installation-et-configuration)
5. [Tests](#tests)
6. [Documentation](#documentation)
7. [Prochaines étapes](#prochaines-étapes)

## Présentation du Sprint 4

Le Sprint 4 se concentre sur l'amélioration du système de recommandation avec les objectifs suivants :

- Améliorer la transparence des recommandations en fournissant des explications détaillées
- Intégrer le système de recommandation avec le service de modération
- Mettre en place un système de feedback utilisateur pour améliorer les recommandations
- Suivre les interactions des utilisateurs avec les recommandations via un service d'analytics

## Fonctionnalités implémentées

### 1. Intégration avec le service de modération

- Filtrage des recommandations selon les règles de modération
- Vérification de la conformité des recommandations
- Signalement des recommandations inappropriées
- Stockage local des signalements en cas d'indisponibilité du service de modération

### 2. Explications améliorées des recommandations

- Génération d'explications détaillées et personnalisées
- Facteurs d'explication avec poids et descriptions
- Visualisations pour une meilleure compréhension (graphiques à barres, radar)
- Support multilingue (français, anglais)
- Différents niveaux de détail (basique, standard, détaillé)

### 3. Système de feedback utilisateur

- Différents types de feedback (like, dislike, save, hide, report)
- Commentaires et notes pour un feedback détaillé
- Gestion des feedbacks par utilisateur et par recommandation
- Stockage local des feedbacks en cas d'indisponibilité de la base de données

### 4. Intégration avec le service d'analytics

- Suivi des interactions des utilisateurs avec les recommandations
- Événements de visualisation, clic, feedback, etc.
- Intégration avec les services de feedback, d'explications et de modération
- Stockage local des événements en cas d'indisponibilité du service d'analytics

### 5. Composants frontend

- Affichage des explications améliorées
- Boutons de feedback pour les recommandations
- Cartes de recommandation avec explications et feedback
- Page d'exemple pour démontrer l'utilisation des composants

## Architecture

L'architecture du système de recommandation amélioré est basée sur les principes suivants :

- **Modularité** : Chaque fonctionnalité est implémentée dans un module distinct
- **Extensibilité** : Le système est conçu pour être facilement étendu avec de nouvelles fonctionnalités
- **Résilience** : Le système continue de fonctionner même en cas d'indisponibilité des services externes
- **Testabilité** : Chaque composant est conçu pour être facilement testable

### Diagramme d'architecture

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Frontend        |     |  Backend         |     |  Services        |
|  (React)         |     |  (NestJS)        |     |  externes        |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
| - RecommendCard  |     | - FeedbackSvc    |     | - Moderation     |
| - EnhancedExpl.  |<--->| - EnhancedExpl.  |<--->| - Analytics      |
| - FeedbackBtns   |     | - ModerationInt. |     |                  |
| - RecommPage     |     | - AnalyticsInt.  |     |                  |
+------------------+     +------------------+     +------------------+
```

## Installation et configuration

### Prérequis

- Node.js 14+
- npm 6+
- NestJS CLI
- React 17+

### Installation

1. Cloner le dépôt :
   ```bash
   git clone https://github.com/retreat-and-be/rb2.git
   cd rb2
   ```

2. Installer les dépendances du backend :
   ```bash
   cd Projet-RB2/Backend-NestJS
   npm install
   ```

3. Installer les dépendances du frontend :
   ```bash
   cd ../Front-Audrey-V1-Main-main
   npm install
   ```

### Configuration

#### Backend

Créer un fichier `.env` dans le dossier `Projet-RB2/Backend-NestJS` avec les variables suivantes :

```
# Configuration de base
PORT=3000
NODE_ENV=development

# Configuration de la base de données
DATABASE_URL="postgresql://user:password@localhost:5432/rb2"

# Configuration des services externes
MODERATION_SERVICE_URL="http://localhost:3001"
MODERATION_SERVICE_API_KEY="your-api-key"
ANALYTICS_SERVICE_URL="http://localhost:3003"
ANALYTICS_SERVICE_API_KEY="your-api-key"
```

#### Frontend

Créer un fichier `.env` dans le dossier `Projet-RB2/Front-Audrey-V1-Main-main` avec les variables suivantes :

```
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_ENV=development
```

### Démarrage

#### Backend

```bash
cd Projet-RB2/Backend-NestJS
npm run start:dev
```

#### Frontend

```bash
cd Projet-RB2/Front-Audrey-V1-Main-main
npm start
```

## Tests

### Tests unitaires

Pour exécuter les tests unitaires du backend :

```bash
cd Projet-RB2/Backend-NestJS
npm run test
```

### Tests d'intégration

Pour exécuter les tests d'intégration du backend :

```bash
cd Projet-RB2/Backend-NestJS
./scripts/run-integration-tests.sh
```

### Tests frontend

Pour exécuter les tests du frontend :

```bash
cd Projet-RB2/Front-Audrey-V1-Main-main
npm test
```

## Documentation

### Documentation pour les développeurs

La documentation pour les développeurs est disponible dans le dossier `Projet-RB2/Backend-NestJS/docs` :

- [Documentation des fonctionnalités du Sprint 4](Projet-RB2/Backend-NestJS/docs/sprint4-features.md)

### Documentation pour les utilisateurs

La documentation pour les utilisateurs est disponible dans le dossier `Projet-RB2/Front-Audrey-V1-Main-main/docs` :

- [Guide utilisateur des nouvelles fonctionnalités](Projet-RB2/Front-Audrey-V1-Main-main/docs/user-guide-sprint4.md)

## Prochaines étapes

Les prochaines étapes pour le système de recommandation sont définies dans le Sprint 5 :

- Mise en place d'un système d'apprentissage continu
- Personnalisation avancée des recommandations
- Intégration avec d'autres microservices
- Amélioration des performances et de la scalabilité
