#!/usr/bin/env python3
"""
Service d'authentification centralisé pour le projet Retreat And Be.

Ce service gère l'authentification et l'autorisation pour tous les microservices.
"""
import os
import logging
import uuid
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from flask import Flask, request, jsonify, make_response
from flask_cors import CORS
import jwt
import bcrypt
import redis

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Création de l'application Flask
app = Flask(__name__)
CORS(app)

# Configuration
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'development')
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key-change-in-production')
JWT_ALGORITHM = os.environ.get('JWT_ALGORITHM', 'HS256')
JWT_EXPIRATION = int(os.environ.get('JWT_EXPIRATION', '3600'))  # 1 heure
REFRESH_TOKEN_EXPIRATION = int(os.environ.get('REFRESH_TOKEN_EXPIRATION', '2592000'))  # 30 jours

# Configuration Redis pour le stockage des tokens révoqués
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', '6379'))
REDIS_DB = int(os.environ.get('REDIS_DB', '0'))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

# Connexion à Redis
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=True
)

# Base de données utilisateurs simulée (à remplacer par une vraie base de données)
# Format: {username: {password_hash: str, roles: List[str], user_id: str}}
USERS_DB = {
    "admin": {
        "password_hash": bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
        "roles": ["admin"],
        "user_id": "1"
    },
    "user": {
        "password_hash": bcrypt.hashpw("user123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
        "roles": ["user"],
        "user_id": "2"
    },
    "partner": {
        "password_hash": bcrypt.hashpw("partner123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8'),
        "roles": ["partner"],
        "user_id": "3"
    }
}

# Définition des rôles et permissions
# Format: {role: List[str]}
ROLES_PERMISSIONS = {
    "admin": ["read:all", "write:all", "delete:all"],
    "user": ["read:own", "write:own"],
    "partner": ["read:own", "write:own", "read:retreats", "write:retreats"]
}

# Routes pour la santé et la préparation
@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint pour vérifier l'état de santé du service."""
    return jsonify({"status": "healthy"})

@app.route('/ready', methods=['GET'])
def ready_check():
    """Endpoint pour vérifier si le service est prêt."""
    try:
        # Vérifier la connexion à Redis
        redis_client.ping()
        return jsonify({"status": "ready"})
    except redis.exceptions.ConnectionError:
        return jsonify({"status": "not ready", "reason": "Redis connection failed"}), 503

# Routes d'authentification
@app.route('/auth/login', methods=['POST'])
def login():
    """Endpoint pour l'authentification des utilisateurs."""
    data = request.json
    
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({"error": "Missing username or password"}), 400
    
    username = data['username']
    password = data['password']
    
    # Vérifier si l'utilisateur existe
    if username not in USERS_DB:
        return jsonify({"error": "Invalid credentials"}), 401
    
    # Vérifier le mot de passe
    user = USERS_DB[username]
    if not bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
        return jsonify({"error": "Invalid credentials"}), 401
    
    # Générer les tokens
    access_token = generate_access_token(username, user['user_id'], user['roles'])
    refresh_token = generate_refresh_token(username, user['user_id'])
    
    # Stocker le refresh token dans Redis
    store_refresh_token(refresh_token, username)
    
    return jsonify({
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "Bearer",
        "expires_in": JWT_EXPIRATION,
        "user_id": user['user_id'],
        "username": username,
        "roles": user['roles']
    })

@app.route('/auth/refresh', methods=['POST'])
def refresh():
    """Endpoint pour rafraîchir le token d'accès."""
    data = request.json
    
    if not data or 'refresh_token' not in data:
        return jsonify({"error": "Missing refresh token"}), 400
    
    refresh_token = data['refresh_token']
    
    try:
        # Vérifier le refresh token
        payload = jwt.decode(refresh_token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        
        # Vérifier si le token est révoqué
        if is_token_revoked(refresh_token):
            return jsonify({"error": "Refresh token revoked"}), 401
        
        username = payload['sub']
        user_id = payload['user_id']
        
        # Vérifier si l'utilisateur existe
        if username not in USERS_DB:
            return jsonify({"error": "User not found"}), 401
        
        # Générer un nouveau token d'accès
        user = USERS_DB[username]
        access_token = generate_access_token(username, user_id, user['roles'])
        
        return jsonify({
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": JWT_EXPIRATION
        })
    
    except jwt.ExpiredSignatureError:
        return jsonify({"error": "Refresh token expired"}), 401
    except jwt.InvalidTokenError:
        return jsonify({"error": "Invalid refresh token"}), 401

@app.route('/auth/logout', methods=['POST'])
def logout():
    """Endpoint pour la déconnexion des utilisateurs."""
    auth_header = request.headers.get('Authorization')
    
    if not auth_header:
        return jsonify({"error": "Missing authorization header"}), 400
    
    parts = auth_header.split()
    
    if parts[0].lower() != 'bearer' or len(parts) != 2:
        return jsonify({"error": "Invalid authorization header"}), 400
    
    token = parts[1]
    
    try:
        # Vérifier le token
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        
        # Révoquer le token
        revoke_token(token, payload['exp'] - int(time.time()))
        
        # Révoquer le refresh token si fourni
        data = request.json
        if data and 'refresh_token' in data:
            refresh_token = data['refresh_token']
            try:
                refresh_payload = jwt.decode(refresh_token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
                revoke_token(refresh_token, refresh_payload['exp'] - int(time.time()))
            except jwt.InvalidTokenError:
                pass  # Ignorer les erreurs de token invalide pour le refresh token
        
        return jsonify({"message": "Logout successful"})
    
    except jwt.ExpiredSignatureError:
        return jsonify({"message": "Token already expired"}), 200
    except jwt.InvalidTokenError:
        return jsonify({"error": "Invalid token"}), 401

@app.route('/auth/validate', methods=['POST'])
def validate_token():
    """Endpoint pour valider un token d'accès."""
    data = request.json
    
    if not data or 'token' not in data:
        return jsonify({"error": "Missing token"}), 400
    
    token = data['token']
    
    try:
        # Vérifier le token
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        
        # Vérifier si le token est révoqué
        if is_token_revoked(token):
            return jsonify({"valid": False, "error": "Token revoked"}), 401
        
        # Vérifier si l'utilisateur existe
        username = payload['sub']
        if username not in USERS_DB:
            return jsonify({"valid": False, "error": "User not found"}), 401
        
        return jsonify({
            "valid": True,
            "user_id": payload['user_id'],
            "username": username,
            "roles": payload['roles'],
            "permissions": payload['permissions'],
            "exp": payload['exp']
        })
    
    except jwt.ExpiredSignatureError:
        return jsonify({"valid": False, "error": "Token expired"}), 401
    except jwt.InvalidTokenError:
        return jsonify({"valid": False, "error": "Invalid token"}), 401

@app.route('/auth/permissions', methods=['GET'])
def get_permissions():
    """Endpoint pour récupérer les permissions de l'utilisateur courant."""
    auth_header = request.headers.get('Authorization')
    
    if not auth_header:
        return jsonify({"error": "Missing authorization header"}), 401
    
    parts = auth_header.split()
    
    if parts[0].lower() != 'bearer' or len(parts) != 2:
        return jsonify({"error": "Invalid authorization header"}), 401
    
    token = parts[1]
    
    try:
        # Vérifier le token
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        
        # Vérifier si le token est révoqué
        if is_token_revoked(token):
            return jsonify({"error": "Token revoked"}), 401
        
        return jsonify({
            "user_id": payload['user_id'],
            "username": payload['sub'],
            "roles": payload['roles'],
            "permissions": payload['permissions']
        })
    
    except jwt.ExpiredSignatureError:
        return jsonify({"error": "Token expired"}), 401
    except jwt.InvalidTokenError:
        return jsonify({"error": "Invalid token"}), 401

@app.route('/auth/service-token', methods=['POST'])
def generate_service_token():
    """Endpoint pour générer un token de service pour la communication entre microservices."""
    data = request.json
    
    if not data or 'service_name' not in data or 'service_secret' not in data:
        return jsonify({"error": "Missing service name or secret"}), 400
    
    service_name = data['service_name']
    service_secret = data['service_secret']
    
    # Vérifier le secret du service (à remplacer par une vraie vérification)
    if service_secret != os.environ.get('SERVICE_SECRET', 'service-secret-change-in-production'):
        return jsonify({"error": "Invalid service secret"}), 401
    
    # Générer un token de service
    service_token = generate_service_token(service_name)
    
    return jsonify({
        "service_token": service_token,
        "token_type": "Bearer",
        "expires_in": JWT_EXPIRATION
    })

# Fonctions utilitaires
def generate_access_token(username: str, user_id: str, roles: List[str]) -> str:
    """
    Génère un token d'accès JWT.
    
    Args:
        username: Nom d'utilisateur
        user_id: ID de l'utilisateur
        roles: Rôles de l'utilisateur
        
    Returns:
        Token d'accès JWT
    """
    now = datetime.utcnow()
    
    # Récupérer les permissions pour les rôles
    permissions = []
    for role in roles:
        if role in ROLES_PERMISSIONS:
            permissions.extend(ROLES_PERMISSIONS[role])
    
    # Supprimer les doublons
    permissions = list(set(permissions))
    
    payload = {
        'iat': now,
        'exp': now + timedelta(seconds=JWT_EXPIRATION),
        'sub': username,
        'user_id': user_id,
        'roles': roles,
        'permissions': permissions,
        'type': 'access'
    }
    
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def generate_refresh_token(username: str, user_id: str) -> str:
    """
    Génère un token de rafraîchissement JWT.
    
    Args:
        username: Nom d'utilisateur
        user_id: ID de l'utilisateur
        
    Returns:
        Token de rafraîchissement JWT
    """
    now = datetime.utcnow()
    
    payload = {
        'iat': now,
        'exp': now + timedelta(seconds=REFRESH_TOKEN_EXPIRATION),
        'sub': username,
        'user_id': user_id,
        'type': 'refresh',
        'jti': str(uuid.uuid4())
    }
    
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def generate_service_token(service_name: str) -> str:
    """
    Génère un token de service JWT pour la communication entre microservices.
    
    Args:
        service_name: Nom du service
        
    Returns:
        Token de service JWT
    """
    now = datetime.utcnow()
    
    payload = {
        'iat': now,
        'exp': now + timedelta(seconds=JWT_EXPIRATION),
        'sub': service_name,
        'type': 'service',
        'jti': str(uuid.uuid4())
    }
    
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def store_refresh_token(token: str, username: str) -> None:
    """
    Stocke un refresh token dans Redis.
    
    Args:
        token: Token de rafraîchissement
        username: Nom d'utilisateur
    """
    try:
        # Décoder le token pour obtenir l'ID et l'expiration
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        jti = payload['jti']
        exp = payload['exp']
        
        # Calculer la durée de vie en secondes
        ttl = exp - int(time.time())
        
        # Stocker le token dans Redis
        key = f"refresh_token:{jti}"
        redis_client.setex(key, ttl, username)
    except Exception as e:
        logger.error(f"Error storing refresh token: {str(e)}")

def revoke_token(token: str, ttl: int) -> None:
    """
    Révoque un token en l'ajoutant à la liste des tokens révoqués.
    
    Args:
        token: Token à révoquer
        ttl: Durée de vie du token en secondes
    """
    try:
        # Décoder le token pour obtenir l'ID
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        jti = payload.get('jti', str(uuid.uuid4()))
        
        # Stocker le token dans Redis
        key = f"revoked_token:{jti}"
        redis_client.setex(key, ttl, "1")
    except Exception as e:
        logger.error(f"Error revoking token: {str(e)}")

def is_token_revoked(token: str) -> bool:
    """
    Vérifie si un token est révoqué.
    
    Args:
        token: Token à vérifier
        
    Returns:
        True si le token est révoqué, False sinon
    """
    try:
        # Décoder le token pour obtenir l'ID
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        jti = payload.get('jti')
        
        if not jti:
            return False
        
        # Vérifier si le token est dans la liste des tokens révoqués
        key = f"revoked_token:{jti}"
        return redis_client.exists(key) == 1
    except Exception as e:
        logger.error(f"Error checking if token is revoked: {str(e)}")
        return False

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5003))
    app.run(host='0.0.0.0', port=port, debug=ENVIRONMENT == 'development')
