import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

interface FileChecksum {
  path: string;
  checksum: string;
  lastModified: Date;
  size: number;
}

interface IntegrityCheck {
  timestamp: Date;
  filesChecked: number;
  modifiedFiles: string[];
  newFiles: string[];
  missingFiles: string[];
}

@Injectable()
export class FileIntegrityMonitor implements OnModuleInit {
  private readonly logger = new Logger(FileIntegrityMonitor.name);
  private checksumDatabase: Map<string, FileChecksum> = new Map();
  private monitoredDirectories: string[] = [];
  private monitorInterval: NodeJS.Timeout;
  private readonly intervalMs: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.intervalMs = this.configService.get<number>(
      'security.fileIntegrity.checkIntervalMs',
      3600000
    ); // Default: 1 hour
  }

  async onModuleInit() {
    // Load monitored directories from config
    this.monitoredDirectories = this.configService.get<string[]>(
      'security.fileIntegrity.directories',
      []
    );

    if (this.monitoredDirectories.length === 0) {
      this.logger.warn('No directories configured for file integrity monitoring');
      return;
    }

    // Initialize checksums database
    await this.buildInitialChecksums();

    // Start periodic monitoring
    this.startMonitoring();
  }

  private async buildInitialChecksums(): Promise<void> {
    this.logger.log('Building initial file checksums database...');

    for (const directory of this.monitoredDirectories) {
      try {
        await this.scanDirectory(directory);
      } catch (error) {
        this.logger.error(`Error building checksums for directory ${directory}`, error.stack);
      }
    }

    this.logger.log(`Initial checksums database built with ${this.checksumDatabase.size} files`);
  }

  private async scanDirectory(directory: string): Promise<void> {
    try {
      const files = fs.readdirSync(directory);

      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
          await this.scanDirectory(filePath);
        } else {
          const checksum = this.calculateChecksum(filePath);
          this.checksumDatabase.set(filePath, {
            path: filePath,
            checksum,
            lastModified: stats.mtime,
            size: stats.size,
          });
        }
      }
    } catch (error) {
      this.logger.error(`Error scanning directory ${directory}`, error.stack);
    }
  }

  private calculateChecksum(filePath: string): string {
    try {
      const fileBuffer = fs.readFileSync(filePath);
      const hashSum = crypto.createHash('sha256');
      hashSum.update(fileBuffer);
      return hashSum.digest('hex');
    } catch (error) {
      this.logger.error(`Error calculating checksum for ${filePath}`, error.stack);
      return '';
    }
  }

  private startMonitoring(): void {
    this.logger.log(`Starting file integrity monitoring with interval of ${this.intervalMs}ms`);

    this.monitorInterval = setInterval(async () => {
      await this.performIntegrityCheck();
    }, this.intervalMs);
  }

  private async performIntegrityCheck(): Promise<IntegrityCheck> {
    this.logger.log('Performing file integrity check...');

    const result: IntegrityCheck = {
      timestamp: new Date(),
      filesChecked: 0,
      modifiedFiles: [],
      newFiles: [],
      missingFiles: [],
    };

    // Track all current file paths to identify missing files
    const currentPaths = new Set<string>();

    // Check all directories
    for (const directory of this.monitoredDirectories) {
      try {
        await this.checkDirectoryIntegrity(directory, result, currentPaths);
      } catch (error) {
        this.logger.error(`Error checking integrity of directory ${directory}`, error.stack);
      }
    }

    // Identify missing files
    for (const [storedPath] of this.checksumDatabase) {
      if (!currentPaths.has(storedPath)) {
        result.missingFiles.push(storedPath);
      }
    }

    // Emit event if any changes detected
    if (
      result.modifiedFiles.length > 0 ||
      result.newFiles.length > 0 ||
      result.missingFiles.length > 0
    ) {
      this.eventEmitter.emit('file.integrity.violation', result);
      this.logger.warn(
        `File integrity violations detected: ${result.modifiedFiles.length} modified, ` +
          `${result.newFiles.length} new, ${result.missingFiles.length} missing`
      );
    }

    return result;
  }

  private async checkDirectoryIntegrity(
    directory: string,
    result: IntegrityCheck,
    currentPaths: Set<string>
  ): Promise<void> {
    try {
      const files = fs.readdirSync(directory);

      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
          await this.checkDirectoryIntegrity(filePath, result, currentPaths);
        } else {
          result.filesChecked++;
          currentPaths.add(filePath);

          const currentChecksum = this.calculateChecksum(filePath);
          const storedChecksum = this.checksumDatabase.get(filePath);

          if (!storedChecksum) {
            // New file
            result.newFiles.push(filePath);

            // Add to database
            this.checksumDatabase.set(filePath, {
              path: filePath,
              checksum: currentChecksum,
              lastModified: stats.mtime,
              size: stats.size,
            });
          } else if (storedChecksum.checksum !== currentChecksum) {
            // Modified file
            result.modifiedFiles.push(filePath);

            // Update database
            this.checksumDatabase.set(filePath, {
              path: filePath,
              checksum: currentChecksum,
              lastModified: stats.mtime,
              size: stats.size,
            });
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error checking integrity of directory ${directory}`, error.stack);
    }
  }

  // Public methods for external use

  public async forceIntegrityCheck(): Promise<IntegrityCheck> {
    return await this.performIntegrityCheck();
  }

  public async addDirectory(directory: string): Promise<void> {
    if (!this.monitoredDirectories.includes(directory)) {
      this.monitoredDirectories.push(directory);
      await this.scanDirectory(directory);
      this.logger.log(`Added directory to monitoring: ${directory}`);
    }
  }

  public removeDirectory(directory: string): void {
    this.monitoredDirectories = this.monitoredDirectories.filter(dir => dir !== directory);

    // Remove checksums for files in this directory
    for (const [storedPath] of this.checksumDatabase) {
      if (storedPath.startsWith(directory)) {
        this.checksumDatabase.delete(storedPath);
      }
    }

    this.logger.log(`Removed directory from monitoring: ${directory}`);
  }

  public onModuleDestroy(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.logger.log('File integrity monitoring stopped');
    }
  }
}
