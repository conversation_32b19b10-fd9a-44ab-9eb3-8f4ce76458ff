import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface FunctionGasEstimate {
  name: string;
  estimatedGas: number;
  complexity: 'low' | 'medium' | 'high';
  suggestions: string[];
}

interface ContractGasAnalysis {
  contractName: string;
  totalEstimatedGas: number;
  deploymentGas: number;
  functions: FunctionGasEstimate[];
  optimizationSuggestions: string[];
}

@Injectable()
export class GasUsageAnalyzer {
  private readonly logger = new Logger(GasUsageAnalyzer.name);

  constructor(private readonly configService: ConfigService) {}

  async analyzeContract(contractPath: string): Promise<ContractGasAnalysis> {
    this.logger.log(`Analyzing gas usage for contract at ${contractPath}`);

    // This would normally analyze the bytecode or use tools like eth-gas-reporter
    // This is a mock implementation for demonstration
    const mockAnalysis: ContractGasAnalysis = {
      contractName: 'TokenContract',
      totalEstimatedGas: 2345000,
      deploymentGas: 1500000,
      functions: [
        {
          name: 'transfer',
          estimatedGas: 45000,
          complexity: 'low',
          suggestions: [],
        },
        {
          name: 'batchTransfer',
          estimatedGas: 210000,
          complexity: 'high',
          suggestions: [
            'Consider using multiple individual transactions instead of loops',
            'Optimize storage access pattern to reduce gas',
          ],
        },
        {
          name: 'approve',
          estimatedGas: 30000,
          complexity: 'low',
          suggestions: [],
        },
      ],
      optimizationSuggestions: [
        'Use uint256 instead of smaller unsigned integers if no space savings',
        'Avoid unnecessary storage of variables that could be calculated',
        'Consider using events instead of storing transaction history',
      ],
    };

    return mockAnalysis;
  }

  async compareGasUsage(
    contractPathBefore: string,
    contractPathAfter: string
  ): Promise<{
    before: ContractGasAnalysis;
    after: ContractGasAnalysis;
    diff: {
      totalGasDiff: number;
      deploymentGasDiff: number;
      functionDiffs: Array<{
        name: string;
        before: number;
        after: number;
        diff: number;
        percentChange: string;
      }>;
    };
  }> {
    const before = await this.analyzeContract(contractPathBefore);
    const after = await this.analyzeContract(contractPathAfter);

    // Calculate differences
    const functionDiffs = before.functions
      .map(beforeFn => {
        const afterFn = after.functions.find(fn => fn.name === beforeFn.name);
        if (!afterFn) return null;

        const diff = afterFn.estimatedGas - beforeFn.estimatedGas;
        const percentChange = ((diff / beforeFn.estimatedGas) * 100).toFixed(2) + '%';

        return {
          name: beforeFn.name,
          before: beforeFn.estimatedGas,
          after: afterFn.estimatedGas,
          diff,
          percentChange,
        };
      })
      .filter(Boolean);

    return {
      before,
      after,
      diff: {
        totalGasDiff: after.totalEstimatedGas - before.totalEstimatedGas,
        deploymentGasDiff: after.deploymentGas - before.deploymentGas,
        functionDiffs,
      },
    };
  }

  async estimateTransactionCost(gasUsage: number): Promise<{
    eth: number;
    usd: number;
    gasPrice: string;
  }> {
    // Mock implementation - would normally use a gas price oracle
    const mockGasPrice = 50; // gwei
    const mockEthPrice = 2000; // USD

    const ethCost = (gasUsage * mockGasPrice) / 1e9;
    const usdCost = ethCost * mockEthPrice;

    return {
      eth: ethCost,
      usd: usdCost,
      gasPrice: `${mockGasPrice} gwei`,
    };
  }
}
