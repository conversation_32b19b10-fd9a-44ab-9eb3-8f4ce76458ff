// Fichier content.js - Script injecté dans les pages web visitées
console.log('Web3 Community Hub - Content script chargé');

// Configuration
let config = {
  enabled: true,
  detectNFTs: true,
  enhanceWebsites: true
};

// État
let isConnected = false;
let walletAddress = null;

// Initialisation
initialize();

// Fonction d'initialisation
async function initialize() {
  // Charger la configuration
  try {
    chrome.storage.local.get(['config', 'walletAddress'], function(result) {
      if (result.config) {
        config = { ...config, ...result.config };
      }
      
      if (result.walletAddress) {
        isConnected = true;
        walletAddress = result.walletAddress;
      }
      
      // Démarrer l'analyse de la page
      if (config.enabled) {
        analyzePageContent();
      }
    });
    
    // Écouter les messages de l'extension
    chrome.runtime.onMessage.addListener(handleRuntimeMessages);
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du content script:', error);
  }
}

// Analyser le contenu de la page
function analyzePageContent() {
  const currentUrl = window.location.href;
  
  // Détecter les signatures blockchain ou adresses de contrats NFT
  if (config.detectNFTs) {
    detectBlockchainAddresses();
  }
  
  // Améliorer les sites web spécifiques
  if (config.enhanceWebsites) {
    enhanceWebsite(currentUrl);
  }
}

// Détecter les adresses blockchain et de contrats sur la page
function detectBlockchainAddresses() {
  // Expression régulière pour les adresses Ethereum
  const ethAddressRegex = /0x[a-fA-F0-9]{40}/g;
  
  // Rechercher dans le texte de la page
  const pageText = document.body.innerText;
  const ethAddresses = pageText.match(ethAddressRegex);
  
  if (ethAddresses && ethAddresses.length > 0) {
    console.log('Adresses Ethereum détectées:', ethAddresses);
    
    // Notifier l'extension
    chrome.runtime.sendMessage({
      type: 'ADDRESSES_FOUND',
      payload: {
        addresses: ethAddresses,
        url: window.location.href
      }
    });
    
    // Ajouter des infobulles aux adresses
    addAddressTooltips(ethAddresses);
  }
}

// Ajouter des infobulles aux adresses
function addAddressTooltips(addresses) {
  // Uniquifier les adresses
  const uniqueAddresses = [...new Set(addresses)];
  
  // Parcourir tous les nœuds de texte dans le document
  const textNodes = [];
  const walk = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null, false);
  let node;
  while (node = walk.nextNode()) {
    textNodes.push(node);
  }
  
  // Pour chaque adresse, ajouter une infobulle
  uniqueAddresses.forEach(address => {
    const ethAddressRegex = new RegExp(address, 'g');
    
    textNodes.forEach(textNode => {
      if (ethAddressRegex.test(textNode.nodeValue)) {
        // Créer un élément span pour remplacer le nœud de texte
        const span = document.createElement('span');
        span.innerHTML = textNode.nodeValue.replace(ethAddressRegex, `<span class="eth-address" title="Adresse Ethereum détectée">${address}</span>`);
        
        // Remplacer le nœud de texte par le span
        if (textNode.parentNode) {
          textNode.parentNode.replaceChild(span, textNode);
        }
      }
    });
  });
  
  // Ajouter des styles CSS pour les adresses
  const style = document.createElement('style');
  style.textContent = `
    .eth-address {
      color: #6C63FF;
      border-bottom: 1px dotted #6C63FF;
      cursor: pointer;
    }
    .eth-address:hover {
      background-color: rgba(108, 99, 255, 0.1);
    }
  `;
  document.head.appendChild(style);
  
  // Ajouter un écouteur pour les clics sur les adresses
  document.addEventListener('click', function(e) {
    if (e.target.classList.contains('eth-address')) {
      // Ouvrir l'explorateur de blocs
      const address = e.target.textContent;
      openBlockExplorer(address);
    }
  });
}

// Ouvrir l'explorateur de blocs
function openBlockExplorer(address) {
  chrome.runtime.sendMessage({
    type: 'OPEN_BLOCK_EXPLORER',
    payload: { address }
  });
}

// Améliorer les sites web spécifiques
function enhanceWebsite(url) {
  // Si nous sommes sur ethereum.org
  if (url.includes('ethereum.org')) {
    enhanceEthereumOrg();
  }
  // Si nous sommes sur polygon.technology
  else if (url.includes('polygon.technology')) {
    enhancePolygonTech();
  }
}

// Améliorer ethereum.org
function enhanceEthereumOrg() {
  // Ajouter un bouton pour se connecter
  const header = document.querySelector('header');
  if (header) {
    const connectButton = document.createElement('button');
    connectButton.innerText = isConnected ? 'Wallet connecté' : 'Connecter wallet';
    connectButton.className = 'web3-hub-connect-btn';
    connectButton.addEventListener('click', function() {
      chrome.runtime.sendMessage({ type: 'POPUP_CONNECT_WALLET' });
    });
    
    header.appendChild(connectButton);
    
    // Ajouter des styles CSS
    const style = document.createElement('style');
    style.textContent = `
      .web3-hub-connect-btn {
        background-color: #6C63FF;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: pointer;
        margin-left: 10px;
      }
      .web3-hub-connect-btn:hover {
        background-color: #4E44CE;
      }
    `;
    document.head.appendChild(style);
  }
}

// Améliorer polygon.technology
function enhancePolygonTech() {
  // Fonctionnalité similaire à enhanceEthereumOrg
}

// Gérer les messages de l'extension
function handleRuntimeMessages(message, sender, sendResponse) {
  switch (message.type) {
    case 'CONFIG_UPDATED':
      config = message.payload.config;
      if (config.enabled) {
        analyzePageContent();
      }
      break;
      
    case 'WALLET_CONNECTED':
      isConnected = true;
      walletAddress = message.payload.address;
      break;
      
    case 'WALLET_DISCONNECTED':
      isConnected = false;
      walletAddress = null;
      break;
  }
  
  sendResponse({ success: true });
  return true;
} 