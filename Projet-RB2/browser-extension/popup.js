// Fonctionnalités du popup de l'extension
document.addEventListener('DOMContentLoaded', function() {
  // Éléments DOM
  const connectWalletBtn = document.getElementById('connect-wallet');
  const walletAddressEl = document.getElementById('wallet-address');
  const navItems = document.querySelectorAll('nav li');
  const tabContents = document.querySelectorAll('.tab-content');
  const eventsList = document.getElementById('events-list');
  const livestreamsList = document.getElementById('livestreams-list');
  const nftsList = document.getElementById('nfts-list');
  const chatMessages = document.getElementById('chat-messages');
  const messageInput = document.getElementById('message-input');
  const sendMessageBtn = document.getElementById('send-message');
  const addEventBtn = document.getElementById('add-event');
  const addEventModal = document.getElementById('add-event-modal');
  const addEventForm = document.getElementById('add-event-form');
  const closeModalBtn = document.querySelector('.close');
  const cancelBtn = document.querySelector('.cancel');
  const requiresNftCheckbox = document.getElementById('event-requires-nft');
  const nftDetails = document.getElementById('nft-details');

  // État initial
  let walletAddress = null;
  let connectedChainId = null;
  
  // Initialiser l'interface
  initializeUI();
  
  // Fonctions d'initialisation
  function initializeUI() {
    loadSettings();
    loadEvents();
    loadLivestreams();
    
    // Vérifier si un wallet est déjà connecté
    chrome.storage.local.get(['walletAddress', 'chainId'], function(result) {
      if (result.walletAddress) {
        walletAddress = result.walletAddress;
        connectedChainId = result.chainId || 1;
        updateWalletUI();
        loadNFTs();
        enableChatFeatures();
      }
    });
  }
  
  // Navigation entre les onglets
  navItems.forEach(item => {
    item.addEventListener('click', function() {
      // Mise à jour des classes actives
      navItems.forEach(i => i.classList.remove('active'));
      this.classList.add('active');
      
      // Affichage du contenu correspondant
      const tabName = this.getAttribute('data-tab');
      tabContents.forEach(tab => {
        tab.classList.remove('active');
        if (tab.id === tabName) {
          tab.classList.add('active');
        }
      });
    });
  });
  
  // Connexion du wallet
  connectWalletBtn.addEventListener('click', async function() {
    if (walletAddress) {
      // Déconnexion
      walletAddress = null;
      connectedChainId = null;
      chrome.storage.local.remove(['walletAddress', 'chainId']);
      updateWalletUI();
      nftsList.innerHTML = '<div class="loader">Connectez votre wallet pour voir vos NFTs</div>';
      disableChatFeatures();
    } else {
      // Connexion
      try {
        const response = await sendMessage({
          type: 'CONNECT_WALLET',
          payload: {}
        });
        
        if (response && response.success) {
          walletAddress = response.address;
          connectedChainId = response.chainId || 1;
          chrome.storage.local.set({
            walletAddress: walletAddress,
            chainId: connectedChainId
          });
          updateWalletUI();
          loadNFTs();
          enableChatFeatures();
        } else {
          showError('Échec de la connexion du wallet');
        }
      } catch (error) {
        console.error('Erreur lors de la connexion du wallet:', error);
        showError('Échec de la connexion du wallet');
      }
    }
  });
  
  // Mise à jour de l'interface du wallet
  function updateWalletUI() {
    if (walletAddress) {
      // Format: 0x1234...5678
      const shortAddress = walletAddress.substring(0, 6) + '...' + 
                          walletAddress.substring(walletAddress.length - 4);
      walletAddressEl.textContent = shortAddress;
      connectWalletBtn.textContent = 'Déconnecter';
    } else {
      walletAddressEl.textContent = 'Non connecté';
      connectWalletBtn.textContent = 'Connecter';
    }
  }
  
  // Chargement des événements
  async function loadEvents() {
    try {
      eventsList.innerHTML = '<div class="loader">Chargement des événements...</div>';
      
      const response = await sendMessage({
        type: 'GET_EVENTS',
        payload: {
          filter: {
            upcoming: true
          },
          limit: 10
        }
      });
      
      if (response && response.success && response.events) {
        if (response.events.length === 0) {
          eventsList.innerHTML = '<div class="empty-state">Aucun événement à venir</div>';
          return;
        }
        
        const eventsHTML = response.events.map(event => {
          const startDate = new Date(event.startDate);
          const endDate = new Date(event.endDate);
          
          return `
            <div class="event-card" data-id="${event.id}">
              <div class="event-card-header">
                ${event.name}
                ${event.requiresNFT ? '<span class="nft-badge">NFT requis</span>' : ''}
              </div>
              <div class="event-card-body">
                <p>${event.description}</p>
                <div class="event-date">
                  ${startDate.toLocaleDateString()} ${startDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                </div>
              </div>
              <div class="event-card-footer">
                <div class="event-tags">
                  ${event.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
                <button class="join-event">Participer</button>
              </div>
            </div>
          `;
        }).join('');
        
        eventsList.innerHTML = eventsHTML;
        
        // Ajouter des écouteurs aux boutons
        document.querySelectorAll('.join-event').forEach(btn => {
          btn.addEventListener('click', function() {
            const eventId = this.closest('.event-card').getAttribute('data-id');
            joinEvent(eventId);
          });
        });
      } else {
        eventsList.innerHTML = '<div class="error-state">Erreur lors du chargement des événements</div>';
      }
    } catch (error) {
      console.error('Erreur lors du chargement des événements:', error);
      eventsList.innerHTML = '<div class="error-state">Erreur lors du chargement des événements</div>';
    }
  }
  
  // Chargement des livestreams
  async function loadLivestreams() {
    // Logique similaire à loadEvents
    livestreamsList.innerHTML = '<div class="empty-state">Aucun livestream disponible actuellement</div>';
  }
  
  // Chargement des NFTs
  async function loadNFTs() {
    if (!walletAddress) {
      return;
    }
    
    nftsList.innerHTML = '<div class="loader">Chargement de vos NFTs...</div>';
    
    // Simulation de NFTs
    setTimeout(() => {
      nftsList.innerHTML = `
        <div class="nft-card">
          <img src="https://via.placeholder.com/150" alt="NFT Example">
          <div class="nft-info">
            <h4>Cool NFT #1234</h4>
            <p>Collection: Awesome Art</p>
          </div>
        </div>
        <div class="nft-card">
          <img src="https://via.placeholder.com/150" alt="NFT Example">
          <div class="nft-info">
            <h4>Super Token #5678</h4>
            <p>Collection: Super Tokens</p>
          </div>
        </div>
      `;
    }, 1000);
  }
  
  // Activer/désactiver le chat
  function enableChatFeatures() {
    messageInput.disabled = false;
    sendMessageBtn.disabled = false;
    chatMessages.innerHTML = `
      <div class="welcome-message">
        Bienvenue dans le chat! Vous pouvez maintenant participer.
      </div>
    `;
  }
  
  function disableChatFeatures() {
    messageInput.disabled = true;
    sendMessageBtn.disabled = true;
    chatMessages.innerHTML = `
      <div class="welcome-message">
        Bienvenue dans le chat! Connectez-vous pour participer.
      </div>
    `;
  }
  
  // Envoi de message dans le chat
  sendMessageBtn.addEventListener('click', function() {
    const message = messageInput.value.trim();
    if (!message) return;
    
    sendChatMessage(message);
    messageInput.value = '';
  });
  
  messageInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessageBtn.click();
    }
  });
  
  async function sendChatMessage(message) {
    try {
      const response = await sendMessage({
        type: 'SEND_MESSAGE',
        payload: {
          channelId: 'general',
          userId: walletAddress,
          userNickname: walletAddress.substring(0, 6),
          message: message
        }
      });
      
      if (response && response.success) {
        addMessageToChat(response.userNickname, response.message, 'sent');
      } else {
        showError(response?.error || 'Échec de l\'envoi du message');
      }
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      showError('Échec de l\'envoi du message');
    }
  }
  
  function addMessageToChat(author, content, type = 'received') {
    const messageEl = document.createElement('div');
    messageEl.className = `chat-message ${type}`;
    messageEl.innerHTML = `
      <div class="message-author">${author}</div>
      <div class="message-content">${content}</div>
    `;
    chatMessages.appendChild(messageEl);
    chatMessages.scrollTop = chatMessages.scrollHeight;
  }
  
  // Modal d'ajout d'événement
  addEventBtn.addEventListener('click', function() {
    if (!walletAddress) {
      showError('Vous devez connecter un wallet pour créer un événement');
      return;
    }
    addEventModal.style.display = 'block';
  });
  
  closeModalBtn.addEventListener('click', function() {
    addEventModal.style.display = 'none';
  });
  
  cancelBtn.addEventListener('click', function() {
    addEventModal.style.display = 'none';
  });
  
  // Afficher/cacher les détails NFT
  requiresNftCheckbox.addEventListener('change', function() {
    if (this.checked) {
      nftDetails.classList.remove('hidden');
    } else {
      nftDetails.classList.add('hidden');
    }
  });
  
  // Soumission du formulaire d'événement
  addEventForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Récupérer les valeurs du formulaire
    const name = document.getElementById('event-name').value;
    const description = document.getElementById('event-description').value;
    const startDate = new Date(document.getElementById('event-start').value).getTime();
    const endDate = new Date(document.getElementById('event-end').value).getTime();
    const location = document.getElementById('event-location').value;
    const url = document.getElementById('event-url').value;
    const imageUrl = document.getElementById('event-image').value;
    const tagsStr = document.getElementById('event-tags').value;
    const tags = tagsStr.split(',').map(tag => tag.trim()).filter(tag => tag);
    const requiresNFT = document.getElementById('event-requires-nft').checked;
    
    let contractAddress, chainId;
    if (requiresNFT) {
      contractAddress = document.getElementById('event-contract').value;
      chainId = parseInt(document.getElementById('event-chain').value);
    }
    
    try {
      const response = await sendMessage({
        type: 'ADD_EVENT',
        payload: {
          event: {
            name,
            description,
            startDate,
            endDate,
            location,
            url,
            imageUrl,
            tags,
            requiresNFT,
            contractAddress,
            chainId
          }
        }
      });
      
      if (response && response.success) {
        addEventModal.style.display = 'none';
        addEventForm.reset();
        nftDetails.classList.add('hidden');
        loadEvents(); // Recharger la liste des événements
        showSuccess('Événement créé avec succès');
      } else {
        showError(response?.error || 'Échec de la création de l\'événement');
      }
    } catch (error) {
      console.error('Erreur lors de la création de l\'événement:', error);
      showError('Échec de la création de l\'événement');
    }
  });
  
  // Fonction pour charger les paramètres
  function loadSettings() {
    chrome.storage.local.get(['settings'], function(result) {
      if (result.settings) {
        const settings = result.settings;
        
        // Notifications
        if (settings.notificationPreferences) {
          document.getElementById('notif-events').checked = settings.notificationPreferences.events;
          document.getElementById('notif-livestreams').checked = settings.notificationPreferences.livestreams;
          document.getElementById('notif-messages').checked = settings.notificationPreferences.messages;
        }
        
        // Thème
        if (settings.theme) {
          document.getElementById('theme').value = settings.theme;
        }
        
        // Langue
        if (settings.language) {
          document.getElementById('language').value = settings.language;
        }
      }
    });
    
    // Ajouter des écouteurs pour les changements de paramètres
    document.querySelectorAll('#settings input, #settings select').forEach(input => {
      input.addEventListener('change', updateSettings);
    });
  }
  
  // Mettre à jour les paramètres
  function updateSettings() {
    const settings = {
      notificationPreferences: {
        events: document.getElementById('notif-events').checked,
        livestreams: document.getElementById('notif-livestreams').checked,
        messages: document.getElementById('notif-messages').checked
      },
      theme: document.getElementById('theme').value,
      language: document.getElementById('language').value
    };
    
    sendMessage({
      type: 'UPDATE_SETTINGS',
      payload: { settings }
    });
  }
  
  // Utilitaires
  function joinEvent(eventId) {
    if (!walletAddress) {
      showError('Vous devez connecter un wallet pour participer à cet événement');
      return;
    }
    
    // TODO: Vérifier si l'événement nécessite un NFT
    showSuccess('Vous participez maintenant à cet événement');
  }
  
  function showError(message) {
    // TODO: Afficher un toast ou une notification d'erreur
    console.error(message);
  }
  
  function showSuccess(message) {
    // TODO: Afficher un toast ou une notification de succès
    console.log(message);
  }
  
  // Fonction d'envoi de message au background
  function sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, function(response) {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  }
}); 