import { chains, getChain<PERSON>yId } from './config/chains';
import { wagmiConfig, checkNFTOwnership as checkNFTFromWagmi } from './config/wagmi';
import { NotificationService } from './services/notifications';
import { LivestreamService } from './services/livestream';
import { EventService } from './services/events';

// Configuration initiale
chrome.runtime.onInstalled.addListener(() => {
  // Configuration initiale
  
  // Stockage des configurations
  chrome.storage.local.set({
    chains,
    wagmiConfig,
    features: {
      enableNFT: true,
      enableWallet: true,
      enableNotifications: true,
      enableLivestream: true,
      enableChat: true,
      enableVR: true
    },
    settings: {
      notificationPreferences: {
        events: true,
        messages: true,
        livestreams: true
      },
      theme: 'system',
      language: 'en'
    }
  });

  // Initialiser les services
  LivestreamService.initialize();
  EventService.initialize();
  
  // Créer une notification d'installation/mise à jour
  NotificationService.createNotification({
    type: 'basic',
    iconUrl: '/icons/icon-128.png',
    title: 'Extension installée avec succès',
    message: 'Bienvenue ! L\'extension est prête à être utilisée.',
    priority: 1
  });
  
  // Configurer une vérification périodique des événements
  chrome.alarms.create('checkEvents', {
    periodInMinutes: 60 // Vérifier les événements toutes les heures
  });
});

// Écouteur d'alarmes
chrome.alarms.onAlarm.addListener((alarm: chrome.alarms.Alarm) => {
  if (alarm.name === 'checkEvents') {
    checkUpcomingEvents();
  }
});

// Fonction pour vérifier les événements à venir
async function checkUpcomingEvents() {
  try {
    const events = EventService.getEvents();
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    
    // Filtrer les événements qui commencent dans les prochaines 24 heures
    const upcomingEvents = events.filter(event => {
      const timeUntilStart = event.startDate - now;
      return timeUntilStart > 0 && timeUntilStart <= oneDayMs;
    });
    
    if (upcomingEvents.length > 0) {
      // Notifier l'utilisateur des événements à venir
      for (const event of upcomingEvents) {
        await NotificationService.createNotification({
          type: 'basic',
          iconUrl: event.imageUrl || '/icons/icon-128.png',
          title: 'Événement à venir',
          message: `${event.name} commence bientôt`,
          contextMessage: event.description
        });
      }
    }
  } catch (error) {
    console.error('Erreur lors de la vérification des événements à venir:', error);
  }
}

// Écoute des messages
chrome.runtime.onMessage.addListener((request: any, sender: chrome.runtime.MessageSender, sendResponse: (response?: any) => void) => {
  switch (request.type) {
    case 'CONNECT_WALLET':
      handleWalletConnection(request.payload).then(sendResponse);
      return true;
    case 'CHECK_NFT':
      checkNFTOwnership(request.payload).then(sendResponse);
      return true;
    case 'JOIN_LIVESTREAM':
      joinLivestream(request.payload).then(sendResponse);
      return true;
    case 'SEND_MESSAGE':
      sendCommunityMessage(request.payload).then(sendResponse);
      return true;
    case 'UPDATE_SETTINGS':
      updateUserSettings(request.payload).then(sendResponse);
      return true;
    case 'GET_EVENTS':
      getEvents(request.payload).then(sendResponse);
      return true;
    case 'ADD_EVENT':
      addEvent(request.payload).then(sendResponse);
      return true;
  }
});

// Services handlers
async function handleWalletConnection(payload: any) {
  // Logique de connexion au wallet
  try {
    // Simuler une connexion réussie
    return { success: true, address: payload.address };
  } catch (error) {
    console.error('Erreur lors de la connexion au wallet:', error);
    return { success: false, error: 'Échec de la connexion au wallet' };
  }
}

async function checkNFTOwnership(payload: any) {
  // Vérification de la propriété des NFTs
  try {
    const { address, contractAddress, tokenId } = payload;
    const hasNFT = await checkNFTFromWagmi(address, contractAddress, tokenId);
    
    return { 
      success: true, 
      hasNFT,
      address,
      contractAddress,
      tokenId
    };
  } catch (error) {
    console.error('Erreur lors de la vérification de la propriété NFT:', error);
    return { success: false, error: 'Échec de la vérification NFT' };
  }
}

async function joinLivestream(payload: any) {
  // Logique de connexion au livestream
  try {
    const { streamId, userId, userNickname } = payload;
    
    // Obtenir les informations du stream
    const stream = LivestreamService.getStreamById(streamId);
    if (!stream) {
      throw new Error('Stream non trouvé');
    }
    
    // Vérifier si le stream est en direct
    if (!stream.isLive) {
      return {
        success: false,
        message: 'Ce stream n\'est pas encore en direct',
        streamId,
        nextStartTime: stream.startTime
      };
    }
    
    // Simuler une connexion réussie au livestream
    console.log(`Utilisateur ${userNickname} (${userId}) a rejoint le stream ${streamId}: ${stream.title}`);
    
    // Envoyer une notification
    await NotificationService.createNotification({
      type: 'basic',
      iconUrl: stream.thumbnail || '/icons/icon-128.png',
      title: 'Livestream rejoint',
      message: `Vous avez rejoint le stream: ${stream.title}`,
      priority: 0
    });
    
    return {
      success: true,
      stream,
      joinedAt: Date.now()
    };
  } catch (error) {
    console.error('Erreur lors de la connexion au livestream:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Échec de la connexion au livestream'
    };
  }
}

async function sendCommunityMessage(payload: any) {
  // Logique d'envoi de message
  try {
    const { channelId, userId, userNickname, message, attachments = [] } = payload;
    
    // Vérification du contenu du message
    if (!message || message.trim().length === 0) {
      return {
        success: false,
        error: 'Le message ne peut pas être vide'
      };
    }
    
    // Simuler l'envoi d'un message
    console.log(`Message de ${userNickname} (${userId}) dans le canal ${channelId}: ${message}`);
    
    // Si le message contient des mentions de NFT, vérifier la propriété
    if (message.includes('#NFT') && payload.nftContractAddress) {
      const hasNFT = await checkNFTFromWagmi(userId, payload.nftContractAddress);
      if (!hasNFT) {
        return {
          success: false,
          error: 'Vous ne possédez pas le NFT requis pour mentionner cette collection'
        };
      }
    }
    
    return {
      success: true,
      messageId: Date.now().toString(36) + Math.random().toString(36).substr(2),
      timestamp: Date.now(),
      channelId,
      userId,
      userNickname,
      message,
      attachments
    };
  } catch (error) {
    console.error('Erreur lors de l\'envoi du message:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Échec de l\'envoi du message'
    };
  }
}

async function updateUserSettings(payload: any) {
  // Mise à jour des paramètres utilisateur
  try {
    const { settings } = payload;
    
    // Récupérer les paramètres actuels
    chrome.storage.local.get(['settings'], (result) => {
      const currentSettings = result.settings || {};
      
      // Fusion des paramètres actuels avec les nouveaux
      const updatedSettings = {
        ...currentSettings,
        ...settings
      };
      
      // Enregistrer les nouveaux paramètres
      chrome.storage.local.set({ settings: updatedSettings }, () => {
        console.log('Paramètres utilisateur mis à jour:', updatedSettings);
      });
    });
    
    return {
      success: true,
      message: 'Paramètres mis à jour avec succès'
    };
  } catch (error) {
    console.error('Erreur lors de la mise à jour des paramètres:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Échec de la mise à jour des paramètres'
    };
  }
}

async function getEvents(payload: any) {
  // Récupération des événements
  try {
    const { filter, limit } = payload || {};
    let events = EventService.getEvents();
    
    // Filtrer les événements si nécessaire
    if (filter) {
      if (filter.tags && filter.tags.length > 0) {
        events = events.filter(event => 
          event.tags.some(tag => filter.tags.includes(tag))
        );
      }
      
      if (filter.requiresNFT !== undefined) {
        events = events.filter(event => event.requiresNFT === filter.requiresNFT);
      }
      
      if (filter.chainId) {
        events = events.filter(event => event.chainId === filter.chainId);
      }
      
      if (filter.upcoming) {
        const now = Date.now();
        events = events.filter(event => event.startDate > now);
      }
    }
    
    // Limiter le nombre d'événements si nécessaire
    if (limit && events.length > limit) {
      events = events.slice(0, limit);
    }
    
    return {
      success: true,
      events,
      total: events.length
    };
  } catch (error) {
    console.error('Erreur lors de la récupération des événements:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Échec de la récupération des événements'
    };
  }
}

async function addEvent(payload: any) {
  // Ajout d'un nouvel événement
  try {
    const eventData = payload.event;
    
    // Valider les données de l'événement
    if (!eventData.name || !eventData.startDate || !eventData.endDate) {
      return {
        success: false,
        error: 'Les données de l\'événement sont incomplètes. Nom, date de début et date de fin sont requis.'
      };
    }
    
    // Générer un ID unique pour l'événement
    const eventId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    
    // Créer l'événement
    const newEvent = {
      id: eventId,
      ...eventData,
      tags: eventData.tags || []
    };
    
    // Ajouter l'événement
    EventService.addEvent(newEvent);
    
    // Notifier l'utilisateur
    await NotificationService.createNotification({
      type: 'basic',
      iconUrl: eventData.imageUrl || '/icons/icon-128.png',
      title: 'Nouvel événement créé',
      message: `L'événement ${eventData.name} a été créé avec succès`,
      priority: 0
    });
    
    return {
      success: true,
      eventId,
      event: newEvent
    };
  } catch (error) {
    console.error('Erreur lors de la création d\'un événement:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Échec de la création de l\'événement'
    };
  }
}
