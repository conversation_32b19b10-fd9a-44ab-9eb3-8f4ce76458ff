// Configuration des chaînes blockchain supportées par l'extension

export interface Chain {
  id: number;
  name: string;
  rpcUrl: string;
  blockExplorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

// Liste des chaînes supportées
export const chains: Chain[] = [
  {
    id: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
    blockExplorerUrl: 'https://etherscan.io',
    nativeCurrency: {
      name: '<PERSON><PERSON>',
      symbol: 'ETH',
      decimals: 18
    }
  },
  {
    id: 137,
    name: 'Polygon',
    rpcUrl: 'https://polygon-rpc.com',
    blockExplorerUrl: 'https://polygonscan.com',
    nativeCurrency: {
      name: 'MATI<PERSON>',
      symbol: 'MATIC',
      decimals: 18
    }
  }
];

// Fonction pour obtenir une chaîne par son ID
export function getChainById(chainId: number): Chain | undefined {
  return chains.find(chain => chain.id === chainId);
}

export default chains; 