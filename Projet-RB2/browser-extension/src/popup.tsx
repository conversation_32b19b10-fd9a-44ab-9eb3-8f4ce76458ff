import React, { useEffect, useState } from 'react';
import { WalletConnect } from './components/WalletConnect';
import { NFTGallery } from './components/NFTGallery';
import { useWallet } from './hooks/useWallet';

export const Popup: React.FC = () => {
  const { isConnected, connect, disconnect } = useWallet();
  const [nfts, setNfts] = useState([]);

  useEffect(() => {
    if (isConnected) {
      fetchUserNFTs();
    }
  }, [isConnected]);

  const fetchUserNFTs = async () => {
    // Récupération des NFTs de l'utilisateur
  };

  return (
    <div className="popup-container">
      <header>
        <h1>Retreat And Be</h1>
        <WalletConnect 
          isConnected={isConnected}
          onConnect={connect}
          onDisconnect={disconnect}
        />
      </header>
      
      {isConnected && (
        <NFTGallery nfts={nfts} />
      )}
    </div>
  );
};