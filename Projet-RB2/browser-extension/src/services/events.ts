// Service d'événements pour l'extension de navigateur

// Interface pour les données d'événement
export interface EventData {
  id: string;
  name: string;
  description: string;
  startDate: number; // timestamp
  endDate: number; // timestamp
  location?: string;
  url?: string;
  imageUrl?: string;
  tags: string[];
  requiresNFT?: boolean;
  contractAddress?: string;
  chainId?: number;
}

// Classe de service pour gérer les événements
export class EventService {
  private static events: Map<string, EventData> = new Map();

  // Méthode pour initialiser le service
  static initialize() {
    console.log('Initialisation du service d\'événements');
    
    // Charger les événements stockés
    this.loadEvents();
    
    // Configuration d'un intervalle pour les rappels d'événements
    setInterval(() => {
      this.checkUpcomingEvents();
    }, 3600000); // Vérifier toutes les heures
  }

  // Méthode pour obtenir tous les événements
  static getEvents(): EventData[] {
    return Array.from(this.events.values());
  }

  // Méthode pour obtenir un événement par son ID
  static getEventById(eventId: string): EventData | undefined {
    return this.events.get(eventId);
  }

  // Méthode pour ajouter un nouvel événement
  static addEvent(event: EventData): void {
    this.events.set(event.id, event);
    this.saveEvents();
  }

  // Méthode pour mettre à jour un événement existant
  static updateEvent(eventId: string, updates: Partial<EventData>): boolean {
    const event = this.events.get(eventId);
    
    if (!event) {
      return false;
    }
    
    this.events.set(eventId, { ...event, ...updates });
    this.saveEvents();
    return true;
  }

  // Méthode pour supprimer un événement
  static deleteEvent(eventId: string): boolean {
    const result = this.events.delete(eventId);
    if (result) {
      this.saveEvents();
    }
    return result;
  }

  // Méthode pour sauvegarder les événements dans le stockage
  private static saveEvents(): void {
    if (chrome && chrome.storage) {
      chrome.storage.local.set({
        events: Array.from(this.events.values())
      });
    }
  }

  // Méthode pour charger les événements depuis le stockage
  private static loadEvents(): void {
    if (chrome && chrome.storage) {
      chrome.storage.local.get(['events'], (result) => {
        if (result.events && Array.isArray(result.events)) {
          this.events.clear();
          result.events.forEach((event: EventData) => {
            this.events.set(event.id, event);
          });
        }
      });
    }
  }

  // Méthode pour vérifier les événements à venir et envoyer des rappels
  private static checkUpcomingEvents(): void {
    const now = Date.now();
    const oneHour = 3600000;
    const oneDay = 86400000;
    
    this.events.forEach(event => {
      const timeUntilStart = event.startDate - now;
      
      // Rappel 1 jour avant
      if (timeUntilStart > 0 && timeUntilStart <= oneDay) {
        this.sendEventReminder(event, 'dans 24 heures');
      }
      
      // Rappel 1 heure avant
      if (timeUntilStart > 0 && timeUntilStart <= oneHour) {
        this.sendEventReminder(event, 'dans 1 heure');
      }
    });
  }

  // Méthode pour envoyer un rappel d'événement
  private static sendEventReminder(event: EventData, timeFrame: string): void {
    if (chrome && chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: event.imageUrl || '/icons/icon-128.png',
        title: `Rappel d'événement: ${event.name}`,
        message: `L'événement commence ${timeFrame}`,
        contextMessage: event.description
      });
    }
  }
}

export default EventService; 