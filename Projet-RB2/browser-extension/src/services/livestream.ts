// Service de livestream pour l'extension de navigateur

// Interface pour les données de stream
export interface StreamData {
  id: string;
  title: string;
  description: string;
  url: string;
  startTime: number;
  endTime?: number;
  isLive: boolean;
  thumbnail?: string;
  creator: {
    id: string;
    name: string;
    avatar?: string;
  };
}

// Classe de service pour gérer les livestreams
export class LivestreamService {
  private static streams: Map<string, StreamData> = new Map();

  // Méthode pour démarrer la surveillance des streams
  static initialize() {
    console.log('Initialisation du service de livestream');
    
    // Configuration d'un intervalle pour mettre à jour le statut des streams (simulation)
    setInterval(() => {
      this.checkStreamUpdates();
    }, 60000); // Vérifier toutes les minutes
  }

  // Méthode pour obtenir tous les streams
  static getStreams(): StreamData[] {
    return Array.from(this.streams.values());
  }

  // Méthode pour obtenir un stream par son ID
  static getStreamById(streamId: string): StreamData | undefined {
    return this.streams.get(streamId);
  }

  // Méthode pour ajouter un nouveau stream
  static addStream(stream: StreamData): void {
    this.streams.set(stream.id, stream);
    
    // Notifier l'utilisateur du nouveau stream
    if (chrome && chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: stream.thumbnail || '/icons/icon-128.png',
        title: 'Nouveau livestream',
        message: `${stream.title} par ${stream.creator.name}`
      });
    }
  }

  // Méthode pour mettre à jour un stream existant
  static updateStream(streamId: string, updates: Partial<StreamData>): boolean {
    const stream = this.streams.get(streamId);
    
    if (!stream) {
      return false;
    }
    
    this.streams.set(streamId, { ...stream, ...updates });
    return true;
  }

  // Simuler la vérification des mises à jour des streams
  private static checkStreamUpdates(): void {
    console.log('Vérification des mises à jour des streams');
    
    // Parcourir tous les streams et simuler des changements de statut
    this.streams.forEach(stream => {
      // Simuler des changements (dans une version réelle, vous feriez des appels API)
      const now = Date.now();
      
      // Si le stream est prévu pour commencer et qu'il n'est pas encore en direct
      if (stream.startTime <= now && !stream.isLive && (!stream.endTime || stream.endTime > now)) {
        this.updateStream(stream.id, { isLive: true });
        
        // Notifier l'utilisateur
        if (chrome && chrome.notifications) {
          chrome.notifications.create({
            type: 'basic',
            iconUrl: stream.thumbnail || '/icons/icon-128.png',
            title: 'Livestream démarré',
            message: `${stream.title} par ${stream.creator.name} est maintenant en direct!`
          });
        }
      }
      
      // Si le stream est terminé mais toujours marqué comme en direct
      if (stream.endTime && stream.endTime < now && stream.isLive) {
        this.updateStream(stream.id, { isLive: false });
      }
    });
  }
}

export default LivestreamService; 