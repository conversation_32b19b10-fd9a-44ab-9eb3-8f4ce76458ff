// Type definitions for Chrome extension API
// This is a simplified version of the Chrome types

declare namespace chrome {
  namespace runtime {
    const lastError: chrome.runtime.LastError | undefined;
    
    interface LastError {
      message?: string;
    }
    
    function getURL(path: string): string;
    function getManifest(): any;
    function reload(): void;
    function restart(): void;
    
    function sendMessage(message: any, responseCallback?: (response: any) => void): void;
    function sendMessage(extensionId: string, message: any, responseCallback?: (response: any) => void): void;
    
    interface MessageSender {
      id?: string;
      tab?: chrome.tabs.Tab;
      frameId?: number;
      url?: string;
      origin?: string;
    }
    
    interface ConnectInfo {
      name?: string;
      includeTlsChannelId?: boolean;
    }
    
    interface Port {
      name: string;
      disconnect(): void;
      onDisconnect: events.Event<(port: Port) => void>;
      onMessage: events.Event<(message: any, port: Port) => void>;
      postMessage(message: any): void;
      sender?: MessageSender;
    }
    
    function connect(connectInfo?: ConnectInfo): Port;
    function connect(extensionId: string, connectInfo?: ConnectInfo): Port;
    
    interface MessageOptions {
      includeTlsChannelId?: boolean;
    }
    
    type MessageCallback = (message: any, sender: MessageSender, sendResponse: (response?: any) => void) => boolean | void;
    
    interface OnMessageEvent extends events.Event<MessageCallback> {
      addListener(callback: MessageCallback): void;
    }
    
    const onMessage: OnMessageEvent;
    
    // Add onInstalled event
    type InstalledCallback = (details: {
      reason: "install" | "update" | "chrome_update" | "shared_module_update";
      previousVersion?: string;
      id?: string;
    }) => void;
    
    interface OnInstalledEvent extends events.Event<InstalledCallback> {
      addListener(callback: InstalledCallback): void;
    }
    
    const onInstalled: OnInstalledEvent;
  }
  
  namespace storage {
    interface StorageArea {
      get(keys: string | string[] | object | null, callback: (items: { [key: string]: any }) => void): void;
      set(items: object, callback?: () => void): void;
      remove(keys: string | string[], callback?: () => void): void;
      clear(callback?: () => void): void;
    }
    
    const local: StorageArea;
    const sync: StorageArea;
  }
  
  namespace tabs {
    interface Tab {
      id?: number;
      index: number;
      windowId: number;
      highlighted: boolean;
      active: boolean;
      pinned: boolean;
      url?: string;
      title?: string;
      favIconUrl?: string;
      status?: string;
      incognito: boolean;
      width?: number;
      height?: number;
      sessionId?: string;
    }
    
    function get(tabId: number, callback: (tab: Tab) => void): void;
    function getCurrent(callback: (tab?: Tab) => void): void;
    function create(createProperties: object, callback?: (tab: Tab) => void): void;
    function update(tabId: number, updateProperties: object, callback?: (tab?: Tab) => void): void;
    function remove(tabIds: number | number[], callback?: () => void): void;
    function query(queryInfo: object, callback: (result: Tab[]) => void): void;
  }
  
  namespace notifications {
    type TemplateType = 'basic' | 'image' | 'list' | 'progress';
    
    interface NotificationOptions {
      type: TemplateType;
      iconUrl?: string;
      appIconMaskUrl?: string;
      title: string;
      message: string;
      contextMessage?: string;
      priority?: number;
      eventTime?: number;
      buttons?: { title: string; iconUrl?: string }[];
      imageUrl?: string;
      items?: { title: string; message: string }[];
      progress?: number;
      isClickable?: boolean;
      requireInteraction?: boolean;
      silent?: boolean;
    }
    
    function create(options: NotificationOptions, callback?: (notificationId: string) => void): void;
    function create(notificationId: string, options: NotificationOptions, callback?: (notificationId: string) => void): void;
    function clear(notificationId: string, callback?: (wasCleared: boolean) => void): void;
    function getAll(callback: (notifications: { [key: string]: any }) => void): void;
  }
  
  namespace alarms {
    interface Alarm {
      name: string;
      scheduledTime: number;
      periodInMinutes?: number;
    }
    
    function create(name: string, alarmInfo: { when?: number; delayInMinutes?: number; periodInMinutes?: number }): void;
    function getAll(callback: (alarms: Alarm[]) => void): void;
    function clearAll(callback?: (wasCleared: boolean) => void): void;
    function clear(name?: string, callback?: (wasCleared: boolean) => void): void;
    
    // Add onAlarm event
    type AlarmCallback = (alarm: Alarm) => void;
    
    interface OnAlarmEvent extends events.Event<AlarmCallback> {
      addListener(callback: AlarmCallback): void;
    }
    
    const onAlarm: OnAlarmEvent;
  }
  
  namespace events {
    interface Event<T extends Function> {
      addListener(callback: T): void;
      removeListener(callback: T): void;
      hasListener(callback: T): boolean;
    }
  }
}

// Declare chrome as a global object
declare const chrome: typeof chrome; 