replicaCount: 2
image:
  repository: backend-service
  tag: latest
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  port: 5000
  targetPort: 5000
securityContext:
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
resources:
  requests:
    cpu: 500m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 1Gi
postgresql:
  enabled: true
  auth:
    database: backend_db
    username: backend_user
redis:
  enabled: true
  auth:
    enabled: true
ingress:
  enabled: false
livenessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10
readinessProbe:
  httpGet:
    path: /ready
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5
