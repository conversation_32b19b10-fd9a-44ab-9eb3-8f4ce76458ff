apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: frontend
  labels:
    app: frontend
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend
  service:
    port: 3000
    targetPort: 3000
  analysis:
    interval: "1m"
    threshold: 10
    maxWeight: 50
    stepWeight: 5
    metrics:
    - name: request-success-rate
      threshold: 99
      interval: "1m"
    - name: request-duration
      threshold: 500
      interval: "1m"
    webhooks:
      - name: load-test
        url: http://flagger-loadtester.test/
        timeout: 5s
        metadata:
          cmd: "hey -z 1m -q 10 -c 2 http://frontend-canary.test:3000/health"
  progressDeadlineSeconds: 60