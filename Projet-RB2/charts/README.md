# Retreat And Be - Helm Charts

## Overview
This directory contains Helm charts for deploying the Retreat And Be platform's microservices architecture. Each service is packaged as an independent Helm chart, allowing for flexible deployment and management.

## Service Architecture

Core Services:
- **Backend**: Main API service handling business logic
- **Frontend**: React-based user interface
- **Analyzer**: AI and analytics service
- **Decentralized-Storage**: IPFS-based storage solution
- **rb-new**: AI Layer with orchestrator and microservices

Supporting Services:
- **Security-Service**: Authentication and authorization
- **Messaging-Service**: Real-time communication
- **Financial-Service**: Payment processing and transactions

Feature Services:
- **Social**: Community and social networking features
- **VR**: Virtual reality experiences
- **Transport-Booking**: Transportation management
- **Hotel-Booking**: Accommodation services
- **Website-Creator**: Custom website builder
- **Education**: Learning platform features

Infrastructure Services:
- **Monitoring**: Prometheus and Grafana setup
- **Istio**: Service mesh configuration

## Installation

### Prerequisites
- Kubernetes 1.19+
- Helm 3.2.0+
- Prometheus Operator (for monitoring)
- Istio (for service mesh)

### Global Installation
```bash
# Add required repositories
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add istio https://istio.io/charts

# Update repositories
helm repo update

# Install infrastructure services first
helm install monitoring ./charts/monitoring
helm install istio-base ./charts/istio/base

# Install core services
helm install backend ./charts/Backend
helm install frontend ./charts/Frontend
helm install analyzer ./charts/Analyzer
helm install storage ./charts/Decentralized-Storage
helm install rb-new ./charts/rb-new

# Or install the main chart that includes all components
helm install retreatandbe ./charts/retreatandbe
```

## Configuration

Each service has its own configuration values in `values.yaml`. Common configurations include:

- Image repository and tag
- Resource limits and requests
- Service type and port
- Environment variables
- Persistence configuration

## Maintenance

### Upgrading
```bash
helm upgrade [RELEASE] ./charts/[CHART] --values custom-values.yaml
```

### Rollback
```bash
helm rollback [RELEASE] [REVISION]
```

### Uninstalling
```bash
helm uninstall [RELEASE]
```

## Development

### Adding a New Service
1. Copy the `_template` directory
2. Customize the Chart.yaml and values.yaml
3. Update templates as needed
4. Document in README.md

### Testing Charts
```bash
# Lint the chart
helm lint ./charts/[CHART]

# Test template rendering
helm template ./charts/[CHART]

# Run chart tests
./validate-charts.sh
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## Support
For support and issues:
1. Check the chart's specific README
2. Review troubleshooting guides
3. Submit an issue with detailed information

## License
Copyright © 2024 Retreat And Be