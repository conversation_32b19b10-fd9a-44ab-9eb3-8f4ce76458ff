# Default values for browser-extension
replicaCount: 2

image:
  repository: retreatandbe/browser-extension
  tag: latest
  pullPolicy: Always

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: extension.retreatandbe.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: extension-tls
      hosts:
        - extension.retreatandbe.com

resources:
  limits:
    cpu: 200m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80

monitoring:
  enabled: true
  serviceMonitor:
    enabled: true

config:
  apiUrl: https://api.retreatandbe.com
  websocketUrl: wss://ws.retreatandbe.com
  updateInterval: 3600