redis:
  enabled: true
  image: redis:alpine
  resources:
    limits:
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
    - type: External
      external:
        metric:
          name: http_requests_per_second
          selector:
            matchLabels:
              service: financial
        target:
          type: AverageValue
          averageValue: 100
