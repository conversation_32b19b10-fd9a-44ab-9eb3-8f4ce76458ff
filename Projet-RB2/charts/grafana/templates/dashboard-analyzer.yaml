apiVersion: monitoring.grafana.com/v1alpha1
kind: GrafanaDashboard
metadata:
  name: analyzer-live
spec:
  json: >
    {
      "title": "Analyzer Live Metrics",
      "refresh": "5s",
      "panels": [
        {
          "type": "graph",
          "title": "Traffic Réel",
          "targets": [{
            "expr": "sum(rate(http_requests_total[1m])) by (service)",
            "instant": true
          }]
        },
        {
          "type": "gauge",
          "title": "Memory Usage",
          "targets": [{
            "expr": "process_resident_memory_bytes{service=\"analyzer\"}",
            "instant": true
          }]
        },
        {
          "type": "timeseries",
          "title": "Response Times",
          "targets": [{
            "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service=\"analyzer\"}[5m])) by (le))",
            "legendFormat": "p95"
          }]
        }
      ]
    }