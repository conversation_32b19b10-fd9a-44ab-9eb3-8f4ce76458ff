package analyzer.authz

default allow = false

allow {
  input.method == "GET"
  input.path == ["health"]
}

allow {
  jwt.payload.roles[_] == "admin"
  input.method == "POST"
  input.path == ["api", "v1", "analyze", _]
}

allow {
  jwt.payload.roles[_] == "user"
  input.method == "GET"
  input.path == ["api", "v1", "analyze", "status", _]
}

allow {
  jwt.verify
  jwt.payload.scope == "service"
  input.path == ["api", "internal", _]
}