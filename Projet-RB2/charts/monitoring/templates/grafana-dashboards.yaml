apiVersion: monitoring.grafana.com/v1alpha1
kind: GrafanaDashboard
metadata:
  name: microservices-overview
  labels:
    grafana_dashboard: "true"
spec:
  json: |
    {
      "title": "Microservices Metrics Overview",
      "uid": "microservices-metrics",
      "panels": [
        {
          "type": "heatmap",
          "title": "Inter-Service Requests",
          "targets": [{
            "expr": "sum(rate(istio_requests_total[5m])) by (source_service, destination_service)",
            "format": "heatmap"
          }]
        },
        {
          "type": "graph",
          "title": "Service Response Times",
          "targets": [{
            "expr": "histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket[5m])) by (service, le))"
          }]
        },
        {
          "type": "stat",
          "title": "Total Error Rate",
          "targets": [{
            "expr": "sum(rate(istio_requests_total{response_code=~\"5.*\"}[5m])) / sum(rate(istio_requests_total[5m])) * 100"
          }]
        },
        {
          "type": "gauge",
          "title": "Resource Usage",
          "targets": [{
            "expr": "sum(container_memory_usage_bytes) by (pod) / sum(container_spec_memory_limit_bytes) by (pod) * 100"
          }]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 33,
      "style": "dark",
      "timezone": "browser",
      "version": 1
    }