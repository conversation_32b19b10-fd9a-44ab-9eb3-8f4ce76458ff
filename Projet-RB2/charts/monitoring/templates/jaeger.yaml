apiVersion: jaegertracing.io/v1
kind: Jaeger
metadata:
  name: retreat-jaeger
  namespace: monitoring
spec:
  strategy: production
  storage:
    type: elasticsearch
    options:
      es:
        server-urls: http://elasticsearch:9200
  ingress:
    enabled: true
    annotations:
      kubernetes.io/ingress.class: nginx
    hosts:
      - jaeger.retreatandbe.com
  query:
    serviceType: ClusterIP
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 250m
        memory: 256Mi
  collector:
    replicas: 2
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
  agent:
    strategy: DaemonSet
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
  storage:
    dependencies:
      enabled: true
      schedule: "55 23 * * *"
    esIndexCleaner:
      enabled: true
      numberOfDays: 7
      schedule: "55 23 * * *"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "16686"