{{/*
Expand the name of the chart.
*/}}
{{- define "rb-new.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "rb-new.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "rb-new.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "rb-new.labels" -}}
helm.sh/chart: {{ include "rb-new.chart" . }}
{{ include "rb-new.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "rb-new.selectorLabels" -}}
app.kubernetes.io/name: {{ include "rb-new.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "rb-new.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "rb-new.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create a fully qualified orchestrator name.
*/}}
{{- define "rb-new.orchestrator.fullname" -}}
{{- printf "%s-orchestrator" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified recommender name.
*/}}
{{- define "rb-new.recommender.fullname" -}}
{{- printf "%s-recommender" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified chatbot name.
*/}}
{{- define "rb-new.chatbot.fullname" -}}
{{- printf "%s-chatbot" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified content-generator name.
*/}}
{{- define "rb-new.contentGenerator.fullname" -}}
{{- printf "%s-content-generator" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified analytics-engine name.
*/}}
{{- define "rb-new.analyticsEngine.fullname" -}}
{{- printf "%s-analytics-engine" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified feature-flags name.
*/}}
{{- define "rb-new.featureFlags.fullname" -}}
{{- printf "%s-feature-flags" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified virtual-coach name.
*/}}
{{- define "rb-new.virtualCoach.fullname" -}}
{{- printf "%s-virtual-coach" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a fully qualified mlflow name.
*/}}
{{- define "rb-new.mlflow.fullname" -}}
{{- printf "%s-mlflow" (include "rb-new.fullname" .) | trunc 63 | trimSuffix "-" -}}
{{- end -}}
