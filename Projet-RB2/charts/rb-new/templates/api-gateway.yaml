{{- if .Values.apiGateway.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "rb-new.fullname" . }}-api-gateway
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
  annotations:
    kubernetes.io/ingress.class: {{ .Values.apiGateway.ingressClass | quote }}
    {{- if eq .Values.apiGateway.type "kong" }}
    konghq.com/strip-path: "true"
    konghq.com/plugins: {{ include "rb-new.fullname" . }}-auth-plugin
    {{- end }}
    {{- if eq .Values.apiGateway.type "ambassador" }}
    getambassador.io/config: |
      ---
      apiVersion: ambassador/v1
      kind: AuthService
      name: authentication
      auth_service: {{ .Values.apiGateway.authService }}
      proto: http
      path_prefix: /auth
      timeout_ms: 5000
    {{- end }}
spec:
  rules:
  - host: {{ .Values.apiGateway.host | quote }}
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: {{ include "rb-new.orchestrator.fullname" . }}
            port:
              number: {{ .Values.orchestrator.service.port }}
{{- end }}

{{- if and .Values.apiGateway.enabled (eq .Values.apiGateway.type "kong") }}
---
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: {{ include "rb-new.fullname" . }}-auth-plugin
spec:
  plugin: jwt
  config:
    secret_is_base64: false
    claims_to_verify:
    - exp
    - nbf
{{- end }}
