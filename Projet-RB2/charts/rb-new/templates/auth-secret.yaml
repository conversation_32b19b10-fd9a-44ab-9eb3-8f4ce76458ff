{{- if .Values.auth.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "rb-new.fullname" . }}-auth-secret
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if .Values.auth.clientSecret }}
  client-secret: {{ .Values.auth.clientSecret | b64enc | quote }}
  {{- else }}
  client-secret: {{ randAlphaNum 32 | b64enc | quote }}
  {{- end }}
  {{- if .Values.auth.jwtSecret }}
  jwt-secret: {{ .Values.auth.jwtSecret | b64enc | quote }}
  {{- else }}
  jwt-secret: {{ randAlphaNum 64 | b64enc | quote }}
  {{- end }}
{{- end }}
