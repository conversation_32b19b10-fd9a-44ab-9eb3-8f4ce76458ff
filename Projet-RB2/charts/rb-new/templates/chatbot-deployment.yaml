{{- if .Values.aiEngine.chatbot.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rb-new.chatbot.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: chatbot
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "rb-new.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: chatbot
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "rb-new.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: chatbot
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}-chatbot
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.global.imageRegistry }}{{ .Values.aiEngine.chatbot.image.repository }}:{{ .Values.aiEngine.chatbot.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.aiEngine.chatbot.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.aiEngine.chatbot.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 30
          resources:
            {{- toYaml .Values.aiEngine.chatbot.resources | nindent 12 }}
          env:
            {{- range .Values.env.common }}
            - name: {{ .name }}
              value: {{ .value | quote }}
            {{- end }}
            {{- range .Values.env.database }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}
            {{- range .Values.env.redis }}
            - name: {{ .name }}
              {{- if .value }}
              value: {{ .value | quote }}
              {{- else if .valueFrom }}
              valueFrom:
                {{- toYaml .valueFrom | nindent 16 }}
              {{- end }}
            {{- end }}
          volumeMounts:
            - name: models-volume
              mountPath: /app/models
      volumes:
        - name: models-volume
          {{- if .Values.persistence.models.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "rb-new.fullname" . }}-models
          {{- else }}
          emptyDir: {}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
