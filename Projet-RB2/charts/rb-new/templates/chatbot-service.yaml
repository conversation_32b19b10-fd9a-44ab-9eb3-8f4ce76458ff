{{- if .Values.aiEngine.chatbot.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.chatbot.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: chatbot
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiEngine.chatbot.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: chatbot
{{- end }}
