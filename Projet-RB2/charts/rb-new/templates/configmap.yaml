apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "rb-new.fullname" . }}-config
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
data:
  # General configuration
  ENVIRONMENT: {{ .Values.global.environment | quote }}
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_DEBUG: {{ eq .Values.global.environment "development" | ternary "True" "False" | quote }}
  LOG_LEVEL: {{ .Values.config.logLevel | default "INFO" | quote }}

  # AI model configuration
  MODEL_PATH: "/app/models/"
  RECOMMENDER_MODEL: {{ .Values.config.models.recommender | default "recommender_v1" | quote }}
  CONTENT_MODEL: {{ .Values.config.models.content | default "content_generator_v1" | quote }}
  CHATBOT_MODEL: {{ .Values.config.models.chatbot | default "chatbot_v1" | quote }}
  ANALYTICS_MODEL: {{ .Values.config.models.analytics | default "analytics_v1" | quote }}
  OPTIMIZER_MODEL: {{ .Values.config.models.optimizer | default "optimizer_v1" | quote }}

  # Feature flags
  FEATURE_RECOMMENDER_ENABLED: {{ .Values.config.features.recommender | default true | quote }}
  FEATURE_CHATBOT_ENABLED: {{ .Values.config.features.chatbot | default true | quote }}
  FEATURE_CONTENT_GENERATOR_ENABLED: {{ .Values.config.features.contentGenerator | default true | quote }}
  FEATURE_ANALYTICS_ENABLED: {{ .Values.config.features.analytics | default true | quote }}

  # Kafka configuration
  KAFKA_BOOTSTRAP_SERVERS: {{ .Values.config.kafka.bootstrapServers | default "kafka:9092" | quote }}
  KAFKA_TOPIC_PREFIX: {{ .Values.config.kafka.topicPrefix | default "retreatandbe" | quote }}

  # Application configuration
  app-config.json: |
    {
      "version": "{{ .Chart.AppVersion }}",
      "environment": "{{ .Values.global.environment }}",
      "services": {
        "recommender": {
          "url": "http://{{ include "rb-new.recommender.fullname" . }}:{{ .Values.aiEngine.recommender.service.port }}",
          "timeout": {{ .Values.config.timeouts.recommender | default 30 }}
        },
        "chatbot": {
          "url": "http://{{ include "rb-new.chatbot.fullname" . }}:{{ .Values.aiEngine.chatbot.service.port }}",
          "timeout": {{ .Values.config.timeouts.chatbot | default 60 }}
        },
        "contentGenerator": {
          "url": "http://{{ include "rb-new.contentGenerator.fullname" . }}:{{ .Values.aiEngine.contentGenerator.service.port }}",
          "timeout": {{ .Values.config.timeouts.contentGenerator | default 120 }}
        },
        "analyticsEngine": {
          "url": "http://{{ include "rb-new.analyticsEngine.fullname" . }}:{{ .Values.aiEngine.analyticsEngine.service.port }}",
          "timeout": {{ .Values.config.timeouts.analyticsEngine | default 60 }}
        },
        "featureFlags": {
          "url": "http://{{ include "rb-new.featureFlags.fullname" . }}:{{ .Values.aiEngine.featureFlags.service.port }}",
          "timeout": {{ .Values.config.timeouts.featureFlags | default 10 }}
        },
        "virtualCoach": {
          "url": "http://{{ include "rb-new.virtualCoach.fullname" . }}:{{ .Values.aiEngine.virtualCoach.service.port }}",
          "timeout": {{ .Values.config.timeouts.virtualCoach | default 60 }}
        },
        "mlflow": {
          "url": "http://{{ include "rb-new.mlflow.fullname" . }}:{{ .Values.mlflow.service.port }}",
          "timeout": {{ .Values.config.timeouts.mlflow | default 30 }}
        }
      },
      "monitoring": {
        "enabled": {{ .Values.monitoring.enabled }},
        "metrics": {
          "interval": "{{ .Values.monitoring.serviceMonitor.interval }}"
        }
      }
    }
