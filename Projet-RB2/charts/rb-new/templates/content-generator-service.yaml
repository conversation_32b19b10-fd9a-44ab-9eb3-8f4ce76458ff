{{- if .Values.aiEngine.contentGenerator.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.contentGenerator.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: content-generator
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiEngine.contentGenerator.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: content-generator
{{- end }}
