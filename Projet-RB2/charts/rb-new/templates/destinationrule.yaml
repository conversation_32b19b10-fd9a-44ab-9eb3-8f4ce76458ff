{{- if .Values.istio.enabled }}
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{ include "rb-new.orchestrator.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  host: {{ include "rb-new.orchestrator.fullname" . }}
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 5s
      http:
        http1MaxPendingRequests: 100
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
  subsets:
  - name: v1
    labels:
      version: v1
{{- end }}

{{- if and .Values.istio.enabled .Values.aiEngine.recommender.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{ include "rb-new.recommender.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  host: {{ include "rb-new.recommender.fullname" . }}
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 100
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
  subsets:
  - name: v1
    labels:
      version: v1
{{- end }}
