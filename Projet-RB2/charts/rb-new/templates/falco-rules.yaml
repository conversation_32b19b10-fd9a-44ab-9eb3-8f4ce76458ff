{{- if .Values.falco.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "rb-new.fullname" . }}-falco-rules
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
data:
  rb-new-rules.yaml: |-
    - rule: Terminal shell in rb-new container
      desc: A shell was spawned in a rb-new container
      condition: >
        evt.type = execve and
        evt.dir=< and
        container.image.repository contains "rb-new" and
        proc.name = bash
      output: >
        Shell spawned in a rb-new container (user=%user.name container_id=%container.id
        container_name=%container.name image=%container.image.repository:%container.image.tag)
      priority: WARNING
      tags: [container, shell, rb-new]

    - rule: Package management process in rb-new container
      desc: Package management process executed in rb-new container
      condition: >
        evt.type = execve and
        evt.dir=< and
        container.image.repository contains "rb-new" and
        (proc.name in (apt, apt-get, pip, pip3, npm, yarn))
      output: >
        Package management process executed in rb-new container (user=%user.name command=%proc.cmdline
        container_id=%container.id container_name=%container.name image=%container.image.repository:%container.image.tag)
      priority: WARNING
      tags: [container, process, rb-new]

    - rule: Outbound connection to suspicious network
      desc: Detect outbound connections to suspicious networks
      condition: >
        evt.type = connect and
        evt.dir = < and
        container.image.repository contains "rb-new" and
        (fd.sip in (suspicious_ips) or fd.snet in (suspicious_nets))
      output: >
        Outbound connection to suspicious network detected (user=%user.name command=%proc.cmdline
        connection=%fd.name container_id=%container.id container_name=%container.name image=%container.image.repository:%container.image.tag)
      priority: WARNING
      tags: [container, network, rb-new]
      
    - list: suspicious_ips
      items: [
        # Liste d'IPs suspectes
        "*******",
        "*******"
      ]
      
    - list: suspicious_nets
      items: [
        # Liste de réseaux suspects
        "10.0.0.0/24",
        "***********/16"
      ]
{{- end }}
