{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "rb-new.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "rb-new.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow ingress from the same namespace
    - from:
        - podSelector: {}
      ports:
        - protocol: TCP
          port: {{ .Values.orchestrator.service.port }}
    # Allow ingress from the ingress controller
    {{- if .Values.networkPolicy.allowIngressFromNamespace }}
    - from:
        - namespaceSelector:
            matchLabels:
              name: {{ .Values.networkPolicy.ingressNamespace }}
      ports:
        - protocol: TCP
          port: {{ .Values.orchestrator.service.port }}
    {{- end }}
    # Allow ingress from API Gateway if enabled
    {{- if .Values.apiGateway.enabled }}
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: {{ .Values.apiGateway.type }}
      ports:
        - protocol: TCP
          port: {{ .Values.orchestrator.service.port }}
    {{- end }}
    # Allow ingress from frontend if specified
    {{- if .Values.networkPolicy.allowFrontendAccess }}
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: frontend
        {{- if .Values.networkPolicy.frontendNamespace }}
        - namespaceSelector:
            matchLabels:
              name: {{ .Values.networkPolicy.frontendNamespace }}
        {{- end }}
      ports:
        - protocol: TCP
          port: {{ .Values.orchestrator.service.port }}
    {{- end }}
  egress:
    # Allow egress to the same namespace
    - to:
        - podSelector: {}
    # Allow egress to PostgreSQL
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: postgresql
      ports:
        - protocol: TCP
          port: 5432
    # Allow egress to Redis
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: redis
      ports:
        - protocol: TCP
          port: 6379
    # Allow DNS resolution
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow egress to external APIs if needed
    {{- if .Values.networkPolicy.allowExternalEgress }}
    - to:
        - ipBlock:
            cidr: 0.0.0.0/0
            except:
              - 10.0.0.0/8
              - **********/12
              - ***********/16
      ports:
        - protocol: TCP
          port: 443
    {{- end }}
{{- end }}
