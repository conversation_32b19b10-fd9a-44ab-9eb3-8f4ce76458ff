{{- if .Values.opa.enabled }}
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sSecurityContext
metadata:
  name: {{ include "rb-new.fullname" . }}-security-context
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
    namespaces:
      - {{ .Release.Namespace }}
    labelSelector:
      matchLabels:
        {{- include "rb-new.selectorLabels" . | nindent 8 }}
  parameters:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    runAsNonRoot: true
    capabilities:
      required:
        drop:
          - ALL
---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sBlockNodePort
metadata:
  name: {{ include "rb-new.fullname" . }}-block-nodeport
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Service"]
    namespaces:
      - {{ .Release.Namespace }}
---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredLabels
metadata:
  name: {{ include "rb-new.fullname" . }}-required-labels
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
    namespaces:
      - {{ .Release.Namespace }}
  parameters:
    labels:
      - key: "app.kubernetes.io/name"
      - key: "app.kubernetes.io/instance"
      - key: "app.kubernetes.io/component"
{{- end }}
