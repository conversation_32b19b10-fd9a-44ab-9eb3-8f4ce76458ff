{{- if .Values.aiEngine.recommender.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.recommender.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: recommender
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiEngine.recommender.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: recommender
{{- end }}
