apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "rb-new.fullname" . }}-test-connection"
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "rb-new.orchestrator.fullname" . }}:{{ .Values.orchestrator.service.port }}']
  restartPolicy: Never
