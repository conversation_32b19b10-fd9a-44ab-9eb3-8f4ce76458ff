{{- if .Values.vault.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rb-new.fullname" . }}-vault-agent
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: vault-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- include "rb-new.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: vault-agent
  template:
    metadata:
      labels:
        {{- include "rb-new.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: vault-agent
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-inject-status: "update"
        vault.hashicorp.com/role: {{ .Values.vault.role | quote }}
        vault.hashicorp.com/agent-inject-secret-config: {{ .Values.vault.secretPath | quote }}
        vault.hashicorp.com/agent-inject-template-config: |
          {{`{{- with secret "`}}{{ .Values.vault.secretPath }}{{`" -}}
          export DB_PASSWORD="{{ .Data.data.db_password }}"
          export API_KEY="{{ .Data.data.api_key }}"
          export JWT_SECRET="{{ .Data.data.jwt_secret }}"
          {{- end }}`}}
    spec:
      serviceAccountName: {{ include "rb-new.serviceAccountName" . }}
      containers:
        - name: vault-agent
          image: "{{ .Values.vault.image.repository }}:{{ .Values.vault.image.tag }}"
          imagePullPolicy: {{ .Values.vault.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - |
              while true; do
                if [ -f /vault/secrets/config ]; then
                  echo "Secrets updated at $(date)"
                  # Ici, vous pourriez déclencher une action comme recharger les configurations
                fi
                sleep 60
              done
          resources:
            {{- toYaml .Values.vault.resources | nindent 12 }}
          volumeMounts:
            - name: vault-secrets
              mountPath: /vault/secrets
      volumes:
        - name: vault-secrets
          emptyDir:
            medium: Memory
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "rb-new.fullname" . }}-vault
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
  annotations:
    vault.hashicorp.com/agent-inject: "true"
{{- end }}
