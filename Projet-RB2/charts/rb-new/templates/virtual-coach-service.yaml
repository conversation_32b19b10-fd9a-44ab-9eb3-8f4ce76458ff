{{- if .Values.aiEngine.virtualCoach.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rb-new.virtualCoach.fullname" . }}
  labels:
    {{- include "rb-new.labels" . | nindent 4 }}
    app.kubernetes.io/component: virtual-coach
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.aiEngine.virtualCoach.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rb-new.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: virtual-coach
{{- end }}
