# Production environment values for rb-new
global:
  environment: production
  imageRegistry: ""
  imagePullPolicy: Always

# Increase resource requirements for production
orchestrator:
  replicaCount: 3
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

# Configure AI Engine services for production
aiEngine:
  recommender:
    replicaCount: 3
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
  
  chatbot:
    replicaCount: 3
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
  
  contentGenerator:
    replicaCount: 2
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
  
  analyticsEngine:
    replicaCount: 2
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi

# Configure production ingress with TLS
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: ai.retreatandbe.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: rb-new-tls
      hosts:
        - ai.retreatandbe.com

# Increase persistence sizes for production
persistence:
  models:
    size: 50Gi
  logs:
    size: 20Gi

# Environment variables for production
env:
  common:
    - name: ENVIRONMENT
      value: "production"
    - name: LOG_LEVEL
      value: "INFO"
