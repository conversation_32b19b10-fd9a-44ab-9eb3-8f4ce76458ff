# Default values for rb-new
replicaCount: 1

# Global settings
global:
  environment: development
  imageRegistry: ""
  imagePullPolicy: IfNotPresent

# Image settings for the main orchestrator
orchestrator:
  image:
    repository: rb-new-orchestrator
    tag: latest
    pullPolicy: IfNotPresent
  service:
    type: ClusterIP
    port: 8000
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 200m
      memory: 256Mi
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
    # Advanced autoscaling behavior
    scaleDownStabilizationWindowSeconds: 300
    scaleDownPercentage: 10
    scaleDownPeriodSeconds: 60
    scaleUpStabilizationWindowSeconds: 0
    scaleUpPercentage: 100
    scaleUpPeriodSeconds: 15
    scaleUpPods: 4
    # Custom metrics for scaling
    customMetrics: []

# AI Engine microservices
aiEngine:
  recommender:
    enabled: true
    image:
      repository: rb-new-recommender
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 8000
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi
    autoscaling:
      enabled: false
      minReplicas: 1
      maxReplicas: 3
      targetCPUUtilizationPercentage: 80
      targetMemoryUtilizationPercentage: 80

  chatbot:
    enabled: true
    image:
      repository: rb-new-chatbot
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 8001
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi
    autoscaling:
      enabled: false
      minReplicas: 1
      maxReplicas: 3
      targetCPUUtilizationPercentage: 80
      targetMemoryUtilizationPercentage: 80

  contentGenerator:
    enabled: true
    image:
      repository: rb-new-content-generator
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 8002
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi

  analyticsEngine:
    enabled: true
    image:
      repository: rb-new-analytics-engine
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 8003
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi

  featureFlags:
    enabled: true
    image:
      repository: rb-new-feature-flags
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 8004
    resources:
      limits:
        cpu: 300m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

  virtualCoach:
    enabled: true
    image:
      repository: rb-new-virtual-coach
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 5005
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi

# MLflow settings
mlflow:
  enabled: true
  image:
    repository: ghcr.io/mlflow/mlflow
    tag: latest
    pullPolicy: IfNotPresent
  service:
    port: 5000
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 200m
      memory: 256Mi
  persistence:
    enabled: true
    size: 10Gi

# Database settings
postgresql:
  enabled: true
  auth:
    username: retreatandbe
    password: ""  # Will be auto-generated if empty
    database: retreatandbe
  primary:
    persistence:
      size: 10Gi

# Redis settings
redis:
  enabled: true
  auth:
    enabled: true
    password: ""  # Will be auto-generated if empty
  master:
    persistence:
      enabled: true
      size: 8Gi

# Ingress settings
ingress:
  enabled: false
  className: "nginx"
  annotations: {}
  hosts:
    - host: rb-new.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Persistence settings for models and logs
persistence:
  models:
    enabled: true
    size: 10Gi
  logs:
    enabled: true
    size: 5Gi

# Environment variables
env:
  # Common environment variables
  common:
    - name: ENVIRONMENT
      value: "development"

  # Orchestrator-specific environment variables
  orchestrator:
    - name: API_HOST
      value: "0.0.0.0"
    - name: API_PORT
      value: "8000"
    - name: API_DEBUG
      value: "True"

  # Database connection environment variables
  database:
    - name: DB_HOST
      value: "{{ .Release.Name }}-postgresql"
    - name: DB_PORT
      value: "5432"
    - name: DB_NAME
      value: "retreatandbe"
    - name: DB_USER
      valueFrom:
        secretKeyRef:
          name: "{{ .Release.Name }}-postgresql"
          key: username
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          name: "{{ .Release.Name }}-postgresql"
          key: password

  # Redis connection environment variables
  redis:
    - name: REDIS_HOST
      value: "{{ .Release.Name }}-redis-master"
    - name: REDIS_PORT
      value: "6379"
    - name: REDIS_PASSWORD
      valueFrom:
        secretKeyRef:
          name: "{{ .Release.Name }}-redis"
          key: redis-password

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 2000
  # Paramètres de sécurité avancés
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  seccompProfile:
    type: RuntimeDefault
  seLinuxOptions:
    level: "s0:c123,c456"

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 2000

# Pod Security Policy
podSecurityPolicy:
  enabled: false
  readOnlyRootFilesystem: true

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# RBAC configuration
rbac:
  create: true
  allowConfigUpdate: false
  allowSecretUpdate: false
  additionalRules: []

# OPA/Gatekeeper configuration
opa:
  enabled: false
  enforcementAction: dryrun  # dryrun ou deny

# Falco configuration
falco:
  enabled: false
  alertOutput: stdout  # stdout, syslog, file, program, http
  alertPriority: WARNING  # EMERGENCY, ALERT, CRITICAL, ERROR, WARNING, NOTICE, INFO, DEBUG

# Vault configuration
vault:
  enabled: false
  role: "rb-new"
  secretPath: "secret/data/rb-new"
  image:
    repository: hashicorp/vault
    tag: "1.12.1"
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Trivy configuration
trivy:
  enabled: false
  schedule: "0 2 * * *"  # Tous les jours à 2h du matin
  severity: "CRITICAL,HIGH"  # UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL
  exitCode: 0  # 0 pour ne pas échouer le job, 1 pour échouer en cas de vulnérabilités
  image:
    repository: aquasec/trivy
    tag: "latest"
    pullPolicy: Always
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 200m
      memory: 256Mi

# Rate Limiter configuration
rateLimiter:
  enabled: false
  type: "nginx"  # nginx ou kong
  ingressClass: "nginx"
  host: "api.retreatandbe.com"
  requestsPerSecond: "10"
  requestsPerMinute: "300"
  requestsPerHour: "10000"
  connections: "20"
  burstMultiplier: "5"
  statusCode: "429"
  whitelist: "127.0.0.1/32,10.0.0.0/8"

# ModSecurity configuration
modsecurity:
  enabled: false
  ruleEngine: "On"  # On, Off, DetectionOnly
  auditEngine: "RelevantOnly"  # On, Off, RelevantOnly
  debugLevel: 3  # 0-9, 9 étant le plus verbeux
  whitelist:
    - "/api/health"
    - "/api/metrics"
    - "/api/status"

# Pod annotations
podAnnotations: {}

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Monitoring configuration
monitoring:
  enabled: false
  serviceMonitor:
    additionalLabels:
      release: prometheus
    interval: 30s
    scrapeTimeout: 10s

# Network policy configuration
networkPolicy:
  enabled: false
  allowIngressFromNamespace: true
  ingressNamespace: ingress-nginx
  allowExternalEgress: true
  allowFrontendAccess: true
  frontendNamespace: "default"

# API Gateway configuration
apiGateway:
  enabled: false
  type: "kong"  # ou "ambassador", "nginx"
  ingressClass: "kong"
  host: "api.retreatandbe.com"
  authService: "auth-service.auth.svc.cluster.local:3000"

# Authentication configuration
auth:
  enabled: false
  provider: "keycloak"  # ou "auth0", "okta", etc.
  realm: "retreatandbe"
  clientId: "rb-new-backend"
  clientSecret: ""  # Laissez vide pour générer automatiquement
  jwtSecret: ""      # Laissez vide pour générer automatiquement
  serverUrl: "https://auth.retreatandbe.com/auth"
  redirectUri: "https://app.retreatandbe.com/callback"
  keycloak:
    publicKey: ""
  auth0:
    domain: ""
    audience: ""
  middleware:
    enabled: false
    replicaCount: 2
    image:
      repository: rb-new-auth-middleware
      tag: latest
      pullPolicy: IfNotPresent
    service:
      port: 9000
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

# Pod Disruption Budget configuration
podDisruptionBudget:
  enabled: false
  # Only one of minAvailable or maxUnavailable can be specified
  minAvailable: 1
  # maxUnavailable: 1

# Secrets configuration
# Note: For production, use a secrets management solution like Vault or Sealed Secrets
secrets:
  # API keys (these should be provided via a secure method, not in values files)
  openaiApiKey: ""
  huggingfaceApiKey: ""
  # Custom secrets
  custom: {}

# Resource Quota configuration
resourceQuota:
  enabled: false
  hard:
    # Compute resources
    limits.cpu: "16"
    limits.memory: "32Gi"
    requests.cpu: "8"
    requests.memory: "16Gi"
    # Storage resources
    requests.storage: "100Gi"
    # Object count limits
    pods: "30"
    services: "20"
    configmaps: "30"
    secrets: "30"
    persistentvolumeclaims: "20"

# Application configuration
config:
  # Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  logLevel: "INFO"

  # AI model configuration
  models:
    recommender: "recommender_v1"
    content: "content_generator_v1"
    chatbot: "chatbot_v1"
    analytics: "analytics_v1"
    optimizer: "optimizer_v1"

  # Feature flags
  features:
    recommender: true
    chatbot: true
    contentGenerator: true
    analytics: true

  # Service timeouts in seconds
  timeouts:
    recommender: 30
    chatbot: 60
    contentGenerator: 120
    analyticsEngine: 60
    featureFlags: 10
    virtualCoach: 60
    mlflow: 30

  # Kafka configuration
  kafka:
    bootstrapServers: "kafka:9092"
    topicPrefix: "retreatandbe"

# Istio configuration
istio:
  enabled: false
  hosts:
    - "rb-new.local"
    - "rb-new.retreatandbe.svc.cluster.local"
  gateways:
    - "istio-system/retreatandbe-gateway"
  mtls:
    enabled: true

# Metrics exporter configuration
metrics:
  enabled: false
  image:
    repository: prom/statsd-exporter
    tag: latest
    pullPolicy: IfNotPresent
  port: 9102
  scrapeInterval: "15s"
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Database migrations configuration
migrations:
  enabled: false
  image:
    repository: rb-new-migrations
    tag: latest
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  persistence:
    enabled: false
    size: 1Gi

# Backup configuration
backup:
  enabled: false
  schedule: "0 1 * * *"  # Daily at 1 AM
  image:
    repository: postgres
    tag: "13-alpine"
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  persistence:
    enabled: true
    size: 10Gi
  retentionDays: 7
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  uploadToCloud: false
