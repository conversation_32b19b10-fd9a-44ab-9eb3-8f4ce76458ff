{{/*
Shared helpers for all charts
*/}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "shared.fullname" -}}
{{- if .Values.fullnameOverride -}}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- $name := default .Chart.Name .Values.nameOverride -}}
{{- if contains $name .Release.Name -}}
{{- .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- else -}}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "shared.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "shared.labels" -}}
 helm.sh/chart: {{ include "shared.chart" . }}
{{ include "shared.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "shared.selectorLabels" -}}
app.kubernetes.io/name: {{ include "shared.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "shared.serviceAccountName" -}}
{{- if .Values.serviceAccount.create -}}
    {{ default (include "shared.fullname" .) .Values.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{/*
Define common annotations
*/}}
{{- define "shared.annotations" -}}
kubernetes.io/ingress-class: {{ .Values.ingress.class | default "nginx" }}
app.kubernetes.io/part-of: {{ .Values.app.partOf | default .Release.Name }}
app.kubernetes.io/component: {{ .Values.app.component | default "service" }}
{{- end -}}

{{/*
Define common environment variables
*/}}
{{- define "shared.env" -}}
- name: NODE_ENV
  value: {{ .Values.environment | default "production" | quote }}
- name: APP_NAME
  value: {{ include "shared.fullname" . | quote }}
{{- end -}}