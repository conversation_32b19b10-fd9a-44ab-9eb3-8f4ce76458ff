{{- define "common.secret" -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.fullname" . }}
  labels:
    {{- include "common.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if .Values.secret.data }}
  {{- range $key, $value := .Values.secret.data }}
  {{ $key }}: {{ $value | b64enc }}
  {{- end }}
  {{- end }}
  {{- if .Values.secret.stringData }}
  {{- range $key, $value := .Values.secret.stringData }}
  {{ $key }}: {{ $value | b64enc }}
  {{- end }}
  {{- end }}
{{- end -}}