apiVersion: apps/v1
kind: Deployment
metadata:
  name: vault-agent-injector
  annotations:
    vault.hashicorp.com/agent-inject: "true"
    vault.hashicorp.com/role: "project-final"
    vault.hashicorp.com/agent-pre-populate-only: "true"
spec:
  selector:
    matchLabels:
      app: vault-agent-injector
  template:
    metadata:
      labels:
        app: vault-agent-injector
    spec:
      serviceAccountName: vault-auth
      containers:
      - name: vault-agent
        image: vault:1.12.0
        args: ["agent", "-config=/vault/config/agent.hcl"]
        volumeMounts:
        - name: vault-config
          mountPath: /vault/config
        - name: vault-token
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        securityContext:
          runAsUser: 100
          runAsGroup: 1000
      volumes:
      - name: vault-config
        configMap:
          name: vault-agent-config
      - name: vault-token
        projected:
          sources:
          - serviceAccountToken:
              path: token
              expirationSeconds: 7200
              audience: vault