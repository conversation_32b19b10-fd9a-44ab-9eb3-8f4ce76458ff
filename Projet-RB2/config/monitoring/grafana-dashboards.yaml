dashboards:
  main:
    name: "Vue d'ensemble système"
    panels:
      - title: "Santé des Services"
        type: status-history
        metrics:
          - expr: up{job=~"backend|analyzer|storage"}
            legend: "{{job}}"
      
      - title: "Latence API"
        type: graph
        metrics:
          - expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))
            legend: "{{service}} p95"

      - title: "Utilisation Ressources"
        type: gauge
        metrics:
          - expr: sum(rate(container_cpu_usage_seconds_total[5m])) by (container)
            legend: "CPU {{container}}"
          - expr: sum(container_memory_usage_bytes) by (container)
            legend: "Mémoire {{container}}"

alerts:
  rules:
    - alert: ServiceDown
      expr: up == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        description: "Le service {{ $labels.job }} est arrêté"

    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
      for: 2m
      labels:
        severity: warning
      annotations:
        description: "Taux d'erreur élevé sur {{ $labels.service }}"