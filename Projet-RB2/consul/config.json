{"datacenter": "dc1", "data_dir": "/consul/data", "log_level": "INFO", "node_name": "consul-server", "server": true, "bootstrap_expect": 1, "client_addr": "0.0.0.0", "ui_config": {"enabled": true}, "telemetry": {"prometheus_retention_time": "60s", "disable_hostname": true}, "connect": {"enabled": true, "enable_mesh_gateway_wan_federation": true}, "ports": {"grpc": 8502, "http": 8500, "https": -1, "dns": 8600, "serf_lan": 8301, "serf_wan": 8302, "server": 8300}, "acl": {"enabled": true, "default_policy": "deny", "enable_token_persistence": true, "tokens": {"master": "your-master-token-here", "agent": "your-agent-token-here"}}, "performance": {"raft_multiplier": 1}, "retry_join": ["consul-server"], "service": {"name": "consul", "tags": ["server"], "check": {"id": "consul-server-health", "name": "Consul Server Health", "tcp": "localhost:8500", "interval": "10s", "timeout": "1s"}}}