describe('Button Component E2E Tests', () => {
  beforeEach(() => {
    // Visiter la page Storybook du bouton
    cy.visit('http://localhost:6006/?path=/story/atoms-button--primary');
  });

  it('should be visible and clickable in the Primary variant', () => {
    // Vérifier que le bouton est visible dans le canvas Storybook
    cy.get('button').contains('Bouton Primaire').should('be.visible');
    
    // Vérifier que le bouton est cliquable
    cy.get('button').contains('Bouton Primaire').click();
  });

  it('should navigate to Secondary variant and check its appearance', () => {
    // Naviguer vers la variante secondaire
    cy.visit('http://localhost:6006/?path=/story/atoms-button--secondary');
    
    // Vérifier que le bouton secondaire est visible
    cy.get('button').contains('Bouton Secondaire').should('be.visible');
    
    // Vérifier son apparence (classe CSS)
    cy.get('button').contains('Bouton Secondaire')
      .should('have.class', 'bg-white')
      .should('have.class', 'text-indigo-800');
  });

  it('should check disabled state and verify it cannot be clicked', () => {
    // Naviguer vers la variante désactivée
    cy.visit('http://localhost:6006/?path=/story/atoms-button--disabled');
    
    // Vérifier que le bouton est visible et désactivé
    cy.get('button').contains('Bouton Désactivé')
      .should('be.visible')
      .should('be.disabled');
    
    // On pourrait aussi vérifier l'absence d'action lors du clic,
    // mais ce n'est pas nécessaire car Cypress ne génère pas d'événements
    // de clic sur les éléments désactivés
  });

  it('should check different button sizes', () => {
    // Vérifier le petit bouton
    cy.visit('http://localhost:6006/?path=/story/atoms-button--small');
    cy.get('button').contains('Petit')
      .should('be.visible')
      .should('have.class', 'py-1')
      .should('have.class', 'px-4')
      .should('have.class', 'text-sm');
    
    // Vérifier le grand bouton
    cy.visit('http://localhost:6006/?path=/story/atoms-button--large');
    cy.get('button').contains('Grand')
      .should('be.visible')
      .should('have.class', 'py-4')
      .should('have.class', 'px-12')
      .should('have.class', 'text-lg');
  });

  it('should change appearance with dark mode toggle', () => {
    // Aller à la variante principale
    cy.visit('http://localhost:6006/?path=/story/atoms-button--primary');
    
    // Activer le mode sombre via le bouton dans l'interface Storybook
    cy.get('#tabbutton-backgrounds').click();
    cy.contains('dark').click();
    
    // Vérifier que le bouton est toujours visible après le changement de thème
    cy.get('button').contains('Bouton Primaire').should('be.visible');
  });
}); 