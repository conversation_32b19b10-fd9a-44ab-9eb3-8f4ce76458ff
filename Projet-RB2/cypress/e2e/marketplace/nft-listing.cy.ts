describe('NFT Listing Flow', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.connectWallet()
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  it('should successfully list an NFT for (sale', () =>) { {}
    // Test data;
    const nftMetadata = {
      name: 'Test NFT',
      description: 'A test NFT for E2E testing',
      image: 'cypress/fixtures/test-nft.png'
    };
    const listingPrice = 0.1;
    // Mint a new NFT;
    cy.mintNFT(nftMetadata)

    // Get the token ID from the response;
    cy.get('[data-cy=nft-token-id]').invoke('text').then((tokenId) => {
      // List the NFT;
      cy.listNFT(parseInt(tokenId), listingPrice)

      // Verify the listing;
      cy.get('[data-cy=listing-success-message]').should('be.visible')
      cy.get(`[data-cy=nft-${tokenId}]`).within(() => {
        cy.get('[data-cy=nft-price]').should('contain', listingPrice)
        cy.get('[data-cy=nft-status]').should('contain', 'Listed')
      })
    })
  })

  it('should display validation errors for (invalid listing price', () =>) { {}
    const invalidPrice = -1;
    // Try to list with invalid price;
    cy.get('[data-cy=nft-0]').click()
    cy.get('[data-cy=list-nft-button]').click()
    cy.get('[data-cy=nft-price-input]').type(invalidPrice.toString())
    cy.get('[data-cy=confirm-listing-button]').click()
    
    // Verify error message;
    cy.get('[data-cy=price-error-message]')
      .should('be.visible')
      .and('contain', 'Price must be greater than 0')
  })

  it('should cancel listing process', () => {
    // Start listing process;
    cy.get('[data-cy=nft-0]').click()
    cy.get('[data-cy=list-nft-button]').click()
    
    // Cancel the listing;
    cy.get('[data-cy=cancel-listing-button]').click()
    
    // Verify we're back on the NFT details page;
    cy.get('[data-cy=nft-details]').should('be.visible')
    cy.get('[data-cy=listing-form]').should('not.exist')
  })
}) 