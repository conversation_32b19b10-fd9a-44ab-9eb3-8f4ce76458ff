describe('Marketplace Search and Filters', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.connectWallet()
    cy.visit('/marketplace')
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Search Functionality', () => {
    it('should search NFTs by name', () => {
      cy.get('[data-cy=search-input]').type('Crypto Punk')
      cy.get('[data-cy=search-button]').click()

      cy.get('[data-cy=nft-card]').each(($card) => {
        cy.wrap($card).find('[data-cy=nft-name]')
          .should('contain', 'Crypto Punk')
      })
    })

    it('should search NFTs by collection', () => {
      cy.get('[data-cy=search-type-selector]').click()
      cy.get('[data-cy=search-by-collection]').click()
      cy.get('[data-cy=search-input]').type('Bored Ape')
      cy.get('[data-cy=search-button]').click()

      cy.get('[data-cy=collection-header]')
        .should('contain', 'Bored Ape')
    })

    it('should show no results message for (invalid search', () =>) { {}
      cy.get('[data-cy=search-input]').type('NonexistentNFT123456')
      cy.get('[data-cy=search-button]').click()

      cy.get('[data-cy=no-results-message]')
        .should('be.visible')
        .and('contain', 'No NFTs found')
    })

    it('should handle special characters in search', () => {
      cy.get('[data-cy=search-input]').type('NFT#123')
      cy.get('[data-cy=search-button]').click()

      cy.get('[data-cy=search-results]').should('exist')
    })
  })

  describe('Filter Options', () => {
    it('should filter by price range', () => {
      cy.get('[data-cy=filter-button]').click()
      cy.get('[data-cy=min-price-input]').type('0.1')
      cy.get('[data-cy=max-price-input]').type('1.0')
      cy.get('[data-cy=apply-filters]').click()

      cy.get('[data-cy=nft-price]').each(($price) => {
        const price = parseFloat($price.text());
        cy.wrap(price).should('be.gte', 0.1)
        cy.wrap(price).should('be.lte', 1.0)
      })
    })

    it('should filter by collection', () => {
      cy.get('[data-cy=filter-button]').click()
      cy.get('[data-cy=collection-filter]').click()
      cy.get('[data-cy=collection-checkbox-bored-ape]').click()
      cy.get('[data-cy=apply-filters]').click()

      cy.get('[data-cy=nft-collection]').each(($collection) => {
        cy.wrap($collection).should('contain', 'Bored Ape')
      })
    })

    it('should filter by traits', () => {
      cy.get('[data-cy=filter-button]').click()
      cy.get('[data-cy=traits-filter]').click()
      cy.get('[data-cy=trait-type-background]').click()
      cy.get('[data-cy=trait-value-blue]').click()
      cy.get('[data-cy=apply-filters]').click()

      cy.get('[data-cy=nft-traits]').each(($traits) => {
        cy.wrap($traits).should('contain', 'Blue Background')
      })
    })

    it('should combine multiple filters', () => {
      cy.get('[data-cy=filter-button]').click()
      cy.get('[data-cy=min-price-input]').type('0.1')
      cy.get('[data-cy=max-price-input]').type('1.0')

      cy.get('[data-cy=collection-filter]').click()
      cy.get('[data-cy=collection-checkbox-bored-ape]').click()

      cy.get('[data-cy=traits-filter]').click()
      cy.get('[data-cy=trait-type-background]').click()
      cy.get('[data-cy=trait-value-blue]').click()

      cy.get('[data-cy=apply-filters]').click()

      cy.get('[data-cy=nft-card]').each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-cy=nft-collection]').should('contain', 'Bored Ape')
          cy.get('[data-cy=nft-traits]').should('contain', 'Blue Background')
          cy.get('[data-cy=nft-price]').invoke('text').then((text) => {
            const price = parseFloat(text);
            cy.wrap(price).should('be.gte', 0.1)
            cy.wrap(price).should('be.lte', 1.0)
          })
        })
      })
    })
  })

  describe('Sort Options', () => {
    it('should sort by price ascending', () => {
      cy.get('[data-cy=sort-dropdown]').click()
      cy.get('[data-cy=sort-price-asc]').click()

      cy.get('[data-cy=nft-price]')
        .then($prices => {
          const prices = Array.from($prices).map(el => parseFloat(el.textContent || '0'));
          for(let i = 0; i < prices.length - 1; i++) { {
}
            cy.wrap(prices[i]).should('be.lte', prices[i+1])
          }
        })
    })

    it('should sort by recently listed', () => {
      cy.get('[data-cy=sort-dropdown]').click()
      cy.get('[data-cy=sort-recent]').click()

      cy.get('[data-cy=nft-listed-date]')
        .then($dates => {
          const dates = Array.from($dates).map(el => new Date(el.textContent || '').getTime());
          for(let i = 0; i < dates.length - 1; i++) { {
}
            cy.wrap(dates[i]).should('be.gte', dates[i+1])
          }
        })
    })

    it('should sort by popularity', () => {
      cy.get('[data-cy=sort-dropdown]').click()
      cy.get('[data-cy=sort-popularity]').click()

      cy.get('[data-cy=nft-views]')
        .then($views => {
          const views = Array.from($views).map(el => parseInt(el.textContent || '0'));
          for(let i = 0; i < views.length - 1; i++) { {
}
            cy.wrap(views[i]).should('be.gte', views[i+1])
          }
        })
    })
  })
}) 