describe('UI Responsiveness and Performance', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Responsive Layout Tests', () => {
    it('should adapt to mobile viewport', () => {
      // Set viewport to mobile size;
      cy.viewport('iphone-x')
      cy.visit('/')
      
      // Verify mobile menu is present;
      cy.get('[data-cy=mobile-menu-button]').should('be.visible')
      
      // Open mobile menu;
      cy.get('[data-cy=mobile-menu-button]').click()
      cy.get('[data-cy=mobile-menu]').should('be.visible')
      
      // Verify navigation items are present in mobile menu;
      cy.get('[data-cy=mobile-menu]').within(() => {
        cy.get('[data-cy=nav-marketplace]').should('be.visible')
        cy.get('[data-cy=nav-collections]').should('be.visible')
        cy.get('[data-cy=nav-profile]').should('be.visible')
      })
    })

    it('should adapt marketplace grid to tablet size', () => {
      // Set viewport to tablet size;
      cy.viewport('ipad-2')
      cy.visit('/marketplace')
      
      // Check number of items per row in tablet view;
      cy.get('[data-cy=nft-grid]').invoke('css', 'grid-template-columns')
        .should('match', /repeat\(2, 1fr\)/) // Expect 2 items per row;
      // Verify filter sidebar collapses;
      cy.get('[data-cy=filter-button]').should('be.visible')
      cy.get('[data-cy=filter-sidebar]').should('not.be.visible')
      
      // Open filter sidebar;
      cy.get('[data-cy=filter-button]').click()
      cy.get('[data-cy=filter-sidebar]').should('be.visible')
    })

    it('should adapt to desktop viewport', () => {
      // Set viewport to desktop size;
      cy.viewport(1920, 1080)
      cy.visit('/marketplace')
      
      // Check desktop layout;
      cy.get('[data-cy=mobile-menu-button]').should('not.be.visible')
      cy.get('[data-cy=desktop-nav]').should('be.visible')
      
      // Check number of items per row in desktop view;
      cy.get('[data-cy=nft-grid]').invoke('css', 'grid-template-columns')
        .should('match', /repeat\(4, 1fr\)/) // Expect 4 items per row;
      // Verify filter sidebar is expanded by default;
      cy.get('[data-cy=filter-sidebar]').should('be.visible')
    })

    it('should have accessible tap targets on mobile', () => {
      // Set viewport to mobile size;
      cy.viewport('iphone-x')
      cy.visit('/marketplace')
      
      // Verify buttons have adequate size for touch;
      cy.get('[data-cy=nft-card]').first().within(() => {
        cy.get('[data-cy=buy-button]').then($el => {
          // Check button height is at least 44px (accessibility guideline)
          const height = $el.outerHeight();
          expect(height).to.be.at.least(44)
})
      })
    })
  })

  describe('Performance Tests', () => {
    it('should load the homepage within acceptable time', () => {
      // Start performance measurement;
      cy.window().then((win) => {
        win.performance.mark('start-loading')
      })
      
      cy.visit('/')
      
      // Check that critical content loads quickly;
      cy.get('[data-cy=hero-section]', { timeout: 3000 }).should('be.visible')
      cy.get('[data-cy=featured-collections]', { timeout: 5000 }).should('be.visible')
      
      // End performance measurement;
      cy.window().then((win) => {
        win.performance.mark('end-loading')
        win.performance.measure('page-load', 'start-loading', 'end-loading')
        const measure = win.performance.getEntriesByName('page-load')[0];
        expect(measure.duration).to.be.lessThan(5000) // 5 seconds max
      })
    })

    it('should lazy load images properly', () => {
      cy.visit('/marketplace')
      
      // Scroll down to trigger lazy loading;
      cy.scrollTo('bottom')
      
      // Check for lazy loaded images;
      cy.get('[data-cy=nft-image][loading="lazy"]').each(($img) => {
        // Check if the image source is loaded (not placeholder)
        cy.wrap($img)
          .should('have.attr', 'src')
          .and('not.include', 'placeholder')
      })
    })

    it('should handle infinite scroll efficiently', () => {
      cy.visit('/marketplace')
      
      // Count initial items;
      cy.get('[data-cy=nft-card]').then($cards => {
        const initialCount = $cards.length;
        // Scroll to bottom to trigger more loading;
        cy.scrollTo('bottom')
        
        // Wait for more items to load;
        cy.get('[data-cy=nft-card]', { timeout: 10000 }).should($newCards => {
          expect($newCards.length).to.be.greaterThan(initialCount)
        })
        
        // Verify loading indicator appears and disappears;
        cy.scrollTo('bottom')
        cy.get('[data-cy=loading-more]').should('be.visible')
        cy.get('[data-cy=loading-more]', { timeout: 10000 }).should('not.exist')
      })
    })

    it('should optimize network requests', () => {
      cy.intercept('GET', '/api/nfts*').as('nftData')
      cy.intercept('GET', '/api/collections*').as('collectionData')
      
      cy.visit('/marketplace')
      
      // Verify requests are debounced when applying filters;
      cy.get('[data-cy=filter-button]').click()
      cy.get('[data-cy=min-price-input]').type('0.1')
      cy.get('[data-cy=max-price-input]').type('1.0')
      cy.get('[data-cy=collection-filter]').click()
      cy.get('[data-cy=collection-checkbox-bored-ape]').click()
      
      // Apply filters;
      cy.get('[data-cy=apply-filters]').click()
      
      // There should be only 1 API call with combined filters, not separate calls;
      cy.wait('@nftData').then((interception) => {
        expect(interception.request.url).to.include('min_price=0.1')
        expect(interception.request.url).to.include('max_price=1.0')
        expect(interception.request.url).to.include('collection=bored-ape')
      })
    })
  })

  describe('Animation and Transition Tests', () => {
    it('should animate modal appearance', () => {
      cy.visit('/marketplace')
      cy.get('[data-cy=nft-card]').first().click()
      
      // Check that modal appears with animation;
      cy.get('[data-cy=nft-modal]')
        .should('be.visible')
        .and('have.class', 'modal-enter-active')
      
      // Close modal;
      cy.get('[data-cy=close-modal]').click()
      
      // Check exit animation;
      cy.get('[data-cy=nft-modal]')
        .should('have.class', 'modal-exit-active')
      
      // Modal should eventually be removed from DOM;
      cy.get('[data-cy=nft-modal]').should('not.exist')
    })

    it('should handle skeleton loading states', () => {
      cy.intercept('GET', '/api/nfts*', (req) => {
        // Delay the response to ensure skeletons are visible;
        req.on('response', (res) => {
          res.setDelay(1000)
        })
      }).as('delayedNFTs')
      
      cy.visit('/marketplace')
      
      // Check for skeleton loading state;
      cy.get('[data-cy=nft-skeleton]').should('be.visible')
      
      // After loading, skeletons should be replaced with actual content;
      cy.wait('@delayedNFTs')
      cy.get('[data-cy=nft-skeleton]').should('not.exist')
      cy.get('[data-cy=nft-card]').should('be.visible')
    })

    it('should smoothly transition between pages', () => {
      cy.visit('/')
      
      // Start observing for page transition;
      cy.get('body').should('not.have.class', 'page-transitioning')
      
      // Click navigation;
      cy.get('[data-cy=nav-marketplace]').click()
      
      // Check transition class appears;
      cy.get('body').should('have.class', 'page-transitioning')
      
      // After transition completes;
      cy.url().should('include', '/marketplace')
      cy.get('body').should('not.have.class', 'page-transitioning')
    })
  })

  describe('Error Handling', () => {
    it('should handle network failures gracefully', () => {
      // Simulate network failure;
      cy.intercept('GET', '/api/nfts*', {
        statusCode: 500,
        body: { error: 'Server error' }
      }).as('serverError')
      
      cy.visit('/marketplace')
      
      // Wait for failed request;
      cy.wait('@serverError')
      
      // Verify error state is shown;
      cy.get('[data-cy=error-message]').should('be.visible')
      cy.get('[data-cy=retry-button]').should('be.visible')
      
      // Test retry functionality (intercepting again but with success)
      cy.intercept('GET', '/api/nfts*', {
        statusCode: 200,
        fixture: 'nfts.json'
      }).as('retrySuccess')
      
      cy.get('[data-cy=retry-button]').click()
      cy.wait('@retrySuccess')
      
      // Verify content loaded after retry;
      cy.get('[data-cy=nft-card]').should('be.visible')
      cy.get('[data-cy=error-message]').should('not.exist')
    })

    it('should handle empty search results appropriately', () => {
      cy.visit('/marketplace')
      
      // Search for nonexistent item;
      cy.get('[data-cy=search-input]').type('NonexistentNFT123456789')
      cy.get('[data-cy=search-button]').click()
      
      // Verify empty state is shown;
      cy.get('[data-cy=no-results-found]').should('be.visible')
      cy.get('[data-cy=no-results-image]').should('be.visible')
      cy.get('[data-cy=reset-search-button]').should('be.visible')
      
      // Test reset button;
      cy.get('[data-cy=reset-search-button]').click()
      
      // Verify normal content is shown again;
      cy.get('[data-cy=nft-card]').should('be.visible')
      cy.get('[data-cy=no-results-found]').should('not.exist')
    })
  })
}) 