import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  FormHelperText
} from '@mui/material';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import BlockIcon from '@mui/icons-material/Block';

// Types pour le système de crédits
enum CreditStatus {
  ACTIVE = 'ACTIVE',
  USED = 'USED',
  EXPIRED = 'EXPIRED'
}

interface Credit {
  id: string;
  amount: number;
  currency: string;
  description: string;
  status: CreditStatus;
  expiresAt: Date;
  usedAt?: Date;
  createdAt: Date;
}

interface CreditManagerProps {
  partnerId: string;
  subscriptionId: string;
}

const CreditManager: React.FC<CreditManagerProps> = ({ partnerId, subscriptionId }) => {
  const [credits, setCredits] = useState<Credit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [useCreditDialogOpen, setUseCreditDialogOpen] = useState(false);
  const [selectedCreditId, setSelectedCreditId] = useState<string | null>(null);
  const [applyToSubscription, setApplyToSubscription] = useState(true);
  const [extendMonths, setExtendMonths] = useState(1);

  // Simuler le chargement des crédits
  useEffect(() => {
    const fetchCredits = async () => {
      setLoading(true);
      try {
        // Simuler un délai de chargement
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Données de démonstration
        const mockCredits: Credit[] = [
          {
            id: '1',
            amount: 99,
            currency: 'EUR',
            description: 'Remboursement sous forme de crédit - Problème technique',
            status: CreditStatus.ACTIVE,
            expiresAt: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 180 jours
            createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
          },
          {
            id: '2',
            amount: 50,
            currency: 'EUR',
            description: 'Crédit de bienvenue',
            status: CreditStatus.USED,
            expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
            usedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
          }
        ];
        
        setCredits(mockCredits);
      } catch (error) {
        console.error('Error fetching credits:', error);
        setError('Erreur lors du chargement des crédits');
      } finally {
        setLoading(false);
      }
    };

    fetchCredits();
  }, [partnerId]);

  const handleOpenUseCreditDialog = (creditId: string) => {
    setSelectedCreditId(creditId);
    setUseCreditDialogOpen(true);
  };

  const handleCloseUseCreditDialog = () => {
    setUseCreditDialogOpen(false);
    setSelectedCreditId(null);
    setApplyToSubscription(true);
    setExtendMonths(1);
  };

  const handleUseCredit = async () => {
    if (!selectedCreditId) return;
    
    setLoading(true);
    try {
      // Simuler un délai de traitement
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simuler l'utilisation du crédit
      setCredits(credits.map(credit => 
        credit.id === selectedCreditId 
          ? { 
              ...credit, 
              status: CreditStatus.USED, 
              usedAt: new Date() 
            } 
          : credit
      ));
      
      setSuccessMessage('Crédit utilisé avec succès');
      handleCloseUseCreditDialog();
      
      // Effacer le message après 3 secondes
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error using credit:', error);
      setError('Erreur lors de l\'utilisation du crédit');
      
      // Effacer le message après 3 secondes
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const getStatusChip = (status: CreditStatus) => {
    switch (status) {
      case CreditStatus.ACTIVE:
        return (
          <Chip 
            icon={<CheckCircleIcon />} 
            label="Actif" 
            color="success" 
            size="small" 
          />
        );
      case CreditStatus.USED:
        return (
          <Chip 
            icon={<HourglassEmptyIcon />} 
            label="Utilisé" 
            color="primary" 
            size="small" 
          />
        );
      case CreditStatus.EXPIRED:
        return (
          <Chip 
            icon={<BlockIcon />} 
            label="Expiré" 
            color="default" 
            size="small" 
          />
        );
    }
  };

  const getActiveCredits = () => {
    return credits.filter(credit => credit.status === CreditStatus.ACTIVE);
  };

  const getUsedCredits = () => {
    return credits.filter(credit => credit.status === CreditStatus.USED);
  };

  const getTotalActiveCredit = () => {
    return getActiveCredits().reduce((total, credit) => total + credit.amount, 0);
  };

  if (loading && !credits.length) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Gestion des crédits
      </Typography>
      
      {successMessage && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {successMessage}
        </Alert>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Paper 
            variant="outlined" 
            sx={{ 
              p: 2, 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              height: '100%'
            }}
          >
            <AccountBalanceWalletIcon 
              color="primary" 
              sx={{ fontSize: 48, mb: 2 }} 
            />
            <Typography variant="h5" gutterBottom>
              {getTotalActiveCredit()} €
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              Crédit disponible
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center">
              Vous pouvez utiliser ce crédit pour vos abonnements ou d'autres services.
            </Typography>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Crédits actifs
            </Typography>
            
            {getActiveCredits().length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                Vous n'avez pas de crédits actifs.
              </Typography>
            ) : (
              <List>
                {getActiveCredits().map((credit) => (
                  <Paper key={credit.id} variant="outlined" sx={{ mb: 2, p: 2 }}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={8}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <MoneyOffIcon color="primary" sx={{ mr: 1 }} />
                          <Typography variant="subtitle2">
                            {credit.amount} {credit.currency}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {credit.description}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <CalendarTodayIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="caption" color="text.secondary">
                            Expire le {credit.expiresAt.toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={4} sx={{ textAlign: 'right' }}>
                        <Box sx={{ mb: 1 }}>
                          {getStatusChip(credit.status)}
                        </Box>
                        <Button 
                          variant="outlined" 
                          size="small"
                          onClick={() => handleOpenUseCreditDialog(credit.id)}
                        >
                          Utiliser
                        </Button>
                      </Grid>
                    </Grid>
                  </Paper>
                ))}
              </List>
            )}
          </Box>
          
          <Divider sx={{ my: 3 }} />
          
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Historique des crédits utilisés
            </Typography>
            
            {getUsedCredits().length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                Vous n'avez pas encore utilisé de crédits.
              </Typography>
            ) : (
              <List>
                {getUsedCredits().map((credit) => (
                  <ListItem 
                    key={credit.id}
                    secondaryAction={getStatusChip(credit.status)}
                    sx={{ 
                      bgcolor: 'background.paper', 
                      mb: 1, 
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}
                  >
                    <ListItemText
                      primary={`${credit.amount} ${credit.currency} - ${credit.description}`}
                      secondary={`Utilisé le ${credit.usedAt?.toLocaleDateString()}`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Box>
        </Grid>
      </Grid>
      
      {/* Dialog pour utiliser un crédit */}
      <Dialog open={useCreditDialogOpen} onClose={handleCloseUseCreditDialog}>
        <DialogTitle>Utiliser un crédit</DialogTitle>
        <DialogContent>
          {selectedCreditId && (
            <>
              <Typography variant="body1" paragraph>
                Vous êtes sur le point d'utiliser un crédit de {credits.find(c => c.id === selectedCreditId)?.amount} {credits.find(c => c.id === selectedCreditId)?.currency}.
              </Typography>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="apply-to-label">Appliquer à</InputLabel>
                <Select
                  labelId="apply-to-label"
                  value={applyToSubscription ? 'subscription' : 'other'}
                  label="Appliquer à"
                  onChange={(e) => setApplyToSubscription(e.target.value === 'subscription')}
                >
                  <MenuItem value="subscription">Abonnement actuel</MenuItem>
                  <MenuItem value="other">Autre service</MenuItem>
                </Select>
                <FormHelperText>
                  Choisissez où appliquer votre crédit
                </FormHelperText>
              </FormControl>
              
              {applyToSubscription && (
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel id="extend-months-label">Prolonger l'abonnement de</InputLabel>
                  <Select
                    labelId="extend-months-label"
                    value={extendMonths}
                    label="Prolonger l'abonnement de"
                    onChange={(e) => setExtendMonths(Number(e.target.value))}
                  >
                    <MenuItem value={1}>1 mois</MenuItem>
                    <MenuItem value={3}>3 mois</MenuItem>
                    <MenuItem value={6}>6 mois</MenuItem>
                    <MenuItem value={12}>12 mois</MenuItem>
                  </Select>
                  <FormHelperText>
                    Le crédit sera utilisé pour prolonger votre abonnement
                  </FormHelperText>
                </FormControl>
              )}
              
              {!applyToSubscription && (
                <TextField
                  label="Description"
                  fullWidth
                  margin="normal"
                  placeholder="Décrivez l'utilisation de ce crédit"
                />
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUseCreditDialog}>Annuler</Button>
          <Button 
            onClick={handleUseCredit} 
            variant="contained" 
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Confirmer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default CreditManager;
