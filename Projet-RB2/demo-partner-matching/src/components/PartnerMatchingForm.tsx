import React from 'react';
import {
  Grid,
  TextField,
  Typography,
  Box,
  ToggleButtonGroup,
  ToggleButton,
  InputAdornment,
  Paper,
  Alert
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { MatchingCriteria, PartnerTier } from '../types';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import GroupIcon from '@mui/icons-material/Group';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import HomeWorkIcon from '@mui/icons-material/HomeWork';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import FlightIcon from '@mui/icons-material/Flight';
import SpaIcon from '@mui/icons-material/Spa';

interface PartnerMatchingFormProps {
  criteria: MatchingCriteria;
  setCriteria: React.Dispatch<React.SetStateAction<MatchingCriteria>>;
  selectedTier: PartnerTier;
}

const PartnerMatchingForm: React.FC<PartnerMatchingFormProps> = ({
  criteria,
  setCriteria,
  selectedTier
}) => {
  // Fonctionnalités disponibles selon le niveau d'abonnement
  const tierFeatures = {
    PARTNER: {
      canFilterByLocation: true,
      canFilterByDate: false,
      canFilterByBudget: true,
      canFilterByType: true,
      maxParticipants: 20
    },
    CERTIFIED: {
      canFilterByLocation: true,
      canFilterByDate: true,
      canFilterByBudget: true,
      canFilterByType: true,
      maxParticipants: 50
    },
    PREMIUM: {
      canFilterByLocation: true,
      canFilterByDate: true,
      canFilterByBudget: true,
      canFilterByType: true,
      maxParticipants: 200
    }
  };

  const features = tierFeatures[selectedTier];

  const handleTypeChange = (
    event: React.MouseEvent<HTMLElement>,
    newType: 'venue' | 'catering' | 'travel' | 'wellness',
  ) => {
    if (newType !== null) {
      setCriteria({ ...criteria, type: newType });
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Définissez vos critères de recherche
      </Typography>

      {selectedTier === 'PARTNER' && (
        <Alert severity="info" sx={{ mb: 3 }}>
          Avec le niveau Partner standard, vous avez accès aux fonctionnalités de base de matching.
          Passez au niveau supérieur pour débloquer des filtres avancés et plus de résultats.
        </Alert>
      )}

      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Typography gutterBottom>Type de partenaire recherché</Typography>
          <ToggleButtonGroup
            value={criteria.type}
            exclusive
            onChange={handleTypeChange}
            aria-label="type de partenaire"
            fullWidth
            sx={{ mb: 2 }}
          >
            <ToggleButton value="venue" aria-label="venue">
              <HomeWorkIcon sx={{ mr: 1 }} />
              Lieu
            </ToggleButton>
            <ToggleButton value="catering" aria-label="catering">
              <RestaurantIcon sx={{ mr: 1 }} />
              Restauration
            </ToggleButton>
            <ToggleButton value="travel" aria-label="travel">
              <FlightIcon sx={{ mr: 1 }} />
              Transport
            </ToggleButton>
            <ToggleButton value="wellness" aria-label="wellness">
              <SpaIcon sx={{ mr: 1 }} />
              Bien-être
            </ToggleButton>
          </ToggleButtonGroup>
        </Grid>

        {features.canFilterByLocation && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Localisation"
              value={criteria.location}
              onChange={(e) => setCriteria({ ...criteria, location: e.target.value })}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationOnIcon />
                  </InputAdornment>
                ),
              }}
              helperText="Entrez une ville, un pays ou une région"
            />
          </Grid>
        )}

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Nombre de participants"
            type="number"
            value={criteria.participants}
            onChange={(e) => setCriteria({ ...criteria, participants: parseInt(e.target.value) || 0 })}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <GroupIcon />
                </InputAdornment>
              ),
              inputProps: { min: 1, max: features.maxParticipants }
            }}
            helperText={`Maximum: ${features.maxParticipants} participants`}
          />
        </Grid>

        {features.canFilterByBudget && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Budget maximum"
              type="number"
              value={criteria.budget}
              onChange={(e) => setCriteria({ ...criteria, budget: parseInt(e.target.value) || 0 })}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AttachMoneyIcon />
                  </InputAdornment>
                ),
                inputProps: { min: 0 }
              }}
              helperText="Budget par personne ou par service"
            />
          </Grid>
        )}

        {features.canFilterByDate && (
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <DatePicker
                label="Date de début"
                value={criteria.startDate}
                onChange={(date) => setCriteria({ ...criteria, startDate: date })}
                slotProps={{ textField: { fullWidth: true } }}
              />
              <DatePicker
                label="Date de fin"
                value={criteria.endDate}
                onChange={(date) => setCriteria({ ...criteria, endDate: date })}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Box>
          </Grid>
        )}
      </Grid>

      {selectedTier !== 'PREMIUM' && (
        <Paper sx={{ mt: 4, p: 2, bgcolor: '#fff8e1' }}>
          <Typography variant="subtitle2" color="warning.dark">
            Fonctionnalités limitées
          </Typography>
          <Typography variant="body2">
            {selectedTier === 'PARTNER'
              ? "Le niveau Partner standard limite vos résultats à 3 partenaires et n'inclut pas le filtrage par date."
              : "Le niveau Certified Partner limite vos résultats à 5 partenaires."}
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default PartnerMatchingForm;
