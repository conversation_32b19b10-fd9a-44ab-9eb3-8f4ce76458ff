import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { PartnerTierConfig, PartnerTier } from '../types';

interface PartnerTierComparisonProps {
  selectedTier: PartnerTier;
  onTierChange: (tier: PartnerTier) => void;
  tiers: Record<string, PartnerTierConfig>;
}

const PartnerTierComparison: React.FC<PartnerTierComparisonProps> = ({
  selectedTier,
  onTierChange,
  tiers
}) => {
  // Fonctionnalités supplémentaires pour la comparaison
  const tierFeatures = {
    PARTNER: {
      matchingResults: '3 résultats maximum',
      partnerSelection: '1 partenaire',
      contactAccess: 'Limité',
      advancedFilters: 'Non',
      directMessaging: 'Non',
      price: 'Gratuit'
    },
    CERTIFIED: {
      matchingResults: '5 résultats maximum',
      partnerSelection: '2 partenaires',
      contactAccess: 'Complet',
      advancedFilters: 'Partiel',
      directMessaging: 'Oui',
      price: '99€/mois'
    },
    PREMIUM: {
      matchingResults: 'Illimité',
      partnerSelection: '5 partenaires',
      contactAccess: 'Complet',
      advancedFilters: 'Complet',
      directMessaging: 'Prioritaire',
      price: '199€/mois'
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Choisissez votre niveau d'abonnement
      </Typography>

      <Grid container spacing={3}>
        {Object.entries(tiers).map(([key, tier]) => (
          <Grid item xs={12} md={4} key={key}>
            <Paper
              elevation={selectedTier === key ? 3 : 1}
              sx={{
                p: 3,
                height: '100%',
                border: selectedTier === key ? '2px solid #4caf50' : 'none',
                position: 'relative',
                transition: 'all 0.3s ease'
              }}
            >
              {selectedTier === key && (
                <Chip
                  label="Sélectionné"
                  color="success"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: 10,
                    right: 10
                  }}
                />
              )}

              <Typography variant="h5" component="h3" gutterBottom>
                {tier.name}
              </Typography>

              <Typography variant="h6" color="primary" gutterBottom>
                {tierFeatures[key as keyof typeof tierFeatures].price}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <List dense>
                <ListItem disablePadding sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`Matching: ${tierFeatures[key as keyof typeof tierFeatures].matchingResults}`}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    <CheckCircleIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={`Sélection: ${tierFeatures[key as keyof typeof tierFeatures].partnerSelection}`}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    {key === 'PARTNER' ? (
                      <RemoveCircleOutlineIcon color="action" fontSize="small" />
                    ) : (
                      <CheckCircleIcon color="success" fontSize="small" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={`Accès contacts: ${tierFeatures[key as keyof typeof tierFeatures].contactAccess}`}
                    sx={{ color: key === 'PARTNER' ? 'text.secondary' : 'inherit' }}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    {key === 'PARTNER' ? (
                      <RemoveCircleOutlineIcon color="action" fontSize="small" />
                    ) : (
                      <CheckCircleIcon color="success" fontSize="small" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={`Filtres avancés: ${tierFeatures[key as keyof typeof tierFeatures].advancedFilters}`}
                    sx={{ color: key === 'PARTNER' ? 'text.secondary' : 'inherit' }}
                  />
                </ListItem>

                <ListItem disablePadding sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    {key === 'PARTNER' ? (
                      <RemoveCircleOutlineIcon color="action" fontSize="small" />
                    ) : (
                      <CheckCircleIcon color="success" fontSize="small" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={`Messagerie: ${tierFeatures[key as keyof typeof tierFeatures].directMessaging}`}
                    sx={{ color: key === 'PARTNER' ? 'text.secondary' : 'inherit' }}
                  />
                </ListItem>
              </List>

              <Box sx={{ mt: 3 }}>
                <Button
                  variant={selectedTier === key ? "contained" : "outlined"}
                  color={selectedTier === key ? "success" : "primary"}
                  fullWidth
                  onClick={() => onTierChange(key as PartnerTier)}
                >
                  {selectedTier === key ? "Sélectionné" : "Sélectionner"}
                </Button>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Note: Dans une application réelle, le changement de niveau d'abonnement impliquerait un processus de paiement.
          Cette démonstration simule les différentes fonctionnalités disponibles à chaque niveau.
        </Typography>
      </Box>
    </Box>
  );
};

export default PartnerTierComparison;
