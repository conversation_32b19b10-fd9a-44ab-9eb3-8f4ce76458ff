import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  Grid,
  Tabs,
  Tab
} from '@mui/material';
import PartnerMatchingForm from '../components/PartnerMatchingForm';
import PartnerMatchingResults from '../components/PartnerMatchingResults';
import PartnerTierComparison from '../components/PartnerTierComparison';
import SubscriptionManager from '../components/SubscriptionManager';
import ReferralProgram from '../components/ReferralProgram';
import { MatchingCriteria, Partner, PartnerTier } from '../types';
import { partnerTiers, MOCK_PARTNERS } from '../data';

const steps = ['Définir vos critères', 'Consulter les correspondances', 'Contacter les partenaires'];

const PartnerMatchingExample: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [selectedTier, setSelectedTier] = useState<PartnerTier>('PARTNER');
  const [criteria, setCriteria] = useState<MatchingCriteria>({
    location: '',
    startDate: null,
    endDate: null,
    participants: 10,
    type: 'venue',
    budget: 2000
  });
  const [matches, setMatches] = useState<Partner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Fonction simulant la recherche de partenaires
  const findMatches = async (searchCriteria: MatchingCriteria) => {
    setIsLoading(true);

    // Simuler un délai de chargement
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Filtrer les partenaires selon les critères
    let filteredPartners = MOCK_PARTNERS.filter(partner => {
      // Filtre par type
      if (partner.type !== searchCriteria.type) return false;

      // Filtre par budget (si le prix minimum du partenaire est supérieur au budget)
      if (partner.priceRange[0] > searchCriteria.budget) return false;

      // Filtre par capacité
      if (partner.maxCapacity < searchCriteria.participants) return false;

      // Filtre par localisation (si spécifié)
      if (searchCriteria.location && !partner.location.toLowerCase().includes(searchCriteria.location.toLowerCase())) return false;

      return true;
    });

    // Limiter le nombre de résultats selon le niveau d'abonnement
    const maxResults = {
      partner: 3,
      certified: 5,
      premium: filteredPartners.length
    };

    filteredPartners = filteredPartners.slice(0, maxResults[selectedTier]);

    setMatches(filteredPartners);
    setIsLoading(false);
  };

  const handleNext = () => {
    if (activeStep === 0) {
      findMatches(criteria);
    }
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
    setMatches([]);
  };

  const handleTierChange = (tier: PartnerTier) => {
    setSelectedTier(tier);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 8 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Exemple de Matching de Partenaires pour Retraites
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Cet exemple montre comment les différents niveaux d'abonnement (Partner, Certified Partner, Premium Partner)
        affectent les fonctionnalités de matching disponibles pour la création de retraites.
      </Typography>

      <Box sx={{ my: 4 }}>
        <PartnerTierComparison
          selectedTier={selectedTier}
          onTierChange={handleTierChange}
          tiers={partnerTiers}
        />
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          aria-label="partner management tabs"
        >
          <Tab label="Matching de Partenaires" />
          <Tab label="Gestion de l'Abonnement" />
          <Tab label="Programme de Parrainage" />
        </Tabs>
      </Box>

      {activeTab === 0 ? (
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {activeStep === 0 && (
            <PartnerMatchingForm
              criteria={criteria}
              setCriteria={setCriteria}
              selectedTier={selectedTier}
            />
          )}

          {activeStep === 1 && (
            <PartnerMatchingResults
              matches={matches}
              isLoading={isLoading}
              selectedTier={selectedTier}
            />
          )}

          {activeStep === 2 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h5" gutterBottom>
                Contacter les partenaires sélectionnés
              </Typography>
              <Typography variant="body1" paragraph>
                Dans une application réelle, vous pourriez maintenant contacter les partenaires sélectionnés
                pour discuter de votre projet de retraite et commencer la planification.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Les fonctionnalités de messagerie et de planification seraient disponibles ici.
              </Typography>
            </Box>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Button
              color="inherit"
              disabled={activeStep === 0}
              onClick={handleBack}
              sx={{ mr: 1 }}
            >
              Retour
            </Button>
            <Box sx={{ flex: '1 1 auto' }} />
            {activeStep === steps.length - 1 ? (
              <Button onClick={handleReset}>Recommencer</Button>
            ) : (
              <Button onClick={handleNext} variant="contained" color="primary">
                {activeStep === 0 ? 'Rechercher des partenaires' : 'Suivant'}
              </Button>
            )}
          </Box>
        </Paper>
      ) : activeTab === 1 ? (
        <SubscriptionManager
          partnerId="partner_123"
          currentTier={selectedTier}
          onTierChange={handleTierChange}
        />
      ) : (
        <ReferralProgram
          partnerId="partner_123"
        />
      )}

      <Paper elevation={2} sx={{ p: 3, bgcolor: '#f9f9f9' }}>
        <Typography variant="h6" gutterBottom>
          Notes pour les développeurs
        </Typography>
        <Typography variant="body2">
          Ce composant démontre comment implémenter un système de matching de partenaires avec différents niveaux d'accès.
          Les principales fonctionnalités incluent:
        </Typography>
        <ul>
          <li>Filtrage par type de partenaire, localisation, budget et capacité</li>
          <li>Limitation du nombre de résultats selon le niveau d'abonnement</li>
          <li>Affichage des scores de compatibilité</li>
          <li>Interface utilisateur guidée par étapes</li>
        </ul>
      </Paper>
    </Container>
  );
};

export default PartnerMatchingExample;
