#!/bin/bash

# Couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Vérifier si le fichier de rapport existe
if [ ! -f "deployment-summary.md" ]; then
    echo -e "${RED}Erreur: Le fichier de rapport 'deployment-summary.md' n'existe pas.${NC}"
    exit 1
fi

# Fonction pour afficher le menu
show_menu() {
    clear
    echo -e "${BLUE}=====================================${NC}"
    echo -e "${BLUE}   RAPPORT DE DÉPLOIEMENT PROJET RB2 ${NC}"
    echo -e "${BLUE}=====================================${NC}"
    echo ""
    echo -e "${YELLOW}1.${NC} Afficher le rapport complet"
    echo -e "${YELLOW}2.${NC} Vérifier la santé des services"
    echo -e "${YELLOW}3.${NC} Afficher les problèmes connus"
    echo -e "${YELLOW}4.${NC} Afficher les URLs d'accès"
    echo -e "${YELLOW}5.${NC} Exécuter un port-forward pour accéder aux services"
    echo -e "${YELLOW}6.${NC} Quitter"
    echo ""
    echo -e "${BLUE}=====================================${NC}"
    echo -n "Entrez votre choix [1-6]: "
}

# Fonction pour afficher le rapport
show_report() {
    clear
    echo -e "${BLUE}Affichage du rapport complet...${NC}"
    echo ""
    # Utiliser less pour parcourir le rapport si disponible
    if command -v less &> /dev/null; then
        less -R deployment-summary.md
    else
        cat deployment-summary.md
    fi
    
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour vérifier la santé
check_health() {
    clear
    echo -e "${BLUE}Vérification de la santé des services...${NC}"
    echo ""
    if [ -f "health-check.sh" ]; then
        chmod +x health-check.sh
        ./health-check.sh
    else
        echo "Script de vérification de santé non trouvé."
    fi
    
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour afficher les problèmes
show_issues() {
    clear
    echo -e "${BLUE}Problèmes connus dans le déploiement...${NC}"
    echo ""
    
    # Extraire la section "Problèmes connus" du rapport
    sed -n '/^## Problèmes connus/,/^## /p' deployment-summary.md | sed '$d'
    
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour afficher les URLs
show_urls() {
    clear
    echo -e "${BLUE}URLs d'accès aux services...${NC}"
    echo ""
    
    # Extraire la section "URLs d'accès" du rapport
    sed -n '/^### URLs d/,/^$/p' deployment-summary.md
    
    read -p "Appuyez sur Entrée pour continuer..."
}

# Fonction pour le port-forward
do_port_forward() {
    clear
    echo -e "${BLUE}Port-forward vers les services...${NC}"
    echo ""
    echo -e "${YELLOW}1.${NC} Frontend (port 3000)"
    echo -e "${YELLOW}2.${NC} Backend API (port 5000)"
    echo -e "${YELLOW}3.${NC} Grafana (port 3000)"
    echo -e "${YELLOW}4.${NC} Prometheus (port 9090)"
    echo -e "${YELLOW}5.${NC} Keycloak (port 8080)"
    echo -e "${YELLOW}6.${NC} Retour au menu principal"
    echo ""
    echo -n "Choisissez un service [1-6]: "
    read choice
    
    case $choice in
        1) kubectl port-forward svc/frontend-frontend 3000:3000 -n retreat-and-be ;;
        2) kubectl port-forward svc/backend-backend 5000:5000 -n retreat-and-be ;;
        3) kubectl port-forward svc/grafana 3000:3000 -n retreat-and-be ;;
        4) kubectl port-forward svc/prometheus 9090:9090 -n retreat-and-be ;;
        5) kubectl port-forward svc/keycloak 8080:8080 -n retreat-and-be ;;
        6) return ;;
        *) echo "Choix invalide"; read -p "Appuyez sur Entrée pour continuer..." ;;
    esac
}

# Boucle principale
while true; do
    show_menu
    read choice
    
    case $choice in
        1) show_report ;;
        2) check_health ;;
        3) show_issues ;;
        4) show_urls ;;
        5) do_port_forward ;;
        6) clear; echo "Au revoir!"; exit 0 ;;
        *) echo "Choix invalide"; read -p "Appuyez sur Entrée pour continuer..." ;;
    esac
done
