#!/bin/bash

# Script de déploiement en environnement de staging

set -e

NAMESPACE="retreatandbe-staging"
CHART_DIR="../charts"
VALUES_FILE="staging-values.yaml"

# Création du namespace s'il n'existe pas
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Déploiement des services dans l'ordre
echo "Déploiement des services en environnement de staging..."

# Déployer les services de base d'abord
echo "Déploiement des services de base..."
helm upgrade --install database $CHART_DIR/database -f $VALUES_FILE -n $NAMESPACE
helm upgrade --install redis $CHART_DIR/redis -f $VALUES_FILE -n $NAMESPACE

# Attendre que les services de base soient prêts
echo "Attente de la disponibilité des services de base..."
kubectl wait --for=condition=available --timeout=300s deployment/database -n $NAMESPACE
kubectl wait --for=condition=available --timeout=300s deployment/redis -n $NAMESPACE

# Déployer les microservices
echo "Déploiement du Backend..."
helm upgrade --install backend $CHART_DIR/Backend -f $VALUES_FILE -n $NAMESPACE

echo "Déploiement du Decentralized-Storage..."
helm upgrade --install decentralized-storage $CHART_DIR/Decentralized-Storage -f $VALUES_FILE -n $NAMESPACE

echo "Déploiement de l'Analyzer..."
helm upgrade --install analyzer $CHART_DIR/Analyzer -f $VALUES_FILE -n $NAMESPACE

echo "Déploiement du Frontend..."
helm upgrade --install frontend $CHART_DIR/Frontend -f $VALUES_FILE -n $NAMESPACE

echo "Déploiement en staging terminé avec succès!"