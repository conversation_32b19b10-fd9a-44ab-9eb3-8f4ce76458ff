import http from 'k6/http';
import { sleep, check } from 'k6';

export const options = {
  stages: [
    { duration: '2m', target: 100 }, // Montée en charge
    { duration: '5m', target: 100 }, // Charge constante
    { duration: '2m', target: 200 }, // Pic de charge
    { duration: '5m', target: 200 }, // Maintien du pic
    { duration: '2m', target: 0 },   // Descente progressive
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% des requêtes doivent être sous 500ms
    http_req_failed: ['rate<0.01'],   // Moins de 1% d'erreurs
  },
};

export default function () {
  // Test du Frontend
  const frontendRes = http.get('https://staging.retreatandbe.com');
  check(frontendRes, {
    'frontend status is 200': (r) => r.status === 200,
    'frontend loads in under 1s': (r) => r.timings.duration < 1000,
  });

  // Test de l'API Backend
  const backendRes = http.get('https://api.staging.retreatandbe.com/health');
  check(backendRes, {
    'backend status is 200': (r) => r.status === 200,
    'backend responds in under 300ms': (r) => r.timings.duration < 300,
  });

  // Test de l'API Analyzer
  const analyzerRes = http.get('https://analyzer.staging.retreatandbe.com/health');
  check(analyzerRes, {
    'analyzer status is 200': (r) => r.status === 200,
    'analyzer responds in under 500ms': (r) => r.timings.duration < 500,
  });

  // Test du service Decentralized-Storage
  const storageRes = http.get('https://storage.staging.retreatandbe.com/health');
  check(storageRes, {
    'storage status is 200': (r) => r.status === 200,
    'storage responds in under 400ms': (r) => r.timings.duration < 400,
  });

  sleep(1);
}