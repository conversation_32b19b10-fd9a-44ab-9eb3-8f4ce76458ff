#!/bin/bash

# Script d'exécution des tests de performance

# Définir les variables
NAMESPACE="retreatandbe-staging"
RESULTS_DIR="./results/$(date +%Y-%m-%d_%H-%M-%S)"

# Créer le répertoire pour les résultats
mkdir -p $RESULTS_DIR

echo "Démarrage des tests de performance..."

# Test de charge avec k6
echo "Exécution des tests de charge..."
k6 run load-test.js --out json=$RESULTS_DIR/load-test-results.json

# Tests de latence réseau
echo "Test de latence réseau..."
for service in backend frontend analyzer decentralized-storage; do
  echo "Test de latence pour $service..."
  SERVICE_IP=$(kubectl get svc $service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  ping -c 100 $SERVICE_IP > $RESULTS_DIR/ping-$service.txt
done

# Tests de performance de base de données
echo "Test de performance de la base de données..."
kubectl exec -it $(kubectl get pods -n $NAMESPACE -l app=database -o jsonpath='{.items[0].metadata.name}') -n $NAMESPACE -- \
  pgbench -i -s 10 && \
  pgbench -c 10 -j 2 -t 1000 > $RESULTS_DIR/db-performance.txt

# Génération du rapport
echo "Génération du rapport de performance..."
echo "# Rapport de performance - $(date +%Y-%m-%d)" > $RESULTS_DIR/report.md
echo "## Résultats des tests de charge" >> $RESULTS_DIR/report.md
cat $RESULTS_DIR/load-test-results.json | jq -r '.metrics' >> $RESULTS_DIR/report.md

echo "## Résultats de latence réseau" >> $RESULTS_DIR/report.md
for service in backend frontend analyzer decentralized-storage; do
  echo "### $service" >> $RESULTS_DIR/report.md
  cat $RESULTS_DIR/ping-$service.txt | grep 'rtt' >> $RESULTS_DIR/report.md
done

echo "## Performance de la base de données" >> $RESULTS_DIR/report.md
cat $RESULTS_DIR/db-performance.txt >> $RESULTS_DIR/report.md

echo "Tests de performance terminés. Rapport disponible dans $RESULTS_DIR/report.md"