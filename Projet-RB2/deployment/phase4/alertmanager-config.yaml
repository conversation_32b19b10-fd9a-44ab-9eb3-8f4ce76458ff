# Configuration d'AlertManager pour les notifications
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: 'https://hooks.slack.com/services/XXXXXXXXX/XXXXXXXXX/XXXXXXXXXXXXXXXXXXXXXXXX'
      
    route:
      receiver: 'slack-notifications'
      group_by: ['alertname', 'severity']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 4h
      
      routes:
      - match:
          severity: critical
        receiver: 'pagerduty-critical'
        continue: true
        
    receivers:
    - name: 'slack-notifications'
      slack_configs:
      - channel: '#alerts'