# Configuration du monitoring pour la production
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: retreatandbe-services
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/part-of: retreatandbe
  endpoints:
  - port: http
    path: /metrics
    interval: 15s
    scrapeTimeout: 10s
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: retreatandbe-alerts
  namespace: monitoring
spec:
  groups:
  - name: retreatandbe.rules
    rules:
    - alert: HighErrorRate
      expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "High error rate detected"
        description: "Error rate is above 5% for 5 minutes (current value: {{ $value }})"
        
    - alert: HighLatency
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High latency detected"
        description: "95th percentile of request duration is above 1s for 5 minutes (current value: {{ $value }}s)"
        
    - alert: HighCPUUsage
      expr: sum(rate(container_cpu_usage_seconds_total{namespace="retreatandbe-production"}[5m])) by (pod) / sum(kube_pod_container_resource_limits_cpu_cores{namespace="retreatandbe-production"}) by (pod) > 0.8
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage detected"
        description: "Pod {{ $labels.pod }} is using more than 80% of its CPU limit for 15 minutes"
        
    - alert: HighMemoryUsage
      expr: sum(container_memory_usage_bytes{namespace="retreatandbe-production"}) by (pod) / sum(kube_pod_container_resource_limits_memory_bytes{namespace="retreatandbe-production"}) by (pod) > 0.8
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage detected"
        description: "Pod {{ $labels.pod }} is using more than 80% of its memory limit for 15 minutes"