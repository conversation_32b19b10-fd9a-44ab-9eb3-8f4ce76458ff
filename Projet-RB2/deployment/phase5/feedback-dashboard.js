// Dashboard pour l'analyse des retours utilisateurs
import React, { useState, useEffect } from 'react';
import { Line, Bar, Pie } from 'react-chartjs-2';
import { Table, Tag, Button, Modal, Input, DatePicker } from 'antd';
import axios from 'axios';

const FeedbackDashboard = () => {
  const [feedbackData, setFeedbackData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState([null, null]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  const [response, setResponse] = useState('');

  useEffect(() => {
    fetchFeedbackData();
  }, [dateRange]);

  const fetchFeedbackData = async () => {
    setLoading(true);
    try {
      const [startDate, endDate] = dateRange;
      const params = {};
      if (startDate) params.startDate = startDate.format('YYYY-MM-DD');
      if (endDate) params.endDate = endDate.format('YYYY-MM-DD');
      
      const response = await axios.get('/api/feedback', { params });
      setFeedbackData(response.data);
    } catch (error) {
      console.error('Error fetching feedback data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleResponseSubmit = async () => {
    try {
      await axios.post(`/api/feedback/${selectedFeedback.id}/respond`, { response });
      setModalVisible(false);
      setResponse('');
      fetchFeedbackData();
    } catch (error) {
      console.error('Error submitting response:', error);
    }
  };

  const sentimentDistribution = {
    labels: ['Positif', 'Neutre', 'Négatif'],
    datasets: [
      {
        data: [
          feedbackData.filter(item => item.sentiment === 'positive').length,
          feedbackData.filter(item => item.sentiment === 'neutral').length,
          feedbackData.filter(item => item.sentiment === 'negative').length,
        ],
        backgroundColor: ['#52c41a', '#1890ff', '#f5222d'],
      },
    ],
  };

  const feedbackOverTime = {
    labels: [...new Set(feedbackData.map(item => item.date))].sort(),
    datasets: [
      {
        label: 'Nombre de retours',
        data: [...new Set(feedbackData.map(item => item.date))].sort()
          .map(date => feedbackData.filter(item => item.date === date).length),
        fill: false,
        borderColor: '#1890ff',
      },
    ],
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: 'Utilisateur',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: 'Catégorie',
      dataIndex: 'category',
      key: 'category',
      render: category => <Tag color="blue">{category}</Tag>,
    },
    {
      title: 'Sentiment',
      dataIndex: 'sentiment',
      key: 'sentiment',
      render: sentiment => {
        let color = 'blue';
        if (sentiment === 'positive') color = 'green';
        if (sentiment === 'negative') color = 'red';
        return <Tag color={color}>{sentiment}</Tag>;
      },
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button 
          type="primary" 
          onClick={() => {
            setSelectedFeedback(record);
            setModalVisible(true);
          }}
        >
          Répondre
        </Button>
      ),
    },
  ];

  return (
    <div className="feedback-dashboard">
      <h1>Tableau de bord des retours utilisateurs</h1>
      
      <div className="filters">
        <DatePicker.RangePicker 
          value={dateRange}
          onChange={setDateRange}
        />
        <Button type="primary" onClick={fetchFeedbackData}>Actualiser</Button>
      </div>
      
      <div className="charts">
        <div className="chart-container">
          <h2>Distribution des sentiments</h2>
          <Pie data={sentimentDistribution} />
        </div>
        
        <div className="chart-container">
          <h2>Évolution des retours dans le temps</h2>
          <Line data={feedbackOverTime} />
        </div>
      </div>
      
      <div className="feedback-table">
        <h2>Liste des retours</h2>
        <Table 
          columns={columns} 
          dataSource={feedbackData} 
          loading={loading}
          rowKey="id"
        />
      </div>
      
      <Modal
        title="Répondre au retour"
        visible={modalVisible}
        onOk={handleResponseSubmit}
        onCancel={() => setModalVisible(false)}
      >
        {selectedFeedback && (
          <div>
            <p><strong>Utilisateur:</strong> {selectedFeedback.user}</p>
            <p><strong>Message:</strong> {selectedFeedback.message}</p>
            <Input.TextArea
              rows={4}
              value={response}
              onChange={e => setResponse(e.target.value)}
              placeholder="Votre réponse..."
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FeedbackDashboard;