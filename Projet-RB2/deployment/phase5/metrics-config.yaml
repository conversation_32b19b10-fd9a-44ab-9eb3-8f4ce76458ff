metrics:
  security:
    - name: vulnerability_scan
      type: counter
      labels:
        - severity
        - component
    - name: compliance_check
      type: gauge
      labels:
        - standard
        - requirement
    - name: security_incident
      type: counter
      labels:
        - type
        - severity

  performance:
    - name: response_time
      type: histogram
      buckets: [0.1, 0.5, 1, 2, 5]
      labels:
        - endpoint
        - method
    - name: error_rate
      type: gauge
      labels:
        - service
        - type

  resources:
    - name: cpu_usage
      type: gauge
      labels:
        - pod
        - namespace
    - name: memory_usage
      type: gauge
      labels:
        - pod
        - namespace

alerts:
  - name: HighVulnerabilityDetected
    condition: vulnerability_scan{severity="critical"} > 0
    duration: 5m
    severity: critical
    
  - name: ComplianceCheckFailed
    condition: compliance_check < 1
    duration: 1h
    severity: warning
    
  - name: HighErrorRate
    condition: error_rate > 0.05
    duration: 10m
    severity: warning