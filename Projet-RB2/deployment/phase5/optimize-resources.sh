#!/bin/bash

# Configuration des seuils d'optimisation
MEMORY_THRESHOLD=85
CPU_THRESHOLD=75
SCALE_FACTOR=1.2

# Fonction d'optimisation améliorée
optimize_resources() {
  local namespace=$1
  
  # Analyse des métriques historiques
  analyze_historical_metrics "$namespace"
  
  # Génération des recommandations
  generate_optimization_recommendations "$namespace"
  
  # Application automatique des optimisations validées
  apply_validated_optimizations "$namespace"
}

# Script d'optimisation des ressources basé sur l'utilisation réelle

set -e

NAMESPACE="retreatandbe-production"
OBSERVATION_PERIOD="7d"  # Période d'observation pour les métriques

echo "Analyse de l'utilisation des ressources sur $OBSERVATION_PERIOD..."

# Obtenir les métriques d'utilisation CPU
echo "Analyse de l'utilisation CPU..."
kubectl get --raw "/apis/metrics.k8s.io/v1beta1/namespaces/$NAMESPACE/pods" | jq '.items[] | {name: .metadata.name, cpu: .containers[0].usage.cpu}' > cpu_usage.json

# Obtenir les métriques d'utilisation mémoire
echo "Analyse de l'utilisation mémoire..."
kubectl get --raw "/apis/metrics.k8s.io/v1beta1/namespaces/$NAMESPACE/pods" | jq '.items[] | {name: .metadata.name, memory: .containers[0].usage.memory}' > memory_usage.json

# Obtenir les limites actuelles
echo "Récupération des limites actuelles..."
kubectl get pods -n $NAMESPACE -o json | jq '.items[] | {name: .metadata.name, limits: .spec.containers[0].resources.limits}' > current_limits.json

# Analyser et générer des recommandations
echo "Génération des recommandations d'optimisation..."
python3 - <<EOF
import json
import re

# Charger les données
with open('cpu_usage.json') as f:
    cpu_data = [json.loads(line) for line in f]

with open('memory_usage.json') as f:
    memory_data = [json.loads(line) for line in f]

with open('current_limits.json') as f:
    limits_data = [json.loads(line) for line in f]

# Convertir les valeurs CPU
def parse_cpu(cpu_str):
    if cpu_str.endswith('n'):
        return int(cpu_str[:-1]) / 1000000
    elif cpu_str.endswith('m'):
        return int(cpu_str[:-1]) / 1000
    else:
        return float(cpu_str)

# Convertir les valeurs mémoire
def parse_memory(mem_str):
    if mem_str.endswith('Ki'):
        return int(mem_str[:-2]) * 1024
    elif mem_str.endswith('Mi'):
        return int(mem_str[:-2]) * 1024 * 1024
    elif mem_str.endswith('Gi'):
        return int(mem_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(re.sub(r'[^0-9]', '', mem_str))

# Générer des recommandations
recommendations = []
for limit in limits_data:
    pod_name = limit['name']
    
    # Trouver l'utilisation CPU
    cpu_usage = next((item['cpu'] for item in cpu_data if item['name'] == pod_name), None)
    if cpu_usage:
        cpu_usage_value = parse_cpu(cpu_usage)
        current_cpu_limit = parse_cpu(limit['limits'].get('cpu', '1000m'))
        
        # Recommander 2x l'utilisation actuelle
        recommended_cpu = max(cpu_usage_value * 2, 0.1)  # Au moins 100m
        
        if abs(recommended_cpu - current_cpu_limit) / current_cpu_limit > 0.2:  # Différence > 20%
            recommendations.append({
                'pod': pod_name,
                'resource': 'cpu',
                'current': f"{current_cpu_limit}",
                'recommended': f"{recommended_cpu}",
                'change': f"{(recommended_cpu - current_cpu_limit) / current_cpu_limit * 100:.1f}%"
            })
    
    # Trouver l'utilisation mémoire
    memory_usage = next((item['memory'] for item in memory_data if item['name'] == pod_name), None)
    if memory_usage:
        memory_usage_value = parse_memory(memory_usage)
        current_memory_limit = parse_memory(limit['limits'].get('memory', '512Mi'))
        
        # Recommander 1.5x l'utilisation actuelle
        recommended_memory = max(memory_usage_value * 1.5, 64 * 1024 * 1024)  # Au moins 64Mi
        
        if abs(recommended_memory - current_memory_limit) / current_memory_limit > 0.2:  # Différence > 20%
            recommendations.append({
                'pod': pod_name,
                'resource': 'memory',
                'current': f"{current_memory_limit / (1024 * 1024):.0f}Mi",
                'recommended': f"{recommended_memory / (1024 * 1024):.0f}Mi",
                'change': f"{(recommended_memory - current_memory_limit) / current_memory_limit * 100:.1f}%"
            })

# Afficher les recommandations
print("Recommandations d'optimisation des ressources:")
print("=============================================")
for rec in recommendations:
    print(f"Pod: {rec['pod']}")
    print(f"Resource: {rec['resource']}")
    print(f"Current: {rec['current']}")
    print(f"Recommended: {rec['recommended']} ({rec['change']})")
    print("---------------------------------------------")

# Générer les commandes pour appliquer les changements
print("\nCommandes pour appliquer les changements:")
print("=======================================")
for rec in recommendations:
    pod_base_name = rec['pod'].rsplit('-', 1)[0]
    print(f"kubectl set resources deployment {pod_base_name} -n {namespace} --limits={rec['resource']}={rec['recommended']}")
EOF

echo "Analyse terminée. Veuillez revoir les recommandations ci-dessus."