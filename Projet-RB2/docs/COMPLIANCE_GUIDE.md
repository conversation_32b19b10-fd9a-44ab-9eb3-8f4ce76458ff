# Guide de Conformité - Retreat And Be

Ce document détaille les exigences de conformité CCPA (California Consumer Privacy Act) et PCI DSS (Payment Card Industry Data Security Standard) pour la plateforme Retreat And Be, ainsi que les implémentations techniques et procédures à suivre pour assurer cette conformité.

## Table des matières

1. [Conformité CCPA](#1-conformité-ccpa)
   - [Résumé des exigences CCPA](#11-résumé-des-exigences-ccpa)
   - [Implémentation technique](#12-implémentation-technique)
   - [Procédures opérationnelles](#13-procédures-opérationnelles)
   - [Checklist de conformité](#14-checklist-de-conformité)

2. [Conformité PCI DSS](#2-conformité-pci-dss)
   - [Résumé des exigences PCI DSS](#21-résumé-des-exigences-pci-dss)
   - [Architecture de sécurité des paiements](#22-architecture-de-sécurité-des-paiements)
   - [Tokenisation des données de cartes](#23-tokenisation-des-données-de-cartes)
   - [Checklist de conformité](#24-checklist-de-conformité)
   
3. [Contrôles Communs](#3-contrôles-communs)
   - [Gestion des accès](#31-gestion-des-accès)
   - [Chiffrement des données](#32-chiffrement-des-données)
   - [Journalisation et monitoring](#33-journalisation-et-monitoring)

4. [Formation et documentation](#4-formation-et-documentation)
   - [Programmes de formation](#41-programmes-de-formation)
   - [Documentation pour les utilisateurs](#42-documentation-pour-les-utilisateurs)
   - [Procédures d'audit](#43-procédures-daudit)

5. [Plan de Mise en Conformité](#5-plan-de-mise-en-conformité)
   - [Étapes CCPA](#51-étapes-ccpa)
   - [Étapes PCI DSS](#52-étapes-pci-dss)
   - [Calendrier d'implémentation](#53-calendrier-dimplémentation)

## 1. Conformité CCPA

### 1.1 Résumé des exigences CCPA

Le California Consumer Privacy Act (CCPA) est entré en vigueur le 1er janvier 2020 et accorde aux résidents californiens de nouveaux droits concernant leurs informations personnelles. Les principales exigences sont :

1. **Droit à l'information** : Les consommateurs ont le droit de savoir quelles informations personnelles sont collectées, utilisées, partagées ou vendues.
2. **Droit à la suppression** : Les consommateurs ont le droit de demander la suppression de leurs informations personnelles.
3. **Droit de désinscription** : Les consommateurs ont le droit de refuser la vente de leurs informations personnelles.
4. **Droit à la non-discrimination** : Les entreprises ne peuvent pas discriminer les consommateurs qui exercent leurs droits CCPA.

### 1.2 Implémentation technique

#### 1.2.1 Collecte et cartographie des données

```typescript
// Exemple de modèle de données avec annotations de confidentialité
@Entity()
export class UserProfile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @Privacy({ type: 'PII', retention: '18m', purpose: 'account' })
  email: string;

  @Column()
  @Privacy({ type: 'PII', retention: '18m', purpose: 'account' })
  fullName: string;

  @Column({ nullable: true })
  @Privacy({ type: 'PII', retention: '18m', purpose: 'marketing', optOut: true })
  phoneNumber: string;

  @Column()
  @Privacy({ type: 'preference', retention: '18m', purpose: 'experience' })
  preferredLanguage: string;

  @Column({ type: 'json', nullable: true })
  @Privacy({ type: 'tracking', retention: '12m', purpose: 'analytics', optOut: true })
  activityHistory: object;
}
```

#### 1.2.2 API pour les droits des utilisateurs

```typescript
// API pour gérer les demandes CCPA
@Controller('privacy')
export class PrivacyController {
  constructor(private readonly privacyService: PrivacyService) {}

  @Get('data-export/:userId')
  @UseGuards(AuthGuard, PrivacyGuard)
  async exportUserData(@Param('userId') userId: string): Promise<UserDataExport> {
    return this.privacyService.generateUserDataExport(userId);
  }

  @Delete('data/:userId')
  @UseGuards(AuthGuard, PrivacyGuard)
  async deleteUserData(@Param('userId') userId: string): Promise<void> {
    return this.privacyService.deleteUserData(userId);
  }

  @Post('opt-out/:userId')
  @UseGuards(AuthGuard)
  async optOutDataSale(@Param('userId') userId: string): Promise<void> {
    return this.privacyService.optOutFromDataSale(userId);
  }

  @Get('data-categories')
  async getDataCategories(): Promise<DataCategoryInfo[]> {
    return this.privacyService.getDataCategoriesInfo();
  }
}
```

#### 1.2.3 Bannière de consentement et préférences

La plateforme inclut une bannière de consentement de cookies et un centre de préférences de confidentialité accessible depuis toutes les pages :

```javascript
// Configuration du centre de préférences de confidentialité
const privacyPreferencesConfig = {
  cookieCategories: [
    {
      id: 'essential',
      label: 'Essentiels',
      description: 'Nécessaires au fonctionnement du site',
      required: true
    },
    {
      id: 'functional',
      label: 'Fonctionnels',
      description: 'Permettent de personnaliser votre expérience',
      required: false
    },
    {
      id: 'analytics',
      label: 'Analytiques',
      description: 'Nous aident à comprendre comment vous utilisez notre site',
      required: false
    },
    {
      id: 'marketing',
      label: 'Marketing',
      description: 'Utilisés pour la publicité ciblée',
      required: false
    }
  ],
  dataProcessingPurposes: [
    {
      id: 'account',
      label: 'Gestion de compte',
      description: 'Nécessaire pour gérer votre compte et vos réservations',
      required: true
    },
    {
      id: 'experience',
      label: 'Personnalisation',
      description: 'Améliorer votre expérience sur la plateforme',
      required: false
    },
    {
      id: 'marketing',
      label: 'Marketing',
      description: 'Vous envoyer des offres personnalisées',
      required: false,
      defaultState: false
    },
    {
      id: 'sharing',
      label: 'Partage avec des partenaires',
      description: 'Partager vos données avec nos partenaires de confiance',
      required: false,
      defaultState: false
    }
  ],
  privacyPolicyUrl: '/legal/privacy-policy',
  ccpaSpecificRights: true
};
```

### 1.3 Procédures opérationnelles

#### 1.3.1 Traitement des demandes d'accès, de suppression et d'opt-out

1. **Réception des demandes** : Les demandes sont reçues via le portail utilisateur, l'email dédié `<EMAIL>`, ou le formulaire de contact.
2. **Vérification d'identité** : L'identité du demandeur est vérifiée via :
   - Connexion au compte
   - Vérification de l'email
   - Questions de sécurité si nécessaire
3. **Traitement de la demande** :
   - Demandes d'accès : Délai de 45 jours pour fournir les données
   - Demandes de suppression : Délai de 45 jours pour confirmer la suppression
   - Demandes d'opt-out : Traitement immédiat
4. **Réponse au demandeur** : Confirmation par email avec accusé de réception

#### 1.3.2 Registre des demandes

Toutes les demandes sont consignées dans un registre sécurisé, incluant :
- Date de la demande
- Type de demande (accès, suppression, opt-out)
- Identité du demandeur
- Date de traitement
- Statut de la demande

### 1.4 Checklist de conformité

- [x] Avis de collecte des informations personnelles
- [x] Politique de confidentialité mise à jour avec dispositions CCPA
- [x] Mécanisme "Do Not Sell My Personal Information"
- [x] Procédures de vérification d'identité
- [x] Système de gestion des demandes d'accès et de suppression
- [x] Formation du personnel
- [ ] Tests et audit de conformité
- [ ] Documentation à jour

## 2. Conformité PCI DSS

### 2.1 Résumé des exigences PCI DSS

Le PCI DSS (Payment Card Industry Data Security Standard) est un ensemble d'exigences de sécurité pour les entreprises qui traitent, stockent ou transmettent des données de cartes de crédit. Les 12 exigences principales sont :

1. Installer et maintenir une configuration de pare-feu pour protéger les données
2. Ne pas utiliser les mots de passe système et autres paramètres de sécurité par défaut
3. Protéger les données de titulaires de cartes stockées
4. Chiffrer la transmission des données de titulaires de cartes sur les réseaux publics
5. Utiliser et mettre à jour régulièrement un logiciel antivirus
6. Développer et maintenir des systèmes et des applications sécurisés
7. Restreindre l'accès aux données selon les besoins professionnels
8. Attribuer un identifiant unique à chaque personne ayant accès à l'infrastructure
9. Restreindre l'accès physique aux données des titulaires de cartes
10. Suivre et surveiller tous les accès aux ressources réseau et aux données
11. Tester régulièrement les systèmes et processus de sécurité
12. Maintenir une politique qui traite de la sécurité de l'information

### 2.2 Architecture de sécurité des paiements

La plateforme Retreat And Be utilise une architecture qui minimise l'exposition aux données de cartes de paiement :

```mermaid
graph TD
    Client[Client Browser] --> FE[Frontend SPA]
    FE -- Tokenized Card Data Only --> API[API Gateway]
    FE <--> PSP[Payment Service Provider]
    
    subgraph PCI DSS Scope
    PSP --> Vault[Token Vault]
    end
    
    API --> PaymentService[Payment Service]
    PaymentService -- Token Only --> PSP
    
    PSP -- Transaction Result --> PaymentService
    PaymentService --> DB[(Database)]
    
    style PCI DSS Scope fill:#f9f,stroke:#333,stroke-width:2px
```

#### 2.2.1 Intégration avec Stripe

```typescript
// Service de paiement qui ne touche jamais les données de cartes
@Injectable()
export class PaymentService {
  constructor(
    private readonly stripeService: StripeService,
    private readonly configService: ConfigService,
  ) {}

  async processPayment(paymentDto: PaymentDto): Promise<PaymentResponseDto> {
    try {
      // Utilisation du token Stripe, pas des données de carte
      const paymentIntent = await this.stripeService.createPaymentIntent({
        amount: paymentDto.amount,
        currency: paymentDto.currency.toLowerCase(),
        payment_method: paymentDto.paymentToken, // Token seulement
        confirm: true,
        description: `Booking: ${paymentDto.bookingId}`,
        metadata: {
          bookingId: paymentDto.bookingId,
          userId: paymentDto.userId,
        },
      });

      // Stocker uniquement les métadonnées de transaction, pas les données de carte
      const transactionRecord = await this.transactionRepository.save({
        bookingId: paymentDto.bookingId,
        userId: paymentDto.userId,
        amount: paymentDto.amount,
        currency: paymentDto.currency,
        transactionId: paymentIntent.id,
        status: this.mapStripeStatusToInternal(paymentIntent.status),
        paymentMethod: 'CARD',
        transactionDate: new Date(),
      });

      return {
        transactionId: paymentIntent.id,
        status: this.mapStripeStatusToInternal(paymentIntent.status),
        amount: paymentDto.amount,
        currency: paymentDto.currency,
        paymentMethod: 'CARD',
        transactionDate: new Date().toISOString(),
        bookingId: paymentDto.bookingId,
      };
    } catch (error) {
      this.logger.error(`Payment processing error: ${error.message}`, error.stack);
      throw new PaymentProcessingException(error.message);
    }
  }
}
```

### 2.3 Tokenisation des données de cartes

La plateforme n'accepte que des tokens de paiement et jamais des numéros de cartes directement :

```typescript
// DTO de paiement qui n'accepte que des tokens
export class PaymentDto {
  @IsString()
  @IsNotEmpty()
  bookingId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsNumber()
  @Min(1)
  amount: number;

  @IsString()
  @IsIn(['EUR', 'USD', 'GBP'])
  currency: string;

  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @IsString()
  @IsNotEmpty()
  paymentToken: string; // Token créé côté client, jamais de PAN
}
```

```javascript
// Code frontend pour la tokenisation côté client
async function handlePaymentSubmit(event) {
  event.preventDefault();
  
  const stripe = await loadStripe(STRIPE_PUBLIC_KEY);
  const elements = stripe.elements();
  const cardElement = elements.create('card');
  
  try {
    // Tokenisation côté client via Stripe.js
    const { token, error } = await stripe.createToken(cardElement);
    
    if (error) {
      throw new Error(error.message);
    }
    
    // Envoi du token au backend, jamais des données de carte
    const response = await api.post('/payments', {
      bookingId: booking.id,
      amount: booking.totalPrice,
      currency: booking.currency,
      paymentMethod: 'CARD',
      paymentToken: token.id
    });
    
    if (response.data.status === 'COMPLETED') {
      showConfirmation(response.data.transactionId);
    }
  } catch (err) {
    showError(err.message);
  }
}
```

### 2.4 Checklist de conformité

- [x] Segmentation réseau pour isoler l'environnement de paiement
- [x] Intégration avec des processeurs de paiement conformes PCI DSS (Stripe, PayPal)
- [x] Tokenisation des données de cartes
- [x] Chiffrement des communications via TLS 1.2+
- [x] Contrôles d'accès stricts pour les systèmes de paiement
- [x] Journalisation des accès et des transactions
- [ ] Scan trimestriel de vulnérabilités PCI
- [ ] Tests de pénétration annuels
- [ ] Questionnaire d'auto-évaluation (SAQ) complété
- [ ] Formation du personnel sur la sécurité des données de paiement

## 3. Contrôles Communs

### 3.1 Gestion des accès

La plateforme implémente un système d'accès basé sur le principe du moindre privilège :

```typescript
// Système de contrôle d'accès basé sur les rôles et permissions
@Injectable()
export class AccessControlService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  async hasPermission(userId: string, permission: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['roles', 'roles.permissions'],
    });

    if (!user) {
      return false;
    }

    // Vérifier dans les permissions directes de l'utilisateur
    if (user.permissions?.some(p => p.name === permission)) {
      return true;
    }

    // Vérifier dans les rôles de l'utilisateur
    return user.roles.some(role => 
      role.permissions.some(p => p.name === permission)
    );
  }

  async getUserPermissions(userId: string): Promise<string[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['permissions', 'roles', 'roles.permissions'],
    });

    if (!user) {
      return [];
    }

    const directPermissions = user.permissions.map(p => p.name);
    
    const rolePermissions = user.roles
      .flatMap(role => role.permissions)
      .map(p => p.name);

    // Éliminer les doublons
    return [...new Set([...directPermissions, ...rolePermissions])];
  }
}
```

### 3.2 Chiffrement des données

Toutes les données sensibles sont chiffrées au repos et en transit :

```typescript
// Configuration du chiffrement des données sensibles
@Injectable()
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key: Buffer;
  
  constructor(
    private readonly configService: ConfigService
  ) {
    // Dériver la clé à partir d'un secret stocké dans un coffre-fort de secrets
    const secret = this.configService.get<string>('ENCRYPTION_SECRET');
    this.key = createHash('sha256').update(secret).digest();
  }
  
  encrypt(text: string): EncryptedData {
    const iv = randomBytes(16);
    const cipher = createCipheriv(this.algorithm, this.key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }
  
  decrypt(data: EncryptedData): string {
    const decipher = createDecipheriv(
      this.algorithm,
      this.key,
      Buffer.from(data.iv, 'hex')
    );
    
    decipher.setAuthTag(Buffer.from(data.authTag, 'hex'));
    
    let decrypted = decipher.update(data.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### 3.3 Journalisation et monitoring

Un système complet de journalisation et de monitoring est implémenté pour détecter les accès non autorisés et les comportements suspects :

```typescript
// Service de journalisation sécurisée
@Injectable()
export class SecureLoggingService {
  constructor(
    private readonly logger: Logger,
    private readonly alertService: AlertService,
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
  ) {}

  async logAccess(
    userId: string,
    resource: string,
    action: string,
    success: boolean,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const log = this.auditLogRepository.create({
      userId,
      resource,
      action,
      success,
      ipAddress: this.getClientIp(),
      timestamp: new Date(),
      metadata: this.sanitizeMetadata(metadata),
    });

    await this.auditLogRepository.save(log);
    
    // Alerter sur les échecs d'authentification répétés
    if (!success && action === 'authentication') {
      const recentFailures = await this.getRecentAuthFailures(userId);
      if (recentFailures >= 5) {
        await this.alertService.sendSecurityAlert(
          'multiple_auth_failures',
          `Multiple authentication failures for user ${userId}`
        );
      }
    }
  }
  
  private sanitizeMetadata(metadata?: Record<string, any>): Record<string, any> {
    if (!metadata) {
      return {};
    }
    
    // Supprimer ou masquer les données sensibles dans les logs
    const sanitized = { ...metadata };
    const sensitiveFields = ['password', 'token', 'cardNumber', 'cvv', 'ssn'];
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '********';
      }
    }
    
    return sanitized;
  }
  
  private async getRecentAuthFailures(userId: string): Promise<number> {
    const timeThreshold = new Date();
    timeThreshold.setMinutes(timeThreshold.getMinutes() - 30);
    
    const count = await this.auditLogRepository.count({
      where: {
        userId,
        action: 'authentication',
        success: false,
        timestamp: MoreThan(timeThreshold),
      },
    });
    
    return count;
  }
  
  private getClientIp(): string {
    // Implémentation pour obtenir l'adresse IP du client
    // en tenant compte des proxys et équilibreurs de charge
    // ...
    return '0.0.0.0';
  }
}
```

## 4. Formation et documentation

### 4.1 Programmes de formation

Tous les employés doivent suivre une formation annuelle sur :

- **Sensibilisation à la sécurité des données**
  - Protection des données personnelles
  - Détection et signalement des incidents
  - Phishing et ingénierie sociale
  
- **Formation spécifique CCPA**
  - Identification des données personnelles
  - Traitement des demandes d'accès et de suppression
  - Documentation des processus

- **Formation spécifique PCI DSS**
  - Manipulation sécurisée des données de paiement
  - Détection des dispositifs de skimming
  - Signalement des incidents de sécurité

### 4.2 Documentation pour les utilisateurs

La plateforme fournit aux utilisateurs une documentation claire sur :

- Politique de confidentialité
- Conditions d'utilisation
- FAQ sur les droits CCPA
- Guide de sécurité des paiements
- Procédures de demande d'accès et de suppression des données

### 4.3 Procédures d'audit

Des audits réguliers sont effectués pour garantir la conformité continue :

- **Audits trimestriels**
  - Revue des contrôles d'accès
  - Scan de vulnérabilités
  - Test des procédures de demande CCPA

- **Audits annuels**
  - Test de pénétration complet
  - Revue complète de la politique de sécurité
  - Audit de conformité PCI DSS

## 5. Plan de Mise en Conformité

### 5.1 Étapes CCPA

1. **Phase 1 : Préparation (Semaine 1-2)**
   - Finaliser la cartographie des données
   - Mettre à jour la politique de confidentialité
   - Développer le portail de préférences de confidentialité

2. **Phase 2 : Implémentation (Semaine 3-4)**
   - Déployer l'API des droits utilisateurs
   - Intégrer le lien "Do Not Sell My Info"
   - Configurer le suivi des demandes

3. **Phase 3 : Validation (Semaine 5)**
   - Tests utilisateurs du portail de confidentialité
   - Formation du personnel au traitement des demandes
   - Audit interne de conformité

### 5.2 Étapes PCI DSS

1. **Phase 1 : Sécurisation (Semaine 1-2)**
   - Compléter la segmentation réseau
   - Finaliser l'intégration de la tokenisation
   - Mettre en place le chiffrement bout en bout

2. **Phase 2 : Contrôles et monitoring (Semaine 3-4)**
   - Déployer la journalisation avancée
   - Configurer les alertes de sécurité
   - Implémenter les contrôles d'accès granulaires

3. **Phase 3 : Certification (Semaine 5-6)**
   - Scan de vulnérabilités par un tiers qualifié
   - Compléter le questionnaire d'auto-évaluation (SAQ)
   - Remédier aux non-conformités identifiées

### 5.3 Calendrier d'implémentation

| Semaine | Actions CCPA | Actions PCI DSS |
|---------|--------------|----------------|
| 1 | Cartographie des données | Segmentation réseau |
| 2 | Mise à jour politique de confidentialité | Tokenisation & chiffrement |
| 3 | Développement API | Journalisation & alertes |
| 4 | Tests d'intégration | Configuration contrôles d'accès |
| 5 | Validation & formation | Scan de vulnérabilités |
| 6 | Audit final | Certification |

---

Document préparé par l'équipe Sécurité & Conformité de Retreat And Be.  
Dernière mise à jour : Octobre 2023 