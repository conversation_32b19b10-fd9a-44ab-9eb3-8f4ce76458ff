# Contributing Guide

Thank you for considering contributing to our project! This document provides guidelines and instructions for contributing.

## Code of Conduct

Please read and follow our [Code of Conduct](./CODE_OF_CONDUCT.md) to maintain a respectful and inclusive environment.

## Getting Started

1. Fork the repository
2. Clone your fork
3. Install dependencies
4. Create a feature branch

```bash
git clone https://github.com/your-username/project.git
cd project
npm install
git checkout -b feature/your-feature-name
```

## Development Process

### 1. Code Style

We use ESLint and Prettier for code formatting. Before committing, ensure your code follows our style guide:

```bash
npm run lint
npm run format
```

### 2. TypeScript

- Use TypeScript for all new code
- Define interfaces for props and state
- Avoid using `any` type
- Document complex types

Example:
```typescript
interface Props {
  /** Description of the title prop */
  title: string;
  /** Optional callback for click events */
  onClick?: () => void;
}

const Component: React.FC<Props> = ({ title, onClick }) => {
  // ...
};
```

### 3. Testing

All new features should include tests:

- Unit tests with Je<PERSON> and Testing Library
- Integration tests for complex features
- Accessibility tests with axe-core

```typescript
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

describe('Component', () => {
  it('should render correctly', () => {
    render(<Component />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### 4. Documentation

Document your code using JSDoc comments:

```typescript
/**
 * Component description
 * @param {Props} props - Component props
 * @returns {JSX.Element} Rendered component
 */
```

### 5. Accessibility

- Use semantic HTML
- Include ARIA attributes when needed
- Test with screen readers
- Follow WCAG guidelines

### 6. Performance

- Optimize bundle size
- Use React.memo for expensive components
- Implement code splitting
- Profile rendering performance

## Project Structure

### Frontend
```
frontend/
├── src/
│   ├── components/      # Reusable UI components
│   │   ├── common/      # Shared components
│   │   └── booking/     # Booking-specific components
│   ├── pages/          # Page components
│   ├── hooks/          # Custom React hooks
│   ├── services/       # API and other services
│   ├── store/          # Redux store and slices
│   ├── tests/          # Test files
│   └── utils/          # Utility functions
```

### Component Guidelines

1. **Lazy Loading**
- Use the `LazyImage` component for images
- Implement code splitting with React.lazy for routes
```typescript
const Component = React.lazy(() => import('./Component'));
```

2. **Performance**
- Use React Query for data fetching and caching
- Implement memoization where appropriate
- Follow lazy loading patterns

3. **Testing**
- Write unit tests for components
- Include integration tests for workflows
- Add accessibility tests
- Implement performance tests

4. **API Integration**
- Use the provided API service
- Handle errors appropriately
- Implement proper loading states

## Current Focus Areas

1. **Performance Optimization**
- Improve loading times
- Optimize bundle size
- Enhance caching strategies

2. **Testing Coverage**
- Increase unit test coverage
- Add more integration tests
- Expand E2E test scenarios

3. **Documentation**
- Document new components
- Update API documentation
- Maintain up-to-date examples

4. **Accessibility**
- Ensure WCAG compliance
- Test with screen readers
- Maintain keyboard navigation

## Pull Request Process

1. Update documentation
2. Add tests
3. Update changelog
4. Create PR with description
5. Request review
6. Address feedback

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
Describe testing done

## Screenshots
If applicable

## Checklist
- [ ] Tests added
- [ ] Documentation updated
- [ ] Changelog updated
- [ ] Linting passed
```

## Release Process

1. Update version in package.json
2. Update CHANGELOG.md
3. Create release PR
4. Deploy to staging
5. Test thoroughly
6. Deploy to production

## Versioning

We follow [Semantic Versioning](https://semver.org/):

- MAJOR version for incompatible API changes
- MINOR version for new functionality
- PATCH version for bug fixes

## Questions?

Feel free to:
- Open an issue
- Join our Discord
- Contact maintainers

Thank you for contributing!
