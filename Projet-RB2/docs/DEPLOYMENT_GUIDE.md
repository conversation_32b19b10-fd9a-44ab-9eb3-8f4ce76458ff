# Guide de Déploiement - Retreat And Be

Ce document décrit les procédures et les exigences pour déployer la plateforme Retreat And Be dans différents environnements. Suivez ces instructions pour assurer un déploiement cohérent et sécurisé.

## Table des matières

1. [Prérequis système](#1-prérequis-système)
2. [Configuration des environnements](#2-configuration-des-environnements)
3. [Procédures de déploiement](#3-procédures-de-déploiement)
4. [Vérifications post-déploiement](#4-vérifications-post-déploiement)
5. [Rollback en cas d'échec](#5-rollback-en-cas-déchec)
6. [Références](#6-références)

## 1. Prérequis système

### 1.1 Infrastructure hardware

#### Production

| Composant | Spécification | Quantité |
|-----------|---------------|----------|
| Kubernetes Worker Nodes | 8 vCPU, 32 GB RAM, 100 GB SSD | 3+ |
| Kubernetes Master Nodes | 4 vCPU, 16 GB RAM, 100 GB SSD | 3 |
| Base de données PostgreSQL | 8 vCPU, 32 GB RAM, 500 GB SSD | 2 (primaire + standby) |
| Redis Cluster | 4 vCPU, 16 GB RAM, 100 GB SSD | 3 (1 master, 2 replicas) |
| Object Storage | S3-compatible, min 1 TB | - |
| CDN | Edge locations mondiales | - |

#### Staging

| Composant | Spécification | Quantité |
|-----------|---------------|----------|
| Kubernetes Worker Nodes | 4 vCPU, 16 GB RAM, 100 GB SSD | 2 |
| Kubernetes Master Node | 2 vCPU, 8 GB RAM, 50 GB SSD | 1 |
| Base de données PostgreSQL | 4 vCPU, 16 GB RAM, 250 GB SSD | 1 |
| Redis | 2 vCPU, 8 GB RAM, 50 GB SSD | 1 |
| Object Storage | S3-compatible, min 100 GB | - |

#### Développement

| Composant | Spécification | Quantité |
|-----------|---------------|----------|
| Kubernetes Worker Nodes | 2 vCPU, 8 GB RAM, 50 GB SSD | 1 |
| Kubernetes Master Node | 2 vCPU, 4 GB RAM, 20 GB SSD | 1 |
| Base de données PostgreSQL | 2 vCPU, 4 GB RAM, 50 GB SSD | 1 |
| Redis | 1 vCPU, 2 GB RAM, 10 GB SSD | 1 |
| Object Storage | S3-compatible, min 20 GB | - |

### 1.2 Logiciels requis

| Logiciel | Version | Notes |
|----------|---------|-------|
| Kubernetes | v1.27+ | Utiliser une distribution telle que EKS, GKE, AKS ou K3s |
| Docker | 24.0+ | Pour la conteneurisation des applications |
| PostgreSQL | 16.0+ | Base de données principale |
| Redis | 7.2+ | Cache et stockage des sessions |
| Node.js | 20 LTS | Environnement d'exécution pour le backend |
| Nginx | 1.25+ | Serveur web et proxy inverse |
| Helm | 3.12+ | Gestionnaire de packages Kubernetes |
| Prometheus | 2.47+ | Monitoring |
| Grafana | 10.1+ | Visualisation des métriques |
| ELK Stack | 8.11+ | Gestion des logs |
| Cert-Manager | 1.13+ | Gestion des certificats TLS |

### 1.3 Domaines et certificats

- Domaine principal : `retreatnbe.com`
- Sous-domaines :
  - `api.retreatnbe.com` : API Backend
  - `app.retreatnbe.com` : Application frontend
  - `admin.retreatnbe.com` : Tableau de bord administrateur
  - `cdn.retreatnbe.com` : Contenu statique
  - `auth.retreatnbe.com` : Service d'authentification

Tous les domaines doivent être configurés avec des certificats SSL/TLS valides.

### 1.4 Services externes

| Service | Utilisation | Configuration requise |
|---------|-------------|----------------------|
| AWS S3 / GCP Storage | Stockage des médias | Bucket, accès IAM |
| Stripe | Paiements | Clés API, Webhooks |
| SendGrid | Emails | Clés API, domaine vérifié |
| Ethereum Node | Smart Contracts | Point d'accès RPC |
| Google Maps | Géolocalisation | Clé API avec restrictions |
| Auth0 | Authentification OAuth | Client ID, Client Secret |
| Cloudflare | CDN & Protection DDoS | Configuration DNS |
| Sentry | Suivi des erreurs | DSN projet |

## 2. Configuration des environnements

### 2.1 Variables d'environnement

Toutes les variables d'environnement doivent être stockées dans des secrets Kubernetes ou dans un service de gestion de secrets (AWS Secrets Manager, HashiCorp Vault).

Exemple de fichier `.env` de référence (ne jamais stocker directement):

```bash
# Général
NODE_ENV=production
PORT=3000
API_URL=https://api.retreatnbe.com
FRONTEND_URL=https://app.retreatnbe.com

# Base de données
DATABASE_URL=****************************************/retreatnbe?schema=public
DATABASE_POOL_SIZE=20
DATABASE_SSL=true

# Redis
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=complexpassword
REDIS_TLS=true

# Auth
JWT_SECRET=extremely-long-and-secure-secret-key
JWT_EXPIRY=3600
REFRESH_TOKEN_EXPIRY=2592000
COOKIE_SECRET=another-secure-secret

# Services externes
STRIPE_SECRET_KEY=sk_live_xxx
STRIPE_WEBHOOK_SECRET=whsec_xxx
SENDGRID_API_KEY=SG.xxx
AWS_ACCESS_KEY_ID=AKIAXXX
AWS_SECRET_ACCESS_KEY=xxx
AWS_REGION=eu-west-1
S3_BUCKET=retreatnbe-production-media

# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/xxx
ETHEREUM_CONTRACT_ADDRESS=0x123...

# Monitoring
SENTRY_DSN=https://<EMAIL>/xxx
PROMETHEUS_ENDPOINT=/metrics
LOG_LEVEL=info

# Recommandation
RECOMMENDATION_CACHE_TTL=3600
RECOMMENDATION_BATCH_SIZE=20
MODEL_UPDATE_CRON="0 3 * * *"

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100
```

### 2.2 Namespaces Kubernetes

Création des namespaces pour séparer les ressources:

```bash
kubectl create namespace retreatnbe-production
kubectl create namespace retreatnbe-monitoring
kubectl create namespace retreatnbe-logging
kubectl create namespace cert-manager
```

### 2.3 Configuration de la base de données

Exemple de configuration PostgreSQL pour la production:

```yaml
# postgresql-values.yaml
global:
  postgresql:
    auth:
      postgresPassword: "secure-password"
      username: "retreatnbe"
      password: "another-secure-password"
      database: "retreatnbe"
  
primary:
  persistence:
    size: 500Gi
  resources:
    requests:
      memory: "16Gi"
      cpu: "2"
    limits:
      memory: "32Gi"
      cpu: "8"
  
  pgConfig: |-
    max_connections = 300
    shared_buffers = 8GB
    effective_cache_size = 24GB
    maintenance_work_mem = 2GB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    work_mem = 52428kB
    min_wal_size = 2GB
    max_wal_size = 8GB
    max_worker_processes = 8
    max_parallel_workers_per_gather = 4
    max_parallel_workers = 8

readReplicas:
  replicaCount: 1
  resources:
    requests:
      memory: "8Gi"
      cpu: "1"
    limits:
      memory: "16Gi"
      cpu: "4"
```

### 2.4 Sécurité

#### Network Policies

Exemple de Network Policy pour limiter les accès à la base de données:

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: db-access-policy
  namespace: retreatnbe-production
spec:
  podSelector:
    matchLabels:
      app: postgresql
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api
    ports:
    - protocol: TCP
      port: 5432
```

#### RBAC

Créer des rôles spécifiques pour chaque composant:

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: retreatnbe-production
  name: backend-service-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
```

## 3. Procédures de déploiement

### 3.1 Pipeline CI/CD

Utilisez GitLab CI/CD pour automatiser le déploiement. Voici un exemple simplifié de `.gitlab-ci.yml`:

```yaml
stages:
  - test
  - build
  - security
  - deploy

variables:
  DOCKER_REGISTRY: registry.retreatnbe.com
  K8S_CLUSTER: production-cluster

# Stage: Test
unit_tests:
  stage: test
  image: node:20-alpine
  script:
    - cd backend
    - npm ci
    - npm run test:unit
  cache:
    paths:
      - backend/node_modules/

integration_tests:
  stage: test
  image: node:20-alpine
  script:
    - cd backend
    - npm ci
    - npm run test:integration
  cache:
    paths:
      - backend/node_modules/

# Stage: Build
build_backend:
  stage: build
  image: docker:24.0.0
  services:
    - docker:24.0.0-dind
  script:
    - cd backend
    - docker build -t $DOCKER_REGISTRY/backend:$CI_COMMIT_SHA -t $DOCKER_REGISTRY/backend:latest .
    - docker push $DOCKER_REGISTRY/backend:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/backend:latest

build_frontend:
  stage: build
  image: docker:24.0.0
  services:
    - docker:24.0.0-dind
  script:
    - cd frontend
    - docker build -t $DOCKER_REGISTRY/frontend:$CI_COMMIT_SHA -t $DOCKER_REGISTRY/frontend:latest .
    - docker push $DOCKER_REGISTRY/frontend:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/frontend:latest

# Stage: Security
security_scan:
  stage: security
  image: aquasec/trivy
  script:
    - trivy image --severity HIGH,CRITICAL $DOCKER_REGISTRY/backend:$CI_COMMIT_SHA
    - trivy image --severity HIGH,CRITICAL $DOCKER_REGISTRY/frontend:$CI_COMMIT_SHA

# Stage: Deploy
deploy_production:
  stage: deploy
  image: bitnami/kubectl:1.27
  script:
    - kubectl config use-context $K8S_CLUSTER
    - helm upgrade --install backend ./helm/backend --set image.tag=$CI_COMMIT_SHA -n retreatnbe-production
    - helm upgrade --install frontend ./helm/frontend --set image.tag=$CI_COMMIT_SHA -n retreatnbe-production
  environment:
    name: production
  when: manual
  only:
    - main
```

### 3.2 Stratégie de déploiement Blue/Green

Pour les déploiements sans temps d'arrêt, utilisez une stratégie Blue/Green:

1. Déployez la nouvelle version (Green) parallèlement à l'ancienne (Blue)
2. Effectuez les tests sur la nouvelle version
3. Basculez le trafic de Blue vers Green
4. Surveillez les performances et les erreurs
5. En cas de problème, revenez à Blue

Configuration Kubernetes pour Blue/Green:

```yaml
apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: retreatnbe-production
spec:
  selector:
    app: api
    version: blue  # Basculer entre blue et green
  ports:
  - port: 80
    targetPort: 3000
```

### 3.3 Migration de base de données

Les migrations doivent être automatisées avec Prisma et exécutées avant le déploiement des services:

```bash
# Commande à exécuter dans le pipeline CI/CD
npx prisma migrate deploy
```

Script pour vérifier l'état de la migration et faire un rollback si nécessaire:

```bash
#!/bin/bash
# check_migration.sh

echo "Vérification de l'état de la migration..."
MIGRATION_STATUS=$(npx prisma migrate status)

if [[ $MIGRATION_STATUS == *"Error"* ]]; then
  echo "Erreur de migration détectée. Rollback..."
  npx prisma migrate resolve --rolled-back "20230915120000_add_new_features"
  exit 1
else
  echo "Migration réussie."
  exit 0
fi
```

### 3.4 Déploiement des services

Exemple de déploiement Helm pour le backend:

```bash
# Installation initiale
helm install backend ./helm/backend \
  --namespace retreatnbe-production \
  --set image.tag=v1.0.0 \
  --set replicaCount=3 \
  --set resources.requests.memory=2Gi \
  --set resources.requests.cpu=1 \
  --set ingress.enabled=true \
  --set ingress.hosts[0].host=api.retreatnbe.com \
  --set ingress.tls[0].hosts[0]=api.retreatnbe.com

# Mise à jour
helm upgrade backend ./helm/backend \
  --namespace retreatnbe-production \
  --set image.tag=v1.0.1
```

## 4. Vérifications post-déploiement

### 4.1 Tests de santé

Vérifier l'état des services:

```bash
# Vérification des pods
kubectl get pods -n retreatnbe-production

# Vérification des services
kubectl get services -n retreatnbe-production

# Logs des pods
kubectl logs -f deployment/backend -n retreatnbe-production
```

### 4.2 Endpoint de santé

Vérifier les endpoints de santé de l'API:

```bash
# Vérification de l'API
curl -X GET "https://api.retreatnbe.com/health" -H "accept: application/json"

# Vérification détaillée
curl -X GET "https://api.retreatnbe.com/health/detailed" -H "accept: application/json" -H "Authorization: Bearer $TOKEN"
```

Réponse attendue:

```json
{
  "status": "ok",
  "version": "1.0.1",
  "timestamp": "2023-09-20T14:30:00Z",
  "uptime": 1872,
  "services": {
    "database": "connected",
    "redis": "connected",
    "storage": "connected"
  }
}
```

### 4.3 Tests fonctionnels

Exécutez des tests automatisés post-déploiement:

```bash
# Tests E2E
cd e2e-tests
npm run test:production
```

### 4.4 Vérification de la performance

Vérifier les métriques de performance dans Grafana:

1. Temps de réponse de l'API < 100ms pour le 95e percentile
2. Taux d'erreur < 0.1%
3. Utilisation CPU < 70%
4. Utilisation mémoire < 80%

### 4.5 Vérification de la sécurité

Effectuez des vérifications de sécurité:

```bash
# Scanner les vulnérabilités
trivy image registry.retreatnbe.com/backend:latest

# Test de pénétration automatisé
zap-cli quick-scan --self-contained --start-options "-config api.disablekey=true" https://api.retreatnbe.com
```

## 5. Rollback en cas d'échec

### 5.1 Procédure de rollback

En cas de problèmes, suivez ces étapes:

1. Revenez à la version précédente de l'application:

```bash
# Rollback avec Helm
helm rollback backend 1 -n retreatnbe-production
```

2. Si nécessaire, restaurez la base de données:

```bash
# Restauration depuis un snapshot
pg_restore -h $DB_HOST -U $DB_USER -d retreatnbe -v ./backups/retreatnbe_backup_before_deploy.dump
```

3. Vérifiez l'état après rollback:

```bash
# Vérification des pods
kubectl get pods -n retreatnbe-production

# Vérification de l'API
curl -X GET "https://api.retreatnbe.com/health" -H "accept: application/json"
```

### 5.2 Communication de l'incident

1. Informez l'équipe via Slack (#deployments et #incidents)
2. Créez un ticket d'incident dans Jira
3. Informez les utilisateurs si nécessaire via la page de statut
4. Organisez un post-mortem après résolution

## 6. Références

- [Documentation Kubernetes](https://kubernetes.io/docs/)
- [Helm Charts Repository](https://github.com/retreatandbe/helm-charts)
- [Procédures internes de déploiement](https://wiki.retreatnbe.com/ops/deployment)
- [Modèles de monitoring](https://wiki.retreatnbe.com/ops/monitoring)
- [SLAs et SLOs](https://wiki.retreatnbe.com/sla)
- [Plan de réponse aux incidents](https://wiki.retreatnbe.com/incident-response)
- [Architecture système](https://wiki.retreatnbe.com/architecture) 