# Guide des Tests E2E

## Vue d'ensemble

Ce document décrit l'architecture et les bonnes pratiques pour les tests end-to-end (E2E) du projet. Les tests E2E vérifient le bon fonctionnement de l'application dans son ensemble, en simulant les interactions utilisateur réelles.

## Structure des Tests

Les tests sont organisés par fonctionnalité dans les répertoires suivants :
- `cypress/e2e/auth/` - Tests d'authentification
- `cypress/e2e/web3/` - Tests des fonctionnalités Web3
- `cypress/e2e/marketplace/` - Tests du marketplace NFT
- `cypress/e2e/dashboard/` - Tests du tableau de bord

## Configuration de l'Environnement

### Prérequis
- Node.js v16 ou supérieur
- npm ou yarn
- Cypress
- Hardhat (pour le réseau blockchain local)

### Installation
```bash
npm install
npx cypress install
```

### Configuration de l'Environnement de Test
1. D<PERSON><PERSON><PERSON> le réseau blockchain local :
```bash
npx hardhat node
```

2. Démarrer le backend :
```bash
npm run start:api
```

3. Démarrer le frontend :
```bash
npm run dev
```

## Exécution des Tests

### Tests Locaux
```bash
# Ouvrir Cypress en mode interactif
npm run cypress:open

# Exécuter tous les tests en mode headless
npm run cypress:run
```

### Tests dans CI/CD
Les tests sont automatiquement exécutés dans GitHub Actions pour :
- Chaque push sur main et develop
- Chaque pull request vers main et develop

## Bonnes Pratiques

### Organisation du Code
1. Utiliser les commandes personnalisées pour les actions communes
2. Maintenir les données de test dans `cypress/fixtures/`
3. Utiliser les sélecteurs data-cy pour les éléments de test

### Performance
1. Réutiliser l'état d'authentification entre les tests
2. Nettoyer les données après chaque test
3. Paralléliser les tests dans CI/CD

### Maintenance
1. Documenter les scénarios de test
2. Maintenir les fixtures à jour
3. Revoir régulièrement les timeouts

## Débogage

### Logs et Captures d'Écran
- Les vidéos sont enregistrées dans `cypress/videos/`
- Les captures d'écran des échecs sont dans `cypress/screenshots/`
- Les rapports de test sont dans `cypress/reports/`

### Commandes Utiles
```bash
# Vérifier la syntaxe des tests
npm run cypress:lint

# Nettoyer les artefacts de test
npm run cypress:clean

# Exécuter un seul fichier de test
npm run cypress:run --spec "cypress/e2e/marketplace/nft-listing.cy.ts"
```

## Monitoring

### Métriques de Performance
- Temps d'exécution des tests
- Taux de réussite
- Temps de réponse des API

### Notifications
- Les échecs sont notifiés sur Slack
- Les rapports détaillés sont disponibles dans GitHub Actions

## Résolution des Problèmes Courants

### Timeouts
Si les tests échouent avec des timeouts :
1. Vérifier la connexion réseau
2. Augmenter les timeouts dans `cypress.config.ts`
3. Vérifier les logs du backend

### Erreurs Web3
Pour les erreurs liées à Web3 :
1. Vérifier que le réseau local est en cours d'exécution
2. Réinitialiser l'état du portefeuille
3. Vérifier les soldes des comptes de test

## Contribution

### Ajout de Nouveaux Tests
1. Créer un nouveau fichier dans le répertoire approprié
2. Suivre le modèle de test existant
3. Ajouter des commentaires explicatifs
4. Mettre à jour la documentation si nécessaire

### Revue de Code
- Les PR doivent inclure les tests appropriés
- Les tests doivent être revus pour :
  - Couverture
  - Performance
  - Maintenabilité
  - Documentation 