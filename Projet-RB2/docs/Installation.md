# Installation Guide

## Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher)
- MongoDB (v4.4 or higher)
- Git

## Quick Start

1. Clone the repository
```bash
git clone https://github.com/your-username/your-project.git
cd your-project
```

2. Install dependencies
```bash
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

3. Set up environment variables

Frontend (.env):
```env
REACT_APP_API_URL=http://localhost:3001
REACT_APP_SENTRY_DSN=your-sentry-dsn
```

Backend (.env):
```env
PORT=3001
MONGODB_URI=mongodb://localhost:27017/your-database
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
```

4. Start development servers
```bash
# Start backend server
cd backend
npm run dev

# Start frontend server (in a new terminal)
cd frontend
npm start
```

## Detailed Setup

### Frontend Setup

1. Install dependencies
```bash
cd frontend
npm install
```

2. Available scripts
```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Run E2E tests
npm run cypress

# Run accessibility tests
npm run test:a11y

# Run performance tests
npm run test:performance
```

3. Environment configuration
Create a `.env` file in the frontend directory:
```env
REACT_APP_API_URL=http://localhost:3001
REACT_APP_SENTRY_DSN=your-sentry-dsn
REACT_APP_GA_TRACKING_ID=your-ga-id
```

### Backend Setup

1. Install dependencies
```bash
cd backend
npm install
```

2. Available scripts
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Run database migrations
npm run migrate

# Generate API documentation
npm run docs
```

3. Environment configuration
Create a `.env` file in the backend directory:
```env
PORT=3001
MONGODB_URI=mongodb://localhost:27017/your-database
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your-email
SMTP_PASS=your-password
```

## Production Deployment

### Frontend Deployment

1. Build the application
```bash
cd frontend
npm run build
```

2. Serve the build directory
```bash
npm install -g serve
serve -s build
```

### Backend Deployment

1. Build the application
```bash
cd backend
npm run build
```

2. Start the server
```bash
npm start
```

## Docker Deployment

1. Build images
```bash
# Build frontend
docker build -t your-app-frontend ./frontend

# Build backend
docker build -t your-app-backend ./backend
```

2. Run containers
```bash
# Run frontend
docker run -p 3000:3000 your-app-frontend

# Run backend
docker run -p 3001:3001 your-app-backend
```

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific test suite
npm test -- booking

# Run with coverage
npm test -- --coverage

# Run E2E tests
npm run cypress

# Run accessibility tests
npm run test:a11y

# Run performance tests
npm run test:performance
```

## Troubleshooting

### Common Issues

1. MongoDB Connection
```bash
# Check MongoDB status
sudo service mongodb status

# Start MongoDB
sudo service mongodb start
```

2. Port Conflicts
```bash
# Check ports in use
lsof -i :3000
lsof -i :3001

# Kill process
kill -9 PID
```

3. Node Version
```bash
# Check Node version
node -v

# Use correct version
nvm use 16
```

## Support

For support, please contact:
- Email: <EMAIL>
- Documentation: https://docs.example.com
- GitHub Issues: https://github.com/your-username/your-project/issues
