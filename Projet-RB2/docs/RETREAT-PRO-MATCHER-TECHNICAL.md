# Documentation Technique : Service Retreat-Pro-Matcher

## Vue d'ensemble

Le service Retreat-Pro-Matcher est un système intelligent de mise en relation entre les professionnels partenaires et les retraites. Il utilise un algorithme de matching avancé pour calculer des scores de compatibilité basés sur plusieurs facteurs, permettant ainsi de recommander les partenaires les plus adaptés pour une retraite donnée, et inversement.

## Architecture

Le service Retreat-Pro-Matcher est implémenté selon une architecture modulaire qui s'intègre parfaitement dans l'écosystème de microservices de Retreat And Be.

### Composants principaux

1. **Backend (NestJS)**
   - Module de matching (`MatchingModule`)
   - Service de matching (`MatchingService`)
   - Service de notification (`MatchingNotificationService`)
   - Contrôleur REST (`MatchingController`)
   - DTOs pour les critères et résultats

2. **Frontend (React)**
   - Service client pour le matching (`matchingService`)
   - Composants UI pour la recherche et l'affichage des résultats
   - Pages dédiées au matching et aux détails

3. **Intégrations**
   - Base de données (Prisma)
   - Système de notification
   - Système de messagerie
   - Système d'authentification

## Algorithme de matching

L'algorithme de matching calcule un score de compatibilité entre un partenaire et une retraite en se basant sur plusieurs facteurs pondérés :

### Facteurs de compatibilité

1. **Compétences (35%)**
   - Correspondance entre les spécialisations du partenaire et les catégories de la retraite
   - Langues parlées par le partenaire

2. **Localisation (20%)**
   - Correspondance entre les zones de couverture du partenaire et la localisation de la retraite

3. **Évaluations (20%)**
   - Note moyenne du partenaire
   - Nombre d'avis

4. **Disponibilité (15%)**
   - Correspondance entre les disponibilités du partenaire et les dates de la retraite

5. **Budget (10%)**
   - Compatibilité entre le tarif du partenaire et le budget de la retraite

### Calcul du score

Le score global est calculé comme une moyenne pondérée des scores individuels pour chaque facteur :

```typescript
score = (skillMatch * 0.35) + (locationMatch * 0.2) + (ratingMatch * 0.2) +
        (availabilityMatch * 0.15) + (budgetMatch * 0.1)
```

Chaque score individuel est normalisé sur une échelle de 0 à 100.

## API Backend

Le service expose les endpoints REST suivants :

### Recherche de partenaires

```
POST /matching/partners
```

Trouve des partenaires correspondant aux critères spécifiés.

**Corps de la requête :**
```json
{
  "retreatId": "string",
  "categories": ["WELLNESS", "GUIDE"],
  "types": ["CERTIFIED", "PREMIUM_CERTIFIED"],
  "specializations": ["Yoga", "Méditation"],
  "languages": ["Français", "Anglais"],
  "maxBudget": 200,
  "dateRange": {
    "start": "2023-06-01",
    "end": "2023-06-15"
  },
  "location": {
    "country": "France",
    "region": "Provence"
  },
  "minExperience": 3,
  "minRating": 4.5,
  "minCapacity": 10,
  "certifiedOnly": true,
  "limit": 10
}
```

**Réponse :**
```json
{
  "results": [
    {
      "partnerId": "string",
      "retreatId": "string",
      "score": 85,
      "compatibilityFactors": {
        "skillMatch": 90,
        "availabilityMatch": 80,
        "locationMatch": 100,
        "ratingMatch": 85,
        "budgetMatch": 70
      },
      "partner": {
        "id": "string",
        "companyName": "string",
        "type": "CERTIFIED",
        "category": "WELLNESS",
        "description": "string",
        "specializations": ["string"],
        "languages": ["string"],
        "averageRating": 4.8,
        "totalReviews": 42,
        "completedServices": 120
      }
    }
  ],
  "total": 1,
  "executionTimeMs": 120
}
```

### Recherche de retraites

```
POST /matching/retreats
```

Trouve des retraites correspondant aux critères spécifiés.

**Corps de la requête :**
```json
{
  "partnerId": "string",
  "dateRange": {
    "start": "2023-06-01",
    "end": "2023-06-15"
  },
  "location": {
    "country": "France"
  },
  "maxBudget": 1000,
  "minCapacity": 10,
  "limit": 10
}
```

**Réponse :**
```json
{
  "results": [
    {
      "partnerId": "string",
      "retreatId": "string",
      "score": 85,
      "compatibilityFactors": {
        "skillMatch": 90,
        "availabilityMatch": 80,
        "locationMatch": 100,
        "ratingMatch": 85,
        "budgetMatch": 70
      },
      "retreat": {
        "id": "string",
        "title": "string",
        "description": "string",
        "startDate": "2023-06-01T00:00:00.000Z",
        "endDate": "2023-06-08T00:00:00.000Z",
        "location": "string",
        "capacity": 20,
        "price": 1200,
        "images": ["string"]
      }
    }
  ],
  "total": 1,
  "executionTimeMs": 120
}
```

### Autres endpoints

```
GET /matching/partners/retreat/:retreatId
GET /matching/retreats/partner/:partnerId
GET /matching/score/:partnerId/:retreatId
```

## Système de notification

Le service intègre un système de notification pour informer les utilisateurs des nouveaux matchings de haute qualité.

### Types de notifications

1. **Notification de matching individuel**
   - Envoyée lorsqu'un matching avec un score élevé est trouvé
   - Contient les détails du matching et le score

2. **Notification de matching groupé**
   - Envoyée lorsque plusieurs matchings avec un score élevé sont trouvés
   - Contient le nombre de matchings et le score le plus élevé

3. **Notification de rappel**
   - Envoyée périodiquement pour rappeler les matchings non traités
   - Contient le nombre de matchings en attente

### Seuil de notification

Par défaut, seuls les matchings avec un score supérieur ou égal à 80% génèrent des notifications. Ce seuil est configurable via la configuration de l'application.

## Frontend

### Services clients

#### Service de matching (`matchingService`)

```typescript
// Recherche de partenaires
findPartners(criteria: MatchingCriteria): Promise<MatchingResponse>

// Recherche de retraites
findRetreats(criteria: MatchingCriteria): Promise<MatchingResponse>

// Recherche de partenaires pour une retraite spécifique
findPartnersForRetreat(retreatId: string, options?: object): Promise<MatchingResponse>

// Recherche de retraites pour un partenaire spécifique
findRetreatsForPartner(partnerId: string, options?: object): Promise<MatchingResponse>

// Obtention du score de compatibilité
getMatchingScore(partnerId: string, retreatId: string): Promise<{ score: number, compatibilityFactors: object }>
```

#### Service de messagerie pour le matching (`matchingMessagingService`)

```typescript
// Envoie un message du partenaire à l'organisateur
sendPartnerToOrganizerMessage(partnerId: string, retreatId: string, message?: string): Promise<any>

// Envoie un message de l'organisateur au partenaire
sendOrganizerToPartnerMessage(partnerId: string, retreatId: string, message?: string): Promise<any>

// Envoie un message de suivi dans une conversation existante
sendFollowUpMessage(conversationId: string, message?: string): Promise<any>

// Initie un contact depuis la page de détails de matching
contactFromMatching(partnerId: string, retreatId: string, message?: string): Promise<any>
```

#### Service d'analyse pour le matching (`matchingAnalyticsService`)

```typescript
// Enregistre une vue de matching
recordMatchingView(result: MatchingResult, metadata?: any): Promise<void>

// Enregistre un contact après matching
recordMatchingContact(result: MatchingResult, contactMethod: string, metadata?: any): Promise<void>

// Enregistre une conversion après matching
recordMatchingConversion(result: MatchingResult, conversionType: string, metadata?: any): Promise<void>

// Obtient les données du tableau de bord d'analyse
getDashboardData(period?: string): Promise<MatchingDashboardData>
```

#### Service de recommandation pour le matching (`matchingRecommendationService`)

```typescript
// Obtient les recommandations pour un partenaire
getPartnerRecommendations(partnerId: string): Promise<MatchingResult[]>

// Génère des recommandations pour un partenaire
generatePartnerRecommendations(partnerId: string): Promise<any>

// Génère des recommandations pour tous les partenaires
generateAllPartnerRecommendations(): Promise<any>
```

### Composants UI

1. **Formulaire de recherche (`PartnerSearchForm`)**
   - Permet de spécifier les critères de recherche
   - Supporte les filtres avancés

2. **Résultats de matching (`PartnerMatchResults`)**
   - Affiche les résultats de matching avec les scores
   - Permet de voir les détails d'un matching

3. **Tableau de bord de matching (`PartnerMatchingDashboard`)**
   - Affiche les matchings recommandés pour un partenaire
   - Affiche les retraites à venir

4. **Page de détails de matching (`MatchingDetailsPage`)**
   - Affiche les détails complets d'un matching
   - Permet de contacter le partenaire ou de voir la retraite
   - Visualisation interactive des scores de matching

5. **Formulaire de contact (`MatchingContactForm`)**
   - Permet de contacter un partenaire ou un organisateur
   - Intégration avec le système de messagerie
   - Suivi des contacts pour l'analyse

6. **Visualisation des scores (`MatchingScoreChart`)**
   - Graphique interactif des facteurs de compatibilité
   - Visualisation des poids de chaque facteur
   - Animation pour une meilleure expérience utilisateur

7. **Recommandations personnalisées (`PartnerRecommendations`)**
   - Affiche les recommandations personnalisées pour un partenaire
   - Tri par score de compatibilité
   - Détails sur chaque recommandation

## Tests

Le service est couvert par des tests unitaires et d'intégration pour garantir sa fiabilité :

1. **Tests unitaires**
   - Tests du service de matching
   - Tests du contrôleur
   - Tests du service de notification

2. **Tests d'intégration**
   - Tests de l'API complète
   - Tests de l'intégration avec les autres services

## Performances et optimisations

1. **Mise en cache**
   - Les résultats de matching fréquemment demandés sont mis en cache
   - Les données de référence (catégories, types, etc.) sont mises en cache

2. **Pagination**
   - Les résultats sont paginés pour améliorer les performances
   - Le nombre de résultats peut être limité via le paramètre `limit`

3. **Indexation**
   - Les champs utilisés pour le matching sont indexés dans la base de données
   - Les requêtes sont optimisées pour utiliser ces index

## Sécurité

1. **Authentification**
   - Tous les endpoints sont protégés par JWT
   - Vérification des rôles pour les opérations sensibles

2. **Validation des données**
   - Validation stricte des critères de recherche
   - Protection contre les injections

3. **Limitation de débit**
   - Limitation du nombre de requêtes par utilisateur
   - Protection contre les abus

## Système de recommandation proactive

Le service Retreat-Pro-Matcher inclut un système de recommandation proactive qui analyse automatiquement les profils des partenaires et des retraites pour suggérer des matchings pertinents.

### Fonctionnement

1. **Génération automatique**
   - Exécution quotidienne pour générer des recommandations pour tous les partenaires
   - Exécution quotidienne pour générer des recommandations pour toutes les retraites à venir

2. **Critères de recommandation**
   - Seuil de score configurable (par défaut : 85%)
   - Limite du nombre de recommandations par utilisateur
   - Période de refroidissement entre les recommandations

3. **Notifications**
   - Envoi de notifications aux utilisateurs pour les informer des nouvelles recommandations
   - Regroupement des recommandations pour éviter de surcharger les utilisateurs

4. **API dédiée**
   - Endpoints pour générer des recommandations à la demande
   - Endpoints pour récupérer les recommandations existantes

## Intégration avec le système de réservation

Le service Retreat-Pro-Matcher s'intègre avec le système de réservation pour permettre la création directe de réservations à partir des matchings.

### Fonctionnalités

1. **Création de réservation**
   - Création directe de réservations depuis la page de détails de matching
   - Vérification des réservations existantes pour éviter les doublons
   - Transfert automatique des informations de matching vers la réservation

2. **Suivi des conversions**
   - Enregistrement des conversions de matching en réservation
   - Calcul des taux de conversion (vues → contacts → réservations)
   - Analyse des performances par catégorie de partenaire

3. **Statistiques de conversion**
   - Tableau de bord d'analyse des conversions
   - Visualisation des tendances de conversion
   - Identification des facteurs influençant les conversions

4. **API dédiée**
   - Endpoints pour créer des réservations à partir de matchings
   - Endpoints pour vérifier les réservations existantes
   - Endpoints pour obtenir des statistiques de conversion

## Système d'analyse

Le service Retreat-Pro-Matcher inclut un système d'analyse complet pour suivre les performances du matching et améliorer continuellement l'algorithme.

### Types d'événements analysés

1. **Événements de matching**
   - Exécution de recherche de matching
   - Consultation des résultats de matching
   - Contact après matching
   - Conversion après matching (réservation)

2. **Métriques suivies**
   - Temps d'exécution des requêtes
   - Taux de conversion
   - Score moyen des matchings
   - Distribution des scores

3. **Tableau de bord d'analyse**
   - Visualisation des tendances
   - Analyse des performances par catégorie, type, etc.
   - Recommandations automatiques pour l'optimisation

## Exportation des données

Le service permet d'exporter les données de matching dans différents formats pour une analyse plus approfondie ou une intégration avec d'autres systèmes.

### Formats d'exportation

1. **CSV**
   - Format tabulaire simple
   - Compatible avec Excel, Google Sheets, etc.

2. **Excel**
   - Format XLSX avec mise en forme avancée
   - Formules et formatage conditionnel

3. **JSON**
   - Format structuré pour l'intégration avec d'autres systèmes
   - Préservation de la structure des données

### Fonctionnalités d'exportation

1. **Exportation filtrée**
   - Possibilité de filtrer les données avant l'exportation
   - Critères de filtrage personnalisables

2. **Historique d'exportation**
   - Suivi des exportations effectuées
   - Accès aux fichiers exportés précédemment

3. **Sécurité**
   - Vérification des permissions avant l'exportation
   - Limitation de l'accès aux données sensibles

## Évolutions futures

1. **Apprentissage automatique**
   - Intégration de l'apprentissage automatique pour améliorer les recommandations
   - Personnalisation des recommandations en fonction des préférences utilisateur

2. **Analyse de sentiment**
   - Analyse des avis pour extraire des informations supplémentaires
   - Prise en compte du sentiment dans le calcul du score

3. **Matching en temps réel**
   - Notifications en temps réel pour les nouveaux matchings
   - Mise à jour en temps réel des scores de compatibilité

4. **API GraphQL**
   - Ajout d'une API GraphQL pour des requêtes plus flexibles
   - Support des abonnements pour les mises à jour en temps réel
