# Guide des Transitions d'Éléments Partagés

Ce document décrit l'implémentation des transitions d'éléments partagés dans le microservice Social-Platform-Video pour créer des transitions fluides où un élément se transforme en un autre élément entre différentes vues.

## Table des matières

1. [Introduction](#introduction)
2. [Concept et Fonctionnement](#concept-et-fonctionnement)
3. [Service de Transition](#service-de-transition)
4. [Composants React](#composants-react)
5. [Hooks React](#hooks-react)
6. [Cas d'Utilisation](#cas-dutilisation)
7. [Bonnes Pratiques](#bonnes-pratiques)
8. [Intégration avec les Autres Systèmes d'Animation](#intégration-avec-les-autres-systèmes-danimation)

## Introduction

Les transitions d'éléments partagés (shared element transitions) sont des animations qui créent une continuité visuelle entre différentes vues en animant des éléments communs d'une position et d'un état à un autre. Ces transitions aident les utilisateurs à comprendre la relation entre les différentes vues et à suivre le flux de navigation.

### Avantages des Transitions d'Éléments Partagés

- **Continuité Visuelle** : Créent une expérience fluide et cohérente entre les différentes vues
- **Orientation Spatiale** : Aident les utilisateurs à comprendre d'où viennent les éléments et où ils vont
- **Focus Attentionnel** : Dirigent l'attention de l'utilisateur vers les éléments importants
- **Expérience Premium** : Donnent une impression de qualité et de sophistication à l'interface

## Concept et Fonctionnement

Le principe des transitions d'éléments partagés est simple :

1. **Capture** : Capturer l'état (position, taille, style) d'un élément dans la vue source
2. **Création** : Créer un élément de transition qui sera animé
3. **Animation** : Animer l'élément de transition de l'état source vers l'état cible
4. **Finalisation** : Afficher l'élément dans la vue cible et supprimer l'élément de transition

Dans notre implémentation, nous utilisons une approche basée sur un élément "placeholder" qui est créé dynamiquement et positionné par-dessus l'interface utilisateur pendant la transition.

## Service de Transition

Le cœur de notre système est le service `sharedElementTransition`, qui gère toutes les transitions d'éléments partagés :

### Fonctionnalités Principales

- **Capture d'État** : Capture l'état d'un élément (position, taille, styles)
- **Gestion des Transitions** : Démarre, annule et suit les transitions actives
- **Création de Placeholders** : Crée des éléments de transition qui sont animés
- **Animation** : Gère l'animation des éléments de transition

### Méthodes Clés

```typescript
// Capture l'état d'un élément
captureElement(id: string, element: HTMLElement): ElementState

// Démarre une transition entre deux éléments
startTransition(fromId: string, toId: string, options?: TransitionOptions): Promise<void>

// Annule une transition active
cancelTransition(fromId: string, toId: string): void

// Annule toutes les transitions actives
cancelAllTransitions(): void

// Vérifie si une transition est active
isTransitionActive(fromId: string, toId: string): boolean

// Récupère toutes les transitions actives
getActiveTransitions(): Map<string, number>
```

### Options de Transition

Les transitions peuvent être personnalisées avec plusieurs options :

```typescript
interface TransitionOptions {
  duration?: number;       // Durée de la transition en ms
  easing?: string;         // Fonction de timing (easing)
  delay?: number;          // Délai avant le début de la transition
  properties?: string[];   // Propriétés CSS à animer
  onStart?: () => void;    // Callback au début de la transition
  onComplete?: () => void; // Callback à la fin de la transition
}
```

## Composants React

Nous avons créé plusieurs composants React pour faciliter l'utilisation des transitions d'éléments partagés :

### SharedElement

Un composant qui enveloppe un élément pour le rendre "partageable" dans une transition.

```jsx
<SharedElement
  id="my-element"
  captureOnMount={true}
  className="my-class"
>
  <div>Contenu de l'élément</div>
</SharedElement>
```

Le composant `SharedElement` :
- Enregistre l'élément avec un ID unique
- Capture l'état de l'élément au montage (si `captureOnMount` est `true`)
- Ajoute l'attribut `data-shared-id` pour l'identification

### SharedElementTransition

Un composant qui gère une transition entre deux éléments partagés.

```jsx
<SharedElementTransition
  fromId="element-source"
  toId="element-target"
  isActive={true}
  options={{
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }}
  onComplete={() => console.log('Transition terminée')}
/>
```

Le composant `SharedElementTransition` :
- Démarre la transition lorsque `isActive` devient `true`
- Annule la transition lorsque `isActive` devient `false`
- Nettoie les ressources lorsqu'il est démonté

### SharedElementPair

Un composant de plus haut niveau qui gère une paire d'éléments partagés et leur transition.

```jsx
<SharedElementPair
  id="my-pair"
  isActive={isExpanded}
  sourceContent={<div>État initial</div>}
  targetContent={<div>État étendu</div>}
  transitionOptions={{
    duration: 400,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }}
/>
```

Le composant `SharedElementPair` :
- Gère la visibilité des éléments source et cible
- Crée automatiquement les IDs pour les éléments partagés
- Gère la transition entre les deux états

## Hooks React

Nous avons créé plusieurs hooks React pour faciliter l'utilisation des transitions d'éléments partagés :

### useSharedElement

Hook pour enregistrer un élément comme élément partagé.

```jsx
function MyComponent() {
  const { ref, captureElement, clearElement } = useSharedElement('my-element', true);
  
  return <div ref={ref}>Élément partagé</div>;
}
```

### useSharedElementTransition

Hook pour créer et gérer des transitions entre éléments partagés.

```jsx
function MyComponent() {
  const {
    startTransition,
    cancelTransition,
    isTransitionActive
  } = useSharedElementTransition({
    duration: 300,
    easing: 'ease-out'
  });
  
  const handleClick = () => {
    startTransition('element-source', 'element-target');
  };
  
  return <button onClick={handleClick}>Démarrer la transition</button>;
}
```

### useSharedElementPair

Hook pour créer et gérer une paire d'éléments partagés.

```jsx
function MyComponent() {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    sourceRef,
    targetRef,
    startTransition,
    cancelTransition
  } = useSharedElementPair('my-pair', {
    duration: 400,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  });
  
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    startTransition();
  };
  
  return (
    <>
      <div ref={sourceRef} style={{ display: isExpanded ? 'none' : 'block' }}>
        État initial
      </div>
      <div ref={targetRef} style={{ display: isExpanded ? 'block' : 'none' }}>
        État étendu
      </div>
      <button onClick={toggleExpanded}>Toggle</button>
    </>
  );
}
```

## Cas d'Utilisation

Les transitions d'éléments partagés sont particulièrement utiles dans les scénarios suivants :

### Transitions Liste-Détail

Pour les transitions entre une liste d'éléments et une vue détaillée d'un élément, les transitions d'éléments partagés peuvent créer une continuité visuelle qui aide l'utilisateur à comprendre la relation entre les deux vues.

```jsx
// Vue liste
<SharedElement id={`item-${item.id}-image`}>
  <img src={item.image} alt={item.title} />
</SharedElement>

// Vue détail
<SharedElement id={`item-${item.id}-image`}>
  <img src={item.image} alt={item.title} />
</SharedElement>

// Transition
<SharedElementTransition
  fromId={`item-${item.id}-image`}
  toId={`item-${item.id}-image`}
  isActive={isDetailView}
/>
```

### Expansion d'Éléments

Pour les éléments qui s'agrandissent ou se réduisent en réponse à une interaction utilisateur, les transitions d'éléments partagés peuvent créer une animation fluide qui aide l'utilisateur à comprendre la transformation.

```jsx
<SharedElementPair
  id="expandable-card"
  isActive={isExpanded}
  sourceContent={<div className="card-collapsed">Contenu réduit</div>}
  targetContent={<div className="card-expanded">Contenu étendu</div>}
/>
```

### Navigation Entre Pages

Pour les transitions entre différentes pages ou vues, les transitions d'éléments partagés peuvent créer une continuité visuelle qui aide l'utilisateur à comprendre la relation entre les pages.

```jsx
// Page 1
<SharedElement id="header-logo">
  <Logo />
</SharedElement>

// Page 2
<SharedElement id="header-logo">
  <Logo />
</SharedElement>

// Transition (dans un composant de niveau supérieur)
<SharedElementTransition
  fromId="header-logo"
  toId="header-logo"
  isActive={isNavigating}
/>
```

## Bonnes Pratiques

Pour tirer le meilleur parti des transitions d'éléments partagés, suivez ces bonnes pratiques :

### Conception

- **Identifiez les Éléments Clés** : Identifiez les éléments qui sont présents dans les deux vues et qui peuvent servir de points d'ancrage visuels
- **Cohérence Visuelle** : Assurez-vous que les éléments partagés ont une apparence similaire dans les deux vues
- **Timing Approprié** : Utilisez des durées de transition appropriées (généralement entre 200 et 500 ms)
- **Easing Naturel** : Utilisez des fonctions d'easing qui créent des mouvements naturels (comme `cubic-bezier(0.4, 0, 0.2, 1)`)

### Implémentation

- **IDs Uniques** : Utilisez des IDs uniques pour chaque élément partagé
- **Capture au Bon Moment** : Capturez l'état des éléments au bon moment (généralement au montage ou juste avant la transition)
- **Nettoyage** : Assurez-vous de nettoyer les ressources lorsque les composants sont démontés
- **Fallback** : Prévoyez un comportement de repli en cas d'échec de la transition

## Intégration avec les Autres Systèmes d'Animation

Notre système de transitions d'éléments partagés s'intègre avec les autres systèmes d'animation que nous avons implémentés :

### Intégration avec les Animations Adaptatives

Les transitions d'éléments partagés peuvent utiliser les hooks adaptatifs pour ajuster leurs paramètres :

```jsx
function AdaptiveSharedElementTransition({ fromId, toId, isActive }) {
  const { adaptDuration, getTimingFunction } = useAnimationPreferences();
  
  return (
    <SharedElementTransition
      fromId={fromId}
      toId={toId}
      isActive={isActive}
      options={{
        duration: adaptDuration(300),
        easing: getTimingFunction('ease-out')
      }}
    />
  );
}
```

### Intégration avec les Animations Coordonnées

Les transitions d'éléments partagés peuvent être intégrées dans des séquences d'animation coordonnées :

```jsx
const { play } = useCreateAnimationSequence(
  'page-transition',
  [
    {
      id: 'shared-element-transition',
      type: 'custom',
      targetIds: [],
      params: {
        customAnimate: (onComplete) => {
          startTransition('element-source', 'element-target', {
            onComplete
          });
        }
      }
    },
    {
      id: 'content-fade-in',
      type: 'fade',
      targetIds: ['page-content'],
      params: {
        duration: 300,
        direction: 'in'
      },
      dependencies: ['shared-element-transition']
    }
  ]
);
```

---

Ces transitions d'éléments partagés permettent d'offrir une expérience utilisateur plus fluide et cohérente, améliorant la compréhension et l'engagement des utilisateurs.
