# Intégration des microservices Social, Social-Platform-Video et messaging-service

Ce document décrit l'intégration entre les microservices Social, Social-Platform-Video et messaging-service dans le projet Retreat And Be.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Fonctionnalités implémentées](#fonctionnalités-implémentées)
4. [Configuration](#configuration)
5. [Flux de données](#flux-de-données)
6. [API](#api)
7. [WebSockets](#websockets)
8. [Sécurité](#sécurité)
9. [Déploiement](#déploiement)
10. [Dépannage](#dépannage)

## Vue d'ensemble

L'intégration entre les microservices Social, Social-Platform-Video et messaging-service permet de créer une expérience utilisateur cohérente et interactive pour les fonctionnalités sociales de la plateforme Retreat And Be. Cette intégration permet notamment :

- L'envoi de notifications en temps réel pour les événements sociaux (livestreams, commentaires, likes)
- Le relais des messages de chat des livestreams vers le système de messagerie
- Le partage de contenu social via la messagerie

## Architecture

L'architecture de l'intégration repose sur une communication bidirectionnelle entre les microservices, utilisant à la fois des API REST et des WebSockets pour les communications en temps réel.

### Diagramme d'architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │◄───►│  Backend-NestJS │◄───►│  Social-Platform│
│  (React)        │     │  (Gateway)      │     │  -Video         │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        ▲                        ▲                       ▲
        │                        │                       │
        │                        │                       │
        │                        │                       │
        ▼                        ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  WebSocket      │◄───►│  messaging-     │◄───►│  Notifications  │
│  Client         │     │  service        │     │  System         │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Composants principaux

1. **Backend-NestJS** : Agit comme une passerelle (gateway) pour les microservices et gère l'authentification et l'autorisation.
2. **Social-Platform-Video** : Gère les fonctionnalités de livestream, blog et analyses sociales.
3. **messaging-service** : Gère les conversations et les messages entre utilisateurs.
4. **WebSocket Client** : Permet aux utilisateurs de recevoir des notifications et des messages en temps réel.
5. **Notifications System** : Gère l'envoi et la réception des notifications système.

## Fonctionnalités implémentées

### 1. Notifications de livestream

- Notification lors de la création d'un nouveau livestream
- Notification lors du démarrage d'un livestream
- Notification lors de la fin d'un livestream

### 2. Chat de livestream

- Relais des messages de chat des livestreams vers le système de messagerie
- Affichage des messages de chat en temps réel pour tous les participants

### 3. Notifications de blog

- Notification lors de la création d'un nouveau commentaire sur un article de blog
- Notification lors d'un like sur un article de blog

### 4. Partage de contenu social

- Partage d'articles de blog avec d'autres utilisateurs
- Partage de livestreams avec d'autres utilisateurs

## Configuration

### Variables d'environnement

#### Backend-NestJS

```env
# Microservices
MICROSERVICES_SOCIAL_PLATFORM_VIDEO_URL=http://social-platform-video:3002
MICROSERVICES_SOCIAL_PLATFORM_VIDEO_API_KEY=social-platform-video-api-key
MICROSERVICES_MESSAGING_URL=http://messaging-service:5178
MICROSERVICES_MESSAGING_API_KEY=messaging-service-api-key
```

#### Social-Platform-Video

```env
# Messaging Service
MESSAGING_SERVICE_URL=http://messaging-service:5178
MESSAGING_SERVICE_API_KEY=messaging-service-api-key
```

#### messaging-service

```env
# Social Service
SOCIAL_SERVICE_URL=http://backend-nestjs:3000/social
SOCIAL_PLATFORM_VIDEO_URL=http://social-platform-video:3002
SOCIAL_API_KEY=social-api-key
```

### Docker Compose

Un fichier `docker-compose.microservices.yml` est fourni pour déployer tous les microservices ensemble avec les bonnes configurations.

## Flux de données

### 1. Création d'un livestream

1. L'utilisateur crée un livestream via le frontend
2. Le frontend envoie une requête au Backend-NestJS
3. Le Backend-NestJS transmet la requête au microservice Social-Platform-Video
4. Le microservice Social-Platform-Video crée le livestream et envoie une notification au microservice messaging-service
5. Le microservice messaging-service diffuse la notification aux utilisateurs concernés via WebSocket

### 2. Message dans un livestream

1. L'utilisateur envoie un message dans le chat d'un livestream
2. Le frontend envoie le message au Backend-NestJS
3. Le Backend-NestJS transmet le message au microservice Social-Platform-Video
4. Le microservice Social-Platform-Video enregistre le message et le relaie au microservice messaging-service
5. Le microservice messaging-service diffuse le message à tous les participants du livestream via WebSocket

### 3. Commentaire sur un article de blog

1. L'utilisateur commente un article de blog
2. Le frontend envoie le commentaire au Backend-NestJS
3. Le Backend-NestJS transmet le commentaire au microservice Social-Platform-Video
4. Le microservice Social-Platform-Video enregistre le commentaire et envoie une notification au microservice messaging-service
5. Le microservice messaging-service envoie une notification à l'auteur de l'article via WebSocket

## API

### Endpoints du microservice messaging-service

#### Notifications

```
POST /api/notifications
```

Reçoit les notifications des autres microservices et les traite.

**Corps de la requête :**
```json
{
  "type": "livestream_notification",
  "livestreamId": "123",
  "recipientId": "456",
  "eventType": "created",
  "message": "Nouveau livestream créé",
  "timestamp": "2023-06-15T14:30:00Z"
}
```

#### Messages

```
POST /api/messages/relay
```

Reçoit les messages à relayer des autres microservices.

**Corps de la requête :**
```json
{
  "type": "livestream_message",
  "livestreamId": "123",
  "userId": "456",
  "userName": "John Doe",
  "content": "Hello world!",
  "timestamp": "2023-06-15T14:35:00Z"
}
```

## WebSockets

Le microservice messaging-service utilise Socket.IO pour les communications en temps réel.

### Événements émis

- `livestream:notification` : Notification concernant un livestream
- `livestream:message` : Message dans un livestream
- `notification` : Notification générale pour un utilisateur spécifique

## Sécurité

### Authentification

- Utilisation de JWT pour l'authentification entre les microservices
- Vérification des clés API pour les communications inter-services

### Autorisation

- Vérification des permissions pour chaque action
- Contrôle d'accès basé sur les rôles (RBAC)

## Déploiement

### Avec Docker Compose

```bash
docker-compose -f docker-compose.microservices.yml up -d
```

### Avec Kubernetes

Des fichiers de configuration Kubernetes sont disponibles dans le répertoire `k8s/`.

## Dépannage

### Problèmes courants

1. **Les notifications ne sont pas reçues**
   - Vérifier que les variables d'environnement sont correctement configurées
   - Vérifier que les microservices sont en cours d'exécution
   - Vérifier les journaux pour les erreurs

2. **Les messages de chat ne sont pas relayés**
   - Vérifier la connexion WebSocket
   - Vérifier les autorisations de l'utilisateur
   - Vérifier les journaux pour les erreurs

3. **Erreurs d'authentification entre les microservices**
   - Vérifier que les clés API sont correctes
   - Vérifier que les JWT sont valides
   - Vérifier les journaux pour les erreurs
