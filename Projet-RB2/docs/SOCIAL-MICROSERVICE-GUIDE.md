# Guide d'utilisation du microservice Social

## Introduction

Ce guide explique comment utiliser les fonctionnalités du microservice Social dans l'application Retreat And Be. Il couvre les fonctionnalités de livestream, de blog et d'analyses sociales.

## Table des matières

1. [Livestreams](#livestreams)
2. [Blog](#blog)
3. [Analyses sociales](#analyses-sociales)
4. [Intégration avec d'autres microservices](#intégration-avec-dautres-microservices)
5. [Dépannage](#dépannage)

## Livestreams

### Création d'un livestream

Pour créer un nouveau livestream, suivez ces étapes :

1. Connectez-vous à votre compte
2. Accédez à la page "Tableau de bord social" via le menu principal
3. Cliquez sur l'onglet "Livestreams"
4. Cliquez sur le bouton "Créer un livestream"
5. Remplissez le formulaire avec les informations suivantes :
   - Titre du livestream
   - Description
   - Date et heure de début
   - Image de couverture (optionnel)
   - Visibilité (public ou privé)
6. <PERSON><PERSON><PERSON> sur "Créer"

### Démarrage d'un livestream

Pour démarrer un livestream programmé :

1. Accédez à la page du livestream
2. Cliquez sur le bouton "Démarrer le livestream"
3. Configurez vos paramètres audio et vidéo
4. Cliquez sur "Commencer la diffusion"

### Interaction avec un livestream

En tant que spectateur, vous pouvez :

1. Voir les livestreams en cours dans l'onglet "Livestreams"
2. Cliquer sur un livestream pour le regarder
3. Envoyer des messages dans le chat en direct
4. Poser des questions à l'hôte

### Fin d'un livestream

Pour terminer un livestream en cours :

1. Cliquez sur le bouton "Terminer le livestream"
2. Confirmez votre choix
3. Le livestream sera automatiquement enregistré et disponible pour visionnage ultérieur

## Blog

### Création d'un article de blog

Pour créer un nouvel article de blog :

1. Connectez-vous à votre compte
2. Accédez à la page "Tableau de bord social" via le menu principal
3. Cliquez sur l'onglet "Blog"
4. Cliquez sur le bouton "Créer un article"
5. Remplissez le formulaire avec les informations suivantes :
   - Titre de l'article
   - Contenu (éditeur riche disponible)
   - Tags (pour faciliter la recherche)
   - Image de couverture (optionnel)
   - Statut (brouillon, publié, archivé)
6. Cliquez sur "Enregistrer" ou "Publier"

### Gestion des articles de blog

Pour gérer vos articles de blog :

1. Accédez à l'onglet "Blog" du tableau de bord social
2. Vous verrez vos articles publiés et vos brouillons
3. Cliquez sur un article pour le modifier
4. Utilisez les options pour :
   - Modifier l'article
   - Changer son statut
   - Supprimer l'article

### Interaction avec les articles de blog

En tant que lecteur, vous pouvez :

1. Parcourir les articles de blog sur la page "Blog"
2. Filtrer les articles par tag ou par auteur
3. Lire un article en cliquant sur son titre
4. Aimer un article en cliquant sur le bouton "J'aime"
5. Commenter un article en utilisant le formulaire de commentaire
6. Partager un article sur les réseaux sociaux

## Analyses sociales

### Accès au tableau de bord d'analyses

Pour accéder aux analyses sociales :

1. Connectez-vous à votre compte
2. Accédez à la page "Tableau de bord social" via le menu principal
3. L'onglet "Analyses" est sélectionné par défaut

### Interprétation des statistiques

Le tableau de bord d'analyses affiche :

1. **Statistiques générales**
   - Total des vues
   - Engagement total
   - Contenu créé
   - Taux d'engagement

2. **Répartition par type de contenu**
   - Graphique montrant la distribution des vues entre livestreams, articles de blog et vidéos

3. **Tendances d'engagement**
   - Graphique montrant l'évolution des vues, likes et commentaires dans le temps

4. **Contenu populaire**
   - Liste des contenus les plus performants avec leurs statistiques

### Filtrage des données

Vous pouvez filtrer les données d'analyse par :

1. Période (jour, semaine, mois, année)
2. Type de contenu (livestream, blog, vidéo)
3. Utilisateur (pour les administrateurs)

## Intégration avec d'autres microservices

### Intégration avec le microservice de matching

Le microservice Social s'intègre avec le microservice de matching pour :

1. Permettre des livestreams de matching entre partenaires et organisateurs
2. Analyser les conversions de matching suite à des livestreams
3. Recommander des partenaires basés sur les intérêts de contenu

### Intégration avec le microservice de notification

Le microservice Social envoie des notifications pour :

1. Nouveaux livestreams programmés
2. Début de livestreams
3. Nouveaux articles de blog
4. Réponses aux commentaires
5. Mentions dans les messages de chat

## Dépannage

### Problèmes courants avec les livestreams

1. **Le livestream ne démarre pas**
   - Vérifiez vos paramètres audio et vidéo
   - Assurez-vous d'avoir autorisé l'accès à votre caméra et microphone
   - Essayez de rafraîchir la page

2. **Le chat ne fonctionne pas**
   - Vérifiez votre connexion internet
   - Assurez-vous que le livestream est en cours
   - Essayez de vous déconnecter et de vous reconnecter

### Problèmes courants avec le blog

1. **Impossible de publier un article**
   - Vérifiez que tous les champs obligatoires sont remplis
   - Assurez-vous que le contenu n'est pas trop volumineux
   - Vérifiez que vous avez les permissions nécessaires

2. **Les images ne s'affichent pas**
   - Vérifiez que l'URL de l'image est correcte
   - Assurez-vous que l'image est dans un format supporté (JPG, PNG, GIF)
   - Essayez de télécharger l'image à nouveau

### Contact support

Si vous rencontrez des problèmes persistants, contactez le support technique :

- Email : <EMAIL>
- Chat en direct : disponible sur la page d'aide
- Téléphone : +33 1 23 45 67 89
