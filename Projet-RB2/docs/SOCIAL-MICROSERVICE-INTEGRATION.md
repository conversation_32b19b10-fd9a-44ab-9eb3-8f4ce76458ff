# Intégration du microservice Social

## Introduction

Ce document décrit l'intégration complète du microservice Social avec le frontend (Front-Audrey-V1-Main-main), le backend (Backend-NestJS) et le schéma de base de données (schema.prisma). Cette intégration permet d'ajouter des fonctionnalités sociales avancées à la plateforme Retreat And Be, notamment les livestreams, les articles de blog et les analyses sociales.

## Architecture

L'intégration du microservice Social suit une architecture en couches qui permet une séparation claire des responsabilités et une évolutivité optimale.

### Diagramme d'architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │◄───►│  Backend-NestJS │◄───►│  Base de données│
│  (React)        │     │  (API Gateway)  │     │  (PostgreSQL)   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Flux de données

1. Le frontend communique avec le backend via des API REST
2. Le backend interagit avec la base de données via Prisma ORM
3. Les événements sont propagés entre les composants via un système d'événements

## Modèles de données

Les modèles de données suivants ont été ajoutés au schéma Prisma pour prendre en charge les fonctionnalités sociales :

### Livestream

```prisma
model Livestream {
  id                String           @id @default(uuid())
  title             String
  description       String           @db.Text
  hostId            String
  status            LivestreamStatus @default(SCHEDULED)
  startTime         DateTime
  endTime           DateTime?
  thumbnailUrl      String?
  streamUrl         String?
  recordingUrl      String?
  isPrivate         Boolean          @default(false)
  viewerCount       Int              @default(0)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  // Relations
  host              User             @relation("UserHostedLivestreams", fields: [hostId], references: [id])
  messages          LivestreamMessage[] @relation("LivestreamMessages")
  participants      LivestreamParticipant[] @relation("LivestreamParticipants")
  analytics         SocialAnalytics? @relation("LivestreamAnalytics")

  // Metadata
  metadata          Json?            @default("{}")
}
```

### BlogPost

```prisma
model BlogPost {
  id                String          @id @default(uuid())
  title             String
  content           String          @db.Text
  authorId          String
  authorName        String
  status            BlogPostStatus  @default(DRAFT)
  publishDate       DateTime?
  imageUrl          String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Relations
  author            User            @relation("UserBlogPosts", fields: [authorId], references: [id])
  comments          BlogComment[]   @relation("BlogPostComments")
  likes             BlogLike[]      @relation("BlogPostLikes")
  tags              BlogPostTag[]   @relation("BlogPostTags")
  analytics         SocialAnalytics? @relation("BlogPostAnalytics")

  // Metadata
  metadata          Json?           @default("{}")
}
```

### SocialAnalytics

```prisma
model SocialAnalytics {
  id                String            @id @default(uuid())
  entityId          String            @unique
  entityType        SocialContentType
  views             Int               @default(0)
  likes             Int               @default(0)
  comments          Int               @default(0)
  shares            Int               @default(0)
  engagementRate    Float             @default(0)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  livestream        Livestream?       @relation("LivestreamAnalytics", fields: [entityId], references: [id], onDelete: Cascade)
  blogPost          BlogPost?         @relation("BlogPostAnalytics", fields: [entityId], references: [id], onDelete: Cascade)

  // Metadata
  metadata          Json?             @default("{}")
}
```

## Intégration backend

### Services implémentés

1. **LivestreamService**
   - Création et gestion des livestreams
   - Gestion des messages du chat
   - Gestion des participants

2. **BlogService**
   - Création et gestion des articles de blog
   - Gestion des commentaires et des likes
   - Gestion des tags

3. **SocialAnalyticsService**
   - Collecte et analyse des données d'engagement
   - Génération de rapports et de statistiques
   - Suivi des événements

### Contrôleurs API

1. **LivestreamController**
   - `GET /social/livestream` - Récupérer tous les livestreams
   - `GET /social/livestream/:id` - Récupérer un livestream par ID
   - `POST /social/livestream` - Créer un nouveau livestream
   - `PATCH /social/livestream/:id` - Mettre à jour un livestream
   - `POST /social/livestream/:id/start` - Démarrer un livestream
   - `POST /social/livestream/:id/end` - Terminer un livestream
   - `GET /social/livestream/:id/messages` - Récupérer les messages d'un livestream
   - `POST /social/livestream/:id/messages` - Envoyer un message à un livestream

2. **BlogController**
   - `GET /social/blog` - Récupérer tous les articles de blog
   - `GET /social/blog/:id` - Récupérer un article de blog par ID
   - `POST /social/blog` - Créer un nouvel article de blog
   - `PATCH /social/blog/:id` - Mettre à jour un article de blog
   - `DELETE /social/blog/:id` - Supprimer un article de blog
   - `GET /social/blog/:id/comments` - Récupérer les commentaires d'un article de blog
   - `POST /social/blog/:id/comments` - Ajouter un commentaire à un article de blog
   - `POST /social/blog/:id/like` - Aimer un article de blog
   - `POST /social/blog/:id/unlike` - Ne plus aimer un article de blog

3. **SocialAnalyticsController**
   - `GET /social/analytics` - Récupérer les statistiques sociales globales
   - `GET /social/analytics/livestreams/:id` - Récupérer les statistiques d'un livestream
   - `GET /social/analytics/blog/:id` - Récupérer les statistiques d'un article de blog
   - `GET /social/analytics/users/:id` - Récupérer les statistiques d'un utilisateur
   - `GET /social/analytics/popular` - Récupérer le contenu populaire
   - `POST /social/analytics/events` - Enregistrer un événement d'analyse
   - `GET /social/analytics/engagement` - Récupérer les tendances d'engagement

## Intégration frontend

### Services clients

1. **livestreamService**
   - Communication avec l'API backend pour les livestreams
   - Gestion des messages et des participants

2. **blogService**
   - Communication avec l'API backend pour les articles de blog
   - Gestion des commentaires et des likes

3. **socialAnalyticsService**
   - Communication avec l'API backend pour les analyses sociales
   - Suivi des événements de vue et d'engagement

### Composants UI

1. **LivestreamList**
   - Affichage des livestreams avec filtrage
   - Support pour les livestreams en direct, programmés et terminés

2. **BlogPostList**
   - Affichage des articles de blog avec filtrage
   - Support pour les articles publiés, brouillons et archivés

3. **SocialAnalyticsDashboard**
   - Tableau de bord d'analyse sociale
   - Graphiques et statistiques d'engagement

### Pages

1. **LivestreamPage**
   - Affichage d'un livestream avec chat en direct
   - Contrôles pour démarrer et terminer un livestream (pour l'hôte)

2. **BlogPostPage**
   - Affichage d'un article de blog avec commentaires
   - Fonctionnalités pour aimer et commenter

3. **SocialAnalyticsPage**
   - Page d'analyse sociale complète
   - Onglets pour les différentes sections (analyses, livestreams, blog, contenu populaire)

## Sécurité

### Authentification et autorisation

- Utilisation de JWT pour l'authentification
- Contrôle d'accès basé sur les rôles (RBAC)
- Vérification des permissions pour chaque action

### Protection des données

- Validation des entrées utilisateur
- Protection contre les attaques CSRF et XSS
- Chiffrement des communications

## Déploiement

### Configuration

1. **Variables d'environnement**
   - `DATABASE_URL` - URL de la base de données PostgreSQL
   - `JWT_SECRET` - Secret pour la génération des tokens JWT
   - `FRONTEND_URL` - URL du frontend pour les CORS

2. **Dépendances**
   - Prisma ORM pour l'accès à la base de données
   - NestJS pour le backend
   - React pour le frontend

### Intégration avec Kubernetes

1. **Services**
   - Définition du service Social
   - Configuration du service de découverte

2. **Déploiements**
   - Configuration des déploiements pour le microservice
   - Gestion des ressources et de la scalabilité

## Maintenance et évolution

### Surveillance

- Mise en place de métriques pour suivre les performances
- Alertes en cas de problèmes
- Journalisation des événements importants

### Évolutions futures

1. **Fonctionnalités avancées de livestream**
   - Streaming multi-participants
   - Partage d'écran et tableau blanc
   - Enregistrement et montage automatisés

2. **Améliorations du blog**
   - Éditeur WYSIWYG avancé
   - Planification des publications
   - SEO automatisé

3. **Analyses avancées**
   - Prédiction des tendances
   - Recommandations de contenu
   - Segmentation des utilisateurs
