# Booking API Documentation

## Overview
The Booking API provides endpoints for managing bookings in the system. It supports creating, reading, updating, and canceling bookings.

## Base URL
```
http://localhost:3001/api/bookings
```

## Authentication
All endpoints require authentication using JWT tokens. Include the token in the Authorization header:
```
Authorization: Bearer <your_token>
```

## Endpoints

### Create Booking
Create a new booking in the system.

**POST** `/api/bookings`

#### Request Body
```typescript
{
  service: string;      // 'yoga' | 'meditation' | 'fitness' | 'wellness'
  date: string;        // ISO 8601 format
  time: string;        // HH:mm format
  participants: number; // 1-10
  specialRequests?: string;
}
```

#### Response
```typescript
{
  id: string;
  userId: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  bookingDetails: {
    service: string;
    date: string;
    time: string;
    participants: number;
    specialRequests?: string;
  };
  createdAt: string;
  updatedAt: string;
}
```

#### Example
```bash
curl -X POST http://localhost:3001/api/bookings \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "yoga",
    "date": "2024-12-15",
    "time": "09:00",
    "participants": 2,
    "specialRequests": "Need yoga mats"
  }'
```

### Get User Bookings
Retrieve all bookings for the authenticated user.

**GET** `/api/bookings`

#### Response
```typescript
[
  {
    id: string;
    userId: string;
    status: string;
    bookingDetails: {
      service: string;
      date: string;
      time: string;
      participants: number;
      specialRequests?: string;
    };
    createdAt: string;
    updatedAt: string;
  }
]
```

### Get Booking
Retrieve a specific booking by ID.

**GET** `/api/bookings/:id`

#### Response
```typescript
{
  id: string;
  userId: string;
  status: string;
  bookingDetails: BookingDetails;
  createdAt: string;
  updatedAt: string;
}
```

### Cancel Booking
Cancel an existing booking.

**POST** `/api/bookings/:id/cancel`

#### Response
```typescript
{
  id: string;
  status: 'cancelled';
  updatedAt: string;
}
```

### Get Available Slots
Get available time slots for a specific date.

**GET** `/api/bookings/available-slots`

#### Query Parameters
- `date`: Date in YYYY-MM-DD format

#### Response
```typescript
string[] // Array of available time slots in HH:mm format
```

## Error Handling

### Error Response Format
```typescript
{
  message: string;
  status: number;
  errors?: {
    [field: string]: string[];
  };
}
```

### Common Error Codes
- `400`: Bad Request - Invalid input
- `401`: Unauthorized - Missing or invalid token
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource doesn't exist
- `409`: Conflict - Resource conflict
- `500`: Internal Server Error

## Rate Limiting
- 100 requests per minute per IP
- 1000 requests per hour per user

## Pagination
For endpoints returning lists, use these query parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

## Versioning
Current version: v1
Include version in header:
```
Accept: application/json; version=1
```

## Testing
Test environment available at:
```
http://localhost:3001/api/test/bookings
```

## Examples

### JavaScript/TypeScript
```typescript
import { apiService } from '../services/api';

// Create booking
const createBooking = async (bookingDetails) => {
  try {
    const response = await apiService.post('/bookings', bookingDetails);
    return response.data;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

// Get user bookings
const getUserBookings = async () => {
  try {
    const response = await apiService.get('/bookings');
    return response.data;
  } catch (error) {
    console.error('Error fetching bookings:', error);
    throw error;
  }
};
```

## Support
For API support, contact:
- Email: <EMAIL>
- Documentation: https://docs.example.com/api
- Status: https://status.example.com
