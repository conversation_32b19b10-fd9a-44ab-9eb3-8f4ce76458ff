openapi: 3.0.0
info:
  title: RandB API
  version: 1.0.0
  description: API complète pour la plateforme RandB

servers:
  - url: https://api.retreatandbe.com/v1
    description: Production
  - url: https://staging-api.retreatandbe.com/v1
    description: Staging

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
        details:
          type: object

paths:
  /auth/login:
    post:
      tags: [Auth]
      summary: Authentification utilisateur
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Connexion réussie
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                  refreshToken:
                    type: string

  /analyzer/analyze:
    post:
      tags: [Analyzer]
      security:
        - bearerAuth: []
      summary: Analyse de données
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                options:
                  type: object
      responses:
        '200':
          description: Analyse réussie
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                  metadata:
                    type: object

  /storage/upload:
    post:
      tags: [Storage]
      security:
        - bearerAuth: []
      summary: Upload de fichier
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: Upload réussi
          content:
            application/json:
              schema:
                type: object
                properties:
                  cid:
                    type: string
                  url:
                    type: string
