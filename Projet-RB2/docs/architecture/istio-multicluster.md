# Configuration Istio Multi-Cluster pour RB2

Ce document décrit la configuration Istio multi-cluster mise en place pour le projet RB2, permettant de déployer l'application sur plusieurs clusters Kubernetes tout en maintenant une connectivité transparente.

## Architecture Multi-Cluster

L'architecture multi-cluster d'Istio permet de connecter plusieurs clusters Kubernetes et de les faire fonctionner comme un maillage de services unifié. Cette configuration offre plusieurs avantages :

- **Haute disponibilité** : Répartition des charges sur plusieurs clusters pour une meilleure résilience
- **Isolation géographique** : Déploiement des services plus près des utilisateurs
- **Isolation des environnements** : Séparation des environnements de production, staging, et développement
- **Migration progressive** : Possibilité de migrer progressivement d'un cluster à un autre

## Configuration Implémentée

Notre configuration multi-cluster comprend les éléments suivants :

### 1. Activation du Multi-Cluster

```yaml
multiCluster:
  enabled: true
  clusterName: "primary-cluster"
  globalDomainSuffix: "global"
```

### 2. Configuration des Réseaux Mesh

```yaml
meshNetworks:
  network1:
    endpoints:
    - fromRegistry: primary-cluster
    gateways:
    - address: istio-eastwestgateway.istio-system.svc.cluster.local
      port: 15443
  network2:
    endpoints:
    - fromRegistry: remote-cluster
    gateways:
    - address: istio-eastwestgateway-remote.istio-system.svc.cluster.local
      port: 15443
```

### 3. Activation des Fonctionnalités Associées

```yaml
features:
  externalIstiod: true
  mountMtlsCerts: true
  includeEnvoyFilter: true
```

### 4. Configuration des Passerelles Est-Ouest

Les passerelles est-ouest sont essentielles pour la communication inter-cluster. Elles sont déployées via les fichiers :
- `expose-istiod.yaml` : Expose le plan de contrôle Istio
- `expose-services.yaml` : Expose les services du maillage

## Déploiement

### Prérequis

- Clusters Kubernetes accessibles via kubectl
- istioctl installé (version 1.18.2 ou supérieure)
- Accès administrateur aux clusters

### Étapes de Déploiement

1. **Sur le cluster primaire** :

   ```bash
   # Exécuter le script de déploiement
   ./scripts/deploy-istio-multicluster.sh
   # Répondre 'y' à la question sur le cluster primaire
   ```

2. **Pour chaque cluster distant** :

   a. Extraire le kubeconfig du cluster distant :
   ```bash
   kubectl config view --context=<context-distant> --raw > remote-cluster.kubeconfig
   ```

   b. Créer un secret dans le cluster primaire :
   ```bash
   kubectl create secret generic remote-cluster-secret \
       --from-file=KUBECONFIG=./remote-cluster.kubeconfig \
       -n istio-system
   ```

   c. Exécuter le script de déploiement sur le cluster distant :
   ```bash
   # Exécuter le script de déploiement
   ./scripts/deploy-istio-multicluster.sh
   # Répondre 'n' à la question sur le cluster primaire
   ```

   d. Créer un secret dans le cluster distant avec le kubeconfig du cluster primaire :
   ```bash
   kubectl create secret generic primary-cluster-secret \
       --from-file=KUBECONFIG=./primary-cluster.kubeconfig \
       -n istio-system
   ```

## Vérification

Pour vérifier que la configuration multi-cluster fonctionne correctement :

```bash
# Vérifier l'état des services Istio
istioctl analyze -n istio-system

# Vérifier la connectivité entre les clusters
kubectl get endpoints -n istio-system

# Vérifier les services disponibles dans le maillage
istioctl proxy-status
```

## Dépannage

### Problèmes de Connectivité Inter-Cluster

Si les services d'un cluster ne sont pas visibles depuis un autre cluster :

1. Vérifier que les passerelles est-ouest sont correctement déployées :
   ```bash
   kubectl get pods -n istio-system -l app=istio-eastwestgateway
   ```

2. Vérifier que les secrets contenant les kubeconfigs sont correctement créés :
   ```bash
   kubectl get secrets -n istio-system
   ```

3. Vérifier les logs des passerelles est-ouest :
   ```bash
   kubectl logs -n istio-system -l app=istio-eastwestgateway
   ```

### Problèmes de Discovery des Services

Si les services ne sont pas découverts correctement :

1. Vérifier les logs d'istiod :
   ```bash
   kubectl logs -n istio-system -l app=istiod
   ```

2. Vérifier la configuration des endpoints :
   ```bash
   kubectl get endpoints -A
   ```

## Références

- [Documentation officielle Istio Multi-Cluster](https://istio.io/latest/docs/setup/install/multicluster/)
- [Istio Multi-Primary Installation](https://istio.io/latest/docs/setup/install/multicluster/multi-primary/)
- [Istio Multi-Primary sur différents réseaux](https://istio.io/latest/docs/setup/install/multicluster/multi-primary_multi-network/)
