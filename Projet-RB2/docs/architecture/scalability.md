# Guide d'Architecture de Scalabilité

## Vue d'ensemble

Ce guide détaille l'architecture de scalabilité de Retreat And Be, couvrant les stratégies de mise à l'échelle horizontale et verticale, la gestion de la charge, et les bonnes pratiques pour maintenir les performances sous forte charge.

## Architecture de Scalabilité

### 1. Composants

```
├── Load Balancing
│   ├── Nginx Load Balancer
│   ├── HAProxy
│   └── DNS Round Robin
├── Services Scaling
│   ├── Kubernetes HPA
│   ├── Container Orchestration
│   └── Auto-scaling Groups
├── Database Scaling
│   ├── Read Replicas
│   ├── Sharding
│   └── Connection Pooling
└── Caching
    ├── Redis Cluster
    ├── CDN
    └── Application Cache
```

## Stratégies de Scalabilité

### 1. Scalabilité Horizontale

#### Services API
```yaml
# kubernetes/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
      - name: api
        image: retreatandbe/api:latest
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-deployment
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

#### Base de données
```yaml
# kubernetes/database-statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
spec:
  serviceName: postgres
  replicas: 3
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:14
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 100Gi
```

### 2. Load Balancing

#### Configuration Nginx
```nginx
# nginx/load-balancer.conf
upstream api_servers {
    least_conn;
    server api1.retreatandbe.com:3000;
    server api2.retreatandbe.com:3000;
    server api3.retreatandbe.com:3000;
}

server {
    listen 80;
    server_name api.retreatandbe.com;

    location / {
        proxy_pass http://api_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Configuration de timeout
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffer_size 16k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
    }
}
```

### 3. Caching

#### Configuration Redis Cluster
```yaml
# kubernetes/redis-cluster.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:6.2
        command: ["redis-server", "/conf/redis.conf"]
        ports:
        - containerPort: 6379
          name: client
        - containerPort: 16379
          name: gossip
        volumeMounts:
        - name: conf
          mountPath: /conf
        - name: data
          mountPath: /data
      volumes:
      - name: conf
        configMap:
          name: redis-cluster-config
```

#### Configuration CDN
```javascript
// frontend/cdn-config.js
const cdnConfig = {
  images: {
    domain: 'cdn.retreatandbe.com',
    paths: {
      avatars: '/images/avatars',
      products: '/images/products',
      events: '/images/events'
    },
    options: {
      quality: 80,
      formats: ['webp', 'jpeg'],
      sizes: [300, 600, 1200]
    }
  },
  static: {
    domain: 'static.retreatandbe.com',
    paths: {
      js: '/js',
      css: '/css',
      fonts: '/fonts'
    },
    cacheControl: 'public, max-age=31536000'
  }
};
```

## Stratégies de Performance

### 1. Optimisation des Requêtes

#### Query Optimization
```typescript
// src/services/database.service.ts
import { Pool } from 'pg';
import { RedisClient } from 'redis';

export class DatabaseService {
    private pool: Pool;
    private redis: RedisClient;
    
    constructor() {
        this.pool = new Pool({
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000
        });
        
        this.redis = new RedisClient({
            cluster: true,
            nodes: [
                { host: 'redis-0', port: 6379 },
                { host: 'redis-1', port: 6379 },
                { host: 'redis-2', port: 6379 }
            ]
        });
    }
    
    async getEvent(eventId: string) {
        // Check cache first
        const cached = await this.redis.get(`event:${eventId}`);
        if (cached) return JSON.parse(cached);
        
        // If not in cache, query database
        const result = await this.pool.query(
            'SELECT * FROM events WHERE id = $1',
            [eventId]
        );
        
        // Store in cache for future requests
        if (result.rows[0]) {
            await this.redis.setex(
                `event:${eventId}`,
                3600, // 1 hour cache
                JSON.stringify(result.rows[0])
            );
        }
        
        return result.rows[0];
    }
}
```

### 2. Gestion de la Charge

#### Rate Limiting
```typescript
// src/middleware/rate-limit.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import { redis } from '../services/redis';

export const apiLimiter = rateLimit({
    store: new RedisStore({
        client: redis,
        prefix: 'rate-limit:'
    }),
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
        error: 'Too many requests, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false
});

export const authLimiter = rateLimit({
    store: new RedisStore({
        client: redis,
        prefix: 'auth-limit:'
    }),
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // Limit each IP to 5 login attempts per hour
    message: {
        error: 'Too many login attempts, please try again later.'
    }
});
```

## Monitoring et Alertes

### 1. Métriques de Scalabilité

```typescript
// src/metrics/scaling.metrics.ts
import { Registry, Gauge } from 'prom-client';

export class ScalingMetrics {
    private registry: Registry;
    private activeConnections: Gauge;
    private responseTime: Gauge;
    private errorRate: Gauge;
    
    constructor() {
        this.registry = new Registry();
        
        this.activeConnections = new Gauge({
            name: 'active_connections',
            help: 'Number of active connections'
        });
        
        this.responseTime = new Gauge({
            name: 'response_time_ms',
            help: 'API response time in milliseconds'
        });
        
        this.errorRate = new Gauge({
            name: 'error_rate',
            help: 'Rate of errors per minute'
        });
        
        this.registry.registerMetric(this.activeConnections);
        this.registry.registerMetric(this.responseTime);
        this.registry.registerMetric(this.errorRate);
    }
    
    updateMetrics(connections: number, respTime: number, errors: number) {
        this.activeConnections.set(connections);
        this.responseTime.set(respTime);
        this.errorRate.set(errors);
    }
}
```

## Bonnes Pratiques

### 1. Scalabilité
- Conception sans état (stateless)
- Mise en cache efficace
- Réplication de données
- Auto-scaling basé sur les métriques

### 2. Performance
- Optimisation des requêtes
- Compression des données
- CDN pour les assets statiques
- Connection pooling

### 3. Résilience
- Circuit breakers
- Retry policies
- Fallback mechanisms
- Health checks

### 4. Monitoring
- Métriques en temps réel
- Alertes proactives
- Logging distribué
- Traçage des transactions

## Ressources

- [Kubernetes Documentation](https://kubernetes.io/docs/home/<USER>
- [Redis Cluster Tutorial](https://redis.io/topics/cluster-tutorial)
- [NGINX Load Balancing](https://docs.nginx.com/nginx/admin-guide/load-balancer/http-load-balancer/)
- [Database Scaling Patterns](https://microservices.io/patterns/data/database-per-service.html)
