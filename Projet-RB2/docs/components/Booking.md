# Booking Components Documentation

## Overview
The booking system consists of several components that work together to provide a seamless booking experience. This document outlines the components, their props, and usage examples.

## Components

### BookingPage
The main container component that manages the booking flow.

```tsx
import BookingPage from '../pages/Booking/BookingPage';

<BookingPage />
```

#### State Management
- Uses Redux for global state
- Uses React Query for server state
- Manages booking flow steps locally

### BookingForm
A form component for collecting booking details.

```tsx
import { BookingForm } from '../pages/Booking/components/BookingForm';

interface BookingFormProps {
  initialDetails: BookingDetails;
  onSubmit: (details: BookingDetails) => void;
}

<BookingForm
  initialDetails={initialDetails}
  onSubmit={handleSubmit}
/>
```

#### Props
- `initialDetails`: Initial booking details
- `onSubmit`: Callback function when form is submitted

#### Features
- Form validation
- Accessible form controls
- Responsive design
- Real-time validation

### BookingCalendar
A calendar component for date selection.

```tsx
import { BookingCalendar } from '../pages/Booking/components/BookingCalendar';

interface BookingCalendarProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

<BookingCalendar
  selectedDate={selectedDate}
  onDateSelect={handleDateSelect}
/>
```

#### Props
- `selectedDate`: Currently selected date
- `onDateSelect`: Callback function when date is selected

#### Features
- Month navigation
- Available slots indication
- Responsive grid layout
- Keyboard navigation

### BookingSummary
A component that displays booking details for confirmation.

```tsx
import { BookingSummary } from '../pages/Booking/components/BookingSummary';

interface BookingSummaryProps {
  details: BookingDetails;
  onConfirm: () => void;
  onEdit: () => void;
}

<BookingSummary
  details={bookingDetails}
  onConfirm={handleConfirm}
  onEdit={handleEdit}
/>
```

#### Props
- `details`: Booking details to display
- `onConfirm`: Callback function when booking is confirmed
- `onEdit`: Callback function to edit booking

## Usage Example

```tsx
import { BookingPage } from "./BookingPage';
import { BookingProvider } from '../contexts/BookingContext';

function App() {
  return (
    <BookingProvider>
      <BookingPage />
    </BookingProvider>
  );
}
```

## Styling
Components use Tailwind CSS for styling. Custom styles can be added through the `className` prop.

## Accessibility
All components are built with accessibility in mind:
- ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance

## Performance
Components are optimized for performance:
- Lazy loading
- Memoization
- Code splitting
- Image optimization

## Error Handling
Components handle various error scenarios:
- Form validation errors
- API errors
- Network errors
- Server errors

## Testing
Components include comprehensive tests:
- Unit tests
- Integration tests
- E2E tests
- Accessibility tests
- Performance tests
