# Guide de Déploiement - Environnement de Développement

Ce guide détaille la configuration et le déploiement de Retreat And Be dans l'environnement de développement.

## Architecture

```
├── Services
│   ├── Frontend (React)
│   ├── Backend API (Node.js)
│   ├── IPFS Node
│   └── Smart Contracts (Local Blockchain)
├── Base de données
│   ├── PostgreSQL
│   └── Redis (Cache)
└── Infrastructure
    ├── Docker Compose
    ├── Local Kubernetes
    └── Monitoring Local
```

## Prérequis

### 1. Outils de Développement
- Node.js v16+
- Docker Desktop
- kubectl
- minikube
- Ganache (pour blockchain locale)

### 2. Configuration Locale

#### Variables d'Environnement
```bash
# .env.development
# API
API_PORT=3000
API_URL=http://localhost:3000
NODE_ENV=development

# Base de données
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=development
DB_NAME=retreatandbe_dev

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# IPFS
IPFS_HOST=localhost
IPFS_PORT=5001

# JWT
JWT_SECRET=dev_secret_key
JWT_EXPIRATION=24h

# Blockchain
BLOCKCHAIN_PROVIDER=http://localhost:8545
CONTRACT_ADDRESS=0x...
```

## Installation

### 1. Configuration Docker Compose

```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      target: development
    ports:
      - "8080:8080"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:3000

  api:
    build:
      context: ./backend
      target: development
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres
      - redis
      - ipfs

  postgres:
    image: postgres:14
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=development
      - POSTGRES_DB=retreatandbe_dev
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6.2
    ports:
      - "6379:6379"

  ipfs:
    image: ipfs/go-ipfs:latest
    ports:
      - "4001:4001"
      - "5001:5001"
      - "8080:8080"
    volumes:
      - ipfs_data:/data/ipfs

volumes:
  postgres_data:
  ipfs_data:
```

### 2. Scripts de Développement

```json
// package.json
{
  "scripts": {
    "dev": "docker-compose -f docker-compose.dev.yml up",
    "dev:build": "docker-compose -f docker-compose.dev.yml up --build",
    "dev:down": "docker-compose -f docker-compose.dev.yml down",
    "dev:clean": "docker-compose -f docker-compose.dev.yml down -v",
    "migrate:dev": "npx prisma migrate dev",
    "seed:dev": "npx prisma db seed"
  }
}
```

### 3. Configuration Base de Données

```typescript
// prisma/schema.prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String?
  role      Role     @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  USER
  ADMIN
}
```

## Déploiement Local

### 1. Démarrer l'Environnement

```bash
# Cloner le repository
git clone https://github.com/LUCDIZA/Retreat-And-Be-V2.git
cd Retreat-And-Be-V2

# Installer les dépendances
npm install

# Copier les variables d'environnement
cp .env.example .env.development

# Démarrer les services
npm run dev
```

### 2. Initialiser la Base de Données

```bash
# Appliquer les migrations
npm run migrate:dev

# Seed la base de données
npm run seed:dev
```

### 3. Démarrer le Frontend

```bash
# Dans un nouveau terminal
cd frontend
npm install
npm start
```

## Tests

### 1. Tests Unitaires

```bash
# Backend
cd backend
npm run test

# Frontend
cd frontend
npm run test
```

### 2. Tests E2E

```bash
# Cypress
cd frontend
npm run cypress:open
```

## Debugging

### 1. Logs Docker

```bash
# Voir les logs de tous les services
docker-compose -f docker-compose.dev.yml logs -f

# Voir les logs d'un service spécifique
docker-compose -f docker-compose.dev.yml logs -f api
```

### 2. Base de Données

```bash
# Accéder à PostgreSQL
docker exec -it retreatandbe-postgres-1 psql -U postgres -d retreatandbe_dev

# Voir les tables
\dt

# Exécuter des requêtes
SELECT * FROM "User";
```

### 3. IPFS

```bash
# Accéder au node IPFS
docker exec -it retreatandbe-ipfs-1 ipfs

# Voir les peers
ipfs swarm peers

# Voir les fichiers
ipfs files ls
```

## Monitoring Local

### 1. Métriques

- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000

### 2. Logs

```bash
# Voir les logs applicatifs
tail -f logs/development.log

# Voir les logs système
docker-compose -f docker-compose.dev.yml logs -f
```

## Troubleshooting

### Problèmes Courants

1. **Erreur de connexion à la base de données**
```bash
# Vérifier que PostgreSQL est en cours d'exécution
docker ps | grep postgres

# Vérifier les logs
docker logs retreatandbe-postgres-1
```

2. **Erreur IPFS**
```bash
# Réinitialiser IPFS
docker-compose -f docker-compose.dev.yml restart ipfs

# Vérifier la configuration
docker exec -it retreatandbe-ipfs-1 ipfs config show
```

3. **Problèmes de Build**
```bash
# Nettoyer Docker
docker system prune -a

# Reconstruire les images
npm run dev:build
```

## Support

Pour le support en développement :
- GitHub Issues: [Retreat And Be Issues](https://github.com/LUCDIZA/Retreat-And-Be-V2/issues)
- Documentation: [Wiki](https://github.com/LUCDIZA/Retreat-And-Be-V2/wiki)
- Slack: #dev-support
