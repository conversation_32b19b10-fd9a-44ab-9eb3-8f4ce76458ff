# Wiki Technique - Projet RB2

## Bienvenue !

Bienvenue dans le wiki technique du projet RB2. Ce document est conçu pour aider les nouveaux développeurs à se familiariser rapidement avec le projet, comprendre son architecture, et commencer à contribuer efficacement. Nous vous recommandons de lire ce document dans son intégralité avant de commencer à travailler sur le code.

## Table des matières

1. [Vue d'ensemble du projet](#vue-densemble-du-projet)
2. [Architecture](#architecture)
3. [Installation et configuration](#installation-et-configuration)
4. [Structure du code](#structure-du-code)
5. [Base de données](#base-de-données)
6. [API et endpoints](#api-et-endpoints)
7. [Authentication et sécurité](#authentication-et-sécurité)
8. [Tests](#tests)
9. [Déploiement](#déploiement)
10. [Monitoring et logging](#monitoring-et-logging)
11. [Guides pratiques](#guides-pratiques)
12. [Ressources et documentation](#ressources-et-documentation)
13. [Glossaire](#glossaire)

## Vue d'ensemble du projet

RB2 est une plateforme de gestion de retraites spirituelles et de bien-être permettant aux utilisateurs de rechercher, réserver et gérer des séjours dans différents centres partenaires. Le système comprend :

- Un backend API RESTful développé avec NestJS et TypeScript
- Une interface frontend développée en React avec l'architecture Atomic Design
- Une base de données PostgreSQL
- Un système de cache Redis
- Un service de notification par email et SMS

### Principales fonctionnalités

- Recherche et filtrage avancés de retraites
- Système de réservation et de paiement
- Gestion des utilisateurs et des profils
- Système d'évaluation et de commentaires
- Tableau de bord administrateur
- Rapports et analytics
- Intégration avec des systèmes externes (paiement, calendrier, etc.)

## Architecture

### Architecture globale

RB2 suit une architecture hexagonale (ou architecture en couches) :

```
┌─────────────────────────────────────────────────────┐
│                   Présentation                       │
│  (Contrôleurs, Middleware, Filtres, Intercepteurs)   │
└───────────────────────┬─────────────────────────────┘
                        │
┌───────────────────────▼─────────────────────────────┐
│                    Application                       │
│               (Services, Use Cases)                  │
└───────────────────────┬─────────────────────────────┘
                        │
┌───────────────────────▼─────────────────────────────┐
│                     Domaine                          │
│             (Entités, Value Objects)                 │
└───────────────────────┬─────────────────────────────┘
                        │
┌───────────────────────▼─────────────────────────────┐
│                 Infrastructure                       │
│      (Repositories, Adaptateurs Externes)            │
└─────────────────────────────────────────────────────┘
```

### Technologies principales

- **Backend**:
  - NestJS (framework Node.js)
  - TypeScript
  - TypeORM (ORM)
  - PostgreSQL (base de données principale)
  - Redis (cache et files d'attente)
  - Jest (tests)
  - Swagger/OpenAPI (documentation API)

- **Frontend**:
  - React
  - TypeScript
  - Architecture Atomic Design
  - Redux pour la gestion d'état
  - Styled-components pour le styling
  - Jest et React Testing Library pour les tests

- **DevOps**:
  - Docker et Docker Compose
  - CI/CD avec GitHub Actions
  - Déploiement sur AWS
  - Monitoring avec Datadog
  - Logging centralisé avec ELK Stack

## Installation et configuration

### Prérequis

- Node.js (v16+)
- npm ou yarn
- Docker et Docker Compose
- PostgreSQL (si exécuté localement)
- Redis (si exécuté localement)

### Installation

1. Cloner le dépôt :
   ```bash
   git clone https://github.com/organisation/rb2.git
   cd rb2
   ```

2. Installer les dépendances :
   ```bash
   npm install
   ```

3. Configurer les variables d'environnement :
   ```bash
   cp .env.example .env
   # Ouvrir .env et compléter les variables manquantes
   ```

4. Démarrer les services avec Docker Compose :
   ```bash
   docker-compose up -d
   ```

5. Exécuter les migrations de base de données :
   ```bash
   npm run migration:run
   ```

6. Démarrer le serveur en mode développement :
   ```bash
   npm run start:dev
   ```

### Scripts utiles

- `npm run start:dev` - Démarrer le serveur en mode développement
- `npm run test` - Exécuter les tests unitaires
- `npm run test:e2e` - Exécuter les tests end-to-end
- `npm run lint` - Vérifier le code avec ESLint
- `npm run migration:generate -- -n NomDeLaMigration` - Générer une nouvelle migration
- `npm run migration:revert` - Annuler la dernière migration
- `npm run seed` - Alimenter la base de données avec des données de test
- `npm run build` - Construire l'application pour la production

## Structure du code

Le projet suit une structure modulaire avec les dossiers suivants :

```
src/
├── app.module.ts             # Module principal de l'application
├── main.ts                   # Point d'entrée de l'application
├── config/                   # Configuration de l'application
├── common/                   # Code partagé, utilitaires, décorateurs, etc.
├── database/                 # Migrations, seeds, configuration de la base de données
│   └── migrations/           # Migrations TypeORM
├── modules/                  # Modules fonctionnels de l'application
│   ├── auth/                 # Module d'authentification
│   ├── users/                # Module de gestion des utilisateurs
│   ├── retreats/             # Module de gestion des retraites
│   ├── bookings/             # Module de gestion des réservations
│   └── ...
├── monitoring/               # Services de monitoring et logging
├── interfaces/               # Interfaces TypeScript partagées
└── swagger/                  # Configuration Swagger/OpenAPI
```

### Structure d'un module

Chaque module fonctionnel suit une structure similaire :

```
modules/example/
├── example.module.ts         # Définition du module
├── example.controller.ts     # Contrôleur REST
├── example.service.ts        # Service principal
├── example.repository.ts     # Repository personnalisé (si nécessaire)
├── dto/                      # Data Transfer Objects
│   ├── create-example.dto.ts
│   └── update-example.dto.ts
├── entities/                 # Entités TypeORM
│   └── example.entity.ts
├── interfaces/               # Interfaces TypeScript
├── pipes/                    # Pipes de validation personnalisés
├── guards/                   # Guards d'autorisation
└── tests/                    # Tests unitaires et d'intégration
```

## Base de données

### Modèle de données

Le schéma simplifié de la base de données est le suivant :

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│     Users     │     │    Retreats   │     │   Bookings    │
├───────────────┤     ├───────────────┤     ├───────────────┤
│ id            │     │ id            │     │ id            │
│ email         │     │ title         │     │ userId        │◄─┐
│ password      │     │ description   │     │ retreatId     │◄─┤
│ firstName     │     │ price         │     │ startDate     │  │
│ lastName      │     │ location      │     │ endDate       │  │
│ roles         │     │ maxCapacity   │     │ status        │  │
│ ...           │     │ amenities     │     │ totalPrice    │  │
└───────┬───────┘     │ ...           │     │ ...           │  │
        │             └───────┬───────┘     └───────────────┘  │
        │                     │                                 │
        └─────────────────────┼─────────────────────────────┐  │
                              │                             │  │
                              │                             │  │
┌───────────────┐     ┌───────▼───────┐     ┌───────────────┼──┘
│    Reviews    │     │   Locations   │     │     Users     │
├───────────────┤     ├───────────────┤     └───────────────┘
│ id            │     │ id            │
│ userId        │◄────┤ name          │
│ retreatId     │◄────┤ address       │
│ rating        │     │ city          │
│ comment       │     │ country       │
│ createdAt     │     │ coordinates   │
└───────────────┘     │ ...           │
                      └───────────────┘
```

### Gestion des migrations

Nous utilisons TypeORM pour gérer les migrations de base de données. Pour créer une nouvelle migration après avoir modifié une entité :

```bash
# Générer une migration
npm run migration:generate -- -n NomDeLaMigration

# Exécuter les migrations
npm run migration:run

# Annuler la dernière migration
npm run migration:revert
```

### Bonnes pratiques

- Utilisez toujours des migrations pour les changements de schéma
- Incluez des fonctions `up()` et `down()` dans les migrations
- Testez les migrations dans un environnement de test avant de les appliquer en production
- Préférez des index pour optimiser les requêtes fréquentes
- Vérifiez la documentation [migration-strategy.md](./migration-strategy.md) pour plus de détails

## API et endpoints

### Documentation OpenAPI

La documentation complète de l'API est disponible via Swagger à l'adresse `/api/docs` lorsque le serveur est en cours d'exécution. Elle contient tous les endpoints, paramètres, corps de requête et réponses.

### Points d'entrée principaux

- **Authentication**
  - `POST /auth/login` - Connexion utilisateur
  - `POST /auth/register` - Inscription utilisateur
  - `POST /auth/refresh-token` - Rafraîchir le token JWT

- **Utilisateurs**
  - `GET /users/profile` - Obtenir le profil utilisateur
  - `PATCH /users/profile` - Mettre à jour le profil
  
- **Retraites**
  - `GET /retreats` - Lister toutes les retraites avec filtres
  - `GET /retreats/:id` - Obtenir une retraite spécifique
  - `POST /retreats` - Créer une nouvelle retraite (admin)
  - `PUT /retreats/:id` - Mettre à jour une retraite (admin)
  - `DELETE /retreats/:id` - Supprimer une retraite (admin)
  
- **Réservations**
  - `GET /bookings` - Lister les réservations de l'utilisateur
  - `GET /bookings/:id` - Obtenir une réservation spécifique
  - `POST /bookings` - Créer une nouvelle réservation
  - `PATCH /bookings/:id/cancel` - Annuler une réservation

- **Avis**
  - `GET /retreats/:id/reviews` - Obtenir les avis pour une retraite
  - `POST /retreats/:id/reviews` - Ajouter un avis
  
- **Recherche**
  - `GET /search` - Recherche globale

Consultez la documentation Swagger pour une liste complète des endpoints.

## Authentication et sécurité

### Mécanisme d'authentification

RB2 utilise JWT (JSON Web Tokens) pour l'authentification :

1. L'utilisateur s'authentifie avec email/mot de passe via `/auth/login`
2. Le serveur retourne un access token (durée courte) et un refresh token (durée longue)
3. L'access token est inclus dans l'en-tête Authorization pour les requêtes API
4. Quand l'access token expire, le refresh token peut être utilisé pour en obtenir un nouveau

### Autorisations

Les contrôles d'accès sont gérés par des guards NestJS basés sur les rôles :

- `USER` - Utilisateur standard
- `HOST` - Hôte pouvant gérer des retraites
- `ADMIN` - Administrateur avec tous les droits

### Sécurité des données

- Les mots de passe sont hachés avec bcrypt
- Les données sensibles sont cryptées en base de données
- Des validations strictes sont appliquées aux entrées utilisateur via class-validator
- Protection CSRF pour les formulaires

## Tests

### Types de tests

Le projet comprend trois niveaux de tests :

1. **Tests unitaires** - Tester les fonctions et classes individuellement
2. **Tests d'intégration** - Tester l'interaction entre différentes parties du système
3. **Tests end-to-end** - Tester le système complet de bout en bout

### Exécution des tests

```bash
# Tests unitaires
npm run test

# Coverage des tests unitaires
npm run test:cov

# Tests d'intégration (plus lents)
npm run test:integration

# Tests end-to-end
npm run test:e2e

# Tests de charge
npm run test:load
```

### Écriture de tests

Voir la section sur les tests dans le document [usage-patterns.md](./usage-patterns.md) pour les patterns à suivre lors de l'écriture de nouveaux tests.

## Déploiement

### Environnements

Le projet dispose de plusieurs environnements :

- **local** - Environnement de développement local
- **dev** - Environnement d'intégration continue
- **staging** - Environnement de pré-production
- **production** - Environnement de production

### CI/CD

Nous utilisons GitHub Actions pour l'intégration et le déploiement continus. Le workflow est le suivant :

1. Les pull requests déclenchent des builds et des tests
2. La fusion dans la branche `develop` déploie automatiquement vers l'environnement `dev`
3. La fusion dans la branche `staging` déploie vers l'environnement `staging`
4. La création d'une nouvelle version (tag) déclenche un déploiement en production

### Configuration d'environnement

Les variables d'environnement sont gérées différemment selon l'environnement :

- **local** - Fichier `.env`
- **dev/staging/production** - Variables d'environnement stockées dans AWS Parameter Store

## Monitoring et logging

### Logging

Le système de logging est structuré et centralisé :

- Les logs sont formatés en JSON pour une analyse facile
- Les niveaux de log (debug, info, warn, error) sont utilisés de façon cohérente
- Les logs sont envoyés à Elasticsearch pour une recherche et une visualisation centralisées via Kibana

### Monitoring des performances

Le monitoring des performances est assuré par plusieurs composants :

- Intercepteurs NestJS pour mesurer les temps de réponse API
- Métriques système (CPU, mémoire, etc.)
- Dashboard en temps réel accessible via `/admin/monitoring/dashboard`
- Alertes automatiques en cas de dégradation des performances

Consultez le document [monitoring-strategy.md](./monitoring-strategy.md) pour plus de détails.

### Gestion des erreurs

Le système avancé de monitoring des erreurs permet :

- Capture et classification des erreurs
- Enrichissement avec contexte (utilisateur, requête, etc.)
- Alertes pour les erreurs critiques
- Analyse des tendances d'erreurs

## Guides pratiques

### Ajouter une nouvelle entité

1. Créer l'entité dans le dossier `entities` du module concerné :
   ```typescript
   @Entity()
   export class NewEntity {
     @PrimaryGeneratedColumn('uuid')
     id: string;
     
     @Column()
     name: string;
     
     // Autres propriétés...
   }
   ```

2. Créer les DTOs correspondants dans le dossier `dto` :
   ```typescript
   export class CreateNewEntityDto {
     @IsString()
     @IsNotEmpty()
     name: string;
   }
   
   export class UpdateNewEntityDto {
     @IsString()
     @IsOptional()
     name?: string;
   }
   ```

3. Ajouter l'entité aux imports du module :
   ```typescript
   @Module({
     imports: [
       TypeOrmModule.forFeature([NewEntity]),
     ],
   })
   ```

4. Générer une migration :
   ```bash
   npm run migration:generate -- -n AddNewEntity
   ```

5. Créer ou mettre à jour le service et le contrôleur

### Ajouter un nouvel endpoint

1. Identifier le contrôleur approprié ou en créer un nouveau
2. Ajouter la méthode avec les décorateurs NestJS :
   ```typescript
   @Get('example/:id')
   @ApiOperation({ summary: 'Get example by ID' })
   @ApiResponse({ status: 200, description: 'Success', type: ExampleDto })
   @ApiResponse({ status: 404, description: 'Not found' })
   async getExampleById(@Param('id') id: string): Promise<ExampleDto> {
     const example = await this.exampleService.findById(id);
     if (!example) {
       throw new NotFoundException('Example not found');
     }
     return this.mapper.toDto(example);
   }
   ```

3. Implémenter la logique métier dans le service associé
4. Ajouter des tests unitaires et d'intégration

### Débugger l'application

1. Pour le debugging local avec VS Code :
   ```bash
   npm run start:debug
   ```

2. Utilisez l'inspecteur Chrome ou l'extension VS Code Node Debugger
3. Exploitez les logs de développement avec le niveau DEBUG :
   ```bash
   LOG_LEVEL=debug npm run start:dev
   ```

## Ressources et documentation

### Documentation interne

- [Patterns d'utilisation](./usage-patterns.md) - Patterns et bonnes pratiques pour le développement
- [Stratégie de migration](./migration-strategy.md) - Guide pour la gestion des migrations de base de données
- [Stratégie de monitoring](./monitoring-strategy.md) - Documentation sur le monitoring et le logging
- [API Swagger](http://localhost:3000/api/docs) - Documentation OpenAPI des endpoints (en local)

### Ressources externes

- [Documentation NestJS](https://docs.nestjs.com/) - Documentation officielle du framework NestJS
- [Documentation TypeORM](https://typeorm.io/) - Documentation de l'ORM utilisé
- [Spécification OpenAPI](https://swagger.io/specification/) - Spécification pour la documentation API

## Glossaire

- **DTO (Data Transfer Object)** - Objet utilisé pour transférer des données entre couches de l'application
- **Entity** - Classe qui représente une table dans la base de données
- **Guard** - Middleware NestJS pour l'autorisation
- **Interceptor** - Middleware NestJS pour transformer la requête/réponse
- **Pipe** - Middleware NestJS pour la validation/transformation
- **Repository** - Classe qui gère l'accès à la base de données pour une entité spécifique
- **Service** - Classe qui contient la logique métier de l'application

---

*Dernière mise à jour: 18 mai 2025*
*Contact: <EMAIL>* 