# Plan d'Action Détaillé : Documentation Disaster Recovery

## Vue d'ensemble
Ce document présente un plan d'action complet pour la mise en place et la documentation des procédures de disaster recovery pour nos systèmes et services. L'objectif est de garantir la continuité des opérations en cas d'incidents majeurs, de défaillances techniques ou de catastrophes naturelles.

## Durée totale estimée : 2 semaines

## Phase 1 : Documentation des Procédures (1 semaine)

### 1.1 Procédures de Backup

#### Stratégie de sauvegarde
```
# Stratégie de sauvegarde

## Fréquence des sauvegardes
- Sauvegardes automatiques quotidiennes à 2h00 UTC
- Sauvegardes complètes hebdomadaires le dimanche à 3h00 UTC
- Sauvegardes incrémentielles quotidiennes les autres jours
- Archivage mensuel en stockage froid le 1er de chaque mois

## Périmètre des sauvegardes
- Bases de données (PostgreSQL, MongoDB)
- Système de fichiers applicatifs
- Configurations des serveurs
- Images de conteneurs
- Fichiers utilisateurs
- Logs système et applicatifs critiques

## Rétention des sauvegardes
- Sauvegardes quotidiennes : conservation 14 jours
- Sauvegardes hebdomadaires : conservation 2 mois
- Sauvegardes mensuelles : conservation 1 an
- Sauvegardes annuelles : conservation 7 ans

## Technologies utilisées
- Bases de données : pg_dump, mongodump
- Fichiers : rsync, tar
- Stockage : Amazon S3 (standard + Glacier pour archivage long terme)
- Conteneurs : Images Docker sauvegardées dans ECR/Docker Hub privé
```

#### Scripts de sauvegarde
```bash
#!/bin/bash
# backup-database.sh
# Script de sauvegarde des bases de données

# Configuration
DATE=$(date +%Y-%m-%d)
BACKUP_DIR="/mnt/backups/databases/$DATE"
LOG_FILE="/var/log/backups/db-backup-$DATE.log"
POSTGRES_USER="backup_user"
POSTGRES_DB="production_db"
MONGO_URI="mongodb://backup:<EMAIL>:27017/admin"

# Création du répertoire de sauvegarde
mkdir -p $BACKUP_DIR
echo "Démarrage de la sauvegarde à $(date)" >> $LOG_FILE

# Sauvegarde PostgreSQL
echo "Sauvegarde PostgreSQL..." >> $LOG_FILE
pg_dump -U $POSTGRES_USER -d $POSTGRES_DB -F c -f $BACKUP_DIR/postgres-$DATE.dump
if [ $? -eq 0 ]; then
    echo "Sauvegarde PostgreSQL réussie." >> $LOG_FILE
else
    echo "ERREUR: Échec de la sauvegarde PostgreSQL." >> $LOG_FILE
    # Notification d'échec
    /usr/local/bin/notify-team "Échec de la sauvegarde PostgreSQL le $DATE"
    exit 1
fi

# Sauvegarde MongoDB
echo "Sauvegarde MongoDB..." >> $LOG_FILE
mongodump --uri="$MONGO_URI" --out=$BACKUP_DIR/mongodb
if [ $? -eq 0 ]; then
    echo "Sauvegarde MongoDB réussie." >> $LOG_FILE
else
    echo "ERREUR: Échec de la sauvegarde MongoDB." >> $LOG_FILE
    # Notification d'échec
    /usr/local/bin/notify-team "Échec de la sauvegarde MongoDB le $DATE"
    exit 1
fi

# Compression des sauvegardes
echo "Compression des sauvegardes..." >> $LOG_FILE
tar -czf $BACKUP_DIR/../db-backup-$DATE.tar.gz $BACKUP_DIR
if [ $? -eq 0 ]; then
    echo "Compression réussie." >> $LOG_FILE
    rm -rf $BACKUP_DIR
else
    echo "ERREUR: Échec de la compression." >> $LOG_FILE
    exit 1
fi

# Transfert vers le stockage distant (S3)
echo "Transfert vers S3..." >> $LOG_FILE
aws s3 cp $BACKUP_DIR/../db-backup-$DATE.tar.gz s3://company-backups/databases/
if [ $? -eq 0 ]; then
    echo "Transfert vers S3 réussi." >> $LOG_FILE
else
    echo "ERREUR: Échec du transfert vers S3." >> $LOG_FILE
    /usr/local/bin/notify-team "Échec du transfert de la sauvegarde vers S3 le $DATE"
    exit 1
fi

echo "Sauvegarde terminée avec succès à $(date)" >> $LOG_FILE
```

#### Monitoring et vérification des sauvegardes
```
# Procédure de vérification des sauvegardes

## Vérification quotidienne automatisée
- Exécution du script verify-backups.sh à 6h00 UTC
- Vérification de l'intégrité des fichiers (checksums)
- Test de restauration sur environnement de test
- Génération de rapport de vérification

## Vérification manuelle hebdomadaire
- Responsable : Équipe DevOps
- Jour : Lundi
- Vérification visuelle des logs de sauvegarde
- Vérification de l'espace de stockage disponible
- Validation de l'accès aux données sauvegardées

## Procédure en cas d'échec de sauvegarde
1. Notification immédiate à l'équipe d'astreinte
2. Analyse de la cause de l'échec
3. Correction du problème
4. Exécution manuelle de la sauvegarde
5. Documentation de l'incident
6. Mise à jour des procédures si nécessaire
```

### 1.2 Procédures de Restauration

#### Restauration des bases de données
```
# Procédure de restauration des bases de données

## Prérequis
- Accès aux credentials de sauvegarde
- Accès au stockage S3
- Droits d'administration sur les bases de données cibles

## Étapes de restauration PostgreSQL
1. Identifier le fichier de sauvegarde à restaurer
   ```
   aws s3 ls s3://company-backups/databases/ | grep postgres
   ```

2. Télécharger le fichier de sauvegarde
   ```
   aws s3 cp s3://company-backups/databases/postgres-2023-05-15.dump /tmp/
   ```

3. Arrêter les services applicatifs dépendants
   ```
   kubectl scale deployment app-deployment --replicas=0
   ```

4. Restaurer la base de données
   ```
   pg_restore -U postgres -d production_db -c /tmp/postgres-2023-05-15.dump
   ```

5. Vérifier l'intégrité des données
   ```
   psql -U postgres -d production_db -c "SELECT count(*) FROM users;"
   ```

6. Redémarrer les services applicatifs
   ```
   kubectl scale deployment app-deployment --replicas=3
   ```

## Étapes de restauration MongoDB
1. Identifier le fichier de sauvegarde à restaurer
   ```
   aws s3 ls s3://company-backups/databases/ | grep mongodb
   ```

2. Télécharger et extraire le fichier de sauvegarde
   ```
   aws s3 cp s3://company-backups/databases/db-backup-2023-05-15.tar.gz /tmp/
   tar -xzf /tmp/db-backup-2023-05-15.tar.gz -C /tmp/
   ```

3. Arrêter les services applicatifs dépendants
   ```
   kubectl scale deployment app-deployment --replicas=0
   ```

4. Restaurer la base de données
   ```
   mongorestore --uri="mongodb://admin:<EMAIL>:27017/admin" --drop /tmp/mongodb
   ```

5. Vérifier l'intégrité des données
   ```
   mongo --uri="mongodb://admin:<EMAIL>:27017/admin" --eval "db.users.count()"
   ```

6. Redémarrer les services applicatifs
   ```
   kubectl scale deployment app-deployment --replicas=3
   ```

## Temps de restauration estimés
- PostgreSQL (<10 GB) : 10-15 minutes
- PostgreSQL (>10 GB) : 30-60 minutes
- MongoDB (<10 GB) : 10-15 minutes
- MongoDB (>10 GB) : 30-60 minutes
```

#### Restauration des applications
```
# Procédure de restauration des applications

## Prérequis
- Accès aux registres d'images Docker
- Accès aux fichiers de configuration Kubernetes/Docker Compose
- Accès aux dépôts Git
- Droits d'administration sur l'infrastructure

## Étapes de restauration des conteneurs
1. Redéployer les images depuis le registre
   ```
   kubectl apply -f k8s/deployment.yaml
   ```
   ou
   ```
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. Vérifier l'état des conteneurs
   ```
   kubectl get pods
   ```
   ou
   ```
   docker ps
   ```

3. Vérifier les logs des applications
   ```
   kubectl logs deployment/app-deployment
   ```
   ou
   ```
   docker logs app-container
   ```

## Étapes de restauration des configurations
1. Restaurer les fichiers de configuration depuis le dépôt Git
   ```
   git clone https://github.com/company/app-config.git /tmp/app-config
   ```

2. Appliquer les configurations
   ```
   kubectl apply -f /tmp/app-config/k8s/
   ```
   ou
   ```
   cp /tmp/app-config/env/.env.production /app/.env
   ```

3. Vérifier que les configurations sont correctement appliquées
   ```
   kubectl describe configmap app-config
   ```

## Étapes de restauration complète de l'application
1. Restaurer les bases de données (voir procédure précédente)
2. Restaurer les configurations
3. Redéployer les conteneurs
4. Vérifier les fonctionnalités clés de l'application
   - Connexion utilisateur
   - Création de données
   - Récupération de données
   - Opérations critiques métier

## Temps de restauration estimés
- Applications simples : 15-30 minutes
- Applications complexes : 1-2 heures
```

#### Restauration des serveurs
```
# Procédure de restauration des serveurs

## Prérequis
- Accès à la console de gestion de l'infrastructure (AWS, GCP, Azure, etc.)
- Accès aux images de sauvegarde des machines (AMI, Snapshots)
- Accès aux outils d'automatisation (Terraform, Ansible, etc.)

## Restauration via Infrastructure as Code (recommandé)
1. Accéder au dépôt Terraform
   ```
   git clone https://github.com/company/infrastructure.git
   cd infrastructure
   ```

2. Initialiser Terraform
   ```
   terraform init
   ```

3. Appliquer la configuration
   ```
   terraform apply -var-file=prod.tfvars
   ```

4. Vérifier le déploiement
   ```
   terraform output
   ```

## Restauration manuelle (alternative)
1. Créer une nouvelle instance à partir d'une AMI/Snapshot
   - AWS: Utiliser la console EC2 pour lancer une instance à partir de l'AMI
   - GCP: Utiliser la console Compute Engine pour créer une instance à partir d'un snapshot
   - Azure: Utiliser la console Virtual Machines pour créer une VM à partir d'un snapshot

2. Configurer les groupes de sécurité/pare-feu
   ```
   aws ec2 authorize-security-group-ingress --group-id sg-1234 --protocol tcp --port 22 --cidr 10.0.0.0/16
   ```

3. Attacher les volumes de données nécessaires
   ```
   aws ec2 attach-volume --volume-id vol-1234 --instance-id i-5678 --device /dev/sdf
   ```

4. Mettre à jour les entrées DNS
   ```
   aws route53 change-resource-record-sets --hosted-zone-id Z1234 --change-batch file://dns-update.json
   ```

## Temps de restauration estimés
- Via Infrastructure as Code : 30-45 minutes
- Restauration manuelle : 1-2 heures
```

### 1.3 Plans de Continuité d'Activité

#### Définition des RTO et RPO
```
# Objectifs de temps de reprise (RTO) et objectifs de point de reprise (RPO)

## Définitions
- RTO (Recovery Time Objective) : Durée maximale acceptable entre l'interruption du service et sa restauration
- RPO (Recovery Point Objective) : Quantité maximale acceptable de perte de données mesurée en temps

## Objectifs par service

| Service               | Criticité | RTO       | RPO       | Justification                                         |
|-----------------------|-----------|-----------|-----------|------------------------------------------------------|
| Site Web Principal    | Haute     | 1 heure   | 5 minutes | Service client-facing générant du revenu             |
| API Paiements         | Critique  | 30 min    | 0 min     | Transactions financières, zéro perte acceptable      |
| Base Utilisateurs     | Haute     | 2 heures  | 15 min    | Données critiques mais tolérance courte acceptable   |
| Système de Réservation| Haute     | 1 heure   | 5 minutes | Impact direct sur le revenu                          |
| CRM                   | Moyenne   | 4 heures  | 1 heure   | Système interne, impact limité à court terme         |
| Email Marketing       | Basse     | 8 heures  | 24 heures | Non critique pour les opérations quotidiennes        |
| Analytique            | Basse     | 12 heures | 24 heures | Système de reporting, non critique pour l'activité   |
| Environnements Dev    | Basse     | 24 heures | 24 heures | N'affecte pas les clients                            |

## Révision des objectifs
- Fréquence : Révision trimestrielle
- Responsable : Comité de Sécurité & Continuité
- Ajustement en fonction de l'évolution de l'activité et des technologies
```

#### Plan d'action en cas de désastre
```
# Plan d'action en cas de désastre majeur

## Équipe de gestion de crise
- Directeur Technique (Leader)
- Responsable Infrastructure
- Responsable Base de Données
- Responsable Développement
- Responsable Support Client
- Directeur des Opérations
- Responsable Communication

## Procédure de déclaration d'incident majeur
1. Détection de l'incident (automatique ou manuelle)
2. Évaluation initiale par l'équipe d'astreinte
3. Si impact sur RTO/RPO > 50% : Escalade au Directeur Technique
4. Directeur Technique évalue et déclare l'incident majeur si nécessaire
5. Activation du plan de continuité

## Processus de communication
1. Communication interne
   - Notification à l'équipe de gestion de crise via PagerDuty + appel téléphonique
   - Création d'un canal Slack dédié à l'incident (#incident-YYYYMMDD)
   - Mise en place d'une conférence téléphonique d'urgence
   - Briefing toutes les 30 minutes

2. Communication externe
   - Mise à jour de la page de statut (status.company.com)
   - Email aux clients impactés
   - Communication sur les réseaux sociaux
   - Communication aux partenaires stratégiques

## Procédure de restauration
1. Évaluation
   - Nature et étendue de l'incident
   - Systèmes impactés
   - Données perdues

2. Décision
   - Activation du site de secours
   - Restauration sur site principal
   - Restauration partielle

3. Exécution
   - Application des procédures de restauration appropriées
   - Test des systèmes restaurés
   - Validation fonctionnelle

4. Retour à la normale
   - Migration vers l'infrastructure principale
   - Validation complète du système
   - Clôture de l'incident

## Analyse post-incident
1. Réunion bilan dans les 48h après résolution
2. Documentation complète de l'incident
3. Analyse des causes profondes
4. Définition des actions préventives
5. Mise à jour des procédures de DR si nécessaire
```

#### Scénarios de désastre
```
# Scénarios de désastre et réponses planifiées

## Scénario 1 : Indisponibilité d'une zone AWS
### Impact
- Services dans la zone concernée indisponibles
- Latence potentielle pour les utilisateurs

### Réponse
1. Activer la bascule vers une autre zone de disponibilité
   ```
   aws autoscaling update-auto-scaling-group --auto-scaling-group-name app-asg --availability-zones us-east-1b us-east-1c
   ```
2. Mettre à jour les entrées DNS si nécessaire
3. Surveiller la performance des applications
4. Informer les utilisateurs via la page de statut

### Temps de reprise estimé : 10-15 minutes

## Scénario 2 : Corruption de base de données
### Impact
- Données incorrectes ou inaccessibles
- Fonctionnalités applicatives dégradées ou indisponibles

### Réponse
1. Isoler la base de données corrompue
   ```
   kubectl scale deployment app-deployment --replicas=0
   ```
2. Identifier le dernier point de sauvegarde viable
3. Restaurer la base de données depuis la sauvegarde
   ```
   pg_restore -U postgres -d production_db -c /path/to/backup.dump
   ```
4. Appliquer les transactions depuis les journaux de transactions (si disponibles)
5. Valider l'intégrité des données
6. Redémarrer les services applicatifs

### Temps de reprise estimé : 30-90 minutes selon la taille de la base

## Scénario 3 : Attaque par déni de service (DDoS)
### Impact
- Services web ralentis ou inaccessibles
- Augmentation des coûts d'infrastructure

### Réponse
1. Activer les protections DDoS avancées
   ```
   aws shield-advanced update-protection --protection-id protection-5678 --enabled
   ```
2. Augmenter les capacités d'absorption
   ```
   kubectl scale deployment app-deployment --replicas=10
   ```
3. Configurer des règles de filtrage au niveau du WAF
   ```
   aws wafv2 update-web-acl --web-acl-id acl-1234 --scope REGIONAL --updates file://waf-rules-ddos.json
   ```
4. Contacter le fournisseur de services cloud pour assistance
5. Analyser les patterns d'attaque pour ajustement

### Temps de reprise estimé : 15-60 minutes

## Scénario 4 : Erreur humaine (suppression accidentelle)
### Impact
- Perte de données ou de configuration
- Services partiellement ou totalement indisponibles

### Réponse
1. Identifier les éléments supprimés
2. Isoler les systèmes affectés pour éviter la propagation
3. Restaurer à partir des sauvegardes
   ```
   aws s3 cp s3://company-backups/configs/app-config-latest.tar.gz /tmp/
   tar -xzf /tmp/app-config-latest.tar.gz -C /etc/app/
   ```
4. Reconfigurer les services si nécessaire
5. Tester avant remise en production
6. Documenter l'incident pour formation

### Temps de reprise estimé : Variable selon l'étendue (30 min - 4 heures)
```

## Phase 2 : Tests et Validation (1 semaine)

### 2.1 Simulation de scénarios de désastre
```
# Plan de tests de disaster recovery

## Calendrier des tests
- Test complet : Annuel (Q1)
- Tests partiels : Trimestriels
- Tests de procédures individuelles : Mensuels

## Types de tests

### 1. Test de table (simulation théorique)
- Fréquence : Trimestrielle
- Participants : Équipe IT complète
- Description : Discussion guidée des procédures sans impact sur les systèmes
- Objectifs : Vérifier la connaissance des procédures, identifier les améliorations

### 2. Test de procédure (test technique limité)
- Fréquence : Mensuelle
- Participants : Équipe technique concernée
- Description : Test d'une procédure spécifique dans un environnement isolé
- Objectifs : Valider une procédure spécifique, former les équipes

### 3. Test partiel (simulation technique)
- Fréquence : Trimestrielle
- Participants : Équipe IT + représentants métier
- Description : Simulation d'un scénario spécifique dans un environnement de test
- Objectifs : Valider les procédures de restauration, mesurer les temps de reprise

### 4. Test complet (simulation réelle)
- Fréquence : Annuelle
- Participants : Toute l'entreprise
- Description : Simulation complète d'un désastre majeur
- Objectifs : Valider l'ensemble du plan de DR, mesurer le RTO et RPO réel

## Procédure de test

### Préparation
1. Définir les objectifs spécifiques du test
2. Identifier les systèmes concernés
3. Communiquer à l'avance aux équipes concernées
4. Préparer les métriques de mesure

### Exécution
1. Déclarer le début du test
2. Simuler l'incident selon le scénario prédéfini
3. Chronométrer le temps de réaction et de restauration
4. Noter les écarts par rapport aux procédures
5. Capturer les métriques pertinentes

### Évaluation
1. Mesurer le RTO et RPO réels
2. Comparer avec les objectifs
3. Identifier les points forts et faibles
4. Documenter les leçons apprises

### Amélioration
1. Mettre à jour les procédures en fonction des résultats
2. Former les équipes sur les points d'amélioration
3. Planifier les prochains tests avec les ajustements
```

### 2.2 Tests de restauration
```
# Procédure de test de restauration

## Test de restauration de base de données

### Prérequis
- Environnement de test isolé
- Copie récente des sauvegardes de production
- Accès aux outils de restauration

### Étapes
1. Sélectionner une sauvegarde récente
   ```
   aws s3 ls s3://company-backups/databases/ | tail -5
   ```

2. Télécharger la sauvegarde dans l'environnement de test
   ```
   aws s3 cp s3://company-backups/databases/db-backup-latest.tar.gz /tmp/
   ```

3. Préparer l'environnement de test
   ```
   docker-compose -f docker-compose.test.yml up -d db
   ```

4. Exécuter la procédure de restauration
   ```
   ./scripts/restore-db.sh --env=test --backup=/tmp/db-backup-latest.tar.gz
   ```

5. Vérifier l'intégrité des données
   ```
   psql -U postgres -h localhost -p 5433 -d test_db -c "SELECT COUNT(*) FROM users;"
   ```

6. Mesurer le temps de restauration
   ```
   echo "Temps de restauration: $SECONDS secondes"
   ```

7. Documenter les résultats
   ```
   ./scripts/log-test-results.sh --test=db-restore --time=$SECONDS --status=$?
   ```

## Test de restauration d'application

### Prérequis
- Environnement de test isolé
- Accès aux images Docker et configurations

### Étapes
1. Simuler une panne application
   ```
   docker-compose -f docker-compose.test.yml stop app
   kubectl scale deployment app-test --replicas=0
   ```

2. Exécuter la procédure de restauration
   ```
   ./scripts/restore-app.sh --env=test
   ```

3. Vérifier le bon fonctionnement
   ```
   curl -s http://app-test.example.com/health | jq .status
   ```

4. Mesurer le temps de restauration
   ```
   echo "Temps de restauration: $SECONDS secondes"
   ```

5. Documenter les résultats
   ```
   ./scripts/log-test-results.sh --test=app-restore --time=$SECONDS --status=$?
   ```

## Test de restauration d'infrastructure complète

### Prérequis
- Environnement de test isolé
- Infrastructure as Code configurée pour l'environnement de test

### Étapes
1. Simuler une panne majeure
   ```
   terraform destroy -target=module.app_cluster -var-file=test.tfvars
   ```

2. Exécuter la procédure de restauration
   ```
   ./scripts/restore-infrastructure.sh --env=test
   ```

3. Vérifier le bon fonctionnement de l'infrastructure
   ```
   ./scripts/verify-infrastructure.sh --env=test
   ```

4. Mesurer le temps de restauration
   ```
   echo "Temps de restauration: $SECONDS secondes"
   ```

5. Documenter les résultats
   ```
   ./scripts/log-test-results.sh --test=infra-restore --time=$SECONDS --status=$?
   ```

## Critères de succès
- Temps de restauration inférieur au RTO défini
- Perte de données inférieure au RPO défini
- 100% des fonctionnalités critiques restaurées
- Documentation claire des étapes effectuées
```

### 2.3 Formation des équipes
```
# Plan de formation Disaster Recovery

## Objectifs de formation
- Comprendre les concepts de base du DR
- Maîtriser les procédures de backup et restauration
- Savoir réagir efficacement en cas d'incident
- Connaître son rôle dans le plan de continuité

## Modules de formation

### Module 1 : Fondamentaux du Disaster Recovery
- Durée : 2 heures
- Public : Tous les employés
- Contenu :
  * Concepts de RTO et RPO
  * Types de désastres potentiels
  * Structure du plan de DR
  * Chaîne de communication en cas d'incident
  * Rôles et responsabilités

### Module 2 : Procédures techniques
- Durée : 4 heures
- Public : Équipe technique
- Contenu :
  * Systèmes de sauvegarde et surveillance
  * Procédures de restauration par type de système
  * Utilisation des outils de DR
  * Documentation technique et journalisation
  * Tests de restauration

### Module 3 : Gestion de crise
- Durée : 3 heures
- Public : Management et équipe de crise
- Contenu :
  * Prise de décision en situation de crise
  * Communication interne et externe
  * Gestion des ressources pendant un incident
  * Coordination des équipes
  * Analyse post-incident

### Module 4 : Exercices pratiques
- Durée : Journée complète
- Public : Équipes techniques et de gestion
- Contenu :
  * Simulation d'incidents réels
  * Exercices de restauration
  * Jeux de rôle
  * Révision des procédures

## Calendrier de formation
- Formation initiale complète : À l'intégration
- Recyclage complet : Annuel
- Modules spécifiques : Trimestriels
- Sessions après mises à jour majeures des procédures

## Méthodes d'évaluation
- QCM de validation des connaissances
- Exercices pratiques notés
- Participation aux simulations de DR
- Évaluation par les pairs

## Ressources pédagogiques
- Manuel de procédures DR (version digitale et papier)
- Vidéos tutorielles
- Wiki technique
- Fiches de synthèse par rôle
```

## Métriques de succès

### Objectifs RTO/RPO
- RTO (Recovery Time Objective) < 4 heures pour les services critiques
- RPO (Recovery Point Objective) < 1 heure pour les services critiques

### Efficacité des tests
- 100% des scénarios critiques testés au moins une fois par an
- 90% de taux de succès lors des tests de disaster recovery
- Temps de restauration réel < RTO défini

### Formation des équipes
- 100% du personnel technique formé aux procédures
- 90% de taux de réussite aux évaluations post-formation
- Temps de réaction < 15 minutes pour les incidents critiques

### Documentation
- Documentation complète à 100%
- Révision trimestrielle des procédures
- Accessibilité 24/7 de la documentation (formats numérique et physique)
