# Stratégie de Migration de Données - Projet RB2

## Introduction

Ce document décrit la stratégie et les outils de migration de données pour le projet RB2. Le système a été conçu pour permettre des modifications structurées et contrôlées de la base de données, tout en conservant l'historique des changements et en permettant des opérations de rollback si nécessaire.

## Architecture du Système de Migration

Le système de migration est basé sur une architecture modulaire qui comprend plusieurs composants clés :

1. **MigrationService** : Service central qui gère l'enregistrement, l'exécution et le suivi des migrations.
2. **MigrationEntity** : Entité qui représente une migration dans la base de données, avec son statut et son historique.
3. **MigrationModule** : Module NestJS qui encapsule les fonctionnalités de migration et facilite leur intégration.
4. **Utilitaires de Migration** : Fonctions et classes utilitaires pour simplifier la création et la gestion des migrations.
5. **CLI de Migration** : Interface en ligne de commande pour les opérations de migration courantes.
6. **API de Migration** : Points d'accès RESTful pour la gestion des migrations via HTTP.

## Cycle de Vie d'une Migration

1. **Création** : Une migration est définie avec des méthodes `up` (application) et `down` (rollback).
2. **Enregistrement** : La migration est enregistrée dans le système via le MigrationModule.
3. **Exécution** : La migration est appliquée lors du démarrage de l'application ou via une commande explicite.
4. **Suivi** : L'état de la migration est enregistré dans la table `migrations`.
5. **Rollback** (si nécessaire) : La migration peut être annulée via sa méthode `down`.

## Types de Migrations

### 1. Migrations Structurelles

Ces migrations concernent la structure de la base de données :
- Création/suppression de tables
- Ajout/suppression/modification de colonnes
- Création/suppression d'index, contraintes, clés étrangères

### 2. Migrations de Données

Ces migrations concernent les données elles-mêmes :
- Migration de données d'une structure à une autre
- Nettoyage ou correction de données
- Insertion de données initiales ou de référence

### 3. Migrations Mixtes

Combinaison de modifications structurelles et de données, généralement utilisées pour :
- Réorganisation complète d'une partie du schéma
- Modifications qui nécessitent des étapes intermédiaires

## Comment Créer une Migration

### 1. Utilisation de la Factory

```typescript
import { createMigration } from '../migration.factory';
import { EntityManager } from 'typeorm';

export const MaNouvelleColonneMigration = createMigration(
  '20230101000000_ma_nouvelle_colonne',
  'Ajoute une nouvelle colonne à la table utilisateurs',
  // Fonction up
  async (manager: EntityManager) => {
    await manager.query(`ALTER TABLE users ADD COLUMN new_column VARCHAR(255)`);
  },
  // Fonction down
  async (manager: EntityManager) => {
    await manager.query(`ALTER TABLE users DROP COLUMN new_column`);
  },
  { transactional: true }
);
```

### 2. Utilisation de la CLI

```bash
# Créer une nouvelle migration
node src/database/migrations/cli/migration-cli.js create ma_nouvelle_colonne

# Résultat : création d'un fichier modèle dans le dossier des migrations
```

## Exécution des Migrations

### 1. Exécution Automatique

Les migrations peuvent s'exécuter automatiquement au démarrage de l'application si l'option `autoRun` est activée :

```typescript
// Dans app.module.ts
MigrationModule.forRoot({
  autoRun: true,
  transactional: true
})
```

### 2. Exécution Manuelle via CLI

```bash
# Exécuter toutes les migrations en attente
node src/database/migrations/cli/migration-cli.js run

# Vérifier le statut des migrations
node src/database/migrations/cli/migration-cli.js status

# Annuler la dernière migration
node src/database/migrations/cli/migration-cli.js rollback
```

### 3. Exécution via API

```
POST /admin/migrations/run - Exécute toutes les migrations en attente
POST /admin/migrations/rollback/last - Annule la dernière migration
GET /admin/migrations/status - Obtient l'état des migrations
```

## Bonnes Pratiques

### 1. Structure des Migrations

- **Une seule responsabilité** : Chaque migration doit avoir un objectif clair et limité.
- **Idempotence** : Vérifiez si les opérations ont déjà été effectuées pour éviter les erreurs en cas de réexécution.
- **Atomicité** : Utilisez des transactions quand c'est possible pour garantir l'intégrité des données.

### 2. Nommage

- Utilisez des noms descriptifs pour les migrations.
- Incluez un préfixe numérique ou timestamp pour garantir l'ordre d'exécution.
- Exemple : `001_initial_schema`, `002_add_user_indexes`, `003_add_product_categories`.

### 3. Gestion des Erreurs

- Gérez proprement les erreurs dans les migrations.
- Assurez-vous que la fonction `down` est capable d'annuler correctement les changements.
- Testez les migrations, y compris les opérations de rollback.

### 4. Documentation

- Documentez le but de chaque migration dans sa description.
- Pour les migrations complexes, incluez des commentaires expliquant les étapes clés.
- Maintenez un journal des migrations appliquées en production.

## Exemples Concrets

### Exemple 1 : Création du Schéma Initial

```typescript
// src/database/migrations/examples/001-initial-schema.migration.ts
// Crée les tables users, products, orders, et order_items avec leurs relations
```

### Exemple 2 : Ajout d'Index

```typescript
// src/database/migrations/examples/002-add-indexes.migration.ts
// Ajoute des index pour optimiser les requêtes fréquentes
```

## Scénarios de Migration Avancés

### 1. Modifications Non Réversibles

Certaines migrations, comme la suppression de données, peuvent ne pas être entièrement réversibles. Dans ces cas :
- Documentez clairement le caractère non réversible.
- Envisagez une stratégie de sauvegarde préalable des données concernées.
- Implantez une fonction `down` qui restaure au mieux l'état précédent.

### 2. Migrations de Grandes Quantités de Données

Pour les migrations impliquant de grandes quantités de données :
- Utilisez des lots (batching) pour éviter les dépassements de mémoire.
- Considérez la mise en place de migrations asynchrones ou en arrière-plan.
- Surveillez les performances et les ressources pendant l'exécution.

### 3. Déploiement en Production

Pour les déploiements en production :
- Testez d'abord les migrations dans un environnement de pré-production.
- Planifiez les migrations pendant les périodes de faible trafic.
- Prévoyez un plan de rollback en cas de problème.
- Surveillez attentivement les logs et les métriques pendant et après la migration.

## Conclusion

Une stratégie de migration de données bien structurée est essentielle pour maintenir l'intégrité et la fiabilité de la base de données tout au long de l'évolution du projet. Le système mis en place pour RB2 offre la flexibilité, la traçabilité et la sécurité nécessaires pour gérer efficacement les changements de base de données, qu'ils soient planifiés ou urgents.

---

*Dernière mise à jour : 4 mai 2025*
*Contact : équipe<EMAIL>* 