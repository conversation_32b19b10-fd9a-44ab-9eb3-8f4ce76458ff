# Mise à jour du système de monitoring

## Résumé des changements

Le système de monitoring a été mis à jour pour utiliser les dernières versions des bibliothèques OpenTelemetry et Prometheus. Ces mises à jour apportent plusieurs améliorations :

1. **Utilisation des modules standardisés** : Remplacement des modules dépréciés (`@opentelemetry/exporter-prometheus`, `@opentelemetry/metrics`) par les modules officiels maintenus (`@opentelemetry/sdk-metrics`).
2. **Amélioration des performances** : Les nouvelles versions offrent de meilleures performances et une consommation de mémoire réduite.
3. **Sécurité renforcée** : Options de configuration supplémentaires pour renforcer la sécurité du service de métriques.
4. **API plus riche** : Support pour plus de types de métriques et d'options de configuration.

## Dépendances mises à jour

| Ancienne dépendance | Nouvelle dépendance | Version |
|---------------------|---------------------|---------|
| `@opentelemetry/exporter-prometheus` | `@opentelemetry/sdk-metrics` | 1.30.1 |
| `@opentelemetry/metrics` | `@opentelemetry/sdk-metrics` | 1.30.1 |
| `opentelemetry-node` (dépréciée) | Supprimée | - |
| `prom-client` | `prom-client` | 15.1.3 |

## Nouvelles fonctionnalités

### Méthode helper pour l'enregistrement des métriques

Une nouvelle méthode helper `recordRequest` a été ajoutée pour simplifier l'enregistrement des métriques de requêtes HTTP :

```typescript
const monitoring = initializeMonitoring();

// Enregistrer une requête
monitoring.recordRequest(
  200,             // Code de statut HTTP
  150,             // Durée en ms
  '/api/users'     // Chemin de la requête
);
```

### Nouvelles métriques

Les métriques suivantes sont désormais disponibles :

1. `http_requests_total` - Compteur total des requêtes HTTP
2. `http_response_time_seconds` - Histogramme des temps de réponse HTTP en secondes
3. `http_requests_active` - Compteur des requêtes HTTP actives
4. `http_errors_total` - Compteur total des erreurs HTTP

### Options de sécurité améliorées

Nouvelle option de configuration `preventServerStart` pour éviter le démarrage automatique du serveur Prometheus dans certains scénarios (utile pour les environnements conteneurisés ou avec reverse proxy) :

```typescript
// Dans les variables d'environnement
PROMETHEUS_PREVENT_SERVER_START=true
```

## Comment utiliser le module mis à jour

### Middleware Express

Exemple d'utilisation avec Express :

```typescript
import express from 'express';
import { initializeMonitoring, shutdownMonitoring } from "./config/monitoring';

const app = express();
const monitoring = initializeMonitoring();

// Middleware pour enregistrer les métriques
app.use((req, res, next) => {
  const startTime = Date.now();
  
  // Incrémenter le compteur de requêtes actives
  monitoring.activeRequestsCounter.add(1);
  
  // Capturer la fin de la requête
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const path = req.path;
    
    // Utiliser la méthode helper pour enregistrer toutes les métriques
    monitoring.recordRequest(res.statusCode, duration, path);
    
    // Décrémenter le compteur de requêtes actives
    monitoring.activeRequestsCounter.add(-1);
  });
  
  next();
});

// Arrêt propre
process.on('SIGTERM', async () => {
  await shutdownMonitoring();
  // Autres opérations de nettoyage...
  process.exit(0);
});
```

### Configuration avec Nest.js

Pour NestJS, vous pouvez créer un service et un middleware :

```typescript
// monitoring.service.ts
import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { initializeMonitoring, shutdownMonitoring } from '../config/monitoring';

@Injectable()
export class MonitoringService implements OnModuleInit, OnModuleDestroy {
  private monitoring;

  onModuleInit() {
    this.monitoring = initializeMonitoring();
    return this.monitoring;
  }

  async onModuleDestroy() {
    await shutdownMonitoring();
  }

  getMonitoring() {
    return this.monitoring;
  }
}

// monitoring.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { MonitoringService } from "./monitoring.service';

@Injectable()
export class MonitoringMiddleware implements NestMiddleware {
  constructor(private monitoringService: MonitoringService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const monitoring = this.monitoringService.getMonitoring();
    const startTime = Date.now();
    
    monitoring.activeRequestsCounter.add(1);
    
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const path = req.path;
      
      monitoring.recordRequest(res.statusCode, duration, path);
      monitoring.activeRequestsCounter.add(-1);
    });
    
    next();
  }
}
```

## Tests

Les tests unitaires, d'intégration, de performance et de sécurité ont été mis à jour pour fonctionner avec les nouvelles dépendances. 

Pour exécuter les tests :

```bash
# Tests unitaires
npm test -- --testPathPattern=unit/config/monitoring

# Tests d'intégration
npm test -- --testPathPattern=integration/monitoring

# Tests de performance
npm test -- --testPathPattern=performance/monitoring

# Tests de sécurité
npm test -- --testPathPattern=security/monitoring
```

## Considérations sur la compatibilité

Cette mise à jour ne devrait pas entraîner de changements d'API majeurs pour les utilisateurs existants du module de monitoring. Les interfaces de base (`requestCounter`, `meter`) sont conservées pour maintenir la compatibilité arrière.

## Prochaines étapes

Pour une utilisation plus avancée, envisagez les options suivantes :

1. Ajouter d'autres exportateurs comme OTLP pour envoyer les métriques à des systèmes comme Jaeger ou Grafana Tempo
2. Configurer des alertes basées sur les métriques dans Prometheus/Grafana
3. Étendre le système pour collecter des métriques personnalisées spécifiques à l'application 