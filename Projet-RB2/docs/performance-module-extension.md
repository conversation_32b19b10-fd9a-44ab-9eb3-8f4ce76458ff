# Guide d'extension du module de performance

Ce guide explique comment étendre et personnaliser le module de performance du Backend-NestJS pour répondre à des besoins spécifiques.

## Table des matières

1. [Création de métriques personnalisées](#création-de-métriques-personnalisées)
2. [Création de sources d'alertes personnalisées](#création-de-sources-dalertes-personnalisées)
3. [Création de canaux de notification personnalisés](#création-de-canaux-de-notification-personnalisés)
4. [Création de types de panneaux personnalisés](#création-de-types-de-panneaux-personnalisés)
5. [Intégration avec d'autres modules](#intégration-avec-dautres-modules)
6. [Bonnes pratiques](#bonnes-pratiques)

## Création de métriques personnalisées

### Définition d'une métrique personnalisée

Pour créer une métrique personnalisée, vous devez définir :

1. Un nom unique pour la métrique
2. Une description de la métrique
3. Le type de métrique (GAUGE, COUNTER, HISTOGRAM, SUMMARY)
4. L'unité de mesure
5. Les tags associés à la métrique

Exemple :

```typescript
import { Injectable } from '@nestjs/common';
import { MetricsService } from '../modules/performance/services/metrics.service';
import { MetricType } from '../modules/performance/interfaces/metric.interface';

@Injectable()
export class YourCustomService {
  constructor(private readonly metricsService: MetricsService) {}

  async trackCustomMetric(value: number) {
    await this.metricsService.storeMetric({
      name: 'your_module.custom_metric',
      description: 'Your custom metric description',
      type: MetricType.GAUGE,
      value,
      unit: 'count',
      timestamp: new Date(),
      tags: {
        module: 'your_module',
        category: 'custom',
        environment: process.env.NODE_ENV || 'development',
      },
    });
  }
}
```

### Collecte automatique de métriques

Pour collecter automatiquement des métriques, vous pouvez créer un service qui implémente l'interface `OnModuleInit` et utilise le décorateur `@Cron` pour planifier la collecte :

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MetricsService } from '../modules/performance/services/metrics.service';
import { MetricType } from '../modules/performance/interfaces/metric.interface';

@Injectable()
export class YourCustomMetricsCollector implements OnModuleInit {
  constructor(
    private readonly metricsService: MetricsService,
    private readonly yourCustomService: YourCustomService,
  ) {}

  async onModuleInit() {
    // Collecter les métriques initiales
    await this.collectMetrics();
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async collectMetrics() {
    try {
      // Obtenir les données à mesurer
      const value = await this.yourCustomService.getSomeValue();

      // Stocker la métrique
      await this.metricsService.storeMetric({
        name: 'your_module.custom_metric',
        description: 'Your custom metric description',
        type: MetricType.GAUGE,
        value,
        unit: 'count',
        timestamp: new Date(),
        tags: {
          module: 'your_module',
          category: 'custom',
          environment: process.env.NODE_ENV || 'development',
        },
      });
    } catch (error) {
      console.error('Error collecting custom metrics:', error);
    }
  }
}
```

### Création d'un intercepteur pour collecter des métriques

Vous pouvez créer un intercepteur pour collecter automatiquement des métriques pour chaque requête HTTP :

```typescript
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { MetricsService } from '../modules/performance/services/metrics.service';
import { MetricType } from '../modules/performance/interfaces/metric.interface';

@Injectable()
export class YourCustomMetricsInterceptor implements NestInterceptor {
  constructor(private readonly metricsService: MetricsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;

    return next.handle().pipe(
      tap({
        next: (data: any) => {
          const response = context.switchToHttp().getResponse();
          const { statusCode } = response;
          const responseTime = Date.now() - now;

          // Stocker la métrique de temps de réponse
          this.metricsService.storeMetric({
            name: 'your_module.response_time',
            description: 'Response time for your module endpoints',
            type: MetricType.HISTOGRAM,
            value: responseTime,
            unit: 'ms',
            timestamp: new Date(),
            tags: {
              module: 'your_module',
              method,
              url,
              statusCode,
              environment: process.env.NODE_ENV || 'development',
            },
          });

          // Stocker la métrique de requête
          this.metricsService.storeMetric({
            name: 'your_module.request_count',
            description: 'Request count for your module endpoints',
            type: MetricType.COUNTER,
            value: 1,
            unit: 'count',
            timestamp: new Date(),
            tags: {
              module: 'your_module',
              method,
              url,
              statusCode,
              environment: process.env.NODE_ENV || 'development',
            },
          });
        },
        error: (error: any) => {
          const responseTime = Date.now() - now;

          // Stocker la métrique d'erreur
          this.metricsService.storeMetric({
            name: 'your_module.error_count',
            description: 'Error count for your module endpoints',
            type: MetricType.COUNTER,
            value: 1,
            unit: 'count',
            timestamp: new Date(),
            tags: {
              module: 'your_module',
              method,
              url,
              error: error.name,
              environment: process.env.NODE_ENV || 'development',
            },
          });
        },
      }),
    );
  }
}
```

## Création de sources d'alertes personnalisées

### Définition d'une source d'alerte personnalisée

Pour créer une source d'alerte personnalisée, vous devez étendre l'énumération `AlertSource` :

```typescript
// src/modules/your-module/interfaces/alert.interface.ts
import { AlertSource as BaseAlertSource } from '../../performance/interfaces/alert.interface';

export enum YourCustomAlertSource {
  YOUR_MODULE = 'your_module',
  // Autres sources personnalisées...
}

// Fusionner avec les sources de base
export type CustomAlertSource = BaseAlertSource | YourCustomAlertSource;
```

### Création de règles d'alerte personnalisées

Vous pouvez créer des règles d'alerte personnalisées pour vos métriques :

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common';
import { AlertService } from '../../performance/services/alert.service';
import { AlertSeverity, AlertCondition } from '../../performance/interfaces/alert.interface';
import { YourCustomAlertSource } from '../interfaces/alert.interface';

@Injectable()
export class YourCustomAlertRulesService implements OnModuleInit {
  constructor(private readonly alertService: AlertService) {}

  async onModuleInit() {
    // Créer des règles d'alerte personnalisées
    await this.createCustomAlertRules();
  }

  async createCustomAlertRules() {
    try {
      // Vérifier si la règle existe déjà
      const existingRules = await this.alertService.getAlertRules({
        metric: 'your_module.custom_metric',
      });

      if (existingRules.total === 0) {
        // Créer une règle d'alerte pour votre métrique personnalisée
        await this.alertService.createAlertRule({
          name: 'High Custom Metric',
          description: 'Alert when your custom metric exceeds threshold',
          severity: AlertSeverity.WARNING,
          source: YourCustomAlertSource.YOUR_MODULE as any, // Cast nécessaire
          metric: 'your_module.custom_metric',
          condition: AlertCondition.GREATER_THAN,
          threshold: 100,
          duration: 300,
          cooldown: 1800,
          tags: {
            module: 'your_module',
            category: 'custom',
            environment: process.env.NODE_ENV || 'development',
          },
        });
      }
    } catch (error) {
      console.error('Error creating custom alert rules:', error);
    }
  }
}
```

## Création de canaux de notification personnalisés

### Création d'un service de notification personnalisé

Pour créer un canal de notification personnalisé, vous devez étendre le service `NotificationService` :

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Alert } from '../../performance/interfaces/alert.interface';

@Injectable()
export class YourCustomNotificationService {
  private readonly logger = new Logger(YourCustomNotificationService.name);
  private readonly enabled: boolean;

  constructor(private readonly configService: ConfigService) {
    this.enabled = this.configService.get<boolean>('your_module.notifications.enabled', false);
  }

  async sendNotification(alert: Alert): Promise<boolean> {
    if (!this.enabled) {
      return false;
    }

    try {
      // Implémenter la logique d'envoi de notification
      // Par exemple, envoyer une notification à un service externe

      this.logger.log(`Sent custom notification for alert: ${alert.id}`);
      return true;
    } catch (error) {
      this.logger.error(`Error sending custom notification: ${error.message}`, error.stack);
      return false;
    }
  }
}
```

### Intégration avec le service de notification existant

Pour intégrer votre service de notification personnalisé avec le service de notification existant, vous pouvez utiliser les événements :

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { YourCustomNotificationService } from './your-custom-notification.service';
import { Alert } from '../../performance/interfaces/alert.interface';

@Injectable()
export class YourCustomNotificationListener implements OnModuleInit {
  constructor(
    private readonly yourCustomNotificationService: YourCustomNotificationService,
  ) {}

  onModuleInit() {
    // Initialisation si nécessaire
  }

  @OnEvent('alert.created')
  async handleAlertCreatedEvent(payload: { alertId: string; name: string; severity: string }) {
    // Récupérer l'alerte complète depuis la base de données
    const alert = await this.prismaService.alert.findUnique({
      where: { id: payload.alertId },
    });

    if (alert) {
      // Envoyer une notification personnalisée
      await this.yourCustomNotificationService.sendNotification(alert as Alert);
    }
  }
}
```

## Création de types de panneaux personnalisés

### Définition d'un type de panneau personnalisé

Pour créer un type de panneau personnalisé, vous devez étendre l'énumération `PanelType` :

```typescript
// src/modules/your-module/interfaces/dashboard.interface.ts
import { PanelType as BasePanelType } from '../../performance/dto/create-dashboard.dto';

export enum YourCustomPanelType {
  CUSTOM_PANEL = 'custom_panel',
  // Autres types de panneaux personnalisés...
}

// Fusionner avec les types de base
export type CustomPanelType = BasePanelType | YourCustomPanelType;
```

### Création d'un tableau de bord personnalisé

Vous pouvez créer un tableau de bord personnalisé avec vos propres types de panneaux :

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common';
import { DashboardService } from '../../performance/services/dashboard.service';
import { DashboardType, ChartType } from '../../performance/dto/create-dashboard.dto';
import { YourCustomPanelType } from '../interfaces/dashboard.interface';

@Injectable()
export class YourCustomDashboardService implements OnModuleInit {
  constructor(private readonly dashboardService: DashboardService) {}

  async onModuleInit() {
    // Créer un tableau de bord personnalisé
    await this.createCustomDashboard();
  }

  async createCustomDashboard() {
    try {
      // Vérifier si le tableau de bord existe déjà
      const existingDashboards = await this.dashboardService.getDashboards({
        type: 'your_module',
      });

      if (existingDashboards.total === 0) {
        // Créer un tableau de bord personnalisé
        await this.dashboardService.createDashboard({
          name: 'Your Module Dashboard',
          description: 'Dashboard for your module metrics',
          type: 'your_module' as DashboardType, // Cast nécessaire
          layout: {
            columns: 12,
            rows: 24,
            gridGap: 8,
          },
          panels: [
            {
              title: 'Your Custom Metric',
              type: 'chart' as any, // Type standard
              position: { x: 0, y: 0 },
              size: { width: 6, height: 8 },
              config: {
                chartType: ChartType.LINE,
                timeRange: { from: 'now-6h', to: 'now' },
                refreshInterval: 60,
                displayLegend: true,
              },
              metrics: ['your_module.custom_metric'],
            },
            {
              title: 'Your Custom Panel',
              type: YourCustomPanelType.CUSTOM_PANEL as any, // Type personnalisé
              position: { x: 6, y: 0 },
              size: { width: 6, height: 8 },
              config: {
                // Configuration spécifique à votre panneau personnalisé
                customOption1: 'value1',
                customOption2: 'value2',
              },
              metrics: ['your_module.custom_metric'],
            },
          ],
          isDefault: false,
          isPublic: true,
        }, 'system'); // ID du propriétaire (système)
      }
    } catch (error) {
      console.error('Error creating custom dashboard:', error);
    }
  }
}
```

## Intégration avec d'autres modules

### Intégration avec le module d'authentification

Vous pouvez intégrer le module de performance avec le module d'authentification pour collecter des métriques liées à l'authentification :

```typescript
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { MetricsService } from '../../performance/services/metrics.service';
import { MetricType } from '../../performance/interfaces/metric.interface';

@Injectable()
export class AuthMetricsService {
  constructor(private readonly metricsService: MetricsService) {}

  @OnEvent('user.login.success')
  async handleUserLoginSuccessEvent(payload: { userId: string }) {
    await this.metricsService.storeMetric({
      name: 'auth.login.success',
      description: 'Successful login attempts',
      type: MetricType.COUNTER,
      value: 1,
      unit: 'count',
      timestamp: new Date(),
      tags: {
        module: 'auth',
        userId: payload.userId,
        environment: process.env.NODE_ENV || 'development',
      },
    });
  }

  @OnEvent('user.login.failure')
  async handleUserLoginFailureEvent(payload: { username: string; reason: string }) {
    await this.metricsService.storeMetric({
      name: 'auth.login.failure',
      description: 'Failed login attempts',
      type: MetricType.COUNTER,
      value: 1,
      unit: 'count',
      timestamp: new Date(),
      tags: {
        module: 'auth',
        username: payload.username,
        reason: payload.reason,
        environment: process.env.NODE_ENV || 'development',
      },
    });
  }
}
```

### Intégration avec le module de base de données

Vous pouvez intégrer le module de performance avec le module de base de données pour collecter des métriques liées aux requêtes de base de données :

```typescript
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { MetricsService } from '../../performance/services/metrics.service';
import { MetricType } from '../../performance/interfaces/metric.interface';

@Injectable()
export class DatabaseMetricsService {
  constructor(private readonly metricsService: MetricsService) {}

  @OnEvent('prisma.query')
  async handlePrismaQueryEvent(payload: { model: string; action: string; duration: number }) {
    await this.metricsService.storeMetric({
      name: 'database.query.time',
      description: 'Database query execution time',
      type: MetricType.HISTOGRAM,
      value: payload.duration,
      unit: 'ms',
      timestamp: new Date(),
      tags: {
        module: 'database',
        model: payload.model,
        action: payload.action,
        environment: process.env.NODE_ENV || 'development',
      },
    });
  }
}
```

## Bonnes pratiques

### Nommage des métriques

Utilisez un schéma de nommage cohérent pour vos métriques :

- Utilisez des points (`.`) pour séparer les parties du nom
- Commencez par le nom de votre module
- Suivez avec la catégorie de la métrique
- Terminez par le nom spécifique de la métrique

Exemples :
- `your_module.request.count`
- `your_module.response.time`
- `your_module.error.count`

### Tags de métriques

Utilisez des tags pour ajouter des dimensions à vos métriques :

- Ajoutez toujours un tag `module` avec le nom de votre module
- Ajoutez un tag `environment` pour distinguer les environnements
- Ajoutez des tags spécifiques à votre métrique (ex: `userId`, `method`, `statusCode`)

### Seuils d'alerte

Définissez des seuils d'alerte appropriés :

- Basez-vous sur des données historiques pour définir des seuils réalistes
- Utilisez des durées appropriées pour éviter les faux positifs
- Utilisez des temps de refroidissement pour éviter les alertes répétitives

### Tableaux de bord

Organisez vos tableaux de bord de manière logique :

- Regroupez les métriques connexes
- Utilisez des titres descriptifs
- Ajoutez des descriptions aux panneaux
- Utilisez des couleurs cohérentes

### Performance

Optimisez la collecte de métriques pour minimiser l'impact sur les performances :

- Évitez de collecter trop de métriques
- Utilisez des tags plutôt que des noms de métriques distincts
- Utilisez des agrégations côté client avant de stocker les métriques
- Nettoyez régulièrement les anciennes métriques

### Sécurité

Sécurisez vos métriques et alertes :

- Ne stockez pas d'informations sensibles dans les métriques
- Limitez l'accès aux tableaux de bord
- Validez les entrées utilisateur
- Utilisez des canaux de notification sécurisés

## Conclusion

En suivant ce guide, vous pouvez étendre et personnaliser le module de performance du Backend-NestJS pour répondre à vos besoins spécifiques. N'hésitez pas à explorer les possibilités offertes par le module et à contribuer à son amélioration.
