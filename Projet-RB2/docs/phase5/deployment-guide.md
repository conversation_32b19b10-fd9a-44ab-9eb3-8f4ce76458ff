# Guide des Déploiements Automatisés

## Vue d'ensemble

Le système de déploiement automatisé assure des mises en production sécurisées et conformes aux normes.

## Processus de Déploiement

### 1. Préparation

- Vérifiez vos accès dans le [Portail Développeur](https://dev.retreatandbe.com)
- Assurez-vous que votre code est mergé sur `main`
- Vérifiez que tous les tests CI sont verts

### 2. Lancement du Déploiement

```bash
# Dans votre terminal
rb deploy start --env production
```

### 3. Surveillance

1. Ouvrez le tableau de bord : https://dashboard.retreatandbe.com/deployments
2. Suivez les métriques en temps réel
3. Vérifiez les logs dans la section "Deployment Logs"

### 4. Validation

- Vérifiez les points de contrôle automatiques
- Exécutez les tests de smoke manuels si nécessaire
- Confirmez le bon fonctionnement des intégrations

## Rollback

En cas de problème, exécutez :

```bash
rb deploy rollback --env production --version <version>
```