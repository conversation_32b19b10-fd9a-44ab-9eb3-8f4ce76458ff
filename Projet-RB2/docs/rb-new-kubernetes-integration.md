# R&B new Kubernetes Integration Guide

This document provides instructions for deploying the R&B new AI Layer to Kubernetes using Helm charts.

## Overview

The R&B new AI Layer is a collection of AI services that provide intelligence to the Retreat And Be platform. It consists of the following components:

1. **AI Orchestrator**: Central component that coordinates all AI services
2. **Recommender Engine**: Provides personalized recommendations
3. **Chatbot**: Conversational AI assistant
4. **Content Generator**: Generates content for various purposes
5. **Analytics Engine**: Analyzes data and provides insights
6. **Feature Flags**: Manages feature flags for AI capabilities
7. **Virtual Coach**: Provides virtual coaching services
8. **MLflow**: For experiment tracking and model management

## Prerequisites

- Kubernetes 1.19+
- Helm 3.2.0+
- PV provisioner support in the underlying infrastructure
- Access to container registry with R&B new images

## Deployment Options

### Option 1: Deploy as part of the main Retreat And Be platform

```bash
# Update dependencies
helm dependency update ./charts/retreatandbe

# Install the chart
helm install retreatandbe ./charts/retreatandbe
```

### Option 2: Deploy R&B new standalone

```bash
# Install the chart
helm install rb-new ./charts/rb-new
```

### Option 3: Deploy using the deployment script

```bash
# Deploy to default namespace
./scripts/deploy-rb-new.sh

# Deploy to specific namespace
./scripts/deploy-rb-new.sh --namespace rb-new

# Deploy with custom values file
./scripts/deploy-rb-new.sh --values ./charts/rb-new/values-dev.yaml

# Perform a dry run
./scripts/deploy-rb-new.sh --dry-run
```

## Environment-Specific Configurations

### Development Environment

```bash
helm install rb-new ./charts/rb-new -f ./charts/rb-new/values-dev.yaml
```

Key configurations:
- Lower resource requirements
- Debug logging enabled
- Local ingress configuration
- Smaller persistence volumes

### Production Environment

```bash
helm install rb-new ./charts/rb-new -f ./charts/rb-new/values-prod.yaml
```

Key configurations:
- Higher resource requirements
- Multiple replicas for high availability
- Autoscaling enabled
- TLS-secured ingress
- Larger persistence volumes

## Customizing the Deployment

You can customize the deployment by creating your own values file or modifying the existing ones. Here are some common customizations:

### Advanced Features

#### Monitoring with Prometheus

```yaml
monitoring:
  enabled: true
  serviceMonitor:
    additionalLabels:
      release: prometheus
    interval: 30s
    scrapeTimeout: 10s
```

#### Metrics Exporter

```yaml
metrics:
  enabled: true
  image:
    repository: prom/statsd-exporter
    tag: latest
  port: 9102
  scrapeInterval: "15s"
```

#### Network Policies

```yaml
networkPolicy:
  enabled: true
  allowIngressFromNamespace: true
  ingressNamespace: ingress-nginx
  allowExternalEgress: true
```

#### Pod Disruption Budget

```yaml
podDisruptionBudget:
  enabled: true
  minAvailable: 1
  # maxUnavailable: 1
```

#### Resource Quotas

```yaml
resourceQuota:
  enabled: true
  hard:
    limits.cpu: "16"
    limits.memory: "32Gi"
    requests.cpu: "8"
    requests.memory: "16Gi"
    requests.storage: "100Gi"
    pods: "30"
```

#### Advanced Autoscaling

```yaml
orchestrator:
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
    # Advanced behavior
    scaleDownStabilizationWindowSeconds: 300
    scaleDownPercentage: 10
    scaleUpStabilizationWindowSeconds: 0
    scaleUpPercentage: 100
    scaleUpPods: 4
```

#### Database Migrations

```yaml
migrations:
  enabled: true
  image:
    repository: rb-new-migrations
    tag: latest
  persistence:
    enabled: true
    size: 1Gi
```

#### Backup Configuration

```yaml
backup:
  enabled: true
  schedule: "0 1 * * *"  # Daily at 1 AM
  image:
    repository: postgres
    tag: "13-alpine"
  persistence:
    enabled: true
    size: 10Gi
  retentionDays: 7
```

#### Istio Integration

```yaml
istio:
  enabled: true
  hosts:
    - "rb-new.local"
    - "rb-new.retreatandbe.svc.cluster.local"
  gateways:
    - "istio-system/retreatandbe-gateway"
  mtls:
    enabled: true
```

#### Application Configuration

```yaml
config:
  logLevel: "INFO"
  models:
    recommender: "recommender_v1"
    content: "content_generator_v1"
  features:
    recommender: true
    chatbot: true
  timeouts:
    recommender: 30
    chatbot: 60
  kafka:
    bootstrapServers: "kafka:9092"
    topicPrefix: "retreatandbe"
```

### Changing Image Registry

```yaml
global:
  imageRegistry: "your-registry.example.com/"
```

### Adjusting Resource Limits

```yaml
orchestrator:
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 200m
      memory: 256Mi
```

### Configuring Ingress

```yaml
ingress:
  enabled: true
  hosts:
    - host: ai.your-domain.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: ai-tls
      hosts:
        - ai.your-domain.com
```

### Setting Environment Variables

```yaml
env:
  common:
    - name: ENVIRONMENT
      value: "production"
    - name: LOG_LEVEL
      value: "INFO"
  orchestrator:
    - name: CUSTOM_VARIABLE
      value: "custom-value"
```

## Monitoring and Maintenance

### Checking Deployment Status

```bash
# List all pods
kubectl get pods -n <namespace> -l app.kubernetes.io/name=rb-new

# Check services
kubectl get services -n <namespace> -l app.kubernetes.io/name=rb-new

# Check ingress
kubectl get ingress -n <namespace> -l app.kubernetes.io/name=rb-new
```

### Viewing Logs

```bash
# View orchestrator logs
kubectl logs -n <namespace> -l app.kubernetes.io/component=orchestrator

# View recommender logs
kubectl logs -n <namespace> -l app.kubernetes.io/component=recommender
```

### Upgrading

```bash
# Update the chart
helm upgrade rb-new ./charts/rb-new -f ./charts/rb-new/values-prod.yaml
```

### Rollback

```bash
# List revisions
helm history rb-new -n <namespace>

# Rollback to a specific revision
helm rollback rb-new <revision-number> -n <namespace>
```

### Uninstalling

```bash
# Uninstall the chart
helm uninstall rb-new -n <namespace>
```

## Troubleshooting

### Common Issues

1. **Pods stuck in Pending state**
   - Check if PVCs are being provisioned
   - Verify node resources are sufficient

2. **Services not accessible**
   - Check service endpoints
   - Verify network policies

3. **Database connection issues**
   - Verify PostgreSQL credentials
   - Check if PostgreSQL pod is running

### Getting Help

If you encounter issues not covered in this guide, please contact the DevOps <NAME_EMAIL>.
