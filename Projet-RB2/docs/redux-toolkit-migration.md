# Plan d'Action Détaillé : Migration Redux Toolkit

## Vue d'ensemble
Ce document présente un plan d'action détaillé pour migrer l'état global de l'application de Redux classique vers Redux Toolkit, en apportant une meilleure structure, une réduction du boilerplate et une meilleure intégration avec TypeScript.

## Durée totale estimée : 4 semaines

## Phase 1 : Préparation (1 semaine)

### 1.1 Audit des états globaux existants
- Inventaire complet des slices d'état actuels
- Identification des dépendances entre les slices
- Analyse des performances des sélecteurs actuels
- Cartographie des actions et des reducers
- Documentation des cas d'utilisation critiques

### 1.2 Configuration du store Redux Toolkit
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'

// Importation des reducers (seront implémentés dans la phase 2)
import authReducer from './slices/auth.slice'
import uiReducer from './slices/ui.slice'
import userReducer from './slices/user.slice'
import notificationsReducer from './slices/notifications.slice'

export const store = configureStore({
  reducer: {
    auth: authReducer,
    ui: uiReducer,
    user: userReducer,
    notifications: notificationsReducer,
    // D'autres reducers seront ajoutés progressivement
  },
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware({
      serializableCheck: {
        // Ignorer certaines actions non-sérialisables si nécessaire
        ignoredActions: ['some/action'],
        // Ignorer certains chemins de l'état si nécessaire
        ignoredPaths: ['some.path'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

// Types inférés à partir du store
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Hooks typés
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
```

### 1.3 Création des types TypeScript communs
```typescript
// src/types/models.ts

// Modèles de base
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  avatarUrl?: string
  preferences: UserPreferences
  createdAt: string
  updatedAt: string
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  PROFESSIONAL = 'PROFESSIONAL',
  PARTNER = 'PARTNER',
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  notifications: NotificationPreferences
  currency: string
}

export interface NotificationPreferences {
  email: boolean
  push: boolean
  sms: boolean
  marketing: boolean
}

export interface Notification {
  id: string
  userId: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  data?: Record<string, any>
  createdAt: string
}

export enum NotificationType {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  SUCCESS = 'SUCCESS',
}

// Ajouter d'autres interfaces selon les besoins du projet
```

## Phase 2 : Migration Progressive (3 semaines)

### 2.1 Migration Slice Authentication
```typescript
// src/store/slices/auth.slice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { User } from '../../types/models'
import { authService } from '../../services/auth.service'
import { RootState } from '../index'

interface AuthState {
  isAuthenticated: boolean
  user: User | null
  loading: boolean
  error: string | null
  token: string | null
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
  token: localStorage.getItem('token'),
}

// Async Thunks
export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authService.login(email, password)
      localStorage.setItem('token', response.token)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to login')
    }
  }
)

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout()
      localStorage.removeItem('token')
      return null
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to logout')
    }
  }
)

export const fetchCurrentUser = createAsyncThunk(
  'auth/fetchCurrentUser',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState() as RootState
      if (!auth.token) return null
      
      const user = await authService.getCurrentUser()
      return user
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user')
    }
  }
)

// Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload
      localStorage.setItem('token', action.payload)
    },
  },
  extraReducers: (builder) => {
    // Login
    builder.addCase(login.pending, (state) => {
      state.loading = true
      state.error = null
    })
    builder.addCase(login.fulfilled, (state, action) => {
      state.isAuthenticated = true
      state.user = action.payload.user
      state.token = action.payload.token
      state.loading = false
    })
    builder.addCase(login.rejected, (state, action) => {
      state.loading = false
      state.error = action.payload as string
    })
    
    // Logout
    builder.addCase(logout.fulfilled, (state) => {
      state.isAuthenticated = false
      state.user = null
      state.token = null
    })
    
    // Fetch current user
    builder.addCase(fetchCurrentUser.pending, (state) => {
      state.loading = true
    })
    builder.addCase(fetchCurrentUser.fulfilled, (state, action) => {
      if (action.payload) {
        state.isAuthenticated = true
        state.user = action.payload
      }
      state.loading = false
    })
    builder.addCase(fetchCurrentUser.rejected, (state, action) => {
      state.isAuthenticated = false
      state.user = null
      state.loading = false
      state.error = action.payload as string
      localStorage.removeItem('token')
    })
  },
})

export const { clearError, setToken } = authSlice.actions
export default authSlice.reducer

// Sélecteurs
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated
export const selectCurrentUser = (state: RootState) => state.auth.user
export const selectAuthLoading = (state: RootState) => state.auth.loading
export const selectAuthError = (state: RootState) => state.auth.error
```

### 2.2 Migration Slice UI/Theme
```typescript
// src/store/slices/ui.slice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { RootState } from '../index'

interface UIState {
  theme: 'light' | 'dark' | 'system'
  sidebarOpen: boolean
  mobileMenuOpen: boolean
  currentBreakpoint: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  modalState: {
    [key: string]: boolean
  }
}

const initialState: UIState = {
  theme: localStorage.getItem('theme') as 'light' | 'dark' | 'system' || 'system',
  sidebarOpen: true,
  mobileMenuOpen: false,
  currentBreakpoint: 'lg',
  modalState: {},
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload
      localStorage.setItem('theme', action.payload)
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload
    },
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen
    },
    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.mobileMenuOpen = action.payload
    },
    setBreakpoint: (state, action: PayloadAction<'xs' | 'sm' | 'md' | 'lg' | 'xl'>) => {
      state.currentBreakpoint = action.payload
      // Fermer automatiquement la sidebar sur les petits écrans
      if (['xs', 'sm'].includes(action.payload)) {
        state.sidebarOpen = false
      } else {
        state.sidebarOpen = true
      }
    },
    openModal: (state, action: PayloadAction<string>) => {
      state.modalState[action.payload] = true
    },
    closeModal: (state, action: PayloadAction<string>) => {
      state.modalState[action.payload] = false
    },
    toggleModal: (state, action: PayloadAction<string>) => {
      state.modalState[action.payload] = !state.modalState[action.payload]
    },
  },
})

export const {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  toggleMobileMenu,
  setMobileMenuOpen,
  setBreakpoint,
  openModal,
  closeModal,
  toggleModal,
} = uiSlice.actions

export default uiSlice.reducer

// Sélecteurs
export const selectTheme = (state: RootState) => state.ui.theme
export const selectSidebarOpen = (state: RootState) => state.ui.sidebarOpen
export const selectMobileMenuOpen = (state: RootState) => state.ui.mobileMenuOpen
export const selectCurrentBreakpoint = (state: RootState) => state.ui.currentBreakpoint
export const selectModalState = (state: RootState, modalId: string) => 
  state.ui.modalState[modalId] || false
```

### 2.3 Migration Slice User
```typescript
// src/store/slices/user.slice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { User, UserPreferences } from '../../types/models'
import { userService } from '../../services/user.service'
import { RootState } from '../index'

interface UserState {
  userProfiles: { [userId: string]: User }
  userPreferences: UserPreferences | null
  loading: boolean
  error: string | null
}

const initialState: UserState = {
  userProfiles: {},
  userPreferences: null,
  loading: false,
  error: null,
}

// Async Thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchUserProfile',
  async (userId: string, { rejectWithValue }) => {
    try {
      const user = await userService.getUserById(userId)
      return user
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user profile')
    }
  }
)

export const updateUserPreferences = createAsyncThunk(
  'user/updateUserPreferences',
  async (preferences: Partial<UserPreferences>, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState() as RootState
      if (!auth.user?.id) {
        return rejectWithValue('User not authenticated')
      }
      
      const updatedPreferences = await userService.updatePreferences(
        auth.user.id, 
        preferences
      )
      return updatedPreferences
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update preferences')
    }
  }
)

// Slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearUserProfiles: (state) => {
      state.userProfiles = {}
    },
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    // Fetch user profile
    builder.addCase(fetchUserProfile.pending, (state) => {
      state.loading = true
      state.error = null
    })
    builder.addCase(fetchUserProfile.fulfilled, (state, action) => {
      state.userProfiles[action.payload.id] = action.payload
      state.loading = false
    })
    builder.addCase(fetchUserProfile.rejected, (state, action) => {
      state.loading = false
      state.error = action.payload as string
    })
    
    // Update user preferences
    builder.addCase(updateUserPreferences.pending, (state) => {
      state.loading = true
      state.error = null
    })
    builder.addCase(updateUserPreferences.fulfilled, (state, action) => {
      state.userPreferences = action.payload
      state.loading = false
    })
    builder.addCase(updateUserPreferences.rejected, (state, action) => {
      state.loading = false
      state.error = action.payload as string
    })
  },
})

export const { clearUserProfiles, clearError } = userSlice.actions
export default userSlice.reducer

// Sélecteurs
export const selectUserProfile = (state: RootState, userId: string) => 
  state.user.userProfiles[userId]
export const selectUserPreferences = (state: RootState) => 
  state.user.userPreferences
export const selectUserLoading = (state: RootState) => state.user.loading
export const selectUserError = (state: RootState) => state.user.error
```

### 2.4 Migration Slice Notifications
```typescript
// src/store/slices/notifications.slice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Notification, NotificationType } from '../../types/models'
import { notificationService } from '../../services/notification.service'
import { RootState } from '../index'

interface NotificationsState {
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  error: string | null
}

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
}

// Async Thunks
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState() as RootState
      if (!auth.user?.id) {
        return rejectWithValue('User not authenticated')
      }
      
      const notifications = await notificationService.getUserNotifications(auth.user.id)
      return notifications
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch notifications')
    }
  }
)

export const markAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      await notificationService.markAsRead(notificationId)
      return notificationId
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark notification as read')
    }
  }
)

export const markAllAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState() as RootState
      if (!auth.user?.id) {
        return rejectWithValue('User not authenticated')
      }
      
      await notificationService.markAllAsRead(auth.user.id)
      return true
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to mark all notifications as read')
    }
  }
)

// Slice
const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'createdAt' | 'read'>>) => {
      const newNotification: Notification = {
        ...action.payload,
        id: `local-${Date.now()}`,
        createdAt: new Date().toISOString(),
        read: false,
      }
      state.notifications.unshift(newNotification)
      state.unreadCount += 1
    },
    clearNotifications: (state) => {
      state.notifications = []
      state.unreadCount = 0
    },
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    // Fetch notifications
    builder.addCase(fetchNotifications.pending, (state) => {
      state.loading = true
      state.error = null
    })
    builder.addCase(fetchNotifications.fulfilled, (state, action) => {
      state.notifications = action.payload
      state.unreadCount = action.payload.filter((n: Notification) => !n.read).length
      state.loading = false
    })
    builder.addCase(fetchNotifications.rejected, (state, action) => {
      state.loading = false
      state.error = action.payload as string
    })
    
    // Mark as read
    builder.addCase(markAsRead.fulfilled, (state, action) => {
      const notification = state.notifications.find(n => n.id === action.payload)
      if (notification && !notification.read) {
        notification.read = true
        state.unreadCount -= 1
      }
    })
    
    // Mark all as read
    builder.addCase(markAllAsRead.fulfilled, (state) => {
      state.notifications.forEach(notification => {
        notification.read = true
      })
      state.unreadCount = 0
    })
  },
})

export const { addNotification, clearNotifications, clearError } = notificationsSlice.actions
export default notificationsSlice.reducer

// Sélecteurs
export const selectNotifications = (state: RootState) => state.notifications.notifications
export const selectUnreadCount = (state: RootState) => state.notifications.unreadCount
export const selectNotificationsLoading = (state: RootState) => state.notifications.loading
export const selectNotificationsError = (state: RootState) => state.notifications.error
```

### 2.5 Intégration dans l'application

```jsx
// src/main.tsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { store } from './store'
import App from './App'
import './index.css'

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
)
```

```tsx
// Exemple d'utilisation dans un composant
// src/components/Header.tsx
import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '../store'
import { 
  selectCurrentUser, 
  selectIsAuthenticated, 
  fetchCurrentUser 
} from '../store/slices/auth.slice'
import { 
  selectTheme, 
  setTheme, 
  toggleSidebar 
} from '../store/slices/ui.slice'
import { 
  selectUnreadCount, 
  fetchNotifications 
} from '../store/slices/notifications.slice'

const Header: React.FC = () => {
  const dispatch = useAppDispatch()
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const user = useAppSelector(selectCurrentUser)
  const theme = useAppSelector(selectTheme)
  const unreadCount = useAppSelector(selectUnreadCount)
  
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchNotifications())
    }
  }, [isAuthenticated, dispatch])
  
  const handleToggleTheme = () => {
    dispatch(setTheme(theme === 'light' ? 'dark' : 'light'))
  }
  
  const handleToggleSidebar = () => {
    dispatch(toggleSidebar())
  }
  
  return (
    <header className="app-header">
      <button onClick={handleToggleSidebar}>
        <span className="sr-only">Toggle sidebar</span>
        <MenuIcon />
      </button>
      
      <div className="logo">MyApp</div>
      
      <div className="header-actions">
        <button onClick={handleToggleTheme}>
          {theme === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
        </button>
        
        {isAuthenticated && (
          <>
            <div className="notifications">
              <BellIcon />
              {unreadCount > 0 && <span className="badge">{unreadCount}</span>}
            </div>
            
            <div className="user-menu">
              <img 
                src={user?.avatarUrl || '/default-avatar.png'} 
                alt={`${user?.firstName} ${user?.lastName}`} 
              />
              <span>{user?.firstName}</span>
            </div>
          </>
        )}
      </div>
    </header>
  )
}

export default Header
```

## Phase 3 : Tests et validation (1 semaine)

### 3.1 Tests unitaires pour les slices
```typescript
// src/store/slices/__tests__/auth.slice.test.ts
import { configureStore } from '@reduxjs/toolkit'
import authReducer, { login, logout, clearError, setToken } from '../auth.slice'

// Mock du service auth
jest.mock('../../../services/auth.service', () => ({
  authService: {
    login: jest.fn().mockResolvedValue({ token: 'test-token', user: { id: '1', name: 'Test User' } }),
    logout: jest.fn().mockResolvedValue(null),
    getCurrentUser: jest.fn().mockResolvedValue({ id: '1', name: 'Test User' }),
  },
}))

describe('Auth Slice', () => {
  const initialState = {
    isAuthenticated: false,
    user: null,
    loading: false,
    error: null,
    token: null,
  }

  it('should handle initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
      token: null,
    })
  })

  it('should handle clearError', () => {
    const actual = authReducer(
      { ...initialState, error: 'test error' },
      clearError()
    )
    expect(actual.error).toBeNull()
  })

  it('should handle setToken', () => {
    const actual = authReducer(
      initialState,
      setToken('new-token')
    )
    expect(actual.token).toEqual('new-token')
  })

  it('should handle successful login', async () => {
    const store = configureStore({
      reducer: { auth: authReducer },
    })

    await store.dispatch(login({ email: '<EMAIL>', password: 'password' }))
    const state = store.getState().auth
    
    expect(state.isAuthenticated).toBe(true)
    expect(state.user).toEqual({ id: '1', name: 'Test User' })
    expect(state.token).toEqual('test-token')
    expect(state.loading).toBe(false)
    expect(state.error).toBeNull()
  })

  it('should handle successful logout', async () => {
    const store = configureStore({
      reducer: { auth: authReducer },
      preloadedState: {
        auth: {
          ...initialState,
          isAuthenticated: true,
          user: { id: '1', name: 'Test User' },
          token: 'test-token',
        },
      },
    })

    await store.dispatch(logout())
    const state = store.getState().auth
    
    expect(state.isAuthenticated).toBe(false)
    expect(state.user).toBeNull()
    expect(state.token).toBeNull()
  })
})
```

### 3.2 Tests d'intégration

```typescript
// src/components/__tests__/Header.test.tsx
import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import authReducer from '../../store/slices/auth.slice'
import uiReducer from '../../store/slices/ui.slice'
import notificationsReducer from '../../store/slices/notifications.slice'
import Header from '../Header'

describe('Header Component', () => {
  const renderWithStore = (
    preloadedState = {}
  ) => {
    const store = configureStore({
      reducer: {
        auth: authReducer,
        ui: uiReducer,
        notifications: notificationsReducer,
      },
      preloadedState,
    })

    return render(
      <Provider store={store}>
        <Header />
      </Provider>
    )
  }

  it('should render logo', () => {
    renderWithStore()
    expect(screen.getByText('MyApp')).toBeInTheDocument()
  })

  it('should toggle theme when clicking theme button', () => {
    renderWithStore({
      ui: {
        theme: 'light',
        sidebarOpen: true,
        mobileMenuOpen: false,
        currentBreakpoint: 'lg',
        modalState: {},
      },
    })

    const themeButton = screen.getByRole('button', { name: /toggle theme/i })
    fireEvent.click(themeButton)
    
    // Vérifier que l'action setTheme a été déclenchée
    // Cette vérification dépend de la façon dont vous avez mis en œuvre les composants d'icônes
  })

  it('should display user info when authenticated', () => {
    renderWithStore({
      auth: {
        isAuthenticated: true,
        user: {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          avatarUrl: '/avatar.png',
        },
        loading: false,
        error: null,
        token: 'test-token',
      },
    })

    expect(screen.getByText('John')).toBeInTheDocument()
    expect(screen.getByAltText('John Doe')).toHaveAttribute('src', '/avatar.png')
  })

  it('should display notification badge when there are unread notifications', () => {
    renderWithStore({
      auth: {
        isAuthenticated: true,
        user: { id: '1', firstName: 'John', lastName: 'Doe' },
        loading: false,
        error: null,
        token: 'test-token',
      },
      notifications: {
        notifications: [],
        unreadCount: 5,
        loading: false,
        error: null,
      },
    })

    expect(screen.getByText('5')).toBeInTheDocument()
  })
})
```

## Métriques de succès

### Qualité du code
- Réduction du boilerplate code > 40%
- Cohérence du style et de la structure des slices
- Amélioration de la qualité et de la couverture des tests (>80%)

### Performance
- Temps de rendu initial équivalent ou meilleur
- Temps de mise à jour de l'état équivalent ou meilleur
- Réduction de la consommation mémoire

### Fonctionnalités
- 100% des états globaux migrés
- Pas de régression fonctionnelle
- Meilleure gestion des erreurs et des cas limites

### Développement
- Meilleure expérience de développement (DX)
- Autocomplétion TypeScript fonctionnelle à 100%
- Documentation complète et à jour 