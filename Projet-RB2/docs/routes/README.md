# Documentation des Routes - Retreat And Be

## Introduction

Cette documentation présente l'arborescence complète des chemins (routes) pour la plateforme Retreat And Be. Elle est destinée aux développeurs, designers et gestionnaires de produit travaillant sur le projet.

## Structure de la Documentation

La documentation des routes est organisée en plusieurs fichiers pour faciliter la navigation et la maintenance :

1. [Routes Publiques](./public-routes.md) - Routes accessibles sans authentification
2. [Routes Client](./client-routes.md) - Routes pour les utilisateurs finaux
3. [Routes Professionnel](./professional-routes.md) - Routes pour les organisateurs et prestataires
4. [Routes Partenaire](./partner-routes.md) - Routes pour les différents niveaux de partenariat
5. [Routes Admin](./admin-routes.md) - Routes pour l'administration de la plateforme
6. [Routes Microservices](./microservices-routes.md) - Routes pour les services intégrés
7. [Routes Utilitaires](./utility-routes.md) - Routes de développement et d'erreur

## Comment Utiliser Cette Documentation

Chaque fichier contient :
- Une description des routes de la section
- Un tableau détaillé avec les chemins, descriptions et composants associés
- Des informations sur la protection des routes (authentification requise)
- Des notes spécifiques sur l'implémentation

## Conventions de Nommage

### Routes
- Utiliser des noms en minuscules séparés par des tirets (`kebab-case`)
- Utiliser des noms courts mais descriptifs
- Utiliser des paramètres avec préfixe `:` pour les identifiants (ex: `:id`, `:bookingId`)
- Éviter les routes trop profondes (plus de 3 niveaux)

### Composants
- Utiliser le format `PascalCase` pour les noms de composants
- Suffixer les composants de page avec `Page` (ex: `HomePage`, `ProfilePage`)
- Utiliser des noms descriptifs qui reflètent la fonction du composant

## Gestion des Accès

Les routes protégées sont indiquées dans les tableaux avec la mention "Oui" dans la colonne "Protection". Ces routes nécessitent une authentification et potentiellement des autorisations spécifiques.

Pour implémenter la protection des routes, utiliser le composant `ProtectedRoute` :

```jsx
<Route 
  path="/client/dashboard" 
  element={
    <ProtectedRoute>
      <ClientDashboard />
    </ProtectedRoute>
  } 
/>
```

## Maintenance et Mise à Jour

Cette documentation doit être mise à jour à chaque modification de l'arborescence des routes. Pour ajouter une nouvelle route :

1. Identifier la section appropriée
2. Ajouter la route dans le tableau correspondant
3. Mettre à jour le fichier de routes dans le code source
4. Mettre à jour cette documentation

## Contacts

Pour toute question concernant cette documentation ou l'arborescence des routes, contacter l'équipe de développement frontend.
