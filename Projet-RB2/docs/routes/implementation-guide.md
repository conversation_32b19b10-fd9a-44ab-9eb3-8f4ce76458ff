# Guide d'Implémentation des Routes - Retreat And Be

Ce guide fournit des instructions détaillées pour implémenter l'arborescence des routes dans l'application Retreat And Be. Il est destiné aux développeurs frontend travaillant sur le projet.

## Table des Matières

1. [Structure des Fichiers](#structure-des-fichiers)
2. [Configuration de Base](#configuration-de-base)
3. [Protection des Routes](#protection-des-routes)
4. [Lazy Loading](#lazy-loading)
5. [Navigation et Breadcrumbs](#navigation-et-breadcrumbs)
6. [Gestion des Erreurs](#gestion-des-erreurs)
7. [Tests](#tests)
8. [Bonnes Pratiques](#bonnes-pratiques)

## Structure des Fichiers

Organisez les fichiers de routes de manière modulaire pour faciliter la maintenance :

```
src/
├── routes/
│   ├── index.tsx           # Point d'entrée principal
│   ├── publicRoutes.tsx    # Routes publiques
│   ├── clientRoutes.tsx    # Routes client
│   ├── professionalRoutes.tsx # Routes professionnel
│   ├── partnerRoutes.tsx   # Routes partenaire
│   ├── adminRoutes.tsx     # Routes admin
│   ├── microservicesRoutes.tsx # Routes microservices
│   └── utilityRoutes.tsx   # Routes utilitaires
├── components/
│   ├── auth/
│   │   ├── ProtectedRoute.tsx # Composant de protection de base
│   │   ├── AdminProtectedRoute.tsx # Protection admin
│   │   └── PartnerProtectedRoute.tsx # Protection partenaire
```

## Configuration de Base

### 1. Créer le fichier de routes principal

```tsx
// src/routes/index.tsx
import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import LoadingFallback from '../components/common/LoadingFallback';

// Import des groupes de routes
import { publicRoutes } from './publicRoutes';
import { clientRoutes } from './clientRoutes';
import { professionalRoutes } from './professionalRoutes';
import { partnerRoutes } from './partnerRoutes';
import { adminRoutes } from './adminRoutes';
import { microservicesRoutes } from './microservicesRoutes';
import { utilityRoutes } from './utilityRoutes';

export const Router = () => {
  return (
    <BrowserRouter>
      <Suspense fallback={<LoadingFallback message="Chargement de l'application..." minHeight="100vh" />}>
        <Routes>
          {publicRoutes}
          {clientRoutes}
          {professionalRoutes}
          {partnerRoutes}
          {adminRoutes}
          {microservicesRoutes}
          {utilityRoutes}
        </Routes>
      </Suspense>
    </BrowserRouter>
  );
};

export default Router;
```

### 2. Créer les fichiers de routes spécifiques

Exemple pour les routes publiques :

```tsx
// src/routes/publicRoutes.tsx
import React from 'react';
import { Route } from 'react-router-dom';

// Import des composants
import HomePage from '../pages/Public/HomePage';
import AboutPage from '../pages/Public/AboutPage';
import ContactPage from '../pages/Public/ContactPage';
// ... autres imports

export const publicRoutes = (
  <>
    <Route path="/" element={<HomePage />} />
    <Route path="/about" element={<AboutPage />} />
    <Route path="/contact" element={<ContactPage />} />
    {/* ... autres routes */}
  </>
);
```

## Protection des Routes

### 1. Créer le composant ProtectedRoute de base

```tsx
// src/components/auth/ProtectedRoute.tsx
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <div>Chargement...</div>;
  }

  if (!isAuthenticated) {
    // Rediriger vers la page de connexion avec l'URL de retour
    return <Navigate to={`/login?returnUrl=${encodeURIComponent(location.pathname)}`} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
```

### 2. Créer des composants de protection spécifiques

```tsx
// src/components/auth/AdminProtectedRoute.tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import ProtectedRoute from './ProtectedRoute';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

export const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { user } = useAuth();

  // Vérifier si l'utilisateur est authentifié et a le rôle d'administrateur
  if (!user || !user.roles.includes('admin')) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <ProtectedRoute>{children}</ProtectedRoute>;
};

export default AdminProtectedRoute;
```

```tsx
// src/components/auth/PartnerProtectedRoute.tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import ProtectedRoute from './ProtectedRoute';

interface PartnerProtectedRouteProps {
  children: React.ReactNode;
  level?: 'base' | 'certified' | 'premium';
}

export const PartnerProtectedRoute: React.FC<PartnerProtectedRouteProps> = ({ 
  children, 
  level = 'base' 
}) => {
  const { user } = useAuth();

  // Vérifier si l'utilisateur est un partenaire
  if (!user || !user.roles.includes('partner')) {
    return <Navigate to="/unauthorized" replace />;
  }

  // Vérifier le niveau de partenariat si nécessaire
  if (level === 'certified' && user.partnerLevel < 2) {
    return <Navigate to="/partner/upgrade" replace />;
  }

  if (level === 'premium' && user.partnerLevel < 3) {
    return <Navigate to="/partner/upgrade" replace />;
  }

  return <>{children}</>;
};

export default PartnerProtectedRoute;
```

## Lazy Loading

Utilisez le lazy loading pour améliorer les performances :

```tsx
// src/lazyComponents.ts
import { lazy } from 'react';

// General pages
export const LazyHomePage = lazy(() => import('./pages/Public/HomePage'));
export const LazyProfilePage = lazy(() => import('./pages/Private/user/ProfilePage'));
export const LazySettingsPage = lazy(() => import('./pages/Private/user/SettingsPage'));

// Client pages
export const LazyRetreatListPage = lazy(() => import('./pages/retreats/ExploreRetreatsPage'));
export const LazyRetreatDetailPage = lazy(() => import('./pages/retreats/RetreatPage'));

// ... autres composants lazy
```

Puis utilisez-les dans vos routes :

```tsx
// src/routes/publicRoutes.tsx
import React from 'react';
import { Route } from 'react-router-dom';
import { LazyHomePage, LazyAboutPage } from '../lazyComponents';

export const publicRoutes = (
  <>
    <Route path="/" element={<LazyHomePage />} />
    <Route path="/about" element={<LazyAboutPage />} />
    {/* ... autres routes */}
  </>
);
```

## Navigation et Breadcrumbs

### 1. Créer un composant de navigation

```tsx
// src/components/navigation/MainNavigation.tsx
import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

export const MainNavigation: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  return (
    <nav className="main-navigation">
      <ul>
        {/* Navigation publique */}
        <li><NavLink to="/">Accueil</NavLink></li>
        <li><NavLink to="/explore-retreats">Explorer</NavLink></li>
        <li><NavLink to="/blog">Blog</NavLink></li>
        <li><NavLink to="/about">À propos</NavLink></li>
        
        {/* Navigation utilisateur connecté */}
        {isAuthenticated && (
          <>
            {/* Navigation client */}
            {user?.roles.includes('client') && (
              <>
                <li><NavLink to="/client/dashboard">Tableau de bord</NavLink></li>
                <li><NavLink to="/client/bookings">Mes réservations</NavLink></li>
              </>
            )}
            
            {/* Navigation professionnel */}
            {user?.roles.includes('professional') && (
              <>
                <li><NavLink to="/professional/dashboard">Tableau de bord</NavLink></li>
                <li><NavLink to="/professional/retreats">Mes retraites</NavLink></li>
              </>
            )}
            
            {/* Navigation admin */}
            {user?.roles.includes('admin') && (
              <li><NavLink to="/admin/dashboard">Administration</NavLink></li>
            )}
            
            {/* Navigation commune */}
            <li><NavLink to="/profile">Profil</NavLink></li>
            <li><NavLink to="/settings">Paramètres</NavLink></li>
          </>
        )}
        
        {/* Authentification */}
        {!isAuthenticated ? (
          <>
            <li><NavLink to="/login">Connexion</NavLink></li>
            <li><NavLink to="/register">Inscription</NavLink></li>
          </>
        ) : (
          <li><button onClick={() => /* logique de déconnexion */}>Déconnexion</button></li>
        )}
      </ul>
    </nav>
  );
};

export default MainNavigation;
```

### 2. Créer un composant Breadcrumbs

```tsx
// src/components/navigation/Breadcrumbs.tsx
import React from 'react';
import { Link, useLocation } from 'react-router-dom';

// Mapping des routes vers des noms lisibles
const routeNameMap: Record<string, string> = {
  '': 'Accueil',
  'client': 'Espace Client',
  'dashboard': 'Tableau de bord',
  'professional': 'Espace Professionnel',
  'admin': 'Administration',
  'retreats': 'Retraites',
  'bookings': 'Réservations',
  // ... autres mappings
};

export const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter(x => x);
  
  return (
    <div className="breadcrumbs">
      <ul>
        <li><Link to="/">Accueil</Link></li>
        {pathnames.map((value, index) => {
          const to = `/${pathnames.slice(0, index + 1).join('/')}`;
          const isLast = index === pathnames.length - 1;
          const name = routeNameMap[value] || value;
          
          return (
            <li key={to}>
              {isLast ? (
                <span>{name}</span>
              ) : (
                <Link to={to}>{name}</Link>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default Breadcrumbs;
```

## Gestion des Erreurs

### 1. Créer un composant ErrorBoundary

```tsx
// src/components/error/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    // Vous pouvez également envoyer l'erreur à un service de reporting
  }

  public render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

### 2. Utiliser ErrorBoundary dans l'application

```tsx
// src/App.tsx
import React from 'react';
import Router from './routes';
import ErrorBoundary from './components/error/ErrorBoundary';
import Error500Page from './pages/errors/Error500Page';

const App: React.FC = () => {
  return (
    <ErrorBoundary fallback={<Error500Page />}>
      <Router />
    </ErrorBoundary>
  );
};

export default App;
```

## Tests

### 1. Tester les routes protégées

```tsx
// src/components/auth/__tests__/ProtectedRoute.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { AuthProvider } from '../../../context/AuthContext';
import ProtectedRoute from '../ProtectedRoute';

// Mock du hook useAuth
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: true,
    isLoading: false,
    user: { id: '1', name: 'Test User' }
  })
}));

describe('ProtectedRoute', () => {
  test('renders children when user is authenticated', () => {
    render(
      <AuthProvider>
        <MemoryRouter initialEntries={['/protected']}>
          <Routes>
            <Route 
              path="/protected" 
              element={
                <ProtectedRoute>
                  <div data-testid="protected-content">Protected Content</div>
                </ProtectedRoute>
              } 
            />
          </Routes>
        </MemoryRouter>
      </AuthProvider>
    );
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });
  
  // Ajouter d'autres tests pour les cas de redirection, etc.
});
```

## Bonnes Pratiques

1. **Cohérence** : Suivez les conventions de nommage et de structure définies dans ce guide.

2. **Documentation** : Documentez toutes les routes et leurs fonctionnalités.

3. **Tests** : Testez les routes, en particulier les routes protégées.

4. **Performance** : Utilisez le lazy loading pour les composants volumineux.

5. **Accessibilité** : Assurez-vous que la navigation est accessible.

6. **Sécurité** : Vérifiez correctement les autorisations pour les routes protégées.

7. **Maintenance** : Organisez les routes de manière modulaire pour faciliter la maintenance.

8. **Expérience utilisateur** : Implémentez des transitions fluides entre les routes.

9. **SEO** : Utilisez des titres de page appropriés et des métadonnées pour chaque route.

10. **Analytics** : Suivez les changements de route pour les analyses.

## Conclusion

Ce guide fournit les bases pour implémenter l'arborescence des routes dans l'application Retreat And Be. Suivez ces instructions pour créer une structure de navigation cohérente, sécurisée et performante.
