# Routes Microservices - Retreat And Be

Les routes microservices sont destinées aux différents services intégrés à la plateforme Retreat And Be. Ces services fonctionnent de manière autonome tout en s'intégrant à l'écosystème global.

## Éducation

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/education` | Page principale éducation | `EducationPage` | Non | ✅ |
| `/education/courses` | Liste des cours | `CoursesListPage` | Non | ❌ |
| `/education/courses/:id` | Détail d'un cours | `CourseDetailPage` | Non | ❌ |

## Transport et location

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/car-rental` | Location de voitures | `CarRentalPage` | Non | ❌ |
| `/car-rental/:id` | Détail d'une voiture | `CarDetailsPage` | Non | ❌ |
| `/transport-booking` | Réservation de transport | `TransportBooking` | Non | ❌ |

## Vidéo et social

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/videos` | Page principale vidéos | `VideoLandingPage` | Non | ✅ |
| `/videos/explore` | Explorer les vidéos | `VideoExplorePage` | Non | ✅ |
| `/videos/upload` | Télécharger une vidéo | `UploadVideoPage` | Oui | ❌ |
| `/livestream` | Diffusion en direct | `LivePage` | Non | ✅ |
| `/social` | Plateforme sociale | `Social` | Non | ❌ |
| `/social-video` | Vidéos sociales | `SocialVideo` | Non | ❌ |

## NFT et Web3

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/nft` | Page principale NFT | `NFT` | Non | ✅ |
| `/nft/gallery` | Galerie NFT | `NFTGalleryPage` | Non | ❌ |
| `/nft/token/:id` | Détail d'un token | `TokenPage` | Non | ❌ |

## Autres microservices

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-matcher` | Service de mise en relation pour retraites | `RetreatMatcher` | Non | ✅ |
| `/retreat-stream` | Service de streaming pour retraites | `RetreatStream` | Oui | ✅ |
| `/financial-management` | Gestion financière | `FinancialManagement` | Oui | ✅ |
| `/security-service` | Service de sécurité | `SecurityService` | Oui | ✅ |
| `/vr` | Réalité virtuelle | `VR` | Non | ✅ |
| `/website-creator` | Créateur de site web | `WebsiteCreatorLayout` | Oui | ✅ |
| `/storage` | Stockage | `StorageLayout` | Oui | ❌ |

## Notes d'implémentation

### Architecture microservices

Chaque microservice doit :
- Être développé et déployé indépendamment
- Avoir sa propre base de données si nécessaire
- Communiquer via des API REST ou GraphQL
- Être conteneurisé pour faciliter le déploiement
- Suivre les principes de conception API cohérents

### Intégration des microservices

L'intégration des microservices dans l'application principale peut se faire de plusieurs façons :
- **Intégration directe** : Le microservice est intégré directement dans l'application principale
- **Iframe** : Le microservice est affiché dans un iframe
- **API Gateway** : Toutes les communications passent par une API Gateway
- **Micro-frontend** : Chaque microservice a sa propre interface utilisateur

### Service d'éducation

Le service d'éducation doit offrir :
- Un catalogue de cours
- Des systèmes de progression
- Des quiz et évaluations
- Des certificats
- Des recommandations personnalisées

### Service de transport et location

Le service de transport et location doit permettre :
- La recherche de véhicules disponibles
- La réservation et le paiement
- La gestion des dates et horaires
- L'ajout d'options et services
- Le suivi des réservations

### Service vidéo et social

Le service vidéo et social doit inclure :
- Le téléchargement et le partage de vidéos
- La diffusion en direct
- Les interactions sociales (commentaires, likes)
- La modération de contenu
- Les recommandations personnalisées

### Service NFT et Web3

Le service NFT et Web3 doit offrir :
- La création et la gestion de NFT
- L'intégration avec des wallets
- La visualisation de collections
- Les transactions sécurisées
- L'historique des transactions

### Autres services

Les autres services doivent être développés selon les besoins spécifiques de la plateforme, en suivant les mêmes principes d'architecture et d'intégration.
