# Routes Publiques - Retreat And Be

Les routes publiques sont accessibles à tous les visiteurs sans authentification. Elle<PERSON> comprennent les pages d'accueil, d'information, de blog, d'exploration des retraites, et d'authentification.

## Pages d'accueil et informations générales

| Route | Description | Composant | Implémenté |
|-------|-------------|-----------|------------|
| `/` | Page d'accueil principale | `HomePage` | ✅ |
| `/main` | Page principale alternative | `MainPage` | ✅ |
| `/about` | À propos de Retreat And Be | `AboutPage` | ✅ |
| `/contact` | Formulaire de contact | `ContactPage` | ✅ |
| `/faq` | Questions fréquemment posées | `FAQPage` | ✅ |
| `/privacy` | Politique de confidentialité | `PrivacyPolicyPage` | ✅ |
| `/terms` | Conditions d'utilisation | `TermsPage` | ✅ |
| `/cgu` | Conditions générales d'utilisation | `CGUPage` | ✅ |
| `/gdpr` | Informations RGPD | `GDPRPage` | ❌ |
| `/security` | Informations sur la sécurité | `SecurityPage` | ❌ |
| `/foundation` | Page de la fondation | `FoundationPage` | ❌ |
| `/careers` | Opportunités de carrière | `CareersPage` | ❌ |
| `/support` | Support client | `SupportPage` | ❌ |

## Blog et actualités

| Route | Description | Composant | Implémenté |
|-------|-------------|-----------|------------|
| `/blog` | Liste des articles de blog | `BlogPage` | ✅ |
| `/blog/:id` | Article de blog spécifique | `BlogPostPage` | ✅ |
| `/news` | Actualités | `NewsPage` | ❌ |
| `/events` | Événements | `EventsPage` | ❌ |
| `/testimonials` | Témoignages | `TestimonialsPage` | ❌ |

## Exploration des retraites

| Route | Description | Composant | Implémenté |
|-------|-------------|-----------|------------|
| `/explore-retreats` | Explorer les retraites | `ExploreRetreatsPage` | ✅ |
| `/retreats` | Liste des retraites | `RetreatsPage` | ✅ |
| `/retreats/:id` | Détail d'une retraite | `RetreatPage` | ✅ |
| `/retreats/livestream/:id` | Livestream d'une retraite | `LivestreamRetreatPage` | ✅ |
| `/retreat-finder` | Formulaire de recherche de retraite | `RetreatFinderForm` | ✅ |
| `/search-results` | Résultats de recherche | `SearchResultsPage` | ✅ |

## Partenaires et professionnels

| Route | Description | Composant | Implémenté |
|-------|-------------|-----------|------------|
| `/hosts` | Hôtes de retraites | `HostsPage` | ❌ |
| `/organizers` | Organisateurs | `OrganizersPage` | ✅ |
| `/partners` | Partenaires | `PartnersPage` | ✅ |
| `/travel-agencies` | Agences de voyage | `TravelAgenciesPage` | ❌ |
| `/caterers` | Traiteurs | `CaterersPage` | ❌ |
| `/wellness` | Bien-être | `WellnessPage` | ❌ |

## Authentification

| Route | Description | Composant | Implémenté |
|-------|-------------|-----------|------------|
| `/login` | Connexion | `LoginPage` | ✅ |
| `/register` | Inscription | `RegisterPage` | ✅ |
| `/forgot-password` | Mot de passe oublié | `ForgotPasswordPage` | ✅ |
| `/reset-password/:token` | Réinitialisation du mot de passe | `ResetPasswordPage` | ✅ |
| `/verify-email/:token` | Vérification de l'email | `VerifyEmailPage` | ❌ |
| `/verify-mfa` | Vérification de l'authentification à deux facteurs | `VerifyMFAPage` | ✅ |

## Notes d'implémentation

### Page d'accueil
La page d'accueil (`/`) est la vitrine principale de l'application. Elle doit présenter :
- Une introduction à Retreat And Be
- Les fonctionnalités principales
- Des témoignages
- Un appel à l'action pour s'inscrire ou explorer les retraites

### Pages d'information
Les pages d'information (`/about`, `/privacy`, etc.) doivent suivre une structure cohérente avec :
- Un en-tête clair
- Un contenu bien structuré avec des sections
- Des liens vers d'autres pages pertinentes
- Un pied de page avec des informations de contact

### Exploration des retraites
Les pages d'exploration des retraites doivent inclure :
- Des filtres avancés
- Une carte interactive (si applicable)
- Des options de tri
- Une pagination efficace
- Des aperçus visuels attrayants

### Authentification
Les pages d'authentification doivent :
- Être sécurisées (HTTPS)
- Inclure une validation des formulaires
- Offrir des messages d'erreur clairs
- Proposer des options de récupération
- Supporter l'authentification à deux facteurs
