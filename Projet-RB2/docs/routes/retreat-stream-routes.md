# Routes du Service de Streaming pour Retraites - Retreat And Be

Le service de streaming pour retraites (RetreatStream) est un microservice protégé qui permet aux utilisateurs de diffuser, regarder et interagir avec des contenus vidéo liés aux retraites. Cette documentation détaille les routes spécifiques à ce service.

> **Restriction d'accès importante** : Seuls les partenaires premium ont la possibilité d'utiliser le service de streaming en direct (création et diffusion). Les autres utilisateurs peuvent uniquement visionner les contenus.

## Routes principales

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-stream` | Point d'entrée du service | `RetreatStream` | Oui | ✅ |
| `/retreat-stream/home` | Page d'accueil du streaming | `HomePage` | Oui | ❌ |
| `/retreat-stream/live` | Diffusion en direct | `LivePage` | Oui | ❌ |
| `/retreat-stream/explore` | Explorer les diffusions | `ExplorePage` | Oui | ❌ |
| `/retreat-stream/my-events` | Mes événements | `MyEventsPage` | Oui | ❌ |
| `/retreat-stream/replay` | Rediffusions | `ReplayPage` | Oui | ❌ |
| `/retreat-stream/profile` | Profil de streaming | `ProfilePage` | Oui | ❌ |

## Routes détaillées

### Page d'accueil

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-stream/home` | Page d'accueil du streaming | `HomePage` | Oui | ❌ |

**Fonctionnalités :**
- Affichage des diffusions en direct en cours
- Recommandations personnalisées
- Événements à venir
- Rediffusions populaires
- Accès rapide aux catégories

### Diffusion en direct

| Route | Description | Composant | Protection | Restriction | Implémenté |
|-------|-------------|-----------|------------|-------------|------------|
| `/retreat-stream/live` | Diffusion en direct | `LivePage` | Oui | Visionnage : Tous<br>Diffusion : Premium | ❌ |
| `/retreat-stream/live/:id` | Diffusion spécifique | `LiveDetailPage` | Oui | Visionnage : Tous | ❌ |
| `/retreat-stream/live/create` | Créer une diffusion | `CreateLivePage` | Oui | Partenaires Premium uniquement | ❌ |

**Fonctionnalités :**
- Lecteur vidéo en direct (accessible à tous les utilisateurs authentifiés)
- Chat en temps réel (accessible à tous les utilisateurs authentifiés)
- Interactions (likes, partages) (accessible à tous les utilisateurs authentifiés)
- Création et gestion de diffusion (réservé aux partenaires premium)
- Tableau de bord de diffusion (réservé aux partenaires premium)
- Statistiques et analyses (réservé aux partenaires premium)

### Explorer

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-stream/explore` | Explorer les diffusions | `ExplorePage` | Oui | ❌ |
| `/retreat-stream/explore/categories` | Catégories | `CategoriesPage` | Oui | ❌ |
| `/retreat-stream/explore/trending` | Tendances | `TrendingPage` | Oui | ❌ |

**Fonctionnalités :**
- Filtres par catégorie
- Recherche avancée
- Tri par popularité/date
- Découverte de nouveaux contenus
- Suggestions basées sur les intérêts

### Mes événements

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-stream/my-events` | Mes événements | `MyEventsPage` | Oui | ❌ |
| `/retreat-stream/my-events/upcoming` | Événements à venir | `UpcomingEventsPage` | Oui | ❌ |
| `/retreat-stream/my-events/past` | Événements passés | `PastEventsPage` | Oui | ❌ |

**Fonctionnalités :**
- Liste des événements auxquels l'utilisateur est inscrit
- Rappels et notifications
- Accès aux rediffusions des événements passés
- Gestion des inscriptions
- Calendrier des événements

### Rediffusions

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-stream/replay` | Rediffusions | `ReplayPage` | Oui | ❌ |
| `/retreat-stream/replay/:id` | Rediffusion spécifique | `ReplayDetailPage` | Oui | ❌ |
| `/retreat-stream/replay/saved` | Rediffusions sauvegardées | `SavedReplaysPage` | Oui | ❌ |

**Fonctionnalités :**
- Lecteur vidéo avec contrôles avancés
- Chapitres et points d'intérêt
- Commentaires et discussions
- Téléchargement (si autorisé)
- Recommandations similaires

### Profil

| Route | Description | Composant | Protection | Implémenté |
|-------|-------------|-----------|------------|------------|
| `/retreat-stream/profile` | Profil de streaming | `ProfilePage` | Oui | ❌ |
| `/retreat-stream/profile/edit` | Édition du profil | `EditProfilePage` | Oui | ❌ |
| `/retreat-stream/profile/settings` | Paramètres de streaming | `StreamSettingsPage` | Oui | ❌ |

**Fonctionnalités :**
- Informations du profil
- Historique de visionnage
- Préférences de contenu
- Paramètres de notification
- Gestion des abonnements

## Notes d'implémentation

### Structure du routeur

Pour implémenter ces routes, il est recommandé d'utiliser un routeur imbriqué :

```jsx
// src/pages/retreats/RetreatStream.tsx
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import StreamLayout from './streaming/StreamLayout';

// Import des composants
import HomePage from './streaming/HomePage';
import LivePage from './streaming/LivePage';
import LiveDetailPage from './streaming/LiveDetailPage';
import CreateLivePage from './streaming/CreateLivePage';
import ExplorePage from './streaming/ExplorePage';
import CategoriesPage from './streaming/CategoriesPage';
import TrendingPage from './streaming/TrendingPage';
import MyEventsPage from './streaming/MyEventsPage';
import UpcomingEventsPage from './streaming/UpcomingEventsPage';
import PastEventsPage from './streaming/PastEventsPage';
import ReplayPage from './streaming/ReplayPage';
import ReplayDetailPage from './streaming/ReplayDetailPage';
import SavedReplaysPage from './streaming/SavedReplaysPage';
import ProfilePage from './streaming/ProfilePage';
import EditProfilePage from './streaming/EditProfilePage';
import StreamSettingsPage from './streaming/StreamSettingsPage';

const RetreatStream: React.FC = () => {
  return (
    <StreamLayout>
      <Routes>
        <Route path="/" element={<Navigate to="home" replace />} />
        <Route path="home" element={<HomePage />} />

        <Route path="live" element={<LivePage />} />
        <Route path="live/:id" element={<LiveDetailPage />} />
        <Route path="live/create" element={<CreateLivePage />} />

        <Route path="explore" element={<ExplorePage />} />
        <Route path="explore/categories" element={<CategoriesPage />} />
        <Route path="explore/trending" element={<TrendingPage />} />

        <Route path="my-events" element={<MyEventsPage />} />
        <Route path="my-events/upcoming" element={<UpcomingEventsPage />} />
        <Route path="my-events/past" element={<PastEventsPage />} />

        <Route path="replay" element={<ReplayPage />} />
        <Route path="replay/:id" element={<ReplayDetailPage />} />
        <Route path="replay/saved" element={<SavedReplaysPage />} />

        <Route path="profile" element={<ProfilePage />} />
        <Route path="profile/edit" element={<EditProfilePage />} />
        <Route path="profile/settings" element={<StreamSettingsPage />} />
      </Routes>
    </StreamLayout>
  );
};

export default RetreatStream;
```

### Layout commun

Créez un composant de layout commun pour maintenir une interface cohérente :

```jsx
// src/pages/retreats/streaming/StreamLayout.tsx
import React, { ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';

interface StreamLayoutProps {
  children: ReactNode;
}

const StreamLayout: React.FC<StreamLayoutProps> = ({ children }) => {
  const location = useLocation();

  return (
    <div className="stream-layout">
      <header className="stream-header">
        <h1>Retreat Stream</h1>
        <nav className="stream-nav">
          <ul>
            <li className={location.pathname.includes('/home') ? 'active' : ''}>
              <Link to="/retreat-stream/home">Accueil</Link>
            </li>
            <li className={location.pathname.includes('/live') ? 'active' : ''}>
              <Link to="/retreat-stream/live">En direct</Link>
            </li>
            <li className={location.pathname.includes('/explore') ? 'active' : ''}>
              <Link to="/retreat-stream/explore">Explorer</Link>
            </li>
            <li className={location.pathname.includes('/my-events') ? 'active' : ''}>
              <Link to="/retreat-stream/my-events">Mes événements</Link>
            </li>
            <li className={location.pathname.includes('/replay') ? 'active' : ''}>
              <Link to="/retreat-stream/replay">Rediffusions</Link>
            </li>
            <li className={location.pathname.includes('/profile') ? 'active' : ''}>
              <Link to="/retreat-stream/profile">Profil</Link>
            </li>
          </ul>
        </nav>
      </header>

      <main className="stream-content">
        {children}
      </main>

      <footer className="stream-footer">
        <p>&copy; {new Date().getFullYear()} Retreat And Be - Service de streaming</p>
      </footer>
    </div>
  );
};

export default StreamLayout;
```

### Protection des routes et vérification du niveau de partenariat

Assurez-vous que toutes les routes du service de streaming sont protégées :

```jsx
// Dans le routeur principal
<Route
  path="/retreat-stream/*"
  element={
    <ProtectedRoute>
      <RetreatStream />
    </ProtectedRoute>
  }
/>
```

Pour les routes réservées aux partenaires premium, ajoutez une vérification supplémentaire :

```jsx
// Dans le routeur du service de streaming
<Route
  path="live/create"
  element={
    <PremiumPartnerRoute>
      <CreateLivePage />
    </PremiumPartnerRoute>
  }
/>
```

Créez un composant `PremiumPartnerRoute` pour vérifier le niveau de partenariat :

```jsx
// src/components/auth/PremiumPartnerRoute.tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

interface PremiumPartnerRouteProps {
  children: React.ReactNode;
}

const PremiumPartnerRoute: React.FC<PremiumPartnerRouteProps> = ({ children }) => {
  const { user } = useAuth();

  // Vérifier si l'utilisateur est un partenaire premium
  const isPremiumPartner = user?.partnerLevel === 'premium';

  if (!isPremiumPartner) {
    // Rediriger vers une page d'upgrade ou d'information
    return <Navigate to="/retreat-stream/premium-required" replace />;
  }

  return <>{children}</>;
};

export default PremiumPartnerRoute;
```

### Composants à implémenter

Pour rendre le service de streaming fonctionnel, vous devrez implémenter les composants suivants :

1. **Pages principales**
   - `HomePage`
   - `LivePage`
   - `ExplorePage`
   - `MyEventsPage`
   - `ReplayPage`
   - `ProfilePage`

2. **Composants partagés**
   - `StreamLayout`
   - `VideoPlayer`
   - `LiveChat`
   - `StreamCard`
   - `CategoryFilter`
   - `SearchBar`

3. **Pages détaillées** (à implémenter dans une phase ultérieure)
   - `LiveDetailPage`
   - `CreateLivePage`
   - `CategoriesPage`
   - `TrendingPage`
   - `UpcomingEventsPage`
   - `PastEventsPage`
   - `ReplayDetailPage`
   - `SavedReplaysPage`
   - `EditProfilePage`
   - `StreamSettingsPage`

## Priorités d'implémentation

1. **Phase 1 (Immédiat)**
   - Route principale `/retreat-stream`
   - Layout commun
   - Pages principales de base

2. **Phase 2 (Court terme)**
   - Fonctionnalités de diffusion en direct
   - Exploration de contenu
   - Profil de base

3. **Phase 3 (Moyen terme)**
   - Fonctionnalités avancées
   - Pages détaillées
   - Intégration complète avec les autres services
