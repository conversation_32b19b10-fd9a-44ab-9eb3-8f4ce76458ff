# Documentation des Méthodes de Cryptage

Ce document détaille les différentes méthodes de cryptage implémentées dans le projet Retreat And Be, leurs cas d'utilisation optimaux, et les bonnes pratiques d'implémentation.

## Table des matières

1. [Introduction](#introduction)
2. [Méthodes de cryptage par service](#méthodes-de-cryptage-par-service)
   - [Services financiers et paiements](#services-financiers-et-paiements)
   - [Stockage décentralisé et fichiers](#stockage-décentralisé-et-fichiers)
   - [Communications entre microservices](#communications-entre-microservices)
   - [Messagerie et communications en temps réel](#messagerie-et-communications-en-temps-réel)
   - [Données médicales et informations personnelles sensibles](#données-médicales-et-informations-personnelles-sensibles)
   - [Protection contre les menaces quantiques futures](#protection-contre-les-menaces-quantiques-futures)
3. [Gestion des clés](#gestion-des-clés)
4. [Bonnes pratiques](#bonnes-pratiques)
5. [Références](#références)

## Introduction

La sécurité des données est un aspect critique de notre plateforme. Ce document présente les différentes méthodes de cryptage utilisées dans notre architecture, en fonction des besoins spécifiques de chaque service. L'objectif est de fournir une protection optimale des données tout en maintenant les performances et la facilité d'utilisation.

## Méthodes de cryptage par service

### Services financiers et paiements

#### Méthode recommandée : Chiffrement AES-256-GCM

Le service `Financial-Management` traite des données de paiement hautement sensibles qui nécessitent une protection robuste.

**Caractéristiques techniques :**
- **Algorithme :** AES (Advanced Encryption Standard)
- **Taille de clé :** 256 bits
- **Mode d'opération :** GCM (Galois/Counter Mode)
- **Authentification :** Intégrée (GMAC)
- **Vecteur d'initialisation (IV) :** 12 octets (96 bits)
- **Tag d'authentification :** 16 octets (128 bits)

**Avantages :**
- Standard de l'industrie pour les données financières
- Offre à la fois confidentialité et authentification des données
- Performances élevées grâce à l'accélération matérielle sur la plupart des processeurs modernes
- Résistance aux attaques par canal auxiliaire lorsqu'il est correctement implémenté

**Cas d'utilisation spécifiques :**
- Tokenisation des données de paiement
- Chiffrement des transactions
- Protection des informations bancaires
- Stockage sécurisé des données financières sensibles

**Exemple d'implémentation :**
```typescript
async function encryptFinancialData(data: string): Promise<EncryptedData> {
  const iv = crypto.randomBytes(12);
  const key = await getEncryptionKey(); // Récupération sécurisée de la clé

  const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
  let encrypted = cipher.update(data, 'utf8', 'base64');
  encrypted += cipher.final('base64');

  return {
    encryptedData: encrypted,
    iv: iv.toString('base64'),
    authTag: cipher.getAuthTag().toString('base64')
  };
}
```

### Stockage décentralisé et fichiers

#### Méthode recommandée : Chiffrement hybride (RSA + AES)

Le service `Decentralized-Storage` nécessite une protection forte des fichiers avec la possibilité de partage sécurisé.

**Caractéristiques techniques :**
- **Chiffrement asymétrique :** RSA-4096 pour l'échange de clés
- **Chiffrement symétrique :** AES-256-GCM pour le contenu des fichiers
- **Dérivation de clé :** PBKDF2 avec 100 000 itérations minimum
- **Hachage :** SHA-256 pour l'intégrité des fichiers

**Avantages :**
- Combine la sécurité de RSA pour l'échange de clés et la performance d'AES pour le chiffrement des données
- Permet un accès sécurisé aux fichiers par différents utilisateurs
- Adapté au stockage à long terme
- Facilite le partage sécurisé de fichiers

**Alternative pour les fichiers volumineux :**
- **ChaCha20-Poly1305**
  - Excellentes performances sur les appareils sans accélération matérielle AES
  - Résistance aux attaques par canal auxiliaire
  - Implémentation plus simple et moins sujette aux erreurs

**Exemple d'implémentation :**
```typescript
async function encryptFile(fileData: Buffer, recipientPublicKey: string): Promise<EncryptedFile> {
  // Générer une clé AES aléatoire pour ce fichier
  const fileKey = crypto.randomBytes(32);

  // Chiffrer le fichier avec AES
  const iv = crypto.randomBytes(12);
  const cipher = crypto.createCipheriv('aes-256-gcm', fileKey, iv);
  const encryptedData = Buffer.concat([cipher.update(fileData), cipher.final()]);
  const authTag = cipher.getAuthTag();

  // Chiffrer la clé AES avec la clé publique RSA du destinataire
  const encryptedKey = crypto.publicEncrypt(
    {
      key: recipientPublicKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: 'sha256'
    },
    fileKey
  );

  return {
    encryptedData,
    iv,
    authTag,
    encryptedKey
  };
}
```

### Communications entre microservices

#### Méthode recommandée : mTLS + EndToEndEncryptionService

Le service `MicroserviceSecurityService` gère les communications inter-services qui nécessitent à la fois authentification et confidentialité.

**Caractéristiques techniques :**
- **Transport Layer Security :** TLS 1.3 minimum
- **Authentification :** Certificats X.509 mutuels (mTLS)
- **Chiffrement de la charge utile :** AES-256-GCM
- **Échange de clés :** ECDHE (Elliptic Curve Diffie-Hellman Ephemeral)
- **Signature :** ECDSA avec P-256 ou supérieur

**Avantages :**
- L'authentification mutuelle TLS (mTLS) garantit l'identité des services
- Le chiffrement de bout en bout protège le contenu des messages
- Protection contre les attaques de type "man-in-the-middle"
- Isolation des services dans un modèle Zero Trust

**Configuration optimale :**
- Activer `ZERO_TRUST_MTLS_ENABLED=true`
- Utiliser des certificats correctement configurés
- Implémenter la rotation des clés
- Valider les certificats à chaque connexion

**Exemple d'implémentation :**
```typescript
// Configuration du serveur mTLS
const httpsOptions = {
  key: fs.readFileSync(path.join(__dirname, '../certs/server.key')),
  cert: fs.readFileSync(path.join(__dirname, '../certs/server.crt')),
  ca: fs.readFileSync(path.join(__dirname, '../certs/ca.crt')),
  requestCert: true,
  rejectUnauthorized: true,
  ciphers: 'TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256',
  minVersion: 'TLSv1.3'
};

// Chiffrement supplémentaire de la charge utile
async function encryptServicePayload(payload: any, targetServicePublicKey: string): Promise<string> {
  const ephemeralKeyPair = await generateEphemeralKeyPair();
  const sharedSecret = await deriveSharedSecret(
    ephemeralKeyPair.privateKey,
    targetServicePublicKey
  );

  const iv = crypto.randomBytes(12);
  const cipher = crypto.createCipheriv('aes-256-gcm', sharedSecret, iv);

  let encryptedData = cipher.update(JSON.stringify(payload), 'utf8', 'base64');
  encryptedData += cipher.final('base64');

  return JSON.stringify({
    encryptedData,
    iv: iv.toString('base64'),
    authTag: cipher.getAuthTag().toString('base64'),
    ephemeralPublicKey: ephemeralKeyPair.publicKey
  });
}
```

### Messagerie et communications en temps réel

#### Méthode recommandée : Chiffrement de bout en bout (E2EE)

Le service `messaging-service` nécessite une confidentialité totale où seuls l'expéditeur et le destinataire peuvent accéder au contenu.

**Caractéristiques techniques :**
- **Algorithme principal :** AES-256-GCM
- **Échange de clés :** Courbes elliptiques (X25519)
- **Protocole :** Inspiré de Signal Protocol (Double Ratchet)
- **Forward Secrecy :** Rotation des clés à chaque message
- **Authentification des messages :** HMAC-SHA256

**Avantages :**
- Seuls l'expéditeur et le destinataire peuvent lire les messages
- Les serveurs intermédiaires ne peuvent pas accéder au contenu
- Protection contre les fuites de données même en cas de compromission du serveur
- Confidentialité persistante grâce à la rotation des clés

**Implémentation :**
- Utiliser `EndToEndEncryptionService` avec échange de clés Diffie-Hellman
- Générer des clés éphémères pour chaque session
- Implémenter un mécanisme de vérification des clés

**Exemple d'implémentation :**
```typescript
class E2EEncryptionService {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly IV_LENGTH = 12;
  private static readonly AUTH_TAG_LENGTH = 16;

  constructor(private readonly encryptionKey: Buffer) {}

  async encrypt(plaintext: string): Promise<{
    iv: string;
    ciphertext: string;
    authTag: string;
  }> {
    const iv = crypto.randomBytes(E2EEncryptionService.IV_LENGTH);
    const cipher = crypto.createCipheriv(
      E2EEncryptionService.ALGORITHM,
      this.encryptionKey,
      iv
    ) as crypto.CipherGCM;

    let ciphertext = cipher.update(plaintext, 'utf8', 'hex');
    ciphertext += cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');

    return {
      iv: iv.toString('hex'),
      ciphertext,
      authTag
    };
  }

  async decrypt(encryptedData: {
    iv: string;
    ciphertext: string;
    authTag: string;
  }): Promise<string> {
    const iv = Buffer.from(encryptedData.iv, 'hex');
    const authTag = Buffer.from(encryptedData.authTag, 'hex');

    const decipher = crypto.createDecipheriv(
      E2EEncryptionService.ALGORITHM,
      this.encryptionKey,
      iv
    ) as crypto.DecipherGCM;

    decipher.setAuthTag(authTag);

    let plaintext = decipher.update(encryptedData.ciphertext, 'hex', 'utf8');
    plaintext += decipher.final('utf8');

    return plaintext;
  }
}
```

### Données médicales et informations personnelles sensibles

#### Méthode recommandée : Chiffrement homomorphique

Le service `HomomorphicEncryptionService` permet des opérations sur données chiffrées sans les déchiffrer.

**Caractéristiques techniques :**
- **Schéma :** BFV (Brakerski/Fan-Vercauteren) ou CKKS
- **Niveau de sécurité :** 128 bits minimum
- **Degré du polynôme :** 4096 ou 8192
- **Module de texte en clair :** 1024 (pour BFV)
- **Bits du module de coefficient :** [60, 40, 40, 60] (exemple)

**Avantages :**
- Permet d'effectuer des calculs sur les données sans les déchiffrer
- Idéal pour l'analyse de données médicales tout en préservant la confidentialité
- Protection renforcée pour les données hautement sensibles
- Conformité avec les réglementations strictes (HIPAA, RGPD)

**Cas d'utilisation :**
- Analyses statistiques sur données médicales chiffrées
- Traitement de données personnelles sensibles
- Calculs préservant la confidentialité

**Exemple d'implémentation simplifiée :**
```typescript
class HomomorphicEncryptionService {
  private readonly options: HomomorphicEncryptionOptions;

  constructor(configService: ConfigService) {
    this.options = {
      enabled: configService.get<boolean>('HOMOMORPHIC_ENCRYPTION_ENABLED', false),
      scheme: configService.get<'BFV' | 'CKKS'>('HOMOMORPHIC_ENCRYPTION_SCHEME', 'BFV'),
      securityLevel: configService.get<number>('HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL', 128),
      polyModulusDegree: configService.get<number>('HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE', 4096),
      plaintextModulus: configService.get<number>('HOMOMORPHIC_ENCRYPTION_PLAINTEXT_MODULUS', 1024),
      coeffModulusBits: configService.get<number[]>('HOMOMORPHIC_ENCRYPTION_COEFF_MODULUS_BITS', [60, 40, 40, 60])
    };
  }

  // Note: Cette implémentation est une simulation, car le vrai chiffrement homomorphique
  // nécessite des bibliothèques spécialisées comme SEAL, HElib ou PALISADE
  async encrypt(data: Buffer): Promise<Buffer> {
    if (!this.options.enabled) {
      throw new Error('Homomorphic encryption is disabled');
    }

    // Utiliser AES pour simuler le chiffrement
    const key = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);

    // Ajouter un en-tête pour simuler les métadonnées du chiffrement homomorphique
    const header = Buffer.from(`${this.options.scheme}:${iv.toString('hex')}:`);

    return Buffer.concat([header, encryptedData]);
  }
}
```

### Protection contre les menaces quantiques futures

#### Méthode recommandée : Chiffrement résistant aux ordinateurs quantiques

Le service `QuantumResistantService` offre une protection à long terme contre les menaces quantiques futures.

**Caractéristiques techniques :**
- **Algorithmes post-quantiques :**
  - Réseaux euclidiens (Kyber, NTRU)
  - Codes correcteurs d'erreurs (McEliece)
  - Isogénies (SIKE)
  - Systèmes multivariés (Rainbow)
- **Mode hybride :** Combinaison d'algorithmes classiques et post-quantiques
- **Taille de clé :** Variable selon l'algorithme (généralement plus grande que RSA)

**Avantages :**
- Résistance aux attaques par ordinateurs quantiques
- Sécurité à long terme pour les données sensibles
- Hybridation possible avec des algorithmes classiques
- Protection contre l'algorithme de Shor

**Cas d'utilisation :**
- Protection des données à très long terme
- Informations critiques nécessitant une sécurité future
- Données soumises à des réglementations strictes

**Exemple d'implémentation (mode hybride) :**
```typescript
class QuantumResistantService {
  private readonly options: {
    enabled: boolean;
    algorithm: 'kyber' | 'ntru' | 'mceliece' | 'sike' | 'hybrid';
    keySize: number;
    hybridClassicalAlgorithm: string;
    hybridClassicalKeySize: number;
  };

  constructor(configService: ConfigService) {
    this.options = {
      enabled: configService.get<boolean>('QUANTUM_RESISTANT_ENABLED', false),
      algorithm: configService.get<'kyber' | 'ntru' | 'mceliece' | 'sike' | 'hybrid'>('QUANTUM_RESISTANT_ALGORITHM', 'hybrid'),
      keySize: configService.get<number>('QUANTUM_RESISTANT_KEY_SIZE', 3072),
      hybridClassicalAlgorithm: configService.get<string>('QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM', 'rsa'),
      hybridClassicalKeySize: configService.get<number>('QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE', 4096)
    };
  }

  async hybridEncrypt(data: Buffer): Promise<Buffer> {
    // 1. Chiffrement classique (RSA)
    const classicalKey = await this.generateClassicalKey();
    const classicalEncrypted = crypto.publicEncrypt(
      {
        key: classicalKey.publicKey,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
      },
      data
    );

    // 2. Chiffrement post-quantique (simulé)
    // Note: Dans une implémentation réelle, utiliser une bibliothèque PQC comme liboqs
    const pqcKey = await this.generatePQCKey();
    const pqcEncrypted = this.simulatePQCEncryption(data, pqcKey.publicKey);

    // 3. Combiner les deux chiffrements
    const combinedData = Buffer.concat([
      Buffer.from('HYBRID:'),
      Buffer.from(classicalEncrypted.length.toString() + ':'),
      classicalEncrypted,
      pqcEncrypted
    ]);

    return combinedData;
  }

  // Méthodes auxiliaires simulées
  private async generateClassicalKey() {
    // Génération de clé RSA simulée
    return { publicKey: 'rsa-public-key', privateKey: 'rsa-private-key' };
  }

  private async generatePQCKey() {
    // Génération de clé post-quantique simulée
    return { publicKey: 'pqc-public-key', privateKey: 'pqc-private-key' };
  }

  private simulatePQCEncryption(data: Buffer, publicKey: string): Buffer {
    // Simulation de chiffrement post-quantique
    return Buffer.concat([Buffer.from('PQC-ENCRYPTED:'), data]);
  }
}
```

## Gestion des clés

La gestion sécurisée des clés de chiffrement est essentielle pour maintenir la sécurité du système.

### Principes fondamentaux

1. **Séparation des clés :** Utiliser différentes clés pour différents services et types de données
2. **Rotation des clés :** Changer régulièrement les clés (tous les 7 à 30 jours selon la sensibilité)
3. **Stockage sécurisé :** Utiliser un service dédié comme HashiCorp Vault ou AWS KMS
4. **Sauvegarde des clés :** Maintenir des sauvegardes sécurisées des clés
5. **Contrôle d'accès :** Limiter l'accès aux clés selon le principe du moindre privilège

### Implémentation recommandée

```typescript
class KeyManagementService {
  private readonly vault: VaultClient;
  private readonly keyCache: Map<string, { key: Buffer, expiry: Date }>;

  constructor(configService: ConfigService) {
    this.vault = new VaultClient({
      endpoint: configService.get<string>('VAULT_ADDR'),
      token: configService.get<string>('VAULT_TOKEN')
    });
    this.keyCache = new Map();
  }

  async getKey(keyId: string): Promise<Buffer> {
    // Vérifier si la clé est dans le cache et valide
    const cachedKey = this.keyCache.get(keyId);
    if (cachedKey && cachedKey.expiry > new Date()) {
      return cachedKey.key;
    }

    // Récupérer la clé depuis Vault
    const response = await this.vault.read(`secret/data/encryption-keys/${keyId}`);
    const key = Buffer.from(response.data.data.value, 'base64');

    // Mettre en cache avec expiration
    const expiry = new Date();
    expiry.setMinutes(expiry.getMinutes() + 15); // Cache de 15 minutes
    this.keyCache.set(keyId, { key, expiry });

    return key;
  }

  async rotateKey(keyId: string): Promise<string> {
    // Générer une nouvelle clé
    const newKey = crypto.randomBytes(32);
    const newKeyId = `${keyId}-${Date.now()}`;

    // Stocker la nouvelle clé dans Vault
    await this.vault.write(`secret/data/encryption-keys/${newKeyId}`, {
      data: { value: newKey.toString('base64') }
    });

    // Mettre à jour les métadonnées pour le suivi des versions
    await this.vault.write(`secret/data/encryption-keys-metadata/${keyId}`, {
      data: { currentVersion: newKeyId }
    });

    return newKeyId;
  }
}
```

## Bonnes pratiques

### Principes généraux

1. **Défense en profondeur :** Combiner plusieurs méthodes de chiffrement pour une protection optimale
2. **Principe du moindre privilège :** Limiter l'accès aux données chiffrées et aux clés
3. **Validation et tests :** Vérifier régulièrement l'efficacité des mécanismes de chiffrement
4. **Documentation :** Maintenir une documentation à jour des méthodes et procédures de chiffrement
5. **Formation :** Former les développeurs aux bonnes pratiques de cryptographie

### Recommandations spécifiques

1. **Ne pas implémenter ses propres algorithmes cryptographiques**
2. **Utiliser des bibliothèques cryptographiques éprouvées et maintenues**
3. **Maintenir les bibliothèques à jour pour corriger les vulnérabilités**
4. **Utiliser des générateurs de nombres aléatoires cryptographiquement sûrs**
5. **Implémenter une journalisation sécurisée des opérations cryptographiques**
6. **Prévoir des procédures de récupération en cas de perte de clés**

## Références

1. NIST Special Publication 800-57: Recommendation for Key Management
2. OWASP Cryptographic Storage Cheat Sheet
3. NIST Post-Quantum Cryptography Standardization
4. Signal Protocol Specification
5. RFC 8446: TLS 1.3
6. Homomorphic Encryption Standardization

## Mise à jour du document

Ce document doit être révisé et mis à jour régulièrement pour refléter les changements dans les méthodes de cryptage et les bonnes pratiques.

Dernière mise à jour : 18 avril 2025