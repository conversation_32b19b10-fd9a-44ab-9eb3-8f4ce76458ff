# Procédures opérationnelles pour les services de cryptage

## Introduction

Ce document décrit les procédures opérationnelles pour la gestion des services de cryptage. Il couvre les procédures de déploiement, de configuration, de maintenance, de sauvegarde et de restauration, ainsi que les procédures d'urgence.

## Table des matières

1. [Déploiement](#déploiement)
2. [Configuration](#configuration)
3. [Gestion des clés](#gestion-des-clés)
4. [Maintenance](#maintenance)
5. [Sauvegarde et restauration](#sauvegarde-et-restauration)
6. [Procédures d'urgence](#procédures-durgence)
7. [Audit et conformité](#audit-et-conformité)
8. [Surveillance et alertes](#surveillance-et-alertes)

## Déploiement

### Prérequis

Avant de déployer les services de cryptage, assurez-vous que les prérequis suivants sont satisfaits :

- Node.js 14.x ou supérieur
- NPM 6.x ou supérieur
- Docker 20.x ou supérieur (pour les déploiements conteneurisés)
- Kubernetes 1.19 ou supérieur (pour les déploiements Kubernetes)
- HashiCorp Vault 1.8 ou supérieur (pour la gestion des clés)
- Redis 6.x ou supérieur (pour le cache)

### Installation des dépendances

Installez les dépendances nécessaires pour les services de cryptage :

```bash
# Installer les dépendances de base
npm install

# Installer les dépendances pour le cryptage homomorphique
npm install seal-wasm

# Installer les dépendances pour le cryptage post-quantique
npm install liboqs-node

# Vérifier l'installation
npm run check-crypto-deps
```

### Déploiement en environnement de développement

Pour déployer les services de cryptage en environnement de développement :

```bash
# Démarrer les services de développement
npm run start:dev

# Vérifier que les services sont opérationnels
curl http://localhost:3000/api/security/health
```

### Déploiement en environnement de production

Pour déployer les services de cryptage en environnement de production :

```bash
# Construire l'application
npm run build

# Démarrer les services de production
npm run start:prod

# Vérifier que les services sont opérationnels
curl http://localhost:3000/api/security/health
```

### Déploiement avec Docker

Pour déployer les services de cryptage avec Docker :

```bash
# Construire l'image Docker
docker build -t encryption-services .

# Démarrer le conteneur
docker run -d -p 3000:3000 --name encryption-services encryption-services

# Vérifier que les services sont opérationnels
curl http://localhost:3000/api/security/health
```

### Déploiement avec Kubernetes

Pour déployer les services de cryptage avec Kubernetes :

```bash
# Appliquer les manifestes Kubernetes
kubectl apply -f kubernetes/encryption-services.yaml

# Vérifier que les pods sont opérationnels
kubectl get pods -l app=encryption-services

# Vérifier que les services sont opérationnels
kubectl port-forward svc/encryption-services 3000:3000
curl http://localhost:3000/api/security/health
```

## Configuration

### Variables d'environnement

Les services de cryptage peuvent être configurés via les variables d'environnement suivantes :

#### Configuration générale

- `NODE_ENV` : Environnement d'exécution (`development`, `production`, `test`)
- `PORT` : Port d'écoute du serveur (défaut : `3000`)
- `LOG_LEVEL` : Niveau de journalisation (`debug`, `info`, `warn`, `error`)
- `ENCRYPTION_ENABLED` : Active ou désactive les services de cryptage (défaut : `true`)

#### Configuration du cryptage standard

- `SENSITIVE_DATA_ENCRYPTION_ENABLED` : Active ou désactive le service (défaut : `true`)
- `SENSITIVE_DATA_ENCRYPTION_ALGORITHM` : Algorithme de cryptage à utiliser (défaut : `AES-256-GCM`)
- `SENSITIVE_DATA_ENCRYPTION_KEY_SIZE` : Taille de clé à utiliser (défaut : `256`)

#### Configuration du cryptage homomorphique

- `HOMOMORPHIC_ENCRYPTION_ENABLED` : Active ou désactive le service (défaut : `true`)
- `HOMOMORPHIC_ENCRYPTION_SCHEME` : Schéma de cryptage à utiliser (défaut : `CKKS`)
- `HOMOMORPHIC_ENCRYPTION_POLY_MODULUS_DEGREE` : Degré du polynôme modulaire (défaut : `8192`)
- `HOMOMORPHIC_ENCRYPTION_SECURITY_LEVEL` : Niveau de sécurité (défaut : `128`)

#### Configuration du cryptage post-quantique

- `QUANTUM_RESISTANT_ENABLED` : Active ou désactive le service (défaut : `true`)
- `QUANTUM_RESISTANT_ALGORITHM` : Algorithme post-quantique à utiliser (défaut : `kyber`)
- `QUANTUM_RESISTANT_KEY_SIZE` : Taille de clé à utiliser (défaut : `3072`)
- `QUANTUM_RESISTANT_HYBRID_MODE` : Active ou désactive le mode hybride (défaut : `true`)

#### Configuration de la gestion des clés

- `KEY_MANAGEMENT_ENABLED` : Active ou désactive le service (défaut : `true`)
- `KEY_MANAGEMENT_STORAGE` : Méthode de stockage des clés (défaut : `vault`)
- `KEY_ROTATION_INTERVAL` : Intervalle de rotation des clés en jours (défaut : `90`)

#### Configuration de Vault

- `VAULT_ENABLED` : Active ou désactive le service (défaut : `true`)
- `VAULT_ADDR` : Adresse du serveur Vault (défaut : `http://localhost:8200`)
- `VAULT_TOKEN` : Token d'authentification Vault
- `VAULT_NAMESPACE` : Namespace Vault à utiliser

#### Configuration du cache

- `CRYPTO_CACHE_ENABLED` : Active ou désactive le cache (défaut : `true`)
- `CRYPTO_CACHE_TTL` : Durée de vie du cache en millisecondes (défaut : `3600000`)
- `CRYPTO_CACHE_MAX_SIZE` : Taille maximale du cache (défaut : `1000`)

#### Configuration de l'optimisation

- `CRYPTO_OPTIMIZATION_ENABLED` : Active ou désactive l'optimisation (défaut : `true`)
- `CRYPTO_OPTIMIZATION_BATCH_SIZE` : Taille des lots par défaut (défaut : `100`)
- `CRYPTO_OPTIMIZATION_PARALLELISM` : Niveau de parallélisme par défaut (défaut : `4`)

### Fichiers de configuration

Les services de cryptage peuvent également être configurés via des fichiers de configuration :

#### Configuration générale

```json
// config/default.json
{
  "encryption": {
    "enabled": true,
    "logLevel": "info"
  }
}
```

#### Configuration spécifique à l'environnement

```json
// config/production.json
{
  "encryption": {
    "logLevel": "warn"
  }
}
```

### Configuration via l'API

Certains paramètres peuvent être configurés via l'API :

```bash
# Définir les paramètres d'optimisation
curl -X POST http://localhost:3000/api/security/crypto-optimization/params/homomorphic.encrypt \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 50, "parallelism": 2}'

# Définir le taux d'échantillonnage du profilage
curl -X POST http://localhost:3000/api/security/crypto-optimization/profiling/sampling-rate \
  -H "Content-Type: application/json" \
  -d '{"rate": 0.1}'
```

## Gestion des clés

### Génération de clés

#### Génération de clés standard

Pour générer des clés standard :

```bash
# Générer une clé AES-256
npm run generate-key -- --type=aes --size=256 --id=aes-key-1

# Générer une paire de clés RSA
npm run generate-key -- --type=rsa --size=2048 --id=rsa-key-1
```

#### Génération de clés homomorphiques

Pour générer des clés homomorphiques :

```bash
# Générer une paire de clés homomorphiques
npm run generate-homomorphic-key -- --scheme=ckks --poly-modulus-degree=8192 --id=he-key-1
```

#### Génération de clés post-quantiques

Pour générer des clés post-quantiques :

```bash
# Générer une paire de clés post-quantiques
npm run generate-quantum-key -- --algorithm=kyber --size=3072 --id=pq-key-1
```

### Rotation des clés

#### Rotation manuelle des clés

Pour effectuer une rotation manuelle des clés :

```bash
# Effectuer une rotation de clé standard
npm run rotate-key -- --id=aes-key-1

# Effectuer une rotation de clé homomorphique
npm run rotate-homomorphic-key -- --id=he-key-1

# Effectuer une rotation de clé post-quantique
npm run rotate-quantum-key -- --id=pq-key-1
```

#### Rotation automatique des clés

La rotation automatique des clés peut être configurée via les variables d'environnement :

```bash
# Configurer la rotation automatique des clés
export KEY_ROTATION_INTERVAL=90 # Rotation tous les 90 jours
export KEY_ROTATION_ENABLED=true
```

### Sauvegarde des clés

#### Sauvegarde manuelle des clés

Pour effectuer une sauvegarde manuelle des clés :

```bash
# Sauvegarder toutes les clés
npm run backup-keys -- --output=keys-backup.enc

# Sauvegarder une clé spécifique
npm run backup-keys -- --id=aes-key-1 --output=aes-key-1-backup.enc
```

#### Sauvegarde automatique des clés

La sauvegarde automatique des clés peut être configurée via les variables d'environnement :

```bash
# Configurer la sauvegarde automatique des clés
export KEY_BACKUP_INTERVAL=7 # Sauvegarde hebdomadaire
export KEY_BACKUP_ENABLED=true
export KEY_BACKUP_PATH=/path/to/backups
```

### Restauration des clés

Pour restaurer des clés à partir d'une sauvegarde :

```bash
# Restaurer toutes les clés
npm run restore-keys -- --input=keys-backup.enc

# Restaurer une clé spécifique
npm run restore-keys -- --id=aes-key-1 --input=aes-key-1-backup.enc
```

## Maintenance

### Vérification de l'état des services

Pour vérifier l'état des services de cryptage :

```bash
# Vérifier l'état général
curl http://localhost:3000/api/security/health

# Vérifier l'état du service de cryptage homomorphique
curl http://localhost:3000/api/security/homomorphic/health

# Vérifier l'état du service de cryptage post-quantique
curl http://localhost:3000/api/security/quantum/health
```

### Mise à jour des services

Pour mettre à jour les services de cryptage :

```bash
# Mettre à jour les dépendances
npm update

# Reconstruire l'application
npm run build

# Redémarrer les services
npm run restart
```

### Nettoyage du cache

Pour nettoyer le cache des services de cryptage :

```bash
# Nettoyer tout le cache
curl -X POST http://localhost:3000/api/security/crypto-optimization/cache/clear

# Nettoyer le cache pour une opération spécifique
curl -X DELETE http://localhost:3000/api/security/crypto-optimization/cache/homomorphic.encrypt
```

### Optimisation des performances

Pour optimiser les performances des services de cryptage :

```bash
# Optimiser tous les paramètres
curl -X POST http://localhost:3000/api/security/crypto-optimization/optimize/auto

# Optimiser les paramètres pour une opération spécifique
curl -X POST http://localhost:3000/api/security/crypto-optimization/optimize/homomorphic.multiply
```

## Sauvegarde et restauration

### Sauvegarde des données

#### Sauvegarde des clés

Pour sauvegarder les clés de cryptage :

```bash
# Sauvegarder toutes les clés
npm run backup-keys -- --output=keys-backup.enc

# Sauvegarder une clé spécifique
npm run backup-keys -- --id=aes-key-1 --output=aes-key-1-backup.enc
```

#### Sauvegarde des configurations

Pour sauvegarder les configurations des services de cryptage :

```bash
# Sauvegarder toutes les configurations
npm run backup-config -- --output=config-backup.json

# Sauvegarder une configuration spécifique
npm run backup-config -- --service=homomorphic --output=homomorphic-config-backup.json
```

#### Sauvegarde des politiques

Pour sauvegarder les politiques de cryptage :

```bash
# Sauvegarder toutes les politiques
curl http://localhost:3000/api/security/encryption-policy > policies-backup.json
```

### Restauration des données

#### Restauration des clés

Pour restaurer les clés de cryptage :

```bash
# Restaurer toutes les clés
npm run restore-keys -- --input=keys-backup.enc

# Restaurer une clé spécifique
npm run restore-keys -- --id=aes-key-1 --input=aes-key-1-backup.enc
```

#### Restauration des configurations

Pour restaurer les configurations des services de cryptage :

```bash
# Restaurer toutes les configurations
npm run restore-config -- --input=config-backup.json

# Restaurer une configuration spécifique
npm run restore-config -- --service=homomorphic --input=homomorphic-config-backup.json
```

#### Restauration des politiques

Pour restaurer les politiques de cryptage :

```bash
# Restaurer toutes les politiques
curl -X POST http://localhost:3000/api/security/encryption-policy/restore \
  -H "Content-Type: application/json" \
  -d @policies-backup.json
```

## Procédures d'urgence

### Révocation de clés

En cas de compromission d'une clé, suivez la procédure suivante :

1. Révocation de la clé compromise :

```bash
# Révoquer une clé
npm run revoke-key -- --id=compromised-key-id
```

2. Génération d'une nouvelle clé :

```bash
# Générer une nouvelle clé
npm run generate-key -- --type=aes --size=256 --id=new-key-id
```

3. Recryptage des données avec la nouvelle clé :

```bash
# Recrypter les données
npm run reencrypt -- --old-key-id=compromised-key-id --new-key-id=new-key-id
```

4. Notification aux utilisateurs et aux administrateurs :

```bash
# Envoyer une notification
npm run notify -- --event=key-compromised --key-id=compromised-key-id
```

### Récupération après sinistre

En cas de sinistre, suivez la procédure suivante :

1. Restauration des services :

```bash
# Restaurer les services
npm run restore-services
```

2. Restauration des clés :

```bash
# Restaurer les clés
npm run restore-keys -- --input=keys-backup.enc
```

3. Restauration des configurations :

```bash
# Restaurer les configurations
npm run restore-config -- --input=config-backup.json
```

4. Vérification de l'intégrité des données :

```bash
# Vérifier l'intégrité des données
npm run verify-data-integrity
```

5. Notification aux utilisateurs et aux administrateurs :

```bash
# Envoyer une notification
npm run notify -- --event=disaster-recovery-completed
```

### Gestion des incidents de sécurité

En cas d'incident de sécurité, suivez la procédure suivante :

1. Isolation du système compromis :

```bash
# Isoler le système
npm run isolate-system
```

2. Analyse de l'incident :

```bash
# Analyser l'incident
npm run analyze-incident -- --incident-id=incident-1
```

3. Correction de la vulnérabilité :

```bash
# Corriger la vulnérabilité
npm run fix-vulnerability -- --vulnerability-id=vuln-1
```

4. Restauration des services :

```bash
# Restaurer les services
npm run restore-services
```

5. Rapport d'incident :

```bash
# Générer un rapport d'incident
npm run generate-incident-report -- --incident-id=incident-1
```

## Audit et conformité

### Journalisation des opérations

Les services de cryptage journalisent toutes les opérations importantes :

```bash
# Consulter les journaux
npm run view-logs -- --service=encryption --level=info

# Exporter les journaux
npm run export-logs -- --service=encryption --start-date=2023-01-01 --end-date=2023-01-31 --output=logs.json
```

### Audit des clés

Pour auditer les clés de cryptage :

```bash
# Auditer toutes les clés
npm run audit-keys

# Auditer une clé spécifique
npm run audit-keys -- --id=aes-key-1

# Générer un rapport d'audit
npm run generate-key-audit-report -- --output=key-audit-report.pdf
```

### Vérification de conformité

Pour vérifier la conformité des services de cryptage :

```bash
# Vérifier la conformité GDPR
npm run check-compliance -- --standard=gdpr

# Vérifier la conformité PCI DSS
npm run check-compliance -- --standard=pci-dss

# Vérifier la conformité HIPAA
npm run check-compliance -- --standard=hipaa

# Générer un rapport de conformité
npm run generate-compliance-report -- --standard=gdpr --output=gdpr-compliance-report.pdf
```

### Rapports d'audit

Pour générer des rapports d'audit :

```bash
# Générer un rapport d'audit général
npm run generate-audit-report -- --output=audit-report.pdf

# Générer un rapport d'audit pour une période spécifique
npm run generate-audit-report -- --start-date=2023-01-01 --end-date=2023-01-31 --output=audit-report-jan-2023.pdf
```

## Surveillance et alertes

### Configuration de la surveillance

Pour configurer la surveillance des services de cryptage :

```bash
# Configurer la surveillance
npm run configure-monitoring -- --service=encryption --interval=60

# Configurer les métriques à collecter
npm run configure-metrics -- --service=encryption --metrics=performance,errors,usage
```

### Configuration des alertes

Pour configurer les alertes pour les services de cryptage :

```bash
# Configurer une alerte de performance
npm run configure-alert -- --type=performance --threshold=1000 --service=homomorphic

# Configurer une alerte d'erreur
npm run configure-alert -- --type=error --threshold=10 --service=encryption

# Configurer une alerte d'utilisation
npm run configure-alert -- --type=usage --threshold=80 --service=encryption
```

### Tableaux de bord

Pour accéder aux tableaux de bord de surveillance :

```bash
# Ouvrir le tableau de bord général
npm run open-dashboard

# Ouvrir le tableau de bord de performance
npm run open-dashboard -- --type=performance

# Ouvrir le tableau de bord de sécurité
npm run open-dashboard -- --type=security
```

### Rapports de surveillance

Pour générer des rapports de surveillance :

```bash
# Générer un rapport de surveillance général
npm run generate-monitoring-report -- --output=monitoring-report.pdf

# Générer un rapport de surveillance pour une période spécifique
npm run generate-monitoring-report -- --start-date=2023-01-01 --end-date=2023-01-31 --output=monitoring-report-jan-2023.pdf
```

## Mise à jour du document

Ce document doit être révisé et mis à jour régulièrement pour refléter les changements dans les procédures opérationnelles et les services de cryptage.

Dernière mise à jour : 18 avril 2025
