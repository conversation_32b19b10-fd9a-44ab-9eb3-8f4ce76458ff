# Documentation du QuantumResistantService

## Introduction

Le `QuantumResistantService` est un service de chiffrement conçu pour protéger les données contre les menaces quantiques futures. Il implémente des algorithmes post-quantiques et propose un mode hybride combinant des algorithmes classiques et post-quantiques pour une sécurité maximale.

## Table des matières

1. [Installation et prérequis](#installation-et-prérequis)
2. [Configuration](#configuration)
3. [Modes de fonctionnement](#modes-de-fonctionnement)
4. [API du service](#api-du-service)
5. [Formats de données](#formats-de-données)
6. [Exemples d'utilisation](#exemples-dutilisation)
7. [Tests et benchmarks](#tests-et-benchmarks)
8. [Limitations et considérations](#limitations-et-considérations)

## Installation et prérequis

### Prérequis

Pour utiliser le mode natif (implémentation réelle des algorithmes post-quantiques) :

- Node.js LTS (éviter les versions trop récentes)
- CMake (pour la compilation de liboqs)
- Ninja (pour la compilation de liboqs)
- Compilateur C/C++ (gcc/clang)
- liboqs-node (`npm install liboqs-node`)

### Vérification de l'environnement

Un script de vérification est fourni pour valider que votre environnement est correctement configuré :

```bash
# Vérifier l'environnement
./scripts/check-pqc-environment.sh
```

### Installation

1. Installer les dépendances :

```bash
npm install @nestjs/config liboqs-node
```

2. Importer le module dans votre application NestJS :

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SecurityModule } from './modules/security/security.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    SecurityModule,
  ],
})
export class AppModule {}
```

## Configuration

Le service utilise les variables d'environnement suivantes :

| Variable | Description | Valeur par défaut |
|----------|-------------|------------------|
| `QUANTUM_RESISTANT_ENABLED` | Active/désactive le chiffrement résistant aux ordinateurs quantiques | `false` |
| `QUANTUM_RESISTANT_ALGORITHM` | Algorithme à utiliser (`kyber`, `ntru`, `mceliece`, `sike`, `hybrid`) | `hybrid` |
| `QUANTUM_RESISTANT_KEY_SIZE` | Taille de clé pour les algorithmes post-quantiques | `3072` |
| `QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM` | Algorithme classique pour le mode hybride (`rsa`, `ec`, etc.) | `rsa` |
| `QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE` | Taille de clé pour l'algorithme classique | `4096` |

Exemple de configuration dans un fichier `.env` :

```
QUANTUM_RESISTANT_ENABLED=true
QUANTUM_RESISTANT_ALGORITHM=hybrid
QUANTUM_RESISTANT_KEY_SIZE=3072
QUANTUM_RESISTANT_HYBRID_CLASSICAL_ALGORITHM=rsa
QUANTUM_RESISTANT_HYBRID_CLASSICAL_KEY_SIZE=4096
```

## Modes de fonctionnement

Le service propose trois modes de fonctionnement :

### 1. Mode natif

Utilise la bibliothèque `liboqs-node` pour implémenter de véritables algorithmes post-quantiques. Ce mode est activé automatiquement si `liboqs-node` est détecté et correctement installé.

### 2. Mode simulation

Si `liboqs-node` n'est pas disponible, le service bascule automatiquement en mode simulation. Dans ce mode, les algorithmes post-quantiques sont simulés à l'aide d'algorithmes classiques (AES-GCM).

### 3. Mode hybride

Combine un algorithme classique (RSA ou ECC) avec un algorithme post-quantique pour une sécurité maximale. Les données sont chiffrées avec une clé symétrique, qui est elle-même chiffrée à la fois avec l'algorithme classique et l'algorithme post-quantique.

## API du service

### Méthodes publiques

#### `isEnabled(): boolean`

Vérifie si le chiffrement résistant aux ordinateurs quantiques est activé.

#### `async generateKeys(): Promise<void>`

Génère une paire de clés pour le chiffrement résistant aux ordinateurs quantiques.

#### `async encrypt(data: Buffer | string): Promise<Buffer>`

Chiffre des données avec la clé publique.

#### `async decrypt(encryptedData: Buffer): Promise<Buffer>`

Déchiffre des données avec la clé privée.

### Exemple d'utilisation

```typescript
import { QuantumResistantService } from './modules/security/services/quantum-resistant.service';

// Injecter le service
constructor(private readonly quantumResistantService: QuantumResistantService) {}

// Utiliser le service
async encryptSensitiveData(data: string): Promise<Buffer> {
  if (!this.quantumResistantService.isEnabled()) {
    throw new Error('Quantum-resistant encryption is not enabled');
  }
  
  return await this.quantumResistantService.encrypt(data);
}

async decryptSensitiveData(encryptedData: Buffer): Promise<string> {
  const decryptedBuffer = await this.quantumResistantService.decrypt(encryptedData);
  return decryptedBuffer.toString();
}
```

## Formats de données

### Format hybride (V2)

Le format hybride V2 est structuré comme suit :

```
HYBRID_V2:iv_length:iv:authTag_length:authTag:classical_length:classical_key:pq_length:pq_key:encrypted_data
```

Où :
- `iv_length` : Longueur de l'IV (1 octet)
- `iv` : Vecteur d'initialisation pour AES-GCM
- `authTag_length` : Longueur du tag d'authentification (1 octet)
- `authTag` : Tag d'authentification pour AES-GCM
- `classical_length` : Longueur de la clé chiffrée avec l'algorithme classique (2 octets)
- `classical_key` : Clé symétrique chiffrée avec l'algorithme classique
- `pq_length` : Longueur de la clé chiffrée avec l'algorithme post-quantique (2 octets)
- `pq_key` : Clé symétrique chiffrée avec l'algorithme post-quantique
- `encrypted_data` : Données chiffrées avec AES-GCM

### Format post-quantique natif

```
ALGO_NATIVE:iv_length:iv:authTag_length:authTag:ciphertext_length:ciphertext:encrypted_data
```

Où :
- `ALGO` : Nom de l'algorithme post-quantique (KYBER, NTRU, etc.)
- `iv_length` : Longueur de l'IV (1 octet)
- `iv` : Vecteur d'initialisation pour AES-GCM
- `authTag_length` : Longueur du tag d'authentification (1 octet)
- `authTag` : Tag d'authentification pour AES-GCM
- `ciphertext_length` : Longueur du texte chiffré par KEM (2 octets)
- `ciphertext` : Texte chiffré par KEM
- `encrypted_data` : Données chiffrées avec AES-GCM

### Format post-quantique simulé

```
ALGO_SIM:iv_length:iv:authTag_length:authTag:encrypted_data
```

Où :
- `ALGO` : Nom de l'algorithme post-quantique simulé (KYBER, NTRU, etc.)
- `iv_length` : Longueur de l'IV (1 octet)
- `iv` : Vecteur d'initialisation pour AES-GCM
- `authTag_length` : Longueur du tag d'authentification (1 octet)
- `authTag` : Tag d'authentification pour AES-GCM
- `encrypted_data` : Données chiffrées avec AES-GCM

## Tests et benchmarks

### Exécution des tests

```bash
# Exécuter les tests unitaires
npm run test -- quantum-resistant.test.ts
```

### Exécution des benchmarks

```bash
# Compiler le service
npm run build

# Exécuter le benchmark
node scripts/benchmark-quantum-resistant.js
```

## Limitations et considérations

### Mode simulation

Le mode simulation ne fournit pas une véritable sécurité post-quantique. Il est destiné uniquement au développement et aux tests lorsque `liboqs-node` n'est pas disponible.

### Compatibilité

Les données chiffrées avec le mode natif ne peuvent être déchiffrées qu'avec le mode natif. De même, les données chiffrées avec le mode simulation ne peuvent être déchiffrées qu'avec le mode simulation.

### Performance

Les algorithmes post-quantiques sont généralement plus lents que les algorithmes classiques. Le mode hybride est encore plus lent car il combine les deux types d'algorithmes.

### Sécurité

Bien que les algorithmes post-quantiques soient conçus pour résister aux attaques quantiques, ils sont encore en cours de standardisation et peuvent présenter des vulnérabilités inconnues.

---

## Références

- [liboqs-node](https://github.com/TapuCosmo/liboqs-node) - Bibliothèque Node.js pour les algorithmes post-quantiques
- [NIST Post-Quantum Cryptography](https://csrc.nist.gov/projects/post-quantum-cryptography) - Projet de standardisation des algorithmes post-quantiques
- [Open Quantum Safe](https://openquantumsafe.org/) - Projet open-source pour les algorithmes post-quantiques
