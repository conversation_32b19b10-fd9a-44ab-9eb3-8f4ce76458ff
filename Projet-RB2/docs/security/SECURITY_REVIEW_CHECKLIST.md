# Security Review Checklist

## New Feature Security Review
- [ ] Input validation implemented
- [ ] XSS protection verified
- [ ] CSRF protection in place
- [ ] Authentication checks
- [ ] Authorization rules
- [ ] Data encryption where needed
- [ ] Secure headers configured
- [ ] Rate limiting implemented
- [ ] Audit logging enabled

## Code Review Guidelines
1. Verify input sanitization
2. Check authorization logic
3. Review error handling
4. Validate secure configurations
5. Check for sensitive data exposure

## Security Testing Steps
1. Run automated SAST scans
2. Execute DAST tests
3. Perform manual penetration testing
4. Review security logs
5. Validate security controls

## Document Maintenance

This checklist should be reviewed and updated regularly to reflect new security best practices and requirements.

Last updated: April 18, 2025