# Guide de formation à la sécurité pour les développeurs

Ce guide présente les bonnes pratiques de sécurité à suivre lors du développement d'applications.

## Table des matières

1. [Introduction](#introduction)
2. [Validation des entrées](#validation-des-entrées)
3. [Authentification et autorisation](#authentification-et-autorisation)
4. [Gestion des sessions](#gestion-des-sessions)
5. [Protection contre les injections](#protection-contre-les-injections)
6. [Sécurité des fichiers](#sécurité-des-fichiers)
7. [Communications sécurisées](#communications-sécurisées)
8. [Gestion des erreurs](#gestion-des-erreurs)
9. [Journalisation et surveillance](#journalisation-et-surveillance)
10. [Dépendances tierces](#dépendances-tierces)
11. [Tests de sécurité](#tests-de-sécurité)
12. [Ressources supplémentaires](#ressources-supplémentaires)

## Introduction

La sécurité est une responsabilité partagée par tous les membres de l'équipe de développement. Ce guide vise à fournir les connaissances et les outils nécessaires pour développer des applications sécurisées.

### Principes fondamentaux

1. **Défense en profondeur** : Ne jamais compter sur une seule mesure de sécurité
2. **Principe du moindre privilège** : Accorder uniquement les permissions nécessaires
3. **Sécurité par défaut** : Les configurations par défaut doivent être sécurisées
4. **Fail-safe** : En cas d'erreur, le système doit rester dans un état sécurisé
5. **Pas de sécurité par l'obscurité** : Ne pas compter sur le secret du code pour la sécurité

## Validation des entrées

La validation des entrées est la première ligne de défense contre de nombreuses attaques.

### Bonnes pratiques

1. **Valider toutes les entrées utilisateur** : Ne jamais faire confiance aux données provenant de l'utilisateur
2. **Valider côté serveur** : La validation côté client peut être contournée
3. **Valider le type, la longueur, le format et la plage** : Utiliser des validateurs spécifiques
4. **Utiliser une liste blanche** : Accepter uniquement les entrées connues comme valides
5. **Encoder les sorties** : Encoder les données avant de les afficher

### Exemple avec class-validator

```typescript
import { IsString, IsEmail, Length, Matches } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @Length(3, 50)
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @Length(8, 100)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, {
    message: 'Password too weak'
  })
  password: string;
}
```

### Utilisation dans un contrôleur

```typescript
@Post()
async create(@Body() createUserDto: CreateUserDto) {
  // Les données sont déjà validées grâce au ValidationPipe global
  return this.usersService.create(createUserDto);
}
```

## Authentification et autorisation

L'authentification vérifie l'identité d'un utilisateur, tandis que l'autorisation détermine ce qu'un utilisateur authentifié peut faire.

### Bonnes pratiques pour l'authentification

1. **Utiliser des mécanismes éprouvés** : Ne pas implémenter ses propres algorithmes cryptographiques
2. **Stocker les mots de passe de manière sécurisée** : Utiliser bcrypt ou Argon2
3. **Mettre en place une politique de mots de passe forts** : Longueur minimale, complexité
4. **Implémenter une limitation de tentatives** : Protéger contre les attaques par force brute
5. **Utiliser l'authentification multi-facteurs** : Ajouter une couche de sécurité supplémentaire

### Exemple de hachage de mot de passe

```typescript
import * as bcrypt from 'bcrypt';

// Hachage du mot de passe
const hashedPassword = await bcrypt.hash(password, 10);

// Vérification du mot de passe
const isMatch = await bcrypt.compare(password, hashedPassword);
```

### Bonnes pratiques pour l'autorisation

1. **Implémenter un contrôle d'accès basé sur les rôles (RBAC)** : Attribuer des rôles aux utilisateurs
2. **Vérifier les autorisations à chaque requête** : Ne pas se fier aux vérifications précédentes
3. **Appliquer le principe du moindre privilège** : Accorder uniquement les permissions nécessaires
4. **Utiliser des gardes pour protéger les routes** : Centraliser la logique d'autorisation

### Exemple de garde RBAC

```typescript
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredRoles) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

### Utilisation du garde

```typescript
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class AdminController {
  // Routes protégées
}
```

## Gestion des sessions

La gestion sécurisée des sessions est essentielle pour maintenir l'état d'authentification des utilisateurs.

### Bonnes pratiques

1. **Utiliser des jetons JWT pour l'API** : Stateless et adaptés aux architectures microservices
2. **Définir une durée d'expiration courte** : Limiter la fenêtre d'exploitation en cas de vol
3. **Utiliser des jetons de rafraîchissement** : Permettre une session plus longue tout en maintenant la sécurité
4. **Stocker les jetons de manière sécurisée** : Utiliser des cookies HttpOnly avec l'attribut Secure
5. **Implémenter une liste de révocation** : Pouvoir invalider des jetons avant leur expiration

### Exemple de configuration JWT

```typescript
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: '15m',
        },
      }),
    }),
  ],
  // ...
})
```

### Exemple de service d'authentification

```typescript
@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmail(email);
    if (user && await bcrypt.compare(password, user.password)) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user: any) {
    const payload = { email: user.email, sub: user.id, roles: user.roles };
    return {
      access_token: this.jwtService.sign(payload),
      refresh_token: this.jwtService.sign(payload, { expiresIn: '7d' }),
    };
  }
}
```

## Protection contre les injections

Les injections sont parmi les vulnérabilités les plus courantes et les plus dangereuses.

### Types d'injections courants

1. **Injection SQL** : Manipulation des requêtes SQL
2. **Injection NoSQL** : Manipulation des requêtes NoSQL
3. **Injection de commandes** : Exécution de commandes système non autorisées
4. **Cross-Site Scripting (XSS)** : Injection de scripts côté client
5. **Injection de dépendances** : Manipulation des chemins d'importation

### Bonnes pratiques

1. **Utiliser des requêtes paramétrées** : Ne jamais concaténer des chaînes pour construire des requêtes
2. **Utiliser un ORM** : Les ORM comme Prisma ou TypeORM offrent une protection contre les injections SQL
3. **Échapper les caractères spéciaux** : Utiliser des fonctions d'échappement appropriées
4. **Valider et assainir les entrées** : Filtrer les caractères dangereux
5. **Implémenter une Content Security Policy (CSP)** : Limiter les sources de contenu autorisées

### Exemple avec Prisma (protection contre l'injection SQL)

```typescript
// Sécurisé - Utilise des paramètres
const user = await prisma.user.findUnique({
  where: { email }
});

// À éviter - Vulnérable à l'injection
const query = `SELECT * FROM users WHERE email = '${email}'`;
const user = await prisma.$queryRaw(query);
```

### Exemple de middleware CSP

```typescript
app.use((req, res, next) => {
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self'; object-src 'none'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; font-src 'self'; frame-src 'none'; connect-src 'self'"
  );
  next();
});
```

## Sécurité des fichiers

La gestion sécurisée des fichiers téléchargés est cruciale pour prévenir de nombreuses attaques.

### Bonnes pratiques

1. **Valider le type de fichier** : Vérifier l'extension et le contenu réel (magic bytes)
2. **Limiter la taille des fichiers** : Prévenir les attaques par déni de service
3. **Scanner les fichiers pour détecter les malwares** : Utiliser un antivirus comme ClamAV
4. **Stocker les fichiers en dehors de la racine web** : Empêcher l'accès direct aux fichiers
5. **Générer des noms de fichiers aléatoires** : Ne pas utiliser les noms fournis par l'utilisateur

### Exemple d'utilisation du service d'orchestration de sécurité des fichiers

```typescript
@Controller('files')
export class FileController {
  constructor(
    private readonly fileSecurityOrchestrator: FileSecurityOrchestrator
  ) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request
  ) {
    // Valider et enregistrer le fichier de manière sécurisée
    const result = await this.fileSecurityOrchestrator.saveFileSecurely(
      file,
      'uploads',
      AllowedFileType.DOCUMENT,
      request.user?.id,
      request.ip
    );

    if (!result.success) {
      throw new BadRequestException(result.error);
    }

    return {
      success: true,
      filePath: result.filePath
    };
  }
}
```

## Communications sécurisées

Sécuriser les communications entre les clients, les serveurs et les microservices est essentiel.

### Bonnes pratiques

1. **Utiliser HTTPS** : Chiffrer toutes les communications
2. **Implémenter HSTS** : Forcer l'utilisation de HTTPS
3. **Utiliser mTLS pour les communications inter-services** : Authentification mutuelle
4. **Configurer correctement les en-têtes de sécurité** : X-Content-Type-Options, X-Frame-Options, etc.
5. **Mettre en place une politique CORS stricte** : Limiter les origines autorisées

### Exemple d'utilisation du service de communication inter-services

```typescript
@Injectable()
export class SomeService {
  constructor(
    private readonly interServiceCommunicationService: InterServiceCommunicationService
  ) {}

  async callSecurityService() {
    // Utiliser le service de communication inter-services avec mTLS
    return this.interServiceCommunicationService.get(
      'https://security-service:3000/api/some-endpoint'
    );
  }
}
```

### Exemple de configuration CORS

```typescript
app.enableCors({
  origin: ['https://example.com', 'https://www.example.com'],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 3600
});
```

## Gestion des erreurs

Une gestion appropriée des erreurs est importante pour la sécurité et l'expérience utilisateur.

### Bonnes pratiques

1. **Ne pas exposer d'informations sensibles** : Pas de traces de pile ou de détails techniques en production
2. **Journaliser les erreurs de manière sécurisée** : Enregistrer les détails pour le débogage
3. **Utiliser des codes d'erreur génériques** : Éviter de révéler des informations spécifiques
4. **Gérer toutes les exceptions** : Ne pas laisser les erreurs non gérées se propager
5. **Retourner des réponses d'erreur cohérentes** : Format standardisé pour toutes les erreurs

### Exemple de filtre d'exception global

```typescript
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(private readonly logger: Logger) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      message = exception.message;
    }
    
    // Journaliser l'erreur avec les détails complets
    this.logger.error(`${request.method} ${request.url} - ${status}`, {
      exception,
      stack: exception instanceof Error ? exception.stack : undefined,
      body: request.body,
      params: request.params,
      query: request.query
    });
    
    // Retourner une réponse d'erreur générique
    response.status(status).json({
      statusCode: status,
      message,
      timestamp: new Date().toISOString(),
      path: request.url
    });
  }
}
```

## Journalisation et surveillance

Une journalisation et une surveillance appropriées sont essentielles pour détecter et répondre aux incidents de sécurité.

### Bonnes pratiques

1. **Journaliser les événements de sécurité** : Authentification, autorisation, modifications de données sensibles
2. **Ne pas journaliser d'informations sensibles** : Mots de passe, jetons, données personnelles
3. **Inclure des informations contextuelles** : Horodatage, ID utilisateur, adresse IP
4. **Centraliser les journaux** : Collecter les journaux de tous les services
5. **Mettre en place des alertes** : Être notifié des événements suspects

### Exemple d'utilisation du service d'événements de sécurité

```typescript
@Injectable()
export class UserService {
  constructor(
    private readonly securityEventService: SecurityEventService
  ) {}

  async login(email: string, password: string, ipAddress: string) {
    try {
      // Logique d'authentification
      const user = await this.validateUser(email, password);
      
      if (user) {
        // Journaliser l'événement de connexion réussie
        await this.securityEventService.createSecurityEvent({
          type: SecurityEventType.AUTH_LOGIN_SUCCESS,
          severity: SecuritySeverity.INFO,
          userId: user.id,
          ipAddress,
          details: {
            email
          }
        });
        
        return user;
      } else {
        // Journaliser l'événement de connexion échouée
        await this.securityEventService.createSecurityEvent({
          type: SecurityEventType.AUTH_LOGIN_FAILURE,
          severity: SecuritySeverity.MEDIUM,
          ipAddress,
          details: {
            email,
            reason: 'Invalid credentials'
          }
        });
        
        return null;
      }
    } catch (error) {
      // Journaliser l'erreur
      await this.securityEventService.createSecurityEvent({
        type: SecurityEventType.SYSTEM_ERROR,
        severity: SecuritySeverity.HIGH,
        ipAddress,
        details: {
          error: error.message,
          stack: error.stack
        }
      });
      
      throw error;
    }
  }
}
```

## Dépendances tierces

Les dépendances tierces peuvent introduire des vulnérabilités dans votre application.

### Bonnes pratiques

1. **Maintenir les dépendances à jour** : Appliquer régulièrement les mises à jour de sécurité
2. **Analyser les dépendances** : Utiliser des outils comme npm audit ou Snyk
3. **Limiter le nombre de dépendances** : Chaque dépendance augmente la surface d'attaque
4. **Vérifier la réputation des packages** : Privilégier les packages maintenus activement
5. **Utiliser le verrouillage des versions** : Éviter les mises à jour automatiques

### Exemple de script d'audit des dépendances

```bash
#!/bin/bash

echo "Auditing dependencies..."

# Audit des dépendances npm
npm audit --json > npm-audit.json
high_vulns=$(cat npm-audit.json | grep -c '"severity":"high"' || echo "0")
critical_vulns=$(cat npm-audit.json | grep -c '"severity":"critical"' || echo "0")

if [ $high_vulns -gt 0 ] || [ $critical_vulns -gt 0 ]; then
  echo "Vulnerabilities detected: $critical_vulns critical, $high_vulns high"
  exit 1
else
  echo "No high or critical vulnerabilities found"
fi
```

## Tests de sécurité

Les tests de sécurité sont essentiels pour identifier et corriger les vulnérabilités avant qu'elles ne soient exploitées.

### Types de tests de sécurité

1. **Analyse statique du code (SAST)** : Analyse du code source pour détecter les vulnérabilités
2. **Analyse dynamique (DAST)** : Tests de sécurité sur l'application en cours d'exécution
3. **Tests de pénétration** : Simulation d'attaques réelles
4. **Fuzzing** : Envoi de données aléatoires ou malformées pour provoquer des erreurs
5. **Analyse de composition logicielle (SCA)** : Analyse des dépendances

### Bonnes pratiques

1. **Intégrer les tests de sécurité dans le CI/CD** : Automatiser les tests de sécurité
2. **Définir des critères de blocage** : Empêcher le déploiement en cas de vulnérabilités critiques
3. **Effectuer des tests réguliers** : Ne pas attendre la fin du développement
4. **Combiner différents types de tests** : Chaque type de test a ses forces et ses faiblesses
5. **Corriger rapidement les vulnérabilités** : Prioriser les correctifs de sécurité

### Exemple de workflow GitHub Actions pour les tests de sécurité

```yaml
name: Security Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  security-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run security tests
      run: bash scripts/security/run-security-tests.sh
```

## Ressources supplémentaires

### Documentation officielle

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [OWASP Cheat Sheet Series](https://cheatsheetseries.owasp.org/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)

### Outils

- [OWASP ZAP](https://www.zaproxy.org/) - Outil de test de sécurité des applications web
- [Snyk](https://snyk.io/) - Analyse de vulnérabilités des dépendances
- [SonarQube](https://www.sonarqube.org/) - Analyse statique du code

### Formations

- [OWASP WebGoat](https://owasp.org/www-project-webgoat/) - Application délibérément vulnérable pour l'apprentissage
- [Portswigger Web Security Academy](https://portswigger.net/web-security) - Cours gratuits sur la sécurité web
- [Hack The Box](https://www.hackthebox.eu/) - Plateforme d'entraînement à la cybersécurité

---

**Note importante** : Ce guide doit être révisé et mis à jour régulièrement pour refléter les meilleures pratiques actuelles en matière de sécurité.
