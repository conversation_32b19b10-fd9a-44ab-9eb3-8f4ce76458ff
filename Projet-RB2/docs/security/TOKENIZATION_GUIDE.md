# Guide d'utilisation du service de tokenisation

Ce document explique comment utiliser le service de tokenisation dans l'application Retreat And Be pour sécuriser les données sensibles.

## Qu'est-ce que la tokenisation ?

La tokenisation est un processus qui remplace des données sensibles par des jetons (tokens) non sensibles. Contrairement au chiffrement, la tokenisation ne repose pas sur des algorithmes mathématiques pour protéger les données, mais sur une table de correspondance entre les jetons et les données originales.

Les avantages de la tokenisation incluent :
- Réduction de la surface d'attaque en limitant la présence de données sensibles dans les systèmes
- Simplification de la conformité aux réglementations (RGPD, PCI DSS, etc.)
- Possibilité d'utiliser les jetons dans les systèmes sans risque d'exposition des données sensibles

## Implémentation

Notre implémentation du service de tokenisation offre les fonctionnalités suivantes :

- Tokenisation de différents types de données sensibles
- Détokenisation sécurisée
- Gestion du cycle de vie des tokens (expiration, révocation)
- Préservation du format des données originales (si nécessaire)
- Statistiques d'utilisation

## Configuration

Le service de tokenisation peut être configuré via des variables d'environnement :

```env
# Activer/désactiver la tokenisation
SECURITY_TOKENIZATION_ENABLED=true

# Durée d'expiration par défaut des tokens (en secondes)
SECURITY_TOKENIZATION_DEFAULT_EXPIRATION_TIME=2592000  # 30 jours

# Format de token par défaut (UUID, ALPHANUMERIC, NUMERIC, PRESERVING)
SECURITY_TOKENIZATION_DEFAULT_TOKEN_FORMAT=UUID

# Préserver le format des données originales par défaut
SECURITY_TOKENIZATION_PRESERVE_FORMAT_BY_DEFAULT=false

# Intervalle de nettoyage des tokens expirés (en secondes)
SECURITY_TOKENIZATION_CLEANUP_INTERVAL=3600  # 1 heure
```

## Utilisation de base

### Importer le service

```typescript
import { TokenizationService } from '@modules/security/services/tokenization.service';
import { TokenFormat, TokenizationType } from '@modules/security/interfaces/tokenization.interfaces';
```

### Vérifier si la tokenisation est activée

```typescript
if (tokenizationService.isEnabled()) {
  // La tokenisation est activée et initialisée
}
```

### Tokeniser des données sensibles

```typescript
// Tokeniser avec les options par défaut
const result = await tokenizationService.tokenize('données-sensibles-123');
const token = result.token;
const tokenId = result.tokenId;
const expiresAt = result.expiresAt;

// Tokeniser avec des options personnalisées
const cardResult = await tokenizationService.tokenize('**************-1111', {
  type: TokenizationType.PAYMENT_CARD,
  format: TokenFormat.PRESERVING,
  preserveFormat: true,
  expiresIn: 3600, // 1 heure
  metadata: {
    userId: 'user-123',
    purpose: 'payment',
  },
});
```

### Détokeniser des données

```typescript
// Récupérer les données originales à partir d'un token
const originalData = await tokenizationService.detokenize(token);
```

### Révoquer un token

```typescript
// Révoquer un token (le rendre inutilisable)
await tokenizationService.revokeToken(token);
```

### Obtenir des statistiques

```typescript
// Récupérer les statistiques de tokenisation
const stats = await tokenizationService.getTokenizationStats();
console.log(`Tokens actifs: ${stats.activeTokens}`);
```

## Types de données supportés

Le service prend en charge différents types de données sensibles :

- `PAYMENT_CARD` : Numéros de cartes de paiement
- `BANK_ACCOUNT` : Numéros de comptes bancaires
- `PERSONAL_DATA` : Données personnelles (noms, adresses, etc.)
- `HEALTH_DATA` : Données de santé
- `CREDENTIALS` : Identifiants et mots de passe
- `CUSTOM` : Autres types de données sensibles

## Formats de tokens

Plusieurs formats de tokens sont disponibles :

- `UUID` : Format UUID standard (par exemple, `550e8400-e29b-41d4-a716-************`)
- `ALPHANUMERIC` : Chaîne alphanumérique aléatoire (par exemple, `a7Bf9cD3eF`)
- `NUMERIC` : Chaîne numérique aléatoire (par exemple, `**********`)
- `PRESERVING` : Préserve le format de la donnée originale (par exemple, `XXXX-XXXX-XXXX-1234` pour une carte)

## Préservation du format

La préservation du format est particulièrement utile pour les numéros de cartes de paiement et les comptes bancaires. Elle permet de conserver le format de la donnée originale tout en remplaçant la plupart des caractères par des caractères de remplacement.

Exemples :
- Carte de paiement : `**************-1111` → `XXXX-XXXX-XXXX-1111`
- Compte bancaire : `FR763000600001**********189` → `XXXXXXXXXXXXXXXXXXXXXXXXXXX89`

## Cycle de vie des tokens

Les tokens peuvent avoir différents statuts :

- `ACTIVE` : Le token est actif et peut être utilisé pour la détokenisation
- `EXPIRED` : Le token a expiré et ne peut plus être utilisé
- `REVOKED` : Le token a été révoqué manuellement et ne peut plus être utilisé

Le service nettoie automatiquement les tokens expirés à intervalles réguliers.

## Sécurité

Le service de tokenisation utilise les mesures de sécurité suivantes :

1. **Chiffrement des données originales** : Les données originales sont chiffrées avant d'être stockées dans la base de données
2. **Rotation des clés** : Intégration avec le service de gestion des clés pour la rotation des clés de chiffrement
3. **Expiration automatique** : Les tokens peuvent expirer automatiquement après une période définie
4. **Journalisation des événements** : Tous les événements de tokenisation sont journalisés
5. **Contrôle d'accès** : Seuls les utilisateurs autorisés peuvent accéder aux fonctionnalités de tokenisation

## Bonnes pratiques

1. **Définir des durées d'expiration appropriées** : Utilisez des durées d'expiration adaptées à la sensibilité des données
2. **Utiliser des types spécifiques** : Spécifiez toujours le type de donnée tokenisée pour une meilleure traçabilité
3. **Préserver le format si nécessaire** : Utilisez la préservation du format uniquement lorsque c'est nécessaire pour l'intégration avec des systèmes existants
4. **Révoquer les tokens inutilisés** : Révoquez les tokens dès qu'ils ne sont plus nécessaires
5. **Surveiller les statistiques** : Surveillez régulièrement les statistiques de tokenisation pour détecter des anomalies

## Intégration avec les services financiers

Le service de tokenisation s'intègre parfaitement avec les services financiers de l'application :

```typescript
// Dans un service de paiement
async processPayment(userId: string, cardNumber: string, amount: number) {
  // Tokeniser la carte
  const tokenResult = await this.tokenizationService.tokenize(cardNumber, {
    type: TokenizationType.PAYMENT_CARD,
    format: TokenFormat.PRESERVING,
    metadata: { userId, purpose: 'payment' },
  });
  
  // Stocker le token au lieu du numéro de carte
  await this.paymentRepository.savePaymentMethod(userId, tokenResult.token);
  
  // Traiter le paiement avec le processeur de paiement
  // (Détokeniser uniquement au moment du traitement)
  const realCardNumber = await this.tokenizationService.detokenize(tokenResult.token);
  const paymentResult = await this.paymentProcessor.processPayment(realCardNumber, amount);
  
  return paymentResult;
}
```

## Exemples d'utilisation

Consultez le fichier d'exemple `src/modules/security/examples/tokenization-example.ts` pour des exemples complets d'utilisation du service.

## Cas d'utilisation

La tokenisation est particulièrement utile dans les scénarios suivants :

1. **Stockage des informations de paiement** : Tokeniser les numéros de cartes de paiement pour se conformer à la norme PCI DSS
2. **Protection des données personnelles** : Tokeniser les données personnelles pour se conformer au RGPD
3. **Sécurisation des données de santé** : Tokeniser les données de santé sensibles
4. **Partage de données avec des tiers** : Partager des tokens au lieu de données sensibles avec des partenaires

## Résolution des problèmes

### Le token ne peut pas être détokenisé

Causes possibles :
- Le token a expiré
- Le token a été révoqué
- Le token n'existe pas dans la base de données

Solution : Vérifiez le statut du token et créez un nouveau token si nécessaire.

### Erreur lors de la tokenisation

Causes possibles :
- Le service de tokenisation n'est pas activé
- Problème avec le service de gestion des clés
- Problème avec la base de données

Solution : Vérifiez les journaux pour plus de détails et assurez-vous que tous les services dépendants fonctionnent correctement.

## Ressources supplémentaires

- [PCI DSS Tokenization Guidelines](https://www.pcisecuritystandards.org/documents/Tokenization_Guidelines_Info_Supplement.pdf)
- [NIST SP 800-38G](https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-38G.pdf) - Recommandations pour la tokenisation préservant le format
