/**
 * Exemple d'utilisation du HybridEncryptionService
 * 
 * Ce fichier montre comment utiliser le service de chiffrement hybride pour
 * sécuriser les fichiers stockés sur IPFS.
 */

import { HybridEncryptionService } from '@decentralized-storage/security';
import { SecureIPFSService } from '@decentralized-storage/services';
import * as fs from 'fs';
import * as path from 'path';

async function hybridEncryptionExample() {
  console.log('=== Exemple de chiffrement hybride ===');

  // Obtenir une instance du service de chiffrement
  const encryptionService = new HybridEncryptionService();
  
  // 1. Génération de paires de clés
  console.log('\n1. Génération de paires de clés');
  const aliceKeyPair = encryptionService.generateKeyPair();
  const bobKeyPair = encryptionService.generateKeyPair();
  
  console.log(`Clé publique d'Alice : ${aliceKeyPair.publicKey.substring(0, 32)}...`);
  console.log(`Clé privée d'Alice : ${aliceKeyPair.privateKey.substring(0, 32)}...`);
  console.log(`Clé publique de Bob : ${bobKeyPair.publicKey.substring(0, 32)}...`);
  
  // 2. Chiffrement d'un fichier
  console.log('\n2. Chiffrement d\'un fichier');
  const fileData = Buffer.from('Contenu du fichier confidentiel');
  
  const encryptedData = await encryptionService.encrypt(fileData, aliceKeyPair.publicKey);
  
  console.log(`Données chiffrées : ${encryptedData.data.toString('hex').substring(0, 32)}...`);
  console.log(`IV : ${encryptedData.iv.toString('hex')}`);
  console.log(`Tag d'authentification : ${encryptedData.tag.toString('hex')}`);
  console.log(`Clé chiffrée : ${encryptedData.encryptedKey.toString('hex').substring(0, 32)}...`);
  
  // 3. Sérialisation des données chiffrées
  console.log('\n3. Sérialisation des données chiffrées');
  const serializedData = encryptionService.serializeEncryptedData(encryptedData);
  
  console.log(`Données sérialisées : ${serializedData.toString('hex').substring(0, 64)}...`);
  
  // 4. Désérialisation des données chiffrées
  console.log('\n4. Désérialisation des données chiffrées');
  const deserializedData = encryptionService.deserializeEncryptedData(serializedData);
  
  console.log(`Données désérialisées : ${deserializedData.data.toString('hex').substring(0, 32)}...`);
  console.log(`IV désérialisé : ${deserializedData.iv.toString('hex')}`);
  console.log(`Tag d'authentification désérialisé : ${deserializedData.tag.toString('hex')}`);
  console.log(`Clé chiffrée désérialisée : ${deserializedData.encryptedKey.toString('hex').substring(0, 32)}...`);
  
  // 5. Déchiffrement d'un fichier
  console.log('\n5. Déchiffrement d\'un fichier');
  const decryptedData = await encryptionService.decrypt(deserializedData, aliceKeyPair.privateKey);
  
  console.log(`Données déchiffrées : ${decryptedData.toString()}`);
  
  // 6. Chiffrement pour plusieurs destinataires
  console.log('\n6. Chiffrement pour plusieurs destinataires');
  const charlieKeyPair = encryptionService.generateKeyPair();
  
  const multiRecipientResult = await encryptionService.encryptForMultipleRecipients(
    fileData,
    [aliceKeyPair.publicKey, bobKeyPair.publicKey, charlieKeyPair.publicKey]
  );
  
  console.log(`Données chiffrées pour plusieurs destinataires : ${multiRecipientResult.data.toString('hex').substring(0, 32)}...`);
  console.log(`Nombre de clés chiffrées : ${multiRecipientResult.encryptedKeys.length}`);
  
  // 7. Déchiffrement par différents destinataires
  console.log('\n7. Déchiffrement par différents destinataires');
  
  // Alice déchiffre
  const aliceEncryptedData = {
    data: multiRecipientResult.data,
    iv: multiRecipientResult.iv,
    tag: multiRecipientResult.tag,
    encryptedKey: multiRecipientResult.encryptedKeys[0]
  };
  
  const aliceDecryptedData = await encryptionService.decrypt(aliceEncryptedData, aliceKeyPair.privateKey);
  console.log(`Données déchiffrées par Alice : ${aliceDecryptedData.toString()}`);
  
  // Bob déchiffre
  const bobEncryptedData = {
    data: multiRecipientResult.data,
    iv: multiRecipientResult.iv,
    tag: multiRecipientResult.tag,
    encryptedKey: multiRecipientResult.encryptedKeys[1]
  };
  
  const bobDecryptedData = await encryptionService.decrypt(bobEncryptedData, bobKeyPair.privateKey);
  console.log(`Données déchiffrées par Bob : ${bobDecryptedData.toString()}`);
  
  // 8. Calcul d'empreinte de fichier
  console.log('\n8. Calcul d\'empreinte de fichier');
  const fileHash = encryptionService.calculateFileHash(fileData);
  
  console.log(`Empreinte du fichier : ${fileHash}`);
  
  // 9. Intégration avec IPFS (si disponible)
  console.log('\n9. Intégration avec IPFS');
  try {
    const ipfsService = new SecureIPFSService();
    
    // Stocker un fichier sur IPFS
    const result = await ipfsService.storeFile(
      fileData,
      {
        name: 'example.txt',
        size: fileData.length,
        type: 'text/plain',
        lastModified: Date.now(),
        owner: 'alice'
      },
      true,  // chiffrer
      aliceKeyPair.publicKey,
      [bobKeyPair.publicKey]  // partager avec Bob
    );
    
    console.log(`Fichier stocké sur IPFS avec CID : ${result.cid}`);
    console.log(`Métadonnées : ${JSON.stringify(result.metadata, null, 2)}`);
    
    // Récupérer le fichier depuis IPFS
    const retrievedFile = await ipfsService.retrieveFile(result.cid, aliceKeyPair.privateKey);
    console.log(`Fichier récupéré depuis IPFS : ${retrievedFile.toString()}`);
    
    // Partager le fichier avec Charlie
    const encryptedKeyForCharlie = await ipfsService.shareFile(
      result.cid,
      aliceKeyPair.privateKey,
      charlieKeyPair.publicKey
    );
    
    console.log(`Clé chiffrée pour Charlie : ${encryptedKeyForCharlie}`);
  } catch (error) {
    console.log(`IPFS non disponible : ${error.message}`);
  }
}

// Exécuter l'exemple
hybridEncryptionExample().catch(error => {
  console.error('Erreur :', error);
});

/**
 * Sortie attendue :
 * 
 * === Exemple de chiffrement hybride ===
 * 
 * 1. Génération de paires de clés
 * Clé publique d'Alice : -----BEGIN PUBLIC KEY-----MIICIj...
 * Clé privée d'Alice : -----BEGIN PRIVATE KEY-----MIIJK...
 * Clé publique de Bob : -----BEGIN PUBLIC KEY-----MIICIj...
 * 
 * 2. Chiffrement d'un fichier
 * Données chiffrées : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * IV : 123456789012
 * Tag d'authentification : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
 * Clé chiffrée : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * 
 * 3. Sérialisation des données chiffrées
 * Données sérialisées : 000c123456789012000fa1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p600000100a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * 
 * 4. Désérialisation des données chiffrées
 * Données désérialisées : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * IV désérialisé : 123456789012
 * Tag d'authentification désérialisé : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
 * Clé chiffrée désérialisée : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * 
 * 5. Déchiffrement d'un fichier
 * Données déchiffrées : Contenu du fichier confidentiel
 * 
 * 6. Chiffrement pour plusieurs destinataires
 * Données chiffrées pour plusieurs destinataires : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 * Nombre de clés chiffrées : 3
 * 
 * 7. Déchiffrement par différents destinataires
 * Données déchiffrées par Alice : Contenu du fichier confidentiel
 * Données déchiffrées par Bob : Contenu du fichier confidentiel
 * 
 * 8. Calcul d'empreinte de fichier
 * Empreinte du fichier : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6
 * 
 * 9. Intégration avec IPFS
 * IPFS non disponible : connect ECONNREFUSED 127.0.0.1:5001
 */
