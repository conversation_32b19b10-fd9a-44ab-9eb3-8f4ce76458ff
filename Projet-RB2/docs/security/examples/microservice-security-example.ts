/**
 * Exemple d'utilisation du MicroserviceSecurityService
 * 
 * Ce fichier montre comment utiliser le service de sécurité pour les microservices
 * afin de sécuriser les communications entre les différents services de la plateforme.
 */

import { MicroserviceSecurityService } from '@security/microservice';
import { CertificateManagementService } from '@security/certificates';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';

async function microserviceSecurityExample() {
  console.log('=== Exemple de sécurité pour les microservices ===');

  // Obtenir une instance du service de sécurité
  const securityService = new MicroserviceSecurityService();
  const certService = new CertificateManagementService();
  
  // 1. Vérification de la configuration mTLS
  console.log('\n1. Vérification de la configuration mTLS');
  
  const mtlsEnabled = securityService.isMtlsEnabled();
  console.log(`mTLS activé : ${mtlsEnabled}`);
  
  if (!mtlsEnabled) {
    console.log('Configuration des variables d\'environnement pour activer mTLS :');
    console.log('  ZERO_TRUST_MTLS_ENABLED=true');
    console.log('  CERT_PATH=/path/to/certs');
    console.log('  PRIVATE_KEY_PATH=/path/to/private/key');
    console.log('  CA_CERT_PATH=/path/to/ca/cert');
  }
  
  // 2. Génération de certificats (si nécessaire)
  console.log('\n2. Génération de certificats');
  
  try {
    // Générer un certificat pour le service actuel
    const serviceInfo = {
      commonName: 'example-service',
      organization: 'Retreat And Be',
      organizationalUnit: 'Security',
      locality: 'Paris',
      country: 'FR',
      validityDays: 365
    };
    
    const { cert, key } = await certService.generateServiceCertificate(serviceInfo);
    
    console.log(`Certificat généré :`);
    console.log(`  Sujet : ${cert.subject.toString()}`);
    console.log(`  Émetteur : ${cert.issuer.toString()}`);
    console.log(`  Valide du ${cert.validFrom} au ${cert.validTo}`);
    
    // Sauvegarder le certificat et la clé (pour l'exemple)
    const certDir = path.join(__dirname, 'certs');
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }
    
    fs.writeFileSync(path.join(certDir, 'service.crt'), cert.raw);
    fs.writeFileSync(path.join(certDir, 'service.key'), key);
    
    console.log(`Certificat et clé sauvegardés dans ${certDir}`);
  } catch (error) {
    console.log(`Génération de certificat non disponible : ${error.message}`);
  }
  
  // 3. Chiffrement d'une charge utile pour un autre service
  console.log('\n3. Chiffrement d\'une charge utile pour un autre service');
  
  // Simuler une clé publique d'un service cible
  const targetServicePublicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvWpIQFjQQCPpaIlJKpeg
irp5kLkzLB1AxHmnLk4IxuKpIvXzEpMQhLm0DTc1jjGCsK5z5S8RJhZ+qpZQMUzO
SoKpVz7Estvf5wDIvHJZZoTwNLtZ1U7jTtZvDwEK6UJM5vOcOX7KfmYz+YujV5ja
vA8VKKBQQKPJUofVYBv/GD/yOTvRYQFxdFSKh7lOjYgr6cYkwqpbPzFzGYNnKdQp
TBQEJwRHCx1uhB4Jw3lZFJcKKPxVOHWyYl5+0xtRTjQZ8fI0vdCRGHmZYFP0+ZK5
3Gg3PVer/EaWOyJEn1kP5zYYeAFgGX2hhMQHZtRjgLpjGkpF8YgQKNY5FPvn0Lvj
ZQIDAQAB
-----END PUBLIC KEY-----`;
  
  // Données à envoyer à un autre service
  const payload = {
    action: 'processPayment',
    data: {
      amount: 100.00,
      currency: 'EUR',
      paymentMethod: {
        type: 'credit_card',
        cardNumber: '****************',
        expiryDate: '12/25',
        cvv: '123'
      },
      customer: {
        id: 'cust_123456789',
        name: 'John Doe',
        email: '<EMAIL>'
      }
    }
  };
  
  try {
    // Chiffrer la charge utile
    const encryptedPayload = await securityService.encryptServicePayload(
      payload,
      targetServicePublicKey
    );
    
    console.log(`Charge utile chiffrée :`);
    console.log(`  IV : ${encryptedPayload.iv}`);
    console.log(`  Données chiffrées : ${encryptedPayload.data.substring(0, 32)}...`);
    console.log(`  Tag d'authentification : ${encryptedPayload.authTag}`);
    console.log(`  Clé chiffrée : ${encryptedPayload.encryptedKey.substring(0, 32)}...`);
    console.log(`  Service source : ${encryptedPayload.sourceService}`);
    console.log(`  Horodatage : ${encryptedPayload.timestamp}`);
    
    // 4. Déchiffrement d'une charge utile (simulation)
    console.log('\n4. Déchiffrement d\'une charge utile (simulation)');
    
    // Simuler une clé privée du service cible
    const targetServicePrivateKey = `-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9akhAWNBAI+lo
iUkql6CKunmQuTMsHUDEeacuTgjG4qki9fMSkxCEubQNNzWOMYKwrnPlLxEmFn6q
llAxTM5KgqlXPsSy29/nAMi8cllmhPA0u1nVTuNO1m8PAQrpQkzm85w5fsp+ZjP5
i6NXmNq8DxUooFBAo8lSh9VgG/8YP/I5O9FhAXF0VIqHuU6NiCvpxiTCqls/MXMZ
g2cp1ClMFAQnBEcLHW6EHgnDeVkUlwoo/FU4dbJiXn7TG1FONBnx8jS90JEYeZlg
U/T5krnc6Dc9V6v8RpY7IkSfWQ/nNhh4AWAZfaGExAdm1GOAumMaSkXxiBAo1jkU
++fQu+NlAgMBAAECggEAJxK9OXZ9MQH+vLUWS9YKJ9WJKVKpnQyV8J/EDdWlrIU1
BQ/7QlAUxoHdPVYz4zyGT0GdFHhYhCDLD5UwcCLxBVZq/VdpU5xGqpwvtJZW9zPP
FQIDAQAB
-----END PRIVATE KEY-----`;
    
    // Déchiffrer la charge utile
    const decryptedPayload = await securityService.decryptServicePayload(
      encryptedPayload,
      targetServicePrivateKey
    );
    
    console.log(`Charge utile déchiffrée :`);
    console.log(JSON.stringify(decryptedPayload, null, 2));
  } catch (error) {
    console.log(`Chiffrement/déchiffrement non disponible : ${error.message}`);
  }
  
  // 5. Création d'un client HTTPS avec mTLS
  console.log('\n5. Création d\'un client HTTPS avec mTLS');
  
  try {
    // Récupérer les certificats
    const clientCert = await certService.getServiceCertificate();
    const clientKey = await certService.getServicePrivateKey();
    const caCert = await certService.getCACertificate();
    
    // Créer les options pour le client HTTPS
    const httpsOptions = {
      cert: clientCert,
      key: clientKey,
      ca: caCert,
      rejectUnauthorized: true  // Vérifier le certificat du serveur
    };
    
    console.log(`Options HTTPS configurées avec mTLS :`);
    console.log(`  Certificat client : ${clientCert ? 'Présent' : 'Absent'}`);
    console.log(`  Clé privée client : ${clientKey ? 'Présente' : 'Absente'}`);
    console.log(`  Certificat CA : ${caCert ? 'Présent' : 'Absent'}`);
    
    // Exemple de requête HTTPS avec mTLS (simulation)
    console.log(`\nExemple de requête HTTPS avec mTLS (simulation) :`);
    console.log(`  const req = https.request({`);
    console.log(`    hostname: 'api.example-service.retreatandbe.com',`);
    console.log(`    port: 443,`);
    console.log(`    path: '/api/v1/resource',`);
    console.log(`    method: 'POST',`);
    console.log(`    ...httpsOptions,`);
    console.log(`    headers: {`);
    console.log(`      'Content-Type': 'application/json',`);
    console.log(`      'X-Service-ID': 'example-service'`);
    console.log(`    }`);
    console.log(`  }, (res) => {`);
    console.log(`    // Traitement de la réponse`);
    console.log(`  });`);
    console.log(`  `);
    console.log(`  req.write(JSON.stringify(encryptedPayload));`);
    console.log(`  req.end();`);
  } catch (error) {
    console.log(`Configuration mTLS non disponible : ${error.message}`);
  }
  
  // 6. Validation d'un certificat client
  console.log('\n6. Validation d\'un certificat client');
  
  try {
    // Simuler un certificat client
    const clientCertPem = `-----BEGIN CERTIFICATE-----
MIIDazCCAlOgAwIBAgIUJjGSCcGVSQQSxhzJe/gvFGSFJMwwDQYJKoZIhvcNAQEL
BQAwRTELMAkGA1UEBhMCRlIxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoM
GEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDAeFw0yMzA2MDExMjAwMDBaFw0yNDA2
MDExMjAwMDBaMEUxCzAJBgNVBAYTAkZSMRMwEQYDVQQIDApTb21lLVN0YXRlMSEw
HwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQwggEiMA0GCSqGSIb3DQEB
AQUAA4IBDwAwggEKAoIBAQC9akhAWNBAI+loiUkql6CKunmQuTMsHUDEeacuTgjG
4qki9fMSkxCEubQNNzWOMYKwrnPlLxEmFn6qllAxTM5KgqlXPsSy29/nAMi8cllm
hPA0u1nVTuNO1m8PAQrpQkzm85w5fsp+ZjP5i6NXmNq8DxUooFBAo8lSh9VgG/8Y
P/I5O9FhAXF0VIqHuU6NiCvpxiTCqls/MXMZg2cp1ClMFAQnBEcLHW6EHgnDeVkU
lwoo/FU4dbJiXn7TG1FONBnx8jS90JEYeZlgU/T5krnc6Dc9V6v8RpY7IkSfWQ/n
Nhh4AWAZfaGExAdm1GOAumMaSkXxiBAo1jkU++fQu+NlAgMBAAGjUzBRMB0GA1Ud
DgQWBBQcZGQc0QwJUCOXwHIxJDzYbGFRvjAfBgNVHSMEGDAWgBQcZGQc0QwJUCOX
wHIxJDzYbGFRvjAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAb
cRhLRwS5qDHeDgxc3cAF9XObl+ZEgTQEuRNQIm4MIgQPZGNYIFEKRnRIEUjJYmNw
6ZUglnH4JjYYXGJBGEOOGKhK0W0iX1aQNw2BLcAHkLEjZiPTtXqNRdKTYC5l+BSk
CQIDAQAB
-----END CERTIFICATE-----`;
    
    // Valider le certificat client
    const isValid = await securityService.validateClientCertificate(clientCertPem);
    
    console.log(`Certificat client valide : ${isValid}`);
    
    // Vérifier si le certificat est révoqué
    const isRevoked = await securityService.isCertificateRevoked(clientCertPem);
    
    console.log(`Certificat client révoqué : ${isRevoked}`);
  } catch (error) {
    console.log(`Validation de certificat non disponible : ${error.message}`);
  }
  
  // 7. Rotation des certificats
  console.log('\n7. Rotation des certificats');
  
  try {
    // Vérifier si une rotation est nécessaire
    const rotationNeeded = await certService.isRotationNeeded();
    
    console.log(`Rotation des certificats nécessaire : ${rotationNeeded}`);
    
    if (rotationNeeded) {
      // Effectuer la rotation des certificats
      await certService.rotateCertificates();
      console.log('Rotation des certificats effectuée');
    }
  } catch (error) {
    console.log(`Rotation de certificat non disponible : ${error.message}`);
  }
}

// Exécuter l'exemple
microserviceSecurityExample().catch(error => {
  console.error('Erreur :', error);
});

/**
 * Sortie attendue :
 * 
 * === Exemple de sécurité pour les microservices ===
 * 
 * 1. Vérification de la configuration mTLS
 * mTLS activé : false
 * Configuration des variables d'environnement pour activer mTLS :
 *   ZERO_TRUST_MTLS_ENABLED=true
 *   CERT_PATH=/path/to/certs
 *   PRIVATE_KEY_PATH=/path/to/private/key
 *   CA_CERT_PATH=/path/to/ca/cert
 * 
 * 2. Génération de certificats
 * Génération de certificat non disponible : certService.generateServiceCertificate is not a function
 * 
 * 3. Chiffrement d'une charge utile pour un autre service
 * Charge utile chiffrée :
 *   IV : a1b2c3d4e5f6g7h8
 *   Données chiffrées : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 *   Tag d'authentification : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
 *   Clé chiffrée : a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6...
 *   Service source : example-service
 *   Horodatage : 2023-06-01T12:00:00.000Z
 * 
 * 4. Déchiffrement d'une charge utile (simulation)
 * Charge utile déchiffrée :
 * {
 *   "action": "processPayment",
 *   "data": {
 *     "amount": 100,
 *     "currency": "EUR",
 *     "paymentMethod": {
 *       "type": "credit_card",
 *       "cardNumber": "****************",
 *       "expiryDate": "12/25",
 *       "cvv": "123"
 *     },
 *     "customer": {
 *       "id": "cust_123456789",
 *       "name": "John Doe",
 *       "email": "<EMAIL>"
 *     }
 *   }
 * }
 * 
 * 5. Création d'un client HTTPS avec mTLS
 * Configuration mTLS non disponible : certService.getServiceCertificate is not a function
 * 
 * 6. Validation d'un certificat client
 * Certificat client valide : true
 * Certificat client révoqué : false
 * 
 * 7. Rotation des certificats
 * Rotation de certificat non disponible : certService.isRotationNeeded is not a function
 */
