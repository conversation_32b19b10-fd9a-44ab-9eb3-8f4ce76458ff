# Workshop : Cryptographie Appliquée

## Jour 1 : Fondamentaux

### Module 1 : Introduction à la Cryptographie Moderne
- Principes de base
- Cryptographie symétrique vs asymétrique
- Hachage et signatures

### Exercice Pratique 1
```typescript
// Implémentation d'un système de chiffrement simple
class BasicEncryption {
  async encrypt(data: string, key: <PERSON><PERSON><PERSON>): Promise<EncryptedData> {
    // Exercice : Implémenter le chiffrement AES-GCM
  }

  async decrypt(encryptedData: EncryptedData, key: Buffer): Promise<string> {
    // Exercice : Implémenter le déchiffrement
  }
}
```

## Jour 2 : Applications Avancées

### Module 4 : Cryptographie Post-Quantique
- Introduction aux menaces quantiques
- Algorithmes résistants aux quantiques
- Implémentation pratique
[...]