# Spécification API

## Endpoints

### Encryption Service

#### POST /api/v1/encrypt
Chi<PERSON>re des données avec l'algorithme spécifié.

```typescript
interface EncryptRequest {
  data: string | Buffer;
  algorithm: 'AES' | 'HOMOMORPHIC' | 'POST_QUANTUM';
  options?: {
    keyId?: string;
    mode?: string;
    padding?: string;
  };
}

interface EncryptResponse {
  ciphertext: string;
  metadata: {
    algorithm: string;
    keyId: string;
    timestamp: string;
  };
}
```

#### POST /api/v1/decrypt
Déchiffre des données précédemment chiffrées.

```typescript
interface DecryptRequest {
  ciphertext: string;
  metadata: {
    algorithm: string;
    keyId: string;
  };
}

interface DecryptResponse {
  plaintext: string;
  metadata: {
    timestamp: string;
  };
}
```

### Key Management

#### POST /api/v1/keys/rotate
Déclenche une rotation de clés.

```typescript
interface KeyRotationRequest {
  keyId: string;
  reason?: string;
}

interface KeyRotationResponse {
  newKeyId: string;
  rotationTimestamp: string;
  status: 'SUCCESS' | 'PENDING' | 'FAILED';
}
```