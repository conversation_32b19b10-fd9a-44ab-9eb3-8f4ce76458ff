# Guide des bonnes pratiques de test

Ce guide présente les bonnes pratiques à suivre pour écrire des tests efficaces et maintenables.

## Principes généraux

### 1. Écrire des tests avant le code (TDD)

Le développement piloté par les tests (TDD) suit un cycle en trois étapes:
1. **Red**: Écrire un test qui échoue
2. **Green**: Écrire le code minimal pour faire passer le test
3. **Refactor**: Améliorer le code sans changer son comportement

Avantages:
- Garantit que chaque fonctionnalité est testée
- Encourage un design modulaire et découplé
- Réduit les bugs et les régressions

### 2. Suivre le principe AAA (Arrange-Act-Assert)

Structurez vos tests en trois parties distinctes:
1. **Arrange**: Préparer les données et l'environnement
2. **Act**: Exécuter l'action à tester
3. **Assert**: Vérifier le résultat

Exemple:
```typescript
it('should calculate total price with tax', () => {
  // Arrange
  const cart = new ShoppingCart();
  cart.addItem({ id: '1', price: 100, quantity: 2 });
  
  // Act
  const total = cart.calculateTotal(0.2); // 20% tax
  
  // Assert
  expect(total).toBe(240); // (100 * 2) * 1.2
});
```

### 3. Tester les comportements, pas l'implémentation

Concentrez-vous sur ce que le code fait, pas sur comment il le fait:
- Testez les entrées et les sorties, pas les détails internes
- Évitez de tester les méthodes privées directement
- Utilisez des mocks pour les dépendances externes, pas pour les classes testées

### 4. Maintenir l'indépendance des tests

Chaque test doit être indépendant des autres:
- Ne pas partager d'état entre les tests
- Réinitialiser l'environnement avant chaque test
- Éviter les dépendances entre les tests

## Types de tests

### Tests unitaires

Les tests unitaires vérifient le comportement d'une unité de code isolée:
- Une fonction, une méthode ou une classe
- Toutes les dépendances sont mockées
- Rapides et légers

Exemple:
```typescript
describe('UserService', () => {
  let userService: UserService;
  let mockUserRepository: jest.Mocked<UserRepository>;
  
  beforeEach(() => {
    mockUserRepository = {
      findById: jest.fn(),
      save: jest.fn(),
    } as any;
    
    userService = new UserService(mockUserRepository);
  });
  
  it('should find user by id', async () => {
    // Arrange
    const mockUser = { id: '1', name: 'John' };
    mockUserRepository.findById.mockResolvedValue(mockUser);
    
    // Act
    const user = await userService.findById('1');
    
    // Assert
    expect(user).toEqual(mockUser);
    expect(mockUserRepository.findById).toHaveBeenCalledWith('1');
  });
});
```

### Tests d'intégration

Les tests d'intégration vérifient l'interaction entre plusieurs composants:
- Plusieurs classes ou modules
- Certaines dépendances peuvent être réelles
- Plus lents que les tests unitaires

Exemple:
```typescript
describe('AuthenticationFlow', () => {
  let authService: AuthService;
  let userService: UserService;
  let tokenService: TokenService;
  let mockDatabase: MockDatabase;
  
  beforeEach(async () => {
    mockDatabase = new MockDatabase();
    await mockDatabase.connect();
    
    userService = new UserService(mockDatabase.getUserRepository());
    tokenService = new TokenService();
    authService = new AuthService(userService, tokenService);
  });
  
  afterEach(async () => {
    await mockDatabase.disconnect();
  });
  
  it('should authenticate user and generate token', async () => {
    // Arrange
    await mockDatabase.seedUser({ username: 'john', password: 'password123' });
    
    // Act
    const result = await authService.login('john', 'password123');
    
    // Assert
    expect(result.success).toBe(true);
    expect(result.token).toBeDefined();
    expect(result.user.username).toBe('john');
  });
});
```

### Tests de sécurité

Les tests de sécurité vérifient que l'application est protégée contre les vulnérabilités:
- Injection SQL
- Cross-Site Scripting (XSS)
- Cross-Site Request Forgery (CSRF)
- Validation des entrées

Exemple:
```typescript
describe('InputValidation', () => {
  it('should reject SQL injection attempts', () => {
    // Arrange
    const maliciousInput = "'; DROP TABLE users; --";
    const validator = new InputValidator();
    
    // Act & Assert
    expect(() => validator.validateUsername(maliciousInput)).toThrow();
  });
  
  it('should sanitize HTML to prevent XSS', () => {
    // Arrange
    const maliciousInput = '<script>alert("XSS")</script>';
    const sanitizer = new HtmlSanitizer();
    
    // Act
    const sanitized = sanitizer.sanitize(maliciousInput);
    
    // Assert
    expect(sanitized).not.toContain('<script>');
  });
});
```

## Bonnes pratiques spécifiques

### Nommage des tests

Utilisez des noms descriptifs qui expliquent ce que le test vérifie:
- ✅ `it('should calculate total price with tax')`
- ❌ `it('test calculation')`

Suivez le format "should [expected behavior] when [condition]":
- ✅ `it('should throw error when input is invalid')`
- ✅ `it('should return empty array when no users exist')`

### Mocks et stubs

Utilisez des mocks pour simuler les dépendances externes:
- Bases de données
- Services externes
- Modules complexes

Préférez les mocks explicites aux mocks automatiques:
- ✅ `const mockUserRepository = { findById: jest.fn() }`
- ❌ `jest.mock('../UserRepository')`

### Assertions

Utilisez des assertions précises:
- ✅ `expect(users.length).toBe(3)`
- ❌ `expect(users.length > 0).toBe(true)`

Testez tous les aspects du résultat:
- Valeur de retour
- Effets secondaires (appels de méthodes, changements d'état)
- Erreurs levées

### Tests asynchrones

Utilisez `async/await` pour les tests asynchrones:
- ✅ `it('should fetch user data', async () => { ... })`
- ❌ `it('should fetch user data', () => { ... }).then(...)`

Attendez toujours que les promesses soient résolues:
- ✅ `await userService.save(user)`
- ❌ `userService.save(user) // Sans await`

## Maintenance des tests

### Revue de code

Lors des revues de code, vérifiez que:
- Chaque nouvelle fonctionnalité a des tests
- Les tests sont clairs et maintenables
- La couverture de code est suffisante

### Refactoring des tests

Refactorisez régulièrement vos tests:
- Éliminez la duplication de code
- Améliorez la lisibilité
- Mettez à jour les tests obsolètes

### Analyse de la couverture

Utilisez l'analyse de couverture pour identifier les parties non testées:
```bash
npm run test:coverage
```

Visez une couverture d'au moins 80% pour le code critique.

## Ressources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Library](https://testing-library.com/docs/)
- [Test-Driven Development by Example](https://www.amazon.com/Test-Driven-Development-Kent-Beck/dp/0321146530) (Kent Beck)
- [xUnit Test Patterns](https://www.amazon.com/xUnit-Test-Patterns-Refactoring-Code/dp/0131495054) (Gerard Meszaros)
