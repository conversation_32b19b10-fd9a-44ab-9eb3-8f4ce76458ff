# Guide des tests

Ce guide explique comment exécuter les tests pour le système de retraites de bien-être.

## Table des matières

1. [Introduction](#introduction)
2. [Types de tests](#types-de-tests)
3. [Structure des tests](#structure-des-tests)
4. [Exécution des tests](#exécution-des-tests)
5. [Écriture de nouveaux tests](#écriture-de-nouveaux-tests)
6. [Intégration continue](#intégration-continue)
7. [Couverture de code](#couverture-de-code)

## Introduction

Le système de retraites de bien-être utilise pytest pour les tests unitaires et d'intégration. Les tests sont organisés par type et par module, et peuvent être exécutés individuellement ou en groupe.

## Types de tests

Le système comprend plusieurs types de tests:

- **Tests unitaires**: Testent des fonctions ou des classes individuelles en isolation.
- **Tests d'intégration**: Testent l'interaction entre plusieurs composants.
- **Tests d'API**: Testent les endpoints de l'API REST.
- **Tests d'internationalisation**: Testent le système d'internationalisation.
- **Tests d'intégration avec les services tiers**: Testent les intégrations avec les calendriers, les cartes, etc.

## Structure des tests

Les tests sont organisés dans le répertoire `tests` avec la structure suivante:

```
tests/
├── unit/                  # Tests unitaires
│   ├── i18n/              # Tests unitaires pour l'internationalisation
│   ├── integrations/      # Tests unitaires pour les intégrations
│   │   ├── calendar/      # Tests unitaires pour les intégrations de calendrier
│   │   ├── maps/          # Tests unitaires pour les intégrations de cartographie
│   │   └── analytics/     # Tests unitaires pour les intégrations d'analyse
│   └── notifications/     # Tests unitaires pour les notifications
├── integration/           # Tests d'intégration
│   └── api/               # Tests d'intégration pour l'API REST
└── conftest.py            # Fixtures partagées pour les tests
```

## Exécution des tests

### Prérequis

- Python 3.8 ou supérieur
- pytest
- pytest-asyncio
- pytest-cov (pour la couverture de code)

### Installation des dépendances

```bash
pip install pytest pytest-asyncio pytest-cov
```

### Exécution de tous les tests

```bash
python run_tests.py --all
```

### Exécution des tests unitaires

```bash
python run_tests.py --unit
```

### Exécution des tests d'intégration

```bash
python run_tests.py --integration
```

### Exécution des tests d'internationalisation

```bash
python run_tests.py --i18n
```

### Exécution des tests d'intégration avec les calendriers

```bash
python run_tests.py --calendar
```

### Exécution des tests d'intégration avec les cartes

```bash
python run_tests.py --maps
```

### Exécution des tests d'intégration avec l'analyse de données

```bash
python run_tests.py --analytics
```

### Exécution des tests de notifications

```bash
python run_tests.py --notifications
```

### Génération d'un rapport de couverture de code

```bash
python run_tests.py --unit --coverage
```

### Génération d'un rapport HTML

```bash
python run_tests.py --unit --coverage --html
```

Le rapport HTML sera généré dans le répertoire `htmlcov`.

## Écriture de nouveaux tests

### Tests unitaires

Les tests unitaires doivent être placés dans le répertoire `tests/unit` et suivre la structure du code source. Par exemple, les tests pour le module `src/i18n/i18n_service.py` doivent être placés dans `tests/unit/i18n/test_i18n_service.py`.

Exemple de test unitaire:

```python
import pytest
from src.i18n.i18n_service import I18nService

class TestI18nService:
    """
    Tests pour le service d'internationalisation.
    """
    
    def setup_method(self):
        """
        Configuration avant chaque test.
        """
        self.i18n_service = I18nService(default_language="fr")
    
    def test_translate(self):
        """
        Test de la traduction d'une clé.
        """
        # Mocker la fonction get_translation
        with patch("src.i18n.i18n_service.get_translation") as mock_get_translation:
            mock_get_translation.return_value = "Bienvenue sur Retreat & Be"
            
            # Traduire une clé
            translation = self.i18n_service.translate("common.welcome")
            
            # Vérifier que la fonction get_translation a été appelée avec les bons paramètres
            mock_get_translation.assert_called_once_with("common.welcome", "fr")
            
            # Vérifier que la traduction est correcte
            assert translation == "Bienvenue sur Retreat & Be"
```

### Tests d'intégration

Les tests d'intégration doivent être placés dans le répertoire `tests/integration` et suivre la structure du code source. Par exemple, les tests pour l'API REST doivent être placés dans `tests/integration/api`.

Exemple de test d'intégration:

```python
import pytest
from fastapi.testclient import TestClient

from src.api.app import create_app

@pytest.fixture
def client():
    """
    Fixture pour créer un client de test.
    """
    app = create_app()
    return TestClient(app)

class TestI18nRoutes:
    """
    Tests pour les routes d'internationalisation.
    """
    
    def test_get_languages(self, client):
        """
        Test de la récupération des langues supportées.
        """
        # Faire une requête GET /api/i18n/languages
        response = client.get("/api/i18n/languages")
        
        # Vérifier que la réponse est correcte
        assert response.status_code == 200
        assert "languages" in response.json()
        assert len(response.json()["languages"]) > 0
```

### Tests asynchrones

Pour les tests asynchrones, utilisez le décorateur `@pytest.mark.asyncio`:

```python
import pytest

@pytest.mark.asyncio
async def test_async_function():
    """
    Test d'une fonction asynchrone.
    """
    result = await async_function()
    assert result == expected_result
```

### Marqueurs

Les tests peuvent être marqués pour être exécutés séparément:

```python
import pytest

@pytest.mark.unit
def test_unit():
    """
    Test unitaire.
    """
    pass

@pytest.mark.integration
def test_integration():
    """
    Test d'intégration.
    """
    pass

@pytest.mark.i18n
def test_i18n():
    """
    Test d'internationalisation.
    """
    pass

@pytest.mark.calendar
def test_calendar():
    """
    Test d'intégration avec les calendriers.
    """
    pass

@pytest.mark.maps
def test_maps():
    """
    Test d'intégration avec les cartes.
    """
    pass

@pytest.mark.analytics
def test_analytics():
    """
    Test d'intégration avec l'analyse de données.
    """
    pass

@pytest.mark.notifications
def test_notifications():
    """
    Test de notifications.
    """
    pass
```

## Intégration continue

Le système utilise GitHub Actions pour l'intégration continue. Les tests sont exécutés automatiquement à chaque push sur les branches `main`, `master` et `develop`, ainsi que pour chaque pull request vers ces branches.

Le workflow est défini dans le fichier `.github/workflows/tests.yml` et exécute les tests unitaires, d'intégration et d'internationalisation.

## Couverture de code

La couverture de code est mesurée avec pytest-cov et peut être visualisée dans un rapport HTML. Pour générer un rapport de couverture de code:

```bash
python run_tests.py --unit --coverage --html
```

Le rapport HTML sera généré dans le répertoire `htmlcov`.

La couverture de code est également mesurée dans l'intégration continue et peut être visualisée sur [Codecov](https://codecov.io/).
