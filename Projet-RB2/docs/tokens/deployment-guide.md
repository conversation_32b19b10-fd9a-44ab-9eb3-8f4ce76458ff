# Guide de Déploiement des Smart Contracts RandB

Ce guide détaille le processus de déploiement des smart contracts RandB sur les différents environnements.

## Prérequis

- Node.js >= 16.0.0
- Hardhat
- Compte Ethereum avec ETH pour le déploiement
- Clés API pour les réseaux de test (Infura/Alchemy)

## Configuration

1. Installation des dépendances

```bash
npm install --save-dev hardhat @nomiclabs/hardhat-ethers @openzeppelin/contracts
```

2. Configuration de Hardhat (`hardhat.config.ts`)

```typescript
import { HardhatUserConfig } from "hardhat/config";
import "@nomiclabs/hardhat-ethers";
import "@nomiclabs/hardhat-waffle";
import "@typechain/hardhat";
import "@openzeppelin/hardhat-upgrades";

require('dotenv').config();

const config: HardhatUserConfig = {
  solidity: {
    version: "0.8.17",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200
      }
    }
  },
  networks: {
    hardhat: {},
    localhost: {
      url: "http://127.0.0.1:8545"
    },
    goerli: {
      url: `https://goerli.infura.io/v3/${process.env.INFURA_PROJECT_ID}`,
      accounts: [process.env.PRIVATE_KEY!]
    },
    mainnet: {
      url: `https://mainnet.infura.io/v3/${process.env.INFURA_PROJECT_ID}`,
      accounts: [process.env.PRIVATE_KEY!]
    }
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY
  }
};

export default config;
```

## Scripts de Déploiement

1. Script de déploiement principal (`scripts/deploy.ts`)

```typescript
import { ethers, upgrades } from "hardhat";

async function main() {
  // Deploy RandB Token
  const RandBToken = await ethers.getContractFactory("RandBToken");
  console.log("Deploying RandB Token...");
  const token = await RandBToken.deploy(
    ethers.utils.parseEther("1000000") // 1M tokens max supply
  );
  await token.deployed();
  console.log("RandB Token deployed to:", token.address);

  // Deploy Reward System
  const RewardSystem = await ethers.getContractFactory("RewardSystem");
  console.log("Deploying Reward System...");
  const rewardSystem = await RewardSystem.deploy(token.address);
  await rewardSystem.deployed();
  console.log("Reward System deployed to:", rewardSystem.address);

  // Deploy Governance
  const timelock = await ethers.getContractFactory("TimelockController");
  const timelockContract = await timelock.deploy(
    2 * 24 * 60 * 60, // 2 days delay
    [], // proposers
    [], // executors
    ethers.constants.AddressZero // admin
  );
  await timelockContract.deployed();

  const RandBGovernance = await ethers.getContractFactory("RandBGovernance");
  const governance = await RandBGovernance.deploy(
    token.address,
    timelockContract.address,
    1, // 1 block voting delay
    50400, // ~1 week voting period
    100000 // proposal threshold
  );
  await governance.deployed();
  console.log("Governance deployed to:", governance.address);

  // Verify contracts
  if (process.env.ETHERSCAN_API_KEY) {
    await verify(token.address, [ethers.utils.parseEther("1000000")]);
    await verify(rewardSystem.address, [token.address]);
    await verify(governance.address, [
      token.address,
      timelockContract.address,
      1,
      50400,
      100000
    ]);
  }
}

async function verify(address: string, constructorArguments: any[]) {
  try {
    await hre.run("verify:verify", {
      address,
      constructorArguments,
    });
  } catch (e) {
    console.error(`Verification failed for ${address}:`, e);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
```

## Procédure de Déploiement

### 1. Environnement Local

```bash
# Démarrer un nœud local
npx hardhat node

# Déployer les contrats
npx hardhat run scripts/deploy.ts --network localhost
```

### 2. Réseau de Test (Goerli)

```bash
# Déployer sur Goerli
npx hardhat run scripts/deploy.ts --network goerli
```

### 3. Mainnet

```bash
# Déployer sur Mainnet (ATTENTION)
npx hardhat run scripts/deploy.ts --network mainnet
```

## Post-Déploiement

1. Configuration des Rôles

```typescript
// scripts/setup-roles.ts
async function setupRoles() {
  const token = await ethers.getContractAt("RandBToken", TOKEN_ADDRESS);
  const rewardSystem = await ethers.getContractAt("RewardSystem", REWARD_SYSTEM_ADDRESS);

  // Ajouter le système de récompenses comme minter
  await token.addMinter(REWARD_SYSTEM_ADDRESS);
  console.log("Added RewardSystem as minter");

  // Configuration des activités initiales
  const activities = [
    {
      id: ethers.utils.id("FIRST_BOOKING"),
      name: "First Booking Reward",
      amount: ethers.utils.parseEther("100")
    },
    {
      id: ethers.utils.id("REFERRAL"),
      name: "Referral Reward",
      amount: ethers.utils.parseEther("50")
    }
  ];

  for (const activity of activities) {
    await rewardSystem.addActivity(activity.id, activity.name, activity.amount);
    console.log(`Added activity: ${activity.name}`);
  }
}

setupRoles()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
```

2. Vérification des Contrats

```bash
# Vérifier sur Etherscan
npx hardhat verify --network mainnet TOKEN_ADDRESS "1000000000000000000000000"
npx hardhat verify --network mainnet REWARD_SYSTEM_ADDRESS TOKEN_ADDRESS
npx hardhat verify --network mainnet GOVERNANCE_ADDRESS TOKEN_ADDRESS TIMELOCK_ADDRESS "1" "50400" "100000"
```

## Sécurité

### 1. Vérifications Pré-Déploiement

- Exécuter les tests
```bash
npx hardhat test
```

- Audit de sécurité
```bash
npm install -g slither-analyzer
slither .
```

### 2. Vérifications Post-Déploiement

```typescript
// scripts/verify-deployment.ts
async function verifyDeployment() {
  const token = await ethers.getContractAt("RandBToken", TOKEN_ADDRESS);
  const rewardSystem = await ethers.getContractAt("RewardSystem", REWARD_SYSTEM_ADDRESS);
  const governance = await ethers.getContractAt("RandBGovernance", GOVERNANCE_ADDRESS);

  // Vérifier les paramètres du token
  const name = await token.name();
  const symbol = await token.symbol();
  const maxSupply = await token.maxSupply();
  console.log(`Token: ${name} (${symbol})`);
  console.log(`Max Supply: ${ethers.utils.formatEther(maxSupply)}`);

  // Vérifier les rôles
  const isMinter = await token.authorizedMinters(REWARD_SYSTEM_ADDRESS);
  console.log(`RewardSystem is minter: ${isMinter}`);

  // Vérifier la gouvernance
  const votingDelay = await governance.votingDelay();
  const votingPeriod = await governance.votingPeriod();
  console.log(`Voting Delay: ${votingDelay}`);
  console.log(`Voting Period: ${votingPeriod}`);
}

verifyDeployment()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
```

## Maintenance

### 1. Mise à Jour des Contrats

Pour les contrats upgradables :

```typescript
// scripts/upgrade.ts
async function upgrade() {
  const RandBTokenV2 = await ethers.getContractFactory("RandBTokenV2");
  console.log("Upgrading RandB Token...");
  await upgrades.upgradeProxy(TOKEN_ADDRESS, RandBTokenV2);
  console.log("Token upgraded");
}
```

### 2. Sauvegarde

- Sauvegarder les adresses des contrats
- Archiver les artifacts de déploiement
- Documenter les paramètres de configuration

## Monitoring

### 1. Configuration des Alertes

```typescript
// scripts/setup-monitoring.ts
async function setupMonitoring() {
  const token = await ethers.getContractAt("RandBToken", TOKEN_ADDRESS);

  // Surveiller les événements
  token.on("Transfer", (from, to, amount) => {
    if (amount.gt(ethers.utils.parseEther("10000"))) {
      notifyLargeTransfer(from, to, amount);
    }
  });

  token.on("RewardDistributed", (to, amount, reason) => {
    logReward(to, amount, reason);
  });
}
```

### 2. Tableau de Bord

- Intégrer avec Dune Analytics
- Configurer Grafana pour la visualisation
- Mettre en place des alertes Discord/Slack

## Résolution des Problèmes

### 1. Problèmes Courants

- **Transaction en attente** : Augmenter le gas price
- **Out of gas** : Ajuster la limite de gas
- **Nonce incorrect** : Reset le compte sur MetaMask

### 2. Récupération

```typescript
// scripts/recover.ts
async function recoverFunds() {
  const token = await ethers.getContractAt("RandBToken", TOKEN_ADDRESS);
  
  // Récupérer les tokens bloqués
  const balance = await token.balanceOf(DEAD_ADDRESS);
  if (balance.gt(0)) {
    await token.recoverTokens(DEAD_ADDRESS);
  }
}
```

## Checklist de Déploiement

- [ ] Tests passés
- [ ] Audit de sécurité effectué
- [ ] Variables d'environnement configurées
- [ ] Gas suffisant pour le déploiement
- [ ] Contrats vérifiés sur Etherscan
- [ ] Rôles configurés
- [ ] Monitoring en place
- [ ] Documentation mise à jour

## Support

Pour toute question ou problème :
- Email : <EMAIL>
- Discord : https://discord.gg/retreatandbe
- Documentation : https://docs.retreatandbe.com
