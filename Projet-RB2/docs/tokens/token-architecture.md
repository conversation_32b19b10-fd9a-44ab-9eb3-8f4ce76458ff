# Architecture du Système de Tokens RandB

Ce document détaille l'architecture et l'implémentation du système de tokens de fidélité RandB pour la plateforme Retreat And Be.

## Vue d'Ensemble

### Objectifs
- Programme de fidélité décentralisé
- Récompenses transparentes et équitables
- Gouvernance communautaire
- Interopérabilité avec l'écosystème Web3
- Sécurité maximale

### Architecture Globale
```
┌─────────────────┐     ┌──────────────┐     ┌─────────────┐
│  Application    │     │ Token Service │     │  Smart      │
│  Frontend       │────▶│    API       │────▶│  Contracts  │
└─────────────────┘     └──────────────┘     └─────────────┘
                              │                     │
                        ┌─────────────┐      ┌──────────────┐
                        │  Blockchain │      │  Token       │
                        │  Network    │      │  Analytics   │
                        └─────────────┘      └──────────────┘
```

## Smart Contracts

### 1. RandB Token (ERC-20)

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

contract RandBToken is ERC20, Ownable, Pausable {
    // Events
    event TokensMinted(address indexed to, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);
    event RewardDistributed(address indexed to, uint256 amount, string reason);

    // State variables
    uint256 public maxSupply;
    mapping(address => bool) public authorizedMinters;
    
    constructor(uint256 _maxSupply) ERC20("RandB Token", "RandB") {
        maxSupply = _maxSupply;
        authorizedMinters[msg.sender] = true;
    }
    
    // Minting with authorized minters
    function mint(address to, uint256 amount) external {
        require(authorizedMinters[msg.sender], "Not authorized to mint");
        require(totalSupply() + amount <= maxSupply, "Max supply exceeded");
        _mint(to, amount);
        emit TokensMinted(to, amount);
    }
    
    // Burning tokens
    function burn(uint256 amount) external {
        _burn(msg.sender, amount);
        emit TokensBurned(msg.sender, amount);
    }
    
    // Distribute rewards
    function distributeReward(address to, uint256 amount, string memory reason) 
        external onlyOwner whenNotPaused {
        require(to != address(0), "Invalid address");
        require(amount > 0, "Invalid amount");
        
        _mint(to, amount);
        emit RewardDistributed(to, amount, reason);
    }
}
```

### 2. Reward System

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "./RandBToken.sol";

contract RewardSystem {
    RandBToken public token;
    
    struct Activity {
        string name;
        uint256 rewardAmount;
        bool active;
    }
    
    mapping(bytes32 => Activity) public activities;
    mapping(address => mapping(bytes32 => uint256)) public userRewards;
    
    event ActivityCompleted(
        address indexed user,
        bytes32 indexed activityId,
        uint256 amount
    );
    
    constructor(address _tokenAddress) {
        token = RandBToken(_tokenAddress);
    }
    
    function addActivity(
        bytes32 activityId,
        string memory name,
        uint256 rewardAmount
    ) external {
        activities[activityId] = Activity(name, rewardAmount, true);
    }
    
    function completeActivity(address user, bytes32 activityId) external {
        Activity memory activity = activities[activityId];
        require(activity.active, "Activity not found or inactive");
        
        token.distributeReward(user, activity.rewardAmount, activity.name);
        userRewards[user][activityId] += activity.rewardAmount;
        
        emit ActivityCompleted(user, activityId, activity.rewardAmount);
    }
}
```

### 3. Governance

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/governance/Governor.sol";
import "@openzeppelin/contracts/governance/extensions/GovernorSettings.sol";
import "@openzeppelin/contracts/governance/extensions/GovernorCountingSimple.sol";
import "@openzeppelin/contracts/governance/extensions/GovernorVotes.sol";
import "@openzeppelin/contracts/governance/extensions/GovernorVotesQuorumFraction.sol";
import "@openzeppelin/contracts/governance/extensions/GovernorTimelockControl.sol";

contract RandBGovernance is Governor, GovernorSettings, GovernorCountingSimple,
    GovernorVotes, GovernorVotesQuorumFraction, GovernorTimelockControl {
    
    constructor(
        IVotes _token,
        TimelockController _timelock,
        uint256 _votingDelay,
        uint256 _votingPeriod,
        uint256 _proposalThreshold
    )
        Governor("RandB Governance")
        GovernorSettings(
            _votingDelay,
            _votingPeriod,
            _proposalThreshold
        )
        GovernorVotes(_token)
        GovernorVotesQuorumFraction(4)
        GovernorTimelockControl(_timelock)
    {}
}
```

## Services Backend

### 1. Token Service API

```typescript
// src/services/token.service.ts
export class TokenService {
    private web3: Web3;
    private tokenContract: Contract;
    
    constructor() {
        this.web3 = new Web3(process.env.WEB3_PROVIDER);
        this.tokenContract = new this.web3.eth.Contract(
            RandBTokenABI,
            process.env.TOKEN_CONTRACT_ADDRESS
        );
    }
    
    async getBalance(address: string): Promise<string> {
        return this.tokenContract.methods.balanceOf(address).call();
    }
    
    async distributeReward(
        userAddress: string,
        amount: string,
        reason: string
    ): Promise<TransactionReceipt> {
        const tx = this.tokenContract.methods.distributeReward(
            userAddress,
            amount,
            reason
        );
        
        return this.sendTransaction(tx);
    }
    
    async transferTokens(
        from: string,
        to: string,
        amount: string
    ): Promise<TransactionReceipt> {
        const tx = this.tokenContract.methods.transfer(to, amount);
        return this.sendTransaction(tx, { from });
    }
}
```

### 2. Analytics Service

```typescript
// src/services/analytics.service.ts
export class TokenAnalyticsService {
    private db: Database;
    
    constructor() {
        this.db = new Database();
    }
    
    async trackReward(data: RewardEvent): Promise<void> {
        await this.db.rewards.insert({
            userId: data.userId,
            amount: data.amount,
            reason: data.reason,
            timestamp: new Date(),
            txHash: data.txHash
        });
    }
    
    async getUserStats(userId: string): Promise<UserStats> {
        const rewards = await this.db.rewards
            .find({ userId })
            .sort({ timestamp: -1 });
            
        return {
            totalRewards: rewards.reduce((sum, r) => sum + r.amount, 0),
            rewardCount: rewards.length,
            lastReward: rewards[0],
            history: rewards
        };
    }
    
    async getGlobalStats(): Promise<GlobalStats> {
        const totalSupply = await this.tokenContract.methods.totalSupply().call();
        const holders = await this.getUniqueHolders();
        
        return {
            totalSupply,
            holders: holders.length,
            avgBalance: totalSupply / holders.length,
            distribution: await this.getDistributionStats()
        };
    }
}
```

## Frontend Integration

### 1. Web3 Provider

```typescript
// src/providers/Web3Provider.tsx
export const Web3Provider: React.FC = ({ children }) => {
    const [web3, setWeb3] = useState<Web3 | null>(null);
    const [account, setAccount] = useState<string | null>(null);
    
    useEffect(() => {
        const initWeb3 = async () => {
            if (window.ethereum) {
                const web3Instance = new Web3(window.ethereum);
                try {
                    await window.ethereum.request({ 
                        method: 'eth_requestAccounts' 
                    });
                    const accounts = await web3Instance.eth.getAccounts();
                    setWeb3(web3Instance);
                    setAccount(accounts[0]);
                } catch (error) {
                    console.error('User denied account access');
                }
            }
        };
        
        initWeb3();
    }, []);
    
    return (
        <Web3Context.Provider value={{ web3, account }}>
            {children}
        </Web3Context.Provider>
    );
};
```

### 2. Token Hooks

```typescript
// src/hooks/useToken.ts
export const useToken = () => {
    const { web3, account } = useWeb3();
    const [balance, setBalance] = useState<string>('0');
    
    useEffect(() => {
        const loadBalance = async () => {
            if (web3 && account) {
                const tokenContract = new web3.eth.Contract(
                    RandBTokenABI,
                    TOKEN_ADDRESS
                );
                const balance = await tokenContract.methods
                    .balanceOf(account)
                    .call();
                setBalance(balance);
            }
        };
        
        loadBalance();
    }, [web3, account]);
    
    return { balance };
};
```

## Sécurité

### 1. Smart Contract Security

```solidity
// Security features implemented in smart contracts
contract SecureToken {
    // Reentrancy Guard
    modifier nonReentrant() {
        require(!_reentrancyGuard, "Reentrant call");
        _reentrancyGuard = true;
        _;
        _reentrancyGuard = false;
    }
    
    // Pausable functions
    modifier whenNotPaused() {
        require(!paused, "Contract is paused");
        _;
    }
    
    // Access Control
    modifier onlyAuthorized() {
        require(authorizedUsers[msg.sender], "Not authorized");
        _;
    }
}
```

### 2. Transaction Security

```typescript
// Transaction signing and validation
class TransactionManager {
    async signAndSendTransaction(tx: Transaction): Promise<string> {
        // Validate transaction parameters
        this.validateTransaction(tx);
        
        // Sign transaction
        const signedTx = await web3.eth.accounts.signTransaction(
            tx,
            PRIVATE_KEY
        );
        
        // Send transaction
        const receipt = await web3.eth.sendSignedTransaction(
            signedTx.rawTransaction
        );
        
        return receipt.transactionHash;
    }
    
    private validateTransaction(tx: Transaction): void {
        // Validate gas price
        if (tx.gasPrice > MAX_GAS_PRICE) {
            throw new Error('Gas price too high');
        }
        
        // Validate value
        if (tx.value > MAX_TRANSACTION_VALUE) {
            throw new Error('Transaction value too high');
        }
    }
}
```

## Monitoring

### 1. Event Monitoring

```typescript
// Monitor token events
class TokenEventMonitor {
    async monitorEvents() {
        const tokenContract = new web3.eth.Contract(
            RandBTokenABI,
            TOKEN_ADDRESS
        );
        
        tokenContract.events.allEvents({
            fromBlock: 'latest'
        })
        .on('data', async (event) => {
            await this.processEvent(event);
        })
        .on('error', console.error);
    }
    
    private async processEvent(event: Event) {
        switch (event.event) {
            case 'Transfer':
                await this.handleTransfer(event);
                break;
            case 'Approval':
                await this.handleApproval(event);
                break;
            case 'RewardDistributed':
                await this.handleReward(event);
                break;
        }
    }
}
```

### 2. Metrics Collection

```typescript
// Collect and store token metrics
class TokenMetrics {
    async collectMetrics(): Promise<void> {
        const metrics = await Promise.all([
            this.getTotalSupply(),
            this.getHolderCount(),
            this.getTransactionVolume(),
            this.getActiveUsers()
        ]);
        
        await this.storeMetrics(metrics);
    }
    
    private async storeMetrics(metrics: Metrics): Promise<void> {
        await this.db.metrics.insert({
            ...metrics,
            timestamp: new Date()
        });
    }
}
```

## Maintenance

Ce document doit être mis à jour :
- À chaque déploiement de smart contract
- Lors des mises à jour majeures
- Après les audits de sécurité
- Suite aux changements de gouvernance

Dernière mise à jour : [DATE]
Version : 1.0
