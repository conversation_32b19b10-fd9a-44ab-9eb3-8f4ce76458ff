# Patterns d'Utilisation - Projet RB2

## Introduction

Ce document détaille les patterns d'utilisation communs pour le projet RB2. Il sert de guide pour les développeurs afin d'assurer la cohérence et l'efficacité lors de l'interaction avec les différentes parties du système. Ces patterns ont été établis pour favoriser la maintenabilité, les performances et la stabilité du projet.

## Table des matières

1. [Services et Dépendances](#services-et-dépendances)
2. [Gestion des Erreurs](#gestion-des-erreurs)
3. [Patterns d'Accès aux Données](#patterns-daccès-aux-données)
4. [Optimisation des Performances](#optimisation-des-performances)
5. [Tests et Développement](#tests-et-développement)
6. [Sécurité](#sécurité)
7. [Monitoring et Logging](#monitoring-et-logging)

## Services et Dépendances

### Injection de Dépendances

Le projet utilise le système d'injection de dépendances de NestJS. Suivez ces bonnes pratiques :

```typescript
// Définition d'un service
@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}
}

// Utilisation dans un contrôleur
@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}
}
```

### Base Service Pattern

Pour les services qui partagent des fonctionnalités similaires, utilisez l'héritage avec notre pattern de service de base :

```typescript
// Pattern de service de base avec audit
export class BaseAuditService<T extends BaseEntity> {
  constructor(
    protected readonly repository: Repository<T>,
    protected readonly auditService: AuditService,
  ) {}

  async create(data: DeepPartial<T>, userId: string): Promise<T> {
    const entity = await this.repository.save(this.repository.create(data));
    await this.auditService.logCreation(entity, userId);
    return entity;
  }
}

// Service spécifique étendant le service de base
@Injectable()
export class ProductService extends BaseAuditService<Product> {
  constructor(
    @InjectRepository(Product) repository: Repository<Product>,
    auditService: AuditService,
    private readonly categoryService: CategoryService,
  ) {
    super(repository, auditService);
  }
}
```

### Event-Driven Communication

Pour la communication entre modules, utilisez le système d'événements :

```typescript
// Émettre un événement
@Injectable()
export class OrderService {
  constructor(private readonly eventEmitter: EventEmitter2) {}

  async placeOrder(order: Order): Promise<Order> {
    // Logique de création de commande
    this.eventEmitter.emit('order.created', order);
    return order;
  }
}

// Écouter un événement
@Injectable()
export class NotificationService {
  constructor() {}

  @OnEvent('order.created')
  handleOrderCreated(order: Order) {
    // Envoyer une notification
  }
}
```

## Gestion des Erreurs

### Capture et Classification

Utilisez notre service de monitoring des erreurs pour capturer et classifier les erreurs :

```typescript
@Injectable()
export class ProductService {
  constructor(private readonly errorMonitoringService: ErrorMonitoringService) {}

  async getProductById(id: string): Promise<Product> {
    try {
      const product = await this.productRepository.findOne({ where: { id } });
      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }
      return product;
    } catch (error) {
      // Utilisation du service de monitoring des erreurs
      this.errorMonitoringService.reportError(
        error,
        ErrorType.DATABASE,
        error instanceof NotFoundException ? ErrorSeverity.LOW : ErrorSeverity.HIGH,
        { productId: id }
      );
      throw error;
    }
  }
}
```

### Filtres d'Exceptions

Laissez le filtre global d'exceptions gérer les erreurs non capturées :

```typescript
// Laisser l'erreur être capturée par le filtre global
@Get(':id')
async getProduct(@Param('id') id: string): Promise<ProductDto> {
  return this.productService.getProductById(id);
}
```

### Gestion Avancée des Erreurs

Pour les opérations complexes, utilisez un pattern de retry avec circuit breaker :

```typescript
@Injectable()
export class ExternalApiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly circuitBreakerService: CircuitBreakerService,
  ) {}

  async fetchExternalData(endpoint: string): Promise<any> {
    return this.circuitBreakerService.executeWithRetry(
      async () => {
        const response = await this.httpService.get(endpoint).toPromise();
        return response.data;
      },
      {
        retries: 3,
        delay: 1000,
        backoff: 2,
      }
    );
  }
}
```

## Patterns d'Accès aux Données

### Query Builder Pattern

Pour des requêtes complexes, utilisez des Query Builders spécialisés :

```typescript
@Injectable()
export class ProductQueryBuilder {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
  ) {}

  buildProductsQuery(filters: ProductFilterDto): SelectQueryBuilder<Product> {
    const query = this.productRepository.createQueryBuilder('product');
    
    if (filters.categoryId) {
      query.andWhere('product.categoryId = :categoryId', { categoryId: filters.categoryId });
    }
    
    if (filters.minPrice !== undefined) {
      query.andWhere('product.price >= :minPrice', { minPrice: filters.minPrice });
    }
    
    // Optimisation : Sélection conditionnelle
    if (filters.includeDetails) {
      query.leftJoinAndSelect('product.details', 'details');
    }
    
    return query;
  }
}
```

### Repository Pattern

Étendez les repositories de base pour ajouter des fonctionnalités spécifiques :

```typescript
@EntityRepository(Product)
export class ProductRepository extends Repository<Product> {
  async findWithCategory(id: string): Promise<Product> {
    return this.findOne({
      where: { id },
      relations: ['category'],
    });
  }

  async searchByKeyword(keyword: string): Promise<Product[]> {
    return this.createQueryBuilder('product')
      .where('product.name ILIKE :keyword', { keyword: `%${keyword}%` })
      .orWhere('product.description ILIKE :keyword', { keyword: `%${keyword}%` })
      .getMany();
  }
}
```

### Transactions

Utilisez des transactions pour les opérations multi-étapes :

```typescript
@Injectable()
export class OrderService {
  constructor(
    private readonly connection: Connection,
    private readonly orderRepository: OrderRepository,
    private readonly productRepository: ProductRepository,
  ) {}

  async createOrderWithItems(orderData: CreateOrderDto): Promise<Order> {
    return this.connection.transaction(async manager => {
      const orderRepository = manager.getCustomRepository(OrderRepository);
      const productRepository = manager.getCustomRepository(ProductRepository);
      
      // Créer la commande
      const order = await orderRepository.save(orderRepository.create({
        userId: orderData.userId,
        totalAmount: 0,
      }));
      
      let totalAmount = 0;
      
      // Ajouter les produits
      for (const item of orderData.items) {
        const product = await productRepository.findOne(item.productId);
        if (!product) {
          throw new NotFoundException(`Product ${item.productId} not found`);
        }
        
        totalAmount += product.price * item.quantity;
        
        // Vérifier et mettre à jour le stock
        if (product.stock < item.quantity) {
          throw new BadRequestException(`Not enough stock for product ${product.name}`);
        }
        
        await productRepository.update(product.id, {
          stock: product.stock - item.quantity,
        });
        
        // Ajouter l'item
        await manager.getRepository(OrderItem).save({
          orderId: order.id,
          productId: product.id,
          quantity: item.quantity,
          unitPrice: product.price,
        });
      }
      
      // Mettre à jour le montant total
      await orderRepository.update(order.id, { totalAmount });
      
      return orderRepository.findOne(order.id, { relations: ['items', 'items.product'] });
    });
  }
}
```

## Optimisation des Performances

### Mise en Cache

Utilisez notre système de cache à plusieurs niveaux :

```typescript
@Injectable()
export class ProductService {
  constructor(
    private readonly cacheService: CacheService,
    private readonly productRepository: ProductRepository,
  ) {}

  async getPopularProducts(): Promise<Product[]> {
    const cacheKey = 'popular_products';
    
    // Vérifier le cache
    const cachedProducts = await this.cacheService.get<Product[]>(cacheKey);
    if (cachedProducts) {
      return cachedProducts;
    }
    
    // Récupérer de la base de données
    const products = await this.productRepository
      .createQueryBuilder('product')
      .orderBy('product.views', 'DESC')
      .take(10)
      .getMany();
    
    // Mettre en cache pour 30 minutes
    await this.cacheService.set(cacheKey, products, 30 * 60);
    
    return products;
  }
}
```

### Chargement Différé

Pour les relations complexes, utilisez le chargement différé :

```typescript
@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly orderRepository: OrderRepository,
  ) {}

  async getUserBasicInfo(id: string): Promise<UserDto> {
    // Charge uniquement les informations de base de l'utilisateur
    const user = await this.userRepository.findOne(id);
    return this.mapToDto(user);
  }

  async getUserWithOrders(id: string): Promise<UserWithOrdersDto> {
    // Charge l'utilisateur et ses commandes séparément
    const user = await this.userRepository.findOne(id);
    
    // Chargement différé des commandes
    const orders = await this.orderRepository.find({
      where: { userId: id },
      take: 10,
      order: { createdAt: 'DESC' },
    });
    
    return {
      ...this.mapToDto(user),
      orders: orders.map(this.mapOrderToDto),
    };
  }
}
```

### Performance Monitoring

Utilisez notre service de monitoring des performances pour suivre les endpoints critiques :

```typescript
// La surveillance des performances est automatique grâce à l'intercepteur global
// Mais vous pouvez ajouter des métriques personnalisées

@Injectable()
export class SearchService {
  constructor(private readonly performanceService: PerformanceService) {}

  async search(query: string): Promise<SearchResult> {
    const startTime = Date.now();
    
    try {
      // Exécuter la recherche
      const results = await this.performComplexSearch(query);
      
      // Enregistrer des métriques personnalisées
      this.performanceService.recordCustomMetric({
        name: 'search_execution_time',
        value: Date.now() - startTime,
        tags: {
          query_length: query.length.toString(),
          results_count: results.length.toString(),
        },
      });
      
      return results;
    } catch (error) {
      // En cas d'erreur, enregistrer également les métriques
      this.performanceService.recordCustomMetric({
        name: 'search_error',
        value: 1,
        tags: {
          query_length: query.length.toString(),
          error_type: error.name,
        },
      });
      throw error;
    }
  }
}
```

## Tests et Développement

### Tests Unitaires

Suivez ce pattern pour les tests unitaires :

```typescript
describe('ProductService', () => {
  let productService: ProductService;
  let productRepository: MockType<Repository<Product>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductService,
        {
          provide: getRepositoryToken(Product),
          useFactory: mockRepository,
        },
      ],
    }).compile();

    productService = module.get<ProductService>(ProductService);
    productRepository = module.get(getRepositoryToken(Product));
  });

  describe('findAll', () => {
    it('should return an array of products', async () => {
      const mockProducts = [/* produits de test */];
      productRepository.find.mockReturnValue(mockProducts);
      
      expect(await productService.findAll()).toBe(mockProducts);
      expect(productRepository.find).toHaveBeenCalledTimes(1);
    });
  });
});
```

### Tests d'Intégration

Pour les tests d'intégration, configurez une base de données de test :

```typescript
describe('ProductController (e2e)', () => {
  let app: INestApplication;
  let connection: Connection;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: 'localhost',
          port: 5433, // Port différent pour la base de données de test
          username: 'test',
          password: 'test',
          database: 'rb2_test',
          entities: [Product, Category],
          synchronize: true,
        }),
        ProductModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    connection = moduleFixture.get<Connection>(Connection);
    
    // Configuration de l'application
    app.useGlobalPipes(new ValidationPipe());
    
    await app.init();
  });

  beforeEach(async () => {
    // Nettoyer la base de données avant chaque test
    await connection.synchronize(true);
    
    // Insérer des données de test
    await connection.getRepository(Category).save({ name: 'Test Category' });
  });

  it('/products (GET)', () => {
    return request(app.getHttpServer())
      .get('/products')
      .expect(200)
      .expect(res => {
        expect(Array.isArray(res.body)).toBe(true);
      });
  });

  afterAll(async () => {
    await app.close();
  });
});
```

## Sécurité

### Validation des Entrées

Utilisez systématiquement les pipes de validation :

```typescript
// DTO avec validation
export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  @Length(3, 50)
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  @IsNotEmpty()
  @Length(8, 100)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/, {
    message: 'Password too weak',
  })
  password: string;
}

// Contrôleur avec validation
@Post()
async create(@Body() createUserDto: CreateUserDto): Promise<User> {
  return this.userService.create(createUserDto);
}
```

### Guards d'Autorisation

Utilisez nos guards personnalisés pour l'autorisation :

```typescript
// Guard de rôle
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get<string[]>('roles', context.getHandler());
    if (!roles) {
      return true;
    }
    
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    return user && user.roles && this.matchRoles(roles, user.roles);
  }

  private matchRoles(requiredRoles: string[], userRoles: string[]): boolean {
    return requiredRoles.some(role => userRoles.includes(role));
  }
}

// Utilisation du guard
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class AdminController {
  // ...
}
```

## Monitoring et Logging

### Logging Structuré

Utilisez notre logger pour un logging structuré :

```typescript
@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  async processPayment(paymentData: PaymentDto): Promise<PaymentResult> {
    this.logger.log({
      message: 'Processing payment',
      userId: paymentData.userId,
      amount: paymentData.amount,
    });

    try {
      // Traitement du paiement
      const result = await this.paymentProvider.process(paymentData);
      
      this.logger.log({
        message: 'Payment processed successfully',
        userId: paymentData.userId,
        amount: paymentData.amount,
        transactionId: result.transactionId,
      });
      
      return result;
    } catch (error) {
      this.logger.error({
        message: 'Payment processing failed',
        userId: paymentData.userId,
        amount: paymentData.amount,
        error: error.message,
        stack: error.stack,
      });
      
      throw error;
    }
  }
}
```

### Monitoring des Performances

Utilisez notre service de monitoring des performances pour surveiller les opérations critiques :

```typescript
@Injectable()
export class ReportService {
  constructor(private readonly performanceService: PerformanceService) {}

  async generateReport(params: ReportParams): Promise<Report> {
    const startTime = Date.now();
    
    // Marquer le début d'une opération critique
    const operationId = `report_generation_${Date.now()}`;
    this.performanceService.startOperation(operationId, {
      type: 'report_generation',
      reportType: params.type,
      filters: Object.keys(params.filters).length,
    });
    
    try {
      // Génération du rapport
      const report = await this.executeReportGeneration(params);
      
      // Marquer la fin de l'opération
      this.performanceService.endOperation(operationId, {
        status: 'success',
        duration: Date.now() - startTime,
        resultSize: this.getReportSize(report),
      });
      
      return report;
    } catch (error) {
      // Marquer l'opération comme échouée
      this.performanceService.endOperation(operationId, {
        status: 'error',
        duration: Date.now() - startTime,
        errorType: error.name,
      });
      
      throw error;
    }
  }
}
```

## Conclusion

Ces patterns d'utilisation ont été développés sur la base de notre expérience avec le projet RB2 et sont destinés à assurer la cohérence, la maintenabilité et les performances du système. Ils représentent les meilleures pratiques à suivre lors du développement de nouvelles fonctionnalités ou de la modification des fonctionnalités existantes.

Pour toute question ou suggestion concernant ces patterns, veuillez contacter l'équipe d'architecture.

---

*Dernière mise à jour: 15 mai 2025*
*Contact: <EMAIL>* 