admin:
  access_log_path: /dev/stdout
  address:
    socket_address:
      protocol: TCP
      address: 0.0.0.0
      port_value: 9901

static_resources:
  listeners:
  - name: listener_0
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 10000
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: ingress_http
          access_log:
          - name: envoy.access_loggers.stdout
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
          route_config:
            name: local_route
            virtual_hosts:
            - name: local_service
              domains: ["*"]
              routes:
              - match:
                  prefix: "/api"
                route:
                  cluster: analyzer_service
                  timeout: 30s
                  retry_policy:
                    retry_on: connect-failure,refused-stream,unavailable,cancelled,retriable-status-codes
                    num_retries: 3
                    retry_host_predicate:
                    - name: envoy.retry_host_predicates.previous_hosts
                    host_selection_retry_max_attempts: 3
          http_filters:
          - name: envoy.filters.http.jwt_authn
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.jwt_authn.v3.JwtAuthentication
              providers:
                jwt_provider:
                  issuer: "analyzer-service"
                  audiences: ["analyzer-clients"]
                  remote_jwks:
                    http_uri:
                      uri: "http://analyzer:3000/.well-known/jwks.json"
                      cluster: analyzer_service
                      timeout: 5s
                    cache_duration:
                      seconds: 300
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

  clusters:
  - name: analyzer_service
    connect_timeout: 0.25s
    type: STRICT_DNS
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: analyzer_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: analyzer
                port_value: 3000
    health_checks:
      - timeout: 1s
        interval: 10s
        unhealthy_threshold: 3
        healthy_threshold: 2
        http_health_check:
          path: "/health"
    circuit_breakers:
      thresholds:
        - priority: DEFAULT
          max_connections: 1000
          max_pending_requests: 1000
          max_requests: 1000
          max_retries: 3
    outlier_detection:
      consecutive_5xx: 5
      base_ejection_time: 30s
      max_ejection_percent: 10

layered_runtime:
  layers:
    - name: static_layer_0
      static_layer:
        envoy:
          resource_limits:
            listener:
              example_listener_name:
                connection_limit: 10000
        overload:
          global_downstream_max_connections: 50000
