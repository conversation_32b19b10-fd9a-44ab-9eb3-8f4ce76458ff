global
    log /dev/log local0
    log /dev/log local1 notice
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin expose-fd listeners
    stats timeout 30s
    user haproxy
    group haproxy
    daemon

    # Default SSL material locations
    ca-base /etc/ssl/certs
    crt-base /etc/ssl/private

    # SSL configuration
    ssl-default-bind-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384
    ssl-default-bind-ciphersuites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256
    ssl-default-bind-options no-sslv3 no-tlsv10 no-tlsv11 no-tls-tickets

defaults
    log global
    mode http
    option httplog
    option dontlognull
    timeout connect 5000
    timeout client  50000
    timeout server  50000
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

frontend main
    bind *:80
    bind *:443 ssl crt /etc/haproxy/certs/
    
    # HSTS (uncomment if you want HSTS)
    # http-response set-header Strict-Transport-Security max-age=63072000
    
    # Security headers
    http-response set-header X-Frame-Options DENY
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    
    # ACLs for different services
    acl is_frontend path_beg /
    acl is_api path_beg /api
    acl is_websocket hdr(Upgrade) -i WebSocket
    
    # Rate limiting
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request deny deny_status 429 if { sc_http_req_rate(0) gt 100 }
    
    # Redirect HTTP to HTTPS
    redirect scheme https code 301 if !{ ssl_fc }
    
    # Route to backends
    use_backend websocket if is_websocket
    use_backend api if is_api
    default_backend frontend

backend frontend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    server frontend1 frontend:80 check
    server frontend2 frontend:80 check backup

backend api
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    server api1 backend:8080 check
    server api2 backend:8080 check backup
    
    # Circuit breaker
    http-request deny if { nbsrv(api) lt 1 }
    
    # Retry failed requests
    retries 3
    option redispatch

backend websocket
    balance source
    option httpchk GET /health
    server ws1 websocket:8080 check
    timeout server 1h
    timeout tunnel 1h

listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 10s
    stats admin if LOCALHOST
    stats auth admin:secure_password
