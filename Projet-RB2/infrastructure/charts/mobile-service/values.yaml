# Default values for mobile-service
replicaCount: 1

image:
  repository: rb2-app/mobile-service
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext: {}

securityContext: {}

istio:
  enabled: true
  mtls:
    mode: STRICT

networkPolicy:
  enabled: true
  
service:
  type: ClusterIP
  port: 8080
  annotations:
    proxy.istio.io/config: '{"holdApplicationUntilProxyStarts": true}'

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: mobile-service.rb2.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 1Gi

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

persistence:
  enabled: true
  storageClass: "standard"
  size: 10Gi
  accessMode: ReadWriteOnce
  mountPath: /app/storage

env:
  NODE_ENV: production
  LOG_LEVEL: info
  CLUSTER_NAME: rb2-main
  SERVICE_MESH_ENABLED: "true"

monitoring:
  enabled: true
  prometheus:
    scrape: true
  jaeger:
    enabled: true

secrets:
  androidKeystore:
    enabled: true
    name: android-keystore
    mountPath: /app/android/keystore
  iosCertificates:
    enabled: true
    name: ios-certificates
    mountPath: /app/ios/certificates

configMap:
  enabled: true
  name: mobile-service-config
  data:
    config.json: |
      {
        "buildService": {
          "androidEnabled": true,
          "iosEnabled": true,
          "webhookUrl": "https://api.rb2.com/webhooks/mobile-build"
        },
        "monitoring": {
          "enabled": true,
          "endpoint": "https://monitoring.rb2.com/api/mobile"
        }
      }
