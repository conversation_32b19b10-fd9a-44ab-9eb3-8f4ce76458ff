apiVersion: v1
kind: Namespace
metadata:
  name: rb2-staging
  labels:
    environment: staging
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-api
  namespace: rb2-staging
  labels:
    app: backend-api
    environment: staging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: backend-api
        environment: staging
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: backend-api
        image: rb2-app/backend-api:staging
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: NODE_ENV
          value: "staging"
        envFrom:
        - configMapRef:
            name: staging-environment-config
        - secretRef:
            name: staging-environment-secrets
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /alive
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        volumeMounts:
        - name: app-config
          mountPath: /app/config
        - name: tmp-data
          mountPath: /tmp
      volumes:
      - name: app-config
        configMap:
          name: staging-environment-config
      - name: tmp-data
        emptyDir: {}
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - backend-api
              topologyKey: "kubernetes.io/hostname"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
  namespace: rb2-staging
  labels:
    app: frontend-app
    environment: staging
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontend-app
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: frontend-app
        environment: staging
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: frontend-app
        image: rb2-app/frontend-app:staging
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: NODE_ENV
          value: "staging"
        envFrom:
        - configMapRef:
            name: staging-environment-config
        resources:
          requests:
            cpu: "200m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /ready
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /alive
            port: 80
          initialDelaySeconds: 15
          periodSeconds: 20
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - frontend-app
              topologyKey: "kubernetes.io/hostname"
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: rb2-staging
  labels:
    app: postgres
    environment: staging
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        environment: staging
    spec:
      containers:
      - name: postgres
        image: postgres:14
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: DB_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: DB_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: DB_NAME
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
          initialDelaySeconds: 30
          periodSeconds: 20
      securityContext:
        fsGroup: 999
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
  namespace: rb2-staging
  labels:
    app: redis
    environment: staging
spec:
  serviceName: redis
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        environment: staging
    spec:
      containers:
      - name: redis
        image: redis:6
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        ports:
        - containerPort: 6379
          name: redis
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 20
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: rb2-staging
  labels:
    app: rabbitmq
    environment: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
        environment: staging
    spec:
      containers:
      - name: rabbitmq
        image: rabbitmq:3-management
        ports:
        - containerPort: 5672
          name: amqp
        - containerPort: 15672
          name: management
        env:
        - name: RABBITMQ_DEFAULT_USER
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: RABBITMQ_USER
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: staging-environment-secrets
              key: RABBITMQ_PASSWORD
        - name: RABBITMQ_DEFAULT_VHOST
          valueFrom:
            configMapKeyRef:
              name: staging-environment-config
              key: RABBITMQ_VHOST
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
        volumeMounts:
        - name: rabbitmq-data
          mountPath: /var/lib/rabbitmq
        readinessProbe:
          exec:
            command:
            - rabbitmq-diagnostics
            - -q
            - check_running
          initialDelaySeconds: 10
          periodSeconds: 30
        livenessProbe:
          exec:
            command:
            - rabbitmq-diagnostics
            - -q
            - check_running
          initialDelaySeconds: 30
          periodSeconds: 60
      volumes:
      - name: rabbitmq-data
        persistentVolumeClaim:
          claimName: rabbitmq-data-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rabbitmq-data-pvc
  namespace: rb2-staging
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: Service
metadata:
  name: backend-api
  namespace: rb2-staging
  labels:
    app: backend-api
    environment: staging
spec:
  ports:
  - port: 80
    targetPort: 8080
    name: http
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: backend-api
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-app
  namespace: rb2-staging
  labels:
    app: frontend-app
    environment: staging
spec:
  ports:
  - port: 80
    targetPort: 80
    name: http
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: frontend-app
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-staging.rb2-internal
  namespace: rb2-staging
  labels:
    app: postgres
    environment: staging
spec:
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  selector:
    app: postgres
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: redis-staging.rb2-internal
  namespace: rb2-staging
  labels:
    app: redis
    environment: staging
spec:
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  selector:
    app: redis
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-staging.rb2-internal
  namespace: rb2-staging
  labels:
    app: rabbitmq
    environment: staging
spec:
  ports:
  - port: 5672
    targetPort: 5672
    name: amqp
  - port: 15672
    targetPort: 15672
    name: management
  selector:
    app: rabbitmq
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rb2-staging-ingress
  namespace: rb2-staging
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - staging.rb2-app.com
    - api.staging.rb2-app.com
    secretName: rb2-staging-tls
  rules:
  - host: staging.rb2-app.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: frontend-app
            port:
              number: 80
  - host: api.staging.rb2-app.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: backend-api
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-api-hpa
  namespace: rb2-staging
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-api
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontend-app-hpa
  namespace: rb2-staging
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend-app
  minReplicas: 2
  maxReplicas: 4
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-api-pdb
  namespace: rb2-staging
spec:
  selector:
    matchLabels:
      app: backend-api
  minAvailable: 1
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: frontend-app-pdb
  namespace: rb2-staging
spec:
  selector:
    matchLabels:
      app: frontend-app
  minAvailable: 1
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-api-network-policy
  namespace: rb2-staging
spec:
  podSelector:
    matchLabels:
      app: backend-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: rb2-staging
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: rabbitmq
    ports:
    - protocol: TCP
      port: 5672
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: kube-system
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0
        except:
        - 10.0.0.0/8
        - **********/12
        - ***********/16
    ports:
    - protocol: TCP
      port: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: frontend-app-network-policy
  namespace: rb2-staging
spec:
  podSelector:
    matchLabels:
      app: frontend-app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: rb2-staging
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: backend-api
    ports:
    - protocol: TCP
      port: 80
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: kube-system
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0
        except:
        - 10.0.0.0/8
        - **********/12
        - ***********/16
    ports:
    - protocol: TCP
      port: 443
