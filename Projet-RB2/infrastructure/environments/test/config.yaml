apiVersion: v1
kind: ConfigMap
metadata:
  name: test-environment-config
  namespace: rb2-test
data:
  # General configuration
  ENVIRONMENT: "test"
  LOG_LEVEL: "debug"
  DEPLOYMENT_TIER: "test"
  
  # API configuration
  API_RATE_LIMIT: "50"
  API_TIMEOUT: "30000"
  API_ENABLE_CACHE: "true"
  API_CACHE_TTL: "60"
  
  # Frontend configuration
  FRONTEND_URL: "https://test.rb2-app.com"
  FRONTEND_API_URL: "https://api.test.rb2-app.com"
  FRONTEND_ASSETS_URL: "https://assets.test.rb2-app.com"
  FRONTEND_ENABLE_ANALYTICS: "false"
  
  # Monitoring configuration
  MONITORING_ENABLED: "true"
  METRICS_SAMPLE_RATE: "1.0"
  TRACING_SAMPLE_RATE: "1.0"
  
  # Feature flags
  FEATURE_NEW_DASHBOARD: "true"
  FEATURE_ADVANCED_ANALYTICS: "true"
  FEATURE_EXPERIMENTAL_API: "true"
  
  # Integration endpoints
  EXTERNAL_SERVICE_A_URL: "https://api-mock.rb2-internal/service-a"
  EXTERNAL_SERVICE_B_URL: "https://api-mock.rb2-internal/service-b"
  
  # Database connection (non-sensitive parts)
  DB_HOST: "postgres-test.rb2-internal"
  DB_PORT: "5432"
  DB_NAME: "rb2_test"
  DB_POOL_SIZE: "5"
  DB_IDLE_TIMEOUT: "10000"
  
  # Redis configuration
  REDIS_HOST: "redis-test.rb2-internal"
  REDIS_PORT: "6379"
  REDIS_DB_INDEX: "0"
  
  # RabbitMQ configuration
  RABBITMQ_HOST: "rabbitmq-test.rb2-internal"
  RABBITMQ_PORT: "5672"
  RABBITMQ_VHOST: "rb2-test"
  
  # Object storage
  STORAGE_BUCKET: "rb2-test-assets"
  STORAGE_REGION: "us-west-2"
  STORAGE_URL: "https://rb2-test-assets.s3.us-west-2.amazonaws.com"
  
  # Logging configuration
  LOG_FORMAT: "json"
  LOG_OUTPUT: "stdout"
  LOG_FILE_PATH: "/var/log/rb2/app.log"
  LOG_MAX_SIZE: "100m"
  LOG_MAX_FILES: "5"
  
  # Metrics configuration
  METRICS_PORT: "9090"
  METRICS_PATH: "/metrics"
  
  # Health check configuration
  HEALTH_CHECK_PORT: "8081"
  HEALTH_CHECK_PATH: "/health"
  READINESS_CHECK_PATH: "/ready"
  LIVENESS_CHECK_PATH: "/alive"
  
  # Test data configuration
  TEST_DATA_ENABLED: "true"
  TEST_DATA_SEED: "54321"
  TEST_USERS_COUNT: "10"
  TEST_PRODUCTS_COUNT: "50"
  TEST_ORDERS_COUNT: "20"
  
  # Performance tuning
  MAX_WORKERS: "2"
  WORKER_CONNECTIONS: "512"
  CONNECTION_TIMEOUT: "5000"
  KEEP_ALIVE_TIMEOUT: "30000"
  
  # Integration testing
  INTEGRATION_TEST_ENABLED: "true"
  MOCK_SERVICES_ENABLED: "true"
  WIPE_DATABASE_ON_STARTUP: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: test-environment-secrets
  namespace: rb2-test
type: Opaque
stringData:
  # Database credentials
  DB_USER: "rb2_test"
  DB_PASSWORD: "test_db_password_replace_me"
  
  # Redis authentication
  REDIS_PASSWORD: "test_redis_password_replace_me"
  
  # RabbitMQ credentials
  RABBITMQ_USER: "rb2_test"
  RABBITMQ_PASSWORD: "test_rabbitmq_password_replace_me"
  
  # API keys
  API_SECRET_KEY: "test_api_secret_key_replace_me"
  JWT_SECRET: "test_jwt_secret_replace_me"
  
  # External service credentials
  EXTERNAL_SERVICE_A_API_KEY: "test_external_service_a_api_key_replace_me"
  EXTERNAL_SERVICE_B_API_KEY: "test_external_service_b_api_key_replace_me"
  
  # Storage credentials
  STORAGE_ACCESS_KEY: "test_storage_access_key_replace_me"
  STORAGE_SECRET_KEY: "test_storage_secret_key_replace_me"
  
  # Encryption keys
  ENCRYPTION_KEY: "test_encryption_key_replace_me"
---
apiVersion: batch/v1
kind: Job
metadata:
  name: test-environment-setup
  namespace: rb2-test
spec:
  backoffLimit: 2
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: db-setup
        image: node:16
        env:
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: DB_NAME
        - name: TEST_DATA_SEED
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: TEST_DATA_SEED
        - name: TEST_USERS_COUNT
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: TEST_USERS_COUNT
        - name: TEST_PRODUCTS_COUNT
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: TEST_PRODUCTS_COUNT
        - name: TEST_ORDERS_COUNT
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: TEST_ORDERS_COUNT
        command:
        - sh
        - -c
        - |
          echo "Setting up test database..."
          npm init -y
          npm install pg faker@5.5.3
          
          cat << EOF > setup-db.js
          const { Client } = require('pg');
          const faker = require('faker');
          
          async function setupDatabase() {
            const client = new Client({
              user: process.env.DB_USER,
              password: process.env.DB_PASSWORD,
              host: process.env.DB_HOST,
              port: parseInt(process.env.DB_PORT, 10),
              database: process.env.DB_NAME
            });
            
            try {
              await client.connect();
              console.log('Connected to database. Creating schema...');
              
              // Drop and recreate schema
              await client.query('DROP SCHEMA IF EXISTS public CASCADE');
              await client.query('CREATE SCHEMA public');
              
              // Create tables
              await client.query(`
                CREATE TABLE users (
                  id SERIAL PRIMARY KEY,
                  first_name VARCHAR(100) NOT NULL,
                  last_name VARCHAR(100) NOT NULL,
                  email VARCHAR(255) UNIQUE NOT NULL,
                  password_hash VARCHAR(255) NOT NULL,
                  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  updated_at TIMESTAMP
                )
              `);
              
              await client.query(`
                CREATE TABLE products (
                  id SERIAL PRIMARY KEY,
                  name VARCHAR(255) NOT NULL,
                  description TEXT,
                  price DECIMAL(10, 2) NOT NULL,
                  category VARCHAR(100),
                  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  updated_at TIMESTAMP
                )
              `);
              
              await client.query(`
                CREATE TABLE orders (
                  id SERIAL PRIMARY KEY,
                  user_id INTEGER REFERENCES users(id),
                  order_date TIMESTAMP NOT NULL,
                  status VARCHAR(50) NOT NULL,
                  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  updated_at TIMESTAMP
                )
              `);
              
              await client.query(`
                CREATE TABLE order_items (
                  id SERIAL PRIMARY KEY,
                  order_id INTEGER REFERENCES orders(id),
                  product_id INTEGER REFERENCES products(id),
                  quantity INTEGER NOT NULL,
                  price DECIMAL(10, 2) NOT NULL,
                  created_at TIMESTAMP NOT NULL DEFAULT NOW()
                )
              `);
              
              console.log('Schema created. Seeding data...');
              
              // Set seed for reproducible data
              faker.seed(parseInt(process.env.TEST_DATA_SEED, 10));
              
              // Seed users
              const userCount = parseInt(process.env.TEST_USERS_COUNT, 10);
              for (let i = 0; i < userCount; i++) {
                const firstName = faker.name.firstName();
                const lastName = faker.name.lastName();
                const email = faker.internet.email(firstName, lastName, 'test.example.com').toLowerCase();
                
                await client.query(
                  'INSERT INTO users (first_name, last_name, email, password_hash) VALUES ($1, $2, $3, $4)',
                  [firstName, lastName, email, 'test_password_hash']
                );
              }
              
              // Seed products
              const productCount = parseInt(process.env.TEST_PRODUCTS_COUNT, 10);
              for (let i = 0; i < productCount; i++) {
                await client.query(
                  'INSERT INTO products (name, description, price, category) VALUES ($1, $2, $3, $4)',
                  [
                    faker.commerce.productName(),
                    faker.commerce.productDescription(),
                    parseFloat(faker.commerce.price()),
                    faker.commerce.department()
                  ]
                );
              }
              
              // Get user IDs
              const userResult = await client.query('SELECT id FROM users');
              const userIds = userResult.rows.map(row => row.id);
              
              // Get product IDs
              const productResult = await client.query('SELECT id FROM products');
              const productIds = productResult.rows.map(row => row.id);
              
              // Seed orders
              const orderCount = parseInt(process.env.TEST_ORDERS_COUNT, 10);
              for (let i = 0; i < orderCount; i++) {
                const userId = userIds[Math.floor(Math.random() * userIds.length)];
                const orderDate = faker.date.past(1);
                const status = faker.random.arrayElement(['pending', 'processing', 'shipped', 'delivered', 'cancelled']);
                
                const orderResult = await client.query(
                  'INSERT INTO orders (user_id, order_date, status) VALUES ($1, $2, $3) RETURNING id',
                  [userId, orderDate, status]
                );
                
                const orderId = orderResult.rows[0].id;
                
                // Add 1-3 order items
                const itemCount = Math.floor(Math.random() * 3) + 1;
                for (let j = 0; j < itemCount; j++) {
                  const productId = productIds[Math.floor(Math.random() * productIds.length)];
                  const quantity = Math.floor(Math.random() * 3) + 1;
                  const price = parseFloat(faker.commerce.price());
                  
                  await client.query(
                    'INSERT INTO order_items (order_id, product_id, quantity, price) VALUES ($1, $2, $3, $4)',
                    [orderId, productId, quantity, price]
                  );
                }
              }
              
              console.log('Database setup completed successfully.');
            } catch (error) {
              console.error('Error setting up database:', error);
              process.exit(1);
            } finally {
              await client.end();
            }
          }
          
          setupDatabase();
          EOF
          
          node setup-db.js
