apiVersion: v1
kind: Namespace
metadata:
  name: rb2-test
  labels:
    environment: test
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-api
  namespace: rb2-test
  labels:
    app: backend-api
    environment: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend-api
  strategy:
    type: Recreate  # Use Recreate for test environment to ensure clean state
  template:
    metadata:
      labels:
        app: backend-api
        environment: test
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: backend-api
        image: rb2-app/backend-api:test
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: NODE_ENV
          value: "test"
        envFrom:
        - configMapRef:
            name: test-environment-config
        - secretRef:
            name: test-environment-secrets
        resources:
          requests:
            cpu: "200m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /ready
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /alive
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
  namespace: rb2-test
  labels:
    app: frontend-app
    environment: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-app
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: frontend-app
        environment: test
    spec:
      containers:
      - name: frontend-app
        image: rb2-app/frontend-app:test
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        env:
        - name: NODE_ENV
          value: "test"
        envFrom:
        - configMapRef:
            name: test-environment-config
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: rb2-test
  labels:
    app: postgres
    environment: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: postgres
        environment: test
    spec:
      containers:
      - name: postgres
        image: postgres:14
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: DB_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: DB_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: DB_NAME
        resources:
          requests:
            cpu: "200m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        emptyDir: {}  # Use emptyDir for test environment for a clean slate each time
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: rb2-test
  labels:
    app: redis
    environment: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: redis
        environment: test
    spec:
      containers:
      - name: redis
        image: redis:6
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        ports:
        - containerPort: 6379
          name: redis
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}  # Use emptyDir for test environment
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rabbitmq
  namespace: rb2-test
  labels:
    app: rabbitmq
    environment: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: rabbitmq
        environment: test
    spec:
      containers:
      - name: rabbitmq
        image: rabbitmq:3-management
        ports:
        - containerPort: 5672
          name: amqp
        - containerPort: 15672
          name: management
        env:
        - name: RABBITMQ_DEFAULT_USER
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: RABBITMQ_USER
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: test-environment-secrets
              key: RABBITMQ_PASSWORD
        - name: RABBITMQ_DEFAULT_VHOST
          valueFrom:
            configMapKeyRef:
              name: test-environment-config
              key: RABBITMQ_VHOST
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "300m"
            memory: "512Mi"
        volumeMounts:
        - name: rabbitmq-data
          mountPath: /var/lib/rabbitmq
      volumes:
      - name: rabbitmq-data
        emptyDir: {}  # Use emptyDir for test environment
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-mock
  namespace: rb2-test
  labels:
    app: api-mock
    environment: test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-mock
  template:
    metadata:
      labels:
        app: api-mock
        environment: test
    spec:
      containers:
      - name: api-mock
        image: stoplight/prism:4
        args:
        - mock
        - -h
        - 0.0.0.0
        - -p
        - "4010"
        - /api/openapi.yaml
        ports:
        - containerPort: 4010
          name: http
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
        volumeMounts:
        - name: api-specs
          mountPath: /api
      volumes:
      - name: api-specs
        configMap:
          name: api-mock-specs
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-mock-specs
  namespace: rb2-test
data:
  "openapi.yaml": |
    openapi: 3.0.0
    info:
      title: Mock API for Testing
      version: 1.0.0
      description: Mock API specifications for integration testing
    servers:
      - url: https://api-mock.rb2-internal
        description: Mock server
    paths:
      /service-a/users:
        get:
          summary: Get users
          operationId: getUsers
          parameters:
            - name: limit
              in: query
              schema:
                type: integer
                default: 10
          responses:
            '200':
              description: List of users
              content:
                application/json:
                  schema:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
        post:
          summary: Create user
          operationId: createUser
          requestBody:
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/UserInput'
          responses:
            '201':
              description: User created
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/User'
      
      /service-a/users/{id}:
        get:
          summary: Get user by ID
          operationId: getUserById
          parameters:
            - name: id
              in: path
              required: true
              schema:
                type: integer
          responses:
            '200':
              description: User details
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/User'
            '404':
              description: User not found
      
      /service-b/products:
        get:
          summary: Get products
          operationId: getProducts
          parameters:
            - name: limit
              in: query
              schema:
                type: integer
                default: 10
            - name: category
              in: query
              schema:
                type: string
          responses:
            '200':
              description: List of products
              content:
                application/json:
                  schema:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
        post:
          summary: Create product
          operationId: createProduct
          requestBody:
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ProductInput'
          responses:
            '201':
              description: Product created
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/Product'
      
      /service-b/products/{id}:
        get:
          summary: Get product by ID
          operationId: getProductById
          parameters:
            - name: id
              in: path
              required: true
              schema:
                type: integer
          responses:
            '200':
              description: Product details
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/Product'
            '404':
              description: Product not found
    
    components:
      schemas:
        User:
          type: object
          properties:
            id:
              type: integer
            firstName:
              type: string
            lastName:
              type: string
            email:
              type: string
            createdAt:
              type: string
              format: date-time
        
        UserInput:
          type: object
          required:
            - firstName
            - lastName
            - email
          properties:
            firstName:
              type: string
            lastName:
              type: string
            email:
              type: string
              format: email
        
        Product:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
            description:
              type: string
            price:
              type: number
              format: float
            category:
              type: string
            createdAt:
              type: string
              format: date-time
        
        ProductInput:
          type: object
          required:
            - name
            - price
          properties:
            name:
              type: string
            description:
              type: string
            price:
              type: number
              format: float
            category:
              type: string
---
apiVersion: v1
kind: Service
metadata:
  name: backend-api
  namespace: rb2-test
  labels:
    app: backend-api
    environment: test
spec:
  ports:
  - port: 80
    targetPort: 8080
    name: http
  - port: 9090
    targetPort: 9090
    name: metrics
  selector:
    app: backend-api
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: frontend-app
  namespace: rb2-test
  labels:
    app: frontend-app
    environment: test
spec:
  ports:
  - port: 80
    targetPort: 80
    name: http
  selector:
    app: frontend-app
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-test.rb2-internal
  namespace: rb2-test
  labels:
    app: postgres
    environment: test
spec:
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  selector:
    app: postgres
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: redis-test.rb2-internal
  namespace: rb2-test
  labels:
    app: redis
    environment: test
spec:
  ports:
  - port: 6379
    targetPort: 6379
    name: redis
  selector:
    app: redis
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-test.rb2-internal
  namespace: rb2-test
  labels:
    app: rabbitmq
    environment: test
spec:
  ports:
  - port: 5672
    targetPort: 5672
    name: amqp
  - port: 15672
    targetPort: 15672
    name: management
  selector:
    app: rabbitmq
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: api-mock.rb2-internal
  namespace: rb2-test
  labels:
    app: api-mock
    environment: test
spec:
  ports:
  - port: 80
    targetPort: 4010
    name: http
  selector:
    app: api-mock
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rb2-test-ingress
  namespace: rb2-test
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - test.rb2-app.com
    - api.test.rb2-app.com
    secretName: rb2-test-tls
  rules:
  - host: test.rb2-app.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: frontend-app
            port:
              number: 80
  - host: api.test.rb2-app.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: backend-api
            port:
              number: 80
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: test-environment-refresh
  namespace: rb2-test
spec:
  schedule: "0 0 * * *"  # Run at midnight every day
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
          - name: cleanup
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - |
              kubectl delete job --namespace=rb2-test test-environment-setup || true
              kubectl create job --namespace=rb2-test --from=cronjob/test-environment-refresh test-environment-setup-$(date +%s)
          serviceAccountName: test-environment-admin  # This service account needs appropriate permissions
