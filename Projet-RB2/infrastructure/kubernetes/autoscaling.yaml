apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-api-hpa
  namespace: rb2-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      selectPolicy: Min
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: frontend-app-hpa
  namespace: rb2-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend-app
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      selectPolicy: Min
---
# Vertical Pod Autoscaler for more efficient resource allocation
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: backend-api-vpa
  namespace: rb2-production
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-api
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 1
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
---
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: frontend-app-vpa
  namespace: rb2-production
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: frontend-app
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 50m
        memory: 64Mi
      maxAllowed:
        cpu: 500m
        memory: 512Mi
      controlledResources: ["cpu", "memory"]
---
# Cluster Autoscaler configuration (assuming AWS EKS)
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-autoscaler-config
  namespace: kube-system
data:
  config.yaml: |
    ---
    autoDiscovery:
      clusterName: rb2-cluster
    awsRegion: us-west-2
    image:
      repository: k8s.gcr.io/autoscaling/cluster-autoscaler
      tag: v1.26.2
    cloudProvider: aws
    extraArgs:
      v: 4
      balance-similar-node-groups: true
      skip-nodes-with-system-pods: true
      skip-nodes-with-local-storage: false
      expander: least-waste
      scale-down-utilization-threshold: "0.5"
      scale-down-non-empty-candidates-count: "5"
      scale-down-delay-after-add: "10m"
      scale-down-delay-after-delete: "10s"
      scale-down-delay-after-failure: "3m"
      scale-down-unneeded-time: "10m"
      max-node-provision-time: "15m"
      scan-interval: "10s"
    rbac:
      create: true
      serviceAccount:
        annotations:
          eks.amazonaws.com/role-arn: arn:aws:iam::************:role/rb2-cluster-autoscaler-role
    resources:
      limits:
        cpu: 100m
        memory: 300Mi
      requests:
        cpu: 100m
        memory: 300Mi
    priorityClassName: system-cluster-critical
    podAnnotations:
      cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
---
# Pod Disruption Budget to ensure service availability during node scaling or maintenance
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-api-pdb
  namespace: rb2-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: backend-api
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: frontend-app-pdb
  namespace: rb2-production
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: frontend-app
