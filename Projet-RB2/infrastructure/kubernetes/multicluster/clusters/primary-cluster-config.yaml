apiVersion: v1
kind: ConfigMap
metadata:
  name: primary-cluster-config
  namespace: istio-system
data:
  mesh: |
    accessLogFile: /dev/stdout
    enableTracing: true
    defaultConfig:
      discoveryAddress: istiod.istio-system.svc:15012
      proxyMetadata:
        AUTO_PROTOCOL_DETECT: "true"
        ISTIO_META_DNS_CAPTURE: "true"
        ISTIO_META_DNS_AUTO_ALLOCATE: "true"
      tracing:
        zipkin:
          address: zipkin.istio-system:9411
      holdApplicationUntilProxyStarts: true
    trustDomain: cluster.local
    trustDomainAliases:
      - cluster.local
      - primary-cluster.global
    rootNamespace: istio-system
    outboundTrafficPolicy:
      mode: ALLOW_ANY
    enableAutoMtls: true
    defaultServiceExportTo:
      - "*"
    defaultVirtualServiceExportTo:
      - "*"
    defaultDestinationRuleExportTo:
      - "*"
  meshNetworks: |
    networks:
      network1:
        endpoints:
        - fromRegistry: primary-cluster
        gateways:
        - address: istio-eastwestgateway.istio-system.svc.cluster.local
          port: 15443
      network2:
        endpoints:
        - fromRegistry: remote-cluster
        gateways:
        - address: istio-eastwestgateway-remote.istio-system.svc.cluster.local
          port: 15443
