#!/bin/bash

# Script pour déployer et tester l'application hello-world sur les clusters

# Définition des variables
PRIMARY_CONTEXT="cluster-primary"
REMOTE_CONTEXT="cluster-remote"
TEST_NAMESPACE="test-multicluster"
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour déployer l'application sur le cluster primaire
deploy_primary() {
    echo -e "${BLUE}Déploiement de l'application sur le cluster primaire...${NC}"
    kubectl config use-context $PRIMARY_CONTEXT
    
    # Création du namespace
    kubectl create namespace $TEST_NAMESPACE --context=$PRIMARY_CONTEXT --dry-run=client -o yaml | kubectl apply --context=$PRIMARY_CONTEXT -f -
    
    # Injection automatique d'Istio
    kubectl label namespace $TEST_NAMESPACE istio-injection=enabled --context=$PRIMARY_CONTEXT
    
    # Modification du ConfigMap pour indiquer le nom du cluster
    sed 's/cluster.name: "primary-cluster"/cluster.name: "primary-cluster"/g' ${BASE_DIR}/test-app.yaml > ${BASE_DIR}/test-app-primary.yaml
    
    # Déploiement de l'application
    kubectl apply -f ${BASE_DIR}/test-app-primary.yaml -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT
    
    echo -e "${GREEN}Application déployée sur le cluster primaire.${NC}"
}

# Fonction pour déployer l'application sur le cluster distant
deploy_remote() {
    echo -e "${BLUE}Déploiement de l'application sur le cluster distant...${NC}"
    kubectl config use-context $REMOTE_CONTEXT
    
    # Création du namespace
    kubectl create namespace $TEST_NAMESPACE --context=$REMOTE_CONTEXT --dry-run=client -o yaml | kubectl apply --context=$REMOTE_CONTEXT -f -
    
    # Injection automatique d'Istio
    kubectl label namespace $TEST_NAMESPACE istio-injection=enabled --context=$REMOTE_CONTEXT
    
    # Modification du ConfigMap pour indiquer le nom du cluster
    sed 's/cluster.name: "primary-cluster"/cluster.name: "remote-cluster"/g' ${BASE_DIR}/test-app.yaml > ${BASE_DIR}/test-app-remote.yaml
    
    # Déploiement de l'application
    kubectl apply -f ${BASE_DIR}/test-app-remote.yaml -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT
    
    echo -e "${GREEN}Application déployée sur le cluster distant.${NC}"
}

# Fonction pour tester la connectivité entre les clusters
test_connectivity() {
    echo -e "${BLUE}Test de connectivité entre les clusters...${NC}"
    
    # Attendre que les pods soient prêts
    echo -e "${YELLOW}Attente que les pods soient prêts sur le cluster primaire...${NC}"
    kubectl wait --for=condition=ready pod -l app=hello-world -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT --timeout=120s
    
    echo -e "${YELLOW}Attente que les pods soient prêts sur le cluster distant...${NC}"
    kubectl wait --for=condition=ready pod -l app=hello-world -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT --timeout=120s
    
    # Vérifier les services sur les deux clusters
    echo -e "${YELLOW}Services sur le cluster primaire:${NC}"
    kubectl get svc -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT
    
    echo -e "${YELLOW}Services sur le cluster distant:${NC}"
    kubectl get svc -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT
    
    # Déployer un pod de test pour vérifier la connectivité
    echo -e "${YELLOW}Déploiement d'un pod de test sur le cluster primaire...${NC}"
    kubectl run curl --image=curlimages/curl:latest -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT -- sleep 3600
    
    # Attendre que le pod soit prêt
    kubectl wait --for=condition=ready pod/curl -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT --timeout=60s
    
    # Tester l'accès au service du cluster primaire
    echo -e "${YELLOW}Test d'accès au service du cluster primaire depuis le cluster primaire:${NC}"
    kubectl exec -it curl -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT -- curl -s hello-world.${TEST_NAMESPACE}.svc.cluster.local
    
    # Tester l'accès au service du cluster distant
    echo -e "${YELLOW}Test d'accès au service du cluster distant depuis le cluster primaire:${NC}"
    kubectl exec -it curl -n $TEST_NAMESPACE --context=$PRIMARY_CONTEXT -- curl -s hello-world.${TEST_NAMESPACE}.svc.cluster.global
    
    # Déployer un pod de test sur le cluster distant
    echo -e "${YELLOW}Déploiement d'un pod de test sur le cluster distant...${NC}"
    kubectl run curl --image=curlimages/curl:latest -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT -- sleep 3600
    
    # Attendre que le pod soit prêt
    kubectl wait --for=condition=ready pod/curl -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT --timeout=60s
    
    # Tester l'accès au service du cluster distant
    echo -e "${YELLOW}Test d'accès au service du cluster distant depuis le cluster distant:${NC}"
    kubectl exec -it curl -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT -- curl -s hello-world.${TEST_NAMESPACE}.svc.cluster.local
    
    # Tester l'accès au service du cluster primaire
    echo -e "${YELLOW}Test d'accès au service du cluster primaire depuis le cluster distant:${NC}"
    kubectl exec -it curl -n $TEST_NAMESPACE --context=$REMOTE_CONTEXT -- curl -s hello-world.${TEST_NAMESPACE}.svc.cluster.global
    
    echo -e "${GREEN}Tests de connectivité terminés.${NC}"
}

# Fonction pour nettoyer
cleanup() {
    echo -e "${BLUE}Nettoyage des ressources de test...${NC}"
    
    # Supprimer le namespace sur le cluster primaire
    kubectl delete namespace $TEST_NAMESPACE --context=$PRIMARY_CONTEXT
    
    # Supprimer le namespace sur le cluster distant
    kubectl delete namespace $TEST_NAMESPACE --context=$REMOTE_CONTEXT
    
    # Supprimer les fichiers temporaires
    rm -f ${BASE_DIR}/test-app-primary.yaml ${BASE_DIR}/test-app-remote.yaml
    
    echo -e "${GREEN}Nettoyage terminé.${NC}"
}

# Menu principal
echo -e "${BLUE}=== Test de l'application multi-cluster ===${NC}"
echo -e "1. Déployer sur le cluster primaire"
echo -e "2. Déployer sur le cluster distant"
echo -e "3. Déployer sur les deux clusters"
echo -e "4. Tester la connectivité"
echo -e "5. Nettoyer les ressources"
echo -e "6. Quitter"
echo -e "${YELLOW}Choisissez une option:${NC}"
read -r option

case $option in
    1)
        deploy_primary
        ;;
    2)
        deploy_remote
        ;;
    3)
        deploy_primary
        deploy_remote
        ;;
    4)
        test_connectivity
        ;;
    5)
        cleanup
        ;;
    6)
        echo -e "${GREEN}Au revoir!${NC}"
        exit 0
        ;;
    *)
        echo -e "${RED}Option invalide.${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}Opération terminée.${NC}"
