apiVersion: v1
kind: Service
metadata:
  name: hello-world
  labels:
    app: hello-world
    service: hello-world
spec:
  ports:
  - port: 80
    name: http
  selector:
    app: hello-world
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hello-world-v1
  labels:
    app: hello-world
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hello-world
      version: v1
  template:
    metadata:
      labels:
        app: hello-world
        version: v1
    spec:
      containers:
      - name: hello-world
        image: docker.io/istio/examples-helloworld-v1:1.18.0
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "500m"
            memory: "256Mi"
        ports:
        - containerPort: 5000
        env:
        - name: SERVICE_VERSION
          value: "v1"
        - name: CLUSTER_NAME
          valueFrom:
            configMapKeyRef:
              name: cluster-info
              key: cluster.name
              optional: true
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-info
data:
  cluster.name: "primary-cluster"  # Changer à "remote-cluster" pour le cluster distant
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: hello-world
spec:
  hosts:
  - "hello-world.example.com"
  gateways:
  - istio-system/istio-ingressgateway
  http:
  - route:
    - destination:
        host: hello-world
        port:
          number: 80
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: hello-world
spec:
  host: hello-world
  subsets:
  - name: v1
    labels:
      version: v1
