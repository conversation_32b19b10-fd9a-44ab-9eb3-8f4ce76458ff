###################################################
# Kubernetes Resource Optimization Configuration
###################################################

---
# Resource limits and requests for backend API
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-api
  namespace: rb2-production
spec:
  template:
    spec:
      containers:
      - name: backend-api
        resources:
          limits:
            cpu: 1
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi
        # Resource-efficient startup and shutdown
        lifecycle:
          postStart:
            exec:
              command: ["/bin/sh", "-c", "echo Starting application; date"]
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 10; echo Graceful shutdown; date"]
        # Liveness probe - determines if application is running
        livenessProbe:
          httpGet:
            path: /health/alive
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        # Readiness probe - determines if application can serve traffic
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        # Startup probe - gives application time to bootstrap
        startupProbe:
          httpGet:
            path: /health/alive
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 12  # Allow 1 minute (12 * 5 seconds) for startup
          successThreshold: 1
      # Pod topology spread constraints for better distribution
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: backend-api
      # Node affinity to prefer certain node types
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 80
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - backend
        # Pod anti-affinity to avoid scheduling multiple instances on same node
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - backend-api
              topologyKey: kubernetes.io/hostname
      # Termination grace period
      terminationGracePeriodSeconds: 60
      # Priority class for resource scheduling
      priorityClassName: high-priority

---
# Resource limits and requests for frontend application
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
  namespace: rb2-production
spec:
  template:
    spec:
      containers:
      - name: frontend-app
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 128Mi
        # Resource-efficient startup and shutdown
        lifecycle:
          postStart:
            exec:
              command: ["/bin/sh", "-c", "echo Starting frontend; date"]
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 5; echo Graceful shutdown; date"]
        # Liveness probe
        livenessProbe:
          httpGet:
            path: /healthz
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        # Readiness probe
        readinessProbe:
          httpGet:
            path: /readyz
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
          successThreshold: 1
      # Topology spread constraints
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: frontend-app
      # Node affinity
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 80
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - frontend
        # Pod anti-affinity
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - frontend-app
              topologyKey: kubernetes.io/hostname
      terminationGracePeriodSeconds: 30
      priorityClassName: medium-priority

---
# Priority classes for resource allocation
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: high-priority
value: 1000000
globalDefault: false
description: "High priority pods that should be scheduled first"
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: medium-priority
value: 100000
globalDefault: true
description: "Medium priority pods, default for cluster"
---
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: low-priority
value: 10000
globalDefault: false
description: "Low priority pods that can be preempted"

---
# Pod Disruption Budget for Backend API
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-api-pdb
  namespace: rb2-production
spec:
  minAvailable: 2  # Always keep at least 2 replicas available
  selector:
    matchLabels:
      app: backend-api

---
# Pod Disruption Budget for Frontend App
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: frontend-app-pdb
  namespace: rb2-production
spec:
  minAvailable: 1  # Always keep at least 1 replica available
  selector:
    matchLabels:
      app: frontend-app

---
# Network Policy for Backend API
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-api-network-policy
  namespace: rb2-production
spec:
  podSelector:
    matchLabels:
      app: backend-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend-app
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: rabbitmq
    ports:
    - protocol: TCP
      port: 5672
  # Allow DNS lookups
  - to:
    - namespaceSelector: {}
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# Network Policy for Frontend App
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: frontend-app-network-policy
  namespace: rb2-production
spec:
  podSelector:
    matchLabels:
      app: frontend-app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: ingress-nginx
    ports:
    - protocol: TCP
      port: 80
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: backend-api
    ports:
    - protocol: TCP
      port: 3000
  # Allow DNS lookups
  - to:
    - namespaceSelector: {}
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# Resource Quota for RB2 Namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: rb2-resource-quota
  namespace: rb2-production
spec:
  hard:
    pods: '20'
    requests.cpu: '4'
    requests.memory: 8Gi
    limits.cpu: '8'
    limits.memory: 16Gi
    persistentvolumeclaims: '10'

---
# Limit Range for RB2 Namespace
apiVersion: v1
kind: LimitRange
metadata:
  name: rb2-limit-range
  namespace: rb2-production
spec:
  limits:
  - type: Container
    default:
      cpu: 250m
      memory: 256Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    min:
      cpu: 50m
      memory: 64Mi
    max:
      cpu: 2
      memory: 2Gi
  - type: PersistentVolumeClaim
    min:
      storage: 1Gi
    max:
      storage: 100Gi

---
# Quality of Service Classes (achieved through resource requests and limits)
# - Guaranteed: requests = limits (both memory and CPU) - Backend API
# - Burstable: requests < limits - Frontend App
# - BestEffort: no resource requests or limits - Batch Jobs

# Batch Job with BestEffort QoS
apiVersion: batch/v1
kind: CronJob
metadata:
  name: data-cleanup
  namespace: rb2-production
spec:
  schedule: "0 1 * * *"  # Run at 1 AM every day
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cleanup
            image: rb2/maintenance:latest
            command: ["/bin/sh", "-c", "/scripts/cleanup.sh"]
            # No resource limits or requests specified = BestEffort QoS
          restartPolicy: OnFailure
          priorityClassName: low-priority

---
# Pod with Init Containers for Dependency Checks
apiVersion: apps/v1
kind: Deployment
metadata:
  name: database-dependent-service
  namespace: rb2-production
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        image: busybox:1.28
        command: ['sh', '-c', 'until nslookup database.$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace).svc.cluster.local; do echo waiting for database; sleep 2; done;']
      - name: check-database-ready
        image: postgres:13-alpine
        command: ['sh', '-c', 'until pg_isready -h database -p 5432; do echo waiting for database; sleep 2; done;']
      containers:
      - name: main-app
        image: rb2/database-consumer:latest
        # Resources configuration omitted for brevity

---
# Taints and Tolerations
# These require node taints to be set separately via kubectl
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dedicated-workload
  namespace: rb2-production
spec:
  template:
    spec:
      tolerations:
      - key: "dedicated"
        operator: "Equal"
        value: "rb2"
        effect: "NoSchedule"
      - key: "CriticalAddonsOnly"
        operator: "Exists"
      containers:
      - name: dedicated-app
        image: rb2/dedicated-app:latest
        # Resources configuration omitted for brevity

---
# Persistent Volume Claim with Storage Class
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: rb2-data
  namespace: rb2-production
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: fast
  resources:
    requests:
      storage: 10Gi

---
# ConfigMaps for environment-specific configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: rb2-config
  namespace: rb2-production
data:
  NODE_ENV: "production"
  API_VERSION: "v1"
  ENABLE_DETAILED_HEALTH_CHECKS: "true"
  LOG_LEVEL: "info"
  CACHE_TTL: "3600"
  DB_HEALTH_TIMEOUT: "5000"
  REDIS_HEALTH_TIMEOUT: "2000"
  RABBITMQ_HEALTH_TIMEOUT: "5000"
  API_HEALTH_TIMEOUT: "5000"

---
# Optimize pod placement with node selector
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cpu-intensive-task
  namespace: rb2-production
spec:
  template:
    spec:
      nodeSelector:
        node-role: compute-optimized
      containers:
      - name: cpu-intensive
        image: rb2/processor:latest
        resources:
          limits:
            cpu: 2
            memory: 2Gi
          requests:
            cpu: 1
            memory: 1Gi

---
# StatefulSet for ordered, persistent applications (e.g., database)
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rb2-database
  namespace: rb2-production
spec:
  serviceName: "database"
  replicas: 3
  selector:
    matchLabels:
      app: database
  template:
    metadata:
      labels:
        app: database
    spec:
      containers:
      - name: postgres
        image: postgres:latest
        ports:
        - containerPort: 5432
          name: db-port
        volumeMounts:
        - name: db-data
          mountPath: /var/lib/postgresql/data
        resources:
          limits:
            cpu: 2
            memory: 4Gi
          requests:
            cpu: 1
            memory: 2Gi
        # Liveness and readiness probes omitted for brevity
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - database
            topologyKey: "kubernetes.io/hostname"
  volumeClaimTemplates:
  - metadata:
      name: db-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "fast"
      resources:
        requests:
          storage: 20Gi
