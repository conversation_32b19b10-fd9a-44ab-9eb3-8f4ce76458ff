---
# API Service Deployment with optimized configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-service
  namespace: rb2
  labels:
    app: api-service
    component: backend
  annotations:
    description: "API service for Projet-RB2"
spec:
  # Ensure high availability with multiple replicas
  replicas: 3
  
  # Define update strategy for zero-downtime deployments
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  
  # Pod selector must match the labels in template
  selector:
    matchLabels:
      app: api-service
  
  # Template for pods created by this deployment
  template:
    metadata:
      labels:
        app: api-service
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      # Optimize pod scheduling for availability
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - api-service
              topologyKey: "kubernetes.io/hostname"
      
      # Set resource quality of service
      priorityClassName: high-priority
      
      # Configure security context
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Initialize with init containers
      initContainers:
      - name: init-db-check
        image: busybox:1.28
        command: ['sh', '-c', 'until nc -z postgres-service 5432; do echo waiting for postgres; sleep 2; done;']
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi
      
      # Main application containers
      containers:
      - name: api-service
        image: rb2/api-service:latest
        imagePullPolicy: Always
        
        # Define container port
        ports:
        - containerPort: 8080
          name: http
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        
        startupProbe:
          httpGet:
            path: /health/startup
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12 # Allow up to 60 seconds (12 * 5s) to start
        
        # Environment variables
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secrets
              key: url
        - name: NODE_ENV
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        
        # Resource limits and requests
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        
        # Volume mounts
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: tmp-volume
          mountPath: /tmp
        - name: log-volume
          mountPath: /app/logs
        
        # Lifecycle hooks
        lifecycle:
          postStart:
            exec:
              command: ["/bin/sh", "-c", "echo Service started at $(date) > /app/logs/startup.log"]
          preStop:
            exec:
              command: ["/bin/sh", "-c", "echo Service stopping at $(date) > /app/logs/shutdown.log && sleep 10"]
      
      # Sidecar container for log collection
      - name: log-collector
        image: fluent/fluent-bit:1.8
        resources:
          limits:
            cpu: "200m"
            memory: "256Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        volumeMounts:
        - name: log-volume
          mountPath: /logs
          readOnly: true
        - name: fluent-bit-config
          mountPath: /fluent-bit/etc/
      
      # Volumes
      volumes:
      - name: config-volume
        configMap:
          name: api-service-config
      - name: tmp-volume
        emptyDir: {}
      - name: log-volume
        emptyDir: {}
      - name: fluent-bit-config
        configMap:
          name: fluent-bit-config
      
      # Optimized pod termination
      terminationGracePeriodSeconds: 30
      
      # Service account with minimal permissions
      serviceAccountName: api-service-account
      
      # Pull secrets for private registry
      imagePullSecrets:
      - name: regcred

---
# Horizontal Pod Autoscaler for API Service
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-service-hpa
  namespace: rb2
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 4
        periodSeconds: 60
      selectPolicy: Max

---
# Pod Disruption Budget to ensure availability during cluster updates
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: api-service-pdb
  namespace: rb2
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: api-service

---
# Network Policy to restrict traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-service-network-policy
  namespace: rb2
spec:
  podSelector:
    matchLabels:
      app: api-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: default
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090

---
# Optimized Service Configuration
apiVersion: v1
kind: Service
metadata:
  name: api-service
  namespace: rb2
  labels:
    app: api-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: api-service
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800 # 3 hours

---
# ConfigMap for application configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-service-config
  namespace: rb2
data:
  app-config.yaml: |
    server:
      port: 8080
      cors:
        enabled: true
        origins:
          - https://rb2-frontend.example.com
    database:
      host: postgres-service
      port: 5432
      name: rb2
      maxConnections: 20
      idleTimeout: 10000
    redis:
      host: redis-service
      port: 6379
      ttl: 3600
    logging:
      level: info
      format: json
      outputs:
        - console
        - file
      file:
        path: /app/logs/application.log
        rotation:
          maxSize: 100m
          maxFiles: 10
      masking:
        enabled: true
        fields:
          - password
          - creditCard
          - personalId
    metrics:
      enabled: true
      path: /metrics
    features:
      featureA: true
      featureB: false

---
# ConfigMap for Fluent Bit configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: rb2
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush           5
        Daemon          off
        Log_Level       info
        Parsers_File    parsers.conf

    [INPUT]
        Name            tail
        Path            /logs/*.log
        Parser          docker
        Tag             kube.api-service
        Mem_Buf_Limit   5MB
        Skip_Long_Lines on

    [FILTER]
        Name            kubernetes
        Match           kube.*
        Kube_URL        https://kubernetes.default.svc.cluster.local:443
        Kube_CA_File    /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log       on
        K8S-Logging.Parser on
        K8S-Logging.Exclude off

    [OUTPUT]
        Name            es
        Match           *
        Host            elasticsearch-service
        Port            9200
        Logstash_Format on
        Logstash_Prefix rb2
        Replace_Dots    on
        Retry_Limit     False
        tls             on
        tls.verify      off

  parsers.conf: |
    [PARSER]
        Name          docker
        Format        json
        Time_Key      time
        Time_Format   %Y-%m-%dT%H:%M:%S.%L
        Time_Keep     on

---
# Resource Quota for Namespace
apiVersion: v1
kind: ResourceQuota
metadata:
  name: rb2-quota
  namespace: rb2
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16"
    limits.memory: 32Gi
    pods: "20"
    services: "10"
    configmaps: "20"
    secrets: "20"
    persistentvolumeclaims: "10"

---
# Limit Range for default container resources
apiVersion: v1
kind: LimitRange
metadata:
  name: rb2-limits
  namespace: rb2
spec:
  limits:
  - default:
      cpu: 500m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    type: Container
