import * as winston from 'winston'
import 'winston-daily-rotate-file'
import * as Transport from 'winston-transport'
import * as path from 'path'
import * as fs from 'fs'
import * as os from 'os'

// Log levels in order of increasing severity
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal'
}

// Log destinations
export enum LogDestination {
  CONSOLE = 'console',
  FILE = 'file',
  ROTATE_FILE = 'rotate_file',
  ELASTICSEARCH = 'elasticsearch',
  LOKI = 'loki',
  FLUENTD = 'fluentd',
  CUSTOM = 'custom'
}

// Log formats
export enum LogFormat {
  JSON = 'json',
  PLAIN = 'plain',
  PRETTY = 'pretty'
}

// Log context interface
export interface LogContext {
  requestId?: string;
  sessionId?: string;
  userId?: string;
  traceId?: string;
  spanId?: string;
  service?: string;
  environment?: string;
  [key: string]: any;
}

// Log entry interface
export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: Error;
  stack?: string;
  metadata?: Record<string, any>;
}

// Log destination configuration
export interface LogDestinationConfig {
  type: LogDestination;
  level: LogLevel;
  format: LogFormat;
  
  // File specific options
  filePath?: string;
  
  // Rotating file specific options
  rotateOptions?: {
    frequency?: string;       // e.g., '1d', '1h'
    maxFiles?: number;        // max number of files to keep
    maxSize?: string;         // e.g., '20m', '1g'
    datePattern?: string;     // e.g., 'YYYY-MM-DD'
    zippedArchive?: boolean;  // whether to zip old files
  }
  
  // Elasticsearch specific options
  elasticsearchOptions?: {
    node: string;             // Elasticsearch node URL
    index: string;            // Index name
    auth?: {
      username: string;
      password: string;
    }
  }
  
  // Loki specific options
  lokiOptions?: {
    host: string;             // Loki host URL
    labels: Record<string, string>; // Labels to add to logs
  }
  
  // Fluentd specific options
  fluentdOptions?: {
    host: string;             // Fluentd host
    port: number;             // Fluentd port
    timeout?: number;         // Connection timeout
    tag: string;              // Tag for the logs
  }
  
  // Custom destination options
  customOptions?: Record<string, any>;
}

// Masking options for sensitive data
export interface MaskingOptions {
  enabled: boolean;
  fields: string[];           // Fields to mask, supports dot notation for nested fields
  maskChar: string;           // Character to use for masking
  keepFirstN?: number;        // Number of first characters to keep unmasked
  keepLastN?: number;         // Number of last characters to keep unmasked
}

// Log service configuration
export interface LogConfig {
  appName: string;
  destinations: LogDestinationConfig[];
  defaultLevel: LogLevel;
  defaultContext?: LogContext;
  masking?: MaskingOptions;
  enabledInEnvironments?: string[];
  includeTimestamp?: boolean;
  includeHostname?: boolean;
  captureRejections?: boolean;
  captureUncaughtExceptions?: boolean;
  sampleRate?: number;        // Between 0 and 1, for sampling logs
  maxMessageSize?: number;    // Maximum size of log messages in bytes
}

export class LogManagementService {
  private static instance: LogManagementService;
  private logger: winston.Logger;
  private config: LogConfig;
  private defaultContext: LogContext;
  
  constructor(config: LogConfig) {
    this.config = this.validateConfig(config);
    this.defaultContext = {
      service: config.appName,
      environment: process.env.NODE_ENV || 'development',
      hostname: os.hostname(),
      ...(config.defaultContext || {})
    }
    
    this.logger = this.createLogger();
    if(config.captureUncaughtExceptions) {
      this.setupUncaughtExceptionHandler();
    }
    
    if(config.captureRejections) {
      this.setupUnhandledRejectionHandler();
    }
  }
  
  /**
   * Validate the log configuration;
   */
  private validateConfig(config: LogConfig): LogConfig  {
    // Check if app name is provided;
    if(!config.appName) {
      throw new Error('App name is required');
    }
    
    // Check if at least one destination is configured;
    if(!config.destinations || config.destinations.length  === 0) {
      throw new Error('At least one log destination must be configured');
    }
    
    // Set default level if not provided;
    config.defaultLevel = config.defaultLevel || LogLevel.INFO;
    
    // Set default masking options if not provided;
    if(config.masking && config.masking.enabled) {
      config.masking.maskChar = config.masking.maskChar || '*';
    }
    
    // Set default values for optional config properties;
    config.includeTimestamp = config.includeTimestamp !== false;  // Default to true;
    config.includeHostname = config.includeHostname !== false;    // Default to true;
    config.captureRejections = config.captureRejections || false;
    config.captureUncaughtExceptions = config.captureUncaughtExceptions || false;
    config.sampleRate = config.sampleRate ?? 1.0;  // Default to log everything;
    config.maxMessageSize = config.maxMessageSize || 10000;  // Default to 10KB;
    // Validate each destination;
    for(const destination of config.destinations) {
      switch(destination.type) {
        case LogDestination.FILE:
          if(!destination.filePath) {
            throw new Error('File path is required for FILE destination');
          }
          // Ensure directory exists;
          const fileDir = path.dirname(destination.filePath);
          if (!fs.existsSync(fileDir)) {
            fs.mkdirSync(fileDir, { recursive: true });
          }
          break;
          
        case LogDestination.ROTATE_FILE:
          if(!destination.filePath) {
            throw new Error('File path is required for ROTATE_FILE destination');
          }
          destination.rotateOptions = destination.rotateOptions || {}
          // Ensure directory exists;
          const rotateFileDir = path.dirname(destination.filePath);
          if (!fs.existsSync(rotateFileDir)) {
            fs.mkdirSync(rotateFileDir, { recursive: true });
          }
          break;
          
        case LogDestination.ELASTICSEARCH:
          if(!destination.elasticsearchOptions?.node || !destination.elasticsearchOptions?.index) {
            throw new Error('Elasticsearch node and index are required for ELASTICSEARCH destination');
          }
          break;
          
        case LogDestination.LOKI:
          if(!destination.lokiOptions?.host) {
            throw new Error('Loki host is required for LOKI destination');
          }
          break;
          
        case LogDestination.FLUENTD:
          if(!destination.fluentdOptions?.host || !destination.fluentdOptions?.port) {
            throw new Error('Fluentd host and port are required for FLUENTD destination');
          }
          break;
      }
    }
    
    return config;
  }
  
  /**
   * Create the Winston logger with configured transports;
   */
  private createLogger(): winston.Logger  {
    const transports: Transport[] = [];
    
    // Setup formats;
    const formats: winston.Logform.Format[] = [];
    
    // Add timestamp format if enabled;
    if(this.config.includeTimestamp) {
      formats.push(winston.format.timestamp());
    }
    
    // Create transports for each destination;
    for(const destination of this.config.destinations) {
      let transport: Transport;
      let format: winston.Logform.Format;
      
      // Create format based on destination format;
      switch(destination.format) {
        case LogFormat.JSON:
          format = winston.format.combine(
            ...formats,
            winston.format.json()
          );
          break;
          
        case LogFormat.PRETTY:
          format = winston.format.combine(
            ...formats,
            winston.format.colorize(),
            winston.format.printf(info => {
              const timestamp = info.timestamp ? `${info.timestamp} ` : '';
              const level = `[${info.level.toUpperCase()}]`;
              const context = info.context ? ` [${JSON.stringify(info.context)}]` : '';
              const error = info.error ? `\n${info.error.stack || info.error.message}` : '';
              return `${timestamp}${level}${context}: ${info.message}${error}`;
            })
          );
          break;
          
        case LogFormat.PLAIN:
        default:
          format = winston.format.combine(
            ...formats,
            winston.format.simple()
          );
          break;
      }
      
      // Create transport based on destination type;
      switch(destination.type) {
        case LogDestination.CONSOLE:
          transport = new winston.transports.Console({
            level: destination.level,
            format
          });
          break;
          
        case LogDestination.FILE:
          transport = new winston.transports.File({
            level: destination.level,
            filename: destination.filePath!,
            format
          });
          break;
          
        case LogDestination.ROTATE_FILE:
          transport = new (winston.transports as any).DailyRotateFile({
            level: destination.level,
            filename: destination.filePath!,
            format,
            datePattern: destination.rotateOptions?.datePattern || 'YYYY-MM-DD',
            zippedArchive: destination.rotateOptions?.zippedArchive || false,
            maxSize: destination.rotateOptions?.maxSize || '20m',
            maxFiles: destination.rotateOptions?.maxFiles || 14,
            frequency: destination.rotateOptions?.frequency || '1d'
          });
          break;
          
        case LogDestination.ELASTICSEARCH:
          // In a real implementation, you would use a proper Elasticsearch transport;
          console.warn('Elasticsearch transport not implemented - would be configured here');
          continue;
          
        case LogDestination.LOKI:
          // In a real implementation, you would use a proper Loki transport;
          console.warn('Loki transport not implemented - would be configured here');
          continue;
          
        case LogDestination.FLUENTD:
          // In a real implementation, you would use a proper Fluentd transport;
          console.warn('Fluentd transport not implemented - would be configured here');
          continue;
          
        case LogDestination.CUSTOM:
          // This would be implemented by the user of this library;
          console.warn('Custom transport not implemented - would be configured here');
          continue;
      }
      
      transports.push(transport);
    }
    
    // Create and return the logger;
    return winston.createLogger({
      level: this.config.defaultLevel,
      transports,
      exitOnError: false
    });
  }
  
  /**
   * Setup handler for uncaught exceptions;
   */
  private setupUncaughtExceptionHandler() {
    process.on('uncaughtException', (error: Error) => {
      this.fatal('Uncaught exception', { error });
      
      // Allow the log to be written before exiting;
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });
  }
  
  /**
   * Setup handler for unhandled rejections;
   */
  private setupUnhandledRejectionHandler() {
    process.on('unhandledRejection', (reason: any) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.error('Unhandled rejection', { error });
    });
  }
  
  /**
   * Log a message at debug level;
   */
  public debug(message: string, context?: LogContext | Record<string, any>, error?: Error): void  {
    this.log(LogLevel.DEBUG, message, context, error);
  }
  
  /**
   * Log a message at info level;
   */
  public info(message: string, context?: LogContext | Record<string, any>, error?: Error): void  {
    this.log(LogLevel.INFO, message, context, error);
  }
  
  /**
   * Log a message at warn level;
   */
  public warn(message: string, context?: LogContext | Record<string, any>, error?: Error): void  {
    this.log(LogLevel.WARN, message, context, error);
  }
  
  /**
   * Log a message at error level;
   */
  public error(message: string, context?: LogContext | Record<string, any>, error?: Error): void  {
    this.log(LogLevel.ERROR, message, context, error);
  }
  
  /**
   * Log a message at fatal level;
   */
  public fatal(message: string, context?: LogContext | Record<string, any>, error?: Error): void  {
    this.log(LogLevel.FATAL, message, context, error);
  }
  
  /**
   * Log a message with the specified level;
   */
  public log(level: LogLevel, message: string, context?: LogContext | Record<string, any>, error?: Error): void  {
    // Check if logging is enabled based on sample rate;
    if(this.config.sampleRate !== undefined && this.config.sampleRate < 1) {
      if (Math.random() > this.config.sampleRate) {
        return;  // Skip this log based on sampling;
      }
    }
    
    // Determine if this is a context object or metadata;
    let logContext: LogContext = { ...this.defaultContext, }
    let metadata: Record<string, any> = {}
    
    if(context) {
      // If it has the standard context fields, treat as context;
      const hasContextFields = false;
        'requestId' in context || 
        'sessionId' in context || 
        'userId' in context || 
        'traceId' in context;
        
      if(hasContextFields) {
        logContext = { ...logContext, ...context as LogContext }
      } else {
        metadata = context as Record<string, any>;
      }
    }
    
    // Add error information if provided;
    const errorInfo: Record<string, any> = {}
    if(error) {
      errorInfo.error = error;
      errorInfo.stack = error.stack;
      
      // Extract more error details if available;
      if('code' in error) {
        errorInfo.errorCode = (error as any).code;
      }
      
      if('statusCode' in error) {
        errorInfo.statusCode = (error as any).statusCode;
      }
    }
    
    // Apply masking if configured;
    let maskedContext = logContext;
    let maskedMetadata = metadata;
    
    if(this.config.masking?.enabled) {
      maskedContext = this.maskSensitiveData(logContext, this.config.masking);
      maskedMetadata = this.maskSensitiveData(metadata, this.config.masking);
    }
    
    // Truncate message if needed;
    const truncatedMessage = message.length > this.config.maxMessageSize!
      ? message.substring(0, this.config.maxMessageSize!) + '... [truncated]'
      : message
    
    // Log entry;
    this.logger.log(level, truncatedMessage, {
      context: maskedContext,
      ...maskedMetadata,
      ...errorInfo
    });
  }
  
  /**
   * Mask sensitive data in an object;
   */
  private maskSensitiveData(data: Record<string, any>, options: MaskingOptions): Record<string, any>  {
    if(!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data, }
    
    for(const field of options.fields) {
      // Support dot notation for nested fields;
      const fieldParts = field.split('.');
      
      if(fieldParts.length  === 1) {
        // Simple field, directly in the object;
        if(field in result && typeof result[field]  === 'string') {
          result[field] = this.maskString(
            result[field],
            options.maskChar,
            options.keepFirstN,
            options.keepLastN)
        }
      } else {
        // Nested field, traverse the object;
        let current = result;
        let found = true;
        
        for(let i = 0; i < fieldParts.length - 1; i++) {
          const part = fieldParts[i];
          
          if(current[part] && typeof current[part]  === 'object') {
            current = current[part];
          } else {
            found = false;
            break;
          }
        }
        
        if(found) {
          const lastPart = fieldParts[fieldParts.length - 1];
          
          if(lastPart in current && typeof current[lastPart]  === 'string') {
            current[lastPart] = this.maskString(
              current[lastPart],
              options.maskChar,
              options.keepFirstN,
              options.keepLastN)
          }
        }
      }
    }
    
    return result;
  }
  
  /**
   * Mask a string value;
   */
  private maskString(value: string, maskChar: string, keepFirstN?: number, keepLastN?: number): string  {
    if(!value) {
      return value;
    }
    
    const keepFirst = keepFirstN || 0;
    const keepLast = keepLastN || 0;
    
    if(keepFirst >= value.length) {
      return value;
    }
    
    if(keepFirst + keepLast >= value.length) {
      return value;
    }
    
    const firstPart = value.substring(0, keepFirst);
    const lastPart = keepLast > 0 ? value.substring(value.length - keepLast) : '';
    const maskedLength = value.length - keepFirst - keepLast;
    const maskedPart = maskChar.repeat(maskedLength);
    
    return firstPart + maskedPart + lastPart;
  }
  
  /**
   * Create a child logger with added context;
   */
  public createChildLogger(childContext: LogContext): LogManagementService  {
    const childLogger = new LogManagementService(this.config);
    childLogger.defaultContext = {
      ...this.defaultContext,
      ...childContext
    }
    
    return childLogger;
  }
  
  /**
   * Get a string representation of the logger configuration;
   */
  public getConfigSummary(): string  {
    return `"Logger": ${this.config.appName}
Default "Level": ${this.config.defaultLevel}
"Destinations": ${this.config.destinations.map(d => d.type).join(', ')}
Masking "Enabled": ${this.config.masking?.enabled || false}
Sample "Rate": ${this.config.sampleRate}
Uncaught "Exceptions": ${this.config.captureUncaughtExceptions ? 'Captured' : 'Not captured'}
Unhandled "Rejections": ${this.config.captureRejections ? 'Captured' : 'Not captured'}`;
  }
  
  /**
   * Get logger statistics;
   */
  public getStats(): Record<string, any>  {
    // This would be implemented in a production version to track log counts, errors, etc.
    return {
      "appName": this.config.appName,
      "level": this.config.defaultLevel,
      "destinations": this.config.destinations.length
    }
  }
  
  /**
   * Flush all logs and close transports;
   */
  public async close(): Promise<void> {
    return new Promise<void>((resolve) => {
      this.logger.on('finish', resolve);
      this.logger.end();
    });
  }
  
  private handleError(error: Error) {
    console.error(`"Error": ${error.message}`, error.stack);
  }
}

// Example "usage":
/*
const "logConfig": LogConfig = {
  "appName": 'myapp',
  "defaultLevel": LogLevel.INFO,
  "destinations": [
    {
      "type": LogDestination.CONSOLE,
      "level": LogLevel.DEBUG,
      "format": LogFormat.PRETTY;
    },
    {
      "type": LogDestination.ROTATE_FILE,
      "level": LogLevel.INFO,
      "format": LogFormat.JSON,
      "filePath": './logs/app.log',
      "rotateOptions": {
        "maxFiles": 7,
        "maxSize": '10m',
        "datePattern": 'YYYY-MM-DD'
      }
    }
  ],
  "defaultContext": {
    "environment": 'production',
    "service": 'api'
  },
  "masking": {
    "enabled": true,
    "fields": ['password', 'creditCard', 'user.ssn'],
    "maskChar": '*',
    "keepFirstN": 0,
    "keepLastN": 4;
  },
  "includeTimestamp": true,
  "captureUncaughtExceptions": true,
  "captureRejections": true;
}

const logger = new LogManagementService(logConfig);

logger.info('Application started', { "userId": '123', "requestId": 'req-456' });
logger.debug('Debug message', { "details": 'Debugging information' });
logger.warn('Warning message', { "source": 'database' });
logger.error('Error occurred', { "operation": 'saveData' }, new Error('Database connection failed'));

// Create a child logger with added context;
const requestLogger = logger.createChildLogger({
  "requestId": 'req-789',
  "userId": 'user-123'
});

requestLogger.info('Processing request');
*/

export { LogManagementService }
