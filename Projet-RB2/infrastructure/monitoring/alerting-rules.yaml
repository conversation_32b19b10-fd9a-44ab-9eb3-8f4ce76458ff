apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: rb2-alerting-rules
  namespace: monitoring
  labels:
    role: alert-rules
    app: prometheus
spec:
  groups:
  # System-level alerts
  - name: system-alerts
    rules:
    - alert: HighCPUUsage
      expr: avg by (node) (100 - (avg by (node) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 85
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage on {{ $labels.node }}"
        description: "CPU usage is above 85% for more than 10 minutes on node {{ $labels.node }}"
        runbook_url: "https://wiki.rb2-app.com/runbooks/high-cpu-usage"
        
    - alert: HighMemoryUsage
      expr: (node_memory_MemTotal_bytes - node_memory_MemFree_bytes - node_memory_Buffers_bytes - node_memory_Cached_bytes) / node_memory_MemTotal_bytes * 100 > 90
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage on {{ $labels.node }}"
        description: "Memory usage is above 90% for more than 10 minutes on node {{ $labels.node }}"
        runbook_url: "https://wiki.rb2-app.com/runbooks/high-memory-usage"
        
    - alert: HighDiskUsage
      expr: 100 - ((node_filesystem_avail_bytes{mountpoint="/"} * 100) / node_filesystem_size_bytes{mountpoint="/"}) > 85
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "High disk usage on {{ $labels.node }}"
        description: "Disk usage is above 85% for more than 10 minutes on node {{ $labels.node }}"
        runbook_url: "https://wiki.rb2-app.com/runbooks/high-disk-usage"
        
    - alert: DiskWillFillIn4Hours
      expr: predict_linear(node_filesystem_free_bytes{mountpoint="/"}[1h], 4 * 3600) < 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Disk will fill in 4 hours on {{ $labels.node }}"
        description: "Disk {{ $labels.device }} on {{ $labels.node }} will fill in 4 hours at the current rate"
        runbook_url: "https://wiki.rb2-app.com/runbooks/disk-space-running-out"
        
    - alert: NodeDown
      expr: up{job="node-exporter"} == 0
      for: 3m
      labels:
        severity: critical
      annotations:
        summary: "Node {{ $labels.node }} is down"
        description: "Node {{ $labels.node }} has been down for more than 3 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/node-down"
  
  # Kubernetes-specific alerts
  - name: kubernetes-alerts
    rules:
    - alert: KubernetesPodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "Pod {{ $labels.pod }} is crash looping"
        description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} is crash looping"
        runbook_url: "https://wiki.rb2-app.com/runbooks/pod-crash-looping"
        
    - alert: KubernetesPodNotReady
      expr: sum by (namespace, pod) (kube_pod_status_phase{phase=~"Pending|Unknown"}) > 0
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "Pod {{ $labels.pod }} is not ready"
        description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has been in non-ready state for more than 15 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/pod-not-ready"
        
    - alert: KubernetesDeploymentReplicasMismatch
      expr: kube_deployment_spec_replicas != kube_deployment_status_replicas_available
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "Deployment {{ $labels.deployment }} replicas mismatch"
        description: "Deployment {{ $labels.deployment }} in namespace {{ $labels.namespace }} has replica mismatch"
        runbook_url: "https://wiki.rb2-app.com/runbooks/deployment-replicas-mismatch"
        
    - alert: KubernetesJobFailed
      expr: kube_job_status_failed > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "Job {{ $labels.job_name }} failed"
        description: "Job {{ $labels.job_name }} in namespace {{ $labels.namespace }} failed to complete"
        runbook_url: "https://wiki.rb2-app.com/runbooks/job-failed"
        
    - alert: KubernetesPersistentVolumeFilling
      expr: kubelet_volume_stats_available_bytes / kubelet_volume_stats_capacity_bytes * 100 < 20
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "PersistentVolume {{ $labels.persistentvolume }} is filling up"
        description: "PersistentVolume {{ $labels.persistentvolume }} has less than 20% free space"
        runbook_url: "https://wiki.rb2-app.com/runbooks/pv-filling-up"
  
  # Application-specific alerts
  - name: rb2-application-alerts
    rules:
    - alert: APIHighErrorRate
      expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service) > 0.05
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High error rate on {{ $labels.service }}"
        description: "Service {{ $labels.service }} has error rate above 5% for 5 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/api-high-error-rate"
        
    - alert: APILatencyHigh
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service)) > 2
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High API latency on {{ $labels.service }}"
        description: "Service {{ $labels.service }} has 95th percentile latency above 2 seconds for 5 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/api-high-latency"
        
    - alert: UserAuthFailures
      expr: sum(increase(auth_failures_total[5m])) by (instance) > 10
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Multiple authentication failures detected"
        description: "More than 10 authentication failures in the last 5 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/auth-failures"
        
    - alert: DatabaseConnectionsHigh
      expr: sum(pg_stat_activity_count) by (datname) > 80
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High database connections"
        description: "Database {{ $labels.datname }} has more than 80 connections for 5 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/db-connections-high"
        
    - alert: RedisMemoryHigh
      expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Redis memory usage high"
        description: "Redis memory usage is above 80% for 5 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/redis-memory-high"
        
    - alert: RabbitMQQueueLength
      expr: rabbitmq_queue_messages > 1000
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "RabbitMQ queue {{ $labels.queue }} is growing"
        description: "RabbitMQ queue {{ $labels.queue }} has more than 1000 messages for 10 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/rabbitmq-queue-length"
        
    - alert: BackendServiceDown
      expr: up{job=~"backend-.*"} == 0
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "Backend service {{ $labels.job }} is down"
        description: "Backend service {{ $labels.job }} has been down for more than 2 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/service-down"
        
    - alert: FrontendServiceDown
      expr: up{job=~"frontend-.*"} == 0
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: "Frontend service {{ $labels.job }} is down"
        description: "Frontend service {{ $labels.job }} has been down for more than 2 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/service-down"
  
  # Business metrics alerts
  - name: business-alerts
    rules:
    - alert: HighCheckoutFailureRate
      expr: sum(rate(checkout_failures_total[15m])) / sum(rate(checkout_attempts_total[15m])) > 0.05
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "High checkout failure rate"
        description: "Checkout failure rate is above 5% for 5 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/high-checkout-failure-rate"
        
    - alert: UserRegistrationDropped
      expr: rate(user_registrations_total[1h]) < 10
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "User registrations have dropped"
        description: "User registrations are lower than 10 per hour for the past hour"
        runbook_url: "https://wiki.rb2-app.com/runbooks/user-registration-drop"
        
    - alert: HighPaymentDeclineRate
      expr: sum(rate(payment_failures_total[15m])) / sum(rate(payment_attempts_total[15m])) > 0.1
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: "High payment decline rate"
        description: "Payment decline rate is above 10% for 15 minutes"
        runbook_url: "https://wiki.rb2-app.com/runbooks/high-payment-decline-rate"
        
  # SLO alerts
  - name: slo-alerts
    rules:
    - alert: APIAvailabilityBudgetBurning
      expr: sum(rate(http_requests_total{status!~"5.."}[1h])) by (service) / sum(rate(http_requests_total[1h])) by (service) < 0.99
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "API availability budget burning"
        description: "Service {{ $labels.service }} availability is below SLO for 1 hour"
        runbook_url: "https://wiki.rb2-app.com/runbooks/api-availability-slo"
        
    - alert: APILatencyBudgetBurning
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[1h])) by (le, service)) > 1
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "API latency budget burning"
        description: "Service {{ $labels.service }} latency SLO is being violated for 1 hour"
        runbook_url: "https://wiki.rb2-app.com/runbooks/api-latency-slo"
