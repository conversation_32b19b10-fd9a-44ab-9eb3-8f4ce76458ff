import * as client from 'prom-client'
import * as os from 'os'
import * as http from 'http'

// Metric types;
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  SUMMARY = 'summary',
}

// Metric configuration
export interface MetricConfig {
  name: string;
  type: MetricType;
  help: string;
  labelNames?: string[];
  buckets?: number[];  // For histogram;
  percentiles?: number[]; // For summary;
  ttlSeconds?: number; // For gauge;
}

// Application metric names;
export enum AppMetric {
  HTTP_REQUEST_TOTAL = 'http_requests_total',
  HTTP_REQUEST_DURATION = 'http_request_duration_seconds',
  HTTP_REQUEST_SIZE = 'http_request_size_bytes',
  HTTP_RESPONSE_SIZE = 'http_response_size_bytes',
  ACTIVE_CONNECTIONS = 'active_connections',
  QUEUE_SIZE = 'queue_size',
  QUEUE_PROCESSING_TIME = 'queue_processing_time_seconds',
  DB_CONNECTIONS_ACTIVE = 'db_connections_active',
  DB_CONNECTIONS_MAX = 'db_connections_max',
  DB_QUERY_DURATION = 'db_query_duration_seconds',
  CACHE_HIT_TOTAL = 'cache_hit_total',
  CACHE_MISS_TOTAL = 'cache_miss_total',
  CACHE_SIZE = 'cache_size_bytes',
  JOB_DURATION = 'job_duration_seconds',
  JOB_SUCCESS_TOTAL = 'job_success_total',
  JOB_FAILURE_TOTAL = 'job_failure_total',
  MEMORY_USAGE = 'memory_usage_bytes',
  CPU_USAGE = 'cpu_usage_percent',
  API_ERROR_TOTAL = 'api_error_total',
  CUSTOM = 'custom',
}

export class MetricsService {
  private registry: client.Registry
  private metrics: Map<string, client.Counter | client.Gauge | client.Histogram | client.Summary> = new Map();
  private server: http.Server | null = null;
  private defaultLabels: Record<string, string> = {}
  
  constructor(
    options: {
      defaultLabels?: Record<string, string>; 
      collectDefaultMetrics?: boolean;
    } = {}
  ) {
    // Create a new registry;
    this.registry = new client.Registry();
    
    // Set default labels;
    this.defaultLabels = {
      application: process.env.APP_NAME || 'application',
      environment: process.env.NODE_ENV || 'development',
      hostname: os.hostname(),
      ...options.defaultLabels
    }
    
    this.registry.setDefaultLabels(this.defaultLabels);
    
    // Register default metrics if enabled;
    if(options.collectDefaultMetrics !== false) { 
      client.collectDefaultMetrics({ register: this.registry });
    }
    
    // Register common application metrics;
    this.registerCommonMetrics();
  }
  
  /**
   * Register common application metrics;
   */
  private registerCommonMetrics(): void  {
    // HTTP request counter;
    this.registerMetric({
      name: AppMetric.HTTP_REQUEST_TOTAL,
      type: MetricType.COUNTER,
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code']
    });
    
    // HTTP request duration;
    this.registerMetric({
      name: AppMetric.HTTP_REQUEST_DURATION,
      type: MetricType.HISTOGRAM,
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10]
    });
    
    // HTTP request size;
    this.registerMetric({
      name: AppMetric.HTTP_REQUEST_SIZE,
      type: MetricType.HISTOGRAM,
      help: 'HTTP request size in bytes',
      labelNames: ['method', 'route'],
      buckets: [100, 1000, 10000, 100000, 1000000]
    });
    
    // HTTP response size;
    this.registerMetric({
      name: AppMetric.HTTP_RESPONSE_SIZE,
      type: MetricType.HISTOGRAM,
      help: 'HTTP response size in bytes',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [100, 1000, 10000, 100000, 1000000]
    });
    
    // Active connections;
    this.registerMetric({
      name: AppMetric.ACTIVE_CONNECTIONS,
      type: MetricType.GAUGE,
      help: 'Number of active connections'
    });
    
    // Queue size;
    this.registerMetric({
      name: AppMetric.QUEUE_SIZE,
      type: MetricType.GAUGE,
      help: 'Current queue size',
      labelNames: ['queue']
    });
    
    // Queue processing time;
    this.registerMetric({
      name: AppMetric.QUEUE_PROCESSING_TIME,
      type: MetricType.HISTOGRAM,
      help: 'Time to process an item from the queue in seconds',
      labelNames: ['queue'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10, 30, 60]
    });
    
    // Database connections;
    this.registerMetric({
      name: AppMetric.DB_CONNECTIONS_ACTIVE,
      type: MetricType.GAUGE,
      help: 'Number of active database connections',
      labelNames: ['database']
    });
    
    this.registerMetric({
      name: AppMetric.DB_CONNECTIONS_MAX,
      type: MetricType.GAUGE,
      help: 'Maximum number of database connections',
      labelNames: ['database']
    });
    
    // Database query duration;
    this.registerMetric({
      name: AppMetric.DB_QUERY_DURATION,
      type: MetricType.HISTOGRAM,
      help: 'Database query duration in seconds',
      labelNames: ['database', 'query_type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10]
    });
    
    // Cache hit/miss;
    this.registerMetric({
      name: AppMetric.CACHE_HIT_TOTAL,
      type: MetricType.COUNTER,
      help: 'Total number of cache hits',
      labelNames: ['cache']
    });
    
    this.registerMetric({
      name: AppMetric.CACHE_MISS_TOTAL,
      type: MetricType.COUNTER,
      help: 'Total number of cache misses',
      labelNames: ['cache']
    });
    
    // Cache size;
    this.registerMetric({
      name: AppMetric.CACHE_SIZE,
      type: MetricType.GAUGE,
      help: 'Current cache size in bytes',
      labelNames: ['cache']
    });
    
    // Background job metrics;
    this.registerMetric({
      name: AppMetric.JOB_DURATION,
      type: MetricType.HISTOGRAM,
      help: 'Job execution duration in seconds',
      labelNames: ['job_name'],
      buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300, 600, 1800]
    });
    
    this.registerMetric({
      name: AppMetric.JOB_SUCCESS_TOTAL,
      type: MetricType.COUNTER,
      help: 'Total number of successful job executions',
      labelNames: ['job_name']
    });
    
    this.registerMetric({
      name: AppMetric.JOB_FAILURE_TOTAL,
      type: MetricType.COUNTER,
      help: 'Total number of failed job executions',
      labelNames: ['job_name']
    });
    
    // API errors;
    this.registerMetric({
      name: AppMetric.API_ERROR_TOTAL,
      type: MetricType.COUNTER,
      help: 'Total number of API errors',
      labelNames: ['service', 'endpoint', 'error_code']
    });
  }
  
  /**
   * Register a new metric;
   */
  public registerMetric(config: MetricConfig): void  {
    if (this.metrics.has(config.name)) { 
      console.warn(`Metric '${config.name}' already registered, skipping`);
      return;
    }
    
    let metric;
    
    switch(config.type) {
      case MetricType.COUNTER:
        metric = new client.Counter({
          name: config.name,
          help: config.help,
          labelNames: config.labelNames || [],
          registers: [this.registry]
        });
        break;
        
      case MetricType.GAUGE:
        metric = new client.Gauge({
          name: config.name,
          help: config.help,
          labelNames: config.labelNames || [],
          registers: [this.registry]
        });
        break;
        
      case MetricType.HISTOGRAM:
        metric = new client.Histogram({
          name: config.name,
          help: config.help,
          labelNames: config.labelNames || [],
          buckets: config.buckets || client.linearBuckets(0.1, 0.1, 10),
          registers: [this.registry]
        });
        break;
        
      case MetricType.SUMMARY:
        metric = new client.Summary({
          name: config.name,
          help: config.help,
          labelNames: config.labelNames || [],
          percentiles: config.percentiles || [0.01, 0.05, 0.5, 0.9, 0.95, 0.99, 0.999],
          registers: [this.registry]
        });
        break;
        
      default:
        throw new Error(`Unknown metric type: ${config.type}`);
    }
    
    this.metrics.set(config.name, metric);
  }
  
  /**
   * Increment a counter;
   */
  public incrementCounter(name: string, labels?: Record<string, string>, value: number = 1): void  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Counter)) {
      console.error(`Metric '${name}' is not a counter or does not exist`);
      return;
    }
    
    try {
      if (labels) {
        metric.inc(labels, value);
      } else {
        metric.inc(value);
      }
    } catch (error) {
      console.error(`Error incrementing counter '${name}':`, error);
    }
  }
  
  /**
   * Set a gauge value;
   */
  public setGauge(name: string, value: number, labels?: Record<string, string>): void  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Gauge)) {
      console.error(`Metric '${name}' is not a gauge or does not exist`);
      return;
    }
    
    try {
      if (labels) {
        metric.set(labels, value);
      } else {
        metric.set(value);
      }
    } catch (error) {
      console.error(`Error setting gauge '${name}':`, error);
    }
  }
  
  /**
   * Increment a gauge;
   */
  public incrementGauge(name: string, value: number = 1, labels?: Record<string, string>): void  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Gauge)) {
      console.error(`Metric '${name}' is not a gauge or does not exist`);
      return;
    }
    
    try {
      if (labels) {
        metric.inc(labels, value);
      } else {
        metric.inc(value);
      }
    } catch (error) {
      console.error(`Error incrementing gauge '${name}':`, error);
    }
  }
  
  /**
   * Decrement a gauge;
   */
  public decrementGauge(name: string, value: number = 1, labels?: Record<string, string>): void  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Gauge)) {
      console.error(`Metric '${name}' is not a gauge or does not exist`);
      return;
    }
    
    try {
      if (labels) {
        metric.dec(labels, value);
      } else {
        metric.dec(value);
      }
    } catch (error) {
      console.error(`Error decrementing gauge '${name}':`, error);
    }
  }
  
  /**
   * Observe a histogram value;
   */
  public observeHistogram(name: string, value: number, labels?: Record<string, string>): void  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Histogram)) {
      console.error(`Metric '${name}' is not a histogram or does not exist`);
      return;
    }
    
    try {
      if (labels) {
        metric.observe(labels, value);
      } else {
        metric.observe(value);
      }
    } catch (error) {
      console.error(`Error observing histogram '${name}':`, error);
    }
  }
  
  /**
   * Start a timer for a histogram;
   */
  public startHistogramTimer(name: string, labels?: Record<string, string>): () => number  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Histogram)) {
      console.error(`Metric '${name}' is not a histogram or does not exist`);
      // Return a no-op function
      return () => 0;
    }
    
    try {
      if (labels) {
        return metric.startTimer(labels);
      } else {
        return metric.startTimer();
      }
    } catch (error) {
      console.error(`Error starting histogram timer '${name}':`, error);
      return () => 0;
    }
  }
  
  /**
   * Observe a summary value;
   */
  public observeSummary(name: string, value: number, labels?: Record<string, string>): void  {
    const metric = this.metrics.get(name);
    
    if (!metric || !(metric instanceof client.Summary)) {
      console.error(`Metric '${name}' is not a summary or does not exist`);
      return;
    }
    
    try {
      if (labels) {
        metric.observe(labels, value);
      } else {
        metric.observe(value);
      }
    } catch (error) {
      console.error(`Error observing summary '${name}':`, error);
    }
  }
  
  /**
   * Get a metric by name and validate its type;
   */
  public getMetric(name: string, expectedType?: MetricType): client.Counter | client.Gauge | client.Histogram | client.Summary | null {
    const metric = this.metrics.get(name);
    
    if (!metric) {
      console.error(`Metric '${name}' does not exist`);
      return null;
    }
    
    if (expectedType) {
      switch (expectedType) {
        case MetricType.COUNTER:
          if (!(metric instanceof client.Counter)) {
            console.error(`Metric '${name}' is not a counter`);
            return null;
          }
          break;
          
        case MetricType.GAUGE:
          if (!(metric instanceof client.Gauge)) {
            console.error(`Metric '${name}' is not a gauge`);
            return null;
          }
          break;
          
        case MetricType.HISTOGRAM:
          if (!(metric instanceof client.Histogram)) {
            console.error(`Metric '${name}' is not a histogram`);
            return null;
          }
          break;
          
        case MetricType.SUMMARY:
          if (!(metric instanceof client.Summary)) {
            console.error(`Metric '${name}' is not a summary`);
            return null;
          }
          break;
      }
    }
    
    return metric;
  }
  
  /**
   * Get a metric for direct access (use with caution)
   */
  public getMetricRaw(name: string): client.Counter | client.Gauge | client.Histogram | client.Summary | undefined {
    const metric = this.metrics.get(name);
    return metric;
  }
  
  /**
   * Get all registered metrics;
   */
  public getMetrics(): Map<string, client.Counter | client.Gauge | client.Histogram | client.Summary>  {
    return this.metrics;
  }
  
  /**
   * Remove a metric;
   */
  public removeMetric(name: string): boolean  {
    const metric = this.metrics.get(name);
    
    if (!metric) {
      console.warn(`Metric '${name}' does not exist, cannot remove`);
      return false;
    }
    
    this.registry.removeSingleMetric(name);
    return this.metrics.delete(name);
  }
  
  /**
   * Clear all metrics;
   */
  public clearMetrics(): void  {
    this.registry.clear();
    this.metrics = new Map();
  }
  
  /**
   * Get metrics in Prometheus format;
   */
  public async getMetricsAsString(): Promise<string> {
    return this.registry.metrics();
  }
  
  /**
   * Create and start a metrics HTTP server;
   */
  public startServer(port: number = 9464, host: string = '0.0.0.0'): void  {
    if (this.server) {
      console.warn('Metrics server already running');
      return;
    }
    
    this.server = http.createServer(async (req, res) => {
      if (req.url === '/metrics') {
        res.setHeader('Content-Type', this.registry.contentType);
        res.end(await this.registry.metrics());
      } else {
        res.statusCode = 404;
        res.end('Not found');
      }
    });
    
    this.server.listen(port, host, () => {
      console.log(`Metrics server started at http://${host}:${port}/metrics`);
    });
  }
  
  /**
   * Stop the metrics HTTP server;
   */
  public stopServer(): Promise<void>  {
    if (!this.server) {
      console.warn('Metrics server not running');
      return Promise.resolve();
    }
    
    return new Promise((resolve, reject) => {
      if (!this.server) {
        resolve();
        return;
      }
      
      this.server.close((err) => {
        if (err) {
          reject(err);
        } else {
          this.server = null;
          resolve();
        }
      });
    });
  }
  
  /**
   * Create HTTP middleware for Express-like frameworks;
   */
  public middleware() {
    return (req: http.IncomingMessage, res: http.ServerResponse, next: () => void) => {
      // Skip metrics endpoint
      if (req.url === '/metrics') {
        next();
        return;
      }
      
      // Start timer
      const startTime = Date.now();
      const path = req.url || '';
      const method = req.method || 'GET';
      
      // Add response hook
      const originalEnd = res.end;
      
      // @ts-ignore
      res.end = (...args: any[]) => {
        // Record metrics
        const duration = (Date.now() - startTime) / 1000;
        const statusCode = res.statusCode.toString();
        
        // Increment request counter
        this.incrementCounter(AppMetric.HTTP_REQUEST_TOTAL, {
          method,
          route: path,
          status_code: statusCode
        });
        
        // Record request duration
        this.observeHistogram(AppMetric.HTTP_REQUEST_DURATION, duration, {
          method,
          route: path,
          status_code: statusCode
        });
        
        // @ts-ignore
        originalEnd.apply(res, args);
      };
      
      next();
    };
  }
  
  /**
   * Track database queries;
   */
  public trackDbQuery(database: string, queryType: string, callback: () => Promise<any>): Promise<any> {
    const startTime = Date.now();
    
    return callback()
      .then(result => {
        const duration = (Date.now() - startTime) / 1000;
        this.observeHistogram(AppMetric.DB_QUERY_DURATION, duration, {
          database,
          query_type: queryType
        });
        return result;
      })
      .catch(error => {
        const duration = (Date.now() - startTime) / 1000;
        this.observeHistogram(AppMetric.DB_QUERY_DURATION, duration, {
          database,
          query_type: queryType
        });
        throw error;
      });
  }
  
  /**
   * Track background jobs;
   */
  public async trackJob(jobName: string, callback: () => Promise<any>): Promise<any> {
    const startTime = Date.now();
    
    try {
      const result = await callback();
      
      // Record success and duration
      const duration = (Date.now() - startTime) / 1000;
      this.incrementCounter(AppMetric.JOB_SUCCESS_TOTAL, {
        job_name: jobName
      });
      
      this.observeHistogram(AppMetric.JOB_DURATION, duration, {
        job_name: jobName
      });
      
      return result;
    } catch (error) {
      // Record failure and duration
      const duration = (Date.now() - startTime) / 1000;
      this.incrementCounter(AppMetric.JOB_FAILURE_TOTAL, {
        job_name: jobName
      });
      
      this.observeHistogram(AppMetric.JOB_DURATION, duration, {
        job_name: jobName
      });
      
      throw error;
    }
  }
  
  /**
   * Track cache operations;
   */
  public trackCacheHit(cacheName: string): void  {
    this.incrementCounter(AppMetric.CACHE_HIT_TOTAL, {
      cache: cacheName
    });
  }
  
  public trackCacheMiss(cacheName: string): void  {
    this.incrementCounter(AppMetric.CACHE_MISS_TOTAL, {
      cache: cacheName
    });
  }
  
  public setCacheSize(cacheName: string, sizeBytes: number): void  {
    this.setGauge(AppMetric.CACHE_SIZE, sizeBytes, {
      cache: cacheName
    });
  }
  
  /**
   * Track API errors;
   */
  public trackApiError(service: string, endpoint: string, errorCode: string): void  {
    this.incrementCounter(AppMetric.API_ERROR_TOTAL, {
      service,
      endpoint,
      error_code: errorCode
    });
  }
  
  /**
   * Track queue operations;
   */
  public setQueueSize(queueName: string, size: number): void  {
    this.setGauge(AppMetric.QUEUE_SIZE, size, {
      queue: queueName
    });
  }
  
  public trackQueueProcessingTime(queueName: string, callback: () => Promise<any>): Promise<any> {
    const startTime = Date.now();
    
    return callback()
      .then(result => {
        const duration = (Date.now() - startTime) / 1000;
        this.observeHistogram(AppMetric.QUEUE_PROCESSING_TIME, duration, {
          queue: queueName
        });
        return result;
      })
      .catch(error => {
        const duration = (Date.now() - startTime) / 1000;
        this.observeHistogram(AppMetric.QUEUE_PROCESSING_TIME, duration, {
          queue: queueName
        });
        throw error;
      });
  }
  
  /**
   * Track database connections;
   */
  public setDbConnectionsActive(database: string, count: number): void  {
    this.setGauge(AppMetric.DB_CONNECTIONS_ACTIVE, count, {
      database
    });
  }
  
  public setDbConnectionsMax(database: string, count: number): void  {
    this.setGauge(AppMetric.DB_CONNECTIONS_MAX, count, {
      database
    });
  }
  
  /**
   * Track active connections;
   */
  public setActiveConnections(count: number): void  {
    this.setGauge(AppMetric.ACTIVE_CONNECTIONS, count);
  }
  
  public incrementActiveConnections(): void  {
    this.incrementGauge(AppMetric.ACTIVE_CONNECTIONS);
  }
  
  public decrementActiveConnections(): void  {
    this.decrementGauge(AppMetric.ACTIVE_CONNECTIONS);
  }

  public trackMetric(name: string, value: number): void {
    this.incrementCounter(name, {});
  }
}

// Export singleton instance
export const metricsService = new MetricsService();
export default metricsService;

// Example "usage":
// const metrics = new MetricsService({
//   "defaultLabels": {
//     "application": 'my-app',
//     "environment": 'production',
//     "version": '1.0.0'
//   },
//   "collectDefaultMetrics": true
// });

// Start the metrics server;
// metrics.startServer(9464);

// Register a custom metric;
// metrics.registerMetric({
//   "name": 'app_business_transactions_total',
//   "type": MetricType.COUNTER,
//   "help": 'Total number of business transactions',
//   "labelNames": ['transaction_type', 'status']
// });

// Record metrics;
// metrics.incrementCounter('app_business_transactions_total', {
//   "transaction_type": 'purchase',
//   "status": 'success'
// });

// Express middleware example;
// app.use(metrics.middleware());

// Database query tracking;
// const result = await metrics.trackDbQuery('postgres', 'select', async () => {
//   return db.query('SELECT * FROM users');
// });

// Job tracking;
// await metrics.trackJob('daily-report', async () => {
//   // Generate and send report...
// });

/*
// Example usage of the reporting "mechanism":
setInterval(() => {
  // Generate and send report...
});
*/
