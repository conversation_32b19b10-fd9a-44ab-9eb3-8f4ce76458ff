apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: logging
  labels:
    app: fluent-bit
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush        5
        Daemon       Off
        Log_Level    info
        Parsers_File parsers.conf
        HTTP_Server  On
        HTTP_Listen  0.0.0.0
        HTTP_Port    2020

    # Collect metrics exposed by Fluent Bit
    [INPUT]
        Name     prometheus_scrape
        Host     0.0.0.0
        Port     2021
        Metrics  on

    # Application logs (from containers)
    [INPUT]
        Name              tail
        Tag               kube.*
        Path              /var/log/containers/*.log
        Parser            docker
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10

    # System logs (host)
    [INPUT]
        Name              tail
        Tag               host.*
        Path              /var/log/syslog
        Parser            syslog
        DB                /var/log/flb_host.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10

    # Kubernetes metadata enrichment
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log           On
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off
        Annotations         Off

    # RB2 API logs processing
    [FILTER]
        Name         grep
        Match        kube.var.log.containers.backend-api-*
        Regex        app rb2-api

    # Convert API logs to structured format
    [FILTER]
        Name         parser
        Match        kube.var.log.containers.backend-api-*
        Key_Name     log
        Parser       json
        Reserve_Data On

    # Add severity level based on log level
    [FILTER]
        Name         modify
        Match        kube.var.log.containers.backend-api-*
        Condition    Key_value_matches level ^error$
        Set          severity error
        Condition    Key_value_matches level ^warn$
        Set          severity warning
        Condition    Key_value_matches level ^info$
        Set          severity info
        Condition    Key_value_matches level ^debug$
        Set          severity debug

    # RB2 Frontend logs processing
    [FILTER]
        Name         grep
        Match        kube.var.log.containers.frontend-app-*
        Regex        app rb2-frontend

    # Convert Frontend logs to structured format
    [FILTER]
        Name         parser
        Match        kube.var.log.containers.frontend-app-*
        Key_Name     log
        Parser       json
        Reserve_Data On

    # Database logs processing
    [FILTER]
        Name         grep
        Match        kube.var.log.containers.postgres-*
        Regex        app postgres

    # Enrich all logs with environment data
    [FILTER]
        Name         record_modifier
        Match        kube.*
        Record       environment ${ENVIRONMENT}
        Record       app_name rb2

    # Convert millisecond timestamps
    [FILTER]
        Name         modify
        Match        kube.*
        Condition    Key_exists timestamp
        Copy         timestamp time_ms

    # Record specific error patterns for quick detection
    [FILTER]
        Name         rewrite_tag
        Match        kube.*
        Rule         $log .*OutOfMemoryError.* memory_errors false
        Rule         $log .*Exception.* exception_detected false
        Rule         $log .*timeout.* timeout_detected false
        Rule         $log .*refused.* connection_issue false
        Rule         $log .*unauthorized.* auth_issue false

    # Send specific urgent errors to a separate Elasticsearch index
    [OUTPUT]
        Name            es
        Match           memory_errors
        Host            ${ELASTICSEARCH_HOST}
        Port            ${ELASTICSEARCH_PORT}
        HTTP_User       ${ELASTICSEARCH_USER}
        HTTP_Passwd     ${ELASTICSEARCH_PASSWORD}
        Index           rb2-critical-errors
        Logstash_Format On
        Logstash_Prefix rb2-critical
        Replace_Dots    On
        Retry_Limit     False
        tls             ${ELASTICSEARCH_TLS}
        tls.verify      ${ELASTICSEARCH_TLS_VERIFY}

    # Output all logs to Elasticsearch with daily indices
    [OUTPUT]
        Name            es
        Match           kube.*
        Host            ${ELASTICSEARCH_HOST}
        Port            ${ELASTICSEARCH_PORT}
        HTTP_User       ${ELASTICSEARCH_USER}
        HTTP_Passwd     ${ELASTICSEARCH_PASSWORD}
        Index           rb2-logs
        Logstash_Format On
        Logstash_Prefix rb2
        Replace_Dots    On
        Retry_Limit     False
        tls             ${ELASTICSEARCH_TLS}
        tls.verify      ${ELASTICSEARCH_TLS_VERIFY}

    # Output host logs to separate index
    [OUTPUT]
        Name            es
        Match           host.*
        Host            ${ELASTICSEARCH_HOST}
        Port            ${ELASTICSEARCH_PORT}
        HTTP_User       ${ELASTICSEARCH_USER}
        HTTP_Passwd     ${ELASTICSEARCH_PASSWORD}
        Index           rb2-host-logs
        Logstash_Format On
        Logstash_Prefix rb2-host
        Replace_Dots    On
        Retry_Limit     False
        tls             ${ELASTICSEARCH_TLS}
        tls.verify      ${ELASTICSEARCH_TLS_VERIFY}

    # Backup logs to S3 for long-term storage and compliance
    [OUTPUT]
        Name                         s3
        Match                        kube.*
        bucket                       ${S3_BUCKET}
        region                       ${S3_REGION}
        s3_key_format                /logs/$TAG[2]/%Y/%m/%d/%H_%M_%S
        s3_key_format_tag_delimiters .
        use_put_object               On
        compression                  gzip
        store_dir                    /tmp/fluent-bit/s3
        total_file_size              50M
        upload_timeout               10m
        Conditional                  ${ENABLE_S3_BACKUP}

  parsers.conf: |
    [PARSER]
        Name        docker
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On

    [PARSER]
        Name        syslog
        Format      regex
        Regex       ^(?<time>[^ ]* {1,2}[^ ]* [^ ]*) (?<host>[^ ]*) (?<ident>[a-zA-Z0-9_\/\.\-]*)(?:\[(?<pid>[0-9]+)\])?(?:[^\:]*\:)? *(?<message>.*)$
        Time_Key    time
        Time_Format %b %d %H:%M:%S

    [PARSER]
        Name        json
        Format      json
        Time_Key    timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%L

    [PARSER]
        Name        k8s-nginx-ingress
        Format      regex
        Regex       ^(?<host>[^ ]*) - (?<user>[^ ]*) \[(?<time>[^\]]*)\] "(?<method>\S+)(?: +(?<path>[^\"]*?)(?: +\S*)?)?" (?<code>[^ ]*) (?<size>[^ ]*) "(?<referer>[^\"]*)" "(?<agent>[^\"]*)" (?<request_length>[^ ]*) (?<request_time>[^ ]*) \[(?<proxy_upstream_name>[^ ]*)\] (\[(?<proxy_alternative_upstream_name>[^ ]*)\] )?(?<upstream_addr>[^ ]*) (?<upstream_response_length>[^ ]*) (?<upstream_response_time>[^ ]*) (?<upstream_status>[^ ]*) (?<reg_id>[^ ]*)
        Time_Key    time
        Time_Format %d/%b/%Y:%H:%M:%S %z

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-dashboards
  namespace: logging
  labels:
    app: fluent-bit
    grafana_dashboard: "1"
data:
  fluent-bit-dashboard.json: |
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": 20,
      "links": [],
      "panels": [
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 2,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "percentage": false,
          "pluginVersion": "7.1.5",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "fluentbit_input_bytes_total",
              "interval": "",
              "legendFormat": "{{name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Input Bytes",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "bytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 0
          },
          "hiddenSeries": false,
          "id": 4,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "percentage": false,
          "pluginVersion": "7.1.5",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "fluentbit_output_proc_bytes_total",
              "interval": "",
              "legendFormat": "{{name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Output Bytes",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "bytes",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 8
          },
          "hiddenSeries": false,
          "id": 6,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "percentage": false,
          "pluginVersion": "7.1.5",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "fluentbit_output_errors_total",
              "interval": "",
              "legendFormat": "{{name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Output Errors",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        },
        {
          "aliasColors": {},
          "bars": false,
          "dashLength": 10,
          "dashes": false,
          "datasource": "Prometheus",
          "fieldConfig": {
            "defaults": {
              "custom": {}
            },
            "overrides": []
          },
          "fill": 1,
          "fillGradient": 0,
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 8
          },
          "hiddenSeries": false,
          "id": 8,
          "legend": {
            "avg": false,
            "current": false,
            "max": false,
            "min": false,
            "show": true,
            "total": false,
            "values": false
          },
          "lines": true,
          "linewidth": 1,
          "nullPointMode": "null",
          "percentage": false,
          "pluginVersion": "7.1.5",
          "pointradius": 2,
          "points": false,
          "renderer": "flot",
          "seriesOverrides": [],
          "spaceLength": 10,
          "stack": false,
          "steppedLine": false,
          "targets": [
            {
              "expr": "fluentbit_output_retries_total",
              "interval": "",
              "legendFormat": "{{name}}",
              "refId": "A"
            }
          ],
          "thresholds": [],
          "timeFrom": null,
          "timeRegions": [],
          "timeShift": null,
          "title": "Output Retries",
          "tooltip": {
            "shared": true,
            "sort": 0,
            "value_type": "individual"
          },
          "type": "graph",
          "xaxis": {
            "buckets": null,
            "mode": "time",
            "name": null,
            "show": true,
            "values": []
          },
          "yaxes": [
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            },
            {
              "format": "short",
              "label": null,
              "logBase": 1,
              "max": null,
              "min": null,
              "show": true
            }
          ],
          "yaxis": {
            "align": false,
            "alignLevel": null
          }
        }
      ],
      "refresh": "10s",
      "schemaVersion": 26,
      "style": "dark",
      "tags": [],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "Fluent Bit",
      "uid": "fluent-bit",
      "version": 1
    }
