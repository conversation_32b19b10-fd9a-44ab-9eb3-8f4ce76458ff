/**
 * Advanced Health Check System for RB2
 * 
 * This module provides sophisticated health check endpoints that go beyond simple
 * ping/pong checks to provide detailed system health information, including:
 * - Component-level health checks
 * - Dependency status
 * - System resource metrics
 * - Self-healing triggers
 * - Circuit breaker status
 */

const express = require('express');
const router = express.Router();
const os = require('os');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);
const mongoose = require('mongoose');
const Redis = require('ioredis');
const amqp = require('amqplib');
const axios = require('axios');
const { CircuitBreaker } = require('../resilience/circuit-breaker');

// Configuration with fallbacks
const config = {
  enableDetailedChecks: process.env.ENABLE_DETAILED_HEALTH_CHECKS === 'true' || false,
  dependencies: {
    database: {
      enabled: true,
      critical: true,
      timeout: parseInt(process.env.DB_HEALTH_TIMEOUT || '5000'),
      connection: process.env.MONGODB_URI || 'mongodb://localhost:27017/rb2',
    },
    redis: {
      enabled: true,
      critical: true,
      timeout: parseInt(process.env.REDIS_HEALTH_TIMEOUT || '2000'),
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
    },
    rabbitmq: {
      enabled: true,
      critical: true,
      timeout: parseInt(process.env.RABBITMQ_HEALTH_TIMEOUT || '5000'),
      url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
    },
    externalApi: {
      enabled: true,
      critical: false,
      timeout: parseInt(process.env.API_HEALTH_TIMEOUT || '5000'),
      url: process.env.EXTERNAL_API_URL,
    }
  },
  thresholds: {
    cpu: parseFloat(process.env.HEALTH_THRESHOLD_CPU || '90.0'),
    memory: parseFloat(process.env.HEALTH_THRESHOLD_MEMORY || '90.0'),
    diskSpace: parseFloat(process.env.HEALTH_THRESHOLD_DISK || '90.0'),
  },
  responseTimeout: parseInt(process.env.HEALTH_RESPONSE_TIMEOUT || '30000'),
};

// Initialize circuit breakers for dependencies
const circuitBreakers = {
  database: new CircuitBreaker('database', { 
    failureThreshold: 3,
    resetTimeout: 30000
  }),
  redis: new CircuitBreaker('redis', { 
    failureThreshold: 3,
    resetTimeout: 10000
  }),
  rabbitmq: new CircuitBreaker('rabbitmq', { 
    failureThreshold: 3,
    resetTimeout: 30000
  }),
  externalApi: new CircuitBreaker('externalApi', { 
    failureThreshold: 5,
    resetTimeout: 60000
  })
};

// Health check cache to prevent hammering dependencies
let healthCache = {
  data: null,
  timestamp: 0
};
const CACHE_TTL = 30000; // 30 seconds

/**
 * Check system resources
 */
async function checkSystemResources() {
  // CPU load average
  const loadAvg = os.loadavg()[0]; // 1 minute load average
  const cpuCount = os.cpus().length;
  const cpuUsage = (loadAvg / cpuCount) * 100;
  
  // Memory usage
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const memoryUsage = ((totalMem - freeMem) / totalMem) * 100;
  
  // Disk space (simplified, would need fs.statfs or similar in real implementation)
  let diskUsage = 0;
  try {
    const { stdout } = await exec('df -k / | tail -1 | awk \'{print $5}\'');
    diskUsage = parseFloat(stdout.replace('%', ''));
  } catch (error) {
    console.error('Error checking disk space:', error);
    diskUsage = -1;
  }
  
  return {
    cpu: {
      usage: cpuUsage.toFixed(2),
      healthy: cpuUsage < config.thresholds.cpu,
      threshold: config.thresholds.cpu,
      cores: cpuCount,
      load1m: loadAvg
    },
    memory: {
      usage: memoryUsage.toFixed(2),
      healthy: memoryUsage < config.thresholds.memory,
      threshold: config.thresholds.memory,
      totalMb: (totalMem / 1024 / 1024).toFixed(0),
      freeMb: (freeMem / 1024 / 1024).toFixed(0)
    },
    disk: {
      usage: diskUsage.toFixed(2),
      healthy: diskUsage < config.thresholds.diskSpace || diskUsage === -1,
      threshold: config.thresholds.diskSpace
    },
    uptime: os.uptime(),
    hostname: os.hostname()
  };
}

/**
 * Check database connection
 */
async function checkDatabase() {
  if (!config.dependencies.database.enabled) {
    return { status: 'skipped', message: 'Database check disabled in configuration' };
  }
  
  return circuitBreakers.database.execute(async () => {
    try {
      // Check connection state if already connected
      if (mongoose.connection.readyState) {
        // 1 = connected
        if (mongoose.connection.readyState === 1) {
          // Perform a simple find operation to verify real connection status
          await mongoose.connection.db.admin().ping();
          return { status: 'healthy', message: 'Database connection is established and responsive' };
        } else if (mongoose.connection.readyState === 2) {
          return { status: 'degraded', message: 'Database connection is currently connecting' };
        } else {
          return { status: 'unhealthy', message: `Database connection is in state: ${mongoose.connection.readyState}` };
        }
      }
      
      // If not connected, try to connect
      await mongoose.connect(config.dependencies.database.connection, {
        serverSelectionTimeoutMS: config.dependencies.database.timeout,
        connectTimeoutMS: config.dependencies.database.timeout
      });
      
      await mongoose.connection.db.admin().ping();
      return { status: 'healthy', message: 'Database connection successful' };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        message: `Database connection failed: ${error.message}`,
        error: error.message 
      };
    }
  }, () => {
    return { 
      status: 'circuit-open', 
      message: 'Database circuit breaker is open, not attempting connection' 
    };
  });
}

/**
 * Check Redis connection
 */
async function checkRedis() {
  if (!config.dependencies.redis.enabled) {
    return { status: 'skipped', message: 'Redis check disabled in configuration' };
  }
  
  return circuitBreakers.redis.execute(async () => {
    let client;
    try {
      client = new Redis({
        host: config.dependencies.redis.host,
        port: config.dependencies.redis.port,
        connectTimeout: config.dependencies.redis.timeout,
        maxRetriesPerRequest: 1
      });
      
      // Set a health check key and read it back
      const testValue = `health-check-${Date.now()}`;
      await client.set('health:test', testValue, 'EX', 60);
      const result = await client.get('health:test');
      
      if (result === testValue) {
        return { status: 'healthy', message: 'Redis connection successful' };
      } else {
        return { status: 'degraded', message: 'Redis read/write test failed' };
      }
    } catch (error) {
      return { 
        status: 'unhealthy', 
        message: `Redis connection failed: ${error.message}`,
        error: error.message 
      };
    } finally {
      if (client) {
        client.disconnect();
      }
    }
  }, () => {
    return { 
      status: 'circuit-open', 
      message: 'Redis circuit breaker is open, not attempting connection' 
    };
  });
}

/**
 * Check RabbitMQ connection
 */
async function checkRabbitMQ() {
  if (!config.dependencies.rabbitmq.enabled) {
    return { status: 'skipped', message: 'RabbitMQ check disabled in configuration' };
  }
  
  return circuitBreakers.rabbitmq.execute(async () => {
    let connection;
    let channel;
    
    try {
      connection = await amqp.connect(config.dependencies.rabbitmq.url, { 
        timeout: config.dependencies.rabbitmq.timeout 
      });
      channel = await connection.createChannel();
      
      // Check if we can create and delete a temporary queue
      const queueName = `health-check-${Date.now()}`;
      await channel.assertQueue(queueName, { durable: false, autoDelete: true });
      await channel.deleteQueue(queueName);
      
      return { status: 'healthy', message: 'RabbitMQ connection successful' };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        message: `RabbitMQ connection failed: ${error.message}`,
        error: error.message 
      };
    } finally {
      if (channel) await channel.close().catch(() => {});
      if (connection) await connection.close().catch(() => {});
    }
  }, () => {
    return { 
      status: 'circuit-open', 
      message: 'RabbitMQ circuit breaker is open, not attempting connection' 
    };
  });
}

/**
 * Check external API connection
 */
async function checkExternalAPI() {
  if (!config.dependencies.externalApi.enabled || !config.dependencies.externalApi.url) {
    return { status: 'skipped', message: 'External API check disabled or URL not configured' };
  }
  
  return circuitBreakers.externalApi.execute(async () => {
    try {
      const response = await axios.get(config.dependencies.externalApi.url, {
        timeout: config.dependencies.externalApi.timeout,
        validateStatus: status => status === 200
      });
      
      return { 
        status: 'healthy', 
        message: 'External API connection successful',
        responseTime: response.headers['x-response-time'] || 'unknown'
      };
    } catch (error) {
      let message = 'External API connection failed';
      if (error.response) {
        message += `: HTTP ${error.response.status}`;
      } else if (error.request) {
        message += ': No response received';
      } else {
        message += `: ${error.message}`;
      }
      
      return { 
        status: 'unhealthy', 
        message,
        error: error.message
      };
    }
  }, () => {
    return { 
      status: 'circuit-open', 
      message: 'External API circuit breaker is open, not attempting connection' 
    };
  });
}

/**
 * Perform application-specific checks
 */
async function checkApplication() {
  // These would be specific to your application logic
  // Examples include checking for required config, feature flags, etc.
  try {
    // Check if required configuration is present
    const requiredConfig = [
      'NODE_ENV',
      'PORT',
      'API_VERSION'
    ];
    
    const missingConfig = requiredConfig.filter(key => !process.env[key]);
    
    if (missingConfig.length > 0) {
      return {
        status: 'degraded',
        message: `Missing required configuration: ${missingConfig.join(', ')}`
      };
    }
    
    // Check business-critical processes
    // This is a placeholder for application-specific checks
    const businessChecks = {
      paymentProcessing: true,
      notificationService: true,
      scheduledJobs: true
    };
    
    if (!Object.values(businessChecks).every(check => check)) {
      const failedChecks = Object.entries(businessChecks)
        .filter(([_, value]) => !value)
        .map(([key]) => key);
      
      return {
        status: 'degraded',
        message: `Business-critical processes not functioning: ${failedChecks.join(', ')}`
      };
    }
    
    return {
      status: 'healthy',
      message: 'Application checks passed'
    };
  } catch (error) {
    return {
      status: 'degraded',
      message: `Application check error: ${error.message}`,
      error: error.message
    };
  }
}

/**
 * Check cluster status (if in Kubernetes)
 */
async function checkCluster() {
  // This would be more comprehensive in a real implementation
  // Here we're just checking if we're in a Kubernetes environment
  const inKubernetes = process.env.KUBERNETES_SERVICE_HOST !== undefined;
  
  if (!inKubernetes) {
    return { status: 'skipped', message: 'Not running in Kubernetes' };
  }
  
  try {
    // Simple check to see if we can access the Kubernetes API server
    // In a real implementation, you would use the Kubernetes client library
    let nodeStatus = 'unknown';
    try {
      const { stdout } = await exec('curl -s -m 1 https://kubernetes.default.svc/healthz', {
        env: { ...process.env, 'CURL_CA_BUNDLE': '/var/run/secrets/kubernetes.io/serviceaccount/ca.crt' }
      });
      nodeStatus = stdout.trim() === 'ok' ? 'healthy' : 'degraded';
    } catch (error) {
      nodeStatus = 'unhealthy';
    }
    
    // Get pod information
    const namespace = process.env.NAMESPACE || 'default';
    const podName = process.env.HOSTNAME || 'unknown';
    
    return {
      status: nodeStatus,
      message: nodeStatus === 'healthy' ? 'Kubernetes cluster is healthy' : 'Kubernetes cluster health check failed',
      metadata: {
        inKubernetes: true,
        namespace,
        podName,
        nodeName: process.env.NODE_NAME || 'unknown'
      }
    };
  } catch (error) {
    return {
      status: 'degraded',
      message: `Cluster check error: ${error.message}`,
      error: error.message
    };
  }
}

/**
 * Aggregate all health checks
 */
async function performHealthChecks(detailed = false) {
  // Check cache first
  const currentTime = Date.now();
  if (healthCache.data && currentTime - healthCache.timestamp < CACHE_TTL) {
    if (!detailed || (detailed && healthCache.data.detailed)) {
      return healthCache.data;
    }
  }
  
  const startTime = process.hrtime();
  
  // Run checks in parallel
  const [
    systemResources,
    databaseCheck,
    redisCheck,
    rabbitmqCheck,
    externalApiCheck,
    applicationCheck,
    clusterCheck
  ] = await Promise.all([
    checkSystemResources(),
    checkDatabase(),
    checkRedis(),
    checkRabbitMQ(),
    checkExternalAPI(),
    checkApplication(),
    checkCluster()
  ]);
  
  const endTime = process.hrtime(startTime);
  const responseTimeMs = Math.round((endTime[0] * 1000) + (endTime[1] / 1000000));
  
  // Determine overall status based on critical dependencies
  let overallStatus = 'healthy';
  const dependencies = {
    database: databaseCheck,
    redis: redisCheck,
    rabbitmq: rabbitmqCheck,
    externalApi: externalApiCheck
  };
  
  // Check if any critical dependencies are unhealthy
  for (const [name, check] of Object.entries(dependencies)) {
    const isCritical = config.dependencies[name]?.critical;
    if (isCritical && check.status === 'unhealthy') {
      overallStatus = 'unhealthy';
      break;
    } else if (check.status === 'degraded' && overallStatus === 'healthy') {
      overallStatus = 'degraded';
    }
  }
  
  // Application check can degrade the overall status
  if (applicationCheck.status === 'degraded' && overallStatus === 'healthy') {
    overallStatus = 'degraded';
  } else if (applicationCheck.status === 'unhealthy') {
    overallStatus = 'unhealthy';
  }
  
  // System resources can degrade the overall status
  const systemHealthy = (
    systemResources.cpu.healthy &&
    systemResources.memory.healthy &&
    systemResources.disk.healthy
  );
  
  if (!systemHealthy && overallStatus === 'healthy') {
    overallStatus = 'degraded';
  }
  
  // Build response
  const response = {
    status: overallStatus,
    version: process.env.npm_package_version || process.env.API_VERSION || 'unknown',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    responseTimeMs,
    detailed: detailed
  };
  
  // Add detailed information if requested
  if (detailed) {
    response.services = {
      database: databaseCheck,
      redis: redisCheck,
      rabbitmq: rabbitmqCheck,
      externalApi: externalApiCheck
    };
    
    response.application = applicationCheck;
    response.cluster = clusterCheck;
    response.system = systemResources;
    
    response.circuit_breakers = {
      database: circuitBreakers.database.getState(),
      redis: circuitBreakers.redis.getState(),
      rabbitmq: circuitBreakers.rabbitmq.getState(),
      externalApi: circuitBreakers.externalApi.getState()
    };
  }
  
  // Update cache
  healthCache = {
    data: response,
    timestamp: currentTime
  };
  
  return response;
}

// Basic health endpoint (Kubernetes liveness probe)
router.get('/alive', async (req, res) => {
  // This endpoint should be fast and only check if the node.js process is running
  // It should almost never return an error unless the process is about to crash
  return res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// Readiness probe (Kubernetes readiness probe)
router.get('/ready', async (req, res) => {
  try {
    // Simpler check just for critical dependencies
    // This determines if the service is ready to serve traffic
    const [databaseCheck, redisCheck, rabbitmqCheck] = await Promise.all([
      config.dependencies.database.critical ? checkDatabase() : { status: 'skipped' },
      config.dependencies.redis.critical ? checkRedis() : { status: 'skipped' },
      config.dependencies.rabbitmq.critical ? checkRabbitMQ() : { status: 'skipped' }
    ]);
    
    // Check if any critical dependencies are unhealthy
    const criticalDependencies = [databaseCheck, redisCheck, rabbitmqCheck];
    const isReady = criticalDependencies.every(dep => 
      dep.status === 'healthy' || dep.status === 'skipped' || dep.status === 'circuit-open'
    );
    
    if (isReady) {
      return res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      return res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        message: 'Critical dependencies not available'
      });
    }
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error.message
    });
  }
});

// Main health check endpoint
router.get('/health', async (req, res) => {
  try {
    // Set a response timeout to prevent hanging requests
    const timeoutId = setTimeout(() => {
      res.status(503).json({
        status: 'timeout',
        message: `Health check timed out after ${config.responseTimeout}ms`,
        timestamp: new Date().toISOString()
      });
    }, config.responseTimeout);
    
    // Determine if detailed mode is requested
    const detailed = req.query.detailed === 'true' || config.enableDetailedChecks;
    
    // Perform health checks
    const healthCheck = await performHealthChecks(detailed);
    
    // Clear the timeout as we have a response
    clearTimeout(timeoutId);
    
    // Set appropriate status code
    let statusCode = 200;
    if (healthCheck.status === 'degraded') {
      statusCode = 200; // Still operational, but with issues
    } else if (healthCheck.status === 'unhealthy') {
      statusCode = 503; // Service Unavailable
    }
    
    return res.status(statusCode).json(healthCheck);
  } catch (error) {
    console.error('Health check error:', error);
    return res.status(500).json({
      status: 'error',
      message: `Health check failed: ${error.message}`,
      timestamp: new Date().toISOString()
    });
  }
});

// Reset all circuit breakers (for recovery and testing)
router.post('/health/reset-circuit-breakers', (req, res) => {
  try {
    for (const [name, breaker] of Object.entries(circuitBreakers)) {
      breaker.forceState('CLOSED');
    }
    
    return res.status(200).json({
      status: 'success',
      message: 'All circuit breakers have been reset',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      message: `Failed to reset circuit breakers: ${error.message}`,
      timestamp: new Date().toISOString()
    });
  }
});

// Clear health check cache
router.post('/health/clear-cache', (req, res) => {
  healthCache = {
    data: null,
    timestamp: 0
  };
  
  return res.status(200).json({
    status: 'success',
    message: 'Health check cache cleared',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
