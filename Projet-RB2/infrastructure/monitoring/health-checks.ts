import axios, { AxiosRequestConfig } from 'axios'
import * as fs from 'fs'
import * as os from 'os'

// Health status enum
export enum HealthStatus {
  UP = 'UP',
  DOWN = 'DOWN',
  DEGRADED = 'DEGRADED',
  UNKNOWN = 'UNKNOWN',
}

// Health check types
export enum HealthCheckType {
  PING = 'ping',
  HTTP = 'http',
  DATABASE = 'database',
  REDIS = 'redis',
  DISK = 'disk',
  MEMORY = 'memory',
  CPU = 'cpu',
  CUSTOM = 'custom',
}

// Health check result
export interface HealthCheckResult {
  status: HealthStatus
  name: string;
  type: HealthCheckType;
  message?: string;
  details?: Record<string, any>;
  timestamp: Date;
  duration: number; // in milliseconds
  dependencies?: HealthCheckResult[];
}

// Health check options
export interface HealthCheckOptions {
  name: string
  type: HealthCheckType;
  timeout?: number; // in milliseconds
  interval?: number; // in milliseconds, for scheduled checks
  failureThreshold?: number;
  successThreshold?: number;
  cacheTime?: number; // in milliseconds
  enabled?: boolean;
  
  // Type-specific options
  httpOptions?: {
    url: string;
    method?: string;
    headers?: Record<string, string>;
    data?: any;
    expectedStatus?: number | number[];
    validateResponse?: (data: any) => boolean;
  }
  
  databaseOptions?: {
    query: string;
    connectionString?: string;
    timeout?: number;
  }
  
  redisOptions?: {
    host: string;
    port: number;
    key?: string;
    auth?: { password: string }
  }
  
  diskOptions?: {
    path: string;
    thresholdPercent: number;
  }
  
  memoryOptions?: {
    thresholdPercent: number;
  }
  
  cpuOptions?: {
    thresholdPercent: number;
    sampleTime?: number; // in milliseconds
  }
  
  customOptions?: Record<string, any>;
  customCheck?: () => Promise<HealthCheckResult>;
}

export class HealthCheckService {
  private checks: Map<string, HealthCheckOptions> = new Map();
  private results: Map<string, HealthCheckResult> = new Map();
  private scheduledChecks: Map<string, NodeJS.Timeout> = new Map();
  private failureCounts: Map<string, number> = new Map();
  private successCounts: Map<string, number> = new Map();

  constructor() {
    // Register basic system checks by default
    this.registerSystemChecks();
  }
  
  /**
   * Register a new health check
   */
  public registerCheck(options: HealthCheckOptions): void  {
    // Validate options
    if(!options.name) {
      throw new Error('Health check name is required');
    }
    
    if (this.checks.has(options.name)) {
      throw new Error(`Health check with name '${options.name}' already exists`);
    }
    
    // Set default values
    options.timeout = options.timeout || 5000;
    options.interval = options.interval || 60000;
    options.failureThreshold = options.failureThreshold || 3;
    options.successThreshold = options.successThreshold || 1;
    options.enabled = options.enabled !== false;
    options.cacheTime = options.cacheTime || 0;
    
    // Validate type-specific options
    switch(options.type) {
      case HealthCheckType.HTTP:
        if(!options.httpOptions?.url) {
          throw new Error('URL is required for HTTP health checks');
        }
        options.httpOptions.method = options.httpOptions.method || 'GET';
        options.httpOptions.expectedStatus = options.httpOptions.expectedStatus || 200;
        break;
        
      case HealthCheckType.DATABASE:
        if(!options.databaseOptions?.query) {
          throw new Error('Query is required for database health checks');
        }
        break;
        
      case HealthCheckType.REDIS:
        if(!options.redisOptions?.host || !options.redisOptions?.port) {
          throw new Error('Host and port are required for Redis health checks');
        }
        break;
        
      case HealthCheckType.DISK:
        if(!options.diskOptions?.path || options.diskOptions?.thresholdPercent === undefined) {
          throw new Error('Path and threshold are required for disk health checks');
        }
        break;
        
      case HealthCheckType.CUSTOM:
        if(!options.customCheck) {
          throw new Error('Custom check function is required for custom health checks');
        }
        break;
    }
    
    // Store the check
    this.checks.set(options.name, options);
    
    // Initialize counters
    this.failureCounts.set(options.name, 0);
    this.successCounts.set(options.name, 0);
    
    // Schedule the check if interval is provided
    if(options.interval && options.interval > 0) {
      this.scheduleCheck(options.name);
    }
  }
  
  /**
   * Register built-in system health checks
   */
  private registerSystemChecks(): void  {
    // Memory check
    this.registerCheck({
      name: 'system.memory',
      type: HealthCheckType.MEMORY,
      interval: 30000,
      memoryOptions: {
        thresholdPercent: 90
      }
    });
    
    // CPU check
    this.registerCheck({
      name: 'system.cpu',
      type: HealthCheckType.CPU,
      interval: 30000,
      cpuOptions: {
        thresholdPercent: 90,
        sampleTime: 1000
      }
    });
    
    // Disk check for root partition
    this.registerCheck({
      name: 'system.disk.root',
      type: HealthCheckType.DISK,
      interval: 300000, // Every 5 minutes
      diskOptions: {
        path: '/',
        thresholdPercent: 90
      }
    });
  }
  
  /**
   * Schedule a health check to run periodically
   */
  private scheduleCheck(name: string): void  {
    // Clear existing schedule if any
    if (this.scheduledChecks.has(name)) {
      clearInterval(this.scheduledChecks.get(name)!);
    }
    
    const check = this.checks.get(name);
    if (!check || !check.enabled) {
      return;
    }
    
    // Schedule the check
    const intervalId = setInterval(async () => {
      try {
        await this.runCheck(name);
      } catch (error) {
        console.error(`Error running scheduled health check '${name}':`, error);
      }
    }, check.interval);
    
    // Store the interval ID for cleanup
    this.scheduledChecks.set(name, intervalId);
  }
  
  /**
   * Unregister a health check
   */
  public unregisterCheck(name: string): boolean  {
    if (!this.checks.has(name)) {
      return false;
    }
    
    // Clear scheduled check if any
    if (this.scheduledChecks.has(name)) {
      clearInterval(this.scheduledChecks.get(name)!);
      this.scheduledChecks.delete(name);
    }
    
    // Remove check and results
    this.checks.delete(name);
    this.results.delete(name);
    this.failureCounts.delete(name);
    this.successCounts.delete(name);
    
    return true;
  }
  
  /**
   * Run a specific health check
   */
  public async runCheck(name: string): Promise<HealthCheckResult> {
    const check = this.checks.get(name);
    if (!check) {
      throw new Error(`Health check '${name}' not found`);
    }
    
    if(!check.enabled) {
      return {
        status: HealthStatus.UNKNOWN,
        name: check.name,
        type: check.type,
        message: 'Health check is disabled',
        timestamp: new Date(),
        duration: 0
      }
    }
    
    // Check cache
    const cachedResult = this.results.get(name);
    if (cachedResult && check.cacheTime && check.cacheTime > 0) {
      const age = Date.now() - cachedResult.timestamp.getTime();
      if (age < check.cacheTime) {
        return cachedResult;
      }
    }
    
    // Execute the check
    const startTime = Date.now();
    let result: HealthCheckResult;
    
    try {
      result = await this.executeCheck(check);
      result.duration = Date.now() - startTime;
      
      // Update success/failure counters
      if (result.status === HealthStatus.UP) {
        this.successCounts.set(name, (this.successCounts.get(name) || 0) + 1);
        this.failureCounts.set(name, 0);
      } else {
        this.failureCounts.set(name, (this.failureCounts.get(name) || 0) + 1);
        this.successCounts.set(name, 0);
      }
      
      // Check for state transitions based on thresholds
      const failures = this.failureCounts.get(name) || 0;
      const successes = this.successCounts.get(name) || 0;
      
      if (result.status !== HealthStatus.UP && failures >= check.failureThreshold!) {
        result.status = HealthStatus.DOWN;
        result.message = `${result.message || ''} (Failed ${failures} times in a row)`;
      } else if(result.status === HealthStatus.UP && successes < check.successThreshold!) {
        result.status = HealthStatus.DEGRADED;
        result.message = `Recovering (Success ${successes}/${check.successThreshold})`;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      result = {
        status: HealthStatus.DOWN,
        name: check.name,
        type: check.type,
        message: `Check failed with error: ${errorMessage}`,
        timestamp: new Date(),
        duration: Date.now() - startTime
      }
      
      // Update failure counter
      this.failureCounts.set(name, (this.failureCounts.get(name) || 0) + 1);
      this.successCounts.set(name, 0);
    }
    
    // Store result
    this.results.set(name, result);
    
    return result;
  }
  
  /**
   * Execute a health check based on its type
   */
  private async executeCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    switch (check.type) {
      case HealthCheckType.PING:
        return this.executePingCheck(check);
        
      case HealthCheckType.HTTP:
        return this.executeHttpCheck(check);
        
      case HealthCheckType.DATABASE:
        return this.executeDatabaseCheck(check);
        
      case HealthCheckType.REDIS:
        return this.executeRedisCheck(check);
        
      case HealthCheckType.DISK:
        return this.executeDiskCheck(check);
        
      case HealthCheckType.MEMORY:
        return this.executeMemoryCheck(check);
        
      case HealthCheckType.CPU:
        return this.executeCpuCheck(check);
        
      case HealthCheckType.CUSTOM:
        return check.customCheck!();
        
      default:
        return {
          status: HealthStatus.UNKNOWN,
          name: check.name,
          type: check.type,
          message: `Unsupported health check type: ${check.type}`,
          timestamp: new Date(),
          duration: 0
        }
    }
  }
  
  /**
   * Execute a ping health check
   */
  private async executePingCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    return {
      status: HealthStatus.UP,
      name: check.name,
      type: check.type,
      message: 'Ping successful',
      timestamp: new Date(),
      duration: 0
    }
  }
  
  /**
   * Execute an HTTP health check
   */
  private async executeHttpCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    const options = check.httpOptions!;
    
    try {
      const config: AxiosRequestConfig = {
        method: options.method,
        url: options.url,
        headers: options.headers,
        data: options.data,
        timeout: check.timeout,
        validateStatus: null // Don't throw on any status code
      }
      
      const startTime = Date.now();
      const response = await axios(config);
      const duration = Date.now() - startTime;
      
      // Check status code
      const expectedStatus = Array.isArray(options.expectedStatus) ? options.expectedStatus : [options.expectedStatus as number];
      
      const statusValid = expectedStatus.includes(response.status);
      
      // Validate response if needed
      let responseValid = true;
      if(options.validateResponse && response.data) {
        responseValid = options.validateResponse(response.data);
      }
      
      const status = statusValid && responseValid ? HealthStatus.UP : HealthStatus.DOWN;
      
      return {
        status,
        name: check.name,
        type: check.type,
        message: statusValid && responseValid ? `HTTP check successful (${response.status})` : `HTTP check failed: status = ${response.status}, expected = ${expectedStatus.join(' or ')}, ${!responseValid ? 'response validation failed' : ''}`,
        details: {
          statusCode: response.status,
          responseTime: duration,
          url: options.url
        },
        timestamp: new Date(),
        duration
      }
    } catch (error) {
      return {
        status: HealthStatus.DOWN,
        name: check.name,
        type: check.type,
        message: `HTTP check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          url: options.url,
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: new Date(),
        duration: 0
      }
    }
  }
  
  /**
   * Execute a database health check
   * Note: This is a simplified version. In a real implementation,
   * you would use a database driver to execute the query.
   */
  private async executeDatabaseCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    const options = check.databaseOptions!;
    
    // This is a mock implementation
    return {
      status: HealthStatus.UP,
      name: check.name,
      type: check.type,
      message: 'Database check successful',
      details: {
        query: options.query,
        responseTime: 5,
        connectionString: options.connectionString?.replace(/password=([^;]*)/, 'password=****')
      },
      timestamp: new Date(),
      duration: 5
    }
  }
  
  /**
   * Execute a Redis health check
   * Note: This is a simplified version. In a real implementation,
   * you would use a Redis client to perform the check.
   */
  private async executeRedisCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    const options = check.redisOptions!;
    
    // This is a mock implementation
    return {
      status: HealthStatus.UP,
      name: check.name,
      type: check.type,
      message: 'Redis check successful',
      details: {
        host: options.host,
        port: options.port,
        responseTime: 2
      },
      timestamp: new Date(),
      duration: 2
    }
  }
  
  /**
   * Execute a disk health check
   */
  private async executeDiskCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    const options = check.diskOptions!;
    
    try {
      // Check if path exists
      if (!fs.existsSync(options.path)) {
        return {
          status: HealthStatus.DOWN,
          name: check.name,
          type: check.type,
          message: `Path does not exist: ${options.path}`,
          timestamp: new Date(),
          duration: 0
        }
      }
      
      // Get disk usage (this is a simplified implementation)
      const diskInfo = this.getDiskInfo(options.path);
      const usedPercent = (diskInfo.used / diskInfo.total) * 100;
      
      const status = usedPercent < options.thresholdPercent ? HealthStatus.UP : HealthStatus.DOWN;
      
      return {
        status,
        name: check.name,
        type: check.type,
        message: status === HealthStatus.UP ? `Disk check successful: ${usedPercent.toFixed(1)}% used` : `Disk usage above threshold: ${usedPercent.toFixed(1)}% used (threshold: ${options.thresholdPercent}%)`,
        details: {
          path: options.path,
          total: this.formatBytes(diskInfo.total),
          used: this.formatBytes(diskInfo.used),
          free: this.formatBytes(diskInfo.free),
          usedPercent: usedPercent.toFixed(1) + '%'
        },
        timestamp: new Date(),
        duration: 0
      }
    } catch (error) {
      return {
        status: HealthStatus.DOWN,
        name: check.name,
        type: check.type,
        message: `Disk check failed: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date(),
        duration: 0
      }
    }
  }
  
  /**
   * Get disk info for a path
   * Note: This is a simplified mockup, in a real implementation
   * you would use a library like `diskusage` or `node-disk-info`.
   */
  private getDiskInfo(path: string): { total: number; free: number; used: number } {
    // Mock implementation - in a real app, you'd use actual disk metrics
    const total = 1000000000000; // 1TB
    const free = 400000000000;  // 400GB
    const used = total - free;
    
    return { total, free, used }
  }
  
  /**
   * Format bytes to a human-readable format
   */
  private formatBytes(bytes: number): string  {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let value = bytes;
    let unitIndex = 0;
    
    while(value >= 1024 && unitIndex < units.length - 1) {
      value /= 1024;
      unitIndex++;
    }
    
    return `${value.toFixed(2)} ${units[unitIndex]}`;
  }
  
  /**
   * Execute a memory health check
   */
  private async executeMemoryCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    const options = check.memoryOptions!;
    
    try {
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      const usedPercent = (usedMemory / totalMemory) * 100;
      
      const status = usedPercent < options.thresholdPercent ? HealthStatus.UP : HealthStatus.DOWN;
      
      return {
        status,
        name: check.name,
        type: check.type,
        message: status === HealthStatus.UP ? `Memory check successful: ${usedPercent.toFixed(1)}% used` : `Memory usage above threshold: ${usedPercent.toFixed(1)}% used (threshold: ${options.thresholdPercent}%)`,
        details: {
          total: this.formatBytes(totalMemory),
          used: this.formatBytes(usedMemory),
          free: this.formatBytes(freeMemory),
          usedPercent: usedPercent.toFixed(1) + '%'
        },
        timestamp: new Date(),
        duration: 0
      }
    } catch (error) {
      return {
        status: HealthStatus.DOWN,
        name: check.name,
        type: check.type,
        message: `Memory check failed: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date(),
        duration: 0
      }
    }
  }
  
  /**
   * Execute a CPU health check
   */
  private async executeCpuCheck(check: HealthCheckOptions): Promise<HealthCheckResult> {
    const options = check.cpuOptions!;
    
    try {
      // This is a simplified implementation
      // In a real app, you would measure CPU over a period of time
      const cpuUsage = this.getCpuUsage();
      const status = cpuUsage < options.thresholdPercent ? HealthStatus.UP : HealthStatus.DOWN;
      
      return {
        status,
        name: check.name,
        type: check.type,
        message: status === HealthStatus.UP ? `CPU check successful: ${cpuUsage.toFixed(1)}% used` : `CPU usage above threshold: ${cpuUsage.toFixed(1)}% used (threshold: ${options.thresholdPercent}%)`,
        details: {
          usage: cpuUsage.toFixed(1) + '%',
          cores: os.cpus().length
        },
        timestamp: new Date(),
        duration: 0
      }
    } catch (error) {
      return {
        status: HealthStatus.DOWN,
        name: check.name,
        type: check.type,
        message: `CPU check failed: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: new Date(),
        duration: 0
      }
    }
  }
  
  /**
   * Get CPU usage percentage
   * Note: This is a simplified mockup, in a real implementation
   * you would use a more accurate method to measure CPU usage.
   */
  private getCpuUsage(): number  {
    // Mock implementation - in a real app, you'd use actual CPU measurements
    return Math.random() * 100;
  }
  
  /**
   * Run all health checks
   */
  public async runAllChecks(): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];
    
    for (const name of this.checks.keys()) {
      try {
        const result = await this.runCheck(name);
        results.push(result);
      } catch (error) {
        console.error(`Error running health check '${name}':`, error);
      }
    }
    
    return results;
  }
  
  /**
   * Get overall health status
   */
  public async getHealthStatus(): Promise<{
    status: HealthStatus;
    checks: HealthCheckResult[];
    timestamp: Date;
  }> {
    const checks = await this.runAllChecks();
    // Determine overall status
    let overallStatus = HealthStatus.UP;
    
    for(const check of checks) {
      if(check.status === HealthStatus.DOWN) {
        overallStatus = HealthStatus.DOWN;
        break;
      } else if(check.status === HealthStatus.DEGRADED && overallStatus !== HealthStatus.DOWN) {
        overallStatus = HealthStatus.DEGRADED;
      }
    }
    
    return {
      status: overallStatus,
      checks,
      timestamp: new Date()
    }
  }
  
  /**
   * Get a specific health check result
   */
  public getCheckResult(name: string): HealthCheckResult | undefined  {
    return this.results.get(name);
  }
  
  /**
   * Stop all scheduled health checks
   */
  public stopAllChecks(): void  {
    for (const intervalId of this.scheduledChecks.values()) {
      clearInterval(intervalId);
    }
    
    this.scheduledChecks.clear();
  }
}

// Example usage:
/*
const healthService = new HealthCheckService();

// Register additional checks
healthService.registerCheck({
  name: 'api.payments',
  type: HealthCheckType.HTTP,
  interval: 30000, // Check every 30 seconds
  httpOptions: {
    url: 'https://api.example.com/payments/health',
    method: 'GET',
    expectedStatus: 200
  }
});

healthService.registerCheck({
  name: 'database.main',
  type: HealthCheckType.DATABASE,
  interval: 60000, // Check every minute
  databaseOptions: {
    query: 'SELECT 1',
    connectionString: 'postgres://user:password@localhost:5432/db'
  }
});

// Run a specific check
healthService.runCheck('api.payments')
  .then(result => console.log('Payment API health:', result))
  .catch(error => console.error('Health check failed:', error));

// Get overall health
healthService.getHealthStatus()
  .then(status => console.log('Overall health:', status))
  .catch(error => console.error('Health check failed:', error));

// Expose a health endpoint in an Express app
app.get('/health', async (req, res) => {
  try {
    const health = await healthService.getHealthStatus();
    res.status(health.status === HealthStatus.UP ? 200 : 
               health.status === HealthStatus.DEGRADED ? 200 : 503)
       .json(health);
  } catch (error) {
    res.status(500).json({
      status: HealthStatus.DOWN,
      message: 'Error checking health',
      error: error.message
    });
  }
});

// For detailed check
app.get('/health/:check', async (req, res) => {
  try {
    const result = await healthService.runCheck(req.params.check);
    res.status(result.status === HealthStatus.UP ? 200 : 
               result.status === HealthStatus.DEGRADED ? 200 : 503)
       .json(result);
  } catch (error) {
    res.status(404).json({
      status: HealthStatus.UNKNOWN,
      message: `Health check '${req.params.check}' not found`
    });
  }
});
*/

export { HealthCheckService }
