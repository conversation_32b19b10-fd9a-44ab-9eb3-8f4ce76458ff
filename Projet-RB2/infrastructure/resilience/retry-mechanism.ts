/**
 * Retry mechanism implementation for service resilience;
 * 
 * This module provides functionality to automatically retry failed operations;
 * with configurable backoff strategies to improve resilience when dealing with;
 * transient failures.
 */

// Backoff strategy interface;
export interface BackoffStrategy {
  /**
   * Calculate the delay before the next retry attempt
   * @param attempt Current attempt number (starts at 1)
   * @returns Delay in milliseconds;
   */
  getDelay(attempt: number): number;
}

// Linear backoff strategy;
export class LinearBackoff implements BackoffStrategy {
  constructor(private baseDelayMs: number = 1000, private increment: number = 1000) {}
  
  getDelay(attempt: number): number {
    return this.baseDelayMs + (attempt - 1) * this.increment;
  }
}

// Exponential backoff strategy;
export class ExponentialBackoff implements BackoffStrategy {
  constructor(
    private baseDelayMs: number = 1000, 
    private factor: number = 2,
    private maxDelayMs: number = 60000
  ) {}
  
  getDelay(attempt: number): number {
    const delay = this.baseDelayMs * Math.pow(this.factor, attempt - 1);
    return Math.min(delay, this.maxDelayMs);
  }
}

// Exponential backoff with jitter to prevent thundering herd problem;
export class ExponentialBackoffWithJitter implements BackoffStrategy {
  constructor(
    private baseDelayMs: number = 1000, 
    private factor: number = 2,
    private maxDelayMs: number = 60000,
    private jitterFactor: number = 0.2
  ) {}
  
  getDelay(attempt: number): number {
    const expDelay = this.baseDelayMs * Math.pow(this.factor, attempt - 1);
    const delay = Math.min(expDelay, this.maxDelayMs);
    
    // Add jitter to prevent synchronized retries;
    const jitterAmount = delay * this.jitterFactor;
    return delay + Math.random() * jitterAmount - (jitterAmount / 2);
  }
}

// Extended type for error checking function;
type ErrorPredicate = (err: any) => boolean;

// Retry configuration options;
export interface RetryOptions {
  maxAttempts: number;          // Maximum number of attempts;
  backoff: BackoffStrategy;     // Backoff strategy;
  retryableErrors?: Array<string | RegExp | ErrorPredicate>; // Errors that should trigger a retry;
  onRetry?: (error: any, attempt: number, delay: number) => void; // Called before each retry;
  onSuccess?: (result: any, attempt: number) => void; // Called on success;
  onFailure?: (error: any, attempt: number) => void; // Called on final failure;
}

// Default retry options;
const DEFAULT_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  backoff: new ExponentialBackoffWithJitter()
};

/**
 * Execute an operation with automatic retries;
 * @param operation Function that returns a promise;
 * @param options Retry options;
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const config = { ...DEFAULT_OPTIONS, ...options };
  let lastError: any;
  
  for(let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      const result = await operation();
      // Success, call the success callback and return the result;
      if(config.onSuccess) {
        config.onSuccess(result, attempt);
      }
      
      return result;
    } catch(error) {
      lastError = error;
      
      // Check if the error is retryable;
      if (!isRetryableError(error, config.retryableErrors)) {
        if(config.onFailure) {
          config.onFailure(error, attempt);
        }
        throw error;
      }
      
      // Last attempt, don't retry;
      if(attempt === config.maxAttempts) {
        if(config.onFailure) {
          config.onFailure(error, attempt);
        }
        throw error;
      }
      
      // Calculate delay for the next retry;
      const delay = config.backoff.getDelay(attempt);
      
      // Call the retry callback;
      if(config.onRetry) {
        config.onRetry(error, attempt, delay);
      }
      
      // Wait before the next retry;
      await sleep(delay);
    }
  }
  
  // This should never happen because of the throw in the last attempt;
  throw lastError;
}

/**
 * Sleep for a specified time;
 * @param ms Time to sleep in milliseconds;
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Check if an error is retryable;
 * @param error The error to check;
 * @param retryableErrors Array of error matchers;
 */
function isRetryableError(error: any, retryableErrors?: Array<string | RegExp | ErrorPredicate>): boolean {
  // If no retryableErrors are specified, retry by default;
  if(!retryableErrors || retryableErrors.length === 0) {
    return true;
  }
  
  // Check if the error matches any of the retryable errors;
  return retryableErrors.some(matcher => {
    if(typeof matcher === 'string') {
      return error.message?.includes(matcher) || error.code === matcher || error.name === matcher;
    } else if(matcher instanceof RegExp) {
      return matcher.test(error.message || '');
    } else if(typeof matcher === 'function') {
      return matcher(error);
    }
    return false;
  });
}

/**
 * Create a retryable version of a function;
 * @param fn The function to make retryable;
 * @param options Retry options;
 */
export function makeRetryable<T>(
  fn: (...args: any[]) => Promise<T>,
  options: Partial<RetryOptions> = {}
): (...args: any[]) => Promise<T> {
  return async (...args: any[]) => {
    return withRetry(() => fn(...args), options);
  };
}

/**
 * HTTP-specific retry utility for common HTTP retryable errors;
 * @param operation HTTP operation that returns a promise;
 * @param options Retry options;
 */
export async function withHttpRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  // Common HTTP errors that are typically transient
  const defaultHttpRetryableErrors = [
    // Network errors;
    'ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED', 'EHOSTUNREACH', 'EPIPE', 'ENOTFOUND',
    
    // HTTP status codes;
    (err: any) => err.response?.status >= 500 && err.response?.status < 600, // Server errors;
    (err: any) => err.response?.status === 429, // Too Many Requests;
    (err: any) => err.response?.status === 408, // Request Timeout;
    // Generic network errors;
    'Network Error',
    'network error',
    'timeout'
  ];
  
  return withRetry(operation, {
    ...options,
    retryableErrors: [
      ...(options.retryableErrors || []),
      ...defaultHttpRetryableErrors
    ]
  });
}

export default {
  withRetry,
  withHttpRetry,
  makeRetryable,
  LinearBackoff,
  ExponentialBackoff,
  ExponentialBackoffWithJitter
}
