#!/bin/bash
set -e

# Variables
NAMESPACE="rb2-mobile"
RELEASE_NAME="mobile-service"
CHART_PATH="../charts/mobile-service"
ENV=$1

# Vérification de l'environnement
if [ -z "$ENV" ]; then
  echo "Usage: $0 <environment>"
  echo "Environments: dev, test, staging, production"
  exit 1
fi

# Vérification que l'environnement est valide
if [[ ! "$ENV" =~ ^(dev|test|staging|production)$ ]]; then
  echo "Invalid environment: $ENV"
  echo "Valid environments: dev, test, staging, production"
  exit 1
fi

# Création du namespace s'il n'existe pas
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# Chemin vers les valeurs spécifiques à l'environnement
VALUES_FILE="../environments/$ENV/mobile-values.yaml"

# Vérification que le fichier de valeurs existe
if [ ! -f "$VALUES_FILE" ]; then
  echo "Values file not found: $VALUES_FILE"
  exit 1
fi

# Déploiement avec Helm
echo "Deploying mobile-service to $ENV environment..."
helm upgrade --install $RELEASE_NAME $CHART_PATH \
  --namespace $NAMESPACE \
  -f $VALUES_FILE \
  --timeout 10m \
  --wait

# Vérification du déploiement
echo "Checking deployment status..."
kubectl rollout status deployment/$RELEASE_NAME -n $NAMESPACE

echo "Mobile service deployment to $ENV completed successfully!"
