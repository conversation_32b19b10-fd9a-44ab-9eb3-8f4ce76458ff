import http from 'k6/http';
import { sleep, check } from 'k6';
import { Counter, Rate, Trend } from 'k6/metrics';
import { randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { htmlReport } from "https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js";
const { faker } = require('@faker-js/faker');

// Custom metrics
const errorRate = new Rate('error_rate');
const httpReqs = new Counter('http_reqs');
const httpReqDuration = new Trend('http_req_duration');
const httpReqWaiting = new Trend('http_req_waiting');
const httpReqFailed = new Counter('http_req_failed');

// Environment variables with defaults
const BASE_URL = __ENV.BASE_URL || 'http://localhost:5000';
const API_KEY = __ENV.API_KEY || 'test-api-key';

// Test scenarios configuration
export let options = {
  // Common test options
  insecureSkipTLSVerify: true,
  noConnectionReuse: false,
  
  // Scenarios
  scenarios: {
    // Smoke test - verifies the system works under minimal load
    smoke: {
      executor: 'constant-vus',
      vus: 1,
      duration: '1m',
      env: { SCENARIO: 'smoke' },
      tags: { test_type: 'smoke' },
    },
    
    // Load test - tests the system under expected normal load
    load: {
      executor: 'ramping-vus',
      startVUs: 5,
      stages: [
        { duration: '2m', target: 10 },  // Ramp up
        { duration: '5m', target: 10 },  // Stay at normal load
        { duration: '2m', target: 0 },   // Ramp down
      ],
      env: { SCENARIO: 'load' },
      tags: { test_type: 'load' },
    },
    
    // Stress test - tests the system under high load to identify breaking points
    stress: {
      executor: 'ramping-vus',
      startVUs: 10,
      stages: [
        { duration: '3m', target: 50 },   // Ramp up gradually
        { duration: '5m', target: 50 },   // Stay at peak load
        { duration: '3m', target: 100 },  // Increase to higher load
        { duration: '5m', target: 100 },  // Stay at higher peak
        { duration: '3m', target: 0 },    // Ramp down
      ],
      env: { SCENARIO: 'stress' },
      tags: { test_type: 'stress' },
    },
    
    // Soak test - tests the system over a longer period to find issues that manifest over time
    soak: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30m',
      env: { SCENARIO: 'soak' },
      tags: { test_type: 'soak' },
    },
    
    // Spike test - tests how the system handles sudden large spikes in load
    spike: {
      executor: 'ramping-vus',
      startVUs: 5,
      stages: [
        { duration: '1m', target: 5 },     // Start with normal load
        { duration: '30s', target: 100 },  // Sudden spike
        { duration: '3m', target: 100 },   // Stay at spike
        { duration: '30s', target: 5 },    // Back to normal
        { duration: '3m', target: 5 },     // Stay at normal
        { duration: '30s', target: 0 },    // Ramp down
      ],
      env: { SCENARIO: 'spike' },
      tags: { test_type: 'spike' },
    },
    
    // Breakpoint test - finds the system's breaking point by gradually increasing load
    breakpoint: {
      executor: 'ramping-arrival-rate',
      startRate: 5,
      timeUnit: '1s',
      preAllocatedVUs: 20,
      maxVUs: 500,
      stages: [
        { duration: '5m', target: 10 },   // 10 RPS
        { duration: '5m', target: 20 },   // 20 RPS
        { duration: '5m', target: 30 },   // 30 RPS
        { duration: '5m', target: 40 },   // 40 RPS
        { duration: '5m', target: 50 },   // 50 RPS
        { duration: '5m', target: 60 },   // 60 RPS
        { duration: '5m', target: 0 },    // Ramp down
      ],
      env: { SCENARIO: 'breakpoint' },
      tags: { test_type: 'breakpoint' },
    },
  },
  
  // Thresholds - define performance objectives
  thresholds: {
    // Global thresholds
    http_req_duration: ['p(95)<500', 'p(99)<1000'],  // 95% of requests must complete below 500ms, 99% below 1s
    http_req_failed: ['rate<0.01'],                  // error rate must be less than 1%
    
    // Endpoint-specific thresholds
    'http_req_duration{endpoint:healthcheck}': ['p(99)<100'],  // Health checks must be very fast
    'http_req_duration{endpoint:login}': ['p(95)<1000'],       // Login can be slightly slower
    'http_req_duration{endpoint:search}': ['p(95)<800'],       // Search must be responsive
  },
};

// Test lifecycle hooks
export function setup() {
  console.log(`Running performance tests against ${BASE_URL}`);
  
  // Perform any test setup like authentication
  const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
    username: 'performance_test_user',
    password: 'test_password'
  }), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  check(loginRes, {
    'login successful': (r) => r.status === 200,
  });
  
  // Return data to be used in the test
  return {
    authToken: loginRes.json('token') || 'fallback-token',
    testData: generateTestData(),
  };
}

// Main test function
export default function(data) {
  const scenario = __ENV.SCENARIO || 'load';
  const authToken = data.authToken;
  const testData = data.testData;
  
  // Common headers for all requests
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json',
    'X-Test-Scenario': scenario,
  };
  
  // Group related requests into a transaction
  const responses = http.batch([
    // Health check
    {
      method: 'GET', 
      url: `${BASE_URL}/health`, 
      headers: headers,
      tags: { endpoint: 'healthcheck' },
    },
    
    // Public API endpoints
    {
      method: 'GET', 
      url: `${BASE_URL}/api/products`, 
      headers: headers,
      tags: { endpoint: 'products-list' },
    },
    
    // Search endpoint with random query
    {
      method: 'GET', 
      url: `${BASE_URL}/api/search?q=${testData.searchTerms[randomIntBetween(0, testData.searchTerms.length-1)]}`, 
      headers: headers,
      tags: { endpoint: 'search' },
    },
  ]);
  
  // Check responses
  check(responses[0], {
    'health check is ok': (r) => r.status === 200,
  });
  
  check(responses[1], {
    'products list is ok': (r) => r.status === 200,
    'products list has items': (r) => r.json('data').length > 0,
  });
  
  check(responses[2], {
    'search is ok': (r) => r.status === 200,
  });
  
  // Simulate user thinking time
  sleep(randomIntBetween(1, 5));
  
  // POST operations (these can change data)
  if (randomIntBetween(1, 10) <= 3) {  // Only perform write operations 30% of the time
    // Create a new test entity
    const createEntityRes = http.post(
      `${BASE_URL}/api/entities`, 
      JSON.stringify({
        name: `Test Entity ${Date.now()}`,
        description: 'Created during performance testing',
        properties: {
          testRun: true,
          scenario: scenario,
          timestamp: new Date().toISOString(),
        }
      }),
      { headers: headers, tags: { endpoint: 'create-entity' } }
    );
    
    check(createEntityRes, {
      'entity created successfully': (r) => r.status === 201 || r.status === 200,
    });
    
    // Track custom metrics
    httpReqDuration.add(createEntityRes.timings.duration, { endpoint: 'create-entity' });
    httpReqWaiting.add(createEntityRes.timings.waiting, { endpoint: 'create-entity' });
    httpReqs.add(1, { endpoint: 'create-entity' });
    errorRate.add(createEntityRes.status >= 400);
  }
  
  // Add more custom logic here based on the scenario
  if (scenario === 'stress' || scenario === 'spike') {
    // When under high load, perform more intensive operations
    for (let i = 0; i < 3; i++) {
      const processRes = http.post(
        `${BASE_URL}/api/process`, 
        JSON.stringify({
          action: 'heavy_process',
          data: { size: 'large', iterations: 10 }
        }),
        { headers: headers, tags: { endpoint: 'heavy-process' } }
      );
      
      check(processRes, {
        'heavy process completed': (r) => r.status < 500,
      });
      
      if (processRes.status >= 400) {
        httpReqFailed.add(1, { endpoint: 'heavy-process' });
      }
    }
    
    // Shorter think time during stress testing
    sleep(randomIntBetween(0.5, 2));
  }
}

// Helper to generate test data
function generateTestData() {
  return {
    searchTerms: [
      'electronics',
      'clothing',
      'furniture',
      'books',
      'computers',
      'phones',
      'kitchen',
      'food',
      'travel',
      'music',
      'shoes',
      'toys',
    ],
    productIds: Array.from({ length: 20 }, (_, i) => i + 1),
    userIds: Array.from({ length: 5 }, (_, i) => i + 1),
  };
}

// Generate a report after test completion
export function handleSummary(data) {
  return {
    "summary.html": htmlReport(data),
    "summary.json": JSON.stringify(data),
  };
}
