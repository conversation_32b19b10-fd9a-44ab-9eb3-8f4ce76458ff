apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-ia
  labels:
    app: agent-ia
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-ia
  template:
    metadata:
      labels:
        app: agent-ia
    spec:
      containers:
      - name: agent-ia
        image: agent-ia:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5002
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: AGENT_RB_SERVICE_URL
          value: "http://agent-rb-service:5000"
        - name: SUPERAGENT_SERVICE_URL
          value: "http://superagent-service:5001"
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 5002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 5002
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: agent-ia-service
spec:
  selector:
    app: agent-ia
  ports:
  - port: 5002
    targetPort: 5002
  type: ClusterIP
