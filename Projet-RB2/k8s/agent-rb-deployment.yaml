apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-rb
  labels:
    app: agent-rb
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-rb
  template:
    metadata:
      labels:
        app: agent-rb
    spec:
      containers:
      - name: agent-rb
        image: agent-rb:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: SUPERAGENT_SERVICE_URL
          value: "http://superagent-service:5001"
        - name: AGENT_IA_SERVICE_URL
          value: "http://agent-ia-service:5002"
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "200m"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: agent-rb-service
spec:
  selector:
    app: agent-rb
  ports:
  - port: 5000
    targetPort: 5000
  type: ClusterIP
