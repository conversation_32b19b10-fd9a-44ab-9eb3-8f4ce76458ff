apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: retreatandbe
  namespace: argocd
spec:
  description: RetreatAndBe Project with Approval Workflows
  sourceRepos:
  - '*'
  destinations:
  - namespace: retreatandbe
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  roles:
  - name: approver
    description: Role for approving deployments
    policies:
    - p, proj:retreatandbe:approver, applications, action/*, retreatandbe/*, allow
    groups:
    - retreatandbe-approvers
  syncWindows:
  - kind: allow
    schedule: '* * * * *'
    duration: 1h
    applications:
    - '*'
    manualSync: true
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: retreatandbe
  namespace: argocd
  annotations:
    notifications.argoproj.io/subscribe.on-deployed.slack: deployments
    notifications.argoproj.io/subscribe.on-health-degraded.slack: alerts
    notifications.argoproj.io/subscribe.on-sync-failed.slack: alerts
spec:
  project: retreatandbe
  source:
    repoURL: 'https://github.com/your-org/retreatandbe.git'
    targetRevision: HEAD
    path: k8s/overlays/production
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: retreatandbe
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
    - PruneLast=true
    - ApplyOutOfSyncOnly=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m