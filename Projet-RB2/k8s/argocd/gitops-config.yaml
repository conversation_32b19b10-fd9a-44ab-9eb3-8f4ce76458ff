apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: gitops-apps
  namespace: argocd
spec:
  generators:
  - git:
      repoURL: 'https://github.com/your-org/Project-Final.git'
      revision: HEAD
      directories:
      - path: charts/*
  template:
    metadata:
      name: '{{path.basename}}'
      namespace: argocd
      annotations:
        notifications.argoproj.io/subscribe.on-sync-succeeded.slack: deployments
        notifications.argoproj.io/subscribe.on-sync-failed.slack: alerts
    spec:
      project: default
      source:
        repoURL: 'https://github.com/your-org/Project-Final.git'
        targetRevision: HEAD
        path: '{{path}}'
        helm:
          valueFiles:
          - values.yaml
      destination:
        server: https://kubernetes.default.svc
        namespace: '{{path.basename}}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
          allowEmpty: true
        syncOptions:
        - CreateNamespace=true
        - ServerSideApply=true
        retry:
          limit: 5
          backoff:
            duration: 5s
            factor: 2
            maxDuration: 3m
---
apiVersion: argoproj.io/v1alpha1
kind: ConfigManagementPlugin
metadata:
  name: kustomize-build
  namespace: argocd
spec:
  version: v1
  generate:
    command: ["/bin/sh", "-c"]
    args: ["kustomize build"]
  discover:
    fileName: "kustomization.yaml"
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: gitops-project
  namespace: argocd
spec:
  description: GitOps Project for RetreatAndBe
  sourceRepos:
  - '*'
  destinations:
  - namespace: '*'
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  namespaceResourceWhitelist:
  - group: '*'
    kind: '*'
  roles:
  - name: developer
    description: Developer role for GitOps workflows
    policies:
    - p, proj:gitops-project:developer, applications, sync, gitops-project/*, allow
    - p, proj:gitops-project:developer, applications, get, gitops-project/*, allow
    groups:
    - retreatandbe-developers