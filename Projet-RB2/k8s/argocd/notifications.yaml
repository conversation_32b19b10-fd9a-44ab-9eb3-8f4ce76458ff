apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
  namespace: argocd
data:
  service.slack: |
    token: $slack-token
    username: ArgoCD
  trigger.on-deployed: |
    - when: app.status.operationState.phase in ['Succeeded']
      send: [app-deployed]
  trigger.on-health-degraded: |
    - when: app.status.health.status == 'Degraded'
      send: [app-health-degraded]
  trigger.on-sync-failed: |
    - when: app.status.operationState.phase in ['Error', 'Failed']
      send: [app-sync-failed]
  template.app-deployed: |
    message: Application {{.app.metadata.name}} has been successfully deployed.
  template.app-health-degraded: |
    message: Application {{.app.metadata.name}} health status is now Degraded.
  template.app-sync-failed: |
    message: Failed to sync application {{.app.metadata.name}}.
---
apiVersion: v1
kind: Secret
metadata:
  name: argocd-notifications-secret
  namespace: argocd
type: Opaque
data:
  slack-token: <base64-encoded-slack-token>