#!/bin/bash

# Configuration
BACKUP_DIR="/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
POSTGRES_DB="retreatdb"

# Création du répertoire de sauvegarde
mkdir -p $BACKUP_DIR

# Sauvegarde de PostgreSQL
echo "Starting PostgreSQL backup..."
PGPASSWORD=$POSTGRES_PASSWORD pg_dump -h postgres-service -U $POSTGRES_USER $POSTGRES_DB > $BACKUP_DIR/postgres_${TIMESTAMP}.sql

# Compression de la sauvegarde PostgreSQL
gzip $BACKUP_DIR/postgres_${TIMESTAMP}.sql

# Sauvegarde des fichiers partagés
echo "Starting shared files backup..."
tar -czf $BACKUP_DIR/shared_files_${TIMESTAMP}.tar.gz /data/shared/

# Nettoyage des anciennes sauvegardes (garde les 7 derniers jours)
find $BACKUP_DIR -type f -mtime +7 -delete

echo "Backup completed successfully!"
