apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: retreatandbe
  namespace: retreatandbe
spec:
  replicas: 3
  selector:
    matchLabels:
      app: retreatandbe
  template:
    metadata:
      labels:
        app: retreatandbe
    spec:
      containers:
      - name: retreatandbe
        image: your-registry.azurecr.io/retreatandbe:latest
        ports:
        - containerPort: 8080
  strategy:
    blueGreen:
      activeService: retreatandbe-active
      previewService: retreatandbe-preview
      autoPromotionEnabled: false
      prePromotionAnalysis:
        templates:
        - templateName: smoke-tests
      postPromotionAnalysis:
        templates:
        - templateName: performance-tests
      scaleDownDelaySeconds: 300
---
apiVersion: v1
kind: Service
metadata:
  name: retreatandbe-active
  namespace: retreatandbe
spec:
  ports:
  - port: 80
    targetPort: 8080
  selector:
    app: retreatandbe
---
apiVersion: v1
kind: Service
metadata:
  name: retreatandbe-preview
  namespace: retreatandbe
spec:
  ports:
  - port: 80
    targetPort: 8080
  selector:
    app: retreatandbe