apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: retreatandbe-gateway
  namespace: retreatandbe
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*"
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: retreatandbe-tls
    hosts:
    - "api.retreatandbe.com"
    - "www.retreatandbe.com"
