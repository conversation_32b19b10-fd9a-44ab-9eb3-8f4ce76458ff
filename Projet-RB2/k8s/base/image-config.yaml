apiVersion: v1
kind: ConfigMap
metadata:
  name: image-config
data:
  DOCKER_REGISTRY: "docker.io"
  NAMESPACE: "votre-namespace"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
spec:
  template:
    spec:
      containers:
      - name: frontend
        image: $(DOCKER_REGISTRY)/$(NAMESPACE)/frontend:latest
        imagePullPolicy: Always
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
spec:
  template:
    spec:
      containers:
      - name: backend
        image: $(DOCKER_REGISTRY)/$(NAMESPACE)/backend:latest
        imagePullPolicy: Always
