apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: retreatandbe

resources:
- namespace.yaml
- analyzer-deployment.yaml
- analyzer-service.yaml
- analyzer-hpa.yaml
- analyzer-pdb.yaml
- gateway.yaml
- virtual-service.yaml
- destination-rule.yaml
- network-policy.yaml
- service-monitor.yaml

commonLabels:
  app.kubernetes.io/name: analyzer
  app.kubernetes.io/part-of: retreatandbe
  app.kubernetes.io/managed-by: kustomize

configMapGenerator:
- name: analyzer-config
  literals:
  - NODE_ENV=production

secretGenerator:
- name: vault-token
  files:
  - token=secrets/vault-token.txt
- name: tls-cert
  files:
  - tls.crt=secrets/tls.crt
  - tls.key=secrets/tls.key

images:
- name: analyzer
  newName: analyzer
  newTag: latest

replicas:
- name: analyzer
  count: 3
