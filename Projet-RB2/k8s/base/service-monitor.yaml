apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: analyzer-monitor
  namespace: retreatandbe
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: analyzer
  namespaceSelector:
    matchNames:
    - retreatandbe
  endpoints:
  - port: http
    path: /metrics
    interval: 15s
    scrapeTimeout: 14s
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'go_.*'
      action: drop
  - port: http
    path: /metrics/custom
    interval: 30s
    scrapeTimeout: 28s
    honorLabels: true
