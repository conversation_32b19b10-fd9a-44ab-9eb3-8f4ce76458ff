apiVersion: apps/v1
kind: Deployment
metadata:
  name: insurance-service
  labels:
    app: insurance-service
spec:
  selector:
    matchLabels:
      app: insurance-service
  template:
    metadata:
      labels:
        app: insurance-service
    spec:
      containers:
      - name: insurance-service
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/insurance-service:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-creator-service
  labels:
    app: website-creator-service
spec:
  selector:
    matchLabels:
      app: website-creator-service
  template:
    metadata:
      labels:
        app: website-creator-service
    spec:
      containers:
      - name: website-creator-service
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/website-creator-service:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gps-service
  labels:
    app: gps-service
spec:
  selector:
    matchLabels:
      app: gps-service
  template:
    metadata:
      labels:
        app: gps-service
    spec:
      containers:
      - name: gps-service
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/gps-service:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketplace-service
  labels:
    app: marketplace-service
spec:
  selector:
    matchLabels:
      app: marketplace-service
  template:
    metadata:
      labels:
        app: marketplace-service
    spec:
      containers:
      - name: marketplace-service
        image: ${DOCKER_REGISTRY}/${NAMESPACE}/marketplace-service:latest
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 5
