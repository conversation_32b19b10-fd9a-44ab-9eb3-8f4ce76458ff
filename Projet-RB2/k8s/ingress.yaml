apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: retreat-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 7000
      - path: /agent-rb
        pathType: Prefix
        backend:
          service:
            name: agent-rb-service
            port:
              number: 5000
      - path: /superagent
        pathType: Prefix
        backend:
          service:
            name: superagent-service
            port:
              number: 5001
      - path: /agent-ia
        pathType: Prefix
        backend:
          service:
            name: agent-ia-service
            port:
              number: 5002
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80
