apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
  name: rb2-multicluster
spec:
  profile: default
  # Enable multiCluster functionality
  meshConfig:
    # Enable default destination rule for all services
    defaultConfig:
      proxyMetadata:
        # Enable automatic protocol detection
        AUTO_PROTOCOL_DETECT: "true"
    # Configure the trust domain for this cluster
    trustDomain: "cluster.local"
    # Add global domain suffix for multi-cluster service discovery
    defaultServiceExportTo:
      - "*"
    defaultVirtualServiceExportTo:
      - "*"
    defaultDestinationRuleExportTo:
      - "*"
    # Enable access logging for debugging multi-cluster traffic
    accessLogFile: "/dev/stdout"
    accessLogEncoding: JSON
    accessLogFormat: |
      {
        "protocol": "%PROTOCOL%",
        "upstream_service": "%UPSTREAM_SERVICE%",
        "response_code": "%RESPONSE_CODE%",
        "response_flags": "%RESPONSE_FLAGS%",
        "connection_termination_details": "%CONNECTION_TERMINATION_DETAILS%",
        "upstream_host": "%UPSTREAM_HOST%",
        "upstream_cluster": "%UPSTREAM_CLUSTER%",
        "upstream_local_address": "%UPSTREAM_LOCAL_ADDRESS%",
        "downstream_local_address": "%DOWNSTREAM_LOCAL_ADDRESS%",
        "downstream_remote_address": "%DOWNSTREAM_REMOTE_ADDRESS%",
        "requested_server_name": "%REQUESTED_SERVER_NAME%",
        "route_name": "%ROUTE_NAME%"
      }
  
  # Global values passed through to helm templates
  values:
    global:
      # Enable multiCluster mode
      multiCluster:
        enabled: true
        # Name of this cluster for cross-cluster communication
        clusterName: "primary-cluster"
        # Global domain suffix for multi-cluster service discovery
        globalDomainSuffix: "global"
      
      # Configure mesh networks for multi-cluster communication
      meshNetworks:
        network1:
          endpoints:
          - fromRegistry: primary-cluster
          gateways:
          - address: istio-eastwestgateway.istio-system.svc.cluster.local
            port: 15443
        network2:
          endpoints:
          - fromRegistry: remote-cluster
          gateways:
          - address: istio-eastwestgateway-remote.istio-system.svc.cluster.local
            port: 15443
      
      # Enable features required for multi-cluster
      pilotCertProvider: kubernetes
      
      # Configure proxy for multi-cluster
      proxy:
        # Enable automatic protocol detection
        autoInject: enabled
        # Configure cluster domain
        clusterDomain: "cluster.local"
        # Configure proxy resource limits
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
      
      # Configure proxy initialization
      proxy_init:
        resources:
          limits:
            cpu: 2000m
            memory: 1024Mi
          requests:
            cpu: 10m
            memory: 10Mi
  
  # Components configuration
  components:
    # Configure base components
    base:
      enabled: true
    
    # Configure pilot (istiod)
    pilot:
      enabled: true
      k8s:
        env:
          # Enable endpoints from other clusters
          - name: PILOT_ENABLE_K8S_SELECT_WORKLOAD_ENTRIES
            value: "true"
          # Enable cross-cluster service discovery
          - name: PILOT_ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY
            value: "true"
          # Enable endpoint discovery features
          - name: PILOT_ENABLE_ENDPOINT_DISCOVERY
            value: "true"
    
    # Configure ingress gateway
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
    
    # Configure east-west gateway for cross-cluster communication
    - name: istio-eastwestgateway
      enabled: true
      label:
        istio: eastwestgateway
        app: istio-eastwestgateway
      k8s:
        env:
          - name: ISTIO_META_ROUTER_MODE
            value: "sni-dnat"
          - name: ISTIO_META_REQUESTED_NETWORK_VIEW
            value: "network1"
        service:
          ports:
            - name: status-port
              port: 15021
              targetPort: 15021
            - name: tls
              port: 15443
              targetPort: 15443
            - name: tls-istiod
              port: 15012
              targetPort: 15012
            - name: tls-webhook
              port: 15017
              targetPort: 15017
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
    
    # Configure egress gateway
    egressGateways:
    - name: istio-egressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
  
  # Additional features configuration
  addonComponents:
    # Enable Prometheus for metrics collection
    prometheus:
      enabled: true
    # Enable Grafana for metrics visualization
    grafana:
      enabled: true
    # Enable Kiali for service mesh visualization
    kiali:
      enabled: true
    # Enable Jaeger for distributed tracing
    tracing:
      enabled: true
  
  # Feature settings
  features:
    # Enable external istiod for remote clusters
    externalIstiod: true
    # Enable mounting of mTLS certificates for secure communication
    mountMtlsCerts: true
    # Include Envoy filter for advanced traffic management
    includeEnvoyFilter: true
