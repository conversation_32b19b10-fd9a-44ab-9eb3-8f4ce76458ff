apiVersion: v1
kind: ConfigMap
metadata:
  name: advanced-aggregation-config
  namespace: logging
data:
  aggregation.conf: |
    # Parser pour les logs structurés
    <parse>
      @type multi_format
      <pattern>
        format json
        time_key timestamp
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </pattern>
      <pattern>
        format regexp
        expression /^(?<time>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (?<level>\w+): (?<message>.*)$/
        time_format %Y-%m-%d %H:%M:%S
      </pattern>
    </parse>

    # Enrichissement des logs
    <filter kubernetes.**>
      @type record_transformer
      enable_ruby true
      <record>
        cluster_name "#{ENV['CLUSTER_NAME']}"
        environment "#{ENV['ENVIRONMENT']}"
        pod_ip ${record["kubernetes"]["pod_ip"]}
        container_name ${record["kubernetes"]["container_name"]}
        namespace_name ${record["kubernetes"]["namespace_name"]}
        pod_name ${record["kubernetes"]["pod_name"]}
        container_image ${record["kubernetes"]["container_image"]}
        container_image_id ${record["kubernetes"]["container_image_id"]}
        timestamp ${time.strftime('%Y-%m-%dT%H:%M:%S.%N%z')}
      </record>
    </filter>

    # Agrégation temporelle
    <filter kubernetes.**>
      @type aggregate
      <buffer>
        @type memory
        flush_interval 60s
        chunk_limit_size 5M
      </buffer>
      aggregate time
      group_by container_name
      time_range 300
    </filter>

    # Corrélation des événements
    <filter kubernetes.**>
      @type grep
      <regexp>
        key message
        pattern (error|exception|failed|failure|warning)
      </regexp>
    </filter>

    # Métriques de performance
    <filter kubernetes.**>
      @type prometheus
      <metric>
        name kube_pod_log_entries_total
        type counter
        desc Total number of log entries per pod
        <labels>
          pod ${record["kubernetes"]["pod_name"]}
          namespace ${record["kubernetes"]["namespace_name"]}
          container ${record["kubernetes"]["container_name"]}
        </labels>
      </metric>
      <metric>
        name kube_pod_error_logs_total
        type counter
        desc Total number of error log entries per pod
        <labels>
          pod ${record["kubernetes"]["pod_name"]}
          namespace ${record["kubernetes"]["namespace_name"]}
          container ${record["kubernetes"]["container_name"]}
        </labels>
      </metric>
    </filter>

    # Output vers Elasticsearch avec routage intelligent
    <match kubernetes.**>
      @type elasticsearch_dynamic
      host elasticsearch
      port 9200
      logstash_format true
      <buffer>
        @type file
        path /var/log/fluentd/elasticsearch
        flush_interval 10s
        chunk_limit_size 2M
        total_limit_size 512M
        retry_max_interval 30
        retry_forever true
      </buffer>
      <routing>
        field level
        routing_table {
          "error": "logstash-errors-${index_suffix}",
          "warn": "logstash-warnings-${index_suffix}",
          "info": "logstash-info-${index_suffix}",
          "debug": "logstash-debug-${index_suffix}"
        }
      </routing>
    </match>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: log-aggregator
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: log-aggregator
  template:
    metadata:
      labels:
        app: log-aggregator
    spec:
      containers:
      - name: fluentd-aggregator
        image: fluent/fluentd-kubernetes-daemonset:v1.14.6-debian-elasticsearch7-1.1
        env:
        - name: CLUSTER_NAME
          value: "production"
        - name: ENVIRONMENT
          value: "production"
        ports:
        - containerPort: 24224
          name: forward
        - containerPort: 24231
          name: prometheus
        volumeMounts:
        - name: config
          mountPath: /fluentd/etc/aggregation.conf
          subPath: aggregation.conf
        - name: buffer
          mountPath: /var/log/fluentd
        resources:
          limits:
            memory: 1Gi
            cpu: 500m
          requests:
            memory: 512Mi
            cpu: 250m
      volumes:
      - name: config
        configMap:
          name: advanced-aggregation-config
      - name: buffer
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: log-aggregator
  namespace: logging
spec:
  selector:
    app: log-aggregator
  ports:
  - port: 24224
    name: forward
  - port: 24231
    name: prometheus
  type: ClusterIP
