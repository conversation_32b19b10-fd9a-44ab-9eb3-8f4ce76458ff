apiVersion: v1
kind: ConfigMap
metadata:
  name: advanced-alerts-config
  namespace: logging
data:
  alerts.conf: |
    # Configuration des règles d'alerte
    groups:
    - name: application_alerts
      rules:
      # Alertes de performance
      - alert: HighLatency
        expr: http_request_duration_seconds > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High latency detected"
          description: "Request latency is above 1s for 5 minutes"

      - alert: ErrorSpike
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Error rate spike detected"
          description: "Error rate is above 10 per second for 2 minutes"

      # Alertes de ressources
      - alert: HighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Container memory usage is above 90%"

      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "Container CPU usage is above 80%"

      # Alertes de logs
      - alert: HighErrorRate
        expr: rate(log_messages_total{level="error"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error log rate"
          description: "Error log rate is above 0.1 per second"

      - alert: LoggingBackpressure
        expr: rate(fluentd_output_status_retry_count[5m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Logging backpressure detected"
          description: "Fluentd is experiencing backpressure"

    # Configuration des notifications
    receivers:
    - name: slack
      slack_configs:
      - channel: '#alerts'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        footer: '{{ template "slack.default.footer" . }}'

    - name: email
      email_configs:
      - to: '<EMAIL>'
        send_resolved: true
        headers:
          subject: '{{ template "email.default.subject" . }}'
        html: '{{ template "email.default.html" . }}'

    - name: pagerduty
      pagerduty_configs:
      - service_key: '<service_key>'
        send_resolved: true
        description: '{{ template "pagerduty.default.description" . }}'
        severity: '{{ if eq .Status "firing" }}{{ .CommonLabels.severity }}{{ else }}resolved{{ end }}'

    # Templates personnalisés
    templates:
    - name: slack.default.title
      template: '[{{ .Status | toUpper }}] {{ .CommonLabels.alertname }}'
    - name: slack.default.text
      template: |
        *Alert:* {{ .CommonLabels.alertname }}
        *Severity:* {{ .CommonLabels.severity }}
        *Summary:* {{ .CommonAnnotations.summary }}
        *Description:* {{ .CommonAnnotations.description }}
        *Duration:* {{ .StartsAt | since }}
    - name: slack.default.footer
      template: 'Alertmanager · {{ .ExternalURL }}'

    # Routes d'alerte
    route:
      receiver: slack
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 4h
      routes:
      - match:
          severity: critical
        receiver: pagerduty
        group_wait: 0s
        repeat_interval: 1h
      - match:
          severity: warning
        receiver: slack
        group_wait: 30s
        repeat_interval: 2h
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alert-manager
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: alert-manager
  template:
    metadata:
      labels:
        app: alert-manager
    spec:
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.24.0
        args:
        - "--config.file=/etc/alertmanager/alerts.conf"
        - "--storage.path=/alertmanager"
        ports:
        - containerPort: 9093
          name: alertmanager
        volumeMounts:
        - name: config
          mountPath: /etc/alertmanager
        - name: storage
          mountPath: /alertmanager
        resources:
          limits:
            memory: 256Mi
            cpu: 200m
          requests:
            memory: 128Mi
            cpu: 100m
      volumes:
      - name: config
        configMap:
          name: advanced-alerts-config
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: alert-manager
  namespace: logging
spec:
  selector:
    app: alert-manager
  ports:
  - port: 9093
    name: alertmanager
  type: ClusterIP
