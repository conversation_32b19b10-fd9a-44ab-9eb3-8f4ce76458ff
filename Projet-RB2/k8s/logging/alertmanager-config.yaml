apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-notification-config
  namespace: logging
type: Opaque
stringData:
  alertmanager.yaml: |
    global:
      resolve_timeout: 5m
      slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK'
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'your-app-specific-password'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
      victorops_api_url: 'https://alert.victorops.com/integrations/generic/20131114/alert'

    templates:
    - '/etc/alertmanager/config/*.tmpl'

    route:
      receiver: 'slack-notifications'
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 4h

      routes:
      - receiver: 'critical-pagerduty'
        matchers:
        - severity = 'critical'
        group_wait: 30s
        repeat_interval: 1h

      - receiver: 'warning-slack'
        matchers:
        - severity = 'warning'
        group_wait: 30s
        repeat_interval: 2h

      - receiver: 'info-email'
        matchers:
        - severity = 'info'
        group_wait: 2m
        group_interval: 10m
        repeat_interval: 12h

    inhibit_rules:
    - source_matchers:
      - severity = 'critical'
      target_matchers:
      - severity = 'warning'
      equal: ['alertname', 'cluster', 'service']

    receivers:
    - name: 'critical-pagerduty'
      pagerduty_configs:
      - routing_key: 'your-pagerduty-routing-key'
        severity: '{{ .CommonLabels.severity }}'
        class: '{{ .CommonLabels.class }}'
        group: '{{ .CommonLabels.group }}'
        description: |-
          {{ .CommonAnnotations.description }}
          Runbook: {{ .CommonAnnotations.runbook_url }}
        details:
          firing: '{{ .Alerts.Firing | len }}'
          resolved: '{{ .Alerts.Resolved | len }}'
          num_firing: '{{ .Alerts.Firing | len }}'
      victorops_configs:
      - api_key: 'your-victorops-api-key'
        routing_key: 'your-team-routing-key'
        message_type: 'CRITICAL'
        entity_display_name: '{{ .CommonLabels.alertname }}'
        state_message: |-
          {{ .CommonAnnotations.description }}
          Runbook: {{ .CommonAnnotations.runbook_url }}

    - name: 'warning-slack'
      slack_configs:
      - channel: '#monitoring-alerts'
        send_resolved: true
        icon_emoji: ':warning:'
        title: |-
          [{{ .Status | toUpper }}] {{ .CommonLabels.alertname }}
        text: |-
          *Alert:* {{ .CommonLabels.alertname }}
          *Severity:* {{ .CommonLabels.severity }}
          *Summary:* {{ .CommonAnnotations.summary }}
          *Description:* {{ .CommonAnnotations.description }}
          *Runbook:* {{ .CommonAnnotations.runbook_url }}
          *Details:*
          {{ range .Alerts }}
            *Alert:* {{ .Annotations.summary }}
            *Started:* {{ .StartsAt }}
          {{ end }}

    - name: 'info-email'
      email_configs:
      - to: '<EMAIL>'
        send_resolved: true
        headers:
          subject: |-
            [{{ .Status | toUpper }}] {{ .CommonLabels.alertname }}
        html: |-
          <h2>{{ .CommonLabels.alertname }}</h2>
          <p><strong>Severity:</strong> {{ .CommonLabels.severity }}</p>
          <p><strong>Summary:</strong> {{ .CommonAnnotations.summary }}</p>
          <p><strong>Description:</strong> {{ .CommonAnnotations.description }}</p>
          <p><strong>Runbook:</strong> <a href="{{ .CommonAnnotations.runbook_url }}">{{ .CommonAnnotations.runbook_url }}</a></p>
          <h3>Alerts:</h3>
          <ul>
          {{ range .Alerts }}
            <li>
              <p><strong>Started:</strong> {{ .StartsAt }}</p>
              <p><strong>Summary:</strong> {{ .Annotations.summary }}</p>
            </li>
          {{ end }}
          </ul>
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-templates
  namespace: logging
data:
  default.tmpl: |
    {{ define "slack.default.title" }}
    [{{ .Status | toUpper }}] {{ .CommonLabels.alertname }}
    {{ end }}

    {{ define "slack.default.text" }}
    *Alert:* {{ .CommonLabels.alertname }}
    *Severity:* {{ .CommonLabels.severity }}
    *Summary:* {{ .CommonAnnotations.summary }}
    *Description:* {{ .CommonAnnotations.description }}
    *Runbook:* {{ .CommonAnnotations.runbook_url }}

    *Details:*
    {{ range .Alerts }}
      *Alert:* {{ .Annotations.summary }}
      *Started:* {{ .StartsAt }}
    {{ end }}
    {{ end }}

    {{ define "pagerduty.default.description" }}
    {{ .CommonAnnotations.description }}
    Runbook: {{ .CommonAnnotations.runbook_url }}
    {{ end }}

    {{ define "email.default.subject" }}
    [{{ .Status | toUpper }}] {{ .CommonLabels.alertname }}
    {{ end }}

    {{ define "email.default.html" }}
    <h2>{{ .CommonLabels.alertname }}</h2>
    <p><strong>Severity:</strong> {{ .CommonLabels.severity }}</p>
    <p><strong>Summary:</strong> {{ .CommonAnnotations.summary }}</p>
    <p><strong>Description:</strong> {{ .CommonAnnotations.description }}</p>
    <p><strong>Runbook:</strong> <a href="{{ .CommonAnnotations.runbook_url }}">{{ .CommonAnnotations.runbook_url }}</a></p>
    <h3>Alerts:</h3>
    <ul>
    {{ range .Alerts }}
      <li>
        <p><strong>Started:</strong> {{ .StartsAt }}</p>
        <p><strong>Summary:</strong> {{ .Annotations.summary }}</p>
      </li>
    {{ end }}
    </ul>
    {{ end }}
