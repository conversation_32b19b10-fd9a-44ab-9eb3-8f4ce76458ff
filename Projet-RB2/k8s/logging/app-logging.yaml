apiVersion: v1
kind: ConfigMap
metadata:
  name: app-logging-config
  namespace: logging
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         5
        Daemon       Off
        Log_Level    info
        Parsers_File parsers.conf

    [INPUT]
        Name              tail
        Tag               kube.*
        Path              /var/log/containers/*.log
        Parser            docker
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10

    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL           https://kubernetes.default.svc:443
        Kube_CA_File       /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File    /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log          On
        K8S-Logging.Parser On
        K8S-Logging.Exclude On

    # Enrichissement des logs avec les métadonnées
    [FILTER]
        Name                modify
        Match               kube.*
        Add                 env ${ENV}
        Add                 app_version ${APP_VERSION}
        Add                 cluster_name ${CLUSTER_NAME}

    # Parser pour les logs structurés
    [FILTER]
        Name                parser
        Match               kube.*
        Key_Name            log
        Parser             json
        Reserve_Data       On

    # Filtre pour les logs de l'application frontend
    [FILTER]
        Name                grep
        Match               kube.var.log.containers.frontend-*
        Regex              level (error|warn|info|debug)

    # Filtre pour les logs de l'API
    [FILTER]
        Name                grep
        Match               kube.var.log.containers.api-*
        Regex              level (error|warn|info|debug)

    # Output vers Elasticsearch
    [OUTPUT]
        Name               es
        Match              kube.*
        Host               elasticsearch-master
        Port               9200
        HTTP_User          ${ES_USER}
        HTTP_Passwd        ${ES_PASSWORD}
        Logstash_Format    On
        Logstash_Prefix    k8s-logs
        Time_Key           @timestamp
        Generate_ID        On
        Replace_Dots       On
        Retry_Limit        False
        tls               On
        tls.verify        Off
        Buffer_Size        False

    # Output vers Loki
    [OUTPUT]
        Name               loki
        Match              kube.*
        Host               loki
        Port               3100
        Labels            job=fluentbit, env=${ENV}, app=${APP_NAME}
        Label_Keys        $kubernetes['namespace_name'], $kubernetes['pod_name'], $kubernetes['container_name']
        Remove_Keys       kubernetes, stream, time
        Auto_Kubernetes_Labels On
        Line_Format       json

  parsers.conf: |
    [PARSER]
        Name        docker
        Format      json
        Time_Key    time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On

    [PARSER]
        Name        json
        Format      json
        Time_Key    timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On

    [PARSER]
        Name        nginx
        Format      regex
        Regex       ^(?<remote>[^ ]*) (?<host>[^ ]*) (?<user>[^ ]*) \[(?<time>[^\]]*)\] "(?<method>\S+)(?: +(?<path>[^\"]*?)(?: +\S*)?)?" (?<code>[^ ]*) (?<size>[^ ]*)(?: "(?<referer>[^\"]*)" "(?<agent>[^\"]*)")
        Time_Key    time
        Time_Format %d/%b/%Y:%H:%M:%S %z

    [PARSER]
        Name        python
        Format      regex
        Regex       ^(?<time>[^ ]* [^ ]*) (?<level>[^ ]*) (?<message>.*)$
        Time_Key    time
        Time_Format %Y-%m-%d %H:%M:%S

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: logging
  labels:
    app: fluent-bit
spec:
  selector:
    matchLabels:
      app: fluent-bit
  template:
    metadata:
      labels:
        app: fluent-bit
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2020"
        prometheus.io/path: /api/v1/metrics/prometheus
    spec:
      serviceAccountName: fluent-bit
      containers:
      - name: fluent-bit
        image: fluent/fluent-bit:1.9.3
        imagePullPolicy: Always
        env:
        - name: ENV
          value: "production"
        - name: APP_VERSION
          value: "1.0.0"
        - name: CLUSTER_NAME
          value: "main-cluster"
        - name: ES_USER
          valueFrom:
            secretKeyRef:
              name: elasticsearch-credentials
              key: username
        - name: ES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: elasticsearch-credentials
              key: password
        volumeMounts:
        - name: config
          mountPath: /fluent-bit/etc/
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: etcmachineid
          mountPath: /etc/machine-id
          readOnly: true
        ports:
        - containerPort: 2020
          name: metrics
        resources:
          limits:
            memory: 500Mi
            cpu: 500m
          requests:
            cpu: 100m
            memory: 200Mi
        livenessProbe:
          httpGet:
            path: /
            port: 2020
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/metrics/prometheus
            port: 2020
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: config
        configMap:
          name: app-logging-config
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: etcmachineid
        hostPath:
          path: /etc/machine-id
          type: File
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit
  namespace: logging
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit-role
rules:
- apiGroups: [""]
  resources:
  - namespaces
  - pods
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit-role
subjects:
- kind: ServiceAccount
  name: fluent-bit
  namespace: logging
