apiVersion: v1
kind: ConfigMap
metadata:
  name: reporting-config
  namespace: logging
data:
  report-templates.yaml: |
    # Configuration des rapports automatiques
    reports:
      # Rapport quotidien d'infrastructure
      daily_infrastructure:
        schedule: "0 7 * * *"  # Tous les jours à 7h
        title: "Rapport Quotidien d'Infrastructure"
        sections:
          - name: "Utilisation des Ressources"
            metrics:
              - name: "CPU Peak"
                query: "max_over_time(node_cpu_usage_percent[24h])"
              - name: "Memory Peak"
                query: "max_over_time(node_memory_usage_percent[24h])"
              - name: "Disk Usage"
                query: "max_over_time(node_disk_usage_percent[24h])"
            thresholds:
              warning: 80
              critical: 90
            
          - name: "Performance"
            metrics:
              - name: "Response Time P95"
                query: "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[24h])) by (le))"
              - name: "Error Rate"
                query: "sum(rate(http_requests_total{status=~'5..'}[24h])) / sum(rate(http_requests_total[24h])) * 100"
            thresholds:
              warning: 5
              critical: 10

      # Rapport hebdomadaire de sécurité
      weekly_security:
        schedule: "0 8 * * 1"  # Tous les lundis à 8h
        title: "Rapport Hebdomadaire de Sécurité"
        sections:
          - name: "Incidents de Sécurité"
            metrics:
              - name: "Unauthorized Access"
                query: "sum(increase(unauthorized_access_total[7d]))"
              - name: "Failed Logins"
                query: "sum(increase(failed_login_attempts_total[7d]))"
              - name: "Security Events"
                query: "sum(increase(security_events_total[7d])) by (type)"
            thresholds:
              warning: 50
              critical: 100

          - name: "Audit"
            metrics:
              - name: "Configuration Changes"
                query: "sum(increase(config_changes_total[7d])) by (component)"
              - name: "Permission Changes"
                query: "sum(increase(permission_changes_total[7d])) by (type)"
            thresholds:
              warning: 10
              critical: 20

      # Rapport mensuel de performance
      monthly_performance:
        schedule: "0 9 1 * *"  # Premier jour du mois à 9h
        title: "Rapport Mensuel de Performance"
        sections:
          - name: "Tendances"
            metrics:
              - name: "Average Response Time"
                query: "avg_over_time(http_request_duration_seconds[30d])"
              - name: "99th Percentile"
                query: "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket[30d])) by (le))"
              - name: "Request Volume"
                query: "sum(increase(http_requests_total[30d])) by (endpoint)"
            comparisons:
              previous_month: true
              year_over_year: true

  custom-metrics.yaml: |
    # Métriques personnalisées
    metrics:
      # Métriques business
      business:
        - name: "user_engagement"
          type: "gauge"
          help: "Mesure l'engagement des utilisateurs"
          labels:
            - "feature"
            - "user_type"
          aggregations:
            - "sum"
            - "rate"
            
        - name: "transaction_value"
          type: "histogram"
          help: "Valeur des transactions"
          buckets: [10, 50, 100, 500, 1000, 5000]
          labels:
            - "product"
            - "currency"
          aggregations:
            - "sum"
            - "avg"
            
        - name: "feature_usage"
          type: "counter"
          help: "Utilisation des fonctionnalités"
          labels:
            - "feature"
            - "user_type"
            - "status"
          aggregations:
            - "rate"
            - "increase"

      # Métriques techniques
      technical:
        - name: "cache_efficiency"
          type: "gauge"
          help: "Efficacité du cache"
          labels:
            - "cache_type"
            - "operation"
          aggregations:
            - "avg"
            - "min"
            - "max"
            
        - name: "queue_depth"
          type: "gauge"
          help: "Profondeur des files d'attente"
          labels:
            - "queue"
            - "priority"
          aggregations:
            - "avg"
            - "max"
            
        - name: "batch_processing"
          type: "histogram"
          help: "Temps de traitement des lots"
          buckets: [0.1, 0.5, 1, 2, 5, 10]
          labels:
            - "batch_type"
            - "size"
          aggregations:
            - "rate"
            - "histogram_quantile"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: report-generator
  namespace: logging
spec:
  schedule: "*/30 * * * *"  # Toutes les 30 minutes
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: report-generator
            image: python:3.9-slim
            command:
            - /bin/bash
            - -c
            - |
              pip install prometheus-api-client elasticsearch-dsl jinja2 pandas matplotlib
              python - <<EOF
              import os
              import json
              from datetime import datetime
              from prometheus_api_client import PrometheusConnect
              from elasticsearch import Elasticsearch
              from jinja2 import Template
              import pandas as pd
              import matplotlib.pyplot as plt

              def generate_report(template_name, metrics_data):
                  with open(f'/etc/reporting/templates/{template_name}.html') as f:
                      template = Template(f.read())
                  
                  report = template.render(
                      data=metrics_data,
                      timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                  )
                  
                  # Génération des graphiques
                  for section in metrics_data['sections']:
                      plt.figure(figsize=(10, 6))
                      # ... code de génération de graphiques ...
                      plt.savefig(f'/reports/{section["name"]}.png')
                  
                  # Envoi par email
                  send_report_by_email(report)

              def send_report_by_email(report):
                  import smtplib
                  from email.mime.multipart import MIMEMultipart
                  from email.mime.text import MIMEText
                  
                  msg = MIMEMultipart()
                  msg['Subject'] = f'Rapport de monitoring - {datetime.now().strftime("%Y-%m-%d")}'
                  msg.attach(MIMEText(report, 'html'))
                  
                  with smtplib.SMTP('smtp.gmail.com', 587) as server:
                      server.starttls()
                      server.login(os.environ['SMTP_USER'], os.environ['SMTP_PASSWORD'])
                      server.send_message(msg)

              def main():
                  # Chargement de la configuration
                  with open('/etc/reporting/config/report-templates.yaml') as f:
                      config = yaml.safe_load(f)
                  
                  # Connexion aux sources de données
                  prom = PrometheusConnect(url='http://prometheus:9090')
                  es = Elasticsearch(['http://elasticsearch:9200'])
                  
                  # Génération des rapports
                  for report_name, report_config in config['reports'].items():
                      if is_report_due(report_config['schedule']):
                          metrics_data = collect_metrics(prom, es, report_config)
                          generate_report(report_name, metrics_data)

              if __name__ == '__main__':
                  main()
              EOF
            volumeMounts:
            - name: config-volume
              mountPath: /etc/reporting/config
            - name: template-volume
              mountPath: /etc/reporting/templates
            - name: report-volume
              mountPath: /reports
            env:
            - name: SMTP_USER
              valueFrom:
                secretKeyRef:
                  name: alert-secrets
                  key: smtp-username
            - name: SMTP_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: alert-secrets
                  key: smtp-password
          volumes:
          - name: config-volume
            configMap:
              name: reporting-config
          - name: template-volume
            configMap:
              name: report-templates
          - name: report-volume
            persistentVolumeClaim:
              claimName: report-storage
          restartPolicy: OnFailure

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: report-storage
  namespace: logging
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: report-templates
  namespace: logging
data:
  infrastructure.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>{{ title }}</title>
        <style>
            body { font-family: Arial, sans-serif; }
            .metric { margin: 20px 0; }
            .warning { color: orange; }
            .critical { color: red; }
            .chart { width: 100%; max-width: 800px; }
        </style>
    </head>
    <body>
        <h1>{{ title }}</h1>
        <p>Généré le {{ timestamp }}</p>
        
        {% for section in sections %}
        <h2>{{ section.name }}</h2>
        {% for metric in section.metrics %}
        <div class="metric">
            <h3>{{ metric.name }}</h3>
            <p>Valeur: {{ metric.value }}
            {% if metric.value > section.thresholds.critical %}
            <span class="critical">(CRITIQUE)</span>
            {% elif metric.value > section.thresholds.warning %}
            <span class="warning">(ATTENTION)</span>
            {% endif %}
            </p>
            <img class="chart" src="charts/{{ metric.name }}.png">
        </div>
        {% endfor %}
        {% endfor %}
    </body>
    </html>

  security.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>{{ title }}</title>
        <style>
            body { font-family: Arial, sans-serif; }
            .incident { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
            .high { border-color: red; }
            .medium { border-color: orange; }
            .low { border-color: yellow; }
        </style>
    </head>
    <body>
        <h1>{{ title }}</h1>
        <p>Période: {{ period }}</p>
        
        <h2>Résumé des Incidents</h2>
        {% for incident in incidents %}
        <div class="incident {{ incident.severity }}">
            <h3>{{ incident.type }}</h3>
            <p>Nombre: {{ incident.count }}</p>
            <p>Impact: {{ incident.impact }}</p>
        </div>
        {% endfor %}
        
        <h2>Recommandations</h2>
        <ul>
        {% for rec in recommendations %}
            <li>{{ rec }}</li>
        {% endfor %}
        </ul>
    </body>
    </html>

  performance.html: |
    <!DOCTYPE html>
    <html>
    <head>
        <title>{{ title }}</title>
        <style>
            body { font-family: Arial, sans-serif; }
            .trend { margin: 20px 0; }
            .improved { color: green; }
            .degraded { color: red; }
            .table { width: 100%; border-collapse: collapse; }
            .table td, .table th { border: 1px solid #ddd; padding: 8px; }
        </style>
    </head>
    <body>
        <h1>{{ title }}</h1>
        <p>Période: {{ period }}</p>
        
        {% for metric in metrics %}
        <div class="trend">
            <h2>{{ metric.name }}</h2>
            <p>Valeur actuelle: {{ metric.current }}</p>
            <p>Variation:
                <span class="{{ 'improved' if metric.trend > 0 else 'degraded' }}">
                    {{ metric.trend }}%
                </span>
            </p>
            <img class="chart" src="charts/{{ metric.name }}.png">
        </div>
        {% endfor %}
        
        <h2>Recommandations d'Optimisation</h2>
        <ul>
        {% for rec in recommendations %}
            <li>{{ rec }}</li>
        {% endfor %}
        </ul>
    </body>
    </html>
