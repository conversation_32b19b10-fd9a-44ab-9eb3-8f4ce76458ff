apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-config
  namespace: logging
data:
  backup-policy.conf: |
    # Configuration des sauvegardes
    backup:
      # Sauvegarde Elasticsearch
      elasticsearch:
        schedule: "0 1 * * *"  # Tous les jours à 1h du matin
        retention:
          days: 30
          copies: 7
        snapshot:
          repository: "es-snapshots"
          name: "daily-backup-${timestamp}"
        indices:
          - "logstash-*"
          - "metrics-*"
          - ".kibana"
        settings:
          compress: true
          max_snapshot_bytes_per_sec: "50mb"
          max_restore_bytes_per_sec: "50mb"

      # Sauvegarde Grafana
      grafana:
        schedule: "0 2 * * *"  # Tous les jours à 2h du matin
        retention:
          days: 30
          copies: 7
        include:
          - dashboards
          - datasources
          - users
          - alerts

      # Sauvegarde des configurations
      configs:
        schedule: "0 3 * * *"  # Tous les jours à 3h du matin
        retention:
          days: 90
          copies: 30
        include:
          - configmaps
          - secrets
          - deployments
          - services

    # Configuration de la restauration
    restore:
      # Restauration Elasticsearch
      elasticsearch:
        validation:
          - check_cluster_health
          - verify_indices
          - validate_snapshots
        steps:
          - stop_indexing
          - restore_snapshot
          - verify_restore
          - resume_indexing

      # Restauration Grafana
      grafana:
        validation:
          - check_service_status
          - verify_dashboards
        steps:
          - stop_service
          - restore_data
          - verify_restore
          - start_service

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: elasticsearch-backup
  namespace: logging
spec:
  schedule: "0 1 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: elasticsearch:7.17.3
            command:
            - /bin/bash
            - -c
            - |
              # Création du repository de snapshots
              curl -X PUT "elasticsearch:9200/_snapshot/es-snapshots" -H 'Content-Type: application/json' -d'
              {
                "type": "fs",
                "settings": {
                  "location": "/backup/elasticsearch"
                }
              }'

              # Création du snapshot
              SNAPSHOT_NAME="daily-backup-$(date +%Y%m%d-%H%M%S)"
              curl -X PUT "elasticsearch:9200/_snapshot/es-snapshots/$SNAPSHOT_NAME?wait_for_completion=true" -H 'Content-Type: application/json' -d'
              {
                "indices": ["logstash-*", "metrics-*", ".kibana"],
                "ignore_unavailable": true,
                "include_global_state": true
              }'
            volumeMounts:
            - name: backup-volume
              mountPath: /backup
          volumes:
          - name: backup-volume
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: grafana-backup
  namespace: logging
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: grafana/grafana:latest
            command:
            - /bin/bash
            - -c
            - |
              # Sauvegarde des dashboards
              curl -X GET "http://grafana:3000/api/dashboards" -u admin:admin > /backup/grafana/dashboards.json
              
              # Sauvegarde des datasources
              curl -X GET "http://grafana:3000/api/datasources" -u admin:admin > /backup/grafana/datasources.json
              
              # Sauvegarde des alertes
              curl -X GET "http://grafana:3000/api/alerts" -u admin:admin > /backup/grafana/alerts.json
            volumeMounts:
            - name: backup-volume
              mountPath: /backup
          volumes:
          - name: backup-volume
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-pvc
  namespace: logging
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: Service
metadata:
  name: backup-restore
  namespace: logging
spec:
  selector:
    app: backup-restore
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
