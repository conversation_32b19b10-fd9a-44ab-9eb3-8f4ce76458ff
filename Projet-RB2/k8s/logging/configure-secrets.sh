#!/bin/bash

# Configuration des secrets pour la sécurité
echo "Configuration des secrets de sécurité..."
read -p "Entrez la passphrase TLS: " TLS_PASSPHRASE
read -p "Entrez le mot de passe admin Kibana: " KIBANA_ADMIN_PASSWORD

TLS_PASSPHRASE_BASE64=$(echo -n "$TLS_PASSPHRASE" | base64)
KIBANA_ADMIN_PASSWORD_BASE64=$(echo -n "$KIBANA_ADMIN_PASSWORD" | base64)

# Configuration des secrets pour le monitoring externe
echo -e "\nConfiguration des secrets de monitoring externe..."
read -p "Entrez la clé API Datadog: " DATADOG_API_KEY
read -p "Entrez la clé de licence New Relic: " NEWRELIC_LICENSE_KEY
read -p "Entrez la clé API Grafana Cloud: " GRAFANA_CLOUD_API_KEY
read -p "Entrez le token bearer Prometheus: " PROMETHEUS_BEARER_TOKEN

DATADOG_API_KEY_BASE64=$(echo -n "$DATADOG_API_KEY" | base64)
NEWRELIC_LICENSE_KEY_BASE64=$(echo -n "$NEWRELIC_LICENSE_KEY" | base64)
GRAFANA_CLOUD_API_KEY_BASE64=$(echo -n "$GRAFANA_CLOUD_API_KEY" | base64)
PROMETHEUS_BEARER_TOKEN_BASE64=$(echo -n "$PROMETHEUS_BEARER_TOKEN" | base64)

# Création des secrets Kubernetes
cat << EOF | kubectl apply -f -
apiVersion: v1
kind: Secret
metadata:
  name: logging-security-credentials
  namespace: logging
type: Opaque
data:
  tls-passphrase: $TLS_PASSPHRASE_BASE64
  kibana-admin-password: $KIBANA_ADMIN_PASSWORD_BASE64
---
apiVersion: v1
kind: Secret
metadata:
  name: external-monitoring-credentials
  namespace: logging
type: Opaque
data:
  datadog-api-key: $DATADOG_API_KEY_BASE64
  newrelic-license-key: $NEWRELIC_LICENSE_KEY_BASE64
  grafana-cloud-api-key: $GRAFANA_CLOUD_API_KEY_BASE64
  prometheus-bearer-token: $PROMETHEUS_BEARER_TOKEN_BASE64
EOF

echo -e "\nSecrets configurés avec succès!"
