apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-index-lifecycle
  namespace: logging
data:
  index-lifecycle.json: |
    {
      "policy": {
        "phases": {
          "hot": {
            "min_age": "0ms",
            "actions": {
              "rollover": {
                "max_age": "7d",
                "max_size": "5gb"
              }
            }
          },
          "warm": {
            "min_age": "7d",
            "actions": {
              "allocate": {
                "number_of_replicas": 0
              },
              "forcemerge": {
                "max_num_segments": 1
              }
            }
          },
          "delete": {
            "min_age": "30d",
            "actions": {
              "delete": {}
            }
          }
        }
      }
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: elasticsearch-ilm-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 30
          # Create ILM policy
          curl -X PUT "http://elasticsearch:9200/_ilm/policy/logs-policy" \
            -H "Content-Type: application/json" \
            -d @/config/index-lifecycle.json
          
          # Create index template
          curl -X PUT "http://elasticsearch:9200/_template/logs-template" \
            -H "Content-Type: application/json" \
            -d '{
              "index_patterns": ["logstash-*"],
              "settings": {
                "index.lifecycle.name": "logs-policy",
                "index.lifecycle.rollover_alias": "logs"
              }
            }'
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: elasticsearch-index-lifecycle
      restartPolicy: Never
