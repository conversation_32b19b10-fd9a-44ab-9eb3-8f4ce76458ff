apiVersion: v1
kind: ConfigMap
metadata:
  name: external-monitoring-config
  namespace: logging
data:
  external-outputs.conf: |
    # Configuration Datadog
    <match kubernetes.**>
      @type datadog
      @id datadog_agent
      api_key "#{ENV['DATADOG_API_KEY']}"
      include_tag_key true
      dd_source fluentd
      dd_tags "env:#{ENV['DD_ENV']},service:#{ENV['DD_SERVICE']},version:#{ENV['DD_VERSION']}"
      dd_sourcecategory "#{ENV['DD_SOURCE_CATEGORY']}"
      # Ajout du tracing APM
      apm_enabled true
      trace_agent_url "#{ENV['DD_TRACE_AGENT_URL']}"
      # Ajout des métriques custom
      custom_metrics true
      metric_tags ["service", "env", "version"]
      <buffer>
        @type memory
        flush_interval 10s
        chunk_limit_size 5M
        retry_max_times 5
        retry_wait 1s
      </buffer>
    </match>

    # Configuration New Relic
    <match kubernetes.**>
      @type newrelic
      license_key "#{ENV['NEW_RELIC_LICENSE_KEY']}"
      base_uri "#{ENV['NEW_RELIC_LOG_API_URL']}"
      # Ajout du tracing distribué
      distributed_tracing true
      span_events_enabled true
      # Ajout des attributs personnalisés
      custom_attributes {
        "deployment": "#{ENV['DEPLOYMENT_NAME']}",
        "cluster": "#{ENV['CLUSTER_NAME']}",
        "region": "#{ENV['REGION']}"
      }
      <buffer>
        @type memory
        flush_interval 10s
        chunk_limit_size 5M
        retry_max_times 5
        retry_wait 1s
      </buffer>
    </match>

    # Configuration Grafana Cloud
    <match kubernetes.**>
      @type grafana_loki
      url "#{ENV['GRAFANA_CLOUD_URL']}"
      username "#{ENV['GRAFANA_CLOUD_USERNAME']}"
      password "#{ENV['GRAFANA_CLOUD_API_KEY']}"
      extra_labels {
        "env":"#{ENV['ENVIRONMENT']}", 
        "cluster":"#{ENV['CLUSTER_NAME']}",
        "service":"#{ENV['SERVICE_NAME']}",
        "version":"#{ENV['VERSION']}"
      }
      # Ajout de la compression
      compress true
      # Ajout du tenant ID pour multi-tenancy
      tenant "#{ENV['GRAFANA_TENANT_ID']}"
      <buffer>
        @type memory
        flush_interval 10s
        chunk_limit_size 5M
        retry_max_times 5
        retry_wait 1s
      </buffer>
    </match>

    # Configuration Prometheus Remote Write
    <match kubernetes.**>
      @type prometheus_remote_write
      url "#{ENV['PROMETHEUS_REMOTE_WRITE_URL']}"
      bearer_token_file /var/run/secrets/prometheus/token
      # Ajout des labels globaux
      global_labels {
        environment: "#{ENV['ENVIRONMENT']}",
        cluster: "#{ENV['CLUSTER_NAME']}",
        region: "#{ENV['REGION']}"
      }
      # Configuration de la compression
      remote_write_compression gzip
      # Configuration du batching
      batch_size 1000
      flush_interval 10s
      max_retry_count 5
      <buffer>
        @type memory
        flush_interval 10s
        chunk_limit_size 5M
        retry_max_times 5
        retry_wait 1s
      </buffer>
    </match>

    # Ajout de Elastic Cloud
    <match kubernetes.**>
      @type elasticsearch
      host "#{ENV['ELASTIC_CLOUD_HOST']}"
      port "#{ENV['ELASTIC_CLOUD_PORT']}"
      user "#{ENV['ELASTIC_CLOUD_USER']}"
      password "#{ENV['ELASTIC_CLOUD_PASSWORD']}"
      scheme https
      ssl_verify true
      # Configuration de l'index
      index_name "logs-#{ENV['ENVIRONMENT']}-#{ENV['SERVICE_NAME']}-%Y%m%d"
      # Ajout des métadonnées
      include_timestamp true
      <buffer>
        @type memory
        flush_interval 10s
        chunk_limit_size 5M
        retry_max_times 5
        retry_wait 1s
      </buffer>
    </match>

---
apiVersion: v1
kind: Secret
metadata:
  name: external-monitoring-credentials
  namespace: logging
type: Opaque
data:
  datadog-api-key: ZGF0YWRvZy1hcGkta2V5Cg==
  newrelic-license-key: bmV3cmVsaWMtbGljZW5zZS1rZXkK
  grafana-cloud-api-key: Z3JhZmFuYS1jbG91ZC1hcGkta2V5Cg==
  prometheus-bearer-token: cHJvbWV0aGV1cy1iZWFyZXItdG9rZW4K
  elastic-cloud-user: ZWxhc3RpYy1jbG91ZC11c2VyCg==
  elastic-cloud-password: ZWxhc3RpYy1jbG91ZC1wYXNzd29yZAo=
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: monitoring-proxy
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: monitoring-proxy
  template:
    metadata:
      labels:
        app: monitoring-proxy
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "24231"
    spec:
      containers:
      - name: monitoring-proxy
        image: fluent/fluentd-kubernetes-daemonset:v1.14.6-debian-elasticsearch7-1.1
        ports:
        - containerPort: 24224
          name: forward
        - containerPort: 24231
          name: metrics
        env:
        - name: DATADOG_API_KEY
          valueFrom:
            secretKeyRef:
              name: external-monitoring-credentials
              key: datadog-api-key
        - name: NEW_RELIC_LICENSE_KEY
          valueFrom:
            secretKeyRef:
              name: external-monitoring-credentials
              key: newrelic-license-key
        - name: GRAFANA_CLOUD_API_KEY
          valueFrom:
            secretKeyRef:
              name: external-monitoring-credentials
              key: grafana-cloud-api-key
        - name: ELASTIC_CLOUD_USER
          valueFrom:
            secretKeyRef:
              name: external-monitoring-credentials
              key: elastic-cloud-user
        - name: ELASTIC_CLOUD_PASSWORD
          valueFrom:
            secretKeyRef:
              name: external-monitoring-credentials
              key: elastic-cloud-password
        - name: DD_ENV
          value: "production"
        - name: DD_SERVICE
          value: "monitoring-proxy"
        - name: DD_VERSION
          value: "1.0.0"
        - name: ENVIRONMENT
          value: "production"
        - name: CLUSTER_NAME
          value: "main-cluster"
        - name: REGION
          value: "us-west-1"
        volumeMounts:
        - name: config
          mountPath: /fluentd/etc/external-outputs.conf
          subPath: external-outputs.conf
        - name: prometheus-token
          mountPath: /var/run/secrets/prometheus
          readOnly: true
        resources:
          limits:
            memory: 1Gi
            cpu: 500m
          requests:
            cpu: 200m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /metrics
            port: 24231
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /metrics
            port: 24231
          initialDelaySeconds: 5
          periodSeconds: 15
      volumes:
      - name: config
        configMap:
          name: external-monitoring-config
      - name: prometheus-token
        secret:
          secretName: external-monitoring-credentials
          items:
          - key: prometheus-bearer-token
            path: token
---
apiVersion: v1
kind: Service
metadata:
  name: monitoring-proxy
  namespace: logging
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "24231"
spec:
  selector:
    app: monitoring-proxy
  ports:
  - port: 24224
    name: forward
  - port: 24231
    name: metrics
  type: ClusterIP
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: monitoring-proxy
  namespace: logging
spec:
  selector:
    matchLabels:
      app: monitoring-proxy
  endpoints:
  - port: metrics
    interval: 30s
