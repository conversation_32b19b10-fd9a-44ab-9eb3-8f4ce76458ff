apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-advanced-alerts
  namespace: logging
data:
  advanced-alerts.ndjson: |
    {
      "type": "alert",
      "id": "api-latency",
      "attributes": {
        "name": "API Latency Alert",
        "tags": ["performance"],
        "schedule": { "interval": "1m" },
        "enabled": true,
        "params": {
          "conditions": {
            "array": [{
              "aggType": "avg",
              "field": "response_time",
              "operator": ">=",
              "value": 1000
            }]
          },
          "timeField": "@timestamp",
          "timeWindowSize": 5,
          "timeWindowUnit": "m"
        },
        "actions": [{
          "id": "slack-notification",
          "params": {
            "message": "High API latency detected: {{context.value}} ms"
          }
        }]
      }
    }
    {
      "type": "alert",
      "id": "error-spike",
      "attributes": {
        "name": "Error Rate Spike",
        "tags": ["errors"],
        "schedule": { "interval": "1m" },
        "enabled": true,
        "params": {
          "conditions": {
            "array": [{
              "aggType": "count",
              "field": "error_type",
              "operator": ">=",
              "value": 5
            }]
          },
          "timeField": "@timestamp",
          "timeWindowSize": 5,
          "timeWindowUnit": "m"
        },
        "actions": [{
          "id": "slack-notification",
          "params": {
            "message": "Error rate spike detected: {{context.value}} errors in 5 minutes"
          }
        }]
      }
    }
    {
      "type": "alert",
      "id": "user-activity-drop",
      "attributes": {
        "name": "User Activity Drop",
        "tags": ["activity"],
        "schedule": { "interval": "5m" },
        "enabled": true,
        "params": {
          "conditions": {
            "array": [{
              "aggType": "count",
              "field": "user_id",
              "operator": "<=",
              "value": 10
            }]
          },
          "timeField": "@timestamp",
          "timeWindowSize": 15,
          "timeWindowUnit": "m"
        },
        "actions": [{
          "id": "slack-notification",
          "params": {
            "message": "Low user activity detected: {{context.value}} active users in the last 15 minutes"
          }
        }]
      }
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-alerts-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: alerts-init
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 30
          curl -X POST "http://kibana:5601/api/alerts/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/advanced-alerts.ndjson
        volumeMounts:
        - name: config
          mountPath: /config/advanced-alerts.ndjson
          subPath: advanced-alerts.ndjson
      volumes:
      - name: config
        configMap:
          name: kibana-advanced-alerts
          items:
          - key: advanced-alerts.ndjson
            path: advanced-alerts.ndjson
      restartPolicy: Never
