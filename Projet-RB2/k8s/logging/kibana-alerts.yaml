apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-alerts
  namespace: logging
data:
  alerts.ndjson: |
    {
      "attributes": {
        "name": "High Error Rate Alert",
        "tags": ["monitoring", "errors"],
        "schedule": { "interval": "1m" },
        "enabled": true,
        "params": {
          "aggType": "count",
          "termSize": 5,
          "thresholdComparator": ">",
          "timeWindowSize": 5,
          "timeWindowUnit": "m",
          "threshold": [10],
          "groupBy": "kubernetes.namespace",
          "query": "level:error"
        },
        "consumer": "alerts",
        "rule_type_id": "metrics.alert.threshold",
        "notify_when": "onActionGroupChange",
        "actions": []
      },
      "references": [],
      "type": "alert",
      "id": "high-error-rate"
    }
    {
      "attributes": {
        "name": "Slow Response Time Alert",
        "tags": ["monitoring", "performance"],
        "schedule": { "interval": "1m" },
        "enabled": true,
        "params": {
          "aggType": "avg",
          "termSize": 5,
          "thresholdComparator": ">",
          "timeWindowSize": 5,
          "timeWindowUnit": "m",
          "threshold": [2000],
          "groupBy": "kubernetes.pod.name",
          "field": "response_time",
          "query": "response_time:>0"
        },
        "consumer": "alerts",
        "rule_type_id": "metrics.alert.threshold",
        "notify_when": "onActionGroupChange",
        "actions": []
      },
      "references": [],
      "type": "alert",
      "id": "slow-response-time"
    }
    {
      "attributes": {
        "name": "Pod Restart Alert",
        "tags": ["monitoring", "kubernetes"],
        "schedule": { "interval": "1m" },
        "enabled": true,
        "params": {
          "aggType": "count",
          "termSize": 5,
          "thresholdComparator": ">",
          "timeWindowSize": 5,
          "timeWindowUnit": "m",
          "threshold": [3],
          "groupBy": "kubernetes.pod.name",
          "query": "kubernetes.event.reason:Restarted"
        },
        "consumer": "alerts",
        "rule_type_id": "metrics.alert.threshold",
        "notify_when": "onActionGroupChange",
        "actions": []
      },
      "references": [],
      "type": "alert",
      "id": "pod-restart-alert"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-alerts-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 60
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/alerts.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-alerts
      restartPolicy: OnFailure
