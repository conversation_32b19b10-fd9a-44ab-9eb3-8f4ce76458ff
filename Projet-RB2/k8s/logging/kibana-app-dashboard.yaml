apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-app-dashboard
  namespace: logging
data:
  app-dashboard.ndjson: |
    {
      "type": "dashboard",
      "id": "app-performance",
      "attributes": {
        "title": "Application Performance Dashboard",
        "hits": 0,
        "description": "Vue complète des performances de l'application",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"api-response-times\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"API Response Times\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-distribution\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Error Distribution\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":48,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"user-activity\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"User Activity\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"
        }
      }
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-app-dashboard-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: dashboard-init
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 30
          # Import visualizations
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/app-visualizations.ndjson
          
          # Import dashboard
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/app-dashboard.ndjson
        volumeMounts:
        - name: config-vis
          mountPath: /config/app-visualizations.ndjson
          subPath: app-visualizations.ndjson
        - name: config-dash
          mountPath: /config/app-dashboard.ndjson
          subPath: app-dashboard.ndjson
      volumes:
      - name: config-vis
        configMap:
          name: kibana-app-visualizations
          items:
          - key: app-visualizations.ndjson
            path: app-visualizations.ndjson
      - name: config-dash
        configMap:
          name: kibana-app-dashboard
          items:
          - key: app-dashboard.ndjson
            path: app-dashboard.ndjson
      restartPolicy: Never
