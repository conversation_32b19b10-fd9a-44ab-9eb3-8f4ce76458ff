apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-error-tracking
  namespace: logging
data:
  error-tracking.ndjson: |
    {
      "attributes": {
        "title": "Error Tracking Dashboard",
        "hits": 0,
        "description": "Comprehensive error tracking and analysis",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-timeline\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Error Timeline\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-types\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Error Types Distribution\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-stack-traces\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"Error Stack Traces\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"4\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-correlation\",
            \"panelIndex\":\"4\",
            \"embeddableConfig\":{
              \"title\":\"Error Correlation Analysis\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"language\":\"kuery\",\"query\":\"log.level:error OR level:error\"},\"filter\":[]}"
        }
      },
      "id": "error-tracking",
      "type": "dashboard"
    }
    {
      "attributes": {
        "title": "Error Analysis Dashboard",
        "hits": 0,
        "description": "Detailed error analysis and patterns",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":48,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-patterns\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Common Error Patterns\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-impact\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Error Impact Analysis\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"error-resolution-time\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"Error Resolution Time\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"language\":\"kuery\",\"query\":\"log.level:error OR level:error\"},\"filter\":[]}"
        }
      },
      "id": "error-analysis",
      "type": "dashboard"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-error-tracking-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 150
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/error-tracking.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-error-tracking
      restartPolicy: OnFailure
