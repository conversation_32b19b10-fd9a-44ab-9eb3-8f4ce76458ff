apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-index-patterns
  namespace: logging
data:
  index-patterns.ndjson: |
    {"attributes":{"title":"logstash-*","timeFieldName":"@timestamp"},"id":"logstash-*","type":"index-pattern"}
    {"attributes":{"title":"kubernetes-*","timeFieldName":"@timestamp"},"id":"kubernetes-*","type":"index-pattern"}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-index-patterns-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 30
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/index-patterns.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-index-patterns
      restartPolicy: OnFailure
