apiVersion: v1
kind: Secret
metadata:
  name: slack-webhook
  namespace: logging
type: Opaque
data:
  webhook-url: ${SLACK_WEBHOOK_URL_BASE64}  # À remplacer par votre URL Slack encodée en base64
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-notification-config
  namespace: logging
data:
  notification-config.json: |
    {
      "name": "slack-notification",
      "type": "slack",
      "secrets": {
        "webhookUrl": {
          "fromSecret": {
            "name": "slack-webhook",
            "key": "webhook-url"
          }
        }
      },
      "config": {
        "throttle": "10m",
        "useMarkdown": true
      }
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-notification-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 30
          # Create notification channel
          curl -X POST "http://kibana:5601/api/actions/connector" \
            -H "kbn-xsrf: true" \
            -H "Content-Type: application/json" \
            --data @/config/notification-config.json
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-notification-config
      restartPolicy: Never
