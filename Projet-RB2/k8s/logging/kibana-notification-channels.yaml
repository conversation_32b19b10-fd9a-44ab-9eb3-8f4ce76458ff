apiVersion: v1
kind: Secret
metadata:
  name: notification-secrets
  namespace: logging
type: Opaque
stringData:
  slack-webhook: "https://hooks.slack.com/services/your-webhook-url"
  email-password: "your-email-password"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-notification-channels
  namespace: logging
data:
  notification-channels.ndjson: |
    {
      "attributes": {
        "name": "Slack Alert Channel",
        "connector_type_id": ".slack",
        "config": {
          "webhookUrl": "https://hooks.slack.com/services/your-webhook-url",
          "channel": "#monitoring-alerts"
        },
        "secrets": {}
      },
      "id": "slack-channel",
      "type": "action"
    }
    {
      "attributes": {
        "name": "Email Alert Channel",
        "connector_type_id": ".email",
        "config": {
          "from": "<EMAIL>",
          "host": "smtp.yourdomain.com",
          "port": "587",
          "secure": true
        },
        "secrets": {
          "user": "<EMAIL>",
          "password": "your-email-password"
        }
      },
      "id": "email-channel",
      "type": "action"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-notification-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 90
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/notification-channels.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-notification-channels
      restartPolicy: OnFailure
