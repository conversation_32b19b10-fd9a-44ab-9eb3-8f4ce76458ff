apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-service-dashboards
  namespace: logging
data:
  service-dashboards.ndjson: |
    {
      "attributes": {
        "title": "Frontend Service Dashboard",
        "hits": 0,
        "description": "Frontend monitoring dashboard",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"frontend-response-times\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Frontend Response Times\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"frontend-errors\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Frontend Error Distribution\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":48,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"frontend-user-activity\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"User Activity\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"language\":\"kuery\",\"query\":\"kubernetes.labels.app=frontend\"},\"filter\":[]}"
        }
      },
      "id": "frontend-dashboard",
      "type": "dashboard"
    }
    {
      "attributes": {
        "title": "Backend API Dashboard",
        "hits": 0,
        "description": "Backend API monitoring dashboard",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"api-endpoints\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"API Endpoint Usage\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"api-response-times\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"API Response Times\"
            }
          },
          {
            \"gridData\": {\"x\":0,\"y\":15,\"w\":24,\"h\":15,\"i\":\"3\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"api-errors\",
            \"panelIndex\":\"3\",
            \"embeddableConfig\":{
              \"title\":\"API Errors\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":15,\"w\":24,\"h\":15,\"i\":\"4\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"api-status-codes\",
            \"panelIndex\":\"4\",
            \"embeddableConfig\":{
              \"title\":\"API Status Codes\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"language\":\"kuery\",\"query\":\"kubernetes.labels.app=backend\"},\"filter\":[]}"
        }
      },
      "id": "backend-dashboard",
      "type": "dashboard"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-service-dashboards-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: curl
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 105
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/service-dashboards.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-service-dashboards
      restartPolicy: OnFailure
