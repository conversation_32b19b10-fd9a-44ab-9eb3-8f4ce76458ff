apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-system-dashboard
  namespace: logging
data:
  system-dashboard.ndjson: |
    {
      "attributes": {
        "title": "System Metrics Dashboard",
        "hits": 0,
        "description": "Vue d'ensemble des métriques système",
        "panelsJSON": "[
          {
            \"gridData\": {\"x\":0,\"y\":0,\"w\":24,\"h\":15,\"i\":\"1\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"pod-restarts\",
            \"panelIndex\":\"1\",
            \"embeddableConfig\":{
              \"title\":\"Pod Restarts\"
            }
          },
          {
            \"gridData\": {\"x\":24,\"y\":0,\"w\":24,\"h\":15,\"i\":\"2\"},
            \"version\":\"7.17.3\",
            \"type\":\"visualization\",
            \"id\":\"memory-usage\",
            \"panelIndex\":\"2\",
            \"embeddableConfig\":{
              \"title\":\"Memory Usage by Pod\"
            }
          }
        ]",
        "optionsJSON": "{\"hidePanelTitles\":false,\"useMargins\":true}",
        "version": 1,
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"query\":\"\",\"language\":\"kuery\"},\"filter\":[]}"
        }
      },
      "type": "dashboard",
      "id": "system-metrics-dashboard"
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-system-dashboard-init
  namespace: logging
spec:
  template:
    spec:
      containers:
      - name: kibana-system-dashboard-init
        image: curlimages/curl
        command:
        - sh
        - -c
        - |
          sleep 30
          curl -X POST "http://kibana:5601/api/saved_objects/_import" \
            -H "kbn-xsrf: true" \
            --form file=@/config/system-dashboard.ndjson
        volumeMounts:
        - name: config
          mountPath: /config
      volumes:
      - name: config
        configMap:
          name: kibana-system-dashboard
      restartPolicy: Never
