apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - namespace.yaml
  - elasticsearch-config.yaml
  - fluentd-config.yaml
  - kibana-config.yaml
  - kibana-index-patterns.yaml
  - kibana-dashboards.yaml
  - kibana-alerts.yaml
  - kibana-visualizations.yaml
  - kibana-notification-channels.yaml
  - kibana-service-dashboards.yaml
  - kibana-advanced-metrics.yaml
  - kibana-advanced-alerts.yaml
  - kibana-error-tracking.yaml
  - kibana-ingress.yaml
