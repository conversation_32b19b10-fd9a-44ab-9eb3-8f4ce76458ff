apiVersion: v1
kind: ConfigMap
metadata:
  name: log-analysis-config
  namespace: logging
data:
  analysis-rules.conf: |
    # Règles d'analyse des logs
    rules:
      # Détection des anomalies
      - name: response_time_anomaly
        type: anomaly_detection
        metric: http_request_duration_seconds
        algorithm: ewma
        parameters:
          alpha: 0.3
          threshold: 2.0
          training_period: 1h

      - name: error_pattern_analysis
        type: pattern_recognition
        pattern: (?P<error_type>\w+)Exception|Error:\s+(?P<message>.+)
        aggregation:
          window: 5m
          group_by: [error_type]
          metrics: [count, unique_count]

      # Analyse de performance
      - name: slow_query_analysis
        type: performance
        filters:
          - type: duration
            threshold: 1s
          - type: query_type
            values: [SELECT, INSERT, UPDATE, DELETE]
        metrics:
          - type: percentile
            values: [50, 90, 95, 99]
          - type: histogram
            buckets: [0.1, 0.5, 1, 2, 5]

      # Analyse de sécurité
      - name: security_event_analysis
        type: security
        patterns:
          - name: failed_login
            pattern: Failed login attempt.*user=(?P<username>\w+)
          - name: permission_denied
            pattern: Permission denied.*resource=(?P<resource>\S+)
          - name: suspicious_activity
            pattern: Suspicious activity detected.*ip=(?P<ip>\S+)
        actions:
          - type: alert
            severity: high
          - type: block_ip
            duration: 1h

  visualization-templates.conf: |
    # Templates de visualisation
    visualizations:
      # Dashboard de performance
      - name: performance_overview
        type: dashboard
        panels:
          - name: request_latency
            type: line_graph
            metric: http_request_duration_seconds
            aggregation: avg
            by: [service, endpoint]
            interval: 1m

          - name: error_distribution
            type: pie_chart
            metric: error_count
            by: [error_type]
            interval: 5m

          - name: throughput
            type: bar_chart
            metric: request_count
            by: [service]
            interval: 1m

      # Dashboard de sécurité
      - name: security_overview
        type: dashboard
        panels:
          - name: failed_logins
            type: counter
            metric: failed_login_count
            by: [username, ip]
            interval: 5m

          - name: blocked_ips
            type: table
            metric: blocked_ip_count
            by: [ip, reason]
            interval: 15m

          - name: security_events
            type: timeline
            metric: security_event_count
            by: [event_type]
            interval: 1m

      # Dashboard système
      - name: system_health
        type: dashboard
        panels:
          - name: node_resources
            type: gauge
            metrics:
              - cpu_usage
              - memory_usage
              - disk_usage
            by: [node]
            interval: 1m

          - name: container_stats
            type: heatmap
            metric: container_resource_usage
            by: [container, resource_type]
            interval: 5m

  maintenance-automation.conf: |
    # Automatisation de la maintenance
    tasks:
      # Rotation des logs
      - name: log_rotation
        schedule: "0 0 * * *"  # Tous les jours à minuit
        type: rotation
        parameters:
          max_age: 30d
          max_size: 50G
          compress: true
          indexes:
            - pattern: logstash-*
              retention: 7d
            - pattern: metrics-*
              retention: 30d

      # Optimisation des index
      - name: index_optimization
        schedule: "0 1 * * *"  # Tous les jours à 1h du matin
        type: optimization
        parameters:
          target_segments: 1
          max_num_segments: 1
          indexes:
            - pattern: logstash-*
            - pattern: metrics-*

      # Sauvegarde des données
      - name: log_backup
        schedule: "0 2 * * *"  # Tous les jours à 2h du matin
        type: backup
        parameters:
          destination: s3://logs-backup
          retention: 90d
          compress: true
          indexes:
            - pattern: logstash-*
            - pattern: metrics-*

      # Nettoyage des données
      - name: data_cleanup
        schedule: "0 3 * * *"  # Tous les jours à 3h du matin
        type: cleanup
        parameters:
          rules:
            - type: age
              older_than: 90d
              indexes: [logstash-*]
            - type: size
              max_size: 100G
              indexes: [metrics-*]
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: log-analyzer
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: log-analyzer
  template:
    metadata:
      labels:
        app: log-analyzer
    spec:
      containers:
      - name: analyzer
        image: custom/log-analyzer:latest
        env:
        - name: ELASTICSEARCH_HOST
          value: elasticsearch
        - name: ELASTICSEARCH_PORT
          value: "9200"
        volumeMounts:
        - name: config
          mountPath: /etc/log-analyzer
        resources:
          limits:
            memory: 2Gi
            cpu: 1000m
          requests:
            memory: 1Gi
            cpu: 500m
      volumes:
      - name: config
        configMap:
          name: log-analysis-config
---
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: log-maintenance
  namespace: logging
spec:
  schedule: "0 0 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: maintenance
            image: custom/log-maintenance:latest
            env:
            - name: ELASTICSEARCH_HOST
              value: elasticsearch
            volumeMounts:
            - name: config
              mountPath: /etc/log-maintenance
            resources:
              limits:
                memory: 1Gi
                cpu: 500m
              requests:
                memory: 512Mi
                cpu: 250m
          volumes:
          - name: config
            configMap:
              name: log-analysis-config
          restartPolicy: OnFailure
