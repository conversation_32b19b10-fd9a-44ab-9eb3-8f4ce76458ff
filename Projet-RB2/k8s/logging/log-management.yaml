apiVersion: v1
kind: ConfigMap
metadata:
  name: log-management-config
  namespace: logging
data:
  log-lifecycle.conf: |
    # Configuration de la rotation des logs
    rotation:
      # Rotation basée sur la taille
      size_based:
        max_size: "1GB"
        max_files: 5
        compress: true
        compress_format: "gz"
        
      # Rotation basée sur le temps
      time_based:
        frequency: "daily"
        max_age: "30d"
        max_history: 30
        
    # Configuration de la rétention
    retention:
      # Politique de rétention par environnement
      development:
        max_age: "7d"
        max_size: "5GB"
      staging:
        max_age: "14d"
        max_size: "10GB"
      production:
        max_age: "90d"
        max_size: "50GB"
        
      # Politique de rétention par type de log
      application_logs:
        max_age: "30d"
        priority: "high"
        backup_enabled: true
        backup_schedule: "0 0 * * *"
      system_logs:
        max_age: "15d"
        priority: "medium"
        backup_enabled: true
        backup_schedule: "0 0 * * 0"
      audit_logs:
        max_age: "365d"
        priority: "critical"
        backup_enabled: true
        backup_schedule: "0 0 * * *"
        encryption_enabled: true
        
    # Configuration de l'archivage
    archival:
      enabled: true
      schedule: "0 0 * * 0"  # Tous les dimanches à minuit
      target: "s3://my-logging-bucket/archives"
      format: "tar.gz"
      retention: "1y"
      compression_level: 9
      encryption:
        enabled: true
        algorithm: "AES-256"
      lifecycle_rules:
        transition_glacier: "180d"
        expire: "730d"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: performance-optimization
  namespace: logging
data:
  elasticsearch-optimization.conf: |
    # Configuration de l'optimisation Elasticsearch
    indices:
      number_of_shards: 5
      number_of_replicas: 1
      refresh_interval: "30s"
      
      # Configuration des segments
      merge:
        scheduler:
          max_thread_count: 4
          max_merge_count: 8
        policy:
          segments_per_tier: 10
          max_merged_segment: "5gb"
          
      # Configuration du cache
      cache:
        field:
          max_size: "50mb"
          expire_after: "1h"
        query:
          max_size: "25mb"
          expire_after: "30m"
        request:
          max_size: "30mb"
          expire_after: "45m"
          
      # Configuration des requêtes
      search:
        slowlog:
          threshold:
            query:
              warn: "10s"
              info: "5s"
            fetch:
              warn: "1s"
              info: "500ms"
        circuit_breaker:
          request:
            limit: "60%"
            overhead: 2.0
          fielddata:
            limit: "40%"
            overhead: 1.5

  fluentd-optimization.conf: |
    # Configuration de l'optimisation Fluentd
    system:
      workers: 4
      root_dir: "/var/log/fluentd"
      
    buffer:
      type: "file"
      path: "/var/log/fluentd/buffers"
      flush_interval: "60s"
      flush_thread_count: 4
      chunk_limit_size: "256m"
      queue_limit_length: 32
      overflow_action: "block"
      retry_max_times: 10
      retry_wait: "1s"
      retry_exponential_backoff: true
      
    parser:
      enable_parser_cache: true
      parser_cache_size: 1000
      multiline_flush_interval: "5s"
      
    formatter:
      enable_formatter_cache: true
      formatter_cache_size: 1000
      time_format: "iso8601"

    monitoring:
      prometheus_export:
        enable: true
        port: 24231
      statsd:
        enable: true
        host: "statsd"
        port: 8125
        prefix: "fluentd"

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: log-cleanup
  namespace: logging
spec:
  schedule: "0 1 * * *"  # Tous les jours à 1h du matin
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 5
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          annotations:
            prometheus.io/scrape: "true"
            prometheus.io/port: "9091"
        spec:
          containers:
          - name: cleanup
            image: elasticsearch:7.17.3
            command:
            - /bin/bash
            - -c
            - |
              # Nettoyage des vieux indices
              curator_cli --host elasticsearch delete_indices \
                --filter_list '[
                  {"filtertype":"age","source":"creation_date","direction":"older","unit":"days","unit_count":30},
                  {"filtertype":"pattern","kind":"prefix","value":"logstash-"}
                ]'
              
              # Forcer le merge des segments
              curator_cli --host elasticsearch forcemerge \
                --filter_list '[
                  {"filtertype":"pattern","kind":"prefix","value":"logstash-"}
                ]' \
                --max_num_segments 1

              # Snapshot des indices importants
              curator_cli --host elasticsearch snapshot \
                --repository daily_backup \
                --name "backup-$(date +%Y%m%d)" \
                --filter_list '[
                  {"filtertype":"pattern","kind":"prefix","value":"logstash-"},
                  {"filtertype":"age","source":"creation_date","direction":"younger","unit":"days","unit_count":1}
                ]'
            resources:
              limits:
                memory: "1Gi"
                cpu: "500m"
              requests:
                memory: "500Mi"
                cpu: "250m"
            volumeMounts:
            - name: config-volume
              mountPath: /usr/share/elasticsearch/config
            - name: backup-volume
              mountPath: /backup
          volumes:
          - name: config-volume
            configMap:
              name: performance-optimization
          - name: backup-volume
            persistentVolumeClaim:
              claimName: log-backup-pvc
          restartPolicy: OnFailure

---
apiVersion: v1
kind: Service
metadata:
  name: log-management
  namespace: logging
  labels:
    app: log-management
    component: management
spec:
  selector:
    app: log-management
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 9091
    targetPort: 9091
    name: metrics
  type: ClusterIP
