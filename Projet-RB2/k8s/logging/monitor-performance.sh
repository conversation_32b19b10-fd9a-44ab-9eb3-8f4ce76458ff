#!/bin/bash

# Couleurs pour le formatage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Fonction pour vérifier l'état d'Elasticsearch
check_elasticsearch() {
    echo -e "${YELLOW}Vérification de l'état d'Elasticsearch...${NC}"
    
    # Vérification de la santé du cluster
    health=$(curl -s "elasticsearch:9200/_cluster/health" | jq -r '.status')
    if [ "$health" = "green" ]; then
        echo -e "${GREEN}État du cluster: $health${NC}"
    else
        echo -e "${RED}État du cluster: $health${NC}"
    fi
    
    # Vérification des indices
    echo -e "\n${YELLOW}État des indices:${NC}"
    curl -s "elasticsearch:9200/_cat/indices?v"
    
    # Vérification de l'utilisation du disque
    echo -e "\n${YELLOW}Utilisation du disque:${NC}"
    curl -s "elasticsearch:9200/_cat/allocation?v"
    
    # Vérification des segments
    echo -e "\n${YELLOW}État des segments:${NC}"
    curl -s "elasticsearch:9200/_cat/segments?v"
}

# Fonction pour vérifier l'état de Fluentd
check_fluentd() {
    echo -e "\n${YELLOW}Vérification de l'état de Fluentd...${NC}"
    
    # Vérification du statut du pod Fluentd
    kubectl get pods -n logging -l app=fluentd -o wide
    
    # Vérification des logs récents
    echo -e "\n${YELLOW}Logs récents de Fluentd:${NC}"
    kubectl logs -n logging -l app=fluentd --tail=50
}

# Fonction pour optimiser les performances
optimize_performance() {
    echo -e "\n${YELLOW}Optimisation des performances...${NC}"
    
    # Forcer le merge des segments
    echo "Fusion des segments..."
    curl -X POST "elasticsearch:9200/_forcemerge?max_num_segments=1"
    
    # Vider le cache
    echo "Nettoyage du cache..."
    curl -X POST "elasticsearch:9200/_cache/clear"
    
    # Rafraîchir les indices
    echo "Rafraîchissement des indices..."
    curl -X POST "elasticsearch:9200/_refresh"
}

# Menu principal
while true; do
    echo -e "\n${GREEN}=== Moniteur de Performance des Logs ===${NC}"
    echo "1. Vérifier l'état d'Elasticsearch"
    echo "2. Vérifier l'état de Fluentd"
    echo "3. Optimiser les performances"
    echo "4. Tout vérifier"
    echo "5. Quitter"
    
    read -p "Choisissez une option (1-5): " choice
    
    case $choice in
        1)
            check_elasticsearch
            ;;
        2)
            check_fluentd
            ;;
        3)
            optimize_performance
            ;;
        4)
            check_elasticsearch
            check_fluentd
            ;;
        5)
            echo -e "${GREEN}Au revoir!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Option invalide${NC}"
            ;;
    esac
done
