apiVersion: v1
kind: ConfigMap
metadata:
  name: security-hardening
  namespace: logging
data:
  network-policy.yaml: |
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: logging-network-policy
      namespace: logging
    spec:
      podSelector:
        matchLabels:
          app: elasticsearch
      policyTypes:
      - Ingress
      - Egress
      ingress:
      - from:
        - podSelector:
            matchLabels:
              app: fluentd
        - podSelector:
            matchLabels:
              app: kibana
        ports:
        - protocol: TCP
          port: 9200
        - protocol: TCP
          port: 9300
      egress:
      - to:
        - podSelector:
            matchLabels:
              app: kibana
        ports:
        - protocol: TCP
          port: 5601

  audit-policy.yaml: |
    apiVersion: audit.k8s.io/v1
    kind: Policy
    rules:
    - level: Metadata
      resources:
      - group: ""
        resources: ["pods", "services", "configmaps"]
    - level: Request
      resources:
      - group: "apps"
        resources: ["deployments", "statefulsets"]
    - level: RequestResponse
      resources:
      - group: ""
        resources: ["secrets"]
      omitStages:
      - RequestReceived

  encryption-config.yaml: |
    apiVersion: apiserver.config.k8s.io/v1
    kind: EncryptionConfiguration
    resources:
    - resources:
      - secrets
      providers:
      - aescbc:
          keys:
          - name: key1
            secret: ${ENCRYPTION_KEY}
      - identity: {}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-scripts
  namespace: logging
data:
  generate-certs.sh: |
    #!/bin/bash
    
    # Génération des certificats pour Elasticsearch
    openssl req -x509 -nodes -days 365 -newkey rsa:4096 \
      -keyout elastic-certificate.key \
      -out elastic-certificate.crt \
      -subj "/CN=elasticsearch/O=logging"
    
    # Création du keystore
    openssl pkcs12 -export \
      -in elastic-certificate.crt \
      -inkey elastic-certificate.key \
      -out elastic-certificates.p12 \
      -name "elasticsearch" \
      -password pass:${KEYSTORE_PASSWORD}

  security-audit.sh: |
    #!/bin/bash
    
    # Audit de sécurité
    echo "Démarrage de l'audit de sécurité..."
    
    # Vérification des pods non sécurisés
    echo "Vérification des pods privilégiés..."
    kubectl get pods -n logging -o json | jq '.items[] | select(.spec.containers[].securityContext.privileged == true)'
    
    # Vérification des secrets
    echo "Vérification des secrets..."
    kubectl get secrets -n logging -o json | jq '.items[] | select(.type != "kubernetes.io/service-account-token")'
    
    # Vérification des network policies
    echo "Vérification des network policies..."
    kubectl get networkpolicies -n logging
    
    # Vérification des RBAC
    echo "Vérification des RBAC..."
    kubectl get roles,clusterroles -n logging
    kubectl get rolebindings,clusterrolebindings -n logging

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: security-audit
  namespace: logging
spec:
  schedule: "0 2 * * *"  # Tous les jours à 2h du matin
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: security-audit
            image: bitnami/kubectl:latest
            command:
            - /bin/bash
            - -c
            - /scripts/security-audit.sh
            volumeMounts:
            - name: security-scripts
              mountPath: /scripts
          restartPolicy: OnFailure
          volumes:
          - name: security-scripts
            configMap:
              name: security-scripts
              defaultMode: 0755

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: logging-security
  namespace: logging
rules:
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments", "statefulsets"]
  verbs: ["get", "list"]
- apiGroups: ["batch"]
  resources: ["jobs", "cronjobs"]
  verbs: ["get", "list", "create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: logging-security-binding
  namespace: logging
subjects:
- kind: ServiceAccount
  name: logging-security
  namespace: logging
roleRef:
  kind: Role
  name: logging-security
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: logging-security
  namespace: logging
