apiVersion: monitoring.coreos.com/v1
kind: AlertmanagerConfig
metadata:
  name: retreatandbe-alerts
  namespace: monitoring
spec:
  route:
    receiver: 'slack'
    groupBy: ['alertname', 'service']
    groupWait: 30s
    groupInterval: 5m
    repeatInterval: 4h
    routes:
    - matchers:
      - name: severity
        value: critical
      receiver: 'pagerduty'
      continue: true
    - matchers:
      - name: severity
        value: warning
      receiver: 'slack'

  receivers:
  - name: 'slack'
    slackConfigs:
    - apiURL:
        key: SLACK_WEBHOOK_URL
        name: alertmanager-slack
      channel: '#alerts'
      title: '{{ .GroupLabels.alertname }}'
      text: >-
        {{ range .Alerts }}
        *Alert:* {{ .Labels.alertname }}
        *Service:* {{ .Labels.service }}
        *Severity:* {{ .Labels.severity }}
        *Description:* {{ .Annotations.description }}
        *Value:* {{ .Annotations.value }}
        {{ end }}
      sendResolved: true

  - name: 'pagerduty'
    pagerdutyConfigs:
    - serviceKey:
        key: PAGERDUTY_ROUTING_KEY
        name: alertmanager-pagerduty
      description: >-
        {{ range .Alerts }}
        Alert: {{ .Labels.alertname }}
        Service: {{ .Labels.service }}
        Severity: {{ .Labels.severity }}
        Description: {{ .Annotations.description }}
        Value: {{ .Annotations.value }}
        {{ end }}
      severity: 'critical'
      class: '{{ .CommonLabels.service }}'
      group: '{{ .CommonLabels.cluster }}'
      details:
        summary: '{{ .CommonAnnotations.summary }}'
        description: '{{ .CommonAnnotations.description }}'
        severity: '{{ .CommonLabels.severity }}'
      sendResolved: true

  templates:
  - '/etc/alertmanager/config/*.tmpl'

  inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']

  secret:
    name: alertmanager-config
    key: alertmanager.yaml

  global:
    resolve_timeout: 5m
