apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m

    route:
      group_by: ['alertname', 'service']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 4h
      receiver: 'slack'
      routes:
      - match:
          severity: critical
        receiver: 'pagerduty'
      - match:
          severity: warning
        receiver: 'slack'

    receivers:
    - name: 'slack'
      slack_configs:
      - channel: '#alerts'
        api_url: 'https://hooks.slack.com/services/your-webhook-url'
        title: '{{ .GroupLabels.alertname }}'
        text: >-
          {{ range .Alerts }}
          *Alert:* {{ .Labels.alertname }}
          *Service:* {{ .Labels.service }}
          *Severity:* {{ .Labels.severity }}
          *Description:* {{ .Annotations.description }}
          *Value:* {{ .Annotations.value }}
          {{ end }}

    - name: 'pagerduty'
      pagerduty_configs:
      - routing_key: '9fc50c7bd2374305c10cd257cb28f455'
        description: >-
          {{ range .Alerts }}
          Alert: {{ .Labels.alertname }}
          Service: {{ .Labels.service }}
          Severity: {{ .Labels.severity }}
          Description: {{ .Annotations.description }}
          Value: {{ .Annotations.value }}
          {{ end }}
