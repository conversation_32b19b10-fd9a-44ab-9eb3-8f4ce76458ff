apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: blockchain-metrics
  namespace: monitoring
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: blockchain-metrics
  namespaceSelector:
    matchNames:
      - default
  endpoints:
    - port: metrics
      interval: 15s
      path: /metrics
---
apiVersion: v1
kind: Service
metadata:
  name: blockchain-metrics
  namespace: default
  labels:
    app: blockchain-metrics
spec:
  ports:
    - name: metrics
      port: 9090
      targetPort: metrics
  selector:
    app: blockchain-metrics
