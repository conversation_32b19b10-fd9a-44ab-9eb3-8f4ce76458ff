apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: compare-insurance-rules
  namespace: monitoring
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
    - name: compare-insurance.rules
      rules:
        # Alertes de performance
        - alert: CompareInsuranceHighLatency
          expr: |
            histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service="compare-insurance"}[5m])) by (le, endpoint)) > 2
          for: 5m
          labels:
            severity: warning
            service: compare-insurance
          annotations:
            description: "95th percentile latency is above 2 seconds for endpoint {{ $labels.endpoint }}"
            value: "{{ $value }}s"

        - alert: CompareInsuranceErrorRate
          expr: |
            sum(rate(http_requests_total{service="compare-insurance",status=~"5.."}[5m])) by (endpoint)
            /
            sum(rate(http_requests_total{service="compare-insurance"}[5m])) by (endpoint)
            * 100 > 5
          for: 5m
          labels:
            severity: warning
            service: compare-insurance
          annotations:
            description: "Error rate is above 5% for endpoint {{ $labels.endpoint }}"
            value: "{{ $value }}%"

        # Alertes business
        - alert: HighComparisonFailureRate
          expr: |
            sum(rate(insurance_comparison_failed_total[5m])) by (insurance_type)
            /
            sum(rate(insurance_comparison_total[5m])) by (insurance_type)
            * 100 > 10
          for: 5m
          labels:
            severity: warning
            service: compare-insurance
          annotations:
            description: "Comparison failure rate is above 10% for insurance type {{ $labels.insurance_type }}"
            value: "{{ $value }}%"

        - alert: LowQuoteCount
          expr: |
            histogram_quantile(0.50, sum(rate(insurance_quotes_returned_bucket[5m])) by (le, insurance_type)) < 3
          for: 5m
          labels:
            severity: warning
            service: compare-insurance
          annotations:
            description: "Median number of quotes is below 3 for insurance type {{ $labels.insurance_type }}"
            value: "{{ $value }} quotes"

        - alert: SlowComparisons
          expr: |
            histogram_quantile(0.95, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type)) > 5
          for: 5m
          labels:
            severity: warning
            service: compare-insurance
          annotations:
            description: "95th percentile comparison time is above 5 seconds for insurance type {{ $labels.insurance_type }}"
            value: "{{ $value }}s"

        # Alertes business critiques
        - alert: ComparisonServiceDegraded
          expr: |
            sum(rate(insurance_comparison_failed_total[5m])) by (insurance_type)
            /
            sum(rate(insurance_comparison_total[5m])) by (insurance_type)
            * 100 > 25
          for: 5m
          labels:
            severity: critical
            service: compare-insurance
          annotations:
            description: "Critical: Comparison failure rate is above 25% for insurance type {{ $labels.insurance_type }}"
            value: "{{ $value }}%"

        - alert: LowPurchaseConversion
          expr: |
            sum(rate(insurance_purchase_success_total[1h])) by (insurance_type)
            /
            sum(rate(insurance_comparison_total[1h])) by (insurance_type)
            * 100 < 5
          for: 1h
          labels:
            severity: warning
            service: compare-insurance
          annotations:
            description: "Purchase conversion rate is below 5% for insurance type {{ $labels.insurance_type }}"
            value: "{{ $value }}%"
