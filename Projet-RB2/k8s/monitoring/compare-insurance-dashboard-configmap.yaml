apiVersion: v1
kind: ConfigMap
metadata:
  name: compare-insurance-grafana-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "true"
data:
  compare-insurance-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "title": "Service Health",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Request Rate",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(http_requests_total{service=\"compare-insurance\"}[5m])) by (endpoint)",
                  "legendFormat": "{{endpoint}}"
                }
              ]
            },
            {
              "title": "Error Rate",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(http_requests_total{service=\"compare-insurance\",status=~\"5..\"}[5m])) by (endpoint) / sum(rate(http_requests_total{service=\"compare-insurance\"}[5m])) by (endpoint) * 100",
                  "legendFormat": "{{endpoint}}"
                }
              ]
            }
          ]
        },
        {
          "title": "Comparison Performance",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Comparison Duration",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
              "targets": [
                {
                  "expr": "histogram_quantile(0.95, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type))",
                  "legendFormat": "p95 - {{insurance_type}}"
                },
                {
                  "expr": "histogram_quantile(0.50, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type))",
                  "legendFormat": "p50 - {{insurance_type}}"
                }
              ]
            },
            {
              "title": "Comparison Success Rate",
              "type": "gauge",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
              "targets": [
                {
                  "expr": "(1 - sum(rate(insurance_comparison_failed_total[5m])) / sum(rate(insurance_comparison_total[5m]))) * 100",
                  "legendFormat": "Success Rate"
                }
              ],
              "options": {
                "fieldOptions": {
                  "min": 0,
                  "max": 100,
                  "thresholds": [
                    { "value": 75, "color": "red" },
                    { "value": 90, "color": "yellow" },
                    { "value": 95, "color": "green" }
                  ]
                }
              }
            }
          ]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": ["compare-insurance", "monitoring"],
      "templating": {
        "list": [
          {
            "name": "insurance_type",
            "type": "query",
            "query": "label_values(insurance_comparison_total, insurance_type)",
            "refresh": 2
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timezone": "browser",
      "title": "Compare Insurance Dashboard",
      "uid": "compare-insurance",
      "version": 1
    }
