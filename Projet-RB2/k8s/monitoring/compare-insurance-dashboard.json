{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "Service Health", "type": "row", "collapsed": false, "panels": [{"title": "Request Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum(rate(http_requests_total{service=\"compare-insurance\"}[5m])) by (endpoint)", "legendFormat": "{{endpoint}}"}]}, {"title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "sum(rate(http_requests_total{service=\"compare-insurance\",status=~\"5..\"}[5m])) by (endpoint) / sum(rate(http_requests_total{service=\"compare-insurance\"}[5m])) by (endpoint) * 100", "legendFormat": "{{endpoint}}"}]}]}, {"title": "Comparison Performance", "type": "row", "collapsed": false, "panels": [{"title": "Comparison Duration", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type))", "legendFormat": "p95 - {{insurance_type}}"}, {"expr": "histogram_quantile(0.50, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type))", "legendFormat": "p50 - {{insurance_type}}"}]}, {"title": "Comparison Success Rate", "type": "gauge", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "(1 - sum(rate(insurance_comparison_failed_total[5m])) / sum(rate(insurance_comparison_total[5m]))) * 100", "legendFormat": "Success Rate"}], "options": {"fieldOptions": {"min": 0, "max": 100, "thresholds": [{"value": 75, "color": "red"}, {"value": 90, "color": "yellow"}, {"value": 95, "color": "green"}]}}}]}, {"title": "Quote Analysis", "type": "row", "collapsed": false, "panels": [{"title": "Quotes per Request", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(insurance_quotes_returned_bucket[5m])) by (le, insurance_type))", "legendFormat": "p95 - {{insurance_type}}"}, {"expr": "histogram_quantile(0.50, sum(rate(insurance_quotes_returned_bucket[5m])) by (le, insurance_type))", "legendFormat": "p50 - {{insurance_type}}"}]}, {"title": "Quote Amounts Distribution", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(rate(insurance_quote_amounts_bucket[5m])) by (le, insurance_type)", "format": "heatmap", "legendFormat": "{{le}}"}]}]}, {"title": "Business Metrics", "type": "row", "collapsed": false, "panels": [{"title": "Purchase Success Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "sum(rate(insurance_purchase_success_total[1h])) by (insurance_type) / sum(rate(insurance_comparison_total[1h])) by (insurance_type) * 100", "legendFormat": "{{insurance_type}}"}]}, {"title": "Provider Performance", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(rate(insurance_purchase_success_total[24h])) by (provider, insurance_type)", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"insurance_type": "Type", "provider": "Provider", "Value": "Purchases/Day"}}}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["compare-insurance", "monitoring"], "templating": {"list": [{"name": "insurance_type", "type": "query", "query": "label_values(insurance_comparison_total, insurance_type)", "refresh": 2}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Compare Insurance Dashboard", "uid": "compare-insurance", "version": 1}