apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: compare-insurance-recording-rules
  namespace: monitoring
  labels:
    app: kube-prometheus-stack
    release: prometheus
spec:
  groups:
    - name: compare-insurance.rules
      interval: 1m
      rules:
        # Request Rate Recording Rules
        - record: compare_insurance:request_rate:5m
          expr: |
            sum(rate(http_requests_total{service="compare-insurance"}[5m])) by (endpoint)
        
        # Error Rate Recording Rules
        - record: compare_insurance:error_rate_percent:5m
          expr: |
            sum(rate(http_requests_total{service="compare-insurance",status=~"5.."}[5m])) by (endpoint)
            / sum(rate(http_requests_total{service="compare-insurance"}[5m])) by (endpoint) * 100
        
        # Latency Recording Rules
        - record: compare_insurance:comparison_duration:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type))
        
        - record: compare_insurance:comparison_duration:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(insurance_comparison_duration_seconds_bucket[5m])) by (le, insurance_type))
        
        # Success Rate Recording Rules
        - record: compare_insurance:comparison_success_rate:5m
          expr: |
            (1 - sum(rate(insurance_comparison_failed_total[5m])) / sum(rate(insurance_comparison_total[5m]))) * 100
        
        # Business Metrics Recording Rules
        - record: compare_insurance:purchase_conversion_rate:1h
          expr: |
            sum(rate(insurance_purchase_success_total[1h])) by (insurance_type)
            / sum(rate(insurance_comparison_total[1h])) by (insurance_type) * 100
        
        - record: compare_insurance:provider_success_rate:24h
          expr: |
            sum(rate(insurance_purchase_success_total[24h])) by (provider, insurance_type)
