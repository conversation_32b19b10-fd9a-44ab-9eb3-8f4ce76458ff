apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: education-service-rules
  namespace: monitoring
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
    - name: education.rules
      rules:
        # Alertes de performance
        - alert: EducationServiceHighLatency
          expr: |
            histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service="education"}[5m])) by (le, endpoint)) > 2
          for: 5m
          labels:
            severity: warning
            service: education
          annotations:
            description: "95th percentile latency is above 2 seconds for endpoint {{ $labels.endpoint }}"
            value: "{{ $value }}s"

        - alert: EducationServiceErrorRate
          expr: |
            sum(rate(http_requests_total{service="education",status=~"5.."}[5m])) by (endpoint)
            /
            sum(rate(http_requests_total{service="education"}[5m])) by (endpoint)
            * 100 > 5
          for: 5m
          labels:
            severity: warning
            service: education
          annotations:
            description: "Error rate is above 5% for endpoint {{ $labels.endpoint }}"
            value: "{{ $value }}%"

        # Alertes business
        - alert: LowCourseCompletionRate
          expr: |
            sum(rate(education_course_completions_total[24h])) by (course_id)
            /
            sum(rate(education_course_enrollments_total[24h])) by (course_id)
            * 100 < 30
          for: 24h
          labels:
            severity: warning
            service: education
          annotations:
            description: "Course completion rate is below 30% for course {{ $labels.course_id }}"
            value: "{{ $value }}%"

        - alert: LowCourseRating
          expr: |
            avg_over_time(education_course_ratings_sum[24h]) / avg_over_time(education_course_ratings_count[24h]) < 3.5
          for: 24h
          labels:
            severity: warning
            service: education
          annotations:
            description: "Average course rating is below 3.5 for course {{ $labels.course_id }}"
            value: "{{ $value }}"

        - alert: HighQuizFailureRate
          expr: |
            sum(rate(education_quiz_scores_count{le="60"}[1h])) by (quiz_id)
            /
            sum(rate(education_quiz_scores_count[1h])) by (quiz_id)
            * 100 > 40
          for: 1h
          labels:
            severity: warning
            service: education
          annotations:
            description: "Quiz failure rate is above 40% for quiz {{ $labels.quiz_id }}"
            value: "{{ $value }}%"

        - alert: AnomalousLessonDuration
          expr: |
            histogram_quantile(0.95, sum(rate(education_lesson_duration_seconds_bucket[1h])) by (le, lesson_id))
            >
            3600
          for: 1h
          labels:
            severity: warning
            service: education
          annotations:
            description: "95th percentile lesson duration is above 1 hour for lesson {{ $labels.lesson_id }}"
            value: "{{ $value }}s"

        # Alertes de capacité
        - alert: HighEnrollmentRate
          expr: |
            sum(rate(education_course_enrollments_total[5m])) by (course_id) > 100
          for: 15m
          labels:
            severity: warning
            service: education
          annotations:
            description: "High enrollment rate detected for course {{ $labels.course_id }}"
            value: "{{ $value }} enrollments/s"
