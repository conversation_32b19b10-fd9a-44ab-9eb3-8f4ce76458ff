apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-grafana-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "true"
data:
  frontend-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "title": "User Experience",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Page Load Time",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
              "targets": [
                {
                  "expr": "histogram_quantile(0.95, sum(rate(frontend_page_load_time_seconds_bucket[5m])) by (le, page))",
                  "legendFormat": "p95 - {{page}}"
                },
                {
                  "expr": "histogram_quantile(0.50, sum(rate(frontend_page_load_time_seconds_bucket[5m])) by (le, page))",
                  "legendFormat": "p50 - {{page}}"
                }
              ]
            },
            {
              "title": "Client-Side Errors",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(frontend_error_total{type=\"client\"}[5m])) by (error_type)",
                  "legendFormat": "{{error_type}}"
                }
              ]
            }
          ]
        },
        {
          "title": "User Interactions",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Active Users",
              "type": "stat",
              "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8},
              "targets": [
                {
                  "expr": "sum(frontend_active_users)",
                  "instant": true
                }
              ]
            },
            {
              "title": "User Actions",
              "type": "graph",
              "gridPos": {"h": 8, "w": 16, "x": 8, "y": 8},
              "targets": [
                {
                  "expr": "sum(rate(frontend_user_action_total[5m])) by (action)",
                  "legendFormat": "{{action}}"
                }
              ]
            }
          ]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": ["frontend", "monitoring"],
      "templating": {
        "list": [
          {
            "name": "page",
            "type": "query",
            "query": "label_values(frontend_page_load_time_seconds_bucket, page)",
            "refresh": 2
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timezone": "browser",
      "title": "Frontend Dashboard",
      "uid": "frontend",
      "version": 1
    }
