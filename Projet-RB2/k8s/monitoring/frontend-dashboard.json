{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "User Experience", "type": "row", "collapsed": false, "panels": [{"title": "Page Load Time", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(frontend_page_load_time_seconds_bucket[5m])) by (le, page))", "legendFormat": "p95 - {{page}}"}, {"expr": "histogram_quantile(0.50, sum(rate(frontend_page_load_time_seconds_bucket[5m])) by (le, page))", "legendFormat": "p50 - {{page}}"}]}, {"title": "Client-Side Errors", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "sum(rate(frontend_error_total{type=\"client\"}[5m])) by (error_type)", "legendFormat": "{{error_type}}"}]}]}, {"title": "User Interactions", "type": "row", "collapsed": false, "panels": [{"title": "Active Users", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "targets": [{"expr": "sum(frontend_active_users)", "instant": true}], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}}}, {"title": "User Actions", "type": "graph", "gridPos": {"h": 8, "w": 16, "x": 8, "y": 8}, "targets": [{"expr": "sum(rate(frontend_user_action_total[5m])) by (action)", "legendFormat": "{{action}}"}]}]}, {"title": "API Integration", "type": "row", "collapsed": false, "panels": [{"title": "API Request Duration", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(frontend_api_request_duration_seconds_bucket[5m])) by (le, endpoint))", "legendFormat": "p95 - {{endpoint}}"}, {"expr": "histogram_quantile(0.50, sum(rate(frontend_api_request_duration_seconds_bucket[5m])) by (le, endpoint))", "legendFormat": "p50 - {{endpoint}}"}]}, {"title": "API Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(rate(frontend_api_error_total[5m])) by (endpoint, error_type)", "legendFormat": "{{endpoint}} - {{error_type}}"}]}]}, {"title": "Resource Usage", "type": "row", "collapsed": false, "panels": [{"title": "Memory Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "process_resident_memory_bytes{service=\"frontend\"}", "legendFormat": "Memory Usage"}]}, {"title": "CPU Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "rate(process_cpu_seconds_total{service=\"frontend\"}[5m])", "legendFormat": "CPU Usage"}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["frontend", "monitoring"], "templating": {"list": [{"name": "page", "type": "query", "query": "label_values(frontend_page_load_time_seconds_bucket, page)", "refresh": 2}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Frontend Dashboard", "uid": "frontend", "version": 1}