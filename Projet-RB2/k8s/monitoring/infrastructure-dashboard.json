{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "Node Resources", "type": "row", "collapsed": false, "panels": [{"title": "CPU Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "sum by (instance) (rate(node_cpu_seconds_total{mode!=\"idle\"}[5m])) / count by (instance) (node_cpu_seconds_total{mode=\"idle\"}) * 100", "legendFormat": "{{instance}}"}], "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}]}, {"title": "Memory Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "100 - ((node_memory_MemAvailable_bytes * 100) / node_memory_MemTotal_bytes)", "legendFormat": "{{instance}}"}], "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}]}]}, {"title": "Storage & Network", "type": "row", "collapsed": false, "panels": [{"title": "Disk Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "100 - ((node_filesystem_avail_bytes{mountpoint=\"/\"} * 100) / node_filesystem_size_bytes{mountpoint=\"/\"})", "legendFormat": "{{instance}}"}], "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}]}, {"title": "Network Traffic", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "rate(node_network_receive_bytes_total[5m])", "legendFormat": "{{instance}} - Receive"}, {"expr": "rate(node_network_transmit_bytes_total[5m])", "legendFormat": "{{instance}} - Transmit"}], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "show": true}]}]}, {"title": "Kubernetes Resources", "type": "row", "collapsed": false, "panels": [{"title": "Pod Status", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "sum by (phase) (kube_pod_status_phase)", "legendFormat": "{{phase}}"}]}, {"title": "Container Restarts", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "sum(kube_pod_container_status_restarts_total) by (container)", "legendFormat": "{{container}}"}]}]}, {"title": "Resource Quotas", "type": "row", "collapsed": false, "panels": [{"title": "CPU Requests vs Limits", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\"}) by (namespace)", "legendFormat": "{{namespace}} - Requests"}, {"expr": "sum(kube_pod_container_resource_limits{resource=\"cpu\"}) by (namespace)", "legendFormat": "{{namespace}} - Limits"}]}, {"title": "Memory Requests vs Limits", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(kube_pod_container_resource_requests{resource=\"memory\"}) by (namespace)", "legendFormat": "{{namespace}} - Requests"}, {"expr": "sum(kube_pod_container_resource_limits{resource=\"memory\"}) by (namespace)", "legendFormat": "{{namespace}} - Limits"}], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "show": true}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["infrastructure", "monitoring"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Infrastructure Dashboard", "uid": "infrastructure", "version": 1}