apiVersion: batch/v1
kind: CronJob
metadata:
  name: log-cleanup
  namespace: monitoring
spec:
  schedule: "0 1 * * *"  # Run at 1 AM daily
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: log-cleanup
              image: busybox:1.35
              command:
                - /bin/sh
                - -c
                - |
                  # Clean up logs older than 31 days
                  find /var/log -type f -name "*.gz" -mtime +31 -delete
                  find /var/log -type f -name "*.log.*" -mtime +31 -delete
                  
                  # Clean up empty directories
                  find /var/log -type d -empty -delete
                  
                  # Clean up docker container logs older than 31 days
                  find /var/lib/docker/containers -type f -name "*-json.log.*" -mtime +31 -delete
              volumeMounts:
                - name: varlog
                  mountPath: /var/log
                - name: varlibdockercontainers
                  mountPath: /var/lib/docker/containers
              resources:
                requests:
                  cpu: 50m
                  memory: 64Mi
                limits:
                  cpu: 100m
                  memory: 128Mi
              securityContext:
                privileged: true  # Required for cleanup operations
          volumes:
            - name: varlog
              hostPath:
                path: /var/log
            - name: varlibdockercontainers
              hostPath:
                path: /var/lib/docker/containers
          restartPolicy: OnFailure
