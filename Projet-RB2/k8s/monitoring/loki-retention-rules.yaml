apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-retention-rules
  namespace: monitoring
data:
  retention.yaml: |
    compactor:
      retention_enabled: true
      retention_delete_delay: 2h
      retention_delete_worker_count: 150

    limits_config:
      retention_period: 744h  # 31 days
      per_stream_rate_limit: 5MB
      per_stream_rate_limit_burst: 15MB
      ingestion_rate_mb: 10
      ingestion_burst_size_mb: 20
      max_chunk_age: 2h

    chunk_store_config:
      max_look_back_period: 744h  # 31 days

    table_manager:
      retention_deletes_enabled: true
      retention_period: 744h  # 31 days

    schema_config:
      configs:
        - from: "2023-01-01"
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h

    storage_config:
      boltdb_shipper:
        active_index_directory: /data/loki/index
        cache_location: /data/loki/index_cache
        shared_store: filesystem
      filesystem:
        directory: /data/loki/chunks

    compactor:
      working_directory: /data/loki/compactor
      shared_store: filesystem
      compaction_interval: 10m
      retention_enabled: true
      retention_delete_delay: 2h
      retention_delete_worker_count: 150

    ruler:
      storage:
        type: local
        local:
          directory: /data/loki/rules
      rule_path: /data/loki/rules
      alertmanager_url: http://alertmanager:9093
      ring:
        kvstore:
          store: inmemory
      enable_api: true

    analytics:
      reporting_enabled: false
