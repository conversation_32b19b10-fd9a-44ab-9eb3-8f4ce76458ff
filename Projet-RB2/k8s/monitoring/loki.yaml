apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: loki
  namespace: monitoring
  labels:
    app: loki
spec:
  serviceName: loki
  replicas: 1
  selector:
    matchLabels:
      app: loki
  template:
    metadata:
      labels:
        app: loki
    spec:
      securityContext:
        fsGroup: 10001
        runAsGroup: 10001
        runAsNonRoot: true
        runAsUser: 10001
      containers:
        - name: loki
          image: grafana/loki:2.8.4
          args:
            - -config.file=/etc/loki/loki.yaml
          ports:
            - containerPort: 3100
              name: http
            - containerPort: 9096
              name: grpc
          volumeMounts:
            - name: config
              mountPath: /etc/loki
            - name: storage
              mountPath: /loki
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 128Mi
          livenessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 45
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: 45
      volumes:
        - name: config
          configMap:
            name: loki-config
  volumeClaimTemplates:
    - metadata:
        name: storage
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: loki
  namespace: monitoring
  labels:
    app: loki
spec:
  ports:
    - port: 3100
      protocol: TCP
      name: http
    - port: 9096
      protocol: TCP
      name: grpc
  selector:
    app: loki
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: loki
  namespace: monitoring
  labels:
    app: loki
    release: prometheus
spec:
  selector:
    matchLabels:
      app: loki
  endpoints:
    - port: http
      path: /metrics
