apiVersion: v1
kind: ConfigMap
metadata:
  name: reviews-grafana-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "true"
data:
  reviews-dashboard.json: |-
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": "-- Grafana --",
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "gnetId": null,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "panels": [
        {
          "title": "Review Activity",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Reviews Submitted",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
              "targets": [
                {
                  "expr": "sum(rate(reviews_submitted_total[5m])) by (service_type)",
                  "legendFormat": "{{service_type}}"
                }
              ]
            },
            {
              "title": "Average Rating",
              "type": "gauge",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
              "targets": [
                {
                  "expr": "avg(reviews_rating_average) by (service_type)",
                  "legendFormat": "{{service_type}}"
                }
              ],
              "options": {
                "fieldOptions": {
                  "min": 0,
                  "max": 5,
                  "thresholds": [
                    { "value": 2, "color": "red" },
                    { "value": 3.5, "color": "yellow" },
                    { "value": 4, "color": "green" }
                  ]
                }
              }
            }
          ]
        },
        {
          "title": "Review Processing",
          "type": "row",
          "collapsed": false,
          "panels": [
            {
              "title": "Processing Duration",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8},
              "targets": [
                {
                  "expr": "histogram_quantile(0.95, sum(rate(review_processing_duration_seconds_bucket[5m])) by (le))",
                  "legendFormat": "p95"
                },
                {
                  "expr": "histogram_quantile(0.50, sum(rate(review_processing_duration_seconds_bucket[5m])) by (le))",
                  "legendFormat": "p50"
                }
              ]
            },
            {
              "title": "Processing Errors",
              "type": "graph",
              "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8},
              "targets": [
                {
                  "expr": "sum(rate(review_processing_errors_total[5m])) by (error_type)",
                  "legendFormat": "{{error_type}}"
                }
              ]
            }
          ]
        }
      ],
      "refresh": "10s",
      "schemaVersion": 27,
      "style": "dark",
      "tags": ["reviews", "monitoring"],
      "templating": {
        "list": [
          {
            "name": "service_type",
            "type": "query",
            "query": "label_values(reviews_submitted_total, service_type)",
            "refresh": 2
          }
        ]
      },
      "time": {
        "from": "now-6h",
        "to": "now"
      },
      "timezone": "browser",
      "title": "Reviews Service Dashboard",
      "uid": "reviews",
      "version": 1
    }
