apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: reviews-recording-rules
  namespace: monitoring
  labels:
    app: kube-prometheus-stack
    release: prometheus
spec:
  groups:
    - name: reviews.rules
      interval: 1m
      rules:
        # Review Activity Rules
        - record: reviews:submission_rate:5m
          expr: |
            sum(rate(reviews_submitted_total[5m])) by (service_type)
        
        - record: reviews:rating_average:5m
          expr: |
            avg(reviews_rating_average) by (service_type)
        
        # Processing Performance Rules
        - record: reviews:processing_duration:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(review_processing_duration_seconds_bucket[5m])) by (le))
        
        - record: reviews:processing_duration:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(review_processing_duration_seconds_bucket[5m])) by (le))
        
        - record: reviews:processing_errors:rate:5m
          expr: |
            sum(rate(review_processing_errors_total[5m])) by (error_type)
        
        # Content Analysis Rules
        - record: reviews:sentiment_distribution:total
          expr: |
            sum(reviews_sentiment_distribution) by (sentiment)
        
        - record: reviews:moderation:rate:5m
          expr: |
            sum(rate(reviews_moderation_total[5m])) by (moderation_result)
        
        # API Performance Rules
        - record: reviews:request_duration:p95:5m
          expr: |
            histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service="reviews"}[5m])) by (le, endpoint))
        
        - record: reviews:request_duration:p50:5m
          expr: |
            histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{service="reviews"}[5m])) by (le, endpoint))
        
        - record: reviews:error_rate_percent:5m
          expr: |
            sum(rate(http_requests_total{service="reviews", status=~"5.."}[5m])) by (endpoint)
            / sum(rate(http_requests_total{service="reviews"}[5m])) by (endpoint) * 100
        
        # Business Metrics
        - record: reviews:positive_feedback_ratio:1h
          expr: |
            sum(rate(reviews_helpful_votes_total{helpful="true"}[1h]))
            / sum(rate(reviews_helpful_votes_total[1h]))
        
        - record: reviews:service_rating_trend:24h
          expr: |
            avg_over_time(reviews_rating_average[24h])
