apiVersion: monitoring.coreos.com/v1
kind: AlertmanagerConfig
metadata:
  name: storage-alert-routes
  namespace: monitoring
spec:
  route:
    groupBy: ['alertname', 'severity']
    groupWait: 30s
    groupInterval: 5m
    repeatInterval: 12h
    receiver: 'slack-notifications'
    routes:
      - receiver: 'pagerduty-critical'
        matchers:
        - name: 'severity'
          value: 'critical'
      - receiver: 'slack-notifications'
        matchers:
        - name: 'severity'
          value: 'warning'
      - matchers:
        - name: service
          value: ipfs
        receiver: storage-team
        groupWait: 30s
        groupInterval: 5m
        repeatInterval: 4h
        routes:
          - matchers:
            - name: severity
              value: critical
            receiver: storage-team-critical
            groupWait: 0s
            groupInterval: 1m
            repeatInterval: 30m

      - matchers:
        - name: service
          value: blockchain
        receiver: blockchain-team
        groupWait: 30s
        groupInterval: 5m
        repeatInterval: 4h
        routes:
          - matchers:
            - name: severity
              value: critical
            receiver: blockchain-team-critical
            groupWait: 0s
            groupInterval: 1m
            repeatInterval: 30m

  receivers:
    - name: default
      slackConfigs:
        - channel: '#alerts-general'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          httpConfig:
            authorization:
              credentials:
                key: slack-token
                name: alertmanager-slack-secret

    - name: storage-team
      slackConfigs:
        - channel: '#storage-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          httpConfig:
            authorization:
              credentials:
                key: slack-token
                name: alertmanager-slack-secret
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          from: '<EMAIL>'
          smarthost: 'smtp.example.com:587'
          authUsername: '{{ "{{" }} .AuthUsername }}'
          authPassword: '{{ "{{" }} .AuthPassword }}'
          headers:
            subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'

    - name: storage-team-critical
      slackConfigs:
        - channel: '#storage-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          httpConfig:
            authorization:
              credentials:
                key: slack-token
                name: alertmanager-slack-secret
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          from: '<EMAIL>'
          smarthost: 'smtp.example.com:587'
          authUsername: '{{ "{{" }} .AuthUsername }}'
          authPassword: '{{ "{{" }} .AuthPassword }}'
          headers:
            subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'
      pagerdutyConfigs:
        - routingKey:
            key: pagerduty-key
            name: alertmanager-pagerduty-secret
          description: '{{ template "slack.default.title" . }}'
          severity: critical
          class: storage
          client: 'AlertManager'
          clientURL: '{{ .ExternalURL }}'
          details:
            summary: '{{ .CommonAnnotations.summary }}'
            description: '{{ .CommonAnnotations.description }}'
            runbook: '{{ .CommonAnnotations.runbook_url }}'

    - name: blockchain-team
      slackConfigs:
        - channel: '#blockchain-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          httpConfig:
            authorization:
              credentials:
                key: slack-token
                name: alertmanager-slack-secret
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          from: '<EMAIL>'
          smarthost: 'smtp.example.com:587'
          authUsername: '{{ "{{" }} .AuthUsername }}'
          authPassword: '{{ "{{" }} .AuthPassword }}'
          headers:
            subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'

    - name: blockchain-team-critical
      slackConfigs:
        - channel: '#blockchain-alerts'
          sendResolved: true
          title: '{{ template "slack.default.title" . }}'
          text: '{{ template "slack.default.text" . }}'
          httpConfig:
            authorization:
              credentials:
                key: slack-token
                name: alertmanager-slack-secret
      emailConfigs:
        - to: '<EMAIL>'
          sendResolved: true
          from: '<EMAIL>'
          smarthost: 'smtp.example.com:587'
          authUsername: '{{ "{{" }} .AuthUsername }}'
          authPassword: '{{ "{{" }} .AuthPassword }}'
          headers:
            subject: '{{ template "email.default.subject" . }}'
          html: '{{ template "email.default.html" . }}'
      pagerdutyConfigs:
        - routingKey:
            key: pagerduty-key
            name: alertmanager-pagerduty-secret
          description: '{{ template "slack.default.title" . }}'
          severity: critical
          class: blockchain
          client: 'AlertManager'
          clientURL: '{{ .ExternalURL }}'
          details:
            summary: '{{ .CommonAnnotations.summary }}'
            description: '{{ .CommonAnnotations.description }}'
            runbook: '{{ .CommonAnnotations.runbook_url }}'

    - name: slack-notifications
      slackConfigs:
        - channel: '#storage-alerts'
          apiURL:
            key: slack-token
            name: alertmanager-slack-secret

    - name: pagerduty-critical
      pagerdutyConfigs:
        - routingKey:
            key: pagerduty-key
            name: alertmanager-pagerduty-secret
          severity: '{{ if eq .GroupLabels.severity "critical" }}critical{{ else }}warning{{ end }}'
          description: '{{ .CommonAnnotations.description }}'
          client: 'AlertManager'
          clientURL: 'https://alertmanager.your-domain.com'
