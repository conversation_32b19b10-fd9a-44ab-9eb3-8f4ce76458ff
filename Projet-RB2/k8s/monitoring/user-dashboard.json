{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"title": "User Activity", "type": "row", "collapsed": false, "panels": [{"title": "Active Users", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "targets": [{"expr": "sum(user_sessions_active)", "instant": true}], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}}}, {"title": "New User Registrations", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}, "targets": [{"expr": "sum(rate(user_registrations_total[5m])) by (registration_type)", "legendFormat": "{{registration_type}}"}]}, {"title": "Login Activity", "type": "graph", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}, "targets": [{"expr": "sum(rate(user_login_attempts_total[5m])) by (status)", "legendFormat": "{{status}}"}]}]}, {"title": "Authentication", "type": "row", "collapsed": false, "panels": [{"title": "Authentication Duration", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(user_auth_duration_seconds_bucket[5m])) by (le, auth_type))", "legendFormat": "p95 - {{auth_type}}"}, {"expr": "histogram_quantile(0.50, sum(rate(user_auth_duration_seconds_bucket[5m])) by (le, auth_type))", "legendFormat": "p50 - {{auth_type}}"}]}, {"title": "Authentication Errors", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "sum(rate(user_auth_errors_total[5m])) by (error_type)", "legendFormat": "{{error_type}}"}]}]}, {"title": "Profile Management", "type": "row", "collapsed": false, "panels": [{"title": "Profile Updates", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "sum(rate(user_profile_updates_total[5m])) by (update_type)", "legendFormat": "{{update_type}}"}]}, {"title": "Profile Completeness", "type": "gauge", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "avg(user_profile_completeness_percent)", "legendFormat": "Average Completeness"}], "options": {"fieldOptions": {"min": 0, "max": 100, "thresholds": [{"value": 30, "color": "red"}, {"value": 70, "color": "yellow"}, {"value": 90, "color": "green"}]}}}]}, {"title": "API Performance", "type": "row", "collapsed": false, "panels": [{"title": "Request Latency", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{service=\"user\"}[5m])) by (le, endpoint))", "legendFormat": "p95 - {{endpoint}}"}, {"expr": "histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{service=\"user\"}[5m])) by (le, endpoint))", "legendFormat": "p50 - {{endpoint}}"}]}, {"title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum(rate(http_requests_total{service=\"user\", status=~\"5..\"}[5m])) by (endpoint) / sum(rate(http_requests_total{service=\"user\"}[5m])) by (endpoint) * 100", "legendFormat": "{{endpoint}}"}]}]}, {"title": "Security", "type": "row", "collapsed": false, "panels": [{"title": "Failed Login Attempts", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "sum(rate(user_failed_login_attempts_total[5m])) by (reason)", "legendFormat": "{{reason}}"}]}, {"title": "Password Resets", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "sum(rate(user_password_reset_total[5m])) by (status)", "legendFormat": "{{status}}"}]}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["user", "monitoring"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "User Service Dashboard", "uid": "user", "version": 1}