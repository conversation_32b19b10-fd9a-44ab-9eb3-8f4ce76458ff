apiVersion: apps/v1
kind: Deployment
metadata:
  name: insurance-service
spec:
  template:
    spec:
      containers:
      - name: insurance-service
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "400m"
            memory: "1Gi"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: website-creator-service
spec:
  template:
    spec:
      containers:
      - name: website-creator-service
        resources:
          requests:
            cpu: "300m"
            memory: "512Mi"
          limits:
            cpu: "600m"
            memory: "1Gi"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gps-service
spec:
  template:
    spec:
      containers:
      - name: gps-service
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "400m"
            memory: "1Gi"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketplace-service
spec:
  template:
    spec:
      containers:
      - name: marketplace-service
        resources:
          requests:
            cpu: "300m"
            memory: "512Mi"
          limits:
            cpu: "600m"
            memory: "1Gi"
