apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: analyzer-vpa
  namespace: retreatandbe
spec:
  targetRef:
    apiVersion: "apps/v1"
    kind: Deployment
    name: analyzer
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        memory: "256Mi"
        cpu: "100m"
      maxAllowed:
        memory: "4Gi"
        cpu: "2"
      controlledResources: ["cpu", "memory"]
