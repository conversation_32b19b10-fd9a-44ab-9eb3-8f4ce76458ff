apiVersion: v1
kind: Secret
metadata:
  name: vault-secrets
  namespace: default
type: Opaque
---
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: vault-secrets-provider
  namespace: default
spec:
  provider: vault
  parameters:
    vaultAddress: "http://vault:8200"
    roleName: "k8s-role"
    objects: |
      - objectName: "db-credentials"
        secretPath: "secret/data/database"
        secretKey: "credentials"
      - objectName: "api-keys"
        secretPath: "secret/data/api"
        secretKey: "keys"
  secretObjects:
    - data:
        - key: DB_CREDENTIALS
          objectName: db-credentials
        - key: API_KEYS
          objectName: api-keys
      secretName: app-secrets
      type: Opaque
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-deployment
spec:
  template:
    spec:
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: vault-secrets-provider
      containers:
        - name: app
          volumeMounts:
            - name: secrets-store
              mountPath: "/mnt/secrets-store"
              readOnly: true