import { check } from 'k6';
import http from 'k6/http';
import { Rate } from 'k6/metrics';

const errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 }    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    errors: ['rate<0.1'],              // Error rate must be less than 10%
  },
}

export default function() {
  const BASE_URL = __ENV.TARGET_URL || 'http://education-service';
  
  // Test course listing endpoint
  const courseListRes = http.get(`${BASE_URL}/api/courses`);
  check(courseListRes, {
    'courses list status is 200': (r) => r.status === 200,
    'courses list response time < 500ms': (r) => r.timings.duration < 500,
  });
  errorRate.add(courseListRes.status !== 200);

  // Test single course endpoint
  const courseDetailRes = http.get(`${BASE_URL}/api/courses/101`);
  check(courseDetailRes, {
    'course detail status is 200': (r) => r.status === 200,
    'course detail response time < 500ms': (r) => r.timings.duration < 500,
  });
  errorRate.add(courseDetailRes.status !== 200);

  // Test search endpoint with random query
  const searchRes = http.get(`${BASE_URL}/api/courses/search?q=programming`);
  check(searchRes, {
    'search status is 200': (r) => r.status === 200,
    'search response time < 1s': (r) => r.timings.duration < 1000,
  });
  errorRate.add(searchRes.status !== 200);

  // Simulate user interaction delay
  sleep(Math.random() * 3 + 2); // Random sleep between 2-5 seconds
}