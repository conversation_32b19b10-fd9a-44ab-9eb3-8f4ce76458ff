apiVersion: batch/v1
kind: CronJob
metadata:
  name: load-test
  namespace: testing
spec:
  schedule: "0 2 * * *"  # Exécution quotidienne à 2h du matin
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: k6
            image: grafana/k6:latest
            args:
            - run
            - /scripts/load-test.js
            volumeMounts:
            - name: k6-scripts
              mountPath: /scripts
          volumes:
          - name: k6-scripts
            configMap:
              name: k6-test-scripts
          restartPolicy: Never
