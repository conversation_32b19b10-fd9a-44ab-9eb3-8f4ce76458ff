---
# ConfigMap pour Grafana
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: retreat-and-be
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        url: http://prometheus:9090
        access: proxy
        isDefault: true

---
# Déploiement de Grafana
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: retreat-and-be
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:9.5.2
        ports:
        - containerPort: 3000
        volumeMounts:
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources/
        env:
        - name: GF_SECURITY_ADMIN_USER
          value: admin
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: admin123
        - name: GF_USERS_ALLOW_SIGN_UP
          value: "false"
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        securityContext:
          runAsNonRoot: false
      volumes:
      - name: grafana-datasources
        configMap:
          name: grafana-datasources

---
# Service pour Grafana
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: retreat-and-be
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000

---
# Ingress pour Grafana
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: grafana-ingress
  namespace: retreat-and-be
  annotations:
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host: grafana.rb2.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000 