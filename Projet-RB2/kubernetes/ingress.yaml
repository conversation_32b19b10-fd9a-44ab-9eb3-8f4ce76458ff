apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rb2-ingress
  namespace: retreat-and-be
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
  - host: rb2.example.com
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: frontend-frontend
            port:
              number: 3000
      - path: /api/(.*)
        pathType: Prefix
        backend:
          service:
            name: backend-backend
            port:
              number: 5000
      - path: /auth/(.*)
        pathType: Prefix
        backend:
          service:
            name: keycloak
            port:
              number: 8080
      - path: /financial/(.*)
        pathType: Prefix
        backend:
          service:
            name: financial-service
            port:
              number: 80
      - path: /security/(.*)
        pathType: Prefix
        backend:
          service:
            name: security-service
            port:
              number: 80
      - path: /messaging/(.*)
        pathType: Prefix
        backend:
          service:
            name: messaging-service
            port:
              number: 80
      - path: /transport/(.*)
        pathType: Prefix
        backend:
          service:
            name: transport-booking
            port:
              number: 80 