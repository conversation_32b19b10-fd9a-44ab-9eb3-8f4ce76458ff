FROM node:20-alpine

# Arguments and Environment variables
ARG APP_PORT=3002
ENV PORT=${APP_PORT}

# Installation de wget pour le healthcheck (ou curl si préféré et disponible)
RUN apk add --no-cache wget

WORKDIR /app

# Copie des fichiers de dépendances (package.json et package-lock.json)
# Cela permet de bénéficier du cache Docker si les dépendances ne changent pas
COPY package*.json ./

# Installation des dépendances
# Pour la production, envisagez d'utiliser npm ci --only=production dans une étape de build séparée (voir plus bas)
RUN npm install

# Copie du reste des fichiers de l'application
# Assurez-vous d'avoir un fichier .dockerignore pour exclure les fichiers inutiles (node_modules, .git, etc.)
COPY . .

# Exposition du port sur lequel l'application tourne
EXPOSE ${PORT}

# Healthcheck pour vérifier que l'application répond correctement
# L'endpoint /health doit exister dans votre application (ce qui est le cas ici)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:${PORT}/health || exit 1

# Définition de l'utilisateur pour exécuter l'application
# Il est recommandé de ne pas utiliser root en production
USER node

# Commande de démarrage en mode développement
# Pour la production, vous compileriez le code TypeScript en JavaScript (ex: avec tsc)
# et lanceriez directement avec node (ex: CMD [ "node", "dist/index.js" ])
# La commande NPM "dev" est définie dans package.json: "nodemon --exec \"ts-node --esm src/index.ts\""
# Pour que l'application Express écoute sur l'hôte 0.0.0.0 et le PORT défini par l'ENV,
# assurez-vous que votre script index.ts utilise bien process.env.PORT
# et que le serveur Express écoute sur 0.0.0.0 pour être accessible de l'extérieur du conteneur.
# Le script `dev` de `package.json` n'accepte pas directement `--host` ou `--port` pour `ts-node` de cette manière.
# `nodemon` les relaie, mais `ts-node src/index.ts` doit être configuré pour les utiliser,
# ou (plus commun) l'application doit lire `process.env.PORT`.
CMD [ "npm", "run", "dev" ]

# --- EXEMPLE DE MULTI-STAGE BUILD POUR LA PRODUCTION ---
# FROM node:20-alpine AS builder
# WORKDIR /app
# COPY package*.json ./
# RUN npm ci
# COPY . .
# RUN npm run build # Si votre script de build s'appelle "build"

# FROM node:20-alpine
# ARG APP_PORT=3001
# ENV PORT=${APP_PORT}
# WORKDIR /app
# COPY --from=builder /app/package*.json ./
# COPY --from=builder /app/node_modules ./node_modules
# COPY --from=builder /app/dist ./dist
# # Copiez d'autres assets nécessaires si besoin (ex: src/i18n)
# # COPY --from=builder /app/src/i18n ./src/i18n 

# USER node
# EXPOSE ${PORT}
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#     CMD wget --no-verbose --tries=1 --spider http://localhost:${PORT}/health || exit 1
# CMD [ "node", "dist/index.js" ]
# --- FIN EXEMPLE PRODUCTION ---
