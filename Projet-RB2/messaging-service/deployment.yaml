apiVersion: apps/v1
kind: Deployment
metadata:
  name: messaging-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: messaging-service
  template:
    metadata:
      labels:
        app: messaging-service
    spec:
      containers:
      - name: messaging-service
        image: your-image-name:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENV_VARIABLE_NAME
          value: "value"
