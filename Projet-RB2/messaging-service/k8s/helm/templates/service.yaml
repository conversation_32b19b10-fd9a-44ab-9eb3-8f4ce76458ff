apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-messaging
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}-messaging
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/part-of: messaging
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: http
      port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
    - name: websocket
      port: {{ add .Values.service.port 1 }}
      targetPort: {{ add .Values.service.targetPort 1 }}
      protocol: TCP
  selector:
    app.kubernetes.io/name: {{ .Release.Name }}-messaging
    app.kubernetes.io/instance: {{ .Release.Name }}