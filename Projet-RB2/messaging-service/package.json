{"name": "messaging-service", "version": "1.0.0", "description": "Modern and secure instant messaging microservice", "main": "src/index.ts", "type": "module", "scripts": {"start": "node --loader ts-node/esm src/index.ts", "dev": "nodemon --exec \"node --loader ts-node/esm src/index.ts\"", "build": "tsc", "test": "jest", "lint": "eslint . --ext .ts"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@types/node": "^18.0.0", "@types/socket.io": "^3.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.1", "express": "^4.18.1", "helmet": "^5.1.0", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^2.7.3", "jsonwebtoken": "^9.0.0", "mongoose": "^6.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "socket.io": "^4.5.1", "winston": "^3.8.1", "zod": "^3.17.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.12", "@types/express": "^4.17.13", "@types/jest": "^28.1.3", "@types/jsonwebtoken": "^9.0.0", "@types/mongoose": "^5.11.97", "@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/socket.io": "^3.0.2", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "eslint": "^8.18.0", "jest": "^28.1.1", "nodemon": "^2.0.18", "ts-jest": "^28.0.5", "ts-node": "^10.8.1", "typescript": "^4.7.4"}}