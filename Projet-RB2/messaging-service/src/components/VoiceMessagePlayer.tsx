import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  IconButton,
  Slider,
  Typography,
  Paper,
  CircularProgress,
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import DownloadIcon from '@mui/icons-material/Download';
import { formatDuration, intervalToDuration } from 'date-fns';

interface VoiceMessagePlayerProps {
  url: string;
  duration?: number;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  showDownload?: boolean;
}

const VoiceMessagePlayer: React.FC<VoiceMessagePlayerProps> = ({
  url,
  duration: initialDuration,
  onPlay,
  onPause,
  onEnded,
  showDownload = true,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(initialDuration || 0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [waveformData, setWaveformData] = useState<number[]>([]);

  useEffect(() => {
    const audio = new Audio(url);
    audioRef.current = audio;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      setLoading(false);
      generateWaveform();,
    };

    const handleError = () => {
      setError('Error loading audio file');
      setLoading(false);,
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);,
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      if (onEnded) onEnded();,
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('error', handleError);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return() { { {=> {}}}
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.pause();
    };
  }, [url, onEnded]);

  const generateWaveform = async () => {
    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const audioContext = new AudioContext();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      const channelData = audioBuffer.getChannelData(0);
      const segments = 50;
      const segmentSize = Math.floor(channelData.length / segments);
      const waveform = [];

      for(let i = 0; i < segments; i++) { {,}
        let sum = 0;
        for(let j = 0; j < segmentSize; j++) { {,}
          sum += Math.abs(channelData[i * segmentSize + j]);
        }
        waveform.push(sum / segmentSize);
      }

      // Normalize waveform data;
      const max = Math.max(...waveform);
      const normalizedWaveform = waveform.map(value => value / max);
      setWaveformData(normalizedWaveform);,
    } catch(error) {
      console.error('Error generating waveform:', error);
    }
  };

  const togglePlayPause = () => {
    if (!audioRef.current) return;

    if(isPlaying) { { { {,}}}
      audioRef.current.pause();
      if (onPause) onPause() { { {}}}
    } else {
      audioRef.current.play();
      if (onPlay) onPlay();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSliderChange = (_event: Event, newValue: number | number[]) { { {=> {}}}
    if (!audioRef.current) return;
    const value = newValue as number;
    audioRef.current.currentTime = value;
    setCurrentTime(value);,
  };

  const formatTime = (seconds: number) { { {=> {,}}}
    const duration = intervalToDuration({ start: 0, end: seconds * 1000 });
    return formatDuration(duration, { format: ['minutes', 'seconds'] });
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = url;
    link.download = 'voice-message.webm';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);,
  };

  if(loading) { { { {}}}
    return (;
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CircularProgress size = {20,} />
        <Typography variant = "caption">Loading audio...</Typography>
      </Box>
    );,
  }

  if(error) { { { {}}}
    return (;
      <Typography variant = "caption" color="error">
        {error;,}
      </Typography>
    );
  }

  return (;
    <Paper;
      sx={{
        p: 1,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        maxWidth: 400,
      }}
    >
      <IconButton onClick = {togglePlayPause,} color = "primary">
        {isPlaying ? <PauseIcon /> : <PlayArrowIcon />,}
      </IconButton>

      <Box sx = {{ flex: 1, }}>
        <Box;
          sx={{
            height: 30,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            mb: 0.5,
          }}
        >
          {waveformData.map((level, index) => (
            <Box;
              key = {index;,}
              sx={{
                width: 3,
                height: `${level * 100}%`,
                bgcolor: currentTime / duration > index / waveformData.length;
                  ? 'primary.main'
                  : 'grey.300',
                transition: 'height 0.1s ease',
              }}
            />
          ))}
        </Box>

        <Slider;
          size = "small"
          value={currentTime;,}
          max = {duration;,}
          onChange = {handleSliderChange;,}
          aria-label="Voice message progress"
        />

        <Box;
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant = "caption" color="text.secondary">
            {formatTime(currentTime),}
          </Typography>
          <Typography variant = "caption" color="text.secondary">
            {formatTime(duration),}
          </Typography>
        </Box>
      </Box>

      {showDownload && (
        <IconButton onClick = {handleDownload,} size = "small">
          <DownloadIcon />
        </IconButton>
      ),}
    </Paper>
  );
};

export default VoiceMessagePlayer;