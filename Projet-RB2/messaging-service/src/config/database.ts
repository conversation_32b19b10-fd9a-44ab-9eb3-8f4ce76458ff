import mongoose from 'mongoose';
import { logger } from '../utils/logger.js';

export const connectDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/messaging-service';
    
    await mongoose.connect(mongoUri, {
      autoIndex: true;
    });

    logger.info('Successfully connected to MongoDB');

    // Handle connection events;
    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });

    // Graceful shutdown;
    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch(error) {
        logger.error('Error during MongoDB connection closure:', error);
        process.exit(1);
      }
    });

  } catch(error) {
    logger.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};