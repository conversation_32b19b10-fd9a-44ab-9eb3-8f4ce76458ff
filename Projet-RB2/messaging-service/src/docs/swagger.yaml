openapi: 3.0.0
info:
  title: Messaging Service API
  description: REST API for managing messages and conversations
  version: 1.0.0

servers:
  - url: http://localhost:3001
    description: Development server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Message:
      type: object
      properties:
        id:
          type: string
          format: uuid
        content:
          type: string
        senderId:
          type: string
          format: uuid
        conversationId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Conversation:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        participants:
          type: array
          items:
            type: string
            format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    Error:
      type: object
      properties:
        message:
          type: string
        code:
          type: integer

security:
  - bearerAuth: []

paths:
  /api/messages:
    get:
      summary: Get all messages for a conversation
      parameters:
        - in: query
          name: conversationId
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: List of messages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Message'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

    post:
      summary: Send a new message
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - content
                - conversationId
              properties:
                content:
                  type: string
                conversationId:
                  type: string
                  format: uuid
      responses:
        '201':
          description: Message created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/messages/{id}:
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
          format: uuid
    get:
      summary: Get a specific message
      responses:
        '200':
          description: Message details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '404':
          description: Message not found

    put:
      summary: Update a message
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - content
              properties:
                content:
                  type: string
      responses:
        '200':
          description: Message updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'
        '404':
          description: Message not found

    delete:
      summary: Delete a message
      responses:
        '204':
          description: Message deleted
        '404':
          description: Message not found

  /api/conversations:
    get:
      summary: Get all conversations for the current user
      responses:
        '200':
          description: List of conversations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Conversation'

    post:
      summary: Create a new conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - participants
              properties:
                name:
                  type: string
                participants:
                  type: array
                  items:
                    type: string
                    format: uuid
      responses:
        '201':
          description: Conversation created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'

  /api/conversations/{id}:
    parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
          format: uuid
    get:
      summary: Get a specific conversation
      responses:
        '200':
          description: Conversation details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
        '404':
          description: Conversation not found

    put:
      summary: Update a conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                participants:
                  type: array
                  items:
                    type: string
                    format: uuid
      responses:
        '200':
          description: Conversation updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
        '404':
          description: Conversation not found

    delete:
      summary: Delete a conversation
      responses:
        '204':
          description: Conversation deleted
        '404':
          description: Conversation not found