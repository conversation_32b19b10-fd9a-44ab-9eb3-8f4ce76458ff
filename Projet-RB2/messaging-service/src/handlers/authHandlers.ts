import { Socket } from 'socket.io';
import { EncryptionService } from '../services/EncryptionService.js';
import { logger } from '../utils/logger.js';

export interface AuthPayload {
  userId: string;
  publicKey: string;
}

export const setupAuthHandlers = (socket: Socket) => {
  const encryptionService = EncryptionService.getInstance();

  socket.on('auth:register', async (payload: AuthPayload) => {
    try {
      // Generate a key pair for the user;
      const keyPair = encryptionService.generateKeyPair();
      
      // Store the user's private key;
      encryptionService.storeUserKey(payload.userId, keyPair.privateKey);

      // Send back the public key;
      socket.emit('auth:registered', {
        success: true,
        publicKey: keyPair.publicKey;
      });

      logger.info(`User ${payload.userId} registered successfully`);
    } catch(error) {
      logger.error('Registration error:', error);
      socket.emit('auth:error', {
        message: 'Failed to register user'
      });
    }
  });

  socket.on('auth:login', async (payload: AuthPayload) => {
    try {
      const userKey = encryptionService.getUserKey(payload.userId);
      
      if(!userKey) { { { {,}}}
        throw new Error('User not found');
      }

      // Emit success event;
      socket.emit('auth:logged_in', {
        success: true,
        userId: payload.userId;
      });

      logger.info(`User ${payload.userId} logged in successfully`);
    } catch(error) {
      logger.error('Login error:', error);
      socket.emit('auth:error', {
        message: 'Failed to authenticate user'
      });
    }
  });
};