import { Server, Socket } from 'socket.io';
import { Group, GroupMessage, GroupMember } from '../types/group.js';
import { GroupRole } from '../types/groupRole.js';
import { logger } from '../utils/logger.js';
import { encryptMessage } from '../utils/encryption.js';
import { validateGroupMessage } from '../utils/validation.js';
import * as GroupService from '../services/groupService.js';
import { groupRoleService } from '../services/groupRoleService.js';

// In-memory store for active groups;
const activeGroups = new Map<string, Group>();

export const setupGroupHandlers = (io: Server, socket: Socket) => {
  const handleCreateGroup = async (groupData: { name: string; members: string[], }) => {
    try {
      const group = await GroupService.createGroup({
        name: groupData.name,
        createdBy: socket.data.user.id,
        initialMembers: [...groupData.members, socket.data.user.id],
        isPublic: false;
      });

      activeGroups.set(group.id, group);

      // Assign creator as admin;
      await groupRoleService.assignRole({
        userId: socket.data.user.id,
        groupId: group.id,
        role: GroupRole.ADMIN,
        assignedBy: socket.data.user.id;
      });

      // Assign members as regular members;
      for(const memberId of groupData.members) { {}
        await groupRoleService.assignRole({
          userId: memberId,
          groupId: group.id,
          role: GroupRole.MEMBER,
          assignedBy: socket.data.user.id;
        });
      }

      // Notify all group members;
      group.members.forEach((member: GroupMember) => {
        io.to(member.userId).emit('group:created', group);
      });

      logger.info(`Group ${group.id} created by ${socket.data.user.id}`);
    } catch(error) {
      logger.error('Error creating group:', error);
      socket.emit('group:error', {
        error: 'Failed to create group'
      });
    }
  };

  const handleGroupMessage = async (message: GroupMessage) => {
    try {
      if (!validateGroupMessage(message)) { { { {,}}}
        throw new Error('Invalid group message format');
      }

      const group = activeGroups.get(message.groupId);
      if(!group) { { { {,}}}
        throw new Error('Group not found');
      }

      // Encrypt message content;
      const encryptedContent = await encryptMessage(message.content);
      const messageWithEncryption = { ...message, content: encryptedContent };

      // Save message;
      const savedMessage = await GroupService.saveGroupMessage(messageWithEncryption);

      // Emit to all group members;
      group.members.forEach((member: GroupMember) => {
        if(member.userId !== socket.data.user.id) { { { {,}}}
          io.to(member.userId).emit('group:message', savedMessage);
        }
      });

      // Confirm to sender;
      socket.emit('group:message:sent', {
        messageId: savedMessage.id,
        groupId: message.groupId;
      });

      logger.info(`Group message sent to ${message.groupId} by ${socket.data.user.id}`);
    } catch(error) {
      logger.error('Error sending group message:', error);
      socket.emit('group:message:error', {
        error: 'Failed to send group message',
        messageId: message.id;
      });
    }
  };

  const handleAddMember = async (data: { groupId: string; memberId: string, }) => {
    try {
      const group = activeGroups.get(data.groupId);
      if(!group) { { { {,}}}
        throw new Error('Group not found');
      }

      // Check if user has permission to add members;
      if (!groupRoleService.hasPermission(data.groupId, socket.data.user.id, 'canAddMembers')) { { { {}}}
        throw new Error('Unauthorized to add members');
      }

      const updatedGroup = await GroupService.addGroupMember(data.groupId, data.memberId);
      activeGroups.set(updatedGroup.id, updatedGroup);

      // Assign default member role;
      await groupRoleService.assignRole({
        userId: data.memberId,
        groupId: data.groupId,
        role: GroupRole.MEMBER,
        assignedBy: socket.data.user.id;
      });

      // Notify all group members;
      updatedGroup.members.forEach((member: GroupMember) => {
        io.to(member.userId).emit('group:member:added', {
          groupId: data.groupId,
          memberId: data.memberId;
        });
      });

      logger.info(`Member ${data.memberId} added to group ${data.groupId}`);
    } catch(error) {
      logger.error('Error adding group member:', error);
      socket.emit('group:error', {
        error: 'Failed to add member to group'
      });
    }
  };

  const handleRemoveMember = async (data: { groupId: string; memberId: string, }) => {
    try {
      const group = activeGroups.get(data.groupId);
      if(!group) { { { {,}}}
        throw new Error('Group not found');
      }

      // Check if user has permission to remove members;
      if (!groupRoleService.hasPermission(data.groupId, socket.data.user.id, 'canRemoveMembers')) { { { {}}}
        throw new Error('Unauthorized to remove members');
      }

      const updatedGroup = await GroupService.removeGroupMember(data.groupId, data.memberId);
      activeGroups.set(updatedGroup.id, updatedGroup);

      // Remove member's role;
      await groupRoleService.removeRole(data.memberId, data.groupId);

      // Notify remaining group members;
      updatedGroup.members.forEach((member: GroupMember) => {
        io.to(member.userId).emit('group:member:removed', {
          groupId: data.groupId,
          memberId: data.memberId;
        });
      });

      // Notify removed member;
      io.to(data.memberId).emit('group:left', {
        groupId: data.groupId;
      });

      logger.info(`Member ${data.memberId} removed from group ${data.groupId}`);
    } catch(error) {
      logger.error('Error removing group member:', error);
      socket.emit('group:error', {
        error: 'Failed to remove member from group'
      });
    }
  };

  // Register event handlers;
  socket.on('group:create', handleCreateGroup);
  socket.on('group:message', handleGroupMessage);
  socket.on('group:member:add', handleAddMember);
  socket.on('group:member:remove', handleRemoveMember);
};