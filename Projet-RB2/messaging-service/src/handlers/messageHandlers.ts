import { Server, Socket } from 'socket.io';
import { Message, MessageStatus, MessageReaction } from '../types/message.js';
import { logger } from '../utils/logger.js';
import { encryptMessage, decryptMessage } from '../utils/encryption.js';
import { validateMessage } from '../utils/validation.js';
import { saveMessage, markMessageAsRead } from '../services/MessageService.js';
import { notificationService } from '../services/notificationService.js';

// In-memory message store for quick lookups (consider replacing with a robust cache like Redis for production)
const messages = new Map<string, Message>();
// Typing indicator state
const typingUsers = new Map<string, NodeJS.Timeout>();
// In-memory storage for message reactions (consider replacing with database persistence)
const messageReactions = new Map<string, MessageReaction[]>();

/**
 * Get message history for a conversation;
 * @param conversationId The ID of the conversation;
 * @param limit Maximum number of messages to return;
 * @param before Timestamp to get messages before;
 * @returns Array of messages;
 */
async function getMessageHistory(
  conversationId: string,
  limit: number = 50,
  before?: Date
): Promise<Message[]> {
  try {
    // Filter messages by conversation
    const conversationMessages = Array.from(messages.values()).filter(
      (msg) => msg.conversationId === conversationId
    );

    // Apply before filter if provided
    let filteredMessages = conversationMessages;
    if (before) {
      filteredMessages = conversationMessages.filter(
        (msg) => new Date(msg.createdAt) < before // Ensure comparison with Date objects if `before` is Date
      );
    }

    // Sort by timestamp (newest first)
    filteredMessages.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // Apply limit
    return filteredMessages.slice(0, limit);
  } catch (error) {
    logger.error('Error retrieving message history:', error);
    throw new Error('Failed to retrieve message history');
  }
}

/**
 * Save a reaction to a message;
 * @param messageId The ID of the message;
 * @param reaction The reaction to save;
 * @returns The updated reactions array;
 */
async function saveMessageReaction(
  messageId: string,
  reaction: MessageReaction
): Promise<MessageReaction[]> {
  try {
    const message = messages.get(messageId);

    if (!message) {
      throw new Error('Message not found');
    }

    // Get existing reactions or create new array
    let reactions = messageReactions.get(messageId) || [];

    // Check if user already reacted with the same emoji
    const existingIndex = reactions.findIndex(
      (r) => r.userId === reaction.userId && r.emoji === reaction.emoji
    );

    if (existingIndex >= 0) {
      // Remove existing reaction (toggle behavior)
      reactions.splice(existingIndex, 1);
    } else {
      // Add new reaction
      reactions.push({
        ...reaction,
        createdAt: new Date(),
      });
    }

    // Update storage
    messageReactions.set(messageId, reactions);
    logger.info(`Reaction saved for message: ${messageId}`); // Corrected template literal

    return reactions;
  } catch (error) {
    logger.error('Error saving message reaction:', error);
    throw new Error('Failed to save message reaction');
  }
}

// Placeholder: Actual implementation should interact with a database or persistent store
async function deleteMessageDB(messageId: string): Promise<void> {
  logger.info(`(Placeholder) Message ${messageId} deleted from DB`);
  // Example: await MessageModel.findByIdAndDelete(messageId);
  messages.delete(messageId); // Also remove from in-memory if used as cache
  messageReactions.delete(messageId); // Remove associated reactions
}

// Placeholder: Actual implementation should interact with a database or persistent store
async function updateMessageDB(messageId: string, updatedContent: string): Promise<Message | null> {
  logger.info(`(Placeholder) Message ${messageId} updated in DB`);
  const message = messages.get(messageId);
  if (message) {
    message.content = updatedContent;
    message.updatedAt = new Date();
    messages.set(messageId, message);
    return message;
  }
  return null;
  // Example: return await MessageModel.findByIdAndUpdate(messageId, { content: updatedContent, updatedAt: new Date() }, { new: true });
}

export const setupMessageHandlers = (io: Server, socket: Socket) => {
  const handleNewMessage = async (messageDraft: Omit<Message, 'id' | 'createdAt' | 'status'>) => { // Use a more specific draft type
    try {
      const fullMessage: Message = {
        ...messageDraft,
        id: `${Date.now()}-${Math.random().toString(36).substring(2,9)}`, // Generate unique ID
        createdAt: new Date(),
        status: MessageStatus.SENT, // Initial status
        // senderId is already in messageDraft from client
      };

      // Validate message format and content
      if (!validateMessage(fullMessage)) { // Validate the full message object
        throw new Error('Invalid message format');
      }

      // Encrypt message content (if not already handled or if required by policy here)
      // Assuming content is already in its final form for saving if encryption happens client-side or in service layer
      // For this example, let's assume encryption for saving/transmitting if necessary.
      // const contentToSave = await encryptMessage(fullMessage.content); 
      // const messageToSaveInDB = { ...fullMessage, content: contentToSave };

      // Save message to database (or in-memory store for this example)
      // const savedMessage = await saveMessage(messageToSaveInDB); // if using a DB service
      messages.set(fullMessage.id, fullMessage); // Using in-memory for now
      const savedMessage = fullMessage; // Use the constructed fullMessage as saved for this example


      // Handle push notification for the recipient
      if (socket.data.user && savedMessage.recipientId) { // Ensure user and recipientId exist
        await notificationService.handleNewMessage(
          socket.data.user.id,
          savedMessage.recipientId,
          savedMessage.content.substring(0, 50) + '...'
        );
      }

      // Emit to recipient
      if (savedMessage.recipientId) {
        const recipientSockets = await io.in(savedMessage.recipientId).fetchSockets();
        if (recipientSockets.length > 0) {
          recipientSockets.forEach(recipientSocket => {
            recipientSocket.emit('message:received', {
              ...savedMessage,
              status: MessageStatus.DELIVERED, // Update status for recipient
            });
          });
        }
      }

      // Confirm to sender
      socket.emit('message:sent', {
        messageId: savedMessage.id,
        status: MessageStatus.SENT,
        tempId: messageDraft.id, // If client sent a temporary ID, echo it back for mapping
        createdAt: savedMessage.createdAt // Send server-generated timestamp
      });

      logger.info(`Message sent from ${socket.data.user?.id || 'UnknownUser'} to ${savedMessage.recipientId}`);
    } catch (error: any) {
      logger.error('Error sending message:', error);
      socket.emit('message:error', {
        error: 'Failed to send message',
        details: error.message,
        // messageId: messageDraft.id, // Use a tempId if available from draft
      });
    }
  };

  // Handle message read status
  const handleMessageRead = async (data: { messageId: string, conversationId?: string }) => {
    try {
      const { messageId, conversationId } = data;
      const message = messages.get(messageId);
      if (message) {
        // await markMessageAsRead(messageId); // If using a DB service
        message.status = MessageStatus.READ;
        messages.set(messageId, message);

        // Reset unread count (this logic might be more complex, involving conversation tracking)
        if (socket.data.user && conversationId) { // Ensure user and conversationId exist
             // notificationService.resetUnreadCountForConversation(socket.data.user.id, conversationId);
        }
        
        // Notify sender that the message was read
        io.to(message.senderId).emit('message:read', { messageId, status: MessageStatus.READ, readerId: socket.data.user?.id });
        logger.info(`Message ${messageId} marked as read by ${socket.data.user?.id}`);
      }
    } catch (error) {
      logger.error('Error marking message as read:', error);
    }
  };

  // Handle message deletion
  const handleMessageDeletion = async (data: { messageId: string, conversationId: string }) => {
    try {
      const { messageId, conversationId } = data;
      // Basic authorization: check if the user is the sender or admin
      const message = messages.get(messageId);
      if (message && message.senderId === socket.data.user?.id) { // Add admin check if needed
        await deleteMessageDB(messageId);
        // Notify other clients in the conversation
        io.to(conversationId).emit('message:deleted', { messageId, conversationId });
        logger.info(`Message ${messageId} deleted by ${socket.data.user?.id}`);
      } else {
        logger.warn(`User ${socket.data.user?.id} attempted to delete message ${messageId} without permission.`);
        socket.emit('message:error', { error: 'Permission denied to delete message' });
      }
    } catch (error: any) {
      logger.error('Error deleting message:', error);
      socket.emit('message:error', { error: 'Failed to delete message', details: error.message });
    }
  };

  // Handle message update
  const handleMessageUpdate = async (data: { messageId: string, conversationId: string, newContent: string }) => {
    try {
      const { messageId, conversationId, newContent } = data;
      // Basic authorization: check if the user is the sender
      const message = messages.get(messageId);
      if (message && message.senderId === socket.data.user?.id) {
        // const contentToUpdate = await encryptMessage(newContent); // Encrypt if needed
        const updatedMessage = await updateMessageDB(messageId, newContent);
        if (updatedMessage) {
             // Notify other clients in the conversation
            io.to(conversationId).emit('message:updated', { messageId, conversationId, updatedMessage });
            logger.info(`Message ${messageId} updated by ${socket.data.user?.id}`);
        } else {
            throw new Error('Message not found or update failed in DB');
        }
      } else {
        logger.warn(`User ${socket.data.user?.id} attempted to update message ${messageId} without permission.`);
        socket.emit('message:error', { error: 'Permission denied to update message' });
      }
    } catch (error: any) {
      logger.error('Error updating message:', error);
      socket.emit('message:error', { error: 'Failed to update message', details: error.message });
    }
  };

  // Handle message reaction
  const handleMessageReaction = async (data: { messageId: string, conversationId: string, reaction: Omit<MessageReaction, 'createdAt' | 'userId'> }) => {
    try {
      const { messageId, conversationId, reaction } = data;
      if (!socket.data.user?.id) throw new Error('User not authenticated for reaction');
      
      const fullReaction: MessageReaction = {
        ...reaction,
        userId: socket.data.user.id,
        createdAt: new Date()
      };
      const updatedReactions = await saveMessageReaction(messageId, fullReaction);
      // Notify other clients in the conversation
      io.to(conversationId).emit('message:reaction', { messageId, conversationId, reactions: updatedReactions });
      logger.info(`Reaction ${fullReaction.emoji} added to message ${messageId} by ${socket.data.user.id}`);
    } catch (error: any) {
      logger.error('Error saving message reaction:', error);
      socket.emit('message:error', { error: 'Failed to save reaction', details: error.message });
    }
  };

  // Handle message history retrieval
  const handleMessageHistory = async (data: { conversationId: string, limit?: number, before?: string }) => {
    try {
      const { conversationId, limit, before } = data;
      const beforeDate = before ? new Date(before) : undefined;
      // Authorization: ensure user is part of the conversationId before fetching history
      // This might involve checking a user_conversations table or similar logic
      const messageHistory = await getMessageHistory(conversationId, limit, beforeDate);
      socket.emit('message:history', { conversationId, messages: messageHistory });
    } catch (error: any) {
      logger.error('Error retrieving message history:', error);
      socket.emit('message:error', { error: 'Failed to retrieve message history', details: error.message });
    }
  };

  // Socket event listeners
  socket.on('message:send', handleNewMessage);
  socket.on('message:read', handleMessageRead);
  socket.on('message:delete', handleMessageDeletion);
  socket.on('message:update', handleMessageUpdate);
  socket.on('message:reaction', handleMessageReaction);
  socket.on('message:history', handleMessageHistory);
};