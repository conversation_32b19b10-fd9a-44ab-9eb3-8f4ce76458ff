import { useState, useRef, useEffect, useCallback } from 'react';

interface UseAudioVisualizerOptions {
  fftSize?: number;
  smoothingTimeConstant?: number;
  minDecibels?: number;
  maxDecibels?: number;
  barCount?: number;
}

export const useAudioVisualizer = (options: UseAudioVisualizerOptions = {}) => {
  const {
    fftSize = 256,
    smoothingTimeConstant = 0.8,
    minDecibels = -100,
    maxDecibels = -30,
    barCount = 32
  } = options;

  const [visualizerData, setVisualizerData] = useState<number[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const audioContextRef = useRef<AudioContext>();
  const analyserRef = useRef<AnalyserNode>();
  const sourceRef = useRef<MediaStreamAudioSourceNode>();
  const animationFrameRef = useRef<number>();
  const dataArrayRef = useRef<Uint8Array>();

  // Cleanup function
  const cleanup = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    if (sourceRef.current) {
      sourceRef.current.disconnect();
    }

    if (audioContextRef.current) {
      audioContextRef.current?.close();
    }

    setIsInitialized(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => cleanup();
  }, [cleanup]);

  // Initialize analyzer with stream
  const initialize = useCallback(async (stream: MediaStream) => {
    try {
      // Create audio context and nodes
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      sourceRef.current = audioContextRef.current.createMediaStreamSource(stream);

      // Configure analyser
      analyserRef.current.fftSize = fftSize;
      analyserRef.current.smoothingTimeConstant = smoothingTimeConstant;
      analyserRef.current.minDecibels = minDecibels;
      analyserRef.current.maxDecibels = maxDecibels;

      // Connect nodes
      sourceRef.current.connect(analyserRef.current);

      // Create data array
      const bufferLength = analyserRef.current.frequencyBinCount;
      dataArrayRef.current = new Uint8Array(bufferLength);

      setIsInitialized(true);
    } catch (error) {
      console.error('Error initializing audio analyser:', error);
      cleanup();
    }
  }, [fftSize, smoothingTimeConstant, minDecibels, maxDecibels, cleanup]);

  // Start visualizer data updates
  const startVisualizerUpdates = useCallback(() => {
    if (!analyserRef.current || !dataArrayRef.current) return;

    const updateVisualizer = () => {
      if (!analyserRef.current || !dataArrayRef.current) return;

      analyserRef.current.getByteFrequencyData(dataArrayRef.current);

      // Calculate average levels for visualization
      const levels: number[] = [];
      const segmentSize = Math.floor(dataArrayRef.current.length / barCount);

      for (let i = 0; i < barCount; i++) {
        let sum = 0;
        const start = i * segmentSize;
        const end = start + segmentSize;

        // Average frequency data for this segment
        for (let j = start; j < end; j++) {
          sum += dataArrayRef.current[j];
        }

        const average = sum / segmentSize;
        // Normalize to 0-1 range (byte values are 0-255)
        levels.push(average / 255);
      }

      setVisualizerData(levels);
      animationFrameRef.current = requestAnimationFrame(updateVisualizer);
    };

    updateVisualizer();
  }, [barCount]);

  // Stop visualizer data updates
  const stopVisualizerUpdates = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  return {
    visualizerData,
    isInitialized,
    initialize,
    startVisualizerUpdates,
    stopVisualizerUpdates,
    cleanup
  };
};

export default useAudioVisualizer;