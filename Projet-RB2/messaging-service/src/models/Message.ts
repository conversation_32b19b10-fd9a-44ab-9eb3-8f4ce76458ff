import mongoose, { Schema, Document } from 'mongoose';
import { MessageStatus } from '../types/message.js';

export interface IMessage extends Document {
  senderId: string;
  recipientId: string;
  content: string;
  timestamp: Date;
  status: MessageStatus;
  conversationId: Schema.Types.ObjectId;
  attachments?: string[];
  readAt?: Date;
}

const messageSchema = new Schema<IMessage>(;
  {
    senderId: {
      type: String,
      required: true,
      index: true;
    },
    recipientId: {
      type: String,
      required: true,
      index: true;
    },
    content: {
      type: String,
      required: true;
    },
    timestamp: {
      type: Date,
      default: Date.now;
    },
    status: {
      type: String,
      enum: Object.values(MessageStatus),
      default: MessageStatus.SENT;
    },
    conversationId: {
      type: Schema.Types.ObjectId,
      ref: 'Conversation',
      required: true,
      index: true;
    },
    attachments: [{
      type: String;
    }],
    readAt: {
      type: Date;
    }
  },
  {
    timestamps: true;
  }
);

export const Message = mongoose.model<IMessage>('Message', messageSchema);