import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  IconButton,
  Divider,
  useTheme,
  alpha,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import MicIcon from '@mui/icons-material/Mic';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import EmojiEmotionsIcon from '@mui/icons-material/EmojiEmotions';
// Commentez ces importations problématiques jusqu'à l'installation des packages;
// import data from '@emoji-mart/data';
// import Picker from '@emoji-mart/react';
import { useMessage } from '../contexts/MessageContext.jsx';
// import { useAuth } from '../contexts/AuthContext'; 
import MessageList from '../components/MessageList.jsx';
// import MessageInput from '../components/MessageInput';
import VoiceMessageRecorder from '../components/VoiceMessageRecorder.jsx';
import { Message, MessageDraft, MessageAttachment } from '../types/message.js';
import MessageService from '../services/MessageService.js';

// Mock pour useAuth jusqu'à ce que le contexte soit créé
const useAuth = () => {
  return {
    user: {
      id: 'mock-user-id',
      name: 'Mock User',
      status: 'online' as const
    }
  };
};

const MessagesPage: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const {
    messages,
    sendMessage,
    isLoading,
    currentConversation,
    loadMoreMessages,
    hasMoreMessages,
    messageService, // Assurez-vous que ce service est exporté par le contexte;
  } = useMessage();

  const [messageText, setMessageText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [isAttaching, setIsAttaching] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if(messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    if(container.scrollTop === 0 && hasMoreMessages && !isLoading) {
      loadMoreMessages();
    }
  };

  // Handlers pour les événements de réponse et d'édition;
  const handleReply = (message: Message) => {
    console.log('Replying to message:', message);
    // Implémentation de la réponse;
  };

  const handleEdit = (message: Message) => {
    console.log('Editing message:', message);
    // Implémentation de l'édition;
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() && !isAttaching) return;
    
    try {
      // Créer un message conforme à l'interface MessageDraft;
      const messageDraft: MessageDraft = {
        conversationId: currentConversation?.id || '',
        content: messageText,
        type: 'text',
        // Ne pas inclure sender qui n'existe pas dans MessageDraft;
      };
      
      await sendMessage(messageDraft);
      setMessageText('');
      setShowEmojiPicker(false);
    } catch(error) {
      console.error('Error sending message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if(e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiSelect = (emoji: any) => {
    setMessageText((prev) => prev + emoji.native);
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsAttaching(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // TODO: Implement file upload logic;
      // Créer un message de type fichier;
      const attachment: File = file;
      const messageDraft: MessageDraft = {
        conversationId: currentConversation?.id || '',
        content: 'File: ' + file.name,
        type: 'file',
        attachments: [attachment],
        // Ne pas inclure sender qui n'existe pas dans MessageDraft;
      };
      
      await sendMessage(messageDraft);
    } catch(error) {
      console.error('Error uploading file:', error);
    } finally {
      setIsAttaching(false);
      if(fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <Container maxWidth="lg" sx={{ height: '100vh', py: 2 }}>
      <Paper
        elevation={3}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          bgcolor: 'background.default',
        }}
      >
        {currentConversation ? (
          <>
            <Box
              sx={{
                p: 2,
                bgcolor: 'background.paper',
                borderBottom: 1,
                borderColor: 'divider',
              }}
            >
              <Typography variant="h6">
                {currentConversation.name || 'Conversation'} {/* Utiliser name au lieu de title */}
              </Typography>
            </Box>

            <Box
              ref={containerRef}
              onScroll={handleScroll}
              sx={{
                flex: 1,
                overflow: 'auto',
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
              }}
            >
              {isLoading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                  <CircularProgress size={24} />
                </Box>
              )}
              {/* Ajouter les props requises à MessageList */}
              <MessageList
                messages={messages}
                currentUser={user}
                messageService={messageService}
                onReply={handleReply}
                onEdit={handleEdit}
              />
              <div ref={messagesEndRef} />
            </Box>

            <Divider />

            <Box
              sx={{
                p: 2,
                bgcolor: 'background.paper',
                position: 'relative',
              }}
            >
              {showVoiceRecorder ? (
                <VoiceMessageRecorder
                  onCancel={() => setShowVoiceRecorder(false)}
                  maxDuration={300}
                />
              ) : (
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                  <Box sx={{ position: 'relative' }}>
                    <IconButton
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                      sx={{
                        color: showEmojiPicker ? 'primary.main' : 'text.secondary',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        },
                      }}
                    >
                      <EmojiEmotionsIcon />
                    </IconButton>
                    {showEmojiPicker && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: '100%',
                          left: 0,
                          zIndex: 1,
                        }}
                      >
                        {/* Commentez le sélecteur d'émojis jusqu'à l'installation du package */}
                        {/* <Picker
                          data={data}
                          onEmojiSelect={handleEmojiSelect}
                          theme={theme.palette.mode}
                        /> */}
                        <div>Emoji Picker Placeholder</div>
                      </Box>
                    )}
                  </Box>

                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    style={{ display: 'none' }}
                    multiple={false}
                  />
                  <IconButton
                    onClick={() => fileInputRef.current?.click()}
                    sx={{ color: 'text.secondary' }}
                  >
                    <AttachFileIcon />
                  </IconButton>

                  <Box
                    sx={{
                      flex: 1,
                      display: 'flex',
                      position: 'relative',
                      bgcolor: alpha(theme.palette.background.paper, 0.8),
                      borderRadius: 4,
                      pl: 2,
                      pr: 1,
                      py: 1,
                      border: 1,
                      borderColor: 'divider',
                    }}
                  >
                    <textarea
                      placeholder="Type a message..."
                      value={messageText}
                      onChange={(e) => setMessageText(e.target.value)}
                      onKeyPress={handleKeyPress}
                      style={{
                        width: '100%',
                        border: 'none',
                        outline: 'none',
                        backgroundColor: 'transparent',
                        resize: 'none',
                        fontFamily: theme.typography.fontFamily,
                        fontSize: theme.typography.body1.fontSize,
                        lineHeight: 1.5,
                        overflow: 'auto',
                        maxHeight: '100px',
                      }}
                      rows={1}
                    />
                    <IconButton
                      color="primary"
                      onClick={handleSendMessage}
                      disabled={!messageText.trim() && !isAttaching}
                    >
                      <SendIcon />
                    </IconButton>
                  </Box>
                </Box>
              )}
            </Box>
          </>
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <Typography variant="h6" color="text.secondary">
              Select a conversation to start messaging
            </Typography>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default MessagesPage;