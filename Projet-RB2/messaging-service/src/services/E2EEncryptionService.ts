import { randomBytes, createCipheriv, createDecipheriv, createHmac, createHash } from 'crypto';
import { logger } from '../utils/logger';

/**
 * Interface pour les clés de chiffrement
 */
export interface EncryptionKeys {
  identityKeyPair: KeyPair;
  signedPreKey: KeyPair;
  oneTimePreKeys: KeyPair[];
}

/**
 * Interface pour une paire de clés
 */
export interface KeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * Interface pour une session de chiffrement
 */
export interface EncryptionSession {
  sessionId: string;
  rootKey: string;
  chainKeys: {
    sending: string;
    receiving: string;
  };
  messageKeys: Map<number, string>;
  skippedMessageKeys: Map<number, string>;
  sendingRatchetKey: KeyPair;
  receivingRatchetKey: {
    publicKey: string;
  };
  sendingCounter: number;
  receivingCounter: number;
  previousCounter: number;
  maxSkip: number;
}

/**
 * Interface pour un message chiffré
 */
export interface EncryptedMessage {
  header: {
    ratchetKey: string;
    counter: number;
    previousCounter: number;
  };
  ciphertext: string;
  iv: string;
  authTag: string;
  hmac: string;
}

/**
 * Service de chiffrement de bout en bout pour la messagerie
 * Ce service implémente un protocole inspiré de Double Ratchet (Signal Protocol)
 * pour offrir un chiffrement de bout en bout avec forward secrecy
 */
export class E2EEncryptionService {
  private static instance: E2EEncryptionService;
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 12; // 96 bits for GCM
  private readonly maxSkip = 100; // Maximum de messages sautés autorisés
  private readonly maxOneTimeKeys = 20; // Nombre maximum de clés à usage unique

  private keys: Map<string, EncryptionKeys> = new Map();
  private sessions: Map<string, EncryptionSession> = new Map();

  private constructor() {}

  /**
   * Obtient l'instance unique du service (Singleton)
   */
  public static getInstance(): E2EEncryptionService {
    if (!E2EEncryptionService.instance) {
      E2EEncryptionService.instance = new E2EEncryptionService();
    }
    return E2EEncryptionService.instance;
  }

  /**
   * Génère une paire de clés
   * @returns Paire de clés (publique et privée)
   */
  public generateKeyPair(): KeyPair {
    const privateKey = randomBytes(this.keyLength).toString('base64');
    // Dans une implémentation réelle, on utiliserait une cryptographie à courbe elliptique (X25519)
    // Ici, nous simulons une clé publique dérivée pour simplifier
    const publicKey = createHash('sha256').update(privateKey).digest('base64');
    
    return { publicKey, privateKey };
  }

  /**
   * Initialise les clés pour un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @returns Clés générées
   */
  public initializeUserKeys(userId: string): EncryptionKeys {
    // Générer les clés d'identité
    const identityKeyPair = this.generateKeyPair();
    
    // Générer la clé pré-signée
    const signedPreKey = this.generateKeyPair();
    
    // Générer les clés à usage unique
    const oneTimePreKeys: KeyPair[] = [];
    for (let i = 0; i < this.maxOneTimeKeys; i++) {
      oneTimePreKeys.push(this.generateKeyPair());
    }
    
    const keys: EncryptionKeys = {
      identityKeyPair,
      signedPreKey,
      oneTimePreKeys
    };
    
    this.keys.set(userId, keys);
    return keys;
  }

  /**
   * Obtient les clés publiques d'un utilisateur
   * @param userId Identifiant de l'utilisateur
   * @returns Clés publiques de l'utilisateur
   */
  public getUserPublicKeys(userId: string): {
    identityKey: string;
    signedPreKey: string;
    oneTimePreKeys: string[];
  } | null {
    const keys = this.keys.get(userId);
    if (!keys) {
      return null;
    }
    
    return {
      identityKey: keys.identityKeyPair.publicKey,
      signedPreKey: keys.signedPreKey.publicKey,
      oneTimePreKeys: keys.oneTimePreKeys.map(key => key.publicKey)
    };
  }

  /**
   * Crée une session de chiffrement entre deux utilisateurs
   * @param senderId Identifiant de l'expéditeur
   * @param recipientId Identifiant du destinataire
   * @param recipientPublicKeys Clés publiques du destinataire
   * @returns Identifiant de la session
   */
  public createSession(
    senderId: string,
    recipientId: string,
    recipientPublicKeys: {
      identityKey: string;
      signedPreKey: string;
      oneTimePreKey?: string;
    }
  ): string {
    try {
      const senderKeys = this.keys.get(senderId);
      if (!senderKeys) {
        throw new Error(`No keys found for sender ${senderId}`);
      }
      
      // Générer un identifiant de session unique
      const sessionId = `${senderId}:${recipientId}:${Date.now()}`;
      
      // Générer une clé racine initiale
      // Dans une implémentation réelle, on utiliserait un échange de clés Diffie-Hellman triple
      const rootKey = this.deriveRootKey(
        senderKeys.identityKeyPair.privateKey,
        recipientPublicKeys.identityKey,
        recipientPublicKeys.signedPreKey,
        recipientPublicKeys.oneTimePreKey
      );
      
      // Générer une paire de clés de ratchet initiale
      const sendingRatchetKey = this.generateKeyPair();
      
      // Dériver les clés de chaîne initiales
      const chainKeys = this.deriveChainKeys(rootKey, sendingRatchetKey.publicKey);
      
      // Créer la session
      const session: EncryptionSession = {
        sessionId,
        rootKey,
        chainKeys,
        messageKeys: new Map(),
        skippedMessageKeys: new Map(),
        sendingRatchetKey,
        receivingRatchetKey: {
          publicKey: recipientPublicKeys.signedPreKey
        },
        sendingCounter: 0,
        receivingCounter: 0,
        previousCounter: 0,
        maxSkip: this.maxSkip
      };
      
      this.sessions.set(sessionId, session);
      return sessionId;
    } catch (error) {
      logger.error('Error creating session:', error);
      throw new Error('Failed to create encryption session');
    }
  }

  /**
   * Chiffre un message
   * @param sessionId Identifiant de la session
   * @param message Message à chiffrer
   * @returns Message chiffré
   */
  public async encryptMessage(sessionId: string, message: string): Promise<EncryptedMessage> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Dériver la clé de message
      const messageKey = this.deriveMessageKey(session.chainKeys.sending, session.sendingCounter);
      
      // Mettre à jour la clé de chaîne d'envoi
      session.chainKeys.sending = this.ratchetChainKey(session.chainKeys.sending);
      
      // Incrémenter le compteur d'envoi
      session.sendingCounter++;
      
      // Générer un vecteur d'initialisation aléatoire
      const iv = randomBytes(this.ivLength);
      
      // Chiffrer le message
      const cipher = createCipheriv(this.algorithm, Buffer.from(messageKey, 'base64'), iv);
      let ciphertext = cipher.update(message, 'utf8', 'base64');
      ciphertext += cipher.final('base64');
      
      // Récupérer le tag d'authentification
      const authTag = cipher.getAuthTag().toString('base64');
      
      // Créer l'en-tête du message
      const header = {
        ratchetKey: session.sendingRatchetKey.publicKey,
        counter: session.sendingCounter - 1,
        previousCounter: session.previousCounter
      };
      
      // Calculer le HMAC pour l'intégrité du message
      const hmac = this.calculateHMAC(
        JSON.stringify(header) + ciphertext + iv.toString('base64') + authTag,
        messageKey
      );
      
      // Mettre à jour la session
      this.sessions.set(sessionId, session);
      
      return {
        header,
        ciphertext,
        iv: iv.toString('base64'),
        authTag,
        hmac
      };
    } catch (error) {
      logger.error('Error encrypting message:', error);
      throw new Error('Failed to encrypt message');
    }
  }

  /**
   * Déchiffre un message
   * @param sessionId Identifiant de la session
   * @param encryptedMessage Message chiffré
   * @returns Message déchiffré
   */
  public async decryptMessage(sessionId: string, encryptedMessage: EncryptedMessage): Promise<string> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }
      
      // Vérifier si la clé de ratchet a changé
      if (encryptedMessage.header.ratchetKey !== session.receivingRatchetKey.publicKey) {
        // Sauvegarder les clés de message sautées
        this.skipMessageKeys(session, encryptedMessage.header.previousCounter);
        
        // Mettre à jour la clé de ratchet de réception
        session.receivingRatchetKey.publicKey = encryptedMessage.header.ratchetKey;
        session.previousCounter = session.receivingCounter;
        session.receivingCounter = 0;
        
        // Mettre à jour la clé de chaîne de réception
        session.chainKeys.receiving = this.deriveChainKeys(
          session.rootKey,
          encryptedMessage.header.ratchetKey
        ).receiving;
      }
      
      // Vérifier si des messages ont été sautés
      if (encryptedMessage.header.counter > session.receivingCounter) {
        this.skipMessageKeys(session, encryptedMessage.header.counter);
      }
      
      // Vérifier si le message a déjà été déchiffré (protection contre la réutilisation)
      if (session.messageKeys.has(encryptedMessage.header.counter)) {
        throw new Error('Message already decrypted (possible replay attack)');
      }
      
      // Récupérer ou dériver la clé de message
      let messageKey: string;
      if (session.skippedMessageKeys.has(encryptedMessage.header.counter)) {
        messageKey = session.skippedMessageKeys.get(encryptedMessage.header.counter)!;
        session.skippedMessageKeys.delete(encryptedMessage.header.counter);
      } else {
        // Dériver la clé de message
        messageKey = this.deriveMessageKey(session.chainKeys.receiving, encryptedMessage.header.counter);
        
        // Mettre à jour la clé de chaîne de réception
        for (let i = session.receivingCounter; i <= encryptedMessage.header.counter; i++) {
          session.chainKeys.receiving = this.ratchetChainKey(session.chainKeys.receiving);
        }
        
        // Mettre à jour le compteur de réception
        session.receivingCounter = encryptedMessage.header.counter + 1;
      }
      
      // Vérifier l'intégrité du message avec le HMAC
      const calculatedHMAC = this.calculateHMAC(
        JSON.stringify(encryptedMessage.header) + encryptedMessage.ciphertext + encryptedMessage.iv + encryptedMessage.authTag,
        messageKey
      );
      
      if (calculatedHMAC !== encryptedMessage.hmac) {
        throw new Error('Message integrity check failed');
      }
      
      // Déchiffrer le message
      const decipher = createDecipheriv(
        this.algorithm,
        Buffer.from(messageKey, 'base64'),
        Buffer.from(encryptedMessage.iv, 'base64')
      );
      
      decipher.setAuthTag(Buffer.from(encryptedMessage.authTag, 'base64'));
      
      let decrypted = decipher.update(encryptedMessage.ciphertext, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      // Marquer la clé de message comme utilisée
      session.messageKeys.set(encryptedMessage.header.counter, messageKey);
      
      // Mettre à jour la session
      this.sessions.set(sessionId, session);
      
      return decrypted;
    } catch (error) {
      logger.error('Error decrypting message:', error);
      throw new Error('Failed to decrypt message');
    }
  }

  /**
   * Dérive une clé racine à partir des clés d'identité et pré-signées
   * @param senderPrivateKey Clé privée de l'expéditeur
   * @param recipientIdentityKey Clé d'identité du destinataire
   * @param recipientSignedPreKey Clé pré-signée du destinataire
   * @param recipientOneTimePreKey Clé à usage unique du destinataire (optionnelle)
   * @returns Clé racine dérivée
   */
  private deriveRootKey(
    senderPrivateKey: string,
    recipientIdentityKey: string,
    recipientSignedPreKey: string,
    recipientOneTimePreKey?: string
  ): string {
    // Dans une implémentation réelle, on utiliserait un échange de clés Diffie-Hellman triple
    // Ici, nous simulons la dérivation de clé pour simplifier
    const keyMaterial = senderPrivateKey + recipientIdentityKey + recipientSignedPreKey + (recipientOneTimePreKey || '');
    return createHash('sha256').update(keyMaterial).digest('base64');
  }

  /**
   * Dérive les clés de chaîne à partir de la clé racine et de la clé de ratchet
   * @param rootKey Clé racine
   * @param ratchetKey Clé de ratchet
   * @returns Clés de chaîne dérivées
   */
  private deriveChainKeys(rootKey: string, ratchetKey: string): { sending: string; receiving: string } {
    // Dériver les clés de chaîne à partir de la clé racine et de la clé de ratchet
    const hmac = createHmac('sha256', rootKey);
    hmac.update(ratchetKey);
    const derivedKey = hmac.digest('base64');
    
    // Dériver les clés de chaîne d'envoi et de réception
    const sendingKey = createHmac('sha256', derivedKey).update('sending').digest('base64');
    const receivingKey = createHmac('sha256', derivedKey).update('receiving').digest('base64');
    
    return {
      sending: sendingKey,
      receiving: receivingKey
    };
  }

  /**
   * Dérive une clé de message à partir de la clé de chaîne et du compteur
   * @param chainKey Clé de chaîne
   * @param counter Compteur de message
   * @returns Clé de message dérivée
   */
  private deriveMessageKey(chainKey: string, counter: number): string {
    // Dériver la clé de message à partir de la clé de chaîne et du compteur
    const hmac = createHmac('sha256', chainKey);
    hmac.update(counter.toString());
    return hmac.digest('base64');
  }

  /**
   * Fait avancer la clé de chaîne d'un cran
   * @param chainKey Clé de chaîne actuelle
   * @returns Nouvelle clé de chaîne
   */
  private ratchetChainKey(chainKey: string): string {
    // Faire avancer la clé de chaîne d'un cran
    const hmac = createHmac('sha256', chainKey);
    hmac.update('ratchet');
    return hmac.digest('base64');
  }

  /**
   * Saute les clés de message pour les messages manqués
   * @param session Session de chiffrement
   * @param targetCounter Compteur cible
   */
  private skipMessageKeys(session: EncryptionSession, targetCounter: number): void {
    // Vérifier si le nombre de sauts est trop grand
    if (targetCounter - session.receivingCounter > session.maxSkip) {
      throw new Error('Too many skipped messages');
    }
    
    // Dériver et stocker les clés de message sautées
    for (let i = session.receivingCounter; i < targetCounter; i++) {
      const messageKey = this.deriveMessageKey(session.chainKeys.receiving, i);
      session.skippedMessageKeys.set(i, messageKey);
      session.chainKeys.receiving = this.ratchetChainKey(session.chainKeys.receiving);
    }
  }

  /**
   * Calcule un HMAC pour l'intégrité du message
   * @param data Données à authentifier
   * @param key Clé HMAC
   * @returns HMAC calculé
   */
  private calculateHMAC(data: string, key: string): string {
    const hmac = createHmac('sha256', key);
    hmac.update(data);
    return hmac.digest('base64');
  }

  /**
   * Supprime une session de chiffrement
   * @param sessionId Identifiant de la session
   */
  public deleteSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  /**
   * Supprime les clés d'un utilisateur
   * @param userId Identifiant de l'utilisateur
   */
  public deleteUserKeys(userId: string): void {
    this.keys.delete(userId);
    
    // Supprimer toutes les sessions associées à cet utilisateur
    for (const [sessionId, session] of this.sessions.entries()) {
      if (sessionId.startsWith(`${userId}:`) || sessionId.includes(`:${userId}:`)) {
        this.sessions.delete(sessionId);
      }
    }
  }
}
