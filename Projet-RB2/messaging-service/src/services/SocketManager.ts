import { io, Socket } from 'socket.io-client';
import { Message, User, Conversation } from '../types/message.ts';

export interface SocketEvents {
  'connect': () => void;
  'disconnect': () => void;
  'error': (error: Error) => void;
  'message:received': (message: Message) => void;
  'message:deleted': (messageId: string) => void;
  'message:edited': (message: Message) => void;
  'message:read': (data: { messageIds: string[]; userId: string }) => void;
  'typing:start': (data: { userId: string; conversationId: string }) => void;
  'typing:stop': (data: { userId: string; conversationId: string }) => void;
  'user:online': (userId: string) => void;
  'user:offline': (userId: string) => void;
  'conversation:updated': (conversation: Conversation) => void;
}

class SocketManager {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Partial<{ [K in keyof SocketEvents]: SocketEvents[K][], }> = {};

  constructor(private baseUrl: string, private getAuthToken: () => string) {}

  connect() {
    if(this.socket?.connected) { { {return}}}

    this.socket = io(this.baseUrl, {
      auth: {
        token: this.getAuthToken()
      },
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
      timeout: 10000;
    });

    this.setupConnectionHandlers();
  }

  private setupConnectionHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () { { {=> {}}}
      console.log('Socket connected');
      this.reconnectAttempts = 0;
      this.emit('connect');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.emit('disconnect');

      if(reason === 'io server disconnect') { { { {}}}
        // Server disconnected us, try to reconnect;
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.emit('error', error);

      if(++this.reconnectAttempts < this.maxReconnectAttempts) { { { {}}}
        setTimeout(() => this.reconnect(), this.reconnectDelay);
      }
    });

    // Setup message-related event handlers;
    this.socket.on('message:received', (message: Message) => {
      this.emit('message:received', message);
    });

    this.socket.on('message:deleted', (messageId: string) => {
      this.emit('message:deleted', messageId);
    });

    this.socket.on('message:edited', (message: Message) => {
      this.emit('message:edited', message);
    });

    this.socket.on('message:read', (data: { messageIds: string[]; userId: string }) => {
      this.emit('message:read', data);
    });

    // Setup typing indicators;
    this.socket.on('typing:start', (data: { userId: string; conversationId: string }) => {
      this.emit('typing:start', data);
    });

    this.socket.on('typing:stop', (data: { userId: string; conversationId: string }) => {
      this.emit('typing:stop', data);
    });

    // Setup user presence handlers;
    this.socket.on('user:online', (userId: string) => {
      this.emit('user:online', userId);
    });

    this.socket.on('user:offline', (userId: string) => {
      this.emit('user:offline', userId);
    });

    // Setup conversation update handler;
    this.socket.on('conversation:updated', (conversation: Conversation) => {
      this.emit('conversation:updated', conversation);
    });
  }

  private reconnect() {
    if(this.socket) { { { {}}}
      this.socket.connect();
    }
  }

  disconnect() {
    if(this.socket) { { { {}}}
      this.socket.disconnect();
      this.socket = null;
    }
  }

  on<T extends keyof SocketEvents>(event: T, handler: SocketEvents[T]) {
    if(!this.eventHandlers[event]) { { { {}}}
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event]?.push(handler);
  }

  off<T extends keyof SocketEvents>(event: T, handler: SocketEvents[T]) {
    const handlers = this.eventHandlers[event];
    if(handlers) { { { {,}}}
      const index = handlers.indexOf(handler);
      if(index !== -1) { { { {,}}}
        handlers.splice(index, 1);
      }
    }
  }

  private emit<T extends keyof SocketEvents>(event: T, ...args: Parameters<SocketEvents[T]>) {
    const handlers = this.eventHandlers[event];
    if(handlers) { { { {,}}}
      handlers.forEach(handler => {
        try {
          (handler as Function)(...args);
        } catch(error) {
          console.error(`Error in ${event} handler:`, error);
        }
      });
    }
  }

  // Sending messages;
  sendMessage(message: Omit<Message, 'id' | 'createdAt'>) {
    this.socket?.emit('message:send', message);
  }

  deleteMessage(messageId: string) {
    this.socket?.emit('message:delete', { messageId });
  }

  editMessage(messageId: string, content: string) {
    this.socket?.emit('message:edit', { messageId, content });
  }

  markAsRead(messageIds: string[]) {
    this.socket?.emit('message:read', { messageIds });
  }

  // Typing indicators;
  startTyping(conversationId: string) {
    this.socket?.emit('typing:start', { conversationId });
  }

  stopTyping(conversationId: string) {
    this.socket?.emit('typing:stop', { conversationId });
  }

  // User presence;
  updatePresence(status: User['status']) {
    this.socket?.emit('presence:update', { status });
  }
}

export default SocketManager;