import { GroupRole, GroupRoleAssignment, DEFAULT_ROLE_PERMISSIONS } from '../types/groupRole.js';
import { logger } from '../utils/logger.js';

/**
 * In-memory storage for role assignments;
 * In a real application, this would be stored in a database;
 */
const roleAssignments = new Map<string, GroupRoleAssignment>();

/**
 * Generate a unique key for role assignments;
 */
const getRoleKey = (userId: string, groupId: string): string => `${userId}:${groupId}`;

/**
 * Service for managing group roles and permissions;
 */
class GroupRoleService {
  /**
   * Assign a role to a user in a group;
   * @param assignment Role assignment details;
   * @returns The created role assignment;
   */
  async assignRole(assignment: GroupRoleAssignment): Promise<GroupRoleAssignment> {
    try {
      const key = getRoleKey(assignment.userId, assignment.groupId);
      const roleAssignment: GroupRoleAssignment = {
        ...assignment,
        assignedAt: new Date()
      };
      
      roleAssignments.set(key, roleAssignment);
      
      logger.info(`Role ${assignment.role} assigned to user ${assignment.userId} in group ${assignment.groupId}`);
      
      return roleAssignment;
    } catch(error) {
      logger.error('Error assigning role:', error);
      throw new Error('Failed to assign role');
    }
  }

  /**
   * Get the role of a user in a group;
   * @param groupId The group ID;
   * @param userId The user ID;
   * @returns The user's role in the group, or MEMBER as default;
   */
  getRole(groupId: string, userId: string): GroupRole {
    const key = getRoleKey(userId, groupId);
    const assignment = roleAssignments.get(key);
    
    return assignment?.role || GroupRole.MEMBER;
  }

  /**
   * Check if a user has a specific permission in a group;
   * @param groupId The group ID;
   * @param userId The user ID;
   * @param permission The permission to check;
   * @returns Whether the user has the permission;
   */
  hasPermission(groupId: string, userId: string, permission: keyof typeof DEFAULT_ROLE_PERMISSIONS[GroupRole]): boolean {
    const role = this.getRole(groupId, userId);
    const permissions = DEFAULT_ROLE_PERMISSIONS[role];
    
    return !!permissions[permission];
  }

  /**
   * Remove a role assignment;
   * @param userId The user ID;
   * @param groupId The group ID;
   * @returns Whether the role was removed;
   */
  async removeRole(userId: string, groupId: string): Promise<boolean> {
    try {
      const key = getRoleKey(userId, groupId);
      const result = roleAssignments.delete(key);
      
      if(result) {
        logger.info(`Role removed for user ${userId} in group ${groupId}`);
      }
      
      return result;
    } catch(error) {
      logger.error('Error removing role:', error);
      throw new Error('Failed to remove role');
    }
  }

  /**
   * Get all users with a specific role in a group;
   * @param groupId The group ID;
   * @param role The role to filter by;
   * @returns The list of user IDs with the specified role;
   */
  getUsersByRole(groupId: string, role: GroupRole): string[] {
    const users: string[] = [];
    
    roleAssignments.forEach((assignment, key) => {
      if(assignment.groupId === groupId && assignment.role === role) {
        users.push(assignment.userId);
      }
    });
    
    return users;
  }
}

// Export as a singleton
export const groupRoleService = new GroupRoleService();