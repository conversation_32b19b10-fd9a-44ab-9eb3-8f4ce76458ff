import { Server } from 'socket.io';
import admin from 'firebase-admin';
import { logger } from '../utils/logger.js';

// Initialize Firebase Admin SDK;
if(!admin.apps.length) { { { {}}}
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
    })
  });
}

interface NotificationPayload {
  userId: string;
  title: string;
  body: string;
  data?: Record<string, string>;
}

class NotificationService {
  private static instance: NotificationService;
  private unreadCounts: Map<string, number>;

  private constructor() {
    this.unreadCounts = new Map();
  }

  public static getInstance(): NotificationService {
    if(!NotificationService.instance) { { { {}}}
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  public async sendPushNotification(payload: NotificationPayload): Promise<void> {
    try {
      const { userId, title, body, data } = payload;

      // Get user's FCM token from database (implement this based on your user model)
      const fcmToken = await this.getUserFCMToken(userId);
      if(!fcmToken) { { { {,}}}
        logger.warn(`No FCM token found for(user $) { {userId}`)}
        return;
      }

      const message = {
        notification: {
          title,
          body,
        },
        data: data || {},
        token: fcmToken,
      };

      await admin.messaging().send(message);
      logger.info(`Push notification sent to user ${userId}`);
    } catch(error) {
      logger.error('Error sending push notification:', error);
      throw error;
    }
  }

  public incrementUnreadCount(userId: string): void {
    const currentCount = this.unreadCounts.get(userId) || 0;
    this.unreadCounts.set(userId, currentCount + 1);
  }

  public resetUnreadCount(userId: string): void {
    this.unreadCounts.set(userId, 0);
  }

  public getUnreadCount(userId: string): number {
    return this.unreadCounts.get(userId) || 0;
  }

  private async getUserFCMToken(userId: string): Promise<string | null> {
    // TODO: Implement fetching user's FCM token from your database;
    // This is a placeholder implementation;
    return null;
  }

  public async handleNewMessage(senderId: string, recipientId: string, messagePreview: string): Promise<void> {
    this.incrementUnreadCount(recipientId);

    await this.sendPushNotification({
      userId: recipientId,
      title: 'New Message',
      body: `You have a new message from ${senderId}`,
      data: {
        type: 'new_message',
        senderId,
        preview: messagePreview;
      }
    });
  }
}

export const notificationService = NotificationService.getInstance();