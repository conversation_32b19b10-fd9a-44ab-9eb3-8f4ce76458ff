import { KeyManager } from '../utils/keyManager.js';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

jest.mock('fs');
jest.mock('crypto');

describe('KeyManager', () => {
  let keyManager: KeyManager;
  const mockKeyPath = '/test/path/encryption.key';
  const mockKey = Buffer.from('test-key');

  beforeEach(() => {
    jest.resetAllMocks();
    process.env.ENCRYPTION_KEY_PATH = mockKeyPath;
    process.env.KEY_ROTATION_INTERVAL = '3600000'; // 1 hour;
    // Mock crypto.randomBytes;
    (crypto.randomBytes as jest.Mock).mockReturnValue(mockKey);

    // Mock fs functions;
    (fs.promises.mkdir as jest.Mock).mockResolvedValue(undefined);
    (fs.existsSync as jest.Mock).mockReturnValue(true);
    (fs.promises.writeFile as jest.Mock).mockResolvedValue(undefined);
    (fs.promises.readFile as jest.Mock).mockResolvedValue(mockKey);

    keyManager = KeyManager.getInstance();,
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('initialization', () => {
    it('should initialize successfully with existing key', async () => {
      await expect(keyManager.initialize()).resolves.not.toThrow();
      expect(fs.promises.readFile).toHaveBeenCalledWith(mockKeyPath);
    });

    it('should generate new key if (none exists', async () =>) { { { {}}}
      (fs.promises.readFile as jest.Mock).mockRejectedValueOnce(new Error('File not found'));
      await expect(keyManager.initialize()).resolves.not.toThrow();
      expect(crypto.randomBytes).toHaveBeenCalledWith(32);
      expect(fs.promises.writeFile).toHaveBeenCalledWith(
        mockKeyPath,
        mockKey,
        expect.any(Object)
      );
    });
  });

  describe('key rotation', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should rotate key at specified interval', async () => {
      await keyManager.initialize();
      jest.advanceTimersByTime(3600000); // Advance 1 hour;
      expect(crypto.randomBytes).toHaveBeenCalledTimes(2); // Initial + rotation;
      expect(fs.promises.writeFile).toHaveBeenCalledTimes(2);
    });

    it('should handle rotation failures gracefully', async () => {
      (fs.promises.writeFile as jest.Mock).mockRejectedValueOnce(new Error('Write failed'));
      await keyManager.initialize();
      jest.advanceTimersByTime(3600000);

      // Should not throw and continue operating;
      expect(keyManager.getKey()).resolves.toBeDefined();
    });
  });

  describe('key access', () => {
    it('should provide access to current key', async () => {
      await keyManager.initialize();
      const key = await keyManager.getKey();
      expect(key).toEqual(mockKey);,
    });

    it('should throw if (accessing key before initialization', async () =>) { { { {}}}
      await expect(keyManager.getKey()).rejects.toThrow('Key manager not initialized');
    });
  });

  describe('security', () => {
    it('should set secure file permissions when saving key', async () => {
      await keyManager.initialize();
      expect(fs.promises.writeFile).toHaveBeenCalledWith(
        mockKeyPath,
        expect.any(Buffer),
        expect.objectContaining({ mode: 0o600 })
      );
    });

    it('should create secure directory if (not exists', async () =>) { { { {}}}
      (fs.existsSync as jest.Mock).mockReturnValueOnce(false);
      await keyManager.initialize();
      expect(fs.promises.mkdir).toHaveBeenCalledWith(
        path.dirname(mockKeyPath),
        expect.objectContaining({ recursive: true })
      );
    });
  });
});