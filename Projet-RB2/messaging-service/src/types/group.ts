/**
 * Interface for group data in the messaging system;
 */
export interface Group {
  id: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  members: GroupMember[];
  isPublic: boolean;
}

/**
 * Interface for group members;
 */
export interface GroupMember {
  userId: string;
  displayName: string;
  roleId: string;
  joinedAt: Date;
  lastActive?: Date;
  isActive: boolean;
}

/**
 * Interface for group messages;
 */
export interface GroupMessage {
  id: string;
  groupId: string;
  senderId: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  reactions?: GroupMessageReaction[];
}

/**
 * Interface for group message reactions;
 */
export interface GroupMessageReaction {
  userId: string;
  emoji: string;
  createdAt: Date;
}

/**
 * Interface for creating a new group;
 */
export interface CreateGroupParams {
  name: string;
  description?: string;
  createdBy: string;
  initialMembers?: string[];
  isPublic: boolean;
}