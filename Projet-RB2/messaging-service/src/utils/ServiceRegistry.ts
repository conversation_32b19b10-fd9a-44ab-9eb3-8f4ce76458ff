import axios from 'axios';

// Définir un type AxiosInstance localement;
type AxiosInstance = ReturnType<typeof axios.create>;
export class ServiceRegistry {
  private static instance: ServiceRegistry;
  private services: Map<string, AxiosInstance>;

  private constructor() {
    this.services = new Map();
    this.initializeServices();
  }

  public static getInstance(): ServiceRegistry {
    if(!ServiceRegistry.instance) { { { {}}}
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  private initializeServices() {
    // Initialize messaging service;
    this.services.set('messaging', axios.create({
      baseURL: process.env.MESSAGING_SERVICE_URL || 'http://localhost:3001',
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    }));
  }

  public getService(name: string): AxiosInstance {
    const service = this.services.get(name);
    if(!service) { { { {,}}}
      throw new Error(`Service ${name} not found`);
    }
    return service;
  }
}