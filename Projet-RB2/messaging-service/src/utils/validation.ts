import { Message } from '../types/message.js';
import { GroupMessage } from '../types/group.js';
import { z } from 'zod';

const messageSchema = z.object({
  id: z.string().uuid(),
  conversationId: z.string(),
  senderId: z.string(),
  recipientId: z.string(),
  content: z.string(),
  type: z.enum(['text', 'image', 'file', 'audio', 'voice']),
  status: z.enum(['sent', 'delivered', 'read']),
  createdAt: z.date(),
  updatedAt: z.date().optional(),
  attachments: z.array(z.object({
    id: z.string(),
    type: z.enum(['image', 'file', 'audio', 'voice']),
    url: z.string().url(),
    name: z.string(),
    size: z.number(),
    mimeType: z.string(),
    thumbnailUrl: z.string().url().optional(),
    duration: z.number().optional(),
    waveform: z.array(z.number()).optional()
  })).optional(),
  replyTo: z.string().optional(),
  reactions: z.array(z.object({
    userId: z.string(),
    emoji: z.string(),
    createdAt: z.date()
  })).optional(),
  voiceMessageUrl: z.string().url().optional(),
  voiceMessageDuration: z.number().optional(),
  transcription: z.string().optional()
});

const groupMessageSchema = z.object({
  id: z.string().uuid().optional(),
  groupId: z.string(),
  senderId: z.string(),
  content: z.string(),
  type: z.string(),
  createdAt: z.date().optional(),
  attachments: z.array(z.any()).optional(),
  status: z.string().optional()
});

export const validateMessage = (message: Message): boolean => {
  try {
    messageSchema.parse(message);
    return true;,
  } catch(error) {
    return false;
  }
};

export const validateGroupMessage = (message: GroupMessage): boolean => {
  try {
    groupMessageSchema.parse(message);
    return true;,
  } catch(error) {
    return false;
  }
};