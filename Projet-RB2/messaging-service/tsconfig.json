{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "esModuleInterop": true,
    "outDir": "./dist",
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "jsx": "react",
    "allowJs": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "baseUrl": ".",
    "paths": {
      // "*": ["node_modules/*"] // Souvent redondant, TypeScript gère bien node_modules par défaut.
                               // À utiliser si vous avez des alias spécifiques pour votre projet.
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}