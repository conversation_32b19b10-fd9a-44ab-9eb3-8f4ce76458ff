# Synchronisation Web-Mobile

Ce document décrit l'architecture de synchronisation entre l'application web et l'application mobile du projet RB2.

## Architecture générale

L'application mobile est maintenant intégrée en tant que microservice dans notre infrastructure Kubernetes, ce qui permet une meilleure cohérence avec l'architecture existante et facilite la synchronisation des données entre les plateformes.

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Application    │     │     API         │     │  Application    │
│     Web         │◄────┤   Backend       │────►│    Mobile       │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                              │
                              │
                              ▼
                        ┌─────────────────┐
                        │                 │
                        │   Base de       │
                        │   données       │
                        │                 │
                        └─────────────────┘
```

## Mécanisme de synchronisation

### 1. Synchronisation côté mobile

L'application mobile utilise un service de synchronisation (`SyncService`) qui gère :

- La récupération des données depuis l'API backend
- Le stockage local des données pour une utilisation hors ligne
- La synchronisation périodique en arrière-plan
- La gestion des conflits de données

### 2. Stratégie de mise en cache

- **Cache-first** : L'application mobile utilise d'abord les données en cache, puis les met à jour en arrière-plan
- **Synchronisation périodique** : Par défaut, les données sont synchronisées toutes les 15 minutes
- **Synchronisation forcée** : L'utilisateur peut forcer une synchronisation via l'interface (pull-to-refresh)

### 3. Gestion hors ligne

L'application mobile peut fonctionner hors ligne grâce à :

- Une base de données locale (WatermelonDB)
- Une file d'attente pour les actions utilisateur (réservations, modifications de profil, etc.)
- Une synchronisation automatique lors de la reconnexion au réseau

## Implémentation technique

### Service de synchronisation

Le service de synchronisation (`SyncService`) implémente les méthodes suivantes :

- `syncRetreats()` : Synchronise les données des retraites
- `syncUserData()` : Synchronise les données utilisateur (profil, réservations, notifications)
- `shouldSync()` : Détermine si une synchronisation est nécessaire
- `syncAll()` : Synchronise toutes les données
- `startBackgroundSync()` : Démarre une synchronisation en arrière-plan

### Vérification de la synchronisation

Un script de vérification (`verify-web-mobile-sync.js`) est intégré au pipeline CI/CD pour s'assurer que les données sont correctement synchronisées entre les plateformes web et mobile.

## Déploiement avec Docker et Kubernetes

Le service mobile est maintenant déployé en tant que conteneur Docker dans notre cluster Kubernetes :

1. **Dockerfile** : Définit l'environnement de build pour l'application mobile
2. **Chart Helm** : Configure le déploiement Kubernetes du service mobile
3. **Pipeline CI/CD** : Intègre la construction et le déploiement du conteneur Docker

## Monitoring et alertes

Le système de monitoring surveille la synchronisation des données et déclenche des alertes en cas de problèmes :

- Écarts importants entre les données web et mobile
- Échecs répétés de synchronisation
- Latence élevée lors des synchronisations

## Bonnes pratiques

1. **Minimiser les données synchronisées** : Ne synchroniser que les données essentielles
2. **Gestion efficace de la batterie** : Limiter les synchronisations en arrière-plan
3. **Compression des données** : Réduire la taille des données transférées
4. **Gestion des versions** : S'assurer que les modifications de schéma sont compatibles entre les plateformes

## Tests

Les tests suivants sont effectués pour garantir la fiabilité de la synchronisation :

1. **Tests unitaires** : Vérifier le comportement des composants individuels
2. **Tests d'intégration** : Vérifier l'interaction entre les composants
3. **Tests de performance** : Mesurer les temps de synchronisation
4. **Tests de résilience** : Vérifier le comportement en cas de perte de connexion
