#!/bin/bash

# Script pour générer les versions téléchargeables pour tous les systèmes d'exploitation

echo "Génération des versions téléchargeables pour tous les systèmes d'exploitation..."

# Créer le dossier de builds s'il n'existe pas
BUILDS_DIR="./builds"
mkdir -p "$BUILDS_DIR"

# Définir les variables
VERSION=$(grep -o '"version": "[^"]*' package.json | cut -d'"' -f4)
DATE=$(date +"%Y%m%d_%H%M%S")
BUILD_INFO_FILE="$BUILDS_DIR/build-info-${DATE}.txt"

# Créer un fichier d'information sur le build
echo "Retreat & Be Mobile - Build Information" > "$BUILD_INFO_FILE"
echo "Version: $VERSION" >> "$BUILD_INFO_FILE"
echo "Date: $(date)" >> "$BUILD_INFO_FILE"
echo "Commit: $(git rev-parse HEAD)" >> "$BUILD_INFO_FILE"
echo "Branch: $(git rev-parse --abbrev-ref HEAD)" >> "$BUILD_INFO_FILE"
echo "" >> "$BUILD_INFO_FILE"

# Fonction pour vérifier si une commande a réussi
check_result() {
  if [ $? -ne 0 ]; then
    echo "Erreur lors de l'exécution de $1"
    echo "Build échoué: $1" >> "$BUILD_INFO_FILE"
    exit 1
  else
    echo "Build réussi: $1" >> "$BUILD_INFO_FILE"
  fi
}

# Installer les dépendances
echo "Installation des dépendances..."
npm install
check_result "Installation des dépendances"

# Vérifier les types TypeScript
echo "Vérification des types TypeScript..."
npm run typecheck
check_result "Vérification des types TypeScript"

# Exécuter les tests
echo "Exécution des tests..."
npm run test
check_result "Exécution des tests"

# Build Android
echo "Build Android..."
echo "" >> "$BUILD_INFO_FILE"
echo "=== Build Android ===" >> "$BUILD_INFO_FILE"
chmod +x ./scripts/build-android.sh
./scripts/build-android.sh | tee -a "$BUILD_INFO_FILE"
check_result "Build Android"

# Build iOS (uniquement sur macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
  echo "Build iOS..."
  echo "" >> "$BUILD_INFO_FILE"
  echo "=== Build iOS ===" >> "$BUILD_INFO_FILE"
  chmod +x ./scripts/build-ios.sh
  ./scripts/build-ios.sh | tee -a "$BUILD_INFO_FILE"
  check_result "Build iOS"
else
  echo "Skipping iOS build (not on macOS)"
  echo "" >> "$BUILD_INFO_FILE"
  echo "=== Build iOS ===" >> "$BUILD_INFO_FILE"
  echo "Skipped (not on macOS)" >> "$BUILD_INFO_FILE"
fi

echo "Tous les builds ont été générés avec succès!"
echo "Consultez le fichier $BUILD_INFO_FILE pour plus d'informations."

# Lister les fichiers générés
echo ""
echo "Fichiers générés:"
find "$BUILDS_DIR" -type f -name "retreat-and-be-v${VERSION}-${DATE}*" | while read -r file; do
  echo "- $(basename "$file")"
done
