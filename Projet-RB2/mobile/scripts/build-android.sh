#!/bin/bash

# Script pour générer un APK signé pour Android

echo "Génération d'un APK signé pour Android..."

# Vérifier si le keystore existe
KEYSTORE_PATH="./android/app/retreat-and-be.keystore"
if [ ! -f "$KEYSTORE_PATH" ]; then
  echo "Le keystore n'existe pas à l'emplacement $KEYSTORE_PATH"
  echo "Veuillez d'abord exécuter le script generate-android-keystore.sh"
  exit 1
fi

# Définir les variables d'environnement
export ENVFILE=.env.production

# Nettoyer les builds précédents
echo "Nettoyage des builds précédents..."
cd android && ./gradlew clean && cd ..

# Générer l'APK de production
echo "Génération de l'APK de production..."
cd android && ./gradlew assembleRelease && cd ..

# Vérifier si la génération a réussi
APK_PATH="./android/app/build/outputs/apk/release/app-release.apk"
if [ -f "$APK_PATH" ]; then
  echo "APK généré avec succès à l'emplacement $APK_PATH"
  
  # Créer un dossier pour les builds
  BUILDS_DIR="./builds"
  mkdir -p "$BUILDS_DIR"
  
  # Copier l'APK dans le dossier des builds avec un nom incluant la version et la date
  VERSION=$(grep -o '"version": "[^"]*' package.json | cut -d'"' -f4)
  DATE=$(date +"%Y%m%d_%H%M%S")
  APK_NAME="retreat-and-be-v${VERSION}-${DATE}.apk"
  cp "$APK_PATH" "$BUILDS_DIR/$APK_NAME"
  
  echo "APK copié dans $BUILDS_DIR/$APK_NAME"
else
  echo "Erreur lors de la génération de l'APK."
  exit 1
fi

# Générer un bundle AAB (Android App Bundle) pour le Play Store
echo "Génération du bundle AAB pour le Play Store..."
cd android && ./gradlew bundleRelease && cd ..

# Vérifier si la génération du bundle a réussi
AAB_PATH="./android/app/build/outputs/bundle/release/app-release.aab"
if [ -f "$AAB_PATH" ]; then
  echo "Bundle AAB généré avec succès à l'emplacement $AAB_PATH"
  
  # Copier le bundle dans le dossier des builds
  AAB_NAME="retreat-and-be-v${VERSION}-${DATE}.aab"
  cp "$AAB_PATH" "$BUILDS_DIR/$AAB_NAME"
  
  echo "Bundle AAB copié dans $BUILDS_DIR/$AAB_NAME"
else
  echo "Erreur lors de la génération du bundle AAB."
  exit 1
fi

echo "Build Android terminé avec succès!"
echo "Fichiers générés:"
echo "- APK: $BUILDS_DIR/$APK_NAME"
echo "- AAB: $BUILDS_DIR/$AAB_NAME"
