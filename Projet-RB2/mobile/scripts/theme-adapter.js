#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Liste des fichiers à corriger
const filesToFix = [
  '../src/components/ui/BookingCard.tsx',
  '../src/components/ui/Card.tsx',
  '../src/components/ui/ConflictResolutionScreen.tsx',
  '../src/components/ui/FilterChip.tsx',
  '../src/components/ui/NotificationItem.tsx',
  '../src/components/ui/SettingsItem.tsx',
  '../src/screens/main/BookingConfirmationScreen.tsx',
  '../src/screens/main/BookingSuccessScreen.tsx'
];

// Code pour l'adaptateur de thème
const themeAdapterCode = `
// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
`;

// Modifier chaque fichier
filesToFix.forEach(filePath => {
  const fullPath = path.resolve(__dirname, filePath);
  
  try {
    if (!fs.existsSync(fullPath)) {
      console.log(`Le fichier ${filePath} n'existe pas, on le saute`);
      return;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Vérifier si l'adaptateur n'est pas déjà présent
    if (content.includes('getCompatibleTheme')) {
      console.log(`Le fichier ${filePath} contient déjà un adaptateur, on le saute`);
      return;
    }
    
    // Trouver l'importation du thème
    const importThemeMatch = content.match(/import\s+\{\s*useTheme\s*\}\s+from\s+['"].*?['"]/);
    if (!importThemeMatch) {
      console.log(`Le fichier ${filePath} n'importe pas useTheme, on le saute`);
      return;
    }
    
    // Ajouter l'adaptateur après l'importation du thème
    const importThemeIndex = importThemeMatch.index + importThemeMatch[0].length;
    const contentBefore = content.substring(0, importThemeIndex);
    const contentAfter = content.substring(importThemeIndex);
    content = contentBefore + '\n' + themeAdapterCode + contentAfter;
    
    // Remplacer const { theme } = useTheme(); par const { theme: originalTheme } = useTheme(); const theme = getCompatibleTheme(originalTheme);
    content = content.replace(
      /const\s+\{\s*theme\s*\}\s*=\s*useTheme\(\);/g,
      'const { theme: originalTheme } = useTheme();\n  // Utiliser l\'adaptateur de thème pour la compatibilité\n  const theme = getCompatibleTheme(originalTheme);'
    );
    
    // Écrire le fichier modifié
    fs.writeFileSync(fullPath, content);
    console.log(`Le fichier ${filePath} a été modifié avec succès`);
  } catch (error) {
    console.error(`Erreur lors de la modification du fichier ${filePath}:`, error);
  }
});

console.log('Terminé !'); 