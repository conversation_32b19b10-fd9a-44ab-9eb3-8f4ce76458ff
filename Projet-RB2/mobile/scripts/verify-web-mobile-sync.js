#!/usr/bin/env node

/**
 * Script de vérification de la synchronisation entre l'application web et mobile
 * Ce script vérifie que les données sont correctement synchronisées entre les deux plateformes
 */

const axios = require('axios');
const colors = require('colors/safe');
const { promisify } = require('util');
const sleep = promisify(setTimeout);

// Configuration
const config = {
  webApiUrl: process.env.WEB_API_URL || 'https://api.rb2.com',
  mobileApiUrl: process.env.MOBILE_API_URL || 'https://api.rb2.com/mobile',
  authToken: process.env.AUTH_TOKEN || 'test-token',
  endpoints: {
    retreats: '/retreats',
    bookings: '/user/bookings',
    notifications: '/user/notifications',
    profile: '/user/profile'
  },
  maxRetries: 3,
  retryDelay: 2000 // 2 secondes
};

// Fonction principale
async function verifySync() {
  console.log(colors.cyan('=== Vérification de la synchronisation Web/Mobile ==='));
  
  try {
    // Vérifier la connectivité aux APIs
    await checkApiConnectivity();
    
    // Vérifier les données des retraites
    await compareEndpoint('retreats');
    
    // Vérifier les données des réservations
    await compareEndpoint('bookings');
    
    // Vérifier les données des notifications
    await compareEndpoint('notifications');
    
    // Vérifier les données du profil utilisateur
    await compareEndpoint('profile');
    
    console.log(colors.green('\n✓ Toutes les vérifications sont passées avec succès!'));
    return true;
  } catch (error) {
    console.error(colors.red(`\n✗ Échec de la vérification: ${error.message}`));
    return false;
  }
}

// Vérifier la connectivité aux APIs
async function checkApiConnectivity() {
  console.log(colors.cyan('\nVérification de la connectivité aux APIs...'));
  
  try {
    await retryOperation(() => axios.get(`${config.webApiUrl}/health`));
    console.log(colors.green('✓ API Web accessible'));
  } catch (error) {
    throw new Error(`API Web inaccessible: ${error.message}`);
  }
  
  try {
    await retryOperation(() => axios.get(`${config.mobileApiUrl}/health`));
    console.log(colors.green('✓ API Mobile accessible'));
  } catch (error) {
    throw new Error(`API Mobile inaccessible: ${error.message}`);
  }
}

// Comparer les données entre les APIs web et mobile
async function compareEndpoint(endpointName) {
  console.log(colors.cyan(`\nVérification de la synchronisation des ${endpointName}...`));
  
  const endpoint = config.endpoints[endpointName];
  
  try {
    // Récupérer les données de l'API web
    const webResponse = await retryOperation(() => 
      axios.get(`${config.webApiUrl}${endpoint}`, {
        headers: { Authorization: `Bearer ${config.authToken}` }
      })
    );
    
    // Récupérer les données de l'API mobile
    const mobileResponse = await retryOperation(() => 
      axios.get(`${config.mobileApiUrl}${endpoint}`, {
        headers: { Authorization: `Bearer ${config.authToken}` }
      })
    );
    
    // Comparer les données
    const webData = webResponse.data;
    const mobileData = mobileResponse.data;
    
    if (Array.isArray(webData) && Array.isArray(mobileData)) {
      // Comparer les tableaux
      if (webData.length !== mobileData.length) {
        console.log(colors.yellow(`⚠ Différence de longueur pour ${endpointName}: Web (${webData.length}) vs Mobile (${mobileData.length})`));
      }
      
      // Vérifier les IDs
      const webIds = new Set(webData.map(item => item.id));
      const mobileIds = new Set(mobileData.map(item => item.id));
      
      const missingInMobile = [...webIds].filter(id => !mobileIds.has(id));
      const missingInWeb = [...mobileIds].filter(id => !webIds.has(id));
      
      if (missingInMobile.length > 0) {
        console.log(colors.yellow(`⚠ Éléments manquants dans l'API mobile: ${missingInMobile.join(', ')}`));
      }
      
      if (missingInWeb.length > 0) {
        console.log(colors.yellow(`⚠ Éléments manquants dans l'API web: ${missingInWeb.join(', ')}`));
      }
      
      if (missingInMobile.length === 0 && missingInWeb.length === 0) {
        console.log(colors.green(`✓ Synchronisation des ${endpointName} réussie`));
      }
    } else {
      // Comparer les objets
      const webKeys = Object.keys(webData);
      const mobileKeys = Object.keys(mobileData);
      
      const missingInMobile = webKeys.filter(key => !mobileKeys.includes(key));
      const missingInWeb = mobileKeys.filter(key => !webKeys.includes(key));
      
      if (missingInMobile.length > 0) {
        console.log(colors.yellow(`⚠ Propriétés manquantes dans l'API mobile: ${missingInMobile.join(', ')}`));
      }
      
      if (missingInWeb.length > 0) {
        console.log(colors.yellow(`⚠ Propriétés manquantes dans l'API web: ${missingInWeb.join(', ')}`));
      }
      
      if (missingInMobile.length === 0 && missingInWeb.length === 0) {
        console.log(colors.green(`✓ Synchronisation des ${endpointName} réussie`));
      }
    }
  } catch (error) {
    throw new Error(`Erreur lors de la comparaison des ${endpointName}: ${error.message}`);
  }
}

// Fonction utilitaire pour réessayer une opération en cas d'échec
async function retryOperation(operation, retries = config.maxRetries) {
  try {
    return await operation();
  } catch (error) {
    if (retries <= 1) throw error;
    
    await sleep(config.retryDelay);
    return retryOperation(operation, retries - 1);
  }
}

// Exécuter le script
verifySync()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error(colors.red(`Erreur fatale: ${error.message}`));
    process.exit(1);
  });
