/**
 * Tests d'accessibilité pour les composants de monitoring
 */

import React from 'react';
import { render } from '@testing-library/react-native';
import { SafeAreaView, TouchableOpacity, ScrollView, View } from 'react-native';
import PerformanceDashboard from '../../components/PerformanceDashboard';

// Mock des dépendances
jest.mock('../../utils/unifiedMonitoring', () => ({
  unifiedMonitoring: {
    getPerformanceStats: jest.fn().mockReturnValue({
      'http.api_request': {
        count: 5,
        sum: 1200,
        avg: 240,
        min: 100,
        max: 500
      }
    }),
    flush: jest.fn().mockResolvedValue(true)
  }
}));

jest.mock('../../contexts/MonitoringContext', () => ({
  useMonitoringContext: jest.fn().mockReturnValue({
    flushMetrics: jest.fn().mockResolvedValue(true),
    isEnabled: true
  })
}));

jest.mock('../../hooks/useMonitoring', () => {
  return jest.fn().mockReturnValue({
    trackUserAction: jest.fn()
  });
});

describe('Tests d\'accessibilité du PerformanceDashboard', () => {
  it('Le composant devrait se rendre sans erreurs', () => {
    const { getByText } = render(<PerformanceDashboard />);
    
    // Vérifier que le titre est présent
    const title = getByText('Tableau de Bord Performance');
    expect(title).toBeTruthy();
  });
  
  it('Les éléments interactifs devraient avoir des attributs d\'accessibilité appropriés', () => {
    const { getByRole } = render(<PerformanceDashboard />);
    
    // Chercher le bouton d'export
    const exportButton = getByRole('button', {
      name: 'Exporter les métriques'
    });
    
    expect(exportButton).toBeDefined();
  });
  
  it('Le container principal devrait avoir des attributs d\'accessibilité', () => {
    const { getByRole } = render(<PerformanceDashboard />);
    
    // Trouver le container principal
    const safeAreaView = getByRole('view', {
      name: 'Container principal'
    });
    expect(safeAreaView).toBeDefined();
  });
  
  it('Les onglets de catégorie devraient avoir un rôle d\'accessibilité approprié', () => {
    const { getAllByRole } = render(<PerformanceDashboard />);
    
    // Trouver tous les onglets
    const categoryTabs = getAllByRole('tab');
    
    // Vérifier qu'il y a au moins un onglet
    expect(categoryTabs.length).toBeGreaterThan(0);
    
    // Vérifier que chaque onglet a un label d'accessibilité
    categoryTabs.forEach(tab => {
      expect(tab.accessibilityLabel).toBeTruthy();
    });
  });
  
  it('Les cartes de métriques devraient avoir des propriétés d\'accessibilité', () => {
    const { getByTestId } = render(<PerformanceDashboard />);
    
    // Vérifier que les cartes de métriques ont un testID
    const metricCards = getByTestId('metric-card');
    expect(metricCards).toBeDefined();
    
    // Vérifier qu'elles ont des propriétés d'accessibilité
    expect(metricCards.accessible).toBe(true);
    expect(metricCards.accessibilityLabel).toBeTruthy();
  });
  
  it('La liste des métriques devrait être navigable', () => {
    const { getByRole } = render(<PerformanceDashboard />);
    
    // Chercher le ScrollView principal des métriques
    const scrollView = getByRole('list');
    expect(scrollView).toBeDefined();
    
    // Vérifier que le ScrollView est accessible
    expect(scrollView.accessible).toBe(true);
    expect(scrollView.accessibilityRole).toBe('list');
  });
});