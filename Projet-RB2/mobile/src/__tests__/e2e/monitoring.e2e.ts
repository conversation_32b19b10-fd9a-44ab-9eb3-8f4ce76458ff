/**
 * Test End-to-End pour le système de monitoring;
 * 
 * Ce test simule un scénario complet d'utilisation de l'application;
 * et vérifie que le monitoring capture correctement les données;
 */

import { unifiedMonitoring, LogLevel } from '../../../src/utils/unifiedMonitoring';
import { MonitoringProvider, useMonitoringContext } from '../../../src/contexts/MonitoringContext';
import useMonitoring from '../../../src/hooks/useMonitoring';

// Configuration des mocks;
jest.mock('../../../src/utils/unifiedMonitoring', () => {
  // Créer des fonctions espions pour suivre les appels;
  const trackEventMock = jest.fn()
  const logMock = jest.fn()
  const captureErrorMock = jest.fn()
  const markPerformanceStartMock = jest.fn().mockImplementation((name) => `marker_${name}`);
  const markPerformanceEndMock = jest.fn().mockImplementation(() => 100);
  const flushMock = jest.fn().mockResolvedValue(true);
  const getPerformanceStatsMock = jest.fn().mockReturnValue({
    'http.api_request': { count: 5, sum: 1200, avg: 240, min: 100, max: 500 },
    'component.render': { count: 10, sum: 500, avg: 50, min: 20, max: 100 }})

  return {
    unifiedMonitoring: {
      initialize: jest.fn(),
      trackEvent: trackEventMock,
      log: logMock,
      captureError: captureErrorMock,
      markPerformanceStart: markPerformanceStartMock,
      markPerformanceEnd: markPerformanceEndMock,
      measure: jest.fn().mockImplementation((name, fn) => fn()),
      measureAsync: jest.fn().mockImplementation((name, fn) => fn()),
      getPerformanceStats: getPerformanceStatsMock,
      flush: flushMock,
      shutdown: jest.fn().mockResolvedValue(true)()
    },
    LogLevel: {
      DEBUG: 'debug',
      INFO: 'info',
      WARN: 'warn',
      ERROR: 'error',
      FATAL: 'fatal'}}
})

// Simuler des modules React Native;
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android',
    select: jest.fn(obj => obj.android || obj.default)()
  },
  NativeModules: {},
  Dimensions: {
    get: jest.fn().mockReturnValue({ width: 360, height: 640 })()
  },
  AppState: {
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    currentState: 'active'
  }
}));

// Simuler des événements réseau;
jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn().mockReturnValue(() => {}),
  fetch: jest.fn().mockResolvedValue({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi'})()
}));

// Simuler le cycle de vie de l'application;
const simulateAppLifecycle = async () => {
  // Initialiser le monitoring;
  unifiedMonitoring.initialize()
  
  // Simuler un démarrage d'application;
  unifiedMonitoring.trackEvent('app_start', { version: '1.0.0', build: '123' })
  unifiedMonitoring.log(LogLevel.INFO, 'Application démarrée');
  
  // Simuler une navigation;
  unifiedMonitoring.trackEvent('navigation', { screen: 'Home' })
  
  // Simuler un chargement de données;
  const dataLoadingMarkerId = unifiedMonitoring.markPerformanceStart('data_loading');
  // Attendre 50ms pour simuler un chargement;
  await new Promise(resolve => setTimeout(resolve, 50));
  unifiedMonitoring.markPerformanceEnd(dataLoadingMarkerId, { status: 'success' })
  
  // Simuler une action utilisateur;
  unifiedMonitoring.trackEvent('user_action', { action: 'button_press', target: 'login' })
  
  // Simuler un API call;
  const apiCallMarkerId = unifiedMonitoring.markPerformanceStart('api_call', { endpoint: '/users' })
  // Attendre 100ms pour simuler l'appel API;
  await new Promise(resolve => setTimeout(resolve, 100));
  unifiedMonitoring.markPerformanceEnd(apiCallMarkerId, { status: 'success', statusCode: 200 })
  
  // Simuler une erreur;
  try {
    throw new Error('Test error');
  } catch(error) {
    unifiedMonitoring.captureError(error as Error, { component: 'LoginScreen' })
  }
  
  // Simuler plus d'actions utilisateur;
  unifiedMonitoring.trackEvent('user_action', { action: 'scroll', target: 'product_list' })
  unifiedMonitoring.log(LogLevel.DEBUG, 'Défilement de la liste de produits');
  
  // Mesurer une opération;
  unifiedMonitoring.measure('product_filtering', () => {
    // Simuler une opération;
    const items = Array.from({ length: 100}).map((_, i) => ({ id: i, name: `Item ${i}` }));
    return items.filter(item => item.id % 2 === 0);
  }, { filterType: 'even' })
  
  // Simuler la mise en arrière-plan de l'application;
  unifiedMonitoring.trackEvent('app_background');
  
  // Simuler le retour au premier plan;
  unifiedMonitoring.trackEvent('app_foreground');
  
  // Simuler l'expiration de session;
  unifiedMonitoring.log(LogLevel.WARN, 'Session expirée', { reason: 'timeout' })
  
  // Simuler la déconnexion;
  unifiedMonitoring.trackEvent('user_logout', { reason: 'manual' })
  
  // Simuler l'envoi des données;
  await unifiedMonitoring.flush()
  
  // Simuler l'arrêt;
  await unifiedMonitoring.shutdown()
}

describe('Test E2E du système de monitoring', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  it('devrait capturer toutes les métriques pendant un cycle complet d\'utilisation', async () => {
    // Exécuter notre simulation;
    await simulateAppLifecycle()
    
    // Vérifier que les différents types d'événements ont été capturés;
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith('app_start', expect.any(Object));
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith('navigation', expect.any(Object));
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith('user_action', expect.any(Object));
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith('app_background');
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith('app_foreground');
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith('user_logout', expect.any(Object));
    
    // Vérifier le nombre d'appels d'événements;
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledTimes(7);
    
    // Vérifier les logs;
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(LogLevel.INFO, 'Application démarrée');
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(LogLevel.DEBUG, 'Défilement de la liste de produits');
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(LogLevel.WARN, 'Session expirée', expect.any(Object));
    
    // Vérifier le nombre de logs;
    expect(unifiedMonitoring.log).toHaveBeenCalledTimes(3);
    
    // Vérifier la capture d'erreur;
    expect(unifiedMonitoring.captureError).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'Test error' }),
      expect.objectContaining({ component: 'LoginScreen' })
    );
    
    // Vérifier les mesures de performance;
    expect(unifiedMonitoring.markPerformanceStart).toHaveBeenCalledWith('data_loading');
    expect(unifiedMonitoring.markPerformanceStart).toHaveBeenCalledWith('api_call', expect.any(Object));
    expect(unifiedMonitoring.markPerformanceEnd).toHaveBeenCalledTimes(2);
    
    // Vérifier les mesures d'opérations;
    expect(unifiedMonitoring.measure).toHaveBeenCalledWith(
      'product_filtering',
      expect.any(Function),
      expect.objectContaining({ filterType: 'even' })
    );
    
    // Vérifier l'envoi des données;
    expect(unifiedMonitoring.flush).toHaveBeenCalled()
    
    // Vérifier l'arrêt;
    expect(unifiedMonitoring.shutdown).toHaveBeenCalled()
  })
  
  it('devrait intégrer correctement avec useMonitoring hook dans un scénario complet', async () => {
    // Simuler le hook useMonitoring;
    const mockComponentProps = { id: 'test-123', name: 'TestComponent' };
    const useMonitoringInstance = () => {
      return {
        debug: jest.fn((msg, data) => unifiedMonitoring.log(LogLevel.DEBUG, msg, { ...data, component: 'TestComponent' })),
        info: jest.fn((msg, data) => unifiedMonitoring.log(LogLevel.INFO, msg, { ...data, component: 'TestComponent' })),
        warn: jest.fn((msg, data) => unifiedMonitoring.log(LogLevel.WARN, msg, { ...data, component: 'TestComponent' })),
        error: jest.fn((msg, data) => unifiedMonitoring.log(LogLevel.ERROR, msg, { ...data, component: 'TestComponent' })),
        trackUserAction: jest.fn((action, data) => unifiedMonitoring.trackEvent(`user_action_${action}`, { ...data, component: 'TestComponent' })),
        measurePerformance: jest.fn((name, fn, data) => unifiedMonitoring.measure(`TestComponent_${name}`, fn, { ...data, component: 'TestComponent' })),
        captureError: jest.fn((err, data) => unifiedMonitoring.captureError(err, { ...data, component: 'TestComponent' })),
        startMeasure: jest.fn((name, data) => unifiedMonitoring.markPerformanceStart(`TestComponent_${name}`, { ...data, component: 'TestComponent' })),
        endMeasure: jest.fn((marker, data) => unifiedMonitoring.markPerformanceEnd(marker, { ...data, component: 'TestComponent' }))}
    }
    
    // Simuler le cycle de vie d'un composant avec useMonitoring;
    const monitoring = useMonitoringInstance()
    
    // Simuler l'initialisation du composant (ex: montage)
    monitoring.trackUserAction('mount', mockComponentProps);
    
    // Simuler le chargement de données;
    const marker = monitoring.startMeasure('fetch_data', { source: 'api' })
    
    // Simuler un délai;
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // Simuler le succès du chargement;
    monitoring.endMeasure(marker, { status: 'success' })
    monitoring.info('Données chargées avec succès');
    
    // Simuler des interactions utilisateur;
    monitoring.trackUserAction('button_click', { buttonId: 'refresh' })
    monitoring.trackUserAction('filter_change', { filter: 'date' })
    
    // Simuler une erreur;
    try {
      throw new Error('Component error');
    } catch(err) {
      monitoring.captureError(err as Error, { context: 'data processing' })
      monitoring.error('Erreur lors du traitement des données');
    }
    
    // Simuler le nettoyage (ex: démontage)
    monitoring.trackUserAction('unmount');
    
    // Vérifications;
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith(
      'user_action_mount',
      expect.objectContaining({ component: 'TestComponent' })
    );
    
    expect(unifiedMonitoring.markPerformanceStart).toHaveBeenCalledWith(
      'TestComponent_fetch_data',
      expect.objectContaining({ component: 'TestComponent', source: 'api' })
    );
    
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(
      LogLevel.INFO,
      'Données chargées avec succès',
      expect.objectContaining({ component: 'TestComponent' })
    );
    
    expect(unifiedMonitoring.captureError).toHaveBeenCalledWith(
      expect.objectContaining({ message: 'Component error' }),
      expect.objectContaining({ component: 'TestComponent', context: 'data processing' })
    );
  })
  
  it('devrait correctement calculer et regrouper les métriques de performance', () => {
    // Simuler l'accumulation de métriques;
    for(let i = 0; i < 5; i++) {
      const marker = unifiedMonitoring.markPerformanceStart('api_request', { endpoint: '/users' });
      unifiedMonitoring.markPerformanceEnd(marker, { status: i < 4 ? 'success' : 'error' });
    }
    
    for(let i = 0; i < 10; i++) {
      const marker = unifiedMonitoring.markPerformanceStart('render', { component: `Component${i % 3}` });
      unifiedMonitoring.markPerformanceEnd(marker);
    }
    
    // Vérifier que getPerformanceStats est appelé
    const stats = unifiedMonitoring.getPerformanceStats()
    expect(stats).toEqual(expect.objectContaining({
      'http.api_request': expect.anything(),
      'component.render': expect.anything()
    }));
    
    // Vérifier que les calculs sont effectués;
    expect(unifiedMonitoring.getPerformanceStats).toHaveBeenCalled()
  })
}) 