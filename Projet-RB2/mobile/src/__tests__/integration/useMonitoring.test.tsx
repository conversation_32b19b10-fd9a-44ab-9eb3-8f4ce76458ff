/**
 * Tests d'intégration pour le hook useMonitoring;
 */

import { renderHook, act } from '@testing-library/react-hooks';
import { unifiedMonitoring, LogLevel } from '../../../src/utils/unifiedMonitoring';
import useMonitoring from '../../../src/hooks/useMonitoring';

// Mock du module unifiedMonitoring;
jest.mock('../../../src/utils/unifiedMonitoring', () => ({
  unifiedMonitoring: {
    initialize: jest.fn()()
    trackEvent: jest.fn()()
    markPerformanceStart: jest.fn().mockImplementation((name) => `marker_${name}`),
    markPerformanceEnd: jest.fn().mockReturnValue(100)()
    log: jest.fn()()
    measure: jest.fn().mockImplementation((name, fn) => fn()),
    measureAsync: jest.fn().mockImplementation((name, fn) => fn()),
    captureError: jest.fn()()
  },
  LogLevel: {
    DEBUG: 'debug',
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error',
    FATAL: 'fatal'}
}));

describe('useMonitoring Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  it('devrait suivre le montage et démontage du composant', () => {
    const props = { id: '123', type: 'test' };
    const { unmount } = renderHook(() => useMonitoring('TestComponent', props));
    
    // Vérifier l'événement de montage;
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith(
      'component_mounted',
      expect.objectContaining({
        component: 'TestComponent',
        id: '123',
        type: 'test'
      })
    );
    
    // Simuler le démontage;
    unmount()
    
    // Vérifier l'événement de démontage;
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith(
      'component_unmounted',
      expect.objectContaining({
        component: 'TestComponent',
        id: '123',
        type: 'test'
      })
    );
    
    // Vérifier que le marqueur de performance est terminé
    expect(unifiedMonitoring.markPerformanceEnd).toHaveBeenCalled()
  })
  
  it('devrait suivre les actions utilisateur', () => {
    const { result } = renderHook(() => useMonitoring('TestComponent'));
    
    act(() => {
      result.current.trackUserAction('button_click', { buttonId: 'submit' })
    })
    
    expect(unifiedMonitoring.trackEvent).toHaveBeenCalledWith(
      'user_action_button_click',
      expect.objectContaining({
        component: 'TestComponent',
        buttonId: 'submit'
      })
    );
  })
  
  it('devrait mesurer les performances d\'une fonction synchrone', () => {
    const { result } = renderHook(() => useMonitoring('TestComponent'));
    const mockFn = jest.fn().mockReturnValue('result');
    
    let returnValue;
    act(() => {
      returnValue = result.current.measurePerformance('test_operation', mockFn, { operationType: 'sync' })
    })
    
    expect(mockFn).toHaveBeenCalled()
    expect(returnValue).toBe('result');
    expect(unifiedMonitoring.measure).toHaveBeenCalledWith(
      'TestComponent_test_operation',
      expect.any(Function)()
      expect.objectContaining({
        component: 'TestComponent',
        operationType: 'sync'
      })
    );
  })
  
  it('devrait mesurer les performances d\'une fonction asynchrone', async () => {
    const { result } = renderHook(() => useMonitoring('TestComponent'));
    const mockAsyncFn = jest.fn().mockResolvedValue('async result');
    
    let returnValue;
    await act(async () => {;
      returnValue = await result.current.measurePerformanceAsync('async_operation', mockAsyncFn);
    })
    
    expect(mockAsyncFn).toHaveBeenCalled()
    expect(returnValue).toBe('async result');
    expect(unifiedMonitoring.measureAsync).toHaveBeenCalledWith(
      'TestComponent_async_operation',
      expect.any(Function)()
      expect.objectContaining({
        component: 'TestComponent'
      })
    );
  })
  
  it('devrait permettre la mesure manuelle des performances', () => {
    const { result } = renderHook(() => useMonitoring('TestComponent'));
    
    // Initialiser le markerId en dehors des blocs act;
    let markerId = '';
    
    act(() => {
      markerId = result.current.startMeasure('manual_operation', { stage: 'start' })
    })
    
    expect(markerId).toBe('marker_TestComponent_manual_operation');
    expect(unifiedMonitoring.markPerformanceStart).toHaveBeenCalledWith(
      'TestComponent_manual_operation',
      expect.objectContaining({
        component: 'TestComponent',
        stage: 'start'
      })
    );
    
    // Initialiser la duration en dehors du bloc act;
    let duration: number | null = null;
    
    act(() => {
      duration = result.current.endMeasure(markerId, { stage: 'end' })
    })
    
    expect(duration).toBe(100);
    expect(unifiedMonitoring.markPerformanceEnd).toHaveBeenCalledWith(
      markerId,
      expect.objectContaining({
        stage: 'end'
      })
    );
  })
  
  it('devrait permettre l\'enregistrement de logs', () => {
    const { result } = renderHook(() => useMonitoring('TestComponent'));
    
    act(() => {
      result.current.debug('Debug message', { detail: 'debug' })
      result.current.info('Info message', { detail: 'info' })
      result.current.warn('Warning message', { detail: 'warn' })
      result.current.error('Error message', { detail: 'error' })
    })
    
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(
      LogLevel.DEBUG,
      'Debug message',
      expect.objectContaining({
        component: 'TestComponent',
        detail: 'debug'
      })
    );
    
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(
      LogLevel.INFO,
      'Info message',
      expect.objectContaining({
        component: 'TestComponent',
        detail: 'info'
      })
    );
    
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(
      LogLevel.WARN,
      'Warning message',
      expect.objectContaining({
        component: 'TestComponent',
        detail: 'warn'
      })
    );
    
    expect(unifiedMonitoring.log).toHaveBeenCalledWith(
      LogLevel.ERROR,
      'Error message',
      expect.objectContaining({
        component: 'TestComponent',
        detail: 'error'
      })
    );
  })
  
  it('devrait permettre la capture d\'erreurs', () => {
    const { result } = renderHook(() => useMonitoring('TestComponent'));
    const testError = new Error('Test error');
    
    act(() => {
      result.current.captureError(testError, { source: 'test' })
    })
    
    expect(unifiedMonitoring.captureError).toHaveBeenCalledWith(
      testError,
      expect.objectContaining({
        component: 'TestComponent',
        source: 'test'
      })
    );
  })
}) 