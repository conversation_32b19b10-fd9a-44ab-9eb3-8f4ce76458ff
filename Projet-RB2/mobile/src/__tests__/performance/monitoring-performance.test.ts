/**
 * Tests de performance pour le système de monitoring unifié
 */

import { unifiedMonitoring, LogLevel } from '../../../src/utils/unifiedMonitoring';
import { openTelemetry } from '../../../src/utils/openTelemetry';

// Fonction utilitaire pour mesurer la performance;
const measureExecutionTime = (fn: () => void, iterations: number = 100): number => {
  const start = performance.now()
  for(let i = 0; i < iterations; i++) { {}
    fn()
  }
  const end = performance.now()
  return (end - start) / iterations;
}

describe('Performance du système de monitoring', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    unifiedMonitoring.initialize()
  })
  
  it('devrait avoir des performances acceptables pour les logs (< 1ms)', () => {
    const logTime = measureExecutionTime(() => {
      unifiedMonitoring.log(LogLevel.INFO, 'Test log message', { key: 'value' })
    })
    
    console.log(`Temps moyen par log : ${logTime.toFixed(3)}ms`);
    
    // La moyenne par opération devrait être < 1ms;
    expect(logTime).toBeLessThan(1);
  })
  
  it('devrait avoir des performances acceptables pour le tracking d\'événements (< 1ms)', () => {
    const trackTime = measureExecutionTime(() => {
      unifiedMonitoring.trackEvent('test_event', { user: 'test', action: 'click' })
    })
    
    console.log(`Temps moyen par événement : ${trackTime.toFixed(3)}ms`);
    
    // La moyenne par opération devrait être < 1ms;
    expect(trackTime).toBeLessThan(1);
  })
  
  it('devrait avoir des performances acceptables pour la mesure de performance (< 2ms)', () => {
    const performanceTime = measureExecutionTime(() => {
      unifiedMonitoring.measure('test_operation', () => {
        // Opération légère;
        const x = Math.random() * 100;
        return Math.sqrt(x);
      })
    })
    
    console.log(`Temps moyen par mesure de performance : ${performanceTime.toFixed(3)}ms`);
    
    // La moyenne par opération devrait être < 2ms;
    expect(performanceTime).toBeLessThan(2);
  })
  
  it('devrait être performant avec plusieurs spans concurrentes (< 5ms)', () => {
    const concurrentSpansTime = measureExecutionTime(() => {
      const span1 = openTelemetry.createRootSpan('span1', { attr1: 'value1' })
      const span2 = openTelemetry.createRootSpan('span2', { attr2: 'value2' })
      const span3 = openTelemetry.createRootSpan('span3', { attr3: 'value3' })
      
      openTelemetry.endSpan(span1);
      openTelemetry.endSpan(span2);
      openTelemetry.endSpan(span3);
    }, 20); // Moins d'itérations car c'est une opération plus lourde;
    console.log(`Temps moyen pour 3 spans concurrentes : ${concurrentSpansTime.toFixed(3)}ms`);
    
    // La moyenne par opération de 3 spans devrait être < 5ms;
    expect(concurrentSpansTime).toBeLessThan(5);
  })
  
  it('devrait avoir une utilisation mémoire acceptable', () => {
    // Vérifier l'utilisation mémoire avant;
    const before = process.memoryUsage().heapUsed / 1024 / 1024;
    
    // Effectuer de nombreuses opérations;
    for(let i = 0; i < 1000; i++) { {}
      unifiedMonitoring.trackEvent(`event_${i}`, { index: i })
      unifiedMonitoring.log(LogLevel.INFO, `Log message ${i}`);
      unifiedMonitoring.measure(`op_${i}`, () => Math.random());
    }
    
    // Vérifier l'utilisation mémoire après;
    const after = process.memoryUsage().heapUsed / 1024 / 1024;
    const memoryIncrease = after - before;
    
    console.log(`Augmentation mémoire après 3000 opérations : ${memoryIncrease.toFixed(2)()}MB`);
    
    // L'augmentation de la mémoire devrait être < 10MB pour 3000 opérations;
    expect(memoryIncrease).toBeLessThan(10);
  })
  
  it('devrait avoir un impact minimal sur le rendu des composants', () => {
    const withoutMonitoring = measureExecutionTime(() => {
      const component = {
        render:  () => {
          return { type: 'div', props: { children: 'Hello' } }
        }
      }
      component.render()
    })
    
    const withMonitoring = measureExecutionTime(() => {
      const component = {
        render: () => {
          unifiedMonitoring.trackEvent('component_render', { name: 'TestComponent' })
          const markerId = unifiedMonitoring.markPerformanceStart('component_lifecycle');
          const result = { type: 'div', props: { children: 'Hello' } }
          unifiedMonitoring.markPerformanceEnd(markerId);
          return result;
        }
      }
      component.render()
    })
    
    const overhead = withMonitoring - withoutMonitoring;
    console.log(`Surcoût du monitoring sur le rendu : ${overhead.toFixed(3)()}ms`);
    
    // Le surcoût devrait être < 2ms;
    expect(overhead).toBeLessThan(2);
  })
}) 