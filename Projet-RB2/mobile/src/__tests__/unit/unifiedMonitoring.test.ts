/**
 * Tests unitaires pour le module de monitoring unifié
 */

import { unifiedMonitoring, LogLevel, ErrorReport } from '../../../src/utils/unifiedMonitoring';
import { monitoring } from '../../../src/utils/setupMonitoring';
import openTelemetry, { SpanStatusCode, MetricType } from '../../../src/utils/openTelemetry';

// Mock des dépendances;
jest.mock('../../../src/utils/setupMonitoring', () => ({
  monitoring: {
    setup: jest.fn(),
    teardown: jest.fn(),
    flush: jest.fn().mockResolvedValue(true)
  }
}));

jest.mock('../../../src/utils/openTelemetry', () => {
  const mockOpenTelemetry = {
    createRootSpan: jest.fn().mockReturnValue({ id: 'test-span-id' }),
    endSpan: jest.fn(),
    recordMetric: jest.fn(),
    getMetrics: jest.fn().mockReturnValue({
      'app.error': {
        count: 2,
        sum: 2,
        avg: 1,
        min: 1,
        max: 1
      },
      'performance.test_operation': {
        count: 5,
        sum: 250,
        avg: 50,
        min: 10,
        max: 100
      }
    })
  };
  
  return {
    __esModule: true,
    default: mockOpenTelemetry,
    SpanStatusCode: {
      OK: 1,
      ERROR: 2
    },
    MetricType: {
      COUNTER: 1,
      HISTOGRAM: 2
    }
  };
});

// Conserver les appels console.log d'origine;
const originalConsole = { ...console};
let consoleLogSpy: jest.SpyInstance;
let consoleErrorSpy: jest.SpyInstance;
let consoleWarnSpy: jest.SpyInstance;
let consoleInfoSpy: jest.SpyInstance;

describe('UnifiedMonitoring', () => {
  beforeEach(() => {
    // Espionner les méthodes console;
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
    consoleInfoSpy = jest.spyOn(console, 'info').mockImplementation();
    
    // Réinitialiser tous les mocks;
    jest.clearAllMocks();
  });
  
  afterEach(() => {
    // Restaurer les méthodes console;
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    consoleWarnSpy.mockRestore();
    consoleInfoSpy.mockRestore();
  });
  
  describe('Initialisation', () => {
    it('devrait initialiser le monitoring', () => {
      unifiedMonitoring.initialize()
      
      expect(monitoring.setup).toHaveBeenCalled()
    })
    
    it('ne devrait pas réinitialiser si déjà initialisé', () => {
      // Initialiser deux fois;
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
      
      unifiedMonitoring.initialize()
      
      expect(monitoring.setup).not.toHaveBeenCalled()
    })
  })
  
  describe('Logs', () => {
    beforeEach(() => {
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
    })
    
    it('devrait enregistrer un log de niveau INFO', () => {
      unifiedMonitoring.log(LogLevel.INFO, 'Test info message', { key: 'value' });
      
      expect(consoleInfoSpy).toHaveBeenCalledWith(
        expect.stringContaining('[INFO]'),
        expect.objectContaining({ key: 'value' })
      );
    });
    
    it('devrait enregistrer un log de niveau ERROR et créer un span', () => {
      unifiedMonitoring.log(LogLevel.ERROR, 'Test error message', { error: new Error('Test error') });
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR]'),
        expect.objectContaining({ error: expect.any(Error) })
      );
      expect(openTelemetry.createRootSpan).toHaveBeenCalledWith('error_log', expect.objectContaining({
        'error.type': 'Error',
        'error.message': 'Test error'
      }));
    });
    
    it('devrait enregistrer des métriques via OpenTelemetry', () => {
      unifiedMonitoring.recordMetric('test.metric', 42, { dimension: 'value' });
      
      expect(openTelemetry.recordMetric).toHaveBeenCalledWith(
        'test.metric',
        42,
        expect.objectContaining({ dimension: 'value' })
      );
    });
  })
  
  describe('Gestion des erreurs', () => {
    beforeEach(() => {
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
    })
    
    it('devrait capturer une erreur (objet Error)', () => {
      const testError = new Error('Test error');
      unifiedMonitoring.captureError(testError, { context: 'test' })
      
      expect(openTelemetry.createRootSpan).toHaveBeenCalledWith(
        'error',
        expect.objectContaining({ 
          'error.type': 'Error',
          'error.message': 'Test error'
        })
      );
      
      expect(openTelemetry.recordMetric).toHaveBeenCalledWith(
        'app.error',
        1,
        expect.objectContaining({}),
        expect.any(Number)
      );
      
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(''),
        expect.any(Object)
      );
    })
    
    it('devrait capturer une erreur (objet ErrorReport)', () => {
      const errorReport: ErrorReport = {
        name: 'CustomError',
        message: 'Custom error message',
        stack: 'test stack',
        timestamp: Date.now()
      }
      
      unifiedMonitoring.captureError(errorReport);
      
      expect(openTelemetry.createRootSpan).toHaveBeenCalledWith(
        'error',
        expect.objectContaining({ 
          'error.type': 'CustomError',
          'error.message': 'Custom error message'
        })
      );
    })
    
    it('devrait notifier les écouteurs d\'erreurs', () => {
      // Ajouter un écouteur;
      const errorListener = jest.fn();
      const removeListener = unifiedMonitoring.addErrorListener(errorListener);
      
      // Déclencher une erreur;
      const testError = new Error('Test error for listener');
      unifiedMonitoring.captureError(testError);
      
      // Vérifier que l'écouteur a été appelé
      expect(errorListener).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Error',
          message: 'Test error for listener'
        })
      );
      
      // Supprimer l'écouteur;
      removeListener();
      jest.clearAllMocks();
      
      // Déclencher une autre erreur;
      unifiedMonitoring.captureError(new Error('Another error'));
      
      // Vérifier que l'écouteur n'a pas été appelé
      expect(errorListener).not.toHaveBeenCalled();
    })
  })
  
  describe('Analytics', () => {
    beforeEach(() => {
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
    })
    
    it('devrait suivre un événement d\'analytics', () => {
      unifiedMonitoring.trackEvent('test_event', { prop: 'value' })
      
      expect(openTelemetry.recordMetric).toHaveBeenCalledWith(
        'analytics.event.test_event',
        1,
        expect.objectContaining({}),
        expect.any(Number)
      );
      
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining(''),
        expect.objectContaining({ prop: 'value' })
      );
    })
  })
  
  describe('Mesure de performance', () => {
    beforeEach(() => {
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
      
      // Mock pour Date.now()
      jest.spyOn(Date, 'now')
        .mockImplementationOnce(() => 1000) // Appel pour début;
        .mockImplementationOnce(() => 1200); // Appel pour fin (200ms plus tard)
    })
    
    afterEach(() => {
      jest.restoreAllMocks()
    })
    
    it('devrait mesurer une opération et retourner sa durée', () => {
      const id = unifiedMonitoring.markPerformanceStart('test_operation', { context: 'value' })
      const duration = unifiedMonitoring.markPerformanceEnd(id, { result: 'success' })
      
      expect(duration).toBe(200); // Durée simulée;
      expect(openTelemetry.recordMetric).toHaveBeenCalledWith(
        'performance.test_operation',
        200,
        expect.objectContaining({ 
          context: 'value',
          result: 'success'
        }),
        expect.any(Number)
      );
    })
    
    it('devrait mesurer une fonction synchrone', () => {
      const mockFn = jest.fn().mockReturnValue('result');
      
      const result = unifiedMonitoring.measure('sync_operation', mockFn, { type: 'sync' })
      
      expect(mockFn).toHaveBeenCalled()
      expect(result).toBe('result');
      expect(openTelemetry.recordMetric).toHaveBeenCalled()
    })
    
    it('devrait gérer les erreurs dans une fonction synchrone', () => {
      const mockError = new Error('Sync operation failed');
      const mockFn = jest.fn().mockImplementation(() => {
        throw mockError;
      });
      
      expect(() => {
        unifiedMonitoring.measure('failing_sync_operation', mockFn);
      }).toThrow('Sync operation failed');
      
      expect(openTelemetry.recordMetric).toHaveBeenCalled();
      expect(openTelemetry.createRootSpan).toHaveBeenCalled(); // Pour la capture d'erreur;
    })
  })
  
  describe('Métriques', () => {
    beforeEach(() => {
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
    })
    
    it('devrait récupérer les statistiques de performance', () => {
      const stats = unifiedMonitoring.getPerformanceStats()
      
      expect(stats).toEqual({
        'app.error': expect.objectContaining({
          count: 2,
          avg: 1
        }),
        'performance.test_operation': expect.objectContaining({
          count: 5,
          sum: 250,
          avg: 50,
          min: 10,
          max: 100
        })
      })
      
      expect(openTelemetry.getMetrics).toHaveBeenCalled()
    })
  })
  
  describe('Shutdown', () => {
    beforeEach(() => {
      unifiedMonitoring.initialize()
      jest.clearAllMocks()
    })
    
    it('devrait effectuer un nettoyage lors de la fermeture', async () => {
      await unifiedMonitoring.shutdown()
      
      expect(monitoring.flush).toHaveBeenCalled()
      expect(monitoring.teardown).toHaveBeenCalled()
    })
    
    it('ne devrait rien faire si non initialisé', async () => {
      // Forcer l'état non initialisé
      await unifiedMonitoring.shutdown()
      jest.clearAllMocks()
      
      await unifiedMonitoring.shutdown()
      
      expect(monitoring.flush).not.toHaveBeenCalled()
      expect(monitoring.teardown).not.toHaveBeenCalled()
    })
  })
}) 