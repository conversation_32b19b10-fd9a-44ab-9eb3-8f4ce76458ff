import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useApiMonitoring } from '../hooks/useApiMonitoring';

interface ApiStatsProps {
  refreshInterval?: number;
  showControls?: boolean;
  compact?: boolean;
  style?: any;
}

/**
 * Composant pour afficher les statistiques de performance de l'API;
 */
const ApiStats: React.FC<ApiStatsProps> = ({
  refreshInterval = 5000,
  showControls = true,
  compact = false,
  style}) => {
  const { stats, refreshStats, flushMetrics, isOffline } = useApiMonitoring(
    undefined, 
    refreshInterval
  );
  
  const [isFlushing, setIsFlushing] = React.useState(false);
  
  // Arrondir les nombres à 2 décimales;
  const formatPercent = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  }
  
  // Formater le temps de réponse;
  const formatResponseTime = (time: number) => {
    if(time < 1) {
      return '< 1ms';
    }
    if(time < 1000) {
      return `${time.toFixed(0)}ms`;
    }
    return `${(time / 1000).toFixed(2)}s`;
  }
  
  // Forcer l'envoi des métriques;
  const handleFlushMetrics = async () => {
    setIsFlushing(true);
    try {
      await flushMetrics()
    } finally {
      setIsFlushing(false);
    }
  }

  // Version compacte (pour les barres d'état, etc.)
  if(compact) {
    return (
      <View style={[styles.compactContainer, style]}>
        <Text style={[styles.statusText, isOffline ? styles.offline : styles.online]}>
          {isOffline ? 'Hors ligne' : 'En ligne'}
        </Text>
        <Text style={styles.compactStat}>
          {stats.totalRequests} req | {formatResponseTime(stats.avgResponseTime)}
        </Text>
        {showControls && (
          <TouchableOpacity
            style={styles.compactButton}
            onPress={refreshStats}
            activeOpacity={0.7}
          >
            <Text style={styles.compactButtonText}>↺</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  // Version complète;
  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>Statistiques API</Text>
        <View style={styles.statusBadge}>
          <Text style={[styles.statusText, isOffline ? styles.offline : styles.online]}>
            {isOffline ? 'Hors ligne' : 'En ligne'}
          </Text>
        </View>
      </View>
      
      <View style={styles.statsContainer}>
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>Requêtes:</Text>
          <Text style={styles.statValue}>{stats.totalRequests}</Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>Taux de succès:</Text>
          <Text style={[
            styles.statValue, 
            stats.successRate < 0.9 ? styles.warning : styles.success
          ]}>
            {formatPercent(stats.successRate)}
          </Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>Taux d'erreur:</Text>
          <Text style={[
            styles.statValue, 
            stats.errorRate > 0.1 ? styles.error : styles.success
          ]}>
            {formatPercent(stats.errorRate)}
          </Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>Taux de cache:</Text>
          <Text style={styles.statValue}>{formatPercent(stats.cacheHitRate)}</Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>Temps de réponse moyen:</Text>
          <Text style={[
            styles.statValue,
            stats.avgResponseTime > 1000 ? styles.warning : 
            stats.avgResponseTime > 2000 ? styles.error : styles.success
          ]}>
            {formatResponseTime(stats.avgResponseTime)}
          </Text>
        </View>
        
        <View style={styles.statRow}>
          <Text style={styles.statLabel}>Réseau:</Text>
          <Text style={styles.statValue}>{stats.networkType}</Text>
        </View>
      </View>
      
      {showControls && (
        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.button}
            onPress={refreshStats}
            activeOpacity={0.7}
          >
            <Text style={styles.buttonText}>Rafraîchir</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, isFlushing && styles.buttonDisabled]} 
            onPress={handleFlushMetrics}
            disabled={isFlushing}
            activeOpacity={0.7}
          >
            {isFlushing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Envoyer les métriques</Text>
            )}
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16},
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333'},
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: '#eee'},
  statusText: {
    fontWeight: 'bold',
    fontSize: 12},
  online: {
    color: '#28a745'},
  offline: {
    color: '#dc3545'},
  statsContainer: {
    marginBottom: 16},
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee'},
  statLabel: {
    color: '#666',
    fontSize: 14},
  statValue: {
    fontWeight: 'bold',
    color: '#333',
    fontSize: 14},
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between'},
  button: {
    backgroundColor: '#007bff',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center'},
  buttonDisabled: {
    backgroundColor: '#6c757d'},
  buttonText: {
    color: '#fff',
    fontWeight: 'bold'},
  success: {
    color: '#28a745'},
  warning: {
    color: '#ffc107'},
  error: {
    color: '#dc3545'},
  // Styles pour la version compacte;
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4},
  compactStat: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8},
  compactButton: {
    marginLeft: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#007bff',
    justifyContent: 'center',
    alignItems: 'center'},
  compactButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12}})

export default ApiStats;