import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  useColorScheme,
  SafeAreaView,
  Platform} from 'react-native';
import { useDashboardConfig } from '../hooks/useDashboardConfig';
import { MetricConfig } from '../types/dashboardTypes';
import { unifiedMonitoring } from '../utils/unifiedMonitoring';
import MetricCard from './ui/MetricCard';
import FilterModal from './ui/FilterModal';
import Button from './ui/Button';
import DashboardHeader from './ui/DashboardHeader';
import EmptyState from './ui/EmptyState';
import useMonitoring from '../hooks/useMonitoring';
import AddDashboardModal from './DashboardCustomizer';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

/**
 * Composant de tableau de bord personnalisable;
 * Permet aux utilisateurs de personnaliser les métriques affichées et de gérer plusieurs tableaux de bord;
 */
const CustomizableDashboard: React.FC = () => {
  // État pour la visibilité du customizer;
  const [isCustomizerVisible, setIsCustomizerVisible] = useState(false);
  const [editingDashboardId, setEditingDashboardId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Dark mode;
  const colorScheme = useColorScheme()
  const isDarkMode = colorScheme === 'dark';
  
  // Monitoring;
  const { trackUserAction, measurePerformance } = useMonitoring('CustomizableDashboard');
  
  // Configuration du tableau de bord;
  const {
    dashboards,
    activeDashboard,
    activeView,
    visibleMetrics,
    isLoading,
    preferences,
    setActiveDashboard,
    setActiveView,
    toggleMetricVisibility,
    refreshMetrics} = useDashboardConfig()
  
  // Refreshing des métriques;
  useEffect(() => {
    const refreshInterval = activeView.refreshInterval || 30; // 30 secondes par défaut;
    // Si autoRefresh est activé dans les préférences;
    if(preferences.autoRefresh) {
      const interval = setInterval(() => {
        refreshMetrics()
      }, refreshInterval * 1000);
      
      return () => {
        clearInterval(interval);
      }
    }
  }, [activeView, preferences.autoRefresh, refreshMetrics]);
  
  // Fonction pour rafraîchir manuellement;
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    trackUserAction('refresh_dashboard');
    
    try {
      await refreshMetrics()
    } catch(error) {
      console.error('Erreur lors du rafraîchissement des métriques:', error);
      unifiedMonitoring.captureError(error as Error, { context: 'CustomizableDashboard.handleRefresh' })
    } finally {
      setRefreshing(false);
    }
  }, [refreshMetrics, trackUserAction]);
  
  // Ouvrir le customizer;
  const handleOpenCustomizer = useCallback(() => {
    setIsCustomizerVisible(true);
    setEditingDashboardId(null);
    trackUserAction('open_dashboard_customizer');
  }, [trackUserAction]);
  
  // Fermer le customizer;
  const handleCloseCustomizer = useCallback(() => {
    setIsCustomizerVisible(false);
    setEditingDashboardId(null);
    trackUserAction('close_dashboard_customizer');
  }, [trackUserAction]);
  
  // Éditer un tableau de bord existant;
  const handleEditDashboard = useCallback((dashboardId: string) => {
    setEditingDashboardId(dashboardId);
    setIsCustomizerVisible(true);
    trackUserAction('edit_dashboard', { dashboardId })
  }, [trackUserAction]);
  
  // Changer de tableau de bord;
  const handleChangeDashboard = useCallback((dashboardId: string) => {
    measurePerformance('change_dashboard', () => {
      setActiveDashboard(dashboardId);
      trackUserAction('change_dashboard', { dashboardId })
    })
  }, [setActiveDashboard, trackUserAction, measurePerformance]);
  
  // Changer de vue;
  const handleChangeView = useCallback((viewId: string) => {
    measurePerformance('change_view', () => {
      setActiveView(viewId);
      trackUserAction('change_view', { viewId })
    })
  }, [setActiveView, trackUserAction, measurePerformance]);
  
  // Gérer la visibilité des métriques;
  const handleToggleMetric = useCallback((metricId: string) => {
    toggleMetricVisibility(metricId);
    trackUserAction('toggle_metric', { metricId })
  }, [toggleMetricVisibility, trackUserAction]);
  
  // Obtenir le nombre de colonnes pour l'affichage;
  const getColumnCount = () => {
    return activeView.layout?.columns || 2;
  }
  
  // Obtenir le style de colonne en fonction du nombre de colonnes;
  const getColumnStyle = () => {
    return { 
      flex: 1}
  }
  
  // Fonction pour trier les métriques par ordre;
  const sortedMetrics = [...visibleMetrics].sort((a, b) => {
    const orderA = a.order || 0;
    const orderB = b.order || 0;
    return orderA - orderB;
  })
  
  // Grouper les métriques par catégorie si nécessaire;
  const getGroupedMetrics = () => {
    if(!activeView.layout?.groupByCategory) {
      return { ungrouped: sortedMetrics }
    }
    
    const grouped: Record<string, MetricConfig[]> = {}
    
    sortedMetrics.forEach(metric => {
      const category = metric.category || 'other';
      if(!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(metric);
    })
    
    return grouped;
  }
  
  // Récupérer les métriques groupées;
  const groupedMetrics = getGroupedMetrics()
  
  // Render des métriques;
  const renderMetrics = (metrics: MetricConfig[]) => {
    return metrics.map(metric => (
      <View key={metric.id} style={[styles.column, getColumnStyle()]}>
        <MetricCard
          metric={metric}
          onToggle={() => handleToggleMetric(metric.id)}
          isDarkMode={isDarkMode}
        />
      </View>
    ));
  }
  
  // Message d'erreur;
  if(isLoading) {
    return (
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        <ActivityIndicator size="large" color={isDarkMode ? '#0A84FF' : '#007AFF'} />
      </View>
    );
  }
  
  // Render par catégorie si nécessaire;
  const renderContent = () => {
    if (Object.keys(groupedMetrics).length === 0 || 
        (Object.keys(groupedMetrics).length === 1 && 
         groupedMetrics.ungrouped && 
         groupedMetrics.ungrouped.length === 0)) {
      return (
        <EmptyState
          icon="chart-line"
          title="Aucune métrique disponible"
          description="Ajoutez des métriques à ce tableau de bord ou activez les métriques existantes."
          actionLabel="Personnaliser"
          onAction={handleOpenCustomizer}
          isDarkMode={isDarkMode}
        />
      );
    }
    
    if(!activeView.layout?.groupByCategory) {
      return (
        <View style={styles.metricsContainer}>
          {renderMetrics(groupedMetrics.ungrouped || [])}
        </View>
      );
    }
    
    // Afficher les métriques par catégorie;
    return Object.entries(groupedMetrics).map(([category, metrics]) => {
      if (metrics.length === 0) return null;
      
      // Formater le nom de la catégorie;
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      
      return (
        <View key={category} style={styles.categoryContainer}>
          <Text style={[styles.categoryTitle, isDarkMode && styles.categoryTitleDark]}>
            {categoryName}
          </Text>
          <View style={styles.metricsContainer}>
            {renderMetrics(metrics)}
          </View>
        </View>
      );
    });
  }
  
  return (
    <View style={[styles.container, isDarkMode && styles.containerDark]}>
      <DashboardHeader
        dashboards={dashboards}
        activeDashboard={activeDashboard}
        activeView={activeView}
        onChangeDashboard={handleChangeDashboard}
        onChangeView={handleChangeView}
        onOpenCustomizer={handleOpenCustomizer}
        onEditDashboard={() => handleEditDashboard(activeDashboard.id)}
        isDarkMode={isDarkMode}
      />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor={isDarkMode ? '#0A84FF' : '#007AFF'}
          />
        }
      >
        {renderContent()}
      </ScrollView>
      
      <TouchableOpacity
        style={[styles.fab, isDarkMode && styles.fabDark]}
        onPress={handleOpenCustomizer}
      >
        <Icon name="pencil" size={24} color="#FFFFFF" />
      </TouchableOpacity>
      
      <AddDashboardModal
        visible={isCustomizerVisible}
        onClose={handleCloseCustomizer}
        activeDashboard={editingDashboardId ? dashboards.find(d => d.id === editingDashboardId) : undefined}
      />
    </View>
  );
}

// Styles;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA'},
  containerDark: {
    backgroundColor: '#121212'},
  scrollView: {
    flex: 1},
  scrollContent: {
    padding: 16,
    paddingBottom: 80, // Espace pour le FAB;
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8},
  column: {
    paddingHorizontal: 8,
    marginBottom: 16},
  categoryContainer: {
    marginBottom: 24},
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333333'},
  categoryTitleDark: {
    color: '#E0E0E0'},
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4},
  fabDark: {
    backgroundColor: '#0A84FF'}})

export default CustomizableDashboard;