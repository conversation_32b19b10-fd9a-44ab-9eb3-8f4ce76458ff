/**
 * Tableau de bord de performance
 * 
 * Composant permettant de visualiser en temps réel les métriques de performance
 * collectées par le système de monitoring.
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
  RefreshControl,
  SafeAreaView
} from 'react-native';
import { unifiedMonitoring } from '../utils/unifiedMonitoring';
import { useMonitoringContext } from '../contexts/MonitoringContext';
import useMonitoring from '../hooks/useMonitoring';

// Types pour les métriques de performance
interface MetricCategory {
  name: string;
  metrics: Metric[];
}

interface Metric {
  name: string;
  count: number;
  sum: number;
  avg: number;
  min: number;
  max: number;
}

// Catégories de métriques pour l'affichage
const CATEGORIES = [
  { id: 'http', label: 'Requêtes HTTP' },
  { id: 'component', label: 'Composants' },
  { id: 'performance', label: 'Performance' },
  { id: 'operation', label: 'Opérations' },
  { id: 'analytics', label: 'Analytics' },
  { id: 'other', label: 'Autres' },
];

/**
 * Composant de tableau de bord de performance
 */
const PerformanceDashboard: React.FC = () => {
  // Récupérer l'instance de monitoring du contexte et du hook
  const monitoring = useMonitoringContext();
  const { trackUserAction } = useMonitoring('PerformanceDashboard');
  
  // État pour les données de métriques
  const [metricsData, setMetricsData] = useState<Record<string, MetricCategory>>({});
  const [selectedCategory, setSelectedCategory] = useState<string>('http');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [totalMetrics, setTotalMetrics] = useState<number>(0);
  
  // Fonction pour récupérer et organiser les métriques
  const fetchMetrics = useCallback(async () => {
    // Récupérer les métriques brutes
    const rawMetrics = unifiedMonitoring.getPerformanceStats();
    
    // Organiser les métriques par catégorie
    const categorized: Record<string, MetricCategory> = {};
    let metricCount = 0;
    
    Object.entries(rawMetrics).forEach(([name, data]) => {
      // Déterminer la catégorie
      let category = 'other';
      
      if (name.startsWith('http.')) {
        category = 'http';
      } else if (name.startsWith('component.')) {
        category = 'component';
      } else if (name.startsWith('performance.')) {
        category = 'performance';
      } else if (name.startsWith('operation.')) {
        category = 'operation';
      } else if (name.startsWith('analytics.')) {
        category = 'analytics';
      }
      
      // Créer la catégorie si elle n'existe pas encore
      if(!categorized[category]) {
        categorized[category] = {
          name: CATEGORIES.find(c => c.id === category)?.label || 'Autres',
          metrics: []
        };
      }
      
      // Ajouter la métrique à sa catégorie
      categorized[category].metrics.push({
        name,
        count: data.count,
        sum: data.sum,
        avg: data.avg,
        min: data.min,
        max: data.max
      });
      
      metricCount++;
    });
    
    // Trier les métriques par nom
    Object.values(categorized).forEach(category => {
      category.metrics.sort((a, b) => a.name.localeCompare(b.name));
    });
    
    setMetricsData(categorized);
    setTotalMetrics(metricCount);
  }, []);
  
  // Charger les métriques au démarrage et périodiquement
  useEffect(() => {
    fetchMetrics();
    
    // Actualiser les métriques toutes les 5 secondes
    const intervalId = setInterval(fetchMetrics, 5000);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [fetchMetrics]);
  
  // Gérer le rafraîchissement manuel
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    trackUserAction('refresh_dashboard');
    
    try {
      await fetchMetrics();
    } finally {
      setRefreshing(false);
    }
  }, [fetchMetrics, trackUserAction]);
  
  // Gérer le changement de catégorie
  const handleCategoryChange = useCallback((categoryId: string) => {
    setSelectedCategory(categoryId);
    trackUserAction('change_category', { category: categoryId });
  }, [trackUserAction]);
  
  // Gérer le flush manuel des métriques
  const handleFlushMetrics = useCallback(async () => {
    trackUserAction('flush_metrics');
    
    try {
      await monitoring.flushMetrics();
      await fetchMetrics();
    } catch(error) {
      console.error('Erreur lors du flush des métriques:', error);
    }
  }, [monitoring, fetchMetrics, trackUserAction]);
  
  // Rendu du composant
  return (
    <SafeAreaView style={styles.container} accessible={true} accessibilityRole="none">
      <View style={styles.header} accessibilityRole="header">
        <Text style={styles.title} accessibilityRole="header">Tableau de Bord Performance</Text>
        <Text
          style={styles.subtitle}
          accessibilityLabel={`${totalMetrics} métriques collectées`}
        >
          {totalMetrics} métriques collectées
        </Text>
      </View>
      
      {/* Sélecteur de catégorie */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categorySelector}
        contentContainerStyle={styles.categorySelectorContent}
        accessibilityRole="tablist"
        accessibilityLabel="Catégories de métriques"
      >
        {CATEGORIES.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryTab,
              selectedCategory === category.id && styles.categoryTabActive
            ]}
            onPress={() => handleCategoryChange(category.id)}
            accessibilityRole="tab"
            accessibilityLabel={category.label}
            accessibilityState={{ selected: selectedCategory === category.id}}
          >
            <Text
              style={[
                styles.categoryTabText,
                selectedCategory === category.id && styles.categoryTabTextActive
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      {/* Liste des métriques */}
      <ScrollView
        style={styles.metricsContainer}
        testID="metrics-scrollview"
        accessibilityLabel="Liste des métriques"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            accessibilityLabel="Rafraîchir les métriques" 
          />}
      >
        {metricsData[selectedCategory]?.metrics.map((metric, index) => (
          <View
            key={metric.name}
            style={styles.metricCard}
            testID="metric-card"
            accessible={true}
            accessibilityLabel={`Métrique ${formatMetricName(metric.name)}`}
            accessibilityRole="text"
          >
            <Text style={styles.metricName}>{formatMetricName(metric.name)}</Text>
            
            <View style={styles.metricDetailsContainer}>
              <View style={styles.metricDetail}>
                <Text style={styles.metricDetailLabel}>Compte</Text>
                <Text
                  style={styles.metricDetailValue}
                  accessibilityLabel={`Compte: ${metric.count}`}
                >
                  {metric.count}
                </Text>
              </View>
              
              <View style={styles.metricDetail}>
                <Text style={styles.metricDetailLabel}>Moyenne</Text>
                <Text
                  style={styles.metricDetailValue}
                  accessibilityLabel={`Moyenne: ${formatValue(metric.avg, metric.name)}`}
                >
                  {formatValue(metric.avg, metric.name)}
                </Text>
              </View>
              
              <View style={styles.metricDetail}>
                <Text style={styles.metricDetailLabel}>Min</Text>
                <Text
                  style={styles.metricDetailValue}
                  accessibilityLabel={`Minimum: ${formatValue(metric.min, metric.name)}`}
                >
                  {formatValue(metric.min, metric.name)}
                </Text>
              </View>
              
              <View style={styles.metricDetail}>
                <Text style={styles.metricDetailLabel}>Max</Text>
                <Text
                  style={styles.metricDetailValue}
                  accessibilityLabel={`Maximum: ${formatValue(metric.max, metric.name)}`}
                >
                  {formatValue(metric.max, metric.name)}
                </Text>
              </View>
            </View>
          </View>
        ))}
        
        {(!metricsData[selectedCategory] || metricsData[selectedCategory]?.metrics.length === 0) && (
          <View
            style={styles.emptyContainer}
            accessibilityLabel="Aucune métrique disponible dans cette catégorie"
          >
            <Text style={styles.emptyText}>
              Aucune métrique disponible dans cette catégorie
            </Text>
          </View>
        )}
      </ScrollView>
      
      {/* Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleFlushMetrics}
          accessibilityRole="button"
          accessibilityLabel="Exporter les métriques vers le serveur"
          accessibilityHint="Envoie toutes les métriques collectées vers le serveur de télémétrie"
        >
          <Text style={styles.actionButtonText}>Exporter les métriques</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

// Fonctions utilitaires pour le formatage des données
const formatMetricName = (name: string): string => {
  // Enlever le préfixe de la catégorie
  const parts = name.split('.');
  if(parts.length > 1) {
    return parts.slice(1).join('.');
  }
  return name;
};

const formatValue = (value: number, metricName: string): string => {
  // Formater différemment selon le type de métrique
  if (metricName.includes('duration') || metricName.includes('time')) {
    return `${value.toFixed(1)} ms`;
  }
  
  if (metricName.includes('size') || metricName.includes('bytes')) {
    return `${(value / 1024).toFixed(1)} kb`;
  }
  
  // Format par défaut
  return value.toFixed(1);
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f7'
  },
  header: {
    padding: 16,
    backgroundColor: '#2c3e50'
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4
  },
  subtitle: {
    fontSize: 14,
    color: '#ecf0f1'
  },
  categorySelector: {
    backgroundColor: '#34495e'
  },
  categorySelectorContent: {
    paddingHorizontal: 8
  },
  categoryTab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 4
  },
  categoryTabActive: {
    backgroundColor: '#3498db'
  },
  categoryTabText: {
    color: '#ecf0f1',
    fontWeight: '600'
  },
  categoryTabTextActive: {
    color: '#ffffff',
    fontWeight: 'bold'
  },
  metricsContainer: {
    flex: 1,
    padding: 8
  },
  metricCard: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4
      },
      android: {
        elevation: 2
      }
    })
  },
  metricName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#2c3e50'
  },
  metricDetailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8
  },
  metricDetail: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 8
  },
  metricDetailLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 2
  },
  metricDetailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50'
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center'
  },
  emptyText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center'
  },
  actionsContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#ffffff'
  },
  actionButton: {
    backgroundColor: '#3498db',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center'
  },
  actionButtonText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 16
  }
});

export default PerformanceDashboard;