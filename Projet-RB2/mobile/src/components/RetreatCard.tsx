import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { useTheme } from '../hooks/useTheme';
import Icon from 'react-native-vector-icons/Ionicons';

export interface RetreatCardProps {
  id: string;
  title: string;
  location: string;
  imageUrl: string;
  price: string;
  startDate?: string;
  endDate?: string;
  rating?: number;
  reviewCount?: number;
  onPress: () => void;
  style?: ViewStyle;
}

const RetreatCard: React.FC<RetreatCardProps> = ({
  id,
  title,
  location,
  imageUrl,
  price,
  startDate,
  endDate,
  rating,
  reviewCount,
  onPress,
  style,
}) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: theme.cardBackground },
        style,
      ]}
      onPress={onPress}
    >
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.text }]} numberOfLines={1}>
          {title}
        </Text>
        <View style={styles.locationContainer}>
          <Icon name="location-outline" size={16} color={theme.text} />
          <Text style={[styles.location, { color: theme.text }]} numberOfLines={1}>
            {location}
          </Text>
        </View>
        {startDate && endDate && (
          <Text style={[styles.date, { color: theme.text }]}>
            {startDate} - {endDate}
          </Text>
        )}
        {rating !== undefined && (
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={[styles.rating, { color: theme.text }]}>
              {rating.toFixed(1)}
            </Text>
            {reviewCount !== undefined && (
              <Text style={[styles.reviewCount, { color: theme.text + '80' }]}>
                ({reviewCount})
              </Text>
            )}
          </View>
        )}
        <Text style={[styles.price, { color: theme.primary }]}>{price}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  image: {
    height: 140,
    width: '100%',
  },
  content: {
    padding: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    marginLeft: 4,
  },
  date: {
    fontSize: 14,
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  rating: {
    fontSize: 14,
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: 14,
    marginLeft: 4,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
});

export default RetreatCard; 