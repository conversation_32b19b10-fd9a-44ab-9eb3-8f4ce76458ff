/**
 * Composant pour afficher et gérer les feature flags;
 */
import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  ActivityIndicator,
  Alert} from 'react-native';
import { Text } from '../ui';
import { useTheme } from '../../theme';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { featureFlagManager } from '@projet-rb2/core';

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get textSecondary() { return theme.secondaryText; }
});

interface FeatureFlagItemProps {
  name: string;
  description: string;
  enabled: boolean;
  isOverridden: boolean;
  onToggle: (enabled: boolean) => void;
  onClearOverride: () => void
}

/**
 * Composant pour afficher un feature flag individuel;
 */
const FeatureFlagItem: React.FC<FeatureFlagItemProps> = ({
  name,
  description,
  enabled,
  isOverridden,
  onToggle,
  onClearOverride}) => {
  const { theme: originalTheme } = useTheme()
  const theme = getCompatibleTheme(originalTheme);
  const [expanded, setExpanded] = useState(false);

  return (
    <View
      style={[
        styles.flagItem,
        {
          backgroundColor: theme.cardBackground,
          borderColor: isOverridden ? theme.primary + '40' : theme.border},
      ]}
    >
      <View style={styles.flagHeader}>
        <TouchableOpacity
          style={styles.flagTitleContainer}
          onPress={() => setExpanded(!expanded)}
        >
          <Icon
            name={expanded ? 'chevron-down' : 'chevron-right'}
            size={20}
            color={theme.text}
            style={styles.expandIcon}
          />
          <Text
            style={[
              styles.flagName,
              { color: theme.text, fontWeight: isOverridden ? 'bold' : 'normal' },
            ]}
          >
            {name}
          </Text>
        </TouchableOpacity>

        <View style={styles.flagControls}>
          {isOverridden && (
            <TouchableOpacity
              style={styles.resetButton}
              onPress={onClearOverride}
            >
              <Icon name="refresh" size={16} color={theme.primary} />
            </TouchableOpacity>
          )}
          <Switch
            value={enabled}
            onValueChange={onToggle}
            trackColor={{ false: theme.border, true: theme.primary + '70' }}
            thumbColor={enabled ? theme.primary : theme.text + '40'}
          />
        </View>
      </View>

      {expanded && (
        <View style={styles.flagDetails}>
          <Text style={[styles.flagDescription, { color: theme.secondaryText }]}>
            {description}
          </Text>
          {isOverridden && (
            <View
              style={[
                styles.overrideBadge,
                { backgroundColor: theme.primary + '20' },
              ]}
            >
              <Text
                style={[
                  styles.overrideText,
                  { color: theme.primary },
                ]}
              >
                Personnalisé
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

/**
 * Composant principal pour gérer les feature flags;
 */
const FeatureFlagsManager: React.FC = () => {
  const { theme: originalTheme } = useTheme()
  const theme = getCompatibleTheme(originalTheme);
  const {
    getAllFeatureFlags,
    isFeatureEnabled,
    setFeatureOverride,
    clearFeatureOverride,
    clearAllFeatureOverrides,
    loading} = useFeatureFlags()

  const [flags, setFlags] = useState<Record<string, any>>({})
  const [groups, setGroups] = useState<Record<string, string[]>>({})

  // Charger les feature flags;
  useEffect(() => {
    if(!loading) {
      const allFlags = getAllFeatureFlags()
      setFlags(allFlags);

      // Organiser les flags par groupe;
      const flagGroups: Record<string, string[]> = {};
      const coreFlags = featureFlagManager.getAllFlags()

      Object.keys(coreFlags).forEach((key) => {
        const group = coreFlags[key].group || 'other';
        if(!flagGroups[group]) {
          flagGroups[group] = [];
        }
        flagGroups[group].push(key);
      })

      setGroups(flagGroups);
    }
  }, [loading, getAllFeatureFlags]);

  // Gérer le toggle d'un feature flag;
  const handleToggleFlag = (key: string) => {
    const newValue = !isFeatureEnabled(key);
    setFeatureOverride(key, newValue);

    // Mettre à jour l'état local;
    setFlags((prev) => ({
      ...prev,
      [key]: {
        ...prev[key],
        enabled: newValue,
        isOverridden: true}}));
  }

  // Gérer la réinitialisation d'un feature flag;
  const handleClearOverride = (key: string) => {
    clearFeatureOverride(key);

    // Mettre à jour l'état local;
    const coreFlags = featureFlagManager.getAllFlags()
    const defaultValue = coreFlags[key]?.defaultValue || false;

    setFlags((prev) => ({
      ...prev,
      [key]: {
        ...prev[key],
        enabled: defaultValue,
        isOverridden: false}}));
  }

  // Gérer la réinitialisation de tous les feature flags;
  const handleResetAll = () => {
    Alert.alert(
      'Réinitialiser tous les paramètres',
      'Êtes-vous sûr de vouloir réinitialiser tous les feature flags à leurs valeurs par défaut ?',
      [
        {
          text: 'Annuler',
          style: 'cancel'},
        {
          text: 'Réinitialiser',
          style: 'destructive',
          onPress: () => {
            clearAllFeatureOverrides()
            
            // Mettre à jour l'état local;
            const allFlags = getAllFeatureFlags()
            setFlags(allFlags);
          }},
      ]
    );
  }

  // Afficher un loader pendant le chargement;
  if(loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color={theme.primary} />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>
          Fonctionnalités expérimentales;
        </Text>
        <TouchableOpacity
          style={[styles.resetAllButton, { borderColor: theme.border }]}
          onPress={handleResetAll}
        >
          <Text style={[styles.resetAllText, { color: theme.danger }]}>
            Réinitialiser tout;
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={[styles.description, { color: theme.secondaryText }]}>
        Activez ou désactivez des fonctionnalités expérimentales. Ces paramètres peuvent affecter la stabilité de l'application.
      </Text>

      <ScrollView style={styles.scrollView}>
        {Object.keys(groups).map((group) => (
          <View key={group} style={styles.groupContainer}>
            <Text
              style={[
                styles.groupTitle,
                { color: theme.secondaryText, borderBottomColor: theme.border },
              ]}
            >
              {group.charAt(0).toUpperCase() + group.slice(1)}
            </Text>

            {groups[group].map((key) => {
              const coreFlags = featureFlagManager.getAllFlags()
              const flag = coreFlags[key];
              const flagState = flags[key];

              if(!flag || !flagState) {
                return null;
              }

              return (
                <FeatureFlagItem
                  key={key}
                  name={flag.name || key}
                  description={flag.description || ''}
                  enabled={flagState.enabled}
                  isOverridden={flagState.isOverridden}
                  onToggle={() => handleToggleFlag(key)}
                  onClearOverride={() => handleClearOverride(key)}
                />
              );
            })}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8},
  title: {
    fontSize: 20,
    fontWeight: 'bold'},
  description: {
    fontSize: 14,
    paddingHorizontal: 16,
    paddingBottom: 16},
  resetAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    borderWidth: 1},
  resetAllText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1},
  groupContainer: {
    marginBottom: 16},
  groupTitle: {
    fontSize: 16,
    fontWeight: '500',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1},
  flagItem: {
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden'},
  flagHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12},
  flagTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1},
  expandIcon: {
    marginRight: 8},
  flagName: {
    fontSize: 16},
  flagControls: {
    flexDirection: 'row',
    alignItems: 'center'},
  resetButton: {
    padding: 8,
    marginRight: 8},
  flagDetails: {
    padding: 12,
    paddingTop: 0},
  flagDescription: {
    fontSize: 14,
    marginBottom: 8},
  overrideBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4},
  overrideText: {
    fontSize: 12,
    fontWeight: '500'}})

export default FeatureFlagsManager;