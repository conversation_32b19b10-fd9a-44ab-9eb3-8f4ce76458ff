import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import Text from './Text';

interface BookingCardProps {
  retreatName: string;
  location: string;
  startDate: string;
  endDate: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  price?: string;
  onPress?: () => void;
  style?: ViewStyle;
}

const BookingCard: React.FC<BookingCardProps> = ({
  retreatName,
  location,
  startDate,
  endDate,
  status,
  price,
  onPress,
  style
}) => {
  const { theme } = useTheme()

  const getStatusColor = () => {
    switch(status) {
      case 'pending':
        return theme.warning;
      case 'confirmed':
        return theme.secondary;
      case 'completed':
        return theme.primary;
      case 'cancelled':
        return theme.error;
      default: return theme.text
    }
  };

  const getStatusLabel = () => {
    switch(status) {
      case 'pending':
        return 'En attente';
      case 'confirmed':
        return 'Confirmé';
      case 'completed':
        return 'Terminé';
      case 'cancelled':
        return 'Annulé';
      default: return ''
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: theme.cardBackground,
          borderColor: theme.border
        },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusIndicator,
              { backgroundColor: getStatusColor() },
            ]}
          />
          <Text
            style={[
              styles.statusText,
              { color: getStatusColor() },
            ]}
          >
            {getStatusLabel()}
          </Text>
        </View>
        
        {price && (
          <Text style={[styles.price, { color: theme.text }]}>
            {price}
          </Text>
        )}
      </View>
      
      <View style={styles.content}>
        <Text
          style={[styles.retreatName, { color: theme.text }]}
          numberOfLines={1}
        >
          {retreatName}
        </Text>
        
        <View style={styles.infoRow}>
          <Icon name="map-marker" size={16} color={theme.text + '80'} />
          <Text
            style={[styles.infoText, { color: theme.text + '80' }]}
            numberOfLines={1}
          >
            {location}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Icon name="calendar-range" size={16} color={theme.text + '80'} />
          <Text
            style={[styles.infoText, { color: theme.text + '80' }]}
            numberOfLines={1}
          >
            {formatDate(startDate)} - {formatDate(endDate)}
          </Text>
        </View>
      </View>
      
      <View style={[styles.footer, { borderTopColor: theme.border }]}>
        <Text
          style={[styles.viewDetails, { color: theme.primary }]}
        >
          Voir les détails
        </Text>
        <Icon name="chevron-right" size={20} color={theme.primary} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500'
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 16
  },
  retreatName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1
  },
  viewDetails: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4
  }
})

export default BookingCard;