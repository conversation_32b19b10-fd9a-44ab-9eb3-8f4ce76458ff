import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  type?: string; // Pour la compatibilité avec DashboardCustomizer qui utilise 'danger'
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  icon?: string; // Pour la compatibilité avec DashboardCustomizer
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  type,
  size = 'medium',
  loading = false,
  disabled = false,
  icon,
  style,
  textStyle,
  ...props
}) => {
  const { theme } = useTheme();
  
  // Si type est fourni, l'utiliser pour déterminer la variante
  const effectiveVariant = (type === 'danger') ? 'secondary' : variant;
  
  const getVariantStyle = () => {
    switch(effectiveVariant) {
      case 'primary':
        return {
          container: {
            backgroundColor: theme.primary
          },
          text: {
            color: '#FFFFFF'
          }
        };
      case 'secondary':
        return {
          container: {
            backgroundColor: type === 'danger' ? '#E53935' : theme.secondary
          },
          text: {
            color: '#FFFFFF'
          }
        };
      case 'outline':
        return {
          container: {
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: type === 'danger' ? '#E53935' : theme.primary
          },
          text: {
            color: type === 'danger' ? '#E53935' : theme.primary
          }
        };
      case 'text':
        return {
          container: {
            backgroundColor: 'transparent'
          },
          text: {
            color: type === 'danger' ? '#E53935' : theme.primary
          }
        };
      default:
        return {
          container: {
            backgroundColor: theme.primary
          },
          text: {
            color: '#FFFFFF'
          }
        };
    }
  };
  
  const getSizeStyle = () => {
    switch(size) {
      case 'small':
        return {
          container: {
            paddingVertical: 8,
            paddingHorizontal: 16
          },
          text: {
            fontSize: 14
          }
        };
      case 'medium':
        return {
          container: {
            paddingVertical: 12,
            paddingHorizontal: 24
          },
          text: {
            fontSize: 16
          }
        };
      case 'large':
        return {
          container: {
            paddingVertical: 16,
            paddingHorizontal: 32
          },
          text: {
            fontSize: 18
          }
        };
      default:
        return {
          container: {
            paddingVertical: 12,
            paddingHorizontal: 24
          },
          text: {
            fontSize: 16
          }
        };
    }
  };
  
  const variantStyle = getVariantStyle();
  const sizeStyle = getSizeStyle();
  
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[
        styles.container,
        variantStyle.container,
        sizeStyle.container,
        disabled && styles.disabled,
        style,
      ]}
      activeOpacity={0.7}
      {...props}
    >
      {loading ? (
        <ActivityIndicator
          size="small" 
          color={effectiveVariant === 'outline' || effectiveVariant === 'text' ? theme.primary : '#FFFFFF'} 
        />
      ) : (
        <>
          {icon && (
            <Icon 
              name={icon} 
              size={sizeStyle.text.fontSize + 2} 
              color={variantStyle.text.color} 
              style={styles.icon} 
            />
          )}
          <Text
            style={[
              styles.text,
              variantStyle.text,
              sizeStyle.text,
              disabled && styles.disabledText,
              textStyle,
            ]}
          >
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row'
  },
  text: {
    fontWeight: '600',
    textAlign: 'center'
  },
  icon: {
    marginRight: 8
  },
  disabled: {
    opacity: 0.5
  },
  disabledText: {
    opacity: 0.8
  }
});

export default Button;