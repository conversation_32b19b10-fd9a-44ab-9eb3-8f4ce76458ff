import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ImageSourcePropType,
  ViewStyle,
  ImageStyle
} from 'react-native';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import Text from './Text';

interface CardProps {
  title: string;
  subtitle?: string;
  description?: string;
  imageUrl?: string;
  price?: string;
  onPress?: () => void;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
}

const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  description,
  imageUrl,
  price,
  onPress,
  style,
  imageStyle
}) => {
  const { theme } = useTheme()

  const CardContainer = onPress ? TouchableOpacity : View;

  return (
    <CardContainer
      style={[
        styles.container,
        {
          backgroundColor: theme.cardBackground,
          borderColor: theme.border
        },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {imageUrl && (
        <Image
          source={{ uri: imageUrl}}
          style={[styles.image, imageStyle]}
          resizeMode="cover"
        />
      )}
      
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text
              style={[styles.title, { color: theme.text }]}
              numberOfLines={1}
            >
              {title}
            </Text>
            
            {subtitle && (
              <Text
                style={[styles.subtitle, { color: theme.text + '80' }]}
                numberOfLines={1}
              >
                {subtitle}
              </Text>
            )}
          </View>
          
          {price && (
            <View style={[styles.priceTag, { backgroundColor: theme.primary + '20' }]}>
              <Text style={[styles.price, { color: theme.primary }]}>
                {price}
              </Text>
            </View>
          )}
        </View>
        
        {description && (
          <Text
            style={[styles.description, { color: theme.text + '90' }]}
            numberOfLines={3}
          >
            {description}
          </Text>
        )}
      </View>
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  image: {
    height: 180,
    width: '100%'
  },
  content: {
    padding: 16
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8
  },
  titleContainer: {
    flex: 1,
    marginRight: 8
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4
  },
  subtitle: {
    fontSize: 14
  },
  priceTag: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center'
  },
  price: {
    fontSize: 14,
    fontWeight: 'bold'
  },
  description: {
    fontSize: 14,
    lineHeight: 20
  }
})

export default Card;