import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

/**
 * Props pour le composant EmptyState
 */
export interface EmptyStateProps {
  icon: string;
  title: string;
  description: string;
  actionLabel?: string;
  onAction?: () => void;
  useDarkMode?: boolean;
}

/**
 * Composant pour afficher un état vide
 */
const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actionLabel,
  onAction,
  useDarkMode
}) => {
  const colorScheme = useColorScheme();
  const isDarkMode = useDarkMode !== undefined ? useDarkMode : colorScheme === 'dark';

  return (
    <View style={[styles.container, isDarkMode && styles.containerDark]}>
      <Icon
        name={icon}
        size={64}
        color={isDarkMode ? '#555555' : '#CCCCCC'}
        style={styles.icon}
      />
      <Text style={[styles.title, isDarkMode && styles.titleDark]}>
        {title}
      </Text>
      <Text style={[styles.description, isDarkMode && styles.descriptionDark]}>
        {description}
      </Text>
      
      {actionLabel && onAction && (
        <TouchableOpacity
          style={[styles.button, isDarkMode && styles.buttonDark]}
          onPress={onAction}
        >
          <Text style={[styles.buttonText, isDarkMode && styles.buttonTextDark]}>
            {actionLabel}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    margin: 16
  },
  containerDark: {
    backgroundColor: '#1C1C1E',
    borderColor: '#333333'
  },
  icon: {
    marginBottom: 16
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center'
  },
  titleDark: {
    color: '#E0E0E0'
  },
  description: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24
  },
  descriptionDark: {
    color: '#999999'
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center'
  },
  buttonDark: {
    backgroundColor: '#0A84FF'
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14
  },
  buttonTextDark: {
    color: '#FFFFFF'
  }
});

export default EmptyState;