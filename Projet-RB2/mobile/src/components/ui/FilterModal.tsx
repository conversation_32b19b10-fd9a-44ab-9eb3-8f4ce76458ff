import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  FlatList,
  TextInput,
  Switch,
  ScrollView,
  useColorScheme,
  SafeAreaView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { MetricConfig } from '../../types/dashboardTypes';

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  metrics: MetricConfig[];
  activeFilters: {
    categories?: string[];
    search?: string;
    [key: string]: any
  };
  onToggleMetric: (metricId: string) => void;
  onApplyFilters: (filters: any) => void;
}

/**
 * Modal pour filtrer les métriques du tableau de bord
 */
const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  metrics,
  activeFilters,
  onToggleMetric,
  onApplyFilters
}) => {
  const [searchQuery, setSearchQuery] = useState(activeFilters.search || '');
  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    activeFilters.categories || []
  );
  const [filteredMetrics, setFilteredMetrics] = useState<MetricConfig[]>([]);
  const colorScheme = useColorScheme();
  const isDarkMode = colorScheme === 'dark';

  // Extraire toutes les catégories disponibles
  const allCategories = Array.from(
    new Set(metrics.map((metric) => metric.category || 'other'))
  );

  // Filtrer les métriques en fonction de la recherche et des catégories sélectionnées
  useEffect(() => {
    let filtered = [...metrics];

    // Filtrer par requête de recherche
    if(searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (metric) =>
          metric.name.toLowerCase().includes(query) ||
          (metric.displayName && metric.displayName.toLowerCase().includes(query)) ||
          (metric.description && metric.description.toLowerCase().includes(query))
      );
    }

    // Filtrer par catégories
    if(selectedCategories.length > 0) {
      filtered = filtered.filter(
        (metric) =>
          selectedCategories.includes(metric.category || 'other')
      );
    }

    setFilteredMetrics(filtered);
  }, [searchQuery, selectedCategories, metrics]);

  // Fonction pour basculer une catégorie
  const toggleCategory = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter((c) => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };

  // Fonction pour appliquer les filtres
  const applyFilters = () => {
    onApplyFilters({
      search: searchQuery,
      categories: selectedCategories
    });
    onClose();
  };

  // Fonction pour réinitialiser les filtres
  const resetFilters = () => {
    setSearchQuery('');
    setSelectedCategories([]);
  };

  // Rendu d'une métrique
  const renderMetricItem = ({ item }: { item: MetricConfig }) => (
    <View style={[styles.metricItem, isDarkMode && styles.metricItemDark]}>
      <View style={styles.metricDetails}>
        <Text style={[styles.metricName, isDarkMode && styles.textDark]}>
          {item.displayName || item.name}
        </Text>
        {item.category && (
          <View style={[styles.categoryBadge, getCategoryStyle(item.category)]}>
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
        )}
      </View>
      <Switch
        value={item.visible}
        onValueChange={() => onToggleMetric(item.id)}
        trackColor={{ false: '#D1D1D6', true: '#007AFF' }}
        thumbColor="#FFFFFF"
      />
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={[styles.modalContainer, isDarkMode && styles.modalContainerDark]}>
        <View style={[styles.modalContent, isDarkMode && styles.modalContentDark]}>
          {/* En-tête */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon
                name="close"
                size={24}
                color={isDarkMode ? '#E0E0E0' : '#333333'}
              />
            </TouchableOpacity>
            <Text style={[styles.title, isDarkMode && styles.textDark]}>
              Filtrer les métriques
            </Text>
            <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
              <Text style={styles.resetText}>Réinitialiser</Text>
            </TouchableOpacity>
          </View>

          {/* Champ de recherche */}
          <View style={[styles.searchContainer, isDarkMode && styles.searchContainerDark]}>
            <Icon
              name="magnify"
              size={20}
              color={isDarkMode ? '#999999' : '#666666'}
              style={styles.searchIcon}
            />
            <TextInput
              style={[styles.searchInput, isDarkMode && styles.searchInputDark]}
              placeholder="Rechercher des métriques..."
              placeholderTextColor={isDarkMode ? '#999999' : '#999999'}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Icon
                  name="close-circle"
                  size={16}
                  color={isDarkMode ? '#999999' : '#999999'}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* Filtres de catégorie */}
          <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
            Catégories
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
            contentContainerStyle={styles.categoriesContent}
          >
            {allCategories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryChip,
                  selectedCategories.includes(category) && styles.selectedCategoryChip,
                  isDarkMode && styles.categoryChipDark,
                  selectedCategories.includes(category) && isDarkMode && styles.selectedCategoryChipDark,
                ]}
                onPress={() => toggleCategory(category)}
              >
                <Text
                  style={[
                    styles.categoryChipText,
                    selectedCategories.includes(category) && styles.selectedCategoryChipText,
                    isDarkMode && styles.categoryChipTextDark,
                  ]}
                >
                  {category}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Liste des métriques */}
          <Text style={[styles.sectionTitle, isDarkMode && styles.textDark]}>
            Métriques ({filteredMetrics.length})
          </Text>
          <FlatList
            data={filteredMetrics}
            renderItem={renderMetricItem}
            keyExtractor={(item) => item.id}
            style={styles.metricsList}
            contentContainerStyle={styles.metricsListContent}
          />

          {/* Boutons d'action */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, styles.cancelButton, isDarkMode && styles.cancelButtonDark]}
              onPress={onClose}
            >
              <Text style={[styles.actionButtonText, styles.cancelButtonText, isDarkMode && styles.cancelButtonTextDark]}>
                Annuler
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.applyButton]}
              onPress={applyFilters}
            >
              <Text style={[styles.actionButtonText, styles.applyButtonText]}>
                Appliquer
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

// Fonction pour obtenir les styles en fonction de la catégorie
const getCategoryStyle = (category: string) => {
  switch(category) {
    case 'performance':
      return styles.categoryPerformance;
    case 'http':
      return styles.categoryHttp;
    case 'component':
      return styles.categoryComponent;
    case 'analytics':
      return styles.categoryAnalytics;
    case 'operation':
      return styles.categoryOperation;
    default: 
      return styles.categoryOther;
  }
};

// Styles
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)'
  },
  modalContainerDark: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)'
  },
  modalContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    marginTop: 60
  },
  modalContentDark: {
    backgroundColor: '#121212'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0'
  },
  closeButton: {
    padding: 4
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333'
  },
  textDark: {
    color: '#E0E0E0'
  },
  resetButton: {
    padding: 4
  },
  resetText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '500'
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    margin: 16,
    paddingHorizontal: 12
  },
  searchContainerDark: {
    backgroundColor: '#2A2A2A'
  },
  searchIcon: {
    marginRight: 8
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: '#333333'
  },
  searchInputDark: {
    color: '#E0E0E0'
  },
  clearButton: {
    padding: 4
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8
  },
  categoriesContainer: {
    marginHorizontal: 16
  },
  categoriesContent: {
    paddingBottom: 8
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0'
  },
  categoryChipDark: {
    backgroundColor: '#2A2A2A',
    borderColor: '#444444'
  },
  selectedCategoryChip: {
    backgroundColor: '#E1F5FE',
    borderColor: '#4FC3F7'
  },
  selectedCategoryChipDark: {
    backgroundColor: '#0D47A1',
    borderColor: '#1976D2'
  },
  categoryChipText: {
    fontSize: 14,
    color: '#666666'
  },
  categoryChipTextDark: {
    color: '#BBBBBB'
  },
  selectedCategoryChipText: {
    color: '#0288D1',
    fontWeight: '500'
  },
  metricsList: {
    flex: 1
  },
  metricsListContent: {
    paddingHorizontal: 16
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0'
  },
  metricItemDark: {
    borderBottomColor: '#333333'
  },
  metricDetails: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  },
  metricName: {
    fontSize: 14,
    color: '#333333'
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8
  },
  categoryPerformance: {
    backgroundColor: '#E3F2FD'
  },
  categoryHttp: {
    backgroundColor: '#F3E5F5'
  },
  categoryComponent: {
    backgroundColor: '#E8F5E9'
  },
  categoryAnalytics: {
    backgroundColor: '#FFF3E0'
  },
  categoryOperation: {
    backgroundColor: '#FFEBEE'
  },
  categoryOther: {
    backgroundColor: '#F5F5F5'
  },
  categoryText: {
    fontSize: 10,
    color: '#333333',
    fontWeight: '500'
  },
  actionsContainer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0'
  },
  actionButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center'
  },
  cancelButton: {
    backgroundColor: '#F5F5F5',
    marginRight: 8
  },
  cancelButtonDark: {
    backgroundColor: '#2A2A2A'
  },
  applyButton: {
    backgroundColor: '#007AFF'
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500'
  },
  cancelButtonText: {
    color: '#333333'
  },
  cancelButtonTextDark: {
    color: '#E0E0E0'
  },
  applyButtonText: {
    color: '#FFFFFF'
  }
});

export default FilterModal;