import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { MetricConfig } from '../../types/dashboardTypes';

/**
 * Props pour le composant MetricCard
 */
export interface MetricCardProps {
  metric: MetricConfig;
  onToggle: () => void;
  isDarkMode?: boolean;
}

/**
 * Composant pour afficher une métrique sur le tableau de bord
 */
const MetricCard: React.FC<MetricCardProps> = ({
  metric,
  onToggle,
  isDarkMode = false
}) => {
  // Fonction pour formater la valeur de la métrique
  const formatValue = (value: number): string => {
    if(value >= 1000) {
      return `${(value / 1000).toFixed(1)}k`;
    }
    return value.toFixed(1);
  }
  
  // Déterminer la couleur selon les seuils
  const getColorByThreshold = (value: number): string => {
    if (!metric.colorThreshold) return isDarkMode ? '#0A84FF' : '#007AFF';
    
    if(metric.colorThreshold.critical && value >= metric.colorThreshold.critical) {
      return '#FF3B30'; // Rouge pour critique
    } else if(metric.colorThreshold.warning && value >= metric.colorThreshold.warning) {
      return '#FF9500'; // Orange pour avertissement
    } else {
      return '#34C759'; // Vert pour normal
    }
  }
  
  // Simuler une valeur pour cette démo
  const simulatedValue = Math.random() * 100;
  const valueColor = getColorByThreshold(simulatedValue);
  
  // Obtenir l'icône appropriée pour la catégorie
  const getIconForCategory = (): string => {
    switch(metric.category) {
      case 'performance':
        return 'speedometer';
      case 'memory':
        return 'memory';
      case 'network':
        return 'lan';
      case 'http':
        return 'api';
      case 'component':
        return 'view-dashboard';
      case 'operation':
        return 'cog';
      default: return 'chart-line'
    }
  }
  
  return (
    <View style={[
      styles.container,
      isDarkMode ? styles.containerDark : styles.containerLight
    ]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Icon
            name={getIconForCategory()} 
            size={16}
            color={isDarkMode ? '#BBBBBB' : '#666666'} 
            style={styles.icon}
          />
          <Text
            style={[
              styles.title,
              isDarkMode && styles.titleDark
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {metric.name}
          </Text>
        </View>
        <TouchableOpacity onPress={onToggle}>
          <Icon
            name="dots-vertical" 
            size={20}
            color={isDarkMode ? '#BBBBBB' : '#666666'} 
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <Text style={[styles.value, { color: valueColor }]}>
          {formatValue(simulatedValue)}
        </Text>
        <Text style={[styles.unit, isDarkMode && styles.unitDark]}>
          {metric.unit || ''}
        </Text>
      </View>
      
      {metric.description && (
        <Text
          style={[styles.description, isDarkMode && styles.descriptionDark]}
          numberOfLines={2}
        >
          {metric.description}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2
  },
  containerLight: {
    backgroundColor: '#FFFFFF'
  },
  containerDark: {
    backgroundColor: '#1C1C1E'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  icon: {
    marginRight: 8
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    flex: 1
  },
  titleDark: {
    color: '#E0E0E0'
  },
  content: {
    alignItems: 'flex-start',
    marginBottom: 8
  },
  value: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  unit: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4
  },
  unitDark: {
    color: '#BBBBBB'
  },
  description: {
    fontSize: 12,
    color: '#999999'
  },
  descriptionDark: {
    color: '#777777'
  }
})

export default MetricCard;