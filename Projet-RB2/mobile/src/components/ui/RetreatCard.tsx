import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ViewStyle,
  ImageStyle
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme';
import Text from './Text';

interface RetreatCardProps {
  title: string;
  location: string;
  imageUrl: string;
  startDate: string;
  endDate: string;
  price: string;
  rating?: number;
  reviewCount?: number;
  isFavorite?: boolean;
  onPress?: () => void;
  onFavoritePress?: () => void;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
}

const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; }
});

const RetreatCard: React.FC<RetreatCardProps> = ({
  title,
  location,
  imageUrl,
  startDate,
  endDate,
  price,
  rating,
  reviewCount,
  isFavorite = false,
  onPress,
  onFavoritePress,
  style,
  imageStyle
}) => {
  const { theme: originalTheme } = useTheme();
  const theme = getCompatibleTheme(originalTheme);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short'
    });
  };

  const formatDateRange = (start: string, end: string) => {
    return `${formatDate(start)} - ${formatDate(end)}`;
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: theme.card,
          borderColor: theme.border
        },
        style
      ]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: imageUrl}}
          style={[styles.image, imageStyle]}
          resizeMode="cover"
        />
        
        <TouchableOpacity
          style={[
            styles.favoriteButton,
            {
              backgroundColor: theme.background + 'E6'
            }
          ]}
          onPress={onFavoritePress}
          activeOpacity={0.9}
        >
          <Icon
            name={isFavorite ? 'heart' : 'heart-outline'}
            size={20}
            color={isFavorite ? theme.error : theme.text}
          />
        </TouchableOpacity>
        
        <View
          style={[
            styles.priceTag,
            {
              backgroundColor: theme.primary
            }
          ]}
        >
          <Text style={styles.priceText}>
            {price}
          </Text>
        </View>
      </View>
      
      <View style={styles.content}>
        <Text
          style={[styles.title, { color: theme.text }]}
          numberOfLines={1}
        >
          {title}
        </Text>
        
        <View style={styles.locationContainer}>
          <Icon name="map-marker" size={16} color={theme.text + '80'} />
          <Text
            style={[styles.location, { color: theme.text + '80' }]}
            numberOfLines={1}
          >
            {location}
          </Text>
        </View>
        
        <View style={styles.dateContainer}>
          <Icon name="calendar-range" size={16} color={theme.text + '80'} />
          <Text
            style={[styles.date, { color: theme.text + '80' }]}
            numberOfLines={1}
          >
            {formatDateRange(startDate, endDate)}
          </Text>
        </View>
        
        {rating !== undefined && (
          <View style={styles.ratingContainer}>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Icon
                  key={star}
                  name={
                    star <= Math.floor(rating)
                      ? 'star'
                      : star === Math.ceil(rating) && star > Math.floor(rating)
                      ? 'star-half-full'
                      : 'star-outline'
                  }
                  size={16}
                  color={theme.accent}
                  style={styles.starIcon}
                />
              ))}
            </View>
            
            {reviewCount !== undefined && (
              <Text style={[styles.reviewCount, { color: theme.text + '60' }]}>
                ({reviewCount})
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 16
  },
  imageContainer: {
    position: 'relative'
  },
  image: {
    height: 180,
    width: '100%'
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center'
  },
  priceTag: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderTopLeftRadius: 8
  },
  priceText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14
  },
  content: {
    padding: 12
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4
  },
  location: {
    fontSize: 14,
    marginLeft: 4
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  date: {
    fontSize: 14,
    marginLeft: 4
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  starsContainer: {
    flexDirection: 'row'
  },
  starIcon: {
    marginRight: 2
  },
  reviewCount: {
    fontSize: 12,
    marginLeft: 4
  }
});

export default RetreatCard;