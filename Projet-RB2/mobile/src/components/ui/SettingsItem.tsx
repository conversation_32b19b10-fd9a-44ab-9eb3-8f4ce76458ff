import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Switch,
  ViewStyle
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../theme'

// Créer un adaptateur de thème pour gérer les différences de nommage de propriétés
const getCompatibleTheme = (theme) => ({
  ...theme,
  get card() { return theme.cardBackground; },
  get textSecondary() { return theme.secondaryText; },
  get info() { return theme.primary; } // Fallback pour 'info'
});
;
import Text from './Text';

type SettingsItemType = 'toggle' | 'link' | 'button' | 'info';
interface SettingsItemProps {
  title: string;
  description?: string;
  iconName?: string;
  type?: SettingsItemType;
  value?: boolean;
  onValueChange?: (value: boolean) => void;
  onPress?: () => void;
  style?: ViewStyle;
  rightText?: string;
  disabled?: boolean;
}

const SettingsItem: React.FC<SettingsItemProps> = ({
  title,
  description,
  iconName,
  type = 'link',
  value,
  onValueChange,
  onPress,
  style,
  rightText,
  disabled = false
}) => {
  const { theme: originalTheme } = useTheme();
  // Utiliser l'adaptateur de thème pour la compatibilité
  const theme = getCompatibleTheme(originalTheme);

  const renderRightElement = () => {
    switch(type) {
      case 'toggle':
        return (
          <Switch
            value={value}
            onValueChange={onValueChange}
            trackColor={{ false: theme.border, true: theme.primary + '80' }}
            thumbColor={value ? theme.primary : theme.text + '40'}
            disabled={disabled}
          />
        );
      case 'link':
        return (
          <View style={styles.rightContainer}>
            {rightText && (
              <Text style={[styles.rightText, { color: theme.text + '80' }]}>
                {rightText}
              </Text>
            )}
            <Icon name="chevron-right" size={20} color={theme.text + '60'} />
          </View>
        );
      case 'button':
        return (
          <View style={[styles.buttonContainer, { backgroundColor: theme.primary + '20' }]}>
            <Text style={[styles.buttonText, { color: theme.primary }]}>
              {rightText || 'Modifier'}
            </Text>
          </View>
        );
      case 'info':
      default:
        return rightText ? (
          <Text style={[styles.rightText, { color: theme.text + '80' }]}>
            {rightText}
          </Text>
        ) : null;
    }
  };

  const Container = type !== 'info' ? TouchableOpacity : View;
  const containerProps = type !== 'info' ? {
    onPress,
    activeOpacity: 0.7,
    disabled
  } : {};

  return (
    <Container
      style={[
        styles.container,
        {
          backgroundColor: theme.card,
          opacity: disabled ? 0.6 : 1
        },
        style
      ]}
      {...containerProps}
    >
      {iconName && (
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: theme.primary + '15' }
          ]}
        >
          <Icon name={iconName} size={20} color={theme.primary} />
        </View>
      )}
      
      <View style={[styles.content, !iconName && styles.contentWithoutIcon]}>
        <View style={styles.textContainer}>
          <Text
            style={[styles.title, { color: theme.text }]}
            numberOfLines={1}
          >
            {title}
          </Text>
          
          {description && (
            <Text
              style={[styles.description, { color: theme.text + '80' }]}
              numberOfLines={2}
            >
              {description}
            </Text>
          )}
        </View>
        
        {renderRightElement()}
      </View>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  contentWithoutIcon: {
    paddingLeft: 0
  },
  textContainer: {
    flex: 1,
    marginRight: 16
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4
  },
  description: {
    fontSize: 14,
    lineHeight: 18
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  rightText: {
    fontSize: 14,
    marginRight: 8
  },
  buttonContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center'
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500'
  }
});

export default SettingsItem;