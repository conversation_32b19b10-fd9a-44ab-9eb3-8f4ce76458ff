import React from 'react';
import { Text as RNText, TextProps as RNTextProps, StyleSheet } from 'react-native';
import { useTheme } from '../../theme';

interface TextProps extends RNTextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body1' | 'body2' | 'caption';
}

const Text: React.FC<TextProps> = ({ 
  children, 
  style, 
  variant = 'body1', 
  ...props
}) => {
  const { theme } = useTheme();
  
  const getVariantStyle = () => {
    switch(variant) {
      case 'h1':
        return styles.h1;
      case 'h2':
        return styles.h2;
      case 'h3':
        return styles.h3;
      case 'h4':
        return styles.h4;
      case 'body1':
        return styles.body1;
      case 'body2':
        return styles.body2;
      case 'caption':
        return styles.caption;
      default: 
        return styles.body1;
    }
  };

  return (
    <RNText
      style={[
        getVariantStyle(), 
        { color: theme.text },
        style
      ]} 
      {...props}
    >
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  h1: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 40
  },
  h2: {
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 32
  },
  h3: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 28
  },
  h4: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 26
  },
  body1: {
    fontSize: 16,
    lineHeight: 24
  },
  body2: {
    fontSize: 14,
    lineHeight: 22
  },
  caption: {
    fontSize: 12,
    lineHeight: 18
  }
});

export default Text;