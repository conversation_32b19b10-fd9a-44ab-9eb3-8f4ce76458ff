const API_CONFIG = {
  // Services directs;
  SOCIAL_API: 'http://social-service:4000',
  MESSAGING_API: 'http://messaging-service:5178',
  
  // Services sécurisés via gateway;
  SECURE_API: 'https://mobile-gateway:8080/api',
  
  // Configuration des timeouts et retry;
  TIMEOUT: 30000,
  MAX_RETRIES: 3,
  
  // Headers de sécurité
  getSecureHeaders: (token: string) => ({
    'Authorization': `Bear<PERSON> ${token}`,
    'X-Client-Version': APP_VERSION,
    'X-Platform': Platform.OS})
};

export default API_CONFIG;