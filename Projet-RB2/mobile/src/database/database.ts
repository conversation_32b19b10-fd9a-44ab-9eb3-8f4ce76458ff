import { Model } from '../models/mocks';
import Booking from '../models/Booking';
import Retreat from '../models/Retreat';
import User from '../models/User';
import Notification from '../models/Notification';

// Simulation de la base de données pour développement
class Database {
  private adapter: any;
  private modelClasses: any[];
  private collections: Map<string, Collection<any>>;

  constructor({ adapter, modelClasses }: { adapter: any; modelClasses: any[] }) {
    this.adapter = adapter;
    this.modelClasses = modelClasses;
    this.collections = new Map();

    // Initialiser les collections
    for (const ModelClass of modelClasses) {
      this.collections.set(ModelClass.table, new Collection(ModelClass));
    }
  }

  get<T>(collectionName: string): Collection<T> {
    const collection = this.collections.get(collectionName);
    if (!collection) {
      throw new Error(`Collection ${collectionName} does not exist`);
    }
    return collection as Collection<T>;
  }

  async write(callback: () => Promise<void>): Promise<void> {
    await callback();
  }

  async batch(...operations: any[]): Promise<void> {
    // Simulation de l'opération batch
    console.log(`Exécution de ${operations.length} opérations batch`);
  }
}

// Classe Collection pour simuler les collections WatermelonDB
class Collection<T> {
  private ModelClass: any;
  private items: Map<string, T>;

  constructor(ModelClass: any) {
    this.ModelClass = ModelClass;
    this.items = new Map();
  }

  query() {
    return {
      fetch: async (): Promise<T[]> => {
        return Array.from(this.items.values());
      }
    };
  }

  async create(data: Partial<T>): Promise<T> {
    const instance = new this.ModelClass(data) as T;
    const id = (instance as any).id;
    this.items.set(id, instance);
    return instance;
  }

  async find(id: string): Promise<T> {
    const instance = this.items.get(id);
    if (!instance) {
      throw new Error(`Record with id ${id} not found`);
    }
    return instance;
  }

  async update(id: string, data: Partial<T>): Promise<T> {
    const instance = await this.find(id);
    Object.assign(instance, data);
    return instance;
  }
}

// Simulation de l'adaptateur SQLite
const adapter = {
  schema: {
    version: 1,
    tables: []
  }
};

// Création de la base de données avec l'adaptateur et les modèles
export const database = new Database({
  adapter,
  modelClasses: [Booking, Retreat, User, Notification],
});

// Export des collections pour un accès facile ailleurs
export const getBookingsCollection = () => database.get<Booking>('bookings');
export const getRetreatsCollection = () => database.get<Retreat>('retreats');
export const getUsersCollection = () => database.get<User>('users');
export const getNotificationsCollection = () => database.get<Notification>('notifications');

/**
 * Réinitialise la base de données locale
 * Cette fonction supprime toutes les données des collections et réinitialise les compteurs
 * @returns {Promise<boolean>} Vrai si la réinitialisation a réussi
 */
export const resetDatabase = async (): Promise<boolean> => {
  try {
    await database.write(async () => {
      // Récupérer toutes les collections
      const bookingsCollection = getBookingsCollection();
      const retreatsCollection = getRetreatsCollection();
      const usersCollection = getUsersCollection();
      const notificationsCollection = getNotificationsCollection();
      
      // Récupérer tous les enregistrements pour chaque collection
      const allBookings = await bookingsCollection.query().fetch();
      const allRetreats = await retreatsCollection.query().fetch();
      const allUsers = await usersCollection.query().fetch();
      const allNotifications = await notificationsCollection.query().fetch();
      
      // Supprimer tous les enregistrements dans une transaction
      const batch = [
        ...allBookings.map(record => record.prepareDestroyPermanently()),
        ...allRetreats.map(record => record.prepareDestroyPermanently()),
        ...allUsers.map(record => record.prepareDestroyPermanently()),
        ...allNotifications.map(record => record.prepareDestroyPermanently())
      ];
      
      if (batch.length > 0) {
        await database.batch(...batch);
      }
    });
    
    console.log('Base de données réinitialisée avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de la réinitialisation de la base de données:', error);
    return false;
  }
};
