import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class NFT extends Model {
  static table = 'nfts';

  @field('token_id') tokenId!: string;
  @field('contract_address') contractAddress!: string;
  @field('owner_id') ownerId!: string;
  @field('metadata') metadata!: string;
  @date('last_sync_at') lastSyncAt!: Date;
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
} 