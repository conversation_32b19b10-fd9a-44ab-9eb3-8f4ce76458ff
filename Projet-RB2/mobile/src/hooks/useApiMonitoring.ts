import { useEffect, useState } from 'react';
import { apiMonitoring, MonitoringConfig } from '../utils/apiMonitoring';

// Interface pour les statistiques de performance
export interface PerformanceStats {
  totalRequests: number;
  successRate: number;
  errorRate: number;
  cacheHitRate: number;
  avgResponseTime: number;
  offline: boolean;
  networkType: string;
  customMetrics?: Record<string, number>;
  lastUpdate?: number;
}

interface ApiMonitoringHookResult {
  stats: PerformanceStats;
  refreshStats: () => void;
  flushMetrics: () => Promise<void>;
  addCustomMetric: (name: string, value: number, attributes?: Record<string, unknown>) => void;
  isOffline: boolean;
  networkType: string;
}

const transformRawStats = (rawStats: Record<string, unknown>): PerformanceStats => ({
  totalRequests: Number(rawStats.totalRequests) || 0,
  successRate: Number(rawStats.successRate) || 0,
  errorRate: Number(rawStats.errorRate) || 0,
  cacheHitRate: Number(rawStats.cacheHitRate) || 0,
  avgResponseTime: Number(rawStats.avgResponseTime) || 0,
  offline: Boolean(rawStats.offline) || false,
  networkType: String(rawStats.networkType || 'unknown'),
  customMetrics: rawStats.customMetrics as Record<string, number>,
  lastUpdate: Date.now()
});

/**
 * Hook pour faciliter l'utilisation du monitoring d'API dans les composants React
 * 
 * @param config - Configuration optionnelle du monitoring
 * @param refreshInterval - Intervalle de rafraîchissement des statistiques en ms (0 pour désactiver)
 * @returns Un objet contenant les statistiques de performance et des fonctions utilitaires
 */
export const useApiMonitoring = (
  config?: Partial<MonitoringConfig>,
  refreshInterval = 0
): ApiMonitoringHookResult => {
  const [stats, setStats] = useState<PerformanceStats>(() => 
    transformRawStats(apiMonitoring.getPerformanceStats())
  );
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialiser le monitoring avec la configuration fournie
  useEffect(() => {
    if (!isInitialized) {
      apiMonitoring.initialize(config);
      setIsInitialized(true);
    }
  }, [config, isInitialized]);

  // Rafraîchir les statistiques à intervalles réguliers si demandé
  useEffect(() => {
    if (refreshInterval <= 0) return;

    const intervalId = setInterval(() => {
      setStats(transformRawStats(apiMonitoring.getPerformanceStats()));
    }, refreshInterval);

    return () => clearInterval(intervalId);
  }, [refreshInterval]);

  const refreshStats = () => {
    setStats(transformRawStats(apiMonitoring.getPerformanceStats()));
  };

  const flushMetrics = async () => {
    await apiMonitoring.flushMetrics();
  };

  const addCustomMetric = (
    name: string,
    value: number,
    attributes: Record<string, unknown> = {}
  ) => {
    apiMonitoring.addCustomMetric(name, value, attributes);
  };

  return {
    stats,
    refreshStats,
    flushMetrics,
    addCustomMetric,
    isOffline: stats.offline,
    networkType: stats.networkType
  };
};

/**
 * Hook simplifié pour surveiller uniquement l'état du réseau
 * 
 * @returns L'état actuel du réseau
 */
export const useNetworkStatus = () => {
  const { stats, refreshStats } = useApiMonitoring();
  return {
    isOffline: stats.offline,
    networkType: stats.networkType,
    refresh: refreshStats
  };
};

interface ComponentOperation {
  startTime: number;
  name: string;
}

/**
 * Hook pour mesurer la performance d'un composant
 * 
 * @param componentName - Nom du composant à surveiller
 * @returns Un objet contenant des fonctions pour marquer le début et la fin des opérations
 */
export const useComponentPerformance = (componentName: string) => {
  const operations = new Map<string, ComponentOperation>();
  
  const startOperation = (operationName: string): void => {
    const fullName = `component.${componentName}.${operationName}`;
    operations.set(operationName, {
      startTime: Date.now(),
      name: fullName
    });
    
    apiMonitoring.addCustomMetric(`${fullName}.start`, 1, {
      componentName,
      operationName,
      phase: 'start'
    });
  };
  
  const endOperation = (
    operationName: string,
    success = true,
    metadata: Record<string, unknown> = {}
  ): number | undefined => {
    const operation = operations.get(operationName);
    if (!operation) {
      console.warn(`Operation "${operationName}" n'a pas été démarrée avec startOperation`);
      return;
    }
    
    const duration = Date.now() - operation.startTime;
    
    apiMonitoring.addCustomMetric(`${operation.name}.duration`, duration, {
      componentName,
      operationName,
      success,
      duration,
      ...metadata
    });
    
    operations.delete(operationName);
    return duration;
  };
  
  useEffect(() => {
    apiMonitoring.addCustomMetric(`component.${componentName}.mounted`, 1, {
      componentName,
      timestamp: Date.now()
    });

    return () => {
      apiMonitoring.addCustomMetric(`component.${componentName}.unmounted`, 1, {
        componentName,
        timestamp: Date.now()
      });
    };
  }, [componentName]);

  return {
    startOperation,
    endOperation
  };
};

export default useApiMonitoring;