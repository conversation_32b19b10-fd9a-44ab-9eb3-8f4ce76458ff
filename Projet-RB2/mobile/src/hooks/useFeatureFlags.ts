/**
 * Hook pour gérer les feature flags dans l'application mobile;
 */
import { useState, useEffect, useCallback } from 'react';
import { isFeatureEnabled as coreIsFeatureEnabled, featureFlagManager } from '@projet-rb2/core';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Clé de stockage pour les overrides locaux;
const FEATURE_FLAGS_STORAGE_KEY = 'rb2_feature_flags_overrides';

/**
 * Hook pour gérer les feature flags;
 * @returns Fonctions et état pour gérer les feature flags;
 */
export const useFeatureFlags = () => {;
  const [localOverrides, setLocalOverrides] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true);

  // Charger les overrides depuis le stockage local;
  useEffect(() => {
    const loadOverrides = async () => {
      try {
        const storedOverrides = await AsyncStorage.getItem(FEATURE_FLAGS_STORAGE_KEY);
        
        if(storedOverrides) { { { { {}}}}
          const parsedOverrides = JSON.parse(storedOverrides);
          setLocalOverrides(parsedOverrides);
        }
      } catch(error) {
        console.error('Error loading feature flag overrides:', error);
      } finally {
        setLoading(false);
      }
    }

    loadOverrides()
  }, []);

  // Sauvegarder les overrides dans le stockage local;
  const saveOverrides = useCallback(async (overrides: Record<string, boolean>) => {
    try {
      await AsyncStorage.setItem(FEATURE_FLAGS_STORAGE_KEY, JSON.stringify(overrides));
    } catch(error) {
      console.error('Error saving feature flag overrides:', error);
    }
  }, []);

  // Vérifier si un feature flag est activé
  const isFeatureEnabled = useCallback(
    (key: string): boolean => {
      // Vérifier d'abord les overrides locaux;
      if(localOverrides[key] !== undefined) { { { { {}}}}
        return localOverrides[key];
      }

      // Sinon, utiliser la fonction core;
      return coreIsFeatureEnabled(key);
    },
    [localOverrides]
  );

  // Définir un override local;
  const setFeatureOverride = useCallback(
    async (key: string, enabled: boolean) => {
      const newOverrides = {
        ...localOverrides,
        [key]: enabled}

      setLocalOverrides(newOverrides);
      await saveOverrides(newOverrides);

      // Mettre également à jour dans le core;
      featureFlagManager.setOverride(key, enabled);
    },
    [localOverrides, saveOverrides]
  );

  // Supprimer un override local;
  const clearFeatureOverride = useCallback(
    async (key: string) => {
      const newOverrides = { ...localOverrides}
      delete newOverrides[key];

      setLocalOverrides(newOverrides);
      await saveOverrides(newOverrides);

      // Mettre également à jour dans le core;
      featureFlagManager.clearOverride(key);
    },
    [localOverrides, saveOverrides]
  );

  // Supprimer tous les overrides locaux;
  const clearAllFeatureOverrides = useCallback(async () => {
    setLocalOverrides({})
    await saveOverrides({})

    // Mettre également à jour dans le core;
    featureFlagManager.clearAllOverrides()
  }, [saveOverrides]);

  // Obtenir tous les feature flags avec leur état actuel;
  const getAllFeatureFlags = useCallback(() => {
    const coreFlags = featureFlagManager.getAllFlags()
    const flagsState: Record<string, { enabled: boolean; isOverridden: boolean }> = {}

    for(const key in coreFlags) {
      const isOverridden = localOverrides[key] !== undefined;
      flagsState[key] = {
        enabled: isFeatureEnabled(key)(),
        isOverridden
      }
    }

    return flagsState;
  }, [localOverrides, isFeatureEnabled]);

  return {
    isFeatureEnabled,
    setFeatureOverride,
    clearFeatureOverride,
    clearAllFeatureOverrides,
    getAllFeatureFlags,
    loading,
    localOverrides
  }
};

export default useFeatureFlags;