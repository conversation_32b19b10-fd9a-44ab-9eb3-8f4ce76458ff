/**
 * Hook pour l'utilisation du monitoring dans les composants React
 * 
 * Ce hook permet d'accéder facilement aux fonctionnalités de monitoring
 * depuis les composants React, d'instrumenter les actions des utilisateurs
 * et de mesurer les performances des composants.
 */

import { useEffect, useRef, useCallback } from 'react';
import { unifiedMonitoring, LogLevel, PerformanceMarker } from '../utils/unifiedMonitoring';

/**
 * Hook personnalisé pour l'utilisation du monitoring dans les composants
 */
export const useMonitoring = (componentName: string, props?: Record<string, any>) => {
  // Référence pour stocker les marqueurs de performance
  const performanceMarkers = useRef<Record<string, string>>({})
  
  // Instrumenter le montage et le démontage du composant
  useEffect(() => {
    // Enregistrer le montage du composant
    const mountMetadata = {
      component: componentName,
      ...(props || {})
    }
    
    // Créer un marqueur de performance pour le cycle de vie du composant
    const lifecycleMarkerId = unifiedMonitoring.markPerformanceStart(`component_lifecycle_${componentName}`, mountMetadata);
    
    // Suivre l'événement de montage
    unifiedMonitoring.trackEvent('component_mounted', mountMetadata);
    
    // Nettoyer lors du démontage
    return () => {
      // Enregistrer le démontage du composant
      unifiedMonitoring.trackEvent('component_unmounted', {
        component: componentName,
        ...(props || {})
      })
      
      // Terminer le marqueur de performance
      unifiedMonitoring.markPerformanceEnd(lifecycleMarkerId, {
        lifecycle_completed: true
      })
    }
  }, [componentName, props]);
  
  /**
   * Mesurer la performance d'une action utilisateur
   */
  const trackUserAction = useCallback((actionName: string, metadata: Record<string, any> = {}) => {
    unifiedMonitoring.trackEvent(`user_action_${actionName}`, {
      component: componentName,
      ...metadata
    })
  }, [componentName]);
  
  /**
   * Mesurer la performance d'une fonction synchrone
   */
  const measurePerformance = useCallback(<T>(name: string, fn:  () => T, metadata: Record<string, any> = {}): T => {
    return unifiedMonitoring.measure(`${componentName}_${name}`, fn, {
      component: componentName,
      ...metadata
    })
  }, [componentName]);
  
  /**
   * Mesurer la performance d'une fonction asynchrone
   */
  const measurePerformanceAsync = useCallback(<T>(name: string, fn:  () => Promise<T>, metadata: Record<string, any> = {}): Promise<T> => {
    return unifiedMonitoring.measureAsync(`${componentName}_${name}`, fn, {
      component: componentName,
      ...metadata
    })
  }, [componentName]);
  
  /**
   * Marquer le début d'une opération manuelle
   */
  const startMeasure = useCallback((operationName: string, metadata: Record<string, any> = {}): string => {
    const name = `${componentName}_${operationName}`;
    const markerId = unifiedMonitoring.markPerformanceStart(name, {
      component: componentName,
      ...metadata
    })
    
    // Stocker l'ID du marqueur pour y accéder plus tard
    performanceMarkers.current[operationName] = markerId;
    
    return markerId;
  }, [componentName]);
  
  /**
   * Marquer la fin d'une opération manuelle
   */
  const endMeasure = useCallback((operationNameOrId: string, additionalMetadata: Record<string, any> = {}): number | null => {
    // Vérifier si c'est un nom d'opération (et récupérer l'ID) ou directement un ID de marqueur
    const markerId = performanceMarkers.current[operationNameOrId] || operationNameOrId;
    
    // Supprimer la référence si c'était un nom d'opération
    if(performanceMarkers.current[operationNameOrId]) {
      delete performanceMarkers.current[operationNameOrId];
    }
    
    // Terminer la mesure
    return unifiedMonitoring.markPerformanceEnd(markerId, additionalMetadata);
  }, []);
  
  /**
   * Logger un message
   */
  const log = useCallback((level: LogLevel, message: string, context: Record<string, any> = {}) => {
    unifiedMonitoring.log(level, message, {
      component: componentName,
      ...context
    })
  }, [componentName]);
  
  /**
   * Méthodes de log par niveau
   */
  const debug = useCallback((message: string, context: Record<string, any> = {}) => {
    log(LogLevel.DEBUG, message, context);
  }, [log]);
  
  const info = useCallback((message: string, context: Record<string, any> = {}) => {
    log(LogLevel.INFO, message, context);
  }, [log]);
  
  const warn = useCallback((message: string, context: Record<string, any> = {}) => {
    log(LogLevel.WARN, message, context);
  }, [log]);
  
  const error = useCallback((message: string, context: Record<string, any> = {}) => {
    log(LogLevel.ERROR, message, context);
  }, [log]);
  
  /**
   * Capturer une erreur
   */
  const captureError = useCallback((err: Error, context: Record<string, any> = {}) => {
    unifiedMonitoring.captureError(err, {
      component: componentName,
      ...context
    })
  }, [componentName]);
  
  // Retourner toutes les fonctions utilitaires
  return {
    trackUserAction,
    measurePerformance,
    measurePerformanceAsync,
    startMeasure,
    endMeasure,
    log,
    debug,
    info,
    warn,
    error,
    captureError
  }
};

export default useMonitoring;