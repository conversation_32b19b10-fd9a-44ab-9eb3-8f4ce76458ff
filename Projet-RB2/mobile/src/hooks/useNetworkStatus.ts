/**
 * Hook pour surveiller l'état de la connexion réseau
 */
import { useState, useEffect, useCallback } from 'react';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import { Platform } from 'react-native';

// Interface pour le statut réseau
export interface NetworkStatus {
  isConnected: boolean;
  connectionType: string | null;
  isInternetReachable: boolean | null;
  details: any;
  lastChecked: Date
}

/**
 * Hook pour surveiller l'état de la connexion réseau
 * @param options Options de configuration
 * @returns État de la connexion réseau
 */
export const useNetworkStatus = (options?: {
  checkOnMount?: boolean;
  checkInterval?: number;
}) => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true, // Optimiste par défaut
    connectionType: null,
    isInternetReachable: null,
    details: null,
    lastChecked: new Date()
  })

  // Fonction pour vérifier l'état de la connexion
  const checkConnection = useCallback(async () => {
    try {
      const state = await NetInfo.fetch()
      updateNetworkStatus(state);
    } catch(error) {
      console.error('Error checking network status:', error);
    }
  }, []);

  // Mettre à jour l'état de la connexion
  const updateNetworkStatus = (state: NetInfoState) => {
    setNetworkStatus({
      isConnected: state.isConnected ?? false,
      connectionType: state.type,
      isInternetReachable: state.isInternetReachable,
      details: state.details,
      lastChecked: new Date()
    })
  }

  // Écouter les changements de connexion
  useEffect(() => {
    // Vérifier la connexion au montage si demandé
    if(options?.checkOnMount !== false) {
      checkConnection()
    }

    // S'abonner aux changements d'état de la connexion
    const unsubscribe = NetInfo.addEventListener(updateNetworkStatus);

    // Configurer un intervalle de vérification si demandé
    let intervalId: NodeJS.Timeout | undefined;
    if(options?.checkInterval && options.checkInterval > 0) {
      intervalId = setInterval(checkConnection, options.checkInterval);
    }

    // Nettoyer les abonnements et intervalles
    return () => {
      unsubscribe()
      if(intervalId) {
        clearInterval(intervalId);
      }
    }
  }, [options?.checkOnMount, options?.checkInterval, checkConnection]);

  // Fonction pour forcer une vérification de la connexion
  const refreshNetworkStatus = useCallback(async () => {
    await checkConnection()
    return networkStatus;
  }, [checkConnection, networkStatus]);

  // Fonction pour vérifier si une URL spécifique est accessible
  const checkEndpointReachability = useCallback(
    async (url: string, timeout = 5000): Promise<boolean> => {
      if(!networkStatus.isConnected) {
        return false;
      }

      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          method: 'HEAD',
          signal: controller.signal
        })

        clearTimeout(timeoutId);
        return response.ok;
      } catch(error) {
        console.log(`Endpoint ${url} is not reachable:`, error);
        return false;
      }
    },
    [networkStatus.isConnected]
  );

  return {
    ...networkStatus,
    refreshNetworkStatus,
    checkEndpointReachability
  }
};

export default useNetworkStatus;