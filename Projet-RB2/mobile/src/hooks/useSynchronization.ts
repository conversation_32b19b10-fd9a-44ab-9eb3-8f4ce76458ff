import { useState, useEffect, useCallback } from 'react';
import { SyncService, SYNC_EVENTS, ConflictResolutionStrategy } from '../services/sync.service';
import { periodicSyncService } from '../services/periodicSync.service';
import { EventEmitter } from '../utils/eventEmitter';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import { Platform } from 'react-native';
import { resetDatabase as resetDatabaseFn } from '../database';

interface UseSynchronizationOptions {
  autoSync?: boolean;
  syncInterval?: number; // en millisecondes
}

// Type pour les conflits résolus - utilisé dans l'interface SyncHookState
interface ResolvedConflict {
  id: string;
  entityName: string;
  localData: any;
  serverData: any;
  localModifiedAt: number;
  serverModifiedAt: number;
  resolvedAt: number;
  resolutionStrategy: ConflictResolutionStrategy;
}

// Interface pour le hook de synchronisation
interface SyncHookState {
  isSyncing: boolean;
  lastSyncTimestamp: number | null;
  error: Error | null;
  progress: number;
  pendingConflicts: Array<{
    id: string;
    entityName: string;
    localData: any;
    serverData: any;
    localModifiedAt: number;
    serverModifiedAt: number;
  }>;
  resolvedConflicts: Array<ResolvedConflict>;
  pendingOperationsCount: number;
}

/**
 * Hook pour gérer la synchronisation avec le backend Prisma
 *
 * @param options Options de configuration
 * @returns Des méthodes et états pour contrôler la synchronisation
 */
export function useSynchronization(options: UseSynchronizationOptions = {}) {
  const {
    autoSync = true,
    syncInterval = 10 * 60 * 1000 // 10 minutes par défaut
  } = options;

  // États de synchronisation
  const [state, setState] = useState<SyncHookState>({
    isSyncing: false,
    lastSyncTimestamp: null,
    error: null,
    progress: 0,
    pendingConflicts: [],
    resolvedConflicts: [],
    pendingOperationsCount: 0,
  });
  const [isOnline, setIsOnline] = useState<boolean>(true);

  // Chargement initial des données
  const loadInitialData = useCallback(async () => {
    try {
      const lastSync = await SyncService.getLastSyncTimestamp();
      const pendingConflicts = await SyncService.getPendingConflicts();
      const resolvedConflicts = await SyncService.getResolvedConflicts();
      const pendingOperationsCount = await SyncService.getPendingOperationsCount();

      setState(prevState => ({
        ...prevState,
        lastSyncTimestamp: lastSync,
        pendingConflicts,
        resolvedConflicts,
        pendingOperationsCount,
      }));
    } catch (error) {
      console.error('Erreur lors du chargement des données de synchronisation:', error);
    }
  }, []);

  // Surveillance de la connexion réseau
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((networkState: NetInfoState) => {
      // Vérification sécurisée de l'état de connexion
      const online = networkState?.isConnected === true &&
                    networkState?.isInternetReachable === true;
      setIsOnline(online);

      // Si la connexion est rétablie et des modifications sont en attente, proposer une synchronisation
      if (online && !state.isSyncing) {
        checkPendingOperations();
      }
    });

    return () => unsubscribe();
  }, [state.isSyncing]);

  // Mettre en place la synchronisation automatique
  useEffect(() => {
    if (!autoSync) return;

    // Utiliser le service de synchronisation périodique
    periodicSyncService.setSyncInterval(syncInterval);
    periodicSyncService.startPeriodicSync();

    return () => {
      periodicSyncService.stopPeriodicSync();
    };
  }, [autoSync, syncInterval]);

  // Écouter les événements de synchronisation
  useEffect(() => {
    // Charger les données initiales
    loadInitialData();

    // Gestionnaires d'événements
    const handleSyncStarted = () => {
      setState(prevState => ({ ...prevState, isSyncing: true, error: null, progress: 0 }));
    };

    const handleSyncProgress = (progress: number) => {
      setState(prevState => ({ ...prevState, progress }));
    };

    const handleSyncCompleted = async () => {
      const lastSync = await SyncService.getLastSyncTimestamp();
      const pendingOperationsCount = await SyncService.getPendingOperationsCount();

      setState(prevState => ({
        ...prevState,
        isSyncing: false,
        lastSyncTimestamp: lastSync,
        pendingOperationsCount,
        progress: 1,
      }));
    };

    const handleSyncFailed = (error: Error) => {
      setState(prevState => ({
        ...prevState,
        isSyncing: false,
        error,
      }));
    };

    const handleConflictDetected = async () => {
      const pendingConflicts = await SyncService.getPendingConflicts();
      setState(prevState => ({
        ...prevState,
        pendingConflicts,
      }));
    };

    const handleConflictResolved = async () => {
      const pendingConflicts = await SyncService.getPendingConflicts();
      const resolvedConflicts = await SyncService.getResolvedConflicts();

      setState(prevState => ({
        ...prevState,
        pendingConflicts,
        resolvedConflicts,
      }));
    };

    // S'abonner aux événements
    EventEmitter.on(SYNC_EVENTS.SYNC_STARTED, handleSyncStarted);
    EventEmitter.on(SYNC_EVENTS.SYNC_PROGRESS, handleSyncProgress);
    EventEmitter.on(SYNC_EVENTS.SYNC_COMPLETED, handleSyncCompleted);
    EventEmitter.on(SYNC_EVENTS.SYNC_FAILED, handleSyncFailed);
    EventEmitter.on(SYNC_EVENTS.CONFLICT_DETECTED, handleConflictDetected);
    EventEmitter.on(SYNC_EVENTS.CONFLICT_RESOLVED, handleConflictResolved);

    // Se désabonner lors du démontage
    return () => {
      EventEmitter.off(SYNC_EVENTS.SYNC_STARTED, handleSyncStarted);
      EventEmitter.off(SYNC_EVENTS.SYNC_PROGRESS, handleSyncProgress);
      EventEmitter.off(SYNC_EVENTS.SYNC_COMPLETED, handleSyncCompleted);
      EventEmitter.off(SYNC_EVENTS.SYNC_FAILED, handleSyncFailed);
      EventEmitter.off(SYNC_EVENTS.CONFLICT_DETECTED, handleConflictDetected);
      EventEmitter.off(SYNC_EVENTS.CONFLICT_RESOLVED, handleConflictResolved);
    };
  }, [loadInitialData]);

  // Charger les conflits en attente
  const loadPendingConflicts = useCallback(async () => {
    try {
      const pendingConflicts = await SyncService.getPendingConflicts();
      setState(prevState => ({
        ...prevState,
        pendingConflicts,
      }));
      return pendingConflicts;
    } catch (error) {
      console.error('Erreur lors du chargement des conflits en attente:', error);
      return [];
    }
  }, []);

  // Vérifier les opérations en attente
  const checkPendingOperations = useCallback(async () => {
    try {
      const pendingCount = await SyncService.getPendingOperationsCount();
      return pendingCount || 0;
    } catch (error) {
      console.error('Erreur lors de la vérification des opérations en attente:', error);
      return 0;
    }
  }, []);

  // Synchroniser toutes les entités
  const syncAll = useCallback(async () => {
    if (state.isSyncing || !isOnline) return false;

    try {
      await SyncService.syncAll();
      return true;
    } catch (error) {
      console.error('Erreur lors de la synchronisation:', error);
      return false;
    }
  }, [state.isSyncing, isOnline]);

  // Synchroniser une entité spécifique
  const syncEntity = useCallback(async (entityName: string) => {
    if (state.isSyncing || !isOnline) return false;

    try {
      return await SyncService.syncEntity(entityName);
    } catch (error) {
      console.error(`Erreur lors de la synchronisation de l'entité ${entityName}:`, error);
      return false;
    }
  }, [state.isSyncing, isOnline]);

  // Résoudre un conflit
  const resolveConflict = useCallback(async (
    conflictId: string,
    strategy: ConflictResolutionStrategy,
    customMerge?: any
  ) => {
    try {
      // Implémentation d'une résolution de conflit personnalisée via événements
      // puisque SyncService.resolveConflict est privée

      // Récupérer les conflits en attente
      const pendingConflictsJson = await SyncService.getPendingConflicts();
      const conflictIndex = pendingConflictsJson.findIndex((c: any) => c.id === conflictId);

      if (conflictIndex === -1) {
        throw new Error('Conflit non trouvé');
      }

      // Émettre un événement que SyncService devrait écouter
      EventEmitter.emit('custom:resolve_conflict', {
        conflictId,
        strategy,
        customMerge
      });

      // Attendre un peu pour que le traitement soit effectué
      await new Promise(resolve => setTimeout(resolve, 100));

      // Recharger les conflits après la résolution
      const pendingConflicts = await SyncService.getPendingConflicts();
      const resolvedConflicts = await SyncService.getResolvedConflicts();

      setState(prevState => ({
        ...prevState,
        pendingConflicts,
        resolvedConflicts,
      }));

      return true;
    } catch (error) {
      console.error('Erreur lors de la résolution du conflit:', error);
      return false;
    }
  }, []);

  // Déclencher une synchronisation
  const triggerSync = useCallback(async () => {
    try {
      setState(prevState => ({ ...prevState, isSyncing: true, error: null, progress: 0 }));

      // Utiliser le service de synchronisation périodique
      const success = await periodicSyncService.performManualSync();

      if (!success) {
        throw new Error('La synchronisation a échoué');
      }

      // Après la synchronisation, mettre à jour les données
      const lastSync = await SyncService.getLastSyncTimestamp();
      const pendingConflicts = await SyncService.getPendingConflicts();
      const resolvedConflicts = await SyncService.getResolvedConflicts();
      const pendingOperationsCount = await SyncService.getPendingOperationsCount();

      setState(prevState => ({
        ...prevState,
        isSyncing: false,
        lastSyncTimestamp: lastSync,
        pendingConflicts,
        resolvedConflicts,
        pendingOperationsCount,
        progress: 1,
      }));

      return true;
    } catch (error) {
      setState(prevState => ({
        ...prevState,
        isSyncing: false,
        error: error instanceof Error ? error : new Error('Erreur de synchronisation'),
      }));

      return false;
    }
  }, []);

  // Calculer si une synchronisation est nécessaire (plus de 30 minutes depuis la dernière sync)
  const isSyncNeeded = useCallback(() => {
    if (!state.lastSyncTimestamp) return true;

    const thirtyMinutesInMs = 30 * 60 * 1000;
    const now = Date.now();
    return now - state.lastSyncTimestamp > thirtyMinutesInMs;
  }, [state.lastSyncTimestamp]);

  // Formater la date de dernière synchronisation
  const formatLastSyncTime = useCallback(() => {
    if (!state.lastSyncTimestamp) return 'Jamais';

    const date = new Date(state.lastSyncTimestamp);
    return date.toLocaleString(Platform.OS === 'ios' ? 'fr-FR' : 'fr', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }, [state.lastSyncTimestamp]);

  // Fonction pour réinitialiser la base de données
  const resetDatabase = useCallback(async (): Promise<boolean> => {
    try {
      return await resetDatabaseFn();
    } catch (error) {
      console.error('Erreur lors de la réinitialisation de la base de données:', error);
      return false;
    }
  }, []);

  return {
    isSyncing: state.isSyncing,
    lastSyncTimestamp: state.lastSyncTimestamp,
    error: state.error,
    progress: state.progress,
    pendingConflicts: state.pendingConflicts,
    resolvedConflicts: state.resolvedConflicts,
    pendingOperationsCount: state.pendingOperationsCount,
    isOnline,
    formatLastSyncTime,
    isSyncNeeded,
    syncAll,
    syncEntity,
    resolveConflict,
    loadPendingConflicts,
    checkPendingOperations,
    triggerSync,
    resetDatabase
  };
}