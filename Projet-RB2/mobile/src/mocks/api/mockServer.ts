import { API_URL } from '../../config/api.config';
import { retreatsMock, RetreatMock } from './data/retreats';
import { bookingsMock, BookingMock } from './data/bookings';
import { usersMock, UserMock } from './data/users';
import { notificationsMock, NotificationMock } from './data/notifications';

// Stockage des données en mémoire pour simuler une persistance
let retreats = [...retreatsMock];
let bookings = [...bookingsMock];
let users = [...usersMock];
let notifications = [...notificationsMock];

// Compteur pour simuler un timestamp serveur
let serverTimestamp = Date.now();

// État de synchronisation pour le tracking
interface SyncState {
  lastPulledAt: number | null;
  changes: any;
}

let lastSync: SyncState = {
  lastPulledAt: null,
  changes: {}
}

/**
 * Simule une réponse asynchrone avec un délai pour imiter un serveur réel
 */
function simulateResponse<T>(data: T, status = 200, delay = 300): Promise<{ data: T, status: number }> {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ data, status });
    }, delay);
  });
}

/**
 * Simule une erreur de réseau
 */
function simulateNetworkError(delay = 300): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error('Network Error'));
    }, delay);
  });
}

/**
 * Intercepte les appels fetch et retourne des réponses mockées
 */
export function setupMockServer() {
  // On sauvegarde la fonction fetch originale
  const originalFetch = global.fetch;

  // On remplace fetch par notre version mockée
  global.fetch = async (url: string | URL | Request, options: RequestInit = {}) => {
    // Convertir en string si l'URL est complexe
    const urlString = url instanceof URL ? url.toString() : url instanceof Request ? url.url : url;
    
    // On extrait la partie après le domaine 
    const endpoint = urlString.replace(API_URL, '');
    const method = options.method || 'GET';
    
    console.log(`[Mock Server] ${method} ${endpoint}`);
    
    // Simuler une erreur réseau aléatoire (10% de chance)
    if (Math.random() < 0.1) {
      return simulateNetworkError().then(() => {
        throw new Error('Network Error');
      });
    }
    
    // Mapper les endpoints aux réponses mockées
    try {
      let response: { data: any, status: number };
      
      if (endpoint.startsWith('/sync')) {
        response = await handleSyncEndpoint(options);
      } else if (endpoint.startsWith('/retreats')) {
        response = await handleRetreatsEndpoint(endpoint, method, options);
      } else if (endpoint.startsWith('/auth')) {
        response = await handleAuthEndpoint(endpoint, method, options);
      } else if (endpoint.startsWith('/users')) {
        response = await handleUsersEndpoint(endpoint, method, options);
      } else {
        // Fallback pour les autres endpoints non implémentés
        response = await simulateResponse({ message: 'Not implemented' }, 501);
      }
      
      // Créer un objet Response basé sur les données mockées
      return new Response(JSON.stringify(response.data), {
        status: response.status,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (error: any) {
      console.error('[Mock Server] Error:', error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  };

  // Fonction pour restaurer le fetch original
  return () => {
    global.fetch = originalFetch;
  };
}

/**
 * Gère les endpoints de synchronisation
 */
async function handleSyncEndpoint(options: RequestInit): Promise<{ data: any, status: number }> {
  const method = options.method || 'GET';
  let body: any = {};
  
  if (options.body) {
    try {
      body = JSON.parse(options.body.toString());
    } catch (e) {
      console.error('[Mock Server] Error parsing body:', e);
    }
  }
  
  if (method === 'POST') {
    // Pull changes
    const { lastPulledAt } = body;
    
    // Simuler des changements côté serveur
    const changes = {
      retreats: retreats.map(r => ({ ...r, _status: 'updated' })),
      bookings: bookings.map(b => ({ ...b, _status: 'updated' })),
      users: users.map(u => ({ ...u, _status: 'updated' })),
      notifications: notifications.map(n => ({ ...n, _status: 'updated' }))
    };
    
    serverTimestamp = Date.now();
    
    lastSync = {
      lastPulledAt: serverTimestamp,
      changes
    };
    
    return simulateResponse({
      changes,
      timestamp: serverTimestamp
    });
  } else if (method === 'PUT') {
    // Push changes
    const { changes } = body;
    
    // Traiter les changements envoyés par le client
    if (changes) {
      // Simuler la mise à jour des données sur le serveur
      console.log('[Mock Server] Received changes:', JSON.stringify(changes, null, 2));
      
      // Mettre à jour serverTimestamp pour la prochaine sync
      serverTimestamp = Date.now();
    }
    
    return simulateResponse({ success: true });
  }
  
  return simulateResponse({ error: 'Invalid sync method' }, 400);
}

/**
 * Gère les endpoints des retraites
 */
async function handleRetreatsEndpoint(endpoint: string, method: string, options: RequestInit): Promise<{ data: any, status: number }> {
  if (endpoint === '/retreats' && method === 'GET') {
    return simulateResponse(retreats);
  }
  
  // Extraction de l'ID depuis l'URL (ex: /retreats/123)
  const idMatch = endpoint.match(/\/retreats\/([^\/]+)$/);
  if (idMatch) {
    const id = idMatch[1];
    const retreat = retreats.find(r => r.id === id);
    
    if (!retreat) {
      return simulateResponse({ error: 'Retreat not found' }, 404);
    }
    
    return simulateResponse(retreat);
  }
  
  // Endpoint pour réserver une retraite
  const bookMatch = endpoint.match(/\/retreats\/([^\/]+)\/book$/);
  if (bookMatch && method === 'POST') {
    const retreatId = bookMatch[1];
    let body: any = {};
    
    if (options.body) {
      try {
        body = JSON.parse(options.body.toString());
      } catch (e) {
        console.error('[Mock Server] Error parsing body:', e);
      }
    }
    
    // Créer une nouvelle réservation
    const newBooking: BookingMock = {
      id: `booking-${Date.now()}`,
      retreatId,
      userId: body.userId || 'current-user',
      status: 'confirmed',
      participants: 1,
      totalPrice: 0, // À calculer en fonction de la retraite
      currency: 'EUR',
      date: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    bookings.push(newBooking);
    
    return simulateResponse({ booking: newBooking, success: true });
  }
  
  return simulateResponse({ error: 'Invalid retreats endpoint' }, 400);
}

/**
 * Gère les endpoints d'authentification
 */
async function handleAuthEndpoint(endpoint: string, method: string, options: RequestInit): Promise<{ data: any, status: number }> {
  if (endpoint === '/auth/login' && method === 'POST') {
    let body: any = {};
    
    if (options.body) {
      try {
        body = JSON.parse(options.body.toString());
      } catch (e) {
        console.error('[Mock Server] Error parsing body:', e);
      }
    }
    
    const { email, password } = body;
    
    // Simulation d'authentification simple
    if (email && password) {
      const user = users.find(u => u.email === email);
      
      if (user) {
        // En mode mock, on accepte n'importe quel mot de passe
        return simulateResponse({
          token: 'mock-jwt-token-' + Date.now(),
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName
          }
        });
      }
    }
    
    return simulateResponse({ error: 'Invalid credentials' }, 401);
  }
  
  if (endpoint === '/auth/register' && method === 'POST') {
    let body: any = {};
    
    if (options.body) {
      try {
        body = JSON.parse(options.body.toString());
      } catch (e) {
        console.error('[Mock Server] Error parsing body:', e);
      }
    }
    
    const { email, password, firstName, lastName } = body;
    
    if (email && password && firstName && lastName) {
      // Vérifier si l'utilisateur existe déjà
      const existingUser = users.find(u => u.email === email);
      if (existingUser) {
        return simulateResponse({ error: 'User already exists' }, 409);
      }
      
      // Créer un nouvel utilisateur
      const newUser: UserMock = {
        id: `user-${Date.now()}`,
        email,
        firstName,
        lastName,
        role: 'user',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      users.push(newUser);
      
      return simulateResponse({
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.firstName,
          lastName: newUser.lastName
        }
      });
    }
    
    return simulateResponse({ error: 'Invalid registration data' }, 400);
  }
  
  return simulateResponse({ error: 'Invalid auth endpoint' }, 400);
}

/**
 * Gère les endpoints des utilisateurs
 */
async function handleUsersEndpoint(endpoint: string, method: string, options: RequestInit): Promise<{ data: any, status: number }> {
  if (endpoint === '/users/profile' && method === 'GET') {
    // Simuler le profil de l'utilisateur courant
    const currentUser = users.find(u => u.id === 'current-user');
    if (currentUser) {
      return simulateResponse(currentUser);
    }
    
    // Fallback si l'utilisateur n'est pas trouvé
    return simulateResponse({
      id: 'current-user',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  
  if (endpoint === '/users/bookings' && method === 'GET') {
    // Retourner les réservations de l'utilisateur courant
    const userBookings = bookings.filter(booking => booking.userId === 'current-user');
    return simulateResponse(userBookings);
  }
  
  if (endpoint === '/users/notifications' && method === 'GET') {
    // Retourner les notifications de l'utilisateur courant
    const userNotifications = notifications.filter(notif => notif.userId === 'current-user');
    return simulateResponse(userNotifications);
  }
  
  return simulateResponse({ error: 'Invalid users endpoint' }, 400);
} 