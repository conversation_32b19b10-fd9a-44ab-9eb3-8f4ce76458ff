import Model from '@nozbe/watermelondb/Model';
// import { field, date, readonly, children, table } from '@nozbe/watermelondb/decorators';
import { field, date, readonly, children, table } from '../types/watermelondb-stubs';

@table('retreats')
export class Retreat extends Model {
  static table = 'retreats';

  @field('name') name!: string;
  @field('description') description!: string;
  @field('location') location!: string;
  @date('start_date') startDate!: Date;
  @date('end_date') endDate!: Date;
  @field('capacity') capacity!: number;
  @field('price') price!: number;
  @field('image_url') imageUrl?: string;
  @field('status') status!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;

  // Relations
  @children('bookings') bookings: any;

  // Méthodes utilitaires
  get availableSpots(): number {
    // Cette méthode serait normalement implémentée pour calculer les places disponibles
    // en se basant sur les réservations associées
    return this.capacity;
  }

  // Champ calculé : la retraite est-elle complète ?
  get isFull(): boolean {
    return this.availableSpots <= 0;
  }

  // Champ calculé : durée en jours
  get durationInDays(): number {
    const diffTime = Math.abs(this.endDate.getTime() - this.startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}

// Export par défaut pour compatibilité avec code existant
export default Retreat;
