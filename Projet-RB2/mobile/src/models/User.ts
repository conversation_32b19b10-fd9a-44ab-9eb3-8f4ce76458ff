import Model from '@nozbe/watermelondb/Model';
// import { field, date, readonly, children, relation, table } from '@nozbe/watermelondb/decorators';
import { field, date, readonly, children, relation, table } from '../types/watermelondb-stubs';

@table('users')
export class User extends Model {
  static table = 'users';

  @field('email') email!: string;
  @field('first_name') firstName!: string;
  @field('last_name') lastName!: string;
  @field('avatar_url') avatarUrl?: string;
  @field('phone') phone?: string;
  @field('role') role!: string;
  @readonly @date('created_at') createdAt!: Date;
  @readonly @date('updated_at') updatedAt!: Date;

  // Relations
  @children('bookings') bookings: any;
  @children('notifications') notifications: any;
  
  // Nom complet de l'utilisateur
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
}

// Export par défaut pour compatibilité avec code existant
export default User;
