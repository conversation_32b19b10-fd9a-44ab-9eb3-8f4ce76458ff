// Mock decorators for WatermelonDB
// This file provides TypeScript-compatible decorator functions to simulate WatermelonDB decorators

/**
 * <PERSON><PERSON> @field decorator
 */
export function field(columnName: string): PropertyDecorator {
  return function(_target: Object, _propertyKey: string | symbol): void {
    // In a real implementation, this would set up field mapping
  };
}

/**
 * <PERSON>ck @date decorator
 */
export function date(columnName: string): PropertyDecorator {
  return function(_target: Object, _propertyKey: string | symbol): void {
    // In a real implementation, this would set up date field mapping
  };
}

/**
 * <PERSON><PERSON> @relation decorator
 */
export function relation(table: string, foreignKey: string): PropertyDecorator {
  return function(_target: Object, _propertyKey: string | symbol): void {
    // In a real implementation, this would set up relation mapping
  };
}

/**
 * Mock @children decorator
 */
export function children(table: string): PropertyDecorator {
  return function(_target: Object, _propertyKey: string | symbol): void {
    // In a real implementation, this would set up children mapping
  };
}

/**
 * <PERSON><PERSON> @readonly decorator
 */
export function readonly(_target: Object, _propertyKey: string | symbol): void {
  // In a real implementation, this would mark a field as readonly
}

/**
 * <PERSON><PERSON> @lazy decorator
 */
export function lazy(): PropertyDecorator {
  return function(_target: Object, _propertyKey: string | symbol): void {
    // In a real implementation, this would set up lazy loading
  };
}

/**
 * Mock @immutableRelation decorator
 */
export function immutableRelation(table: string, foreignKey: string): PropertyDecorator {
  return function(_target: Object, _propertyKey: string | symbol): void {
    // In a real implementation, this would set up immutable relation mapping
  };
}

/**
 * Mock @action decorator
 */
export function action(_target: Object, _propertyKey: string | symbol, _descriptor?: PropertyDescriptor): PropertyDescriptor | void {
  // In a real implementation, this would decorate a method as an action
  return;
}

/**
 * Mock table decorator
 */
export function table(tableName: string): ClassDecorator {
  return function<T extends Function>(_constructor: T): void {
    // In a real implementation, this would set up table mapping
  };
} 