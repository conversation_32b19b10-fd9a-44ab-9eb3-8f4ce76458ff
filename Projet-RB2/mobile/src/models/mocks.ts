/**
 * Mock des types et fonctions pour WatermelonDB
 * Ce fichier simule les dépendances pour éviter les erreurs TypeScript
 * Une fois que WatermelonDB sera correctement installé, ce fichier pourra être supprimé
 */

// Class de base pour tous les modèles
export class Model {
  static table: string;
  
  id: string;
  _raw: any;
  _isEditing: boolean;
  _preparedState: any;

  constructor(data?: any) {
    this.id = data?.id || Math.random().toString(36).substring(2, 15);
    this._raw = data || {};
    this._isEditing = false;
    this._preparedState = null;
  }

  prepareDestroyPermanently(): any {
    return { type: 'destroy', record: this };
  }
}

// Décorateurs pour les champs
export function field(name: string): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    // Cette fonction crée un getter/setter pour le champ
  };
}

export function date(name: string): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    // Cette fonction crée un getter/setter pour le champ date
  };
}

export function readonly(target: any, propertyKey: string | symbol): void {
  // Cette fonction marque un champ comme étant en lecture seule
}

export function children(relation: string): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    // Cette fonction définit une relation "un à plusieurs"
  };
}

export function relation(relation: string, inverseName?: string): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    // Cette fonction définit une relation "un à un"
  };
}

// Export un objet Q pour les requêtes
export const Q = {
  where: (field: string, operator: string, value: any) => ({
    field,
    operator,
    value,
  }),
  or: (...conditions: any[]) => ({
    type: 'or',
    conditions,
  }),
  and: (...conditions: any[]) => ({
    type: 'and',
    conditions,
  }),
}; 