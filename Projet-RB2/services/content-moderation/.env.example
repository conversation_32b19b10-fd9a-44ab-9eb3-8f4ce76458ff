# Application
NODE_ENV=development
PORT=3010
API_PREFIX=api
APP_NAME=content-moderation

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/content_moderation?schema=public

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRATION=1d
JWT_REFRESH_SECRET=your-refresh-secret
JWT_REFRESH_EXPIRATION=7d

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging
LOG_LEVEL=debug

# External Services
USER_SERVICE_URL=http://localhost:3000/api
CONTENT_SERVICE_URL=http://localhost:3001/api
NOTIFICATION_SERVICE_URL=http://localhost:3002/api

# AI Models
TEXT_MODEL_PATH=./models/text-moderation
IMAGE_MODEL_PATH=./models/image-moderation

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100
