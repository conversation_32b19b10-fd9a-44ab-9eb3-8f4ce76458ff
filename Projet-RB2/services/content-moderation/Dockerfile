# Stage 1: Build
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Stage 2: Production
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install production dependencies
RUN npm ci --only=production

# Copy Prisma schema
COPY prisma ./prisma/

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma

# Create models directory
RUN mkdir -p ./models/text-moderation
RUN mkdir -p ./models/image-moderation

# Create logs directory
RUN mkdir -p ./logs

# Expose port
EXPOSE 3010

# Set environment variables
ENV NODE_ENV=production

# Run the application
CMD ["node", "dist/main"]
