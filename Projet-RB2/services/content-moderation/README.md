# Service de Modération de Contenu - Retreat And Be

Ce microservice est responsable de la modération automatique et manuelle du contenu sur la plateforme Retreat And Be. Il fournit des API pour la détection de contenu inapproprié, la gestion des signalements et les workflows de modération.

## Fonctionnalités

- Détection automatique de contenu textuel inapproprié
- Détection de contenu inapproprié dans les images
- Système de signalement par les utilisateurs
- Workflows de modération pour les modérateurs
- Tableau de bord de modération
- Système de réputation des utilisateurs
- Métriques de performance de modération

## Architecture

Le service est construit avec NestJS et s'intègre avec les autres microservices de la plateforme via des API REST et des webhooks.

```
                  ┌─────────────────────┐
                  │                     │
                  │  Frontend           │
                  │                     │
                  └─────────┬───────────┘
                            │
                            ▼
┌─────────────────────────────────────────────┐
│                                             │
│  API Gateway                                │
│                                             │
└─────────────────┬───────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Service de Modération de Contenu           │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Détection   │    │ Gestion des         │ │
│  │ Automatique │    │ Signalements        │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Workflows   │    │ Système de          │ │
│  │ Modération  │    │ Réputation          │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
└─────────────────────┬───────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Base de Données                            │
│                                             │
└─────────────────────────────────────────────┘
```

## Technologies

- **Backend**: NestJS, TypeScript
- **Base de données**: PostgreSQL avec Prisma ORM
- **IA & ML**: TensorFlow, Hugging Face Transformers
- **Vision par ordinateur**: TensorFlow Image, OpenCV
- **Cache**: Redis
- **Monitoring**: Prometheus, Grafana

## Installation

```bash
# Installer les dépendances
npm install

# Configurer les variables d'environnement
cp .env.example .env

# Lancer les migrations de base de données
npm run prisma:migrate

# Démarrer le service en mode développement
npm run start:dev
```

## Structure du Projet

```
content-moderation/
├── src/
│   ├── main.ts                    # Point d'entrée de l'application
│   ├── app.module.ts              # Module principal
│   ├── config/                    # Configuration
│   ├── common/                    # Code partagé
│   ├── prisma/                    # Client Prisma et schémas
│   ├── text-moderation/           # Modération de texte
│   ├── image-moderation/          # Modération d'images
│   ├── reports/                   # Gestion des signalements
│   ├── workflows/                 # Workflows de modération
│   ├── reputation/                # Système de réputation
│   └── dashboard/                 # API pour le tableau de bord
├── test/                          # Tests
├── prisma/                        # Schémas Prisma
├── Dockerfile                     # Configuration Docker
└── docker-compose.yml             # Configuration Docker Compose
```

## API

### Modération de Texte

- `POST /api/moderation/text` - Analyser un texte pour détecter du contenu inapproprié
- `GET /api/moderation/text/rules` - Obtenir les règles de modération de texte

### Modération d'Images

- `POST /api/moderation/image` - Analyser une image pour détecter du contenu inapproprié
- `GET /api/moderation/image/rules` - Obtenir les règles de modération d'images

### Signalements

- `POST /api/reports` - Créer un signalement
- `GET /api/reports` - Obtenir la liste des signalements
- `GET /api/reports/:id` - Obtenir les détails d'un signalement
- `PATCH /api/reports/:id` - Mettre à jour un signalement

### Workflows de Modération

- `POST /api/workflows/actions/:reportId` - Exécuter une action de modération
- `GET /api/workflows/actions` - Obtenir les actions de modération disponibles
- `GET /api/workflows/history/:reportId` - Obtenir l'historique des actions pour un signalement

### Système de Réputation

- `GET /api/reputation/:userId` - Obtenir la réputation d'un utilisateur
- `GET /api/reputation/privileges` - Obtenir les privilèges basés sur la réputation

### Tableau de Bord

- `GET /api/dashboard/metrics` - Obtenir les métriques de modération
- `GET /api/dashboard/reports/stats` - Obtenir les statistiques des signalements

## Intégration avec les Autres Services

- **Service d'Authentification** : Vérification des tokens JWT et des permissions
- **Service Utilisateur** : Récupération des informations utilisateur et mise à jour des statuts
- **Service de Contenu** : Récupération et mise à jour du contenu modéré
- **Service de Notification** : Envoi de notifications aux utilisateurs et modérateurs

## Développement

### Exécution des Tests

```bash
# Tests unitaires
npm run test

# Tests e2e
npm run test:e2e

# Couverture de test
npm run test:cov
```

### Linting et Formatting

```bash
# Linting
npm run lint

# Formatting
npm run format
```

## Déploiement

Le service est déployé en tant que conteneur Docker dans un cluster Kubernetes.

```bash
# Construire l'image Docker
docker build -t retreat-and-be/content-moderation .

# Déployer sur Kubernetes
kubectl apply -f k8s/
```
