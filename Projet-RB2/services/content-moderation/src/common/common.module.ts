import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ExternalServiceClient } from './services/external-service.client';
import { CacheService } from './services/cache.service';
import { AgentIAService } from './services/agent-ia.service';

@Global()
@Module({
  imports: [
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [ExternalServiceClient, CacheService, AgentIAService],
  exports: [ExternalServiceClient, CacheService, AgentIAService],
})
export class CommonModule {}
