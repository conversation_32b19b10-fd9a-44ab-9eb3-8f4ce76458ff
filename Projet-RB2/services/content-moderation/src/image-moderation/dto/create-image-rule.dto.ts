import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsO<PERSON>al, IsBoolean, IsN<PERSON>ber, Min, Max } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Severity } from '@prisma/client';

export class CreateImageRuleDto {
  @ApiProperty({
    description: 'Nom de la règle',
    example: 'Détection de contenu explicite',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description de la règle',
    example: 'Détecte les images contenant du contenu explicite',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Catégorie de contenu à détecter',
    example: 'nudity',
  })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({
    description: 'Seuil de confiance (entre 0 et 1)',
    example: 0.7,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold: number;

  @ApiProperty({
    description: 'Sévérité de la règle',
    enum: Severity,
    example: Severity.HIGH,
  })
  @IsEnum(Severity)
  severity: Severity;

  @ApiProperty({
    description: 'Indique si la règle est active',
    example: true,
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
