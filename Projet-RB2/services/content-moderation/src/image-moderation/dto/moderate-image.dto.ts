import { IsS<PERSON>, <PERSON>NotEmpty, IsUrl, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ModerateImageDto {
  @ApiProperty({
    description: 'URL de l\'image à modérer',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsUrl()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'Image encodée en base64',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
    required: false,
  })
  @IsString()
  @IsOptional()
  base64Image?: string;

  constructor(partial: Partial<ModerateImageDto>) {
    Object.assign(this, partial);
  }
}
