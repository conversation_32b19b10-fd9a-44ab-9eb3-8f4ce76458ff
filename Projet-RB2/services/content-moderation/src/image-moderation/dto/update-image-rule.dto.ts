import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsBoolean, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Severity } from '@prisma/client';

export class UpdateImageRuleDto {
  @ApiProperty({
    description: 'Nom de la règle',
    example: 'Détection de contenu explicite',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Description de la règle',
    example: 'Détecte les images contenant du contenu explicite',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Catégorie de contenu à détecter',
    example: 'nudity',
    required: false,
  })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiProperty({
    description: 'Seuil de confiance (entre 0 et 1)',
    example: 0.7,
    minimum: 0,
    maximum: 1,
    required: false,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  threshold?: number;

  @ApiProperty({
    description: 'Sévérité de la règle',
    enum: Severity,
    example: Severity.HIGH,
    required: false,
  })
  @IsEnum(Severity)
  @IsOptional()
  severity?: Severity;

  @ApiProperty({
    description: 'Indique si la règle est active',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
