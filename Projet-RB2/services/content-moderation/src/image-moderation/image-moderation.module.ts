import { Module } from '@nestjs/common';
import { ImageModerationService } from './image-moderation.service';
import { ImageModerationController } from './image-moderation.controller';
import { ImageModerationRuleRepository } from './repositories/image-moderation-rule.repository';
import { AgentIAService } from '../common/services/agent-ia.service';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [CommonModule],
  controllers: [ImageModerationController],
  providers: [ImageModerationService, ImageModerationRuleRepository, AgentIAService],
  exports: [ImageModerationService],
})
export class ImageModerationModule {}
