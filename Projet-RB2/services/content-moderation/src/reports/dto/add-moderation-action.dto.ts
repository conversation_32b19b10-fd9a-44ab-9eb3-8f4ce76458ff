import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ActionType } from '@prisma/client';

export class AddModerationActionDto {
  @ApiProperty({
    description: 'ID du modérateur',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @IsString()
  @IsNotEmpty()
  moderatorId: string;

  @ApiProperty({
    description: 'Type d\'action de modération',
    enum: ActionType,
    example: ActionType.APPROVE,
  })
  @IsEnum(ActionType)
  action: ActionType;

  @ApiProperty({
    description: 'Commentaire sur l\'action de modération',
    example: 'Contenu approuvé après vérification',
    required: false,
  })
  @IsString()
  @IsOptional()
  comment?: string;
}
