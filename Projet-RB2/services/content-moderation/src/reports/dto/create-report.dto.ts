import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ContentType } from '@prisma/client';

export class CreateReportDto {
  @ApiProperty({
    description: 'Type de contenu signalé',
    enum: ContentType,
    example: ContentType.TEXT,
  })
  @IsEnum(ContentType)
  contentType: ContentType;

  @ApiProperty({
    description: 'ID du contenu signalé',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  contentId: string;

  @ApiProperty({
    description: 'ID de l\'utilisateur qui signale',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @IsString()
  @IsNotEmpty()
  reporterId: string;

  @ApiProperty({
    description: 'Raison du signalement',
    example: 'Contenu inapproprié',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({
    description: 'Description détaillée du signalement',
    example: 'Ce contenu contient des insultes et des propos haineux',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
