import { IsEnum, IsOptional, IsString, IsInt, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ReportStatus, ContentType } from '@prisma/client';
import { Type } from 'class-transformer';

export class FilterReportsDto {
  @ApiProperty({
    description: 'Statut du signalement',
    enum: ReportStatus,
    required: false,
  })
  @IsEnum(ReportStatus)
  @IsOptional()
  status?: ReportStatus;

  @ApiProperty({
    description: 'Type de contenu signalé',
    enum: ContentType,
    required: false,
  })
  @IsEnum(ContentType)
  @IsOptional()
  contentType?: ContentType;

  @ApiProperty({
    description: 'ID de l\'utilisateur qui a signalé',
    required: false,
  })
  @IsString()
  @IsOptional()
  reporterId?: string;

  @ApiProperty({
    description: 'Nombre d\'éléments à sauter (pagination)',
    required: false,
    default: 0,
    minimum: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  @Type(() => Number)
  skip?: number = 0;

  @ApiProperty({
    description: 'Nombre d\'éléments à récupérer (pagination)',
    required: false,
    default: 10,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  @IsOptional()
  @Type(() => Number)
  take?: number = 10;
}
