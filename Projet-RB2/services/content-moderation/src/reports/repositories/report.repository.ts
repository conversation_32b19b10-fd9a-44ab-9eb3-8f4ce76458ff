import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Report, ReportStatus, ContentType, ModerationAction, ActionType } from '@prisma/client';
import { CreateReportDto } from '../dto/create-report.dto';
import { UpdateReportDto } from '../dto/update-report.dto';

@Injectable()
export class ReportRepository {
  private readonly logger = new Logger(ReportRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async findAll(
    filters?: {
      status?: ReportStatus;
      contentType?: ContentType;
      reporterId?: string;
    },
    pagination?: {
      skip?: number;
      take?: number;
    },
  ): Promise<{ reports: Report[]; total: number }> {
    const where = {};
    
    if (filters?.status) {
      where['status'] = filters.status;
    }
    
    if (filters?.contentType) {
      where['contentType'] = filters.contentType;
    }
    
    if (filters?.reporterId) {
      where['reporterId'] = filters.reporterId;
    }
    
    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where,
        skip: pagination?.skip || 0,
        take: pagination?.take || 10,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          moderationActions: true,
        },
      }),
      this.prisma.report.count({ where }),
    ]);
    
    return { reports, total };
  }

  async findById(id: string): Promise<Report | null> {
    return this.prisma.report.findUnique({
      where: { id },
      include: {
        moderationActions: true,
      },
    });
  }

  async create(data: CreateReportDto): Promise<Report> {
    return this.prisma.report.create({
      data: {
        contentType: data.contentType,
        contentId: data.contentId,
        reporterId: data.reporterId,
        reason: data.reason,
        description: data.description,
      },
    });
  }

  async update(id: string, data: UpdateReportDto): Promise<Report> {
    return this.prisma.report.update({
      where: { id },
      data,
    });
  }

  async updateStatus(id: string, status: ReportStatus): Promise<Report> {
    return this.prisma.report.update({
      where: { id },
      data: { status },
    });
  }

  async addModerationAction(
    reportId: string,
    moderatorId: string,
    action: ActionType,
    comment?: string,
  ): Promise<ModerationAction> {
    return this.prisma.moderationAction.create({
      data: {
        reportId,
        moderatorId,
        action,
        comment,
      },
    });
  }

  async getModerationActions(reportId: string): Promise<ModerationAction[]> {
    return this.prisma.moderationAction.findMany({
      where: { reportId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getReportsByContentId(contentType: ContentType, contentId: string): Promise<Report[]> {
    return this.prisma.report.findMany({
      where: { contentType, contentId },
      include: {
        moderationActions: true,
      },
    });
  }

  async getReportStats(): Promise<{
    total: number;
    pending: number;
    inReview: number;
    approved: number;
    rejected: number;
    escalated: number;
  }> {
    const total = await this.prisma.report.count();
    const pending = await this.prisma.report.count({ where: { status: ReportStatus.PENDING } });
    const inReview = await this.prisma.report.count({ where: { status: ReportStatus.IN_REVIEW } });
    const approved = await this.prisma.report.count({ where: { status: ReportStatus.APPROVED } });
    const rejected = await this.prisma.report.count({ where: { status: ReportStatus.REJECTED } });
    const escalated = await this.prisma.report.count({ where: { status: ReportStatus.ESCALATED } });
    
    return {
      total,
      pending,
      inReview,
      approved,
      rejected,
      escalated,
    };
  }
}
