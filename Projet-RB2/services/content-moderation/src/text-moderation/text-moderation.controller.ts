import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Put,
  Delete,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TextModerationService } from './text-moderation.service';
import { ModerateTextDto } from './dto/moderate-text.dto';
import { CreateRuleDto } from './dto/create-rule.dto';
import { UpdateRuleDto } from './dto/update-rule.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('text-moderation')
@Controller('moderation/text')
export class TextModerationController {
  private readonly logger = new Logger(TextModerationController.name);

  constructor(private readonly textModerationService: TextModerationService) {}

  @Post()
  @ApiOperation({ summary: 'Modérer un texte' })
  @ApiResponse({
    status: 200,
    description: 'Résultat de la modération du texte',
  })
  async moderateText(@Body() moderateTextDto: ModerateTextDto) {
    this.logger.debug(`Received request to moderate text: ${moderateTextDto.text.substring(0, 50)}...`);
    return this.textModerationService.moderateText(moderateTextDto.text);
  }

  @Get('rules')
  @ApiOperation({ summary: 'Obtenir toutes les règles de modération de texte' })
  @ApiResponse({
    status: 200,
    description: 'Liste des règles de modération de texte',
  })
  async getRules() {
    return this.textModerationService.getRules();
  }

  @Post('rules')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Créer une nouvelle règle de modération de texte' })
  @ApiResponse({
    status: 201,
    description: 'La règle a été créée avec succès',
  })
  async createRule(@Body() createRuleDto: CreateRuleDto) {
    return this.textModerationService.createRule(createRuleDto);
  }

  @Put('rules/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'moderator')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mettre à jour une règle de modération de texte' })
  @ApiResponse({
    status: 200,
    description: 'La règle a été mise à jour avec succès',
  })
  async updateRule(
    @Param('id') id: string,
    @Body() updateRuleDto: UpdateRuleDto,
  ) {
    return this.textModerationService.updateRule(id, updateRuleDto);
  }

  @Delete('rules/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Supprimer une règle de modération de texte' })
  @ApiResponse({
    status: 200,
    description: 'La règle a été supprimée avec succès',
  })
  async deleteRule(@Param('id') id: string) {
    return this.textModerationService.deleteRule(id);
  }
}
