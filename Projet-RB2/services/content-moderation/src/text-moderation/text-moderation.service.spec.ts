import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { TextModerationService } from './text-moderation.service';
import { TextModerationRuleRepository } from './repositories/text-moderation-rule.repository';
import { Severity } from '@prisma/client';

describe('TextModerationService', () => {
  let service: TextModerationService;
  let repository: TextModerationRuleRepository;

  const mockTextModerationRuleRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findBySeverity: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockImplementation((key, defaultValue) => {
      if (key === 'TEXT_MODEL_PATH') {
        return './models/text-moderation';
      }
      return defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TextModerationService,
        {
          provide: TextModerationRuleRepository,
          useValue: mockTextModerationRuleRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<TextModerationService>(TextModerationService);
    repository = module.get<TextModerationRuleRepository>(TextModerationRuleRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('moderateText', () => {
    it('should return isInappropriate=false when no rules match', async () => {
      // Arrange
      const text = 'This is a clean text without any inappropriate content';
      const rules = [
        {
          id: '1',
          name: 'Insults',
          pattern: '\\b(insult|idiot|stupid)\\b',
          severity: Severity.MEDIUM,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      
      mockTextModerationRuleRepository.findAll.mockResolvedValue(rules);

      // Act
      const result = await service.moderateText(text);

      // Assert
      expect(result.isInappropriate).toBe(false);
      expect(result.severity).toBeNull();
      expect(result.matchedRules).toHaveLength(0);
      expect(mockTextModerationRuleRepository.findAll).toHaveBeenCalled();
    });

    it('should return isInappropriate=true when a rule matches', async () => {
      // Arrange
      const text = 'This text contains an insult which is inappropriate';
      const rules = [
        {
          id: '1',
          name: 'Insults',
          pattern: '\\b(insult|idiot|stupid)\\b',
          severity: Severity.MEDIUM,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      
      mockTextModerationRuleRepository.findAll.mockResolvedValue(rules);

      // Act
      const result = await service.moderateText(text);

      // Assert
      expect(result.isInappropriate).toBe(true);
      expect(result.severity).toBe(Severity.MEDIUM);
      expect(result.matchedRules).toHaveLength(1);
      expect(result.matchedRules[0].id).toBe('1');
      expect(mockTextModerationRuleRepository.findAll).toHaveBeenCalled();
    });

    it('should return the highest severity when multiple rules match', async () => {
      // Arrange
      const text = 'This text contains insults and hate which are inappropriate';
      const rules = [
        {
          id: '1',
          name: 'Insults',
          pattern: '\\b(insult|idiot|stupid)\\b',
          severity: Severity.MEDIUM,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Hate',
          pattern: '\\b(hate|racism|discrimination)\\b',
          severity: Severity.HIGH,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      
      mockTextModerationRuleRepository.findAll.mockResolvedValue(rules);

      // Act
      const result = await service.moderateText(text);

      // Assert
      expect(result.isInappropriate).toBe(true);
      expect(result.severity).toBe(Severity.HIGH);
      expect(result.matchedRules).toHaveLength(2);
      expect(mockTextModerationRuleRepository.findAll).toHaveBeenCalled();
    });
  });

  describe('getRules', () => {
    it('should return all rules', async () => {
      // Arrange
      const rules = [
        {
          id: '1',
          name: 'Insults',
          pattern: '\\b(insult|idiot|stupid)\\b',
          severity: Severity.MEDIUM,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      
      mockTextModerationRuleRepository.findAll.mockResolvedValue(rules);

      // Act
      const result = await service.getRules();

      // Assert
      expect(result).toEqual(rules);
      expect(mockTextModerationRuleRepository.findAll).toHaveBeenCalled();
    });
  });

  // Autres tests pour createRule, updateRule, deleteRule...
});
