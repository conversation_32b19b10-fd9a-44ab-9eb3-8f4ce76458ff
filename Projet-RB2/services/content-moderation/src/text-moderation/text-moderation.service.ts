import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TextModerationRuleRepository } from './repositories/text-moderation-rule.repository';
import { Severity } from '@prisma/client';
import * as tf from '@tensorflow/tfjs-node';

interface ModerationResult {
  isInappropriate: boolean;
  severity: Severity | null;
  matchedRules: {
    id: string;
    name: string;
    pattern: string;
    severity: Severity;
  }[];
  confidence: number;
}

@Injectable()
export class TextModerationService {
  private readonly logger = new Logger(TextModerationService.name);
  private model: tf.LayersModel | null = null;
  private readonly modelPath: string;

  constructor(
    private readonly textModerationRuleRepository: TextModerationRuleRepository,
    private readonly configService: ConfigService,
  ) {
    this.modelPath = this.configService.get<string>('TEXT_MODEL_PATH', './models/text-moderation');
    this.loadModel();
  }

  private async loadModel(): Promise<void> {
    try {
      this.logger.log(`Loading text moderation model from ${this.modelPath}...`);
      // Dans un environnement réel, nous chargerions un modèle TensorFlow.js
      // Pour l'instant, nous simulons le chargement du modèle
      // this.model = await tf.loadLayersModel(`file://${this.modelPath}/model.json`);
      this.logger.log('Text moderation model loaded successfully');
    } catch (error) {
      this.logger.error(`Failed to load text moderation model: ${error.message}`);
    }
  }

  async moderateText(text: string): Promise<ModerationResult> {
    this.logger.debug(`Moderating text: ${text.substring(0, 50)}...`);
    
    // 1. Vérification basée sur les règles
    const rules = await this.textModerationRuleRepository.findAll();
    const matchedRules = rules.filter(rule => {
      const regex = new RegExp(rule.pattern, 'i');
      return regex.test(text);
    });

    // 2. Déterminer si le texte est inapproprié basé sur les règles
    const isInappropriateByRules = matchedRules.length > 0;
    
    // 3. Déterminer la sévérité la plus élevée parmi les règles correspondantes
    let highestSeverity: Severity | null = null;
    if (matchedRules.length > 0) {
      const severityOrder = {
        LOW: 1,
        MEDIUM: 2,
        HIGH: 3,
        CRITICAL: 4,
      };
      
      highestSeverity = matchedRules.reduce((highest, rule) => {
        return severityOrder[rule.severity] > severityOrder[highest] ? rule.severity : highest;
      }, matchedRules[0].severity);
    }

    // 4. Utilisation du modèle d'IA (simulé pour l'instant)
    let aiConfidence = 0;
    if (this.model) {
      // Dans un environnement réel, nous utiliserions le modèle pour prédire
      // Pour l'instant, nous simulons une prédiction
      aiConfidence = this.simulateAIPrediction(text);
    }

    // 5. Combiner les résultats des règles et de l'IA
    const isInappropriate = isInappropriateByRules || aiConfidence > 0.7;
    
    return {
      isInappropriate,
      severity: highestSeverity,
      matchedRules: matchedRules.map(rule => ({
        id: rule.id,
        name: rule.name,
        pattern: rule.pattern,
        severity: rule.severity,
      })),
      confidence: Math.max(aiConfidence, isInappropriateByRules ? 0.9 : 0),
    };
  }

  private simulateAIPrediction(text: string): number {
    // Simulation d'une prédiction d'IA
    // Dans un environnement réel, nous utiliserions le modèle TensorFlow.js
    
    // Mots-clés inappropriés pour la simulation
    const inappropriateKeywords = [
      'insulte', 'haine', 'racisme', 'violence', 'obscénité',
      'insult', 'hate', 'racism', 'violence', 'obscenity',
    ];
    
    // Vérifier si le texte contient des mots-clés inappropriés
    const containsInappropriate = inappropriateKeywords.some(keyword => 
      text.toLowerCase().includes(keyword)
    );
    
    // Simuler une confiance basée sur la présence de mots-clés
    return containsInappropriate ? 0.85 : 0.1;
  }

  async getRules() {
    return this.textModerationRuleRepository.findAll();
  }

  async createRule(data: {
    name: string;
    description?: string;
    pattern: string;
    severity: Severity;
    isActive?: boolean;
  }) {
    return this.textModerationRuleRepository.create(data);
  }

  async updateRule(
    id: string,
    data: {
      name?: string;
      description?: string;
      pattern?: string;
      severity?: Severity;
      isActive?: boolean;
    },
  ) {
    return this.textModerationRuleRepository.update(id, data);
  }

  async deleteRule(id: string) {
    return this.textModerationRuleRepository.delete(id);
  }
}
