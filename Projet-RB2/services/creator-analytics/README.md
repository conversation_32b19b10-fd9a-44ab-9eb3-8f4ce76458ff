# Service d'Analyse Avancée pour Créateurs - Retreat And Be

Ce microservice est responsable de l'analyse avancée des données pour les créateurs sur la plateforme Retreat And Be. Il fournit des API pour l'accès aux métriques, la visualisation des données, les prévisions et l'analyse comparative.

## Fonctionnalités

- Collecte et stockage des données d'engagement
- Métriques de base (vues, likes, commentaires)
- Tableaux de bord analytiques personnalisables
- Visualisations interactives
- Prévisions d'engagement
- Analyse des tendances temporelles
- Analyse comparative avec benchmarks
- Recommandations personnalisées

## Architecture

Le service est construit avec NestJS et s'intègre avec les autres microservices de la plateforme via des API REST et des webhooks.

```
                  ┌─────────────────────┐
                  │                     │
                  │  Frontend           │
                  │                     │
                  └─────────┬───────────┘
                            │
                            ▼
┌─────────────────────────────────────────────┐
│                                             │
│  API Gateway                                │
│                                             │
└─────────────────┬───────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Service d'Analyse Avancée pour Créateurs   │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Collecte    │    │ Métriques           │ │
│  │ de Données  │    │ de Base             │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
│  ┌─────────────┐    ┌─────────────────────┐ │
│  │             │    │                     │ │
│  │ Prévisions  │    │ Analyse             │ │
│  │ et Tendances│    │ Comparative         │ │
│  │             │    │                     │ │
│  └─────────────┘    └─────────────────────┘ │
│                                             │
└─────────────────────┬───────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────┐
│                                             │
│  Base de Données                            │
│                                             │
└─────────────────────────────────────────────┘
```

## Technologies

- **Backend**: NestJS, TypeScript
- **Base de données**: PostgreSQL avec Prisma ORM, TimescaleDB pour les séries temporelles
- **Analyse de données**: Pandas, NumPy, scikit-learn (via API Python)
- **Visualisation**: D3.js, Chart.js
- **Cache**: Redis
- **Monitoring**: Prometheus, Grafana

## Installation

```bash
# Installer les dépendances
npm install

# Configurer les variables d'environnement
cp .env.example .env

# Lancer les migrations de base de données
npm run prisma:migrate

# Démarrer le service en mode développement
npm run start:dev
```

## Structure du Projet

```
creator-analytics/
├── src/
│   ├── main.ts                    # Point d'entrée de l'application
│   ├── app.module.ts              # Module principal
│   ├── config/                    # Configuration
│   ├── common/                    # Code partagé
│   ├── prisma/                    # Client Prisma et schémas
│   ├── data-collection/           # Collecte de données
│   ├── metrics/                   # Métriques de base
│   ├── dashboards/                # Tableaux de bord
│   ├── forecasting/               # Prévisions et tendances
│   ├── benchmarks/                # Analyse comparative
│   └── recommendations/           # Recommandations personnalisées
├── test/                          # Tests
├── prisma/                        # Schémas Prisma
├── Dockerfile                     # Configuration Docker
└── docker-compose.yml             # Configuration Docker Compose
```

## API

### Métriques de Base

- `GET /api/metrics/:creatorId` - Obtenir les métriques de base pour un créateur
- `GET /api/metrics/:creatorId/content/:contentId` - Obtenir les métriques pour un contenu spécifique
- `GET /api/metrics/:creatorId/summary` - Obtenir un résumé des métriques pour un créateur

### Tableaux de Bord

- `GET /api/dashboards/:creatorId` - Obtenir les tableaux de bord d'un créateur
- `POST /api/dashboards/:creatorId` - Créer un nouveau tableau de bord
- `PUT /api/dashboards/:dashboardId` - Mettre à jour un tableau de bord
- `DELETE /api/dashboards/:dashboardId` - Supprimer un tableau de bord

### Prévisions et Tendances

- `GET /api/forecasting/:creatorId/engagement` - Obtenir les prévisions d'engagement
- `GET /api/forecasting/:creatorId/trends` - Obtenir les tendances d'engagement
- `GET /api/forecasting/:creatorId/content/:contentId/performance` - Obtenir les prévisions de performance pour un contenu

### Analyse Comparative

- `GET /api/benchmarks/:creatorId/category` - Obtenir les benchmarks par catégorie
- `GET /api/benchmarks/:creatorId/performance` - Obtenir la performance par rapport aux benchmarks

### Recommandations

- `GET /api/recommendations/:creatorId/content` - Obtenir des recommandations pour le contenu
- `GET /api/recommendations/:creatorId/timing` - Obtenir des recommandations pour le timing de publication
- `GET /api/recommendations/:creatorId/engagement` - Obtenir des recommandations pour améliorer l'engagement

## Intégration avec les Autres Services

- **Service de Contenu** : Récupération des données de contenu
- **Service Utilisateur** : Récupération des informations utilisateur
- **Service d'Événements** : Collecte des événements d'engagement
- **Service de Recommandation** : Partage des insights pour les recommandations

## Développement

### Exécution des Tests

```bash
# Tests unitaires
npm run test

# Tests e2e
npm run test:e2e

# Couverture de test
npm run test:cov
```

### Linting et Formatting

```bash
# Linting
npm run lint

# Formatting
npm run format
```

## Déploiement

Le service est déployé en tant que conteneur Docker dans un cluster Kubernetes.

```bash
# Construire l'image Docker
docker build -t retreat-and-be/creator-analytics .

# Déployer sur Kubernetes
kubectl apply -f k8s/
```
