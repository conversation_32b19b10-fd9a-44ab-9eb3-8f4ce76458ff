#!/usr/bin/env node

/**
 * Script to generate an analytics report for creators
 *
 * Usage:
 *   node generate-analytics-report.js --creatorId <creatorId> [--startDate YYYY-MM-DD] [--endDate YYYY-MM-DD] [--format json|csv|html|pdf] [--output filename] [--compare] [--interactive]
 *
 * Options:
 *   --creatorId   ID of the creator (required)
 *   --startDate   Start date for the report (default: 30 days ago)
 *   --endDate     End date for the report (default: today)
 *   --format      Output format: json, csv, html, or pdf (default: html)
 *   --output      Output file (default: creator-analytics-report-{creatorId}.{format})
 *   --metrics     Comma-separated list of metrics to include (default: all)
 *   --compare     Compare with previous period
 *   --interactive Generate an interactive HTML report with charts
 *   --theme       Report theme: light, dark, or branded (default: light)
 *   --benchmark   Include industry benchmarks for comparison
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { ChartJSNodeCanvas } = require('chartjs-node-canvas');
const PDFDocument = require('pdfkit');
const { format, subDays, differenceInDays, parseISO } = require('date-fns');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  creatorId: null,
  startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
  endDate: new Date(),
  format: 'html',
  output: null,
  metrics: null,
  compare: false,
  interactive: false,
  theme: 'light',
  benchmark: false,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--creatorId' && i + 1 < args.length) {
    options.creatorId = args[i + 1];
    i++;
  } else if (args[i] === '--startDate' && i + 1 < args.length) {
    options.startDate = new Date(args[i + 1]);
    i++;
  } else if (args[i] === '--endDate' && i + 1 < args.length) {
    options.endDate = new Date(args[i + 1]);
    i++;
  } else if (args[i] === '--format' && i + 1 < args.length) {
    options.format = args[i + 1];
    i++;
  } else if (args[i] === '--output' && i + 1 < args.length) {
    options.output = args[i + 1];
    i++;
  } else if (args[i] === '--metrics' && i + 1 < args.length) {
    options.metrics = args[i + 1].split(',');
    i++;
  } else if (args[i] === '--compare') {
    options.compare = true;
  } else if (args[i] === '--interactive') {
    options.interactive = true;
  } else if (args[i] === '--theme' && i + 1 < args.length) {
    options.theme = args[i + 1];
    i++;
  } else if (args[i] === '--benchmark') {
    options.benchmark = true;
  }
}

// Validate required options
if (!options.creatorId) {
  console.error('Error: --creatorId is required');
  process.exit(1);
}

// Set default output filename if not provided
if (!options.output) {
  const dateStr = format(new Date(), 'yyyyMMdd');
  options.output = `creator-analytics-report-${options.creatorId}-${dateStr}.${options.format}`;
}

// Force interactive mode for HTML format
if (options.format === 'html') {
  options.interactive = true;
}

// API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// Configuration pour la génération de graphiques
const chartJSNodeCanvas = new ChartJSNodeCanvas({
  width: 800,
  height: 400,
  backgroundColour: options.theme === 'dark' ? '#2c3e50' : 'white'
});

// Thèmes de couleurs pour les graphiques
const chartColors = {
  light: {
    primary: '#3498db',
    secondary: '#2ecc71',
    tertiary: '#e74c3c',
    quaternary: '#f39c12',
    background: 'white',
    text: '#333333',
    grid: '#dddddd'
  },
  dark: {
    primary: '#3498db',
    secondary: '#2ecc71',
    tertiary: '#e74c3c',
    quaternary: '#f39c12',
    background: '#2c3e50',
    text: '#ecf0f1',
    grid: '#34495e'
  },
  branded: {
    primary: '#8e44ad',
    secondary: '#27ae60',
    tertiary: '#d35400',
    quaternary: '#2c3e50',
    background: '#ecf0f1',
    text: '#2c3e50',
    grid: '#bdc3c7'
  }
};

async function generateReport() {
  console.log('Generating creator analytics report...');
  console.log(`Creator ID: ${options.creatorId}`);
  console.log(`Start date: ${options.startDate.toISOString().split('T')[0]}`);
  console.log(`End date: ${options.endDate.toISOString().split('T')[0]}`);
  console.log(`Format: ${options.format}${options.interactive ? ' (interactive)' : ''}`);

  if (options.compare) {
    console.log('Comparing with previous period');
  }

  if (options.benchmark) {
    console.log('Including industry benchmarks');
  }

  try {
    // Fetch data from the API
    const data = await fetchAnalyticsData();

    // Fetch comparison data if requested
    if (options.compare) {
      const periodLength = differenceInDays(options.endDate, options.startDate);
      const previousStartDate = subDays(options.startDate, periodLength);
      const previousEndDate = subDays(options.startDate, 1);

      console.log(`Comparison period: ${format(previousStartDate, 'yyyy-MM-dd')} to ${format(previousEndDate, 'yyyy-MM-dd')}`);

      data.comparisonData = await fetchAnalyticsData({
        startDate: previousStartDate,
        endDate: previousEndDate
      });
    }

    // Fetch benchmark data if requested
    if (options.benchmark) {
      data.benchmarkData = await fetchBenchmarkData();
    }

    // Generate report in the requested format
    let reportContent;
    switch (options.format) {
      case 'json':
        reportContent = JSON.stringify(data, null, 2);
        break;
      case 'csv':
        reportContent = generateCsvReport(data);
        break;
      case 'html':
        reportContent = await generateHtmlReport(data);
        break;
      case 'pdf':
        await generatePdfReport(data);
        reportContent = null; // PDF is written directly to file
        break;
      default:
        console.error(`Unsupported format: ${options.format}`);
        process.exit(1);
    }

    // Write report to file if not PDF (PDF is written directly)
    if (reportContent) {
      fs.writeFileSync(options.output, reportContent);
      console.log(`Report written to ${options.output}`);
    }

  } catch (error) {
    console.error('Error generating report:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function fetchAnalyticsData(dateOverride = null) {
  try {
    // Prepare query parameters
    const params = {
      startDate: dateOverride?.startDate?.toISOString() || options.startDate.toISOString(),
      endDate: dateOverride?.endDate?.toISOString() || options.endDate.toISOString(),
      detailed: 'true', // Demander des données plus détaillées
    };

    if (options.metrics) {
      params.metrics = options.metrics.join(',');
    }

    // Fetch data from the API
    const responses = await Promise.all([
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/overview/${options.creatorId}`, { params }),
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/audience/${options.creatorId}`, { params }),
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/content/${options.creatorId}`, { params }),
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/engagement/${options.creatorId}`, { params }),
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/revenue/${options.creatorId}`, { params }),
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/performance/${options.creatorId}`, { params }),
      axios.get(`${API_BASE_URL}/creator-analytics/dashboard/trends/${options.creatorId}`, { params }),
    ]);

    // Combine the data
    return {
      overview: responses[0].data,
      audience: responses[1].data,
      content: responses[2].data,
      engagement: responses[3].data,
      revenue: responses[4].data,
      performance: responses[5].data,
      trends: responses[6].data,
      reportInfo: {
        creatorId: options.creatorId,
        startDate: params.startDate.split('T')[0],
        endDate: params.endDate.split('T')[0],
        generatedAt: new Date().toISOString(),
        format: options.format,
        interactive: options.interactive,
        theme: options.theme,
        compare: options.compare,
        benchmark: options.benchmark,
      },
    };
  } catch (error) {
    console.error('Error fetching analytics data:', error.message);

    // If API is not available, generate mock data for testing
    console.log('Generating mock data for testing...');
    return generateMockData(dateOverride);
  }
}

/**
 * Récupère les données de benchmark de l'industrie
 */
async function fetchBenchmarkData() {
  try {
    const response = await axios.get(`${API_BASE_URL}/creator-analytics/benchmarks`, {
      params: {
        creatorId: options.creatorId,
        startDate: options.startDate.toISOString(),
        endDate: options.endDate.toISOString(),
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching benchmark data:', error.message);
    return generateMockBenchmarkData();
  }
}

function generateMockData() {
  return {
    overview: {
      summary: {
        totalViews: 12500,
        totalEngagements: 3200,
        totalFollowers: 1500,
        followerGrowth: 120,
        totalRevenue: 2500,
        engagementRate: 25.6,
      },
      trendingContent: [
        { id: 'content1', title: 'How to meditate', views: 1200, engagements: 350 },
        { id: 'content2', title: 'Yoga for beginners', views: 980, engagements: 280 },
        { id: 'content3', title: 'Healthy eating habits', views: 850, engagements: 210 },
      ],
      overTime: {
        views: generateTimeSeriesData('views', 30),
        engagements: generateTimeSeriesData('engagements', 30),
        followers: generateTimeSeriesData('followers', 30),
        revenue: generateTimeSeriesData('revenue', 30),
      },
    },
    audience: {
      demographics: {
        age: [
          { range: '18-24', percentage: 15 },
          { range: '25-34', percentage: 35 },
          { range: '35-44', percentage: 25 },
          { range: '45-54', percentage: 15 },
          { range: '55+', percentage: 10 },
        ],
        gender: [
          { gender: 'Male', percentage: 40 },
          { gender: 'Female', percentage: 58 },
          { gender: 'Other', percentage: 2 },
        ],
      },
      locations: [
        { country: 'United States', count: 800 },
        { country: 'Canada', count: 250 },
        { country: 'United Kingdom', count: 180 },
        { country: 'Australia', count: 120 },
        { country: 'Germany', count: 90 },
      ],
      interests: [
        { interest: 'Wellness', count: 650 },
        { interest: 'Fitness', count: 580 },
        { interest: 'Nutrition', count: 420 },
        { interest: 'Meditation', count: 380 },
        { interest: 'Yoga', count: 350 },
      ],
      activity: {
        byHour: generateHourlyData(),
        byDay: generateDailyData(),
      },
      growth: generateTimeSeriesData('followers', 30),
      retention: {
        rate: 75,
        byWeek: [
          { week: 1, rate: 90 },
          { week: 2, rate: 85 },
          { week: 3, rate: 80 },
          { week: 4, rate: 75 },
        ],
      },
    },
    content: {
      contentPerformance: [
        { id: 'content1', title: 'How to meditate', views: 1200, engagements: 350, engagementRate: 29.2 },
        { id: 'content2', title: 'Yoga for beginners', views: 980, engagements: 280, engagementRate: 28.6 },
        { id: 'content3', title: 'Healthy eating habits', views: 850, engagements: 210, engagementRate: 24.7 },
        { id: 'content4', title: 'Stress reduction techniques', views: 780, engagements: 190, engagementRate: 24.4 },
        { id: 'content5', title: 'Morning routine for success', views: 720, engagements: 185, engagementRate: 25.7 },
      ],
      contentTypePerformance: [
        { type: 'Video', views: 5200, engagements: 1500, engagementRate: 28.8 },
        { type: 'Article', views: 3800, engagements: 950, engagementRate: 25.0 },
        { type: 'Audio', views: 2100, engagements: 480, engagementRate: 22.9 },
        { type: 'Image', views: 1400, engagements: 270, engagementRate: 19.3 },
      ],
      topContent: [
        { id: 'content1', title: 'How to meditate', views: 1200, engagements: 350, engagementRate: 29.2 },
        { id: 'content2', title: 'Yoga for beginners', views: 980, engagements: 280, engagementRate: 28.6 },
        { id: 'content3', title: 'Healthy eating habits', views: 850, engagements: 210, engagementRate: 24.7 },
      ],
      publishingTimes: generateHourlyData(),
      engagementByTime: generateHourlyData(),
    },
    engagement: {
      engagementMetrics: {
        likes: 1800,
        comments: 950,
        shares: 450,
        saves: 380,
        totalEngagements: 3580,
        engagementRate: 25.6,
      },
      engagementByContentType: [
        { type: 'Video', likes: 950, comments: 480, shares: 250, saves: 200 },
        { type: 'Article', likes: 520, comments: 280, shares: 120, saves: 100 },
        { type: 'Audio', likes: 210, comments: 120, shares: 50, saves: 50 },
        { type: 'Image', likes: 120, comments: 70, shares: 30, saves: 30 },
      ],
      engagementRateOverTime: generateTimeSeriesData('engagementRate', 30),
      topEngagingUsers: [
        { userId: 'user1', name: 'John Doe', engagements: 45 },
        { userId: 'user2', name: 'Jane Smith', engagements: 38 },
        { userId: 'user3', name: 'Bob Johnson', engagements: 32 },
        { userId: 'user4', name: 'Alice Brown', engagements: 29 },
        { userId: 'user5', name: 'Charlie Wilson', engagements: 25 },
      ],
      commentSentiment: {
        positive: 65,
        neutral: 25,
        negative: 10,
      },
    },
    revenue: {
      revenueMetrics: {
        totalRevenue: 2500,
        subscriptions: 1800,
        oneTimePayments: 450,
        donations: 250,
        averageRevenuePerUser: 12.5,
      },
      revenueBySource: [
        { source: 'Subscriptions', amount: 1800 },
        { source: 'One-time payments', amount: 450 },
        { source: 'Donations', amount: 250 },
      ],
      revenueOverTime: generateTimeSeriesData('revenue', 30),
      topRevenueContent: [
        { id: 'content1', title: 'How to meditate', revenue: 350 },
        { id: 'content2', title: 'Yoga for beginners', revenue: 280 },
        { id: 'content3', title: 'Healthy eating habits', revenue: 210 },
      ],
      subscriberMetrics: {
        totalSubscribers: 200,
        newSubscribers: 25,
        churnRate: 5,
        retentionRate: 95,
      },
    },
    reportInfo: {
      creatorId: options.creatorId,
      startDate: options.startDate.toISOString().split('T')[0],
      endDate: options.endDate.toISOString().split('T')[0],
      generatedAt: new Date().toISOString(),
    },
  };
}

function generateTimeSeriesData(metric, days) {
  const data = [];
  const startDate = new Date(options.startDate);

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);

    let value;
    switch (metric) {
      case 'views':
        value = Math.floor(Math.random() * 500) + 300;
        break;
      case 'engagements':
        value = Math.floor(Math.random() * 150) + 80;
        break;
      case 'followers':
        // Cumulative value that increases over time
        const baseFollowers = 1380;
        const dailyGrowth = Math.floor(Math.random() * 10) + 1;
        value = baseFollowers + (dailyGrowth * i);
        break;
      case 'revenue':
        value = Math.floor(Math.random() * 100) + 50;
        break;
      case 'engagementRate':
        value = (Math.random() * 5) + 20; // 20-25%
        break;
      default:
        value = Math.floor(Math.random() * 100);
    }

    data.push({
      date: date.toISOString().split('T')[0],
      value,
    });
  }

  return data;
}

function generateHourlyData() {
  const data = [];

  for (let hour = 0; hour < 24; hour++) {
    data.push({
      hour,
      value: Math.floor(Math.random() * 100) + 10,
    });
  }

  return data;
}

function generateDailyData() {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const data = [];

  for (const day of days) {
    data.push({
      day,
      value: Math.floor(Math.random() * 200) + 100,
    });
  }

  return data;
}

function generateCsvReport(data) {
  // This is a simplified CSV generation
  // In a real implementation, you would use a library like json2csv

  let csv = '';

  // Add header
  csv += 'Creator Analytics Report\n';
  csv += `Creator ID: ${data.reportInfo.creatorId}\n`;
  csv += `Period: ${data.reportInfo.startDate} to ${data.reportInfo.endDate}\n`;
  csv += `Generated: ${data.reportInfo.generatedAt}\n\n`;

  // Overview summary
  csv += 'Overview Summary\n';
  csv += 'Metric,Value\n';
  for (const [key, value] of Object.entries(data.overview.summary)) {
    csv += `${key},${value}\n`;
  }
  csv += '\n';

  // Trending content
  csv += 'Trending Content\n';
  csv += 'ID,Title,Views,Engagements\n';
  for (const content of data.overview.trendingContent) {
    csv += `${content.id},${content.title},${content.views},${content.engagements}\n`;
  }
  csv += '\n';

  // Views over time
  csv += 'Views Over Time\n';
  csv += 'Date,Views\n';
  for (const item of data.overview.overTime.views) {
    csv += `${item.date},${item.value}\n`;
  }

  return csv;
}

function generateHtmlReport(data) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Creator Analytics Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
    h1, h2, h3 { color: #2c3e50; }
    .report-header { margin-bottom: 30px; }
    .report-section { margin-bottom: 40px; }
    .metrics-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
    .metric-card { background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .metric-value { font-size: 24px; font-weight: bold; margin: 10px 0; color: #3498db; }
    .metric-label { font-size: 14px; color: #7f8c8d; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    tr:hover { background-color: #f5f5f5; }
    .chart-container { height: 300px; margin-bottom: 30px; background-color: #f8f9fa; border-radius: 8px; padding: 15px; }
    .chart-placeholder { display: flex; align-items: center; justify-content: center; height: 100%; color: #7f8c8d; }
    footer { margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #7f8c8d; }
  </style>
</head>
<body>
  <div class="report-header">
    <h1>Creator Analytics Report</h1>
    <p><strong>Creator ID:</strong> ${data.reportInfo.creatorId}</p>
    <p><strong>Period:</strong> ${data.reportInfo.startDate} to ${data.reportInfo.endDate}</p>
    <p><strong>Generated:</strong> ${new Date(data.reportInfo.generatedAt).toLocaleString()}</p>
  </div>

  <div class="report-section">
    <h2>Overview</h2>
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-label">Total Views</div>
        <div class="metric-value">${data.overview.summary.totalViews.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Total Engagements</div>
        <div class="metric-value">${data.overview.summary.totalEngagements.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Total Followers</div>
        <div class="metric-value">${data.overview.summary.totalFollowers.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Follower Growth</div>
        <div class="metric-value">+${data.overview.summary.followerGrowth.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Total Revenue</div>
        <div class="metric-value">$${data.overview.summary.totalRevenue.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Engagement Rate</div>
        <div class="metric-value">${data.overview.summary.engagementRate.toFixed(1)}%</div>
      </div>
    </div>

    <h3>Trending Content</h3>
    <table>
      <thead>
        <tr>
          <th>Title</th>
          <th>Views</th>
          <th>Engagements</th>
        </tr>
      </thead>
      <tbody>
        ${data.overview.trendingContent.map(content => `
          <tr>
            <td>${content.title}</td>
            <td>${content.views.toLocaleString()}</td>
            <td>${content.engagements.toLocaleString()}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>

    <h3>Metrics Over Time</h3>
    <div class="chart-container">
      <div class="chart-placeholder">
        [Views Over Time Chart - In a real report, this would be an actual chart]
      </div>
    </div>
  </div>

  <div class="report-section">
    <h2>Audience</h2>
    <h3>Demographics</h3>
    <div class="chart-container">
      <div class="chart-placeholder">
        [Demographics Chart - In a real report, this would be an actual chart]
      </div>
    </div>

    <h3>Top Locations</h3>
    <table>
      <thead>
        <tr>
          <th>Country</th>
          <th>Followers</th>
        </tr>
      </thead>
      <tbody>
        ${data.audience.locations.map(location => `
          <tr>
            <td>${location.country}</td>
            <td>${location.count.toLocaleString()}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  </div>

  <div class="report-section">
    <h2>Content Performance</h2>
    <table>
      <thead>
        <tr>
          <th>Title</th>
          <th>Views</th>
          <th>Engagements</th>
          <th>Engagement Rate</th>
        </tr>
      </thead>
      <tbody>
        ${data.content.contentPerformance.map(content => `
          <tr>
            <td>${content.title}</td>
            <td>${content.views.toLocaleString()}</td>
            <td>${content.engagements.toLocaleString()}</td>
            <td>${content.engagementRate.toFixed(1)}%</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  </div>

  <div class="report-section">
    <h2>Engagement</h2>
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-label">Likes</div>
        <div class="metric-value">${data.engagement.engagementMetrics.likes.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Comments</div>
        <div class="metric-value">${data.engagement.engagementMetrics.comments.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Shares</div>
        <div class="metric-value">${data.engagement.engagementMetrics.shares.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Saves</div>
        <div class="metric-value">${data.engagement.engagementMetrics.saves.toLocaleString()}</div>
      </div>
    </div>

    <h3>Comment Sentiment</h3>
    <div class="chart-container">
      <div class="chart-placeholder">
        [Comment Sentiment Chart - In a real report, this would be an actual chart]
      </div>
    </div>
  </div>

  <div class="report-section">
    <h2>Revenue</h2>
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-label">Total Revenue</div>
        <div class="metric-value">$${data.revenue.revenueMetrics.totalRevenue.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Subscriptions</div>
        <div class="metric-value">$${data.revenue.revenueMetrics.subscriptions.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">One-time Payments</div>
        <div class="metric-value">$${data.revenue.revenueMetrics.oneTimePayments.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Donations</div>
        <div class="metric-value">$${data.revenue.revenueMetrics.donations.toLocaleString()}</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Avg. Revenue Per User</div>
        <div class="metric-value">$${data.revenue.revenueMetrics.averageRevenuePerUser.toFixed(2)}</div>
      </div>
    </div>

    <h3>Revenue Over Time</h3>
    <div class="chart-container">
      <div class="chart-placeholder">
        [Revenue Over Time Chart - In a real report, this would be an actual chart]
      </div>
    </div>
  </div>

  <footer>
    <p>This report was generated automatically by the Retreat And Be Creator Analytics System.</p>
    <p>© ${new Date().getFullYear()} Retreat And Be. All rights reserved.</p>
  </footer>
</body>
</html>
  `;
}

// Run the report generation
generateReport();
