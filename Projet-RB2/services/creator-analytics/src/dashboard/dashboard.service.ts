import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../../Backend-NestJS/src/prisma/prisma.service';
import { MetricsService } from '../metrics/metrics.service';
import { DataCollectionService } from '../data-collection/data-collection.service';
import { FilterAnalyticsDto } from '../dto/filter-analytics.dto';
import { DashboardConfigDto } from '../dto/dashboard-config.dto';
import * as fs from 'fs';
import * as path from 'path';
import * as json2csv from 'json2csv';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);
  private readonly dashboardConfigs: Map<string, DashboardConfigDto> = new Map();

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly dataCollectionService: DataCollectionService,
  ) {}

  async getOverview(creatorId: string, startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting analytics overview for creator: ${creatorId}`);
    
    // Get basic metrics
    const totalViews = await this.metricsService.getTotalViews(creatorId, startDate, endDate);
    const totalEngagements = await this.metricsService.getTotalEngagements(creatorId, startDate, endDate);
    const totalFollowers = await this.metricsService.getTotalFollowers(creatorId, endDate);
    const followerGrowth = await this.metricsService.getFollowerGrowth(creatorId, startDate, endDate);
    const totalRevenue = await this.metricsService.getTotalRevenue(creatorId, startDate, endDate);
    
    // Get trending content
    const trendingContent = await this.metricsService.getTrendingContent(creatorId, startDate, endDate);
    
    // Get engagement rate
    const engagementRate = totalViews > 0 ? (totalEngagements / totalViews) * 100 : 0;
    
    // Get metrics over time
    const viewsOverTime = await this.metricsService.getMetricOverTime(
      creatorId,
      'views',
      startDate,
      endDate,
    );
    
    const engagementsOverTime = await this.metricsService.getMetricOverTime(
      creatorId,
      'engagements',
      startDate,
      endDate,
    );
    
    const followersOverTime = await this.metricsService.getMetricOverTime(
      creatorId,
      'followers',
      startDate,
      endDate,
    );
    
    const revenueOverTime = await this.metricsService.getMetricOverTime(
      creatorId,
      'revenue',
      startDate,
      endDate,
    );
    
    return {
      summary: {
        totalViews,
        totalEngagements,
        totalFollowers,
        followerGrowth,
        totalRevenue,
        engagementRate,
      },
      trendingContent,
      overTime: {
        views: viewsOverTime,
        engagements: engagementsOverTime,
        followers: followersOverTime,
        revenue: revenueOverTime,
      },
    };
  }

  async getAudienceAnalytics(creatorId: string, startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting audience analytics for creator: ${creatorId}`);
    
    // Get audience demographics
    const demographics = await this.metricsService.getAudienceDemographics(creatorId);
    
    // Get audience locations
    const locations = await this.metricsService.getAudienceLocations(creatorId);
    
    // Get audience interests
    const interests = await this.metricsService.getAudienceInterests(creatorId);
    
    // Get audience activity
    const activity = await this.metricsService.getAudienceActivity(creatorId, startDate, endDate);
    
    // Get audience growth
    const growth = await this.metricsService.getAudienceGrowth(creatorId, startDate, endDate);
    
    // Get audience retention
    const retention = await this.metricsService.getAudienceRetention(creatorId, startDate, endDate);
    
    return {
      demographics,
      locations,
      interests,
      activity,
      growth,
      retention,
    };
  }

  async getContentAnalytics(creatorId: string, startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting content analytics for creator: ${creatorId}`);
    
    // Get content performance
    const contentPerformance = await this.metricsService.getContentPerformance(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get content types performance
    const contentTypePerformance = await this.metricsService.getContentTypePerformance(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get top performing content
    const topContent = await this.metricsService.getTopContent(creatorId, startDate, endDate);
    
    // Get content publishing times
    const publishingTimes = await this.metricsService.getContentPublishingTimes(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get content engagement by time of day
    const engagementByTime = await this.metricsService.getEngagementByTimeOfDay(
      creatorId,
      startDate,
      endDate,
    );
    
    return {
      contentPerformance,
      contentTypePerformance,
      topContent,
      publishingTimes,
      engagementByTime,
    };
  }

  async getEngagementAnalytics(creatorId: string, startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting engagement analytics for creator: ${creatorId}`);
    
    // Get engagement metrics
    const engagementMetrics = await this.metricsService.getEngagementMetrics(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get engagement by content type
    const engagementByContentType = await this.metricsService.getEngagementByContentType(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get engagement rate over time
    const engagementRateOverTime = await this.metricsService.getEngagementRateOverTime(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get top engaging users
    const topEngagingUsers = await this.metricsService.getTopEngagingUsers(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get comment sentiment analysis
    const commentSentiment = await this.metricsService.getCommentSentiment(
      creatorId,
      startDate,
      endDate,
    );
    
    return {
      engagementMetrics,
      engagementByContentType,
      engagementRateOverTime,
      topEngagingUsers,
      commentSentiment,
    };
  }

  async getRevenueAnalytics(creatorId: string, startDate?: Date, endDate?: Date) {
    this.logger.debug(`Getting revenue analytics for creator: ${creatorId}`);
    
    // Get revenue metrics
    const revenueMetrics = await this.metricsService.getRevenueMetrics(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get revenue by source
    const revenueBySource = await this.metricsService.getRevenueBySource(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get revenue over time
    const revenueOverTime = await this.metricsService.getRevenueOverTime(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get top revenue generating content
    const topRevenueContent = await this.metricsService.getTopRevenueContent(
      creatorId,
      startDate,
      endDate,
    );
    
    // Get subscriber metrics
    const subscriberMetrics = await this.metricsService.getSubscriberMetrics(
      creatorId,
      startDate,
      endDate,
    );
    
    return {
      revenueMetrics,
      revenueBySource,
      revenueOverTime,
      topRevenueContent,
      subscriberMetrics,
    };
  }

  async getCustomAnalytics(
    creatorId: string,
    metrics: string[],
    startDate?: Date,
    endDate?: Date,
    filterDto?: FilterAnalyticsDto,
  ) {
    this.logger.debug(`Getting custom analytics for creator: ${creatorId}`);
    
    const results = {};
    
    for (const metric of metrics) {
      switch (metric) {
        case 'views':
          results['views'] = await this.metricsService.getTotalViews(creatorId, startDate, endDate);
          break;
        case 'engagements':
          results['engagements'] = await this.metricsService.getTotalEngagements(creatorId, startDate, endDate);
          break;
        case 'followers':
          results['followers'] = await this.metricsService.getTotalFollowers(creatorId, endDate);
          break;
        case 'followerGrowth':
          results['followerGrowth'] = await this.metricsService.getFollowerGrowth(creatorId, startDate, endDate);
          break;
        case 'revenue':
          results['revenue'] = await this.metricsService.getTotalRevenue(creatorId, startDate, endDate);
          break;
        case 'engagementRate':
          const views = await this.metricsService.getTotalViews(creatorId, startDate, endDate);
          const engagements = await this.metricsService.getTotalEngagements(creatorId, startDate, endDate);
          results['engagementRate'] = views > 0 ? (engagements / views) * 100 : 0;
          break;
        case 'trendingContent':
          results['trendingContent'] = await this.metricsService.getTrendingContent(creatorId, startDate, endDate);
          break;
        case 'audienceDemographics':
          results['audienceDemographics'] = await this.metricsService.getAudienceDemographics(creatorId);
          break;
        case 'contentPerformance':
          results['contentPerformance'] = await this.metricsService.getContentPerformance(creatorId, startDate, endDate);
          break;
        case 'topContent':
          results['topContent'] = await this.metricsService.getTopContent(creatorId, startDate, endDate);
          break;
        default:
          // For metrics over time
          if (metric.endsWith('OverTime')) {
            const baseMetric = metric.replace('OverTime', '');
            results[metric] = await this.metricsService.getMetricOverTime(
              creatorId,
              baseMetric,
              startDate,
              endDate,
            );
          }
      }
    }
    
    return results;
  }

  async saveDashboardConfig(creatorId: string, configDto: DashboardConfigDto) {
    this.logger.debug(`Saving dashboard config for creator: ${creatorId}`);
    
    // In a real implementation, this would save to the database
    // For now, we'll just store it in memory
    this.dashboardConfigs.set(creatorId, configDto);
    
    return {
      success: true,
      message: 'Dashboard configuration saved successfully',
      config: configDto,
    };
  }

  async getDashboardConfig(creatorId: string) {
    this.logger.debug(`Getting dashboard config for creator: ${creatorId}`);
    
    // In a real implementation, this would fetch from the database
    // For now, we'll just retrieve from memory or return a default config
    const config = this.dashboardConfigs.get(creatorId) || this.getDefaultDashboardConfig();
    
    return config;
  }

  async exportAnalytics(
    creatorId: string,
    format: 'csv' | 'json' | 'pdf',
    metrics?: string[],
    startDate?: Date,
    endDate?: Date,
  ) {
    this.logger.debug(`Exporting analytics for creator: ${creatorId} in ${format} format`);
    
    // Get the data to export
    const data = await this.getDataForExport(creatorId, metrics, startDate, endDate);
    
    // Export in the requested format
    switch (format) {
      case 'csv':
        return this.exportToCsv(data);
      case 'json':
        return this.exportToJson(data);
      case 'pdf':
        return this.exportToPdf(data);
      default:
        return { error: 'Unsupported export format' };
    }
  }

  // Helper methods
  private getDefaultDashboardConfig(): DashboardConfigDto {
    return {
      layout: [
        { id: 'overview', type: 'overview', position: { x: 0, y: 0, w: 12, h: 6 } },
        { id: 'audience', type: 'audience', position: { x: 0, y: 6, w: 6, h: 6 } },
        { id: 'content', type: 'content', position: { x: 6, y: 6, w: 6, h: 6 } },
        { id: 'engagement', type: 'engagement', position: { x: 0, y: 12, w: 6, h: 6 } },
        { id: 'revenue', type: 'revenue', position: { x: 6, y: 12, w: 6, h: 6 } },
      ],
      widgets: [
        { id: 'totalViews', type: 'metric', dataSource: 'views' },
        { id: 'totalEngagements', type: 'metric', dataSource: 'engagements' },
        { id: 'totalFollowers', type: 'metric', dataSource: 'followers' },
        { id: 'followerGrowth', type: 'metric', dataSource: 'followerGrowth' },
        { id: 'totalRevenue', type: 'metric', dataSource: 'revenue' },
        { id: 'engagementRate', type: 'metric', dataSource: 'engagementRate' },
        { id: 'viewsOverTime', type: 'lineChart', dataSource: 'viewsOverTime' },
        { id: 'engagementsOverTime', type: 'lineChart', dataSource: 'engagementsOverTime' },
        { id: 'followersOverTime', type: 'lineChart', dataSource: 'followersOverTime' },
        { id: 'revenueOverTime', type: 'lineChart', dataSource: 'revenueOverTime' },
        { id: 'audienceDemographics', type: 'pieChart', dataSource: 'audienceDemographics' },
        { id: 'contentPerformance', type: 'barChart', dataSource: 'contentPerformance' },
        { id: 'topContent', type: 'table', dataSource: 'topContent' },
      ],
      preferences: {
        theme: 'light',
        dateRange: 'last30Days',
        refreshInterval: 3600,
      },
    };
  }

  private async getDataForExport(
    creatorId: string,
    metrics?: string[],
    startDate?: Date,
    endDate?: Date,
  ) {
    if (metrics && metrics.length > 0) {
      return this.getCustomAnalytics(creatorId, metrics, startDate, endDate);
    } else {
      // Get all analytics
      const overview = await this.getOverview(creatorId, startDate, endDate);
      const audience = await this.getAudienceAnalytics(creatorId, startDate, endDate);
      const content = await this.getContentAnalytics(creatorId, startDate, endDate);
      const engagement = await this.getEngagementAnalytics(creatorId, startDate, endDate);
      const revenue = await this.getRevenueAnalytics(creatorId, startDate, endDate);
      
      return {
        overview,
        audience,
        content,
        engagement,
        revenue,
      };
    }
  }

  private exportToCsv(data: any) {
    try {
      // Flatten the data for CSV export
      const flatData = this.flattenData(data);
      
      // Convert to CSV
      const csv = json2csv.parse(flatData);
      
      return {
        format: 'csv',
        data: csv,
      };
    } catch (error) {
      this.logger.error(`Error exporting to CSV: ${error.message}`);
      return { error: 'Failed to export to CSV' };
    }
  }

  private exportToJson(data: any) {
    try {
      return {
        format: 'json',
        data: JSON.stringify(data, null, 2),
      };
    } catch (error) {
      this.logger.error(`Error exporting to JSON: ${error.message}`);
      return { error: 'Failed to export to JSON' };
    }
  }

  private exportToPdf(data: any) {
    // In a real implementation, this would generate a PDF
    // For now, we'll just return a message
    return {
      format: 'pdf',
      message: 'PDF export is not implemented yet',
      data: JSON.stringify(data),
    };
  }

  private flattenData(data: any, prefix = ''): any[] {
    if (!data || typeof data !== 'object') {
      return [];
    }
    
    let result = [];
    
    // Handle arrays
    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        if (typeof item === 'object') {
          result = result.concat(this.flattenData(item, `${prefix}[${index}]`));
        } else {
          result.push({ [`${prefix}[${index}]`]: item });
        }
      });
      return result;
    }
    
    // Handle simple objects with primitive values
    const hasComplexValues = Object.values(data).some(value => typeof value === 'object' && value !== null);
    
    if (!hasComplexValues) {
      return [data];
    }
    
    // Handle complex objects
    for (const [key, value] of Object.entries(data)) {
      const newPrefix = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null) {
        result = result.concat(this.flattenData(value, newPrefix));
      } else {
        const obj = {};
        obj[newPrefix] = value;
        result.push(obj);
      }
    }
    
    return result;
  }
}
