import { Injectable, Logger } from '@nestjs/common';
import { ForecastingService } from '../../forecasting/forecasting.service';
import { WidgetType } from '@prisma/client';

@Injectable()
export class ForecastWidgetService {
  private readonly logger = new Logger(ForecastWidgetService.name);

  constructor(private readonly forecastingService: ForecastingService) {}

  /**
   * Génère un widget de prévision d'engagement
   * @param creatorId ID du créateur
   * @param days Nombre de jours à prévoir
   * @param metric Métrique à prévoir
   * @returns Données du widget
   */
  async generateEngagementForecastWidget(
    creatorId: string,
    days: number = 30,
    metric: string = 'views',
  ) {
    this.logger.debug(`Generating engagement forecast widget for creator ${creatorId}`);

    try {
      // Récupérer les prévisions d'engagement
      const forecasts = await this.forecastingService.forecastEngagement(creatorId, days);

      // Trouver la prévision pour la métrique spécifiée
      const forecast = forecasts.find(f => f.metric.toLowerCase() === metric.toLowerCase());

      if (!forecast) {
        throw new Error(`No forecast found for metric ${metric}`);
      }

      // Formater les données pour le widget
      const chartData = forecast.predictions.map(prediction => ({
        date: prediction.date,
        value: prediction.value,
        lowerBound: prediction.lowerBound,
        upperBound: prediction.upperBound,
      }));

      return {
        type: WidgetType.LINE_CHART,
        title: `Prévision de ${metric} pour les ${days} prochains jours`,
        data: chartData,
        config: {
          xAxis: {
            dataKey: 'date',
            type: 'time',
            label: 'Date',
          },
          yAxis: {
            label: metric.charAt(0).toUpperCase() + metric.slice(1),
          },
          series: [
            {
              dataKey: 'value',
              name: metric,
              color: '#4C9AFF',
            },
            {
              dataKey: 'lowerBound',
              name: 'Borne inférieure',
              color: '#DFE1E6',
              opacity: 0.5,
              areaStyle: {},
            },
            {
              dataKey: 'upperBound',
              name: 'Borne supérieure',
              color: '#DFE1E6',
              opacity: 0.5,
              areaStyle: {},
            },
          ],
          tooltip: {
            formatter: '${value} ${name}',
          },
        },
        metadata: {
          confidence: forecast.confidence,
          generatedAt: new Date(),
          forecastDays: days,
        },
      };
    } catch (error) {
      this.logger.error(`Error generating engagement forecast widget: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un widget de tendances d'engagement
   * @param creatorId ID du créateur
   * @returns Données du widget
   */
  async generateEngagementTrendsWidget(creatorId: string) {
    this.logger.debug(`Generating engagement trends widget for creator ${creatorId}`);

    try {
      // Récupérer les tendances d'engagement
      const trends = await this.forecastingService.analyzeEngagementTrends(creatorId);

      // Formater les données pour le widget
      const trendsData = trends.map(trend => ({
        metric: trend.metric,
        trend: trend.trend,
        changeRate: trend.changeRate,
        significance: trend.significance,
      }));

      return {
        type: WidgetType.TABLE,
        title: 'Tendances d\'engagement',
        data: trendsData,
        config: {
          columns: [
            {
              title: 'Métrique',
              dataIndex: 'metric',
              key: 'metric',
            },
            {
              title: 'Tendance',
              dataIndex: 'trend',
              key: 'trend',
              render: (trend) => {
                const icons = {
                  increasing: '↗️',
                  decreasing: '↘️',
                  stable: '➡️',
                };
                return `${icons[trend]} ${trend}`;
              },
            },
            {
              title: 'Taux de changement',
              dataIndex: 'changeRate',
              key: 'changeRate',
              render: (rate) => `${(rate * 100).toFixed(2)}%`,
            },
            {
              title: 'Confiance',
              dataIndex: 'significance',
              key: 'significance',
              render: (significance) => `${(significance * 100).toFixed(2)}%`,
            },
          ],
        },
        metadata: {
          analyzedAt: new Date(),
        },
      };
    } catch (error) {
      this.logger.error(`Error generating engagement trends widget: ${error.message}`);
      throw error;
    }
  }

  /**
   * Génère un widget de saisonnalité
   * @param creatorId ID du créateur
   * @returns Données du widget
   */
  async generateSeasonalityWidget(creatorId: string) {
    this.logger.debug(`Generating seasonality widget for creator ${creatorId}`);

    try {
      // Récupérer l'analyse de saisonnalité
      const seasonalityResults = await this.forecastingService.analyzeSeasonality(creatorId);

      // Trouver l'analyse pour les vues
      const viewsSeasonality = seasonalityResults.find(s => s.metric.toLowerCase() === 'views');

      if (!viewsSeasonality || !viewsSeasonality.weekly) {
        throw new Error('No weekly seasonality data found for views');
      }

      // Formater les données pour le widget hebdomadaire
      const weeklyData = Object.entries(viewsSeasonality.weekly.pattern).map(([day, value]) => ({
        day: this.getDayName(parseInt(day)),
        value,
        isPeak: viewsSeasonality.weekly.peakDays.includes(parseInt(day)),
        isLow: viewsSeasonality.weekly.lowDays.includes(parseInt(day)),
      }));

      // Formater les données pour le widget quotidien
      const dailyData = viewsSeasonality.daily
        ? Object.entries(viewsSeasonality.daily.pattern).map(([hour, value]) => ({
            hour: `${hour}h`,
            value,
            isPeak: viewsSeasonality.daily.peakHours.includes(parseInt(hour)),
            isLow: viewsSeasonality.daily.lowHours.includes(parseInt(hour)),
          }))
        : [];

      return {
        type: WidgetType.CUSTOM,
        title: 'Analyse de saisonnalité',
        data: {
          weekly: weeklyData,
          daily: dailyData,
        },
        config: {
          weekly: {
            xAxis: {
              dataKey: 'day',
              type: 'category',
              label: 'Jour',
            },
            yAxis: {
              label: 'Engagement moyen',
            },
            series: [
              {
                dataKey: 'value',
                name: 'Engagement',
                color: (data) => (data.isPeak ? '#36B37E' : data.isLow ? '#FF5630' : '#4C9AFF'),
              },
            ],
          },
          daily: {
            xAxis: {
              dataKey: 'hour',
              type: 'category',
              label: 'Heure',
            },
            yAxis: {
              label: 'Engagement moyen',
            },
            series: [
              {
                dataKey: 'value',
                name: 'Engagement',
                color: (data) => (data.isPeak ? '#36B37E' : data.isLow ? '#FF5630' : '#4C9AFF'),
              },
            ],
          },
        },
        metadata: {
          analyzedAt: new Date(),
          peakDays: viewsSeasonality.weekly.peakDays.map(day => this.getDayName(day)),
          lowDays: viewsSeasonality.weekly.lowDays.map(day => this.getDayName(day)),
          peakHours: viewsSeasonality.daily?.peakHours.map(hour => `${hour}h`) || [],
          lowHours: viewsSeasonality.daily?.lowHours.map(hour => `${hour}h`) || [],
        },
      };
    } catch (error) {
      this.logger.error(`Error generating seasonality widget: ${error.message}`);
      throw error;
    }
  }

  /**
   * Convertit un numéro de jour en nom de jour
   * @param day Numéro du jour (0-6)
   * @returns Nom du jour
   */
  private getDayName(day: number): string {
    const days = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    return days[day] || `Jour ${day}`;
  }
}
