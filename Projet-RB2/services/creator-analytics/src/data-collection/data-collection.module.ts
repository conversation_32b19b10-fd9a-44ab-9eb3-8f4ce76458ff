import { Module } from '@nestjs/common';
import { DataCollectionService } from './data-collection.service';
import { DataCollectionController } from './data-collection.controller';
import { EngagementMetricRepository } from './repositories/engagement-metric.repository';
import { AudienceMetricRepository } from './repositories/audience-metric.repository';
import { RevenueMetricRepository } from './repositories/revenue-metric.repository';

@Module({
  controllers: [DataCollectionController],
  providers: [
    DataCollectionService,
    EngagementMetricRepository,
    AudienceMetricRepository,
    RevenueMetricRepository,
  ],
  exports: [DataCollectionService],
})
export class DataCollectionModule {}
