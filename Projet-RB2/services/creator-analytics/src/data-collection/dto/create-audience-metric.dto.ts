import { IsString, <PERSON>NotEmpty, IsInt, IsOptional, Min, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAudienceMetricDto {
  @ApiProperty({
    description: 'ID du créateur',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  creatorId: string;

  @ApiProperty({
    description: 'Nombre total d\'abonnés',
    example: 5000,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  totalFollowers?: number = 0;

  @ApiProperty({
    description: 'Nombre de nouveaux abonnés',
    example: 100,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  newFollowers?: number = 0;

  @ApiProperty({
    description: 'Nombre d\'abonnés perdus',
    example: 20,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  lostFollowers?: number = 0;

  @ApiProperty({
    description: 'Nombre d\'abonnés actifs',
    example: 2000,
    default: 0,
  })
  @IsInt()
  @Min(0)
  @IsOptional()
  activeFollowers?: number = 0;

  @ApiProperty({
    description: 'Données démographiques',
    example: {
      age: {
        '18-24': 20,
        '25-34': 35,
        '35-44': 25,
        '45-54': 15,
        '55+': 5
      },
      gender: {
        male: 40,
        female: 58,
        other: 2
      },
      location: {
        'France': 60,
        'Belgium': 15,
        'Switzerland': 10,
        'Canada': 8,
        'Other': 7
      }
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  demographics?: Record<string, any>;

  @ApiProperty({
    description: 'Date de la métrique',
    example: '2023-10-15T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  date?: Date;
}
