import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { RevenueMetric, RevenueSource } from '@prisma/client';
import { CreateRevenueMetricDto } from '../dto/create-revenue-metric.dto';

@Injectable()
export class RevenueMetricRepository {
  private readonly logger = new Logger(RevenueMetricRepository.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateRevenueMetricDto): Promise<RevenueMetric> {
    return this.prisma.revenueMetric.create({
      data,
    });
  }

  async findByCreatorId(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<RevenueMetric[]> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    return this.prisma.revenueMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
  }

  async findByContentId(
    contentId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<RevenueMetric[]> {
    const where: any = { contentId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    return this.prisma.revenueMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });
  }

  async getTotalRevenue(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const result = await this.prisma.revenueMetric.aggregate({
      where,
      _sum: {
        amount: true,
      },
    });

    return result._sum.amount || 0;
  }

  async getRevenueBySource(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    const where: any = { creatorId };

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.revenueMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Agréger par source
    const sourceMap = new Map();
    
    metrics.forEach(metric => {
      if (!sourceMap.has(metric.source)) {
        sourceMap.set(metric.source, 0);
      }
      
      sourceMap.set(metric.source, sourceMap.get(metric.source) + metric.amount);
    });
    
    // Convertir en tableau
    const result = Array.from(sourceMap.entries()).map(([source, amount]) => ({
      source,
      amount,
    }));
    
    // Calculer le total
    const total = result.reduce((sum, item) => sum + item.amount, 0);
    
    // Ajouter le pourcentage
    result.forEach(item => {
      item.percentage = total > 0 ? (item.amount / total) * 100 : 0;
    });
    
    return {
      sources: result,
      total,
      period: {
        startDate: startDate || metrics[0]?.date,
        endDate: endDate || metrics[metrics.length - 1]?.date,
      },
    };
  }

  async getTopEarningContent(
    creatorId: string,
    limit: number = 10,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any[]> {
    const where: any = { creatorId };
    where.contentId = { not: null }; // Exclure les revenus non liés à un contenu

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = startDate;
      }
      if (endDate) {
        where.date.lte = endDate;
      }
    }

    const metrics = await this.prisma.revenueMetric.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Agréger par contenu
    const contentMap = new Map();
    
    metrics.forEach(metric => {
      if (!contentMap.has(metric.contentId)) {
        contentMap.set(metric.contentId, {
          contentId: metric.contentId,
          amount: 0,
          currency: metric.currency,
        });
      }
      
      const content = contentMap.get(metric.contentId);
      content.amount += metric.amount;
    });
    
    // Convertir en tableau et trier par montant
    const contentArray = Array.from(contentMap.values());
    
    // Trier par montant et limiter
    return contentArray
      .sort((a, b) => b.amount - a.amount)
      .slice(0, limit);
  }
}
