import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../../Backend-NestJS/src/prisma/prisma.service';
import { MetricsService } from '../metrics/metrics.service';
import { DataCollectionService } from '../data-collection/data-collection.service';
import { MetricType } from '@prisma/client';

interface TimeSeriesPoint {
  date: Date;
  value: number;
}

interface TimeSeriesData {
  data: TimeSeriesPoint[];
  metric: string;
}

interface ForecastResult {
  metric: string;
  predictions: {
    date: Date;
    value: number;
    lowerBound?: number;
    upperBound?: number;
  }[];
  confidence: number;
}

interface TrendResult {
  metric: string;
  trend: 'increasing' | 'decreasing' | 'stable';
  changeRate: number;
  significance: number;
  period: {
    start: Date;
    end: Date;
  };
}

interface SeasonalityResult {
  metric: string;
  daily?: {
    pattern: Record<string, number>; // Heure -> valeur
    peakHours: number[];
    lowHours: number[];
  };
  weekly?: {
    pattern: Record<string, number>; // Jour -> valeur
    peakDays: number[];
    lowDays: number[];
  };
  monthly?: {
    pattern: Record<string, number>; // Jour du mois -> valeur
    peakDays: number[];
    lowDays: number[];
  };
}

@Injectable()
export class ForecastingService {
  private readonly logger = new Logger(ForecastingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly metricsService: MetricsService,
    private readonly dataCollectionService: DataCollectionService,
  ) {}

  /**
   * Génère des prévisions d'engagement pour un créateur
   * @param creatorId ID du créateur
   * @param days Nombre de jours à prévoir
   * @param startDate Date de début pour les données historiques
   * @param endDate Date de fin pour les données historiques
   * @returns Prévisions d'engagement
   */
  async forecastEngagement(
    creatorId: string,
    days: number = 30,
    startDate?: Date,
    endDate?: Date,
  ): Promise<ForecastResult[]> {
    this.logger.debug(`Forecasting engagement for creator ${creatorId} for ${days} days`);

    // Récupérer les métriques d'engagement historiques
    const viewsData = await this.getTimeSeriesData(creatorId, 'views', startDate, endDate);
    const likesData = await this.getTimeSeriesData(creatorId, 'likes', startDate, endDate);
    const commentsData = await this.getTimeSeriesData(creatorId, 'comments', startDate, endDate);
    const sharesData = await this.getTimeSeriesData(creatorId, 'shares', startDate, endDate);

    // Générer les prévisions pour chaque métrique
    const viewsForecast = await this.generateForecast(viewsData, days);
    const likesForecast = await this.generateForecast(likesData, days);
    const commentsForecast = await this.generateForecast(commentsData, days);
    const sharesForecast = await this.generateForecast(sharesData, days);

    // Stocker les prévisions dans la base de données
    await this.storeForecast(creatorId, viewsForecast, MetricType.VIEWS);
    await this.storeForecast(creatorId, likesForecast, MetricType.LIKES);
    await this.storeForecast(creatorId, commentsForecast, MetricType.COMMENTS);
    await this.storeForecast(creatorId, sharesForecast, MetricType.SHARES);

    return [viewsForecast, likesForecast, commentsForecast, sharesForecast];
  }

  /**
   * Génère des prévisions de performance pour un contenu spécifique
   * @param creatorId ID du créateur
   * @param contentId ID du contenu
   * @param days Nombre de jours à prévoir
   * @returns Prévisions de performance
   */
  async forecastContentPerformance(
    creatorId: string,
    contentId: string,
    days: number = 14,
  ): Promise<ForecastResult[]> {
    this.logger.debug(`Forecasting performance for content ${contentId} for ${days} days`);

    // Récupérer les métriques historiques pour ce contenu
    const viewsData = await this.getContentTimeSeriesData(contentId, 'views');
    const engagementData = await this.getContentTimeSeriesData(contentId, 'engagement');

    // Générer les prévisions
    const viewsForecast = await this.generateForecast(viewsData, days);
    const engagementForecast = await this.generateForecast(engagementData, days);

    // Stocker les prévisions
    await this.storeForecast(creatorId, viewsForecast, MetricType.VIEWS, contentId);
    await this.storeForecast(creatorId, engagementForecast, MetricType.ENGAGEMENT_RATE, contentId);

    return [viewsForecast, engagementForecast];
  }

  /**
   * Analyse les tendances d'engagement pour un créateur
   * @param creatorId ID du créateur
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Tendances d'engagement
   */
  async analyzeEngagementTrends(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<TrendResult[]> {
    this.logger.debug(`Analyzing engagement trends for creator ${creatorId}`);

    // Récupérer les métriques d'engagement historiques
    const viewsData = await this.getTimeSeriesData(creatorId, 'views', startDate, endDate);
    const likesData = await this.getTimeSeriesData(creatorId, 'likes', startDate, endDate);
    const commentsData = await this.getTimeSeriesData(creatorId, 'comments', startDate, endDate);
    const sharesData = await this.getTimeSeriesData(creatorId, 'shares', startDate, endDate);
    const followersData = await this.getTimeSeriesData(creatorId, 'followers', startDate, endDate);

    // Analyser les tendances pour chaque métrique
    const viewsTrend = this.analyzeTrend(viewsData);
    const likesTrend = this.analyzeTrend(likesData);
    const commentsTrend = this.analyzeTrend(commentsData);
    const sharesTrend = this.analyzeTrend(sharesData);
    const followersTrend = this.analyzeTrend(followersData);

    return [viewsTrend, likesTrend, commentsTrend, sharesTrend, followersTrend];
  }

  /**
   * Analyse la saisonnalité des métriques d'engagement
   * @param creatorId ID du créateur
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Résultats de l'analyse de saisonnalité
   */
  async analyzeSeasonality(
    creatorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<SeasonalityResult[]> {
    this.logger.debug(`Analyzing seasonality for creator ${creatorId}`);

    // Récupérer les métriques d'engagement historiques avec une granularité horaire
    const viewsData = await this.getDetailedTimeSeriesData(creatorId, 'views', startDate, endDate);
    const engagementData = await this.getDetailedTimeSeriesData(creatorId, 'engagement', startDate, endDate);

    // Analyser la saisonnalité pour chaque métrique
    const viewsSeasonality = this.detectSeasonality(viewsData);
    const engagementSeasonality = this.detectSeasonality(engagementData);

    return [viewsSeasonality, engagementSeasonality];
  }

  /**
   * Récupère les données de série temporelle pour une métrique
   * @param creatorId ID du créateur
   * @param metricName Nom de la métrique
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Données de série temporelle
   */
  private async getTimeSeriesData(
    creatorId: string,
    metricName: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<TimeSeriesData> {
    // Récupérer les données de la métrique sur la période spécifiée
    const metricData = await this.metricsService.getMetricOverTime(
      creatorId,
      metricName,
      startDate,
      endDate,
    );

    // Transformer les données au format de série temporelle
    const timeSeriesData: TimeSeriesPoint[] = metricData.map(point => ({
      date: new Date(point.date),
      value: point.value,
    }));

    return {
      data: timeSeriesData,
      metric: metricName,
    };
  }

  /**
   * Récupère les données de série temporelle pour un contenu spécifique
   * @param contentId ID du contenu
   * @param metricName Nom de la métrique
   * @returns Données de série temporelle
   */
  private async getContentTimeSeriesData(
    contentId: string,
    metricName: string,
  ): Promise<TimeSeriesData> {
    // Récupérer les métriques d'engagement pour ce contenu
    const metrics = await this.prisma.engagementMetric.findMany({
      where: {
        contentId,
      },
      orderBy: {
        date: 'asc',
      },
    });

    // Transformer les données au format de série temporelle
    const timeSeriesData: TimeSeriesPoint[] = metrics.map(metric => {
      let value = 0;
      switch (metricName) {
        case 'views':
          value = metric.views;
          break;
        case 'likes':
          value = metric.likes;
          break;
        case 'comments':
          value = metric.comments;
          break;
        case 'shares':
          value = metric.shares;
          break;
        case 'engagement':
          value = metric.likes + metric.comments + metric.shares + metric.bookmarks;
          break;
      }

      return {
        date: metric.date,
        value,
      };
    });

    return {
      data: timeSeriesData,
      metric: metricName,
    };
  }

  /**
   * Récupère les données de série temporelle détaillées (granularité horaire)
   * @param creatorId ID du créateur
   * @param metricName Nom de la métrique
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Données de série temporelle détaillées
   */
  private async getDetailedTimeSeriesData(
    creatorId: string,
    metricName: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<TimeSeriesData> {
    // Cette méthode devrait récupérer des données avec une granularité horaire
    // Pour l'instant, nous utilisons les mêmes données que getTimeSeriesData
    return this.getTimeSeriesData(creatorId, metricName, startDate, endDate);
  }

  /**
   * Génère des prévisions à partir de données de série temporelle
   * @param timeSeriesData Données de série temporelle
   * @param days Nombre de jours à prévoir
   * @returns Résultat de la prévision
   */
  private async generateForecast(
    timeSeriesData: TimeSeriesData,
    days: number,
  ): Promise<ForecastResult> {
    const { data, metric } = timeSeriesData;

    if (data.length < 7) {
      this.logger.warn(`Not enough data points to generate forecast for ${metric}`);
      return {
        metric,
        predictions: [],
        confidence: 0,
      };
    }

    // Implémenter un algorithme de prévision simple (moyenne mobile)
    const predictions = this.simpleMovingAverageForecast(data, days);

    return {
      metric,
      predictions,
      confidence: 0.7, // Valeur arbitraire pour l'exemple
    };
  }

  /**
   * Implémente un algorithme de prévision simple basé sur la moyenne mobile
   * @param data Données historiques
   * @param days Nombre de jours à prévoir
   * @returns Prévisions
   */
  private simpleMovingAverageForecast(data: TimeSeriesPoint[], days: number) {
    // Trier les données par date
    const sortedData = [...data].sort((a, b) => a.date.getTime() - b.date.getTime());

    // Calculer la moyenne des 7 derniers jours
    const windowSize = Math.min(7, sortedData.length);
    const recentData = sortedData.slice(-windowSize);
    const average = recentData.reduce((sum, point) => sum + point.value, 0) / windowSize;

    // Calculer la tendance (pente)
    let trend = 0;
    if (sortedData.length >= 14) {
      const firstWeek = sortedData.slice(-14, -7);
      const secondWeek = sortedData.slice(-7);
      const firstAvg = firstWeek.reduce((sum, point) => sum + point.value, 0) / 7;
      const secondAvg = secondWeek.reduce((sum, point) => sum + point.value, 0) / 7;
      trend = (secondAvg - firstAvg) / 7;
    }

    // Générer les prévisions
    const predictions = [];
    const lastDate = sortedData[sortedData.length - 1].date;

    for (let i = 1; i <= days; i++) {
      const forecastDate = new Date(lastDate);
      forecastDate.setDate(forecastDate.getDate() + i);

      const forecastValue = Math.max(0, average + trend * i);
      const variance = average * 0.2; // 20% de variance pour les bornes

      predictions.push({
        date: forecastDate,
        value: Math.round(forecastValue),
        lowerBound: Math.round(Math.max(0, forecastValue - variance)),
        upperBound: Math.round(forecastValue + variance),
      });
    }

    return predictions;
  }

  /**
   * Analyse la tendance d'une série temporelle
   * @param timeSeriesData Données de série temporelle
   * @returns Résultat de l'analyse de tendance
   */
  private analyzeTrend(timeSeriesData: TimeSeriesData): TrendResult {
    const { data, metric } = timeSeriesData;

    if (data.length < 7) {
      return {
        metric,
        trend: 'stable',
        changeRate: 0,
        significance: 0,
        period: {
          start: data[0]?.date || new Date(),
          end: data[data.length - 1]?.date || new Date(),
        },
      };
    }

    // Trier les données par date
    const sortedData = [...data].sort((a, b) => a.date.getTime() - b.date.getTime());

    // Calculer la tendance linéaire
    const n = sortedData.length;
    const xValues = Array.from({ length: n }, (_, i) => i);
    const yValues = sortedData.map(point => point.value);

    const sumX = xValues.reduce((sum, x) => sum + x, 0);
    const sumY = yValues.reduce((sum, y) => sum + y, 0);
    const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
    const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculer le taux de changement relatif
    const firstValue = yValues[0] || 1; // Éviter la division par zéro
    const lastValue = yValues[n - 1] || 1;
    const changeRate = (lastValue - firstValue) / firstValue;

    // Déterminer la tendance
    let trend: 'increasing' | 'decreasing' | 'stable';
    if (slope > 0.05) {
      trend = 'increasing';
    } else if (slope < -0.05) {
      trend = 'decreasing';
    } else {
      trend = 'stable';
    }

    // Calculer la significativité (R²)
    const yMean = sumY / n;
    const ssTotal = yValues.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
    const ssResidual = yValues.reduce((sum, y, i) => {
      const yPred = slope * xValues[i] + intercept;
      return sum + Math.pow(y - yPred, 2);
    }, 0);
    const rSquared = 1 - ssResidual / ssTotal;

    return {
      metric,
      trend,
      changeRate,
      significance: rSquared,
      period: {
        start: sortedData[0].date,
        end: sortedData[n - 1].date,
      },
    };
  }

  /**
   * Détecte la saisonnalité dans une série temporelle
   * @param timeSeriesData Données de série temporelle
   * @returns Résultat de l'analyse de saisonnalité
   */
  private detectSeasonality(timeSeriesData: TimeSeriesData): SeasonalityResult {
    const { data, metric } = timeSeriesData;

    if (data.length < 14) {
      return {
        metric,
        daily: {
          pattern: {},
          peakHours: [],
          lowHours: [],
        },
        weekly: {
          pattern: {},
          peakDays: [],
          lowDays: [],
        },
      };
    }

    // Analyser la saisonnalité quotidienne (par heure)
    const hourlyPattern: Record<string, number[]> = {};
    for (let hour = 0; hour < 24; hour++) {
      hourlyPattern[hour] = [];
    }

    // Regrouper les valeurs par heure
    data.forEach(point => {
      const hour = point.date.getHours();
      hourlyPattern[hour].push(point.value);
    });

    // Calculer la moyenne pour chaque heure
    const hourlyAverage: Record<string, number> = {};
    for (const [hour, values] of Object.entries(hourlyPattern)) {
      if (values.length > 0) {
        hourlyAverage[hour] = values.reduce((sum, val) => sum + val, 0) / values.length;
      } else {
        hourlyAverage[hour] = 0;
      }
    }

    // Identifier les heures de pointe et les heures creuses
    const hourlyEntries = Object.entries(hourlyAverage).map(([hour, value]) => ({
      hour: parseInt(hour),
      value,
    }));
    hourlyEntries.sort((a, b) => b.value - a.value);

    const peakHours = hourlyEntries.slice(0, 3).map(entry => entry.hour);
    const lowHours = hourlyEntries.slice(-3).map(entry => entry.hour);

    // Analyser la saisonnalité hebdomadaire (par jour)
    const dailyPattern: Record<string, number[]> = {};
    for (let day = 0; day < 7; day++) {
      dailyPattern[day] = [];
    }

    // Regrouper les valeurs par jour de la semaine
    data.forEach(point => {
      const day = point.date.getDay();
      dailyPattern[day].push(point.value);
    });

    // Calculer la moyenne pour chaque jour
    const dailyAverage: Record<string, number> = {};
    for (const [day, values] of Object.entries(dailyPattern)) {
      if (values.length > 0) {
        dailyAverage[day] = values.reduce((sum, val) => sum + val, 0) / values.length;
      } else {
        dailyAverage[day] = 0;
      }
    }

    // Identifier les jours de pointe et les jours creux
    const dailyEntries = Object.entries(dailyAverage).map(([day, value]) => ({
      day: parseInt(day),
      value,
    }));
    dailyEntries.sort((a, b) => b.value - a.value);

    const peakDays = dailyEntries.slice(0, 2).map(entry => entry.day);
    const lowDays = dailyEntries.slice(-2).map(entry => entry.day);

    return {
      metric,
      daily: {
        pattern: hourlyAverage,
        peakHours,
        lowHours,
      },
      weekly: {
        pattern: dailyAverage,
        peakDays,
        lowDays,
      },
    };
  }

  /**
   * Stocke une prévision dans la base de données
   * @param creatorId ID du créateur
   * @param forecast Résultat de la prévision
   * @param metricType Type de métrique
   * @param contentId ID du contenu (optionnel)
   */
  private async storeForecast(
    creatorId: string,
    forecast: ForecastResult,
    metricType: MetricType,
    contentId?: string,
  ) {
    if (forecast.predictions.length === 0) {
      return;
    }

    const startDate = forecast.predictions[0].date;
    const endDate = forecast.predictions[forecast.predictions.length - 1].date;

    try {
      await this.prisma.forecast.create({
        data: {
          creatorId,
          contentId,
          metricType,
          predictions: forecast.predictions,
          confidence: forecast.confidence,
          startDate,
          endDate,
        },
      });
    } catch (error) {
      this.logger.error(`Error storing forecast: ${error.message}`);
    }
  }
}
