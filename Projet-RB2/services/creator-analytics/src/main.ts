import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as helmet from 'helmet';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

async function bootstrap() {
  // Configuration du logger
  const logger = WinstonModule.createLogger({
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.colorize(),
          winston.format.printf(({ timestamp, level, message, context }) => {
            return `${timestamp} [${context}] ${level}: ${message}`;
          }),
        ),
      }),
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json(),
        ),
      }),
      new winston.transports.File({
        filename: 'logs/combined.log',
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json(),
        ),
      }),
    ],
  });

  // Création de l'application NestJS
  const app = await NestFactory.create(AppModule, {
    logger,
  });

  // Récupération de la configuration
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT', 3020);
  const apiPrefix = configService.get<string>('API_PREFIX', 'api');
  const appName = configService.get<string>('APP_NAME', 'creator-analytics');

  // Configuration de la sécurité
  app.use(helmet());
  app.enableCors();

  // Configuration de la validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Configuration du préfixe global pour les routes
  app.setGlobalPrefix(apiPrefix);

  // Configuration de Swagger
  const options = new DocumentBuilder()
    .setTitle(`${appName} API`)
    .setDescription(`API documentation for the ${appName} service`)
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(`${apiPrefix}/docs`, app, document);

  // Démarrage du serveur
  await app.listen(port);
  logger.log(`Application is running on: http://localhost:${port}/${apiPrefix}`, 'Bootstrap');
  logger.log(`Swagger documentation is available at: http://localhost:${port}/${apiPrefix}/docs`, 'Bootstrap');
}

bootstrap();
