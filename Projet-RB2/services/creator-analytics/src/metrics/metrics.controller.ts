import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MetricsService } from './metrics.service';
import { GetMetricsDto } from './dto/get-metrics.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('metrics')
@Controller('metrics')
export class MetricsController {
  private readonly logger = new Logger(MetricsController.name);

  constructor(private readonly metricsService: MetricsService) {}

  @Get(':creatorId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les métriques pour un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Les métriques du créateur',
  })
  async getCreatorMetrics(
    @Param('creatorId') creatorId: string,
    @Query() filters: GetMetricsDto,
  ) {
    this.logger.debug(`Getting metrics for creator: ${creatorId}`);
    return this.metricsService.getCreatorMetrics(creatorId, filters);
  }

  @Get(':creatorId/content/:contentId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir les métriques pour un contenu spécifique' })
  @ApiResponse({
    status: 200,
    description: 'Les métriques du contenu',
  })
  @ApiResponse({
    status: 404,
    description: 'Contenu non trouvé',
  })
  async getContentMetrics(
    @Param('creatorId') creatorId: string,
    @Param('contentId') contentId: string,
    @Query() filters: GetMetricsDto,
  ) {
    this.logger.debug(`Getting metrics for content: ${contentId}`);
    return this.metricsService.getContentMetrics(creatorId, contentId, filters);
  }

  @Get(':creatorId/summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Obtenir un résumé des métriques pour un créateur' })
  @ApiResponse({
    status: 200,
    description: 'Le résumé des métriques du créateur',
  })
  async getCreatorSummary(@Param('creatorId') creatorId: string) {
    this.logger.debug(`Getting summary for creator: ${creatorId}`);
    return this.metricsService.getCreatorSummary(creatorId);
  }
}
