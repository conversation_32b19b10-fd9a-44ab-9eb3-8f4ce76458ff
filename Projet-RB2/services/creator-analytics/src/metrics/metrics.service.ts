import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { EngagementMetricRepository } from '../data-collection/repositories/engagement-metric.repository';
import { AudienceMetricRepository } from '../data-collection/repositories/audience-metric.repository';
import { RevenueMetricRepository } from '../data-collection/repositories/revenue-metric.repository';
import { GetMetricsDto } from './dto/get-metrics.dto';
import { ContentType } from '@prisma/client';

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);

  constructor(
    private readonly engagementMetricRepository: EngagementMetricRepository,
    private readonly audienceMetricRepository: AudienceMetricRepository,
    private readonly revenueMetricRepository: RevenueMetricRepository,
  ) {}

  async getCreatorMetrics(creatorId: string, filters: GetMetricsDto) {
    this.logger.debug(`Getting metrics for creator: ${creatorId}`);
    
    const startDate = filters.startDate ? new Date(filters.startDate) : undefined;
    const endDate = filters.endDate ? new Date(filters.endDate) : undefined;
    
    // Récupérer les métriques d'engagement
    let engagementMetrics;
    if (filters.contentType) {
      engagementMetrics = await this.engagementMetricRepository.findByCreatorAndContentType(
        creatorId,
        filters.contentType as ContentType,
        startDate,
        endDate,
      );
    } else {
      engagementMetrics = await this.engagementMetricRepository.findByCreatorId(
        creatorId,
        startDate,
        endDate,
      );
    }
    
    // Récupérer les métriques d'audience
    const audienceMetrics = await this.audienceMetricRepository.findByCreatorId(
      creatorId,
      startDate,
      endDate,
    );
    
    // Récupérer les métriques de revenus
    const revenueMetrics = await this.revenueMetricRepository.findByCreatorId(
      creatorId,
      startDate,
      endDate,
    );
    
    // Agréger les métriques
    const aggregatedEngagement = await this.engagementMetricRepository.getAggregatedMetrics(
      creatorId,
      startDate,
      endDate,
    );
    
    const audienceGrowth = await this.audienceMetricRepository.getAudienceGrowth(
      creatorId,
      startDate,
      endDate,
    );
    
    const revenueBySource = await this.revenueMetricRepository.getRevenueBySource(
      creatorId,
      startDate,
      endDate,
    );
    
    // Récupérer le contenu le plus performant
    const topContent = await this.engagementMetricRepository.getTopPerformingContent(
      creatorId,
      10,
      startDate,
      endDate,
    );
    
    // Récupérer le contenu générant le plus de revenus
    const topEarningContent = await this.revenueMetricRepository.getTopEarningContent(
      creatorId,
      10,
      startDate,
      endDate,
    );
    
    return {
      engagement: {
        aggregated: aggregatedEngagement,
        topContent,
      },
      audience: {
        growth: audienceGrowth,
        demographics: await this.audienceMetricRepository.getAudienceDemographics(creatorId),
      },
      revenue: {
        bySource: revenueBySource,
        topEarningContent,
      },
      period: {
        startDate: startDate || 'all',
        endDate: endDate || 'now',
      },
    };
  }

  async getContentMetrics(creatorId: string, contentId: string, filters: GetMetricsDto) {
    this.logger.debug(`Getting metrics for content: ${contentId}`);
    
    const startDate = filters.startDate ? new Date(filters.startDate) : undefined;
    const endDate = filters.endDate ? new Date(filters.endDate) : undefined;
    
    // Récupérer les métriques d'engagement pour ce contenu
    const engagementMetrics = await this.engagementMetricRepository.findByContentId(
      contentId,
      startDate,
      endDate,
    );
    
    if (engagementMetrics.length === 0) {
      throw new NotFoundException(`No metrics found for content: ${contentId}`);
    }
    
    // Récupérer les métriques de revenus pour ce contenu
    const revenueMetrics = await this.revenueMetricRepository.findByContentId(
      contentId,
      startDate,
      endDate,
    );
    
    // Agréger les métriques d'engagement
    const totalViews = engagementMetrics.reduce((sum, metric) => sum + metric.views, 0);
    const totalLikes = engagementMetrics.reduce((sum, metric) => sum + metric.likes, 0);
    const totalComments = engagementMetrics.reduce((sum, metric) => sum + metric.comments, 0);
    const totalShares = engagementMetrics.reduce((sum, metric) => sum + metric.shares, 0);
    const totalBookmarks = engagementMetrics.reduce((sum, metric) => sum + metric.bookmarks, 0);
    const totalClickThroughs = engagementMetrics.reduce((sum, metric) => sum + metric.clickThroughs, 0);
    
    // Calculer les métriques dérivées
    const engagementRate = totalViews > 0
      ? ((totalLikes + totalComments + totalShares) / totalViews) * 100
      : 0;
    
    const clickThroughRate = totalViews > 0
      ? (totalClickThroughs / totalViews) * 100
      : 0;
    
    // Agréger les métriques de revenus
    const totalRevenue = revenueMetrics.reduce((sum, metric) => sum + metric.amount, 0);
    const currency = revenueMetrics.length > 0 ? revenueMetrics[0].currency : 'EUR';
    
    // Calculer le revenu par vue
    const revenuePerView = totalViews > 0 ? totalRevenue / totalViews : 0;
    
    return {
      engagement: {
        totalViews,
        totalLikes,
        totalComments,
        totalShares,
        totalBookmarks,
        totalClickThroughs,
        engagementRate,
        clickThroughRate,
      },
      revenue: {
        totalRevenue,
        currency,
        revenuePerView,
      },
      contentType: engagementMetrics[0].contentType,
      period: {
        startDate: startDate || engagementMetrics[0].date,
        endDate: endDate || engagementMetrics[engagementMetrics.length - 1].date,
      },
    };
  }

  async getCreatorSummary(creatorId: string) {
    this.logger.debug(`Getting summary for creator: ${creatorId}`);
    
    // Récupérer les métriques agrégées
    const aggregatedEngagement = await this.engagementMetricRepository.getAggregatedMetrics(creatorId);
    const audienceGrowth = await this.audienceMetricRepository.getAudienceGrowth(creatorId);
    const totalRevenue = await this.revenueMetricRepository.getTotalRevenue(creatorId);
    
    // Récupérer le contenu le plus performant
    const topContent = await this.engagementMetricRepository.getTopPerformingContent(creatorId, 5);
    
    return {
      engagement: {
        totalViews: aggregatedEngagement.totalViews,
        totalLikes: aggregatedEngagement.totalLikes,
        totalComments: aggregatedEngagement.totalComments,
        totalShares: aggregatedEngagement.totalShares,
        engagementRate: aggregatedEngagement.engagementRate,
      },
      audience: {
        totalFollowers: audienceGrowth.totalFollowers,
        netGrowth: audienceGrowth.netGrowth,
        growthRate: audienceGrowth.growthRate,
      },
      revenue: {
        totalRevenue,
        currency: 'EUR',
      },
      topContent,
    };
  }
}
