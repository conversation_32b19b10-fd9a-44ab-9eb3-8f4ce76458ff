import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { AnalysisRequest, AnalysisResult, KPIDefinition, Dashboard, Report, DataAlert, AgentConfig } from '../types';
/**
 * Agent Data Analyst - Analyse de données avancée et insights business
 */
export declare class DataAnalystAgent extends EventEmitter {
    private logger;
    private config;
    private analyticsEngine;
    private predictiveModelingEngine;
    private visualizationEngine;
    private reportingEngine;
    private kpiTrackingEngine;
    private dataProcessingEngine;
    private memory;
    private communication;
    private isInitialized;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise l'agent
     */
    initialize(): Promise<void>;
    /**
     * Effectue une analyse de données
     */
    performAnalysis(request: AnalysisRequest): Promise<AnalysisResult>;
    /**
     * Crée un dashboard personnalisé
     */
    createDashboard(definition: Partial<Dashboard>): Promise<Dashboard>;
    /**
     * Génère un rapport
     */
    generateReport(reportConfig: Partial<Report>): Promise<Buffer>;
    /**
     * Suit les KPIs
     */
    trackKPIs(kpiDefinitions: KPIDefinition[]): Promise<void>;
    /**
     * Configure des alertes de données
     */
    setupDataAlerts(alerts: DataAlert[]): Promise<void>;
    /**
     * Obtient les insights automatiques
     */
    getAutomatedInsights(dataSource: string, timeRange?: any): Promise<any[]>;
    /**
     * Effectue une analyse prédictive
     */
    performPredictiveAnalysis(data: any[], target: string, features: string[]): Promise<any>;
    /**
     * Analyse les données en temps réel
     */
    analyzeRealTimeData(streamData: any): Promise<any>;
    /**
     * Configure les listeners d'événements
     */
    private setupEventListeners;
    /**
     * Valide une requête d'analyse
     */
    private validateAnalysisRequest;
    /**
     * Obtient l'algorithme pour un type d'analyse
     */
    private getAlgorithmForType;
    /**
     * Génère un ID unique
     */
    private generateId;
    /**
     * Arrête l'agent
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=DataAnalystAgent.d.ts.map