import { Logger } from 'winston';
import { AgentConfig, AnalysisData, Insight, Recommendation, AnalysisParameters } from '../types';
/**
 * Moteur d'analyse de données avancé
 */
export declare class AnalyticsEngine {
    private logger;
    private config;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur d'analyse
     */
    initialize(): Promise<void>;
    /**
     * Effectue une analyse descriptive
     */
    performDescriptiveAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une analyse prescriptive
     */
    performPrescriptiveAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une analyse diagnostique
     */
    performDiagnosticAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une analyse comparative
     */
    performComparativeAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une analyse de tendances
     */
    performTrendAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une analyse de corrélation
     */
    performCorrelationAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une analyse de segmentation
     */
    performSegmentationAnalysis(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Effectue une détection d'anomalies
     */
    performAnomalyDetection(data: any[], parameters: AnalysisParameters): Promise<AnalysisData>;
    /**
     * Génère des insights automatiques
     */
    generateInsights(analysisData: AnalysisData, analysisType: string): Promise<Insight[]>;
    /**
     * Génère des recommandations
     */
    generateRecommendations(analysisData: AnalysisData, insights: Insight[]): Promise<Recommendation[]>;
    /**
     * Analyse les données en temps réel
     */
    analyzeRealTimeData(streamData: any): Promise<any>;
    /**
     * Génère des insights automatiques pour une source de données
     */
    generateAutomatedInsights(dataSource: string, timeRange?: any): Promise<any[]>;
    private calculateDescriptiveStatistics;
    private calculateMetrics;
    private analyzeTrends;
    private calculateCorrelations;
    private detectAnomalies;
    private generateMetricInsights;
    private generateTrendInsights;
    private generateCorrelationInsights;
    private generateAnomalyInsights;
    private generateRecommendationFromInsight;
    private generateAnomalyRecommendations;
    private generateTrendRecommendations;
    private calculateRealTimeMetrics;
    private detectRealTimeAnomalies;
    private generateRealTimeAlerts;
    private generateOptimalActions;
    private analyzeScenarios;
    private identifyConstraints;
    private identifyRootCauses;
    private analyzeContributingFactors;
    private performComparisons;
    private calculateBenchmarks;
    private analyzeVariances;
    private detectSeasonality;
    private detectChangePoints;
    private calculatePartialCorrelations;
    private analyzeCausality;
    private performClustering;
    private createSegmentProfiles;
    private calculateSegmentMetrics;
    private detectOutliers;
    private analyzeAnomalyPatterns;
    private generateId;
}
//# sourceMappingURL=AnalyticsEngine.d.ts.map