import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import winston from 'winston';
import dotenv from 'dotenv';
import { DevOpsAgent } from './core/DevOpsAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
import { AgentConfig } from './types';

// Charger les variables d'environnement
dotenv.config();

// Configuration du logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'agent-devops' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Configuration de l'agent
const agentConfig: AgentConfig = {
  id: process.env.AGENT_ID || 'agent-devops-001',
  name: 'Agent DevOps Deployment Manager',
  type: 'devops',
  version: '1.0.0',
  capabilities: [
    'kubernetes-deployment',
    'vercel-deployment',
    'netlify-deployment',
    'aws-deployment',
    'infrastructure-management',
    'pipeline-execution',
    'monitoring',
    'rollback',
    'scaling',
    'terraform-management',
    'docker-deployment'
  ],
  endpoints: {
    health: '/health',
    ready: '/ready',
    info: '/api/info',
    deploy: '/api/deploy',
    infrastructure: '/api/infrastructure',
    pipeline: '/api/pipeline',
    rollback: '/api/rollback',
    scale: '/api/scale',
    status: '/api/status'
  },
  memory: {
    store: 'weaviate',
    collections: [
      'Deployment',
      'Infrastructure',
      'Pipeline',
      'Configuration',
      'Metric',
      'Alert'
    ]
  },
  communication: {
    kafka: {
      topics: [
        'agent.devops.deployment.complete',
        'agent.devops.deployment.failed',
        'agent.devops.infrastructure.ready',
        'agent.devops.pipeline.complete',
        'agent.devops.rollback.complete',
        'agent.devops.scaling.complete'
      ],
      groupId: 'agent-devops-group'
    },
    redis: {
      channels: [
        'devops:notifications',
        'devops:requests'
      ]
    }
  },
  deployment: {
    supportedPlatforms: ['kubernetes', 'vercel', 'netlify', 'aws', 'gcp', 'azure', 'docker'],
    defaultPlatform: 'kubernetes',
    infrastructureProviders: ['aws', 'gcp', 'azure', 'kubernetes'],
    pipelineTypes: ['build', 'test', 'deploy', 'full']
  }
};

// Initialisation de l'application Express
const app = express();
const port = process.env.PORT || 3007;

// Middleware de sécurité
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

// Middleware CORS
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Middleware de parsing
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware de logging des requêtes
app.use((req, res, next) => {
  logger.info('Requête reçue', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Variables globales pour les services
let devopsAgent: DevOpsAgent;
let memory: WeaviateMemory;
let communication: KafkaCommunication;

/**
 * Initialisation des services
 */
async function initializeServices(): Promise<void> {
  try {
    logger.info('Initialisation des services de l\'agent DevOps');

    // Initialiser la mémoire Weaviate
    memory = new WeaviateMemory(
      logger,
      process.env.WEAVIATE_URL || 'http://weaviate:8080'
    );

    // Initialiser la communication Kafka
    communication = new KafkaCommunication(
      logger,
      process.env.KAFKA_BROKERS || 'kafka:9092',
      agentConfig.id
    );

    // Initialiser l'agent DevOps
    devopsAgent = new DevOpsAgent(
      agentConfig,
      logger,
      memory,
      communication
    );

    logger.info('Services DevOps initialisés avec succès');

  } catch (error) {
    logger.error('Erreur lors de l\'initialisation des services DevOps', { error: error.message });
    throw error;
  }
}

// Routes de santé et statut

/**
 * Endpoint de santé
 */
app.get('/health', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    agent: {
      id: agentConfig.id,
      name: agentConfig.name,
      version: agentConfig.version
    },
    services: {
      memory: memory?.isConnected() || false,
      communication: communication?.isConnected() || false
    },
    deployment: {
      supportedPlatforms: agentConfig.deployment.supportedPlatforms,
      activeDeployments: devopsAgent?.getStatus()?.activeDeployments || 0,
      queuedDeployments: devopsAgent?.getStatus()?.queuedDeployments || 0
    },
    uptime: process.uptime(),
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  };

  const statusCode = health.services.memory && health.services.communication ? 200 : 503;
  res.status(statusCode).json(health);
});

/**
 * Endpoint de disponibilité
 */
app.get('/ready', (req, res) => {
  const ready = devopsAgent && memory?.isConnected() && communication?.isConnected();
  res.status(ready ? 200 : 503).json({
    ready,
    timestamp: new Date().toISOString()
  });
});

/**
 * Informations sur l'agent
 */
app.get('/api/info', (req, res) => {
  res.json({
    agent: agentConfig,
    capabilities: agentConfig.capabilities,
    endpoints: agentConfig.endpoints,
    supportedPlatforms: agentConfig.deployment.supportedPlatforms,
    infrastructureProviders: agentConfig.deployment.infrastructureProviders,
    version: agentConfig.version,
    timestamp: new Date().toISOString()
  });
});

/**
 * Statut détaillé de l'agent
 */
app.get('/api/status', (req, res) => {
  try {
    const status = devopsAgent?.getStatus() || {
      activeDeployments: 0,
      queuedDeployments: 0,
      isProcessingQueue: false,
      supportedPlatforms: agentConfig.deployment.supportedPlatforms,
      uptime: process.uptime()
    };

    res.json({
      success: true,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Erreur lors de la récupération du statut', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la récupération du statut',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Routes API principales

/**
 * Déploiement d'une application
 */
app.post('/api/deploy', async (req, res) => {
  try {
    const deploymentRequest = req.body;

    if (!deploymentRequest || !deploymentRequest.applicationName || !deploymentRequest.platform) {
      return res.status(400).json({
        error: 'Requête de déploiement invalide',
        required: ['applicationName', 'platform'],
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de déploiement', { 
      app: deploymentRequest.applicationName,
      platform: deploymentRequest.platform.type,
      environment: deploymentRequest.environment 
    });

    const deploymentResult = await devopsAgent.deployApplication(deploymentRequest);

    res.json({
      success: true,
      deploymentResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors du déploiement', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors du déploiement',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Gestion de l'infrastructure
 */
app.post('/api/infrastructure', async (req, res) => {
  try {
    const infrastructureRequest = req.body;

    if (!infrastructureRequest || !infrastructureRequest.type || !infrastructureRequest.provider) {
      return res.status(400).json({
        error: 'Requête d\'infrastructure invalide',
        required: ['type', 'provider'],
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de gestion d\'infrastructure', { 
      type: infrastructureRequest.type,
      provider: infrastructureRequest.provider 
    });

    const infrastructureResult = await devopsAgent.manageInfrastructure(infrastructureRequest);

    res.json({
      success: true,
      infrastructureResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la gestion de l\'infrastructure', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la gestion de l\'infrastructure',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Exécution de pipeline
 */
app.post('/api/pipeline', async (req, res) => {
  try {
    const pipeline = req.body;

    if (!pipeline || !pipeline.name || !pipeline.stages) {
      return res.status(400).json({
        error: 'Pipeline invalide',
        required: ['name', 'stages'],
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande d\'exécution de pipeline', { 
      name: pipeline.name,
      type: pipeline.type 
    });

    const pipelineResult = await devopsAgent.executePipeline(pipeline);

    res.json({
      success: true,
      pipelineResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de l\'exécution du pipeline', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de l\'exécution du pipeline',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Rollback d'un déploiement
 */
app.post('/api/rollback', async (req, res) => {
  try {
    const { deploymentId, targetVersion } = req.body;

    if (!deploymentId) {
      return res.status(400).json({
        error: 'ID de déploiement requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de rollback', { deploymentId, targetVersion });

    const rollbackResult = await devopsAgent.rollbackDeployment(deploymentId, targetVersion);

    res.json({
      success: true,
      rollbackResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors du rollback', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors du rollback',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Mise à l'échelle d'un déploiement
 */
app.post('/api/scale', async (req, res) => {
  try {
    const { deploymentId, replicas } = req.body;

    if (!deploymentId || typeof replicas !== 'number') {
      return res.status(400).json({
        error: 'ID de déploiement et nombre de replicas requis',
        timestamp: new Date().toISOString()
      });
    }

    logger.info('Demande de mise à l\'échelle', { deploymentId, replicas });

    const scalingResult = await devopsAgent.scaleDeployment(deploymentId, replicas);

    res.json({
      success: true,
      scalingResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à l\'échelle', { error: error.message });
    res.status(500).json({
      error: 'Erreur lors de la mise à l\'échelle',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Middleware de gestion des erreurs
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Erreur non gérée', { 
    error: error.message, 
    stack: error.stack,
    url: req.url,
    method: req.method 
  });

  res.status(500).json({
    error: 'Erreur interne du serveur',
    timestamp: new Date().toISOString()
  });
});

// Route 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint non trouvé',
    timestamp: new Date().toISOString()
  });
});

/**
 * Démarrage du serveur
 */
async function startServer(): Promise<void> {
  try {
    // Initialiser les services
    await initializeServices();

    // Démarrer le serveur HTTP
    const server = app.listen(port, () => {
      logger.info(`Agent DevOps démarré sur le port ${port}`, {
        agentId: agentConfig.id,
        version: agentConfig.version,
        environment: process.env.NODE_ENV || 'development',
        supportedPlatforms: agentConfig.deployment.supportedPlatforms
      });
    });

    // Gestion gracieuse de l'arrêt
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Signal ${signal} reçu, arrêt gracieux en cours...`);
      
      server.close(async () => {
        try {
          if (devopsAgent) {
            await devopsAgent.shutdown();
          }
          if (communication) {
            await communication.disconnect();
          }
          if (memory) {
            await memory.close();
          }
          logger.info('Arrêt gracieux terminé');
          process.exit(0);
        } catch (error) {
          logger.error('Erreur lors de l\'arrêt gracieux', { error: error.message });
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Erreur lors du démarrage du serveur DevOps', { error: error.message });
    process.exit(1);
  }
}

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  logger.error('Exception non capturée', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promesse rejetée non gérée', { reason, promise });
  process.exit(1);
});

// Démarrer l'application
startServer().catch((error) => {
  logger.error('Erreur fatale lors du démarrage DevOps', { error: error.message });
  process.exit(1);
});

export { app, devopsAgent, memory, communication };
