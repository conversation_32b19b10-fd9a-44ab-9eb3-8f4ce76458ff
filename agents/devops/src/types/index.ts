/**
 * Types pour l'Agent DevOps
 */

// Types de base pour les déploiements
export interface DeploymentRequest {
  id: string;
  applicationName: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
  platform: DeploymentPlatform;
  source: DeploymentSource;
  configuration: DeploymentConfiguration;
  metadata: DeploymentMetadata;
}

export interface DeploymentPlatform {
  type: 'kubernetes' | 'docker' | 'vercel' | 'netlify' | 'aws' | 'gcp' | 'azure';
  region?: string;
  cluster?: string;
  namespace?: string;
  credentials: PlatformCredentials;
}

export interface PlatformCredentials {
  apiKey?: string;
  secretKey?: string;
  token?: string;
  kubeconfig?: string;
  serviceAccount?: string;
  [key: string]: any;
}

export interface DeploymentSource {
  type: 'git' | 'archive' | 'docker' | 'generated-code';
  repository?: string;
  branch?: string;
  tag?: string;
  path?: string;
  dockerfile?: string;
  buildContext?: string;
  generatedCode?: GeneratedCodeSource;
}

export interface GeneratedCodeSource {
  agentId: string;
  codeId: string;
  framework: string;
  files: CodeFile[];
  dependencies: Dependencies;
  buildCommands: string[];
}

export interface CodeFile {
  path: string;
  content: string;
  type: 'source' | 'config' | 'asset' | 'test';
}

export interface Dependencies {
  production: Record<string, string>;
  development: Record<string, string>;
  peer?: Record<string, string>;
}

export interface DeploymentConfiguration {
  replicas?: number;
  resources?: ResourceRequirements;
  environment: Record<string, string>;
  secrets?: Record<string, string>;
  volumes?: VolumeMount[];
  networking?: NetworkingConfig;
  scaling?: ScalingConfig;
  monitoring?: MonitoringConfig;
  backup?: BackupConfig;
}

export interface ResourceRequirements {
  cpu: {
    request: string;
    limit: string;
  };
  memory: {
    request: string;
    limit: string;
  };
  storage?: {
    size: string;
    class?: string;
  };
}

export interface VolumeMount {
  name: string;
  mountPath: string;
  type: 'configMap' | 'secret' | 'persistentVolume' | 'emptyDir';
  source?: string;
}

export interface NetworkingConfig {
  ports: Port[];
  ingress?: IngressConfig;
  service?: ServiceConfig;
  loadBalancer?: LoadBalancerConfig;
}

export interface Port {
  name: string;
  port: number;
  targetPort: number;
  protocol: 'TCP' | 'UDP';
}

export interface IngressConfig {
  enabled: boolean;
  hostname: string;
  path?: string;
  tls?: boolean;
  annotations?: Record<string, string>;
}

export interface ServiceConfig {
  type: 'ClusterIP' | 'NodePort' | 'LoadBalancer';
  annotations?: Record<string, string>;
}

export interface LoadBalancerConfig {
  type: 'application' | 'network';
  scheme: 'internet-facing' | 'internal';
  subnets?: string[];
}

export interface ScalingConfig {
  enabled: boolean;
  minReplicas: number;
  maxReplicas: number;
  targetCPU?: number;
  targetMemory?: number;
  metrics?: ScalingMetric[];
}

export interface ScalingMetric {
  type: 'cpu' | 'memory' | 'custom';
  target: number;
  metric?: string;
}

export interface MonitoringConfig {
  enabled: boolean;
  metrics?: MetricsConfig;
  logging?: LoggingConfig;
  alerting?: AlertingConfig;
  healthChecks?: HealthCheckConfig;
}

export interface MetricsConfig {
  prometheus?: boolean;
  grafana?: boolean;
  customMetrics?: CustomMetric[];
}

export interface CustomMetric {
  name: string;
  type: 'counter' | 'gauge' | 'histogram';
  description: string;
  labels?: string[];
}

export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  outputs: LogOutput[];
}

export interface LogOutput {
  type: 'console' | 'file' | 'elasticsearch' | 'loki';
  configuration?: Record<string, any>;
}

export interface AlertingConfig {
  enabled: boolean;
  rules: AlertRule[];
  channels: AlertChannel[];
}

export interface AlertRule {
  name: string;
  condition: string;
  severity: 'critical' | 'warning' | 'info';
  duration: string;
  message: string;
}

export interface AlertChannel {
  type: 'email' | 'slack' | 'webhook' | 'pagerduty';
  configuration: Record<string, any>;
}

export interface HealthCheckConfig {
  liveness?: HealthCheck;
  readiness?: HealthCheck;
  startup?: HealthCheck;
}

export interface HealthCheck {
  path: string;
  port: number;
  initialDelaySeconds: number;
  periodSeconds: number;
  timeoutSeconds: number;
  failureThreshold: number;
}

export interface BackupConfig {
  enabled: boolean;
  schedule: string;
  retention: string;
  storage: BackupStorage;
}

export interface BackupStorage {
  type: 's3' | 'gcs' | 'azure' | 'local';
  bucket?: string;
  path?: string;
  credentials?: Record<string, string>;
}

export interface DeploymentMetadata {
  requestedBy: string;
  requestedAt: Date;
  sourceAgent: string;
  correlationId?: string;
  tags: Record<string, string>;
  description?: string;
}

// Types pour les résultats de déploiement
export interface DeploymentResult {
  id: string;
  status: DeploymentStatus;
  url?: string;
  endpoints: DeploymentEndpoint[];
  resources: DeployedResource[];
  metrics: DeploymentMetrics;
  logs: DeploymentLog[];
  error?: DeploymentError;
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
}

export type DeploymentStatus = 
  | 'pending'
  | 'building'
  | 'deploying'
  | 'running'
  | 'failed'
  | 'stopped'
  | 'scaling'
  | 'updating';

export interface DeploymentEndpoint {
  name: string;
  url: string;
  type: 'web' | 'api' | 'admin' | 'metrics';
  status: 'healthy' | 'unhealthy' | 'unknown';
}

export interface DeployedResource {
  type: 'pod' | 'service' | 'ingress' | 'configmap' | 'secret' | 'pvc';
  name: string;
  namespace?: string;
  status: string;
  created: Date;
  metadata?: Record<string, any>;
}

export interface DeploymentMetrics {
  cpu: ResourceMetric;
  memory: ResourceMetric;
  network: NetworkMetric;
  requests: RequestMetric;
  errors: ErrorMetric;
}

export interface ResourceMetric {
  current: number;
  average: number;
  peak: number;
  unit: string;
}

export interface NetworkMetric {
  bytesIn: number;
  bytesOut: number;
  connectionsActive: number;
  connectionsTotal: number;
}

export interface RequestMetric {
  total: number;
  rate: number;
  latencyP50: number;
  latencyP95: number;
  latencyP99: number;
}

export interface ErrorMetric {
  total: number;
  rate: number;
  types: Record<string, number>;
}

export interface DeploymentLog {
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  source: string;
  message: string;
  metadata?: Record<string, any>;
}

export interface DeploymentError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
  recoverable: boolean;
}

// Types pour l'infrastructure
export interface InfrastructureRequest {
  id: string;
  type: 'create' | 'update' | 'destroy';
  provider: 'aws' | 'gcp' | 'azure' | 'kubernetes';
  resources: InfrastructureResource[];
  configuration: InfrastructureConfiguration;
  metadata: InfrastructureMetadata;
}

export interface InfrastructureResource {
  type: string;
  name: string;
  configuration: Record<string, any>;
  dependencies?: string[];
  tags?: Record<string, string>;
}

export interface InfrastructureConfiguration {
  region: string;
  environment: string;
  terraform?: TerraformConfig;
  kubernetes?: KubernetesConfig;
  monitoring?: boolean;
  backup?: boolean;
}

export interface TerraformConfig {
  version: string;
  backend: TerraformBackend;
  providers: TerraformProvider[];
  variables: Record<string, any>;
}

export interface TerraformBackend {
  type: 's3' | 'gcs' | 'azurerm' | 'local';
  configuration: Record<string, any>;
}

export interface TerraformProvider {
  name: string;
  version: string;
  configuration: Record<string, any>;
}

export interface KubernetesConfig {
  version: string;
  cluster: string;
  namespace: string;
  manifests: KubernetesManifest[];
}

export interface KubernetesManifest {
  apiVersion: string;
  kind: string;
  metadata: Record<string, any>;
  spec: Record<string, any>;
}

export interface InfrastructureMetadata {
  requestedBy: string;
  requestedAt: Date;
  description?: string;
  tags: Record<string, string>;
}

// Types pour les pipelines CI/CD
export interface Pipeline {
  id: string;
  name: string;
  type: 'build' | 'test' | 'deploy' | 'full';
  stages: PipelineStage[];
  triggers: PipelineTrigger[];
  configuration: PipelineConfiguration;
  metadata: PipelineMetadata;
}

export interface PipelineStage {
  name: string;
  type: 'build' | 'test' | 'security' | 'deploy' | 'notify';
  steps: PipelineStep[];
  conditions?: PipelineCondition[];
  parallel?: boolean;
  timeout?: number;
}

export interface PipelineStep {
  name: string;
  action: string;
  parameters: Record<string, any>;
  retries?: number;
  timeout?: number;
  continueOnError?: boolean;
}

export interface PipelineCondition {
  type: 'branch' | 'tag' | 'environment' | 'manual';
  value: string;
  operator: 'equals' | 'contains' | 'matches';
}

export interface PipelineTrigger {
  type: 'webhook' | 'schedule' | 'manual' | 'agent';
  configuration: Record<string, any>;
  enabled: boolean;
}

export interface PipelineConfiguration {
  environment: Record<string, string>;
  secrets: string[];
  artifacts: ArtifactConfig[];
  notifications: NotificationConfig[];
}

export interface ArtifactConfig {
  name: string;
  path: string;
  type: 'build' | 'test' | 'deployment';
  retention: string;
}

export interface NotificationConfig {
  type: 'email' | 'slack' | 'webhook';
  events: string[];
  configuration: Record<string, any>;
}

export interface PipelineMetadata {
  createdBy: string;
  createdAt: Date;
  version: string;
  description?: string;
  tags: Record<string, string>;
}

// Types pour la communication avec les autres agents
export interface AgentMessage {
  id: string;
  type: 'request' | 'response' | 'notification';
  from: string;
  to: string;
  payload: any;
  timestamp: Date;
  correlationId?: string;
}

export interface AgentConfig {
  id: string;
  name: string;
  type: 'devops';
  version: string;
  capabilities: string[];
  endpoints: Record<string, string>;
  memory: {
    store: string;
    collections: string[];
  };
  communication: {
    kafka: {
      topics: string[];
      groupId: string;
    };
    redis: {
      channels: string[];
    };
  };
  deployment: {
    supportedPlatforms: string[];
    defaultPlatform: string;
    infrastructureProviders: string[];
    pipelineTypes: string[];
  };
}

// Types pour le monitoring et les métriques
export interface MonitoringData {
  timestamp: Date;
  deploymentId: string;
  metrics: {
    performance: PerformanceMetrics;
    availability: AvailabilityMetrics;
    security: SecurityMetrics;
    cost: CostMetrics;
  };
}

export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
}

export interface AvailabilityMetrics {
  uptime: number;
  downtime: number;
  incidents: number;
  mttr: number; // Mean Time To Recovery
  mtbf: number; // Mean Time Between Failures
}

export interface SecurityMetrics {
  vulnerabilities: SecurityVulnerability[];
  complianceScore: number;
  securityEvents: number;
  accessAttempts: number;
}

export interface SecurityVulnerability {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: string;
  description: string;
  component: string;
  fixed: boolean;
}

export interface CostMetrics {
  totalCost: number;
  costPerRequest: number;
  resourceCosts: ResourceCost[];
  optimization: CostOptimization[];
}

export interface ResourceCost {
  resource: string;
  cost: number;
  usage: number;
  efficiency: number;
}

export interface CostOptimization {
  type: string;
  description: string;
  potentialSavings: number;
  effort: 'low' | 'medium' | 'high';
}
