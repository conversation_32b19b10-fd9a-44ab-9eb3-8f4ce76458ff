# Agent Documentation 📚

Agent spécialisé dans la génération automatique de documentation technique, guides utilisateur et spécifications pour l'écosystème d'agents distribués.

## 🎯 Fonctionnalités

### Génération Automatique
- **Documentation API** : Génération automatique depuis OpenAPI/Swagger
- **Guides Utilisateur** : Création de guides interactifs avec captures d'écran
- **Spécifications Techniques** : Documentation d'architecture et de conception
- **Documentation de Code** : Extraction automatique depuis les commentaires
- **Guides d'Installation** : Procédures d'installation automatisées
- **Guides de Dépannage** : Solutions aux problèmes courants

### Formats Multiples
- **Markdown** : Format standard pour la documentation technique
- **HTML** : Documentation web interactive
- **PDF** : Documents imprimables et partageables
- **DOCX** : Documents Microsoft Word
- **Confluence** : Intégration avec Atlassian Confluence
- **Gitea Wiki** : Publication directe sur Gitea

### Intégrations
- **Gitea** : Publication automatique sur les wikis
- **API Analysis** : Analyse automatique des endpoints
- **Code Scanning** : Extraction de documentation depuis le code
- **Screenshot Capture** : Captures d'écran automatiques avec Puppeteer

## 🏗️ Architecture

### Engines Spécialisés
- **APIDocumentationEngine** : Documentation d'API REST/GraphQL
- **UserGuideEngine** : Guides utilisateur interactifs
- **TechnicalSpecEngine** : Spécifications techniques détaillées
- **CodeDocumentationEngine** : Documentation de code source
- **TemplateEngine** : Système de templates personnalisables
- **PublishingEngine** : Publication multi-plateforme

### Communication Synaptique
- **KafkaCommunication** : Coordination avec autres agents
- **WeaviateMemory** : Stockage de la documentation générée

## 📊 API Endpoints

### Génération de Documentation
```bash
# Générer documentation API
POST /documentation/generate
{
  "type": "api_documentation",
  "source": {
    "type": "api",
    "location": "http://localhost:3000/api-docs"
  },
  "target": {
    "format": "html",
    "language": "fr",
    "audience": "developer"
  }
}

# Générer guide utilisateur
POST /documentation/generate
{
  "type": "user_guide",
  "source": {
    "type": "codebase",
    "location": "/path/to/app"
  },
  "target": {
    "format": "pdf",
    "language": "fr",
    "audience": "end_user"
  },
  "options": {
    "includeScreenshots": true,
    "includeExamples": true
  }
}

# Générer spécification technique
POST /documentation/generate
{
  "type": "technical_specification",
  "source": {
    "type": "codebase",
    "location": "/path/to/project"
  },
  "target": {
    "format": "markdown",
    "language": "fr",
    "audience": "developer"
  },
  "options": {
    "includeDiagrams": true,
    "includeCodeSamples": true
  }
}
```

### Gestion de Documentation
```bash
# Mettre à jour documentation
PUT /documentation/{id}
{
  "content": "Updated content",
  "version": "2.0.0"
}

# Rechercher documentation
GET /documentation/search?q=authentication&format=api

# Publier documentation
POST /documentation/{id}/publish
{
  "platforms": [
    {
      "type": "gitea",
      "config": {
        "repository": "docs",
        "path": "api/"
      }
    }
  ]
}
```

### Templates et Styles
```bash
# Créer template personnalisé
POST /templates
{
  "name": "Company API Template",
  "type": "api_documentation",
  "format": "html",
  "sections": [
    {
      "name": "overview",
      "title": "Vue d'ensemble",
      "required": true
    }
  ]
}

# Appliquer style personnalisé
POST /documentation/generate
{
  "target": {
    "style": {
      "template": "company-template",
      "branding": {
        "logo": "/assets/logo.png",
        "colors": {
          "primary": "#007bff",
          "secondary": "#6c757d"
        }
      }
    }
  }
}
```

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- Puppeteer (pour captures d'écran)
- Gitea (optionnel, pour publication)

### Variables d'Environnement
```bash
# Port du serveur
PORT=3011

# Configuration Kafka
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=agent-documentation
KAFKA_GROUP_ID=documentation-group

# Configuration Weaviate
WEAVIATE_SCHEME=http
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080

# Configuration Gitea
GITEA_URL=http://localhost:3000
GITEA_TOKEN=your_gitea_token
GITEA_ORGANIZATION=your_org

# Logging
LOG_LEVEL=info
```

### Installation
```bash
# Installer les dépendances
npm install

# Compiler TypeScript
npm run build

# Démarrer en développement
npm run dev

# Démarrer en production
npm start
```

## 🔧 Configuration

### Types de Documentation Supportés
- **api_documentation** : Documentation d'API REST/GraphQL
- **user_guide** : Guides utilisateur avec captures d'écran
- **technical_specification** : Spécifications techniques détaillées
- **installation_guide** : Guides d'installation pas-à-pas
- **troubleshooting_guide** : Guides de résolution de problèmes
- **changelog** : Historique des versions et changements
- **readme** : Documentation de projet README
- **architecture_diagram** : Diagrammes d'architecture
- **code_documentation** : Documentation de code source
- **deployment_guide** : Guides de déploiement

### Formats de Sortie
- **markdown** : Format Markdown standard
- **html** : HTML avec CSS intégré
- **pdf** : Documents PDF
- **docx** : Documents Microsoft Word
- **confluence** : Format Confluence
- **gitea_wiki** : Format Wiki Gitea
- **swagger** : Spécification Swagger/OpenAPI
- **openapi** : Spécification OpenAPI 3.0

### Audiences Cibles
- **developer** : Développeurs et équipes techniques
- **end_user** : Utilisateurs finaux de l'application
- **administrator** : Administrateurs système
- **business_user** : Utilisateurs métier
- **technical_writer** : Rédacteurs techniques
- **stakeholder** : Parties prenantes du projet

## 📈 Fonctionnalités Avancées

### Génération Intelligente
- **Analyse de Code** : Extraction automatique de la documentation
- **Détection d'API** : Identification automatique des endpoints
- **Génération de Diagrammes** : Création automatique de diagrammes
- **Captures d'Écran** : Screenshots automatiques avec annotations

### Templates Personnalisables
- **Système de Templates** : Templates Handlebars personnalisables
- **Styles CSS** : Personnalisation complète de l'apparence
- **Branding** : Intégration de logos et couleurs d'entreprise
- **Variables Dynamiques** : Insertion automatique de métadonnées

### Publication Automatique
- **Multi-Plateforme** : Publication simultanée sur plusieurs plateformes
- **Versioning** : Gestion automatique des versions
- **Notifications** : Alertes de publication par email/Slack
- **Synchronisation** : Mise à jour automatique lors de changements

### Qualité et Validation
- **Vérification de Liens** : Validation des liens internes/externes
- **Analyse de Lisibilité** : Score de lisibilité automatique
- **Détection d'Incohérences** : Identification des problèmes
- **Métriques de Qualité** : Évaluation automatique de la qualité

## 🔗 Intégrations

### Agents Connectés
- **Agent DevOps** : Documentation de déploiement
- **Agent QA** : Documentation de tests
- **Agent Security** : Documentation de sécurité
- **Agent Backend** : Documentation d'API
- **Agent Frontend** : Guides utilisateur

### Outils Externes
- **Gitea** : Publication sur wikis et repositories
- **Confluence** : Intégration Atlassian
- **Swagger/OpenAPI** : Import/export de spécifications
- **Puppeteer** : Captures d'écran automatiques
- **JSDoc** : Extraction de documentation JavaScript

## 🛡️ Sécurité

### Contrôle d'Accès
- Authentification pour publication
- Autorisation basée sur les rôles
- Audit des modifications
- Chiffrement des tokens d'accès

### Validation de Contenu
- Sanitisation du contenu HTML
- Validation des templates
- Vérification des liens externes
- Protection contre l'injection de code

## 📚 Exemples d'Usage

### Documentation API Complète
```javascript
const docRequest = {
  type: 'api_documentation',
  source: {
    type: 'api',
    location: 'http://localhost:3000/api-docs'
  },
  target: {
    format: 'html',
    language: 'fr',
    audience: 'developer',
    style: {
      template: 'modern-api',
      branding: {
        logo: '/assets/company-logo.png',
        colors: {
          primary: '#007bff',
          secondary: '#6c757d'
        }
      }
    },
    output: {
      destination: '/docs/api',
      filename: 'api-documentation',
      publishing: {
        autoPublish: true,
        platforms: [
          {
            type: 'gitea',
            config: {
              repository: 'documentation',
              path: 'api/'
            }
          }
        ]
      }
    }
  },
  options: {
    includeExamples: true,
    includeCodeSamples: true,
    autoUpdate: true
  }
};
```

### Guide Utilisateur avec Screenshots
```javascript
const guideRequest = {
  type: 'user_guide',
  source: {
    type: 'codebase',
    location: '/path/to/frontend'
  },
  target: {
    format: 'pdf',
    language: 'fr',
    audience: 'end_user'
  },
  options: {
    includeScreenshots: true,
    includeExamples: true,
    interactive: true
  }
};
```

## 🤝 Contribution

### Standards de Documentation
- Markdown avec extensions GitHub
- Templates Handlebars pour la personnalisation
- CSS moderne pour le styling
- Validation automatique de la qualité

### Processus de Contribution
1. Fork du repository
2. Création d'une branche feature
3. Développement avec tests
4. Documentation des changements
5. Pull request avec review

## 📄 Licence

MIT License - Voir le fichier LICENSE pour plus de détails.

---

**Agent Documentation** - Génération automatique de documentation technique pour l'écosystème d'agents distribués Retreat And Be.
