
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for engines</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> engines</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.27% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>121/215</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.86% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>29/51</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.26% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>33/53</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.85% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>112/197</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="NeuroplasticityEngine.ts"><a href="NeuroplasticityEngine.ts.html">NeuroplasticityEngine.ts</a></td>
	<td data-value="56.27" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 56%"></div><div class="cover-empty" style="width: 44%"></div></div>
	</td>
	<td data-value="56.27" class="pct medium">56.27%</td>
	<td data-value="215" class="abs medium">121/215</td>
	<td data-value="56.86" class="pct medium">56.86%</td>
	<td data-value="51" class="abs medium">29/51</td>
	<td data-value="62.26" class="pct medium">62.26%</td>
	<td data-value="53" class="abs medium">33/53</td>
	<td data-value="56.85" class="pct medium">56.85%</td>
	<td data-value="197" class="abs medium">112/197</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-25T12:25:11.004Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    