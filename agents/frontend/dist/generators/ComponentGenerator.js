"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComponentGenerator = void 0;
/**
 * Générateur de Composants React/Vue
 *
 * Génère des composants UI optimisés basés sur le design system
 * et les spécifications d'accessibilité.
 */
class ComponentGenerator {
    constructor(logger, framework) {
        this.logger = logger;
        this.framework = framework;
    }
    /**
     * Génère un composant de base
     */
    async generateComponent(componentName, designSystem, componentSpec, request) {
        this.logger.info(`Génération du composant ${componentName}`, { framework: this.framework });
        const extension = request.typescript ? 'tsx' : 'jsx';
        const content = await this.generateComponentContent(componentName, designSystem, componentSpec, request);
        return {
            path: `src/components/${componentName}/${componentName}.${extension}`,
            content,
            type: 'component',
            language: request.typescript ? 'typescript' : 'javascript',
            size: content.length,
            dependencies: this.extractDependencies(content),
            exports: [componentName],
            imports: this.extractImports(content)
        };
    }
    /**
     * Génère un composant spécialisé pour Retreat And Be
     */
    async generateSpecializedComponent(componentName, design, request) {
        this.logger.info(`Génération du composant spécialisé ${componentName}`);
        const extension = request.typescript ? 'tsx' : 'jsx';
        let content;
        switch (componentName) {
            case 'RetreatCard':
                content = await this.generateRetreatCard(design, request);
                break;
            case 'BookingForm':
                content = await this.generateBookingForm(design, request);
                break;
            case 'FilterPanel':
                content = await this.generateFilterPanel(design, request);
                break;
            case 'ReviewComponent':
                content = await this.generateReviewComponent(design, request);
                break;
            case 'PartnerProfile':
                content = await this.generatePartnerProfile(design, request);
                break;
            case 'SearchBar':
                content = await this.generateSearchBar(design, request);
                break;
            case 'PricingCard':
                content = await this.generatePricingCard(design, request);
                break;
            case 'TrustSignals':
                content = await this.generateTrustSignals(design, request);
                break;
            default:
                content = await this.generateGenericComponent(componentName, design, request);
        }
        return {
            path: `src/components/${componentName}/${componentName}.${extension}`,
            content,
            type: 'component',
            language: request.typescript ? 'typescript' : 'javascript',
            size: content.length,
            dependencies: this.extractDependencies(content),
            exports: [componentName],
            imports: this.extractImports(content)
        };
    }
    /**
     * Génère le contenu d'un composant de base
     */
    async generateComponentContent(componentName, designSystem, componentSpec, request) {
        const isTypeScript = request.typescript;
        const colors = designSystem.colorSystem;
        const typography = designSystem.typography;
        switch (componentName) {
            case 'Button':
                return this.generateButtonComponent(colors, typography, request);
            case 'Input':
                return this.generateInputComponent(colors, typography, request);
            case 'Card':
                return this.generateCardComponent(colors, typography, request);
            case 'Modal':
                return this.generateModalComponent(colors, typography, request);
            case 'Navigation':
                return this.generateNavigationComponent(colors, typography, request);
            case 'Header':
                return this.generateHeaderComponent(colors, typography, request);
            case 'Footer':
                return this.generateFooterComponent(colors, typography, request);
            case 'Layout':
                return this.generateLayoutComponent(colors, typography, request);
            default:
                return this.generateGenericComponentContent(componentName, designSystem, request);
        }
    }
    /**
     * Génère un composant Button optimisé
     */
    generateButtonComponent(colors, typography, request) {
        const isTypeScript = request.typescript;
        const styling = request.styling;
        const typeDefinitions = isTypeScript ? `
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  'aria-label'?: string;
}` : '';
        const styledComponent = styling === 'styled-components' ? `
import styled from 'styled-components';

const StyledButton = styled.button<{ variant: string; size: string }>\`
  /* Styles basés sur le design system */
  font-family: ${typography.fontPairings.body};
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  \${props => props.variant === 'primary' && \`
    background-color: ${colors.primary[4]};
    color: white;
    &:hover { background-color: ${colors.primary[5]}; }
  \`}
  
  \${props => props.size === 'md' && \`
    padding: 12px 24px;
    font-size: 16px;
  \`}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &:focus {
    outline: 2px solid ${colors.primary[3]};
    outline-offset: 2px;
  }
\`;` : '';
        const cssClasses = styling === 'tailwind' ? 'className={`btn btn-\${variant} btn-\${size} \${className || ""}`}' :
            styling === 'styled-components' ? '' :
                'className={`button button--\${variant} button--\${size} \${className || ""}`}';
        return `import React from 'react';
${styling === 'styled-components' ? styledComponent : ''}
${!isTypeScript ? '' : typeDefinitions}

${isTypeScript ? 'const Button: React.FC<ButtonProps> = ({' : 'const Button = ({'}
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  className,
  'aria-label': ariaLabel,
  ...props
${isTypeScript ? '}) => {' : '}) => {'}
  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  return (
    ${styling === 'styled-components' ?
            `<StyledButton
        variant={variant}
        size={size}
        disabled={disabled || loading}
        onClick={handleClick}
        type={type}
        aria-label={ariaLabel}
        {...props}
      >` :
            `<button
        ${cssClasses}
        disabled={disabled || loading}
        onClick={handleClick}
        type={type}
        aria-label={ariaLabel}
        {...props}
      >`}
      {loading ? (
        <span className="loading-spinner" aria-hidden="true">⏳</span>
      ) : null}
      {children}
    ${styling === 'styled-components' ? '</StyledButton>' : '</button>'}
  );
};

export default Button;`;
    }
    /**
     * Génère un composant RetreatCard spécialisé
     */
    async generateRetreatCard(design, request) {
        const isTypeScript = request.typescript;
        const colors = design.designSystem.colorSystem;
        const typeDefinitions = isTypeScript ? `
interface Retreat {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  duration: number;
  rating: number;
  reviewCount: number;
  images: string[];
  tags: string[];
  partner: {
    name: string;
    verified: boolean;
  };
  availability: {
    startDate: string;
    endDate: string;
    spotsLeft: number;
  };
}

interface RetreatCardProps {
  retreat: Retreat;
  onBookNow?: (retreatId: string) => void;
  onViewDetails?: (retreatId: string) => void;
  className?: string;
}` : '';
        return `import React from 'react';
import Button from '../Button/Button';
import TrustSignals from '../TrustSignals/TrustSignals';
${!isTypeScript ? '' : typeDefinitions}

${isTypeScript ? 'const RetreatCard: React.FC<RetreatCardProps> = ({' : 'const RetreatCard = ({'}
  retreat,
  onBookNow,
  onViewDetails,
  className = ''
${isTypeScript ? '}) => {' : '}) => {'}
  const handleBookNow = () => {
    if (onBookNow) {
      onBookNow(retreat.id);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(retreat.id);
    }
  };

  return (
    <article 
      className={\`retreat-card \${className}\`}
      role="article"
      aria-labelledby={\`retreat-title-\${retreat.id}\`}
    >
      {/* Image principale */}
      <div className="retreat-card__image-container">
        <img
          src={retreat.images[0]}
          alt={\`Retraite \${retreat.title} à \${retreat.location}\`}
          className="retreat-card__image"
          loading="lazy"
        />
        {retreat.availability.spotsLeft <= 3 && (
          <div className="retreat-card__urgency-badge" role="status" aria-live="polite">
            Plus que {retreat.availability.spotsLeft} places !
          </div>
        )}
      </div>

      {/* Contenu */}
      <div className="retreat-card__content">
        <header className="retreat-card__header">
          <h3 
            id={\`retreat-title-\${retreat.id}\`}
            className="retreat-card__title"
          >
            {retreat.title}
          </h3>
          <div className="retreat-card__location">
            <span className="sr-only">Lieu :</span>
            📍 {retreat.location}
          </div>
        </header>

        <p className="retreat-card__description">
          {retreat.description}
        </p>

        {/* Tags */}
        <div className="retreat-card__tags" role="list">
          {retreat.tags.slice(0, 3).map((tag, index) => (
            <span 
              key={index}
              className="retreat-card__tag"
              role="listitem"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Métriques */}
        <div className="retreat-card__metrics">
          <div className="retreat-card__rating">
            <span className="sr-only">Note :</span>
            ⭐ {retreat.rating}/5
            <span className="retreat-card__review-count">
              ({retreat.reviewCount} avis)
            </span>
          </div>
          <div className="retreat-card__duration">
            <span className="sr-only">Durée :</span>
            🕐 {retreat.duration} jours
          </div>
        </div>

        {/* Signaux de confiance */}
        <TrustSignals 
          partner={retreat.partner}
          rating={retreat.rating}
          reviewCount={retreat.reviewCount}
        />

        {/* Prix et actions */}
        <footer className="retreat-card__footer">
          <div className="retreat-card__price">
            <span className="retreat-card__price-amount">
              {retreat.price}€
            </span>
            <span className="retreat-card__price-period">
              / personne
            </span>
          </div>
          
          <div className="retreat-card__actions">
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewDetails}
              aria-label={\`Voir les détails de la retraite \${retreat.title}\`}
            >
              Détails
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={handleBookNow}
              aria-label={\`Réserver la retraite \${retreat.title}\`}
            >
              Réserver
            </Button>
          </div>
        </footer>
      </div>
    </article>
  );
};

export default RetreatCard;`;
    }
    /**
     * Génère un composant BookingForm spécialisé
     */
    async generateBookingForm(design, request) {
        const isTypeScript = request.typescript;
        const typeDefinitions = isTypeScript ? `
interface BookingFormData {
  retreatId: string;
  guestCount: number;
  startDate: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  specialRequests?: string;
  dietaryRestrictions?: string[];
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
}

interface BookingFormProps {
  retreatId: string;
  onSubmit: (data: BookingFormData) => void;
  onCancel?: () => void;
  loading?: boolean;
  className?: string;
}` : '';
        return `import React, { useState } from 'react';
import Button from '../Button/Button';
import Input from '../Input/Input';
${!isTypeScript ? '' : typeDefinitions}

${isTypeScript ? 'const BookingForm: React.FC<BookingFormProps> = ({' : 'const BookingForm = ({'}
  retreatId,
  onSubmit,
  onCancel,
  loading = false,
  className = ''
${isTypeScript ? '}) => {' : '}) => {'}
  const [formData, setFormData] = useState${isTypeScript ? '<BookingFormData>' : ''}({
    retreatId,
    guestCount: 1,
    startDate: '',
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
      phone: ''
    },
    specialRequests: '',
    dietaryRestrictions: [],
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    }
  });

  const [errors, setErrors] = useState${isTypeScript ? '<Record<string, string>>' : ''}({});
  const [currentStep, setCurrentStep] = useState(1);

  const validateStep = (step${isTypeScript ? ': number' : ''}) => {
    const newErrors${isTypeScript ? ': Record<string, string>' : ''} = {};

    switch (step) {
      case 1:
        if (!formData.guestCount || formData.guestCount < 1) {
          newErrors.guestCount = 'Nombre de participants requis';
        }
        if (!formData.startDate) {
          newErrors.startDate = 'Date de début requise';
        }
        break;
      case 2:
        if (!formData.personalInfo.firstName.trim()) {
          newErrors.firstName = 'Prénom requis';
        }
        if (!formData.personalInfo.lastName.trim()) {
          newErrors.lastName = 'Nom requis';
        }
        if (!formData.personalInfo.email.trim()) {
          newErrors.email = 'Email requis';
        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.personalInfo.email)) {
          newErrors.email = 'Email invalide';
        }
        break;
      case 3:
        if (!formData.emergencyContact.name.trim()) {
          newErrors.emergencyContactName = 'Nom du contact d\\'urgence requis';
        }
        if (!formData.emergencyContact.phone.trim()) {
          newErrors.emergencyContactPhone = 'Téléphone du contact d\\'urgence requis';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleSubmit = (e${isTypeScript ? ': React.FormEvent' : ''}) => {
    e.preventDefault();
    if (validateStep(3)) {
      onSubmit(formData);
    }
  };

  const updateFormData = (field${isTypeScript ? ': string' : ''}, value${isTypeScript ? ': any' : ''}) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updatePersonalInfo = (field${isTypeScript ? ': string' : ''}, value${isTypeScript ? ': string' : ''}) => {
    setFormData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value
      }
    }));
  };

  return (
    <form 
      className={\`booking-form \${className}\`}
      onSubmit={handleSubmit}
      noValidate
    >
      {/* Indicateur de progression */}
      <div className="booking-form__progress" role="progressbar" aria-valuenow={currentStep} aria-valuemin={1} aria-valuemax={3}>
        <div className="booking-form__progress-bar">
          <div 
            className="booking-form__progress-fill"
            style={{ width: \`\${(currentStep / 3) * 100}%\` }}
          />
        </div>
        <span className="booking-form__progress-text">
          Étape {currentStep} sur 3
        </span>
      </div>

      {/* Étape 1: Détails de la réservation */}
      {currentStep === 1 && (
        <fieldset className="booking-form__step">
          <legend className="booking-form__step-title">Détails de la réservation</legend>
          
          <Input
            label="Nombre de participants"
            type="number"
            min="1"
            max="10"
            value={formData.guestCount}
            onChange={(value) => updateFormData('guestCount', parseInt(value) || 1)}
            error={errors.guestCount}
            required
          />

          <Input
            label="Date de début"
            type="date"
            value={formData.startDate}
            onChange={(value) => updateFormData('startDate', value)}
            error={errors.startDate}
            required
          />
        </fieldset>
      )}

      {/* Étape 2: Informations personnelles */}
      {currentStep === 2 && (
        <fieldset className="booking-form__step">
          <legend className="booking-form__step-title">Informations personnelles</legend>
          
          <div className="booking-form__row">
            <Input
              label="Prénom"
              value={formData.personalInfo.firstName}
              onChange={(value) => updatePersonalInfo('firstName', value)}
              error={errors.firstName}
              required
            />
            <Input
              label="Nom"
              value={formData.personalInfo.lastName}
              onChange={(value) => updatePersonalInfo('lastName', value)}
              error={errors.lastName}
              required
            />
          </div>

          <Input
            label="Email"
            type="email"
            value={formData.personalInfo.email}
            onChange={(value) => updatePersonalInfo('email', value)}
            error={errors.email}
            required
          />

          <Input
            label="Téléphone"
            type="tel"
            value={formData.personalInfo.phone}
            onChange={(value) => updatePersonalInfo('phone', value)}
          />
        </fieldset>
      )}

      {/* Étape 3: Contact d'urgence et préférences */}
      {currentStep === 3 && (
        <fieldset className="booking-form__step">
          <legend className="booking-form__step-title">Contact d'urgence et préférences</legend>
          
          <div className="booking-form__emergency-contact">
            <h4>Contact d'urgence</h4>
            <Input
              label="Nom du contact"
              value={formData.emergencyContact.name}
              onChange={(value) => setFormData(prev => ({
                ...prev,
                emergencyContact: { ...prev.emergencyContact, name: value }
              }))}
              error={errors.emergencyContactName}
              required
            />
            <Input
              label="Téléphone du contact"
              type="tel"
              value={formData.emergencyContact.phone}
              onChange={(value) => setFormData(prev => ({
                ...prev,
                emergencyContact: { ...prev.emergencyContact, phone: value }
              }))}
              error={errors.emergencyContactPhone}
              required
            />
          </div>

          <Input
            label="Demandes spéciales (optionnel)"
            type="textarea"
            value={formData.specialRequests}
            onChange={(value) => updateFormData('specialRequests', value)}
            placeholder="Allergies, besoins spéciaux, préférences..."
          />
        </fieldset>
      )}

      {/* Actions */}
      <div className="booking-form__actions">
        {currentStep > 1 && (
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            disabled={loading}
          >
            Précédent
          </Button>
        )}
        
        {onCancel && (
          <Button
            type="button"
            variant="ghost"
            onClick={onCancel}
            disabled={loading}
          >
            Annuler
          </Button>
        )}

        {currentStep < 3 ? (
          <Button
            type="button"
            variant="primary"
            onClick={handleNext}
            disabled={loading}
          >
            Suivant
          </Button>
        ) : (
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            disabled={loading}
          >
            Confirmer la réservation
          </Button>
        )}
      </div>
    </form>
  );
};

export default BookingForm;`;
    }
    // Méthodes utilitaires
    extractDependencies(content) {
        const importRegex = /import.*from ['"]([^'"]+)['"]/g;
        const dependencies = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const dep = match[1];
            if (!dep.startsWith('.') && !dep.startsWith('/')) {
                dependencies.push(dep);
            }
        }
        return [...new Set(dependencies)];
    }
    extractImports(content) {
        const importRegex = /import\s+(?:{([^}]+)}|(\w+))\s+from/g;
        const imports = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            if (match[1]) {
                // Named imports
                imports.push(...match[1].split(',').map(imp => imp.trim()));
            }
            else if (match[2]) {
                // Default import
                imports.push(match[2]);
            }
        }
        return [...new Set(imports)];
    }
    // Méthodes de génération simplifiées (à implémenter complètement)
    generateInputComponent(colors, typography, request) {
        return `// Input component implementation`;
    }
    generateCardComponent(colors, typography, request) {
        return `// Card component implementation`;
    }
    generateModalComponent(colors, typography, request) {
        return `// Modal component implementation`;
    }
    generateNavigationComponent(colors, typography, request) {
        return `// Navigation component implementation`;
    }
    generateHeaderComponent(colors, typography, request) {
        return `// Header component implementation`;
    }
    generateFooterComponent(colors, typography, request) {
        return `// Footer component implementation`;
    }
    generateLayoutComponent(colors, typography, request) {
        return `// Layout component implementation`;
    }
    generateGenericComponentContent(componentName, designSystem, request) {
        return `// Generic ${componentName} component implementation`;
    }
    async generateFilterPanel(design, request) {
        return `// FilterPanel component implementation`;
    }
    async generateReviewComponent(design, request) {
        return `// ReviewComponent implementation`;
    }
    async generatePartnerProfile(design, request) {
        return `// PartnerProfile component implementation`;
    }
    async generateSearchBar(design, request) {
        return `// SearchBar component implementation`;
    }
    async generatePricingCard(design, request) {
        return `// PricingCard component implementation`;
    }
    async generateTrustSignals(design, request) {
        return `// TrustSignals component implementation`;
    }
    async generateGenericComponent(componentName, design, request) {
        return `// Generic ${componentName} component implementation`;
    }
}
exports.ComponentGenerator = ComponentGenerator;
//# sourceMappingURL=ComponentGenerator.js.map