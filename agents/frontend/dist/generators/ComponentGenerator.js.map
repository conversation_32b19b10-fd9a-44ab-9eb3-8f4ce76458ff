{"version": 3, "file": "ComponentGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/ComponentGenerator.ts"], "names": [], "mappings": ";;;AAQA;;;;;GAKG;AACH,MAAa,kBAAkB;IAI7B,YAAY,MAAc,EAAE,SAAiB;QAC3C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,aAAqB,EACrB,YAAkC,EAClC,aAAkB,EAClB,OAA8B;QAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAE5F,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAEzG,OAAO;YACL,IAAI,EAAE,kBAAkB,aAAa,IAAI,aAAa,IAAI,SAAS,EAAE;YACrE,OAAO;YACP,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY;YAC1D,IAAI,EAAE,OAAO,CAAC,MAAM;YACpB,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YAC/C,OAAO,EAAE,CAAC,aAAa,CAAC;YACxB,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,4BAA4B,CAChC,aAAqB,EACrB,MAA6B,EAC7B,OAA8B;QAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,aAAa,EAAE,CAAC,CAAC;QAExE,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,IAAI,OAAe,CAAC;QAEpB,QAAQ,aAAa,EAAE,CAAC;YACtB,KAAK,aAAa;gBAChB,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,aAAa;gBAChB,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,aAAa;gBAChB,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,iBAAiB;gBACpB,OAAO,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,gBAAgB;gBACnB,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC7D,MAAM;YACR,KAAK,WAAW;gBACd,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxD,MAAM;YACR,KAAK,aAAa;gBAChB,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,cAAc;gBACjB,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC3D,MAAM;YACR;gBACE,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAClF,CAAC;QAED,OAAO;YACL,IAAI,EAAE,kBAAkB,aAAa,IAAI,aAAa,IAAI,SAAS,EAAE;YACrE,OAAO;YACP,IAAI,EAAE,WAAW;YACjB,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY;YAC1D,IAAI,EAAE,OAAO,CAAC,MAAM;YACpB,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YAC/C,OAAO,EAAE,CAAC,aAAa,CAAC;YACxB,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,aAAqB,EACrB,YAAkC,EAClC,aAAkB,EAClB,OAA8B;QAE9B,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;QACxC,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAE3C,QAAQ,aAAa,EAAE,CAAC;YACtB,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACnE,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAClE,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACjE,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAClE,KAAK,YAAY;gBACf,OAAO,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACvE,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACnE,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACnE,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACnE;gBACE,OAAO,IAAI,CAAC,+BAA+B,CAAC,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QAC1F,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEhC,MAAM,eAAe,GAAG,YAAY,CAAC,CAAC,CAAC;;;;;;;;;;;EAWzC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEJ,MAAM,eAAe,GAAG,OAAO,KAAK,mBAAmB,CAAC,CAAC,CAAC;;;;;iBAK7C,UAAU,CAAC,YAAY,CAAC,IAAI;;;;;;;wBAOrB,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;;kCAEP,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;;;;;;;;;;;;;;yBAc1B,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;;;IAGtC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEN,MAAM,UAAU,GAAG,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,oEAAoE,CAAC,CAAC;YAChG,OAAO,KAAK,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACtC,+EAA+E,CAAC;QAElG,OAAO;EACT,OAAO,KAAK,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;EACtD,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe;;EAEpC,YAAY,CAAC,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC,mBAAmB;;;;;;;;;;;EAW/E,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;;;;;;;;MAQhC,OAAO,KAAK,mBAAmB,CAAC,CAAC;YACjC;;;;;;;;QAQE,CAAC,CAAC;YACJ;UACI,UAAU;;;;;;QAOhB;;;;;MAKE,OAAO,KAAK,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW;;;;uBAIhD,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAA6B,EAAE,OAA8B;QAC7F,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC;QAE/C,MAAM,eAAe,GAAG,YAAY,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BzC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEJ,OAAO;;;EAGT,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe;;EAEpC,YAAY,CAAC,CAAC,CAAC,oDAAoD,CAAC,CAAC,CAAC,wBAAwB;;;;;EAK9F,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BA2HV,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,MAA6B,EAAE,OAA8B;QAC7F,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;QAExC,MAAM,eAAe,GAAG,YAAY,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;EA0BzC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEJ,OAAO;;;EAGT,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,eAAe;;EAEpC,YAAY,CAAC,CAAC,CAAC,oDAAoD,CAAC,CAAC,CAAC,wBAAwB;;;;;;EAM9F,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;4CACM,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;wCAmB3C,YAAY,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE;;;8BAGxD,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;qBACvC,YAAY,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAgDxC,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;;;;;;;iCAOjC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;;;;;;qCAO/D,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAwL/E,CAAC;IAC3B,CAAC;IAED,uBAAuB;IACf,mBAAmB,CAAC,OAAe;QACzC,MAAM,WAAW,GAAG,gCAAgC,CAAC;QACrD,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IACpC,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,MAAM,WAAW,GAAG,sCAAsC,CAAC;QAC3D,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACb,gBAAgB;gBAChB,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpB,iBAAiB;gBACjB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,kEAAkE;IAC1D,sBAAsB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QACzF,OAAO,mCAAmC,CAAC;IAC7C,CAAC;IAEO,qBAAqB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QACxF,OAAO,kCAAkC,CAAC;IAC5C,CAAC;IAEO,sBAAsB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QACzF,OAAO,mCAAmC,CAAC;IAC7C,CAAC;IAEO,2BAA2B,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QAC9F,OAAO,wCAAwC,CAAC;IAClD,CAAC;IAEO,uBAAuB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QAC1F,OAAO,oCAAoC,CAAC;IAC9C,CAAC;IAEO,uBAAuB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QAC1F,OAAO,oCAAoC,CAAC;IAC9C,CAAC;IAEO,uBAAuB,CAAC,MAAW,EAAE,UAAe,EAAE,OAA8B;QAC1F,OAAO,oCAAoC,CAAC;IAC9C,CAAC;IAEO,+BAA+B,CAAC,aAAqB,EAAE,YAAkC,EAAE,OAA8B;QAC/H,OAAO,cAAc,aAAa,2BAA2B,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAA6B,EAAE,OAA8B;QAC7F,OAAO,yCAAyC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAA6B,EAAE,OAA8B;QACjG,OAAO,mCAAmC,CAAC;IAC7C,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAA6B,EAAE,OAA8B;QAChG,OAAO,4CAA4C,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAA6B,EAAE,OAA8B;QAC3F,OAAO,uCAAuC,CAAC;IACjD,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAA6B,EAAE,OAA8B;QAC7F,OAAO,yCAAyC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAA6B,EAAE,OAA8B;QAC9F,OAAO,0CAA0C,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,aAAqB,EAAE,MAA6B,EAAE,OAA8B;QACzH,OAAO,cAAc,aAAa,2BAA2B,CAAC;IAChE,CAAC;CACF;AAnzBD,gDAmzBC"}