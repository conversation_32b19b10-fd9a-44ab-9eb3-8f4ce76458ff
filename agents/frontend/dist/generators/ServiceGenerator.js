"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceGenerator = void 0;
/**
 * Générateur de Services
 *
 * Génère des services pour l'interaction avec les APIs,
 * la gestion des données et la logique métier.
 */
class ServiceGenerator {
    constructor(logger, framework) {
        this.logger = logger;
        this.framework = framework;
    }
    /**
     * Génère un service
     */
    async generateService(serviceName, design, request) {
        this.logger.info(`Génération du service ${serviceName}`, { framework: this.framework });
        const extension = request.typescript ? 'ts' : 'js';
        let content;
        switch (serviceName) {
            case 'apiService':
                content = await this.generateApiService(design, request);
                break;
            case 'authService':
                content = await this.generateAuthService(design, request);
                break;
            case 'retreatService':
                content = await this.generateRetreatService(design, request);
                break;
            case 'bookingService':
                content = await this.generateBookingService(design, request);
                break;
            case 'userService':
                content = await this.generateUserService(design, request);
                break;
            case 'analyticsService':
                content = await this.generateAnalyticsService(design, request);
                break;
            default:
                content = await this.generateGenericService(serviceName, design, request);
        }
        return {
            path: `src/services/${serviceName}.${extension}`,
            content,
            type: 'service',
            language: request.typescript ? 'typescript' : 'javascript',
            size: content.length,
            dependencies: this.extractDependencies(content),
            exports: [serviceName],
            imports: this.extractImports(content)
        };
    }
    /**
     * Génère le service API principal
     */
    async generateApiService(design, request) {
        const isTypeScript = request.typescript;
        const typeDefinitions = isTypeScript ? `
interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

interface RequestConfig {
  headers?: Record<string, string>;
  params?: Record<string, any>;
  timeout?: number;
  signal?: AbortSignal;
}` : '';
        return `import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
${typeDefinitions}

class ApiService {
  private client: AxiosInstance;
  private baseURL: string;

  constructor(config${isTypeScript ? ': Partial<ApiConfig>' : ''} = {}) {
    this.baseURL = config.baseURL || process.env.REACT_APP_API_URL || 'http://localhost:3000/api';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    });

    this.setupInterceptors();
  }

  /**
   * Configuration des intercepteurs pour la gestion automatique des erreurs et de l'auth
   */
  private setupInterceptors() {
    // Intercepteur de requête pour ajouter le token d'authentification
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = \`Bearer \${token}\`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Intercepteur de réponse pour la gestion des erreurs
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        // Gestion de l'expiration du token
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.post('/auth/refresh', { refreshToken });
              const { token } = response.data;
              localStorage.setItem('auth_token', token);
              originalRequest.headers.Authorization = \`Bearer \${token}\`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Rediriger vers la page de connexion
            localStorage.removeItem('auth_token');
            localStorage.removeItem('refresh_token');
            window.location.href = '/login';
          }
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * Gestion centralisée des erreurs
   */
  private handleError(error${isTypeScript ? ': any' : ''}): ApiError {
    if (error.response) {
      // Erreur de réponse du serveur
      return {
        message: error.response.data?.message || 'Erreur du serveur',
        status: error.response.status,
        code: error.response.data?.code,
        details: error.response.data?.details
      };
    } else if (error.request) {
      // Erreur de réseau
      return {
        message: 'Erreur de connexion au serveur',
        status: 0,
        code: 'NETWORK_ERROR'
      };
    } else {
      // Erreur de configuration
      return {
        message: error.message || 'Erreur inconnue',
        status: 0,
        code: 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * Requête GET
   */
  async get${isTypeScript ? '<T = any>' : ''}(
    url${isTypeScript ? ': string' : ''}, 
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<ApiResponse<T>>' : ''} {
    try {
      const response = await this.client.get(url, config);
      return this.formatResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Requête POST
   */
  async post${isTypeScript ? '<T = any>' : ''}(
    url${isTypeScript ? ': string' : ''}, 
    data${isTypeScript ? '?: any' : ''} = {}, 
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<ApiResponse<T>>' : ''} {
    try {
      const response = await this.client.post(url, data, config);
      return this.formatResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Requête PUT
   */
  async put${isTypeScript ? '<T = any>' : ''}(
    url${isTypeScript ? ': string' : ''}, 
    data${isTypeScript ? '?: any' : ''} = {}, 
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<ApiResponse<T>>' : ''} {
    try {
      const response = await this.client.put(url, data, config);
      return this.formatResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Requête PATCH
   */
  async patch${isTypeScript ? '<T = any>' : ''}(
    url${isTypeScript ? ': string' : ''}, 
    data${isTypeScript ? '?: any' : ''} = {}, 
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<ApiResponse<T>>' : ''} {
    try {
      const response = await this.client.patch(url, data, config);
      return this.formatResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Requête DELETE
   */
  async delete${isTypeScript ? '<T = any>' : ''}(
    url${isTypeScript ? ': string' : ''}, 
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<ApiResponse<T>>' : ''} {
    try {
      const response = await this.client.delete(url, config);
      return this.formatResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Upload de fichier
   */
  async upload${isTypeScript ? '<T = any>' : ''}(
    url${isTypeScript ? ': string' : ''}, 
    file${isTypeScript ? ': File' : ''}, 
    onProgress${isTypeScript ? '?: (progress: number) => void' : ''} = () => {},
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<ApiResponse<T>>' : ''} {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await this.client.post(url, formData, {
        ...config,
        headers: {
          'Content-Type': 'multipart/form-data',
          ...config.headers
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      });
      return this.formatResponse(response);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Téléchargement de fichier
   */
  async download(
    url${isTypeScript ? ': string' : ''}, 
    filename${isTypeScript ? '?: string' : ''}, 
    config${isTypeScript ? '?: RequestConfig' : ''} = {}
  )${isTypeScript ? ': Promise<void>' : ''} {
    try {
      const response = await this.client.get(url, {
        ...config,
        responseType: 'blob'
      });

      // Créer un lien de téléchargement
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Formatage de la réponse
   */
  private formatResponse${isTypeScript ? '<T>' : ''}(response${isTypeScript ? ': AxiosResponse<T>' : ''})${isTypeScript ? ': ApiResponse<T>' : ''} {
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    };
  }

  /**
   * Mise à jour de la configuration
   */
  updateConfig(config${isTypeScript ? ': Partial<ApiConfig>' : ''}) {
    if (config.baseURL) {
      this.client.defaults.baseURL = config.baseURL;
    }
    if (config.timeout) {
      this.client.defaults.timeout = config.timeout;
    }
    if (config.headers) {
      this.client.defaults.headers = {
        ...this.client.defaults.headers,
        ...config.headers
      };
    }
  }

  /**
   * Obtenir l'instance Axios pour des besoins avancés
   */
  getClient(): AxiosInstance {
    return this.client;
  }
}

// Instance singleton
export const apiService = new ApiService();
export default apiService;`;
    }
    /**
     * Génère le service de retraites
     */
    async generateRetreatService(design, request) {
        const isTypeScript = request.typescript;
        const typeDefinitions = isTypeScript ? `
interface Retreat {
  id: string;
  title: string;
  description: string;
  location: string;
  price: number;
  duration: number;
  rating: number;
  reviewCount: number;
  images: string[];
  tags: string[];
  partner: Partner;
  availability: Availability;
  amenities: string[];
  difficulty: string;
  included: string[];
  notIncluded: string[];
  schedule: DaySchedule[];
  cancellationPolicy: string;
  requirements: string[];
}

interface Partner {
  id: string;
  name: string;
  verified: boolean;
  rating: number;
  description: string;
  certifications: string[];
  experience: number;
}

interface Availability {
  startDate: string;
  endDate: string;
  spotsLeft: number;
  totalSpots: number;
  price: number;
  earlyBirdDiscount?: number;
}

interface DaySchedule {
  day: number;
  title: string;
  activities: Activity[];
}

interface Activity {
  time: string;
  title: string;
  description: string;
  duration: number;
  type: 'meditation' | 'yoga' | 'workshop' | 'meal' | 'free-time';
}

interface SearchParams {
  query?: string;
  filters?: SearchFilters;
  sortBy?: string;
  page?: number;
  limit?: number;
}

interface SearchFilters {
  location?: string;
  priceRange?: [number, number];
  duration?: number[];
  type?: string[];
  rating?: number;
  dates?: [string, string];
  amenities?: string[];
  difficulty?: string;
}

interface SearchResponse {
  results: Retreat[];
  totalCount: number;
  page: number;
  hasMore: boolean;
  filters: AvailableFilters;
}

interface AvailableFilters {
  locations: string[];
  types: string[];
  amenities: string[];
  priceRange: [number, number];
  durationRange: [number, number];
}` : '';
        return `import { apiService } from './apiService';
${typeDefinitions}

class RetreatService {
  private readonly basePath = '/retreats';

  /**
   * Recherche de retraites avec filtres
   */
  async search(params${isTypeScript ? ': SearchParams' : ''}, signal${isTypeScript ? '?: AbortSignal' : ''}): Promise<SearchResponse> {
    const queryParams = new URLSearchParams();
    
    if (params.query) queryParams.append('q', params.query);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    
    // Ajouter les filtres
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(\`filters[\${key}][]\`, v.toString()));
          } else {
            queryParams.append(\`filters[\${key}]\`, value.toString());
          }
        }
      });
    }

    const response = await apiService.get(\`\${this.basePath}/search?\${queryParams}\`, { signal });
    return response.data;
  }

  /**
   * Obtenir les retraites en vedette
   */
  async getFeatured(limit${isTypeScript ? ': number' : ''} = 6): Promise<Retreat[]> {
    const response = await apiService.get(\`\${this.basePath}/featured?limit=\${limit}\`);
    return response.data;
  }

  /**
   * Obtenir une retraite par ID
   */
  async getById(id${isTypeScript ? ': string' : ''}): Promise<Retreat> {
    const response = await apiService.get(\`\${this.basePath}/\${id}\`);
    return response.data;
  }

  /**
   * Obtenir les suggestions de recherche
   */
  async getSuggestions(query${isTypeScript ? ': string' : ''}): Promise<string[]> {
    const response = await apiService.get(\`\${this.basePath}/suggestions?q=\${encodeURIComponent(query)}\`);
    return response.data;
  }

  /**
   * Obtenir les filtres populaires
   */
  async getPopularFilters(): Promise<AvailableFilters> {
    const response = await apiService.get(\`\${this.basePath}/filters\`);
    return response.data;
  }

  /**
   * Obtenir les retraites similaires
   */
  async getSimilar(id${isTypeScript ? ': string' : ''}, limit${isTypeScript ? ': number' : ''} = 4): Promise<Retreat[]> {
    const response = await apiService.get(\`\${this.basePath}/\${id}/similar?limit=\${limit}\`);
    return response.data;
  }

  /**
   * Obtenir les avis d'une retraite
   */
  async getReviews(id${isTypeScript ? ': string' : ''}, page${isTypeScript ? ': number' : ''} = 1, limit${isTypeScript ? ': number' : ''} = 10) {
    const response = await apiService.get(\`\${this.basePath}/\${id}/reviews?page=\${page}&limit=\${limit}\`);
    return response.data;
  }

  /**
   * Ajouter une retraite aux favoris
   */
  async addToFavorites(id${isTypeScript ? ': string' : ''}): Promise<void> {
    await apiService.post(\`\${this.basePath}/\${id}/favorite\`);
  }

  /**
   * Retirer une retraite des favoris
   */
  async removeFromFavorites(id${isTypeScript ? ': string' : ''}): Promise<void> {
    await apiService.delete(\`\${this.basePath}/\${id}/favorite\`);
  }

  /**
   * Obtenir les favoris de l'utilisateur
   */
  async getFavorites(): Promise<Retreat[]> {
    const response = await apiService.get(\`\${this.basePath}/favorites\`);
    return response.data;
  }

  /**
   * Vérifier la disponibilité d'une retraite
   */
  async checkAvailability(id${isTypeScript ? ': string' : ''}, startDate${isTypeScript ? ': string' : ''}, guestCount${isTypeScript ? ': number' : ''}): Promise<any> {
    const response = await apiService.post(\`\${this.basePath}/\${id}/availability\`, {
      startDate,
      guestCount
    });
    return response.data;
  }

  /**
   * Obtenir le calendrier de disponibilité
   */
  async getAvailabilityCalendar(id${isTypeScript ? ': string' : ''}, year${isTypeScript ? ': number' : ''}, month${isTypeScript ? ': number' : ''}): Promise<any> {
    const response = await apiService.get(\`\${this.basePath}/\${id}/calendar?year=\${year}&month=\${month}\`);
    return response.data;
  }

  /**
   * Sauvegarder une recherche
   */
  async saveSearch(searchData${isTypeScript ? ': { query: string; filters: SearchFilters; name: string }' : ''}): Promise<void> {
    await apiService.post('/user/saved-searches', searchData);
  }

  /**
   * Obtenir les recherches sauvegardées
   */
  async getSavedSearches(): Promise<any[]> {
    const response = await apiService.get('/user/saved-searches');
    return response.data;
  }

  /**
   * Supprimer une recherche sauvegardée
   */
  async deleteSavedSearch(id${isTypeScript ? ': string' : ''}): Promise<void> {
    await apiService.delete(\`/user/saved-searches/\${id}\`);
  }

  /**
   * Signaler une retraite
   */
  async reportRetreat(id${isTypeScript ? ': string' : ''}, reason${isTypeScript ? ': string' : ''}, details${isTypeScript ? ': string' : ''}): Promise<void> {
    await apiService.post(\`\${this.basePath}/\${id}/report\`, {
      reason,
      details
    });
  }

  /**
   * Obtenir les statistiques d'une retraite (pour les partenaires)
   */
  async getStats(id${isTypeScript ? ': string' : ''}): Promise<any> {
    const response = await apiService.get(\`\${this.basePath}/\${id}/stats\`);
    return response.data;
  }
}

// Instance singleton
export const retreatService = new RetreatService();
export default retreatService;`;
    }
    // Méthodes utilitaires
    extractDependencies(content) {
        const importRegex = /import.*from ['"]([^'"]+)['"]/g;
        const dependencies = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            const dep = match[1];
            if (!dep.startsWith('.') && !dep.startsWith('/')) {
                dependencies.push(dep);
            }
        }
        return [...new Set(dependencies)];
    }
    extractImports(content) {
        const importRegex = /import\s+(?:{([^}]+)}|(\w+))\s+from/g;
        const imports = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            if (match[1]) {
                imports.push(...match[1].split(',').map(imp => imp.trim()));
            }
            else if (match[2]) {
                imports.push(match[2]);
            }
        }
        return [...new Set(imports)];
    }
    // Méthodes de génération simplifiées (à implémenter complètement)
    async generateAuthService(design, request) {
        return `// AuthService implementation`;
    }
    async generateBookingService(design, request) {
        return `// BookingService implementation`;
    }
    async generateUserService(design, request) {
        return `// UserService implementation`;
    }
    async generateAnalyticsService(design, request) {
        return `// AnalyticsService implementation`;
    }
    async generateGenericService(serviceName, design, request) {
        return `// Generic ${serviceName} service implementation`;
    }
}
exports.ServiceGenerator = ServiceGenerator;
//# sourceMappingURL=ServiceGenerator.js.map