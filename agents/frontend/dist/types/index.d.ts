/**
 * Types pour l'Agent Frontend
 */
export interface ComprehensiveUXDesign {
    userResearch: any;
    designIntelligence: any;
    personas: any[];
    designSystem: AdaptiveDesignSystem;
    wireframes: ConversionWireframes;
    usabilityResults: any;
    conversionOptimizations: any;
    componentLibrary: Record<string, any>;
    accessibilityCompliance: Record<string, any>;
    implementationGuide: string;
    testingStrategy: Record<string, any>;
}
export interface AdaptiveDesignSystem {
    colorSystem: {
        primary: string[];
        secondary: string[];
        semantic: Record<string, string>;
        neutral: string[];
        darkMode: Record<string, string>;
    };
    typography: {
        fontPairings: {
            heading: string;
            body: string;
        };
        responsiveScale: Record<string, string>;
        hierarchy: Record<string, any>;
        accessibility: Record<string, any>;
    };
    spacing: {
        baseUnit: number;
        scale: number[];
        responsive: Record<string, number>;
        density: 'compact' | 'comfortable' | 'spacious';
    };
    components: Record<string, any>;
    motion: {
        durations: Record<string, number>;
        easings: Record<string, string>;
        choreography: Record<string, any>;
        reducedMotion: Record<string, any>;
    };
    tokens: Record<string, any>;
    implementationGuide: string;
}
export interface ConversionWireframes {
    userFlows: Record<string, any>;
    landingPages: Record<string, any>;
    signupFlow: Record<string, any>;
    onboarding: Record<string, any>;
    dashboard: Record<string, any>;
    mobileExperience: Record<string, any>;
    accessibilityFeatures: Record<string, any>;
    conversionFunnels: Record<string, any>;
    trustSignals: Record<string, any>;
    socialProof: Record<string, any>;
}
export interface CodeGenerationRequest {
    design: ComprehensiveUXDesign;
    framework: 'react' | 'vue' | 'angular' | 'svelte';
    typescript: boolean;
    styling: 'css' | 'scss' | 'tailwind' | 'styled-components' | 'emotion';
    stateManagement: 'redux' | 'zustand' | 'vuex' | 'pinia' | 'context' | 'none';
    testing: 'jest' | 'vitest' | 'cypress' | 'playwright' | 'none';
    buildTool: 'vite' | 'webpack' | 'parcel' | 'rollup';
    packageManager: 'npm' | 'yarn' | 'pnpm';
    features: CodeGenerationFeatures;
    outputFormat: 'zip' | 'git' | 'files';
}
export interface CodeGenerationFeatures {
    routing: boolean;
    authentication: boolean;
    internationalization: boolean;
    pwa: boolean;
    ssr: boolean;
    darkMode: boolean;
    responsive: boolean;
    accessibility: boolean;
    seo: boolean;
    analytics: boolean;
    errorBoundaries: boolean;
    lazyLoading: boolean;
    codesplitting: boolean;
    storybook: boolean;
    documentation: boolean;
}
export interface GeneratedCode {
    id: string;
    framework: string;
    files: GeneratedFile[];
    structure: ProjectStructure;
    dependencies: Dependencies;
    scripts: Record<string, string>;
    configuration: ProjectConfiguration;
    documentation: GeneratedDocumentation;
    tests: GeneratedTest[];
    metadata: CodeMetadata;
}
export interface GeneratedFile {
    path: string;
    content: string;
    type: 'component' | 'page' | 'hook' | 'service' | 'util' | 'style' | 'config' | 'test';
    language: 'typescript' | 'javascript' | 'css' | 'scss' | 'html' | 'json' | 'md';
    size: number;
    dependencies: string[];
    exports: string[];
    imports: string[];
}
export interface ProjectStructure {
    name: string;
    version: string;
    directories: Directory[];
    entryPoints: string[];
    publicFiles: string[];
    configFiles: string[];
}
export interface Directory {
    name: string;
    path: string;
    type: 'components' | 'pages' | 'hooks' | 'services' | 'utils' | 'styles' | 'assets' | 'tests' | 'config';
    files: string[];
    subdirectories: Directory[];
}
export interface Dependencies {
    production: Record<string, string>;
    development: Record<string, string>;
    peer: Record<string, string>;
    optional: Record<string, string>;
}
export interface ProjectConfiguration {
    typescript?: any;
    eslint?: any;
    prettier?: any;
    jest?: any;
    vite?: any;
    webpack?: any;
    tailwind?: any;
    postcss?: any;
    babel?: any;
    storybook?: any;
}
export interface GeneratedDocumentation {
    readme: string;
    apiDocs: string;
    componentDocs: ComponentDoc[];
    deploymentGuide: string;
    developmentGuide: string;
    changelog: string;
}
export interface ComponentDoc {
    name: string;
    description: string;
    props: PropDoc[];
    examples: string[];
    accessibility: string;
    testing: string;
}
export interface PropDoc {
    name: string;
    type: string;
    required: boolean;
    default?: any;
    description: string;
}
export interface GeneratedTest {
    file: string;
    type: 'unit' | 'integration' | 'e2e';
    framework: string;
    coverage: TestCoverage;
    content: string;
}
export interface TestCoverage {
    statements: number;
    branches: number;
    functions: number;
    lines: number;
}
export interface CodeMetadata {
    generatedAt: Date;
    generatedBy: string;
    designVersion: string;
    codeVersion: string;
    framework: string;
    features: string[];
    metrics: CodeMetrics;
    quality: QualityMetrics;
}
export interface CodeMetrics {
    totalFiles: number;
    totalLines: number;
    totalSize: number;
    componentCount: number;
    pageCount: number;
    hookCount: number;
    serviceCount: number;
    testCount: number;
    complexity: number;
}
export interface QualityMetrics {
    accessibility: number;
    performance: number;
    seo: number;
    maintainability: number;
    testCoverage: number;
    codeQuality: number;
    security: number;
}
export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationWarning[];
    suggestions: string[];
    score: number;
    metrics: ValidationMetrics;
}
export interface ValidationError {
    type: 'syntax' | 'accessibility' | 'performance' | 'security' | 'design-compliance';
    severity: 'error' | 'warning' | 'info';
    message: string;
    file: string;
    line?: number;
    column?: number;
    rule: string;
    fix?: string;
}
export interface ValidationWarning {
    type: string;
    message: string;
    file: string;
    line?: number;
    suggestion: string;
}
export interface ValidationMetrics {
    designCompliance: number;
    accessibilityScore: number;
    performanceScore: number;
    securityScore: number;
    codeQualityScore: number;
    testCoverageScore: number;
}
export interface AgentMessage {
    id: string;
    type: 'request' | 'response' | 'notification';
    from: string;
    to: string;
    payload: any;
    timestamp: Date;
    correlationId?: string;
}
export interface AgentConfig {
    id: string;
    name: string;
    type: 'frontend';
    version: string;
    capabilities: string[];
    endpoints: Record<string, string>;
    memory: {
        store: string;
        collections: string[];
    };
    communication: {
        kafka: {
            topics: string[];
            groupId: string;
        };
        redis: {
            channels: string[];
        };
    };
    codeGeneration: {
        supportedFrameworks: string[];
        defaultFramework: string;
        outputDirectory: string;
        templatesDirectory: string;
    };
}
export interface Template {
    id: string;
    name: string;
    framework: string;
    type: 'component' | 'page' | 'layout' | 'hook' | 'service';
    template: string;
    variables: TemplateVariable[];
    dependencies: string[];
    examples: string[];
}
export interface TemplateVariable {
    name: string;
    type: string;
    required: boolean;
    default?: any;
    description: string;
    validation?: string;
}
export interface FrameworkAdapter {
    name: string;
    version: string;
    fileExtensions: string[];
    componentStructure: ComponentStructure;
    routingPattern: string;
    stateManagementOptions: string[];
    stylingOptions: string[];
    testingFrameworks: string[];
    buildTools: string[];
}
export interface ComponentStructure {
    functional: boolean;
    class: boolean;
    hooks: boolean;
    props: boolean;
    state: boolean;
    lifecycle: boolean;
    styling: string[];
}
export interface OptimizationResult {
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
    optimizations: Optimization[];
    performance: PerformanceMetrics;
    accessibility: AccessibilityMetrics;
}
export interface Optimization {
    type: 'bundle-size' | 'code-splitting' | 'lazy-loading' | 'tree-shaking' | 'minification';
    description: string;
    impact: 'high' | 'medium' | 'low';
    savings: number;
    applied: boolean;
}
export interface PerformanceMetrics {
    bundleSize: number;
    loadTime: number;
    renderTime: number;
    interactiveTime: number;
    coreWebVitals: {
        lcp: number;
        fid: number;
        cls: number;
    };
}
export interface AccessibilityMetrics {
    wcagLevel: 'A' | 'AA' | 'AAA';
    score: number;
    issues: AccessibilityIssue[];
    recommendations: string[];
}
export interface AccessibilityIssue {
    type: string;
    severity: 'error' | 'warning' | 'notice';
    element: string;
    description: string;
    fix: string;
    wcagReference: string;
}
export interface DeploymentConfig {
    platform: 'vercel' | 'netlify' | 'aws' | 'gcp' | 'azure' | 'docker';
    environment: 'development' | 'staging' | 'production';
    buildCommand: string;
    outputDirectory: string;
    environmentVariables: Record<string, string>;
    domains: string[];
    ssl: boolean;
    cdn: boolean;
}
//# sourceMappingURL=index.d.ts.map