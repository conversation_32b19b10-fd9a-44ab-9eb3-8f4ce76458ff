{"version": 3, "file": "AnalyticsEngine.js", "sourceRoot": "", "sources": ["../../src/engines/AnalyticsEngine.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AActC;;;GAGG;AACH,MAAa,eAAgB,SAAQ,qBAAY;IAK/C,YAAY,MAAc,EAAE,MAAuB;QACjD,KAAK,EAAE,CAAC;QAHF,kBAAa,GAAY,KAAK,CAAC;QAIrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAkC;QAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACjD,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE;YACjC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE;SAC9B,CAAC,CAAC;QAEH,4DAA4D;QAC5D,MAAM,IAAI,GAAG;YACX,SAAS,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACjD,QAAQ,EAAE,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC/C,QAAQ,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAChD,WAAW,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YACrD,QAAQ,EAAE,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;SACjD,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAY,EAAE,MAAkC;QACtE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAEtD,MAAM,SAAS,GAAuB;YACpC,MAAM;YACN,QAAQ,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC9C,SAAS,EAAE,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC;YAClE,QAAQ,EAAE,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC/D,QAAQ,EAAE,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,QAAQ,CAAC;YAChE,UAAU,EAAE,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,WAAW,CAAC;YACvE,MAAM,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;SAClD,CAAC;QAEF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,QAAkB;QACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;QAE5E,MAAM,WAAW,GAAG;YAClB,OAAO,EAAE,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC3D,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC;YAC7E,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzD,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC7D,eAAe,EAAE,IAAI,CAAC,kCAAkC,CAAC,QAAQ,CAAC;SACnE,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAA6B;QAClD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,yCAAyC;QACzC,IAAI,SAAS,CAAC,QAAQ,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QACjF,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACzD,CAAC;QAED,0BAA0B;QAC1B,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAC9D,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC1C,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,mBAAmB,WAAW,CAAC,OAAO,WAAW,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAE/F,6BAA6B;QAC7B,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC,CAAC;QACrF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,QAAQ,CAAC,IAAI,CAAC,wBAAwB,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAkC;QAClE,qCAAqC;QACrC,OAAO;YACL;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE;oBACP,WAAW,EAAE,KAAK;oBAClB,MAAM,EAAE,IAAI;oBACZ,WAAW,EAAE,GAAG;oBAChB,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE,KAAK;iBACf;aACF;YACD;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM;oBACnB,MAAM,EAAE,IAAI;oBACZ,WAAW,EAAE,EAAE;oBACf,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE,KAAK;iBACf;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,MAAkC;QACjE,OAAO;YACL,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE;YACnE,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE;YACnE,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE;SACtE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAkC;QAClE,OAAO;YACL,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE;gBACZ,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,EAAE;aACV;YACD,SAAS,EAAE;gBACT,YAAY,EAAE,EAAE;gBAChB,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,EAAE;aAChB;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAkC;QACpE,OAAO;YACL,gBAAgB,EAAE,GAAG;YACrB,cAAc,EAAE,GAAG;YACnB,iBAAiB,EAAE,GAAG;YACtB,UAAU,EAAE;gBACV,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE;gBAC3D,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE;gBAC7D,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE;aACzD;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAkC;QAClE,OAAO;YACL,YAAY,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC;YAC9D,kBAAkB,EAAE,UAAU;YAC9B,WAAW,EAAE,MAAM;SACpB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,OAAY;QACzC,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,OAAY,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAClG,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,OAAY,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACtG,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,OAAY,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAE9G,OAAO;YACL,UAAU;YACV,YAAY;YACZ,gBAAgB;YAChB,WAAW,EAAE,YAAY,GAAG,UAAU;YACtC,UAAU,EAAE,UAAU,GAAG,CAAC,gBAAgB,GAAG,EAAE,CAAC,EAAE,aAAa;YAC/D,qBAAqB,EAAE,CAAC,gBAAgB,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,aAAa;SACnF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,YAAmB;QACzD,OAAO,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACnC,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,YAAY,EAAE,QAAQ,CAAC,IAAI;YAC3B,OAAO,EAAE;gBACP,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW;gBACzC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG;gBACzC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;gBAC/B,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW;gBACzC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI;gBAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO;gBACjC,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG;gBACnE,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM;gBACpD,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI;gBAClE,cAAc,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG;gBAC9E,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI;gBACtD,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG;aACxF;YACD,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;YACzF,QAAQ,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;SACrF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,WAAkB;QACvD,OAAO,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACjC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK;YACrC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;SACzE,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,YAAiB;QACvD,OAAO;YACL,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,YAAY,EAAE,YAAY,CAAC,YAAY;YACvC,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,sBAAsB;oBAC5B,IAAI,EAAE,KAAK;oBACX,eAAe,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;oBACxD,WAAW,EAAE;wBACX,cAAc,EAAE,GAAG;wBACnB,iBAAiB,EAAE,GAAG;wBACtB,cAAc,EAAE,IAAI;qBACrB;iBACF;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,cAAmB;QAC3D,OAAO;YACL,gBAAgB,EAAE,cAAc,CAAC,gBAAgB;YACjD,cAAc,EAAE,cAAc,CAAC,cAAc;YAC7C,iBAAiB,EAAE,cAAc,CAAC,iBAAiB;YACnD,iBAAiB,EAAE,cAAc,CAAC,iBAAiB,GAAG,CAAC,cAAc,CAAC,cAAc,GAAG,GAAG,CAAC;YAC3F,cAAc,EAAE,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC5D,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,cAAc,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;gBACxD,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG;aACxE,CAAC,CAAC;YACH,gBAAgB,EAAE;gBAChB,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;gBACnG,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;aACpG;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,OAAY;QAC9C,OAAO;YACL;gBACE,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,YAAY;gBACnB,gBAAgB,EAAE,IAAI;gBACtB,QAAQ,EAAE,CAAC,wCAAwC,CAAC;gBACpD,eAAe,EAAE,CAAC,iCAAiC,EAAE,qBAAqB,CAAC;aAC5E;YACD;gBACE,MAAM,EAAE,KAAK;gBACb,KAAK,EAAE,QAAQ;gBACf,gBAAgB,EAAE,GAAG;gBACrB,QAAQ,EAAE,CAAC,oBAAoB,CAAC;gBAChC,eAAe,EAAE,CAAC,6BAA6B,CAAC;aACjD;SACF,CAAC;IACJ,CAAC;IAEO,2BAA2B,CAAC,OAAY;QAC9C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;QAC5C,IAAI,IAAI,GAAG,GAAG;YAAE,OAAO,WAAW,CAAC;QACnC,IAAI,IAAI,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC;QAC9B,IAAI,IAAI,GAAG,GAAG;YAAE,OAAO,SAAS,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,yBAAyB,CAAC,QAAe,EAAE,OAAY;QAC7D,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,OAAO,EAAE,OAAO,CAAC,IAAI;YACrB,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,kBAAkB,CAAC;YACnE,eAAe,EAAE,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;SACzD,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,yBAAyB,CAAC,OAAc;QAC9C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1B,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,WAAW,EAAE,MAAM,EAAE,aAAa;YAClC,UAAU,EAAE,MAAM;SACnB,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,0BAA0B,CAAC,SAAc;QAC/C,OAAO;YACL,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,MAAM;YAClB,UAAU,EAAE,SAAS;SACtB,CAAC;IACJ,CAAC;IAEO,kCAAkC,CAAC,QAAkB;QAC3D,MAAM,eAAe,GAAG,EAAE,CAAC;QAE3B,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC;YAC1C,eAAe,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YAChC,eAAe,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,qBAAqB,CAAC,IAAY;QACxC,IAAI,IAAI,GAAG,GAAG;YAAE,OAAO,WAAW,CAAC;QACnC,IAAI,IAAI,GAAG,GAAG;YAAE,OAAO,MAAM,CAAC;QAC9B,IAAI,IAAI,GAAG,GAAG;YAAE,OAAO,SAAS,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,yBAAyB,CAAC,OAAY;QAC5C,OAAO,CAAC,4BAA4B,OAAO,CAAC,IAAI,EAAE,EAAE,4BAA4B,CAAC,CAAC;IACpF,CAAC;CACF;AA/UD,0CA+UC"}