import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { Campaign, CampaignMetrics } from '../types';
import { MarketingMemory } from '../memory/MarketingMemory';
/**
 * Gestionnaire de campagnes marketing
 * Responsable de la création, gestion et suivi des campagnes
 */
export declare class CampaignManager extends EventEmitter {
    private logger;
    private memory;
    private isInitialized;
    private activeCampaigns;
    private readonly campaignTemplates;
    constructor(logger: Logger, memory: MarketingMemory);
    /**
     * Initialise le gestionnaire de campagnes
     */
    initialize(): Promise<void>;
    /**
     * Crée une nouvelle campagne
     */
    createCampaign(campaignData: any): Promise<Campaign>;
    /**
     * Lance une campagne
     */
    launchCampaign(campaign: Campaign): Promise<void>;
    /**
     * Met en pause une campagne
     */
    pauseCampaign(campaignId: string): Promise<void>;
    /**
     * Arrête une campagne
     */
    stopCampaign(campaignId: string): Promise<void>;
    /**
     * Met à jour les métriques d'une campagne
     */
    updateCampaignMetrics(campaignId: string, newMetrics: Partial<CampaignMetrics>): Promise<void>;
    /**
     * Génère le contenu de campagne
     */
    private generateCampaignContent;
    /**
     * Configure le targeting de campagne
     */
    private configureCampaignTargeting;
    /**
     * Configure le planning de campagne
     */
    private configureCampaignSchedule;
    /**
     * Sélectionne les canaux de campagne
     */
    private selectCampaignChannels;
    /**
     * Calcule le budget de campagne
     */
    private calculateCampaignBudget;
    /**
     * Initialise les métriques de campagne
     */
    private initializeCampaignMetrics;
    /**
     * Valide une campagne
     */
    private validateCampaign;
    /**
     * Vérifications pré-lancement
     */
    private preLaunchChecks;
    /**
     * Démarre le monitoring d'une campagne
     */
    private startCampaignMonitoring;
    /**
     * Simule la mise à jour des métriques
     */
    private simulateMetricsUpdate;
    /**
     * Calcule les métriques dérivées
     */
    private calculateDerivedMetrics;
    /**
     * Charge les campagnes actives
     */
    private loadActiveCampaigns;
    /**
     * Génère le nom de campagne
     */
    private generateCampaignName;
    /**
     * Génère la description de campagne
     */
    private generateCampaignDescription;
    /**
     * Génère le corps du contenu
     */
    private generateContentBody;
    /**
     * Génère les variations de contenu
     */
    private generateContentVariations;
}
//# sourceMappingURL=CampaignManager.d.ts.map