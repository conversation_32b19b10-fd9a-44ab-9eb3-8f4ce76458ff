import { Logger } from 'winston';
import { MarketingStrategy, Campaign, ABTest, MarketingAnalytics, MarketingResponse } from '../types';
/**
 * Système de mémoire pour l'Agent Marketing
 * Utilise Weaviate pour le stockage vectoriel des données marketing
 */
export declare class MarketingMemory {
    private client;
    private logger;
    private isConnected;
    private readonly collections;
    constructor(logger: Logger);
    /**
     * Initialise la connexion à Weaviate
     */
    initialize(): Promise<void>;
    /**
     * Stocke une stratégie marketing
     */
    storeStrategy(strategy: MarketingStrategy): Promise<void>;
    /**
     * Récupère une stratégie marketing
     */
    getStrategy(strategyId: string): Promise<MarketingStrategy | null>;
    /**
     * Stocke une campagne
     */
    storeCampaign(campaign: Campaign): Promise<void>;
    /**
     * Récupère une campagne
     */
    getCampaign(campaignId: string): Promise<Campaign | null>;
    /**
     * Met à jour une campagne
     */
    updateCampaign(campaign: Campaign): Promise<void>;
    /**
     * Stocke un test A/B
     */
    storeABTest(abTest: ABTest): Promise<void>;
    /**
     * <PERSON>e des analyses marketing
     */
    storeAnalytics(analytics: MarketingAnalytics): Promise<void>;
    /**
     * Stocke une réponse marketing
     */
    storeResponse(response: MarketingResponse): Promise<void>;
    /**
     * Recherche des stratégies similaires
     */
    findSimilarStrategies(strategy: MarketingStrategy, limit?: number): Promise<MarketingStrategy[]>;
    /**
     * Crée les schémas Weaviate
     */
    private createSchemas;
    /**
     * Parse une stratégie depuis Weaviate
     */
    private parseStrategy;
    /**
     * Parse une campagne depuis Weaviate
     */
    private parseCampaign;
    /**
     * Déconnexion
     */
    disconnect(): Promise<void>;
}
//# sourceMappingURL=MarketingMemory.d.ts.map