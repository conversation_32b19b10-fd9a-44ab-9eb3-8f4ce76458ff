import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createLogger, format, transports } from 'winston';
import dotenv from 'dotenv';
import { MarketingAgent } from './core/MarketingAgent';
import { MarketingMemory } from './memory/MarketingMemory';
import { MarketingCommunication } from './communication/MarketingCommunication';
import { AgentConfig, MarketingRequest } from './types';

// Configuration de l'environnement
dotenv.config();

// Configuration du logger
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { service: 'agent-marketing' },
  transports: [
    new transports.File({ filename: 'logs/error.log', level: 'error' }),
    new transports.File({ filename: 'logs/combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ]
});

// Configuration de l'agent
const agentConfig: AgentConfig = {
  id: 'agent-marketing-001',
  name: 'Agent Marketing',
  type: 'marketing',
  version: '1.0.0',
  capabilities: [
    'strategy_generation',
    'campaign_management',
    'conversion_optimization',
    'ab_testing',
    'social_media_management',
    'analytics_generation'
  ],
  endpoints: {
    health: '/health',
    strategy: '/api/strategy',
    campaign: '/api/campaign',
    analytics: '/api/analytics',
    optimization: '/api/optimization',
    social: '/api/social'
  },
  memory: {
    store: 'weaviate',
    collections: [
      'MarketingStrategy',
      'MarketingCampaign',
      'ABTest',
      'MarketingAnalytics',
      'MarketingResponse',
      'SocialMediaPost'
    ]
  },
  communication: {
    kafka: {
      topics: [
        'marketing.strategy.created',
        'marketing.campaign.launched',
        'marketing.conversion.optimized',
        'marketing.analytics.generated',
        'marketing.social.managed'
      ]
    }
  }
};

// Initialisation de l'application
const app = express();
const port = process.env.PORT || 3000;

// Middlewares de sécurité et performance
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Variables globales
let marketingAgent: MarketingAgent;
let memory: MarketingMemory;
let communication: MarketingCommunication;

/**
 * Initialise l'agent marketing
 */
async function initializeAgent(): Promise<void> {
  try {
    logger.info('Initialisation de l\'Agent Marketing...');

    // Initialisation de la mémoire
    memory = new MarketingMemory(logger);
    await memory.initialize();

    // Initialisation de la communication
    communication = new MarketingCommunication(logger);
    await communication.initialize();

    // Initialisation de l'agent principal
    marketingAgent = new MarketingAgent(agentConfig, logger, memory, communication);
    await marketingAgent.initialize();

    logger.info('Agent Marketing initialisé avec succès');

  } catch (error) {
    logger.error('Erreur lors de l\'initialisation de l\'agent:', error);
    process.exit(1);
  }
}

// Routes de santé
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    agent: {
      id: agentConfig.id,
      name: agentConfig.name,
      version: agentConfig.version
    },
    uptime: process.uptime()
  });
});

app.get('/status', (req, res) => {
  res.json({
    agent: agentConfig,
    memory: {
      connected: memory ? true : false,
      collections: agentConfig.memory.collections
    },
    communication: {
      connected: communication ? true : false,
      topics: agentConfig.communication.kafka.topics
    }
  });
});

// Routes API principales

/**
 * Création de stratégie marketing
 */
app.post('/api/strategy', async (req, res) => {
  try {
    const request: MarketingRequest = {
      id: `req-${Date.now()}`,
      type: 'strategy',
      description: 'Création de stratégie marketing',
      requirements: req.body,
      priority: 'medium',
      requestedBy: req.headers['x-user-id'] as string || 'anonymous',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const response = await marketingAgent.processRequest(request);
    
    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la création de stratégie:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Gestion de campagnes
 */
app.post('/api/campaign', async (req, res) => {
  try {
    const request: MarketingRequest = {
      id: `req-${Date.now()}`,
      type: 'campaign',
      description: 'Création/gestion de campagne',
      requirements: req.body,
      priority: 'high',
      requestedBy: req.headers['x-user-id'] as string || 'anonymous',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const response = await marketingAgent.processRequest(request);
    
    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la gestion de campagne:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Optimisation de conversion
 */
app.post('/api/optimization', async (req, res) => {
  try {
    const request: MarketingRequest = {
      id: `req-${Date.now()}`,
      type: 'optimization',
      description: 'Optimisation de conversion',
      requirements: req.body,
      priority: 'high',
      requestedBy: req.headers['x-user-id'] as string || 'anonymous',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const response = await marketingAgent.processRequest(request);
    
    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de l\'optimisation:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Génération d'analyses
 */
app.post('/api/analytics', async (req, res) => {
  try {
    const request: MarketingRequest = {
      id: `req-${Date.now()}`,
      type: 'analysis',
      description: 'Génération d\'analyses marketing',
      requirements: req.body,
      priority: 'medium',
      requestedBy: req.headers['x-user-id'] as string || 'anonymous',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const response = await marketingAgent.processRequest(request);
    
    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la génération d\'analyses:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Gestion des réseaux sociaux
 */
app.post('/api/social', async (req, res) => {
  try {
    const request: MarketingRequest = {
      id: `req-${Date.now()}`,
      type: 'content',
      description: 'Gestion des réseaux sociaux',
      requirements: req.body,
      priority: 'medium',
      requestedBy: req.headers['x-user-id'] as string || 'anonymous',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const response = await marketingAgent.processRequest(request);
    
    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Erreur lors de la gestion des réseaux sociaux:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Middleware de gestion d'erreurs
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Erreur non gérée:', error);
  res.status(500).json({
    success: false,
    error: 'Erreur interne du serveur',
    timestamp: new Date().toISOString()
  });
});

// Gestion de l'arrêt gracieux
process.on('SIGTERM', async () => {
  logger.info('Signal SIGTERM reçu, arrêt gracieux...');
  
  if (marketingAgent) {
    await marketingAgent.shutdown();
  }
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('Signal SIGINT reçu, arrêt gracieux...');
  
  if (marketingAgent) {
    await marketingAgent.shutdown();
  }
  
  process.exit(0);
});

// Démarrage du serveur
async function startServer(): Promise<void> {
  try {
    await initializeAgent();
    
    app.listen(port, () => {
      logger.info(`Agent Marketing démarré sur le port ${port}`);
      logger.info(`Health check: http://localhost:${port}/health`);
      logger.info(`Status: http://localhost:${port}/status`);
    });

  } catch (error) {
    logger.error('Erreur lors du démarrage du serveur:', error);
    process.exit(1);
  }
}

// Démarrage de l'application
if (require.main === module) {
  startServer();
}

export { app, marketingAgent, logger };
