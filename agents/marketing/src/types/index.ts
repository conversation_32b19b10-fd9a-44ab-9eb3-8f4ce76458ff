/**
 * Types pour l'Agent Marketing
 */

export interface AgentConfig {
  id: string;
  name: string;
  type: string;
  version: string;
  capabilities: string[];
  endpoints: Record<string, string>;
  memory: {
    store: string;
    collections: string[];
  };
  communication: {
    kafka: {
      topics: string[];
    };
  };
}

export interface MarketingStrategy {
  id: string;
  name: string;
  description: string;
  targetAudience: TargetAudience;
  objectives: MarketingObjective[];
  channels: MarketingChannel[];
  budget: Budget;
  timeline: Timeline;
  kpis: KPI[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TargetAudience {
  demographics: Demographics;
  psychographics: Psychographics;
  behaviors: Behavior[];
  painPoints: string[];
  motivations: string[];
}

export interface Demographics {
  ageRange: [number, number];
  gender: string[];
  location: string[];
  income: [number, number];
  education: string[];
  occupation: string[];
}

export interface Psychographics {
  values: string[];
  interests: string[];
  lifestyle: string[];
  personality: string[];
}

export interface Behavior {
  type: string;
  description: string;
  frequency: string;
  triggers: string[];
}

export interface MarketingObjective {
  type: 'awareness' | 'consideration' | 'conversion' | 'retention' | 'advocacy';
  description: string;
  target: number;
  metric: string;
  deadline: Date;
}

export interface MarketingChannel {
  name: string;
  type: 'digital' | 'traditional' | 'social' | 'email' | 'content' | 'paid';
  platform?: string;
  budget: number;
  expectedReach: number;
  expectedConversion: number;
}

export interface Budget {
  total: number;
  currency: string;
  allocation: Record<string, number>;
  period: 'monthly' | 'quarterly' | 'yearly';
}

export interface Timeline {
  startDate: Date;
  endDate: Date;
  phases: Phase[];
}

export interface Phase {
  name: string;
  startDate: Date;
  endDate: Date;
  activities: Activity[];
}

export interface Activity {
  name: string;
  description: string;
  channel: string;
  startDate: Date;
  endDate: Date;
  budget: number;
  responsible: string;
  status: 'planned' | 'in-progress' | 'completed' | 'cancelled';
}

export interface KPI {
  name: string;
  description: string;
  target: number;
  current: number;
  unit: string;
  category: 'awareness' | 'engagement' | 'conversion' | 'retention';
}

export interface Campaign {
  id: string;
  name: string;
  description: string;
  type: 'email' | 'social' | 'content' | 'paid' | 'seo' | 'influencer';
  status: 'draft' | 'scheduled' | 'active' | 'paused' | 'completed';
  strategy: string; // Strategy ID
  channels: MarketingChannel[];
  content: CampaignContent[];
  targeting: Targeting;
  budget: Budget;
  schedule: Schedule;
  metrics: CampaignMetrics;
  createdAt: Date;
  updatedAt: Date;
}

export interface CampaignContent {
  id: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'interactive';
  title: string;
  description: string;
  content: string;
  assets: Asset[];
  variations: ContentVariation[];
}

export interface Asset {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  filename: string;
  size: number;
  metadata: Record<string, any>;
}

export interface ContentVariation {
  id: string;
  name: string;
  content: string;
  targetSegment: string;
  performance?: VariationPerformance;
}

export interface VariationPerformance {
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  conversionRate: number;
  cost: number;
}

export interface Targeting {
  demographics: Demographics;
  interests: string[];
  behaviors: string[];
  customAudiences: string[];
  lookalikes: string[];
  exclusions: string[];
}

export interface Schedule {
  startDate: Date;
  endDate: Date;
  timezone: string;
  frequency: 'once' | 'daily' | 'weekly' | 'monthly';
  dayOfWeek?: number[];
  timeOfDay?: string[];
}

export interface CampaignMetrics {
  impressions: number;
  reach: number;
  clicks: number;
  conversions: number;
  cost: number;
  revenue: number;
  ctr: number;
  cpc: number;
  cpm: number;
  conversionRate: number;
  roas: number;
  roi: number;
}

export interface ABTest {
  id: string;
  name: string;
  description: string;
  hypothesis: string;
  campaign: string; // Campaign ID
  variations: TestVariation[];
  trafficSplit: number[];
  metrics: string[];
  status: 'draft' | 'running' | 'completed' | 'cancelled';
  startDate: Date;
  endDate?: Date;
  results?: ABTestResults;
  createdAt: Date;
}

export interface TestVariation {
  id: string;
  name: string;
  description: string;
  content: CampaignContent;
  trafficPercentage: number;
}

export interface ABTestResults {
  winner?: string; // Variation ID
  confidence: number;
  significance: number;
  variationResults: Record<string, VariationPerformance>;
  insights: string[];
  recommendations: string[];
}

export interface SocialMediaPost {
  id: string;
  platform: 'facebook' | 'instagram' | 'twitter' | 'linkedin' | 'tiktok' | 'youtube';
  content: string;
  media: Asset[];
  hashtags: string[];
  mentions: string[];
  scheduledAt?: Date;
  publishedAt?: Date;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  engagement: SocialEngagement;
  campaign?: string; // Campaign ID
}

export interface SocialEngagement {
  likes: number;
  shares: number;
  comments: number;
  saves: number;
  clicks: number;
  reach: number;
  impressions: number;
}

export interface MarketingAnalytics {
  period: {
    start: Date;
    end: Date;
  };
  overview: AnalyticsOverview;
  campaigns: CampaignAnalytics[];
  channels: ChannelAnalytics[];
  audience: AudienceAnalytics;
  conversion: ConversionAnalytics;
  trends: TrendAnalysis[];
}

export interface AnalyticsOverview {
  totalSpend: number;
  totalRevenue: number;
  totalConversions: number;
  averageRoas: number;
  averageCpc: number;
  averageConversionRate: number;
}

export interface CampaignAnalytics {
  campaignId: string;
  campaignName: string;
  metrics: CampaignMetrics;
  performance: 'excellent' | 'good' | 'average' | 'poor';
  insights: string[];
}

export interface ChannelAnalytics {
  channel: string;
  spend: number;
  revenue: number;
  conversions: number;
  roas: number;
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

export interface AudienceAnalytics {
  totalReach: number;
  uniqueUsers: number;
  demographics: Record<string, number>;
  interests: Record<string, number>;
  behaviors: Record<string, number>;
  segments: AudienceSegment[];
}

export interface AudienceSegment {
  name: string;
  size: number;
  characteristics: string[];
  performance: {
    conversionRate: number;
    averageOrderValue: number;
    lifetime_value: number;
  };
}

export interface ConversionAnalytics {
  totalConversions: number;
  conversionRate: number;
  averageOrderValue: number;
  revenuePerVisitor: number;
  funnelAnalysis: FunnelStep[];
  attributionModel: AttributionData[];
}

export interface FunnelStep {
  step: string;
  visitors: number;
  conversions: number;
  conversionRate: number;
  dropoffRate: number;
}

export interface AttributionData {
  channel: string;
  touchpoints: number;
  firstTouch: number;
  lastTouch: number;
  assisted: number;
  revenue: number;
}

export interface TrendAnalysis {
  metric: string;
  trend: 'increasing' | 'decreasing' | 'stable';
  changePercentage: number;
  insights: string[];
  recommendations: string[];
}

export interface MarketingRequest {
  id: string;
  type: 'strategy' | 'campaign' | 'analysis' | 'optimization' | 'content';
  description: string;
  requirements: Record<string, any>;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  requestedBy: string;
  assignedTo?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface MarketingResponse {
  requestId: string;
  type: string;
  data: any;
  insights: string[];
  recommendations: string[];
  nextSteps: string[];
  confidence: number;
  generatedAt: Date;
}

export interface IntegrationConfig {
  seoAgent: {
    url: string;
    enabled: boolean;
  };
  translationAgent: {
    url: string;
    enabled: boolean;
  };
  webResearchAgent: {
    url: string;
    enabled: boolean;
  };
  uiuxAgent: {
    url: string;
    enabled: boolean;
  };
  contentCreatorAgent: {
    url: string;
    enabled: boolean;
  };
}

export interface CommunicationMessage {
  id: string;
  from: string;
  to: string;
  type: string;
  payload: any;
  timestamp: Date;
  correlationId?: string;
}
