"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateContentType = exports.validatePayloadSize = exports.validateRateLimit = exports.validateAuthHeaders = exports.validateReportsQuery = exports.validateAnalysisRequest = exports.validateMonitoringRequest = exports.validateOptimizationRequest = exports.validateBenchmarkRequest = void 0;
const joi_1 = __importDefault(require("joi"));
/**
 * Schéma de validation pour les requêtes de benchmark
 */
const benchmarkRequestSchema = joi_1.default.object({
    id: joi_1.default.string().optional(),
    type: joi_1.default.string().valid('lighthouse', 'load', 'profiling', 'full').required(),
    target: joi_1.default.object({
        url: joi_1.default.string().uri().when('type', {
            is: joi_1.default.string().valid('lighthouse', 'load'),
            then: joi_1.default.required(),
            otherwise: joi_1.default.optional()
        }),
        service: joi_1.default.string().when('type', {
            is: 'profiling',
            then: joi_1.default.required(),
            otherwise: joi_1.default.optional()
        }),
        endpoint: joi_1.default.string().optional()
    }).required(),
    configuration: joi_1.default.object({
        lighthouse: joi_1.default.object({
            categories: joi_1.default.array().items(joi_1.default.string()).optional(),
            formFactor: joi_1.default.string().valid('mobile', 'desktop').optional(),
            throttling: joi_1.default.string().optional(),
            emulatedUserAgent: joi_1.default.string().optional(),
            locale: joi_1.default.string().optional()
        }).optional(),
        loadTesting: joi_1.default.object({
            duration: joi_1.default.string().optional(),
            connections: joi_1.default.number().integer().min(1).max(1000).optional(),
            rps: joi_1.default.number().integer().min(1).optional(),
            headers: joi_1.default.object().optional(),
            body: joi_1.default.string().optional(),
            method: joi_1.default.string().valid('GET', 'POST', 'PUT', 'DELETE').optional()
        }).optional(),
        profiling: joi_1.default.object({
            duration: joi_1.default.number().integer().min(10).max(3600).optional(),
            sampleRate: joi_1.default.number().min(0.1).max(1).optional(),
            includeMemory: joi_1.default.boolean().optional(),
            includeCpu: joi_1.default.boolean().optional()
        }).optional()
    }).optional(),
    metadata: joi_1.default.object().optional()
});
/**
 * Schéma de validation pour les requêtes d'optimisation
 */
const optimizationRequestSchema = joi_1.default.object({
    id: joi_1.default.string().optional(),
    target: joi_1.default.object({
        codebase: joi_1.default.string().optional(),
        service: joi_1.default.string().optional(),
        architecture: joi_1.default.string().optional()
    }).required(),
    scope: joi_1.default.array().items(joi_1.default.string()).min(1).required(),
    constraints: joi_1.default.object({
        budget: joi_1.default.number().min(0).optional(),
        timeline: joi_1.default.string().valid('urgent', 'normal', 'flexible').optional(),
        technologies: joi_1.default.array().items(joi_1.default.string()).optional()
    }).optional(),
    goals: joi_1.default.object({
        performance: joi_1.default.number().min(0).max(100).optional(),
        scalability: joi_1.default.number().min(0).max(100).optional(),
        maintainability: joi_1.default.number().min(0).max(100).optional()
    }).optional()
});
/**
 * Schéma de validation pour les requêtes de monitoring
 */
const monitoringRequestSchema = joi_1.default.object({
    id: joi_1.default.string().optional(),
    targets: joi_1.default.array().items(joi_1.default.string()).min(1).required(),
    metrics: joi_1.default.array().items(joi_1.default.string()).min(1).required(),
    thresholds: joi_1.default.object().pattern(joi_1.default.string(), joi_1.default.number().min(0)).required(),
    duration: joi_1.default.string().optional(),
    interval: joi_1.default.number().integer().min(1000).optional() // minimum 1 seconde
});
/**
 * Schéma de validation pour les requêtes d'analyse
 */
const analysisRequestSchema = joi_1.default.object({
    id: joi_1.default.string().optional(),
    type: joi_1.default.string().valid('code', 'architecture', 'infrastructure').required(),
    target: joi_1.default.string().required(),
    depth: joi_1.default.string().valid('shallow', 'deep', 'comprehensive').optional(),
    focus: joi_1.default.array().items(joi_1.default.string()).optional()
});
/**
 * Middleware de validation générique
 */
function validateSchema(schema) {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true
        });
        if (error) {
            const errorDetails = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
                value: detail.context?.value
            }));
            return res.status(400).json({
                error: 'Données de requête invalides',
                details: errorDetails
            });
        }
        // Remplacer req.body par les données validées et nettoyées
        req.body = value;
        next();
    };
}
/**
 * Validation des requêtes de benchmark
 */
exports.validateBenchmarkRequest = validateSchema(benchmarkRequestSchema);
/**
 * Validation des requêtes d'optimisation
 */
exports.validateOptimizationRequest = validateSchema(optimizationRequestSchema);
/**
 * Validation des requêtes de monitoring
 */
exports.validateMonitoringRequest = validateSchema(monitoringRequestSchema);
/**
 * Validation des requêtes d'analyse
 */
exports.validateAnalysisRequest = validateSchema(analysisRequestSchema);
/**
 * Validation des paramètres de requête pour les rapports
 */
const validateReportsQuery = (req, res, next) => {
    const schema = joi_1.default.object({
        type: joi_1.default.string().valid('benchmarks', 'optimizations', 'analyses', 'all').optional(),
        limit: joi_1.default.number().integer().min(1).max(100).optional(),
        offset: joi_1.default.number().integer().min(0).optional(),
        startDate: joi_1.default.date().iso().optional(),
        endDate: joi_1.default.date().iso().min(joi_1.default.ref('startDate')).optional(),
        status: joi_1.default.string().valid('success', 'error', 'timeout', 'all').optional()
    });
    const { error, value } = schema.validate(req.query, {
        abortEarly: false,
        stripUnknown: true
    });
    if (error) {
        const errorDetails = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
            value: detail.context?.value
        }));
        return res.status(400).json({
            error: 'Paramètres de requête invalides',
            details: errorDetails
        });
    }
    req.query = value;
    next();
};
exports.validateReportsQuery = validateReportsQuery;
/**
 * Validation des en-têtes d'authentification (si nécessaire)
 */
const validateAuthHeaders = (req, res, next) => {
    const authHeader = req.headers.authorization;
    // Si l'authentification est requise
    if (process.env.REQUIRE_AUTH === 'true') {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: 'Token d\'authentification requis',
                message: 'Veuillez fournir un token Bearer valide'
            });
        }
        const token = authHeader.substring(7);
        // Validation basique du token (à adapter selon vos besoins)
        if (!token || token.length < 10) {
            return res.status(401).json({
                error: 'Token d\'authentification invalide',
                message: 'Le token fourni n\'est pas valide'
            });
        }
    }
    next();
};
exports.validateAuthHeaders = validateAuthHeaders;
/**
 * Validation du rate limiting personnalisé
 */
const validateRateLimit = (maxRequests, windowMs) => {
    const requests = new Map();
    return (req, res, next) => {
        const clientId = req.ip || 'unknown';
        const now = Date.now();
        if (!requests.has(clientId)) {
            requests.set(clientId, []);
        }
        const clientRequests = requests.get(clientId);
        // Nettoyer les anciennes requêtes
        const validRequests = clientRequests.filter(timestamp => now - timestamp < windowMs);
        requests.set(clientId, validRequests);
        if (validRequests.length >= maxRequests) {
            return res.status(429).json({
                error: 'Trop de requêtes',
                message: `Limite de ${maxRequests} requêtes par ${windowMs / 1000} secondes dépassée`,
                retryAfter: Math.ceil(windowMs / 1000)
            });
        }
        validRequests.push(now);
        next();
    };
};
exports.validateRateLimit = validateRateLimit;
/**
 * Validation de la taille du payload
 */
const validatePayloadSize = (maxSizeBytes) => {
    return (req, res, next) => {
        const contentLength = parseInt(req.headers['content-length'] || '0');
        if (contentLength > maxSizeBytes) {
            return res.status(413).json({
                error: 'Payload trop volumineux',
                message: `La taille du payload (${contentLength} bytes) dépasse la limite autorisée (${maxSizeBytes} bytes)`,
                maxSize: maxSizeBytes
            });
        }
        next();
    };
};
exports.validatePayloadSize = validatePayloadSize;
/**
 * Validation des types de contenu acceptés
 */
const validateContentType = (allowedTypes) => {
    return (req, res, next) => {
        const contentType = req.headers['content-type'];
        if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
            return res.status(415).json({
                error: 'Type de contenu non supporté',
                message: `Types acceptés: ${allowedTypes.join(', ')}`,
                received: contentType
            });
        }
        next();
    };
};
exports.validateContentType = validateContentType;
//# sourceMappingURL=validators.js.map