"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaCommunication = void 0;
const kafkajs_1 = require("kafkajs");
/**
 * Communication Kafka pour l'Agent Performance
 * Gère la communication synaptique avec les autres agents
 */
class KafkaCommunication {
    config;
    logger;
    kafka;
    producer = null;
    consumer = null;
    isConnected = false;
    messageHandlers = new Map();
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        this.kafka = new kafkajs_1.Kafka({
            clientId: config.clientId,
            brokers: config.brokers,
            retry: {
                initialRetryTime: 100,
                retries: 8
            }
        });
        this.logger.info('KafkaCommunication initialisé');
    }
    /**
     * Se connecter à Kafka
     */
    async connect() {
        if (this.isConnected) {
            this.logger.warn('Déjà connecté à Kafka');
            return;
        }
        try {
            this.logger.info('Connexion à Kafka...');
            // Initialiser le producer
            this.producer = this.kafka.producer({
                maxInFlightRequests: 1,
                idempotent: true,
                transactionTimeout: 30000
            });
            await this.producer.connect();
            this.logger.info('Producer Kafka connecté');
            // Initialiser le consumer
            this.consumer = this.kafka.consumer({
                groupId: this.config.groupId,
                sessionTimeout: 30000,
                rebalanceTimeout: 60000,
                heartbeatInterval: 3000
            });
            await this.consumer.connect();
            this.logger.info('Consumer Kafka connecté');
            // S'abonner aux topics
            await this.subscribeToTopics();
            // Démarrer la consommation
            await this.startConsuming();
            this.isConnected = true;
            this.logger.info('Connexion Kafka établie avec succès');
        }
        catch (error) {
            this.logger.error('Erreur lors de la connexion à Kafka:', error);
            throw error;
        }
    }
    /**
     * Se déconnecter de Kafka
     */
    async disconnect() {
        if (!this.isConnected) {
            return;
        }
        try {
            this.logger.info('Déconnexion de Kafka...');
            if (this.consumer) {
                await this.consumer.disconnect();
                this.consumer = null;
            }
            if (this.producer) {
                await this.producer.disconnect();
                this.producer = null;
            }
            this.isConnected = false;
            this.messageHandlers.clear();
            this.logger.info('Déconnexion Kafka terminée');
        }
        catch (error) {
            this.logger.error('Erreur lors de la déconnexion Kafka:', error);
            throw error;
        }
    }
    /**
     * S'abonner à un topic avec un handler
     */
    async subscribe(topic, handler) {
        this.messageHandlers.set(topic, handler);
        this.logger.info(`Handler enregistré pour le topic: ${topic}`);
    }
    /**
     * Publier un résultat de benchmark
     */
    async publishResult(result) {
        await this.publishMessage(this.config.topics.results, {
            key: result.id,
            value: {
                type: 'benchmark_result',
                payload: result,
                timestamp: new Date(),
                agentId: 'performance-agent'
            }
        });
        this.logger.info(`Résultat de benchmark publié: ${result.id}`);
    }
    /**
     * Publier des recommandations
     */
    async publishRecommendations(recommendations) {
        await this.publishMessage(this.config.topics.recommendations, {
            key: `recommendations-${Date.now()}`,
            value: {
                type: 'optimization_recommendations',
                payload: recommendations,
                timestamp: new Date(),
                agentId: 'performance-agent'
            }
        });
        this.logger.info(`${recommendations.length} recommandations publiées`);
    }
    /**
     * Publier une alerte
     */
    async publishAlert(alert) {
        await this.publishMessage(this.config.topics.alerts, {
            key: alert.id,
            value: {
                type: 'performance_alert',
                payload: alert,
                timestamp: new Date(),
                agentId: 'performance-agent'
            }
        });
        this.logger.warn(`Alerte publiée: ${alert.title}`);
    }
    /**
     * Publier un message générique
     */
    async publishMessage(topic, message) {
        if (!this.producer || !this.isConnected) {
            throw new Error('Producer Kafka non connecté');
        }
        try {
            await this.producer.send({
                topic,
                messages: [{
                        key: message.key,
                        value: JSON.stringify(message.value),
                        headers: message.headers,
                        timestamp: message.timestamp?.getTime().toString()
                    }]
            });
            this.logger.debug(`Message publié sur ${topic}:`, { key: message.key });
        }
        catch (error) {
            this.logger.error(`Erreur lors de la publication sur ${topic}:`, error);
            throw error;
        }
    }
    /**
     * Demander un benchmark à un autre agent
     */
    async requestBenchmark(targetAgent, request) {
        await this.publishMessage(`${targetAgent}.requests`, {
            key: request.id,
            value: {
                type: 'benchmark_request',
                payload: request,
                timestamp: new Date(),
                agentId: 'performance-agent',
                replyTo: this.config.topics.results
            }
        });
        this.logger.info(`Demande de benchmark envoyée à ${targetAgent}: ${request.id}`);
    }
    /**
     * Demander une optimisation
     */
    async requestOptimization(targetAgent, request) {
        await this.publishMessage(`${targetAgent}.requests`, {
            key: request.id,
            value: {
                type: 'optimization_request',
                payload: request,
                timestamp: new Date(),
                agentId: 'performance-agent',
                replyTo: this.config.topics.recommendations
            }
        });
        this.logger.info(`Demande d'optimisation envoyée à ${targetAgent}: ${request.id}`);
    }
    /**
     * S'abonner aux topics configurés
     */
    async subscribeToTopics() {
        if (!this.consumer) {
            throw new Error('Consumer non initialisé');
        }
        const topics = Object.values(this.config.topics);
        for (const topic of topics) {
            await this.consumer.subscribe({ topic, fromBeginning: false });
            this.logger.info(`Abonné au topic: ${topic}`);
        }
    }
    /**
     * Démarrer la consommation des messages
     */
    async startConsuming() {
        if (!this.consumer) {
            throw new Error('Consumer non initialisé');
        }
        await this.consumer.run({
            eachMessage: async (payload) => {
                await this.handleMessage(payload);
            }
        });
        this.logger.info('Consommation des messages démarrée');
    }
    /**
     * Traiter un message reçu
     */
    async handleMessage(payload) {
        const { topic, partition, message } = payload;
        try {
            if (!message.value) {
                this.logger.warn('Message reçu sans valeur', { topic, partition });
                return;
            }
            const messageData = JSON.parse(message.value.toString());
            const messageKey = message.key?.toString() || 'unknown';
            this.logger.debug(`Message reçu sur ${topic}:`, {
                key: messageKey,
                type: messageData.type
            });
            // Trouver le handler approprié
            const handler = this.messageHandlers.get(topic);
            if (handler) {
                await handler(messageData);
            }
            else {
                // Handler par défaut selon le topic
                await this.handleDefaultMessage(topic, messageData);
            }
        }
        catch (error) {
            this.logger.error(`Erreur lors du traitement du message sur ${topic}:`, error);
            // Optionnel: publier l'erreur sur un topic d'erreurs
            await this.publishError(topic, error, message);
        }
    }
    /**
     * Handler par défaut pour les messages
     */
    async handleDefaultMessage(topic, messageData) {
        switch (topic) {
            case this.config.topics.requests:
                await this.handlePerformanceRequest(messageData);
                break;
            case this.config.topics.results:
                await this.handlePerformanceResult(messageData);
                break;
            case this.config.topics.alerts:
                await this.handlePerformanceAlert(messageData);
                break;
            case this.config.topics.recommendations:
                await this.handleRecommendations(messageData);
                break;
            default:
                this.logger.warn(`Aucun handler pour le topic: ${topic}`);
        }
    }
    /**
     * Traiter une requête de performance
     */
    async handlePerformanceRequest(messageData) {
        this.logger.info(`Requête de performance reçue: ${messageData.type}`);
        // La logique de traitement sera gérée par l'agent principal
    }
    /**
     * Traiter un résultat de performance
     */
    async handlePerformanceResult(messageData) {
        this.logger.info(`Résultat de performance reçu: ${messageData.type}`);
        // Traitement des résultats d'autres agents
    }
    /**
     * Traiter une alerte de performance
     */
    async handlePerformanceAlert(messageData) {
        this.logger.warn(`Alerte de performance reçue: ${messageData.payload.title}`);
        // Traitement des alertes
    }
    /**
     * Traiter des recommandations
     */
    async handleRecommendations(messageData) {
        this.logger.info(`Recommandations reçues: ${messageData.payload.length} items`);
        // Traitement des recommandations
    }
    /**
     * Publier une erreur
     */
    async publishError(topic, error, originalMessage) {
        try {
            await this.publishMessage('errors', {
                key: `error-${Date.now()}`,
                value: {
                    type: 'message_processing_error',
                    payload: {
                        originalTopic: topic,
                        error: error.message,
                        stack: error.stack,
                        originalMessage: originalMessage.key?.toString()
                    },
                    timestamp: new Date(),
                    agentId: 'performance-agent'
                }
            });
        }
        catch (publishError) {
            this.logger.error('Erreur lors de la publication de l\'erreur:', publishError);
        }
    }
    /**
     * Vérifier la santé de la connexion
     */
    isHealthy() {
        return this.isConnected && this.producer !== null && this.consumer !== null;
    }
    /**
     * Obtenir les statistiques de la communication
     */
    getStats() {
        return {
            isConnected: this.isConnected,
            hasProducer: this.producer !== null,
            hasConsumer: this.consumer !== null,
            registeredHandlers: this.messageHandlers.size,
            topics: this.config.topics
        };
    }
}
exports.KafkaCommunication = KafkaCommunication;
//# sourceMappingURL=KafkaCommunication.js.map