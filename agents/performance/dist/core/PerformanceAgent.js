"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceAgent = void 0;
const events_1 = require("events");
const BenchmarkEngine_1 = require("../engines/BenchmarkEngine");
const OptimizationAdvisor_1 = require("../engines/OptimizationAdvisor");
const MonitoringIntegrator_1 = require("../engines/MonitoringIntegrator");
const KafkaCommunication_1 = require("../communication/KafkaCommunication");
const WeaviateMemory_1 = require("../memory/WeaviateMemory");
const logger_1 = require("../utils/logger");
/**
 * Agent Performance principal
 * Orchestrateur intelligent pour l'optimisation et le monitoring des performances
 */
class PerformanceAgent extends events_1.EventEmitter {
    logger;
    config;
    benchmarkEngine;
    optimizationAdvisor;
    monitoringIntegrator;
    communication;
    memory;
    isRunning = false;
    activeJobs = new Map();
    metrics = new Map();
    alerts = [];
    constructor(config) {
        super();
        this.config = config;
        this.logger = (0, logger_1.createLogger)('PerformanceAgent');
        // Initialiser les moteurs spécialisés
        this.benchmarkEngine = new BenchmarkEngine_1.BenchmarkEngine(config.benchmarking, this.logger);
        this.optimizationAdvisor = new OptimizationAdvisor_1.OptimizationAdvisor(this.logger);
        this.monitoringIntegrator = new MonitoringIntegrator_1.MonitoringIntegrator(config.monitoring, this.logger);
        // Initialiser la communication et la mémoire
        this.communication = new KafkaCommunication_1.KafkaCommunication(config.kafka, this.logger);
        this.memory = new WeaviateMemory_1.WeaviateMemory(config.weaviate, this.logger);
        this.setupEventHandlers();
        this.logger.info('PerformanceAgent initialisé avec succès');
    }
    /**
     * Démarrer l'agent
     */
    async start() {
        try {
            this.logger.info('Démarrage de l\'Agent Performance...');
            // Démarrer les composants
            await this.memory.connect();
            await this.communication.connect();
            await this.monitoringIntegrator.start();
            // Configurer les abonnements Kafka
            await this.setupKafkaSubscriptions();
            this.isRunning = true;
            this.emit('started');
            this.logger.info('Agent Performance démarré avec succès');
        }
        catch (error) {
            this.logger.error('Erreur lors du démarrage de l\'agent:', error);
            throw error;
        }
    }
    /**
     * Arrêter l'agent
     */
    async stop() {
        try {
            this.logger.info('Arrêt de l\'Agent Performance...');
            this.isRunning = false;
            // Arrêter les composants
            await this.monitoringIntegrator.stop();
            await this.communication.disconnect();
            await this.memory.disconnect();
            // Nettoyer les jobs actifs
            this.activeJobs.clear();
            this.emit('stopped');
            this.logger.info('Agent Performance arrêté avec succès');
        }
        catch (error) {
            this.logger.error('Erreur lors de l\'arrêt de l\'agent:', error);
            throw error;
        }
    }
    /**
     * Exécuter un benchmark
     */
    async executeBenchmark(request) {
        const jobId = request.id;
        this.logger.info(`Démarrage du benchmark ${jobId}`, { type: request.type, target: request.target });
        try {
            // Enregistrer le job actif
            this.activeJobs.set(jobId, { type: 'benchmark', startTime: Date.now(), request });
            // Exécuter le benchmark selon le type
            const result = await this.benchmarkEngine.execute(request);
            // Générer des recommandations
            const recommendations = await this.optimizationAdvisor.generateRecommendations(result);
            result.recommendations = recommendations;
            // Stocker les résultats en mémoire
            await this.memory.storeBenchmarkResult(result);
            // Publier les résultats
            await this.communication.publishResult(result);
            // Nettoyer le job
            this.activeJobs.delete(jobId);
            this.logger.info(`Benchmark ${jobId} terminé avec succès`);
            this.emit('benchmarkCompleted', result);
            return result;
        }
        catch (error) {
            this.logger.error(`Erreur lors du benchmark ${jobId}:`, error);
            this.activeJobs.delete(jobId);
            throw error;
        }
    }
    /**
     * Demander des optimisations
     */
    async requestOptimization(request) {
        this.logger.info(`Génération d'optimisations pour ${request.id}`);
        try {
            // Analyser le contexte
            const context = await this.analyzeOptimizationContext(request);
            // Générer les recommandations
            const recommendations = await this.optimizationAdvisor.generateOptimizations(request, context);
            // Stocker les recommandations
            await this.memory.storeRecommendations(recommendations);
            // Publier les recommandations
            await this.communication.publishRecommendations(recommendations);
            this.logger.info(`${recommendations.length} recommandations générées pour ${request.id}`);
            this.emit('optimizationsGenerated', recommendations);
            return recommendations;
        }
        catch (error) {
            this.logger.error(`Erreur lors de la génération d'optimisations:`, error);
            throw error;
        }
    }
    /**
     * Configurer le monitoring
     */
    async configureMonitoring(request) {
        this.logger.info(`Configuration du monitoring pour ${request.id}`);
        try {
            await this.monitoringIntegrator.configure(request);
            this.logger.info(`Monitoring configuré pour ${request.id}`);
            this.emit('monitoringConfigured', request);
        }
        catch (error) {
            this.logger.error(`Erreur lors de la configuration du monitoring:`, error);
            throw error;
        }
    }
    /**
     * Analyser une application/service
     */
    async analyzeApplication(request) {
        this.logger.info(`Analyse de l'application ${request.id}`);
        try {
            const analysis = await this.optimizationAdvisor.analyzeApplication(request);
            // Stocker l'analyse
            await this.memory.storeAnalysis(analysis);
            this.logger.info(`Analyse terminée pour ${request.id}`);
            this.emit('analysisCompleted', analysis);
            return analysis;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'analyse:`, error);
            throw error;
        }
    }
    /**
     * Obtenir les métriques actuelles
     */
    getCurrentMetrics() {
        return Object.fromEntries(this.metrics);
    }
    /**
     * Obtenir les alertes actives
     */
    getActiveAlerts() {
        return this.alerts.filter(alert => !alert.resolved);
    }
    /**
     * Obtenir le statut de l'agent
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            activeJobs: this.activeJobs.size,
            totalMetrics: Array.from(this.metrics.values()).reduce((sum, metrics) => sum + metrics.length, 0),
            activeAlerts: this.getActiveAlerts().length,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            timestamp: new Date()
        };
    }
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        // Gestionnaire de métriques
        this.monitoringIntegrator.on('metric', (metric) => {
            this.handleMetric(metric);
        });
        // Gestionnaire d'alertes
        this.monitoringIntegrator.on('alert', (alert) => {
            this.handleAlert(alert);
        });
        // Gestionnaire d'erreurs
        this.on('error', (error) => {
            this.logger.error('Erreur dans l\'agent:', error);
        });
    }
    /**
     * Configuration des abonnements Kafka
     */
    async setupKafkaSubscriptions() {
        await this.communication.subscribe('performance.requests', async (message) => {
            await this.handlePerformanceRequest(message);
        });
    }
    /**
     * Traiter une requête de performance
     */
    async handlePerformanceRequest(message) {
        try {
            const { type, payload } = message.value;
            switch (type) {
                case 'benchmark':
                    await this.executeBenchmark(payload);
                    break;
                case 'optimize':
                    await this.requestOptimization(payload);
                    break;
                case 'monitor':
                    await this.configureMonitoring(payload);
                    break;
                case 'analyze':
                    await this.analyzeApplication(payload);
                    break;
                default:
                    this.logger.warn(`Type de requête non supporté: ${type}`);
            }
        }
        catch (error) {
            this.logger.error('Erreur lors du traitement de la requête:', error);
        }
    }
    /**
     * Traiter une métrique
     */
    handleMetric(metric) {
        if (!this.metrics.has(metric.name)) {
            this.metrics.set(metric.name, []);
        }
        const metrics = this.metrics.get(metric.name);
        metrics.push(metric);
        // Limiter l'historique
        if (metrics.length > 1000) {
            metrics.shift();
        }
        this.emit('metric', metric);
    }
    /**
     * Traiter une alerte
     */
    handleAlert(alert) {
        this.alerts.push(alert);
        // Limiter l'historique des alertes
        if (this.alerts.length > 500) {
            this.alerts.shift();
        }
        this.logger.warn(`Alerte de performance: ${alert.title}`, alert);
        this.emit('alert', alert);
    }
    /**
     * Analyser le contexte d'optimisation
     */
    async analyzeOptimizationContext(request) {
        // Récupérer les patterns d'optimisation pertinents
        const patterns = await this.memory.getOptimizationPatterns(request);
        // Récupérer l'historique des performances
        const history = await this.memory.getPerformanceHistory(request.target);
        return {
            patterns,
            history,
            constraints: request.constraints,
            goals: request.goals
        };
    }
}
exports.PerformanceAgent = PerformanceAgent;
//# sourceMappingURL=PerformanceAgent.js.map