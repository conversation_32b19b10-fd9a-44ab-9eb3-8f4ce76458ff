{"version": 3, "file": "PerformanceAgent.js", "sourceRoot": "", "sources": ["../../src/core/PerformanceAgent.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AAEtC,gEAA6D;AAC7D,wEAAqE;AACrE,0EAAuE;AACvE,4EAAyE;AACzE,6DAA0D;AAC1D,4CAA+C;AAa/C;;;GAGG;AACH,MAAa,gBAAiB,SAAQ,qBAAY;IAC/B,MAAM,CAAS;IACf,MAAM,CAAoB;IAC1B,eAAe,CAAkB;IACjC,mBAAmB,CAAsB;IACzC,oBAAoB,CAAuB;IAC3C,aAAa,CAAqB;IAClC,MAAM,CAAiB;IAEhC,SAAS,GAAY,KAAK,CAAC;IAC3B,UAAU,GAAqB,IAAI,GAAG,EAAE,CAAC;IACzC,OAAO,GAAqC,IAAI,GAAG,EAAE,CAAC;IACtD,MAAM,GAAuB,EAAE,CAAC;IAExC,YAAY,MAAyB;QACnC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,IAAA,qBAAY,EAAC,kBAAkB,CAAC,CAAC;QAE/C,sCAAsC;QACtC,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7E,IAAI,CAAC,mBAAmB,GAAG,IAAI,yCAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAErF,6CAA6C;QAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACvE,IAAI,CAAC,MAAM,GAAG,IAAI,+BAAc,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAEzD,0BAA0B;YAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAExC,mCAAmC;YACnC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAErB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAErD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAEvB,yBAAyB;YACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAE/B,2BAA2B;YAC3B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAExB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAAyB;QAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAEpG,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;YAElF,sCAAsC;YACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE3D,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YACvF,MAAM,CAAC,eAAe,GAAG,eAAe,CAAC;YAEzC,mCAAmC;YACnC,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAE/C,wBAAwB;YACxB,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAE/C,kBAAkB;YAClB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,KAAK,sBAAsB,CAAC,CAAC;YAC3D,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;YAExC,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAA4B;QACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAE/D,8BAA8B;YAC9B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/F,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAExD,8BAA8B;YAC9B,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;YAEjE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,MAAM,kCAAkC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1F,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,eAAe,CAAC,CAAC;YAErD,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAA0B;QAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAEnE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAwB;QAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAE5E,oBAAoB;YACpB,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;YAEzC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YAChC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACjG,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM;YAC3C,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,4BAA4B;QAC5B,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAyB,EAAE,EAAE;YACnE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAuB,EAAE,EAAE;YAChE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,sBAAsB,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3E,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,OAAY;QACjD,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;YAExC,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,WAAW;oBACd,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;oBACrC,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxC,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACxC,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;oBACvC,MAAM;gBACR;oBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,MAAyB;QAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAErB,uBAAuB;QACvB,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAuB;QACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAExB,mCAAmC;QACnC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CAAC,OAA4B;QACnE,mDAAmD;QACnD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAEpE,0CAA0C;QAC1C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAExE,OAAO;YACL,QAAQ;YACR,OAAO;YACP,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC;IACJ,CAAC;CACF;AApUD,4CAoUC"}