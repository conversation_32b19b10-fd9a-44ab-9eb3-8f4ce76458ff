import { Logger } from 'winston';
import { WeaviateConfig, BenchmarkResult, Recommendation, OptimizationPattern, OptimizationRequest } from '../types';
/**
 * Mémoire Weaviate pour l'Agent Performance
 * Stockage vectoriel des patterns d'optimisation et historique des performances
 */
export declare class WeaviateMemory {
    private readonly config;
    private readonly logger;
    private client;
    private isConnected;
    constructor(config: WeaviateConfig, logger: Logger);
    /**
     * Se connecter à Weaviate
     */
    connect(): Promise<void>;
    /**
     * Se déconnecter de Weaviate
     */
    disconnect(): Promise<void>;
    /**
     * Stocker un résultat de benchmark
     */
    storeBenchmarkResult(result: BenchmarkResult): Promise<void>;
    /**
     * Stocker des recommandations
     */
    storeRecommendations(recommendations: Recommendation[]): Promise<void>;
    /**
     * Stocker un pattern d'optimisation
     */
    storeOptimizationPattern(pattern: OptimizationPattern): Promise<void>;
    /**
     * Stocker une analyse
     */
    storeAnalysis(analysis: any): Promise<void>;
    /**
     * Rechercher des patterns d'optimisation
     */
    getOptimizationPatterns(request: OptimizationRequest): Promise<OptimizationPattern[]>;
    /**
     * Obtenir l'historique des performances
     */
    getPerformanceHistory(target: any): Promise<any[]>;
    /**
     * Rechercher des recommandations similaires
     */
    findSimilarRecommendations(description: string, limit?: number): Promise<Recommendation[]>;
    /**
     * S'assurer que les collections existent
     */
    private ensureCollections;
    /**
     * Construire une requête de recherche
     */
    private buildSearchQuery;
    /**
     * Vérifier la santé de la connexion
     */
    isHealthy(): boolean;
    /**
     * Obtenir les statistiques de la mémoire
     */
    getStats(): any;
}
//# sourceMappingURL=WeaviateMemory.d.ts.map