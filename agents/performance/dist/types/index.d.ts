export interface PerformanceConfig {
    port: number;
    kafka: KafkaConfig;
    weaviate: WeaviateConfig;
    prometheus: PrometheusConfig;
    benchmarking: BenchmarkingConfig;
    monitoring: MonitoringConfig;
}
export interface KafkaConfig {
    brokers: string[];
    clientId: string;
    groupId: string;
    topics: {
        requests: string;
        results: string;
        alerts: string;
        recommendations: string;
    };
}
export interface WeaviateConfig {
    host: string;
    scheme: string;
    apiKey?: string;
    collections: {
        optimizations: string;
        benchmarks: string;
        recommendations: string;
        metrics: string;
    };
}
export interface PrometheusConfig {
    enabled: boolean;
    port: number;
    path: string;
    collectDefaultMetrics: boolean;
}
export interface BenchmarkingConfig {
    lighthouse: {
        enabled: boolean;
        categories: string[];
        formFactor: 'mobile' | 'desktop';
        throttling: 'simulated3G' | 'applied3G' | 'none';
    };
    loadTesting: {
        enabled: boolean;
        tool: 'k6' | 'autocannon';
        duration: string;
        connections: number;
        rps: number;
    };
    profiling: {
        enabled: boolean;
        tool: 'clinic' | '0x';
        duration: number;
    };
}
export interface MonitoringConfig {
    interval: number;
    thresholds: {
        responseTime: number;
        cpuUsage: number;
        memoryUsage: number;
        errorRate: number;
    };
    alerts: {
        enabled: boolean;
        channels: string[];
    };
}
export interface BenchmarkRequest {
    id: string;
    type: 'lighthouse' | 'load' | 'profiling' | 'full';
    target: {
        url?: string;
        service?: string;
        endpoint?: string;
    };
    configuration: {
        lighthouse?: LighthouseOptions;
        loadTesting?: LoadTestingOptions;
        profiling?: ProfilingOptions;
    };
    metadata?: Record<string, any>;
}
export interface LighthouseOptions {
    categories: string[];
    formFactor: 'mobile' | 'desktop';
    throttling: string;
    emulatedUserAgent?: string;
    locale?: string;
}
export interface LoadTestingOptions {
    duration: string;
    connections: number;
    rps?: number;
    headers?: Record<string, string>;
    body?: string;
    method?: string;
}
export interface ProfilingOptions {
    duration: number;
    sampleRate?: number;
    includeMemory?: boolean;
    includeCpu?: boolean;
}
export interface BenchmarkResult {
    id: string;
    type: string;
    timestamp: Date;
    duration: number;
    status: 'success' | 'error' | 'timeout';
    results: {
        lighthouse?: LighthouseResult;
        loadTesting?: LoadTestingResult;
        profiling?: ProfilingResult;
    };
    recommendations: Recommendation[];
    metadata?: Record<string, any>;
}
export interface LighthouseResult {
    score: number;
    categories: {
        performance: number;
        accessibility: number;
        bestPractices: number;
        seo: number;
        pwa?: number;
    };
    metrics: {
        firstContentfulPaint: number;
        largestContentfulPaint: number;
        firstInputDelay: number;
        cumulativeLayoutShift: number;
        speedIndex: number;
        totalBlockingTime: number;
    };
    opportunities: Array<{
        id: string;
        title: string;
        description: string;
        savings: number;
        impact: 'low' | 'medium' | 'high';
    }>;
}
export interface LoadTestingResult {
    requestsPerSecond: number;
    averageLatency: number;
    p95Latency: number;
    p99Latency: number;
    errorRate: number;
    totalRequests: number;
    totalErrors: number;
    throughput: number;
    connectionErrors: number;
    timeouts: number;
}
export interface ProfilingResult {
    cpuProfile: {
        totalTime: number;
        idleTime: number;
        userTime: number;
        systemTime: number;
        hotspots: Array<{
            function: string;
            file: string;
            line: number;
            selfTime: number;
            totalTime: number;
        }>;
    };
    memoryProfile: {
        heapUsed: number;
        heapTotal: number;
        external: number;
        rss: number;
        leaks: Array<{
            type: string;
            size: number;
            location: string;
        }>;
    };
}
export interface Recommendation {
    id: string;
    type: 'performance' | 'optimization' | 'architecture' | 'security';
    priority: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description: string;
    impact: {
        performance: number;
        complexity: number;
        cost: number;
    };
    implementation: {
        effort: 'low' | 'medium' | 'high';
        timeEstimate: string;
        steps: string[];
        codeExamples?: string[];
    };
    metrics: {
        before: Record<string, number>;
        expectedAfter: Record<string, number>;
    };
}
export interface PerformanceMetric {
    name: string;
    value: number;
    unit: string;
    timestamp: Date;
    labels: Record<string, string>;
    type: 'counter' | 'gauge' | 'histogram' | 'summary';
}
export interface PerformanceAlert {
    id: string;
    type: 'threshold' | 'anomaly' | 'trend';
    severity: 'info' | 'warning' | 'error' | 'critical';
    title: string;
    description: string;
    metric: string;
    value: number;
    threshold?: number;
    timestamp: Date;
    resolved: boolean;
    resolvedAt?: Date;
}
export interface OptimizationPattern {
    id: string;
    name: string;
    description: string;
    category: string;
    applicability: {
        technologies: string[];
        architectures: string[];
        contexts: string[];
    };
    implementation: {
        before: string;
        after: string;
        steps: string[];
    };
    impact: {
        performance: number;
        maintainability: number;
        scalability: number;
    };
    examples: Array<{
        scenario: string;
        improvement: number;
        metrics: Record<string, number>;
    }>;
}
export interface KafkaMessage {
    key: string;
    value: any;
    headers?: Record<string, string>;
    timestamp?: Date;
}
export interface PerformanceRequest extends KafkaMessage {
    value: {
        type: 'benchmark' | 'optimize' | 'monitor' | 'analyze';
        payload: BenchmarkRequest | OptimizationRequest | MonitoringRequest | AnalysisRequest;
        requestId: string;
        agentId: string;
        timestamp: Date;
    };
}
export interface OptimizationRequest {
    id: string;
    target: {
        codebase?: string;
        service?: string;
        architecture?: string;
    };
    scope: string[];
    constraints: {
        budget?: number;
        timeline?: string;
        technologies?: string[];
    };
    goals: {
        performance?: number;
        scalability?: number;
        maintainability?: number;
    };
}
export interface MonitoringRequest {
    id: string;
    targets: string[];
    metrics: string[];
    thresholds: Record<string, number>;
    duration?: string;
    interval?: number;
}
export interface AnalysisRequest {
    id: string;
    type: 'code' | 'architecture' | 'infrastructure';
    target: string;
    depth: 'shallow' | 'deep' | 'comprehensive';
    focus: string[];
}
//# sourceMappingURL=index.d.ts.map