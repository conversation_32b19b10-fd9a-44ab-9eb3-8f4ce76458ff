{"name": "@agents/performance", "version": "1.0.0", "description": "Agent Performance - Optimisation et monitoring avancé pour l'écosystème d'agents", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist", "docker:build": "docker build -t agent-performance .", "docker:run": "docker run -p 3007:3007 agent-performance"}, "keywords": ["performance", "monitoring", "optimization", "benchmarking", "agent", "microservice"], "author": "Retreat And Be Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "kafkajs": "^2.2.4", "weaviate-ts-client": "^1.5.0", "prometheus-api-metrics": "^3.2.2", "prom-client": "^15.1.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "axios": "^1.6.2", "lighthouse": "^11.4.0", "puppeteer": "^21.6.1", "k6": "^0.47.0", "autocannon": "^7.12.0", "clinic": "^13.0.0", "0x": "^5.5.0", "node-clinic": "^13.0.0", "systeminformation": "^5.21.20", "pidusage": "^3.0.2", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}