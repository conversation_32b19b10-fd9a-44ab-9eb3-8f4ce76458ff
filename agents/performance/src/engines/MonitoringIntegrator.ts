import { EventEmitter } from 'events';
import { Logger } from 'winston';
import * as promClient from 'prom-client';
import * as si from 'systeminformation';
import { 
  MonitoringConfig, 
  MonitoringRequest,
  PerformanceMetric,
  PerformanceAlert
} from '../types';

/**
 * Intégrateur de monitoring temps réel
 * Collecte et analyse les métriques de performance
 */
export class MonitoringIntegrator extends EventEmitter {
  private readonly config: MonitoringConfig;
  private readonly logger: Logger;
  private readonly registry: promClient.Registry;
  private readonly metrics: Map<string, promClient.Metric> = new Map();
  
  private isRunning: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private activeMonitors: Map<string, any> = new Map();
  private alertHistory: PerformanceAlert[] = [];

  // Métriques Prometheus
  private cpuUsageGauge: promClient.Gauge;
  private memoryUsageGauge: promClient.Gauge;
  private responseTimeHistogram: promClient.Histogram;
  private requestCounter: promClient.Counter;
  private errorCounter: promClient.Counter;

  constructor(config: MonitoringConfig, logger: Logger) {
    super();
    this.config = config;
    this.logger = logger;
    this.registry = new promClient.Registry();
    
    this.initializePrometheusMetrics();
    this.logger.info('MonitoringIntegrator initialisé');
  }

  /**
   * Démarrer le monitoring
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Le monitoring est déjà en cours');
      return;
    }

    this.logger.info('Démarrage du monitoring...');
    
    try {
      // Démarrer la collecte de métriques par défaut
      promClient.collectDefaultMetrics({ register: this.registry });
      
      // Démarrer la surveillance système
      this.startSystemMonitoring();
      
      this.isRunning = true;
      this.emit('started');
      
      this.logger.info('Monitoring démarré avec succès');
    } catch (error) {
      this.logger.error('Erreur lors du démarrage du monitoring:', error);
      throw error;
    }
  }

  /**
   * Arrêter le monitoring
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('Arrêt du monitoring...');
    
    try {
      // Arrêter l'intervalle de monitoring
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }
      
      // Arrêter tous les monitors actifs
      this.activeMonitors.clear();
      
      this.isRunning = false;
      this.emit('stopped');
      
      this.logger.info('Monitoring arrêté');
    } catch (error) {
      this.logger.error('Erreur lors de l\'arrêt du monitoring:', error);
      throw error;
    }
  }

  /**
   * Configurer un monitoring spécifique
   */
  async configure(request: MonitoringRequest): Promise<void> {
    this.logger.info(`Configuration du monitoring pour ${request.id}`);
    
    try {
      const monitor = {
        id: request.id,
        targets: request.targets,
        metrics: request.metrics,
        thresholds: request.thresholds,
        interval: request.interval || this.config.interval,
        startTime: Date.now()
      };
      
      this.activeMonitors.set(request.id, monitor);
      
      // Démarrer le monitoring spécifique
      this.startSpecificMonitoring(monitor);
      
      this.logger.info(`Monitoring configuré pour ${request.id}`);
      this.emit('monitorConfigured', monitor);
    } catch (error) {
      this.logger.error(`Erreur lors de la configuration du monitoring:`, error);
      throw error;
    }
  }

  /**
   * Enregistrer une métrique
   */
  recordMetric(name: string, value: number, labels: Record<string, string> = {}): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit: 'ms',
      timestamp: new Date(),
      labels,
      type: 'gauge'
    };

    // Mettre à jour les métriques Prometheus
    this.updatePrometheusMetric(metric);
    
    // Vérifier les seuils
    this.checkThresholds(metric);
    
    this.emit('metric', metric);
  }

  /**
   * Enregistrer le temps de réponse
   */
  recordResponseTime(endpoint: string, duration: number, statusCode: number): void {
    const labels = { endpoint, status_code: statusCode.toString() };
    
    this.responseTimeHistogram.observe(labels, duration);
    this.requestCounter.inc(labels);
    
    if (statusCode >= 400) {
      this.errorCounter.inc(labels);
    }
    
    this.recordMetric('response_time', duration, labels);
  }

  /**
   * Obtenir les métriques Prometheus
   */
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  /**
   * Obtenir les alertes actives
   */
  getActiveAlerts(): PerformanceAlert[] {
    return this.alertHistory.filter(alert => !alert.resolved);
  }

  /**
   * Résoudre une alerte
   */
  resolveAlert(alertId: string): void {
    const alert = this.alertHistory.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      this.logger.info(`Alerte ${alertId} résolue`);
      this.emit('alertResolved', alert);
    }
  }

  /**
   * Initialiser les métriques Prometheus
   */
  private initializePrometheusMetrics(): void {
    // CPU Usage
    this.cpuUsageGauge = new promClient.Gauge({
      name: 'system_cpu_usage_percent',
      help: 'Current CPU usage percentage',
      registers: [this.registry]
    });

    // Memory Usage
    this.memoryUsageGauge = new promClient.Gauge({
      name: 'system_memory_usage_bytes',
      help: 'Current memory usage in bytes',
      labelNames: ['type'],
      registers: [this.registry]
    });

    // Response Time
    this.responseTimeHistogram = new promClient.Histogram({
      name: 'http_request_duration_ms',
      help: 'Duration of HTTP requests in milliseconds',
      labelNames: ['endpoint', 'status_code'],
      buckets: [1, 5, 15, 50, 100, 500, 1000, 5000],
      registers: [this.registry]
    });

    // Request Counter
    this.requestCounter = new promClient.Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['endpoint', 'status_code'],
      registers: [this.registry]
    });

    // Error Counter
    this.errorCounter = new promClient.Counter({
      name: 'http_errors_total',
      help: 'Total number of HTTP errors',
      labelNames: ['endpoint', 'status_code'],
      registers: [this.registry]
    });
  }

  /**
   * Démarrer la surveillance système
   */
  private startSystemMonitoring(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectSystemMetrics();
      } catch (error) {
        this.logger.error('Erreur lors de la collecte des métriques système:', error);
      }
    }, this.config.interval);
  }

  /**
   * Collecter les métriques système
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      // CPU
      const cpuData = await si.currentLoad();
      this.cpuUsageGauge.set(cpuData.currentLoad);
      this.recordMetric('cpu_usage', cpuData.currentLoad, { type: 'system' });

      // Mémoire
      const memData = await si.mem();
      this.memoryUsageGauge.set({ type: 'used' }, memData.used);
      this.memoryUsageGauge.set({ type: 'free' }, memData.free);
      this.memoryUsageGauge.set({ type: 'total' }, memData.total);
      
      const memoryUsagePercent = (memData.used / memData.total) * 100;
      this.recordMetric('memory_usage', memoryUsagePercent, { type: 'system' });

      // Disque
      const diskData = await si.fsSize();
      if (diskData.length > 0) {
        const mainDisk = diskData[0];
        const diskUsagePercent = (mainDisk.used / mainDisk.size) * 100;
        this.recordMetric('disk_usage', diskUsagePercent, { type: 'system' });
      }

      // Réseau
      const networkData = await si.networkStats();
      if (networkData.length > 0) {
        const mainInterface = networkData[0];
        this.recordMetric('network_rx', mainInterface.rx_sec || 0, { type: 'system', direction: 'rx' });
        this.recordMetric('network_tx', mainInterface.tx_sec || 0, { type: 'system', direction: 'tx' });
      }

    } catch (error) {
      this.logger.error('Erreur lors de la collecte des métriques système:', error);
    }
  }

  /**
   * Démarrer un monitoring spécifique
   */
  private startSpecificMonitoring(monitor: any): void {
    const interval = setInterval(async () => {
      try {
        for (const target of monitor.targets) {
          await this.monitorTarget(target, monitor);
        }
      } catch (error) {
        this.logger.error(`Erreur lors du monitoring de ${monitor.id}:`, error);
      }
    }, monitor.interval);

    monitor.interval = interval;
  }

  /**
   * Monitorer une cible spécifique
   */
  private async monitorTarget(target: string, monitor: any): Promise<void> {
    // Simuler le monitoring d'une cible
    // Dans une implémentation réelle, ceci ferait des appels HTTP, des requêtes DB, etc.
    
    const responseTime = Math.random() * 1000; // Simulation
    const isHealthy = responseTime < 500;
    
    this.recordMetric(`${target}_response_time`, responseTime, { 
      target, 
      monitor_id: monitor.id 
    });
    
    this.recordMetric(`${target}_health`, isHealthy ? 1 : 0, { 
      target, 
      monitor_id: monitor.id 
    });
  }

  /**
   * Mettre à jour une métrique Prometheus
   */
  private updatePrometheusMetric(metric: PerformanceMetric): void {
    // Logique pour mettre à jour les métriques Prometheus personnalisées
    // selon le type de métrique
  }

  /**
   * Vérifier les seuils et générer des alertes
   */
  private checkThresholds(metric: PerformanceMetric): void {
    const thresholds = this.config.thresholds;
    
    let alertTriggered = false;
    let severity: 'info' | 'warning' | 'error' | 'critical' = 'info';
    
    // Vérifier les seuils selon le type de métrique
    switch (metric.name) {
      case 'cpu_usage':
        if (metric.value > thresholds.cpuUsage) {
          alertTriggered = true;
          severity = metric.value > 90 ? 'critical' : 'warning';
        }
        break;
      
      case 'memory_usage':
        if (metric.value > thresholds.memoryUsage) {
          alertTriggered = true;
          severity = metric.value > 95 ? 'critical' : 'warning';
        }
        break;
      
      case 'response_time':
        if (metric.value > thresholds.responseTime) {
          alertTriggered = true;
          severity = metric.value > thresholds.responseTime * 2 ? 'critical' : 'warning';
        }
        break;
    }
    
    if (alertTriggered) {
      this.createAlert(metric, severity);
    }
  }

  /**
   * Créer une alerte
   */
  private createAlert(metric: PerformanceMetric, severity: 'info' | 'warning' | 'error' | 'critical'): void {
    const alert: PerformanceAlert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'threshold',
      severity,
      title: `Seuil dépassé pour ${metric.name}`,
      description: `La métrique ${metric.name} a atteint ${metric.value}${metric.unit}`,
      metric: metric.name,
      value: metric.value,
      threshold: this.getThresholdForMetric(metric.name),
      timestamp: new Date(),
      resolved: false
    };
    
    this.alertHistory.push(alert);
    
    // Limiter l'historique des alertes
    if (this.alertHistory.length > 1000) {
      this.alertHistory.shift();
    }
    
    this.logger.warn(`Alerte générée: ${alert.title}`, alert);
    this.emit('alert', alert);
  }

  /**
   * Obtenir le seuil pour une métrique
   */
  private getThresholdForMetric(metricName: string): number {
    const thresholds = this.config.thresholds;
    
    switch (metricName) {
      case 'cpu_usage':
        return thresholds.cpuUsage;
      case 'memory_usage':
        return thresholds.memoryUsage;
      case 'response_time':
        return thresholds.responseTime;
      default:
        return 0;
    }
  }
}
