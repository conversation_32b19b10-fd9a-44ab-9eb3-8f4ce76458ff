import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { Logger } from 'winston';
import { 
  WeaviateConfig, 
  BenchmarkResult,
  Recommendation,
  OptimizationPattern,
  OptimizationRequest
} from '../types';

/**
 * Mémoire Weaviate pour l'Agent Performance
 * Stockage vectoriel des patterns d'optimisation et historique des performances
 */
export class WeaviateMemory {
  private readonly config: WeaviateConfig;
  private readonly logger: Logger;
  private client: WeaviateClient | null = null;
  private isConnected: boolean = false;

  constructor(config: WeaviateConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.logger.info('WeaviateMemory initialisé');
  }

  /**
   * Se connecter à Weaviate
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      this.logger.warn('Déjà connecté à Weaviate');
      return;
    }

    try {
      this.logger.info('Connexion à Weaviate...');
      
      this.client = weaviate.client({
        scheme: this.config.scheme,
        host: this.config.host,
        apiKey: this.config.apiKey ? weaviate.apiKey(this.config.apiKey) : undefined
      });

      // Vérifier la connexion
      const isReady = await this.client.misc.readyChecker().do();
      if (!isReady) {
        throw new Error('Weaviate n\'est pas prêt');
      }

      // Créer les collections si elles n'existent pas
      await this.ensureCollections();
      
      this.isConnected = true;
      this.logger.info('Connexion Weaviate établie avec succès');
      
    } catch (error) {
      this.logger.error('Erreur lors de la connexion à Weaviate:', error);
      throw error;
    }
  }

  /**
   * Se déconnecter de Weaviate
   */
  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    this.isConnected = false;
    this.client = null;
    this.logger.info('Déconnexion Weaviate terminée');
  }

  /**
   * Stocker un résultat de benchmark
   */
  async storeBenchmarkResult(result: BenchmarkResult): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      const data = {
        benchmarkId: result.id,
        type: result.type,
        timestamp: result.timestamp.toISOString(),
        duration: result.duration,
        status: result.status,
        results: JSON.stringify(result.results),
        recommendations: JSON.stringify(result.recommendations),
        metadata: JSON.stringify(result.metadata || {})
      };

      await this.client
        .data
        .creator()
        .withClassName(this.config.collections.benchmarks)
        .withProperties(data)
        .do();

      this.logger.info(`Résultat de benchmark stocké: ${result.id}`);
      
    } catch (error) {
      this.logger.error('Erreur lors du stockage du résultat de benchmark:', error);
      throw error;
    }
  }

  /**
   * Stocker des recommandations
   */
  async storeRecommendations(recommendations: Recommendation[]): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      for (const recommendation of recommendations) {
        const data = {
          recommendationId: recommendation.id,
          type: recommendation.type,
          priority: recommendation.priority,
          title: recommendation.title,
          description: recommendation.description,
          impact: JSON.stringify(recommendation.impact),
          implementation: JSON.stringify(recommendation.implementation),
          metrics: JSON.stringify(recommendation.metrics),
          timestamp: new Date().toISOString()
        };

        await this.client
          .data
          .creator()
          .withClassName(this.config.collections.recommendations)
          .withProperties(data)
          .do();
      }

      this.logger.info(`${recommendations.length} recommandations stockées`);
      
    } catch (error) {
      this.logger.error('Erreur lors du stockage des recommandations:', error);
      throw error;
    }
  }

  /**
   * Stocker un pattern d'optimisation
   */
  async storeOptimizationPattern(pattern: OptimizationPattern): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      const data = {
        patternId: pattern.id,
        name: pattern.name,
        description: pattern.description,
        category: pattern.category,
        applicability: JSON.stringify(pattern.applicability),
        implementation: JSON.stringify(pattern.implementation),
        impact: JSON.stringify(pattern.impact),
        examples: JSON.stringify(pattern.examples),
        timestamp: new Date().toISOString()
      };

      await this.client
        .data
        .creator()
        .withClassName(this.config.collections.optimizations)
        .withProperties(data)
        .do();

      this.logger.info(`Pattern d'optimisation stocké: ${pattern.id}`);
      
    } catch (error) {
      this.logger.error('Erreur lors du stockage du pattern d\'optimisation:', error);
      throw error;
    }
  }

  /**
   * Stocker une analyse
   */
  async storeAnalysis(analysis: any): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      const data = {
        analysisId: analysis.id,
        type: analysis.type,
        target: analysis.target,
        timestamp: analysis.timestamp.toISOString(),
        findings: JSON.stringify(analysis.findings),
        recommendations: JSON.stringify(analysis.recommendations),
        metrics: JSON.stringify(analysis.metrics),
        issues: JSON.stringify(analysis.issues)
      };

      await this.client
        .data
        .creator()
        .withClassName(this.config.collections.metrics)
        .withProperties(data)
        .do();

      this.logger.info(`Analyse stockée: ${analysis.id}`);
      
    } catch (error) {
      this.logger.error('Erreur lors du stockage de l\'analyse:', error);
      throw error;
    }
  }

  /**
   * Rechercher des patterns d'optimisation
   */
  async getOptimizationPatterns(request: OptimizationRequest): Promise<OptimizationPattern[]> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      // Construire la requête de recherche sémantique
      const searchQuery = this.buildSearchQuery(request);
      
      const result = await this.client
        .graphql
        .get()
        .withClassName(this.config.collections.optimizations)
        .withFields('patternId name description category applicability implementation impact examples')
        .withNearText({ concepts: [searchQuery] })
        .withLimit(10)
        .do();

      const patterns: OptimizationPattern[] = [];
      
      if (result.data?.Get?.[this.config.collections.optimizations]) {
        for (const item of result.data.Get[this.config.collections.optimizations]) {
          patterns.push({
            id: item.patternId,
            name: item.name,
            description: item.description,
            category: item.category,
            applicability: JSON.parse(item.applicability),
            implementation: JSON.parse(item.implementation),
            impact: JSON.parse(item.impact),
            examples: JSON.parse(item.examples)
          });
        }
      }

      this.logger.info(`${patterns.length} patterns d'optimisation trouvés`);
      return patterns;
      
    } catch (error) {
      this.logger.error('Erreur lors de la recherche de patterns:', error);
      return [];
    }
  }

  /**
   * Obtenir l'historique des performances
   */
  async getPerformanceHistory(target: any): Promise<any[]> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      const result = await this.client
        .graphql
        .get()
        .withClassName(this.config.collections.benchmarks)
        .withFields('benchmarkId type timestamp duration status results')
        .withWhere({
          path: ['metadata'],
          operator: 'Like',
          valueText: `*${JSON.stringify(target)}*`
        })
        .withSort([{ path: ['timestamp'], order: 'desc' }])
        .withLimit(50)
        .do();

      const history: any[] = [];
      
      if (result.data?.Get?.[this.config.collections.benchmarks]) {
        for (const item of result.data.Get[this.config.collections.benchmarks]) {
          history.push({
            id: item.benchmarkId,
            type: item.type,
            timestamp: new Date(item.timestamp),
            duration: item.duration,
            status: item.status,
            results: JSON.parse(item.results)
          });
        }
      }

      this.logger.info(`${history.length} entrées d'historique trouvées`);
      return history;
      
    } catch (error) {
      this.logger.error('Erreur lors de la récupération de l\'historique:', error);
      return [];
    }
  }

  /**
   * Rechercher des recommandations similaires
   */
  async findSimilarRecommendations(description: string, limit: number = 5): Promise<Recommendation[]> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client Weaviate non connecté');
    }

    try {
      const result = await this.client
        .graphql
        .get()
        .withClassName(this.config.collections.recommendations)
        .withFields('recommendationId type priority title description impact implementation metrics')
        .withNearText({ concepts: [description] })
        .withLimit(limit)
        .do();

      const recommendations: Recommendation[] = [];
      
      if (result.data?.Get?.[this.config.collections.recommendations]) {
        for (const item of result.data.Get[this.config.collections.recommendations]) {
          recommendations.push({
            id: item.recommendationId,
            type: item.type,
            priority: item.priority,
            title: item.title,
            description: item.description,
            impact: JSON.parse(item.impact),
            implementation: JSON.parse(item.implementation),
            metrics: JSON.parse(item.metrics)
          });
        }
      }

      this.logger.info(`${recommendations.length} recommandations similaires trouvées`);
      return recommendations;
      
    } catch (error) {
      this.logger.error('Erreur lors de la recherche de recommandations:', error);
      return [];
    }
  }

  /**
   * S'assurer que les collections existent
   */
  private async ensureCollections(): Promise<void> {
    if (!this.client) {
      throw new Error('Client Weaviate non initialisé');
    }

    const collections = [
      {
        name: this.config.collections.benchmarks,
        properties: [
          { name: 'benchmarkId', dataType: ['text'] },
          { name: 'type', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] },
          { name: 'duration', dataType: ['number'] },
          { name: 'status', dataType: ['text'] },
          { name: 'results', dataType: ['text'] },
          { name: 'recommendations', dataType: ['text'] },
          { name: 'metadata', dataType: ['text'] }
        ]
      },
      {
        name: this.config.collections.recommendations,
        properties: [
          { name: 'recommendationId', dataType: ['text'] },
          { name: 'type', dataType: ['text'] },
          { name: 'priority', dataType: ['text'] },
          { name: 'title', dataType: ['text'] },
          { name: 'description', dataType: ['text'] },
          { name: 'impact', dataType: ['text'] },
          { name: 'implementation', dataType: ['text'] },
          { name: 'metrics', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] }
        ]
      },
      {
        name: this.config.collections.optimizations,
        properties: [
          { name: 'patternId', dataType: ['text'] },
          { name: 'name', dataType: ['text'] },
          { name: 'description', dataType: ['text'] },
          { name: 'category', dataType: ['text'] },
          { name: 'applicability', dataType: ['text'] },
          { name: 'implementation', dataType: ['text'] },
          { name: 'impact', dataType: ['text'] },
          { name: 'examples', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] }
        ]
      },
      {
        name: this.config.collections.metrics,
        properties: [
          { name: 'analysisId', dataType: ['text'] },
          { name: 'type', dataType: ['text'] },
          { name: 'target', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] },
          { name: 'findings', dataType: ['text'] },
          { name: 'recommendations', dataType: ['text'] },
          { name: 'metrics', dataType: ['text'] },
          { name: 'issues', dataType: ['text'] }
        ]
      }
    ];

    for (const collection of collections) {
      try {
        // Vérifier si la collection existe
        const exists = await this.client
          .schema
          .exists(collection.name)
          .do();

        if (!exists) {
          // Créer la collection
          await this.client
            .schema
            .classCreator()
            .withClass({
              class: collection.name,
              properties: collection.properties,
              vectorizer: 'text2vec-openai'
            })
            .do();

          this.logger.info(`Collection créée: ${collection.name}`);
        }
      } catch (error) {
        this.logger.error(`Erreur lors de la création de la collection ${collection.name}:`, error);
      }
    }
  }

  /**
   * Construire une requête de recherche
   */
  private buildSearchQuery(request: OptimizationRequest): string {
    const parts = [];
    
    if (request.target.service) {
      parts.push(request.target.service);
    }
    
    if (request.target.architecture) {
      parts.push(request.target.architecture);
    }
    
    if (request.scope) {
      parts.push(...request.scope);
    }
    
    if (request.constraints.technologies) {
      parts.push(...request.constraints.technologies);
    }
    
    return parts.join(' ');
  }

  /**
   * Vérifier la santé de la connexion
   */
  isHealthy(): boolean {
    return this.isConnected && this.client !== null;
  }

  /**
   * Obtenir les statistiques de la mémoire
   */
  getStats(): any {
    return {
      isConnected: this.isConnected,
      hasClient: this.client !== null,
      collections: this.config.collections
    };
  }
}
