/**
 * Types pour l'Agent Project Manager
 */

export interface AgentConfig {
  port: number;
  kafka: {
    brokers: string[];
    clientId: string;
    groupId: string;
  };
  weaviate: {
    scheme: string;
    host: string;
    port: number;
  };
  redis: {
    host: string;
    port: number;
    password?: string;
  };
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  priority: Priority;
  startDate: Date;
  endDate: Date;
  estimatedDuration: number;
  actualDuration?: number;
  budget: Budget;
  team: TeamMember[];
  tasks: Task[];
  milestones: Milestone[];
  risks: Risk[];
  dependencies: ProjectDependency[];
  metadata: ProjectMetadata;
}

export type ProjectStatus = 
  | 'planning'
  | 'active'
  | 'on_hold'
  | 'completed'
  | 'cancelled'
  | 'delayed';

export type Priority = 'low' | 'medium' | 'high' | 'critical';

export interface Budget {
  total: number;
  allocated: number;
  spent: number;
  remaining: number;
  currency: string;
  breakdown: BudgetItem[];
}

export interface BudgetItem {
  category: string;
  allocated: number;
  spent: number;
  description?: string;
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  skills: Skill[];
  availability: Availability;
  workload: number;
  hourlyRate?: number;
  department?: string;
}

export interface Skill {
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  yearsOfExperience: number;
  certifications?: string[];
}

export interface Availability {
  hoursPerWeek: number;
  startDate: Date;
  endDate?: Date;
  unavailableDates: Date[];
  workingHours: WorkingHours;
}

export interface WorkingHours {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string;
  end: string;
}

export interface Task {
  id: string;
  name: string;
  description: string;
  status: TaskStatus;
  priority: Priority;
  type: TaskType;
  estimatedHours: number;
  actualHours?: number;
  startDate: Date;
  endDate: Date;
  assignee?: string;
  assignees: string[];
  dependencies: TaskDependency[];
  subtasks: Task[];
  progress: number;
  tags: string[];
  attachments: Attachment[];
  comments: Comment[];
  timeTracking: TimeEntry[];
}

export type TaskStatus = 
  | 'not_started'
  | 'in_progress'
  | 'blocked'
  | 'review'
  | 'completed'
  | 'cancelled';

export type TaskType = 
  | 'development'
  | 'design'
  | 'testing'
  | 'documentation'
  | 'research'
  | 'meeting'
  | 'review'
  | 'deployment'
  | 'maintenance';

export interface TaskDependency {
  taskId: string;
  type: DependencyType;
  lag?: number;
}

export type DependencyType = 
  | 'finish_to_start'
  | 'start_to_start'
  | 'finish_to_finish'
  | 'start_to_finish';

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
}

export interface Comment {
  id: string;
  text: string;
  author: string;
  createdAt: Date;
  updatedAt?: Date;
  mentions: string[];
  attachments: Attachment[];
}

export interface TimeEntry {
  id: string;
  userId: string;
  taskId: string;
  date: Date;
  hours: number;
  description?: string;
  billable: boolean;
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  date: Date;
  status: MilestoneStatus;
  criteria: string[];
  deliverables: Deliverable[];
  dependencies: string[];
}

export type MilestoneStatus = 'pending' | 'achieved' | 'missed' | 'at_risk';

export interface Deliverable {
  id: string;
  name: string;
  description: string;
  type: string;
  status: DeliverableStatus;
  dueDate: Date;
  assignee: string;
  approver?: string;
  location?: string;
}

export type DeliverableStatus = 'not_started' | 'in_progress' | 'completed' | 'approved' | 'rejected';

export interface Risk {
  id: string;
  title: string;
  description: string;
  category: RiskCategory;
  probability: number;
  impact: number;
  riskScore: number;
  status: RiskStatus;
  owner: string;
  identifiedDate: Date;
  mitigationPlan: MitigationPlan;
  contingencyPlan?: ContingencyPlan;
}

export type RiskCategory = 
  | 'technical'
  | 'resource'
  | 'schedule'
  | 'budget'
  | 'quality'
  | 'external'
  | 'regulatory'
  | 'security';

export type RiskStatus = 'identified' | 'assessed' | 'mitigated' | 'closed' | 'occurred';

export interface MitigationPlan {
  actions: MitigationAction[];
  timeline: string;
  budget?: number;
  responsible: string;
  status: 'planned' | 'in_progress' | 'completed';
}

export interface MitigationAction {
  id: string;
  description: string;
  responsible: string;
  dueDate: Date;
  status: 'pending' | 'in_progress' | 'completed';
  effectiveness?: number;
}

export interface ContingencyPlan {
  trigger: string;
  actions: string[];
  responsible: string;
  budget?: number;
  timeline: string;
}

export interface ProjectDependency {
  projectId: string;
  type: DependencyType;
  description: string;
  impact: 'low' | 'medium' | 'high';
}

export interface ProjectMetadata {
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
  version: string;
  tags: string[];
  customFields: Record<string, any>;
}

export interface ProjectPlan {
  id: string;
  projectId: string;
  name: string;
  version: string;
  ganttChart: GanttChart;
  criticalPath: CriticalPath;
  resourceAllocation: ResourceAllocation;
  timeline: Timeline;
  workBreakdownStructure: WBS;
}

export interface GanttChart {
  tasks: GanttTask[];
  dependencies: GanttDependency[];
  milestones: GanttMilestone[];
  timeline: GanttTimeline;
}

export interface GanttTask {
  id: string;
  name: string;
  start: Date;
  end: Date;
  duration: number;
  progress: number;
  assignee: string;
  color?: string;
  parent?: string;
}

export interface GanttDependency {
  from: string;
  to: string;
  type: DependencyType;
  lag?: number;
}

export interface GanttMilestone {
  id: string;
  name: string;
  date: Date;
  achieved: boolean;
}

export interface GanttTimeline {
  start: Date;
  end: Date;
  unit: 'day' | 'week' | 'month';
  workingDays: number[];
  holidays: Date[];
}

export interface CriticalPath {
  tasks: string[];
  duration: number;
  slack: number;
  bottlenecks: Bottleneck[];
}

export interface Bottleneck {
  taskId: string;
  type: 'resource' | 'dependency' | 'skill';
  description: string;
  impact: number;
  suggestions: string[];
}

export interface ResourceAllocation {
  assignments: ResourceAssignment[];
  conflicts: ResourceConflict[];
  utilization: ResourceUtilization[];
  recommendations: AllocationRecommendation[];
}

export interface ResourceAssignment {
  resourceId: string;
  taskId: string;
  allocation: number;
  startDate: Date;
  endDate: Date;
  role: string;
}

export interface ResourceConflict {
  resourceId: string;
  conflictingTasks: string[];
  overallocation: number;
  period: {
    start: Date;
    end: Date;
  };
  severity: 'low' | 'medium' | 'high';
}

export interface ResourceUtilization {
  resourceId: string;
  period: {
    start: Date;
    end: Date;
  };
  allocatedHours: number;
  availableHours: number;
  utilizationRate: number;
  efficiency: number;
}

export interface AllocationRecommendation {
  type: 'reallocation' | 'hiring' | 'training' | 'outsourcing';
  description: string;
  impact: string;
  cost?: number;
  timeline: string;
  priority: Priority;
}

export interface Timeline {
  phases: ProjectPhase[];
  milestones: TimelineMilestone[];
  buffers: TimeBuffer[];
  constraints: TimeConstraint[];
}

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  status: PhaseStatus;
  deliverables: string[];
  gates: QualityGate[];
}

export type PhaseStatus = 'not_started' | 'in_progress' | 'completed' | 'on_hold';

export interface QualityGate {
  id: string;
  name: string;
  criteria: GateCriteria[];
  status: 'pending' | 'passed' | 'failed';
  reviewer: string;
  reviewDate?: Date;
}

export interface GateCriteria {
  description: string;
  met: boolean;
  evidence?: string;
  reviewer?: string;
}

export interface TimelineMilestone {
  id: string;
  name: string;
  date: Date;
  type: 'internal' | 'external' | 'regulatory';
  importance: Priority;
  dependencies: string[];
}

export interface TimeBuffer {
  id: string;
  name: string;
  duration: number;
  type: 'project' | 'feeding' | 'resource';
  location: string;
  purpose: string;
}

export interface TimeConstraint {
  id: string;
  type: 'must_start_on' | 'must_finish_on' | 'start_no_earlier_than' | 'finish_no_later_than';
  date: Date;
  taskId: string;
  reason: string;
  flexibility: number;
}

export interface WBS {
  id: string;
  name: string;
  level: number;
  parent?: string;
  children: string[];
  workPackages: WorkPackage[];
  estimatedEffort: number;
  actualEffort?: number;
}

export interface WorkPackage {
  id: string;
  name: string;
  description: string;
  deliverables: string[];
  activities: Activity[];
  estimatedHours: number;
  responsible: string;
  acceptanceCriteria: string[];
}

export interface Activity {
  id: string;
  name: string;
  description: string;
  estimatedHours: number;
  skills: string[];
  resources: string[];
  predecessors: string[];
}

export interface ProjectReport {
  id: string;
  projectId: string;
  type: ReportType;
  period: ReportPeriod;
  generatedAt: Date;
  generatedBy: string;
  data: ReportData;
  insights: ReportInsight[];
  recommendations: ReportRecommendation[];
}

export type ReportType = 
  | 'status'
  | 'progress'
  | 'budget'
  | 'resource'
  | 'risk'
  | 'quality'
  | 'performance'
  | 'executive_summary';

export interface ReportPeriod {
  start: Date;
  end: Date;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
}

export interface ReportData {
  summary: ProjectSummary;
  metrics: ProjectMetric[];
  charts: ReportChart[];
  tables: ReportTable[];
}

export interface ProjectSummary {
  overallHealth: 'green' | 'yellow' | 'red';
  progressPercentage: number;
  budgetUtilization: number;
  scheduleVariance: number;
  riskLevel: 'low' | 'medium' | 'high';
  teamMorale: number;
  qualityScore: number;
}

export interface ProjectMetric {
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  target?: number;
  benchmark?: number;
  status: 'good' | 'warning' | 'critical';
}

export interface ReportChart {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'gantt' | 'burndown';
  title: string;
  data: any;
  config: any;
}

export interface ReportTable {
  id: string;
  title: string;
  headers: string[];
  rows: any[][];
  summary?: any;
}

export interface ReportInsight {
  type: 'achievement' | 'concern' | 'opportunity' | 'risk';
  title: string;
  description: string;
  impact: Priority;
  evidence: string[];
  relatedMetrics: string[];
}

export interface ReportRecommendation {
  type: 'action' | 'decision' | 'escalation' | 'monitoring';
  title: string;
  description: string;
  priority: Priority;
  effort: 'low' | 'medium' | 'high';
  timeline: string;
  owner: string;
  expectedOutcome: string;
}

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  methodology: ProjectMethodology;
  phases: TemplatePhase[];
  taskTemplates: TaskTemplate[];
  roles: RoleTemplate[];
  deliverableTemplates: DeliverableTemplate[];
  riskTemplates: RiskTemplate[];
}

export type ProjectMethodology = 
  | 'waterfall'
  | 'agile'
  | 'scrum'
  | 'kanban'
  | 'lean'
  | 'hybrid'
  | 'prince2'
  | 'pmbok';

export interface TemplatePhase {
  name: string;
  description: string;
  duration: number;
  deliverables: string[];
  gates: string[];
  activities: string[];
}

export interface TaskTemplate {
  name: string;
  description: string;
  type: TaskType;
  estimatedHours: number;
  skills: string[];
  dependencies: string[];
  phase: string;
}

export interface RoleTemplate {
  name: string;
  description: string;
  responsibilities: string[];
  skills: string[];
  level: 'junior' | 'mid' | 'senior' | 'lead';
}

export interface DeliverableTemplate {
  name: string;
  description: string;
  type: string;
  acceptanceCriteria: string[];
  qualityStandards: string[];
}

export interface RiskTemplate {
  title: string;
  description: string;
  category: RiskCategory;
  probability: number;
  impact: number;
  mitigationStrategies: string[];
}

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: Priority;
  recipients: string[];
  channels: NotificationChannel[];
  scheduledAt?: Date;
  sentAt?: Date;
  readBy: string[];
  relatedEntity: {
    type: 'project' | 'task' | 'milestone' | 'risk';
    id: string;
  };
}

export type NotificationType = 
  | 'task_assigned'
  | 'task_completed'
  | 'deadline_approaching'
  | 'milestone_achieved'
  | 'risk_escalated'
  | 'budget_exceeded'
  | 'resource_conflict'
  | 'status_update';

export type NotificationChannel = 'email' | 'slack' | 'teams' | 'sms' | 'in_app';

export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  widgets: DashboardWidget[];
  layout: DashboardLayout;
  filters: DashboardFilter[];
  permissions: DashboardPermission[];
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'kanban' | 'gantt' | 'calendar';
  title: string;
  position: WidgetPosition;
  size: WidgetSize;
  config: WidgetConfig;
  dataSource: string;
  refreshInterval?: number;
}

export interface WidgetPosition {
  x: number;
  y: number;
}

export interface WidgetSize {
  width: number;
  height: number;
}

export interface WidgetConfig {
  projectIds?: string[];
  metrics?: string[];
  timeRange?: string;
  groupBy?: string;
  filters?: any;
  styling?: any;
}

export interface DashboardLayout {
  type: 'grid' | 'flex';
  columns: number;
  gap: number;
}

export interface DashboardFilter {
  id: string;
  name: string;
  type: 'select' | 'multiselect' | 'date' | 'daterange';
  field: string;
  options?: FilterOption[];
  defaultValue?: any;
}

export interface FilterOption {
  label: string;
  value: any;
}

export interface DashboardPermission {
  userId: string;
  role: 'viewer' | 'editor' | 'admin';
  restrictions?: string[];
}
