# Configuration de l'Agent QA

# Informations de base
NODE_ENV=development
PORT=3008
AGENT_ID=agent-qa-001
LOG_LEVEL=info

# Weaviate (Mémoire)
WEAVIATE_URL=http://weaviate:8080
WEAVIATE_API_KEY=

# Kafka (Communication)
KAFKA_BROKERS=kafka:9092
KAFKA_CLIENT_ID=agent-qa
KAFKA_GROUP_ID=agent-qa-group

# Redis (Cache)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# Répertoires de travail
TEST_WORKDIR=./test-workspace
E2E_WORKDIR=./e2e-workspace
PERF_WORKDIR=./performance-workspace
A11Y_WORKDIR=./accessibility-workspace
SECURITY_WORKDIR=./security-workspace
VISUAL_WORKDIR=./visual-workspace
REPORTS_DIR=./reports

# Configuration des tests
DEFAULT_TIMEOUT=300000
MAX_PARALLEL_TESTS=3
HEADLESS_BROWSER=true

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Lighthouse CI
LHCI_BUILD_CONTEXT__CURRENT_HASH=
LHCI_BUILD_CONTEXT__COMMIT_TIME=
LHCI_BUILD_CONTEXT__CURRENT_BRANCH=
LHCI_BUILD_CONTEXT__COMMIT_MESSAGE=

# SonarQube
SONAR_HOST_URL=http://sonarqube:9000
SONAR_LOGIN=
SONAR_PASSWORD=
SONAR_PROJECT_KEY=retreat-and-be-qa

# Sécurité
JWT_SECRET=your-jwt-secret-key
API_KEY=your-api-key

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Notifications
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_USER=
EMAIL_PASSWORD=

# Intégrations externes
GITHUB_TOKEN=
GITLAB_TOKEN=
JIRA_URL=
JIRA_USERNAME=
JIRA_API_TOKEN=

# Configuration avancée
ENABLE_VISUAL_REGRESSION=true
ENABLE_PERFORMANCE_BUDGETS=true
ENABLE_ACCESSIBILITY_CHECKS=true
ENABLE_SECURITY_SCANS=true
ENABLE_CODE_COVERAGE=true

# Seuils de qualité
PERFORMANCE_THRESHOLD=90
ACCESSIBILITY_THRESHOLD=95
SECURITY_THRESHOLD=95
COVERAGE_THRESHOLD=80

# Navigateurs pour les tests E2E
BROWSERS=chromium,firefox,webkit
MOBILE_DEVICES=iPhone 12,Pixel 5,iPad

# Configuration des rapports
REPORT_FORMATS=html,json,xml,pdf
REPORT_RETENTION_DAYS=30
ENABLE_REPORT_UPLOAD=false
REPORT_UPLOAD_URL=

# Debug
DEBUG_MODE=false
VERBOSE_LOGGING=false
SAVE_SCREENSHOTS=true
SAVE_VIDEOS=false
SAVE_TRACES=true
