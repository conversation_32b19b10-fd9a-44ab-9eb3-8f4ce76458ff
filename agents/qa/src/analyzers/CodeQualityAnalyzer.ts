import { Logger } from 'winston';
import { WeaviateMemory } from '../memory/WeaviateMemory';

/**
 * Analyseur de Qualité de Code
 * 
 * Analyse la qualité du code avec ESLint, SonarQube,
 * et autres outils pour détecter les problèmes de qualité.
 */
export class CodeQualityAnalyzer {
  private logger: Logger;
  private memory: WeaviateMemory;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
  }

  /**
   * Analyse la qualité du code
   */
  async analyze(code: string, framework: string): Promise<any> {
    this.logger.info('Analyse de la qualité du code', { framework });

    try {
      // 1. Analyse statique du code
      const staticAnalysis = await this.performStaticAnalysis(code, framework);

      // 2. Analyse de la complexité
      const complexityAnalysis = await this.analyzeComplexity(code);

      // 3. Analyse des bonnes pratiques
      const bestPracticesAnalysis = await this.analyzeBestPractices(code, framework);

      // 4. Analyse de la sécurité
      const securityAnalysis = await this.analyzeSecurityIssues(code);

      // 5. Analyse de la maintenabilité
      const maintainabilityAnalysis = await this.analyzeMaintainability(code);

      return {
        framework,
        staticAnalysis,
        complexityAnalysis,
        bestPracticesAnalysis,
        securityAnalysis,
        maintainabilityAnalysis,
        overallScore: this.calculateOverallScore({
          staticAnalysis,
          complexityAnalysis,
          bestPracticesAnalysis,
          securityAnalysis,
          maintainabilityAnalysis
        }),
        recommendations: this.generateRecommendations({
          staticAnalysis,
          complexityAnalysis,
          bestPracticesAnalysis,
          securityAnalysis,
          maintainabilityAnalysis
        })
      };

    } catch (error) {
      this.logger.error('Erreur lors de l\'analyse de qualité', { error: error.message });
      throw error;
    }
  }

  /**
   * Analyse statique du code
   */
  private async performStaticAnalysis(code: string, framework: string): Promise<any> {
    // Simulation d'une analyse ESLint/TSLint
    const issues = [];
    const lines = code.split('\n');

    // Détecter des patterns problématiques
    lines.forEach((line, index) => {
      // Variables non utilisées
      if (line.includes('const ') && !line.includes('export') && Math.random() < 0.1) {
        issues.push({
          type: 'warning',
          rule: 'no-unused-vars',
          message: 'Variable déclarée mais non utilisée',
          line: index + 1,
          severity: 'warning'
        });
      }

      // Console.log en production
      if (line.includes('console.log')) {
        issues.push({
          type: 'warning',
          rule: 'no-console',
          message: 'console.log trouvé - à supprimer en production',
          line: index + 1,
          severity: 'warning'
        });
      }

      // Fonctions trop longues
      if (line.includes('function ') && Math.random() < 0.05) {
        issues.push({
          type: 'warning',
          rule: 'max-lines-per-function',
          message: 'Fonction trop longue - considérer la diviser',
          line: index + 1,
          severity: 'info'
        });
      }
    });

    return {
      totalIssues: issues.length,
      errors: issues.filter(i => i.severity === 'error').length,
      warnings: issues.filter(i => i.severity === 'warning').length,
      info: issues.filter(i => i.severity === 'info').length,
      issues,
      score: Math.max(0, 100 - (issues.length * 2))
    };
  }

  /**
   * Analyse de la complexité
   */
  private async analyzeComplexity(code: string): Promise<any> {
    const lines = code.split('\n');
    const functions = this.extractFunctions(code);

    // Calculer la complexité cyclomatique (simulation)
    let totalComplexity = 0;
    const functionComplexities = [];

    functions.forEach(func => {
      // Compter les points de décision
      const decisionPoints = (func.body.match(/if|else|while|for|switch|case|\?|\&\&|\|\|/g) || []).length;
      const complexity = decisionPoints + 1; // +1 pour le chemin linéaire
      
      totalComplexity += complexity;
      functionComplexities.push({
        name: func.name,
        complexity,
        lines: func.lines,
        parameters: func.parameters
      });
    });

    const averageComplexity = functions.length > 0 ? totalComplexity / functions.length : 0;

    return {
      totalComplexity,
      averageComplexity,
      maxComplexity: Math.max(...functionComplexities.map(f => f.complexity), 0),
      functionsCount: functions.length,
      complexFunctions: functionComplexities.filter(f => f.complexity > 10),
      linesOfCode: lines.length,
      score: this.calculateComplexityScore(averageComplexity)
    };
  }

  /**
   * Analyse des bonnes pratiques
   */
  private async analyzeBestPractices(code: string, framework: string): Promise<any> {
    const issues = [];
    const practices = {
      typescript: framework.includes('typescript') || code.includes(': '),
      propTypes: framework === 'react' && code.includes('PropTypes'),
      errorHandling: code.includes('try') && code.includes('catch'),
      testing: code.includes('test(') || code.includes('it(') || code.includes('describe('),
      documentation: code.includes('/**') || code.includes('//'),
      accessibility: code.includes('aria-') || code.includes('role='),
      performance: code.includes('useMemo') || code.includes('useCallback') || code.includes('React.memo')
    };

    // Vérifier les bonnes pratiques
    if (!practices.typescript) {
      issues.push({
        type: 'suggestion',
        message: 'Considérer l\'utilisation de TypeScript pour une meilleure sécurité de type',
        category: 'type-safety'
      });
    }

    if (framework === 'react' && !practices.propTypes && !practices.typescript) {
      issues.push({
        type: 'warning',
        message: 'PropTypes manquants pour la validation des props',
        category: 'validation'
      });
    }

    if (!practices.errorHandling) {
      issues.push({
        type: 'warning',
        message: 'Gestion d\'erreur insuffisante - ajouter try/catch',
        category: 'error-handling'
      });
    }

    if (!practices.accessibility) {
      issues.push({
        type: 'warning',
        message: 'Attributs d\'accessibilité manquants (aria-*, role)',
        category: 'accessibility'
      });
    }

    const score = Object.values(practices).filter(Boolean).length / Object.keys(practices).length * 100;

    return {
      practices,
      issues,
      score: Math.round(score),
      recommendations: issues.map(i => i.message)
    };
  }

  /**
   * Analyse des problèmes de sécurité
   */
  private async analyzeSecurityIssues(code: string): Promise<any> {
    const issues = [];

    // Détecter des patterns de sécurité problématiques
    if (code.includes('eval(')) {
      issues.push({
        type: 'error',
        message: 'Utilisation d\'eval() détectée - risque de sécurité',
        severity: 'high',
        category: 'code-injection'
      });
    }

    if (code.includes('innerHTML') && !code.includes('DOMPurify')) {
      issues.push({
        type: 'warning',
        message: 'innerHTML utilisé sans sanitisation - risque XSS',
        severity: 'medium',
        category: 'xss'
      });
    }

    if (code.includes('document.write')) {
      issues.push({
        type: 'warning',
        message: 'document.write détecté - peut causer des problèmes de sécurité',
        severity: 'medium',
        category: 'dom-manipulation'
      });
    }

    // Vérifier les secrets hardcodés (simulation)
    const secretPatterns = [
      /api[_-]?key[_-]?=\s*['"][^'"]+['"]/i,
      /password[_-]?=\s*['"][^'"]+['"]/i,
      /secret[_-]?=\s*['"][^'"]+['"]/i
    ];

    secretPatterns.forEach(pattern => {
      if (pattern.test(code)) {
        issues.push({
          type: 'error',
          message: 'Secret potentiel hardcodé détecté',
          severity: 'high',
          category: 'secrets'
        });
      }
    });

    const score = Math.max(0, 100 - (issues.length * 10));

    return {
      issues,
      totalIssues: issues.length,
      highSeverity: issues.filter(i => i.severity === 'high').length,
      mediumSeverity: issues.filter(i => i.severity === 'medium').length,
      lowSeverity: issues.filter(i => i.severity === 'low').length,
      score
    };
  }

  /**
   * Analyse de la maintenabilité
   */
  private async analyzeMaintainability(code: string): Promise<any> {
    const lines = code.split('\n');
    const functions = this.extractFunctions(code);

    // Métriques de maintenabilité
    const metrics = {
      linesOfCode: lines.length,
      functionsCount: functions.length,
      averageFunctionLength: functions.length > 0 ? 
        functions.reduce((sum, f) => sum + f.lines, 0) / functions.length : 0,
      duplicatedLines: this.findDuplicatedLines(lines),
      commentRatio: this.calculateCommentRatio(lines),
      nestingDepth: this.calculateMaxNestingDepth(code)
    };

    // Calculer l'index de maintenabilité (simulation)
    const maintainabilityIndex = this.calculateMaintainabilityIndex(metrics);

    return {
      metrics,
      maintainabilityIndex,
      score: maintainabilityIndex,
      issues: this.identifyMaintainabilityIssues(metrics),
      recommendations: this.generateMaintainabilityRecommendations(metrics)
    };
  }

  // Méthodes utilitaires

  private extractFunctions(code: string): any[] {
    const functions = [];
    const functionRegex = /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:\([^)]*\)\s*=>|\([^)]*\)\s*=>\s*{|function))/g;
    let match;

    while ((match = functionRegex.exec(code)) !== null) {
      const name = match[1] || match[2] || 'anonymous';
      const startIndex = match.index;
      
      // Estimer la longueur de la fonction (simulation)
      const lines = Math.floor(Math.random() * 20) + 5;
      const parameters = Math.floor(Math.random() * 5);
      
      functions.push({
        name,
        startIndex,
        lines,
        parameters,
        body: code.substring(startIndex, startIndex + lines * 50) // Approximation
      });
    }

    return functions;
  }

  private calculateComplexityScore(averageComplexity: number): number {
    if (averageComplexity <= 5) return 100;
    if (averageComplexity <= 10) return 80;
    if (averageComplexity <= 15) return 60;
    if (averageComplexity <= 20) return 40;
    return 20;
  }

  private findDuplicatedLines(lines: string[]): number {
    const lineMap = new Map();
    let duplicated = 0;

    lines.forEach(line => {
      const trimmed = line.trim();
      if (trimmed.length > 10) { // Ignorer les lignes très courtes
        const count = lineMap.get(trimmed) || 0;
        lineMap.set(trimmed, count + 1);
        if (count === 1) duplicated++;
      }
    });

    return duplicated;
  }

  private calculateCommentRatio(lines: string[]): number {
    const commentLines = lines.filter(line => {
      const trimmed = line.trim();
      return trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*');
    }).length;

    return lines.length > 0 ? (commentLines / lines.length) * 100 : 0;
  }

  private calculateMaxNestingDepth(code: string): number {
    let maxDepth = 0;
    let currentDepth = 0;

    for (const char of code) {
      if (char === '{') {
        currentDepth++;
        maxDepth = Math.max(maxDepth, currentDepth);
      } else if (char === '}') {
        currentDepth--;
      }
    }

    return maxDepth;
  }

  private calculateMaintainabilityIndex(metrics: any): number {
    // Formule simplifiée d'index de maintenabilité
    const volume = metrics.linesOfCode * Math.log2(metrics.functionsCount + 1);
    const complexity = metrics.averageFunctionLength * metrics.nestingDepth;
    const comments = metrics.commentRatio;

    const index = Math.max(0, 171 - 5.2 * Math.log(volume) - 0.23 * complexity + 16.2 * Math.log(comments + 1));
    return Math.round(Math.min(100, index));
  }

  private identifyMaintainabilityIssues(metrics: any): any[] {
    const issues = [];

    if (metrics.averageFunctionLength > 50) {
      issues.push({
        type: 'warning',
        message: 'Fonctions trop longues en moyenne',
        category: 'function-length'
      });
    }

    if (metrics.nestingDepth > 4) {
      issues.push({
        type: 'warning',
        message: 'Profondeur d\'imbrication trop élevée',
        category: 'nesting'
      });
    }

    if (metrics.commentRatio < 10) {
      issues.push({
        type: 'info',
        message: 'Ratio de commentaires faible',
        category: 'documentation'
      });
    }

    return issues;
  }

  private generateMaintainabilityRecommendations(metrics: any): string[] {
    const recommendations = [];

    if (metrics.averageFunctionLength > 30) {
      recommendations.push('Diviser les fonctions longues en fonctions plus petites');
    }

    if (metrics.nestingDepth > 3) {
      recommendations.push('Réduire la profondeur d\'imbrication en extrayant des fonctions');
    }

    if (metrics.commentRatio < 15) {
      recommendations.push('Ajouter plus de commentaires pour améliorer la documentation');
    }

    if (metrics.duplicatedLines > 10) {
      recommendations.push('Éliminer le code dupliqué en créant des fonctions réutilisables');
    }

    return recommendations;
  }

  private calculateOverallScore(analyses: any): number {
    const scores = [
      analyses.staticAnalysis.score,
      analyses.complexityAnalysis.score,
      analyses.bestPracticesAnalysis.score,
      analyses.securityAnalysis.score,
      analyses.maintainabilityAnalysis.score
    ];

    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  private generateRecommendations(analyses: any): string[] {
    const recommendations = [];

    // Recommandations basées sur l'analyse statique
    if (analyses.staticAnalysis.score < 80) {
      recommendations.push('Corriger les problèmes détectés par l\'analyse statique');
    }

    // Recommandations basées sur la complexité
    if (analyses.complexityAnalysis.averageComplexity > 10) {
      recommendations.push('Réduire la complexité cyclomatique des fonctions');
    }

    // Recommandations de sécurité
    if (analyses.securityAnalysis.score < 90) {
      recommendations.push('Corriger les problèmes de sécurité identifiés');
    }

    // Recommandations de maintenabilité
    if (analyses.maintainabilityAnalysis.score < 70) {
      recommendations.push('Améliorer la maintenabilité du code');
    }

    return [...new Set(recommendations)];
  }
}
