import { Logger } from 'winston';
import { TestRequest, TestResult, TestStatus } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import axios from 'axios';

/**
 * Runner de Tests API
 * 
 * Exécute les tests d'API REST/GraphQL avec validation
 * des réponses, codes de statut et performance.
 */
export class ApiTestRunner {
  private logger: Logger;
  private memory: WeaviateMemory;

  constructor(logger: Logger, memory: WeaviateMemory) {
    this.logger = logger;
    this.memory = memory;
  }

  async run(request: TestRequest, generatedTests?: any): Promise<TestResult> {
    this.logger.info('Exécution des tests API', { id: request.id });

    const startTime = new Date();
    const testCases = [];
    let passed = 0, failed = 0;

    try {
      // Tests API de base
      const apiTests = [
        { name: 'GET /api/health', method: 'GET', url: '/api/health', expectedStatus: 200 },
        { name: 'GET /api/info', method: 'GET', url: '/api/info', expectedStatus: 200 },
        { name: 'POST /api/test', method: 'POST', url: '/api/test', expectedStatus: 404 }
      ];

      const baseUrl = this.getBaseUrl(request);

      for (const test of apiTests) {
        const testCase = await this.executeApiTest(test, baseUrl);
        testCases.push(testCase);
        
        if (testCase.status === 'passed') passed++;
        else failed++;
      }

      const total = testCases.length;
      const successRate = total > 0 ? passed / total : 0;

      return {
        id: request.id,
        type: request.type,
        status: failed > 0 ? 'failed' : 'passed',
        summary: { total, passed, failed, skipped: 0, errors: 0, successRate },
        details: {
          suites: [{
            name: 'API Tests',
            status: failed > 0 ? 'failed' : 'passed',
            tests: testCases,
            duration: Date.now() - startTime.getTime()
          }],
          environment: {
            os: process.platform,
            node: process.version,
            timestamp: new Date()
          },
          configuration: request.configuration
        },
        reports: [],
        artifacts: [],
        metrics: {},
        issues: [],
        recommendations: ['Implémenter des tests API plus complets'],
        startedAt: startTime,
        completedAt: new Date(),
        duration: Date.now() - startTime.getTime()
      };

    } catch (error) {
      this.logger.error('Erreur lors des tests API', { error: error.message });
      throw error;
    }
  }

  private async executeApiTest(test: any, baseUrl: string): Promise<any> {
    const startTime = Date.now();
    
    try {
      const response = await axios({
        method: test.method,
        url: `${baseUrl}${test.url}`,
        timeout: 5000,
        validateStatus: () => true // Ne pas rejeter sur les codes d'erreur
      });

      const statusMatch = response.status === test.expectedStatus;
      
      return {
        name: test.name,
        status: statusMatch ? 'passed' : 'failed' as TestStatus,
        duration: Date.now() - startTime,
        steps: [{
          name: 'HTTP Status Check',
          status: statusMatch ? 'passed' : 'failed' as TestStatus,
          duration: Date.now() - startTime,
          action: `${test.method} ${test.url}`,
          expected: `Status ${test.expectedStatus}`,
          actual: `Status ${response.status}`
        }]
      };

    } catch (error) {
      return {
        name: test.name,
        status: 'failed' as TestStatus,
        duration: Date.now() - startTime,
        steps: [{
          name: 'HTTP Request',
          status: 'failed' as TestStatus,
          duration: Date.now() - startTime,
          action: `${test.method} ${test.url}`,
          expected: 'Successful request',
          actual: error.message,
          error: {
            message: error.message,
            type: error.constructor.name
          }
        }]
      };
    }
  }

  private getBaseUrl(request: TestRequest): string {
    if (request.source.type === 'url') {
      return request.source.url!;
    }
    if (request.source.type === 'deployment' && request.source.deployment?.url) {
      return request.source.deployment.url;
    }
    return 'http://localhost:3000';
  }
}
