/**
 * Types pour l'Agent QA
 */

// Types de base pour les tests
export interface TestRequest {
  id: string;
  type: TestType;
  source: TestSource;
  configuration: TestConfiguration;
  metadata: TestMetadata;
}

export type TestType = 
  | 'unit'
  | 'integration' 
  | 'e2e'
  | 'performance'
  | 'accessibility'
  | 'security'
  | 'visual'
  | 'api'
  | 'load'
  | 'smoke'
  | 'regression'
  | 'full-suite';

export interface TestSource {
  type: 'code' | 'url' | 'repository' | 'deployment';
  code?: GeneratedCode;
  url?: string;
  repository?: RepositoryInfo;
  deployment?: DeploymentInfo;
}

export interface GeneratedCode {
  id: string;
  framework: string;
  files: CodeFile[];
  dependencies: Dependencies;
  buildCommands: string[];
  testCommands: string[];
}

export interface CodeFile {
  path: string;
  content: string;
  type: 'source' | 'test' | 'config' | 'asset';
  language: string;
}

export interface Dependencies {
  production: Record<string, string>;
  development: Record<string, string>;
  peer?: Record<string, string>;
}

export interface RepositoryInfo {
  url: string;
  branch: string;
  commit?: string;
  credentials?: RepositoryCredentials;
}

export interface RepositoryCredentials {
  username?: string;
  token?: string;
  sshKey?: string;
}

export interface DeploymentInfo {
  url: string;
  environment: string;
  version: string;
  platform: string;
  credentials?: Record<string, string>;
}

export interface TestConfiguration {
  browsers?: BrowserConfig[];
  devices?: DeviceConfig[];
  environments?: EnvironmentConfig[];
  coverage?: CoverageConfig;
  performance?: PerformanceConfig;
  accessibility?: AccessibilityConfig;
  security?: SecurityConfig;
  visual?: VisualConfig;
  api?: ApiConfig;
  load?: LoadConfig;
  timeout?: number;
  retries?: number;
  parallel?: boolean;
  headless?: boolean;
}

export interface BrowserConfig {
  name: 'chromium' | 'firefox' | 'webkit' | 'chrome' | 'edge';
  version?: string;
  viewport?: ViewportConfig;
  userAgent?: string;
  locale?: string;
  timezone?: string;
}

export interface ViewportConfig {
  width: number;
  height: number;
  deviceScaleFactor?: number;
  isMobile?: boolean;
  hasTouch?: boolean;
}

export interface DeviceConfig {
  name: string;
  viewport: ViewportConfig;
  userAgent: string;
}

export interface EnvironmentConfig {
  name: string;
  baseUrl: string;
  variables: Record<string, string>;
  headers?: Record<string, string>;
}

export interface CoverageConfig {
  enabled: boolean;
  threshold: CoverageThreshold;
  include: string[];
  exclude: string[];
  reporters: string[];
}

export interface CoverageThreshold {
  statements: number;
  branches: number;
  functions: number;
  lines: number;
}

export interface PerformanceConfig {
  enabled: boolean;
  metrics: PerformanceMetric[];
  budgets: PerformanceBudget[];
  lighthouse?: LighthouseConfig;
  webPageTest?: WebPageTestConfig;
}

export interface PerformanceMetric {
  name: string;
  threshold: number;
  unit: string;
}

export interface PerformanceBudget {
  resourceType: string;
  budget: number;
  unit: 'KB' | 'MB' | 'ms' | 'count';
}

export interface LighthouseConfig {
  categories: string[];
  audits: string[];
  settings: Record<string, any>;
}

export interface WebPageTestConfig {
  location: string;
  connectivity: string;
  runs: number;
  firstViewOnly: boolean;
}

export interface AccessibilityConfig {
  enabled: boolean;
  standard: 'WCAG2A' | 'WCAG2AA' | 'WCAG2AAA';
  rules: AccessibilityRule[];
  includeNotices: boolean;
  includeWarnings: boolean;
}

export interface AccessibilityRule {
  id: string;
  enabled: boolean;
  level: 'error' | 'warning' | 'notice';
}

export interface SecurityConfig {
  enabled: boolean;
  scans: SecurityScan[];
  vulnerabilityThreshold: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityScan {
  type: 'dependency' | 'code' | 'container' | 'infrastructure';
  tool: string;
  configuration: Record<string, any>;
}

export interface VisualConfig {
  enabled: boolean;
  baseline: string;
  threshold: number;
  ignoreRegions: Region[];
  fullPage: boolean;
}

export interface Region {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ApiConfig {
  enabled: boolean;
  collections: ApiCollection[];
  environment: string;
  globals: Record<string, any>;
}

export interface ApiCollection {
  name: string;
  source: string;
  type: 'postman' | 'openapi' | 'custom';
}

export interface LoadConfig {
  enabled: boolean;
  scenarios: LoadScenario[];
  duration: string;
  rampUp: string;
  rampDown: string;
}

export interface LoadScenario {
  name: string;
  executor: string;
  options: Record<string, any>;
  env: Record<string, string>;
}

export interface TestMetadata {
  requestedBy: string;
  requestedAt: Date;
  sourceAgent: string;
  correlationId?: string;
  tags: Record<string, string>;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// Types pour les résultats de tests
export interface TestResult {
  id: string;
  type: TestType;
  status: TestStatus;
  summary: TestSummary;
  details: TestDetails;
  reports: TestReport[];
  artifacts: TestArtifact[];
  metrics: TestMetrics;
  issues: TestIssue[];
  recommendations: string[];
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
}

export type TestStatus = 
  | 'pending'
  | 'running'
  | 'passed'
  | 'failed'
  | 'skipped'
  | 'timeout'
  | 'error';

export interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
  errors: number;
  successRate: number;
  coverage?: CoverageSummary;
}

export interface CoverageSummary {
  statements: CoverageMetric;
  branches: CoverageMetric;
  functions: CoverageMetric;
  lines: CoverageMetric;
}

export interface CoverageMetric {
  total: number;
  covered: number;
  percentage: number;
}

export interface TestDetails {
  suites: TestSuite[];
  environment: TestEnvironmentInfo;
  configuration: TestConfiguration;
}

export interface TestSuite {
  name: string;
  status: TestStatus;
  tests: TestCase[];
  duration: number;
  setup?: TestStep[];
  teardown?: TestStep[];
}

export interface TestCase {
  name: string;
  status: TestStatus;
  duration: number;
  steps: TestStep[];
  error?: TestError;
  screenshots?: string[];
  videos?: string[];
}

export interface TestStep {
  name: string;
  status: TestStatus;
  duration: number;
  action?: string;
  expected?: string;
  actual?: string;
  error?: TestError;
}

export interface TestError {
  message: string;
  stack?: string;
  type: string;
  line?: number;
  column?: number;
}

export interface TestEnvironmentInfo {
  browser?: BrowserInfo;
  device?: DeviceInfo;
  os: string;
  node: string;
  timestamp: Date;
}

export interface BrowserInfo {
  name: string;
  version: string;
  userAgent: string;
  viewport: ViewportConfig;
}

export interface DeviceInfo {
  name: string;
  type: 'desktop' | 'mobile' | 'tablet';
  os: string;
  screen: ScreenInfo;
}

export interface ScreenInfo {
  width: number;
  height: number;
  density: number;
}

export interface TestReport {
  type: 'html' | 'json' | 'xml' | 'pdf' | 'csv';
  name: string;
  path: string;
  url?: string;
  size: number;
  generatedAt: Date;
}

export interface TestArtifact {
  type: 'screenshot' | 'video' | 'log' | 'trace' | 'har' | 'coverage';
  name: string;
  path: string;
  url?: string;
  size: number;
  metadata?: Record<string, any>;
}

export interface TestMetrics {
  performance?: PerformanceMetrics;
  accessibility?: AccessibilityMetrics;
  security?: SecurityMetrics;
  quality?: QualityMetrics;
  visual?: VisualMetrics;
}

export interface PerformanceMetrics {
  lighthouse?: LighthouseMetrics;
  webVitals?: WebVitalsMetrics;
  loadTime?: LoadTimeMetrics;
  resourceUsage?: ResourceUsageMetrics;
}

export interface LighthouseMetrics {
  performance: number;
  accessibility: number;
  bestPractices: number;
  seo: number;
  pwa: number;
  audits: LighthouseAudit[];
}

export interface LighthouseAudit {
  id: string;
  title: string;
  score: number;
  displayValue?: string;
  description: string;
  details?: any;
}

export interface WebVitalsMetrics {
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
}

export interface LoadTimeMetrics {
  domContentLoaded: number;
  load: number;
  firstPaint: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
}

export interface ResourceUsageMetrics {
  totalSize: number;
  totalRequests: number;
  jsSize: number;
  cssSize: number;
  imageSize: number;
  fontSize: number;
}

export interface AccessibilityMetrics {
  score: number;
  violations: AccessibilityViolation[];
  passes: AccessibilityPass[];
  incomplete: AccessibilityIncomplete[];
}

export interface AccessibilityViolation {
  id: string;
  impact: 'minor' | 'moderate' | 'serious' | 'critical';
  description: string;
  help: string;
  helpUrl: string;
  nodes: AccessibilityNode[];
}

export interface AccessibilityPass {
  id: string;
  description: string;
  nodes: AccessibilityNode[];
}

export interface AccessibilityIncomplete {
  id: string;
  description: string;
  nodes: AccessibilityNode[];
}

export interface AccessibilityNode {
  target: string[];
  html: string;
  impact?: string;
  any?: AccessibilityCheck[];
  all?: AccessibilityCheck[];
  none?: AccessibilityCheck[];
}

export interface AccessibilityCheck {
  id: string;
  impact: string;
  message: string;
  data: any;
}

export interface SecurityMetrics {
  score: number;
  vulnerabilities: SecurityVulnerability[];
  recommendations: SecurityRecommendation[];
}

export interface SecurityVulnerability {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: string;
  title: string;
  description: string;
  solution: string;
  references: string[];
  cve?: string;
  cvss?: number;
}

export interface SecurityRecommendation {
  type: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
}

export interface QualityMetrics {
  codeQuality: CodeQualityMetrics;
  testQuality: TestQualityMetrics;
  maintainability: MaintainabilityMetrics;
}

export interface CodeQualityMetrics {
  complexity: number;
  duplication: number;
  maintainabilityIndex: number;
  technicalDebt: number;
  bugs: number;
  vulnerabilities: number;
  codeSmells: number;
}

export interface TestQualityMetrics {
  coverage: CoverageSummary;
  testCount: number;
  assertionCount: number;
  testDuration: number;
  flakyTests: number;
}

export interface MaintainabilityMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  coupling: number;
  cohesion: number;
}

export interface VisualMetrics {
  differences: VisualDifference[];
  similarity: number;
  pixelDifference: number;
  regions: DifferenceRegion[];
}

export interface VisualDifference {
  type: 'added' | 'removed' | 'modified';
  region: Region;
  severity: 'minor' | 'major' | 'critical';
}

export interface DifferenceRegion {
  region: Region;
  difference: number;
  type: string;
}

export interface TestIssue {
  id: string;
  type: 'bug' | 'performance' | 'accessibility' | 'security' | 'usability';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  location?: IssueLocation;
  reproduction?: string[];
  solution?: string;
  references?: string[];
}

export interface IssueLocation {
  file?: string;
  line?: number;
  column?: number;
  selector?: string;
  url?: string;
}

// Types pour la communication avec les autres agents
export interface AgentMessage {
  id: string;
  type: 'request' | 'response' | 'notification';
  from: string;
  to: string;
  payload: any;
  timestamp: Date;
  correlationId?: string;
}

export interface AgentConfig {
  id: string;
  name: string;
  type: 'qa';
  version: string;
  capabilities: string[];
  endpoints: Record<string, string>;
  memory: {
    store: string;
    collections: string[];
  };
  communication: {
    kafka: {
      topics: string[];
      groupId: string;
    };
    redis: {
      channels: string[];
    };
  };
  testing: {
    supportedTypes: TestType[];
    defaultTimeout: number;
    maxParallelTests: number;
    browsers: string[];
    tools: TestingTool[];
  };
}

export interface TestingTool {
  name: string;
  type: string;
  version: string;
  configuration: Record<string, any>;
}

// Types pour les templates de tests
export interface TestTemplate {
  id: string;
  name: string;
  type: TestType;
  framework: string;
  template: string;
  variables: TestTemplateVariable[];
  dependencies: string[];
  examples: string[];
}

export interface TestTemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
}

// Types pour les stratégies de test
export interface TestStrategy {
  id: string;
  name: string;
  description: string;
  conditions: TestCondition[];
  actions: TestAction[];
  priority: number;
}

export interface TestCondition {
  type: string;
  property: string;
  operator: string;
  value: any;
}

export interface TestAction {
  type: string;
  configuration: Record<string, any>;
  order: number;
}
