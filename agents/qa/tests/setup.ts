import { jest } from '@jest/globals';

// Configuration globale pour les tests
beforeAll(async () => {
  // Configuration des timeouts
  jest.setTimeout(30000);
  
  // Variables d'environnement pour les tests
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error';
  process.env.WEAVIATE_URL = 'http://localhost:8080';
  process.env.KAFKA_BROKERS = 'localhost:9092';
});

afterAll(async () => {
  // Nettoyage après tous les tests
});

// Mock des services externes
jest.mock('weaviate-ts-client', () => ({
  default: {
    client: jest.fn(() => ({
      misc: {
        readyChecker: jest.fn(() => ({
          do: jest.fn().mockResolvedValue(true)
        }))
      },
      schema: {
        exists: jest.fn().mockResolvedValue(false),
        classCreator: jest.fn(() => ({
          withClass: jest.fn(() => ({
            do: jest.fn().mockResolvedValue(true)
          }))
        }))
      },
      data: {
        creator: jest.fn(() => ({
          withClassName: jest.fn(() => ({
            withProperties: jest.fn(() => ({
              do: jest.fn().mockResolvedValue({ id: 'test-id' })
            }))
          }))
        }))
      },
      graphql: {
        get: jest.fn(() => ({
          withClassName: jest.fn(() => ({
            withFields: jest.fn(() => ({
              withWhere: jest.fn(() => ({
                withSort: jest.fn(() => ({
                  withLimit: jest.fn(() => ({
                    do: jest.fn().mockResolvedValue({
                      data: { Get: { TestResult: [] } }
                    })
                  }))
                }))
              }))
            }))
          }))
        })),
        aggregate: jest.fn(() => ({
          withClassName: jest.fn(() => ({
            withFields: jest.fn(() => ({
              withWhere: jest.fn(() => ({
                do: jest.fn().mockResolvedValue({
                  data: { Aggregate: { TestResult: [{ meta: { count: 0 } }] } }
                })
              }))
            }))
          }))
        }))
      }
    }))
  }
}));

// Mock de Playwright
jest.mock('playwright', () => ({
  chromium: {
    launch: jest.fn().mockResolvedValue({
      newContext: jest.fn().mockResolvedValue({
        newPage: jest.fn().mockResolvedValue({
          goto: jest.fn().mockResolvedValue(undefined),
          screenshot: jest.fn().mockResolvedValue(undefined),
          close: jest.fn().mockResolvedValue(undefined),
          addScriptTag: jest.fn().mockResolvedValue(undefined),
          evaluate: jest.fn().mockResolvedValue({
            violations: [],
            passes: []
          })
        }),
        tracing: {
          start: jest.fn().mockResolvedValue(undefined),
          stop: jest.fn().mockResolvedValue(undefined)
        },
        close: jest.fn().mockResolvedValue(undefined)
      }),
      close: jest.fn().mockResolvedValue(undefined)
    })
  },
  firefox: {
    launch: jest.fn().mockResolvedValue({
      close: jest.fn().mockResolvedValue(undefined)
    })
  },
  webkit: {
    launch: jest.fn().mockResolvedValue({
      close: jest.fn().mockResolvedValue(undefined)
    })
  }
}));

// Mock d'Axios
jest.mock('axios', () => ({
  default: jest.fn().mockResolvedValue({
    status: 200,
    data: { message: 'OK' }
  })
}));

// Mock de fs-extra
jest.mock('fs-extra', () => ({
  ensureDir: jest.fn().mockResolvedValue(undefined),
  ensureDirSync: jest.fn(),
  writeFile: jest.fn().mockResolvedValue(undefined),
  readFile: jest.fn().mockResolvedValue('mock file content'),
  pathExists: jest.fn().mockResolvedValue(true),
  copy: jest.fn().mockResolvedValue(undefined),
  remove: jest.fn().mockResolvedValue(undefined),
  stat: jest.fn().mockResolvedValue({ size: 1024, isFile: () => true }),
  readdir: jest.fn().mockResolvedValue(['file1.txt', 'file2.txt'])
}));

// Utilitaires de test
export const createMockTestRequest = (overrides = {}) => ({
  id: 'test-001',
  type: 'unit',
  source: {
    type: 'code',
    code: {
      id: 'code-001',
      framework: 'react',
      files: [
        {
          path: 'src/App.tsx',
          content: 'import React from "react";\n\nfunction App() {\n  return <div>Hello World</div>;\n}\n\nexport default App;',
          type: 'source',
          language: 'typescript'
        }
      ],
      dependencies: {
        production: { 'react': '^18.0.0' },
        development: { '@types/react': '^18.0.0' }
      },
      buildCommands: ['npm run build'],
      testCommands: ['npm test']
    }
  },
  configuration: {
    timeout: 30000,
    parallel: true,
    headless: true
  },
  metadata: {
    requestedBy: 'test',
    requestedAt: new Date(),
    sourceAgent: 'test-agent',
    tags: { framework: 'react' },
    priority: 'medium'
  },
  ...overrides
});

export const createMockTestResult = (overrides = {}) => ({
  id: 'test-001',
  type: 'unit',
  status: 'passed',
  summary: {
    total: 5,
    passed: 5,
    failed: 0,
    skipped: 0,
    errors: 0,
    successRate: 1
  },
  details: {
    suites: [
      {
        name: 'Test Suite',
        status: 'passed',
        tests: [
          {
            name: 'Test Case 1',
            status: 'passed',
            duration: 100,
            steps: []
          }
        ],
        duration: 500
      }
    ],
    environment: {
      os: 'linux',
      node: 'v18.0.0',
      timestamp: new Date()
    },
    configuration: {}
  },
  reports: [],
  artifacts: [],
  metrics: {},
  issues: [],
  recommendations: [],
  startedAt: new Date(),
  completedAt: new Date(),
  duration: 1000,
  ...overrides
});

// Helpers pour les assertions
export const expectTestResult = (result: any) => {
  expect(result).toHaveProperty('id');
  expect(result).toHaveProperty('type');
  expect(result).toHaveProperty('status');
  expect(result).toHaveProperty('summary');
  expect(result.summary).toHaveProperty('total');
  expect(result.summary).toHaveProperty('passed');
  expect(result.summary).toHaveProperty('failed');
  expect(result.summary).toHaveProperty('successRate');
};

export const expectQualityAnalysis = (analysis: any) => {
  expect(analysis).toHaveProperty('framework');
  expect(analysis).toHaveProperty('overallScore');
  expect(analysis).toHaveProperty('staticAnalysis');
  expect(analysis).toHaveProperty('complexityAnalysis');
  expect(analysis).toHaveProperty('recommendations');
};
