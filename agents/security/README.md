# 🔒 Agent Security - Advanced Security & Compliance

## 🌟 Vue d'Ensemble

L'**Agent Security** est un agent spécialisé du système nerveux distribué qui fournit des capacités de sécurité avancées, de compliance et de protection pour l'ensemble de l'écosystème. Il intègre les meilleures pratiques de cybersécurité et les outils de pointe pour assurer une protection complète.

## 🛡️ Capacités de Sécurité

### 🔍 Analyse de Vulnérabilités
- **SAST** (Static Application Security Testing)
- **DAST** (Dynamic Application Security Testing)
- **IAST** (Interactive Application Security Testing)
- **SCA** (Software Composition Analysis)
- **Analyse de conteneurs** Docker/Kubernetes
- **Scan d'infrastructure** cloud et on-premise

### 📋 Compliance et Gouvernance
- **OWASP Top 10** (2021)
- **CIS Controls** v8.0
- **NIST Cybersecurity Framework** v1.1
- **ISO 27001** (optionnel)
- **SOC 2** (optionnel)
- **Politiques personnalisées**

### 🕵️ Intelligence des Menaces
- **Indicateurs de compromission** (IoC)
- **Feeds de threat intelligence**
- **Corrélation d'événements**
- **Attribution d'attaques**
- **Chasse aux menaces** proactive

### 🚨 Réponse aux Incidents
- **Détection automatique** d'incidents
- **Playbooks de réponse** automatisés
- **Collecte de preuves** forensiques
- **Chaîne de custody** numérique
- **Rapports d'incident** détaillés

### 📊 Monitoring de Sécurité
- **Surveillance temps réel**
- **Alertes intelligentes**
- **Tableaux de bord** sécurisés
- **Métriques de sécurité**
- **Rapports exécutifs**

## 🏗️ Architecture

```mermaid
graph TB
    SA[🔒 Security Agent]
    
    subgraph "🔍 Scanners"
        SAST[Static Analyzer]
        DAST[Dynamic Analyzer]
        SCA[Dependency Scanner]
        CS[Container Scanner]
        IS[Infrastructure Scanner]
        NS[Network Scanner]
        WS[Web Scanner]
        AS[API Scanner]
    end
    
    subgraph "📋 Compliance"
        CC[Compliance Checker]
        PV[Policy Validator]
        AR[Audit Reporter]
    end
    
    subgraph "🕵️ Intelligence"
        TI[Threat Intelligence]
        IOC[IoC Manager]
        TH[Threat Hunter]
    end
    
    subgraph "🚨 Response"
        IR[Incident Response]
        PB[Playbook Engine]
        FE[Forensics Engine]
    end
    
    subgraph "📊 Monitoring"
        SM[Security Monitor]
        AM[Alert Manager]
        RM[Report Manager]
    end
    
    SA --> SAST
    SA --> DAST
    SA --> SCA
    SA --> CS
    SA --> IS
    SA --> NS
    SA --> WS
    SA --> AS
    SA --> CC
    SA --> PV
    SA --> AR
    SA --> TI
    SA --> IOC
    SA --> TH
    SA --> IR
    SA --> PB
    SA --> FE
    SA --> SM
    SA --> AM
    SA --> RM
```

## 🚀 Démarrage Rapide

### Prérequis
- **Docker** 20.10+
- **Node.js** 18+
- **Accès réseau** aux services Kafka, Weaviate, Redis

### Installation
```bash
# Cloner le repository
git clone <repository-url>
cd agents/security

# Installation des dépendances
npm install

# Compilation TypeScript
npm run build

# Démarrage en mode développement
npm run dev
```

### Démarrage avec Docker
```bash
# Construction de l'image
docker build -t agent-security .

# Démarrage du conteneur
docker run -d \
  --name agent-security \
  -p 3007:3007 \
  -e KAFKA_BROKERS=kafka:9092 \
  -e WEAVIATE_URL=http://weaviate:8080 \
  -e REDIS_URL=redis://redis:6379 \
  agent-security
```

## 🔧 Configuration

### Variables d'Environnement

#### Configuration de Base
```bash
NODE_ENV=production
PORT=3007
LOG_LEVEL=info
AGENT_ID=agent-security-001
```

#### Services Externes
```bash
KAFKA_BROKERS=kafka:9092
WEAVIATE_URL=http://weaviate:8080
REDIS_URL=redis://redis:6379
CORTEX_CENTRAL_URL=http://cortex-central:8080
```

#### APIs de Threat Intelligence
```bash
OTX_API_KEY=your_otx_api_key
VIRUSTOTAL_API_KEY=your_virustotal_api_key
SHODAN_API_KEY=your_shodan_api_key
```

#### Configuration de Sécurité
```bash
SECURITY_SCAN_DEPTH=comprehensive
COMPLIANCE_FRAMEWORKS=owasp,cis,nist
THREAT_INTELLIGENCE_ENABLED=true
INCIDENT_RESPONSE_ENABLED=true
MAX_CONCURRENT_SCANS=3
SCAN_TIMEOUT=300000
```

## 📡 API Reference

### Endpoints Principaux

#### Santé et Statut
```http
GET /health
GET /ready
GET /api/info
GET /api/status
```

#### Scans de Sécurité
```http
POST /api/scan
Content-Type: application/json

{
  "type": "sast|dast|sca|container|infrastructure",
  "target": {
    "type": "code|application|container|network",
    "location": "/path/to/target",
    "metadata": {
      "language": "javascript",
      "framework": "express"
    }
  },
  "configuration": {
    "depth": "comprehensive",
    "timeout": 300000,
    "reportFormat": "json"
  },
  "priority": "high"
}
```

#### Compliance
```http
POST /api/compliance/check
Content-Type: application/json

{
  "framework": "owasp|cis|nist",
  "target": {
    "type": "application",
    "location": "/path/to/app"
  },
  "configuration": {
    "controls": ["all"],
    "reportFormat": "json"
  }
}
```

#### Rapports
```http
POST /api/report
Content-Type: application/json

{
  "type": "vulnerability|compliance|threat-intelligence|incident",
  "period": {
    "start": "2024-01-01T00:00:00Z",
    "end": "2024-01-31T23:59:59Z"
  },
  "scope": ["application", "infrastructure"],
  "format": "json|html|pdf"
}
```

#### Métriques
```http
GET /api/metrics
GET /metrics  # Format Prometheus
```

## 🔍 Types de Scans Supportés

### SAST (Static Analysis)
- **JavaScript/TypeScript**: ESLint Security, Semgrep
- **Python**: Bandit, Safety, Semgrep
- **Java**: SpotBugs, PMD, SonarQube
- **C#**: Security Code Scan, SonarQube
- **PHP**: PHPCS Security, Psalm
- **Go**: Gosec, StaticCheck

### DAST (Dynamic Analysis)
- **Web Applications**: OWASP ZAP, Nikto
- **APIs**: Postman Security, REST Assured
- **Mobile**: MobSF (via intégration)

### SCA (Software Composition)
- **Node.js**: npm audit, Snyk, Retire.js
- **Python**: Safety, pip-audit
- **Java**: OWASP Dependency Check
- **.NET**: NuGet Security Audit

### Container Security
- **Images**: Trivy, Grype, Clair
- **Runtime**: Falco, Sysdig
- **Registries**: Harbor, Quay

### Infrastructure Security
- **Cloud**: Prowler, Scout Suite, CloudSploit
- **Kubernetes**: kube-bench, kube-hunter
- **Terraform**: Checkov, tfsec

## 📋 Frameworks de Compliance

### OWASP Top 10 (2021)
1. **A01:2021** - Broken Access Control
2. **A02:2021** - Cryptographic Failures
3. **A03:2021** - Injection
4. **A04:2021** - Insecure Design
5. **A05:2021** - Security Misconfiguration
6. **A06:2021** - Vulnerable Components
7. **A07:2021** - Identity & Authentication Failures
8. **A08:2021** - Software & Data Integrity Failures
9. **A09:2021** - Security Logging & Monitoring Failures
10. **A10:2021** - Server-Side Request Forgery

### CIS Controls v8.0
- **Basic CIS Controls** (1-6)
- **Foundational CIS Controls** (7-12)
- **Organizational CIS Controls** (13-18)

### NIST Cybersecurity Framework
- **Identify** (ID)
- **Protect** (PR)
- **Detect** (DE)
- **Respond** (RS)
- **Recover** (RC)

## 🕵️ Threat Intelligence

### Sources Supportées
- **AlienVault OTX** - Community threat intelligence
- **VirusTotal** - Malware and URL analysis
- **Shodan** - Internet-connected device search
- **MISP** - Malware Information Sharing Platform
- **ThreatCrowd** - Open source threat intelligence
- **IBM X-Force** - Commercial threat intelligence

### Types d'Indicateurs
- **IP Addresses** - Adresses IP malveillantes
- **Domains** - Domaines suspects
- **URLs** - URLs malveillantes
- **File Hashes** - Empreintes de malware
- **Email Addresses** - Adresses de phishing
- **YARA Rules** - Règles de détection

## 🚨 Réponse aux Incidents

### Types d'Incidents Supportés
- **Malware** - Détection et analyse
- **Phishing** - Campagnes d'hameçonnage
- **Data Breach** - Fuites de données
- **Unauthorized Access** - Accès non autorisés
- **DDoS** - Attaques par déni de service
- **Insider Threat** - Menaces internes

### Playbooks Automatisés
- **Containment** - Isolation automatique
- **Eradication** - Suppression des menaces
- **Recovery** - Restauration des services
- **Lessons Learned** - Analyse post-incident

## 📊 Monitoring et Alertes

### Métriques Collectées
- **Vulnérabilités** par sévérité et catégorie
- **Incidents** par type et statut
- **Compliance** par framework
- **Threat Intelligence** par source
- **Performance** des scans

### Types d'Alertes
- **Critical Vulnerabilities** - Vulnérabilités critiques
- **Compliance Violations** - Violations de conformité
- **Security Incidents** - Incidents de sécurité
- **Threat Indicators** - Indicateurs de menace
- **System Health** - Santé du système

## 🔧 Développement

### Structure du Projet
```
agents/security/
├── src/
│   ├── core/              # Logique principale
│   ├── scanners/          # Moteurs de scan
│   ├── compliance/        # Vérification compliance
│   ├── intelligence/      # Threat intelligence
│   ├── response/          # Réponse aux incidents
│   ├── monitoring/        # Monitoring sécurité
│   ├── reports/           # Génération rapports
│   ├── memory/            # Stockage Weaviate
│   ├── communication/     # Communication Kafka
│   ├── types/             # Définitions TypeScript
│   └── utils/             # Utilitaires
├── config/                # Configuration
├── scripts/               # Scripts utilitaires
├── tests/                 # Tests unitaires
└── docs/                  # Documentation
```

### Scripts Disponibles
```bash
npm run dev          # Développement avec hot-reload
npm run build        # Compilation TypeScript
npm run start        # Démarrage production
npm run test         # Tests unitaires
npm run test:watch   # Tests en mode watch
npm run lint         # Linting ESLint
npm run format       # Formatage Prettier
npm run security:scan # Scan de sécurité du code
```

### Tests
```bash
# Tests unitaires
npm test

# Tests d'intégration
npm run test:integration

# Tests de sécurité
npm run security:scan

# Couverture de code
npm run test:coverage
```

## 🐳 Déploiement

### Docker Compose
```yaml
version: '3.8'
services:
  agent-security:
    build: .
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=production
      - KAFKA_BROKERS=kafka:9092
      - WEAVIATE_URL=http://weaviate:8080
    depends_on:
      - kafka
      - weaviate
    volumes:
      - security-logs:/app/logs
      - security-reports:/app/reports
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-security
spec:
  replicas: 2
  selector:
    matchLabels:
      app: agent-security
  template:
    metadata:
      labels:
        app: agent-security
    spec:
      containers:
      - name: agent-security
        image: agent-security:latest
        ports:
        - containerPort: 3007
        env:
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: WEAVIATE_URL
          value: "http://weaviate:8080"
```

## 📚 Documentation

- **[API Documentation](./docs/api.md)** - Documentation complète de l'API
- **[Security Guide](./docs/security.md)** - Guide de sécurité
- **[Compliance Guide](./docs/compliance.md)** - Guide de conformité
- **[Deployment Guide](./docs/deployment.md)** - Guide de déploiement
- **[Troubleshooting](./docs/troubleshooting.md)** - Résolution de problèmes

## 🤝 Contribution

1. **Fork** le repository
2. **Créer** une branche feature (`git checkout -b feature/amazing-feature`)
3. **Commit** les changements (`git commit -m 'Add amazing feature'`)
4. **Push** vers la branche (`git push origin feature/amazing-feature`)
5. **Ouvrir** une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 🆘 Support

- **Issues GitHub**: [github.com/retreat-and-be/agent-security/issues](https://github.com/retreat-and-be/agent-security/issues)
- **Documentation**: [docs.retreat-and-be.com/agent-security](https://docs.retreat-and-be.com/agent-security)
- **Discord**: [discord.gg/retreat-and-be](https://discord.gg/retreat-and-be)

---

*Développé avec ❤️ par l'équipe Retreat And Be*
