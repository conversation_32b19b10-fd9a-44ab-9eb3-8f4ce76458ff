import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Gestionnaire de Contrôle d'Accès Avancé
 *
 * Implémente RBAC, ABAC, et contrôles d'accès dynamiques
 */
export declare class AccessControlManager extends EventEmitter {
    private logger;
    private memory;
    private communication;
    private roles;
    private permissions;
    private policies;
    private sessions;
    private accessLogs;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Initialise le gestionnaire de contrôle d'accès
     */
    initialize(): Promise<void>;
    /**
     * Vérifie l'autorisation d'accès
     */
    checkAccess(userId: string, resource: string, action: string, context?: AccessContext): Promise<AccessDecision>;
    /**
     * Crée une session utilisateur
     */
    createSession(userId: string, userAttributes: UserAttributes, authMethod: string): Promise<UserSession>;
    /**
     * Révoque une session utilisateur
     */
    revokeSession(userId: string, reason?: string): Promise<void>;
    /**
     * Gère les rôles utilisateur
     */
    assignRole(userId: string, roleId: string): Promise<void>;
    /**
     * Révoque un rôle utilisateur
     */
    revokeRole(userId: string, roleId: string): Promise<void>;
    /**
     * Vérifie l'accès basé sur les rôles (RBAC)
     */
    private checkRBACAccess;
    /**
     * Vérifie l'accès basé sur les attributs (ABAC)
     */
    private checkABACAccess;
    /**
     * Vérifie les politiques dynamiques
     */
    private checkDynamicPolicies;
    /**
     * Vérifie si une permission correspond à la ressource et l'action
     */
    private matchesPermission;
    /**
     * Vérifie si les conditions d'une politique sont remplies
     */
    private matchesPolicyConditions;
    /**
     * Crée une décision d'accès
     */
    private createAccessDecision;
    /**
     * Enregistre un accès
     */
    private logAccess;
    /**
     * Vérifie si une session est expirée
     */
    private isSessionExpired;
    /**
     * Charge les rôles et permissions
     */
    private loadRolesAndPermissions;
    /**
     * Configure les rôles et permissions par défaut
     */
    private setupDefaultRolesAndPermissions;
    /**
     * Charge les politiques d'accès
     */
    private loadAccessPolicies;
    /**
     * Configure les règles par défaut
     */
    private setupDefaultRules;
    /**
     * Démarre le monitoring d'accès
     */
    private startAccessMonitoring;
    /**
     * Nettoie les sessions expirées
     */
    private cleanupExpiredSessions;
    /**
     * Nettoie les anciens logs
     */
    private cleanupOldLogs;
    /**
     * Génère un ID de session
     */
    private generateSessionId;
    /**
     * Génère un ID de log
     */
    private generateLogId;
    /**
     * Arrêt du gestionnaire de contrôle d'accès
     */
    shutdown(): Promise<void>;
}
interface UserSession {
    id: string;
    userId: string;
    userAttributes: UserAttributes;
    authMethod: string;
    createdAt: Date;
    expiresAt: Date;
    lastActivity: Date;
    isActive: boolean;
    ipAddress: string;
    userAgent: string;
}
interface UserAttributes {
    roles?: string[];
    department?: string;
    allowedHours?: {
        start: number;
        end: number;
    };
    allowedIPs?: string[];
    crossDepartmentAccess?: boolean;
    ipAddress: string;
    userAgent: string;
}
interface AccessContext {
    resourceOwner?: string;
    requestTime?: Date;
    clientInfo?: any;
    additionalData?: any;
}
interface AccessDecision {
    allowed: boolean;
    reason: string;
    timestamp: Date;
    processingTime: number;
}
export {};
//# sourceMappingURL=AccessControlManager.d.ts.map