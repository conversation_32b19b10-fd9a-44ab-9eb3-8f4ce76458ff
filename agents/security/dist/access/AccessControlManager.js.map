{"version": 3, "file": "AccessControlManager.js", "sourceRoot": "", "sources": ["../../src/access/AccessControlManager.ts"], "names": [], "mappings": ";;;AACA,mCAAsC;AAItC;;;;GAIG;AACH,MAAa,oBAAqB,SAAQ,qBAAY;IAapD,YACE,MAAc,EACd,MAAsB,EACtB,aAAiC;QAEjC,KAAK,EAAE,CAAC;QAbF,UAAK,GAAsB,IAAI,GAAG,EAAE,CAAC;QACrC,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;QACjD,aAAQ,GAA8B,IAAI,GAAG,EAAE,CAAC;QAChD,aAAQ,GAA6B,IAAI,GAAG,EAAE,CAAC;QAC/C,eAAU,GAAgB,EAAE,CAAC;QAE7B,kBAAa,GAAY,KAAK,CAAC;QAQrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAEnE,sCAAsC;YACtC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,oCAAoC;YACpC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEhC,sCAAsC;YACtC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAE/B,kCAAkC;YAClC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAE1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+DAA+D,EAAE,KAAK,CAAC,CAAC;YAC1F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,MAAc,EACd,QAAgB,EAChB,MAAc,EACd,OAAuB;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,MAAM,OAAO,QAAQ,IAAI,MAAM,EAAE,CAAC,CAAC;YAEjF,yCAAyC;YACzC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/C,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,6BAA6B,EAAE,SAAS,CAAC,CAAC;YACpF,CAAC;YAED,gDAAgD;YAChD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzE,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACxE,CAAC;YAED,qDAAqD;YACrD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YAClF,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACxE,CAAC;YAED,yCAAyC;YACzC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACzF,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC1E,CAAC;YAED,iBAAiB;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;YAE9E,4BAA4B;YAC5B,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;YAElE,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,MAAc,EACd,cAA8B,EAC9B,UAAkB;QAElB,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW;QAE1E,MAAM,OAAO,GAAgB;YAC3B,EAAE,EAAE,SAAS;YACb,MAAM;YACN,cAAc;YACd,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS;YACT,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,cAAc,CAAC,SAAS;YACnC,SAAS,EAAE,cAAc,CAAC,SAAS;SACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,EAAE,CAAC,CAAC;QAElE,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC;QAEhE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,SAAiB,mBAAmB;QACtE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAE7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,MAAM,KAAK,MAAM,EAAE,CAAC,CAAC;YAEhF,6BAA6B;YAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,MAAc;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,oBAAoB,MAAM,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;gBAClC,OAAO,CAAC,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnD,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,MAAM,4BAA4B,MAAM,EAAE,CAAC,CAAC;gBAExE,qCAAqC;gBACrC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,MAAc;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,MAAM,+BAA+B,MAAM,EAAE,CAAC,CAAC;gBAE1E,qCAAqC;gBACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,OAAoB,EACpB,QAAgB,EAChB,MAAc;QAEd,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;QAC1D,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBACtD,IAAI,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;wBACvE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,wBAAwB,MAAM,EAAE,EAAE,CAAC;oBACrE,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,2BAA2B,EAAE,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,OAAoB,EACpB,QAAgB,EAChB,MAAc,EACd,OAAuB;QAEvB,qDAAqD;QACrD,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC;QAEzC,kCAAkC;QAClC,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC1C,IAAI,WAAW,GAAG,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,WAAW,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;gBAC3F,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,uCAAuC,EAAE,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,0BAA0B,EAAE,CAAC;YAChE,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,IAAI,OAAO,EAAE,aAAa,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACnD,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;gBACvF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,wCAAwC,EAAE,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,OAAoB,EACpB,QAAgB,EAChB,MAAc,EACd,OAAuB;QAEvB,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC/F,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBAC7B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,2BAA2B,QAAQ,EAAE,EAAE,CAAC;gBAC3E,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,8BAA8B,EAAE,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAsB,EAAE,QAAgB,EAAE,MAAc;QAChF,MAAM,aAAa,GAAG,UAAU,CAAC,QAAQ,KAAK,GAAG;YAC5B,UAAU,CAAC,QAAQ,KAAK,QAAQ;YAChC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;YACjC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEvD,OAAO,aAAa,IAAI,WAAW,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,uBAAuB,CAC7B,MAAoB,EACpB,OAAoB,EACpB,QAAgB,EAChB,MAAc,EACd,OAAuB;QAEvB,6CAA6C;QAC7C,iEAAiE;QACjE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,oBAAoB,CAC1B,OAAgB,EAChB,MAAc,EACd,SAAiB;QAEjB,OAAO;YACL,OAAO;YACP,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS,CACrB,MAAc,EACd,QAAgB,EAChB,MAAc,EACd,QAAwB,EACxB,OAAuB;QAEvB,MAAM,SAAS,GAAc;YAC3B,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE;YACxB,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEhC,kCAAkC;QAClC,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAE5C,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAoB;QAC3C,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC;IAC7D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,gDAAgD;QAChD,MAAM,IAAI,CAAC,+BAA+B,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,+BAA+B;QAC3C,sBAAsB;QACtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE;YAC/B,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,kBAAkB;YACxB,QAAQ,EAAE,GAAG;YACb,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;YACzB,UAAU,EAAE,EAAE;SACf,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE;YAChC,EAAE,EAAE,WAAW;YACf,IAAI,EAAE,yBAAyB;YAC/B,QAAQ,EAAE,GAAG;YACb,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,UAAU,EAAE,EAAE;SACf,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;YACrB,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,sBAAsB;YACnC,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;YACtB,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,wBAAwB;YACrC,WAAW,EAAE,CAAC,WAAW,CAAC;YAC1B,QAAQ,EAAE,CAAC,MAAM,CAAC;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB;QAC9B,mCAAmC;QACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB,EAAE;YACnC,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,gCAAgC;YAC7C,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,GAAG;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,6CAA6C;QAC7C,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,uBAAuB;QAE1C,6BAA6B;QAC7B,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;IAC1C,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC7B,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,YAAY,8BAA8B,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW;QACjF,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAE5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC;QAE5E,MAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC3D,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,YAAY,wBAAwB,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5E,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;CACF;AApfD,oDAofC"}