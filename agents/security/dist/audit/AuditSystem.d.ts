import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Système d'Audit Complet
 *
 * Enregistre, analyse et rapporte toutes les activités de sécurité
 */
export declare class AuditSystem extends EventEmitter {
    private logger;
    private memory;
    private communication;
    private auditLogs;
    private auditRules;
    private auditReports;
    private alertThresholds;
    private isInitialized;
    private isMonitoring;
    constructor(logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Initialise le système d'audit
     */
    initialize(): Promise<void>;
    /**
     * Enregistre un événement d'audit
     */
    logEvent(event: AuditEvent): Promise<void>;
    /**
     * Enregistre un accès système
     */
    logAccess(accessEvent: AccessEvent): Promise<void>;
    /**
     * Enregistre une modification de configuration
     */
    logConfigurationChange(configEvent: ConfigurationEvent): Promise<void>;
    /**
     * Enregistre un incident de sécurité
     */
    logSecurityIncident(incident: SecurityIncidentEvent): Promise<void>;
    /**
     * Génère un rapport d'audit
     */
    generateAuditReport(reportType: AuditReportType, period: {
        start: Date;
        end: Date;
    }, filters?: AuditFilters): Promise<AuditReport>;
    /**
     * Recherche dans les logs d'audit
     */
    searchLogs(query: AuditSearchQuery): Promise<AuditLog[]>;
    /**
     * Vérifie les règles d'audit
     */
    private checkAuditRules;
    /**
     * Vérifie si un log correspond à une règle
     */
    private matchesRule;
    /**
     * Exécute l'action d'une règle
     */
    private executeRuleAction;
    /**
     * Envoie une alerte
     */
    private sendAlert;
    /**
     * Calcule la sévérité d'un événement
     */
    private calculateEventSeverity;
    /**
     * Charge les règles d'audit
     */
    private loadAuditRules;
    /**
     * Configure les seuils d'alerte
     */
    private setupAlertThresholds;
    /**
     * Vérifie les seuils d'alerte
     */
    private checkAlertThresholds;
    /**
     * Récupère les logs récents d'un type donné
     */
    private getRecentLogsOfType;
    /**
     * Déclenche une alerte de seuil
     */
    private triggerThresholdAlert;
    /**
     * Charge les logs existants
     */
    private loadExistingLogs;
    /**
     * Démarre le monitoring d'audit
     */
    private startAuditMonitoring;
    /**
     * Nettoie les anciens logs
     */
    private cleanupOldLogs;
    /**
     * Génère des rapports automatiques
     */
    private generateAutomaticReports;
    /**
     * Récupère les logs pour une période
     */
    private getLogsForPeriod;
    /**
     * Génère un résumé de rapport
     */
    private generateReportSummary;
    /**
     * Analyse les logs pour trouver des découvertes
     */
    private analyzeLogsForFindings;
    /**
     * Génère des recommandations
     */
    private generateRecommendations;
    /**
     * Calcule les statistiques
     */
    private calculateStatistics;
    /**
     * Récupère les types d'événements les plus fréquents
     */
    private getTopEventTypes;
    /**
     * Récupère les utilisateurs les plus actifs
     */
    private getTopUsers;
    /**
     * Calcule le score de risque
     */
    private calculateRiskScore;
    /**
     * Génère un ID d'audit
     */
    private generateAuditId;
    /**
     * Génère un ID de rapport
     */
    private generateReportId;
    private escalateIncident;
    private blockAction;
    private sendNotification;
    /**
     * Arrêt du système d'audit
     */
    shutdown(): Promise<void>;
}
interface AuditEvent {
    type: string;
    category: string;
    description: string;
    userId?: string;
    resource?: string;
    action?: string;
    result?: string;
    ipAddress?: string;
    userAgent?: string;
    source?: string;
    metadata?: any;
}
interface AuditLog {
    id: string;
    timestamp: Date;
    event: AuditEvent;
    source: string;
    severity: string;
    category: string;
    metadata: any;
}
interface AccessEvent {
    userId: string;
    resource: string;
    action: string;
    result: string;
    ipAddress: string;
    userAgent: string;
    metadata?: any;
}
interface ConfigurationEvent {
    userId: string;
    component: string;
    oldValue: any;
    newValue: any;
    reason: string;
}
interface SecurityIncidentEvent {
    incidentId: string;
    title: string;
    severity: string;
    category: string;
    affectedResource: string;
    detectionMethod: string;
}
type AuditReportType = 'daily' | 'weekly' | 'monthly' | 'custom' | 'compliance' | 'security';
interface AuditFilters {
    eventTypes?: string[];
    severity?: string;
    userId?: string;
}
interface AuditReport {
    id: string;
    type: AuditReportType;
    period: {
        start: Date;
        end: Date;
    };
    generatedAt: Date;
    summary: AuditReportSummary;
    findings: AuditFinding[];
    recommendations: AuditRecommendation[];
    statistics: AuditStatistics;
    logs: AuditLog[];
    metadata: any;
}
interface AuditReportSummary {
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    timeRange: {
        start: number;
        end: number;
    };
}
interface AuditFinding {
    id: string;
    title: string;
    description: string;
    severity: string;
    category: string;
    evidence: string[];
}
interface AuditRecommendation {
    id: string;
    title: string;
    description: string;
    priority: string;
    category: string;
}
interface AuditStatistics {
    totalEvents: number;
    eventsPerDay: number;
    topEventTypes: Array<{
        type: string;
        count: number;
    }>;
    topUsers: Array<{
        userId: string;
        count: number;
    }>;
    riskScore: number;
}
interface AuditSearchQuery {
    startDate?: Date;
    endDate?: Date;
    userId?: string;
    eventType?: string;
    category?: string;
    severity?: string;
    searchText?: string;
    limit?: number;
}
export {};
//# sourceMappingURL=AuditSystem.d.ts.map