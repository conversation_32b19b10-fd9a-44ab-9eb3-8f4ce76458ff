import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { SecurityAgentConfig, SecurityScanRequest, SecurityMetrics } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';
/**
 * Agent Security Principal
 *
 * Agent spécialisé dans la sécurité, compliance et protection
 * du système nerveux distribué
 */
export declare class SecurityAgent extends EventEmitter {
    private config;
    private logger;
    private memory;
    private communication;
    private vulnerabilityScanner;
    private complianceChecker;
    private threatIntelligence;
    private incidentResponse;
    private securityMonitor;
    private reportGenerator;
    private accessControlManager;
    private auditSystem;
    private encryptionManager;
    private securityPolicyEngine;
    private activeScanQueue;
    private scanResults;
    private activeIncidents;
    private threatIndicators;
    private isInitialized;
    private isProcessingQueue;
    private maxConcurrentScans;
    constructor(config: SecurityAgentConfig, logger: Logger, memory: WeaviateMemory, communication: KafkaCommunication);
    /**
     * Initialise l'agent de sécurité
     */
    initialize(): Promise<void>;
    /**
     * Initialise les composants de sécurité
     */
    private initializeSecurityComponents;
    /**
     * Configure la communication avec les autres agents
     */
    private setupCommunication;
    /**
     * Démarre le monitoring de sécurité
     */
    private startSecurityMonitoring;
    /**
     * Charge les données de sécurité existantes
     */
    private loadSecurityData;
    /**
     * Démarre le processeur de queue de scans
     */
    private startQueueProcessor;
    /**
     * Traite la queue de scans de sécurité
     */
    private processSecurityQueue;
    /**
     * Gère une demande de scan de sécurité
     */
    handleScanRequest(request: SecurityScanRequest): Promise<void>;
    /**
     * Exécute un scan de sécurité
     */
    private executeScan;
    /**
     * Enrichit les résultats avec l'intelligence des menaces
     */
    private enrichWithThreatIntelligence;
    /**
     * Traite les vulnérabilités critiques
     */
    private processCriticalVulnerabilities;
    /**
     * Gère une alerte de sécurité
     */
    private handleSecurityAlert;
    /**
     * Gère une demande de vérification de compliance
     */
    private handleComplianceRequest;
    /**
     * Gère un incident de sécurité
     */
    private handleSecurityIncident;
    /**
     * Gère une demande de rapport
     */
    private handleReportRequest;
    private validateScanRequest;
    private getPriorityWeight;
    private calculateThreatLevel;
    private mapSecurityCategoryToIncident;
    /**
     * Récupère le statut de l'agent
     */
    getStatus(): any;
    /**
     * Récupère les métriques de sécurité
     */
    getSecurityMetrics(): Promise<SecurityMetrics>;
    /**
     * Vérifie l'autorisation d'accès
     */
    checkAccess(userId: string, resource: string, action: string, context?: any): Promise<any>;
    /**
     * Crée une session utilisateur
     */
    createUserSession(userId: string, userAttributes: any, authMethod: string): Promise<any>;
    /**
     * Révoque une session utilisateur
     */
    revokeUserSession(userId: string, reason?: string): Promise<void>;
    /**
     * Enregistre un événement d'audit
     */
    logAuditEvent(event: any): Promise<void>;
    /**
     * Génère un rapport d'audit
     */
    generateAuditReport(reportType: string, period: {
        start: Date;
        end: Date;
    }, filters?: any): Promise<any>;
    /**
     * Chiffre des données
     */
    encryptData(data: string | Buffer, keyId?: string, algorithm?: string): Promise<any>;
    /**
     * Déchiffre des données
     */
    decryptData(encryptedData: string, keyId: string, algorithm: string, iv?: string): Promise<string>;
    /**
     * Génère une nouvelle clé de chiffrement
     */
    generateEncryptionKey(keyId: string, algorithm?: string, keySize?: number): Promise<any>;
    /**
     * Effectue la rotation d'une clé
     */
    rotateEncryptionKey(keyId: string): Promise<any>;
    /**
     * Crée une nouvelle politique de sécurité
     */
    createSecurityPolicy(policyData: any): Promise<any>;
    /**
     * Évalue une action contre les politiques
     */
    evaluateAction(action: any): Promise<any>;
    /**
     * Met à jour une politique de sécurité
     */
    updateSecurityPolicy(policyId: string, updates: any): Promise<any>;
    /**
     * Supprime une politique de sécurité
     */
    deleteSecurityPolicy(policyId: string): Promise<void>;
    /**
     * Arrêt de l'agent de sécurité
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=SecurityAgent.d.ts.map