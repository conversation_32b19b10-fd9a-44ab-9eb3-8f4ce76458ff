"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityAgent = void 0;
const events_1 = require("events");
const VulnerabilityScanner_1 = require("../scanners/VulnerabilityScanner");
const ComplianceChecker_1 = require("../compliance/ComplianceChecker");
const ThreatIntelligence_1 = require("../intelligence/ThreatIntelligence");
const IncidentResponse_1 = require("../response/IncidentResponse");
const SecurityMonitor_1 = require("../monitoring/SecurityMonitor");
const ReportGenerator_1 = require("../reports/ReportGenerator");
const AccessControlManager_1 = require("../access/AccessControlManager");
const AuditSystem_1 = require("../audit/AuditSystem");
const EncryptionManager_1 = require("../encryption/EncryptionManager");
const SecurityPolicyEngine_1 = require("../policies/SecurityPolicyEngine");
/**
 * Agent Security Principal
 *
 * Agent spécialisé dans la sécurité, compliance et protection
 * du système nerveux distribué
 */
class SecurityAgent extends events_1.EventEmitter {
    constructor(config, logger, memory, communication) {
        super();
        this.activeScanQueue = new Map();
        this.scanResults = new Map();
        this.activeIncidents = new Map();
        this.threatIndicators = new Map();
        this.isInitialized = false;
        this.isProcessingQueue = false;
        this.maxConcurrentScans = 3;
        this.config = config;
        this.logger = logger;
        this.memory = memory;
        this.communication = communication;
    }
    /**
     * Initialise l'agent de sécurité
     */
    async initialize() {
        try {
            this.logger.info('🔒 Initialisation de l\'Agent Security...');
            // Initialisation des composants de sécurité
            await this.initializeSecurityComponents();
            // Configuration de la communication
            await this.setupCommunication();
            // Démarrage du monitoring de sécurité
            await this.startSecurityMonitoring();
            // Chargement des données de sécurité existantes
            await this.loadSecurityData();
            // Démarrage du processeur de queue
            this.startQueueProcessor();
            this.isInitialized = true;
            this.logger.info('✅ Agent Security initialisé avec succès');
            this.emit('agent-initialized', {
                agentId: this.config.id,
                capabilities: this.config.capabilities,
                timestamp: new Date()
            });
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation de l\'Agent Security:', error);
            throw error;
        }
    }
    /**
     * Initialise les composants de sécurité
     */
    async initializeSecurityComponents() {
        // Scanner de vulnérabilités
        this.vulnerabilityScanner = new VulnerabilityScanner_1.VulnerabilityScanner(this.config.scanners, this.logger, this.memory);
        // Vérificateur de compliance
        this.complianceChecker = new ComplianceChecker_1.ComplianceChecker(this.config.compliance, this.logger, this.memory);
        // Intelligence des menaces
        this.threatIntelligence = new ThreatIntelligence_1.ThreatIntelligence(this.config.threatIntelligence, this.logger, this.memory);
        // Réponse aux incidents
        this.incidentResponse = new IncidentResponse_1.IncidentResponse(this.logger, this.memory, this.communication);
        // Monitoring de sécurité
        this.securityMonitor = new SecurityMonitor_1.SecurityMonitor(this.config.monitoring, this.logger, this.memory);
        // Générateur de rapports
        this.reportGenerator = new ReportGenerator_1.ReportGenerator(this.logger, this.memory);
        // Gestionnaire de contrôle d'accès
        this.accessControlManager = new AccessControlManager_1.AccessControlManager(this.logger, this.memory, this.communication);
        // Système d'audit
        this.auditSystem = new AuditSystem_1.AuditSystem(this.logger, this.memory, this.communication);
        // Gestionnaire de chiffrement
        this.encryptionManager = new EncryptionManager_1.EncryptionManager(this.logger, this.memory);
        // Moteur de politiques de sécurité
        this.securityPolicyEngine = new SecurityPolicyEngine_1.SecurityPolicyEngine(this.logger, this.memory, this.communication);
        // Initialisation des composants
        await Promise.all([
            this.vulnerabilityScanner.initialize(),
            this.complianceChecker.initialize(),
            this.threatIntelligence.initialize(),
            this.incidentResponse.initialize(),
            this.securityMonitor.initialize(),
            this.reportGenerator.initialize(),
            this.accessControlManager.initialize(),
            this.auditSystem.initialize(),
            this.encryptionManager.initialize(),
            this.securityPolicyEngine.initialize()
        ]);
        this.logger.info('🔧 Composants de sécurité initialisés');
    }
    /**
     * Configure la communication avec les autres agents
     */
    async setupCommunication() {
        // Écoute des demandes de scan de sécurité
        this.communication.on('security-scan-request', async (data) => {
            await this.handleScanRequest(data);
        });
        // Écoute des alertes de sécurité
        this.communication.on('security-alert', async (data) => {
            await this.handleSecurityAlert(data);
        });
        // Écoute des demandes de compliance
        this.communication.on('compliance-check-request', async (data) => {
            await this.handleComplianceRequest(data);
        });
        // Écoute des incidents de sécurité
        this.communication.on('security-incident', async (data) => {
            await this.handleSecurityIncident(data);
        });
        // Écoute des demandes de rapport
        this.communication.on('security-report-request', async (data) => {
            await this.handleReportRequest(data);
        });
        this.logger.info('📡 Communication de sécurité configurée');
    }
    /**
     * Démarre le monitoring de sécurité
     */
    async startSecurityMonitoring() {
        // Monitoring en temps réel
        this.securityMonitor.on('threat-detected', (threat) => {
            this.handleThreatDetection(threat);
        });
        this.securityMonitor.on('vulnerability-found', (vulnerability) => {
            this.handleVulnerabilityDetection(vulnerability);
        });
        this.securityMonitor.on('compliance-violation', (violation) => {
            this.handleComplianceViolation(violation);
        });
        this.securityMonitor.on('security-incident', (incident) => {
            this.handleSecurityIncident(incident);
        });
        await this.securityMonitor.start();
        this.logger.info('👁️ Monitoring de sécurité démarré');
    }
    /**
     * Charge les données de sécurité existantes
     */
    async loadSecurityData() {
        try {
            // Chargement des indicateurs de menace
            const indicators = await this.memory.getThreatIndicators();
            indicators.forEach(indicator => {
                this.threatIndicators.set(indicator.id, indicator);
            });
            // Chargement des incidents actifs
            const incidents = await this.memory.getActiveIncidents();
            incidents.forEach(incident => {
                this.activeIncidents.set(incident.id, incident);
            });
            this.logger.info(`📚 Données de sécurité chargées: ${indicators.length} indicateurs, ${incidents.length} incidents`);
        }
        catch (error) {
            this.logger.warn('⚠️ Erreur lors du chargement des données de sécurité:', error);
        }
    }
    /**
     * Démarre le processeur de queue de scans
     */
    startQueueProcessor() {
        setInterval(async () => {
            if (!this.isProcessingQueue && this.activeScanQueue.size > 0) {
                await this.processSecurityQueue();
            }
        }, 5000); // Toutes les 5 secondes
    }
    /**
     * Traite la queue de scans de sécurité
     */
    async processSecurityQueue() {
        this.isProcessingQueue = true;
        try {
            const activeScanCount = Array.from(this.scanResults.values())
                .filter(result => result.status === 'running').length;
            if (activeScanCount >= this.maxConcurrentScans) {
                return;
            }
            // Prendre le scan avec la plus haute priorité
            const sortedScans = Array.from(this.activeScanQueue.values())
                .sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));
            const nextScan = sortedScans[0];
            if (nextScan) {
                this.activeScanQueue.delete(nextScan.id);
                await this.executeScan(nextScan);
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du traitement de la queue de sécurité:', error);
        }
        finally {
            this.isProcessingQueue = false;
        }
    }
    /**
     * Gère une demande de scan de sécurité
     */
    async handleScanRequest(request) {
        this.logger.info(`🔍 Nouvelle demande de scan: ${request.type} - ${request.target.type}`);
        // Validation de la demande
        if (!this.validateScanRequest(request)) {
            this.logger.error(`❌ Demande de scan invalide: ${request.id}`);
            return;
        }
        // Ajout à la queue
        this.activeScanQueue.set(request.id, request);
        // Notification
        this.emit('scan-queued', {
            scanId: request.id,
            type: request.type,
            priority: request.priority,
            queuePosition: this.activeScanQueue.size
        });
        // Stockage en mémoire
        await this.memory.storeScanRequest(request);
    }
    /**
     * Exécute un scan de sécurité
     */
    async executeScan(request) {
        const startTime = new Date();
        this.logger.info(`🔄 Exécution du scan: ${request.id} (${request.type})`);
        try {
            let result;
            // Exécution selon le type de scan
            switch (request.type) {
                case 'sast':
                case 'dast':
                case 'iast':
                case 'sca':
                    result = await this.vulnerabilityScanner.scan(request);
                    break;
                case 'container':
                    result = await this.vulnerabilityScanner.scanContainer(request);
                    break;
                case 'infrastructure':
                    result = await this.vulnerabilityScanner.scanInfrastructure(request);
                    break;
                case 'network':
                    result = await this.vulnerabilityScanner.scanNetwork(request);
                    break;
                case 'web':
                    result = await this.vulnerabilityScanner.scanWeb(request);
                    break;
                case 'api':
                    result = await this.vulnerabilityScanner.scanAPI(request);
                    break;
                default:
                    throw new Error(`Type de scan non supporté: ${request.type}`);
            }
            // Enrichissement avec l'intelligence des menaces
            result = await this.enrichWithThreatIntelligence(result);
            // Vérification de compliance
            const complianceResults = await this.complianceChecker.checkCompliance(result);
            result.compliance = complianceResults;
            // Stockage du résultat
            this.scanResults.set(request.id, result);
            await this.memory.storeScanResult(result);
            // Notification
            this.emit('scan-completed', result);
            // Envoi du résultat au demandeur
            await this.communication.sendMessage(request.requestedBy, {
                type: 'security-scan-result',
                scanId: request.id,
                result: result
            });
            this.logger.info(`✅ Scan terminé: ${request.id} - ${result.summary.totalVulnerabilities} vulnérabilités trouvées`);
            // Traitement des vulnérabilités critiques
            await this.processCriticalVulnerabilities(result);
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors du scan ${request.id}:`, error);
            const errorResult = {
                id: `result-${request.id}`,
                scanId: request.id,
                timestamp: new Date(),
                status: 'failed',
                duration: Date.now() - startTime.getTime(),
                summary: {
                    totalVulnerabilities: 0,
                    criticalCount: 0,
                    highCount: 0,
                    mediumCount: 0,
                    lowCount: 0,
                    infoCount: 0,
                    riskScore: 0,
                    complianceScore: 0,
                    securityGrade: 'F'
                },
                vulnerabilities: [],
                compliance: [],
                recommendations: [],
                metadata: { error: error.message }
            };
            this.scanResults.set(request.id, errorResult);
            await this.memory.storeScanResult(errorResult);
            this.emit('scan-failed', { scanId: request.id, error: error.message });
        }
    }
    /**
     * Enrichit les résultats avec l'intelligence des menaces
     */
    async enrichWithThreatIntelligence(result) {
        for (const vulnerability of result.vulnerabilities) {
            // Recherche d'indicateurs de menace correspondants
            const indicators = await this.threatIntelligence.findIndicators(vulnerability);
            if (indicators.length > 0) {
                vulnerability.metadata = {
                    ...vulnerability.metadata,
                    threatIndicators: indicators,
                    threatLevel: this.calculateThreatLevel(indicators)
                };
            }
        }
        return result;
    }
    /**
     * Traite les vulnérabilités critiques
     */
    async processCriticalVulnerabilities(result) {
        const criticalVulns = result.vulnerabilities.filter(v => v.severity === 'critical');
        for (const vuln of criticalVulns) {
            // Création d'un incident automatique pour les vulnérabilités critiques
            const incident = await this.incidentResponse.createIncident({
                title: `Vulnérabilité critique détectée: ${vuln.title}`,
                description: vuln.description,
                severity: 'critical',
                category: this.mapSecurityCategoryToIncident(vuln.category),
                source: 'vulnerability-scanner',
                vulnerability: vuln
            });
            this.activeIncidents.set(incident.id, incident);
            // Notification immédiate
            this.emit('critical-vulnerability', {
                vulnerability: vuln,
                incident: incident,
                scanResult: result
            });
            // Alerte aux équipes
            await this.securityMonitor.sendAlert({
                type: 'critical-vulnerability',
                title: `Vulnérabilité critique: ${vuln.title}`,
                description: vuln.description,
                severity: 'critical',
                data: { vulnerability: vuln, incident: incident }
            });
        }
    }
    /**
     * Gère une alerte de sécurité
     */
    async handleSecurityAlert(alert) {
        this.logger.info(`🚨 Alerte de sécurité reçue: ${alert.type}`);
        // Analyse de l'alerte
        const analysis = await this.threatIntelligence.analyzeAlert(alert);
        // Création d'un incident si nécessaire
        if (analysis.createIncident) {
            const incident = await this.incidentResponse.createIncident({
                title: alert.title,
                description: alert.description,
                severity: alert.severity,
                category: alert.category,
                source: alert.source,
                alert: alert
            });
            this.activeIncidents.set(incident.id, incident);
        }
        // Stockage de l'alerte
        await this.memory.storeSecurityAlert(alert);
    }
    /**
     * Gère une demande de vérification de compliance
     */
    async handleComplianceRequest(request) {
        this.logger.info(`📋 Demande de compliance: ${request.framework}`);
        try {
            const results = await this.complianceChecker.checkFramework(request.framework, request.target, request.configuration);
            // Envoi des résultats
            await this.communication.sendMessage(request.requestedBy, {
                type: 'compliance-check-result',
                requestId: request.id,
                results: results
            });
            this.emit('compliance-checked', {
                framework: request.framework,
                results: results
            });
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la vérification de compliance:', error);
        }
    }
    /**
     * Gère un incident de sécurité
     */
    async handleSecurityIncident(incident) {
        this.logger.info(`🚨 Incident de sécurité: ${incident.title} (${incident.severity})`);
        // Ajout à la liste des incidents actifs
        this.activeIncidents.set(incident.id, incident);
        // Démarrage de la réponse automatique
        await this.incidentResponse.handleIncident(incident);
        // Notification
        this.emit('incident-created', incident);
        // Stockage
        await this.memory.storeSecurityIncident(incident);
    }
    /**
     * Gère une demande de rapport
     */
    async handleReportRequest(request) {
        this.logger.info(`📊 Demande de rapport: ${request.type}`);
        try {
            const report = await this.reportGenerator.generateReport(request.type, request.period, request.scope, request.configuration);
            // Envoi du rapport
            await this.communication.sendMessage(request.requestedBy, {
                type: 'security-report-result',
                requestId: request.id,
                report: report
            });
            this.emit('report-generated', {
                type: request.type,
                report: report
            });
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la génération de rapport:', error);
        }
    }
    // Méthodes utilitaires
    validateScanRequest(request) {
        return !!(request.id &&
            request.type &&
            request.target &&
            request.configuration &&
            request.requestedBy);
    }
    getPriorityWeight(priority) {
        const weights = { critical: 4, high: 3, medium: 2, low: 1 };
        return weights[priority] || 1;
    }
    calculateThreatLevel(indicators) {
        const maxConfidence = Math.max(...indicators.map(i => i.confidence));
        const criticalCount = indicators.filter(i => i.severity === 'critical').length;
        if (criticalCount > 0 && maxConfidence > 0.8)
            return 'critical';
        if (maxConfidence > 0.6)
            return 'high';
        if (maxConfidence > 0.4)
            return 'medium';
        return 'low';
    }
    mapSecurityCategoryToIncident(category) {
        const mapping = {
            'injection': 'vulnerability-exploitation',
            'authentication': 'unauthorized-access',
            'authorization': 'unauthorized-access',
            'data-exposure': 'data-breach',
            'cross-site-scripting': 'vulnerability-exploitation',
            'insecure-deserialization': 'vulnerability-exploitation'
        };
        return mapping[category] || 'vulnerability-exploitation';
    }
    /**
     * Récupère le statut de l'agent
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            activeScanQueue: this.activeScanQueue.size,
            completedScans: this.scanResults.size,
            activeIncidents: this.activeIncidents.size,
            threatIndicators: this.threatIndicators.size,
            capabilities: this.config.capabilities,
            scanners: this.config.scanners.filter(s => s.enabled).map(s => s.name),
            uptime: process.uptime()
        };
    }
    /**
     * Récupère les métriques de sécurité
     */
    async getSecurityMetrics() {
        return await this.reportGenerator.generateMetrics();
    }
    /**
     * Vérifie l'autorisation d'accès
     */
    async checkAccess(userId, resource, action, context) {
        return await this.accessControlManager.checkAccess(userId, resource, action, context);
    }
    /**
     * Crée une session utilisateur
     */
    async createUserSession(userId, userAttributes, authMethod) {
        return await this.accessControlManager.createSession(userId, userAttributes, authMethod);
    }
    /**
     * Révoque une session utilisateur
     */
    async revokeUserSession(userId, reason) {
        await this.accessControlManager.revokeSession(userId, reason);
    }
    /**
     * Enregistre un événement d'audit
     */
    async logAuditEvent(event) {
        await this.auditSystem.logEvent(event);
    }
    /**
     * Génère un rapport d'audit
     */
    async generateAuditReport(reportType, period, filters) {
        return await this.auditSystem.generateAuditReport(reportType, period, filters);
    }
    /**
     * Chiffre des données
     */
    async encryptData(data, keyId, algorithm) {
        return await this.encryptionManager.encrypt(data, keyId, algorithm);
    }
    /**
     * Déchiffre des données
     */
    async decryptData(encryptedData, keyId, algorithm, iv) {
        return await this.encryptionManager.decrypt(encryptedData, keyId, algorithm, iv);
    }
    /**
     * Génère une nouvelle clé de chiffrement
     */
    async generateEncryptionKey(keyId, algorithm, keySize) {
        return await this.encryptionManager.generateKey(keyId, algorithm, keySize);
    }
    /**
     * Effectue la rotation d'une clé
     */
    async rotateEncryptionKey(keyId) {
        return await this.encryptionManager.rotateKey(keyId);
    }
    /**
     * Crée une nouvelle politique de sécurité
     */
    async createSecurityPolicy(policyData) {
        return await this.securityPolicyEngine.createPolicy(policyData);
    }
    /**
     * Évalue une action contre les politiques
     */
    async evaluateAction(action) {
        return await this.securityPolicyEngine.evaluateAction(action);
    }
    /**
     * Met à jour une politique de sécurité
     */
    async updateSecurityPolicy(policyId, updates) {
        return await this.securityPolicyEngine.updatePolicy(policyId, updates);
    }
    /**
     * Supprime une politique de sécurité
     */
    async deleteSecurityPolicy(policyId) {
        await this.securityPolicyEngine.deletePolicy(policyId);
    }
    /**
     * Arrêt de l'agent de sécurité
     */
    async shutdown() {
        this.logger.info('🛑 Arrêt de l\'Agent Security...');
        // Arrêt des composants
        await Promise.all([
            this.vulnerabilityScanner.shutdown(),
            this.complianceChecker.shutdown(),
            this.threatIntelligence.shutdown(),
            this.incidentResponse.shutdown(),
            this.securityMonitor.shutdown(),
            this.reportGenerator.shutdown(),
            this.accessControlManager.shutdown(),
            this.auditSystem.shutdown(),
            this.encryptionManager.shutdown(),
            this.securityPolicyEngine.shutdown()
        ]);
        this.isInitialized = false;
        this.logger.info('✅ Agent Security arrêté');
    }
}
exports.SecurityAgent = SecurityAgent;
//# sourceMappingURL=SecurityAgent.js.map