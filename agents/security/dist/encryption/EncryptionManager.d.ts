import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Gestionnaire de Chiffrement
 *
 * Gère tous les aspects du chiffrement et de la cryptographie
 */
export declare class EncryptionManager extends EventEmitter {
    private logger;
    private memory;
    private encryptionKeys;
    private certificates;
    private keyRotationSchedule;
    private isInitialized;
    private defaultAlgorithm;
    private keyDerivationIterations;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le gestionnaire de chiffrement
     */
    initialize(): Promise<void>;
    /**
     * Chiffre des données
     */
    encrypt(data: string | Buffer, keyId?: string, algorithm?: string): Promise<EncryptionResult>;
    /**
     * Déchiffre des données
     */
    decrypt(encryptedData: string, keyId: string, algorithm: string, iv?: string): Promise<string>;
    /**
     * Génère une nouvelle clé de chiffrement
     */
    generateKey(keyId: string, algorithm?: string, keySize?: number): Promise<EncryptionKey>;
    /**
     * Effectue la rotation d'une clé
     */
    rotateKey(keyId: string): Promise<EncryptionKey>;
    catch(error: any): void;
}
interface EncryptionKey {
    id: string;
    algorithm: string;
    type?: 'symmetric' | 'public' | 'private';
    value: Buffer;
    salt?: Buffer;
    keySize: number;
    createdAt: Date;
    expiresAt: Date;
    rotatedAt?: Date;
    isActive: boolean;
    version: number;
    previousVersion?: number;
    metadata: any;
}
interface EncryptionResult {
    encryptedData: string;
    algorithm: string;
    keyId: string;
    iv: string;
    timestamp: Date;
    processingTime: number;
}
export {};
//# sourceMappingURL=EncryptionManager.d.ts.map