import { Logger } from 'winston';
import { SecurityScanRequest, SecurityScanResult, SecurityIncident, ThreatIndicator } from '../types';
/**
 * Gestionnaire de Mémoire Weaviate pour l'Agent Security
 *
 * Stockage et récupération des données de sécurité dans Weaviate
 */
export declare class WeaviateMemory {
    private logger;
    private client;
    private url;
    private isConnected;
    constructor(logger: Logger, weaviateUrl: string);
    /**
     * Initialise la connexion Weaviate
     */
    initialize(): Promise<void>;
    /**
     * Crée les schémas Weaviate pour les données de sécurité
     */
    private createSchemas;
    /**
     * Stocke une demande de scan
     */
    storeScanRequest(request: SecurityScanRequest): Promise<void>;
    /**
     * Stocke un résultat de scan
     */
    storeScanResult(result: SecurityScanResult): Promise<void>;
    /**
     * Stocke un incident de sécurité
     */
    storeSecurityIncident(incident: SecurityIncident): Promise<void>;
    /**
     * Stocke un indicateur de menace
     */
    storeThreatIndicator(indicator: ThreatIndicator): Promise<void>;
    /**
     * Stocke une alerte de sécurité
     */
    storeSecurityAlert(alert: any): Promise<void>;
    /**
     * Récupère les indicateurs de menace
     */
    getThreatIndicators(limit?: number): Promise<ThreatIndicator[]>;
    /**
     * Récupère les incidents actifs
     */
    getActiveIncidents(): Promise<SecurityIncident[]>;
    /**
     * Récupère les résultats de scan récents
     */
    getRecentScanResults(limit?: number): Promise<SecurityScanResult[]>;
    /**
     * Recherche d'indicateurs de menace par valeur
     */
    searchThreatIndicators(value: string): Promise<ThreatIndicator[]>;
    /**
     * Récupère les métriques de sécurité
     */
    getSecurityMetrics(): Promise<any>;
    private calculateVulnerabilityMetrics;
    private calculateIncidentMetrics;
    private calculateThreatMetrics;
    /**
     * Vérifie si la connexion est active
     */
    getConnectionStatus(): boolean;
    /**
     * Ferme la connexion
     */
    close(): Promise<void>;
}
//# sourceMappingURL=WeaviateMemory.d.ts.map