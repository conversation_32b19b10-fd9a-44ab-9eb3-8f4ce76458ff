"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeaviateMemory = void 0;
const weaviate_ts_client_1 = __importDefault(require("weaviate-ts-client"));
/**
 * Gestionnaire de Mémoire Weaviate pour l'Agent Security
 *
 * Stockage et récupération des données de sécurité dans Weaviate
 */
class WeaviateMemory {
    constructor(logger, weaviateUrl) {
        this.isConnected = false;
        this.logger = logger;
        this.url = weaviateUrl;
        this.client = weaviate_ts_client_1.default.client({
            scheme: 'http',
            host: weaviateUrl.replace('http://', '').replace('https://', '')
        });
    }
    /**
     * Initialise la connexion Weaviate
     */
    async initialize() {
        try {
            this.logger.info('🗄️ Initialisation de la mémoire Weaviate Security...');
            // Test de connexion
            await this.client.misc.liveChecker().do();
            // Création des schémas si nécessaire
            await this.createSchemas();
            this.isConnected = true;
            this.logger.info('✅ Mémoire Weaviate Security initialisée');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation Weaviate Security:', error);
            throw error;
        }
    }
    /**
     * Crée les schémas Weaviate pour les données de sécurité
     */
    async createSchemas() {
        const schemas = [
            {
                class: 'SecurityScanRequest',
                description: 'Demandes de scan de sécurité',
                properties: [
                    { name: 'scanId', dataType: ['string'] },
                    { name: 'type', dataType: ['string'] },
                    { name: 'target', dataType: ['object'] },
                    { name: 'priority', dataType: ['string'] },
                    { name: 'requestedBy', dataType: ['string'] },
                    { name: 'timestamp', dataType: ['date'] },
                    { name: 'configuration', dataType: ['object'] }
                ]
            },
            {
                class: 'SecurityScanResult',
                description: 'Résultats de scan de sécurité',
                properties: [
                    { name: 'scanId', dataType: ['string'] },
                    { name: 'status', dataType: ['string'] },
                    { name: 'duration', dataType: ['number'] },
                    { name: 'summary', dataType: ['object'] },
                    { name: 'vulnerabilities', dataType: ['object[]'] },
                    { name: 'compliance', dataType: ['object[]'] },
                    { name: 'recommendations', dataType: ['object[]'] },
                    { name: 'timestamp', dataType: ['date'] }
                ]
            },
            {
                class: 'SecurityIncident',
                description: 'Incidents de sécurité',
                properties: [
                    { name: 'incidentId', dataType: ['string'] },
                    { name: 'title', dataType: ['string'] },
                    { name: 'description', dataType: ['text'] },
                    { name: 'severity', dataType: ['string'] },
                    { name: 'status', dataType: ['string'] },
                    { name: 'category', dataType: ['string'] },
                    { name: 'source', dataType: ['string'] },
                    { name: 'detectedAt', dataType: ['date'] },
                    { name: 'timeline', dataType: ['object[]'] },
                    { name: 'artifacts', dataType: ['object[]'] }
                ]
            },
            {
                class: 'ThreatIndicator',
                description: 'Indicateurs de menace',
                properties: [
                    { name: 'indicatorId', dataType: ['string'] },
                    { name: 'type', dataType: ['string'] },
                    { name: 'value', dataType: ['string'] },
                    { name: 'confidence', dataType: ['number'] },
                    { name: 'severity', dataType: ['string'] },
                    { name: 'source', dataType: ['string'] },
                    { name: 'firstSeen', dataType: ['date'] },
                    { name: 'lastSeen', dataType: ['date'] },
                    { name: 'tags', dataType: ['string[]'] },
                    { name: 'context', dataType: ['object'] }
                ]
            },
            {
                class: 'SecurityAlert',
                description: 'Alertes de sécurité',
                properties: [
                    { name: 'alertId', dataType: ['string'] },
                    { name: 'type', dataType: ['string'] },
                    { name: 'title', dataType: ['string'] },
                    { name: 'description', dataType: ['text'] },
                    { name: 'severity', dataType: ['string'] },
                    { name: 'source', dataType: ['string'] },
                    { name: 'timestamp', dataType: ['date'] },
                    { name: 'data', dataType: ['object'] }
                ]
            }
        ];
        for (const schema of schemas) {
            try {
                // Vérifier si la classe existe déjà
                const exists = await this.client.schema.exists(schema.class);
                if (!exists) {
                    await this.client.schema.classCreator().withClass(schema).do();
                    this.logger.info(`📋 Schéma créé: ${schema.class}`);
                }
            }
            catch (error) {
                this.logger.warn(`⚠️ Erreur lors de la création du schéma ${schema.class}:`, error);
            }
        }
    }
    /**
     * Stocke une demande de scan
     */
    async storeScanRequest(request) {
        try {
            await this.client.data.creator()
                .withClassName('SecurityScanRequest')
                .withProperties({
                scanId: request.id,
                type: request.type,
                target: request.target,
                priority: request.priority,
                requestedBy: request.requestedBy,
                timestamp: request.timestamp.toISOString(),
                configuration: request.configuration
            })
                .do();
            this.logger.debug(`💾 Demande de scan stockée: ${request.id}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du stockage de la demande de scan:', error);
        }
    }
    /**
     * Stocke un résultat de scan
     */
    async storeScanResult(result) {
        try {
            await this.client.data.creator()
                .withClassName('SecurityScanResult')
                .withProperties({
                scanId: result.scanId,
                status: result.status,
                duration: result.duration,
                summary: result.summary,
                vulnerabilities: result.vulnerabilities,
                compliance: result.compliance,
                recommendations: result.recommendations,
                timestamp: result.timestamp.toISOString()
            })
                .do();
            this.logger.debug(`💾 Résultat de scan stocké: ${result.scanId}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du stockage du résultat de scan:', error);
        }
    }
    /**
     * Stocke un incident de sécurité
     */
    async storeSecurityIncident(incident) {
        try {
            await this.client.data.creator()
                .withClassName('SecurityIncident')
                .withProperties({
                incidentId: incident.id,
                title: incident.title,
                description: incident.description,
                severity: incident.severity,
                status: incident.status,
                category: incident.category,
                source: incident.source,
                detectedAt: incident.detectedAt.toISOString(),
                timeline: incident.timeline,
                artifacts: incident.artifacts
            })
                .do();
            this.logger.debug(`💾 Incident de sécurité stocké: ${incident.id}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du stockage de l\'incident:', error);
        }
    }
    /**
     * Stocke un indicateur de menace
     */
    async storeThreatIndicator(indicator) {
        try {
            await this.client.data.creator()
                .withClassName('ThreatIndicator')
                .withProperties({
                indicatorId: indicator.id,
                type: indicator.type,
                value: indicator.value,
                confidence: indicator.confidence,
                severity: indicator.severity,
                source: indicator.source,
                firstSeen: indicator.firstSeen.toISOString(),
                lastSeen: indicator.lastSeen.toISOString(),
                tags: indicator.tags,
                context: indicator.context
            })
                .do();
            this.logger.debug(`💾 Indicateur de menace stocké: ${indicator.id}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du stockage de l\'indicateur:', error);
        }
    }
    /**
     * Stocke une alerte de sécurité
     */
    async storeSecurityAlert(alert) {
        try {
            await this.client.data.creator()
                .withClassName('SecurityAlert')
                .withProperties({
                alertId: alert.id || `alert-${Date.now()}`,
                type: alert.type,
                title: alert.title,
                description: alert.description,
                severity: alert.severity,
                source: alert.source,
                timestamp: (alert.timestamp || new Date()).toISOString(),
                data: alert.data || alert
            })
                .do();
            this.logger.debug(`💾 Alerte de sécurité stockée: ${alert.id}`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du stockage de l\'alerte:', error);
        }
    }
    /**
     * Récupère les indicateurs de menace
     */
    async getThreatIndicators(limit = 100) {
        try {
            const result = await this.client.graphql.get()
                .withClassName('ThreatIndicator')
                .withFields('indicatorId type value confidence severity source firstSeen lastSeen tags context')
                .withLimit(limit)
                .do();
            return result.data.Get.ThreatIndicator?.map((item) => ({
                id: item.indicatorId,
                type: item.type,
                value: item.value,
                confidence: item.confidence,
                severity: item.severity,
                source: item.source,
                firstSeen: new Date(item.firstSeen),
                lastSeen: new Date(item.lastSeen),
                tags: item.tags || [],
                context: item.context || {}
            })) || [];
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la récupération des indicateurs:', error);
            return [];
        }
    }
    /**
     * Récupère les incidents actifs
     */
    async getActiveIncidents() {
        try {
            const result = await this.client.graphql.get()
                .withClassName('SecurityIncident')
                .withFields('incidentId title description severity status category source detectedAt timeline artifacts')
                .withWhere({
                path: ['status'],
                operator: 'NotEqual',
                valueString: 'closed'
            })
                .do();
            return result.data.Get.SecurityIncident?.map((item) => ({
                id: item.incidentId,
                title: item.title,
                description: item.description,
                severity: item.severity,
                status: item.status,
                category: item.category,
                source: item.source,
                detectedAt: new Date(item.detectedAt),
                reportedAt: new Date(item.detectedAt), // Approximation
                timeline: item.timeline || [],
                artifacts: item.artifacts || [],
                response: { actions: [], containment: [], eradication: [], recovery: [], lessons_learned: [] }
            })) || [];
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la récupération des incidents:', error);
            return [];
        }
    }
    /**
     * Récupère les résultats de scan récents
     */
    async getRecentScanResults(limit = 50) {
        try {
            const result = await this.client.graphql.get()
                .withClassName('SecurityScanResult')
                .withFields('scanId status duration summary vulnerabilities compliance recommendations timestamp')
                .withLimit(limit)
                .withSort([{ path: ['timestamp'], order: 'desc' }])
                .do();
            return result.data.Get.SecurityScanResult?.map((item) => ({
                id: `result-${item.scanId}`,
                scanId: item.scanId,
                timestamp: new Date(item.timestamp),
                status: item.status,
                duration: item.duration,
                summary: item.summary,
                vulnerabilities: item.vulnerabilities || [],
                compliance: item.compliance || [],
                recommendations: item.recommendations || [],
                metadata: {}
            })) || [];
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la récupération des résultats:', error);
            return [];
        }
    }
    /**
     * Recherche d'indicateurs de menace par valeur
     */
    async searchThreatIndicators(value) {
        try {
            const result = await this.client.graphql.get()
                .withClassName('ThreatIndicator')
                .withFields('indicatorId type value confidence severity source firstSeen lastSeen tags context')
                .withWhere({
                path: ['value'],
                operator: 'Like',
                valueString: `*${value}*`
            })
                .do();
            return result.data.Get.ThreatIndicator?.map((item) => ({
                id: item.indicatorId,
                type: item.type,
                value: item.value,
                confidence: item.confidence,
                severity: item.severity,
                source: item.source,
                firstSeen: new Date(item.firstSeen),
                lastSeen: new Date(item.lastSeen),
                tags: item.tags || [],
                context: item.context || {}
            })) || [];
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de la recherche d\'indicateurs:', error);
            return [];
        }
    }
    /**
     * Récupère les métriques de sécurité
     */
    async getSecurityMetrics() {
        try {
            // Agrégation des données pour les métriques
            const [scanResults, incidents, indicators] = await Promise.all([
                this.getRecentScanResults(100),
                this.getActiveIncidents(),
                this.getThreatIndicators(1000)
            ]);
            // Calcul des métriques
            const vulnerabilityMetrics = this.calculateVulnerabilityMetrics(scanResults);
            const incidentMetrics = this.calculateIncidentMetrics(incidents);
            const threatMetrics = this.calculateThreatMetrics(indicators);
            return {
                vulnerabilities: vulnerabilityMetrics,
                incidents: incidentMetrics,
                threats: threatMetrics,
                coverage: {
                    assetsScanned: scanResults.length,
                    assetsTotal: scanResults.length, // Approximation
                    coveragePercentage: 100,
                    scanFrequency: 1,
                    lastScanAge: scanResults.length > 0 ?
                        Date.now() - scanResults[0].timestamp.getTime() : 0
                }
            };
        }
        catch (error) {
            this.logger.error('❌ Erreur lors du calcul des métriques:', error);
            return {
                vulnerabilities: { total: 0, bySeverity: {}, byCategory: {}, trends: [] },
                incidents: { total: 0, bySeverity: {}, byCategory: {}, trends: [] },
                threats: { indicatorsTotal: 0, indicatorsByType: {}, trends: [] },
                coverage: { assetsScanned: 0, assetsTotal: 0, coveragePercentage: 0 }
            };
        }
    }
    // Méthodes utilitaires pour le calcul des métriques
    calculateVulnerabilityMetrics(scanResults) {
        const allVulns = scanResults.flatMap(r => r.vulnerabilities);
        const bySeverity = {};
        const byCategory = {};
        allVulns.forEach(vuln => {
            bySeverity[vuln.severity] = (bySeverity[vuln.severity] || 0) + 1;
            byCategory[vuln.category] = (byCategory[vuln.category] || 0) + 1;
        });
        return {
            total: allVulns.length,
            bySeverity,
            byCategory,
            meanTimeToDetection: 0, // À calculer avec plus de données
            meanTimeToRemediation: 0,
            trends: []
        };
    }
    calculateIncidentMetrics(incidents) {
        const bySeverity = {};
        const byCategory = {};
        incidents.forEach(incident => {
            bySeverity[incident.severity] = (bySeverity[incident.severity] || 0) + 1;
            byCategory[incident.category] = (byCategory[incident.category] || 0) + 1;
        });
        return {
            total: incidents.length,
            bySeverity,
            byCategory,
            meanTimeToDetection: 0,
            meanTimeToContainment: 0,
            meanTimeToResolution: 0,
            trends: []
        };
    }
    calculateThreatMetrics(indicators) {
        const indicatorsByType = {};
        indicators.forEach(indicator => {
            indicatorsByType[indicator.type] = (indicatorsByType[indicator.type] || 0) + 1;
        });
        return {
            indicatorsTotal: indicators.length,
            indicatorsByType,
            threatsBlocked: 0, // À implémenter
            threatsDetected: indicators.length,
            falsePositives: 0,
            trends: []
        };
    }
    /**
     * Vérifie si la connexion est active
     */
    getConnectionStatus() {
        return this.isConnected;
    }
    /**
     * Ferme la connexion
     */
    async close() {
        this.isConnected = false;
        this.logger.info('🔌 Connexion Weaviate Security fermée');
    }
}
exports.WeaviateMemory = WeaviateMemory;
//# sourceMappingURL=WeaviateMemory.js.map