import { Logger } from 'winston';
import { SecurityScanRequest, Vulnerability } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Analyseur Statique (SAST)
 *
 * Analyse statique du code source pour détecter les vulnérabilités
 */
export declare class StaticAnalyzer {
    private logger;
    private memory;
    private securityRules;
    private isInitialized;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise l'analyseur statique
     */
    initialize(): Promise<void>;
    /**
     * Charge les règles de sécurité
     */
    private loadSecurityRules;
    /**
     * Analyse le code source
     */
    analyze(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Analyse les dépendances
     */
    analyzeDependencies(request: SecurityScanRequest): Promise<Vulnerability[]>;
    /**
     * Analyse un fichier source
     */
    private analyzeFile;
    /**
     * Analyse les dépendances Node.js
     */
    private analyzeNodeDependencies;
    /**
     * Analyse les dépendances Python
     */
    private analyzePythonDependencies;
    /**
     * Analyse les dépendances Java
     */
    private analyzeJavaDependencies;
    private detectLanguage;
    private getSourceFiles;
    private findMatches;
    private getLineNumber;
    private getCodeContext;
    private mapNpmSeverity;
    private mapCvssSeverity;
    /**
     * Arrêt de l'analyseur
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=StaticAnalyzer.d.ts.map