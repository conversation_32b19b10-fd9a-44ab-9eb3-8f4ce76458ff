"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StaticAnalyzer = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
/**
 * Analyseur Statique (SAST)
 *
 * Analyse statique du code source pour détecter les vulnérabilités
 */
class StaticAnalyzer {
    constructor(logger, memory) {
        this.securityRules = new Map();
        this.isInitialized = false;
        this.logger = logger;
        this.memory = memory;
    }
    /**
     * Initialise l'analyseur statique
     */
    async initialize() {
        try {
            this.logger.info('🔍 Initialisation de l\'Analyseur Statique...');
            // Chargement des règles de sécurité
            await this.loadSecurityRules();
            this.isInitialized = true;
            this.logger.info('✅ Analyseur Statique initialisé');
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'initialisation de l\'analyseur statique:', error);
            throw error;
        }
    }
    /**
     * Charge les règles de sécurité
     */
    async loadSecurityRules() {
        // Règles JavaScript/TypeScript
        this.securityRules.set('javascript', [
            {
                id: 'js-sql-injection',
                name: 'SQL Injection',
                description: 'Détection d\'injection SQL potentielle',
                severity: 'high',
                category: 'injection',
                pattern: /(?:query|execute|exec)\s*\(\s*['"]\s*SELECT.*\+.*['"]\s*\)/gi,
                remediation: 'Utilisez des requêtes préparées ou un ORM',
                references: ['https://owasp.org/www-community/attacks/SQL_Injection'],
                cwe: 'CWE-89',
                owasp: 'A03:2021'
            },
            {
                id: 'js-xss',
                name: 'Cross-Site Scripting (XSS)',
                description: 'Insertion directe de contenu utilisateur dans le DOM',
                severity: 'medium',
                category: 'cross-site-scripting',
                pattern: /\.innerHTML\s*=\s*.*(?:req\.body|req\.query|req\.params)/gi,
                remediation: 'Échappez le contenu utilisateur avant insertion',
                references: ['https://owasp.org/www-community/attacks/xss/'],
                cwe: 'CWE-79',
                owasp: 'A03:2021'
            },
            {
                id: 'js-hardcoded-secrets',
                name: 'Secrets en dur',
                description: 'Détection de secrets potentiels dans le code',
                severity: 'critical',
                category: 'data-exposure',
                pattern: /(?:password|secret|key|token)\s*[:=]\s*['"][^'"]{8,}['"]/gi,
                remediation: 'Utilisez des variables d\'environnement pour les secrets',
                references: ['https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_password'],
                cwe: 'CWE-798'
            },
            {
                id: 'js-eval-usage',
                name: 'Utilisation d\'eval()',
                description: 'Utilisation dangereuse de la fonction eval()',
                severity: 'high',
                category: 'injection',
                pattern: /\beval\s*\(/gi,
                remediation: 'Évitez l\'utilisation d\'eval() ou validez strictement les entrées',
                references: ['https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/eval'],
                cwe: 'CWE-95'
            },
            {
                id: 'js-weak-crypto',
                name: 'Cryptographie faible',
                description: 'Utilisation d\'algorithmes cryptographiques faibles',
                severity: 'medium',
                category: 'cryptographic-failures',
                pattern: /(?:md5|sha1|des|rc4)\s*\(/gi,
                remediation: 'Utilisez des algorithmes cryptographiques modernes (SHA-256, AES)',
                references: ['https://owasp.org/www-project-cryptographic-storage-cheat-sheet/'],
                cwe: 'CWE-327'
            }
        ]);
        // Règles Python
        this.securityRules.set('python', [
            {
                id: 'py-sql-injection',
                name: 'SQL Injection',
                description: 'Détection d\'injection SQL potentielle en Python',
                severity: 'high',
                category: 'injection',
                pattern: /(?:execute|cursor\.execute)\s*\(\s*f?['"].*%.*['"].*%/gi,
                remediation: 'Utilisez des requêtes paramétrées',
                references: ['https://owasp.org/www-community/attacks/SQL_Injection'],
                cwe: 'CWE-89'
            },
            {
                id: 'py-command-injection',
                name: 'Command Injection',
                description: 'Exécution de commandes système avec entrées utilisateur',
                severity: 'critical',
                category: 'injection',
                pattern: /(?:os\.system|subprocess\.call|subprocess\.run)\s*\(.*(?:input\(|request\.)/gi,
                remediation: 'Validez et échappez les entrées utilisateur',
                references: ['https://owasp.org/www-community/attacks/Command_Injection'],
                cwe: 'CWE-78'
            },
            {
                id: 'py-pickle-deserialization',
                name: 'Désérialisation non sécurisée',
                description: 'Utilisation dangereuse de pickle.loads()',
                severity: 'high',
                category: 'insecure-deserialization',
                pattern: /pickle\.loads?\s*\(/gi,
                remediation: 'Utilisez des formats de sérialisation sécurisés comme JSON',
                references: ['https://owasp.org/www-community/vulnerabilities/Deserialization_of_untrusted_data'],
                cwe: 'CWE-502'
            }
        ]);
        // Règles Java
        this.securityRules.set('java', [
            {
                id: 'java-sql-injection',
                name: 'SQL Injection',
                description: 'Détection d\'injection SQL potentielle en Java',
                severity: 'high',
                category: 'injection',
                pattern: /Statement\.execute(?:Query|Update)?\s*\(\s*.*\+.*\)/gi,
                remediation: 'Utilisez PreparedStatement',
                references: ['https://owasp.org/www-community/attacks/SQL_Injection'],
                cwe: 'CWE-89'
            },
            {
                id: 'java-xxe',
                name: 'XML External Entity (XXE)',
                description: 'Traitement XML non sécurisé',
                severity: 'high',
                category: 'xml-external-entities',
                pattern: /DocumentBuilderFactory\.newInstance\(\)(?!.*setFeature)/gi,
                remediation: 'Désactivez les entités externes XML',
                references: ['https://owasp.org/www-community/vulnerabilities/XML_External_Entity_(XXE)_Processing'],
                cwe: 'CWE-611'
            }
        ]);
        this.logger.info(`📚 ${Array.from(this.securityRules.values()).flat().length} règles de sécurité chargées`);
    }
    /**
     * Analyse le code source
     */
    async analyze(request) {
        this.logger.info(`🔍 Analyse statique: ${request.target.location}`);
        const vulnerabilities = [];
        try {
            // Détermination du langage
            const language = this.detectLanguage(request.target);
            const rules = this.securityRules.get(language) || [];
            if (rules.length === 0) {
                this.logger.warn(`⚠️ Aucune règle disponible pour le langage: ${language}`);
                return vulnerabilities;
            }
            // Lecture des fichiers source
            const sourceFiles = await this.getSourceFiles(request.target.location);
            // Analyse de chaque fichier
            for (const filePath of sourceFiles) {
                const fileVulns = await this.analyzeFile(filePath, rules, request);
                vulnerabilities.push(...fileVulns);
            }
            // Analyse des dépendances
            const depVulns = await this.analyzeDependencies(request);
            vulnerabilities.push(...depVulns);
            this.logger.info(`✅ Analyse statique terminée: ${vulnerabilities.length} vulnérabilités trouvées`);
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'analyse statique:', error);
            throw error;
        }
        return vulnerabilities;
    }
    /**
     * Analyse les dépendances
     */
    async analyzeDependencies(request) {
        this.logger.info('📦 Analyse des dépendances...');
        const vulnerabilities = [];
        try {
            const projectPath = request.target.location;
            // Analyse package.json (Node.js)
            const packageJsonPath = path.join(projectPath, 'package.json');
            if (await fs.pathExists(packageJsonPath)) {
                const nodeVulns = await this.analyzeNodeDependencies(packageJsonPath);
                vulnerabilities.push(...nodeVulns);
            }
            // Analyse requirements.txt (Python)
            const requirementsPath = path.join(projectPath, 'requirements.txt');
            if (await fs.pathExists(requirementsPath)) {
                const pythonVulns = await this.analyzePythonDependencies(requirementsPath);
                vulnerabilities.push(...pythonVulns);
            }
            // Analyse pom.xml (Java Maven)
            const pomPath = path.join(projectPath, 'pom.xml');
            if (await fs.pathExists(pomPath)) {
                const javaVulns = await this.analyzeJavaDependencies(pomPath);
                vulnerabilities.push(...javaVulns);
            }
        }
        catch (error) {
            this.logger.error('❌ Erreur lors de l\'analyse des dépendances:', error);
        }
        return vulnerabilities;
    }
    /**
     * Analyse un fichier source
     */
    async analyzeFile(filePath, rules, request) {
        const vulnerabilities = [];
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            const lines = content.split('\n');
            // Application des règles
            for (const rule of rules) {
                const matches = this.findMatches(content, rule.pattern);
                for (const match of matches) {
                    const lineNumber = this.getLineNumber(content, match.index);
                    const vulnerability = {
                        id: `vuln-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                        title: rule.name,
                        description: rule.description,
                        severity: rule.severity,
                        category: rule.category,
                        cwe: rule.cwe,
                        location: {
                            file: path.relative(request.target.location, filePath),
                            line: lineNumber,
                            column: match.index - content.lastIndexOf('\n', match.index) - 1
                        },
                        evidence: [{
                                type: 'code',
                                content: lines[lineNumber - 1]?.trim() || '',
                                metadata: {
                                    context: this.getCodeContext(lines, lineNumber, 3),
                                    pattern: rule.pattern.toString()
                                }
                            }],
                        remediation: {
                            description: rule.remediation,
                            steps: [rule.remediation],
                            effort: 'medium',
                            priority: rule.severity === 'critical' ? 'critical' : 'high',
                            automated: false
                        },
                        references: rule.references,
                        falsePositive: false,
                        suppressed: false,
                        firstFound: new Date(),
                        lastSeen: new Date()
                    };
                    vulnerabilities.push(vulnerability);
                }
            }
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors de l'analyse du fichier ${filePath}:`, error);
        }
        return vulnerabilities;
    }
    /**
     * Analyse les dépendances Node.js
     */
    async analyzeNodeDependencies(packageJsonPath) {
        const vulnerabilities = [];
        try {
            // Utilisation de npm audit
            const projectDir = path.dirname(packageJsonPath);
            const auditResult = (0, child_process_1.execSync)('npm audit --json', {
                cwd: projectDir,
                encoding: 'utf-8'
            });
            const audit = JSON.parse(auditResult);
            if (audit.vulnerabilities) {
                for (const [packageName, vulnData] of Object.entries(audit.vulnerabilities)) {
                    const vuln = vulnData;
                    vulnerabilities.push({
                        id: `dep-${packageName}-${Date.now()}`,
                        title: `Vulnérabilité dans ${packageName}`,
                        description: vuln.title || 'Vulnérabilité de dépendance',
                        severity: this.mapNpmSeverity(vuln.severity),
                        category: 'vulnerable-components',
                        cwe: vuln.cwe,
                        cve: vuln.cves?.[0],
                        location: {
                            file: 'package.json',
                            component: packageName,
                            version: vuln.range
                        },
                        evidence: [{
                                type: 'log',
                                content: JSON.stringify(vuln, null, 2)
                            }],
                        remediation: {
                            description: `Mettre à jour ${packageName} vers une version sécurisée`,
                            steps: [`npm update ${packageName}`],
                            effort: 'low',
                            priority: vuln.severity === 'critical' ? 'critical' : 'high',
                            automated: true
                        },
                        references: vuln.url ? [vuln.url] : [],
                        falsePositive: false,
                        suppressed: false,
                        firstFound: new Date(),
                        lastSeen: new Date()
                    });
                }
            }
        }
        catch (error) {
            this.logger.warn('⚠️ Impossible d\'exécuter npm audit:', error.message);
        }
        return vulnerabilities;
    }
    /**
     * Analyse les dépendances Python
     */
    async analyzePythonDependencies(requirementsPath) {
        const vulnerabilities = [];
        try {
            // Utilisation de safety (si disponible)
            const safetyResult = (0, child_process_1.execSync)('safety check --json', {
                cwd: path.dirname(requirementsPath),
                encoding: 'utf-8'
            });
            const safetyData = JSON.parse(safetyResult);
            for (const vuln of safetyData) {
                vulnerabilities.push({
                    id: `py-dep-${vuln.package}-${Date.now()}`,
                    title: `Vulnérabilité dans ${vuln.package}`,
                    description: vuln.advisory,
                    severity: 'high', // Safety ne fournit pas de niveau de sévérité
                    category: 'vulnerable-components',
                    cve: vuln.id,
                    location: {
                        file: 'requirements.txt',
                        component: vuln.package,
                        version: vuln.installed_version
                    },
                    evidence: [{
                            type: 'log',
                            content: JSON.stringify(vuln, null, 2)
                        }],
                    remediation: {
                        description: `Mettre à jour ${vuln.package} vers la version ${vuln.safe_version} ou supérieure`,
                        steps: [`pip install ${vuln.package}>=${vuln.safe_version}`],
                        effort: 'low',
                        priority: 'high',
                        automated: true
                    },
                    references: [],
                    falsePositive: false,
                    suppressed: false,
                    firstFound: new Date(),
                    lastSeen: new Date()
                });
            }
        }
        catch (error) {
            this.logger.warn('⚠️ Impossible d\'exécuter safety check:', error.message);
        }
        return vulnerabilities;
    }
    /**
     * Analyse les dépendances Java
     */
    async analyzeJavaDependencies(pomPath) {
        const vulnerabilities = [];
        try {
            // Utilisation d'OWASP Dependency Check (si disponible)
            const projectDir = path.dirname(pomPath);
            (0, child_process_1.execSync)('dependency-check --project "Security Scan" --scan . --format JSON --out .', {
                cwd: projectDir
            });
            const reportPath = path.join(projectDir, 'dependency-check-report.json');
            if (await fs.pathExists(reportPath)) {
                const report = await fs.readJson(reportPath);
                for (const dependency of report.dependencies || []) {
                    if (dependency.vulnerabilities) {
                        for (const vuln of dependency.vulnerabilities) {
                            vulnerabilities.push({
                                id: `java-dep-${dependency.fileName}-${vuln.name}`,
                                title: `Vulnérabilité dans ${dependency.fileName}`,
                                description: vuln.description,
                                severity: this.mapCvssSeverity(vuln.cvssv3?.baseScore || vuln.cvssv2?.score),
                                category: 'vulnerable-components',
                                cve: vuln.name,
                                cvss: vuln.cvssv3 ? {
                                    version: '3.1',
                                    baseScore: vuln.cvssv3.baseScore,
                                    vector: vuln.cvssv3.attackVector,
                                    exploitability: vuln.cvssv3.exploitabilityScore,
                                    impact: vuln.cvssv3.impactScore
                                } : undefined,
                                location: {
                                    file: 'pom.xml',
                                    component: dependency.fileName
                                },
                                evidence: [{
                                        type: 'log',
                                        content: JSON.stringify(vuln, null, 2)
                                    }],
                                remediation: {
                                    description: 'Mettre à jour vers une version sécurisée',
                                    steps: ['Vérifier les versions disponibles', 'Mettre à jour la dépendance'],
                                    effort: 'medium',
                                    priority: 'high',
                                    automated: false
                                },
                                references: vuln.references?.map((ref) => ref.url) || [],
                                falsePositive: false,
                                suppressed: false,
                                firstFound: new Date(),
                                lastSeen: new Date()
                            });
                        }
                    }
                }
                // Nettoyage
                await fs.remove(reportPath);
            }
        }
        catch (error) {
            this.logger.warn('⚠️ Impossible d\'exécuter dependency-check:', error.message);
        }
        return vulnerabilities;
    }
    // Méthodes utilitaires
    detectLanguage(target) {
        const location = target.location;
        const metadata = target.metadata;
        // Détection basée sur les métadonnées
        if (metadata?.language) {
            return metadata.language.toLowerCase();
        }
        // Détection basée sur l'extension ou le contenu
        if (location.includes('.js') || location.includes('.ts')) {
            return 'javascript';
        }
        else if (location.includes('.py')) {
            return 'python';
        }
        else if (location.includes('.java')) {
            return 'java';
        }
        else if (location.includes('.php')) {
            return 'php';
        }
        else if (location.includes('.cs')) {
            return 'csharp';
        }
        else if (location.includes('.go')) {
            return 'go';
        }
        return 'unknown';
    }
    async getSourceFiles(projectPath) {
        const files = [];
        const extensions = ['.js', '.ts', '.py', '.java', '.php', '.cs', '.go', '.rb', '.cpp', '.c'];
        const walkDir = async (dir) => {
            const entries = await fs.readdir(dir, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name);
                if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
                    await walkDir(fullPath);
                }
                else if (entry.isFile() && extensions.some(ext => entry.name.endsWith(ext))) {
                    files.push(fullPath);
                }
            }
        };
        await walkDir(projectPath);
        return files;
    }
    findMatches(content, pattern) {
        const matches = [];
        const regex = typeof pattern === 'string' ? new RegExp(pattern, 'gi') : pattern;
        let match;
        while ((match = regex.exec(content)) !== null) {
            matches.push({
                index: match.index,
                match: match[0]
            });
        }
        return matches;
    }
    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }
    getCodeContext(lines, lineNumber, contextSize) {
        const start = Math.max(0, lineNumber - contextSize - 1);
        const end = Math.min(lines.length, lineNumber + contextSize);
        return lines.slice(start, end);
    }
    mapNpmSeverity(severity) {
        const mapping = {
            'critical': 'critical',
            'high': 'high',
            'moderate': 'medium',
            'low': 'low',
            'info': 'info'
        };
        return mapping[severity] || 'medium';
    }
    mapCvssSeverity(score) {
        if (score >= 9.0)
            return 'critical';
        if (score >= 7.0)
            return 'high';
        if (score >= 4.0)
            return 'medium';
        if (score >= 0.1)
            return 'low';
        return 'info';
    }
    /**
     * Arrêt de l'analyseur
     */
    async shutdown() {
        this.logger.info('🛑 Arrêt de l\'Analyseur Statique...');
        this.isInitialized = false;
        this.logger.info('✅ Analyseur Statique arrêté');
    }
}
exports.StaticAnalyzer = StaticAnalyzer;
//# sourceMappingURL=StaticAnalyzer.js.map