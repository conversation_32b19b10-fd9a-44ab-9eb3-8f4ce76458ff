import { Logger } from 'winston';
import { ScannerConfig, SecurityScanRequest, SecurityScanResult } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Scanner de Vulnérabilités
 *
 * Orchestrateur principal pour tous les types de scans de sécurité
 */
export declare class VulnerabilityScanner {
    private scanners;
    private logger;
    private memory;
    private staticAnalyzer;
    private dynamicAnalyzer;
    private containerScanner;
    private infrastructureScanner;
    private networkScanner;
    private webScanner;
    private apiScanner;
    private isInitialized;
    constructor(scanners: ScannerConfig[], logger: Logger, memory: WeaviateMemory);
    /**
     * Initialise le scanner de vulnérabilités
     */
    initialize(): Promise<void>;
    /**
     * Effectue un scan de sécurité
     */
    scan(request: SecurityScanRequest): Promise<SecurityScanResult>;
    /**
     * Scan de conteneur
     */
    scanContainer(request: SecurityScanRequest): Promise<SecurityScanResult>;
    /**
     * Scan d'infrastructure
     */
    scanInfrastructure(request: SecurityScanRequest): Promise<SecurityScanResult>;
    /**
     * Scan réseau
     */
    scanNetwork(request: SecurityScanRequest): Promise<SecurityScanResult>;
    /**
     * Scan web
     */
    scanWeb(request: SecurityScanRequest): Promise<SecurityScanResult>;
    /**
     * Scan API
     */
    scanAPI(request: SecurityScanRequest): Promise<SecurityScanResult>;
    /**
     * Déduplique les vulnérabilités
     */
    private deduplicateVulnerabilities;
    /**
     * Calcule le résumé des vulnérabilités
     */
    private calculateSummary;
    /**
     * Génère des recommandations de sécurité
     */
    private generateRecommendations;
    private getCategoryRecommendationTitle;
    private getCategoryRecommendationDescription;
    private getCategoryImplementationSteps;
    private getSeverityWeight;
    /**
     * Arrêt du scanner
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=VulnerabilityScanner.d.ts.map