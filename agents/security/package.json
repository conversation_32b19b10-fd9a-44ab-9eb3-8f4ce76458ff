{"name": "@retreat-and-be/agent-security", "version": "1.0.0", "description": "Agent de sécurité avancé pour la protection complète du système distribué", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "demo": "ts-node demo/security-demo.ts", "validate": "./scripts/run-security-validation.sh", "clean": "rm -rf dist coverage", "docker:build": "docker build -t agent-security .", "docker:run": "docker run -p 3007:3007 agent-security"}, "keywords": ["security", "vulnerability-scanner", "compliance", "threat-intelligence", "access-control", "audit", "encryption", "policies", "cybersecurity"], "author": "Retreat And Be Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@types/fs-extra": "^11.0.4", "axios": "^1.6.2", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "eventemitter3": "^5.0.1", "express": "^4.18.2", "fs-extra": "^11.3.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "node-cron": "^3.0.3", "uuid": "^9.0.1", "weaviate-ts-client": "^1.5.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"], "coverageDirectory": "coverage"}}