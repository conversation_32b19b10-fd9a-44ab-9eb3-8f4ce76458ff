import { Logger } from 'winston';
import { EventEmitter } from 'events';
import { WeaviateMemory } from '../memory/WeaviateMemory';
import { KafkaCommunication } from '../communication/KafkaCommunication';

/**
 * Gestionnaire de Contrôle d'Accès Avancé
 * 
 * Implémente RBAC, ABAC, et contrôles d'accès dynamiques
 */
export class AccessControlManager extends EventEmitter {
  private logger: Logger;
  private memory: WeaviateMemory;
  private communication: KafkaCommunication;
  
  private roles: Map<string, Role> = new Map();
  private permissions: Map<string, Permission> = new Map();
  private policies: Map<string, AccessPolicy> = new Map();
  private sessions: Map<string, UserSession> = new Map();
  private accessLogs: AccessLog[] = [];
  
  private isInitialized: boolean = false;

  constructor(
    logger: Logger,
    memory: WeaviateMemory,
    communication: KafkaCommunication
  ) {
    super();
    this.logger = logger;
    this.memory = memory;
    this.communication = communication;
  }

  /**
   * Initialise le gestionnaire de contrôle d'accès
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('🔐 Initialisation du Access Control Manager...');

      // Chargement des rôles et permissions
      await this.loadRolesAndPermissions();

      // Chargement des politiques d'accès
      await this.loadAccessPolicies();

      // Configuration des règles par défaut
      await this.setupDefaultRules();

      // Démarrage du monitoring d'accès
      this.startAccessMonitoring();

      this.isInitialized = true;
      this.logger.info('✅ Access Control Manager initialisé');

    } catch (error) {
      this.logger.error('❌ Erreur lors de l\'initialisation du Access Control Manager:', error);
      throw error;
    }
  }

  /**
   * Vérifie l'autorisation d'accès
   */
  async checkAccess(
    userId: string,
    resource: string,
    action: string,
    context?: AccessContext
  ): Promise<AccessDecision> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`🔍 Vérification d'accès: ${userId} -> ${resource}:${action}`);

      // Récupération de la session utilisateur
      const session = this.sessions.get(userId);
      if (!session || this.isSessionExpired(session)) {
        return this.createAccessDecision(false, 'Session invalide ou expirée', startTime);
      }

      // Vérification RBAC (Role-Based Access Control)
      const rbacResult = await this.checkRBACAccess(session, resource, action);
      if (!rbacResult.allowed) {
        return this.createAccessDecision(false, rbacResult.reason, startTime);
      }

      // Vérification ABAC (Attribute-Based Access Control)
      const abacResult = await this.checkABACAccess(session, resource, action, context);
      if (!abacResult.allowed) {
        return this.createAccessDecision(false, abacResult.reason, startTime);
      }

      // Vérification des politiques dynamiques
      const policyResult = await this.checkDynamicPolicies(session, resource, action, context);
      if (!policyResult.allowed) {
        return this.createAccessDecision(false, policyResult.reason, startTime);
      }

      // Accès autorisé
      const decision = this.createAccessDecision(true, 'Accès autorisé', startTime);
      
      // Enregistrement de l'accès
      await this.logAccess(userId, resource, action, decision, context);
      
      return decision;

    } catch (error) {
      this.logger.error('❌ Erreur lors de la vérification d\'accès:', error);
      return this.createAccessDecision(false, 'Erreur interne', startTime);
    }
  }

  /**
   * Crée une session utilisateur
   */
  async createSession(
    userId: string,
    userAttributes: UserAttributes,
    authMethod: string
  ): Promise<UserSession> {
    const sessionId = this.generateSessionId();
    const expiresAt = new Date(Date.now() + (8 * 60 * 60 * 1000)); // 8 heures

    const session: UserSession = {
      id: sessionId,
      userId,
      userAttributes,
      authMethod,
      createdAt: new Date(),
      expiresAt,
      lastActivity: new Date(),
      isActive: true,
      ipAddress: userAttributes.ipAddress,
      userAgent: userAttributes.userAgent
    };

    this.sessions.set(userId, session);
    
    this.logger.info(`👤 Session créée pour l'utilisateur ${userId}`);
    
    // Notification de création de session
    this.emit('session-created', { userId, sessionId, authMethod });
    
    return session;
  }

  /**
   * Révoque une session utilisateur
   */
  async revokeSession(userId: string, reason: string = 'Manual revocation'): Promise<void> {
    const session = this.sessions.get(userId);
    if (session) {
      session.isActive = false;
      this.sessions.delete(userId);
      
      this.logger.info(`🚫 Session révoquée pour l'utilisateur ${userId}: ${reason}`);
      
      // Notification de révocation
      this.emit('session-revoked', { userId, reason });
    }
  }

  /**
   * Gère les rôles utilisateur
   */
  async assignRole(userId: string, roleId: string): Promise<void> {
    const role = this.roles.get(roleId);
    if (!role) {
      throw new Error(`Rôle non trouvé: ${roleId}`);
    }

    const session = this.sessions.get(userId);
    if (session) {
      if (!session.userAttributes.roles) {
        session.userAttributes.roles = [];
      }
      
      if (!session.userAttributes.roles.includes(roleId)) {
        session.userAttributes.roles.push(roleId);
        this.logger.info(`👥 Rôle ${roleId} assigné à l'utilisateur ${userId}`);
        
        // Notification d'assignation de rôle
        this.emit('role-assigned', { userId, roleId });
      }
    }
  }

  /**
   * Révoque un rôle utilisateur
   */
  async revokeRole(userId: string, roleId: string): Promise<void> {
    const session = this.sessions.get(userId);
    if (session && session.userAttributes.roles) {
      const index = session.userAttributes.roles.indexOf(roleId);
      if (index > -1) {
        session.userAttributes.roles.splice(index, 1);
        this.logger.info(`❌ Rôle ${roleId} révoqué pour l'utilisateur ${userId}`);
        
        // Notification de révocation de rôle
        this.emit('role-revoked', { userId, roleId });
      }
    }
  }

  /**
   * Vérifie l'accès basé sur les rôles (RBAC)
   */
  private async checkRBACAccess(
    session: UserSession,
    resource: string,
    action: string
  ): Promise<{ allowed: boolean; reason: string }> {
    if (!session.userAttributes.roles || session.userAttributes.roles.length === 0) {
      return { allowed: false, reason: 'Aucun rôle assigné' };
    }

    for (const roleId of session.userAttributes.roles) {
      const role = this.roles.get(roleId);
      if (role) {
        for (const permissionId of role.permissions) {
          const permission = this.permissions.get(permissionId);
          if (permission && this.matchesPermission(permission, resource, action)) {
            return { allowed: true, reason: `Autorisé par le rôle ${roleId}` };
          }
        }
      }
    }

    return { allowed: false, reason: 'Aucune permission trouvée' };
  }

  /**
   * Vérifie l'accès basé sur les attributs (ABAC)
   */
  private async checkABACAccess(
    session: UserSession,
    resource: string,
    action: string,
    context?: AccessContext
  ): Promise<{ allowed: boolean; reason: string }> {
    // Vérifications basées sur les attributs utilisateur
    const userAttrs = session.userAttributes;
    
    // Vérification de l'heure d'accès
    if (userAttrs.allowedHours) {
      const currentHour = new Date().getHours();
      if (currentHour < userAttrs.allowedHours.start || currentHour > userAttrs.allowedHours.end) {
        return { allowed: false, reason: 'Accès en dehors des heures autorisées' };
      }
    }

    // Vérification de l'adresse IP
    if (userAttrs.allowedIPs && userAttrs.allowedIPs.length > 0) {
      if (!userAttrs.allowedIPs.includes(session.ipAddress)) {
        return { allowed: false, reason: 'Adresse IP non autorisée' };
      }
    }

    // Vérification du département/organisation
    if (context?.resourceOwner && userAttrs.department) {
      if (context.resourceOwner !== userAttrs.department && !userAttrs.crossDepartmentAccess) {
        return { allowed: false, reason: 'Accès inter-départemental non autorisé' };
      }
    }

    return { allowed: true, reason: 'Attributs validés' };
  }

  /**
   * Vérifie les politiques dynamiques
   */
  private async checkDynamicPolicies(
    session: UserSession,
    resource: string,
    action: string,
    context?: AccessContext
  ): Promise<{ allowed: boolean; reason: string }> {
    for (const [policyId, policy] of this.policies) {
      if (policy.enabled && this.matchesPolicyConditions(policy, session, resource, action, context)) {
        if (policy.effect === 'deny') {
          return { allowed: false, reason: `Bloqué par la politique ${policyId}` };
        }
      }
    }

    return { allowed: true, reason: 'Aucune politique restrictive' };
  }

  /**
   * Vérifie si une permission correspond à la ressource et l'action
   */
  private matchesPermission(permission: Permission, resource: string, action: string): boolean {
    const resourceMatch = permission.resource === '*' || 
                         permission.resource === resource ||
                         resource.startsWith(permission.resource);
    
    const actionMatch = permission.actions.includes('*') ||
                       permission.actions.includes(action);
    
    return resourceMatch && actionMatch;
  }

  /**
   * Vérifie si les conditions d'une politique sont remplies
   */
  private matchesPolicyConditions(
    policy: AccessPolicy,
    session: UserSession,
    resource: string,
    action: string,
    context?: AccessContext
  ): boolean {
    // Implémentation des conditions de politique
    // Cette méthode peut être étendue avec des règles plus complexes
    return true;
  }

  /**
   * Crée une décision d'accès
   */
  private createAccessDecision(
    allowed: boolean,
    reason: string,
    startTime: number
  ): AccessDecision {
    return {
      allowed,
      reason,
      timestamp: new Date(),
      processingTime: Date.now() - startTime
    };
  }

  /**
   * Enregistre un accès
   */
  private async logAccess(
    userId: string,
    resource: string,
    action: string,
    decision: AccessDecision,
    context?: AccessContext
  ): Promise<void> {
    const accessLog: AccessLog = {
      id: this.generateLogId(),
      userId,
      resource,
      action,
      decision,
      context,
      timestamp: new Date()
    };

    this.accessLogs.push(accessLog);
    
    // Stockage en mémoire persistante
    await this.memory.storeAccessLog(accessLog);
    
    // Notification d'accès
    this.emit('access-logged', accessLog);
  }

  /**
   * Vérifie si une session est expirée
   */
  private isSessionExpired(session: UserSession): boolean {
    return !session.isActive || new Date() > session.expiresAt;
  }

  /**
   * Charge les rôles et permissions
   */
  private async loadRolesAndPermissions(): Promise<void> {
    // Chargement depuis la mémoire ou configuration
    await this.setupDefaultRolesAndPermissions();
  }

  /**
   * Configure les rôles et permissions par défaut
   */
  private async setupDefaultRolesAndPermissions(): Promise<void> {
    // Permissions de base
    this.permissions.set('read-all', {
      id: 'read-all',
      name: 'Lecture générale',
      resource: '*',
      actions: ['read', 'view'],
      conditions: []
    });

    this.permissions.set('admin-all', {
      id: 'admin-all',
      name: 'Administration complète',
      resource: '*',
      actions: ['*'],
      conditions: []
    });

    // Rôles de base
    this.roles.set('user', {
      id: 'user',
      name: 'Utilisateur',
      description: 'Utilisateur standard',
      permissions: ['read-all'],
      inherits: []
    });

    this.roles.set('admin', {
      id: 'admin',
      name: 'Administrateur',
      description: 'Administrateur système',
      permissions: ['admin-all'],
      inherits: ['user']
    });
  }

  /**
   * Charge les politiques d'accès
   */
  private async loadAccessPolicies(): Promise<void> {
    // Politique de sécurité par défaut
    this.policies.set('security-policy', {
      id: 'security-policy',
      name: 'Politique de sécurité',
      description: 'Politique de sécurité générale',
      effect: 'deny',
      enabled: true,
      conditions: [],
      priority: 100
    });
  }

  /**
   * Configure les règles par défaut
   */
  private async setupDefaultRules(): Promise<void> {
    this.logger.info('📋 Configuration des règles d\'accès par défaut...');
  }

  /**
   * Démarre le monitoring d'accès
   */
  private startAccessMonitoring(): void {
    // Nettoyage périodique des sessions expirées
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000); // Toutes les 5 minutes

    // Nettoyage des logs anciens
    setInterval(() => {
      this.cleanupOldLogs();
    }, 60 * 60 * 1000); // Toutes les heures
  }

  /**
   * Nettoie les sessions expirées
   */
  private cleanupExpiredSessions(): void {
    let cleanedCount = 0;
    
    for (const [userId, session] of this.sessions) {
      if (this.isSessionExpired(session)) {
        this.sessions.delete(userId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.info(`🧹 ${cleanedCount} sessions expirées nettoyées`);
    }
  }

  /**
   * Nettoie les anciens logs
   */
  private cleanupOldLogs(): void {
    const cutoffDate = new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)); // 30 jours
    const initialCount = this.accessLogs.length;
    
    this.accessLogs = this.accessLogs.filter(log => log.timestamp > cutoffDate);
    
    const cleanedCount = initialCount - this.accessLogs.length;
    if (cleanedCount > 0) {
      this.logger.info(`🧹 ${cleanedCount} logs anciens nettoyés`);
    }
  }

  /**
   * Génère un ID de session
   */
  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Génère un ID de log
   */
  private generateLogId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Arrêt du gestionnaire de contrôle d'accès
   */
  async shutdown(): Promise<void> {
    this.logger.info('🛑 Arrêt du Access Control Manager...');
    this.isInitialized = false;
  }
}

// Interfaces pour le contrôle d'accès
interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  inherits: string[];
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  actions: string[];
  conditions: string[];
}

interface AccessPolicy {
  id: string;
  name: string;
  description: string;
  effect: 'allow' | 'deny';
  enabled: boolean;
  conditions: string[];
  priority: number;
}

interface UserSession {
  id: string;
  userId: string;
  userAttributes: UserAttributes;
  authMethod: string;
  createdAt: Date;
  expiresAt: Date;
  lastActivity: Date;
  isActive: boolean;
  ipAddress: string;
  userAgent: string;
}

interface UserAttributes {
  roles?: string[];
  department?: string;
  allowedHours?: { start: number; end: number };
  allowedIPs?: string[];
  crossDepartmentAccess?: boolean;
  ipAddress: string;
  userAgent: string;
}

interface AccessContext {
  resourceOwner?: string;
  requestTime?: Date;
  clientInfo?: any;
  additionalData?: any;
}

interface AccessDecision {
  allowed: boolean;
  reason: string;
  timestamp: Date;
  processingTime: number;
}

interface AccessLog {
  id: string;
  userId: string;
  resource: string;
  action: string;
  decision: AccessDecision;
  context?: AccessContext;
  timestamp: Date;
}
