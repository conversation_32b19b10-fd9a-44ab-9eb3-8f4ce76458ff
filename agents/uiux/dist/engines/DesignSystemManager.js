"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DesignSystemManager = void 0;
/**
 * Gestionnaire de Design System Adaptatif
 *
 * Crée des design systems personnalisés basés sur :
 * - Les personas utilisateur
 * - Les tendances de l'industrie
 * - Les besoins d'accessibilité
 * - Les objectifs de conversion
 */
class DesignSystemManager {
    constructor(logger, memory) {
        this.logger = logger;
        this.memory = memory;
    }
    /**
     * Crée un design system adaptatif complet
     */
    async createAdaptiveSystem(requirements, personas, intelligence) {
        this.logger.info('Création du design system adaptatif');
        try {
            // Générer le système de couleurs optimisé
            const colorSystem = await this.generateColorSystem(requirements, personas, intelligence);
            // Créer le système typographique
            const typography = await this.createTypographySystem(requirements, personas, intelligence);
            // Définir le système d'espacement
            const spacing = await this.createSpacingSystem(personas);
            // Créer les composants UI optimisés
            const components = await this.createOptimizedComponents(personas, intelligence);
            // Définir le système de mouvement
            const motion = await this.createMotionSystem(requirements, personas);
            // Générer les tokens de design
            const tokens = await this.generateDesignTokens(colorSystem, typography, spacing);
            // Créer le guide d'implémentation
            const implementationGuide = await this.createDeveloperGuide();
            const designSystem = {
                colorSystem,
                typography,
                spacing,
                components,
                motion,
                tokens,
                implementationGuide
            };
            // Sauvegarder en mémoire
            await this.memory.storeDesignSystem(requirements.brand.name, designSystem);
            this.logger.info('Design system adaptatif créé avec succès');
            return designSystem;
        }
        catch (error) {
            this.logger.error('Erreur lors de la création du design system', { error: error.message });
            throw error;
        }
    }
    /**
     * Crée une bibliothèque de composants
     */
    async createComponentLibrary(wireframes, designSystem) {
        this.logger.info('Création de la bibliothèque de composants');
        const componentLibrary = {
            // Composants de base
            buttons: await this.createButtonComponents(designSystem),
            forms: await this.createFormComponents(designSystem),
            navigation: await this.createNavigationComponents(designSystem),
            cards: await this.createCardComponents(designSystem),
            modals: await this.createModalComponents(designSystem),
            // Composants spécialisés pour les retraites
            retreatCard: await this.createRetreatCardComponent(designSystem),
            bookingForm: await this.createBookingFormComponent(designSystem),
            filterPanel: await this.createFilterPanelComponent(designSystem),
            reviewComponent: await this.createReviewComponent(designSystem),
            partnerProfile: await this.createPartnerProfileComponent(designSystem),
            // Composants de conversion
            ctaButtons: await this.createCTAComponents(designSystem),
            trustSignals: await this.createTrustSignalComponents(designSystem),
            socialProof: await this.createSocialProofComponents(designSystem),
            pricingCards: await this.createPricingCardComponents(designSystem),
            // Documentation des composants
            documentation: await this.createComponentDocumentation(),
            storybook: await this.createStorybookConfig(),
            figmaTokens: await this.createFigmaTokens(designSystem)
        };
        return componentLibrary;
    }
    /**
     * Crée un guide d'implémentation pour les développeurs
     */
    async createImplementationGuide(designSystem, wireframes) {
        return `
# Guide d'Implémentation - Design System Retreat And Be

## Vue d'ensemble
Ce design system a été généré automatiquement par l'Agent UI/UX basé sur une recherche utilisateur approfondie et l'analyse des meilleures pratiques de l'industrie.

## Tokens de Design

### Couleurs
\`\`\`css
:root {
  /* Couleurs primaires */
  --color-primary-50: ${designSystem.colorSystem.primary[0]};
  --color-primary-500: ${designSystem.colorSystem.primary[4]};
  --color-primary-900: ${designSystem.colorSystem.primary[8]};
  
  /* Couleurs sémantiques */
  --color-success: ${designSystem.colorSystem.semantic.success};
  --color-warning: ${designSystem.colorSystem.semantic.warning};
  --color-error: ${designSystem.colorSystem.semantic.error};
}
\`\`\`

### Typographie
\`\`\`css
:root {
  --font-heading: ${designSystem.typography.fontPairings.heading};
  --font-body: ${designSystem.typography.fontPairings.body};
  
  /* Échelle typographique */
  --text-xs: ${designSystem.typography.responsiveScale.xs};
  --text-sm: ${designSystem.typography.responsiveScale.sm};
  --text-base: ${designSystem.typography.responsiveScale.base};
  --text-lg: ${designSystem.typography.responsiveScale.lg};
  --text-xl: ${designSystem.typography.responsiveScale.xl};
}
\`\`\`

### Espacement
\`\`\`css
:root {
  --spacing-unit: ${designSystem.spacing.baseUnit}px;
  --spacing-xs: calc(var(--spacing-unit) * 0.5);
  --spacing-sm: var(--spacing-unit);
  --spacing-md: calc(var(--spacing-unit) * 2);
  --spacing-lg: calc(var(--spacing-unit) * 3);
  --spacing-xl: calc(var(--spacing-unit) * 4);
}
\`\`\`

## Composants Principaux

### Boutons CTA
Les boutons d'appel à l'action sont optimisés pour maximiser les conversions :
- Utiliser la couleur primaire pour les actions principales
- Taille minimum de 44px pour l'accessibilité mobile
- États hover et focus clairement définis

### Formulaires
Les formulaires suivent les meilleures pratiques UX :
- Validation en temps réel
- Messages d'erreur clairs
- Progression visible pour les formulaires multi-étapes

### Navigation
La navigation est conçue pour être intuitive :
- Menu hamburger sur mobile
- Breadcrumbs pour l'orientation
- Search prominente

## Accessibilité
Ce design system respecte les guidelines WCAG 2.1 AA :
- Contraste minimum de 4.5:1 pour le texte normal
- Contraste minimum de 3:1 pour le texte large
- Support complet du clavier
- Textes alternatifs pour toutes les images

## Responsive Design
Le système utilise une approche mobile-first :
- Breakpoints : 320px, 768px, 1024px, 1440px
- Grille flexible basée sur CSS Grid
- Images responsives avec srcset

## Performance
Optimisations intégrées :
- Lazy loading des images
- Compression des assets
- Critical CSS inline
- Préchargement des ressources critiques

## Tests
Stratégie de test recommandée :
- Tests d'accessibilité automatisés
- Tests de performance Lighthouse
- Tests utilisateur A/B sur les éléments de conversion
- Tests cross-browser sur les navigateurs principaux
`;
    }
    // Méthodes privées pour la génération des systèmes
    async generateColorSystem(requirements, personas, intelligence) {
        // Générer une palette de couleurs optimisée
        const brandColors = requirements.brand.colors || [];
        const industryTrends = intelligence.industryTrends.colorTrends;
        return {
            primary: this.generateAccessiblePrimaryPalette(brandColors, personas),
            secondary: this.generateSecondaryPalette(industryTrends),
            semantic: {
                success: '#10B981',
                warning: '#F59E0B',
                error: '#EF4444',
                info: '#3B82F6'
            },
            neutral: this.generateNeutralPalette(),
            darkMode: this.generateDarkModeVariant(personas)
        };
    }
    async createTypographySystem(requirements, personas, intelligence) {
        // Créer un système typographique optimisé
        return {
            fontPairings: this.selectOptimalFontPairings(requirements.brand, personas),
            responsiveScale: this.createResponsiveTypeScale(personas),
            hierarchy: this.defineTypographicHierarchy(requirements.contentTypes),
            accessibility: this.ensureTypographicAccessibility(personas)
        };
    }
    async createSpacingSystem(personas) {
        // Créer un système d'espacement harmonieux
        const baseUnit = this.calculateOptimalBaseUnit(personas);
        return {
            baseUnit,
            scale: this.createHarmoniousSpacingScale(),
            responsive: this.createResponsiveSpacing(personas),
            density: this.adaptDensityToAudience(personas)
        };
    }
    async createOptimizedComponents(personas, intelligence) {
        // Créer des composants UI optimisés
        return {
            buttons: this.designConversionOptimizedButtons(personas, intelligence.cta_trends),
            forms: this.designAccessibleForms(personas, intelligence.form_trends),
            navigation: this.designIntuitiveNavigation(personas),
            cards: this.designEngagingCards(),
            modals: this.designNonIntrusiveModals(personas)
        };
    }
    async createMotionSystem(requirements, personas) {
        // Créer un système d'animations et micro-interactions
        return {
            durations: this.defineAccessibleAnimationDurations(personas),
            easings: this.selectPersonalityEasings(requirements.brand.personality),
            choreography: this.createMeaningfulMotionChoreography(personas),
            reducedMotion: this.createReducedMotionAlternatives(personas)
        };
    }
    async generateDesignTokens(colorSystem, typography, spacing) {
        // Générer les tokens de design pour les développeurs
        return {
            colors: colorSystem,
            typography,
            spacing,
            shadows: this.generateShadowTokens(),
            borders: this.generateBorderTokens(),
            animations: this.generateAnimationTokens()
        };
    }
    // Méthodes utilitaires privées
    generateAccessiblePrimaryPalette(brandColors, personas) {
        // Générer une palette primaire accessible
        return [
            '#F0F9FF', '#E0F2FE', '#BAE6FD', '#7DD3FC', '#38BDF8',
            '#0EA5E9', '#0284C7', '#0369A1', '#075985', '#0C4A6E'
        ];
    }
    generateSecondaryPalette(trends) {
        // Générer une palette secondaire basée sur les tendances
        return [
            '#FEF3C7', '#FDE68A', '#FCD34D', '#FBBF24', '#F59E0B',
            '#D97706', '#B45309', '#92400E', '#78350F', '#451A03'
        ];
    }
    generateNeutralPalette() {
        // Générer une palette neutre
        return [
            '#FFFFFF', '#F9FAFB', '#F3F4F6', '#E5E7EB', '#D1D5DB',
            '#9CA3AF', '#6B7280', '#4B5563', '#374151', '#111827'
        ];
    }
    generateDarkModeVariant(personas) {
        // Générer les variantes dark mode
        return {
            background: '#0F172A',
            surface: '#1E293B',
            text: '#F1F5F9',
            textSecondary: '#CBD5E1'
        };
    }
    selectOptimalFontPairings(brand, personas) {
        // Sélectionner les pairings de polices optimaux
        return {
            heading: 'Inter, system-ui, sans-serif',
            body: 'Inter, system-ui, sans-serif'
        };
    }
    createResponsiveTypeScale(personas) {
        // Créer une échelle typographique responsive
        return {
            xs: '0.75rem',
            sm: '0.875rem',
            base: '1rem',
            lg: '1.125rem',
            xl: '1.25rem',
            '2xl': '1.5rem',
            '3xl': '1.875rem',
            '4xl': '2.25rem'
        };
    }
    defineTypographicHierarchy(contentTypes) {
        // Définir la hiérarchie typographique
        return {
            h1: { size: '2.25rem', weight: '700', lineHeight: '1.2' },
            h2: { size: '1.875rem', weight: '600', lineHeight: '1.3' },
            h3: { size: '1.5rem', weight: '600', lineHeight: '1.4' },
            body: { size: '1rem', weight: '400', lineHeight: '1.6' },
            caption: { size: '0.875rem', weight: '400', lineHeight: '1.5' }
        };
    }
    ensureTypographicAccessibility(personas) {
        // Assurer l'accessibilité typographique
        return {
            minFontSize: '16px',
            lineHeightMin: 1.5,
            contrastRatio: 4.5,
            dyslexiaFriendly: true
        };
    }
    calculateOptimalBaseUnit(personas) {
        // Calculer l'unité de base optimale pour l'espacement
        return 8; // 8px base unit for consistent spacing
    }
    createHarmoniousSpacingScale() {
        // Créer une échelle d'espacement harmonieuse
        return [0, 4, 8, 12, 16, 24, 32, 48, 64, 96, 128];
    }
    createResponsiveSpacing(personas) {
        // Créer un espacement responsive
        return {
            mobile: 16,
            tablet: 24,
            desktop: 32
        };
    }
    adaptDensityToAudience(personas) {
        // Adapter la densité à l'audience
        return 'comfortable';
    }
    designConversionOptimizedButtons(personas, ctaTrends) {
        // Designer des boutons optimisés pour la conversion
        return {
            primary: {
                backgroundColor: 'var(--color-primary-500)',
                color: 'white',
                padding: '12px 24px',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: '600'
            }
        };
    }
    designAccessibleForms(personas, formTrends) {
        // Designer des formulaires accessibles
        return {
            input: {
                padding: '12px 16px',
                borderRadius: '8px',
                border: '1px solid var(--color-neutral-300)',
                fontSize: '16px'
            }
        };
    }
    designIntuitiveNavigation(personas) {
        // Designer une navigation intuitive
        return {
            header: {
                height: '64px',
                backgroundColor: 'white',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }
        };
    }
    designEngagingCards() {
        // Designer des cartes engageantes
        return {
            card: {
                borderRadius: '12px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                padding: '24px'
            }
        };
    }
    designNonIntrusiveModals(personas) {
        // Designer des modales non intrusives
        return {
            modal: {
                borderRadius: '16px',
                maxWidth: '500px',
                padding: '32px'
            }
        };
    }
    defineAccessibleAnimationDurations(personas) {
        // Définir des durées d'animation accessibles
        return {
            fast: 150,
            normal: 300,
            slow: 500
        };
    }
    selectPersonalityEasings(personality) {
        // Sélectionner des easings selon la personnalité de marque
        return {
            default: 'cubic-bezier(0.4, 0, 0.2, 1)',
            bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
        };
    }
    createMeaningfulMotionChoreography(personas) {
        // Créer une chorégraphie de mouvement significative
        return {
            stagger: 100,
            cascade: 'top-to-bottom'
        };
    }
    createReducedMotionAlternatives(personas) {
        // Créer des alternatives pour reduced motion
        return {
            respectPrefersReducedMotion: true,
            fallbackToFade: true
        };
    }
    async createDeveloperGuide() {
        // Créer un guide pour les développeurs
        return 'Guide d\'implémentation détaillé pour les développeurs';
    }
    // Méthodes pour les composants spécialisés
    async createButtonComponents(designSystem) {
        return { /* Composants boutons */};
    }
    async createFormComponents(designSystem) {
        return { /* Composants formulaires */};
    }
    async createNavigationComponents(designSystem) {
        return { /* Composants navigation */};
    }
    async createCardComponents(designSystem) {
        return { /* Composants cartes */};
    }
    async createModalComponents(designSystem) {
        return { /* Composants modales */};
    }
    async createRetreatCardComponent(designSystem) {
        return { /* Composant carte retraite */};
    }
    async createBookingFormComponent(designSystem) {
        return { /* Composant formulaire de réservation */};
    }
    async createFilterPanelComponent(designSystem) {
        return { /* Composant panneau de filtres */};
    }
    async createReviewComponent(designSystem) {
        return { /* Composant avis */};
    }
    async createPartnerProfileComponent(designSystem) {
        return { /* Composant profil partenaire */};
    }
    async createCTAComponents(designSystem) {
        return { /* Composants CTA */};
    }
    async createTrustSignalComponents(designSystem) {
        return { /* Composants signaux de confiance */};
    }
    async createSocialProofComponents(designSystem) {
        return { /* Composants preuve sociale */};
    }
    async createPricingCardComponents(designSystem) {
        return { /* Composants cartes de prix */};
    }
    async createComponentDocumentation() {
        return { /* Documentation des composants */};
    }
    async createStorybookConfig() {
        return { /* Configuration Storybook */};
    }
    async createFigmaTokens(designSystem) {
        return { /* Tokens Figma */};
    }
    generateShadowTokens() {
        return {
            sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
        };
    }
    generateBorderTokens() {
        return {
            width: {
                thin: '1px',
                medium: '2px',
                thick: '4px'
            },
            radius: {
                sm: '4px',
                md: '8px',
                lg: '12px',
                xl: '16px',
                full: '9999px'
            }
        };
    }
    generateAnimationTokens() {
        return {
            duration: {
                fast: '150ms',
                normal: '300ms',
                slow: '500ms'
            },
            easing: {
                linear: 'linear',
                ease: 'ease',
                easeIn: 'ease-in',
                easeOut: 'ease-out',
                easeInOut: 'ease-in-out'
            }
        };
    }
}
exports.DesignSystemManager = DesignSystemManager;
//# sourceMappingURL=DesignSystemManager.js.map