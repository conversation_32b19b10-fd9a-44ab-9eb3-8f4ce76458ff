import { Logger } from 'winston';
import { DesignRequirements, UserResearch, DesignIntelligence, Persona } from '../types';
import { WeaviateMemory } from '../memory/WeaviateMemory';
/**
 * Engine de recherche utilisateur automatisée
 *
 * Collecte des données utilisateur depuis multiple sources :
 * - Analytics web
 * - Recherche concurrentielle
 * - Tendances industrie
 * - Données démographiques
 */
export declare class UserResearchEngine {
    private logger;
    private memory;
    private browser;
    constructor(logger: Logger, memory: WeaviateMemory);
    /**
     * Conduit une recherche utilisateur complète
     */
    conductResearch(requirements: DesignRequirements): Promise<UserResearch>;
    /**
     * Collecte d'intelligence design et analyse concurrentielle
     */
    gatherDesignIntelligence(requirements: DesignRequirements): Promise<DesignIntelligence>;
    /**
     * Génération de personas basée sur des données réelles
     */
    generatePersonas(userResearch: UserResearch, designIntelligence: DesignIntelligence): Promise<Persona[]>;
    private analyzeTargetDemographics;
    private analyzeBehaviorPatterns;
    private gatherCompetitorUserFeedback;
    private collectIndustryMetrics;
    private assessAccessibilityNeeds;
    private analyzeDeviceUsage;
    private analyzePsychographics;
    private identifyCommonPainPoints;
    private extractUserMotivations;
    private scrapeCompetitorReviews;
    private analyzeIndustryDesignTrends;
    private performCompetitorDesignAnalysis;
    private collectModernDesignTrends;
    private analyzeCTATrends;
    private analyzeFormTrends;
    private buildPersonasPrompt;
    private generateWithAI;
    private parsePersonasResponse;
    private validateAndEnrichPersonas;
}
//# sourceMappingURL=UserResearchEngine.d.ts.map