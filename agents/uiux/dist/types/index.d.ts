/**
 * Types pour l'Agent UI/UX Design Thinking
 */
export interface DesignRequirements {
    industry: string;
    targetAudience: string[];
    brand: {
        name: string;
        personality: string[];
        values: string[];
        colors?: string[];
    };
    competitors: string[];
    similarProducts: string[];
    contentTypes: string[];
    deviceTargets: ('desktop' | 'mobile' | 'tablet')[];
    accessibilityLevel: 'AA' | 'AAA';
    conversionGoals: string[];
}
export interface UserResearch {
    demographics: {
        ageRange: string;
        gender: string[];
        location: string[];
        income: string;
        education: string;
    };
    behaviorPatterns: {
        deviceUsage: Record<string, number>;
        timeSpent: Record<string, number>;
        navigationPatterns: string[];
        painPoints: string[];
    };
    competitorUserFeedback: {
        positive: string[];
        negative: string[];
        suggestions: string[];
    };
    industryMetrics: {
        averageSessionTime: number;
        bounceRate: number;
        conversionRate: number;
        topPerformingFeatures: string[];
    };
    accessibilityNeeds: {
        visualImpairments: number;
        motorImpairments: number;
        cognitiveImpairments: number;
        hearingImpairments: number;
    };
    deviceUsagePatterns: Record<string, number>;
    psychographics: {
        interests: string[];
        values: string[];
        lifestyle: string[];
        motivations: string[];
    };
    painPoints: string[];
    motivations: string[];
}
export interface Persona {
    id: string;
    name: string;
    age: number;
    profession: string;
    location: string;
    goals: {
        primary: string[];
        secondary: string[];
    };
    frustrations: string[];
    preferences: {
        interface: string[];
        communication: string[];
        devices: string[];
    };
    accessibilityNeeds: string[];
    technicalProficiency: 'low' | 'medium' | 'high';
    motivations: string[];
    buyingTriggers: string[];
    objections: string[];
    communicationChannels: string[];
}
export interface DesignIntelligence {
    industryTrends: {
        colorTrends: string[];
        typographyTrends: string[];
        layoutTrends: string[];
        interactionTrends: string[];
    };
    competitorAnalysis: {
        strengths: string[];
        weaknesses: string[];
        opportunities: string[];
        designPatterns: string[];
    };
    modernTrends: string[];
    cta_trends: string[];
    form_trends: string[];
}
export interface AdaptiveDesignSystem {
    colorSystem: {
        primary: string[];
        secondary: string[];
        semantic: Record<string, string>;
        neutral: string[];
        darkMode: Record<string, string>;
    };
    typography: {
        fontPairings: {
            heading: string;
            body: string;
        };
        responsiveScale: Record<string, string>;
        hierarchy: Record<string, any>;
        accessibility: Record<string, any>;
    };
    spacing: {
        baseUnit: number;
        scale: number[];
        responsive: Record<string, number>;
        density: 'compact' | 'comfortable' | 'spacious';
    };
    components: Record<string, any>;
    motion: {
        durations: Record<string, number>;
        easings: Record<string, string>;
        choreography: Record<string, any>;
        reducedMotion: Record<string, any>;
    };
    tokens: Record<string, any>;
    implementationGuide: string;
}
export interface ConversionWireframes {
    userFlows: Record<string, any>;
    landingPages: Record<string, any>;
    signupFlow: Record<string, any>;
    onboarding: Record<string, any>;
    dashboard: Record<string, any>;
    mobileExperience: Record<string, any>;
    accessibilityFeatures: Record<string, any>;
    conversionFunnels: Record<string, any>;
    trustSignals: Record<string, any>;
    socialProof: Record<string, any>;
}
export interface UsabilityResults {
    heatmaps: Record<string, any>;
    clickThroughRates: Record<string, number>;
    attentionData: Record<string, any>;
    conversionRates: Record<string, number>;
    dropOffPoints: string[];
    errorRates: Record<string, number>;
    completionRates: Record<string, number>;
    trustMetrics: Record<string, number>;
    navigationMetrics: Record<string, any>;
    errorPatterns: string[];
    confusionPoints: string[];
}
export interface ConversionOptimizations {
    ctaOptimizations: Record<string, any>;
    formOptimizations: Record<string, any>;
    trustOptimizations: Record<string, any>;
    frictionReduction: Record<string, any>;
}
export interface ComprehensiveUXDesign {
    userResearch: UserResearch;
    designIntelligence: DesignIntelligence;
    personas: Persona[];
    designSystem: AdaptiveDesignSystem;
    wireframes: ConversionWireframes;
    usabilityResults: UsabilityResults;
    conversionOptimizations: ConversionOptimizations;
    componentLibrary: Record<string, any>;
    accessibilityCompliance: Record<string, any>;
    implementationGuide: string;
    testingStrategy: Record<string, any>;
}
export interface AgentMessage {
    id: string;
    type: 'request' | 'response' | 'notification';
    from: string;
    to: string;
    payload: any;
    timestamp: Date;
    correlationId?: string;
}
export interface AgentConfig {
    id: string;
    name: string;
    type: 'uiux';
    version: string;
    capabilities: string[];
    endpoints: Record<string, string>;
    memory: {
        store: string;
        collections: string[];
    };
    communication: {
        kafka: {
            topics: string[];
            groupId: string;
        };
        redis: {
            channels: string[];
        };
    };
}
//# sourceMappingURL=index.d.ts.map