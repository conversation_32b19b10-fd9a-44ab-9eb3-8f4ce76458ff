import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createLogger, format, transports } from 'winston';
import dotenv from 'dotenv';
import { UIUXAgent } from './core/UIUXAgent';
import { WeaviateMemory } from './memory/WeaviateMemory';
import { KafkaCommunication } from './communication/KafkaCommunication';
import { AgentConfig } from './types';

// Charger les variables d'environnement
dotenv.config();

// Configuration du logger
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { service: 'agent-uiux' },
  transports: [
    new transports.File({ filename: 'logs/error.log', level: 'error' }),
    new transports.File({ filename: 'logs/combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ]
});

// Configuration de l'agent
const agentConfig: AgentConfig = {
  id: process.env.AGENT_ID || 'agent-uiux-001',
  name: 'Agent UI/UX Design Thinking',
  type: 'uiux',
  version: '1.0.0',
  capabilities: [
    'user_research',
    'persona_generation',
    'design_system_creation',
    'wireframe_generation',
    'conversion_optimization',
    'accessibility_validation',
    'usability_testing',
    'component_library_creation'
  ],
  endpoints: {
    health: '/health',
    design: '/api/design',
    validate: '/api/validate',
    research: '/api/research',
    personas: '/api/personas',
    designSystem: '/api/design-system',
    wireframes: '/api/wireframes',
    components: '/api/components'
  },
  memory: {
    store: 'weaviate',
    collections: [
      'UserResearch',
      'Persona',
      'DesignSystem',
      'Wireframe',
      'DesignPattern',
      'ComprehensiveDesign'
    ]
  },
  communication: {
    kafka: {
      topics: [
        'agent.uiux.design.complete',
        'agent.uiux.validation.request',
        'agent.uiux.design.feedback',
        'agent.uiux.components.ready'
      ],
      groupId: 'agent-uiux-group'
    },
    redis: {
      channels: [
        'uiux:notifications',
        'uiux:requests'
      ]
    }
  }
};

// Initialisation de l'application
async function initializeAgent(): Promise<void> {
  try {
    logger.info('Initialisation de l\'Agent UI/UX Design Thinking', { config: agentConfig });

    // Initialiser les systèmes de base
    const memory = new WeaviateMemory(logger, process.env.WEAVIATE_URL);
    const communication = new KafkaCommunication(logger, agentConfig.id, process.env.KAFKA_BROKERS);

    // Initialiser l'agent principal
    const uiuxAgent = new UIUXAgent(agentConfig, logger, memory, communication);

    // Initialiser le serveur Express
    const app = express();
    const port = process.env.PORT || 3005;

    // Middleware de sécurité et CORS
    app.use(helmet());
    app.use(cors());
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true }));

    // Middleware de logging
    app.use((req, res, next) => {
      logger.info('Requête reçue', {
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });

    // Routes de santé
    app.get('/health', (req, res) => {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        agent: {
          id: agentConfig.id,
          name: agentConfig.name,
          version: agentConfig.version,
          uptime: process.uptime()
        },
        memory: {
          connected: true, // memory.isConnected()
          collections: agentConfig.memory.collections.length
        },
        communication: {
          kafka: communication.isHealthy(),
          stats: communication.getStats()
        },
        system: {
          nodeVersion: process.version,
          platform: process.platform,
          memory: process.memoryUsage(),
          cpu: process.cpuUsage()
        }
      };

      res.json(health);
    });

    app.get('/ready', (req, res) => {
      res.json({ status: 'ready', timestamp: new Date().toISOString() });
    });

    // Routes API principales

    // Création d'un design complet
    app.post('/api/design', async (req, res) => {
      try {
        const { requirements } = req.body;
        
        if (!requirements) {
          return res.status(400).json({ error: 'Requirements manquants' });
        }

        logger.info('Demande de création de design', { requirements });
        
        const design = await uiuxAgent.createComprehensiveDesign(requirements);
        
        res.json({
          success: true,
          design,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la création du design', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Validation d'implémentation
    app.post('/api/validate', async (req, res) => {
      try {
        const { code, design } = req.body;
        
        if (!code || !design) {
          return res.status(400).json({ error: 'Code et design requis' });
        }

        logger.info('Demande de validation d\'implémentation');
        
        const validation = await uiuxAgent.validateImplementation(code, design);
        
        res.json({
          success: true,
          validation,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la validation', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Recherche utilisateur
    app.post('/api/research', async (req, res) => {
      try {
        const { requirements } = req.body;
        
        if (!requirements) {
          return res.status(400).json({ error: 'Requirements manquants' });
        }

        logger.info('Demande de recherche utilisateur', { requirements });
        
        const research = await uiuxAgent.conductAutomatedUserResearch(requirements);
        
        res.json({
          success: true,
          research,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la recherche utilisateur', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Génération de personas
    app.post('/api/personas', async (req, res) => {
      try {
        const { userResearch, designIntelligence } = req.body;
        
        if (!userResearch || !designIntelligence) {
          return res.status(400).json({ error: 'UserResearch et designIntelligence requis' });
        }

        logger.info('Demande de génération de personas');
        
        const personas = await uiuxAgent.generateDataDrivenPersonas(userResearch, designIntelligence);
        
        res.json({
          success: true,
          personas,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la génération de personas', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Création de design system
    app.post('/api/design-system', async (req, res) => {
      try {
        const { requirements, personas, intelligence } = req.body;
        
        if (!requirements || !personas || !intelligence) {
          return res.status(400).json({ error: 'Requirements, personas et intelligence requis' });
        }

        logger.info('Demande de création de design system');
        
        const designSystem = await uiuxAgent.createAdaptiveDesignSystem(requirements, personas, intelligence);
        
        res.json({
          success: true,
          designSystem,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la création du design system', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Génération de wireframes
    app.post('/api/wireframes', async (req, res) => {
      try {
        const { personas, designSystem } = req.body;
        
        if (!personas || !designSystem) {
          return res.status(400).json({ error: 'Personas et designSystem requis' });
        }

        logger.info('Demande de génération de wireframes');
        
        const wireframes = await uiuxAgent.generateConversionOptimizedWireframes(personas, designSystem);
        
        res.json({
          success: true,
          wireframes,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la génération de wireframes', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Création de bibliothèque de composants
    app.post('/api/components', async (req, res) => {
      try {
        const { wireframes, designSystem } = req.body;
        
        if (!wireframes || !designSystem) {
          return res.status(400).json({ error: 'Wireframes et designSystem requis' });
        }

        logger.info('Demande de création de bibliothèque de composants');
        
        const componentLibrary = await uiuxAgent.createComponentLibrary(wireframes, designSystem);
        
        res.json({
          success: true,
          componentLibrary,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        logger.error('Erreur lors de la création de la bibliothèque', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    // Route d'information sur l'agent
    app.get('/api/info', (req, res) => {
      res.json({
        agent: agentConfig,
        status: 'running',
        timestamp: new Date().toISOString()
      });
    });

    // Middleware de gestion d'erreurs
    app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Erreur non gérée', { error: error.message, stack: error.stack });
      res.status(500).json({
        success: false,
        error: 'Erreur interne du serveur',
        timestamp: new Date().toISOString()
      });
    });

    // Route 404
    app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'Route non trouvée',
        timestamp: new Date().toISOString()
      });
    });

    // Démarrer le serveur
    app.listen(port, () => {
      logger.info(`Agent UI/UX démarré sur le port ${port}`, {
        agentId: agentConfig.id,
        version: agentConfig.version,
        capabilities: agentConfig.capabilities
      });
    });

    // Gestion des signaux de fermeture
    process.on('SIGTERM', async () => {
      logger.info('Signal SIGTERM reçu, fermeture gracieuse...');
      await communication.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('Signal SIGINT reçu, fermeture gracieuse...');
      await communication.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Erreur lors de l\'initialisation de l\'agent', { error: error.message });
    process.exit(1);
  }
}

// Démarrer l'agent
initializeAgent().catch((error) => {
  console.error('Erreur fatale lors du démarrage:', error);
  process.exit(1);
});
