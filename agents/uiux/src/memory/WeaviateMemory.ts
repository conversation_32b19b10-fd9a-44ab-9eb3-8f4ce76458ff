import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import { Logger } from 'winston';
import { 
  UserResearch, 
  Persona, 
  AdaptiveDesignSystem, 
  ConversionWireframes, 
  ComprehensiveUXDesign 
} from '../types';

/**
 * Système de Mémoire Vectorielle avec Weaviate
 * 
 * Stocke et récupère les données de design, recherche utilisateur,
 * personas et patterns de design pour l'apprentissage continu.
 */
export class WeaviateMemory {
  private client: WeaviateClient;
  private logger: Logger;
  private isConnected: boolean = false;

  constructor(logger: Logger, weaviateUrl: string = 'http://weaviate:8080') {
    this.logger = logger;
    this.client = weaviate.client({
      scheme: 'http',
      host: weaviateUrl.replace('http://', ''),
    });
    
    this.initializeSchema();
  }

  /**
   * Initialise le schéma Weaviate pour l'agent UI/UX
   */
  private async initializeSchema(): Promise<void> {
    try {
      await this.createUserResearchSchema();
      await this.createPersonaSchema();
      await this.createDesignSystemSchema();
      await this.createWireframeSchema();
      await this.createDesignPatternSchema();
      await this.createComprehensiveDesignSchema();
      
      this.isConnected = true;
      this.logger.info('Schéma Weaviate initialisé pour l\'agent UI/UX');
    } catch (error) {
      this.logger.error('Erreur lors de l\'initialisation du schéma Weaviate', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke une recherche utilisateur
   */
  async storeUserResearch(industry: string, research: UserResearch): Promise<string> {
    try {
      const result = await this.client
        .data
        .creator()
        .withClassName('UserResearch')
        .withProperties({
          industry,
          demographics: JSON.stringify(research.demographics),
          behaviorPatterns: JSON.stringify(research.behaviorPatterns),
          competitorUserFeedback: JSON.stringify(research.competitorUserFeedback),
          industryMetrics: JSON.stringify(research.industryMetrics),
          accessibilityNeeds: JSON.stringify(research.accessibilityNeeds),
          deviceUsagePatterns: JSON.stringify(research.deviceUsagePatterns),
          psychographics: JSON.stringify(research.psychographics),
          painPoints: JSON.stringify(research.painPoints),
          motivations: JSON.stringify(research.motivations),
          timestamp: new Date().toISOString()
        })
        .do();

      this.logger.info('Recherche utilisateur stockée', { id: result.id, industry });
      return result.id;
    } catch (error) {
      this.logger.error('Erreur lors du stockage de la recherche utilisateur', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke des personas
   */
  async storePersonas(personas: Persona[]): Promise<string[]> {
    try {
      const ids = [];
      
      for (const persona of personas) {
        const result = await this.client
          .data
          .creator()
          .withClassName('Persona')
          .withProperties({
            personaId: persona.id,
            name: persona.name,
            age: persona.age,
            profession: persona.profession,
            location: persona.location,
            goals: JSON.stringify(persona.goals),
            frustrations: JSON.stringify(persona.frustrations),
            preferences: JSON.stringify(persona.preferences),
            accessibilityNeeds: JSON.stringify(persona.accessibilityNeeds),
            technicalProficiency: persona.technicalProficiency,
            motivations: JSON.stringify(persona.motivations),
            buyingTriggers: JSON.stringify(persona.buyingTriggers),
            objections: JSON.stringify(persona.objections),
            communicationChannels: JSON.stringify(persona.communicationChannels),
            timestamp: new Date().toISOString()
          })
          .do();
        
        ids.push(result.id);
      }

      this.logger.info('Personas stockés', { count: personas.length, ids });
      return ids;
    } catch (error) {
      this.logger.error('Erreur lors du stockage des personas', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke un design system
   */
  async storeDesignSystem(brandName: string, designSystem: AdaptiveDesignSystem): Promise<string> {
    try {
      const result = await this.client
        .data
        .creator()
        .withClassName('DesignSystem')
        .withProperties({
          brandName,
          colorSystem: JSON.stringify(designSystem.colorSystem),
          typography: JSON.stringify(designSystem.typography),
          spacing: JSON.stringify(designSystem.spacing),
          components: JSON.stringify(designSystem.components),
          motion: JSON.stringify(designSystem.motion),
          tokens: JSON.stringify(designSystem.tokens),
          implementationGuide: designSystem.implementationGuide,
          timestamp: new Date().toISOString()
        })
        .do();

      this.logger.info('Design system stocké', { id: result.id, brandName });
      return result.id;
    } catch (error) {
      this.logger.error('Erreur lors du stockage du design system', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke des wireframes
   */
  async storeWireframes(type: string, wireframes: ConversionWireframes): Promise<string> {
    try {
      const result = await this.client
        .data
        .creator()
        .withClassName('Wireframe')
        .withProperties({
          type,
          userFlows: JSON.stringify(wireframes.userFlows),
          landingPages: JSON.stringify(wireframes.landingPages),
          signupFlow: JSON.stringify(wireframes.signupFlow),
          onboarding: JSON.stringify(wireframes.onboarding),
          dashboard: JSON.stringify(wireframes.dashboard),
          mobileExperience: JSON.stringify(wireframes.mobileExperience),
          accessibilityFeatures: JSON.stringify(wireframes.accessibilityFeatures),
          conversionFunnels: JSON.stringify(wireframes.conversionFunnels),
          trustSignals: JSON.stringify(wireframes.trustSignals),
          socialProof: JSON.stringify(wireframes.socialProof),
          timestamp: new Date().toISOString()
        })
        .do();

      this.logger.info('Wireframes stockés', { id: result.id, type });
      return result.id;
    } catch (error) {
      this.logger.error('Erreur lors du stockage des wireframes', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke un design complet
   */
  async storeDesign(brandName: string, design: ComprehensiveUXDesign): Promise<string> {
    try {
      const result = await this.client
        .data
        .creator()
        .withClassName('ComprehensiveDesign')
        .withProperties({
          brandName,
          userResearch: JSON.stringify(design.userResearch),
          designIntelligence: JSON.stringify(design.designIntelligence),
          personas: JSON.stringify(design.personas),
          designSystem: JSON.stringify(design.designSystem),
          wireframes: JSON.stringify(design.wireframes),
          usabilityResults: JSON.stringify(design.usabilityResults),
          conversionOptimizations: JSON.stringify(design.conversionOptimizations),
          componentLibrary: JSON.stringify(design.componentLibrary),
          accessibilityCompliance: JSON.stringify(design.accessibilityCompliance),
          implementationGuide: design.implementationGuide,
          testingStrategy: JSON.stringify(design.testingStrategy),
          timestamp: new Date().toISOString()
        })
        .do();

      this.logger.info('Design complet stocké', { id: result.id, brandName });
      return result.id;
    } catch (error) {
      this.logger.error('Erreur lors du stockage du design complet', { error: error.message });
      throw error;
    }
  }

  /**
   * Recherche des patterns de design similaires
   */
  async searchSimilarDesigns(query: string, limit: number = 10): Promise<any[]> {
    try {
      const result = await this.client
        .graphql
        .get()
        .withClassName('ComprehensiveDesign')
        .withFields('brandName userResearch designSystem wireframes timestamp')
        .withNearText({ concepts: [query] })
        .withLimit(limit)
        .do();

      return result.data.Get.ComprehensiveDesign || [];
    } catch (error) {
      this.logger.error('Erreur lors de la recherche de designs similaires', { error: error.message });
      throw error;
    }
  }

  /**
   * Recherche des personas similaires
   */
  async searchSimilarPersonas(characteristics: string[], limit: number = 5): Promise<Persona[]> {
    try {
      const result = await this.client
        .graphql
        .get()
        .withClassName('Persona')
        .withFields('personaId name age profession location goals frustrations preferences accessibilityNeeds technicalProficiency motivations buyingTriggers objections communicationChannels')
        .withNearText({ concepts: characteristics })
        .withLimit(limit)
        .do();

      const personas = result.data.Get.Persona || [];
      return personas.map(p => ({
        id: p.personaId,
        name: p.name,
        age: p.age,
        profession: p.profession,
        location: p.location,
        goals: JSON.parse(p.goals || '{}'),
        frustrations: JSON.parse(p.frustrations || '[]'),
        preferences: JSON.parse(p.preferences || '{}'),
        accessibilityNeeds: JSON.parse(p.accessibilityNeeds || '[]'),
        technicalProficiency: p.technicalProficiency,
        motivations: JSON.parse(p.motivations || '[]'),
        buyingTriggers: JSON.parse(p.buyingTriggers || '[]'),
        objections: JSON.parse(p.objections || '[]'),
        communicationChannels: JSON.parse(p.communicationChannels || '[]')
      }));
    } catch (error) {
      this.logger.error('Erreur lors de la recherche de personas similaires', { error: error.message });
      throw error;
    }
  }

  /**
   * Récupère les tendances de design par industrie
   */
  async getDesignTrendsByIndustry(industry: string): Promise<any[]> {
    try {
      const result = await this.client
        .graphql
        .get()
        .withClassName('UserResearch')
        .withFields('industry demographics behaviorPatterns industryMetrics timestamp')
        .withWhere({
          path: ['industry'],
          operator: 'Equal',
          valueText: industry
        })
        .withLimit(20)
        .do();

      return result.data.Get.UserResearch || [];
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des tendances', { error: error.message });
      throw error;
    }
  }

  /**
   * Stocke un pattern de design réutilisable
   */
  async storeDesignPattern(pattern: any): Promise<string> {
    try {
      const result = await this.client
        .data
        .creator()
        .withClassName('DesignPattern')
        .withProperties({
          name: pattern.name,
          category: pattern.category,
          description: pattern.description,
          usage: JSON.stringify(pattern.usage),
          code: pattern.code,
          accessibility: JSON.stringify(pattern.accessibility),
          performance: JSON.stringify(pattern.performance),
          conversionImpact: pattern.conversionImpact,
          tags: JSON.stringify(pattern.tags),
          timestamp: new Date().toISOString()
        })
        .do();

      this.logger.info('Pattern de design stocké', { id: result.id, name: pattern.name });
      return result.id;
    } catch (error) {
      this.logger.error('Erreur lors du stockage du pattern', { error: error.message });
      throw error;
    }
  }

  // Méthodes de création de schéma

  private async createUserResearchSchema(): Promise<void> {
    const schema = {
      class: 'UserResearch',
      description: 'Données de recherche utilisateur collectées automatiquement',
      properties: [
        { name: 'industry', dataType: ['text'], description: 'Industrie cible' },
        { name: 'demographics', dataType: ['text'], description: 'Données démographiques' },
        { name: 'behaviorPatterns', dataType: ['text'], description: 'Patterns de comportement' },
        { name: 'competitorUserFeedback', dataType: ['text'], description: 'Feedback utilisateurs concurrents' },
        { name: 'industryMetrics', dataType: ['text'], description: 'Métriques de l\'industrie' },
        { name: 'accessibilityNeeds', dataType: ['text'], description: 'Besoins d\'accessibilité' },
        { name: 'deviceUsagePatterns', dataType: ['text'], description: 'Patterns d\'usage des appareils' },
        { name: 'psychographics', dataType: ['text'], description: 'Données psychographiques' },
        { name: 'painPoints', dataType: ['text'], description: 'Points de douleur identifiés' },
        { name: 'motivations', dataType: ['text'], description: 'Motivations utilisateur' },
        { name: 'timestamp', dataType: ['date'], description: 'Date de création' }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  private async createPersonaSchema(): Promise<void> {
    const schema = {
      class: 'Persona',
      description: 'Personas générés basés sur la recherche utilisateur',
      properties: [
        { name: 'personaId', dataType: ['text'], description: 'ID unique du persona' },
        { name: 'name', dataType: ['text'], description: 'Nom du persona' },
        { name: 'age', dataType: ['int'], description: 'Âge du persona' },
        { name: 'profession', dataType: ['text'], description: 'Profession du persona' },
        { name: 'location', dataType: ['text'], description: 'Localisation du persona' },
        { name: 'goals', dataType: ['text'], description: 'Objectifs du persona' },
        { name: 'frustrations', dataType: ['text'], description: 'Frustrations du persona' },
        { name: 'preferences', dataType: ['text'], description: 'Préférences du persona' },
        { name: 'accessibilityNeeds', dataType: ['text'], description: 'Besoins d\'accessibilité' },
        { name: 'technicalProficiency', dataType: ['text'], description: 'Niveau technique' },
        { name: 'motivations', dataType: ['text'], description: 'Motivations' },
        { name: 'buyingTriggers', dataType: ['text'], description: 'Déclencheurs d\'achat' },
        { name: 'objections', dataType: ['text'], description: 'Objections potentielles' },
        { name: 'communicationChannels', dataType: ['text'], description: 'Canaux de communication' },
        { name: 'timestamp', dataType: ['date'], description: 'Date de création' }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  private async createDesignSystemSchema(): Promise<void> {
    const schema = {
      class: 'DesignSystem',
      description: 'Design systems adaptatifs générés',
      properties: [
        { name: 'brandName', dataType: ['text'], description: 'Nom de la marque' },
        { name: 'colorSystem', dataType: ['text'], description: 'Système de couleurs' },
        { name: 'typography', dataType: ['text'], description: 'Système typographique' },
        { name: 'spacing', dataType: ['text'], description: 'Système d\'espacement' },
        { name: 'components', dataType: ['text'], description: 'Composants UI' },
        { name: 'motion', dataType: ['text'], description: 'Système d\'animation' },
        { name: 'tokens', dataType: ['text'], description: 'Tokens de design' },
        { name: 'implementationGuide', dataType: ['text'], description: 'Guide d\'implémentation' },
        { name: 'timestamp', dataType: ['date'], description: 'Date de création' }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  private async createWireframeSchema(): Promise<void> {
    const schema = {
      class: 'Wireframe',
      description: 'Wireframes optimisés pour la conversion',
      properties: [
        { name: 'type', dataType: ['text'], description: 'Type de wireframe' },
        { name: 'userFlows', dataType: ['text'], description: 'Parcours utilisateur' },
        { name: 'landingPages', dataType: ['text'], description: 'Pages d\'atterrissage' },
        { name: 'signupFlow', dataType: ['text'], description: 'Parcours d\'inscription' },
        { name: 'onboarding', dataType: ['text'], description: 'Onboarding' },
        { name: 'dashboard', dataType: ['text'], description: 'Dashboard' },
        { name: 'mobileExperience', dataType: ['text'], description: 'Expérience mobile' },
        { name: 'accessibilityFeatures', dataType: ['text'], description: 'Fonctionnalités d\'accessibilité' },
        { name: 'conversionFunnels', dataType: ['text'], description: 'Funnels de conversion' },
        { name: 'trustSignals', dataType: ['text'], description: 'Signaux de confiance' },
        { name: 'socialProof', dataType: ['text'], description: 'Preuve sociale' },
        { name: 'timestamp', dataType: ['date'], description: 'Date de création' }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  private async createDesignPatternSchema(): Promise<void> {
    const schema = {
      class: 'DesignPattern',
      description: 'Patterns de design réutilisables',
      properties: [
        { name: 'name', dataType: ['text'], description: 'Nom du pattern' },
        { name: 'category', dataType: ['text'], description: 'Catégorie du pattern' },
        { name: 'description', dataType: ['text'], description: 'Description du pattern' },
        { name: 'usage', dataType: ['text'], description: 'Instructions d\'usage' },
        { name: 'code', dataType: ['text'], description: 'Code du pattern' },
        { name: 'accessibility', dataType: ['text'], description: 'Considérations d\'accessibilité' },
        { name: 'performance', dataType: ['text'], description: 'Impact sur les performances' },
        { name: 'conversionImpact', dataType: ['number'], description: 'Impact sur la conversion' },
        { name: 'tags', dataType: ['text'], description: 'Tags pour la recherche' },
        { name: 'timestamp', dataType: ['date'], description: 'Date de création' }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  private async createComprehensiveDesignSchema(): Promise<void> {
    const schema = {
      class: 'ComprehensiveDesign',
      description: 'Designs complets générés par l\'agent UI/UX',
      properties: [
        { name: 'brandName', dataType: ['text'], description: 'Nom de la marque' },
        { name: 'userResearch', dataType: ['text'], description: 'Recherche utilisateur' },
        { name: 'designIntelligence', dataType: ['text'], description: 'Intelligence design' },
        { name: 'personas', dataType: ['text'], description: 'Personas générés' },
        { name: 'designSystem', dataType: ['text'], description: 'Design system' },
        { name: 'wireframes', dataType: ['text'], description: 'Wireframes' },
        { name: 'usabilityResults', dataType: ['text'], description: 'Résultats d\'utilisabilité' },
        { name: 'conversionOptimizations', dataType: ['text'], description: 'Optimisations de conversion' },
        { name: 'componentLibrary', dataType: ['text'], description: 'Bibliothèque de composants' },
        { name: 'accessibilityCompliance', dataType: ['text'], description: 'Conformité d\'accessibilité' },
        { name: 'implementationGuide', dataType: ['text'], description: 'Guide d\'implémentation' },
        { name: 'testingStrategy', dataType: ['text'], description: 'Stratégie de test' },
        { name: 'timestamp', dataType: ['date'], description: 'Date de création' }
      ]
    };

    await this.createClassIfNotExists(schema);
  }

  private async createClassIfNotExists(schema: any): Promise<void> {
    try {
      const exists = await this.client.schema.exists(schema.class);
      if (!exists) {
        await this.client.schema.classCreator().withClass(schema).do();
        this.logger.info(`Classe ${schema.class} créée dans Weaviate`);
      }
    } catch (error) {
      this.logger.warn(`Erreur lors de la création de la classe ${schema.class}`, { error: error.message });
    }
  }
}
