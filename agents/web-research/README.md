# Agent Web Research 🔍

Agent spécialisé dans la recherche web intelligente, la veille concurrentielle et l'analyse de tendances pour le système d'agents distribués.

## 🎯 Fonctionnalités

### Recherche Web Intelligente
- **Multi-sources** : Web, actualités, académique, réseaux sociaux
- **Scraping avancé** : Puppeteer pour le contenu dynamique
- **APIs intégrées** : Google Search, Bing Search
- **Analyse de contenu** : Extraction d'entités, sentiment, mots-clés

### Analyse Concurrentielle
- **Monitoring concurrents** : Surveillance automatique des activités
- **Analyse UX** : Évaluation de l'expérience utilisateur
- **Benchmarking** : Comparaison des performances et stratégies
- **Détection d'opportunités** : Identification des gaps de marché

### Détection de Tendances
- **Tendances design** : Couleurs, typographie, layouts, interactions
- **Tendances marché** : Évolution des mots-clés et sujets
- **Analyse temporelle** : Croissance et déclin des tendances
- **Prédictions** : Anticipation des évolutions futures

### Monitoring Temps Réel
- **Alertes automatiques** : Notifications sur les changements importants
- **Surveillance continue** : Monitoring 24/7 des mots-clés et concurrents
- **Seuils configurables** : Personnalisation des critères d'alerte
- **Rapports périodiques** : Synthèses régulières des observations

## 🏗️ Architecture

### Engines Spécialisés
- **WebScrapingEngine** : Collecte intelligente de données web
- **CompetitiveAnalysisEngine** : Analyse concurrentielle approfondie
- **TrendDetectionEngine** : Détection et analyse des tendances
- **MarketResearchEngine** : Recherche et insights marché
- **MonitoringEngine** : Surveillance temps réel

### Communication Synaptique
- **KafkaCommunication** : Communication inter-agents
- **WeaviateMemory** : Mémoire vectorielle persistante

## 📊 API Endpoints

### Recherche
```bash
POST /research
{
  "query": "AI trends 2024",
  "sources": [
    { "type": "web", "priority": "high" },
    { "type": "news", "priority": "medium" }
  ],
  "options": {
    "maxResults": 20,
    "depth": "deep",
    "language": "en",
    "timeFrame": "last_month"
  }
}
```

### Tendances Design
```bash
POST /design-trends
{
  "industry": "fintech"
}
```

### Comportement Utilisateur
```bash
POST /user-behavior
{
  "domain": "example.com"
}
```

### Analyse Concurrentielle UX
```bash
POST /competitor-ux
{
  "competitors": ["competitor1.com", "competitor2.com"]
}
```

### Monitoring
```bash
# Démarrer
POST /monitoring/start
{
  "keywords": ["AI", "machine learning"],
  "competitors": ["openai.com", "anthropic.com"]
}

# Arrêter
POST /monitoring/stop
```

## 🚀 Installation et Démarrage

### Prérequis
- Node.js 18+
- Kafka
- Weaviate
- Redis (optionnel)

### Variables d'Environnement
```bash
# Port du serveur
PORT=3006

# Configuration Kafka
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=agent-web-research
KAFKA_GROUP_ID=web-research-group

# Configuration Weaviate
WEAVIATE_SCHEME=http
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080

# Configuration Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Configuration Puppeteer
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=30000
PUPPETEER_USER_AGENT=Mozilla/5.0 (compatible; WebResearchBot/1.0)

# APIs externes (optionnelles)
GOOGLE_API_KEY=your_google_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
BING_API_KEY=your_bing_api_key

# Logging
LOG_LEVEL=info
```

### Installation
```bash
# Installer les dépendances
npm install

# Compiler TypeScript
npm run build

# Démarrer en développement
npm run dev

# Démarrer en production
npm start
```

### Docker
```bash
# Construire l'image
docker build -t agent-web-research .

# Démarrer le conteneur
docker run -p 3006:3006 \
  -e KAFKA_BROKERS=kafka:9092 \
  -e WEAVIATE_HOST=weaviate \
  agent-web-research
```

## 🔧 Configuration

### Sources de Recherche
- **Web** : Recherche générale sur le web
- **News** : Actualités et articles de presse
- **Academic** : Publications académiques et recherche
- **Social** : Réseaux sociaux et discussions
- **Competitor** : Sites concurrents spécifiques

### Profondeur d'Analyse
- **Shallow** : Métadonnées et extraits uniquement
- **Medium** : Contenu principal des pages
- **Deep** : Analyse complète avec sous-pages

### Types d'Alertes
- **Keyword** : Changements dans les volumes de recherche
- **Competitor** : Activités concurrentielles détectées
- **Trend** : Nouvelles tendances émergentes
- **News** : Actualités importantes du secteur

## 📈 Métriques et Monitoring

### Métriques de Performance
- Temps de réponse des recherches
- Taux de réussite du scraping
- Précision des analyses de tendances
- Couverture des sources de données

### Métriques de Qualité
- Score de pertinence des résultats
- Niveau de confiance des analyses
- Diversité des sources
- Fraîcheur des données

### Alertes Système
- Échecs de scraping répétés
- Dépassement des quotas d'API
- Problèmes de connectivité
- Surcharge du système

## 🔗 Intégrations

### Agents Connectés
- **Agent UI/UX** : Fourniture de tendances design et insights utilisateur
- **Agent Marketing** : Données concurrentielles et opportunités marché
- **Agent Content Creator** : Tendances de contenu et sujets populaires
- **Cortex Central** : Coordination et orchestration des recherches

### Services Externes
- **Google Search API** : Recherche web étendue
- **Bing Search API** : Source de recherche alternative
- **Social Media APIs** : Données des réseaux sociaux
- **News APIs** : Flux d'actualités en temps réel

## 🛡️ Sécurité

### Mesures de Protection
- Rate limiting sur les APIs
- Validation des entrées utilisateur
- Sandboxing des processus de scraping
- Chiffrement des communications

### Conformité
- Respect des robots.txt
- Limitation des requêtes par seconde
- Anonymisation des données collectées
- Conformité GDPR pour les données européennes

## 📚 Documentation Technique

### Architecture des Données
- Stockage vectoriel dans Weaviate
- Indexation par domaine et temporalité
- Déduplication automatique
- Archivage des données anciennes

### Algorithmes d'Analyse
- Détection de tendances par régression linéaire
- Scoring de pertinence multi-facteurs
- Analyse de sentiment par NLP
- Clustering de contenu similaire

## 🤝 Contribution

### Développement
1. Fork du repository
2. Création d'une branche feature
3. Tests unitaires et d'intégration
4. Documentation des changements
5. Pull request avec description détaillée

### Standards de Code
- TypeScript strict mode
- ESLint + Prettier
- Tests avec Jest
- Documentation JSDoc
- Couverture de code > 80%

## 📄 Licence

MIT License - Voir le fichier LICENSE pour plus de détails.

---

**Agent Web Research** - Système de recherche et veille intelligente pour l'écosystème d'agents distribués Retreat And Be.
