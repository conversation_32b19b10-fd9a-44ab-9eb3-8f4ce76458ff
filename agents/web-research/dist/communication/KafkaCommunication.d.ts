import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { ResearchResult, DesignTrends, MonitoringAlert } from '../types';
/**
 * Communication Kafka pour l'Agent Web Research
 */
export declare class KafkaCommunication extends EventEmitter {
    private kafka;
    private producer;
    private consumer;
    private logger;
    private isConnected;
    private readonly topics;
    constructor(config: any, logger: Logger);
    /**
     * Initialise la communication Kafka
     */
    initialize(): Promise<void>;
    /**
     * S'abonne aux topics nécessaires
     */
    private subscribeToTopics;
    /**
     * Démarre la consommation des messages
     */
    private startConsuming;
    /**
     * Gère les messages entrants
     */
    private handleIncomingMessage;
    /**
     * Publie un résultat de recherche
     */
    publishResearchResult(result: ResearchResult): Promise<void>;
    /**
     * Publie des tendances design
     */
    publishDesignTrends(trends: DesignTrends): Promise<void>;
    /**
     * Publie une alerte de monitoring
     */
    publishAlert(alert: MonitoringAlert): Promise<void>;
    /**
     * Publie des données de comportement utilisateur
     */
    publishUserBehavior(behaviorData: any): Promise<void>;
    /**
     * Publie une analyse concurrentielle
     */
    publishCompetitorAnalysis(analysis: any): Promise<void>;
    /**
     * Publie le statut de l'agent
     */
    publishAgentStatus(status: any): Promise<void>;
    /**
     * Envoie un message personnalisé
     */
    sendMessage(topic: string, key: string, data: any): Promise<void>;
    /**
     * Déconnecte la communication Kafka
     */
    disconnect(): Promise<void>;
    /**
     * Vérifie si la communication est connectée
     */
    isConnectedToKafka(): boolean;
    /**
     * Obtient les métriques de communication
     */
    getMetrics(): any;
}
//# sourceMappingURL=KafkaCommunication.d.ts.map