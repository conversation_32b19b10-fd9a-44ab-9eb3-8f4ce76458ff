{"version": 3, "file": "CompetitiveAnalysisEngine.js", "sourceRoot": "", "sources": ["../../src/engines/CompetitiveAnalysisEngine.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,yBAAyB;IAIpC,YAAY,MAAmB,EAAE,MAAc;QAC7C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,aAA6B;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAEjE,oCAAoC;YACpC,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAE9D,MAAM,WAAW,GAAqB,EAAE,CAAC;YAEzC,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;gBACvD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,sDAAsD;oBAC/E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACjE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,4CAA4C;YAC5C,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAEtE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,WAAW,CAAC,MAAM,cAAc,CAAC,CAAC;YAC/D,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,qBAAqB;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,OAAuB;QACrE,IAAI,CAAC;YACH,kCAAkC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEtD,oCAAoC;YACpC,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEnD,uCAAuC;YACvC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAE7D,+BAA+B;YAC/B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEhE,OAAO;gBACL,IAAI;gBACJ,MAAM;gBACN,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBAC9C,SAAS;gBACT,UAAU;gBACV,cAAc;gBACd,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAwB;gBACzD,cAAc;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,UAAkB;QAC1C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,UAAU,EAAE,CAAC,CAAC;YAE/D,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAC/D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YAErE,oCAAoC;YACpC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAE9D,oBAAoB;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;YAE/E,OAAO;gBACL,UAAU;gBACV,MAAM,EAAE,UAAU;gBAClB,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,QAAQ;aACT,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,aAA6B;QACnD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAEjE,MAAM,QAAQ,GAAG;gBACf,OAAO,EAAE;oBACP;wBACE,OAAO,EAAE,uBAAuB;wBAChC,WAAW,EAAE,kDAAkD;wBAC/D,KAAK,EAAE,8BAA8B;wBACrC,UAAU,EAAE,IAAI;qBACjB;oBACD;wBACE,OAAO,EAAE,mBAAmB;wBAC5B,WAAW,EAAE,sCAAsC;wBACnD,KAAK,EAAE,8BAA8B;wBACrC,UAAU,EAAE,IAAI;qBACjB;oBACD;wBACE,OAAO,EAAE,oBAAoB;wBAC7B,WAAW,EAAE,8BAA8B;wBAC3C,KAAK,EAAE,oCAAoC;wBAC3C,UAAU,EAAE,IAAI;qBACjB;iBACF;gBACD,UAAU,EAAE;oBACV;wBACE,IAAI,EAAE,QAAQ;wBACd,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;wBAC3C,MAAM,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;qBAClE;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC;wBAC3C,MAAM,EAAE,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,qBAAqB,CAAC;qBAC3E;iBACF;aACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAuB;QAClD,MAAM,MAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;QAEjD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACzB,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,MAAc,EAAE,OAAuB;QAChE,mDAAmD;QACnD,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAEhC,iCAAiC;QACjC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAuB;QAC9C,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,qDAAqD;QACrD,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAEvF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;gBACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3B,SAAS,CAAC,IAAI,CAAC,UAAU,OAAO,aAAa,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;QACH,CAAC;QAED,kDAAkD;QAClD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CAAC,6BAA6B,EAAE,yBAAyB,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAuB;QAC/C,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,kCAAkC;QAClC,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QAE9E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;gBACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC3B,UAAU,CAAC,IAAI,CAAC,eAAe,OAAO,cAAc,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAED,sDAAsD;QACtD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,UAAU,CAAC,IAAI,CAAC,+BAA+B,EAAE,mBAAmB,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAuB;QACrD,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC;QAE1F,IAAI,YAAY,GAAG,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YAC3C,OAAO,eAAe,CAAC;QACzB,CAAC;aAAM,IAAI,YAAY,GAAG,GAAG,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YAClD,OAAO,mBAAmB,CAAC;QAC7B,CAAC;aAAM,IAAI,YAAY,GAAG,GAAG,EAAE,CAAC;YAC9B,OAAO,iBAAiB,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,OAAO,cAAc,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAuB;QACjD,iFAAiF;QACjF,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACvC,OAAO,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QACvD,CAAC;QACD,OAAO,4DAA4D,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,MAAc;QAChD,0DAA0D;QAC1D,OAAO;YACL,SAAS,EAAE;gBACT;oBACE,QAAQ,EAAE,UAAU;oBACpB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;oBAC5C,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;oBAC/B,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;iBACzE;gBACD;oBACE,QAAQ,EAAE,SAAS;oBACnB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;oBAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;oBAChC,QAAQ,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;iBACzE;aACF;YACD,SAAS,EAAE,SAAkB;YAC7B,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QAC/C,uEAAuE;QACvE,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,yBAAyB;IAC7D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAkB;QAClD,OAAO;YACL;gBACE,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,0CAA0C;gBACvD,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,CAAC,2BAA2B,EAAE,uBAAuB,CAAC;aACjE;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,oCAAoC;gBACjD,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,CAAC,yBAAyB,EAAE,yBAAyB,CAAC;aACjE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QACnD,OAAO;YACL;gBACE,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,yBAAyB;gBACtC,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,qBAAqB,CAAC;aAC7E;YACD;gBACE,IAAI,EAAE,mBAAmB;gBACzB,WAAW,EAAE,4BAA4B;gBACzC,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,CAAC,6BAA6B,EAAE,wBAAwB,CAAC;aACvE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,UAAkB;QACtD,OAAO;YACL;gBACE,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,yCAAyC;gBACtD,SAAS,EAAE,MAAM;gBACjB,cAAc,EAAE,iDAAiD;aAClE;YACD;gBACE,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,kCAAkC;gBAC/C,SAAS,EAAE,QAAQ;gBACnB,cAAc,EAAE,iDAAiD;aAClE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACjD,+DAA+D;QAC/D,OAAO;YACL,cAAc,UAAU,eAAe;YACvC,cAAc,UAAU,cAAc;YACtC,cAAc,UAAU,aAAa;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAuB,EAAE,UAAwB,EAAE,aAA8B;QAC1G,IAAI,QAAQ,GAAG,0BAA0B,CAAC;QAE1C,QAAQ,IAAI,cAAc,CAAC;QAC3B,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACpB,QAAQ,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,QAAQ,IAAI,iBAAiB,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACrB,QAAQ,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,QAAQ,IAAI,oBAAoB,CAAC;QACjC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACxB,QAAQ,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAnZD,8DAmZC"}