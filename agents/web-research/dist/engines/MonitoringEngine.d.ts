import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { AgentConfig } from '../types';
/**
 * Moteur de monitoring en temps réel
 */
export declare class MonitoringEngine extends EventEmitter {
    private logger;
    private config;
    private isMonitoring;
    private monitoringTasks;
    private keywords;
    private competitors;
    constructor(config: AgentConfig, logger: Logger);
    /**
     * Initialise le moteur de monitoring
     */
    initialize(): Promise<void>;
    /**
     * Démarre le monitoring
     */
    startMonitoring(keywords: string[], competitors: string[]): Promise<void>;
    /**
     * Arrête le monitoring
     */
    stopMonitoring(): Promise<void>;
    /**
     * Effectue le monitoring initial
     */
    private performInitialMonitoring;
    /**
     * Monitoring des mots-clés
     */
    private monitorKeywords;
    /**
     * Monitoring des concurrents
     */
    private monitorCompetitors;
    /**
     * Monitoring des tendances
     */
    private monitorTrends;
    /**
     * Monitoring des actualités
     */
    private monitorNews;
    /**
     * Génère un ID d'alerte unique
     */
    private generateAlertId;
    /**
     * G<PERSON><PERSON> une sévérité aléatoire
     */
    private randomSeverity;
    /**
     * Génère un impact aléatoire
     */
    private randomImpact;
    /**
     * Génère des mots-clés liés
     */
    private generateRelatedKeywords;
    /**
     * Vérifie si le monitoring est actif
     */
    isActive(): boolean;
    /**
     * Obtient les statistiques de monitoring
     */
    getMonitoringStats(): any;
}
//# sourceMappingURL=MonitoringEngine.d.ts.map