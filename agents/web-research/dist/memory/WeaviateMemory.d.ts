import { Logger } from 'winston';
import { ResearchResult, DesignTrends, MonitoringAlert, UserBehaviorData, CompetitorUXAnalysis } from '../types';
/**
 * Mémoire Weaviate pour l'Agent Web Research
 */
export declare class WeaviateMemory {
    private client;
    private logger;
    private isConnected;
    private readonly classes;
    constructor(config: any, logger: Logger);
    /**
     * Initialise la mémoire Weaviate
     */
    initialize(): Promise<void>;
    /**
     * Crée les schémas Weaviate
     */
    private createSchemas;
    /**
     * Crée le schéma pour les résultats de recherche
     */
    private createResearchResultSchema;
    /**
     * Crée le schéma pour les tendances design
     */
    private createDesignTrendSchema;
    /**
     * Crée le schéma pour les alertes de monitoring
     */
    private createMonitoringAlertSchema;
    /**
     * Crée le schéma pour le comportement utilisateur
     */
    private createUserBehaviorSchema;
    /**
     * <PERSON><PERSON><PERSON> le schéma pour l'analyse concurrentielle
     */
    private createCompetitorAnalysisSchema;
    /**
     * Crée une classe si elle n'existe pas
     */
    private createClassIfNotExists;
    /**
     * Stocke un résultat de recherche
     */
    storeResearchResult(result: ResearchResult): Promise<void>;
    /**
     * Stocke des tendances design
     */
    storeDesignTrends(trends: DesignTrends): Promise<void>;
    /**
     * Stocke une alerte de monitoring
     */
    storeAlert(alert: MonitoringAlert): Promise<void>;
    /**
     * Stocke des données de comportement utilisateur
     */
    storeUserBehaviorData(behaviorData: UserBehaviorData): Promise<void>;
    /**
     * Stocke des analyses UX concurrentielles
     */
    storeCompetitorUXAnalyses(analyses: CompetitorUXAnalysis[]): Promise<void>;
    /**
     * Recherche des résultats similaires
     */
    searchSimilarResults(query: string, limit?: number): Promise<any[]>;
    /**
     * Déconnecte la mémoire
     */
    disconnect(): Promise<void>;
    /**
     * Vérifie si la mémoire est connectée
     */
    isConnectedToWeaviate(): boolean;
}
//# sourceMappingURL=WeaviateMemory.d.ts.map