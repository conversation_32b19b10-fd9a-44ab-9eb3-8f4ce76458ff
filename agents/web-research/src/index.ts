import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createLogger, format, transports } from 'winston';
import dotenv from 'dotenv';
import { WebResearchAgent } from './core/WebResearchAgent';
import { AgentConfig } from './types';

// Charger les variables d'environnement
dotenv.config();

// Configuration du logger
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  transports: [
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    }),
    new transports.File({ filename: 'logs/web-research-error.log', level: 'error' }),
    new transports.File({ filename: 'logs/web-research.log' })
  ]
});

// Configuration de l'agent
const config: AgentConfig = {
  port: parseInt(process.env.PORT || '3006'),
  kafka: {
    brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
    clientId: process.env.KAFKA_CLIENT_ID || 'agent-web-research',
    groupId: process.env.KAFKA_GROUP_ID || 'web-research-group'
  },
  weaviate: {
    scheme: process.env.WEAVIATE_SCHEME || 'http',
    host: process.env.WEAVIATE_HOST || 'localhost',
    port: parseInt(process.env.WEAVIATE_PORT || '8080')
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD
  },
  puppeteer: {
    headless: process.env.PUPPETEER_HEADLESS !== 'false',
    timeout: parseInt(process.env.PUPPETEER_TIMEOUT || '30000'),
    userAgent: process.env.PUPPETEER_USER_AGENT || 'Mozilla/5.0 (compatible; WebResearchBot/1.0)'
  },
  apis: {
    googleSearch: process.env.GOOGLE_API_KEY ? {
      apiKey: process.env.GOOGLE_API_KEY,
      searchEngineId: process.env.GOOGLE_SEARCH_ENGINE_ID || ''
    } : undefined,
    bingSearch: process.env.BING_API_KEY ? {
      apiKey: process.env.BING_API_KEY
    } : undefined
  }
};

// Créer l'application Express
const app = express();

// Middleware de sécurité
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Créer l'instance de l'agent
const webResearchAgent = new WebResearchAgent(config, logger);

// Routes API

/**
 * Health check
 */
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    agent: 'web-research',
    version: '1.0.0'
  });
});

/**
 * Effectuer une recherche
 */
app.post('/research', async (req, res) => {
  try {
    const { query, sources, options, metadata } = req.body;

    if (!query) {
      return res.status(400).json({
        error: 'Query is required'
      });
    }

    const request = {
      query,
      sources: sources || [{ type: 'web', priority: 'high' }],
      options: {
        maxResults: 10,
        depth: 'medium',
        ...options
      },
      metadata
    };

    const result = await webResearchAgent.conductResearch(request);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('Research request failed:', error);
    res.status(500).json({
      error: 'Research request failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Analyser les tendances design
 */
app.post('/design-trends', async (req, res) => {
  try {
    const { industry } = req.body;

    if (!industry) {
      return res.status(400).json({
        error: 'Industry is required'
      });
    }

    const trends = await webResearchAgent.analyzeDesignTrends(industry);

    res.json({
      success: true,
      data: trends
    });
  } catch (error) {
    logger.error('Design trends analysis failed:', error);
    res.status(500).json({
      error: 'Design trends analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Analyser le comportement utilisateur
 */
app.post('/user-behavior', async (req, res) => {
  try {
    const { domain } = req.body;

    if (!domain) {
      return res.status(400).json({
        error: 'Domain is required'
      });
    }

    const behaviorData = await webResearchAgent.analyzeUserBehavior(domain);

    res.json({
      success: true,
      data: behaviorData
    });
  } catch (error) {
    logger.error('User behavior analysis failed:', error);
    res.status(500).json({
      error: 'User behavior analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Analyser la concurrence UX
 */
app.post('/competitor-ux', async (req, res) => {
  try {
    const { competitors } = req.body;

    if (!competitors || !Array.isArray(competitors)) {
      return res.status(400).json({
        error: 'Competitors array is required'
      });
    }

    const analyses = await webResearchAgent.performCompetitorUXAnalysis(competitors);

    res.json({
      success: true,
      data: analyses
    });
  } catch (error) {
    logger.error('Competitor UX analysis failed:', error);
    res.status(500).json({
      error: 'Competitor UX analysis failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Démarrer le monitoring
 */
app.post('/monitoring/start', async (req, res) => {
  try {
    const { keywords, competitors } = req.body;

    if (!keywords || !Array.isArray(keywords)) {
      return res.status(400).json({
        error: 'Keywords array is required'
      });
    }

    await webResearchAgent.startMonitoring(keywords, competitors || []);

    res.json({
      success: true,
      message: 'Monitoring started successfully'
    });
  } catch (error) {
    logger.error('Failed to start monitoring:', error);
    res.status(500).json({
      error: 'Failed to start monitoring',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Arrêter le monitoring
 */
app.post('/monitoring/stop', async (req, res) => {
  try {
    await webResearchAgent.stopMonitoring();

    res.json({
      success: true,
      message: 'Monitoring stopped successfully'
    });
  } catch (error) {
    logger.error('Failed to stop monitoring:', error);
    res.status(500).json({
      error: 'Failed to stop monitoring',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Obtenir les métriques de l'agent
 */
app.get('/metrics', (req, res) => {
  try {
    const metrics = {
      agent: 'web-research',
      status: 'running',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };

    res.json(metrics);
  } catch (error) {
    logger.error('Failed to get metrics:', error);
    res.status(500).json({
      error: 'Failed to get metrics'
    });
  }
});

// Gestionnaire d'erreurs global
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Gestionnaire pour les routes non trouvées
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Fonction de démarrage
async function startServer() {
  try {
    logger.info('Starting Web Research Agent...');

    // Initialiser l'agent
    await webResearchAgent.initialize();

    // Démarrer le serveur
    const server = app.listen(config.port, () => {
      logger.info(`Web Research Agent listening on port ${config.port}`);
      logger.info('Available endpoints:');
      logger.info('  GET  /health - Health check');
      logger.info('  POST /research - Conduct research');
      logger.info('  POST /design-trends - Analyze design trends');
      logger.info('  POST /user-behavior - Analyze user behavior');
      logger.info('  POST /competitor-ux - Analyze competitor UX');
      logger.info('  POST /monitoring/start - Start monitoring');
      logger.info('  POST /monitoring/stop - Stop monitoring');
      logger.info('  GET  /metrics - Get agent metrics');
    });

    // Gestion de l'arrêt gracieux
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      
      server.close(async () => {
        try {
          await webResearchAgent.shutdown();
          logger.info('Web Research Agent shut down successfully');
          process.exit(0);
        } catch (error) {
          logger.error('Error during shutdown:', error);
          process.exit(1);
        }
      });
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to start Web Research Agent:', error);
    process.exit(1);
  }
}

// Démarrer le serveur si ce fichier est exécuté directement
if (require.main === module) {
  startServer();
}

export { app, webResearchAgent };
