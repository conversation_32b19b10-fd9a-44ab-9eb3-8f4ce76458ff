import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface AgentInfo {
    id: string;
    name: string;
    type: AgentType;
    brainRegion: BrainRegion;
    status: AgentStatus;
    capabilities: string[];
    specializations: string[];
    healthScore: number;
    lastActivity: Date;
    connectionInfo: {
        url: string;
        port: number;
        protocol: 'http' | 'websocket' | 'grpc';
    };
    metrics: {
        tasksCompleted: number;
        averageResponseTime: number;
        successRate: number;
        currentLoad: number;
    };
}
export declare enum AgentType {
    FRONTEND = "frontend",
    BACKEND = "backend",
    QA = "qa",
    DEVOPS = "devops",
    SEO = "seo",
    SECURITY = "security",
    PERFORMANCE = "performance",
    MARKETING = "marketing",
    WEB_RESEARCH = "web-research",
    DATA_COLLECTOR = "data-collector",
    API_MONITOR = "api-monitor",
    MCP_CONNECTOR = "mcp-connector",
    TRANSLATION = "translation",
    DOCUMENTATION = "documentation",
    MIGRATION = "migration",
    COMPLIANCE = "compliance",
    EVOLUTION = "evolution"
}
export declare enum BrainRegion {
    CORTEX_CENTRAL = "cortex-central",
    CORTEX_DECISION = "cortex-decision",
    CORTEX_CREATIVE = "cortex-creative",
    CORTEX_LOGICAL = "cortex-logical",
    CORTEX_ANALYTICAL = "cortex-analytical",
    CORTEX_PREFRONTAL = "cortex-prefrontal",
    CORTEX_MOTOR = "cortex-motor",
    LIMBIC_SYSTEM = "limbic-system",
    CEREBELLUM_TECHNICAL = "cerebellum-technical",
    CEREBELLUM_MARKETING = "cerebellum-marketing",
    BRAINSTEM_IMMUNE = "brainstem-immune",
    BRAINSTEM_VITAL = "brainstem-vital",
    SENSORY_VISION = "sensory-vision",
    SENSORY_HEARING = "sensory-hearing",
    SENSORY_TOUCH = "sensory-touch",
    SENSORY_TASTE_SMELL = "sensory-taste-smell",
    BROCA_AREA = "broca-area",
    WERNICKE_AREA = "wernicke-area",
    NEUROPLASTICITY = "neuroplasticity"
}
export declare enum AgentStatus {
    INITIALIZING = "initializing",
    ACTIVE = "active",
    BUSY = "busy",
    IDLE = "idle",
    MAINTENANCE = "maintenance",
    ERROR = "error",
    OFFLINE = "offline"
}
export interface AgentRegistryConfig {
    memory: CentralMemory;
    communication: SynapticCommunication;
    healthCheckInterval?: number;
    maxRetries?: number;
}
/**
 * Registre des Agents - Gestion de l'Organisme IA Vivant
 *
 * Gère les 17 agents spécialisés organisés comme un cerveau humain
 * avec leurs connexions synaptiques et leur santé cognitive.
 */
export declare class AgentRegistry extends EventEmitter {
    private memory;
    private communication;
    private agents;
    private healthCheckInterval;
    private maxRetries;
    private isInitialized;
    private healthCheckTimer?;
    private readonly BRAIN_ANATOMY;
    constructor(config: AgentRegistryConfig);
    /**
     * Initialise le registre des agents
     */
    initialize(): Promise<void>;
    /**
     * Enregistre un nouvel agent
     */
    registerAgent(agentInfo: Partial<AgentInfo>): Promise<void>;
    /**
     * Obtient un agent par ID
     */
    getAgent(agentId: string): AgentInfo | undefined;
    /**
     * Obtient tous les agents d'un type donné
     */
    getAgentsByType(type: AgentType): AgentInfo[];
    /**
     * Obtient tous les agents d'une région cérébrale
     */
    getAgentsByBrainRegion(region: BrainRegion): AgentInfo[];
    /**
     * Obtient les agents disponibles pour une tâche
     */
    getAvailableAgents(capabilities?: string[]): AgentInfo[];
    /**
     * Met à jour le statut d'un agent
     */
    updateAgentStatus(agentId: string, status: AgentStatus): Promise<void>;
    /**
     * Met à jour les métriques d'un agent
     */
    updateAgentMetrics(agentId: string, metrics: Partial<AgentInfo['metrics']>): Promise<void>;
    /**
     * Découverte automatique des agents
     */
    private discoverAgents;
    /**
     * Teste la connexion à un agent
     */
    private testAgentConnection;
    /**
     * Calcule le score de santé d'un agent
     */
    private calculateHealthScore;
    /**
     * Vérifie la santé d'un agent
     */
    private checkAgentHealth;
    /**
     * Démarrage du monitoring de santé
     */
    private startHealthMonitoring;
    /**
     * Configuration des événements de communication
     */
    private setupCommunicationEvents;
    /**
     * Gestion des battements de cœur des agents
     */
    private handleAgentHeartbeat;
    /**
     * Gestion de la completion de tâches par les agents
     */
    private handleAgentTaskCompleted;
    /**
     * Charge les agents depuis la mémoire
     */
    private loadAgentsFromMemory;
    /**
     * Sauvegarde un agent en mémoire
     */
    private saveAgentToMemory;
    /**
     * Obtient les statistiques du registre
     */
    getRegistryStats(): {
        totalAgents: number;
        statusDistribution: Record<AgentStatus, number>;
        brainRegionDistribution: Record<BrainRegion, number>;
        averageHealthScore: number;
        activeAgents: number;
        lastUpdate: Date;
    };
    /**
     * Arrêt gracieux du registre
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=AgentRegistry.d.ts.map