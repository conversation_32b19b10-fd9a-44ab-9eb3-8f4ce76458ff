"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentRegistry = exports.AgentStatus = exports.BrainRegion = exports.AgentType = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
var AgentType;
(function (AgentType) {
    // Cortex Spécialisés
    AgentType["FRONTEND"] = "frontend";
    AgentType["BACKEND"] = "backend";
    AgentType["QA"] = "qa";
    // Cervelet (Coordination)
    AgentType["DEVOPS"] = "devops";
    AgentType["SEO"] = "seo";
    // Tronc Cérébral (Fonctions Vitales)
    AgentType["SECURITY"] = "security";
    AgentType["PERFORMANCE"] = "performance";
    // Système Limbique (Émotions/Relations)
    AgentType["MARKETING"] = "marketing";
    // Organes Sensoriels
    AgentType["WEB_RESEARCH"] = "web-research";
    AgentType["DATA_COLLECTOR"] = "data-collector";
    AgentType["API_MONITOR"] = "api-monitor";
    AgentType["MCP_CONNECTOR"] = "mcp-connector";
    // Aires Spécialisées
    AgentType["TRANSLATION"] = "translation";
    AgentType["DOCUMENTATION"] = "documentation";
    AgentType["MIGRATION"] = "migration";
    AgentType["COMPLIANCE"] = "compliance";
    AgentType["EVOLUTION"] = "evolution";
})(AgentType || (exports.AgentType = AgentType = {}));
var BrainRegion;
(function (BrainRegion) {
    BrainRegion["CORTEX_CENTRAL"] = "cortex-central";
    BrainRegion["CORTEX_DECISION"] = "cortex-decision";
    BrainRegion["CORTEX_CREATIVE"] = "cortex-creative";
    BrainRegion["CORTEX_LOGICAL"] = "cortex-logical";
    BrainRegion["CORTEX_ANALYTICAL"] = "cortex-analytical";
    BrainRegion["CORTEX_PREFRONTAL"] = "cortex-prefrontal";
    BrainRegion["CORTEX_MOTOR"] = "cortex-motor";
    BrainRegion["LIMBIC_SYSTEM"] = "limbic-system";
    BrainRegion["CEREBELLUM_TECHNICAL"] = "cerebellum-technical";
    BrainRegion["CEREBELLUM_MARKETING"] = "cerebellum-marketing";
    BrainRegion["BRAINSTEM_IMMUNE"] = "brainstem-immune";
    BrainRegion["BRAINSTEM_VITAL"] = "brainstem-vital";
    BrainRegion["SENSORY_VISION"] = "sensory-vision";
    BrainRegion["SENSORY_HEARING"] = "sensory-hearing";
    BrainRegion["SENSORY_TOUCH"] = "sensory-touch";
    BrainRegion["SENSORY_TASTE_SMELL"] = "sensory-taste-smell";
    BrainRegion["BROCA_AREA"] = "broca-area";
    BrainRegion["WERNICKE_AREA"] = "wernicke-area";
    BrainRegion["NEUROPLASTICITY"] = "neuroplasticity";
})(BrainRegion || (exports.BrainRegion = BrainRegion = {}));
var AgentStatus;
(function (AgentStatus) {
    AgentStatus["INITIALIZING"] = "initializing";
    AgentStatus["ACTIVE"] = "active";
    AgentStatus["BUSY"] = "busy";
    AgentStatus["IDLE"] = "idle";
    AgentStatus["MAINTENANCE"] = "maintenance";
    AgentStatus["ERROR"] = "error";
    AgentStatus["OFFLINE"] = "offline";
})(AgentStatus || (exports.AgentStatus = AgentStatus = {}));
/**
 * Registre des Agents - Gestion de l'Organisme IA Vivant
 *
 * Gère les 17 agents spécialisés organisés comme un cerveau humain
 * avec leurs connexions synaptiques et leur santé cognitive.
 */
class AgentRegistry extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.agents = new Map();
        this.isInitialized = false;
        // Cartographie anatomique des agents
        this.BRAIN_ANATOMY = {
            [AgentType.FRONTEND]: BrainRegion.CORTEX_CREATIVE,
            [AgentType.BACKEND]: BrainRegion.CORTEX_LOGICAL,
            [AgentType.QA]: BrainRegion.CORTEX_ANALYTICAL,
            [AgentType.DEVOPS]: BrainRegion.CEREBELLUM_TECHNICAL,
            [AgentType.SEO]: BrainRegion.CEREBELLUM_MARKETING,
            [AgentType.SECURITY]: BrainRegion.BRAINSTEM_IMMUNE,
            [AgentType.PERFORMANCE]: BrainRegion.BRAINSTEM_VITAL,
            [AgentType.MARKETING]: BrainRegion.LIMBIC_SYSTEM,
            [AgentType.WEB_RESEARCH]: BrainRegion.SENSORY_VISION,
            [AgentType.DATA_COLLECTOR]: BrainRegion.SENSORY_HEARING,
            [AgentType.API_MONITOR]: BrainRegion.SENSORY_TASTE_SMELL,
            [AgentType.MCP_CONNECTOR]: BrainRegion.SENSORY_TOUCH,
            [AgentType.TRANSLATION]: BrainRegion.BROCA_AREA,
            [AgentType.DOCUMENTATION]: BrainRegion.WERNICKE_AREA,
            [AgentType.MIGRATION]: BrainRegion.CORTEX_MOTOR,
            [AgentType.COMPLIANCE]: BrainRegion.CORTEX_PREFRONTAL,
            [AgentType.EVOLUTION]: BrainRegion.NEUROPLASTICITY
        };
        this.memory = config.memory;
        this.communication = config.communication;
        this.healthCheckInterval = config.healthCheckInterval || 30000; // 30 secondes
        this.maxRetries = config.maxRetries || 3;
    }
    /**
     * Initialise le registre des agents
     */
    async initialize() {
        try {
            logger_1.logger.info('🧠 Initialisation du Registre des Agents...');
            // Chargement des agents depuis la mémoire
            await this.loadAgentsFromMemory();
            // Découverte automatique des agents
            await this.discoverAgents();
            // Démarrage du monitoring de santé
            this.startHealthMonitoring();
            // Configuration des événements de communication
            this.setupCommunicationEvents();
            this.isInitialized = true;
            logger_1.logger.info(`✅ Registre des Agents initialisé avec ${this.agents.size} agents`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Registre des Agents:', error);
            throw error;
        }
    }
    /**
     * Enregistre un nouvel agent
     */
    async registerAgent(agentInfo) {
        if (!agentInfo.id || !agentInfo.type) {
            throw new Error('ID et type d\'agent requis pour l\'enregistrement');
        }
        const agent = {
            id: agentInfo.id,
            name: agentInfo.name || agentInfo.id,
            type: agentInfo.type,
            brainRegion: this.BRAIN_ANATOMY[agentInfo.type],
            status: AgentStatus.INITIALIZING,
            capabilities: agentInfo.capabilities || [],
            specializations: agentInfo.specializations || [],
            healthScore: 100,
            lastActivity: new Date(),
            connectionInfo: agentInfo.connectionInfo || {
                url: `http://agent-${agentInfo.type}`,
                port: 3000,
                protocol: 'http'
            },
            metrics: {
                tasksCompleted: 0,
                averageResponseTime: 0,
                successRate: 100,
                currentLoad: 0
            }
        };
        this.agents.set(agent.id, agent);
        // Sauvegarde en mémoire
        await this.saveAgentToMemory(agent);
        // Vérification de santé initiale
        await this.checkAgentHealth(agent.id);
        logger_1.logger.info(`🤖 Agent enregistré: ${agent.name} (${agent.type}) dans ${agent.brainRegion}`);
        this.emit('agent-registered', {
            agent,
            timestamp: new Date()
        });
    }
    /**
     * Obtient un agent par ID
     */
    getAgent(agentId) {
        return this.agents.get(agentId);
    }
    /**
     * Obtient tous les agents d'un type donné
     */
    getAgentsByType(type) {
        return Array.from(this.agents.values()).filter(agent => agent.type === type);
    }
    /**
     * Obtient tous les agents d'une région cérébrale
     */
    getAgentsByBrainRegion(region) {
        return Array.from(this.agents.values()).filter(agent => agent.brainRegion === region);
    }
    /**
     * Obtient les agents disponibles pour une tâche
     */
    getAvailableAgents(capabilities) {
        return Array.from(this.agents.values()).filter(agent => {
            const isAvailable = agent.status === AgentStatus.ACTIVE || agent.status === AgentStatus.IDLE;
            const hasCapabilities = !capabilities || capabilities.some(cap => agent.capabilities.includes(cap) || agent.specializations.includes(cap));
            return isAvailable && hasCapabilities;
        });
    }
    /**
     * Met à jour le statut d'un agent
     */
    async updateAgentStatus(agentId, status) {
        const agent = this.agents.get(agentId);
        if (!agent) {
            throw new Error(`Agent non trouvé: ${agentId}`);
        }
        const oldStatus = agent.status;
        agent.status = status;
        agent.lastActivity = new Date();
        this.agents.set(agentId, agent);
        await this.saveAgentToMemory(agent);
        logger_1.logger.debug(`🔄 Statut de l'agent ${agent.name} changé: ${oldStatus} → ${status}`);
        this.emit('agent-status-changed', {
            agentId,
            oldStatus,
            newStatus: status,
            timestamp: new Date()
        });
    }
    /**
     * Met à jour les métriques d'un agent
     */
    async updateAgentMetrics(agentId, metrics) {
        const agent = this.agents.get(agentId);
        if (!agent) {
            throw new Error(`Agent non trouvé: ${agentId}`);
        }
        agent.metrics = { ...agent.metrics, ...metrics };
        agent.lastActivity = new Date();
        // Calcul du score de santé basé sur les métriques
        agent.healthScore = this.calculateHealthScore(agent);
        this.agents.set(agentId, agent);
        await this.saveAgentToMemory(agent);
        this.emit('agent-metrics-updated', {
            agentId,
            metrics: agent.metrics,
            healthScore: agent.healthScore,
            timestamp: new Date()
        });
    }
    /**
     * Découverte automatique des agents
     */
    async discoverAgents() {
        logger_1.logger.info('🔍 Découverte automatique des agents...');
        const expectedAgents = Object.values(AgentType);
        for (const agentType of expectedAgents) {
            const agentId = `agent-${agentType}`;
            if (!this.agents.has(agentId)) {
                try {
                    // Tentative de connexion à l'agent
                    const connectionInfo = {
                        url: `http://agent-${agentType}`,
                        port: 3000,
                        protocol: 'http'
                    };
                    const isReachable = await this.testAgentConnection(connectionInfo);
                    if (isReachable) {
                        await this.registerAgent({
                            id: agentId,
                            name: `Agent ${agentType.charAt(0).toUpperCase() + agentType.slice(1)}`,
                            type: agentType,
                            connectionInfo
                        });
                    }
                    else {
                        logger_1.logger.warn(`⚠️ Agent ${agentType} non accessible à ${connectionInfo.url}:${connectionInfo.port}`);
                    }
                }
                catch (error) {
                    logger_1.logger.debug(`Agent ${agentType} non découvert:`, error.message);
                }
            }
        }
    }
    /**
     * Teste la connexion à un agent
     */
    async testAgentConnection(connectionInfo) {
        try {
            // Implémentation simplifiée - devrait faire un vrai test de connexion
            // const response = await fetch(`${connectionInfo.url}:${connectionInfo.port}/health`);
            // return response.ok;
            return true; // Pour la démo
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Calcule le score de santé d'un agent
     */
    calculateHealthScore(agent) {
        let score = 100;
        // Pénalité pour faible taux de succès
        if (agent.metrics.successRate < 90) {
            score -= (90 - agent.metrics.successRate);
        }
        // Pénalité pour temps de réponse élevé
        if (agent.metrics.averageResponseTime > 1000) {
            score -= Math.min(20, (agent.metrics.averageResponseTime - 1000) / 100);
        }
        // Pénalité pour charge élevée
        if (agent.metrics.currentLoad > 80) {
            score -= (agent.metrics.currentLoad - 80) / 4;
        }
        // Pénalité pour inactivité
        const inactiveTime = Date.now() - agent.lastActivity.getTime();
        if (inactiveTime > 300000) { // 5 minutes
            score -= Math.min(30, inactiveTime / 60000); // -1 point par minute
        }
        return Math.max(0, Math.round(score));
    }
    /**
     * Vérifie la santé d'un agent
     */
    async checkAgentHealth(agentId) {
        const agent = this.agents.get(agentId);
        if (!agent)
            return;
        try {
            // Test de connexion
            const isReachable = await this.testAgentConnection(agent.connectionInfo);
            if (isReachable) {
                if (agent.status === AgentStatus.ERROR || agent.status === AgentStatus.OFFLINE) {
                    await this.updateAgentStatus(agentId, AgentStatus.ACTIVE);
                }
            }
            else {
                if (agent.status !== AgentStatus.OFFLINE) {
                    await this.updateAgentStatus(agentId, AgentStatus.OFFLINE);
                }
            }
            // Recalcul du score de santé
            agent.healthScore = this.calculateHealthScore(agent);
            if (agent.healthScore < 50) {
                logger_1.logger.warn(`⚠️ Santé dégradée pour l'agent ${agent.name}: ${agent.healthScore}%`);
                this.emit('agent-health-degraded', {
                    agentId,
                    healthScore: agent.healthScore,
                    timestamp: new Date()
                });
            }
        }
        catch (error) {
            logger_1.logger.error(`❌ Erreur lors de la vérification de santé de l'agent ${agentId}:`, error);
            await this.updateAgentStatus(agentId, AgentStatus.ERROR);
        }
    }
    /**
     * Démarrage du monitoring de santé
     */
    startHealthMonitoring() {
        this.healthCheckTimer = setInterval(async () => {
            for (const agentId of this.agents.keys()) {
                await this.checkAgentHealth(agentId);
            }
        }, this.healthCheckInterval);
        logger_1.logger.info(`💓 Monitoring de santé démarré (intervalle: ${this.healthCheckInterval}ms)`);
    }
    /**
     * Configuration des événements de communication
     */
    setupCommunicationEvents() {
        this.communication.on('agent-heartbeat', (data) => {
            this.handleAgentHeartbeat(data);
        });
        this.communication.on('agent-task-completed', (data) => {
            this.handleAgentTaskCompleted(data);
        });
    }
    /**
     * Gestion des battements de cœur des agents
     */
    async handleAgentHeartbeat(data) {
        const { agentId, metrics } = data;
        if (this.agents.has(agentId)) {
            await this.updateAgentMetrics(agentId, metrics);
        }
    }
    /**
     * Gestion de la completion de tâches par les agents
     */
    async handleAgentTaskCompleted(data) {
        const { agentId, success, responseTime } = data;
        const agent = this.agents.get(agentId);
        if (agent) {
            const newMetrics = {
                tasksCompleted: agent.metrics.tasksCompleted + 1,
                averageResponseTime: (agent.metrics.averageResponseTime + responseTime) / 2,
                successRate: success ?
                    Math.min(100, agent.metrics.successRate + 0.1) :
                    Math.max(0, agent.metrics.successRate - 1)
            };
            await this.updateAgentMetrics(agentId, newMetrics);
        }
    }
    /**
     * Charge les agents depuis la mémoire
     */
    async loadAgentsFromMemory() {
        try {
            const storedAgents = await this.memory.retrieve('agent_registry');
            if (storedAgents && Array.isArray(storedAgents)) {
                for (const agentData of storedAgents) {
                    this.agents.set(agentData.id, agentData);
                }
                logger_1.logger.info(`📚 ${storedAgents.length} agents chargés depuis la mémoire`);
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ Impossible de charger les agents depuis la mémoire:', error);
        }
    }
    /**
     * Sauvegarde un agent en mémoire
     */
    async saveAgentToMemory(agent) {
        try {
            const allAgents = Array.from(this.agents.values());
            await this.memory.store('agent_registry', allAgents);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la sauvegarde de l\'agent:', error);
        }
    }
    /**
     * Obtient les statistiques du registre
     */
    getRegistryStats() {
        const agents = Array.from(this.agents.values());
        const statusCounts = agents.reduce((acc, agent) => {
            acc[agent.status] = (acc[agent.status] || 0) + 1;
            return acc;
        }, {});
        const brainRegionCounts = agents.reduce((acc, agent) => {
            acc[agent.brainRegion] = (acc[agent.brainRegion] || 0) + 1;
            return acc;
        }, {});
        return {
            totalAgents: agents.length,
            statusDistribution: statusCounts,
            brainRegionDistribution: brainRegionCounts,
            averageHealthScore: agents.reduce((sum, agent) => sum + agent.healthScore, 0) / agents.length,
            activeAgents: agents.filter(a => a.status === AgentStatus.ACTIVE).length,
            lastUpdate: new Date()
        };
    }
    /**
     * Arrêt gracieux du registre
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du Registre des Agents...');
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
        }
        // Sauvegarde finale
        const allAgents = Array.from(this.agents.values());
        await this.memory.store('agent_registry', allAgents);
        this.isInitialized = false;
        logger_1.logger.info('✅ Registre des Agents arrêté');
    }
}
exports.AgentRegistry = AgentRegistry;
//# sourceMappingURL=AgentRegistry.js.map