import { EventEmitter } from 'events';
export interface SynapticConfig {
    kafkaBrokers: string[];
    redisUrl: string;
}
export interface NeuralSignal {
    id: string;
    type: 'task-assignment' | 'status-update' | 'completion' | 'error' | 'heartbeat';
    fromAgent: string;
    toAgent?: string;
    payload: any;
    timestamp: Date;
    priority: 'low' | 'normal' | 'high' | 'critical';
}
export interface Synapse {
    id: string;
    fromAgent: string;
    toAgent: string;
    strength: number;
    lastUsed: Date;
    messageCount: number;
    averageLatency: number;
}
/**
 * Communication Synaptique - Système Nerveux de l'Organisme IA
 *
 * Gère la communication entre tous les agents via Kafka et Redis,
 * implémente la plasticité synaptique et l'optimisation des connexions.
 */
export declare class SynapticCommunication extends EventEmitter {
    private kafka;
    private producer;
    private consumer;
    private redis;
    private synapses;
    private isInitialized;
    private readonly TOPICS;
    constructor(config: SynapticConfig);
    /**
     * Initialise le système de communication synaptique
     */
    initialize(): Promise<void>;
    /**
     * Envoie un signal neuronal
     */
    sendNeuralSignal(signal: NeuralSignal): Promise<void>;
    /**
     * Établit une synapse entre deux agents
     */
    establishSynapse(fromAgent: string, toAgent: string): Promise<Synapse>;
    /**
     * Sélectionne le topic optimal pour un signal
     */
    private selectOptimalTopic;
    /**
     * Calcule la route optimale pour un signal
     */
    private calculateOptimalRoute;
    /**
     * Sélectionne la partition optimale
     */
    private selectOptimalPartition;
    /**
     * Hash d'un agent pour le partitioning
     */
    private hashAgent;
    /**
     * Trouve la partition la moins chargée
     */
    private getLeastLoadedPartition;
    /**
     * Met à jour la force synaptique
     */
    private updateSynapticStrength;
    /**
     * Log de l'activité neuronale
     */
    private logNeuralActivity;
    /**
     * Configuration des abonnements Kafka
     */
    private setupSubscriptions;
    /**
     * Traitement des messages entrants
     */
    private handleIncomingMessage;
    /**
     * Mise à jour des métriques de latence
     */
    private updateLatencyMetrics;
    /**
     * Création des topics Kafka
     */
    private createTopics;
    /**
     * Démarrage du monitoring synaptique
     */
    private startSynapticMonitoring;
    /**
     * Vérification de santé synaptique
     */
    private performSynapticHealthCheck;
    /**
     * Calcul de la force synaptique moyenne
     */
    private calculateAverageSynapticStrength;
    /**
     * Calcul de la latence moyenne
     */
    private calculateAverageLatency;
    /**
     * Obtient le taux de messages
     */
    private getMessageRate;
    /**
     * Démarrage de la plasticité synaptique
     */
    private startSynapticPlasticity;
    /**
     * Adaptation des connexions synaptiques
     */
    private adaptSynapticConnections;
    /**
     * Récupère le statut de la communication
     */
    getStatus(): any;
    /**
     * Arrêt gracieux
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=SynapticCommunication.d.ts.map