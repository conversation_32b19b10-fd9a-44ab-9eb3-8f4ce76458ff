"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SynapticCommunication = void 0;
const events_1 = require("events");
const kafkajs_1 = require("kafkajs");
const redis_1 = __importDefault(require("redis"));
const logger_1 = require("../utils/logger");
/**
 * Communication Synaptique - Système Nerveux de l'Organisme IA
 *
 * Gère la communication entre tous les agents via Kafka et Redis,
 * implémente la plasticité synaptique et l'optimisation des connexions.
 */
class SynapticCommunication extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.synapses = new Map();
        this.isInitialized = false;
        // Topics Kafka pour différents types de communication
        this.TOPICS = {
            NEURAL_SIGNALS: 'neural-signals',
            AGENT_HEARTBEAT: 'agent-heartbeat',
            TASK_COORDINATION: 'task-coordination',
            MEMORY_SYNC: 'memory-sync',
            EMERGENCY: 'emergency-signals'
        };
        // Configuration Kafka
        this.kafka = new kafkajs_1.Kafka({
            clientId: 'cortex-central-synaptic',
            brokers: config.kafkaBrokers,
            retry: {
                initialRetryTime: 100,
                retries: 8
            }
        });
        this.producer = this.kafka.producer({
            maxInFlightRequests: 1,
            idempotent: true,
            transactionTimeout: 30000
        });
        this.consumer = this.kafka.consumer({
            groupId: 'cortex-central-group',
            sessionTimeout: 30000,
            heartbeatInterval: 3000
        });
        // Configuration Redis
        this.redis = redis_1.default.createClient({
            url: config.redisUrl,
            retry_delay_on_failure: 100,
            max_attempts: 10
        });
    }
    /**
     * Initialise le système de communication synaptique
     */
    async initialize() {
        try {
            logger_1.logger.info('🔗 Initialisation de la Communication Synaptique...');
            // Connexion à Redis
            await this.redis.connect();
            logger_1.logger.info('✅ Connexion Redis établie');
            // Connexion à Kafka
            await this.producer.connect();
            await this.consumer.connect();
            logger_1.logger.info('✅ Connexion Kafka établie');
            // Création des topics si nécessaire
            await this.createTopics();
            // Configuration des abonnements
            await this.setupSubscriptions();
            // Démarrage du monitoring synaptique
            this.startSynapticMonitoring();
            // Démarrage de la plasticité synaptique
            this.startSynapticPlasticity();
            this.isInitialized = true;
            logger_1.logger.info('✅ Communication Synaptique initialisée');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation de la Communication Synaptique:', error);
            throw error;
        }
    }
    /**
     * Envoie un signal neuronal
     */
    async sendNeuralSignal(signal) {
        try {
            // Sélection du topic approprié
            const topic = this.selectOptimalTopic(signal);
            // Calcul de la route optimale
            const route = await this.calculateOptimalRoute(signal);
            // Envoi via Kafka
            await this.producer.send({
                topic,
                messages: [{
                        key: signal.toAgent || 'broadcast',
                        value: JSON.stringify(signal),
                        partition: route.partition,
                        headers: {
                            priority: signal.priority,
                            type: signal.type,
                            timestamp: signal.timestamp.toISOString()
                        }
                    }]
            });
            // Mise à jour des statistiques synaptiques
            if (signal.toAgent) {
                await this.updateSynapticStrength(signal.fromAgent, signal.toAgent);
            }
            // Logging de l'activité neuronale
            await this.logNeuralActivity(signal, route);
            this.emit('signal-sent', {
                signal,
                route,
                timestamp: new Date()
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'envoi du signal neuronal:', error);
            this.emit('signal-error', { signal, error });
            throw error;
        }
    }
    /**
     * Établit une synapse entre deux agents
     */
    async establishSynapse(fromAgent, toAgent) {
        const synapseId = `${fromAgent}->${toAgent}`;
        const synapse = {
            id: synapseId,
            fromAgent,
            toAgent,
            strength: 0.5, // Force initiale
            lastUsed: new Date(),
            messageCount: 0,
            averageLatency: 0
        };
        this.synapses.set(synapseId, synapse);
        // Stockage en Redis pour persistance
        await this.redis.hSet('synapses', synapseId, JSON.stringify(synapse));
        logger_1.logger.info(`🔗 Synapse établie: ${fromAgent} -> ${toAgent}`);
        this.emit('synapse-established', synapse);
        return synapse;
    }
    /**
     * Sélectionne le topic optimal pour un signal
     */
    selectOptimalTopic(signal) {
        switch (signal.type) {
            case 'task-assignment':
                return this.TOPICS.TASK_COORDINATION;
            case 'heartbeat':
                return this.TOPICS.AGENT_HEARTBEAT;
            case 'error':
                return this.TOPICS.EMERGENCY;
            default:
                return this.TOPICS.NEURAL_SIGNALS;
        }
    }
    /**
     * Calcule la route optimale pour un signal
     */
    async calculateOptimalRoute(signal) {
        // Algorithme de routage intelligent basé sur:
        // - La charge des partitions
        // - La latence historique
        // - La priorité du message
        // - La force synaptique
        const route = {
            protocol: 'kafka',
            topic: this.selectOptimalTopic(signal),
            partition: await this.selectOptimalPartition(signal),
            estimatedLatency: 0
        };
        // Calcul de la latence estimée
        if (signal.toAgent) {
            const synapseId = `${signal.fromAgent}->${signal.toAgent}`;
            const synapse = this.synapses.get(synapseId);
            route.estimatedLatency = synapse?.averageLatency || 100;
        }
        return route;
    }
    /**
     * Sélectionne la partition optimale
     */
    async selectOptimalPartition(signal) {
        // Stratégie de partitioning basée sur:
        // - L'agent destinataire (pour la localité)
        // - La charge des partitions
        // - La priorité du message
        if (signal.toAgent) {
            // Hash de l'agent pour consistance
            return this.hashAgent(signal.toAgent) % 3; // 3 partitions par défaut
        }
        // Pour les broadcasts, utiliser la partition la moins chargée
        return await this.getLeastLoadedPartition();
    }
    /**
     * Hash d'un agent pour le partitioning
     */
    hashAgent(agentId) {
        let hash = 0;
        for (let i = 0; i < agentId.length; i++) {
            const char = agentId.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }
    /**
     * Trouve la partition la moins chargée
     */
    async getLeastLoadedPartition() {
        // Implémentation simplifiée - dans un vrai système,
        // on interrogerait les métriques Kafka
        return Math.floor(Math.random() * 3);
    }
    /**
     * Met à jour la force synaptique
     */
    async updateSynapticStrength(fromAgent, toAgent) {
        const synapseId = `${fromAgent}->${toAgent}`;
        let synapse = this.synapses.get(synapseId);
        if (!synapse) {
            synapse = await this.establishSynapse(fromAgent, toAgent);
        }
        // Renforcement basé sur l'usage
        synapse.strength = Math.min(1.0, synapse.strength + 0.01);
        synapse.lastUsed = new Date();
        synapse.messageCount++;
        this.synapses.set(synapseId, synapse);
        await this.redis.hSet('synapses', synapseId, JSON.stringify(synapse));
    }
    /**
     * Log de l'activité neuronale
     */
    async logNeuralActivity(signal, route) {
        const activity = {
            signalId: signal.id,
            type: signal.type,
            fromAgent: signal.fromAgent,
            toAgent: signal.toAgent,
            route,
            timestamp: signal.timestamp
        };
        // Stockage en Redis avec TTL
        await this.redis.setEx(`neural-activity:${signal.id}`, 3600, // 1 heure
        JSON.stringify(activity));
        // Émission d'événement pour monitoring
        this.emit('neural-activity-logged', activity);
    }
    /**
     * Configuration des abonnements Kafka
     */
    async setupSubscriptions() {
        // Abonnement à tous les topics
        await this.consumer.subscribe({
            topics: Object.values(this.TOPICS),
            fromBeginning: false
        });
        // Traitement des messages
        await this.consumer.run({
            eachMessage: async ({ topic, partition, message }) => {
                await this.handleIncomingMessage(topic, partition, message);
            }
        });
    }
    /**
     * Traitement des messages entrants
     */
    async handleIncomingMessage(topic, partition, message) {
        try {
            if (!message.value)
                return;
            const signal = JSON.parse(message.value.toString());
            // Mise à jour des métriques de latence
            const latency = Date.now() - signal.timestamp.getTime();
            await this.updateLatencyMetrics(signal, latency);
            // Émission d'événements selon le type
            switch (signal.type) {
                case 'task-assignment':
                    this.emit('task-assignment', signal);
                    break;
                case 'status-update':
                    this.emit('agent-status-update', signal);
                    break;
                case 'completion':
                    this.emit('task-completion', signal);
                    break;
                case 'error':
                    this.emit('agent-error', signal);
                    break;
                case 'heartbeat':
                    this.emit('agent-heartbeat', signal);
                    break;
                default:
                    this.emit('agent-message', signal);
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du traitement du message:', error);
        }
    }
    /**
     * Mise à jour des métriques de latence
     */
    async updateLatencyMetrics(signal, latency) {
        if (signal.toAgent) {
            const synapseId = `${signal.fromAgent}->${signal.toAgent}`;
            const synapse = this.synapses.get(synapseId);
            if (synapse) {
                // Calcul de la moyenne mobile
                synapse.averageLatency = (synapse.averageLatency * 0.9) + (latency * 0.1);
                this.synapses.set(synapseId, synapse);
            }
        }
    }
    /**
     * Création des topics Kafka
     */
    async createTopics() {
        const admin = this.kafka.admin();
        await admin.connect();
        try {
            await admin.createTopics({
                topics: Object.values(this.TOPICS).map(topic => ({
                    topic,
                    numPartitions: 3,
                    replicationFactor: 1
                }))
            });
        }
        catch (error) {
            // Topics peuvent déjà exister
            logger_1.logger.debug('Topics Kafka déjà existants ou erreur de création:', error);
        }
        await admin.disconnect();
    }
    /**
     * Démarrage du monitoring synaptique
     */
    startSynapticMonitoring() {
        setInterval(async () => {
            await this.performSynapticHealthCheck();
        }, 30000); // Toutes les 30 secondes
    }
    /**
     * Vérification de santé synaptique
     */
    async performSynapticHealthCheck() {
        const metrics = {
            totalSynapses: this.synapses.size,
            averageStrength: this.calculateAverageSynapticStrength(),
            averageLatency: this.calculateAverageLatency(),
            messageRate: await this.getMessageRate(),
            timestamp: new Date()
        };
        this.emit('synaptic-health-check', metrics);
    }
    /**
     * Calcul de la force synaptique moyenne
     */
    calculateAverageSynapticStrength() {
        if (this.synapses.size === 0)
            return 0;
        const totalStrength = Array.from(this.synapses.values())
            .reduce((sum, synapse) => sum + synapse.strength, 0);
        return totalStrength / this.synapses.size;
    }
    /**
     * Calcul de la latence moyenne
     */
    calculateAverageLatency() {
        if (this.synapses.size === 0)
            return 0;
        const totalLatency = Array.from(this.synapses.values())
            .reduce((sum, synapse) => sum + synapse.averageLatency, 0);
        return totalLatency / this.synapses.size;
    }
    /**
     * Obtient le taux de messages
     */
    async getMessageRate() {
        // Implémentation simplifiée
        // Dans un vrai système, on utiliserait les métriques Kafka
        return 0;
    }
    /**
     * Démarrage de la plasticité synaptique
     */
    startSynapticPlasticity() {
        setInterval(async () => {
            await this.adaptSynapticConnections();
        }, 60000); // Toutes les minutes
    }
    /**
     * Adaptation des connexions synaptiques
     */
    async adaptSynapticConnections() {
        const now = new Date();
        const maxIdleTime = 300000; // 5 minutes
        const minStrength = 0.1;
        for (const [synapseId, synapse] of this.synapses) {
            const idleTime = now.getTime() - synapse.lastUsed.getTime();
            // Affaiblissement par non-usage
            if (idleTime > maxIdleTime) {
                synapse.strength = Math.max(minStrength, synapse.strength - 0.01);
            }
            // Élagage des connexions faibles
            if (synapse.strength < minStrength) {
                this.synapses.delete(synapseId);
                await this.redis.hDel('synapses', synapseId);
                logger_1.logger.info(`🔥 Synapse élaguée: ${synapseId}`);
                this.emit('synapse-pruned', synapse);
            }
            else {
                // Mise à jour en Redis
                await this.redis.hSet('synapses', synapseId, JSON.stringify(synapse));
            }
        }
    }
    /**
     * Récupère le statut de la communication
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            totalSynapses: this.synapses.size,
            averageStrength: this.calculateAverageSynapticStrength(),
            averageLatency: this.calculateAverageLatency(),
            topics: this.TOPICS
        };
    }
    /**
     * Arrêt gracieux
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt de la Communication Synaptique...');
        await this.producer.disconnect();
        await this.consumer.disconnect();
        await this.redis.disconnect();
        logger_1.logger.info('✅ Communication Synaptique arrêtée');
    }
}
exports.SynapticCommunication = SynapticCommunication;
//# sourceMappingURL=SynapticCommunication.js.map