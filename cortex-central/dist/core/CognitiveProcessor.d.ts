import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine, CognitiveAnalysis } from '../decision/DecisionEngine';
export interface CognitiveConfig {
    memory: CentralMemory;
    decisionEngine: DecisionEngine;
}
export interface InstructionAnalysis {
    complexity: 'low' | 'medium' | 'high' | 'critical';
    domains: string[];
    requiredSkills: string[];
    estimatedDuration: number;
    dependencies: string[];
    riskLevel: number;
    confidence: number;
    intent: string;
    entities: any[];
    sentiment: number;
}
/**
 * Processeur Cognitif - Analyse et Compréhension des Instructions
 *
 * Responsable de l'analyse cognitive des instructions utilisateur,
 * de l'extraction d'entités et de la compréhension contextuelle.
 */
export declare class CognitiveProcessor extends EventEmitter {
    private memory;
    private decisionEngine;
    private isInitialized;
    private domainPatterns;
    private skillPatterns;
    private complexityIndicators;
    private cognitiveMetrics;
    constructor(config: CognitiveConfig);
    /**
     * Initialise le processeur cognitif
     */
    initialize(): Promise<void>;
    /**
     * Analyse cognitive des instructions utilisateur
     */
    analyzeInstructions(instructions: string): Promise<CognitiveAnalysis>;
    /**
     * Traite un message d'agent
     */
    processAgentMessage(data: any): Promise<any>;
    /**
     * Initialise les patterns de reconnaissance
     */
    private initializePatterns;
    /**
     * Préprocessing du texte
     */
    private preprocessText;
    /**
     * Extraction d'entités
     */
    private extractEntities;
    /**
     * Détection de l'intention
     */
    private detectIntent;
    /**
     * Analyse des domaines
     */
    private analyzeDomains;
    /**
     * Analyse des compétences requises
     */
    private analyzeRequiredSkills;
    /**
     * Évaluation de la complexité
     */
    private evaluateComplexity;
    /**
     * Estimation de la durée
     */
    private estimateDuration;
    /**
     * Analyse des dépendances
     */
    private analyzeDependencies;
    /**
     * Évaluation du niveau de risque
     */
    private evaluateRiskLevel;
    /**
     * Calcul de la confiance
     */
    private calculateConfidence;
    /**
     * Analyse du sentiment
     */
    private analyzeSentiment;
    /**
     * Analyse du contexte d'un message
     */
    private analyzeMessageContext;
    /**
     * Extraction d'insights d'un message
     */
    private extractInsights;
    /**
     * Classification d'un message
     */
    private classifyMessage;
    /**
     * Récupération de l'historique d'un agent
     */
    private getAgentHistory;
    /**
     * Analyse du contexte temporel
     */
    private analyzeTemporalContext;
    /**
     * Analyse du contexte technique
     */
    private analyzeTechnicalContext;
    /**
     * Chargement des patterns depuis la mémoire
     */
    private loadPatternsFromMemory;
    /**
     * Stockage de l'analyse en mémoire
     */
    private storeAnalysisInMemory;
    /**
     * Mise à jour des métriques cognitives
     */
    private updateCognitiveMetrics;
    /**
     * Démarrage de l'auto-amélioration cognitive
     */
    private startCognitiveImprovement;
    /**
     * Exécute l'auto-amélioration cognitive
     */
    private performCognitiveImprovement;
    /**
     * Met à jour les patterns basés sur les analyses
     */
    private updatePatternsFromAnalyses;
    /**
     * Récupère le statut du processeur cognitif
     */
    getStatus(): any;
    /**
     * Arrêt gracieux
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=CognitiveProcessor.d.ts.map