import { EventEmitter } from 'events';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface CortexConfig {
    neuralNetwork: NeuralNetworkManager;
    memory: CentralMemory;
    decisionEngine: DecisionEngine;
    communication: SynapticCommunication;
    io: any;
}
export interface InstructionRequest {
    instructions: string;
    priority: 'low' | 'normal' | 'high' | 'critical';
    requester: string;
    timestamp: Date;
    context?: any;
}
export interface TaskStatus {
    taskId: string;
    status: 'pending' | 'analyzing' | 'planning' | 'executing' | 'completed' | 'failed';
    progress: number;
    assignedAgents: string[];
    startTime: Date;
    estimatedCompletion?: Date;
    result?: any;
    error?: string;
}
/**
 * Cortex Central - Orchestrateur Cognitif Principal
 *
 * Le Cortex Central est le cerveau principal de l'organisme IA.
 * Il analyse les requêtes, prend des décisions stratégiques,
 * et orchestre les agents spécialisés pour accomplir les tâches.
 */
export declare class CortexCentral extends EventEmitter {
    private neuralNetwork;
    private memory;
    private decisionEngine;
    private communication;
    private io;
    private taskOrchestrator;
    private cognitiveProcessor;
    private apiGateway;
    private learningSystem;
    private activeTasks;
    private isInitialized;
    private startTime;
    constructor(config: CortexConfig);
    /**
     * Initialise le Cortex Central
     */
    initialize(): Promise<void>;
    /**
     * Traite des instructions reçues de l'utilisateur ou d'autres systèmes
     */
    processInstructions(request: InstructionRequest): Promise<string>;
    /**
     * Récupère le statut d'une tâche
     */
    getTaskStatus(taskId: string): Promise<TaskStatus | null>;
    /**
     * Met à jour le statut d'une tâche
     */
    private updateTaskStatus;
    /**
     * Configuration des événements de communication
     */
    private setupCommunicationEvents;
    /**
     * Configuration des événements neuronaux
     */
    private setupNeuralEvents;
    /**
     * Gestion des messages d'agents
     */
    private handleAgentMessage;
    /**
     * Gestion des mises à jour de statut d'agents
     */
    private handleAgentStatusUpdate;
    /**
     * Gestion de la completion de tâches
     */
    private handleTaskCompletion;
    /**
     * Démarrage du monitoring cognitif
     */
    private startCognitiveMonitoring;
    /**
     * Vérification de santé cognitive
     */
    private performCognitiveHealthCheck;
    /**
     * Optimisation de la charge de tâches
     */
    private optimizeTaskLoad;
    /**
     * Récupère le statut du cortex
     */
    getStatus(): any;
    /**
     * Arrêt gracieux du cortex
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=CortexCentral.d.ts.map