import { EventEmitter } from 'events';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface OrchestratorConfig {
    neuralNetwork: NeuralNetworkManager;
    memory: CentralMemory;
    communication: SynapticCommunication;
}
export interface OrchestrationRequest {
    taskId: string;
    plan: any;
    priority: string;
}
export interface OrchestrationResult {
    taskId: string;
    assignedAgents: string[];
    estimatedCompletion: Date;
    orchestrationPlan: any;
}
/**
 * Orchestrateur de Tâches - Coordinateur des Agents
 *
 * Responsable de l'attribution et de la coordination des tâches
 * entre les différents agents spécialisés du système.
 */
export declare class TaskOrchestrator extends EventEmitter {
    private neuralNetwork;
    private memory;
    private communication;
    private isInitialized;
    private activeTasks;
    private orchestrationMetrics;
    private agentCoordinator;
    private taskDependencyGraph;
    private resourcePool;
    constructor(config: OrchestratorConfig);
    /**
     * Initialise l'orchestrateur de tâches
     */
    initialize(): Promise<void>;
    /**
     * Orchestre une tâche selon le plan stratégique
     */
    orchestrateTask(request: OrchestrationRequest): Promise<OrchestrationResult>;
    /**
     * Récupère les agents disponibles
     */
    private getAvailableAgents;
    /**
     * Sélectionne et attribue les agents appropriés
     */
    private selectAndAssignAgents;
    /**
     * Vérifie si un agent correspond à la spécialisation requise
     */
    private matchesSpecialization;
    /**
     * Sélectionne le meilleur agent parmi les candidats
     */
    private selectBestAgent;
    /**
     * Crée le plan d'orchestration détaillé
     */
    private createOrchestrationPlan;
    /**
     * Crée la timeline d'exécution
     */
    private createExecutionTimeline;
    /**
     * Tri topologique des phases basé sur les dépendances
     */
    private topologicalSort;
    /**
     * Envoie les instructions aux agents assignés
     */
    private dispatchInstructions;
    /**
     * Calcule l'estimation de completion
     */
    private calculateEstimatedCompletion;
    /**
     * Configuration des événements de communication
     */
    private setupCommunicationEvents;
    /**
     * Gestion de la completion de tâches
     */
    private handleTaskCompletion;
    /**
     * Gestion des mises à jour de statut d'agents
     */
    private handleAgentStatusUpdate;
    /**
     * Gestion des erreurs d'agents
     */
    private handleAgentError;
    /**
     * Tentative de réassignation de tâche
     */
    private attemptTaskReassignment;
    /**
     * Met à jour le temps de completion moyen
     */
    private updateAverageCompletionTime;
    /**
     * Met à jour l'utilisation d'un agent
     */
    private updateAgentUtilization;
    /**
     * Démarrage du monitoring d'orchestration
     */
    private startOrchestrationMonitoring;
    /**
     * Vérification de santé de l'orchestration
     */
    private performOrchestrationHealthCheck;
    /**
     * Initialise la coordination multi-agents
     */
    private initializeAgentCoordination;
    /**
     * Sélection et attribution intelligente des agents
     */
    private selectAndAssignAgentsIntelligent;
    /**
     * Création d'un plan d'orchestration optimisé
     */
    private createOptimizedOrchestrationPlan;
    /**
     * Analyse des dépendances et parallélisation
     */
    private analyzeDependenciesAndParallelize;
    /**
     * Envoi d'instructions avec coordination
     */
    private dispatchInstructionsWithCoordination;
    /**
     * Analyse des exigences en ressources
     */
    private analyzeResourceRequirements;
    /**
     * Vérifie la capacité disponible d'un agent
     */
    private hasAvailableCapacity;
    /**
     * Vérifie si l'agent répond aux exigences en ressources
     */
    private meetsResourceRequirements;
    /**
     * Sélectionne l'agent optimal
     */
    private selectOptimalAgent;
    /**
     * Met à jour la coordination d'un agent
     */
    private updateAgentCoordination;
    /**
     * Démarre l'optimisation des ressources
     */
    private startResourceOptimization;
    /**
     * Optimise l'allocation des ressources
     */
    private optimizeResourceAllocation;
    /**
     * Récupère le statut de l'orchestrateur
     */
    getStatus(): any;
    /**
     * Arrêt gracieux
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=TaskOrchestrator.d.ts.map