"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskOrchestrator = void 0;
const events_1 = require("events");
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
/**
 * Orchestrateur de Tâches - Coordinateur des Agents
 *
 * Responsable de l'attribution et de la coordination des tâches
 * entre les différents agents spécialisés du système.
 */
class TaskOrchestrator extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        // Suivi des tâches actives
        this.activeTasks = new Map();
        // Métriques d'orchestration
        this.orchestrationMetrics = {
            totalTasks: 0,
            successfulOrchestrations: 0,
            averageCompletionTime: 0,
            agentUtilization: new Map(),
            coordinationEfficiency: 0,
            parallelTasksCount: 0,
            resourceOptimization: 0
        };
        // Coordination multi-agents avancée
        this.agentCoordinator = new Map();
        this.taskDependencyGraph = new Map();
        this.resourcePool = new Map();
        this.neuralNetwork = config.neuralNetwork;
        this.memory = config.memory;
        this.communication = config.communication;
    }
    /**
     * Initialise l'orchestrateur de tâches
     */
    async initialize() {
        try {
            logger_1.logger.info('🎼 Initialisation de l\'Orchestrateur de Tâches...');
            // Configuration des événements de communication
            this.setupCommunicationEvents();
            // Initialisation de la coordination multi-agents
            await this.initializeAgentCoordination();
            // Démarrage du monitoring d'orchestration
            this.startOrchestrationMonitoring();
            // Démarrage de l'optimisation des ressources
            this.startResourceOptimization();
            this.isInitialized = true;
            logger_1.logger.info('✅ Orchestrateur de Tâches initialisé');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation de l\'Orchestrateur:', error);
            throw error;
        }
    }
    /**
     * Orchestre une tâche selon le plan stratégique
     */
    async orchestrateTask(request) {
        try {
            logger_1.logger.info(`🎼 Orchestration de la tâche ${request.taskId}...`);
            const { taskId, plan, priority } = request;
            // Analyse des agents disponibles
            const availableAgents = await this.getAvailableAgents();
            // Sélection et attribution des agents avec coordination intelligente
            const assignedAgents = await this.selectAndAssignAgentsIntelligent(plan, availableAgents, priority);
            // Création du plan d'orchestration avec optimisation
            const orchestrationPlan = await this.createOptimizedOrchestrationPlan(plan, assignedAgents);
            // Analyse des dépendances et parallélisation
            await this.analyzeDependenciesAndParallelize(taskId, orchestrationPlan);
            // Envoi des instructions aux agents avec coordination
            await this.dispatchInstructionsWithCoordination(taskId, orchestrationPlan, assignedAgents);
            // Calcul de l'estimation de completion
            const estimatedCompletion = this.calculateEstimatedCompletion(plan);
            // Enregistrement de la tâche active
            this.activeTasks.set(taskId, {
                taskId,
                plan,
                assignedAgents,
                orchestrationPlan,
                startTime: new Date(),
                estimatedCompletion,
                status: 'orchestrated'
            });
            // Mise à jour des métriques
            this.orchestrationMetrics.totalTasks++;
            const result = {
                taskId,
                assignedAgents,
                estimatedCompletion,
                orchestrationPlan
            };
            logger_1.logger.info(`✅ Tâche ${taskId} orchestrée avec ${assignedAgents.length} agents`);
            this.emit('task-orchestrated', {
                taskId,
                assignedAgents,
                estimatedCompletion,
                timestamp: new Date()
            });
            return result;
        }
        catch (error) {
            logger_1.logger.error(`❌ Erreur lors de l'orchestration de la tâche ${request.taskId}:`, error);
            throw error;
        }
    }
    /**
     * Récupère les agents disponibles
     */
    async getAvailableAgents() {
        try {
            const connectedAgents = this.neuralNetwork.getConnectedAgents();
            // Filtrage des agents disponibles (non surchargés)
            const availableAgents = connectedAgents.filter(agent => {
                const utilization = this.orchestrationMetrics.agentUtilization.get(agent.id) || 0;
                return utilization < 0.8; // Moins de 80% d'utilisation
            });
            logger_1.logger.debug(`📊 ${availableAgents.length} agents disponibles sur ${connectedAgents.length} connectés`);
            return availableAgents;
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la récupération des agents disponibles:', error);
            return [];
        }
    }
    /**
     * Sélectionne et attribue les agents appropriés
     */
    async selectAndAssignAgents(plan, availableAgents, priority) {
        const assignedAgents = [];
        // Analyse des exigences d'agents du plan
        const requiredAgents = plan.requiredAgents || [];
        for (const requirement of requiredAgents) {
            const suitableAgents = availableAgents.filter(agent => agent.type === requirement.agentType &&
                this.matchesSpecialization(agent, requirement.specialization));
            if (suitableAgents.length === 0) {
                logger_1.logger.warn(`⚠️ Aucun agent ${requirement.agentType} disponible`);
                continue;
            }
            // Sélection du meilleur agent basé sur les métriques
            const selectedAgent = this.selectBestAgent(suitableAgents, requirement, priority);
            if (selectedAgent && !assignedAgents.includes(selectedAgent.id)) {
                assignedAgents.push(selectedAgent.id);
                // Mise à jour de l'utilisation de l'agent
                const currentUtilization = this.orchestrationMetrics.agentUtilization.get(selectedAgent.id) || 0;
                this.orchestrationMetrics.agentUtilization.set(selectedAgent.id, Math.min(1.0, currentUtilization + (requirement.estimatedWorkload / 480)) // Normalisation sur 8h
                );
            }
        }
        return assignedAgents;
    }
    /**
     * Vérifie si un agent correspond à la spécialisation requise
     */
    matchesSpecialization(agent, specialization) {
        if (!agent.specializations)
            return true;
        return agent.specializations.some((spec) => spec.toLowerCase().includes(specialization.toLowerCase()) ||
            specialization.toLowerCase().includes(spec.toLowerCase()));
    }
    /**
     * Sélectionne le meilleur agent parmi les candidats
     */
    selectBestAgent(candidates, requirement, priority) {
        // Algorithme de sélection basé sur:
        // - Charge actuelle
        // - Historique de performance
        // - Spécialisation
        // - Priorité de la tâche
        let bestAgent = candidates[0];
        let bestScore = 0;
        for (const agent of candidates) {
            let score = 0;
            // Score basé sur la charge (moins chargé = meilleur)
            const utilization = this.orchestrationMetrics.agentUtilization.get(agent.id) || 0;
            score += (1 - utilization) * 40;
            // Score basé sur la spécialisation
            if (this.matchesSpecialization(agent, requirement.specialization)) {
                score += 30;
            }
            // Score basé sur l'historique de performance
            const performanceScore = agent.performanceMetrics?.successRate || 0.5;
            score += performanceScore * 20;
            // Bonus pour la priorité
            if (priority === 'high' || priority === 'critical') {
                score += agent.priority === 'high' ? 10 : 0;
            }
            if (score > bestScore) {
                bestScore = score;
                bestAgent = agent;
            }
        }
        return bestAgent;
    }
    /**
     * Crée le plan d'orchestration détaillé
     */
    async createOrchestrationPlan(strategicPlan, assignedAgents) {
        const orchestrationPlan = {
            id: (0, uuid_1.v4)(),
            phases: [],
            agentAssignments: new Map(),
            dependencies: [],
            timeline: []
        };
        // Mapping des phases aux agents
        for (const phase of strategicPlan.phases) {
            const phaseAgents = assignedAgents.filter(agentId => {
                const agent = this.neuralNetwork.getAgentById(agentId);
                return agent && phase.requiredAgents.includes(agent.type);
            });
            orchestrationPlan.phases.push({
                ...phase,
                assignedAgents: phaseAgents,
                orchestrationId: (0, uuid_1.v4)()
            });
            // Enregistrement des assignations
            phaseAgents.forEach(agentId => {
                if (!orchestrationPlan.agentAssignments.has(agentId)) {
                    orchestrationPlan.agentAssignments.set(agentId, []);
                }
                orchestrationPlan.agentAssignments.get(agentId).push(phase.id);
            });
        }
        // Création de la timeline
        orchestrationPlan.timeline = this.createExecutionTimeline(orchestrationPlan.phases);
        return orchestrationPlan;
    }
    /**
     * Crée la timeline d'exécution
     */
    createExecutionTimeline(phases) {
        const timeline = [];
        let currentTime = new Date();
        // Tri des phases par ordre et dépendances
        const sortedPhases = this.topologicalSort(phases);
        for (const phase of sortedPhases) {
            const startTime = new Date(currentTime);
            const endTime = new Date(currentTime.getTime() + phase.estimatedDuration * 60000); // minutes en ms
            timeline.push({
                phaseId: phase.id,
                startTime,
                endTime,
                duration: phase.estimatedDuration,
                assignedAgents: phase.assignedAgents
            });
            currentTime = endTime;
        }
        return timeline;
    }
    /**
     * Tri topologique des phases basé sur les dépendances
     */
    topologicalSort(phases) {
        const sorted = [];
        const visited = new Set();
        const visiting = new Set();
        const visit = (phase) => {
            if (visiting.has(phase.id)) {
                throw new Error(`Dépendance circulaire détectée: ${phase.id}`);
            }
            if (visited.has(phase.id)) {
                return;
            }
            visiting.add(phase.id);
            // Visiter les dépendances d'abord
            for (const depId of phase.dependencies) {
                const depPhase = phases.find(p => p.id === depId);
                if (depPhase) {
                    visit(depPhase);
                }
            }
            visiting.delete(phase.id);
            visited.add(phase.id);
            sorted.push(phase);
        };
        for (const phase of phases) {
            if (!visited.has(phase.id)) {
                visit(phase);
            }
        }
        return sorted;
    }
    /**
     * Envoie les instructions aux agents assignés
     */
    async dispatchInstructions(taskId, orchestrationPlan, assignedAgents) {
        for (const agentId of assignedAgents) {
            const agentTasks = orchestrationPlan.agentAssignments.get(agentId) || [];
            const agentPhases = orchestrationPlan.phases.filter((phase) => agentTasks.includes(phase.id));
            const instructions = {
                taskId,
                agentId,
                phases: agentPhases,
                timeline: orchestrationPlan.timeline.filter((item) => item.assignedAgents.includes(agentId)),
                priority: 'normal', // TODO: Récupérer la priorité réelle
                orchestrationId: orchestrationPlan.id
            };
            // Envoi via communication synaptique
            await this.communication.sendNeuralSignal({
                id: (0, uuid_1.v4)(),
                type: 'task-assignment',
                fromAgent: 'cortex-central',
                toAgent: agentId,
                payload: instructions,
                timestamp: new Date(),
                priority: 'normal'
            });
            logger_1.logger.debug(`📤 Instructions envoyées à l'agent ${agentId} pour la tâche ${taskId}`);
        }
    }
    /**
     * Calcule l'estimation de completion
     */
    calculateEstimatedCompletion(plan) {
        const totalDuration = plan.totalEstimatedTime || 0;
        return new Date(Date.now() + totalDuration * 60000); // minutes en ms
    }
    /**
     * Configuration des événements de communication
     */
    setupCommunicationEvents() {
        this.communication.on('task-completion', (data) => {
            this.handleTaskCompletion(data);
        });
        this.communication.on('agent-status-update', (data) => {
            this.handleAgentStatusUpdate(data);
        });
        this.communication.on('agent-error', (data) => {
            this.handleAgentError(data);
        });
    }
    /**
     * Gestion de la completion de tâches
     */
    async handleTaskCompletion(data) {
        const { taskId, agentId, success } = data;
        if (this.activeTasks.has(taskId)) {
            const task = this.activeTasks.get(taskId);
            if (success) {
                this.orchestrationMetrics.successfulOrchestrations++;
                // Calcul du temps de completion réel
                const actualDuration = Date.now() - task.startTime.getTime();
                this.updateAverageCompletionTime(actualDuration);
                logger_1.logger.info(`✅ Tâche ${taskId} complétée avec succès par l'agent ${agentId}`);
            }
            // Mise à jour de l'utilisation de l'agent
            this.updateAgentUtilization(agentId, -0.1); // Réduction de la charge
        }
        this.emit('task-completion-handled', {
            taskId,
            agentId,
            success,
            timestamp: new Date()
        });
    }
    /**
     * Gestion des mises à jour de statut d'agents
     */
    handleAgentStatusUpdate(data) {
        const { agentId, status, progress } = data;
        logger_1.logger.debug(`📊 Mise à jour statut agent ${agentId}: ${status} (${progress}%)`);
        this.emit('agent-status-updated', {
            agentId,
            status,
            progress,
            timestamp: new Date()
        });
    }
    /**
     * Gestion des erreurs d'agents
     */
    handleAgentError(data) {
        const { agentId, taskId, error } = data;
        logger_1.logger.error(`❌ Erreur agent ${agentId} sur tâche ${taskId}:`, error);
        // Tentative de réassignation si possible
        this.attemptTaskReassignment(taskId, agentId);
        this.emit('agent-error-handled', {
            agentId,
            taskId,
            error,
            timestamp: new Date()
        });
    }
    /**
     * Tentative de réassignation de tâche
     */
    async attemptTaskReassignment(taskId, failedAgentId) {
        try {
            const task = this.activeTasks.get(taskId);
            if (!task)
                return;
            // Recherche d'agents de remplacement
            const availableAgents = await this.getAvailableAgents();
            const failedAgent = this.neuralNetwork.getAgentById(failedAgentId);
            if (!failedAgent)
                return;
            const replacementAgents = availableAgents.filter(agent => agent.type === failedAgent.type && agent.id !== failedAgentId);
            if (replacementAgents.length > 0) {
                const replacementAgent = replacementAgents[0];
                // Mise à jour des assignations
                const index = task.assignedAgents.indexOf(failedAgentId);
                if (index !== -1) {
                    task.assignedAgents[index] = replacementAgent.id;
                }
                logger_1.logger.info(`🔄 Tâche ${taskId} réassignée de ${failedAgentId} à ${replacementAgent.id}`);
                this.emit('task-reassigned', {
                    taskId,
                    fromAgent: failedAgentId,
                    toAgent: replacementAgent.id,
                    timestamp: new Date()
                });
            }
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la réassignation de tâche:', error);
        }
    }
    /**
     * Met à jour le temps de completion moyen
     */
    updateAverageCompletionTime(actualDuration) {
        const currentAverage = this.orchestrationMetrics.averageCompletionTime;
        const totalTasks = this.orchestrationMetrics.successfulOrchestrations;
        this.orchestrationMetrics.averageCompletionTime =
            ((currentAverage * (totalTasks - 1)) + actualDuration) / totalTasks;
    }
    /**
     * Met à jour l'utilisation d'un agent
     */
    updateAgentUtilization(agentId, delta) {
        const currentUtilization = this.orchestrationMetrics.agentUtilization.get(agentId) || 0;
        const newUtilization = Math.max(0, Math.min(1, currentUtilization + delta));
        this.orchestrationMetrics.agentUtilization.set(agentId, newUtilization);
    }
    /**
     * Démarrage du monitoring d'orchestration
     */
    startOrchestrationMonitoring() {
        setInterval(() => {
            this.performOrchestrationHealthCheck();
        }, 60000); // Toutes les minutes
    }
    /**
     * Vérification de santé de l'orchestration
     */
    performOrchestrationHealthCheck() {
        const metrics = {
            activeTasks: this.activeTasks.size,
            totalTasks: this.orchestrationMetrics.totalTasks,
            successRate: this.orchestrationMetrics.successfulOrchestrations / Math.max(1, this.orchestrationMetrics.totalTasks),
            averageCompletionTime: this.orchestrationMetrics.averageCompletionTime,
            agentUtilization: Object.fromEntries(this.orchestrationMetrics.agentUtilization),
            timestamp: new Date()
        };
        this.emit('orchestration-health-check', metrics);
    }
    /**
     * Initialise la coordination multi-agents
     */
    async initializeAgentCoordination() {
        // Initialisation du pool de ressources
        this.resourcePool.set('cpu', 1.0);
        this.resourcePool.set('memory', 1.0);
        this.resourcePool.set('network', 1.0);
        this.resourcePool.set('storage', 1.0);
        // Configuration des coordinateurs d'agents
        const agentTypes = ['frontend', 'backend', 'uiux', 'qa', 'devops', 'security'];
        for (const agentType of agentTypes) {
            this.agentCoordinator.set(agentType, {
                type: agentType,
                maxConcurrentTasks: 3,
                currentTasks: 0,
                efficiency: 0.8,
                lastOptimization: new Date()
            });
        }
        logger_1.logger.info('🤝 Coordination multi-agents initialisée');
    }
    /**
     * Sélection et attribution intelligente des agents
     */
    async selectAndAssignAgentsIntelligent(plan, availableAgents, priority) {
        const assignedAgents = [];
        // Analyse des besoins en ressources
        const resourceRequirements = this.analyzeResourceRequirements(plan);
        // Optimisation de l'attribution basée sur l'efficacité
        for (const requirement of plan.requiredAgents) {
            const suitableAgents = availableAgents.filter(agent => {
                return this.matchesSpecialization(agent, requirement.agentType) &&
                    this.hasAvailableCapacity(agent.id, requirement.estimatedWorkload) &&
                    this.meetsResourceRequirements(agent.id, resourceRequirements);
            });
            if (suitableAgents.length === 0) {
                logger_1.logger.warn(`⚠️ Aucun agent disponible pour: ${requirement.agentType}`);
                continue;
            }
            // Sélection intelligente basée sur l'efficacité et la charge
            const selectedAgent = this.selectOptimalAgent(suitableAgents, requirement, priority);
            if (selectedAgent && !assignedAgents.includes(selectedAgent.id)) {
                assignedAgents.push(selectedAgent.id);
                // Mise à jour de la coordination
                await this.updateAgentCoordination(selectedAgent.id, requirement);
            }
        }
        return assignedAgents;
    }
    /**
     * Création d'un plan d'orchestration optimisé
     */
    async createOptimizedOrchestrationPlan(strategicPlan, assignedAgents) {
        const orchestrationPlan = {
            id: (0, uuid_1.v4)(),
            phases: [],
            agentAssignments: new Map(),
            dependencies: [],
            timeline: [],
            parallelGroups: [],
            resourceAllocation: new Map()
        };
        // Analyse des possibilités de parallélisation
        const parallelGroups = this.identifyParallelGroups(strategicPlan.phases);
        // Optimisation de l'ordre d'exécution
        const optimizedPhases = this.optimizeExecutionOrder(strategicPlan.phases, assignedAgents);
        // Allocation des ressources
        for (const phase of optimizedPhases) {
            const phaseAgents = assignedAgents.filter(agentId => {
                const agent = this.neuralNetwork.getAgentById(agentId);
                return agent && phase.requiredAgents.some((req) => req.agentType === agent.type);
            });
            const optimizedPhase = {
                ...phase,
                assignedAgents: phaseAgents,
                orchestrationId: (0, uuid_1.v4)(),
                resourceAllocation: this.allocateResources(phase, phaseAgents),
                parallelGroup: this.findParallelGroup(phase.id, parallelGroups)
            };
            orchestrationPlan.phases.push(optimizedPhase);
            // Enregistrement des assignations
            phaseAgents.forEach(agentId => {
                if (!orchestrationPlan.agentAssignments.has(agentId)) {
                    orchestrationPlan.agentAssignments.set(agentId, []);
                }
                orchestrationPlan.agentAssignments.get(agentId).push(phase.id);
            });
        }
        // Création de la timeline optimisée
        orchestrationPlan.timeline = this.createOptimizedTimeline(orchestrationPlan.phases, parallelGroups);
        orchestrationPlan.parallelGroups = parallelGroups;
        return orchestrationPlan;
    }
    /**
     * Analyse des dépendances et parallélisation
     */
    async analyzeDependenciesAndParallelize(taskId, orchestrationPlan) {
        // Construction du graphe de dépendances
        const dependencyGraph = this.buildDependencyGraph(orchestrationPlan.phases);
        this.taskDependencyGraph.set(taskId, dependencyGraph);
        // Identification des tâches parallélisables
        const parallelTasks = this.identifyParallelTasks(dependencyGraph);
        // Mise à jour des métriques
        this.orchestrationMetrics.parallelTasksCount = parallelTasks.length;
        logger_1.logger.info(`📊 Analyse des dépendances: ${parallelTasks.length} tâches parallélisables identifiées`);
    }
    /**
     * Envoi d'instructions avec coordination
     */
    async dispatchInstructionsWithCoordination(taskId, orchestrationPlan, assignedAgents) {
        // Coordination des agents pour éviter les conflits
        const coordinationMatrix = this.buildCoordinationMatrix(assignedAgents);
        for (const agentId of assignedAgents) {
            const agentTasks = orchestrationPlan.agentAssignments.get(agentId) || [];
            const agentPhases = orchestrationPlan.phases.filter((phase) => agentTasks.includes(phase.id));
            // Instructions enrichies avec coordination
            const instructions = {
                taskId,
                agentId,
                phases: agentPhases,
                timeline: orchestrationPlan.timeline.filter((item) => item.assignedAgents.includes(agentId)),
                coordination: {
                    matrix: coordinationMatrix,
                    dependencies: this.getAgentDependencies(agentId, orchestrationPlan),
                    resourceAllocation: orchestrationPlan.resourceAllocation.get(agentId)
                },
                priority: 'normal',
                orchestrationId: orchestrationPlan.id
            };
            // Envoi via communication synaptique avec coordination
            await this.communication.sendNeuralSignal({
                id: (0, uuid_1.v4)(),
                type: 'coordinated-task-assignment',
                fromAgent: 'cortex-central',
                toAgent: agentId,
                payload: instructions,
                timestamp: new Date(),
                priority: 'normal',
                coordination: true
            });
            logger_1.logger.debug(`🤝 Instructions coordonnées envoyées à l'agent ${agentId} pour la tâche ${taskId}`);
        }
    }
    /**
     * Analyse des exigences en ressources
     */
    analyzeResourceRequirements(plan) {
        const requirements = {
            cpu: 0,
            memory: 0,
            network: 0,
            storage: 0
        };
        for (const phase of plan.phases) {
            // Estimation basée sur la complexité et la durée
            const complexity = phase.complexity || 'medium';
            const duration = phase.estimatedDuration || 60;
            const multiplier = {
                'low': 0.3,
                'medium': 0.6,
                'high': 0.9,
                'critical': 1.2
            }[complexity] || 0.6;
            requirements.cpu += (duration / 60) * multiplier * 0.2;
            requirements.memory += (duration / 60) * multiplier * 0.15;
            requirements.network += (duration / 60) * multiplier * 0.1;
            requirements.storage += (duration / 60) * multiplier * 0.05;
        }
        return requirements;
    }
    /**
     * Vérifie la capacité disponible d'un agent
     */
    hasAvailableCapacity(agentId, requiredWorkload) {
        const currentUtilization = this.orchestrationMetrics.agentUtilization.get(agentId) || 0;
        return (currentUtilization + requiredWorkload) <= 0.9; // 90% max
    }
    /**
     * Vérifie si l'agent répond aux exigences en ressources
     */
    meetsResourceRequirements(agentId, requirements) {
        // Simulation - dans un vrai système, vérifier les ressources réelles
        return true;
    }
    /**
     * Sélectionne l'agent optimal
     */
    selectOptimalAgent(candidates, requirement, priority) {
        let bestAgent = candidates[0];
        let bestScore = 0;
        for (const agent of candidates) {
            let score = 0;
            // Score basé sur l'efficacité
            const coordinator = this.agentCoordinator.get(agent.type);
            if (coordinator) {
                score += coordinator.efficiency * 30;
            }
            // Score basé sur la charge actuelle
            const utilization = this.orchestrationMetrics.agentUtilization.get(agent.id) || 0;
            score += (1 - utilization) * 40;
            // Score basé sur la spécialisation
            if (this.matchesSpecialization(agent, requirement.agentType)) {
                score += 20;
            }
            // Score basé sur l'historique de performance
            const performanceScore = agent.performanceMetrics?.successRate || 0.5;
            score += performanceScore * 10;
            if (score > bestScore) {
                bestScore = score;
                bestAgent = agent;
            }
        }
        return bestAgent;
    }
    /**
     * Met à jour la coordination d'un agent
     */
    async updateAgentCoordination(agentId, requirement) {
        const agent = this.neuralNetwork.getAgentById(agentId);
        if (!agent)
            return;
        const coordinator = this.agentCoordinator.get(agent.type);
        if (coordinator) {
            coordinator.currentTasks++;
            // Mise à jour de l'utilisation
            const currentUtilization = this.orchestrationMetrics.agentUtilization.get(agentId) || 0;
            this.orchestrationMetrics.agentUtilization.set(agentId, Math.min(1.0, currentUtilization + (requirement.estimatedWorkload / 480)));
        }
    }
    /**
     * Démarre l'optimisation des ressources
     */
    startResourceOptimization() {
        setInterval(() => {
            this.optimizeResourceAllocation();
        }, 300000); // Toutes les 5 minutes
    }
    /**
     * Optimise l'allocation des ressources
     */
    optimizeResourceAllocation() {
        // Calcul de l'efficacité de coordination
        const totalAgents = this.agentCoordinator.size;
        const activeAgents = Array.from(this.agentCoordinator.values())
            .filter(coord => coord.currentTasks > 0).length;
        this.orchestrationMetrics.coordinationEfficiency = totalAgents > 0 ? activeAgents / totalAgents : 0;
        // Calcul de l'optimisation des ressources
        const totalUtilization = Array.from(this.orchestrationMetrics.agentUtilization.values())
            .reduce((sum, util) => sum + util, 0);
        const avgUtilization = totalUtilization / Math.max(1, this.orchestrationMetrics.agentUtilization.size);
        this.orchestrationMetrics.resourceOptimization = Math.min(1, avgUtilization * 1.2);
        this.emit('resource-optimization', {
            coordinationEfficiency: this.orchestrationMetrics.coordinationEfficiency,
            resourceOptimization: this.orchestrationMetrics.resourceOptimization,
            timestamp: new Date()
        });
    }
    /**
     * Récupère le statut de l'orchestrateur
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            activeTasks: this.activeTasks.size,
            metrics: {
                totalTasks: this.orchestrationMetrics.totalTasks,
                successfulOrchestrations: this.orchestrationMetrics.successfulOrchestrations,
                successRate: this.orchestrationMetrics.successfulOrchestrations / Math.max(1, this.orchestrationMetrics.totalTasks),
                averageCompletionTime: this.orchestrationMetrics.averageCompletionTime,
                agentUtilization: Object.fromEntries(this.orchestrationMetrics.agentUtilization)
            }
        };
    }
    /**
     * Arrêt gracieux
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt de l\'Orchestrateur de Tâches...');
        // Sauvegarde des tâches actives
        for (const [taskId, task] of this.activeTasks) {
            await this.memory.storeTaskStatus(taskId, task);
        }
        logger_1.logger.info('✅ Orchestrateur de Tâches arrêté');
    }
}
exports.TaskOrchestrator = TaskOrchestrator;
//# sourceMappingURL=TaskOrchestrator.js.map