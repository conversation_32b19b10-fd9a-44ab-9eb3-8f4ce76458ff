import { EventEmitter } from 'events';
import { Server as HTTPServer } from 'http';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { WorkflowOrchestrator } from '../orchestration/WorkflowOrchestrator';
import { CentralMemory } from '../memory/CentralMemory';
export interface DashboardData {
    timestamp: Date;
    system: {
        status: 'healthy' | 'warning' | 'critical';
        uptime: number;
        version: string;
        environment: string;
    };
    agents: {
        total: number;
        online: number;
        offline: number;
        byType: Record<string, number>;
        performance: {
            averageSuccessRate: number;
            averageResponseTime: number;
            totalTasksCompleted: number;
        };
    };
    workflows: {
        active: number;
        completed: number;
        failed: number;
        queued: number;
        recentActivity: any[];
    };
    neural: {
        connections: number;
        activity: number;
        health: number;
        recentSignals: any[];
    };
    metrics: {
        cpu: number;
        memory: number;
        network: {
            bytesIn: number;
            bytesOut: number;
        };
        requests: {
            total: number;
            rate: number;
        };
    };
}
export interface AlertData {
    id: string;
    type: 'info' | 'warning' | 'error' | 'critical';
    title: string;
    message: string;
    timestamp: Date;
    source: string;
    acknowledged: boolean;
    metadata?: any;
}
/**
 * Gestionnaire de Dashboard
 *
 * Fournit une interface en temps réel pour surveiller
 * l'état du système nerveux distribué
 */
export declare class DashboardManager extends EventEmitter {
    private io;
    private neuralNetwork;
    private workflowOrchestrator;
    private memory;
    private connectedClients;
    private alerts;
    private metricsHistory;
    private updateInterval;
    private startTime;
    constructor(httpServer: HTTPServer, neuralNetwork: NeuralNetworkManager, workflowOrchestrator: WorkflowOrchestrator, memory: CentralMemory);
    /**
     * Configuration des gestionnaires Socket.IO
     */
    private setupSocketHandlers;
    /**
     * Configuration des écouteurs d'événements système
     */
    private setupEventListeners;
    /**
     * Démarre la collecte de métriques
     */
    private startMetricsCollection;
    /**
     * Récupère les données actuelles du dashboard
     */
    private getCurrentDashboardData;
    /**
     * Calcule le statut global du système
     */
    private calculateSystemStatus;
    /**
     * Estimation de l'utilisation CPU (simulation)
     */
    private getCPUUsage;
    /**
     * Crée une nouvelle alerte
     */
    private createAlert;
    /**
     * Acquitte une alerte
     */
    private acknowledgeAlert;
    /**
     * Supprime une alerte
     */
    private clearAlert;
    /**
     * Diffuse une mise à jour à tous les clients connectés
     */
    private broadcastUpdate;
    /**
     * Récupère l'historique des métriques
     */
    getMetricsHistory(limit?: number): DashboardData[];
    /**
     * Récupère les alertes actives
     */
    getActiveAlerts(): AlertData[];
    /**
     * Récupère le nombre de clients connectés
     */
    getConnectedClientsCount(): number;
    /**
     * Envoie un message personnalisé à tous les clients
     */
    broadcast(event: string, data: any): void;
    /**
     * Envoie un message à un client spécifique
     */
    sendToClient(clientId: string, event: string, data: any): void;
    /**
     * Arrêt du gestionnaire de dashboard
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=DashboardManager.d.ts.map