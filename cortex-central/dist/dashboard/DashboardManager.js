"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardManager = void 0;
const events_1 = require("events");
const socket_io_1 = require("socket.io");
const logger_1 = require("../utils/logger");
/**
 * Gestionnaire de Dashboard
 *
 * Fournit une interface en temps réel pour surveiller
 * l'état du système nerveux distribué
 */
class DashboardManager extends events_1.EventEmitter {
    constructor(httpServer, neuralNetwork, workflowOrchestrator, memory) {
        super();
        this.connectedClients = new Set();
        this.alerts = new Map();
        this.metricsHistory = [];
        this.startTime = new Date();
        this.neuralNetwork = neuralNetwork;
        this.workflowOrchestrator = workflowOrchestrator;
        this.memory = memory;
        // Initialisation de Socket.IO
        this.io = new socket_io_1.Server(httpServer, {
            cors: {
                origin: process.env.DASHBOARD_CORS_ORIGIN || "*",
                methods: ["GET", "POST"]
            },
            path: '/dashboard/socket.io'
        });
        this.setupSocketHandlers();
        this.setupEventListeners();
        this.startMetricsCollection();
    }
    /**
     * Configuration des gestionnaires Socket.IO
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            const clientId = socket.id;
            this.connectedClients.add(clientId);
            logger_1.logger.info(`📊 Client dashboard connecté: ${clientId}`);
            // Envoi des données initiales
            socket.emit('initial-data', this.getCurrentDashboardData());
            socket.emit('alerts', Array.from(this.alerts.values()));
            // Gestionnaires d'événements client
            socket.on('request-data', () => {
                socket.emit('dashboard-data', this.getCurrentDashboardData());
            });
            socket.on('acknowledge-alert', (alertId) => {
                this.acknowledgeAlert(alertId);
            });
            socket.on('clear-alert', (alertId) => {
                this.clearAlert(alertId);
            });
            socket.on('request-agent-details', (agentId) => {
                const agent = this.neuralNetwork.getAgent(agentId);
                socket.emit('agent-details', agent);
            });
            socket.on('request-workflow-details', (workflowId) => {
                const workflow = this.workflowOrchestrator.getWorkflow(workflowId);
                socket.emit('workflow-details', workflow);
            });
            socket.on('start-workflow', async (templateId, context) => {
                try {
                    const workflowId = await this.workflowOrchestrator.createWorkflowFromTemplate(templateId, context, `dashboard-${clientId}`);
                    await this.workflowOrchestrator.startWorkflow(workflowId);
                    socket.emit('workflow-started', { workflowId });
                }
                catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            socket.on('pause-workflow', async (workflowId) => {
                try {
                    await this.workflowOrchestrator.pauseWorkflow(workflowId);
                    socket.emit('workflow-paused', { workflowId });
                }
                catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            socket.on('cancel-workflow', async (workflowId) => {
                try {
                    await this.workflowOrchestrator.cancelWorkflow(workflowId);
                    socket.emit('workflow-cancelled', { workflowId });
                }
                catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            socket.on('disconnect', () => {
                this.connectedClients.delete(clientId);
                logger_1.logger.info(`📊 Client dashboard déconnecté: ${clientId}`);
            });
        });
    }
    /**
     * Configuration des écouteurs d'événements système
     */
    setupEventListeners() {
        // Événements du réseau neuronal
        this.neuralNetwork.on('agent-connected', (agent) => {
            this.createAlert('info', 'Agent Connecté', `Agent ${agent.id} (${agent.type}) connecté`, 'neural-network');
            this.broadcastUpdate();
        });
        this.neuralNetwork.on('agent-disconnected', (agent) => {
            this.createAlert('warning', 'Agent Déconnecté', `Agent ${agent.id} (${agent.type}) déconnecté`, 'neural-network');
            this.broadcastUpdate();
        });
        this.neuralNetwork.on('agent-timeout', (agent) => {
            this.createAlert('error', 'Agent Non Responsif', `Agent ${agent.id} ne répond plus`, 'neural-network');
            this.broadcastUpdate();
        });
        // Événements des workflows
        this.workflowOrchestrator.on('workflow-started', (workflow) => {
            this.createAlert('info', 'Workflow Démarré', `Workflow "${workflow.name}" démarré`, 'orchestrator');
            this.broadcastUpdate();
        });
        this.workflowOrchestrator.on('workflow-completed', (workflow) => {
            this.createAlert('info', 'Workflow Terminé', `Workflow "${workflow.name}" terminé avec succès`, 'orchestrator');
            this.broadcastUpdate();
        });
        this.workflowOrchestrator.on('workflow-failed', ({ workflow, reason }) => {
            this.createAlert('error', 'Workflow Échoué', `Workflow "${workflow.name}" échoué: ${reason}`, 'orchestrator');
            this.broadcastUpdate();
        });
        this.workflowOrchestrator.on('step-completed', ({ workflow, step }) => {
            this.io.emit('step-update', { workflowId: workflow.id, step });
        });
    }
    /**
     * Démarre la collecte de métriques
     */
    startMetricsCollection() {
        this.updateInterval = setInterval(() => {
            const data = this.getCurrentDashboardData();
            this.metricsHistory.push(data);
            // Garder seulement les 100 dernières entrées
            if (this.metricsHistory.length > 100) {
                this.metricsHistory = this.metricsHistory.slice(-100);
            }
            this.broadcastUpdate();
        }, 5000); // Mise à jour toutes les 5 secondes
    }
    /**
     * Récupère les données actuelles du dashboard
     */
    getCurrentDashboardData() {
        const agents = this.neuralNetwork.getConnectedAgents();
        const workflows = this.workflowOrchestrator.getActiveWorkflows();
        const neuralStatus = this.neuralNetwork.getStatus();
        const orchestratorStatus = this.workflowOrchestrator.getStatus();
        // Calcul des métriques d'agents
        const onlineAgents = agents.filter(a => a.status === 'online');
        const agentsByType = agents.reduce((acc, agent) => {
            acc[agent.type] = (acc[agent.type] || 0) + 1;
            return acc;
        }, {});
        const avgSuccessRate = agents.length > 0
            ? agents.reduce((sum, a) => sum + a.performance.successRate, 0) / agents.length
            : 0;
        const avgResponseTime = agents.length > 0
            ? agents.reduce((sum, a) => sum + a.performance.averageResponseTime, 0) / agents.length
            : 0;
        const totalTasksCompleted = agents.reduce((sum, a) => sum + a.performance.tasksCompleted, 0);
        // Calcul des métriques de workflows
        const completedWorkflows = workflows.filter(w => w.status === 'completed').length;
        const failedWorkflows = workflows.filter(w => w.status === 'failed').length;
        const recentActivity = workflows
            .filter(w => w.startedAt && w.startedAt > new Date(Date.now() - 3600000)) // Dernière heure
            .map(w => ({
            id: w.id,
            name: w.name,
            status: w.status,
            startedAt: w.startedAt,
            completedAt: w.completedAt,
            duration: w.duration
        }));
        // Métriques système
        const memUsage = process.memoryUsage();
        const uptime = Date.now() - this.startTime.getTime();
        return {
            timestamp: new Date(),
            system: {
                status: this.calculateSystemStatus(agents, workflows),
                uptime: Math.floor(uptime / 1000),
                version: process.env.npm_package_version || '1.0.0',
                environment: process.env.NODE_ENV || 'development'
            },
            agents: {
                total: agents.length,
                online: onlineAgents.length,
                offline: agents.length - onlineAgents.length,
                byType: agentsByType,
                performance: {
                    averageSuccessRate: Math.round(avgSuccessRate * 100) / 100,
                    averageResponseTime: Math.round(avgResponseTime),
                    totalTasksCompleted
                }
            },
            workflows: {
                active: orchestratorStatus.activeWorkflows,
                completed: completedWorkflows,
                failed: failedWorkflows,
                queued: orchestratorStatus.queuedWorkflows,
                recentActivity
            },
            neural: {
                connections: neuralStatus.synapticConnections,
                activity: neuralStatus.recentActivity,
                health: neuralStatus.networkHealth,
                recentSignals: this.neuralNetwork.getNeuralActivity(10)
            },
            metrics: {
                cpu: this.getCPUUsage(),
                memory: Math.round(memUsage.heapUsed / 1024 / 1024),
                network: {
                    bytesIn: 0, // À implémenter avec des métriques réseau réelles
                    bytesOut: 0
                },
                requests: {
                    total: 0, // À implémenter avec des compteurs de requêtes
                    rate: 0
                }
            }
        };
    }
    /**
     * Calcule le statut global du système
     */
    calculateSystemStatus(agents, workflows) {
        const onlineAgents = agents.filter(a => a.status === 'online').length;
        const agentHealthRatio = agents.length > 0 ? onlineAgents / agents.length : 1;
        const failedWorkflows = workflows.filter(w => w.status === 'failed').length;
        const workflowFailureRatio = workflows.length > 0 ? failedWorkflows / workflows.length : 0;
        if (agentHealthRatio < 0.5 || workflowFailureRatio > 0.3) {
            return 'critical';
        }
        else if (agentHealthRatio < 0.8 || workflowFailureRatio > 0.1) {
            return 'warning';
        }
        else {
            return 'healthy';
        }
    }
    /**
     * Estimation de l'utilisation CPU (simulation)
     */
    getCPUUsage() {
        // Simulation basée sur l'activité du système
        const agents = this.neuralNetwork.getConnectedAgents();
        const workflows = this.workflowOrchestrator.getActiveWorkflows();
        const baseUsage = 10; // Usage de base
        const agentUsage = agents.length * 2;
        const workflowUsage = workflows.filter(w => w.status === 'running').length * 5;
        return Math.min(100, baseUsage + agentUsage + workflowUsage + Math.random() * 10);
    }
    /**
     * Crée une nouvelle alerte
     */
    createAlert(type, title, message, source, metadata) {
        const alert = {
            id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            type,
            title,
            message,
            timestamp: new Date(),
            source,
            acknowledged: false,
            metadata
        };
        this.alerts.set(alert.id, alert);
        // Garder seulement les 50 dernières alertes
        if (this.alerts.size > 50) {
            const oldestAlert = Array.from(this.alerts.keys())[0];
            this.alerts.delete(oldestAlert);
        }
        this.io.emit('new-alert', alert);
        logger_1.logger.info(`🚨 Alerte créée: ${title} - ${message}`);
    }
    /**
     * Acquitte une alerte
     */
    acknowledgeAlert(alertId) {
        const alert = this.alerts.get(alertId);
        if (alert) {
            alert.acknowledged = true;
            this.io.emit('alert-acknowledged', alert);
        }
    }
    /**
     * Supprime une alerte
     */
    clearAlert(alertId) {
        if (this.alerts.delete(alertId)) {
            this.io.emit('alert-cleared', alertId);
        }
    }
    /**
     * Diffuse une mise à jour à tous les clients connectés
     */
    broadcastUpdate() {
        const data = this.getCurrentDashboardData();
        this.io.emit('dashboard-data', data);
    }
    /**
     * Récupère l'historique des métriques
     */
    getMetricsHistory(limit = 50) {
        return this.metricsHistory.slice(-limit);
    }
    /**
     * Récupère les alertes actives
     */
    getActiveAlerts() {
        return Array.from(this.alerts.values()).filter(alert => !alert.acknowledged);
    }
    /**
     * Récupère le nombre de clients connectés
     */
    getConnectedClientsCount() {
        return this.connectedClients.size;
    }
    /**
     * Envoie un message personnalisé à tous les clients
     */
    broadcast(event, data) {
        this.io.emit(event, data);
    }
    /**
     * Envoie un message à un client spécifique
     */
    sendToClient(clientId, event, data) {
        this.io.to(clientId).emit(event, data);
    }
    /**
     * Arrêt du gestionnaire de dashboard
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du gestionnaire de dashboard...');
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        // Notification aux clients
        this.io.emit('system-shutdown', {
            message: 'Le système va s\'arrêter',
            timestamp: new Date()
        });
        // Fermeture des connexions
        this.io.close();
        logger_1.logger.info('✅ Gestionnaire de dashboard arrêté');
    }
}
exports.DashboardManager = DashboardManager;
//# sourceMappingURL=DashboardManager.js.map