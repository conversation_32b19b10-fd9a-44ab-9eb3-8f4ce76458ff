import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface DecisionConfig {
    memory: CentralMemory;
    communication: SynapticCommunication;
    learningSystem?: any;
}
export interface CognitiveAnalysis {
    complexity: 'low' | 'medium' | 'high' | 'critical';
    domains: string[];
    requiredSkills: string[];
    estimatedDuration: number;
    dependencies: string[];
    riskLevel: number;
    confidence: number;
}
export interface StrategicPlan {
    id: string;
    phases: PlanPhase[];
    totalEstimatedTime: number;
    requiredAgents: AgentRequirement[];
    riskMitigation: RiskMitigation[];
    successCriteria: string[];
    fallbackStrategies: string[];
}
export interface PlanPhase {
    id: string;
    name: string;
    description: string;
    order: number;
    estimatedDuration: number;
    requiredAgents: string[];
    dependencies: string[];
    deliverables: string[];
    successCriteria: string[];
}
export interface AgentRequirement {
    agentType: 'frontend' | 'backend' | 'devops' | 'qa' | 'security';
    specialization: string;
    priority: 'required' | 'preferred' | 'optional';
    estimatedWorkload: number;
    skills: string[];
}
export interface RiskMitigation {
    risk: string;
    probability: number;
    impact: number;
    mitigation: string;
    contingency: string;
}
/**
 * Moteur de Décision - Planificateur Intelligent
 *
 * Analyse les requêtes complexes et crée des plans d'exécution stratégiques
 * en utilisant l'intelligence artificielle et l'apprentissage des expériences passées.
 */
export declare class DecisionEngine extends EventEmitter {
    private memory;
    private communication;
    private learningSystem;
    private isInitialized;
    private decisionPatterns;
    private decisionAlgorithms;
    private performanceMetrics;
    constructor(config: DecisionConfig);
    /**
     * Initialise le moteur de décision
     */
    initialize(): Promise<void>;
    /**
     * Crée un plan stratégique basé sur l'analyse cognitive
     */
    createStrategicPlan(request: {
        analysis: CognitiveAnalysis;
        priority: string;
        context?: any;
    }): Promise<StrategicPlan>;
    /**
     * Trouve des patterns similaires dans la mémoire
     */
    private findSimilarPatterns;
    /**
     * Décompose les tâches en phases exécutables
     */
    private decomposeTasks;
    /**
     * Crée des phases simples pour les tâches de faible complexité
     */
    private createSimplePhases;
    /**
     * Sélectionne les agents appropriés pour les domaines donnés
     */
    private selectAgentsForDomains;
    /**
     * Sélectionne les agents appropriés pour les compétences données
     */
    private selectAgentsForSkills;
    /**
     * Analyse les risques du plan
     */
    private analyzeRisks;
    /**
     * Définit les critères de succès
     */
    private defineSuccessCriteria;
    /**
     * Crée des stratégies de fallback
     */
    private createFallbackStrategies;
    /**
     * Optimise une phase avec les patterns existants
     */
    private optimizePhaseWithPatterns;
    /**
     * Valide les dépendances entre phases
     */
    private validatePhaseDependencies;
    /**
     * Charge les patterns de décision depuis la mémoire
     */
    private loadDecisionPatterns;
    /**
     * Configure les événements d'apprentissage
     */
    private setupLearningEvents;
    /**
     * Apprend des succès de tâches
     */
    private learnFromTaskCompletion;
    /**
     * Apprend des échecs de tâches
     */
    private learnFromTaskFailure;
    /**
     * Démarrage de l'auto-amélioration
     */
    private startSelfImprovement;
    /**
     * Exécute l'auto-amélioration
     */
    private performSelfImprovement;
    /**
     * Met à jour les algorithmes de décision
     */
    private updateDecisionAlgorithms;
    /**
     * Trouve les échecs communs
     */
    private findCommonFailures;
    /**
     * Met à jour les stratégies de mitigation
     */
    private updateMitigationStrategies;
    /**
     * Initialise les algorithmes de décision
     */
    private initializeDecisionAlgorithms;
    /**
     * Identifie les agents requis avec intelligence
     */
    private identifyRequiredAgentsIntelligent;
    /**
     * Sélectionne le meilleur algorithme de décision
     */
    private selectBestAlgorithm;
    /**
     * Algorithme de décision basé sur la complexité
     */
    private complexityBasedDecision;
    /**
     * Algorithme de décision basé sur les ressources
     */
    private resourceBasedDecision;
    /**
     * Algorithme de décision adaptatif
     */
    private adaptiveDecision;
    /**
     * Algorithme de décision optimisé
     */
    private optimizedDecision;
    /**
     * Adapte le plan basé sur l'apprentissage
     */
    private adaptPlanFromLearning;
    /**
     * Calcule la complexité d'un domaine
     */
    private calculateDomainComplexity;
    /**
     * Obtient les ressources disponibles
     */
    private getAvailableResources;
    /**
     * Calcule les exigences en ressources
     */
    private calculateResourceRequirement;
    /**
     * Obtient les décisions historiques
     */
    private getHistoricalDecisions;
    /**
     * Obtient les agents candidats
     */
    private getCandidateAgents;
    /**
     * Optimise la sélection selon les critères
     */
    private optimizeSelection;
    /**
     * Récupère le statut du moteur de décision
     */
    getStatus(): any;
    /**
     * Arrêt gracieux
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=DecisionEngine.d.ts.map