"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecisionEngine = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
/**
 * Moteur de Décision - Planificateur Intelligent
 *
 * Analyse les requêtes complexes et crée des plans d'exécution stratégiques
 * en utilisant l'intelligence artificielle et l'apprentissage des expériences passées.
 */
class DecisionEngine extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        // Patterns de décision appris
        this.decisionPatterns = new Map();
        // Algorithmes de décision avancés
        this.decisionAlgorithms = new Map();
        // Métriques de performance
        this.performanceMetrics = {
            totalDecisions: 0,
            successfulPlans: 0,
            averageAccuracy: 0,
            learningRate: 0.1,
            adaptiveDecisions: 0,
            optimizationRate: 0
        };
        this.memory = config.memory;
        this.communication = config.communication;
        this.learningSystem = config.learningSystem;
        this.initializeDecisionAlgorithms();
    }
    /**
     * Initialise le moteur de décision
     */
    async initialize() {
        try {
            logger_1.logger.info('🎯 Initialisation du Moteur de Décision...');
            // Chargement des patterns de décision depuis la mémoire
            await this.loadDecisionPatterns();
            // Configuration des événements d'apprentissage
            this.setupLearningEvents();
            // Démarrage de l'auto-amélioration
            this.startSelfImprovement();
            this.isInitialized = true;
            logger_1.logger.info('✅ Moteur de Décision initialisé');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Moteur de Décision:', error);
            throw error;
        }
    }
    /**
     * Crée un plan stratégique basé sur l'analyse cognitive
     */
    async createStrategicPlan(request) {
        try {
            logger_1.logger.info('🎯 Création d\'un plan stratégique...');
            const { analysis, priority, context } = request;
            // Génération de l'ID du plan
            const planId = `plan-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            // Analyse des patterns similaires
            const similarPatterns = await this.findSimilarPatterns(analysis);
            // Décomposition en phases
            const phases = await this.decomposeTasks(analysis, similarPatterns);
            // Identification des agents requis avec optimisation intelligente
            const requiredAgents = await this.identifyRequiredAgentsIntelligent(analysis, phases);
            // Application de l'apprentissage adaptatif
            if (this.learningSystem) {
                const prediction = await this.learningSystem.predictBestAction(analysis, 'decision-making');
                if (prediction.confidence > 0.7) {
                    // Ajustement du plan basé sur l'apprentissage
                    await this.adaptPlanFromLearning(phases, prediction);
                    this.performanceMetrics.adaptiveDecisions++;
                }
            }
            // Analyse des risques
            const riskMitigation = await this.analyzeRisks(analysis, phases);
            // Calcul du temps total estimé
            const totalEstimatedTime = phases.reduce((total, phase) => total + phase.estimatedDuration, 0);
            // Définition des critères de succès
            const successCriteria = await this.defineSuccessCriteria(analysis, phases);
            // Stratégies de fallback
            const fallbackStrategies = await this.createFallbackStrategies(analysis, phases);
            const strategicPlan = {
                id: planId,
                phases,
                totalEstimatedTime,
                requiredAgents,
                riskMitigation,
                successCriteria,
                fallbackStrategies
            };
            // Stockage du plan en mémoire
            await this.memory.storeGlobalPattern({
                id: planId,
                content: strategicPlan,
                domain: 'strategic-planning',
                crossDomainRelevance: 0.8,
                timestamp: new Date(),
                version: '1.0',
                metadata: {
                    complexity: analysis.complexity,
                    priority,
                    context
                }
            });
            // Mise à jour des métriques
            this.performanceMetrics.totalDecisions++;
            logger_1.logger.info(`✅ Plan stratégique créé: ${planId} (${phases.length} phases, ${totalEstimatedTime}min estimé)`);
            this.emit('strategic-plan-created', {
                planId,
                complexity: analysis.complexity,
                phases: phases.length,
                estimatedTime: totalEstimatedTime,
                timestamp: new Date()
            });
            return strategicPlan;
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la création du plan stratégique:', error);
            throw error;
        }
    }
    /**
     * Trouve des patterns similaires dans la mémoire
     */
    async findSimilarPatterns(analysis) {
        try {
            // Recherche de plans similaires basée sur:
            // - Complexité
            // - Domaines
            // - Compétences requises
            const searchQuery = `complexity:${analysis.complexity} domains:${analysis.domains.join(' ')} skills:${analysis.requiredSkills.join(' ')}`;
            const patterns = await this.memory.queryAcrossDomains(searchQuery, 5);
            logger_1.logger.debug(`🔍 ${patterns.length} patterns similaires trouvés`);
            return patterns;
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la recherche de patterns:', error);
            return [];
        }
    }
    /**
     * Décompose les tâches en phases exécutables
     */
    async decomposeTasks(analysis, similarPatterns) {
        const phases = [];
        // Logique de décomposition basée sur la complexité et les domaines
        switch (analysis.complexity) {
            case 'low':
                phases.push(...await this.createSimplePhases(analysis));
                break;
            case 'medium':
                phases.push(...await this.createMediumPhases(analysis));
                break;
            case 'high':
                phases.push(...await this.createComplexPhases(analysis));
                break;
            case 'critical':
                phases.push(...await this.createCriticalPhases(analysis));
                break;
        }
        // Optimisation basée sur les patterns similaires
        if (similarPatterns.length > 0) {
            phases.forEach(phase => {
                this.optimizePhaseWithPatterns(phase, similarPatterns);
            });
        }
        // Validation des dépendances
        this.validatePhaseDependencies(phases);
        return phases;
    }
    /**
     * Crée des phases simples pour les tâches de faible complexité
     */
    async createSimplePhases(analysis) {
        const phases = [];
        // Phase d'analyse
        phases.push({
            id: 'analysis-phase',
            name: 'Analyse et Préparation',
            description: 'Analyse détaillée des exigences et préparation de l\'environnement',
            order: 1,
            estimatedDuration: Math.max(5, analysis.estimatedDuration * 0.2),
            requiredAgents: this.selectAgentsForDomains(analysis.domains),
            dependencies: [],
            deliverables: ['Spécifications détaillées', 'Environnement préparé'],
            successCriteria: ['Exigences clarifiées', 'Environnement opérationnel']
        });
        // Phase d'implémentation
        phases.push({
            id: 'implementation-phase',
            name: 'Implémentation',
            description: 'Développement et implémentation de la solution',
            order: 2,
            estimatedDuration: Math.max(10, analysis.estimatedDuration * 0.6),
            requiredAgents: this.selectAgentsForSkills(analysis.requiredSkills),
            dependencies: ['analysis-phase'],
            deliverables: ['Code implémenté', 'Documentation'],
            successCriteria: ['Fonctionnalités implémentées', 'Code testé']
        });
        // Phase de validation
        phases.push({
            id: 'validation-phase',
            name: 'Validation et Tests',
            description: 'Tests et validation de la solution',
            order: 3,
            estimatedDuration: Math.max(5, analysis.estimatedDuration * 0.2),
            requiredAgents: ['qa'],
            dependencies: ['implementation-phase'],
            deliverables: ['Tests exécutés', 'Rapport de validation'],
            successCriteria: ['Tests passés', 'Solution validée']
        });
        return phases;
    }
    /**
     * Sélectionne les agents appropriés pour les domaines donnés
     */
    selectAgentsForDomains(domains) {
        const agents = [];
        domains.forEach(domain => {
            switch (domain.toLowerCase()) {
                case 'frontend':
                case 'ui':
                case 'interface':
                    if (!agents.includes('frontend'))
                        agents.push('frontend');
                    break;
                case 'backend':
                case 'api':
                case 'server':
                    if (!agents.includes('backend'))
                        agents.push('backend');
                    break;
                case 'infrastructure':
                case 'deployment':
                case 'devops':
                    if (!agents.includes('devops'))
                        agents.push('devops');
                    break;
                case 'testing':
                case 'quality':
                case 'qa':
                    if (!agents.includes('qa'))
                        agents.push('qa');
                    break;
                default:
                    // Pour les domaines non spécifiés, ajouter backend par défaut
                    if (!agents.includes('backend'))
                        agents.push('backend');
            }
        });
        return agents.length > 0 ? agents : ['backend']; // Au moins un agent
    }
    /**
     * Sélectionne les agents appropriés pour les compétences données
     */
    selectAgentsForSkills(skills) {
        const agents = [];
        skills.forEach(skill => {
            const skillLower = skill.toLowerCase();
            if (skillLower.includes('react') || skillLower.includes('vue') || skillLower.includes('angular') ||
                skillLower.includes('css') || skillLower.includes('html') || skillLower.includes('ui')) {
                if (!agents.includes('frontend'))
                    agents.push('frontend');
            }
            if (skillLower.includes('node') || skillLower.includes('python') || skillLower.includes('java') ||
                skillLower.includes('api') || skillLower.includes('database') || skillLower.includes('sql')) {
                if (!agents.includes('backend'))
                    agents.push('backend');
            }
            if (skillLower.includes('docker') || skillLower.includes('kubernetes') || skillLower.includes('aws') ||
                skillLower.includes('deployment') || skillLower.includes('ci/cd')) {
                if (!agents.includes('devops'))
                    agents.push('devops');
            }
            if (skillLower.includes('test') || skillLower.includes('quality') || skillLower.includes('automation')) {
                if (!agents.includes('qa'))
                    agents.push('qa');
            }
        });
        return agents.length > 0 ? agents : ['backend']; // Au moins un agent
    }
    /**
     * Analyse les risques du plan
     */
    async analyzeRisks(analysis, phases) {
        const risks = [];
        // Risque de complexité
        if (analysis.complexity === 'high' || analysis.complexity === 'critical') {
            risks.push({
                risk: 'Complexité technique élevée',
                probability: 0.7,
                impact: 0.8,
                mitigation: 'Prototypage et validation par étapes',
                contingency: 'Simplification de l\'architecture si nécessaire'
            });
        }
        // Risque de dépendances
        if (analysis.dependencies.length > 3) {
            risks.push({
                risk: 'Dépendances multiples',
                probability: 0.6,
                impact: 0.7,
                mitigation: 'Gestion proactive des dépendances',
                contingency: 'Solutions alternatives identifiées'
            });
        }
        // Risque de délais
        const totalDuration = phases.reduce((sum, phase) => sum + phase.estimatedDuration, 0);
        if (totalDuration > 480) { // Plus de 8 heures
            risks.push({
                risk: 'Dépassement de délais',
                probability: 0.5,
                impact: 0.6,
                mitigation: 'Suivi régulier et ajustements',
                contingency: 'Priorisation des fonctionnalités critiques'
            });
        }
        return risks;
    }
    /**
     * Définit les critères de succès
     */
    async defineSuccessCriteria(analysis, phases) {
        const criteria = [
            'Toutes les phases complétées avec succès',
            'Critères de qualité respectés',
            'Tests passés avec succès'
        ];
        // Critères spécifiques à la complexité
        switch (analysis.complexity) {
            case 'critical':
                criteria.push('Audit de sécurité validé', 'Plan de contingence testé');
                break;
            case 'high':
                criteria.push('Performance validée', 'Architecture documentée');
                break;
            case 'medium':
                criteria.push('Intégration réussie', 'Documentation complète');
                break;
            case 'low':
                criteria.push('Fonctionnalités implémentées', 'Tests unitaires passés');
                break;
        }
        return criteria;
    }
    /**
     * Crée des stratégies de fallback
     */
    async createFallbackStrategies(analysis, phases) {
        const strategies = [
            'Réduction du scope si nécessaire',
            'Extension des délais avec approbation',
            'Allocation d\'agents supplémentaires'
        ];
        // Stratégies spécifiques
        if (analysis.complexity === 'high' || analysis.complexity === 'critical') {
            strategies.push('Implémentation par phases avec validation', 'Architecture simplifiée en cas de blocage', 'Plan de rollback complet');
        }
        return strategies;
    }
    /**
     * Optimise une phase avec les patterns existants
     */
    optimizePhaseWithPatterns(phase, patterns) {
        // Optimisation basée sur les patterns historiques
        patterns.forEach(pattern => {
            if (pattern.content && pattern.content.phases) {
                const similarPhase = pattern.content.phases.find((p) => p.name.toLowerCase().includes(phase.name.toLowerCase().split(' ')[0]));
                if (similarPhase && similarPhase.estimatedDuration) {
                    // Ajustement de la durée basé sur l'historique
                    const historicalDuration = similarPhase.estimatedDuration;
                    phase.estimatedDuration = Math.round((phase.estimatedDuration + historicalDuration) / 2);
                }
            }
        });
    }
    /**
     * Valide les dépendances entre phases
     */
    validatePhaseDependencies(phases) {
        const phaseIds = new Set(phases.map(p => p.id));
        phases.forEach(phase => {
            phase.dependencies.forEach(depId => {
                if (!phaseIds.has(depId)) {
                    logger_1.logger.warn(`⚠️ Dépendance manquante: ${depId} pour la phase ${phase.id}`);
                    // Retirer la dépendance invalide
                    phase.dependencies = phase.dependencies.filter(id => id !== depId);
                }
            });
        });
    }
    /**
     * Charge les patterns de décision depuis la mémoire
     */
    async loadDecisionPatterns() {
        try {
            const patterns = await this.memory.queryAcrossDomains('decision-pattern', 50);
            patterns.forEach(pattern => {
                this.decisionPatterns.set(pattern.id, pattern);
            });
            logger_1.logger.info(`📚 ${patterns.length} patterns de décision chargés`);
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors du chargement des patterns:', error);
        }
    }
    /**
     * Configure les événements d'apprentissage
     */
    setupLearningEvents() {
        this.communication.on('task-completion', (data) => {
            this.learnFromTaskCompletion(data);
        });
        this.communication.on('task-failure', (data) => {
            this.learnFromTaskFailure(data);
        });
    }
    /**
     * Apprend des succès de tâches
     */
    async learnFromTaskCompletion(data) {
        try {
            this.performanceMetrics.successfulPlans++;
            this.performanceMetrics.averageAccuracy =
                this.performanceMetrics.successfulPlans / this.performanceMetrics.totalDecisions;
            // Stockage du pattern de succès
            await this.memory.storeGlobalPattern({
                id: `success-pattern-${data.taskId}`,
                content: {
                    type: 'success-pattern',
                    taskId: data.taskId,
                    result: data.result,
                    actualDuration: data.actualDuration,
                    estimatedDuration: data.estimatedDuration
                },
                domain: 'decision-learning',
                crossDomainRelevance: 0.9,
                timestamp: new Date(),
                version: '1.0',
                metadata: {
                    success: true,
                    accuracy: data.actualDuration / data.estimatedDuration
                }
            });
            this.emit('learning-update', {
                type: 'success',
                taskId: data.taskId,
                accuracy: this.performanceMetrics.averageAccuracy,
                timestamp: new Date()
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'apprentissage du succès:', error);
        }
    }
    /**
     * Apprend des échecs de tâches
     */
    async learnFromTaskFailure(data) {
        try {
            // Analyse de l'échec pour amélioration future
            await this.memory.storeGlobalPattern({
                id: `failure-pattern-${data.taskId}`,
                content: {
                    type: 'failure-pattern',
                    taskId: data.taskId,
                    error: data.error,
                    phase: data.failedPhase,
                    reason: data.reason
                },
                domain: 'decision-learning',
                crossDomainRelevance: 0.8,
                timestamp: new Date(),
                version: '1.0',
                metadata: {
                    success: false,
                    failureReason: data.reason
                }
            });
            this.emit('learning-update', {
                type: 'failure',
                taskId: data.taskId,
                reason: data.reason,
                timestamp: new Date()
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'apprentissage de l\'échec:', error);
        }
    }
    /**
     * Démarrage de l'auto-amélioration
     */
    startSelfImprovement() {
        setInterval(async () => {
            await this.performSelfImprovement();
        }, 3600000); // Toutes les heures
    }
    /**
     * Exécute l'auto-amélioration
     */
    async performSelfImprovement() {
        try {
            // Analyse des patterns de succès et d'échec
            const successPatterns = await this.memory.queryAcrossDomains('success-pattern', 20);
            const failurePatterns = await this.memory.queryAcrossDomains('failure-pattern', 20);
            // Mise à jour des algorithmes de décision basée sur l'apprentissage
            this.updateDecisionAlgorithms(successPatterns, failurePatterns);
            logger_1.logger.info('🔧 Auto-amélioration du moteur de décision effectuée');
            this.emit('self-improvement', {
                successPatterns: successPatterns.length,
                failurePatterns: failurePatterns.length,
                accuracy: this.performanceMetrics.averageAccuracy,
                timestamp: new Date()
            });
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'auto-amélioration:', error);
        }
    }
    /**
     * Met à jour les algorithmes de décision
     */
    updateDecisionAlgorithms(successPatterns, failurePatterns) {
        // Analyse des patterns pour améliorer les estimations
        successPatterns.forEach(pattern => {
            if (pattern.metadata && pattern.metadata.accuracy) {
                const accuracy = pattern.metadata.accuracy;
                // Ajustement du taux d'apprentissage basé sur la précision
                this.performanceMetrics.learningRate = Math.min(0.2, this.performanceMetrics.learningRate * (1 + (accuracy - 1) * 0.1));
            }
        });
        // Identification des causes d'échec communes
        const failureReasons = failurePatterns.map(p => p.metadata?.failureReason).filter(Boolean);
        const commonFailures = this.findCommonFailures(failureReasons);
        // Mise à jour des stratégies de mitigation
        commonFailures.forEach(failure => {
            this.updateMitigationStrategies(failure);
        });
    }
    /**
     * Trouve les échecs communs
     */
    findCommonFailures(reasons) {
        const reasonCounts = {};
        reasons.forEach(reason => {
            reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
        });
        return Object.entries(reasonCounts)
            .filter(([_, count]) => count >= 2)
            .map(([reason, _]) => reason);
    }
    /**
     * Met à jour les stratégies de mitigation
     */
    updateMitigationStrategies(failureReason) {
        // Logique d'amélioration des stratégies basée sur les échecs
        logger_1.logger.debug(`🔧 Mise à jour des stratégies pour: ${failureReason}`);
    }
    /**
     * Initialise les algorithmes de décision
     */
    initializeDecisionAlgorithms() {
        // Algorithme de décision basé sur la complexité
        this.decisionAlgorithms.set('complexity-based', this.complexityBasedDecision.bind(this));
        // Algorithme de décision basé sur les ressources
        this.decisionAlgorithms.set('resource-based', this.resourceBasedDecision.bind(this));
        // Algorithme de décision adaptatif
        this.decisionAlgorithms.set('adaptive', this.adaptiveDecision.bind(this));
        // Algorithme de décision optimisé
        this.decisionAlgorithms.set('optimized', this.optimizedDecision.bind(this));
    }
    /**
     * Identifie les agents requis avec intelligence
     */
    async identifyRequiredAgentsIntelligent(analysis, phases) {
        const requiredAgents = [];
        // Utilisation d'algorithmes de décision avancés
        const algorithm = this.selectBestAlgorithm(analysis);
        const agentRecommendations = await algorithm(analysis, phases);
        for (const recommendation of agentRecommendations) {
            requiredAgents.push({
                agentType: recommendation.type,
                priority: recommendation.priority,
                estimatedWorkload: recommendation.workload,
                requiredSkills: recommendation.skills,
                dependencies: recommendation.dependencies || []
            });
        }
        return requiredAgents;
    }
    /**
     * Sélectionne le meilleur algorithme de décision
     */
    selectBestAlgorithm(analysis) {
        // Logique de sélection basée sur le contexte
        if (analysis.complexity === 'critical') {
            return this.decisionAlgorithms.get('optimized');
        }
        else if (analysis.riskLevel > 0.7) {
            return this.decisionAlgorithms.get('adaptive');
        }
        else if (analysis.domains.length > 3) {
            return this.decisionAlgorithms.get('complexity-based');
        }
        else {
            return this.decisionAlgorithms.get('resource-based');
        }
    }
    /**
     * Algorithme de décision basé sur la complexité
     */
    async complexityBasedDecision(analysis, phases) {
        const recommendations = [];
        // Analyse de la complexité pour chaque domaine
        for (const domain of analysis.domains) {
            const complexity = this.calculateDomainComplexity(domain, analysis);
            if (complexity > 0.8) {
                recommendations.push({
                    type: `agent-${domain}`,
                    priority: 'high',
                    workload: complexity,
                    skills: analysis.requiredSkills.filter(skill => skill.includes(domain)),
                    reasoning: `Complexité élevée détectée pour ${domain}`
                });
            }
        }
        return recommendations;
    }
    /**
     * Algorithme de décision basé sur les ressources
     */
    async resourceBasedDecision(analysis, phases) {
        const recommendations = [];
        // Analyse des ressources disponibles
        const availableResources = await this.getAvailableResources();
        for (const phase of phases) {
            const resourceRequirement = this.calculateResourceRequirement(phase);
            if (availableResources[phase.name] >= resourceRequirement) {
                recommendations.push({
                    type: `agent-${phase.name}`,
                    priority: 'medium',
                    workload: resourceRequirement / availableResources[phase.name],
                    skills: phase.requiredSkills || [],
                    reasoning: `Ressources suffisantes pour ${phase.name}`
                });
            }
        }
        return recommendations;
    }
    /**
     * Algorithme de décision adaptatif
     */
    async adaptiveDecision(analysis, phases) {
        const recommendations = [];
        // Utilisation de l'historique pour l'adaptation
        const historicalData = await this.getHistoricalDecisions(analysis);
        for (const data of historicalData) {
            if (data.successRate > 0.8) {
                recommendations.push({
                    type: data.agentType,
                    priority: data.priority,
                    workload: data.averageWorkload,
                    skills: data.skills,
                    reasoning: `Basé sur ${data.successCount} succès historiques`
                });
            }
        }
        return recommendations;
    }
    /**
     * Algorithme de décision optimisé
     */
    async optimizedDecision(analysis, phases) {
        const recommendations = [];
        // Optimisation multi-critères
        const criteria = {
            performance: 0.4,
            cost: 0.3,
            reliability: 0.3
        };
        for (const phase of phases) {
            const candidates = await this.getCandidateAgents(phase);
            const optimizedCandidate = this.optimizeSelection(candidates, criteria);
            if (optimizedCandidate) {
                recommendations.push({
                    type: optimizedCandidate.type,
                    priority: optimizedCandidate.priority,
                    workload: optimizedCandidate.workload,
                    skills: optimizedCandidate.skills,
                    reasoning: `Optimisé selon critères multi-objectifs`
                });
            }
        }
        return recommendations;
    }
    /**
     * Adapte le plan basé sur l'apprentissage
     */
    async adaptPlanFromLearning(phases, prediction) {
        // Ajustement des phases basé sur les prédictions
        for (const phase of phases) {
            if (prediction.action && prediction.action.optimizations) {
                const optimization = prediction.action.optimizations[phase.name];
                if (optimization) {
                    phase.estimatedDuration *= optimization.durationFactor || 1;
                    phase.priority = optimization.priority || phase.priority;
                    if (optimization.additionalSkills) {
                        phase.requiredSkills = [...(phase.requiredSkills || []), ...optimization.additionalSkills];
                    }
                }
            }
        }
    }
    /**
     * Calcule la complexité d'un domaine
     */
    calculateDomainComplexity(domain, analysis) {
        let complexity = 0.5; // Base
        // Facteurs d'augmentation de complexité
        if (analysis.requiredSkills.filter(skill => skill.includes(domain)).length > 3) {
            complexity += 0.2;
        }
        if (analysis.dependencies.some(dep => dep.includes(domain))) {
            complexity += 0.1;
        }
        if (analysis.estimatedDuration > 3600000) { // Plus d'1 heure
            complexity += 0.2;
        }
        return Math.min(complexity, 1);
    }
    /**
     * Obtient les ressources disponibles
     */
    async getAvailableResources() {
        // Simulation des ressources disponibles
        return {
            'frontend': 0.8,
            'backend': 0.9,
            'uiux': 0.7,
            'qa': 0.6,
            'devops': 0.8,
            'security': 0.9
        };
    }
    /**
     * Calcule les exigences en ressources
     */
    calculateResourceRequirement(phase) {
        return Math.min(phase.estimatedDuration / 3600000, 1); // Normalisation
    }
    /**
     * Obtient les décisions historiques
     */
    async getHistoricalDecisions(analysis) {
        // Simulation de données historiques
        return [
            {
                agentType: 'agent-frontend',
                successRate: 0.85,
                averageWorkload: 0.7,
                priority: 'high',
                skills: ['react', 'typescript'],
                successCount: 42
            },
            {
                agentType: 'agent-backend',
                successRate: 0.92,
                averageWorkload: 0.6,
                priority: 'high',
                skills: ['nodejs', 'api'],
                successCount: 38
            }
        ];
    }
    /**
     * Obtient les agents candidats
     */
    async getCandidateAgents(phase) {
        // Simulation de candidats
        return [
            {
                type: `agent-${phase.name}`,
                performance: 0.8,
                cost: 0.6,
                reliability: 0.9,
                workload: 0.7,
                priority: 'medium',
                skills: phase.requiredSkills || []
            }
        ];
    }
    /**
     * Optimise la sélection selon les critères
     */
    optimizeSelection(candidates, criteria) {
        if (candidates.length === 0)
            return null;
        return candidates.reduce((best, current) => {
            const currentScore = (current.performance * criteria.performance) +
                ((1 - current.cost) * criteria.cost) +
                (current.reliability * criteria.reliability);
            const bestScore = (best.performance * criteria.performance) +
                ((1 - best.cost) * criteria.cost) +
                (best.reliability * criteria.reliability);
            return currentScore > bestScore ? current : best;
        });
    }
    /**
     * Récupère le statut du moteur de décision
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            totalDecisions: this.performanceMetrics.totalDecisions,
            successfulPlans: this.performanceMetrics.successfulPlans,
            averageAccuracy: this.performanceMetrics.averageAccuracy,
            learningRate: this.performanceMetrics.learningRate,
            decisionPatternsCount: this.decisionPatterns.size
        };
    }
    /**
     * Arrêt gracieux
     */
    async shutdown() {
        logger_1.logger.info('🛑 Arrêt du Moteur de Décision...');
        // Sauvegarde des patterns de décision
        for (const [id, pattern] of this.decisionPatterns) {
            await this.memory.storeGlobalPattern(pattern);
        }
        logger_1.logger.info('✅ Moteur de Décision arrêté');
    }
}
exports.DecisionEngine = DecisionEngine;
//# sourceMappingURL=DecisionEngine.js.map