"use strict";
/**
 * EvolutionEngine - Moteur d'évolution continue pour l'adaptation du système
 * Implémente la neuroplasticité artificielle pour l'amélioration continue
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvolutionEngine = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const TechnologyScanner_1 = require("./TechnologyScanner");
const ImpactAnalyzer_1 = require("./ImpactAnalyzer");
const EvolutionPlanner_1 = require("./EvolutionPlanner");
const AgentTrainer_1 = require("./AgentTrainer");
const DeploymentManager_1 = require("./DeploymentManager");
const ValidationSystem_1 = require("./ValidationSystem");
class EvolutionEngine extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isRunning = false;
        this.currentEvolutions = new Map();
        this.logger = new logger_1.Logger('EvolutionEngine');
        this.config = config;
        // Initialisation des composants
        this.technologyScanner = new TechnologyScanner_1.TechnologyScanner(config.technologySources);
        this.impactAnalyzer = new ImpactAnalyzer_1.ImpactAnalyzer();
        this.evolutionPlanner = new EvolutionPlanner_1.EvolutionPlanner();
        this.agentTrainer = new AgentTrainer_1.AgentTrainer();
        this.deploymentManager = new DeploymentManager_1.DeploymentManager();
        this.validationSystem = new ValidationSystem_1.ValidationSystem();
        this.metrics = this.initializeMetrics();
        this.setupEventHandlers();
    }
    /**
     * Démarre le moteur d'évolution
     */
    async start() {
        if (this.isRunning) {
            this.logger.warn('EvolutionEngine is already running');
            return;
        }
        this.logger.info('Starting EvolutionEngine...');
        this.isRunning = true;
        try {
            // Initialisation des composants
            await this.technologyScanner.initialize();
            await this.impactAnalyzer.initialize();
            await this.evolutionPlanner.initialize();
            await this.agentTrainer.initialize();
            await this.deploymentManager.initialize();
            await this.validationSystem.initialize();
            // Démarrage du scan périodique
            this.startPeriodicScanning();
            this.emitEvent({
                type: 'scan_started',
                timestamp: new Date(),
                data: { config: this.config },
                severity: 'info',
                message: 'EvolutionEngine started successfully'
            });
            this.logger.info('EvolutionEngine started successfully');
        }
        catch (error) {
            this.logger.error('Failed to start EvolutionEngine:', error);
            this.isRunning = false;
            throw error;
        }
    }
    /**
     * Arrête le moteur d'évolution
     */
    async stop() {
        if (!this.isRunning) {
            return;
        }
        this.logger.info('Stopping EvolutionEngine...');
        this.isRunning = false;
        // Arrêt du scan périodique
        if (this.scanTimer) {
            clearInterval(this.scanTimer);
            this.scanTimer = undefined;
        }
        // Attendre la fin des évolutions en cours
        await this.waitForCurrentEvolutions();
        this.logger.info('EvolutionEngine stopped');
    }
    /**
     * Exécute un cycle d'évolution complet
     */
    async performEvolutionCycle() {
        const startTime = Date.now();
        this.logger.info('Starting evolution cycle...');
        try {
            // 1. Détection des nouvelles technologies
            const technologies = await this.scanTechnologyLandscape();
            // 2. Évaluation de l'impact
            const impactAnalyses = await this.analyzeImpact(technologies);
            // 3. Planification de l'évolution
            const evolutionPlan = await this.createEvolutionPlan(impactAnalyses);
            if (!evolutionPlan) {
                return this.createEmptyReport(startTime);
            }
            // 4. Formation des agents
            const trainingResults = await this.trainAgents(evolutionPlan);
            // 5. Déploiement progressif
            const deploymentResults = await this.deployUpdatedAgents(evolutionPlan, trainingResults);
            // 6. Validation et rollback si nécessaire
            const validationResults = await this.validateDeployment(evolutionPlan, deploymentResults);
            // Génération du rapport
            const report = await this.generateEvolutionReport(evolutionPlan, technologies, trainingResults, deploymentResults, validationResults, startTime);
            this.updateMetrics(report);
            this.logger.info(`Evolution cycle completed in ${report.duration} hours`);
            return report;
        }
        catch (error) {
            this.logger.error('Evolution cycle failed:', error);
            throw error;
        }
    }
    /**
     * Scan du paysage technologique
     */
    async scanTechnologyLandscape() {
        this.logger.info('Scanning technology landscape...');
        const technologies = await this.technologyScanner.scanAll();
        this.emitEvent({
            type: 'technology_discovered',
            timestamp: new Date(),
            data: { count: technologies.length, technologies: technologies.slice(0, 5) },
            severity: 'info',
            message: `Discovered ${technologies.length} technologies`
        });
        return technologies;
    }
    /**
     * Analyse d'impact des technologies
     */
    async analyzeImpact(technologies) {
        this.logger.info(`Analyzing impact for ${technologies.length} technologies...`);
        const analyses = [];
        for (const technology of technologies) {
            try {
                const analysis = await this.impactAnalyzer.analyze(technology);
                if (analysis.impactScore > this.config.autoApproveThreshold) {
                    analyses.push(analysis);
                }
            }
            catch (error) {
                this.logger.warn(`Failed to analyze impact for ${technology.name}:`, error);
            }
        }
        return analyses.sort((a, b) => b.impactScore - a.impactScore);
    }
    /**
     * Création du plan d'évolution
     */
    async createEvolutionPlan(impactAnalyses) {
        if (impactAnalyses.length === 0) {
            this.logger.info('No significant technologies found for evolution');
            return null;
        }
        this.logger.info(`Creating evolution plan for ${impactAnalyses.length} technologies...`);
        const plan = await this.evolutionPlanner.createPlan(impactAnalyses);
        this.emitEvent({
            type: 'plan_created',
            timestamp: new Date(),
            planId: plan.id,
            data: { plan },
            severity: 'info',
            message: `Evolution plan created: ${plan.name}`
        });
        return plan;
    }
    /**
     * Formation des agents
     */
    async trainAgents(plan) {
        this.logger.info(`Training agents for plan: ${plan.name}`);
        const results = [];
        for (const phase of plan.phases) {
            for (const agentId of phase.agentsToUpdate) {
                try {
                    const result = await this.agentTrainer.trainAgent(agentId, plan.technologies);
                    results.push(result);
                }
                catch (error) {
                    this.logger.error(`Failed to train agent ${agentId}:`, error);
                    results.push({
                        agentId,
                        success: false,
                        duration: 0,
                        newCapabilities: [],
                        performanceImprovement: 0,
                        errors: [error.message],
                        rollbackRequired: true
                    });
                }
            }
        }
        return results;
    }
    /**
     * Déploiement des agents mis à jour
     */
    async deployUpdatedAgents(plan, trainingResults) {
        this.logger.info(`Deploying updated agents for plan: ${plan.name}`);
        this.emitEvent({
            type: 'evolution_started',
            timestamp: new Date(),
            planId: plan.id,
            data: { plan, trainingResults },
            severity: 'info',
            message: `Starting deployment for plan: ${plan.name}`
        });
        const results = await this.deploymentManager.deployPlan(plan, trainingResults);
        return results;
    }
    /**
     * Validation du déploiement
     */
    async validateDeployment(plan, deploymentResults) {
        this.logger.info(`Validating deployment for plan: ${plan.name}`);
        const validationResults = await this.validationSystem.validatePlan(plan, deploymentResults);
        // Vérifier si un rollback est nécessaire
        const hasFailures = validationResults.some(result => !result.passed && result.criteria.required);
        if (hasFailures) {
            this.logger.warn(`Validation failed for plan ${plan.name}, triggering rollback`);
            this.emitEvent({
                type: 'rollback_triggered',
                timestamp: new Date(),
                planId: plan.id,
                data: { validationResults },
                severity: 'warning',
                message: `Rollback triggered for plan: ${plan.name}`
            });
            await this.deploymentManager.rollbackPlan(plan);
        }
        return validationResults;
    }
    /**
     * Démarrage du scan périodique
     */
    startPeriodicScanning() {
        if (!this.config.enableAutomaticEvolution) {
            return;
        }
        const intervalMs = this.config.scanInterval * 60 * 60 * 1000; // conversion en millisecondes
        this.scanTimer = setInterval(async () => {
            try {
                if (this.currentEvolutions.size < this.config.maxConcurrentEvolutions) {
                    await this.performEvolutionCycle();
                }
                else {
                    this.logger.info('Max concurrent evolutions reached, skipping cycle');
                }
            }
            catch (error) {
                this.logger.error('Periodic evolution cycle failed:', error);
            }
        }, intervalMs);
        this.logger.info(`Periodic scanning started with interval: ${this.config.scanInterval} hours`);
    }
    /**
     * Attendre la fin des évolutions en cours
     */
    async waitForCurrentEvolutions() {
        const maxWaitTime = 30000; // 30 secondes
        const startTime = Date.now();
        while (this.currentEvolutions.size > 0 && (Date.now() - startTime) < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        if (this.currentEvolutions.size > 0) {
            this.logger.warn(`Forcing stop with ${this.currentEvolutions.size} evolutions still running`);
        }
    }
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        this.deploymentManager.on('phaseCompleted', (data) => {
            this.emitEvent({
                type: 'phase_completed',
                timestamp: new Date(),
                planId: data.planId,
                phaseId: data.phaseId,
                data,
                severity: 'info',
                message: `Phase completed: ${data.phaseName}`
            });
        });
        this.validationSystem.on('validationFailed', (data) => {
            this.emitEvent({
                type: 'validation_failed',
                timestamp: new Date(),
                planId: data.planId,
                data,
                severity: 'error',
                message: `Validation failed: ${data.reason}`
            });
        });
    }
    /**
     * Émission d'un événement d'évolution
     */
    emitEvent(event) {
        const fullEvent = {
            id: `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            ...event
        };
        this.emit('evolutionEvent', fullEvent);
        this.logger.info(`Evolution event: ${fullEvent.type} - ${fullEvent.message}`);
    }
    /**
     * Initialisation des métriques
     */
    initializeMetrics() {
        return {
            totalEvolutions: 0,
            successfulEvolutions: 0,
            failedEvolutions: 0,
            averageDuration: 0,
            averagePerformanceGain: 0,
            totalRollbacks: 0,
            technologiesAdopted: 0,
            agentsEvolved: 0,
            lastEvolution: new Date(),
            nextScheduledEvolution: new Date(Date.now() + this.config.scanInterval * 60 * 60 * 1000)
        };
    }
    /**
     * Mise à jour des métriques
     */
    updateMetrics(report) {
        this.metrics.totalEvolutions++;
        if (report.rollbacks === 0) {
            this.metrics.successfulEvolutions++;
        }
        else {
            this.metrics.failedEvolutions++;
        }
        this.metrics.averageDuration = (this.metrics.averageDuration * (this.metrics.totalEvolutions - 1) + report.duration) / this.metrics.totalEvolutions;
        this.metrics.averagePerformanceGain = (this.metrics.averagePerformanceGain * (this.metrics.totalEvolutions - 1) + report.performanceGains) / this.metrics.totalEvolutions;
        this.metrics.totalRollbacks += report.rollbacks;
        this.metrics.technologiesAdopted += report.technologiesEvaluated;
        this.metrics.agentsEvolved += report.agentsUpdated;
        this.metrics.lastEvolution = new Date();
        this.metrics.nextScheduledEvolution = report.nextEvolutionDate;
    }
    /**
     * Création d'un rapport vide
     */
    createEmptyReport(startTime) {
        return {
            id: `rpt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            planId: '',
            technologiesEvaluated: 0,
            agentsUpdated: 0,
            performanceGains: 0,
            rollbacks: 0,
            duration: (Date.now() - startTime) / (1000 * 60 * 60), // en heures
            nextEvolutionDate: new Date(Date.now() + this.config.scanInterval * 60 * 60 * 1000),
            recommendations: ['No significant technologies found for evolution'],
            lessons: ['Continue monitoring technology landscape'],
            createdAt: new Date(),
            successRate: 100
        };
    }
    /**
     * Génération du rapport d'évolution
     */
    async generateEvolutionReport(plan, technologies, trainingResults, deploymentResults, validationResults, startTime) {
        const duration = (Date.now() - startTime) / (1000 * 60 * 60); // en heures
        const successfulTraining = trainingResults.filter(r => r.success).length;
        const successfulDeployments = deploymentResults.filter(r => r.success).length;
        const rollbacks = deploymentResults.reduce((sum, r) => sum + r.rollbacks.length, 0);
        const performanceGains = trainingResults.reduce((sum, r) => sum + r.performanceImprovement, 0) / trainingResults.length || 0;
        return {
            id: `rpt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            planId: plan.id,
            technologiesEvaluated: technologies.length,
            agentsUpdated: successfulTraining,
            performanceGains,
            rollbacks,
            duration,
            nextEvolutionDate: new Date(Date.now() + this.config.scanInterval * 60 * 60 * 1000),
            recommendations: this.generateRecommendations(plan, trainingResults, deploymentResults),
            lessons: this.generateLessons(plan, trainingResults, deploymentResults, validationResults),
            createdAt: new Date(),
            successRate: (successfulDeployments / deploymentResults.length) * 100 || 0
        };
    }
    /**
     * Génération des recommandations
     */
    generateRecommendations(plan, trainingResults, deploymentResults) {
        const recommendations = [];
        const failedTraining = trainingResults.filter(r => !r.success);
        if (failedTraining.length > 0) {
            recommendations.push(`Review training process for ${failedTraining.length} failed agents`);
        }
        const failedDeployments = deploymentResults.filter(r => !r.success);
        if (failedDeployments.length > 0) {
            recommendations.push(`Investigate deployment failures for ${failedDeployments.length} phases`);
        }
        if (plan.technologies.some(t => t.maturity === 'experimental')) {
            recommendations.push('Consider waiting for more mature versions of experimental technologies');
        }
        return recommendations;
    }
    /**
     * Génération des leçons apprises
     */
    generateLessons(plan, trainingResults, deploymentResults, validationResults) {
        const lessons = [];
        if (trainingResults.every(r => r.success)) {
            lessons.push('All agent training completed successfully');
        }
        if (deploymentResults.every(r => r.success)) {
            lessons.push('All deployments completed without issues');
        }
        const avgPerformanceGain = trainingResults.reduce((sum, r) => sum + r.performanceImprovement, 0) / trainingResults.length;
        if (avgPerformanceGain > 10) {
            lessons.push(`Significant performance improvements achieved: ${avgPerformanceGain.toFixed(1)}%`);
        }
        return lessons;
    }
    /**
     * Getters pour l'état et les métriques
     */
    isEvolutionRunning() {
        return this.isRunning;
    }
    getMetrics() {
        return { ...this.metrics };
    }
    getCurrentEvolutions() {
        return Array.from(this.currentEvolutions.values());
    }
    getConfig() {
        return { ...this.config };
    }
}
exports.EvolutionEngine = EvolutionEngine;
//# sourceMappingURL=EvolutionEngine.js.map