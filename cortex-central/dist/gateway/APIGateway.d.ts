import { EventEmitter } from 'events';
import { Request, Response, NextFunction } from 'express';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface APIGatewayConfig {
    cortex: any;
    memory: CentralMemory;
    communication: SynapticCommunication;
    port?: number;
    enableAuth?: boolean;
    enableRateLimit?: boolean;
}
export interface APIRoute {
    path: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    handler: (req: Request, res: Response) => Promise<void>;
    middleware?: Array<(req: Request, res: Response, next: NextFunction) => void>;
    rateLimit?: {
        windowMs: number;
        max: number;
    };
}
export interface GatewayMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    activeConnections: number;
    rateLimitHits: number;
    lastRequestTime: Date;
}
/**
 * API Gateway - Passerelle API Unifiée
 *
 * Point d'entrée unique pour toutes les interactions avec le système.
 * Gère l'authentification, la limitation de débit, le routage intelligent,
 * et la coordination avec les agents spécialisés.
 */
export declare class APIGateway extends EventEmitter {
    private app;
    private cortex;
    private memory;
    private communication;
    private config;
    private server;
    private isInitialized;
    private metrics;
    private registeredRoutes;
    private responseCache;
    constructor(config: APIGatewayConfig);
    /**
     * Initialise l'API Gateway
     */
    initialize(): Promise<void>;
    /**
     * Configure les middlewares de base
     */
    private setupMiddleware;
    /**
     * Configure les routes principales
     */
    private setupCoreRoutes;
    /**
     * Middleware de collecte de métriques
     */
    private metricsMiddleware;
    /**
     * Middleware d'authentification
     */
    private authMiddleware;
    /**
     * Valide une clé API
     */
    private validateApiKey;
    /**
     * Démarre le serveur HTTP
     */
    private startServer;
    /**
     * Obtient les métriques système
     */
    private getSystemMetrics;
    /**
     * Configure les événements de communication
     */
    private setupCommunicationEvents;
    /**
     * Configure les routes dynamiques
     */
    private setupDynamicRoutes;
    /**
     * Démarre la collecte de métriques
     */
    private startMetricsCollection;
    /**
     * Arrête l'API Gateway
     */
    shutdown(): Promise<void>;
    /**
     * Obtient les métriques actuelles
     */
    getMetrics(): GatewayMetrics;
    /**
     * Vérifie si le gateway est initialisé
     */
    isReady(): boolean;
}
//# sourceMappingURL=APIGateway.d.ts.map