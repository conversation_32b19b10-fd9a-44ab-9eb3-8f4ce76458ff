import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface ImmuneSystemConfig {
    memory: CentralMemory;
    communication: SynapticCommunication;
    monitoringInterval?: number;
    healingEnabled?: boolean;
    adaptationEnabled?: boolean;
}
/**
 * Système Immunitaire IA - Auto-Réparation et Adaptation
 *
 * Surveille la santé du système, détecte les anomalies,
 * applique des stratégies de guérison et s'adapte pour éviter les récurrences.
 */
export declare class AIImmuneSystem extends EventEmitter {
    private memory;
    private communication;
    private anomalyDetector;
    private autoHealer;
    private adaptationEngine;
    private isInitialized;
    private isMonitoring;
    private monitoringInterval;
    private healingEnabled;
    private adaptationEnabled;
    private currentMetrics;
    private metricsHistory;
    private maxHistorySize;
    private activeAnomalies;
    private healingInProgress;
    private immuneStats;
    constructor(config: ImmuneSystemConfig);
    /**
     * Initialise le système immunitaire
     */
    initialize(): Promise<void>;
    /**
     * Démarre le monitoring de santé
     */
    startMonitoring(): Promise<void>;
    /**
     * Arrête le monitoring de santé
     */
    stopMonitoring(): Promise<void>;
    /**
     * Surveille la santé du système
     */
    monitorHealth(): Promise<void>;
    /**
     * Collecte les métriques de santé du système
     */
    private gatherMetrics;
    /**
     * Gère une nouvelle anomalie détectée
     */
    private handleNewAnomaly;
    /**
     * Initie le processus de guérison pour une anomalie
     */
    private initiateHealing;
    /**
     * Apprend d'une guérison réussie pour éviter les récurrences
     */
    private learnFromHealing;
    /**
     * Vérifie les anomalies existantes
     */
    private checkExistingAnomalies;
    /**
     * Stocke une anomalie en mémoire pour apprentissage
     */
    private storeAnomalyInMemory;
    /**
     * Charge les stratégies de guérison depuis la mémoire
     */
    private loadHealingStrategies;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Gère la déconnexion d'un agent
     */
    private handleAgentDisconnection;
    /**
     * Gère les échecs synaptiques
     */
    private handleSynapticFailure;
    /**
     * Gère la pression mémoire
     */
    private handleMemoryPressure;
    private getSystemLoad;
    private getMemoryUsage;
    private getAverageResponseTime;
    private getErrorRate;
    private getThroughput;
    private getAgentHealthMetrics;
    private getSynapticHealth;
    /**
     * Évolution de l'architecture basée sur l'usage
     */
    evolveArchitecture(): Promise<void>;
    /**
     * Analyse les patterns d'usage
     */
    private analyzeUsage;
    /**
     * Identifie les goulots d'étranglement
     */
    private identifyBottlenecks;
    /**
     * Propose des évolutions architecturales
     */
    private proposeEvolutions;
    /**
     * Applique les évolutions proposées
     */
    private applyEvolutions;
    /**
     * Calcule la tendance d'une série de valeurs
     */
    private calculateTrend;
    /**
     * Récupère le statut du système immunitaire
     */
    getStatus(): any;
    /**
     * Arrêt gracieux du système immunitaire
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=AIImmuneSystem.d.ts.map