import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';
export interface ServiceControllerConfig {
    memory: CentralMemory;
    communication: SynapticCommunication;
    kubernetesEnabled?: boolean;
    dockerEnabled?: boolean;
    systemdEnabled?: boolean;
}
export interface ServiceInfo {
    name: string;
    type: 'kubernetes' | 'docker' | 'systemd' | 'process';
    status: 'running' | 'stopped' | 'error' | 'unknown';
    replicas?: number;
    resources?: {
        cpu: string;
        memory: string;
    };
    endpoints?: string[];
}
/**
 * Contrôleur de Services - Gestion des Services et Composants
 *
 * Responsable de l'exécution des actions de guérison sur les services,
 * conteneurs, et autres composants du système.
 */
export declare class ServiceController extends EventEmitter {
    private memory;
    private communication;
    private kubernetesEnabled;
    private dockerEnabled;
    private systemdEnabled;
    private isInitialized;
    private serviceCache;
    private cacheExpiry;
    private lastCacheUpdate;
    constructor(config: ServiceControllerConfig);
    /**
     * Initialise le contrôleur de services
     */
    initialize(): Promise<void>;
    /**
     * Redémarre un service
     */
    restartService(serviceName: string, timeout?: number): Promise<void>;
    /**
     * Met à l'échelle un service
     */
    scaleService(serviceName: string, replicas: number, timeout?: number): Promise<void>;
    /**
     * Redirige le trafic d'un service
     */
    redirectTraffic(serviceName: string, destination: string, timeout?: number): Promise<void>;
    /**
     * Optimise un service
     */
    optimizeService(serviceName: string, parameters: any): Promise<void>;
    /**
     * Isole un composant
     */
    isolateComponent(serviceName: string, timeout?: number): Promise<void>;
    /**
     * Effectue un rollback de déploiement
     */
    rollbackDeployment(serviceName: string, parameters: any): Promise<void>;
    /**
     * Obtient les informations d'un service
     */
    getServiceInfo(serviceName: string): Promise<ServiceInfo>;
    /**
     * Vérifie les outils disponibles
     */
    private checkAvailableTools;
    /**
     * Met à jour le cache des services
     */
    private updateServiceCache;
    /**
     * Vérifie si le cache des services est valide
     */
    private isServiceCacheValid;
    /**
     * Redémarre un service Kubernetes
     */
    private restartKubernetesService;
    /**
     * Redémarre un service Docker
     */
    private restartDockerService;
    /**
     * Redémarre un service systemd
     */
    private restartSystemdService;
    /**
     * Met à l'échelle un service Kubernetes
     */
    private scaleKubernetesService;
    /**
     * Met à l'échelle un service Docker (Docker Swarm)
     */
    private scaleDockerService;
    /**
     * Met à jour l'endpoint d'un service Kubernetes
     */
    private updateKubernetesServiceEndpoint;
    /**
     * Met à jour la configuration du load balancer
     */
    private updateLoadBalancerConfig;
    /**
     * Vide le cache d'un service
     */
    private clearServiceCache;
    /**
     * Ajuste les ressources d'un service
     */
    private adjustServiceResources;
    /**
     * Optimise la configuration d'un service
     */
    private tuneServiceConfiguration;
    /**
     * Crée une NetworkPolicy Kubernetes pour isoler un composant
     */
    private createKubernetesNetworkPolicy;
    /**
     * Crée des règles de firewall pour isoler un composant
     */
    private createFirewallRules;
    /**
     * Effectue un rollback de déploiement Kubernetes
     */
    private rollbackKubernetesDeployment;
    /**
     * Effectue un rollback de déploiement Docker
     */
    private rollbackDockerDeployment;
    /**
     * Attend que le service soit en bonne santé
     */
    private waitForServiceHealth;
    /**
     * Charge les services Kubernetes
     */
    private loadKubernetesServices;
    /**
     * Charge les services Docker
     */
    private loadDockerServices;
    /**
     * Charge les services systemd
     */
    private loadSystemdServices;
}
//# sourceMappingURL=ServiceController.d.ts.map