"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceController = void 0;
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const child_process_1 = require("child_process");
const util_1 = require("util");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
/**
 * Contrôleur de Services - Gestion des Services et Composants
 *
 * Responsable de l'exécution des actions de guérison sur les services,
 * conteneurs, et autres composants du système.
 */
class ServiceController extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.isInitialized = false;
        this.serviceCache = new Map();
        this.cacheExpiry = 30000; // 30 secondes
        this.lastCacheUpdate = 0;
        this.memory = config.memory;
        this.communication = config.communication;
        this.kubernetesEnabled = config.kubernetesEnabled !== false;
        this.dockerEnabled = config.dockerEnabled !== false;
        this.systemdEnabled = config.systemdEnabled !== false;
    }
    /**
     * Initialise le contrôleur de services
     */
    async initialize() {
        try {
            logger_1.logger.info('🎛️ Initialisation du Contrôleur de Services...');
            // Vérification des outils disponibles
            await this.checkAvailableTools();
            // Mise à jour initiale du cache des services
            await this.updateServiceCache();
            this.isInitialized = true;
            logger_1.logger.info('✅ Contrôleur de Services initialisé');
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de l\'initialisation du Contrôleur de Services:', error);
            throw error;
        }
    }
    /**
     * Redémarre un service
     */
    async restartService(serviceName, timeout = 300000) {
        logger_1.logger.info(`🔄 Redémarrage du service: ${serviceName}`);
        try {
            const serviceInfo = await this.getServiceInfo(serviceName);
            switch (serviceInfo.type) {
                case 'kubernetes':
                    await this.restartKubernetesService(serviceName, timeout);
                    break;
                case 'docker':
                    await this.restartDockerService(serviceName, timeout);
                    break;
                case 'systemd':
                    await this.restartSystemdService(serviceName, timeout);
                    break;
                default:
                    throw new Error(`Type de service non supporté: ${serviceInfo.type}`);
            }
            // Vérification que le service est bien redémarré
            await this.waitForServiceHealth(serviceName, timeout);
            this.emit('service-restarted', {
                serviceName,
                success: true,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Service ${serviceName} redémarré avec succès`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec du redémarrage du service ${serviceName}:`, error);
            this.emit('service-restarted', {
                serviceName,
                success: false,
                error: error.message,
                timestamp: new Date()
            });
            throw error;
        }
    }
    /**
     * Met à l'échelle un service
     */
    async scaleService(serviceName, replicas, timeout = 300000) {
        logger_1.logger.info(`📈 Mise à l'échelle du service ${serviceName} vers ${replicas} répliques`);
        try {
            const serviceInfo = await this.getServiceInfo(serviceName);
            switch (serviceInfo.type) {
                case 'kubernetes':
                    await this.scaleKubernetesService(serviceName, replicas, timeout);
                    break;
                case 'docker':
                    await this.scaleDockerService(serviceName, replicas, timeout);
                    break;
                default:
                    throw new Error(`Mise à l'échelle non supportée pour le type: ${serviceInfo.type}`);
            }
            this.emit('service-scaled', {
                serviceName,
                replicas,
                success: true,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Service ${serviceName} mis à l'échelle vers ${replicas} répliques`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec de la mise à l'échelle du service ${serviceName}:`, error);
            this.emit('service-scaled', {
                serviceName,
                replicas,
                success: false,
                error: error.message,
                timestamp: new Date()
            });
            throw error;
        }
    }
    /**
     * Redirige le trafic d'un service
     */
    async redirectTraffic(serviceName, destination, timeout = 60000) {
        logger_1.logger.info(`🔀 Redirection du trafic de ${serviceName} vers ${destination}`);
        try {
            if (this.kubernetesEnabled) {
                // Mise à jour du service Kubernetes pour pointer vers la nouvelle destination
                await this.updateKubernetesServiceEndpoint(serviceName, destination);
            }
            else {
                // Mise à jour de la configuration du load balancer ou proxy
                await this.updateLoadBalancerConfig(serviceName, destination);
            }
            this.emit('traffic-redirected', {
                serviceName,
                destination,
                success: true,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Trafic redirigé de ${serviceName} vers ${destination}`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec de la redirection du trafic:`, error);
            throw error;
        }
    }
    /**
     * Optimise un service
     */
    async optimizeService(serviceName, parameters) {
        logger_1.logger.info(`⚡ Optimisation du service: ${serviceName}`);
        try {
            // Optimisations possibles selon les paramètres
            if (parameters.clearCache) {
                await this.clearServiceCache(serviceName);
            }
            if (parameters.adjustResources) {
                await this.adjustServiceResources(serviceName, parameters.adjustResources);
            }
            if (parameters.tuneConfiguration) {
                await this.tuneServiceConfiguration(serviceName, parameters.tuneConfiguration);
            }
            this.emit('service-optimized', {
                serviceName,
                parameters,
                success: true,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Service ${serviceName} optimisé`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec de l'optimisation du service ${serviceName}:`, error);
            throw error;
        }
    }
    /**
     * Isole un composant
     */
    async isolateComponent(serviceName, timeout = 60000) {
        logger_1.logger.info(`🚧 Isolation du composant: ${serviceName}`);
        try {
            if (this.kubernetesEnabled) {
                // Isolation via NetworkPolicy Kubernetes
                await this.createKubernetesNetworkPolicy(serviceName);
            }
            else {
                // Isolation via iptables ou autre mécanisme
                await this.createFirewallRules(serviceName);
            }
            this.emit('component-isolated', {
                serviceName,
                success: true,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Composant ${serviceName} isolé`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec de l'isolation du composant ${serviceName}:`, error);
            throw error;
        }
    }
    /**
     * Effectue un rollback de déploiement
     */
    async rollbackDeployment(serviceName, parameters) {
        logger_1.logger.info(`⏪ Rollback du déploiement: ${serviceName}`);
        try {
            const version = parameters.version || 'previous';
            if (this.kubernetesEnabled) {
                await this.rollbackKubernetesDeployment(serviceName, version);
            }
            else if (this.dockerEnabled) {
                await this.rollbackDockerDeployment(serviceName, version);
            }
            this.emit('deployment-rolled-back', {
                serviceName,
                version,
                success: true,
                timestamp: new Date()
            });
            logger_1.logger.info(`✅ Rollback du déploiement ${serviceName} vers ${version}`);
        }
        catch (error) {
            logger_1.logger.error(`❌ Échec du rollback du déploiement ${serviceName}:`, error);
            throw error;
        }
    }
    /**
     * Obtient les informations d'un service
     */
    async getServiceInfo(serviceName) {
        // Vérification du cache
        if (this.isServiceCacheValid() && this.serviceCache.has(serviceName)) {
            return this.serviceCache.get(serviceName);
        }
        // Mise à jour du cache si nécessaire
        await this.updateServiceCache();
        const serviceInfo = this.serviceCache.get(serviceName);
        if (!serviceInfo) {
            throw new Error(`Service non trouvé: ${serviceName}`);
        }
        return serviceInfo;
    }
    /**
     * Vérifie les outils disponibles
     */
    async checkAvailableTools() {
        try {
            // Vérification de kubectl
            if (this.kubernetesEnabled) {
                await execAsync('kubectl version --client');
                logger_1.logger.info('✅ kubectl disponible');
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ kubectl non disponible, désactivation de Kubernetes');
            this.kubernetesEnabled = false;
        }
        try {
            // Vérification de docker
            if (this.dockerEnabled) {
                await execAsync('docker version');
                logger_1.logger.info('✅ docker disponible');
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ docker non disponible, désactivation de Docker');
            this.dockerEnabled = false;
        }
        try {
            // Vérification de systemctl
            if (this.systemdEnabled) {
                await execAsync('systemctl --version');
                logger_1.logger.info('✅ systemctl disponible');
            }
        }
        catch (error) {
            logger_1.logger.warn('⚠️ systemctl non disponible, désactivation de systemd');
            this.systemdEnabled = false;
        }
    }
    /**
     * Met à jour le cache des services
     */
    async updateServiceCache() {
        try {
            this.serviceCache.clear();
            if (this.kubernetesEnabled) {
                await this.loadKubernetesServices();
            }
            if (this.dockerEnabled) {
                await this.loadDockerServices();
            }
            if (this.systemdEnabled) {
                await this.loadSystemdServices();
            }
            this.lastCacheUpdate = Date.now();
        }
        catch (error) {
            logger_1.logger.error('❌ Erreur lors de la mise à jour du cache des services:', error);
        }
    }
    /**
     * Vérifie si le cache des services est valide
     */
    isServiceCacheValid() {
        return Date.now() - this.lastCacheUpdate < this.cacheExpiry;
    }
    /**
     * Redémarre un service Kubernetes
     */
    async restartKubernetesService(serviceName, timeout) {
        try {
            // Redémarrage via rollout restart
            await execAsync(`kubectl rollout restart deployment/${serviceName}`);
            // Attente que le rollout soit terminé
            await execAsync(`kubectl rollout status deployment/${serviceName} --timeout=${timeout}ms`);
        }
        catch (error) {
            throw new Error(`Échec du redémarrage Kubernetes: ${error.message}`);
        }
    }
    /**
     * Redémarre un service Docker
     */
    async restartDockerService(serviceName, timeout) {
        try {
            await execAsync(`docker restart ${serviceName}`);
            // Vérification que le conteneur est bien démarré
            const { stdout } = await execAsync(`docker inspect ${serviceName} --format='{{.State.Status}}'`);
            if (stdout.trim() !== 'running') {
                throw new Error('Le conteneur n\'est pas en cours d\'exécution après redémarrage');
            }
        }
        catch (error) {
            throw new Error(`Échec du redémarrage Docker: ${error.message}`);
        }
    }
    /**
     * Redémarre un service systemd
     */
    async restartSystemdService(serviceName, timeout) {
        try {
            await execAsync(`systemctl restart ${serviceName}`);
            // Vérification du statut
            const { stdout } = await execAsync(`systemctl is-active ${serviceName}`);
            if (stdout.trim() !== 'active') {
                throw new Error('Le service n\'est pas actif après redémarrage');
            }
        }
        catch (error) {
            throw new Error(`Échec du redémarrage systemd: ${error.message}`);
        }
    }
    /**
     * Met à l'échelle un service Kubernetes
     */
    async scaleKubernetesService(serviceName, replicas, timeout) {
        try {
            await execAsync(`kubectl scale deployment/${serviceName} --replicas=${replicas}`);
            // Attente que la mise à l'échelle soit terminée
            await execAsync(`kubectl rollout status deployment/${serviceName} --timeout=${timeout}ms`);
        }
        catch (error) {
            throw new Error(`Échec de la mise à l'échelle Kubernetes: ${error.message}`);
        }
    }
    /**
     * Met à l'échelle un service Docker (Docker Swarm)
     */
    async scaleDockerService(serviceName, replicas, timeout) {
        try {
            await execAsync(`docker service scale ${serviceName}=${replicas}`);
            // Vérification que la mise à l'échelle est terminée
            let attempts = 0;
            const maxAttempts = timeout / 5000; // Vérification toutes les 5 secondes
            while (attempts < maxAttempts) {
                const { stdout } = await execAsync(`docker service ls --filter name=${serviceName} --format "{{.Replicas}}"`);
                const [current, desired] = stdout.trim().split('/');
                if (current === desired && parseInt(current) === replicas) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 5000));
                attempts++;
            }
            if (attempts >= maxAttempts) {
                throw new Error('Timeout lors de la mise à l\'échelle');
            }
        }
        catch (error) {
            throw new Error(`Échec de la mise à l'échelle Docker: ${error.message}`);
        }
    }
    /**
     * Met à jour l'endpoint d'un service Kubernetes
     */
    async updateKubernetesServiceEndpoint(serviceName, destination) {
        try {
            // Création d'un service temporaire pointant vers la nouvelle destination
            const serviceConfig = `
apiVersion: v1
kind: Service
metadata:
  name: ${serviceName}-redirect
spec:
  type: ExternalName
  externalName: ${destination}
  ports:
  - port: 80
`;
            // Application de la configuration
            await execAsync(`echo '${serviceConfig}' | kubectl apply -f -`);
            // Mise à jour du service principal pour pointer vers le service de redirection
            await execAsync(`kubectl patch service ${serviceName} -p '{"spec":{"selector":null}}'`);
            await execAsync(`kubectl patch service ${serviceName} -p '{"spec":{"type":"ExternalName","externalName":"${destination}"}}'`);
        }
        catch (error) {
            throw new Error(`Échec de la redirection Kubernetes: ${error.message}`);
        }
    }
    /**
     * Met à jour la configuration du load balancer
     */
    async updateLoadBalancerConfig(serviceName, destination) {
        // Implémentation simplifiée - devrait être adaptée selon le load balancer utilisé
        logger_1.logger.info(`Mise à jour de la configuration du load balancer pour ${serviceName} -> ${destination}`);
        // Exemple pour nginx
        try {
            const nginxConfig = `
upstream ${serviceName}_backend {
    server ${destination};
}

server {
    listen 80;
    server_name ${serviceName};

    location / {
        proxy_pass http://${serviceName}_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
`;
            // Écriture de la configuration et rechargement
            await execAsync(`echo '${nginxConfig}' > /etc/nginx/sites-available/${serviceName}`);
            await execAsync(`nginx -s reload`);
        }
        catch (error) {
            logger_1.logger.warn('Échec de la mise à jour nginx, tentative avec HAProxy...');
            // Fallback vers HAProxy
            try {
                const haproxyConfig = `
backend ${serviceName}_backend
    server ${serviceName}_server ${destination} check
`;
                await execAsync(`echo '${haproxyConfig}' >> /etc/haproxy/haproxy.cfg`);
                await execAsync(`systemctl reload haproxy`);
            }
            catch (haproxyError) {
                throw new Error(`Échec de la mise à jour du load balancer: ${error.message}`);
            }
        }
    }
    /**
     * Vide le cache d'un service
     */
    async clearServiceCache(serviceName) {
        try {
            // Tentative de vidage du cache Redis si disponible
            try {
                await execAsync(`redis-cli FLUSHDB`);
                logger_1.logger.info(`Cache Redis vidé pour ${serviceName}`);
            }
            catch (redisError) {
                logger_1.logger.debug('Redis non disponible pour le vidage du cache');
            }
            // Tentative de redémarrage des pods pour vider le cache en mémoire
            if (this.kubernetesEnabled) {
                await execAsync(`kubectl delete pods -l app=${serviceName}`);
            }
        }
        catch (error) {
            throw new Error(`Échec du vidage du cache: ${error.message}`);
        }
    }
    /**
     * Ajuste les ressources d'un service
     */
    async adjustServiceResources(serviceName, resources) {
        try {
            if (this.kubernetesEnabled) {
                const resourcePatch = {
                    spec: {
                        template: {
                            spec: {
                                containers: [{
                                        name: serviceName,
                                        resources: {
                                            requests: {
                                                cpu: resources.cpu || '100m',
                                                memory: resources.memory || '128Mi'
                                            },
                                            limits: {
                                                cpu: resources.maxCpu || resources.cpu || '500m',
                                                memory: resources.maxMemory || resources.memory || '512Mi'
                                            }
                                        }
                                    }]
                            }
                        }
                    }
                };
                await execAsync(`kubectl patch deployment ${serviceName} -p '${JSON.stringify(resourcePatch)}'`);
            }
        }
        catch (error) {
            throw new Error(`Échec de l'ajustement des ressources: ${error.message}`);
        }
    }
    /**
     * Optimise la configuration d'un service
     */
    async tuneServiceConfiguration(serviceName, config) {
        try {
            // Mise à jour des variables d'environnement
            if (config.environment && this.kubernetesEnabled) {
                const envPatch = {
                    spec: {
                        template: {
                            spec: {
                                containers: [{
                                        name: serviceName,
                                        env: Object.entries(config.environment).map(([key, value]) => ({
                                            name: key,
                                            value: String(value)
                                        }))
                                    }]
                            }
                        }
                    }
                };
                await execAsync(`kubectl patch deployment ${serviceName} -p '${JSON.stringify(envPatch)}'`);
            }
        }
        catch (error) {
            throw new Error(`Échec de l'optimisation de la configuration: ${error.message}`);
        }
    }
    /**
     * Crée une NetworkPolicy Kubernetes pour isoler un composant
     */
    async createKubernetesNetworkPolicy(serviceName) {
        try {
            const networkPolicy = `
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ${serviceName}-isolation
spec:
  podSelector:
    matchLabels:
      app: ${serviceName}
  policyTypes:
  - Ingress
  - Egress
  ingress: []
  egress: []
`;
            await execAsync(`echo '${networkPolicy}' | kubectl apply -f -`);
        }
        catch (error) {
            throw new Error(`Échec de la création de la NetworkPolicy: ${error.message}`);
        }
    }
    /**
     * Crée des règles de firewall pour isoler un composant
     */
    async createFirewallRules(serviceName) {
        try {
            // Exemple avec iptables
            await execAsync(`iptables -A INPUT -m comment --comment "${serviceName}-isolation" -j DROP`);
            await execAsync(`iptables -A OUTPUT -m comment --comment "${serviceName}-isolation" -j DROP`);
        }
        catch (error) {
            throw new Error(`Échec de la création des règles de firewall: ${error.message}`);
        }
    }
    /**
     * Effectue un rollback de déploiement Kubernetes
     */
    async rollbackKubernetesDeployment(serviceName, version) {
        try {
            if (version === 'previous') {
                await execAsync(`kubectl rollout undo deployment/${serviceName}`);
            }
            else {
                await execAsync(`kubectl rollout undo deployment/${serviceName} --to-revision=${version}`);
            }
            await execAsync(`kubectl rollout status deployment/${serviceName}`);
        }
        catch (error) {
            throw new Error(`Échec du rollback Kubernetes: ${error.message}`);
        }
    }
    /**
     * Effectue un rollback de déploiement Docker
     */
    async rollbackDockerDeployment(serviceName, version) {
        try {
            // Arrêt du conteneur actuel
            await execAsync(`docker stop ${serviceName}`);
            await execAsync(`docker rm ${serviceName}`);
            // Redémarrage avec la version précédente
            const imageTag = version === 'previous' ? 'latest-1' : version;
            await execAsync(`docker run -d --name ${serviceName} ${serviceName}:${imageTag}`);
        }
        catch (error) {
            throw new Error(`Échec du rollback Docker: ${error.message}`);
        }
    }
    /**
     * Attend que le service soit en bonne santé
     */
    async waitForServiceHealth(serviceName, timeout) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            try {
                const serviceInfo = await this.getServiceInfo(serviceName);
                if (serviceInfo.status === 'running') {
                    return;
                }
            }
            catch (error) {
                // Ignore les erreurs temporaires
            }
            await new Promise(resolve => setTimeout(resolve, 5000)); // Attente 5 secondes
        }
        throw new Error(`Timeout: le service ${serviceName} n'est pas devenu sain dans les temps`);
    }
    /**
     * Charge les services Kubernetes
     */
    async loadKubernetesServices() {
        try {
            const { stdout } = await execAsync('kubectl get deployments -o json');
            const deployments = JSON.parse(stdout);
            for (const deployment of deployments.items) {
                const serviceInfo = {
                    name: deployment.metadata.name,
                    type: 'kubernetes',
                    status: deployment.status.readyReplicas === deployment.status.replicas ? 'running' : 'error',
                    replicas: deployment.status.replicas,
                    resources: {
                        cpu: deployment.spec.template.spec.containers[0]?.resources?.requests?.cpu || 'unknown',
                        memory: deployment.spec.template.spec.containers[0]?.resources?.requests?.memory || 'unknown'
                    }
                };
                this.serviceCache.set(deployment.metadata.name, serviceInfo);
            }
        }
        catch (error) {
            logger_1.logger.error('Erreur lors du chargement des services Kubernetes:', error);
        }
    }
    /**
     * Charge les services Docker
     */
    async loadDockerServices() {
        try {
            const { stdout } = await execAsync('docker ps --format "{{.Names}}\t{{.Status}}\t{{.Image}}"');
            const lines = stdout.trim().split('\n');
            for (const line of lines) {
                if (line) {
                    const [name, status, image] = line.split('\t');
                    const serviceInfo = {
                        name,
                        type: 'docker',
                        status: status.includes('Up') ? 'running' : 'stopped'
                    };
                    this.serviceCache.set(name, serviceInfo);
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Erreur lors du chargement des services Docker:', error);
        }
    }
    /**
     * Charge les services systemd
     */
    async loadSystemdServices() {
        try {
            const { stdout } = await execAsync('systemctl list-units --type=service --state=running --no-pager --plain');
            const lines = stdout.trim().split('\n');
            for (const line of lines) {
                if (line && !line.includes('UNIT')) {
                    const parts = line.trim().split(/\s+/);
                    const serviceName = parts[0].replace('.service', '');
                    const serviceInfo = {
                        name: serviceName,
                        type: 'systemd',
                        status: 'running'
                    };
                    this.serviceCache.set(serviceName, serviceInfo);
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Erreur lors du chargement des services systemd:', error);
        }
    }
}
exports.ServiceController = ServiceController;
//# sourceMappingURL=ServiceController.js.map