"use strict";
/**
 * Types partagés pour le Système Immunitaire IA
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealingActionType = exports.AnomalyType = exports.ComponentType = exports.HealingStatus = void 0;
var HealingStatus;
(function (HealingStatus) {
    HealingStatus["PENDING"] = "pending";
    HealingStatus["IN_PROGRESS"] = "in_progress";
    HealingStatus["SUCCESS"] = "success";
    HealingStatus["FAILED"] = "failed";
    HealingStatus["ROLLED_BACK"] = "rolled_back";
    HealingStatus["TIMEOUT"] = "timeout";
})(HealingStatus || (exports.HealingStatus = HealingStatus = {}));
var ComponentType;
(function (ComponentType) {
    ComponentType["FRONTEND"] = "frontend";
    ComponentType["BACKEND"] = "backend";
    ComponentType["DATABASE"] = "database";
    ComponentType["CACHE"] = "cache";
    ComponentType["QUEUE"] = "queue";
    ComponentType["GATEWAY"] = "gateway";
    ComponentType["STORAGE"] = "storage";
    ComponentType["MONITORING"] = "monitoring";
    ComponentType["SECURITY"] = "security";
})(ComponentType || (exports.ComponentType = ComponentType = {}));
var AnomalyType;
(function (AnomalyType) {
    AnomalyType["PERFORMANCE_DEGRADATION"] = "performance_degradation";
    AnomalyType["HIGH_ERROR_RATE"] = "high_error_rate";
    AnomalyType["RESOURCE_EXHAUSTION"] = "resource_exhaustion";
    AnomalyType["COMMUNICATION_FAILURE"] = "communication_failure";
    AnomalyType["AGENT_FAILURE"] = "agent_failure";
    AnomalyType["SECURITY_BREACH"] = "security_breach";
    AnomalyType["DATA_CORRUPTION"] = "data_corruption";
    AnomalyType["SERVICE_UNAVAILABLE"] = "service_unavailable";
})(AnomalyType || (exports.AnomalyType = AnomalyType = {}));
var HealingActionType;
(function (HealingActionType) {
    HealingActionType["RESTART_SERVICE"] = "restart_service";
    HealingActionType["SCALE_UP"] = "scale_up";
    HealingActionType["SCALE_DOWN"] = "scale_down";
    HealingActionType["REDIRECT_TRAFFIC"] = "redirect_traffic";
    HealingActionType["OPTIMIZE_RESOURCES"] = "optimize_resources";
    HealingActionType["ISOLATE_COMPONENT"] = "isolate_component";
    HealingActionType["CLEAR_CACHE"] = "clear_cache";
    HealingActionType["RESTART_DEPENDENCIES"] = "restart_dependencies";
    HealingActionType["ROLLBACK_DEPLOYMENT"] = "rollback_deployment";
    HealingActionType["NOTIFY_OPERATORS"] = "notify_operators";
    HealingActionType["ENABLE_CIRCUIT_BREAKER"] = "enable_circuit_breaker";
    HealingActionType["INCREASE_TIMEOUT"] = "increase_timeout";
})(HealingActionType || (exports.HealingActionType = HealingActionType = {}));
//# sourceMappingURL=types.js.map