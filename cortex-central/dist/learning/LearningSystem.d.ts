import { EventEmitter } from 'events';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
export interface LearningConfig {
    memory: CentralMemory;
    decisionEngine: DecisionEngine;
    neuralNetwork: NeuralNetworkManager;
    learningRate?: number;
    adaptationThreshold?: number;
    maxPatterns?: number;
}
export interface LearningPattern {
    id: string;
    type: 'success' | 'failure' | 'optimization' | 'adaptation';
    context: any;
    action: any;
    result: any;
    confidence: number;
    timestamp: Date;
    domain: string;
    metadata: any;
}
export interface AdaptationRule {
    id: string;
    condition: string;
    action: string;
    priority: number;
    successRate: number;
    usageCount: number;
    lastUsed: Date;
}
export interface LearningMetrics {
    totalPatterns: number;
    successfulAdaptations: number;
    failedAdaptations: number;
    averageConfidence: number;
    learningRate: number;
    adaptationAccuracy: number;
    lastLearningEvent: Date;
}
/**
 * Système d'Apprentissage Continu
 *
 * Implémente l'apprentissage automatique et l'adaptation continue
 * du système basé sur les expériences passées et les résultats obtenus.
 */
export declare class LearningSystem extends EventEmitter {
    private memory;
    private decisionEngine;
    private neuralNetwork;
    private config;
    private isInitialized;
    private learningPatterns;
    private adaptationRules;
    private metrics;
    private predictionCache;
    constructor(config: LearningConfig);
    /**
     * Initialise le système d'apprentissage
     */
    initialize(): Promise<void>;
    /**
     * Apprend d'une expérience
     */
    learnFromExperience(experience: {
        context: any;
        action: any;
        result: any;
        success: boolean;
        domain: string;
        metadata?: any;
    }): Promise<void>;
    /**
     * Prédit le meilleur plan d'action
     */
    predictBestAction(context: any, domain: string): Promise<{
        action: any;
        confidence: number;
        reasoning: string;
    }>;
    /**
     * Adapte le comportement du système
     */
    adaptBehavior(trigger: string, context: any): Promise<boolean>;
    /**
     * Optimise les patterns d'apprentissage
     */
    optimizePatterns(): Promise<void>;
    /**
     * Calcule la confiance d'un pattern
     */
    private calculateConfidence;
    /**
     * Stocke un pattern d'apprentissage
     */
    private storePattern;
    /**
     * Charge les patterns existants
     */
    private loadExistingPatterns;
    /**
     * Trouve des patterns similaires
     */
    private findSimilarPatterns;
    /**
     * Calcule la similarité entre deux contextes
     */
    private calculateSimilarity;
    /**
     * Sélectionne le meilleur pattern
     */
    private selectBestPattern;
    /**
     * Configure les événements d'apprentissage
     */
    private setupLearningEvents;
    /**
     * Apprend d'une décision
     */
    private learnFromDecision;
    /**
     * Apprend de l'achèvement d'une tâche
     */
    private learnFromTaskCompletion;
    /**
     * Démarre l'apprentissage continu
     */
    private startContinuousLearning;
    /**
     * Effectue l'apprentissage continu
     */
    private performContinuousLearning;
    /**
     * Démarre l'optimisation des patterns
     */
    private startPatternOptimization;
    /**
     * Met à jour les métriques d'apprentissage
     */
    private updateLearningMetrics;
    /**
     * Charge les règles d'adaptation
     */
    private loadAdaptationRules;
    /**
     * Met à jour les règles d'adaptation
     */
    private updateAdaptationRules;
    /**
     * Génère une condition à partir d'un pattern
     */
    private generateConditionFromPattern;
    /**
     * Génère une action à partir d'un pattern
     */
    private generateActionFromPattern;
    /**
     * Évalue une condition
     */
    private evaluateCondition;
    /**
     * Applique une règle d'adaptation
     */
    private applyAdaptationRule;
    /**
     * Optimise les performances
     */
    private optimizePerformance;
    /**
     * Active la récupération
     */
    private activateRecovery;
    /**
     * Supprime les patterns obsolètes
     */
    private removeObsoletePatterns;
    /**
     * Fusionne les patterns similaires
     */
    private mergeSimilarPatterns;
    /**
     * Fusionne deux patterns
     */
    private mergePatterns;
    /**
     * Met à jour les confidences des patterns
     */
    private updatePatternConfidences;
    /**
     * Analyse les patterns récents
     */
    private analyzeRecentPatterns;
    /**
     * Met à jour les règles d'adaptation à partir des patterns
     */
    private updateAdaptationRulesFromPatterns;
    /**
     * Obtient les métriques d'apprentissage
     */
    getMetrics(): LearningMetrics;
    /**
     * Vérifie si le système est initialisé
     */
    isReady(): boolean;
}
//# sourceMappingURL=LearningSystem.d.ts.map