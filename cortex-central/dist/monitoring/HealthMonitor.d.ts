import { EventEmitter } from 'events';
import { CortexCentral } from '../core/CortexCentral';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { CentralMemory } from '../memory/CentralMemory';
export interface HealthMetrics {
    timestamp: Date;
    overall: 'healthy' | 'degraded' | 'critical';
    score: number;
    components: {
        cortex: ComponentHealth;
        neuralNetwork: ComponentHealth;
        memory: ComponentHealth;
        agents: ComponentHealth;
        workflows: ComponentHealth;
    };
    alerts: HealthAlert[];
    recommendations: string[];
}
export interface ComponentHealth {
    status: 'healthy' | 'degraded' | 'critical' | 'offline';
    score: number;
    metrics: Record<string, any>;
    lastCheck: Date;
    issues: string[];
}
export interface HealthAlert {
    id: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    component: string;
    message: string;
    timestamp: Date;
    resolved: boolean;
    metadata?: any;
}
export interface HealthConfig {
    cortex: CortexCentral;
    neuralNetwork: NeuralNetworkManager;
    memory: CentralMemory;
    checkInterval?: number;
    alertThresholds?: {
        cpu: number;
        memory: number;
        responseTime: number;
        errorRate: number;
    };
}
/**
 * Moniteur de Santé du Système
 *
 * Surveille en continu la santé de tous les composants
 * du système nerveux distribué
 */
export declare class HealthMonitor extends EventEmitter {
    private cortex;
    private neuralNetwork;
    private memory;
    private checkInterval;
    private alertThresholds;
    private monitoringInterval;
    private healthHistory;
    private activeAlerts;
    private isInitialized;
    constructor(config: HealthConfig);
    /**
     * Initialise le moniteur de santé
     */
    initialize(): Promise<void>;
    /**
     * Démarre le monitoring périodique
     */
    private startPeriodicMonitoring;
    /**
     * Effectue une vérification complète de santé
     */
    performHealthCheck(): Promise<HealthMetrics>;
    /**
     * Vérifie la santé du Cortex Central
     */
    private checkCortexHealth;
    /**
     * Vérifie la santé du réseau neuronal
     */
    private checkNeuralNetworkHealth;
    /**
     * Vérifie la santé de la mémoire centrale
     */
    private checkMemoryHealth;
    /**
     * Vérifie la santé des agents
     */
    private checkAgentsHealth;
    /**
     * Vérifie la santé des workflows
     */
    private checkWorkflowsHealth;
    /**
     * Calcule le score d'un composant
     */
    private calculateComponentScore;
    /**
     * Détermine le statut à partir du score
     */
    private getStatusFromScore;
    /**
     * Calcule le score global
     */
    private calculateOverallScore;
    /**
     * Détermine le statut global
     */
    private determineOverallStatus;
    /**
     * Génère les alertes basées sur l'état des composants
     */
    private generateAlerts;
    /**
     * Génère des recommandations
     */
    private generateRecommendations;
    /**
     * Récupère le statut de santé actuel
     */
    getHealthStatus(): HealthMetrics | null;
    /**
     * Récupère l'historique de santé
     */
    getHealthHistory(limit?: number): HealthMetrics[];
    /**
     * Récupère les alertes actives
     */
    getActiveAlerts(): HealthAlert[];
    /**
     * Résout une alerte
     */
    resolveAlert(alertId: string): boolean;
    /**
     * Arrêt du moniteur de santé
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=HealthMonitor.d.ts.map