import { EventEmitter } from 'events';
import { SynapticCommunication } from '../communication/SynapticCommunication';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
export interface WorkflowStep {
    id: string;
    name: string;
    agentType: string;
    action: string;
    inputs: any;
    outputs?: any;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    error?: string;
    retries: number;
    maxRetries: number;
    dependencies: string[];
    conditions?: any;
}
export interface Workflow {
    id: string;
    name: string;
    description: string;
    type: 'development' | 'deployment' | 'testing' | 'maintenance' | 'custom';
    status: 'created' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
    priority: 'low' | 'medium' | 'high' | 'critical';
    steps: WorkflowStep[];
    currentStep?: string;
    createdAt: Date;
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    createdBy: string;
    metadata: any;
    context: any;
}
export interface WorkflowTemplate {
    id: string;
    name: string;
    description: string;
    type: string;
    steps: Omit<WorkflowStep, 'id' | 'status' | 'startedAt' | 'completedAt' | 'duration' | 'error' | 'retries'>[];
    defaultContext: any;
    tags: string[];
}
/**
 * Orchestrateur de Workflows
 *
 * Gère l'exécution coordonnée de workflows complexes
 * impliquant plusieurs agents du système nerveux distribué
 */
export declare class WorkflowOrchestrator extends EventEmitter {
    private communication;
    private memory;
    private decisionEngine;
    private neuralNetwork;
    private activeWorkflows;
    private workflowTemplates;
    private executionQueue;
    private isProcessing;
    private maxConcurrentWorkflows;
    constructor(communication: SynapticCommunication, memory: CentralMemory, decisionEngine: DecisionEngine, neuralNetwork: NeuralNetworkManager);
    /**
     * Initialise les templates de workflow prédéfinis
     */
    private initializeTemplates;
    /**
     * Configuration des gestionnaires d'événements
     */
    private setupEventHandlers;
    /**
     * Enregistre un template de workflow
     */
    registerTemplate(template: WorkflowTemplate): void;
    /**
     * Crée un workflow à partir d'un template
     */
    createWorkflowFromTemplate(templateId: string, context: any, createdBy?: string): Promise<string>;
    /**
     * Démarre l'exécution d'un workflow
     */
    startWorkflow(workflowId: string): Promise<void>;
    /**
     * Processeur de queue d'exécution
     */
    private startQueueProcessor;
    /**
     * Traite la queue d'exécution
     */
    private processQueue;
    /**
     * Exécute un workflow
     */
    private executeWorkflow;
    /**
     * Trouve la prochaine étape exécutable
     */
    private findNextExecutableStep;
    /**
     * Vérifie si les dépendances d'une étape sont satisfaites
     */
    private areDependenciesMet;
    /**
     * Exécute une étape de workflow
     */
    private executeStep;
    /**
     * Résout les variables dans les inputs
     */
    private resolveVariables;
    /**
     * Évalue les conditions d'une étape
     */
    private evaluateConditions;
    /**
     * Sélectionne le meilleur agent pour une tâche
     */
    private selectBestAgent;
    /**
     * Gère les réponses des agents
     */
    private handleAgentResponse;
    /**
     * Gère les erreurs des agents
     */
    private handleAgentError;
    /**
     * Termine un workflow avec succès
     */
    private completeWorkflow;
    /**
     * Fait échouer un workflow
     */
    private failWorkflow;
    /**
     * Récupère un workflow
     */
    getWorkflow(workflowId: string): Workflow | undefined;
    /**
     * Récupère tous les workflows actifs
     */
    getActiveWorkflows(): Workflow[];
    /**
     * Récupère les templates disponibles
     */
    getTemplates(): WorkflowTemplate[];
    /**
     * Pause un workflow
     */
    pauseWorkflow(workflowId: string): Promise<void>;
    /**
     * Annule un workflow
     */
    cancelWorkflow(workflowId: string): Promise<void>;
    /**
     * Récupère le statut de l'orchestrateur
     */
    getStatus(): any;
}
//# sourceMappingURL=WorkflowOrchestrator.d.ts.map