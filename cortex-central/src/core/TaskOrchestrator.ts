import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';

export interface OrchestratorConfig {
  neuralNetwork: NeuralNetworkManager;
  memory: CentralMemory;
  communication: SynapticCommunication;
}

export interface OrchestrationRequest {
  taskId: string;
  plan: any;
  priority: string;
}

export interface OrchestrationResult {
  taskId: string;
  assignedAgents: string[];
  estimatedCompletion: Date;
  orchestrationPlan: any;
}

/**
 * Orchestrateur de Tâches - Coordinateur des Agents
 *
 * Responsable de l'attribution et de la coordination des tâches
 * entre les différents agents spécialisés du système.
 */
export class TaskOrchestrator extends EventEmitter {
  private neuralNetwork: NeuralNetworkManager;
  private memory: CentralMemory;
  private communication: SynapticCommunication;
  private isInitialized: boolean = false;

  // Suivi des tâches actives
  private activeTasks: Map<string, any> = new Map();

  // Métriques d'orchestration
  private orchestrationMetrics = {
    totalTasks: 0,
    successfulOrchestrations: 0,
    averageCompletionTime: 0,
    agentUtilization: new Map<string, number>(),
    coordinationEfficiency: 0,
    parallelTasksCount: 0,
    resourceOptimization: 0
  };

  // Coordination multi-agents avancée
  private agentCoordinator: Map<string, any> = new Map();
  private taskDependencyGraph: Map<string, string[]> = new Map();
  private resourcePool: Map<string, number> = new Map();

  constructor(config: OrchestratorConfig) {
    super();

    this.neuralNetwork = config.neuralNetwork;
    this.memory = config.memory;
    this.communication = config.communication;
  }

  /**
   * Initialise l'orchestrateur de tâches
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🎼 Initialisation de l\'Orchestrateur de Tâches...');

      // Configuration des événements de communication
      this.setupCommunicationEvents();

      // Initialisation de la coordination multi-agents
      await this.initializeAgentCoordination();

      // Démarrage du monitoring d'orchestration
      this.startOrchestrationMonitoring();

      // Démarrage de l'optimisation des ressources
      this.startResourceOptimization();

      this.isInitialized = true;
      logger.info('✅ Orchestrateur de Tâches initialisé');

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation de l\'Orchestrateur:', error);
      throw error;
    }
  }

  /**
   * Orchestre une tâche selon le plan stratégique
   */
  public async orchestrateTask(request: OrchestrationRequest): Promise<OrchestrationResult> {
    try {
      logger.info(`🎼 Orchestration de la tâche ${request.taskId}...`);

      const { taskId, plan, priority } = request;

      // Analyse des agents disponibles
      const availableAgents = await this.getAvailableAgents();

      // Sélection et attribution des agents avec coordination intelligente
      const assignedAgents = await this.selectAndAssignAgentsIntelligent(plan, availableAgents, priority);

      // Création du plan d'orchestration avec optimisation
      const orchestrationPlan = await this.createOptimizedOrchestrationPlan(plan, assignedAgents);

      // Analyse des dépendances et parallélisation
      await this.analyzeDependenciesAndParallelize(taskId, orchestrationPlan);

      // Envoi des instructions aux agents avec coordination
      await this.dispatchInstructionsWithCoordination(taskId, orchestrationPlan, assignedAgents);

      // Calcul de l'estimation de completion
      const estimatedCompletion = this.calculateEstimatedCompletion(plan);

      // Enregistrement de la tâche active
      this.activeTasks.set(taskId, {
        taskId,
        plan,
        assignedAgents,
        orchestrationPlan,
        startTime: new Date(),
        estimatedCompletion,
        status: 'orchestrated'
      });

      // Mise à jour des métriques
      this.orchestrationMetrics.totalTasks++;

      const result: OrchestrationResult = {
        taskId,
        assignedAgents,
        estimatedCompletion,
        orchestrationPlan
      };

      logger.info(`✅ Tâche ${taskId} orchestrée avec ${assignedAgents.length} agents`);

      this.emit('task-orchestrated', {
        taskId,
        assignedAgents,
        estimatedCompletion,
        timestamp: new Date()
      });

      return result;

    } catch (error) {
      logger.error(`❌ Erreur lors de l'orchestration de la tâche ${request.taskId}:`, error);
      throw error;
    }
  }

  /**
   * Récupère les agents disponibles
   */
  private async getAvailableAgents(): Promise<any[]> {
    try {
      const connectedAgents = this.neuralNetwork.getConnectedAgents();

      // Filtrage des agents disponibles (non surchargés)
      const availableAgents = connectedAgents.filter(agent => {
        const utilization = this.orchestrationMetrics.agentUtilization.get(agent.id) || 0;
        return utilization < 0.8; // Moins de 80% d'utilisation
      });

      logger.debug(`📊 ${availableAgents.length} agents disponibles sur ${connectedAgents.length} connectés`);
      return availableAgents;

    } catch (error) {
      logger.error('❌ Erreur lors de la récupération des agents disponibles:', error);
      return [];
    }
  }

  /**
   * Sélectionne et attribue les agents appropriés
   */
  private async selectAndAssignAgents(plan: any, availableAgents: any[], priority: string): Promise<string[]> {
    const assignedAgents: string[] = [];

    // Analyse des exigences d'agents du plan
    const requiredAgents = plan.requiredAgents || [];

    for (const requirement of requiredAgents) {
      const suitableAgents = availableAgents.filter(agent =>
        agent.type === requirement.agentType &&
        this.matchesSpecialization(agent, requirement.specialization)
      );

      if (suitableAgents.length === 0) {
        logger.warn(`⚠️ Aucun agent ${requirement.agentType} disponible`);
        continue;
      }

      // Sélection du meilleur agent basé sur les métriques
      const selectedAgent = this.selectBestAgent(suitableAgents, requirement, priority);

      if (selectedAgent && !assignedAgents.includes(selectedAgent.id)) {
        assignedAgents.push(selectedAgent.id);

        // Mise à jour de l'utilisation de l'agent
        const currentUtilization = this.orchestrationMetrics.agentUtilization.get(selectedAgent.id) || 0;
        this.orchestrationMetrics.agentUtilization.set(
          selectedAgent.id,
          Math.min(1.0, currentUtilization + (requirement.estimatedWorkload / 480)) // Normalisation sur 8h
        );
      }
    }

    return assignedAgents;
  }

  /**
   * Vérifie si un agent correspond à la spécialisation requise
   */
  private matchesSpecialization(agent: any, specialization: string): boolean {
    if (!agent.specializations) return true;

    return agent.specializations.some((spec: string) =>
      spec.toLowerCase().includes(specialization.toLowerCase()) ||
      specialization.toLowerCase().includes(spec.toLowerCase())
    );
  }

  /**
   * Sélectionne le meilleur agent parmi les candidats
   */
  private selectBestAgent(candidates: any[], requirement: any, priority: string): any {
    // Algorithme de sélection basé sur:
    // - Charge actuelle
    // - Historique de performance
    // - Spécialisation
    // - Priorité de la tâche

    let bestAgent = candidates[0];
    let bestScore = 0;

    for (const agent of candidates) {
      let score = 0;

      // Score basé sur la charge (moins chargé = meilleur)
      const utilization = this.orchestrationMetrics.agentUtilization.get(agent.id) || 0;
      score += (1 - utilization) * 40;

      // Score basé sur la spécialisation
      if (this.matchesSpecialization(agent, requirement.specialization)) {
        score += 30;
      }

      // Score basé sur l'historique de performance
      const performanceScore = agent.performanceMetrics?.successRate || 0.5;
      score += performanceScore * 20;

      // Bonus pour la priorité
      if (priority === 'high' || priority === 'critical') {
        score += agent.priority === 'high' ? 10 : 0;
      }

      if (score > bestScore) {
        bestScore = score;
        bestAgent = agent;
      }
    }

    return bestAgent;
  }

  /**
   * Crée le plan d'orchestration détaillé
   */
  private async createOrchestrationPlan(strategicPlan: any, assignedAgents: string[]): Promise<any> {
    const orchestrationPlan = {
      id: uuidv4(),
      phases: [],
      agentAssignments: new Map(),
      dependencies: [],
      timeline: []
    };

    // Mapping des phases aux agents
    for (const phase of strategicPlan.phases) {
      const phaseAgents = assignedAgents.filter(agentId => {
        const agent = this.neuralNetwork.getAgentById(agentId);
        return agent && phase.requiredAgents.includes(agent.type);
      });

      orchestrationPlan.phases.push({
        ...phase,
        assignedAgents: phaseAgents,
        orchestrationId: uuidv4()
      });

      // Enregistrement des assignations
      phaseAgents.forEach(agentId => {
        if (!orchestrationPlan.agentAssignments.has(agentId)) {
          orchestrationPlan.agentAssignments.set(agentId, []);
        }
        orchestrationPlan.agentAssignments.get(agentId).push(phase.id);
      });
    }

    // Création de la timeline
    orchestrationPlan.timeline = this.createExecutionTimeline(orchestrationPlan.phases);

    return orchestrationPlan;
  }

  /**
   * Crée la timeline d'exécution
   */
  private createExecutionTimeline(phases: any[]): any[] {
    const timeline = [];
    let currentTime = new Date();

    // Tri des phases par ordre et dépendances
    const sortedPhases = this.topologicalSort(phases);

    for (const phase of sortedPhases) {
      const startTime = new Date(currentTime);
      const endTime = new Date(currentTime.getTime() + phase.estimatedDuration * 60000); // minutes en ms

      timeline.push({
        phaseId: phase.id,
        startTime,
        endTime,
        duration: phase.estimatedDuration,
        assignedAgents: phase.assignedAgents
      });

      currentTime = endTime;
    }

    return timeline;
  }

  /**
   * Tri topologique des phases basé sur les dépendances
   */
  private topologicalSort(phases: any[]): any[] {
    const sorted = [];
    const visited = new Set();
    const visiting = new Set();

    const visit = (phase: any) => {
      if (visiting.has(phase.id)) {
        throw new Error(`Dépendance circulaire détectée: ${phase.id}`);
      }

      if (visited.has(phase.id)) {
        return;
      }

      visiting.add(phase.id);

      // Visiter les dépendances d'abord
      for (const depId of phase.dependencies) {
        const depPhase = phases.find(p => p.id === depId);
        if (depPhase) {
          visit(depPhase);
        }
      }

      visiting.delete(phase.id);
      visited.add(phase.id);
      sorted.push(phase);
    };

    for (const phase of phases) {
      if (!visited.has(phase.id)) {
        visit(phase);
      }
    }

    return sorted;
  }

  /**
   * Envoie les instructions aux agents assignés
   */
  private async dispatchInstructions(taskId: string, orchestrationPlan: any, assignedAgents: string[]): Promise<void> {
    for (const agentId of assignedAgents) {
      const agentTasks = orchestrationPlan.agentAssignments.get(agentId) || [];
      const agentPhases = orchestrationPlan.phases.filter((phase: any) =>
        agentTasks.includes(phase.id)
      );

      const instructions = {
        taskId,
        agentId,
        phases: agentPhases,
        timeline: orchestrationPlan.timeline.filter((item: any) =>
          item.assignedAgents.includes(agentId)
        ),
        priority: 'normal', // TODO: Récupérer la priorité réelle
        orchestrationId: orchestrationPlan.id
      };

      // Envoi via communication synaptique
      await this.communication.sendNeuralSignal({
        id: uuidv4(),
        type: 'task-assignment',
        fromAgent: 'cortex-central',
        toAgent: agentId,
        payload: instructions,
        timestamp: new Date(),
        priority: 'normal'
      });

      logger.debug(`📤 Instructions envoyées à l'agent ${agentId} pour la tâche ${taskId}`);
    }
  }

  /**
   * Calcule l'estimation de completion
   */
  private calculateEstimatedCompletion(plan: any): Date {
    const totalDuration = plan.totalEstimatedTime || 0;
    return new Date(Date.now() + totalDuration * 60000); // minutes en ms
  }

  /**
   * Configuration des événements de communication
   */
  private setupCommunicationEvents(): void {
    this.communication.on('task-completion', (data) => {
      this.handleTaskCompletion(data);
    });

    this.communication.on('agent-status-update', (data) => {
      this.handleAgentStatusUpdate(data);
    });

    this.communication.on('agent-error', (data) => {
      this.handleAgentError(data);
    });
  }

  /**
   * Gestion de la completion de tâches
   */
  private async handleTaskCompletion(data: any): Promise<void> {
    const { taskId, agentId, success } = data;

    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId);

      if (success) {
        this.orchestrationMetrics.successfulOrchestrations++;

        // Calcul du temps de completion réel
        const actualDuration = Date.now() - task.startTime.getTime();
        this.updateAverageCompletionTime(actualDuration);

        logger.info(`✅ Tâche ${taskId} complétée avec succès par l'agent ${agentId}`);
      }

      // Mise à jour de l'utilisation de l'agent
      this.updateAgentUtilization(agentId, -0.1); // Réduction de la charge
    }

    this.emit('task-completion-handled', {
      taskId,
      agentId,
      success,
      timestamp: new Date()
    });
  }

  /**
   * Gestion des mises à jour de statut d'agents
   */
  private handleAgentStatusUpdate(data: any): void {
    const { agentId, status, progress } = data;

    logger.debug(`📊 Mise à jour statut agent ${agentId}: ${status} (${progress}%)`);

    this.emit('agent-status-updated', {
      agentId,
      status,
      progress,
      timestamp: new Date()
    });
  }

  /**
   * Gestion des erreurs d'agents
   */
  private handleAgentError(data: any): void {
    const { agentId, taskId, error } = data;

    logger.error(`❌ Erreur agent ${agentId} sur tâche ${taskId}:`, error);

    // Tentative de réassignation si possible
    this.attemptTaskReassignment(taskId, agentId);

    this.emit('agent-error-handled', {
      agentId,
      taskId,
      error,
      timestamp: new Date()
    });
  }

  /**
   * Tentative de réassignation de tâche
   */
  private async attemptTaskReassignment(taskId: string, failedAgentId: string): Promise<void> {
    try {
      const task = this.activeTasks.get(taskId);
      if (!task) return;

      // Recherche d'agents de remplacement
      const availableAgents = await this.getAvailableAgents();
      const failedAgent = this.neuralNetwork.getAgentById(failedAgentId);

      if (!failedAgent) return;

      const replacementAgents = availableAgents.filter(agent =>
        agent.type === failedAgent.type && agent.id !== failedAgentId
      );

      if (replacementAgents.length > 0) {
        const replacementAgent = replacementAgents[0];

        // Mise à jour des assignations
        const index = task.assignedAgents.indexOf(failedAgentId);
        if (index !== -1) {
          task.assignedAgents[index] = replacementAgent.id;
        }

        logger.info(`🔄 Tâche ${taskId} réassignée de ${failedAgentId} à ${replacementAgent.id}`);

        this.emit('task-reassigned', {
          taskId,
          fromAgent: failedAgentId,
          toAgent: replacementAgent.id,
          timestamp: new Date()
        });
      }

    } catch (error) {
      logger.error('❌ Erreur lors de la réassignation de tâche:', error);
    }
  }

  /**
   * Met à jour le temps de completion moyen
   */
  private updateAverageCompletionTime(actualDuration: number): void {
    const currentAverage = this.orchestrationMetrics.averageCompletionTime;
    const totalTasks = this.orchestrationMetrics.successfulOrchestrations;

    this.orchestrationMetrics.averageCompletionTime =
      ((currentAverage * (totalTasks - 1)) + actualDuration) / totalTasks;
  }

  /**
   * Met à jour l'utilisation d'un agent
   */
  private updateAgentUtilization(agentId: string, delta: number): void {
    const currentUtilization = this.orchestrationMetrics.agentUtilization.get(agentId) || 0;
    const newUtilization = Math.max(0, Math.min(1, currentUtilization + delta));
    this.orchestrationMetrics.agentUtilization.set(agentId, newUtilization);
  }

  /**
   * Démarrage du monitoring d'orchestration
   */
  private startOrchestrationMonitoring(): void {
    setInterval(() => {
      this.performOrchestrationHealthCheck();
    }, 60000); // Toutes les minutes
  }

  /**
   * Vérification de santé de l'orchestration
   */
  private performOrchestrationHealthCheck(): void {
    const metrics = {
      activeTasks: this.activeTasks.size,
      totalTasks: this.orchestrationMetrics.totalTasks,
      successRate: this.orchestrationMetrics.successfulOrchestrations / Math.max(1, this.orchestrationMetrics.totalTasks),
      averageCompletionTime: this.orchestrationMetrics.averageCompletionTime,
      agentUtilization: Object.fromEntries(this.orchestrationMetrics.agentUtilization),
      timestamp: new Date()
    };

    this.emit('orchestration-health-check', metrics);
  }

  /**
   * Initialise la coordination multi-agents
   */
  private async initializeAgentCoordination(): Promise<void> {
    // Initialisation du pool de ressources
    this.resourcePool.set('cpu', 1.0);
    this.resourcePool.set('memory', 1.0);
    this.resourcePool.set('network', 1.0);
    this.resourcePool.set('storage', 1.0);

    // Configuration des coordinateurs d'agents
    const agentTypes = ['frontend', 'backend', 'uiux', 'qa', 'devops', 'security'];

    for (const agentType of agentTypes) {
      this.agentCoordinator.set(agentType, {
        type: agentType,
        maxConcurrentTasks: 3,
        currentTasks: 0,
        efficiency: 0.8,
        lastOptimization: new Date()
      });
    }

    logger.info('🤝 Coordination multi-agents initialisée');
  }

  /**
   * Sélection et attribution intelligente des agents
   */
  private async selectAndAssignAgentsIntelligent(plan: any, availableAgents: any[], priority: string): Promise<string[]> {
    const assignedAgents: string[] = [];

    // Analyse des besoins en ressources
    const resourceRequirements = this.analyzeResourceRequirements(plan);

    // Optimisation de l'attribution basée sur l'efficacité
    for (const requirement of plan.requiredAgents) {
      const suitableAgents = availableAgents.filter(agent => {
        return this.matchesSpecialization(agent, requirement.agentType) &&
               this.hasAvailableCapacity(agent.id, requirement.estimatedWorkload) &&
               this.meetsResourceRequirements(agent.id, resourceRequirements);
      });

      if (suitableAgents.length === 0) {
        logger.warn(`⚠️ Aucun agent disponible pour: ${requirement.agentType}`);
        continue;
      }

      // Sélection intelligente basée sur l'efficacité et la charge
      const selectedAgent = this.selectOptimalAgent(suitableAgents, requirement, priority);

      if (selectedAgent && !assignedAgents.includes(selectedAgent.id)) {
        assignedAgents.push(selectedAgent.id);

        // Mise à jour de la coordination
        await this.updateAgentCoordination(selectedAgent.id, requirement);
      }
    }

    return assignedAgents;
  }

  /**
   * Création d'un plan d'orchestration optimisé
   */
  private async createOptimizedOrchestrationPlan(strategicPlan: any, assignedAgents: string[]): Promise<any> {
    const orchestrationPlan = {
      id: uuidv4(),
      phases: [],
      agentAssignments: new Map(),
      dependencies: [],
      timeline: [],
      parallelGroups: [],
      resourceAllocation: new Map()
    };

    // Analyse des possibilités de parallélisation
    const parallelGroups = this.identifyParallelGroups(strategicPlan.phases);

    // Optimisation de l'ordre d'exécution
    const optimizedPhases = this.optimizeExecutionOrder(strategicPlan.phases, assignedAgents);

    // Allocation des ressources
    for (const phase of optimizedPhases) {
      const phaseAgents = assignedAgents.filter(agentId => {
        const agent = this.neuralNetwork.getAgentById(agentId);
        return agent && phase.requiredAgents.some((req: any) => req.agentType === agent.type);
      });

      const optimizedPhase = {
        ...phase,
        assignedAgents: phaseAgents,
        orchestrationId: uuidv4(),
        resourceAllocation: this.allocateResources(phase, phaseAgents),
        parallelGroup: this.findParallelGroup(phase.id, parallelGroups)
      };

      orchestrationPlan.phases.push(optimizedPhase);

      // Enregistrement des assignations
      phaseAgents.forEach(agentId => {
        if (!orchestrationPlan.agentAssignments.has(agentId)) {
          orchestrationPlan.agentAssignments.set(agentId, []);
        }
        orchestrationPlan.agentAssignments.get(agentId).push(phase.id);
      });
    }

    // Création de la timeline optimisée
    orchestrationPlan.timeline = this.createOptimizedTimeline(orchestrationPlan.phases, parallelGroups);
    orchestrationPlan.parallelGroups = parallelGroups;

    return orchestrationPlan;
  }

  /**
   * Analyse des dépendances et parallélisation
   */
  private async analyzeDependenciesAndParallelize(taskId: string, orchestrationPlan: any): Promise<void> {
    // Construction du graphe de dépendances
    const dependencyGraph = this.buildDependencyGraph(orchestrationPlan.phases);
    this.taskDependencyGraph.set(taskId, dependencyGraph);

    // Identification des tâches parallélisables
    const parallelTasks = this.identifyParallelTasks(dependencyGraph);

    // Mise à jour des métriques
    this.orchestrationMetrics.parallelTasksCount = parallelTasks.length;

    logger.info(`📊 Analyse des dépendances: ${parallelTasks.length} tâches parallélisables identifiées`);
  }

  /**
   * Envoi d'instructions avec coordination
   */
  private async dispatchInstructionsWithCoordination(taskId: string, orchestrationPlan: any, assignedAgents: string[]): Promise<void> {
    // Coordination des agents pour éviter les conflits
    const coordinationMatrix = this.buildCoordinationMatrix(assignedAgents);

    for (const agentId of assignedAgents) {
      const agentTasks = orchestrationPlan.agentAssignments.get(agentId) || [];
      const agentPhases = orchestrationPlan.phases.filter((phase: any) =>
        agentTasks.includes(phase.id)
      );

      // Instructions enrichies avec coordination
      const instructions = {
        taskId,
        agentId,
        phases: agentPhases,
        timeline: orchestrationPlan.timeline.filter((item: any) =>
          item.assignedAgents.includes(agentId)
        ),
        coordination: {
          matrix: coordinationMatrix,
          dependencies: this.getAgentDependencies(agentId, orchestrationPlan),
          resourceAllocation: orchestrationPlan.resourceAllocation.get(agentId)
        },
        priority: 'normal',
        orchestrationId: orchestrationPlan.id
      };

      // Envoi via communication synaptique avec coordination
      await this.communication.sendNeuralSignal({
        id: uuidv4(),
        type: 'coordinated-task-assignment',
        fromAgent: 'cortex-central',
        toAgent: agentId,
        payload: instructions,
        timestamp: new Date(),
        priority: 'normal',
        coordination: true
      });

      logger.debug(`🤝 Instructions coordonnées envoyées à l'agent ${agentId} pour la tâche ${taskId}`);
    }
  }

  /**
   * Analyse des exigences en ressources
   */
  private analyzeResourceRequirements(plan: any): any {
    const requirements = {
      cpu: 0,
      memory: 0,
      network: 0,
      storage: 0
    };

    for (const phase of plan.phases) {
      // Estimation basée sur la complexité et la durée
      const complexity = phase.complexity || 'medium';
      const duration = phase.estimatedDuration || 60;

      const multiplier = {
        'low': 0.3,
        'medium': 0.6,
        'high': 0.9,
        'critical': 1.2
      }[complexity] || 0.6;

      requirements.cpu += (duration / 60) * multiplier * 0.2;
      requirements.memory += (duration / 60) * multiplier * 0.15;
      requirements.network += (duration / 60) * multiplier * 0.1;
      requirements.storage += (duration / 60) * multiplier * 0.05;
    }

    return requirements;
  }

  /**
   * Vérifie la capacité disponible d'un agent
   */
  private hasAvailableCapacity(agentId: string, requiredWorkload: number): boolean {
    const currentUtilization = this.orchestrationMetrics.agentUtilization.get(agentId) || 0;
    return (currentUtilization + requiredWorkload) <= 0.9; // 90% max
  }

  /**
   * Vérifie si l'agent répond aux exigences en ressources
   */
  private meetsResourceRequirements(agentId: string, requirements: any): boolean {
    // Simulation - dans un vrai système, vérifier les ressources réelles
    return true;
  }

  /**
   * Sélectionne l'agent optimal
   */
  private selectOptimalAgent(candidates: any[], requirement: any, priority: string): any {
    let bestAgent = candidates[0];
    let bestScore = 0;

    for (const agent of candidates) {
      let score = 0;

      // Score basé sur l'efficacité
      const coordinator = this.agentCoordinator.get(agent.type);
      if (coordinator) {
        score += coordinator.efficiency * 30;
      }

      // Score basé sur la charge actuelle
      const utilization = this.orchestrationMetrics.agentUtilization.get(agent.id) || 0;
      score += (1 - utilization) * 40;

      // Score basé sur la spécialisation
      if (this.matchesSpecialization(agent, requirement.agentType)) {
        score += 20;
      }

      // Score basé sur l'historique de performance
      const performanceScore = agent.performanceMetrics?.successRate || 0.5;
      score += performanceScore * 10;

      if (score > bestScore) {
        bestScore = score;
        bestAgent = agent;
      }
    }

    return bestAgent;
  }

  /**
   * Met à jour la coordination d'un agent
   */
  private async updateAgentCoordination(agentId: string, requirement: any): Promise<void> {
    const agent = this.neuralNetwork.getAgentById(agentId);
    if (!agent) return;

    const coordinator = this.agentCoordinator.get(agent.type);
    if (coordinator) {
      coordinator.currentTasks++;

      // Mise à jour de l'utilisation
      const currentUtilization = this.orchestrationMetrics.agentUtilization.get(agentId) || 0;
      this.orchestrationMetrics.agentUtilization.set(
        agentId,
        Math.min(1.0, currentUtilization + (requirement.estimatedWorkload / 480))
      );
    }
  }

  /**
   * Démarre l'optimisation des ressources
   */
  private startResourceOptimization(): void {
    setInterval(() => {
      this.optimizeResourceAllocation();
    }, 300000); // Toutes les 5 minutes
  }

  /**
   * Optimise l'allocation des ressources
   */
  private optimizeResourceAllocation(): void {
    // Calcul de l'efficacité de coordination
    const totalAgents = this.agentCoordinator.size;
    const activeAgents = Array.from(this.agentCoordinator.values())
      .filter(coord => coord.currentTasks > 0).length;

    this.orchestrationMetrics.coordinationEfficiency = totalAgents > 0 ? activeAgents / totalAgents : 0;

    // Calcul de l'optimisation des ressources
    const totalUtilization = Array.from(this.orchestrationMetrics.agentUtilization.values())
      .reduce((sum, util) => sum + util, 0);

    const avgUtilization = totalUtilization / Math.max(1, this.orchestrationMetrics.agentUtilization.size);
    this.orchestrationMetrics.resourceOptimization = Math.min(1, avgUtilization * 1.2);

    this.emit('resource-optimization', {
      coordinationEfficiency: this.orchestrationMetrics.coordinationEfficiency,
      resourceOptimization: this.orchestrationMetrics.resourceOptimization,
      timestamp: new Date()
    });
  }

  /**
   * Récupère le statut de l'orchestrateur
   */
  public getStatus(): any {
    return {
      isInitialized: this.isInitialized,
      activeTasks: this.activeTasks.size,
      metrics: {
        totalTasks: this.orchestrationMetrics.totalTasks,
        successfulOrchestrations: this.orchestrationMetrics.successfulOrchestrations,
        successRate: this.orchestrationMetrics.successfulOrchestrations / Math.max(1, this.orchestrationMetrics.totalTasks),
        averageCompletionTime: this.orchestrationMetrics.averageCompletionTime,
        agentUtilization: Object.fromEntries(this.orchestrationMetrics.agentUtilization)
      }
    };
  }

  /**
   * Arrêt gracieux
   */
  public async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt de l\'Orchestrateur de Tâches...');

    // Sauvegarde des tâches actives
    for (const [taskId, task] of this.activeTasks) {
      await this.memory.storeTaskStatus(taskId, task);
    }

    logger.info('✅ Orchestrateur de Tâches arrêté');
  }
}
