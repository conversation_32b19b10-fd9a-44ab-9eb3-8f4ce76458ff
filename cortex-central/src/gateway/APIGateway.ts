import { EventEmitter } from 'events';
import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { logger } from '../utils/logger';
import { CentralMemory } from '../memory/CentralMemory';
import { SynapticCommunication } from '../communication/SynapticCommunication';

export interface APIGatewayConfig {
  cortex: any;
  memory: CentralMemory;
  communication: SynapticCommunication;
  port?: number;
  enableAuth?: boolean;
  enableRateLimit?: boolean;
}

export interface APIRoute {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  handler: (req: Request, res: Response) => Promise<void>;
  middleware?: Array<(req: Request, res: Response, next: NextFunction) => void>;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
}

export interface GatewayMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  activeConnections: number;
  rateLimitHits: number;
  lastRequestTime: Date;
}

/**
 * API Gateway - Passerelle API Unifiée
 * 
 * Point d'entrée unique pour toutes les interactions avec le système.
 * Gère l'authentification, la limitation de débit, le routage intelligent,
 * et la coordination avec les agents spécialisés.
 */
export class APIGateway extends EventEmitter {
  private app: Express;
  private cortex: any;
  private memory: CentralMemory;
  private communication: SynapticCommunication;
  private config: APIGatewayConfig;
  private server: any;
  private isInitialized: boolean = false;
  
  // Métriques et monitoring
  private metrics: GatewayMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    activeConnections: 0,
    rateLimitHits: 0,
    lastRequestTime: new Date()
  };
  
  // Routes enregistrées
  private registeredRoutes: Map<string, APIRoute> = new Map();
  
  // Cache des réponses
  private responseCache: Map<string, any> = new Map();

  constructor(config: APIGatewayConfig) {
    super();
    
    this.config = config;
    this.cortex = config.cortex;
    this.memory = config.memory;
    this.communication = config.communication;
    
    this.app = express();
    this.setupMiddleware();
    this.setupCoreRoutes();
  }

  /**
   * Initialise l'API Gateway
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🌐 Initialisation de l\'API Gateway...');

      // Configuration des routes dynamiques
      await this.setupDynamicRoutes();

      // Configuration des événements de communication
      this.setupCommunicationEvents();

      // Démarrage du serveur
      await this.startServer();

      // Démarrage du monitoring
      this.startMetricsCollection();

      this.isInitialized = true;
      logger.info('✅ API Gateway initialisé avec succès');

      this.emit('gateway-initialized', {
        timestamp: new Date(),
        port: this.config.port || 3000
      });

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation de l\'API Gateway:', error);
      throw error;
    }
  }

  /**
   * Configure les middlewares de base
   */
  private setupMiddleware(): void {
    // Sécurité
    this.app.use(helmet());
    
    // CORS
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true
    }));

    // Parsing JSON
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Rate limiting global
    if (this.config.enableRateLimit !== false) {
      const limiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 1000, // limite de 1000 requêtes par fenêtre
        message: 'Trop de requêtes, veuillez réessayer plus tard.',
        standardHeaders: true,
        legacyHeaders: false,
        handler: (req, res) => {
          this.metrics.rateLimitHits++;
          res.status(429).json({
            error: 'Rate limit exceeded',
            retryAfter: Math.round(15 * 60 * 1000 / 1000)
          });
        }
      });
      
      this.app.use(limiter);
    }

    // Middleware de métriques
    this.app.use(this.metricsMiddleware.bind(this));

    // Middleware d'authentification
    if (this.config.enableAuth !== false) {
      this.app.use(this.authMiddleware.bind(this));
    }
  }

  /**
   * Configure les routes principales
   */
  private setupCoreRoutes(): void {
    // Route de santé
    this.app.get('/health', async (req: Request, res: Response) => {
      try {
        const health = await this.cortex.getHealthStatus();
        res.json({
          status: 'healthy',
          timestamp: new Date(),
          cortex: health,
          gateway: {
            uptime: Date.now() - this.cortex.startTime.getTime(),
            metrics: this.metrics
          }
        });
      } catch (error) {
        res.status(500).json({
          status: 'unhealthy',
          error: error.message
        });
      }
    });

    // Route de métriques
    this.app.get('/metrics', async (req: Request, res: Response) => {
      res.json({
        gateway: this.metrics,
        system: await this.getSystemMetrics()
      });
    });

    // Route d'instructions au Cortex
    this.app.post('/cortex/instructions', async (req: Request, res: Response) => {
      try {
        const { instructions, priority = 'medium', context } = req.body;
        
        if (!instructions) {
          return res.status(400).json({
            error: 'Instructions requises'
          });
        }

        const taskId = await this.cortex.processInstructions({
          instructions,
          priority,
          context
        });

        res.json({
          success: true,
          taskId,
          message: 'Instructions transmises au Cortex Central'
        });

      } catch (error) {
        logger.error('Erreur lors du traitement des instructions:', error);
        res.status(500).json({
          error: 'Erreur interne du serveur',
          details: error.message
        });
      }
    });

    // Route de statut des tâches
    this.app.get('/cortex/tasks/:taskId', async (req: Request, res: Response) => {
      try {
        const { taskId } = req.params;
        const taskStatus = await this.cortex.getTaskStatus(taskId);
        
        if (!taskStatus) {
          return res.status(404).json({
            error: 'Tâche non trouvée'
          });
        }

        res.json(taskStatus);

      } catch (error) {
        logger.error('Erreur lors de la récupération du statut:', error);
        res.status(500).json({
          error: 'Erreur interne du serveur'
        });
      }
    });

    // Route de communication avec les agents
    this.app.post('/agents/:agentId/message', async (req: Request, res: Response) => {
      try {
        const { agentId } = req.params;
        const { message, data } = req.body;

        const response = await this.communication.send(agentId, {
          message,
          data,
          timestamp: new Date()
        });

        res.json({
          success: true,
          response
        });

      } catch (error) {
        logger.error('Erreur lors de la communication avec l\'agent:', error);
        res.status(500).json({
          error: 'Erreur de communication'
        });
      }
    });
  }

  /**
   * Middleware de collecte de métriques
   */
  private metricsMiddleware(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    
    this.metrics.totalRequests++;
    this.metrics.activeConnections++;
    this.metrics.lastRequestTime = new Date();

    res.on('finish', () => {
      const responseTime = Date.now() - startTime;
      
      // Mise à jour des métriques
      this.metrics.activeConnections--;
      
      if (res.statusCode >= 200 && res.statusCode < 400) {
        this.metrics.successfulRequests++;
      } else {
        this.metrics.failedRequests++;
      }

      // Calcul du temps de réponse moyen
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
        this.metrics.totalRequests;

      // Émission d'événement pour monitoring
      this.emit('request-completed', {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        responseTime,
        timestamp: new Date()
      });
    });

    next();
  }

  /**
   * Middleware d'authentification
   */
  private authMiddleware(req: Request, res: Response, next: NextFunction): void {
    // Routes publiques
    const publicRoutes = ['/health', '/metrics'];
    if (publicRoutes.includes(req.path)) {
      return next();
    }

    // Vérification du token d'API
    const apiKey = req.headers['x-api-key'] || req.headers['authorization'];
    
    if (!apiKey) {
      return res.status(401).json({
        error: 'Token d\'authentification requis'
      });
    }

    // Validation du token (à implémenter selon vos besoins)
    if (!this.validateApiKey(apiKey as string)) {
      return res.status(403).json({
        error: 'Token d\'authentification invalide'
      });
    }

    next();
  }

  /**
   * Valide une clé API
   */
  private validateApiKey(apiKey: string): boolean {
    // Implémentation basique - à améliorer selon vos besoins
    const validKeys = process.env.API_KEYS?.split(',') || ['default-key'];
    return validKeys.includes(apiKey.replace('Bearer ', ''));
  }

  /**
   * Démarre le serveur HTTP
   */
  private async startServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      const port = this.config.port || 3000;
      
      this.server = this.app.listen(port, () => {
        logger.info(`🌐 API Gateway démarré sur le port ${port}`);
        resolve();
      });

      this.server.on('error', (error: any) => {
        logger.error('Erreur du serveur API Gateway:', error);
        reject(error);
      });
    });
  }

  /**
   * Obtient les métriques système
   */
  private async getSystemMetrics(): Promise<any> {
    try {
      const memoryStatus = await this.memory.getStatus();
      const neuralStatus = await this.cortex.neuralNetwork.getStatus();
      
      return {
        memory: memoryStatus,
        neural: neuralStatus,
        uptime: Date.now() - this.cortex.startTime.getTime()
      };
    } catch (error) {
      logger.error('Erreur lors de la récupération des métriques système:', error);
      return {};
    }
  }

  /**
   * Configure les événements de communication
   */
  private setupCommunicationEvents(): void {
    this.communication.on('agent-message', (data) => {
      this.emit('agent-communication', data);
    });

    this.communication.on('system-alert', (alert) => {
      this.emit('system-alert', alert);
    });
  }

  /**
   * Configure les routes dynamiques
   */
  private async setupDynamicRoutes(): Promise<void> {
    // Routes dynamiques basées sur les agents disponibles
    // À implémenter selon les besoins spécifiques
  }

  /**
   * Démarre la collecte de métriques
   */
  private startMetricsCollection(): void {
    setInterval(() => {
      this.emit('metrics-update', this.metrics);
    }, 30000); // Toutes les 30 secondes
  }

  /**
   * Arrête l'API Gateway
   */
  public async shutdown(): Promise<void> {
    if (this.server) {
      await new Promise<void>((resolve) => {
        this.server.close(() => {
          logger.info('🌐 API Gateway arrêté');
          resolve();
        });
      });
    }
  }

  /**
   * Obtient les métriques actuelles
   */
  public getMetrics(): GatewayMetrics {
    return { ...this.metrics };
  }

  /**
   * Vérifie si le gateway est initialisé
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}
