import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { CentralMemory } from '../memory/CentralMemory';
import {
  Anomaly,
  HealingStrategy,
  HealingResult,
  AdaptationRule,
  AdaptationEngineConfig
} from './types';

/**
 * Moteur d'Adaptation - Apprentissage et Évolution
 * 
 * Responsable de l'apprentissage à partir des succès et échecs
 * de guérison pour améliorer les stratégies futures.
 */
export class AdaptationEngine extends EventEmitter {
  private memory: CentralMemory;
  private learningRate: number;
  private confidenceThreshold: number;
  private maxRules: number;
  private enableAutoCreation: boolean;
  
  private isInitialized: boolean = false;
  private adaptationRules: Map<string, AdaptationRule> = new Map();
  private learningHistory: Array<{
    anomaly: Anomaly;
    strategy: HealingStrategy;
    result: HealingResult;
    timestamp: Date;
  }> = [];
  
  // Statistiques d'apprentissage
  private learningStats = {
    totalLearningEvents: 0,
    rulesCreated: 0,
    rulesUpdated: 0,
    averageConfidence: 0,
    lastLearning: null as Date | null
  };

  constructor(config: AdaptationEngineConfig) {
    super();
    
    this.memory = config.memory;
    this.learningRate = config.learningRate || 0.1;
    this.confidenceThreshold = config.confidenceThreshold || 0.7;
    this.maxRules = config.maxRules || 1000;
    this.enableAutoCreation = config.enableAutoCreation !== false;
  }

  /**
   * Initialise le moteur d'adaptation
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation du Moteur d\'Adaptation...');

      // Chargement des règles d'adaptation depuis la mémoire
      await this.loadAdaptationRules();
      
      // Chargement de l'historique d'apprentissage
      await this.loadLearningHistory();

      this.isInitialized = true;
      logger.info(`✅ Moteur d'Adaptation initialisé avec ${this.adaptationRules.size} règles`);

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du Moteur d\'Adaptation:', error);
      throw error;
    }
  }

  /**
   * Apprend d'un résultat de guérison
   */
  public async learn(
    anomaly: Anomaly, 
    strategy: HealingStrategy, 
    result: HealingResult
  ): Promise<AdaptationRule | null> {
    try {
      logger.debug(`🎓 Apprentissage à partir du résultat: ${result.success ? 'succès' : 'échec'}`);

      // Enregistrement dans l'historique
      this.learningHistory.push({
        anomaly,
        strategy,
        result,
        timestamp: new Date()
      });

      // Limitation de l'historique
      if (this.learningHistory.length > 10000) {
        this.learningHistory.shift();
      }

      // Mise à jour des statistiques
      this.learningStats.totalLearningEvents++;
      this.learningStats.lastLearning = new Date();

      let adaptationRule: AdaptationRule | null = null;

      if (result.success && !result.rollbackPerformed) {
        // Apprentissage à partir d'un succès
        adaptationRule = await this.learnFromSuccess(anomaly, strategy, result);
      } else {
        // Apprentissage à partir d'un échec
        adaptationRule = await this.learnFromFailure(anomaly, strategy, result);
      }

      // Sauvegarde en mémoire
      await this.saveAdaptationRules();
      await this.saveLearningHistory();

      return adaptationRule;

    } catch (error) {
      logger.error('❌ Erreur lors de l\'apprentissage:', error);
      return null;
    }
  }

  /**
   * Applique une règle d'adaptation
   */
  public async apply(adaptationRule: AdaptationRule): Promise<void> {
    try {
      logger.info(`🔧 Application de la règle d'adaptation: ${adaptationRule.name}`);

      // Mise à jour de la dernière utilisation
      adaptationRule.lastUsed = new Date();
      this.adaptationRules.set(adaptationRule.id, adaptationRule);

      // Émission d'événement pour notification
      this.emit('adaptation-applied', {
        rule: adaptationRule,
        timestamp: new Date()
      });

      // Sauvegarde
      await this.saveAdaptationRules();

    } catch (error) {
      logger.error('❌ Erreur lors de l\'application de l\'adaptation:', error);
      throw error;
    }
  }

  /**
   * Obtient les règles d'adaptation applicables à une situation
   */
  public getApplicableRules(anomaly: Anomaly): AdaptationRule[] {
    const applicableRules: AdaptationRule[] = [];
    
    for (const rule of this.adaptationRules.values()) {
      if (this.isRuleApplicable(rule, anomaly)) {
        applicableRules.push(rule);
      }
    }
    
    // Tri par confiance décroissante
    applicableRules.sort((a, b) => b.confidence - a.confidence);
    
    return applicableRules.filter(rule => rule.confidence >= this.confidenceThreshold);
  }

  /**
   * Obtient les statistiques d'apprentissage
   */
  public getStats() {
    return {
      ...this.learningStats,
      totalRules: this.adaptationRules.size,
      highConfidenceRules: Array.from(this.adaptationRules.values())
        .filter(rule => rule.confidence >= this.confidenceThreshold).length
    };
  }

  /**
   * Apprend à partir d'un succès
   */
  private async learnFromSuccess(
    anomaly: Anomaly, 
    strategy: HealingStrategy, 
    result: HealingResult
  ): Promise<AdaptationRule | null> {
    const ruleId = `success_${anomaly.type}_${strategy.id}`;
    let rule = this.adaptationRules.get(ruleId);
    
    if (rule) {
      // Mise à jour d'une règle existante
      rule.successRate = this.updateSuccessRate(rule.successRate, true);
      rule.confidence = Math.min(1.0, rule.confidence + this.learningRate);
      this.learningStats.rulesUpdated++;
    } else if (this.enableAutoCreation && this.adaptationRules.size < this.maxRules) {
      // Création d'une nouvelle règle
      rule = {
        id: ruleId,
        name: `Succès ${strategy.name} pour ${anomaly.type}`,
        description: `Stratégie ${strategy.name} efficace pour les anomalies de type ${anomaly.type}`,
        condition: `anomaly.type === '${anomaly.type}' && anomaly.severity === '${anomaly.severity}'`,
        action: `prefer_strategy('${strategy.id}')`,
        confidence: this.learningRate,
        successRate: 1.0,
        lastUsed: new Date(),
        createdFrom: { anomaly, strategy, result }
      };
      
      this.adaptationRules.set(ruleId, rule);
      this.learningStats.rulesCreated++;
    }
    
    if (rule) {
      this.updateAverageConfidence();
      logger.debug(`📈 Règle mise à jour: ${rule.name} (confiance: ${rule.confidence.toFixed(2)})`);
    }
    
    return rule;
  }

  /**
   * Apprend à partir d'un échec
   */
  private async learnFromFailure(
    anomaly: Anomaly, 
    strategy: HealingStrategy, 
    result: HealingResult
  ): Promise<AdaptationRule | null> {
    const ruleId = `failure_${anomaly.type}_${strategy.id}`;
    let rule = this.adaptationRules.get(ruleId);
    
    if (rule) {
      // Mise à jour d'une règle existante
      rule.successRate = this.updateSuccessRate(rule.successRate, false);
      rule.confidence = Math.max(0.0, rule.confidence - this.learningRate);
      this.learningStats.rulesUpdated++;
    } else if (this.enableAutoCreation && this.adaptationRules.size < this.maxRules) {
      // Création d'une nouvelle règle d'évitement
      rule = {
        id: ruleId,
        name: `Éviter ${strategy.name} pour ${anomaly.type}`,
        description: `Stratégie ${strategy.name} inefficace pour les anomalies de type ${anomaly.type}`,
        condition: `anomaly.type === '${anomaly.type}' && anomaly.severity === '${anomaly.severity}'`,
        action: `avoid_strategy('${strategy.id}')`,
        confidence: this.learningRate,
        successRate: 0.0,
        lastUsed: new Date(),
        createdFrom: { anomaly, strategy, result }
      };
      
      this.adaptationRules.set(ruleId, rule);
      this.learningStats.rulesCreated++;
    }
    
    // Recherche de règles de succès contradictoires à ajuster
    const successRuleId = `success_${anomaly.type}_${strategy.id}`;
    const successRule = this.adaptationRules.get(successRuleId);
    if (successRule) {
      successRule.successRate = this.updateSuccessRate(successRule.successRate, false);
      successRule.confidence = Math.max(0.0, successRule.confidence - this.learningRate * 0.5);
    }
    
    if (rule) {
      this.updateAverageConfidence();
      logger.debug(`📉 Règle d'évitement créée/mise à jour: ${rule.name} (confiance: ${rule.confidence.toFixed(2)})`);
    }
    
    return rule;
  }

  /**
   * Met à jour le taux de succès avec une moyenne mobile
   */
  private updateSuccessRate(currentRate: number, success: boolean): number {
    const newValue = success ? 1.0 : 0.0;
    return currentRate * (1 - this.learningRate) + newValue * this.learningRate;
  }

  /**
   * Vérifie si une règle est applicable à une anomalie
   */
  private isRuleApplicable(rule: AdaptationRule, anomaly: Anomaly): boolean {
    try {
      // Évaluation simplifiée de la condition
      // Dans une implémentation complète, on utiliserait un moteur d'évaluation plus sophistiqué
      const condition = rule.condition
        .replace(/anomaly\.type/g, `'${anomaly.type}'`)
        .replace(/anomaly\.severity/g, `'${anomaly.severity}'`);
      
      // Évaluation basique (à améliorer pour la production)
      if (condition.includes(`'${anomaly.type}'`) && condition.includes(`'${anomaly.severity}'`)) {
        return true;
      }
      
      return false;
    } catch (error) {
      logger.warn(`⚠️ Erreur lors de l'évaluation de la règle ${rule.id}:`, error);
      return false;
    }
  }

  /**
   * Met à jour la confiance moyenne
   */
  private updateAverageConfidence(): void {
    const rules = Array.from(this.adaptationRules.values());
    if (rules.length > 0) {
      const totalConfidence = rules.reduce((sum, rule) => sum + rule.confidence, 0);
      this.learningStats.averageConfidence = totalConfidence / rules.length;
    }
  }

  /**
   * Charge les règles d'adaptation depuis la mémoire
   */
  private async loadAdaptationRules(): Promise<void> {
    try {
      const rules = await this.memory.retrieve('adaptation_rules');
      if (rules && Array.isArray(rules)) {
        rules.forEach((rule: AdaptationRule) => {
          this.adaptationRules.set(rule.id, rule);
        });
        logger.info(`📖 ${rules.length} règles d'adaptation chargées depuis la mémoire`);
      }
    } catch (error) {
      logger.warn('⚠️ Impossible de charger les règles d\'adaptation:', error);
    }
  }

  /**
   * Sauvegarde les règles d'adaptation en mémoire
   */
  private async saveAdaptationRules(): Promise<void> {
    try {
      const rules = Array.from(this.adaptationRules.values());
      await this.memory.store('adaptation_rules', rules);
    } catch (error) {
      logger.error('❌ Erreur lors de la sauvegarde des règles d\'adaptation:', error);
    }
  }

  /**
   * Charge l'historique d'apprentissage depuis la mémoire
   */
  private async loadLearningHistory(): Promise<void> {
    try {
      const history = await this.memory.retrieve('learning_history');
      if (history && Array.isArray(history)) {
        this.learningHistory = history.slice(-1000); // Garde seulement les 1000 derniers
        logger.info(`📚 Historique d'apprentissage chargé: ${this.learningHistory.length} entrées`);
      }
    } catch (error) {
      logger.warn('⚠️ Impossible de charger l\'historique d\'apprentissage:', error);
    }
  }

  /**
   * Sauvegarde l'historique d'apprentissage en mémoire
   */
  private async saveLearningHistory(): Promise<void> {
    try {
      // Garde seulement les 1000 dernières entrées pour éviter une croissance excessive
      const recentHistory = this.learningHistory.slice(-1000);
      await this.memory.store('learning_history', recentHistory);
    } catch (error) {
      logger.error('❌ Erreur lors de la sauvegarde de l\'historique d\'apprentissage:', error);
    }
  }
}
