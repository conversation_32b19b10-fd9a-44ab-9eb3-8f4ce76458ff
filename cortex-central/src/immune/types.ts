/**
 * Types partagés pour le Système Immunitaire IA
 */

export interface HealthMetrics {
  timestamp: Date;
  systemLoad: number;
  memoryUsage: number;
  responseTime: number;
  errorRate: number;
  agentHealth: Map<string, number>;
  synapticHealth: number;
  throughput: number;
  diskUsage?: number;
  networkLatency?: number;
  activeConnections?: number;
  queueDepth?: number;
}

export interface Anomaly {
  id: string;
  type: 'performance' | 'error' | 'resource' | 'communication' | 'agent' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  metrics: any;
  timestamp: Date;
  affectedComponents: string[];
  confidence: number;
  rootCause?: string;
  impact?: string;
  duration?: number;
}

export interface HealingStrategy {
  id: string;
  name: string;
  description: string;
  applicableAnomalies: string[];
  actions: HealingAction[];
  estimatedRecoveryTime: number;
  riskLevel: number; // 0-1, 0 = low risk, 1 = high risk
  prerequisites?: string[];
  rollbackActions?: HealingAction[];
  successCriteria?: string[];
  maxRetries?: number;
}

export interface HealingAction {
  type: 'restart' | 'scale' | 'redirect' | 'optimize' | 'isolate' | 'rollback' | 'notify';
  target: string;
  parameters: any;
  timeout: number;
  priority?: number;
  condition?: string;
  rollbackAction?: HealingAction;
}

export interface HealingResult {
  success: boolean;
  strategy: HealingStrategy;
  anomaly: Anomaly;
  executedActions: HealingAction[];
  startTime: Date;
  endTime: Date;
  error?: string;
  metrics?: {
    beforeHealing: HealthMetrics;
    afterHealing: HealthMetrics;
  };
  rollbackPerformed?: boolean;
  improvementScore?: number; // 0-1, how much the healing improved the situation
}

export interface AdaptationRule {
  id: string;
  name: string;
  description: string;
  condition: string;
  action: string;
  confidence: number;
  successRate: number;
  lastUsed: Date;
  createdFrom: {
    anomaly: Anomaly;
    strategy: HealingStrategy;
    result: HealingResult;
  };
}

export interface ImmuneSystemStats {
  totalAnomaliesDetected: number;
  successfulHealings: number;
  failedHealings: number;
  adaptationsApplied: number;
  averageHealingTime: number;
  systemUptime: number;
  lastHealthCheck: Date;
  activeAnomalies: number;
  healingInProgress: number;
}

export interface ServiceHealth {
  serviceName: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime: number;
  errorRate: number;
  uptime: number;
  version?: string;
  dependencies?: ServiceHealth[];
}

export interface SystemComponent {
  id: string;
  name: string;
  type: 'service' | 'database' | 'cache' | 'queue' | 'gateway' | 'storage';
  status: 'running' | 'stopped' | 'error' | 'starting' | 'stopping';
  health: ServiceHealth;
  metrics: HealthMetrics;
  dependencies: string[];
  criticalityLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface HealingContext {
  anomaly: Anomaly;
  affectedComponents: SystemComponent[];
  systemState: HealthMetrics;
  recentHealings: HealingResult[];
  availableResources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  constraints: {
    maxDowntime: number;
    maxResourceUsage: number;
    allowedActions: string[];
    restrictedComponents: string[];
  };
}

export interface AutoHealerConfig {
  memory: any; // CentralMemory type
  communication: any; // SynapticCommunication type
  maxConcurrentHealings?: number;
  defaultTimeout?: number;
  riskThreshold?: number;
  enableLearning?: boolean;
  enableRollback?: boolean;
  strategies?: HealingStrategy[];
}

export interface AdaptationEngineConfig {
  memory: any; // CentralMemory type
  learningRate?: number;
  confidenceThreshold?: number;
  maxRules?: number;
  enableAutoCreation?: boolean;
}

export enum HealingStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  SUCCESS = 'success',
  FAILED = 'failed',
  ROLLED_BACK = 'rolled_back',
  TIMEOUT = 'timeout'
}

export enum ComponentType {
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  DATABASE = 'database',
  CACHE = 'cache',
  QUEUE = 'queue',
  GATEWAY = 'gateway',
  STORAGE = 'storage',
  MONITORING = 'monitoring',
  SECURITY = 'security'
}

export enum AnomalyType {
  PERFORMANCE_DEGRADATION = 'performance_degradation',
  HIGH_ERROR_RATE = 'high_error_rate',
  RESOURCE_EXHAUSTION = 'resource_exhaustion',
  COMMUNICATION_FAILURE = 'communication_failure',
  AGENT_FAILURE = 'agent_failure',
  SECURITY_BREACH = 'security_breach',
  DATA_CORRUPTION = 'data_corruption',
  SERVICE_UNAVAILABLE = 'service_unavailable'
}

export enum HealingActionType {
  RESTART_SERVICE = 'restart_service',
  SCALE_UP = 'scale_up',
  SCALE_DOWN = 'scale_down',
  REDIRECT_TRAFFIC = 'redirect_traffic',
  OPTIMIZE_RESOURCES = 'optimize_resources',
  ISOLATE_COMPONENT = 'isolate_component',
  CLEAR_CACHE = 'clear_cache',
  RESTART_DEPENDENCIES = 'restart_dependencies',
  ROLLBACK_DEPLOYMENT = 'rollback_deployment',
  NOTIFY_OPERATORS = 'notify_operators',
  ENABLE_CIRCUIT_BREAKER = 'enable_circuit_breaker',
  INCREASE_TIMEOUT = 'increase_timeout'
}
