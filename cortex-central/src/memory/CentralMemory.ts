import { EventEmitter } from 'events';
import weaviate, { WeaviateClient } from 'weaviate-ts-client';
import Redis from 'redis';
import { logger } from '../utils/logger';

export interface MemoryConfig {
  weaviateUrl: string;
  redisUrl: string;
}

export interface Pattern {
  id: string;
  content: any;
  domain: string;
  crossDomainRelevance: number;
  timestamp: Date;
  version: string;
  metadata: any;
}

export interface CodeExperience {
  id: string;
  agentId: string;
  codeType: string;
  content: string;
  context: any;
  success: boolean;
  feedback: any;
  timestamp: Date;
}

export interface KnowledgeGraph {
  nodes: Array<{
    id: string;
    type: string;
    properties: any;
  }>;
  edges: Array<{
    from: string;
    to: string;
    relationship: string;
    weight: number;
  }>;
}

/**
 * Mémoire Centrale - Système de Stockage et Récupération des Connaissances
 * 
 * Implémente un système de mémoire hiérarchisé avec:
 * - Mémoire vectorielle (Weaviate) pour les patterns et connaissances
 * - Mémoire de travail (Redis) pour les données temporaires
 * - Système d'oubli adaptatif pour la plasticité
 */
export class CentralMemory extends EventEmitter {
  private weaviate: WeaviateClient;
  private redis: Redis.RedisClientType;
  private isInitialized: boolean = false;
  
  // Schémas de classes Weaviate
  private readonly SCHEMAS = {
    GLOBAL_PATTERN: 'GlobalPattern',
    CODE_EXPERIENCE: 'CodeExperience',
    AGENT_INTERACTION: 'AgentInteraction',
    TASK_EXECUTION: 'TaskExecution',
    KNOWLEDGE_NODE: 'KnowledgeNode'
  };

  constructor(config: MemoryConfig) {
    super();

    // Configuration Weaviate
    this.weaviate = weaviate.client({
      scheme: 'http',
      host: config.weaviateUrl.replace('http://', '').replace('https://', ''),
    });

    // Configuration Redis
    this.redis = Redis.createClient({
      url: config.redisUrl,
      retry_delay_on_failure: 100,
      max_attempts: 10
    });
  }

  /**
   * Initialise le système de mémoire centrale
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation de la Mémoire Centrale...');

      // Connexion à Redis
      await this.redis.connect();
      logger.info('✅ Connexion Redis établie');

      // Vérification de la connexion Weaviate
      await this.weaviate.misc.liveChecker().do();
      logger.info('✅ Connexion Weaviate établie');

      // Création des schémas Weaviate
      await this.createWeaviateSchemas();

      // Démarrage du système d'oubli adaptatif
      this.startAdaptiveForgetting();

      // Démarrage du monitoring mémoire
      this.startMemoryMonitoring();

      this.isInitialized = true;
      logger.info('✅ Mémoire Centrale initialisée');

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation de la Mémoire Centrale:', error);
      throw error;
    }
  }

  /**
   * Stocke un pattern global avec métadonnées
   */
  public async storeGlobalPattern(pattern: Pattern): Promise<void> {
    try {
      // Calcul de la pertinence cross-domaine
      const crossDomainRelevance = await this.calculateCrossDomainRelevance(pattern);
      
      // Stockage dans Weaviate
      await this.weaviate.data.creator()
        .withClassName(this.SCHEMAS.GLOBAL_PATTERN)
        .withProperties({
          content: JSON.stringify(pattern.content),
          domain: pattern.domain,
          crossDomainRelevance,
          timestamp: pattern.timestamp.toISOString(),
          version: pattern.version,
          metadata: JSON.stringify(pattern.metadata)
        })
        .withId(pattern.id)
        .do();

      // Cache en Redis pour accès rapide
      await this.redis.setEx(
        `pattern:${pattern.id}`,
        3600, // 1 heure
        JSON.stringify(pattern)
      );

      logger.debug(`📚 Pattern global stocké: ${pattern.id} (domaine: ${pattern.domain})`);
      
      this.emit('pattern-stored', {
        patternId: pattern.id,
        domain: pattern.domain,
        crossDomainRelevance,
        timestamp: new Date()
      });

    } catch (error) {
      logger.error('❌ Erreur lors du stockage du pattern global:', error);
      throw error;
    }
  }

  /**
   * Recherche cross-domaine de patterns
   */
  public async queryAcrossDomains(query: string, limit: number = 10): Promise<Pattern[]> {
    try {
      // Recherche hybride dans Weaviate
      const result = await this.weaviate.graphql.get()
        .withClassName(this.SCHEMAS.GLOBAL_PATTERN)
        .withFields('content domain crossDomainRelevance timestamp version metadata')
        .withNearText({ concepts: [query] })
        .withWhere({
          path: ['crossDomainRelevance'],
          operator: 'GreaterThanEqual',
          valueNumber: 0.7
        })
        .withLimit(limit)
        .do();

      const patterns: Pattern[] = result.data.Get[this.SCHEMAS.GLOBAL_PATTERN].map((item: any) => ({
        id: item.id,
        content: JSON.parse(item.content),
        domain: item.domain,
        crossDomainRelevance: item.crossDomainRelevance,
        timestamp: new Date(item.timestamp),
        version: item.version,
        metadata: JSON.parse(item.metadata)
      }));

      logger.debug(`🔍 Recherche cross-domaine: ${patterns.length} patterns trouvés`);
      return patterns;

    } catch (error) {
      logger.error('❌ Erreur lors de la recherche cross-domaine:', error);
      return [];
    }
  }

  /**
   * Stocke une expérience de code avec importance calculée
   */
  public async storeCodeExperience(experience: CodeExperience): Promise<void> {
    try {
      // Calcul de l'importance
      const importance = await this.calculateImportance(experience);
      
      // Calcul de la rétention basée sur l'importance
      const retention = this.calculateRetention(importance);

      // Stockage dans Weaviate
      await this.weaviate.data.creator()
        .withClassName(this.SCHEMAS.CODE_EXPERIENCE)
        .withProperties({
          agentId: experience.agentId,
          codeType: experience.codeType,
          content: experience.content,
          context: JSON.stringify(experience.context),
          success: experience.success,
          feedback: JSON.stringify(experience.feedback),
          timestamp: experience.timestamp.toISOString(),
          importance,
          retention
        })
        .withId(experience.id)
        .do();

      // Mise à jour des statistiques d'agent
      await this.updateAgentStats(experience.agentId, experience.success);

      logger.debug(`💾 Expérience de code stockée: ${experience.id} (importance: ${importance})`);
      
      this.emit('experience-stored', {
        experienceId: experience.id,
        agentId: experience.agentId,
        importance,
        retention,
        timestamp: new Date()
      });

    } catch (error) {
      logger.error('❌ Erreur lors du stockage de l\'expérience:', error);
      throw error;
    }
  }

  /**
   * Stocke l'exécution d'une tâche
   */
  public async storeTaskExecution(taskData: any): Promise<void> {
    try {
      await this.weaviate.data.creator()
        .withClassName(this.SCHEMAS.TASK_EXECUTION)
        .withProperties({
          taskId: taskData.taskId,
          instructions: taskData.instructions,
          analysis: JSON.stringify(taskData.analysis),
          plan: JSON.stringify(taskData.plan),
          orchestration: JSON.stringify(taskData.orchestration),
          timestamp: new Date().toISOString()
        })
        .withId(taskData.taskId)
        .do();

      // Cache en Redis
      await this.redis.setEx(
        `task:${taskData.taskId}`,
        7200, // 2 heures
        JSON.stringify(taskData)
      );

      this.emit('task-stored', {
        taskId: taskData.taskId,
        timestamp: new Date()
      });

    } catch (error) {
      logger.error('❌ Erreur lors du stockage de la tâche:', error);
      throw error;
    }
  }

  /**
   * Stocke une interaction d'agent
   */
  public async storeAgentInteraction(agentId: string, interaction: any): Promise<void> {
    try {
      const interactionId = `${agentId}-${Date.now()}`;
      
      await this.weaviate.data.creator()
        .withClassName(this.SCHEMAS.AGENT_INTERACTION)
        .withProperties({
          agentId,
          interaction: JSON.stringify(interaction),
          timestamp: new Date().toISOString()
        })
        .withId(interactionId)
        .do();

      // Mise à jour du cache d'activité de l'agent
      await this.redis.lPush(`agent-activity:${agentId}`, JSON.stringify(interaction));
      await this.redis.lTrim(`agent-activity:${agentId}`, 0, 99); // Garde les 100 dernières

      this.emit('interaction-stored', {
        agentId,
        interactionId,
        timestamp: new Date()
      });

    } catch (error) {
      logger.error('❌ Erreur lors du stockage de l\'interaction:', error);
      throw error;
    }
  }

  /**
   * Récupère le statut d'une tâche
   */
  public async getTaskStatus(taskId: string): Promise<any> {
    try {
      // Tentative de récupération depuis Redis d'abord
      const cached = await this.redis.get(`task:${taskId}`);
      if (cached) {
        return JSON.parse(cached);
      }

      // Sinon, recherche dans Weaviate
      const result = await this.weaviate.data.getterById()
        .withClassName(this.SCHEMAS.TASK_EXECUTION)
        .withId(taskId)
        .do();

      return result;

    } catch (error) {
      logger.error('❌ Erreur lors de la récupération du statut de tâche:', error);
      return null;
    }
  }

  /**
   * Stocke le résultat d'une tâche
   */
  public async storeTaskResult(taskId: string, result: any): Promise<void> {
    try {
      // Mise à jour en Redis
      await this.redis.setEx(
        `task-result:${taskId}`,
        86400, // 24 heures
        JSON.stringify(result)
      );

      // Mise à jour dans Weaviate si nécessaire
      await this.weaviate.data.updater()
        .withClassName(this.SCHEMAS.TASK_EXECUTION)
        .withId(taskId)
        .withProperties({
          result: JSON.stringify(result),
          completedAt: new Date().toISOString()
        })
        .do();

      this.emit('task-result-stored', {
        taskId,
        timestamp: new Date()
      });

    } catch (error) {
      logger.error('❌ Erreur lors du stockage du résultat:', error);
      throw error;
    }
  }

  /**
   * Stocke le statut d'une tâche
   */
  public async storeTaskStatus(taskId: string, status: any): Promise<void> {
    try {
      await this.redis.setEx(
        `task-status:${taskId}`,
        86400, // 24 heures
        JSON.stringify(status)
      );

      this.emit('task-status-stored', {
        taskId,
        status: status.status,
        timestamp: new Date()
      });

    } catch (error) {
      logger.error('❌ Erreur lors du stockage du statut:', error);
      throw error;
    }
  }

  /**
   * Calcule la pertinence cross-domaine d'un pattern
   */
  private async calculateCrossDomainRelevance(pattern: Pattern): Promise<number> {
    // Algorithme simplifié - dans un vrai système, on utiliserait
    // des techniques d'apprentissage automatique plus sophistiquées
    
    // Facteurs de pertinence:
    // - Généricité du pattern
    // - Fréquence d'utilisation dans différents domaines
    // - Feedback des agents

    let relevance = 0.5; // Base

    // Bonus pour les patterns génériques
    if (pattern.content.type === 'generic' || pattern.content.reusable) {
      relevance += 0.2;
    }

    // Bonus basé sur les métadonnées
    if (pattern.metadata.usageCount > 10) {
      relevance += 0.1;
    }

    if (pattern.metadata.successRate > 0.8) {
      relevance += 0.2;
    }

    return Math.min(1.0, relevance);
  }

  /**
   * Calcule l'importance d'une expérience
   */
  private async calculateImportance(experience: CodeExperience): Promise<number> {
    let importance = 0.5; // Base

    // Facteurs d'importance:
    // - Succès de l'expérience
    // - Complexité du code
    // - Nouveauté du pattern
    // - Feedback positif

    if (experience.success) {
      importance += 0.3;
    }

    if (experience.feedback?.rating > 4) {
      importance += 0.2;
    }

    // Vérification de la nouveauté
    const similar = await this.findSimilarExperiences(experience);
    if (similar.length === 0) {
      importance += 0.3; // Nouveau pattern
    }

    return Math.min(1.0, importance);
  }

  /**
   * Calcule la rétention basée sur l'importance
   */
  private calculateRetention(importance: number): number {
    // Courbe d'oubli adaptative
    // Plus l'importance est élevée, plus la rétention est longue
    return Math.max(0.1, importance * 0.9 + 0.1);
  }

  /**
   * Trouve des expériences similaires
   */
  private async findSimilarExperiences(experience: CodeExperience): Promise<CodeExperience[]> {
    try {
      const result = await this.weaviate.graphql.get()
        .withClassName(this.SCHEMAS.CODE_EXPERIENCE)
        .withFields('agentId codeType content context success feedback timestamp')
        .withNearText({ concepts: [experience.content] })
        .withWhere({
          path: ['codeType'],
          operator: 'Equal',
          valueString: experience.codeType
        })
        .withLimit(5)
        .do();

      return result.data.Get[this.SCHEMAS.CODE_EXPERIENCE] || [];

    } catch (error) {
      logger.error('❌ Erreur lors de la recherche d\'expériences similaires:', error);
      return [];
    }
  }

  /**
   * Met à jour les statistiques d'agent
   */
  private async updateAgentStats(agentId: string, success: boolean): Promise<void> {
    const key = `agent-stats:${agentId}`;
    
    await this.redis.hIncrBy(key, 'totalTasks', 1);
    if (success) {
      await this.redis.hIncrBy(key, 'successfulTasks', 1);
    }
    
    await this.redis.expire(key, 86400); // 24 heures
  }

  /**
   * Création des schémas Weaviate
   */
  private async createWeaviateSchemas(): Promise<void> {
    try {
      // Schéma pour les patterns globaux
      await this.createSchemaIfNotExists(this.SCHEMAS.GLOBAL_PATTERN, {
        class: this.SCHEMAS.GLOBAL_PATTERN,
        properties: [
          { name: 'content', dataType: ['text'] },
          { name: 'domain', dataType: ['string'] },
          { name: 'crossDomainRelevance', dataType: ['number'] },
          { name: 'timestamp', dataType: ['date'] },
          { name: 'version', dataType: ['string'] },
          { name: 'metadata', dataType: ['text'] }
        ]
      });

      // Schéma pour les expériences de code
      await this.createSchemaIfNotExists(this.SCHEMAS.CODE_EXPERIENCE, {
        class: this.SCHEMAS.CODE_EXPERIENCE,
        properties: [
          { name: 'agentId', dataType: ['string'] },
          { name: 'codeType', dataType: ['string'] },
          { name: 'content', dataType: ['text'] },
          { name: 'context', dataType: ['text'] },
          { name: 'success', dataType: ['boolean'] },
          { name: 'feedback', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] },
          { name: 'importance', dataType: ['number'] },
          { name: 'retention', dataType: ['number'] }
        ]
      });

      // Schéma pour les interactions d'agents
      await this.createSchemaIfNotExists(this.SCHEMAS.AGENT_INTERACTION, {
        class: this.SCHEMAS.AGENT_INTERACTION,
        properties: [
          { name: 'agentId', dataType: ['string'] },
          { name: 'interaction', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] }
        ]
      });

      // Schéma pour les exécutions de tâches
      await this.createSchemaIfNotExists(this.SCHEMAS.TASK_EXECUTION, {
        class: this.SCHEMAS.TASK_EXECUTION,
        properties: [
          { name: 'taskId', dataType: ['string'] },
          { name: 'instructions', dataType: ['text'] },
          { name: 'analysis', dataType: ['text'] },
          { name: 'plan', dataType: ['text'] },
          { name: 'orchestration', dataType: ['text'] },
          { name: 'result', dataType: ['text'] },
          { name: 'timestamp', dataType: ['date'] },
          { name: 'completedAt', dataType: ['date'] }
        ]
      });

      logger.info('✅ Schémas Weaviate créés/vérifiés');

    } catch (error) {
      logger.error('❌ Erreur lors de la création des schémas:', error);
      throw error;
    }
  }

  /**
   * Crée un schéma s'il n'existe pas
   */
  private async createSchemaIfNotExists(className: string, schema: any): Promise<void> {
    try {
      await this.weaviate.schema.classGetter().withClassName(className).do();
      logger.debug(`📋 Schéma ${className} existe déjà`);
    } catch (error) {
      // Le schéma n'existe pas, on le crée
      await this.weaviate.schema.classCreator().withClass(schema).do();
      logger.info(`📋 Schéma ${className} créé`);
    }
  }

  /**
   * Démarrage du système d'oubli adaptatif
   */
  private startAdaptiveForgetting(): void {
    setInterval(async () => {
      await this.performAdaptiveForgetting();
    }, 3600000); // Toutes les heures
  }

  /**
   * Exécute l'oubli adaptatif
   */
  private async performAdaptiveForgetting(): Promise<void> {
    try {
      const threshold = 0.3; // Seuil de rétention minimum
      
      // Recherche des expériences avec faible rétention
      const result = await this.weaviate.graphql.get()
        .withClassName(this.SCHEMAS.CODE_EXPERIENCE)
        .withFields('_additional { id } retention timestamp')
        .withWhere({
          path: ['retention'],
          operator: 'LessThan',
          valueNumber: threshold
        })
        .withLimit(100)
        .do();

      const candidates = result.data.Get[this.SCHEMAS.CODE_EXPERIENCE] || [];
      
      for (const candidate of candidates) {
        // Suppression des expériences avec faible rétention
        await this.weaviate.data.deleter()
          .withClassName(this.SCHEMAS.CODE_EXPERIENCE)
          .withId(candidate._additional.id)
          .do();
      }

      if (candidates.length > 0) {
        logger.info(`🧹 Oubli adaptatif: ${candidates.length} expériences supprimées`);
        this.emit('adaptive-forgetting', {
          deletedCount: candidates.length,
          threshold,
          timestamp: new Date()
        });
      }

    } catch (error) {
      logger.error('❌ Erreur lors de l\'oubli adaptatif:', error);
    }
  }

  /**
   * Démarrage du monitoring mémoire
   */
  private startMemoryMonitoring(): void {
    setInterval(async () => {
      await this.performMemoryHealthCheck();
    }, 60000); // Toutes les minutes
  }

  /**
   * Vérification de santé mémoire
   */
  private async performMemoryHealthCheck(): Promise<void> {
    try {
      const stats = await this.getUsageStats();
      
      this.emit('memory-health-check', {
        stats,
        timestamp: new Date()
      });

      // Auto-optimisation si nécessaire
      if (stats.weaviateObjectCount > 100000) {
        logger.warn('⚠️ Nombre élevé d\'objets en mémoire, optimisation recommandée');
      }

    } catch (error) {
      logger.error('❌ Erreur lors de la vérification de santé mémoire:', error);
    }
  }

  /**
   * Récupère les statistiques d'utilisation
   */
  public async getUsageStats(): Promise<any> {
    try {
      // Statistiques Weaviate
      const weaviateStats = await this.weaviate.misc.metaGetter().do();
      
      // Statistiques Redis
      const redisInfo = await this.redis.info('memory');
      
      return {
        weaviateObjectCount: weaviateStats.objects || 0,
        redisMemoryUsage: this.parseRedisMemoryInfo(redisInfo),
        timestamp: new Date()
      };

    } catch (error) {
      logger.error('❌ Erreur lors de la récupération des statistiques:', error);
      return {
        weaviateObjectCount: 0,
        redisMemoryUsage: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Parse les informations mémoire Redis
   */
  private parseRedisMemoryInfo(info: string): number {
    const lines = info.split('\r\n');
    const memoryLine = lines.find(line => line.startsWith('used_memory:'));
    return memoryLine ? parseInt(memoryLine.split(':')[1]) : 0;
  }

  /**
   * Récupère le statut de la mémoire
   */
  public getStatus(): any {
    return {
      isInitialized: this.isInitialized,
      schemas: this.SCHEMAS,
      adaptiveForgettingEnabled: true,
      monitoringEnabled: true
    };
  }

  /**
   * Récupère les statistiques générales
   */
  public getStats(): any {
    return {
      isInitialized: this.isInitialized,
      schemasCount: Object.keys(this.SCHEMAS).length,
      status: 'active'
    };
  }

  /**
   * Arrêt gracieux
   */
  public async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt de la Mémoire Centrale...');
    
    await this.redis.disconnect();
    
    logger.info('✅ Mémoire Centrale arrêtée');
  }
}
