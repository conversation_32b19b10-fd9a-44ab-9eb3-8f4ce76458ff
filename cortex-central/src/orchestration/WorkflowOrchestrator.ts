import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { SynapticCommunication } from '../communication/SynapticCommunication';
import { CentralMemory } from '../memory/CentralMemory';
import { DecisionEngine } from '../decision/DecisionEngine';
import { NeuralNetworkManager } from '../neural/NeuralNetworkManager';
import { v4 as uuidv4 } from 'uuid';

export interface WorkflowStep {
  id: string;
  name: string;
  agentType: string;
  action: string;
  inputs: any;
  outputs?: any;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;
  error?: string;
  retries: number;
  maxRetries: number;
  dependencies: string[];
  conditions?: any;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  type: 'development' | 'deployment' | 'testing' | 'maintenance' | 'custom';
  status: 'created' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  steps: WorkflowStep[];
  currentStep?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;
  createdBy: string;
  metadata: any;
  context: any;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  steps: Omit<WorkflowStep, 'id' | 'status' | 'startedAt' | 'completedAt' | 'duration' | 'error' | 'retries'>[];
  defaultContext: any;
  tags: string[];
}

/**
 * Orchestrateur de Workflows
 * 
 * Gère l'exécution coordonnée de workflows complexes
 * impliquant plusieurs agents du système nerveux distribué
 */
export class WorkflowOrchestrator extends EventEmitter {
  private communication: SynapticCommunication;
  private memory: CentralMemory;
  private decisionEngine: DecisionEngine;
  private neuralNetwork: NeuralNetworkManager;
  
  private activeWorkflows: Map<string, Workflow> = new Map();
  private workflowTemplates: Map<string, WorkflowTemplate> = new Map();
  private executionQueue: string[] = [];
  private isProcessing: boolean = false;
  private maxConcurrentWorkflows: number = 5;

  constructor(
    communication: SynapticCommunication,
    memory: CentralMemory,
    decisionEngine: DecisionEngine,
    neuralNetwork: NeuralNetworkManager
  ) {
    super();
    this.communication = communication;
    this.memory = memory;
    this.decisionEngine = decisionEngine;
    this.neuralNetwork = neuralNetwork;

    this.initializeTemplates();
    this.setupEventHandlers();
  }

  /**
   * Initialise les templates de workflow prédéfinis
   */
  private initializeTemplates(): void {
    // Template de développement complet
    this.registerTemplate({
      id: 'full-development-workflow',
      name: 'Développement Complet',
      description: 'Workflow complet de développement d\'application',
      type: 'development',
      steps: [
        {
          name: 'Génération Frontend',
          agentType: 'frontend',
          action: 'generate',
          inputs: { requirements: '{{requirements}}' },
          dependencies: [],
          maxRetries: 2
        },
        {
          name: 'Génération Backend',
          agentType: 'backend',
          action: 'generate',
          inputs: { requirements: '{{requirements}}' },
          dependencies: [],
          maxRetries: 2
        },
        {
          name: 'Design UI/UX',
          agentType: 'uiux',
          action: 'design',
          inputs: { 
            frontendCode: '{{steps.frontend.outputs}}',
            requirements: '{{requirements}}'
          },
          dependencies: ['frontend'],
          maxRetries: 2
        },
        {
          name: 'Tests Automatiques',
          agentType: 'qa',
          action: 'test',
          inputs: {
            frontendCode: '{{steps.frontend.outputs}}',
            backendCode: '{{steps.backend.outputs}}'
          },
          dependencies: ['frontend', 'backend'],
          maxRetries: 3
        },
        {
          name: 'Déploiement',
          agentType: 'devops',
          action: 'deploy',
          inputs: {
            frontendCode: '{{steps.frontend.outputs}}',
            backendCode: '{{steps.backend.outputs}}',
            testResults: '{{steps.qa.outputs}}'
          },
          dependencies: ['qa'],
          conditions: { testsPassed: true },
          maxRetries: 2
        }
      ],
      defaultContext: {
        environment: 'development',
        autoApprove: false
      },
      tags: ['development', 'full-stack', 'automated']
    });

    // Template de déploiement rapide
    this.registerTemplate({
      id: 'quick-deployment-workflow',
      name: 'Déploiement Rapide',
      description: 'Déploiement rapide avec tests minimaux',
      type: 'deployment',
      steps: [
        {
          name: 'Tests Rapides',
          agentType: 'qa',
          action: 'quickTest',
          inputs: { code: '{{code}}' },
          dependencies: [],
          maxRetries: 1
        },
        {
          name: 'Déploiement',
          agentType: 'devops',
          action: 'deploy',
          inputs: { 
            code: '{{code}}',
            testResults: '{{steps.qa.outputs}}'
          },
          dependencies: ['qa'],
          maxRetries: 2
        }
      ],
      defaultContext: {
        environment: 'staging',
        skipManualApproval: true
      },
      tags: ['deployment', 'quick', 'staging']
    });

    logger.info('📋 Templates de workflow initialisés');
  }

  /**
   * Configuration des gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Écoute des réponses des agents
    this.communication.on('agent-response', (data) => {
      this.handleAgentResponse(data);
    });

    // Écoute des erreurs d'agents
    this.communication.on('agent-error', (data) => {
      this.handleAgentError(data);
    });

    // Démarrage du processeur de queue
    this.startQueueProcessor();
  }

  /**
   * Enregistre un template de workflow
   */
  registerTemplate(template: WorkflowTemplate): void {
    this.workflowTemplates.set(template.id, template);
    logger.info(`📋 Template enregistré: ${template.name}`);
  }

  /**
   * Crée un workflow à partir d'un template
   */
  async createWorkflowFromTemplate(
    templateId: string,
    context: any,
    createdBy: string = 'system'
  ): Promise<string> {
    const template = this.workflowTemplates.get(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} non trouvé`);
    }

    const workflowId = uuidv4();
    const workflow: Workflow = {
      id: workflowId,
      name: template.name,
      description: template.description,
      type: template.type as any,
      status: 'created',
      priority: 'medium',
      steps: template.steps.map((step, index) => ({
        ...step,
        id: `${workflowId}-step-${index}`,
        status: 'pending',
        retries: 0
      })),
      createdAt: new Date(),
      createdBy,
      metadata: {
        templateId,
        tags: template.tags
      },
      context: { ...template.defaultContext, ...context }
    };

    this.activeWorkflows.set(workflowId, workflow);
    await this.memory.storeWorkflow(workflow);

    logger.info(`🚀 Workflow créé: ${workflow.name} (${workflowId})`);
    this.emit('workflow-created', workflow);

    return workflowId;
  }

  /**
   * Démarre l'exécution d'un workflow
   */
  async startWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} non trouvé`);
    }

    if (workflow.status !== 'created' && workflow.status !== 'paused') {
      throw new Error(`Workflow ${workflowId} ne peut pas être démarré (statut: ${workflow.status})`);
    }

    workflow.status = 'running';
    workflow.startedAt = new Date();

    this.executionQueue.push(workflowId);
    await this.memory.updateWorkflow(workflow);

    logger.info(`▶️ Workflow démarré: ${workflow.name} (${workflowId})`);
    this.emit('workflow-started', workflow);
  }

  /**
   * Processeur de queue d'exécution
   */
  private startQueueProcessor(): void {
    setInterval(async () => {
      if (!this.isProcessing && this.executionQueue.length > 0) {
        await this.processQueue();
      }
    }, 1000);
  }

  /**
   * Traite la queue d'exécution
   */
  private async processQueue(): Promise<void> {
    this.isProcessing = true;

    try {
      const runningWorkflows = Array.from(this.activeWorkflows.values())
        .filter(w => w.status === 'running').length;

      while (this.executionQueue.length > 0 && runningWorkflows < this.maxConcurrentWorkflows) {
        const workflowId = this.executionQueue.shift()!;
        await this.executeWorkflow(workflowId);
      }
    } catch (error) {
      logger.error('❌ Erreur lors du traitement de la queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Exécute un workflow
   */
  private async executeWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (!workflow || workflow.status !== 'running') {
      return;
    }

    try {
      logger.info(`🔄 Exécution du workflow: ${workflow.name}`);

      // Trouve la prochaine étape à exécuter
      const nextStep = this.findNextExecutableStep(workflow);
      
      if (nextStep) {
        await this.executeStep(workflow, nextStep);
      } else {
        // Vérifier si le workflow est terminé
        const allCompleted = workflow.steps.every(step => 
          step.status === 'completed' || step.status === 'skipped'
        );

        if (allCompleted) {
          await this.completeWorkflow(workflow);
        }
      }

    } catch (error) {
      logger.error(`❌ Erreur lors de l'exécution du workflow ${workflowId}:`, error);
      await this.failWorkflow(workflow, error.message);
    }
  }

  /**
   * Trouve la prochaine étape exécutable
   */
  private findNextExecutableStep(workflow: Workflow): WorkflowStep | null {
    for (const step of workflow.steps) {
      if (step.status === 'pending' && this.areDependenciesMet(workflow, step)) {
        return step;
      }
    }
    return null;
  }

  /**
   * Vérifie si les dépendances d'une étape sont satisfaites
   */
  private areDependenciesMet(workflow: Workflow, step: WorkflowStep): boolean {
    return step.dependencies.every(depName => {
      const depStep = workflow.steps.find(s => s.name === depName);
      return depStep && depStep.status === 'completed';
    });
  }

  /**
   * Exécute une étape de workflow
   */
  private async executeStep(workflow: Workflow, step: WorkflowStep): Promise<void> {
    logger.info(`🔧 Exécution de l'étape: ${step.name} (${step.agentType})`);

    step.status = 'running';
    step.startedAt = new Date();
    workflow.currentStep = step.id;

    // Résolution des variables dans les inputs
    const resolvedInputs = this.resolveVariables(step.inputs, workflow);

    // Vérification des conditions
    if (step.conditions && !this.evaluateConditions(step.conditions, workflow)) {
      step.status = 'skipped';
      step.completedAt = new Date();
      logger.info(`⏭️ Étape ignorée: ${step.name} (conditions non remplies)`);
      return;
    }

    // Recherche d'un agent approprié
    const availableAgents = this.neuralNetwork.getAgentsByType(step.agentType)
      .filter(agent => agent.status === 'online');

    if (availableAgents.length === 0) {
      throw new Error(`Aucun agent ${step.agentType} disponible`);
    }

    // Sélection du meilleur agent
    const selectedAgent = this.selectBestAgent(availableAgents, step);

    // Envoi de la tâche à l'agent
    await this.communication.sendToAgent(selectedAgent.id, {
      type: 'task-request',
      workflowId: workflow.id,
      stepId: step.id,
      action: step.action,
      inputs: resolvedInputs,
      context: workflow.context,
      correlationId: `${workflow.id}-${step.id}`
    });

    await this.memory.updateWorkflow(workflow);
    this.emit('step-started', { workflow, step });
  }

  /**
   * Résout les variables dans les inputs
   */
  private resolveVariables(inputs: any, workflow: Workflow): any {
    const resolved = JSON.parse(JSON.stringify(inputs));
    
    const resolveValue = (value: any): any => {
      if (typeof value === 'string' && value.includes('{{')) {
        // Résolution des variables de contexte
        value = value.replace(/\{\{(\w+)\}\}/g, (match, key) => {
          return workflow.context[key] || match;
        });

        // Résolution des outputs d'étapes
        value = value.replace(/\{\{steps\.(\w+)\.outputs\}\}/g, (match, stepName) => {
          const step = workflow.steps.find(s => s.name === stepName);
          return step?.outputs ? JSON.stringify(step.outputs) : match;
        });
      }
      return value;
    };

    const traverse = (obj: any): any => {
      if (Array.isArray(obj)) {
        return obj.map(traverse);
      } else if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          result[key] = traverse(value);
        }
        return result;
      } else {
        return resolveValue(obj);
      }
    };

    return traverse(resolved);
  }

  /**
   * Évalue les conditions d'une étape
   */
  private evaluateConditions(conditions: any, workflow: Workflow): boolean {
    // Implémentation simple d'évaluation de conditions
    for (const [key, expectedValue] of Object.entries(conditions)) {
      if (key === 'testsPassed') {
        const qaStep = workflow.steps.find(s => s.agentType === 'qa');
        if (qaStep?.outputs?.success !== expectedValue) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   * Sélectionne le meilleur agent pour une tâche
   */
  private selectBestAgent(agents: any[], step: WorkflowStep): any {
    // Sélection basée sur les performances
    return agents.reduce((best, current) => {
      const bestScore = best.performance.successRate * 0.7 + 
                       (1 / (best.performance.averageResponseTime + 1)) * 0.3;
      const currentScore = current.performance.successRate * 0.7 + 
                          (1 / (current.performance.averageResponseTime + 1)) * 0.3;
      
      return currentScore > bestScore ? current : best;
    });
  }

  /**
   * Gère les réponses des agents
   */
  private async handleAgentResponse(data: any): Promise<void> {
    const { workflowId, stepId, success, result, error } = data;
    
    const workflow = this.activeWorkflows.get(workflowId);
    if (!workflow) return;

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) return;

    step.completedAt = new Date();
    step.duration = step.completedAt.getTime() - (step.startedAt?.getTime() || 0);

    if (success) {
      step.status = 'completed';
      step.outputs = result;
      logger.info(`✅ Étape terminée: ${step.name}`);
      this.emit('step-completed', { workflow, step });
    } else {
      step.error = error;
      
      if (step.retries < step.maxRetries) {
        step.retries++;
        step.status = 'pending';
        logger.warn(`🔄 Nouvelle tentative pour l'étape: ${step.name} (${step.retries}/${step.maxRetries})`);
      } else {
        step.status = 'failed';
        logger.error(`❌ Étape échouée: ${step.name} - ${error}`);
        await this.failWorkflow(workflow, `Étape ${step.name} échouée: ${error}`);
        return;
      }
    }

    await this.memory.updateWorkflow(workflow);

    // Continuer l'exécution
    if (workflow.status === 'running') {
      this.executionQueue.push(workflowId);
    }
  }

  /**
   * Gère les erreurs des agents
   */
  private async handleAgentError(data: any): Promise<void> {
    await this.handleAgentResponse({
      ...data,
      success: false,
      error: data.error || 'Erreur agent inconnue'
    });
  }

  /**
   * Termine un workflow avec succès
   */
  private async completeWorkflow(workflow: Workflow): Promise<void> {
    workflow.status = 'completed';
    workflow.completedAt = new Date();
    workflow.duration = workflow.completedAt.getTime() - (workflow.startedAt?.getTime() || 0);

    await this.memory.updateWorkflow(workflow);

    logger.info(`🎉 Workflow terminé: ${workflow.name} (${workflow.duration}ms)`);
    this.emit('workflow-completed', workflow);
  }

  /**
   * Fait échouer un workflow
   */
  private async failWorkflow(workflow: Workflow, reason: string): Promise<void> {
    workflow.status = 'failed';
    workflow.completedAt = new Date();
    workflow.duration = workflow.completedAt.getTime() - (workflow.startedAt?.getTime() || 0);

    await this.memory.updateWorkflow(workflow);

    logger.error(`💥 Workflow échoué: ${workflow.name} - ${reason}`);
    this.emit('workflow-failed', { workflow, reason });
  }

  /**
   * Récupère un workflow
   */
  getWorkflow(workflowId: string): Workflow | undefined {
    return this.activeWorkflows.get(workflowId);
  }

  /**
   * Récupère tous les workflows actifs
   */
  getActiveWorkflows(): Workflow[] {
    return Array.from(this.activeWorkflows.values());
  }

  /**
   * Récupère les templates disponibles
   */
  getTemplates(): WorkflowTemplate[] {
    return Array.from(this.workflowTemplates.values());
  }

  /**
   * Pause un workflow
   */
  async pauseWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (workflow && workflow.status === 'running') {
      workflow.status = 'paused';
      await this.memory.updateWorkflow(workflow);
      
      logger.info(`⏸️ Workflow mis en pause: ${workflow.name}`);
      this.emit('workflow-paused', workflow);
    }
  }

  /**
   * Annule un workflow
   */
  async cancelWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (workflow && ['running', 'paused'].includes(workflow.status)) {
      workflow.status = 'cancelled';
      workflow.completedAt = new Date();
      await this.memory.updateWorkflow(workflow);
      
      logger.info(`🚫 Workflow annulé: ${workflow.name}`);
      this.emit('workflow-cancelled', workflow);
    }
  }

  /**
   * Récupère le statut de l'orchestrateur
   */
  getStatus(): any {
    return {
      activeWorkflows: this.activeWorkflows.size,
      queuedWorkflows: this.executionQueue.length,
      isProcessing: this.isProcessing,
      templates: this.workflowTemplates.size,
      maxConcurrentWorkflows: this.maxConcurrentWorkflows
    };
  }
}
