# API Reference - Distributed Nervous System

## Overview

This document provides comprehensive API documentation for all agents in the distributed nervous system.

## Base URLs

### Development
- Cortex Central: `http://localhost:3000`
- Agents: `http://localhost:300{1-14}`

### Production
- Cortex Central: `https://api.retreatandbe.com`
- Agents: `https://agent-{name}.retreatandbe.com`

## Authentication

All API endpoints require authentication via JWT tokens or API keys.

```bash
# Using JWT Token
curl -H "Authorization: Bearer <jwt_token>" <endpoint>

# Using API Key
curl -H "X-API-Key: <api_key>" <endpoint>
```

## Cortex Central API

### Core Endpoints

#### POST /api/instructions
Process user instructions and coordinate agent responses.

**Request:**
```json
{
  "instruction": "Create a modern e-commerce website with React",
  "context": {
    "project_type": "web_application",
    "requirements": ["responsive", "accessible", "fast"]
  },
  "priority": "high"
}
```

**Response:**
```json
{
  "task_id": "task_123456",
  "status": "processing",
  "estimated_completion": "2024-01-15T10:30:00Z",
  "assigned_agents": ["frontend", "uiux", "backend"],
  "workflow_id": "workflow_789"
}
```

#### GET /api/tasks/{task_id}
Get task status and results.

**Response:**
```json
{
  "task_id": "task_123456",
  "status": "completed",
  "progress": 100,
  "results": {
    "frontend": { "code": "...", "components": [...] },
    "uiux": { "designs": "...", "wireframes": [...] },
    "backend": { "api": "...", "endpoints": [...] }
  },
  "completion_time": "2024-01-15T10:25:00Z"
}
```

#### GET /api/health
System health check.

**Response:**
```json
{
  "status": "healthy",
  "agents": {
    "frontend": "healthy",
    "backend": "healthy",
    "uiux": "healthy"
  },
  "infrastructure": {
    "kafka": "healthy",
    "redis": "healthy",
    "weaviate": "healthy"
  }
}
```

## Agent APIs

### Frontend Agent (Port 3001)

#### POST /api/frontend/generate
Generate frontend code based on requirements.

**Request:**
```json
{
  "framework": "react",
  "components": ["header", "navigation", "footer"],
  "styling": "tailwind",
  "features": ["responsive", "dark_mode"]
}
```

#### GET /api/frontend/templates
Get available templates and components.

### Backend Agent (Port 3002)

#### POST /api/backend/generate
Generate backend API code.

**Request:**
```json
{
  "framework": "nestjs",
  "database": "postgresql",
  "features": ["auth", "crud", "validation"],
  "endpoints": [
    {
      "path": "/users",
      "methods": ["GET", "POST", "PUT", "DELETE"]
    }
  ]
}
```

### UI/UX Agent (Port 3003)

#### POST /api/uiux/research
Conduct user research and analysis.

#### POST /api/uiux/wireframes
Generate wireframes and prototypes.

#### POST /api/uiux/design-system
Create design system components.

### QA Agent (Port 3004)

#### POST /api/qa/test
Run automated tests on code or applications.

#### GET /api/qa/reports
Get test reports and quality metrics.

### Security Agent (Port 3012)

#### POST /api/security/scan
Perform security vulnerability scanning.

#### GET /api/security/compliance
Check compliance status.

## Error Handling

All APIs use standard HTTP status codes and return errors in the following format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "framework",
      "issue": "Unsupported framework specified"
    }
  },
  "request_id": "req_123456",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Rate Limiting

- **Development:** 1000 requests/hour per API key
- **Production:** 10000 requests/hour per API key
- **Burst:** Up to 100 requests/minute

## WebSocket APIs

### Real-time Updates
Connect to WebSocket endpoints for real-time task updates:

```javascript
const ws = new WebSocket('ws://localhost:3000/ws/tasks');
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  console.log('Task update:', update);
};
```

## SDK Examples

### JavaScript/TypeScript
```typescript
import { CortexClient } from '@retreatandbe/cortex-sdk';

const client = new CortexClient({
  apiKey: 'your-api-key',
  baseUrl: 'http://localhost:3000'
});

const result = await client.processInstruction({
  instruction: 'Create a landing page',
  context: { type: 'marketing' }
});
```

### Python
```python
from cortex_client import CortexClient

client = CortexClient(
    api_key='your-api-key',
    base_url='http://localhost:3000'
)

result = client.process_instruction(
    instruction='Create a landing page',
    context={'type': 'marketing'}
)
```

---

*For complete endpoint documentation, see individual agent documentation files.*
