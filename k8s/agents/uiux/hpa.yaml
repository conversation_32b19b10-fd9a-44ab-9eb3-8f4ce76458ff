apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-uiux-hpa
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-uiux
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: design_requests_per_second
      target:
        type: AverageValue
        averageValue: "10"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
