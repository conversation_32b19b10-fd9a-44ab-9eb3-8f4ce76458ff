apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: agent-uiux
  namespace: retreat-and-be

resources:
- serviceaccount.yaml
- configmap.yaml
- secrets.yaml
- pvc.yaml
- deployment.yaml
- service.yaml
- hpa.yaml

commonLabels:
  app: agent-uiux
  component: ai-agent
  tier: application
  version: v1.0.0

namespace: retreat-and-be

images:
- name: retreat-and-be/agent-uiux
  newTag: latest

configMapGenerator:
- name: agent-uiux-env
  literals:
  - NODE_ENV=production
  - LOG_LEVEL=info
  - PROMETHEUS_ENABLED=true
  - ENABLE_AI_GENERATION=true

secretGenerator:
- name: agent-uiux-runtime-secrets
  literals:
  - runtime-secret=generated-at-deploy-time

replicas:
- name: agent-uiux
  count: 2

patchesStrategicMerge:
- |-
  apiVersion: apps/v1
  kind: Deployment
  metadata:
    name: agent-uiux
  spec:
    template:
      metadata:
        annotations:
          deployment.kubernetes.io/revision: "1"
          config.kubernetes.io/local-config: "true"
