apiVersion: v1
kind: ServiceAccount
metadata:
  name: agent-uiux
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
automountServiceAccountToken: true

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: agent-uiux-role
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: agent-uiux-rolebinding
  namespace: retreat-and-be
  labels:
    app: agent-uiux
    component: ai-agent
    tier: application
subjects:
- kind: ServiceAccount
  name: agent-uiux
  namespace: retreat-and-be
roleRef:
  kind: Role
  name: agent-uiux-role
  apiGroup: rbac.authorization.k8s.io
