#!/bin/bash

# Script de déploiement Kubernetes pour l'Agent UI/UX Design Thinking
# Retreat And Be - Living AI Organism Architecture v3.8

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="retreat-and-be"
AGENT_NAME="agent-uiux"
K8S_DIR="k8s/agents/uiux"
TIMEOUT="300s"

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérification des prérequis
check_prerequisites() {
    log_info "Vérification des prérequis Kubernetes..."
    
    # Vérifier kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl n'est pas installé"
        exit 1
    fi
    
    # Vérifier kustomize
    if ! command -v kustomize &> /dev/null; then
        log_warning "kustomize n'est pas installé, utilisation de kubectl apply -k"
    fi
    
    # Vérifier la connexion au cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Impossible de se connecter au cluster Kubernetes"
        exit 1
    fi
    
    # Vérifier que les manifestes existent
    if [ ! -d "$K8S_DIR" ]; then
        log_error "Répertoire $K8S_DIR non trouvé"
        exit 1
    fi
    
    log_success "Prérequis Kubernetes vérifiés"
}

# Création du namespace
create_namespace() {
    log_info "Création du namespace $NAMESPACE..."
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        log_info "Namespace $NAMESPACE existe déjà"
    else
        kubectl create namespace $NAMESPACE
        log_success "Namespace $NAMESPACE créé"
    fi
    
    # Labeller le namespace
    kubectl label namespace $NAMESPACE \
        app.kubernetes.io/name=retreat-and-be \
        app.kubernetes.io/component=ai-agents \
        --overwrite
}

# Vérification des dépendances
check_dependencies() {
    log_info "Vérification des dépendances dans le cluster..."
    
    # Services requis
    required_services=("weaviate" "kafka" "redis")
    
    for service in "${required_services[@]}"; do
        if kubectl get service $service -n $NAMESPACE &> /dev/null; then
            log_success "✅ Service $service trouvé"
        else
            log_warning "⚠️ Service $service non trouvé - assurez-vous qu'il soit déployé"
        fi
    done
}

# Construction et push de l'image Docker
build_and_push_image() {
    log_info "Construction et push de l'image Docker..."
    
    # Construire l'image
    docker build -t retreat-and-be/agent-uiux:latest ./agents/uiux/
    
    # Tag pour le registry (si configuré)
    if [ ! -z "$DOCKER_REGISTRY" ]; then
        docker tag retreat-and-be/agent-uiux:latest $DOCKER_REGISTRY/retreat-and-be/agent-uiux:latest
        docker push $DOCKER_REGISTRY/retreat-and-be/agent-uiux:latest
        log_success "Image pushée vers $DOCKER_REGISTRY"
    else
        log_info "DOCKER_REGISTRY non configuré, utilisation de l'image locale"
    fi
}

# Déploiement des ressources
deploy_resources() {
    log_info "Déploiement des ressources Kubernetes..."
    
    # Appliquer les manifestes avec kustomize
    if command -v kustomize &> /dev/null; then
        kustomize build $K8S_DIR | kubectl apply -f -
    else
        kubectl apply -k $K8S_DIR
    fi
    
    log_success "Ressources déployées"
}

# Attendre que le déploiement soit prêt
wait_for_deployment() {
    log_info "Attente de la disponibilité du déploiement..."
    
    if kubectl rollout status deployment/$AGENT_NAME -n $NAMESPACE --timeout=$TIMEOUT; then
        log_success "Déploiement $AGENT_NAME prêt"
    else
        log_error "Timeout lors de l'attente du déploiement"
        return 1
    fi
}

# Vérification de la santé
health_check() {
    log_info "Vérification de la santé de l'agent..."
    
    # Obtenir le nom d'un pod
    pod_name=$(kubectl get pods -n $NAMESPACE -l app=$AGENT_NAME -o jsonpath='{.items[0].metadata.name}')
    
    if [ -z "$pod_name" ]; then
        log_error "Aucun pod trouvé pour l'agent $AGENT_NAME"
        return 1
    fi
    
    # Port-forward pour tester
    log_info "Test de santé via port-forward..."
    kubectl port-forward pod/$pod_name -n $NAMESPACE 3005:3005 &
    port_forward_pid=$!
    
    # Attendre que le port-forward soit prêt
    sleep 5
    
    # Tester l'endpoint de santé
    if curl -f -s http://localhost:3005/health > /dev/null; then
        log_success "✅ Agent UI/UX en bonne santé"
        health_status=0
    else
        log_error "❌ Agent UI/UX non accessible"
        health_status=1
    fi
    
    # Arrêter le port-forward
    kill $port_forward_pid 2>/dev/null || true
    
    return $health_status
}

# Tests de base
run_basic_tests() {
    log_info "Exécution des tests de base..."
    
    # Obtenir le nom d'un pod
    pod_name=$(kubectl get pods -n $NAMESPACE -l app=$AGENT_NAME -o jsonpath='{.items[0].metadata.name}')
    
    # Tester les endpoints via kubectl exec
    if kubectl exec $pod_name -n $NAMESPACE -- curl -f -s http://localhost:3005/health > /dev/null; then
        log_success "✅ Health endpoint accessible"
    else
        log_error "❌ Health endpoint non accessible"
        return 1
    fi
    
    if kubectl exec $pod_name -n $NAMESPACE -- curl -f -s http://localhost:3005/ready > /dev/null; then
        log_success "✅ Ready endpoint accessible"
    else
        log_error "❌ Ready endpoint non accessible"
        return 1
    fi
    
    log_success "Tests de base réussis"
}

# Affichage des informations de déploiement
show_deployment_info() {
    log_success "=== DÉPLOIEMENT KUBERNETES TERMINÉ ==="
    echo ""
    log_info "Agent UI/UX déployé avec succès dans Kubernetes !"
    echo ""
    
    # Informations sur les pods
    log_info "📋 Pods déployés :"
    kubectl get pods -n $NAMESPACE -l app=$AGENT_NAME -o wide
    echo ""
    
    # Informations sur les services
    log_info "🌐 Services :"
    kubectl get services -n $NAMESPACE -l app=$AGENT_NAME
    echo ""
    
    # Informations sur les PVC
    log_info "💾 Volumes persistants :"
    kubectl get pvc -n $NAMESPACE -l app=$AGENT_NAME
    echo ""
    
    # HPA status
    log_info "📈 Autoscaling :"
    kubectl get hpa -n $NAMESPACE -l app=$AGENT_NAME
    echo ""
    
    log_info "🔧 Commandes utiles :"
    echo "  • Voir les logs : kubectl logs -f deployment/$AGENT_NAME -n $NAMESPACE"
    echo "  • Port-forward : kubectl port-forward service/$AGENT_NAME -n $NAMESPACE 3005:3005"
    echo "  • Exec dans un pod : kubectl exec -it deployment/$AGENT_NAME -n $NAMESPACE -- /bin/bash"
    echo "  • Redémarrer : kubectl rollout restart deployment/$AGENT_NAME -n $NAMESPACE"
    echo "  • Scaler : kubectl scale deployment/$AGENT_NAME -n $NAMESPACE --replicas=3"
    echo ""
    
    log_info "📊 Monitoring :"
    echo "  • Métriques : kubectl port-forward service/$AGENT_NAME -n $NAMESPACE 3005:3005"
    echo "  • Dashboard : http://localhost:3005/health"
    echo ""
}

# Affichage des logs
show_logs() {
    log_info "Affichage des logs récents..."
    kubectl logs deployment/$AGENT_NAME -n $NAMESPACE --tail=50
}

# Nettoyage en cas d'erreur
cleanup() {
    log_warning "Nettoyage en cours..."
    # Arrêter les port-forwards en cours
    pkill -f "kubectl port-forward" 2>/dev/null || true
}

# Fonction principale
main() {
    log_info "=== DÉPLOIEMENT KUBERNETES AGENT UI/UX ==="
    echo ""
    
    # Vérifier les arguments
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h         Afficher cette aide"
        echo "  --logs             Afficher les logs après le déploiement"
        echo "  --no-build         Ignorer la construction de l'image"
        echo "  --no-tests         Ignorer les tests de base"
        echo "  --cleanup          Supprimer les ressources existantes"
        echo ""
        echo "Variables d'environnement:"
        echo "  DOCKER_REGISTRY    Registry Docker pour push des images"
        echo "  KUBECONFIG         Fichier de configuration kubectl"
        echo ""
        exit 0
    fi
    
    # Nettoyage si demandé
    if [ "$1" = "--cleanup" ]; then
        log_info "Suppression des ressources existantes..."
        kubectl delete -k $K8S_DIR --ignore-not-found=true
        log_success "Ressources supprimées"
        exit 0
    fi
    
    # Configuration du trap pour le nettoyage
    trap cleanup EXIT
    
    # Exécuter les étapes de déploiement
    check_prerequisites
    create_namespace
    check_dependencies
    
    if [ "$1" != "--no-build" ]; then
        build_and_push_image
    fi
    
    deploy_resources
    
    if wait_for_deployment; then
        if health_check; then
            if [ "$1" != "--no-tests" ]; then
                run_basic_tests
            fi
            
            show_deployment_info
            
            if [ "$1" = "--logs" ]; then
                echo ""
                show_logs
            fi
        else
            log_error "Échec du déploiement - l'agent n'est pas en bonne santé"
            show_logs
            exit 1
        fi
    else
        log_error "Échec du déploiement - timeout"
        show_logs
        exit 1
    fi
}

# Exécution du script principal
main "$@"
