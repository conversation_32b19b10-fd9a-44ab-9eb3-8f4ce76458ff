#!/bin/bash

# =============================================================================
# Script d'initialisation des agents avec intégration UX
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/init-agents.log"
CONFIG_DIR="$PROJECT_ROOT/config"
AGENTS_DIR="$PROJECT_ROOT/agents"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Fonction de vérification des prérequis
check_prerequisites() {
    log "INFO" "Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log "ERROR" "Node.js n'est pas installé"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2)
    log "INFO" "Node.js version: $node_version"
    
    # Vérifier npm
    if ! command -v npm &> /dev/null; then
        log "ERROR" "npm n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker
    if ! command -v docker &> /dev/null; then
        log "ERROR" "Docker n'est pas installé"
        exit 1
    fi
    
    # Vérifier Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log "ERROR" "Docker Compose n'est pas installé"
        exit 1
    fi
    
    log "INFO" "Tous les prérequis sont satisfaits"
}

# Fonction de création des répertoires
create_directories() {
    log "INFO" "Création des répertoires nécessaires..."
    
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/data"
    mkdir -p "$PROJECT_ROOT/config"
    mkdir -p "$PROJECT_ROOT/docker/volumes"
    
    log "INFO" "Répertoires créés avec succès"
}

# Fonction d'installation des dépendances
install_dependencies() {
    log "INFO" "Installation des dépendances des agents..."
    
    local agents=(
        "cortex-central"
        "frontend"
        "backend"
        "devops"
        "qa"
        "security"
        "uiux"
        "web-research"
        "data-analyst"
        "project-manager"
        "evolution"
    )
    
    for agent in "${agents[@]}"; do
        local agent_path="$AGENTS_DIR/$agent"
        
        if [[ -d "$agent_path" && -f "$agent_path/package.json" ]]; then
            log "INFO" "Installation des dépendances pour $agent..."
            
            cd "$agent_path"
            
            # Nettoyer les anciens modules
            if [[ -d "node_modules" ]]; then
                rm -rf node_modules
            fi
            
            # Installer les dépendances
            npm ci --silent
            
            # Compiler TypeScript si nécessaire
            if [[ -f "tsconfig.json" ]]; then
                npm run build
            fi
            
            log "INFO" "Dépendances installées pour $agent"
        else
            log "WARN" "Agent $agent non trouvé ou package.json manquant"
        fi
    done
    
    cd "$PROJECT_ROOT"
    log "INFO" "Installation des dépendances terminée"
}

# Fonction de configuration des variables d'environnement
setup_environment() {
    log "INFO" "Configuration des variables d'environnement..."
    
    # Créer le fichier .env principal s'il n'existe pas
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        cat > "$PROJECT_ROOT/.env" << EOF
# Configuration générale
NODE_ENV=development
LOG_LEVEL=info

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=retreat-and-be
KAFKA_GROUP_ID=agents-group

# Weaviate Configuration
WEAVIATE_SCHEME=http
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=retreat_and_be
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Ports des agents
CORTEX_CENTRAL_PORT=3000
AGENT_FRONTEND_PORT=3001
AGENT_BACKEND_PORT=3002
AGENT_DEVOPS_PORT=3003
AGENT_QA_PORT=3004
AGENT_SECURITY_PORT=3005
AGENT_UIUX_PORT=3006
AGENT_WEB_RESEARCH_PORT=3007
AGENT_DATA_ANALYST_PORT=3008
AGENT_PROJECT_MANAGER_PORT=3009
AGENT_EVOLUTION_PORT=3010

# UX Configuration
UX_ANALYTICS_ENABLED=true
UX_HEATMAPS_ENABLED=true
UX_A_B_TESTING_ENABLED=true
UX_ACCESSIBILITY_CHECKS=true

# API Keys (à configurer)
GOOGLE_API_KEY=
BING_API_KEY=
GITHUB_TOKEN=

# Docker Registry
DOCKER_REGISTRY=localhost:5000
EOF
        log "INFO" "Fichier .env créé"
    else
        log "INFO" "Fichier .env existant trouvé"
    fi
    
    # Copier les variables d'environnement pour chaque agent
    local agents=(
        "cortex-central"
        "frontend"
        "backend"
        "devops"
        "qa"
        "security"
        "uiux"
        "web-research"
        "data-analyst"
        "project-manager"
        "evolution"
    )
    
    for agent in "${agents[@]}"; do
        local agent_path="$AGENTS_DIR/$agent"
        
        if [[ -d "$agent_path" ]]; then
            if [[ ! -f "$agent_path/.env" ]]; then
                cp "$PROJECT_ROOT/.env" "$agent_path/.env"
                log "INFO" "Variables d'environnement copiées pour $agent"
            fi
        fi
    done
}

# Fonction de démarrage de l'infrastructure
start_infrastructure() {
    log "INFO" "Démarrage de l'infrastructure..."
    
    # Vérifier si docker-compose.yml existe
    if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        log "ERROR" "Fichier docker-compose.yml non trouvé"
        exit 1
    fi
    
    # Démarrer les services d'infrastructure
    cd "$PROJECT_ROOT"
    docker-compose up -d kafka zookeeper weaviate redis postgres
    
    # Attendre que les services soient prêts
    log "INFO" "Attente du démarrage des services..."
    sleep 30
    
    # Vérifier la santé des services
    check_service_health "kafka" "localhost:9092"
    check_service_health "weaviate" "localhost:8080"
    check_service_health "redis" "localhost:6379"
    check_service_health "postgres" "localhost:5432"
    
    log "INFO" "Infrastructure démarrée avec succès"
}

# Fonction de vérification de la santé des services
check_service_health() {
    local service_name=$1
    local endpoint=$2
    local max_attempts=30
    local attempt=1
    
    log "INFO" "Vérification de la santé de $service_name..."
    
    while [[ $attempt -le $max_attempts ]]; do
        case $service_name in
            "kafka")
                if nc -z localhost 9092 2>/dev/null; then
                    log "INFO" "$service_name est prêt"
                    return 0
                fi
                ;;
            "weaviate")
                if curl -s http://localhost:8080/v1/.well-known/ready >/dev/null 2>&1; then
                    log "INFO" "$service_name est prêt"
                    return 0
                fi
                ;;
            "redis")
                if nc -z localhost 6379 2>/dev/null; then
                    log "INFO" "$service_name est prêt"
                    return 0
                fi
                ;;
            "postgres")
                if nc -z localhost 5432 2>/dev/null; then
                    log "INFO" "$service_name est prêt"
                    return 0
                fi
                ;;
        esac
        
        log "DEBUG" "Tentative $attempt/$max_attempts pour $service_name..."
        sleep 2
        ((attempt++))
    done
    
    log "ERROR" "$service_name n'est pas prêt après $max_attempts tentatives"
    return 1
}

# Fonction d'initialisation des schémas Weaviate
init_weaviate_schemas() {
    log "INFO" "Initialisation des schémas Weaviate..."
    
    # Attendre que Weaviate soit complètement prêt
    sleep 10
    
    # Créer les schémas pour chaque agent
    local schemas=(
        "AgentMemory"
        "ProjectData"
        "TaskData"
        "UserData"
        "DesignSystem"
        "UXInsights"
        "ResearchData"
        "AnalyticsData"
        "SecurityData"
        "QAData"
        "DevOpsData"
        "TechRadar"
        "EvolutionPlan"
    )
    
    for schema in "${schemas[@]}"; do
        log "INFO" "Création du schéma $schema..."
        
        # Exemple de création de schéma (à adapter selon les besoins)
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "{
                \"class\": \"$schema\",
                \"description\": \"Schema for $schema data\",
                \"properties\": [
                    {
                        \"name\": \"content\",
                        \"dataType\": [\"text\"],
                        \"description\": \"Content data\"
                    },
                    {
                        \"name\": \"timestamp\",
                        \"dataType\": [\"date\"],
                        \"description\": \"Creation timestamp\"
                    },
                    {
                        \"name\": \"agentId\",
                        \"dataType\": [\"string\"],
                        \"description\": \"Agent identifier\"
                    }
                ]
            }" \
            http://localhost:8080/v1/schema >/dev/null 2>&1
        
        log "INFO" "Schéma $schema créé"
    done
    
    log "INFO" "Schémas Weaviate initialisés"
}

# Fonction de démarrage des agents
start_agents() {
    log "INFO" "Démarrage des agents..."
    
    # Démarrer les agents dans l'ordre de dépendance
    local agents_order=(
        "cortex-central"
        "backend"
        "frontend"
        "uiux"
        "web-research"
        "data-analyst"
        "project-manager"
        "evolution"
        "devops"
        "qa"
        "security"
    )
    
    for agent in "${agents_order[@]}"; do
        local agent_path="$AGENTS_DIR/$agent"
        
        if [[ -d "$agent_path" && -f "$agent_path/package.json" ]]; then
            log "INFO" "Démarrage de l'agent $agent..."
            
            cd "$agent_path"
            
            # Démarrer l'agent en arrière-plan
            npm start > "$PROJECT_ROOT/logs/$agent.log" 2>&1 &
            local pid=$!
            
            # Sauvegarder le PID
            echo $pid > "$PROJECT_ROOT/logs/$agent.pid"
            
            log "INFO" "Agent $agent démarré (PID: $pid)"
            
            # Attendre un peu avant de démarrer le suivant
            sleep 5
        else
            log "WARN" "Agent $agent non trouvé"
        fi
    done
    
    cd "$PROJECT_ROOT"
    log "INFO" "Tous les agents ont été démarrés"
}

# Fonction de vérification de la santé des agents
check_agents_health() {
    log "INFO" "Vérification de la santé des agents..."
    
    local agents_ports=(
        "cortex-central:3000"
        "frontend:3001"
        "backend:3002"
        "devops:3003"
        "qa:3004"
        "security:3005"
        "uiux:3006"
        "web-research:3007"
        "data-analyst:3008"
        "project-manager:3009"
        "evolution:3010"
    )
    
    for agent_port in "${agents_ports[@]}"; do
        local agent=$(echo $agent_port | cut -d':' -f1)
        local port=$(echo $agent_port | cut -d':' -f2)
        
        log "INFO" "Vérification de $agent sur le port $port..."
        
        local max_attempts=10
        local attempt=1
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -s http://localhost:$port/health >/dev/null 2>&1; then
                log "INFO" "Agent $agent est en bonne santé"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                log "WARN" "Agent $agent ne répond pas sur le port $port"
            fi
            
            sleep 3
            ((attempt++))
        done
    done
    
    log "INFO" "Vérification de santé terminée"
}

# Fonction principale
main() {
    log "INFO" "Début de l'initialisation des agents avec intégration UX"
    
    # Créer le répertoire de logs s'il n'existe pas
    mkdir -p "$(dirname "$LOG_FILE")"
    
    check_prerequisites
    create_directories
    setup_environment
    install_dependencies
    start_infrastructure
    init_weaviate_schemas
    start_agents
    check_agents_health
    
    log "INFO" "Initialisation terminée avec succès!"
    log "INFO" "Les agents sont maintenant opérationnels avec intégration UX complète"
    log "INFO" "Consultez les logs dans $PROJECT_ROOT/logs/"
    
    echo ""
    echo -e "${GREEN}🎉 Initialisation réussie!${NC}"
    echo -e "${BLUE}📊 Dashboard Cortex Central: http://localhost:3000${NC}"
    echo -e "${BLUE}🎨 Interface UX: http://localhost:3001${NC}"
    echo -e "${BLUE}📈 Analytics: http://localhost:3008${NC}"
    echo -e "${BLUE}📋 Project Manager: http://localhost:3009${NC}"
    echo -e "${BLUE}🔄 Evolution: http://localhost:3010${NC}"
}

# Gestion des signaux pour un arrêt propre
cleanup() {
    log "INFO" "Arrêt en cours..."
    
    # Arrêter tous les agents
    for pid_file in "$PROJECT_ROOT/logs"/*.pid; do
        if [[ -f "$pid_file" ]]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                log "INFO" "Agent arrêté (PID: $pid)"
            fi
            rm -f "$pid_file"
        fi
    done
    
    exit 0
}

trap cleanup SIGINT SIGTERM

# Exécuter le script principal
main "$@"
