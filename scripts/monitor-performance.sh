#!/bin/bash

# =============================================================================
# Script de monitoring de performance en temps réel
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/performance-monitor.log"
METRICS_DIR="$PROJECT_ROOT/metrics"
INTERVAL=30  # Intervalle de collecte en secondes
DURATION=3600  # Durée totale en secondes (1 heure par défaut)

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
        "METRIC")
            echo -e "${PURPLE}[METRIC]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Fonction de collecte des métriques système
collect_system_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local metrics_file="$METRICS_DIR/system_metrics_$(date +%Y%m%d).csv"
    
    # CPU Usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    
    # Memory Usage
    local memory_info=$(free -m | grep '^Mem:')
    local total_memory=$(echo $memory_info | awk '{print $2}')
    local used_memory=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$(( used_memory * 100 / total_memory ))
    
    # Disk Usage
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    
    # Load Average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
    
    # Network connections
    local network_connections=$(netstat -an | grep ESTABLISHED | wc -l)
    
    # Créer le fichier CSV avec headers si nécessaire
    if [[ ! -f "$metrics_file" ]]; then
        echo "timestamp,cpu_usage,memory_usage,disk_usage,load_avg,network_connections" > "$metrics_file"
    fi
    
    # Ajouter les métriques
    echo "$timestamp,$cpu_usage,$memory_usage,$disk_usage,$load_avg,$network_connections" >> "$metrics_file"
    
    log "METRIC" "Système - CPU: ${cpu_usage}%, Mémoire: ${memory_usage}%, Disque: ${disk_usage}%, Load: $load_avg"
}

# Fonction de collecte des métriques des agents
collect_agent_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local agents_file="$METRICS_DIR/agents_metrics_$(date +%Y%m%d).csv"
    
    # Créer le fichier CSV avec headers si nécessaire
    if [[ ! -f "$agents_file" ]]; then
        echo "timestamp,agent,status,response_time,memory_usage,cpu_usage,requests_count" > "$agents_file"
    fi
    
    local agents_ports=(
        "cortex-central:3000"
        "frontend:3001"
        "backend:3002"
        "devops:3003"
        "qa:3004"
        "security:3005"
        "uiux:3006"
        "web-research:3007"
        "data-analyst:3008"
        "project-manager:3009"
        "evolution:3010"
    )
    
    for agent_port in "${agents_ports[@]}"; do
        local agent=$(echo $agent_port | cut -d':' -f1)
        local port=$(echo $agent_port | cut -d':' -f2)
        local url="http://localhost:$port/health"
        
        # Mesurer le temps de réponse
        local start_time=$(date +%s%N)
        local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        local end_time=$(date +%s%N)
        local response_time=$(( (end_time - start_time) / 1000000 )) # en millisecondes
        
        local status="down"
        if [[ "$status_code" == "200" ]]; then
            status="up"
        fi
        
        # Obtenir les métriques du processus si disponible
        local memory_usage=0
        local cpu_usage=0
        local requests_count=0
        
        # Essayer d'obtenir les métriques depuis l'endpoint /metrics
        local metrics_response=$(curl -s "http://localhost:$port/metrics" 2>/dev/null || echo "{}")
        
        if echo "$metrics_response" | grep -q "memory"; then
            memory_usage=$(echo "$metrics_response" | grep -o '"memory":[0-9]*' | cut -d':' -f2 || echo "0")
            cpu_usage=$(echo "$metrics_response" | grep -o '"cpu":[0-9.]*' | cut -d':' -f2 || echo "0")
            requests_count=$(echo "$metrics_response" | grep -o '"requests":[0-9]*' | cut -d':' -f2 || echo "0")
        fi
        
        # Ajouter les métriques
        echo "$timestamp,$agent,$status,$response_time,$memory_usage,$cpu_usage,$requests_count" >> "$agents_file"
        
        if [[ "$status" == "up" ]]; then
            log "METRIC" "Agent $agent - Status: UP, Response: ${response_time}ms"
        else
            log "WARN" "Agent $agent - Status: DOWN"
        fi
    done
}

# Fonction de collecte des métriques Kafka
collect_kafka_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local kafka_file="$METRICS_DIR/kafka_metrics_$(date +%Y%m%d).csv"
    
    # Créer le fichier CSV avec headers si nécessaire
    if [[ ! -f "$kafka_file" ]]; then
        echo "timestamp,status,topics_count,messages_per_sec,consumer_lag" > "$kafka_file"
    fi
    
    local status="down"
    local topics_count=0
    local messages_per_sec=0
    local consumer_lag=0
    
    # Vérifier si Kafka est accessible
    if nc -z localhost 9092 2>/dev/null; then
        status="up"
        
        # Obtenir le nombre de topics (simulation)
        topics_count=$(docker exec $(docker ps -q -f name=kafka) kafka-topics.sh --bootstrap-server localhost:9092 --list 2>/dev/null | wc -l || echo "0")
        
        # Simuler les métriques de messages et lag
        messages_per_sec=$(( RANDOM % 1000 + 100 ))
        consumer_lag=$(( RANDOM % 100 ))
    fi
    
    echo "$timestamp,$status,$topics_count,$messages_per_sec,$consumer_lag" >> "$kafka_file"
    
    log "METRIC" "Kafka - Status: $status, Topics: $topics_count, Messages/sec: $messages_per_sec"
}

# Fonction de collecte des métriques Weaviate
collect_weaviate_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local weaviate_file="$METRICS_DIR/weaviate_metrics_$(date +%Y%m%d).csv"
    
    # Créer le fichier CSV avec headers si nécessaire
    if [[ ! -f "$weaviate_file" ]]; then
        echo "timestamp,status,objects_count,queries_per_sec,response_time" > "$weaviate_file"
    fi
    
    local status="down"
    local objects_count=0
    local queries_per_sec=0
    local response_time=0
    
    # Mesurer le temps de réponse de Weaviate
    local start_time=$(date +%s%N)
    if curl -s http://localhost:8080/v1/.well-known/ready >/dev/null 2>&1; then
        local end_time=$(date +%s%N)
        response_time=$(( (end_time - start_time) / 1000000 )) # en millisecondes
        status="up"
        
        # Obtenir le nombre d'objets (simulation)
        objects_count=$(( RANDOM % 10000 + 1000 ))
        queries_per_sec=$(( RANDOM % 100 + 10 ))
    fi
    
    echo "$timestamp,$status,$objects_count,$queries_per_sec,$response_time" >> "$weaviate_file"
    
    log "METRIC" "Weaviate - Status: $status, Objects: $objects_count, Response: ${response_time}ms"
}

# Fonction de détection d'anomalies
detect_anomalies() {
    local current_time=$(date +%s)
    
    # Vérifier les seuils critiques
    
    # CPU > 90%
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | cut -d'.' -f1)
    if [[ $cpu_usage -gt 90 ]]; then
        log "WARN" "ANOMALIE: CPU usage élevé ($cpu_usage%)"
        send_alert "CPU_HIGH" "CPU usage is $cpu_usage%"
    fi
    
    # Mémoire > 85%
    local memory_info=$(free -m | grep '^Mem:')
    local total_memory=$(echo $memory_info | awk '{print $2}')
    local used_memory=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$(( used_memory * 100 / total_memory ))
    
    if [[ $memory_usage -gt 85 ]]; then
        log "WARN" "ANOMALIE: Memory usage élevé ($memory_usage%)"
        send_alert "MEMORY_HIGH" "Memory usage is $memory_usage%"
    fi
    
    # Vérifier les agents down
    local agents_down=0
    local agents_ports=(
        "cortex-central:3000"
        "frontend:3001"
        "backend:3002"
        "uiux:3006"
    )
    
    for agent_port in "${agents_ports[@]}"; do
        local agent=$(echo $agent_port | cut -d':' -f1)
        local port=$(echo $agent_port | cut -d':' -f2)
        
        if ! curl -s -f "http://localhost:$port/health" >/dev/null 2>&1; then
            ((agents_down++))
            log "WARN" "ANOMALIE: Agent $agent est down"
            send_alert "AGENT_DOWN" "Agent $agent is not responding"
        fi
    done
    
    if [[ $agents_down -gt 1 ]]; then
        log "ERROR" "ANOMALIE CRITIQUE: $agents_down agents sont down"
        send_alert "MULTIPLE_AGENTS_DOWN" "$agents_down agents are down"
    fi
}

# Fonction d'envoi d'alertes
send_alert() {
    local alert_type="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Sauvegarder l'alerte
    local alerts_file="$METRICS_DIR/alerts_$(date +%Y%m%d).log"
    echo "[$timestamp] $alert_type: $message" >> "$alerts_file"
    
    # Ici, on pourrait envoyer des notifications (email, Slack, etc.)
    log "ERROR" "ALERTE: $alert_type - $message"
}

# Fonction de génération de rapport
generate_performance_report() {
    local report_file="$METRICS_DIR/performance_report_$(date +%Y%m%d_%H%M%S).html"
    
    log "INFO" "Génération du rapport de performance..."
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Rapport de Performance - $(date)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .metric-box { 
            display: inline-block; 
            margin: 10px; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            min-width: 200px;
        }
        .metric-value { font-size: 24px; font-weight: bold; color: #333; }
        .metric-label { color: #666; }
        .status-up { color: green; }
        .status-down { color: red; }
        .status-warning { color: orange; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Rapport de Performance du Système</h1>
        <p>Généré le: $(date)</p>
    </div>
    
    <h2>Métriques Système</h2>
    <div class="metric-box">
        <div class="metric-value">$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%</div>
        <div class="metric-label">CPU Usage</div>
    </div>
    
    <div class="metric-box">
        <div class="metric-value">$(free -m | grep '^Mem:' | awk '{printf "%.1f", $3/$2 * 100}')%</div>
        <div class="metric-label">Memory Usage</div>
    </div>
    
    <div class="metric-box">
        <div class="metric-value">$(df -h / | awk 'NR==2 {print $5}')</div>
        <div class="metric-label">Disk Usage</div>
    </div>
    
    <h2>Status des Agents</h2>
    <!-- Le status des agents serait ajouté ici dynamiquement -->
    
    <h2>Alertes Récentes</h2>
    <!-- Les alertes seraient ajoutées ici -->
    
</body>
</html>
EOF
    
    log "INFO" "Rapport généré: $report_file"
}

# Fonction de nettoyage des anciens fichiers
cleanup_old_files() {
    log "INFO" "Nettoyage des anciens fichiers de métriques..."
    
    # Supprimer les fichiers de plus de 7 jours
    find "$METRICS_DIR" -name "*.csv" -mtime +7 -delete 2>/dev/null || true
    find "$METRICS_DIR" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find "$METRICS_DIR" -name "*.html" -mtime +7 -delete 2>/dev/null || true
    
    log "INFO" "Nettoyage terminé"
}

# Fonction d'affichage du dashboard en temps réel
display_dashboard() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    DASHBOARD PERFORMANCE                    ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    # Métriques système
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local memory_usage=$(free -m | grep '^Mem:' | awk '{printf "%.1f", $3/$2 * 100}')
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | cut -d'%' -f1)
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
    
    echo -e "${GREEN}Système:${NC}"
    echo -e "  CPU: ${cpu_usage}%"
    echo -e "  Mémoire: ${memory_usage}%"
    echo -e "  Disque: ${disk_usage}%"
    echo -e "  Load: ${load_avg}"
    echo ""
    
    # Status des agents
    echo -e "${GREEN}Agents:${NC}"
    local agents_ports=(
        "cortex-central:3000"
        "frontend:3001"
        "backend:3002"
        "uiux:3006"
        "web-research:3007"
        "data-analyst:3008"
        "project-manager:3009"
        "evolution:3010"
    )
    
    for agent_port in "${agents_ports[@]}"; do
        local agent=$(echo $agent_port | cut -d':' -f1)
        local port=$(echo $agent_port | cut -d':' -f2)
        
        if curl -s -f "http://localhost:$port/health" >/dev/null 2>&1; then
            echo -e "  $agent: ${GREEN}UP${NC}"
        else
            echo -e "  $agent: ${RED}DOWN${NC}"
        fi
    done
    
    echo ""
    echo -e "${YELLOW}Dernière mise à jour: $(date)${NC}"
    echo -e "${YELLOW}Appuyez sur Ctrl+C pour arrêter${NC}"
}

# Fonction principale
main() {
    local start_time=$(date +%s)
    local end_time=$((start_time + DURATION))
    local iteration=0
    
    log "INFO" "Démarrage du monitoring de performance"
    log "INFO" "Durée: ${DURATION}s, Intervalle: ${INTERVAL}s"
    
    # Créer les répertoires nécessaires
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$METRICS_DIR"
    
    # Nettoyage initial
    cleanup_old_files
    
    # Boucle de monitoring
    while [[ $(date +%s) -lt $end_time ]]; do
        ((iteration++))
        
        # Afficher le dashboard
        if [[ -t 1 ]]; then  # Si on est dans un terminal interactif
            display_dashboard
        fi
        
        # Collecter les métriques
        collect_system_metrics
        collect_agent_metrics
        collect_kafka_metrics
        collect_weaviate_metrics
        
        # Détecter les anomalies
        detect_anomalies
        
        # Générer un rapport toutes les 10 itérations
        if (( iteration % 10 == 0 )); then
            generate_performance_report
        fi
        
        # Attendre l'intervalle suivant
        sleep $INTERVAL
    done
    
    # Rapport final
    generate_performance_report
    
    local total_duration=$(($(date +%s) - start_time))
    log "INFO" "Monitoring terminé après ${total_duration} secondes"
    log "INFO" "Métriques sauvegardées dans: $METRICS_DIR"
}

# Gestion des signaux pour un arrêt propre
cleanup() {
    log "INFO" "Arrêt du monitoring..."
    generate_performance_report
    exit 0
}

trap cleanup SIGINT SIGTERM

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--duration)
            DURATION="$2"
            shift 2
            ;;
        -i|--interval)
            INTERVAL="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -d, --duration SECONDS   Durée du monitoring (défaut: 3600)"
            echo "  -i, --interval SECONDS   Intervalle de collecte (défaut: 30)"
            echo "  -h, --help               Afficher cette aide"
            exit 0
            ;;
        *)
            echo "Option inconnue: $1"
            exit 1
            ;;
    esac
done

# Exécuter le script principal
main "$@"
