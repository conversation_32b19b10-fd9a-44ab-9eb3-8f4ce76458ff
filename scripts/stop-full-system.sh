#!/bin/bash

# Script d'arrêt du système complet d'agents
# Arrête tous les agents et l'infrastructure de manière propre

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

log_info() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.v3.8.yml"

# Arrêter les processus Node.js
stop_nodejs_processes() {
    log "🛑 Arrêt des processus Node.js..."
    
    # Liste des fichiers PID à vérifier
    pid_files=(
        "cortex-central.pid"
        "backend-nestjs.pid"
        "agent-performance.pid"
        "agent-qa.pid"
        "agent-security.pid"
        "agent-devops.pid"
        "agent-uiux.pid"
        "agent-frontend.pid"
    )
    
    for pid_file in "${pid_files[@]}"; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            service_name=$(echo "$pid_file" | sed 's/.pid$//')
            
            if kill -0 "$pid" 2>/dev/null; then
                log_info "Arrêt de $service_name (PID: $pid)..."
                
                # Essayer d'abord un arrêt propre
                kill -TERM "$pid" 2>/dev/null || true
                
                # Attendre un peu
                sleep 3
                
                # Vérifier si le processus est toujours en cours
                if kill -0 "$pid" 2>/dev/null; then
                    log_warning "Arrêt forcé de $service_name..."
                    kill -KILL "$pid" 2>/dev/null || true
                fi
                
                log_success "$service_name arrêté"
            else
                log_info "$service_name n'était pas en cours d'exécution"
            fi
            
            # Supprimer le fichier PID
            rm -f "$pid_file"
        fi
    done
}

# Arrêter les processus par nom (fallback)
stop_processes_by_name() {
    log "🔍 Recherche de processus Node.js restants..."
    
    # Processus à rechercher
    process_patterns=(
        "cortex-central"
        "agent-performance"
        "agent-qa"
        "agent-security"
        "agent-devops"
        "agent-uiux"
        "agent-frontend"
        "backend-nestjs"
    )
    
    for pattern in "${process_patterns[@]}"; do
        pids=$(pgrep -f "$pattern" 2>/dev/null || true)
        
        if [ -n "$pids" ]; then
            log_info "Arrêt des processus $pattern..."
            
            for pid in $pids; do
                if kill -0 "$pid" 2>/dev/null; then
                    # Essayer d'abord un arrêt propre
                    kill -TERM "$pid" 2>/dev/null || true
                    sleep 2
                    
                    # Vérifier si le processus est toujours en cours
                    if kill -0 "$pid" 2>/dev/null; then
                        kill -KILL "$pid" 2>/dev/null || true
                    fi
                fi
            done
            
            log_success "Processus $pattern arrêtés"
        fi
    done
}

# Arrêter l'infrastructure Docker
stop_infrastructure() {
    log "🐳 Arrêt de l'infrastructure Docker..."
    
    if [ -f "$DOCKER_COMPOSE_FILE" ]; then
        log_info "Arrêt des services Docker Compose..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        
        log_success "Infrastructure Docker arrêtée"
    else
        log_warning "Fichier docker-compose non trouvé: $DOCKER_COMPOSE_FILE"
        
        # Essayer d'arrêter les conteneurs individuellement
        log_info "Tentative d'arrêt des conteneurs individuels..."
        
        containers=(
            "agent-performance"
            "cortex-central"
            "kafka-performance"
            "zookeeper-performance"
            "weaviate-performance"
            "redis-performance"
            "prometheus-performance"
            "grafana-performance"
        )
        
        for container in "${containers[@]}"; do
            if docker ps -q -f name="$container" | grep -q .; then
                log_info "Arrêt du conteneur $container..."
                docker stop "$container" 2>/dev/null || true
                docker rm "$container" 2>/dev/null || true
            fi
        done
    fi
}

# Nettoyer les ressources
cleanup_resources() {
    log "🧹 Nettoyage des ressources..."
    
    # Nettoyer les fichiers temporaires
    if [ -d "temp" ]; then
        log_info "Nettoyage du répertoire temporaire..."
        rm -rf temp/*
    fi
    
    # Nettoyer les anciens logs (garder les 5 derniers)
    if [ -d "logs" ]; then
        log_info "Rotation des logs..."
        find logs -name "*.log" -type f -mtime +5 -delete 2>/dev/null || true
    fi
    
    # Nettoyer les fichiers PID orphelins
    log_info "Nettoyage des fichiers PID..."
    find . -name "*.pid" -type f -delete 2>/dev/null || true
    
    log_success "Nettoyage terminé"
}

# Vérifier que tout est arrêté
verify_shutdown() {
    log "✅ Vérification de l'arrêt..."
    
    # Vérifier les ports
    ports=(3000 3001 3007 3008 3009 3010 3011 3012 8080 9092 6379 9091)
    active_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -i ":$port" &>/dev/null; then
            active_ports+=("$port")
        fi
    done
    
    if [ ${#active_ports[@]} -eq 0 ]; then
        log_success "Tous les ports sont libérés"
    else
        log_warning "Ports encore actifs: ${active_ports[*]}"
        
        # Afficher les processus utilisant ces ports
        for port in "${active_ports[@]}"; do
            log_info "Port $port utilisé par:"
            lsof -i ":$port" 2>/dev/null || true
        done
    fi
    
    # Vérifier les processus Node.js
    node_processes=$(pgrep -f "node.*agent\|node.*cortex" 2>/dev/null || true)
    
    if [ -z "$node_processes" ]; then
        log_success "Aucun processus Node.js d'agent en cours"
    else
        log_warning "Processus Node.js encore actifs:"
        ps -p $node_processes 2>/dev/null || true
    fi
    
    # Vérifier les conteneurs Docker
    running_containers=$(docker ps -q --filter "name=agent\|cortex\|kafka\|weaviate\|redis\|prometheus\|grafana" 2>/dev/null || true)
    
    if [ -z "$running_containers" ]; then
        log_success "Aucun conteneur Docker en cours"
    else
        log_warning "Conteneurs Docker encore actifs:"
        docker ps --filter "name=agent\|cortex\|kafka\|weaviate\|redis\|prometheus\|grafana" 2>/dev/null || true
    fi
}

# Afficher les statistiques d'arrêt
display_shutdown_stats() {
    log "📊 Statistiques d'arrêt:"
    echo ""
    
    # Temps d'arrêt
    if [ -n "$start_time" ]; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "  ⏱️  Durée d'arrêt: ${duration}s"
    fi
    
    # Espace disque libéré (approximatif)
    if [ -d "logs" ]; then
        log_size=$(du -sh logs 2>/dev/null | cut -f1 || echo "0")
        echo "  💾 Taille des logs: $log_size"
    fi
    
    # Processus arrêtés
    echo "  🛑 Processus arrêtés: Tous les agents et services"
    
    echo ""
    log_success "🎯 Arrêt terminé avec succès!"
}

# Fonction d'arrêt d'urgence
emergency_shutdown() {
    log_error "🚨 Arrêt d'urgence demandé!"
    
    # Tuer tous les processus Node.js liés aux agents
    pkill -f "node.*agent" 2>/dev/null || true
    pkill -f "node.*cortex" 2>/dev/null || true
    
    # Arrêter tous les conteneurs Docker
    docker stop $(docker ps -q) 2>/dev/null || true
    
    # Nettoyer
    cleanup_resources
    
    log_warning "Arrêt d'urgence terminé"
    exit 1
}

# Fonction principale
main() {
    start_time=$(date +%s)
    
    echo ""
    echo "🛑 Arrêt du système complet d'agents"
    echo "===================================="
    echo ""
    
    # Gestion des options
    case "${1:-}" in
        --emergency)
            emergency_shutdown
            ;;
        --force)
            log_warning "Mode forcé activé"
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --emergency    Arrêt d'urgence (force kill)"
            echo "  --force        Arrêt forcé sans attendre"
            echo "  --help         Afficher cette aide"
            echo ""
            exit 0
            ;;
    esac
    
    # Arrêter les processus Node.js
    stop_nodejs_processes
    
    # Arrêter les processus par nom (fallback)
    stop_processes_by_name
    
    # Arrêter l'infrastructure Docker
    stop_infrastructure
    
    # Nettoyer les ressources
    cleanup_resources
    
    # Attendre un peu pour que tout se stabilise
    if [ "$1" != "--force" ]; then
        log_info "Attente de la stabilisation..."
        sleep 3
    fi
    
    # Vérifier que tout est arrêté
    verify_shutdown
    
    # Afficher les statistiques
    display_shutdown_stats
}

# Gestion des signaux
trap 'log_warning "Signal reçu pendant l'\''arrêt..."; emergency_shutdown' SIGINT SIGTERM

# Exécuter la fonction principale
main "$@"
