#!/usr/bin/env node

/**
 * Script de test d'intégration pour l'Agent Performance
 * Valide la communication inter-agents et les workflows end-to-end
 */

const axios = require('axios');
const { Kafka } = require('kafkajs');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  agentPerformance: {
    url: process.env.AGENT_PERFORMANCE_URL || 'http://localhost:3007',
    timeout: 30000
  },
  cortexCentral: {
    url: process.env.CORTEX_CENTRAL_URL || 'http://localhost:3001',
    timeout: 30000
  },
  kafka: {
    brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
    clientId: 'integration-test',
    groupId: 'integration-test-group'
  }
};

// Couleurs pour les logs
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * Test de santé des agents
 */
async function testAgentHealth() {
  log('\n🔍 Test de santé des agents...', 'cyan');
  
  const agents = [
    { name: 'Agent Performance', url: config.agentPerformance.url },
    { name: 'Cortex Central', url: config.cortexCentral.url }
  ];
  
  for (const agent of agents) {
    try {
      const response = await axios.get(`${agent.url}/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        logSuccess(`${agent.name} est en ligne`);
      } else {
        logWarning(`${agent.name} répond mais avec un statut ${response.status}`);
      }
    } catch (error) {
      logError(`${agent.name} n'est pas accessible: ${error.message}`);
      return false;
    }
  }
  
  return true;
}

/**
 * Test de communication Kafka
 */
async function testKafkaCommunication() {
  log('\n📡 Test de communication Kafka...', 'cyan');
  
  const kafka = new Kafka(config.kafka);
  const producer = kafka.producer();
  const consumer = kafka.consumer({ groupId: config.kafka.groupId });
  
  try {
    // Connexion
    await producer.connect();
    await consumer.connect();
    logSuccess('Connexion Kafka établie');
    
    // Test de publication/consommation
    const testTopic = 'integration-test';
    const testMessage = {
      id: `test-${Date.now()}`,
      type: 'integration_test',
      payload: { message: 'Test de communication Kafka' },
      timestamp: new Date()
    };
    
    // S'abonner au topic
    await consumer.subscribe({ topic: testTopic });
    
    let messageReceived = false;
    
    // Consommer les messages
    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        const receivedMessage = JSON.parse(message.value.toString());
        if (receivedMessage.id === testMessage.id) {
          messageReceived = true;
          logSuccess('Message Kafka reçu avec succès');
        }
      }
    });
    
    // Publier le message
    await producer.send({
      topic: testTopic,
      messages: [{
        key: testMessage.id,
        value: JSON.stringify(testMessage)
      }]
    });
    
    logInfo('Message Kafka publié');
    
    // Attendre la réception
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (!messageReceived) {
      logWarning('Message Kafka non reçu dans les temps');
    }
    
    // Déconnexion
    await consumer.disconnect();
    await producer.disconnect();
    
    return messageReceived;
    
  } catch (error) {
    logError(`Erreur de communication Kafka: ${error.message}`);
    return false;
  }
}

/**
 * Test de benchmark via l'Agent Performance
 */
async function testPerformanceBenchmark() {
  log('\n⚡ Test de benchmark de performance...', 'cyan');
  
  try {
    const benchmarkRequest = {
      type: 'lighthouse',
      target: {
        url: 'https://example.com'
      },
      configuration: {
        lighthouse: {
          categories: ['performance'],
          formFactor: 'desktop'
        }
      }
    };
    
    const startTime = performance.now();
    
    const response = await axios.post(
      `${config.agentPerformance.url}/benchmark`,
      benchmarkRequest,
      { timeout: config.agentPerformance.timeout }
    );
    
    const endTime = performance.now();
    
    if (response.status === 200) {
      logSuccess(`Benchmark démarré: ${response.data.benchmarkId}`);
      logInfo(`Temps de réponse: ${Math.round(endTime - startTime)}ms`);
      return true;
    } else {
      logError(`Erreur lors du benchmark: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Erreur lors du test de benchmark: ${error.message}`);
    return false;
  }
}

/**
 * Test d'optimisation via l'Agent Performance
 */
async function testPerformanceOptimization() {
  log('\n💡 Test d\'optimisation de performance...', 'cyan');
  
  try {
    const optimizationRequest = {
      target: {
        service: 'test-service'
      },
      scope: ['frontend', 'backend'],
      constraints: {
        timeline: 'normal',
        technologies: ['react', 'nodejs']
      },
      goals: {
        performance: 80
      }
    };
    
    const startTime = performance.now();
    
    const response = await axios.post(
      `${config.agentPerformance.url}/optimize`,
      optimizationRequest,
      { timeout: config.agentPerformance.timeout }
    );
    
    const endTime = performance.now();
    
    if (response.status === 200) {
      logSuccess(`Optimisations générées: ${response.data.recommendations.length} recommandations`);
      logInfo(`Temps de réponse: ${Math.round(endTime - startTime)}ms`);
      return true;
    } else {
      logError(`Erreur lors de l'optimisation: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Erreur lors du test d'optimisation: ${error.message}`);
    return false;
  }
}

/**
 * Test de monitoring via l'Agent Performance
 */
async function testPerformanceMonitoring() {
  log('\n📊 Test de monitoring de performance...', 'cyan');
  
  try {
    const monitoringRequest = {
      targets: ['test-service-1', 'test-service-2'],
      metrics: ['response_time', 'cpu_usage', 'memory_usage'],
      thresholds: {
        response_time: 1000,
        cpu_usage: 80,
        memory_usage: 85
      },
      interval: 30000
    };
    
    const startTime = performance.now();
    
    const response = await axios.post(
      `${config.agentPerformance.url}/monitor`,
      monitoringRequest,
      { timeout: config.agentPerformance.timeout }
    );
    
    const endTime = performance.now();
    
    if (response.status === 200) {
      logSuccess(`Monitoring configuré: ${response.data.monitorId}`);
      logInfo(`Temps de réponse: ${Math.round(endTime - startTime)}ms`);
      return true;
    } else {
      logError(`Erreur lors de la configuration du monitoring: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Erreur lors du test de monitoring: ${error.message}`);
    return false;
  }
}

/**
 * Test de récupération des métriques
 */
async function testMetricsRetrieval() {
  log('\n📈 Test de récupération des métriques...', 'cyan');
  
  try {
    const response = await axios.get(
      `${config.agentPerformance.url}/metrics`,
      { timeout: 5000 }
    );
    
    if (response.status === 200) {
      const metricsCount = response.data.split('\n').filter(line => 
        line.startsWith('#') || line.trim() === ''
      ).length;
      
      logSuccess(`Métriques récupérées: ${metricsCount} lignes`);
      return true;
    } else {
      logError(`Erreur lors de la récupération des métriques: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Erreur lors du test de métriques: ${error.message}`);
    return false;
  }
}

/**
 * Test de statut de l'agent
 */
async function testAgentStatus() {
  log('\n📋 Test de statut de l\'agent...', 'cyan');
  
  try {
    const response = await axios.get(
      `${config.agentPerformance.url}/status`,
      { timeout: 5000 }
    );
    
    if (response.status === 200) {
      const status = response.data;
      logSuccess(`Agent en fonctionnement: ${status.isRunning ? 'Oui' : 'Non'}`);
      logInfo(`Jobs actifs: ${status.activeJobs}`);
      logInfo(`Alertes actives: ${status.alerts.total}`);
      logInfo(`Uptime: ${Math.round(status.uptime)}s`);
      return true;
    } else {
      logError(`Erreur lors de la récupération du statut: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Erreur lors du test de statut: ${error.message}`);
    return false;
  }
}

/**
 * Fonction principale de test
 */
async function runIntegrationTests() {
  log('🚀 Démarrage des tests d\'intégration Agent Performance', 'bright');
  log('=' * 60, 'cyan');
  
  const tests = [
    { name: 'Santé des agents', fn: testAgentHealth },
    { name: 'Communication Kafka', fn: testKafkaCommunication },
    { name: 'Benchmark de performance', fn: testPerformanceBenchmark },
    { name: 'Optimisation de performance', fn: testPerformanceOptimization },
    { name: 'Monitoring de performance', fn: testPerformanceMonitoring },
    { name: 'Récupération des métriques', fn: testMetricsRetrieval },
    { name: 'Statut de l\'agent', fn: testAgentStatus }
  ];
  
  const results = [];
  const startTime = performance.now();
  
  for (const test of tests) {
    try {
      const testStartTime = performance.now();
      const result = await test.fn();
      const testEndTime = performance.now();
      
      results.push({
        name: test.name,
        success: result,
        duration: Math.round(testEndTime - testStartTime)
      });
      
    } catch (error) {
      logError(`Erreur lors du test "${test.name}": ${error.message}`);
      results.push({
        name: test.name,
        success: false,
        duration: 0,
        error: error.message
      });
    }
  }
  
  const endTime = performance.now();
  const totalDuration = Math.round(endTime - startTime);
  
  // Résumé des résultats
  log('\n📊 Résumé des tests d\'intégration', 'bright');
  log('=' * 60, 'cyan');
  
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const duration = result.duration > 0 ? ` (${result.duration}ms)` : '';
    log(`${status} ${result.name}${duration}`);
    
    if (result.error) {
      log(`   Erreur: ${result.error}`, 'red');
    }
  });
  
  log(`\n📈 Résultats: ${successCount}/${results.length} tests réussis`, 'bright');
  log(`⏱️  Durée totale: ${totalDuration}ms`, 'blue');
  
  if (failureCount > 0) {
    log(`\n⚠️  ${failureCount} test(s) ont échoué`, 'yellow');
    process.exit(1);
  } else {
    log('\n🎉 Tous les tests d\'intégration ont réussi!', 'green');
    process.exit(0);
  }
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  runIntegrationTests().catch(error => {
    logError(`Erreur fatale lors des tests: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runIntegrationTests,
  testAgentHealth,
  testKafkaCommunication,
  testPerformanceBenchmark,
  testPerformanceOptimization,
  testPerformanceMonitoring,
  testMetricsRetrieval,
  testAgentStatus
};
