#!/bin/bash

# Script de test pour l'Agent UI/UX Design Thinking
# Retreat And Be - Living AI Organism Architecture v3.8

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AGENT_URL="http://localhost:3005"
TEST_RESULTS_DIR="test-results"

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Créer le répertoire de résultats
setup_test_environment() {
    log_info "Configuration de l'environnement de test..."
    mkdir -p ${TEST_RESULTS_DIR}
    log_success "Environnement de test configuré"
}

# Test de santé de l'agent
test_health() {
    log_info "Test de santé de l'agent..."
    
    response=$(curl -s -w "%{http_code}" -o "${TEST_RESULTS_DIR}/health.json" "${AGENT_URL}/health")
    
    if [ "$response" = "200" ]; then
        log_success "✅ Health check réussi"
        return 0
    else
        log_error "❌ Health check échoué (HTTP $response)"
        return 1
    fi
}

# Test de disponibilité
test_ready() {
    log_info "Test de disponibilité de l'agent..."
    
    response=$(curl -s -w "%{http_code}" -o "${TEST_RESULTS_DIR}/ready.json" "${AGENT_URL}/ready")
    
    if [ "$response" = "200" ]; then
        log_success "✅ Ready check réussi"
        return 0
    else
        log_error "❌ Ready check échoué (HTTP $response)"
        return 1
    fi
}

# Test des informations de l'agent
test_agent_info() {
    log_info "Test des informations de l'agent..."
    
    response=$(curl -s -w "%{http_code}" -o "${TEST_RESULTS_DIR}/info.json" "${AGENT_URL}/api/info")
    
    if [ "$response" = "200" ]; then
        log_success "✅ Info agent récupérées"
        
        # Vérifier que les capacités sont présentes
        if grep -q "user_research" "${TEST_RESULTS_DIR}/info.json"; then
            log_success "✅ Capacités de recherche utilisateur détectées"
        else
            log_warning "⚠️ Capacités de recherche utilisateur non détectées"
        fi
        
        return 0
    else
        log_error "❌ Récupération info agent échouée (HTTP $response)"
        return 1
    fi
}

# Test de recherche utilisateur
test_user_research() {
    log_info "Test de recherche utilisateur..."
    
    # Données de test
    cat > "${TEST_RESULTS_DIR}/research_request.json" << EOF
{
  "requirements": {
    "industry": "wellness",
    "targetAudience": ["professionals", "wellness-seekers"],
    "brand": {
      "name": "Retreat And Be Test",
      "personality": ["authentic", "calming", "professional"],
      "values": ["wellness", "community", "growth"]
    },
    "competitors": ["headspace.com", "calm.com"],
    "deviceTargets": ["mobile", "desktop"],
    "accessibilityLevel": "AA",
    "conversionGoals": ["booking", "newsletter-signup"]
  }
}
EOF
    
    response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -d @"${TEST_RESULTS_DIR}/research_request.json" \
        -o "${TEST_RESULTS_DIR}/research_response.json" \
        "${AGENT_URL}/api/research")
    
    if [ "$response" = "200" ]; then
        log_success "✅ Recherche utilisateur réussie"
        
        # Vérifier la structure de la réponse
        if grep -q "demographics" "${TEST_RESULTS_DIR}/research_response.json"; then
            log_success "✅ Données démographiques générées"
        fi
        
        if grep -q "personas" "${TEST_RESULTS_DIR}/research_response.json"; then
            log_success "✅ Personas générés"
        fi
        
        return 0
    else
        log_error "❌ Recherche utilisateur échouée (HTTP $response)"
        cat "${TEST_RESULTS_DIR}/research_response.json"
        return 1
    fi
}

# Test de génération de design complet
test_comprehensive_design() {
    log_info "Test de génération de design complet..."
    
    # Données de test pour un design complet
    cat > "${TEST_RESULTS_DIR}/design_request.json" << EOF
{
  "requirements": {
    "industry": "wellness",
    "targetAudience": ["professionals", "wellness-seekers"],
    "brand": {
      "name": "Retreat And Be Test",
      "personality": ["authentic", "calming", "professional"],
      "values": ["wellness", "community", "growth"],
      "colors": ["#2D5A87", "#8FBC8F", "#F5F5DC"]
    },
    "competitors": ["headspace.com", "calm.com"],
    "similarProducts": ["meditation-apps", "wellness-platforms"],
    "contentTypes": ["landing-pages", "booking-forms", "user-dashboard"],
    "deviceTargets": ["mobile", "desktop", "tablet"],
    "accessibilityLevel": "AA",
    "conversionGoals": ["booking", "newsletter-signup", "user-registration"]
  }
}
EOF
    
    log_info "Envoi de la demande de design complet (cela peut prendre du temps)..."
    
    response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -d @"${TEST_RESULTS_DIR}/design_request.json" \
        -o "${TEST_RESULTS_DIR}/design_response.json" \
        --max-time 120 \
        "${AGENT_URL}/api/design")
    
    if [ "$response" = "200" ]; then
        log_success "✅ Design complet généré avec succès"
        
        # Vérifier les composants du design
        if grep -q "userResearch" "${TEST_RESULTS_DIR}/design_response.json"; then
            log_success "✅ Recherche utilisateur incluse"
        fi
        
        if grep -q "designSystem" "${TEST_RESULTS_DIR}/design_response.json"; then
            log_success "✅ Design system généré"
        fi
        
        if grep -q "wireframes" "${TEST_RESULTS_DIR}/design_response.json"; then
            log_success "✅ Wireframes créés"
        fi
        
        if grep -q "componentLibrary" "${TEST_RESULTS_DIR}/design_response.json"; then
            log_success "✅ Bibliothèque de composants générée"
        fi
        
        return 0
    else
        log_error "❌ Génération de design complet échouée (HTTP $response)"
        cat "${TEST_RESULTS_DIR}/design_response.json"
        return 1
    fi
}

# Test de validation d'implémentation
test_implementation_validation() {
    log_info "Test de validation d'implémentation..."
    
    # Code HTML/CSS de test
    cat > "${TEST_RESULTS_DIR}/validation_request.json" << EOF
{
  "code": "<!DOCTYPE html><html lang=\"fr\"><head><meta charset=\"UTF-8\"><title>Test</title></head><body><header><nav><ul><li><a href=\"#\">Accueil</a></li></ul></nav></header><main><h1>Bienvenue</h1><p>Contenu principal</p><button aria-label=\"Réserver maintenant\">Réserver</button></main><footer><p>&copy; 2024 Test</p></footer></body></html>",
  "design": {
    "designSystem": {
      "colorSystem": {
        "primary": ["#2D5A87"],
        "secondary": ["#8FBC8F"]
      },
      "typography": {
        "fontPairings": {
          "heading": "Inter",
          "body": "Inter"
        }
      }
    },
    "accessibilityCompliance": {
      "wcag21AA": {
        "level": "AA",
        "score": 0.9
      }
    }
  }
}
EOF
    
    response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -d @"${TEST_RESULTS_DIR}/validation_request.json" \
        -o "${TEST_RESULTS_DIR}/validation_response.json" \
        "${AGENT_URL}/api/validate")
    
    if [ "$response" = "200" ]; then
        log_success "✅ Validation d'implémentation réussie"
        
        # Vérifier les résultats de validation
        if grep -q "accessibilityReport" "${TEST_RESULTS_DIR}/validation_response.json"; then
            log_success "✅ Rapport d'accessibilité généré"
        fi
        
        return 0
    else
        log_error "❌ Validation d'implémentation échouée (HTTP $response)"
        cat "${TEST_RESULTS_DIR}/validation_response.json"
        return 1
    fi
}

# Test de performance
test_performance() {
    log_info "Test de performance de l'agent..."
    
    start_time=$(date +%s)
    
    # Test de charge légère - 5 requêtes simultanées
    for i in {1..5}; do
        curl -s "${AGENT_URL}/health" > /dev/null &
    done
    wait
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    if [ $duration -lt 10 ]; then
        log_success "✅ Test de performance réussi (${duration}s)"
        return 0
    else
        log_warning "⚠️ Performance dégradée (${duration}s)"
        return 1
    fi
}

# Génération du rapport de test
generate_test_report() {
    log_info "Génération du rapport de test..."
    
    cat > "${TEST_RESULTS_DIR}/test_report.md" << EOF
# Rapport de Test - Agent UI/UX Design Thinking

**Date:** $(date)
**Agent:** UI/UX Design Thinking
**URL:** ${AGENT_URL}

## Résultats des Tests

### Tests de Base
- Health Check: $([ -f "${TEST_RESULTS_DIR}/health.json" ] && echo "✅ RÉUSSI" || echo "❌ ÉCHOUÉ")
- Ready Check: $([ -f "${TEST_RESULTS_DIR}/ready.json" ] && echo "✅ RÉUSSI" || echo "❌ ÉCHOUÉ")
- Agent Info: $([ -f "${TEST_RESULTS_DIR}/info.json" ] && echo "✅ RÉUSSI" || echo "❌ ÉCHOUÉ")

### Tests Fonctionnels
- Recherche Utilisateur: $([ -f "${TEST_RESULTS_DIR}/research_response.json" ] && echo "✅ RÉUSSI" || echo "❌ ÉCHOUÉ")
- Design Complet: $([ -f "${TEST_RESULTS_DIR}/design_response.json" ] && echo "✅ RÉUSSI" || echo "❌ ÉCHOUÉ")
- Validation Implémentation: $([ -f "${TEST_RESULTS_DIR}/validation_response.json" ] && echo "✅ RÉUSSI" || echo "❌ ÉCHOUÉ")

### Tests de Performance
- Test de Charge: Voir les logs ci-dessus

## Fichiers Générés
- \`health.json\` - Réponse du health check
- \`info.json\` - Informations de l'agent
- \`research_response.json\` - Résultats de recherche utilisateur
- \`design_response.json\` - Design complet généré
- \`validation_response.json\` - Résultats de validation

## Recommandations
1. Vérifier les logs de l'agent pour les erreurs
2. Tester avec des données réelles
3. Effectuer des tests de charge plus importants
4. Valider l'intégration avec d'autres agents

EOF
    
    log_success "Rapport de test généré : ${TEST_RESULTS_DIR}/test_report.md"
}

# Fonction principale
main() {
    log_info "=== TESTS AGENT UI/UX DESIGN THINKING ==="
    echo ""
    
    # Vérifier les arguments
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Afficher cette aide"
        echo "  --quick        Tests rapides uniquement"
        echo "  --full         Tests complets (par défaut)"
        echo ""
        exit 0
    fi
    
    setup_test_environment
    
    # Tests de base
    test_count=0
    success_count=0
    
    tests=(
        "test_health"
        "test_ready" 
        "test_agent_info"
    )
    
    # Ajouter les tests complets si demandé
    if [ "$1" != "--quick" ]; then
        tests+=(
            "test_user_research"
            "test_comprehensive_design"
            "test_implementation_validation"
            "test_performance"
        )
    fi
    
    # Exécuter les tests
    for test in "${tests[@]}"; do
        ((test_count++))
        echo ""
        if $test; then
            ((success_count++))
        fi
    done
    
    echo ""
    log_info "=== RÉSULTATS DES TESTS ==="
    log_info "Tests réussis: ${success_count}/${test_count}"
    
    if [ $success_count -eq $test_count ]; then
        log_success "🎉 Tous les tests sont réussis !"
    else
        log_warning "⚠️ Certains tests ont échoué"
    fi
    
    generate_test_report
    
    echo ""
    log_info "📁 Résultats disponibles dans : ${TEST_RESULTS_DIR}/"
    log_info "📋 Rapport complet : ${TEST_RESULTS_DIR}/test_report.md"
}

# Exécution du script principal
main "$@"
