#!/bin/bash

# =============================================================================
# Script de test d'intégration système complet
# =============================================================================

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/integration-tests.log"
RESULTS_DIR="$PROJECT_ROOT/test-results"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Compteurs de tests
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Fonction de logging
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            ;;
        "TEST")
            echo -e "${PURPLE}[TEST]${NC} $message"
            ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Fonction de test générique
run_test() {
    local test_name="$1"
    local test_function="$2"
    
    ((TOTAL_TESTS++))
    log "TEST" "Exécution du test: $test_name"
    
    if $test_function; then
        ((PASSED_TESTS++))
        log "TEST" "✅ RÉUSSI: $test_name"
        return 0
    else
        ((FAILED_TESTS++))
        log "TEST" "❌ ÉCHEC: $test_name"
        return 1
    fi
}

# Test de connectivité des agents
test_agents_connectivity() {
    log "INFO" "Test de connectivité des agents..."
    
    local agents_endpoints=(
        "cortex-central:3000:/health"
        "frontend:3001:/health"
        "backend:3002:/health"
        "devops:3003:/health"
        "qa:3004:/health"
        "security:3005:/health"
        "uiux:3006:/health"
        "web-research:3007:/health"
        "data-analyst:3008:/health"
        "project-manager:3009:/health"
        "evolution:3010:/health"
    )
    
    local all_healthy=true
    
    for endpoint in "${agents_endpoints[@]}"; do
        local agent=$(echo $endpoint | cut -d':' -f1)
        local port=$(echo $endpoint | cut -d':' -f2)
        local path=$(echo $endpoint | cut -d':' -f3)
        local url="http://localhost:$port$path"
        
        if curl -s -f "$url" >/dev/null 2>&1; then
            log "DEBUG" "Agent $agent est accessible"
        else
            log "ERROR" "Agent $agent n'est pas accessible sur $url"
            all_healthy=false
        fi
    done
    
    return $all_healthy
}

# Test de communication Kafka
test_kafka_communication() {
    log "INFO" "Test de communication Kafka..."
    
    # Vérifier que Kafka est accessible
    if ! nc -z localhost 9092 2>/dev/null; then
        log "ERROR" "Kafka n'est pas accessible"
        return 1
    fi
    
    # Test de publication/consommation de message
    local test_topic="test-integration-$(date +%s)"
    local test_message="Test message from integration test"
    
    # Publier un message de test
    echo "$test_message" | docker exec -i $(docker ps -q -f name=kafka) \
        kafka-console-producer.sh --broker-list localhost:9092 --topic "$test_topic" 2>/dev/null
    
    # Consommer le message
    local consumed_message=$(timeout 10s docker exec $(docker ps -q -f name=kafka) \
        kafka-console-consumer.sh --bootstrap-server localhost:9092 --topic "$test_topic" \
        --from-beginning --max-messages 1 2>/dev/null | head -1)
    
    if [[ "$consumed_message" == "$test_message" ]]; then
        log "DEBUG" "Communication Kafka fonctionnelle"
        return 0
    else
        log "ERROR" "Échec de la communication Kafka"
        return 1
    fi
}

# Test de stockage Weaviate
test_weaviate_storage() {
    log "INFO" "Test de stockage Weaviate..."
    
    # Vérifier que Weaviate est accessible
    if ! curl -s http://localhost:8080/v1/.well-known/ready >/dev/null 2>&1; then
        log "ERROR" "Weaviate n'est pas accessible"
        return 1
    fi
    
    # Test de création et récupération d'objet
    local test_class="TestIntegration"
    local test_data='{
        "class": "'$test_class'",
        "properties": {
            "content": "Test integration data",
            "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
        }
    }'
    
    # Créer un objet de test
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$test_data" \
        http://localhost:8080/v1/objects)
    
    if echo "$response" | grep -q "id"; then
        log "DEBUG" "Stockage Weaviate fonctionnel"
        return 0
    else
        log "ERROR" "Échec du stockage Weaviate"
        return 1
    fi
}

# Test de workflow complet UX
test_ux_workflow() {
    log "INFO" "Test de workflow UX complet..."
    
    # 1. Demander une analyse UX
    local ux_request='{
        "type": "design_analysis",
        "target": "test-component",
        "parameters": {
            "includeAccessibility": true,
            "includeUsability": true,
            "includePerformance": true
        }
    }'
    
    local ux_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$ux_request" \
        http://localhost:3006/analyze)
    
    if echo "$ux_response" | grep -q "success"; then
        log "DEBUG" "Analyse UX initiée avec succès"
    else
        log "ERROR" "Échec de l'analyse UX"
        return 1
    fi
    
    # 2. Vérifier que les données sont transmises au web research
    sleep 5
    
    # 3. Vérifier que les insights sont générés
    local insights_response=$(curl -s http://localhost:3006/insights/latest)
    
    if echo "$insights_response" | grep -q "insights"; then
        log "DEBUG" "Workflow UX complet fonctionnel"
        return 0
    else
        log "ERROR" "Échec du workflow UX"
        return 1
    fi
}

# Test de workflow de recherche web
test_web_research_workflow() {
    log "INFO" "Test de workflow de recherche web..."
    
    # 1. Lancer une recherche
    local research_request='{
        "query": "UX design trends 2024",
        "sources": [
            {"type": "web", "priority": "high"}
        ],
        "options": {
            "maxResults": 5,
            "depth": "medium"
        }
    }'
    
    local research_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$research_request" \
        http://localhost:3007/research)
    
    if echo "$research_response" | grep -q "success"; then
        log "DEBUG" "Recherche web initiée avec succès"
    else
        log "ERROR" "Échec de la recherche web"
        return 1
    fi
    
    # 2. Vérifier que les tendances design sont analysées
    sleep 10
    
    local trends_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"industry": "tech"}' \
        http://localhost:3007/design-trends)
    
    if echo "$trends_response" | grep -q "success"; then
        log "DEBUG" "Workflow de recherche web fonctionnel"
        return 0
    else
        log "ERROR" "Échec du workflow de recherche web"
        return 1
    fi
}

# Test de workflow d'analyse de données
test_data_analysis_workflow() {
    log "INFO" "Test de workflow d'analyse de données..."
    
    # 1. Créer une analyse
    local analysis_request='{
        "id": "test-analysis-'$(date +%s)'",
        "type": "descriptive",
        "data": [
            {
                "id": "test-data",
                "name": "Test Dataset",
                "type": "json",
                "source": "test"
            }
        ],
        "parameters": {
            "metrics": ["performance", "usability"],
            "timeRange": {
                "start": "'$(date -d '7 days ago' -u +%Y-%m-%dT%H:%M:%SZ)'",
                "end": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
            }
        }
    }'
    
    local analysis_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$analysis_request" \
        http://localhost:3008/analysis)
    
    if echo "$analysis_response" | grep -q "success"; then
        log "DEBUG" "Analyse de données initiée avec succès"
        return 0
    else
        log "ERROR" "Échec de l'analyse de données"
        return 1
    fi
}

# Test de workflow de gestion de projet
test_project_management_workflow() {
    log "INFO" "Test de workflow de gestion de projet..."
    
    # 1. Créer un projet
    local project_request='{
        "name": "Test Integration Project",
        "description": "Projet de test pour l'\''intégration système",
        "startDate": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "endDate": "'$(date -d '+30 days' -u +%Y-%m-%dT%H:%M:%SZ)'",
        "budget": {
            "total": 10000,
            "currency": "EUR"
        },
        "team": [
            {
                "id": "test-user",
                "name": "Test User",
                "role": "Developer",
                "skills": [{"name": "JavaScript", "level": "expert"}]
            }
        ]
    }'
    
    local project_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$project_request" \
        http://localhost:3009/projects)
    
    if echo "$project_response" | grep -q "success"; then
        log "DEBUG" "Projet créé avec succès"
        
        # 2. Extraire l'ID du projet
        local project_id=$(echo "$project_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        
        if [[ -n "$project_id" ]]; then
            # 3. Créer une tâche
            local task_request='{
                "name": "Test Task",
                "description": "Tâche de test",
                "estimatedHours": 8,
                "priority": "medium"
            }'
            
            local task_response=$(curl -s -X POST \
                -H "Content-Type: application/json" \
                -d "$task_request" \
                "http://localhost:3009/projects/$project_id/tasks")
            
            if echo "$task_response" | grep -q "success"; then
                log "DEBUG" "Workflow de gestion de projet fonctionnel"
                return 0
            fi
        fi
    fi
    
    log "ERROR" "Échec du workflow de gestion de projet"
    return 1
}

# Test de workflow d'évolution
test_evolution_workflow() {
    log "INFO" "Test de workflow d'évolution..."
    
    # 1. Demander une mise à jour du tech radar
    local radar_response=$(curl -s -X POST http://localhost:3010/tech-radar/update)
    
    if echo "$radar_response" | grep -q "success"; then
        log "DEBUG" "Mise à jour du tech radar initiée"
    else
        log "ERROR" "Échec de la mise à jour du tech radar"
        return 1
    fi
    
    # 2. Demander une analyse des tendances UX
    local ux_trends_response=$(curl -s -X POST http://localhost:3010/ux-trends/analyze)
    
    if echo "$ux_trends_response" | grep -q "success"; then
        log "DEBUG" "Analyse des tendances UX initiée"
    else
        log "ERROR" "Échec de l'analyse des tendances UX"
        return 1
    fi
    
    # 3. Demander une optimisation système
    local optimization_response=$(curl -s -X POST http://localhost:3010/system/optimize)
    
    if echo "$optimization_response" | grep -q "success"; then
        log "DEBUG" "Workflow d'évolution fonctionnel"
        return 0
    else
        log "ERROR" "Échec du workflow d'évolution"
        return 1
    fi
}

# Test de performance système
test_system_performance() {
    log "INFO" "Test de performance système..."
    
    local start_time=$(date +%s)
    local concurrent_requests=10
    local total_requests=100
    local success_count=0
    
    # Test de charge sur le cortex central
    for i in $(seq 1 $total_requests); do
        if curl -s -f http://localhost:3000/health >/dev/null 2>&1; then
            ((success_count++))
        fi
        
        # Lancer des requêtes concurrentes
        if (( i % concurrent_requests == 0 )); then
            sleep 0.1
        fi
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local success_rate=$((success_count * 100 / total_requests))
    
    log "INFO" "Performance: $success_count/$total_requests requêtes réussies en ${duration}s (${success_rate}%)"
    
    # Critère de réussite: au moins 95% de succès
    if (( success_rate >= 95 )); then
        log "DEBUG" "Performance système acceptable"
        return 0
    else
        log "ERROR" "Performance système insuffisante"
        return 1
    fi
}

# Génération du rapport de test
generate_test_report() {
    log "INFO" "Génération du rapport de test..."
    
    mkdir -p "$RESULTS_DIR"
    local report_file="$RESULTS_DIR/integration_test_report_$TIMESTAMP.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Rapport de Test d'Intégration Système</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .passed { color: green; }
        .failed { color: red; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
        .test-passed { border-left-color: green; }
        .test-failed { border-left-color: red; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Rapport de Test d'Intégration Système</h1>
        <p>Date: $(date)</p>
        <p>Durée: $(($(date +%s) - start_time)) secondes</p>
    </div>
    
    <div class="summary">
        <h2>Résumé</h2>
        <p>Total des tests: $TOTAL_TESTS</p>
        <p class="passed">Tests réussis: $PASSED_TESTS</p>
        <p class="failed">Tests échoués: $FAILED_TESTS</p>
        <p>Taux de réussite: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%</p>
    </div>
    
    <div class="details">
        <h2>Détails des Tests</h2>
        <!-- Les détails seraient ajoutés ici -->
    </div>
</body>
</html>
EOF
    
    log "INFO" "Rapport généré: $report_file"
}

# Fonction principale
main() {
    local start_time=$(date +%s)
    
    log "INFO" "Début des tests d'intégration système"
    
    # Créer les répertoires nécessaires
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$RESULTS_DIR"
    
    # Exécuter les tests
    run_test "Connectivité des agents" test_agents_connectivity
    run_test "Communication Kafka" test_kafka_communication
    run_test "Stockage Weaviate" test_weaviate_storage
    run_test "Workflow UX" test_ux_workflow
    run_test "Workflow recherche web" test_web_research_workflow
    run_test "Workflow analyse de données" test_data_analysis_workflow
    run_test "Workflow gestion de projet" test_project_management_workflow
    run_test "Workflow évolution" test_evolution_workflow
    run_test "Performance système" test_system_performance
    
    # Générer le rapport
    generate_test_report
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    log "INFO" "Tests terminés en ${total_duration} secondes"
    log "INFO" "Résultats: $PASSED_TESTS/$TOTAL_TESTS tests réussis"
    
    if (( FAILED_TESTS == 0 )); then
        echo -e "${GREEN}🎉 Tous les tests d'intégration ont réussi!${NC}"
        exit 0
    else
        echo -e "${RED}❌ $FAILED_TESTS test(s) ont échoué${NC}"
        exit 1
    fi
}

# Exécuter le script principal
main "$@"
